/** listInput r1 // 2010.03.05 // jQuery 1.3 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

function addNewMenuItemValues(eleId) {
	$.listInput.ref(eleId).addNewMenuItemValues();
}

(function($) {

	$.listInput = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return listInput_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			type					: 'decimal',
			listAreaWidth			: 350,
			onItemChange			: null,
			multipleValueDelimiter	: ','
		}
	};
	
	$.fn.listInput = function (opts) {
		return this.each(function() {
			var conf = $.extend({
					parentContainer: $(this).parent()
				},opts);
			if(conf !== false) new listInput_component().init(this, conf);
		});
	};
	
	function listInput_component () {
		return {
			
		settings : $.extend({},$.listInput.defaults),	

		init : function(elem, conf) {

			var _this = this;
			listInput_component.inst[$(elem).attr("id")] = this;
			this.settings = $.extend(true, {}, this.settings, conf);

			$(elem).hide();
			_this.settings.elemId = $(elem).attr('id');
			_this.settings.binding_ele = $(elem);

			$.get(context + '/includes/javascript/jQueryPlugins/listInput/interface.listInput.hbs', function (data) {
				
			    var template = Handlebars.compile(data);
			    $(elem).after(template({
			    	idx		: _this.settings.elemId,
			    	width	: _this.settings.listAreaWidth,
			    	text	: {
			    		no_menu_value_items : client_messages.text.no_menu_value_items,
			    		add_menu_item		: client_messages.text.add_menu_item,
			    		remove_menu_item	: client_messages.text.remove_menu_item
			    	}
			    }));

				_this.settings.interface_container = $('#listInputContainer_' + _this.settings.elemId);
				
				if ( _this.settings.type == "entities" )
					$(_this.settings.interface_container).find('.listInputMenuItemsContainer').append("<ol></ol>");
				
			    _this.initBtn( $(_this.settings.interface_container).find(".addMenuValueItemBtn") );
			    _this.initBtn( $(_this.settings.interface_container).find(".removeMenuValueItemBtn") );
			    
				$(_this.settings.interface_container).find('.detailTip').each( function(){
					initTip(this);
				});
				
				$(_this.settings.interface_container).find(".addMenuValueItemBtn").click( function() {
					if ( $(this).is('.actionBtn_disabled') )
						return;
					
					var button = $(this);
					
					if ( _this.settings.type == "color" ) {
						// TYPE: Color
						_this.addNewColorValue();
					} else if ( _this.settings.type == "entities" ) {
						// TYPE: Entities
						setTimeout( function() {
							$(button).popupFactory({
								popup_id			: "listInputPopup_" + _this.settings.elemId,
								title				: client_messages.title.add_symbol,
								popupLocation		: "right",
								trigger				: "instant",
								width				: 350,
								concurrentPopups	: true,
								fnSetContent		: function(o) {
									
									// 025cf:black bullet ; 025e6 white bullet; 02023 triangular bullet ; 02043 hyphen bullet ; 02217 asterisk
									// 029be: circle white bullet ; 029bf: circled bullet ; 02192: rightwards arrow ; 021d2 rightwards double arrow
									// 021e8: rightwards white arrow ; 025b6 black right-pointing triangle ; 027a4 black rightwards arrowhead
									// 020de: back square centered ; 02610 ballot box; 02611 ballot box with check ; 02612 ballot box with
									// 02717: ballot x; 02718 heavy ballot x
									var entities = ['none','025cf','025e6','02023','02043','02217','029be','029bf','02192','021d2','021e8',
									                '025b6','027a4','02bc0','02610','02611','02612','02717','02718'];
									
									var popupEle = "<div><div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">";

									for ( var i=0; i < entities.length; i++ ) {
										popupEle +=		"<div class=\"entityItemContainer\" style=\"cursor: pointer; display: inline-block; min-width: 40px; height: 40px; font-size: 20px; vertical-align: middle; text-align: center;\">" +
															"<input type=\"hidden\" value=\"" + entities[i]+ "\" />" +
															"<div style=\"position: relative; padding: 10px; white-space: nowrap;\">" + 
																(entities[i] == 'none' ? "<span style=\"font-size: 12px;\">" + client_messages.text.no_override + "</span>" : "&#x"+entities[i]+";") + 
															"</div>" +
														"</div>";
									}

									popupEle +=			"<div style=\"margin-top: 8px;\">" +
															"<div align=\"left\" style=\"white-space: nowrap;\">" +
																"<div align=\"left\" style=\"white-space: nowrap; display: inline-block;\">" +
																	"<input maxlength=\"5\" id=\"entitiesListInput\" class=\"inputL menuValueItemsInput alphaNum\" filterId=\"entitiesListInput\" />" +
																	$.alphanumeric.generateFilterTooltip("entitiesListInput") +
																"</div>" +
						            							"<div class=\"listInputCustomValueBtn fa-mp-container\" style=\"padding-left: 8px; display: inline-block;\">" +
					            									"<i class=\"fa fa-check fa-mp-btn\" style=\"font-size: 16px; color: #666;\">&nbsp;</i>" +
					            								"</div>" +
															"</div>" +
															"<div align=\"left\" style=\"font-size: 11px; font-weight: bold;\">" + client_messages.text.add_items_base16_entities + "</div>" +
														"</div>" +
													"</div>" +
													"<div style=\"padding: 8px 16px;\">" +
														"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
															"<td/>" +
															"<td width=\"1%\">" +
																"<input title=\"" + client_messages.text.close + "\" type=\"button\" id=\"closeAddMenuValueItemsPopupBtn\" onclick=\"javascript:$('#listInputPopup_" + _this.settings.elemId + "').remove();\" style=\"display: none;\" />" +
															"</td>" +
														"</tr></table>" +
													"</div></div>";
									
									popupEle = $(popupEle);
									$(popupEle).find('.entityItemContainer')
										.mouseover( function() {
											$(this).css({'background-color':'#f5f5f5'});
										})
										.mouseout( function() {
											$(this).css({'background-color':'initial'});
										})
										.click( function() {
											_this.addNewEntityValue($(this).find('input').val());
										});
									
									$(popupEle).find('.listInputCustomValueBtn')
										.mouseover( function() {
											$(this).find('i').css({'color':'green'});
										})
										.mouseout( function() {
											$(this).find('i').css({'color':'#666'});
										})
										.click( function() {
											var inputVal = $(popupEle).find('#entitiesListInput').val();
											if ( inputVal.length != 0 )
												_this.addNewEntityValue( inputVal );
										});
									
									$(popupEle).find('.alphaNum').alphanumeric({alphaNum: true});

									$(popupEle).find('#closeAddMenuValueItemsPopupBtn').styleActionElement();

									return popupEle;
								}
							});
						}, 10);
					} else {
						// TYPE: Decimal
						setTimeout( function() {
							$(button).popupFactory({
								popup_id			: "listInputPopup_" + _this.settings.elemId,
								title				: client_messages.title.add_menu_item,
								popupLocation		: "left",
								trigger				: "instant",
								width				: 350,
								concurrentPopups	: true,
								fnSetContent		: function(o) {
									
									var filter = 'simpleName';
									if ( _this.settings.type == 'decimal' )
										filter = 'decimal';
									if ( _this.settings.type == 'string' )
										filter = 'description';
									
									var popupEle = $("<div><div align=\"left\" style=\"font-size: 11px; margin: 8px 16px;\">" +
														"<div align=\"left\" style=\"font-size: 11px; font-weight: bold;\">" + client_messages.text.add_items_delimiter_separated_values.replace('{0}',_this.settings.multipleValueDelimiter) + "</div>" +
														"<div align=\"left\" style=\"white-space: nowrap;\">" +
															"<textarea id=\"menuValueItemsInput\" class=\"inputL menuValueItemsInput " + filter + "\" filterId=\"menuValueItemsInput\"></textarea>" +
															$.alphanumeric.generateFilterTooltip("menuValueItemsInput") +
														"</div>" +
													"</div>" +
													"<div style=\"padding: 8px 16px;\">" +
														"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
															"<td/>" +
															"<td width=\"1%\">" +
																"<input title=\"" + client_messages.text.add + "\" type=\"button\" id=\"confirmAddMenuValueItemsBtn\" style=\"display: none;\" />" + 
															"</td>" +
															"<td width=\"1%\">" +
																"<input title=\"" + client_messages.text.close + "\" type=\"button\" id=\"closeAddMenuValueItemsPopupBtn\" onclick=\"javascript:$('#listInputPopup_" + _this.settings.elemId + "').remove();\" style=\"display: none;\" />" +
															"</td>" +
														"</tr></table>" +
													"</div></div>");

									$(popupEle).find('#confirmAddMenuValueItemsBtn,#closeAddMenuValueItemsPopupBtn').styleActionElement();
									
									$(popupEle).find('#confirmAddMenuValueItemsBtn_button').click( function() {
										_this.addNewMenuItemValues();
									});
									
									if ( filter == 'decimal' )
										$(popupEle).find('.' + filter).alphanumeric({decimal: true, extended_chars: ","});
	
									return popupEle;
								}
							});
						}, 10);
					
					}
				});
				
				$(_this.settings.interface_container).find(".removeMenuValueItemBtn").click( function() {
					if ( $(this).is('.actionBtn_disabled') )
						return;
					
					$(_this.settings.interface_container).find(".menuItemValueContainer_selected").remove();
					if ( $(_this.settings.interface_container).find('.menuItemValueContainer').length == 0 )
						$('#listInputMenuItemsContainer_'+_this.settings.elemId).find('.noMenuValueItemsMsg').showEle('normal');
					
					_this.updateBinding();
				});
				
				var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
				var menuItems = $(_this.settings.binding_ele).val().split( _this.settings.multipleValueDelimiter );
				for ( var i=0; i < menuItems.length; i++ ) {
					if (menuItems[i].length == 0)
						continue;
					
					if ( _this.settings.type == "color" ) {
						_this.addNewColorValue(menuItems[i]);
					} else if ( _this.settings.type == "entities" ) {
						_this.addNewEntityValue(menuItems[i]);
					} else {
						var itemEle = 
							$("<div class=\"menuItemValueContainer\">" +
									menuItems[i] + 
							"</div>");
							$(itemsContainer).append( itemEle );
					}
				}
				_this.reorderItems();
				$(_this.settings.interface_container).find('.menuItemValueContainer').each( function() {
					_this.initMenuItemValue(this);
				});
				
				if ( _this.settings.type == "entities" )
					$(_this.settings.interface_container).find(".listInputMenuItemsContainer ol").sortable({
						update: function(e,o) {
							_this.updateBinding();
						}
					});
				
				if ( $(_this.settings.interface_container).find('.menuItemValueContainer').length == 0 )
					$(_this.settings.interface_container).find('.noMenuValueItemsMsg').show();
			    
			}, 'html');

		},
		
		addNewEntityValue : function(value) {
			var _this = this;

			var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
			$(itemsContainer).find('.noMenuValueItemsMsg').hide();

			var newItemEle = 
				$("<li class=\"menuItemValueContainer\" style=\"padding-left: 20px; height: 24px;\">" +
					"<div class=\"menuItemEntityContainer\">" +
						"<input type=\"hidden\" value=\"" + value + "\" />" +
					"</div>" +
					"<div class=\"menuItemEntityDisplayContainer\">" +
						 (value == 'none' ? "<span style=\"font-size: 14px;\">" + client_messages.text.no_override + "</span>" : "&#x"+value+";") +
					"</div>" +
				"</li>");
			$(itemsContainer).find('ol').append( newItemEle );

			_this.initMenuItemValue($(newItemEle));
			_this.updateBinding();

			$("#listInputPopup_" + _this.settings.elemId, getTopFrame().document).remove();
			
		},
		
		addNewColorValue : function(value) {
			var _this = this;

			var newItem = value == undefined;
			if ( value == undefined )
				value = "cmyk(0;0;0;100)";

			var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
			$(itemsContainer).find('.noMenuValueItemsMsg').hide();
			
			var existingValues = new Array();
			$(itemsContainer).find('.menuItemValueContainer,.menuItemValueContainer_selected').each( function() {
				existingValues[existingValues.length] = _this.cleanValue( $(this).text() );
			});

			var newItemEle = 
				$("<div class=\"menuItemValueContainer\" style=\"display: none;\">" +
					"<div class=\"menuItemColorContainer\">" +
						"<input type=\"text\" class=\"colorValue newColor\" value=\"" + value + "\" style=\"display: none;\" />" +
					"</div>" +
					"<div class=\"menuItemColorLabelContainer\">" +
						(value != undefined ? value : "") +
					"</div>" +
				"</div>");
			$(itemsContainer).append( newItemEle );

			if ( !newItem )
				$(newItemEle).show();

			$(newItemEle).find('.newColor').colorpicker({
				showOn			: 'both',
				buttonImageOnly	: true,
				buttonImage		: context + '/includes/javascript/jQueryPlugins/listInput/images/ui-colorpicker.png',
				modal			: true,
				parts			: ['map', 'bar', 'hex', 'hsv', 'lab', 'rgb', 'cmyk', 'preview', 'swatches', 'footer'],
				buttonColorize	: true,
				colorFormat		: 'cmyk(cp;mp;yp;kp)',
				autoOpen		: newItem,
				revert			: true,
				inline			: false,
				position		: { 
									my : "center",
									at : "center",
									of : $(window)
								},
		        ok				:	function(event, o) {
		        	var cmykValue = _this.formatColor(o.formatted);
		        	$(o.colorPicker.element).val(cmykValue);
		        	
		        	if ( existingValues.indexOf(cmykValue) != -1 ) {
			        	if ( $(o.colorPicker.element).is('.newColor') )
			        		$(o.colorPicker.element).closest('.menuItemValueContainer').remove();
		        	} else {
		        		$(o.colorPicker.element).removeClass('newColor');
			        	$(o.colorPicker.element).closest('.menuItemValueContainer,.menuItemValueContainer_selected').showEle('normal');
			        	$(o.colorPicker.element).closest('.menuItemValueContainer,.menuItemValueContainer_selected').find('.menuItemColorLabelContainer').text(cmykValue);
			        	
			        	_this.reorderItems();
		        	}
		        },
		        cancel			:	function(event, o) {
		        	// Remove container on cancel of new color add
		        	if ( $(o.colorPicker.element).is('.newColor') )
		        		$(o.colorPicker.element).closest('.menuItemValueContainer').remove();
		        },
		        open			: function(event, o) {
		        	// CMYK Workaround: Set value on open
		        	var v = $(o.colorPicker.element).val().replace('cmyk(','').replace(')','');
		        	// Use either ',' or ';' as separator
		        	var separator = v.indexOf(',') != -1 ? ',' : ';';
		        	var cmyk = v.split(separator);
		        	o.colorPicker.color.setCMYK(cmyk[0] / 100.0, cmyk[1] / 100.0, cmyk[2] / 100.0, cmyk[3] / 100.0);

		        	var pickerControl = $('.ui-colorpicker').first();
		        	pickerControl.css('position', 'absolute');
		        	pickerControl.css("top", Math.max(0, (($(window).height() - $(pickerControl).outerHeight()) / 2) +  $(window).scrollTop()) + "px");
		        	pickerControl.css("left", Math.max(0, (($(window).width() - $(pickerControl).outerWidth()) / 2) +  $(window).scrollLeft()) + "px");
		        },
		        init			: function(event, o) {
		        	// CMYK Workaround: Set default value on init
		        	var v = $(o.colorPicker.element).val().replace('cmyk(','').replace(')','');
		        	// Use either ',' or ';' as separator
		        	var separator = v.indexOf(',') != -1 ? ',' : ';';
		        	var cmyk = v.split(separator);
		        	o.colorPicker.color.setCMYK(cmyk[0] / 100.0, cmyk[1] / 100.0, cmyk[2] / 100.0, cmyk[3] / 100.0);
		        }
			});

			_this.reorderItems();
		},
		
		addNewMenuItemValues : function() {
			var _this = this;

			var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
			$(itemsContainer).find('.noMenuValueItemsMsg').hide();
			
			var existingValues = new Array();
			$(itemsContainer).find('.menuItemValueContainer,.menuItemValueContainer_selected').each( function() {
				existingValues[existingValues.length] = _this.cleanValue( $(this).text() );
			});
			
			var newMenuItems = $('#menuValueItemsInput', getTopFrame().document).val().split( _this.settings.multipleValueDelimiter );
			
			for ( var i=0; i < newMenuItems.length; i++ ) {
				if ( newMenuItems[i].length == 0 || existingValues.indexOf(newMenuItems[i]) != -1 )
					continue;
				
				var newItemEle = 
					$("<div class=\"menuItemValueContainer\">" +
							_this.cleanValue( newMenuItems[i] ) + 
					"</div>");
				$(itemsContainer).append( newItemEle );
			}
			
			_this.reorderItems();
			
			$("#listInputPopup_" + _this.settings.elemId, getTopFrame().document).remove();

		},
		
		cleanValue: function(value) {
			var _this = this;
			value = $.trim(String(value));
			if ( _this.settings.type == "decimal" ) {
				while ( value.indexOf("0") == 0 && value.length > 1 )
					value = value.substring(1);
				while ( value.lastIndexOf(".") != value.indexOf(".") )
					value = value.substring(0, value.lastIndexOf("."));
			};
			return value;
		},
		
		initBtn : function(ele, type) {
			
			$(ele)
				.mouseover( function() {
					if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]') ) {
						$(this).removeClass('actionBtn');
						$(this).addClass('actionBtn_hov');
					}
				})
				.mouseout( function() {
					if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect') ) {
						$(this).removeClass('actionBtn_hov');
						$(this).addClass('actionBtn');
					}
				});

			
			if ( type == "toggle" ) { 
				$(ele)	
					.click( function() {
						if ( !$(this).hasClass('actionBtn_disabled') ) {
							if ( $(this).hasClass('actionBtn_toggleSelectHighlight') )
								$(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
							else
								$(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
						}
							
					});
			} else if ( type == "button" ) {
				$(ele)	
					.mousedown( function() {
						if ( !$(this).hasClass('actionBtn_disabled') )
							$(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
					})
					.mouseup( function() {
						if ( !$(this).hasClass('actionBtn_disabled') )
							$(this).removeClass('actionBtn_selected').addClass('actionBtn');
					});
			}

		},
		
		initMenuItemValue : function(ele) {
			if ( !$(ele).is('.init') ) {
				$(ele)
				.addClass('init')
				.click( function() {
					if ( $(this).is('.menuItemValueContainer') )
						$(this).removeClass('menuItemValueContainer').addClass('menuItemValueContainer_selected');
					else
						$(this).removeClass('menuItemValueContainer_selected').addClass('menuItemValueContainer');
				});
			}
		},
		
		formatColor : function(color) {
			if ( color.indexOf('cmyk') != -1 ) {
				color = color.replace('cmyk(','');
				color = color.replace(')','');
				var colors = color.split(';');
				for (var i=0; i < colors.length; i++) 
					colors[i] = parseInt(colors[i]);
				
				return "cmyk(" + colors.join(';') + ")";
			}
			return color;
		},
		
		reorderItems : function() {
			var _this = this;

			// Entities input is user re-orderable
			if ( _this.settings.type == 'entities' )
				return;

			var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
			var itemsArray = new Array();
			$(itemsContainer).find('.menuItemValueContainer,.menuItemValueContainer_selected').each( function() {
				itemsArray[itemsArray.length] = $(this);
			});

			itemsArray.sort( function(a,b){

				if ( _this.settings.type == 'decimal' ) {
					if ( parseFloat(_this.cleanValue($(a).text())) > parseFloat(_this.cleanValue($(b).text())) )
						return 1;
					else if ( parseFloat(_this.cleanValue($(a).text())) < parseFloat(_this.cleanValue($(b).text())) )
						return -1;
					else
						return 0;
				} else if ( _this.settings.type == 'color' ) {
					if ( _this.cleanValue($(a).find('.menuItemColorLabelContainer').text()) > _this.cleanValue($(b).find('.menuItemColorLabelContainer').text()) )
						return 1;
					else if (  _this.cleanValue($(a).find('.menuItemColorLabelContainer').text()) < _this.cleanValue($(b).find('.menuItemColorLabelContainer').text()) )
						return -1;
					else
						return 0;
				}
			});

			$(itemsContainer).append(itemsArray);
			
			_this.updateBinding();
			
			$(itemsContainer).find('.menuItemValueContainer,.menuItemValueContainer_selected').each( function() {
				_this.initMenuItemValue(this);
			});

		},
		
		updateBinding : function() {
			var _this = this;

			var itemsContainer = _this.settings.interface_container.find('.listInputMenuItemsContainer');
			var itemValuesArray = new Array();
			$(itemsContainer).find('.menuItemValueContainer,.menuItemValueContainer_selected').each( function() {
				var value = null;
				if ( _this.settings.type == "color")
					value = _this.cleanValue( $(this).find('.menuItemColorLabelContainer').text() );
				else if ( _this.settings.type == "entities")
					value = $(this).find('.menuItemEntityContainer input').val();
				else
					value = _this.cleanValue( $(this).text() );
				if ( itemValuesArray.indexOf(value) == -1 )
					itemValuesArray[itemValuesArray.length] = value;
			});

			$(_this.settings.binding_ele).val(itemValuesArray.join( _this.settings.multipleValueDelimiter ));
			
			if ( $.isFunction(_this.settings.onItemChange) )
				_this.settings.onItemChange(_this, $(_this.settings.binding_ele).val());
			
		}

		}
	};
	
	// instance manager
	listInput_component.inst = {};

})(jQuery);	