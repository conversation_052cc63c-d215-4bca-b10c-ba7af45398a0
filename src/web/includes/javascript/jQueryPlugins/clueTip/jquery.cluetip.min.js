/*!
 * clueTip - v1.2.10 - 2013-09-29
 * http://plugins.learningjquery.com/cluetip/
 * Copyright (c) 2013 <PERSON>
 * Licensed MIT (http://www.opensource.org/licenses/mit-license.php)
 */
(function(t){t.cluetip={version:"1.2.10",template:["<div>",'<div class="cluetip-outer">','<h3 class="cluetip-title ui-widget-header ui-cluetip-header"></h3>','<div class="cluetip-inner ui-widget-content ui-cluetip-content"></div>',"</div>",'<div class="cluetip-extra"></div>',"</div>"].join(""),setup:{insertionType:"appendTo",insertionElement:"body"},defaults:{multiple:!1,width:275,height:"auto",cluezIndex:97,positionBy:"auto",topOffset:15,leftOffset:15,snapToEdge:!1,local:!1,localPrefix:null,localIdSuffix:null,hideLocal:!0,attribute:"rel",titleAttribute:"title",splitTitle:"",escapeTitle:!1,showTitle:!0,cluetipClass:"default",hoverClass:"",waitImage:!0,cursor:"help",arrows:!1,dropShadow:!0,dropShadowSteps:6,sticky:!1,mouseOutClose:!1,delayedClose:50,activation:"hover",clickThrough:!0,tracking:!1,closePosition:"top",closeText:"Close",truncate:0,fx:{open:"show",openSpeed:""},hoverIntent:{sensitivity:3,interval:50,timeout:0},onActivate:function(){return!0},onShow:function(){},onHide:function(){},ajaxCache:!0,ajaxProcess:function(t){return t=t.replace(/<(script|style|title)[^<]+<\/(script|style|title)>/gm,"").replace(/<(link|meta)[^>]+>/g,"")},ajaxSettings:{dataType:"html"},debug:!1}};var e,i="cluetip ui-widget ui-widget-content ui-cluetip",o={},a=0,l=0,n=function(t){return t.replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;")};t.fn.attrProp=t.fn.prop||t.fn.attr,t.fn.cluetip=function(s,c){function u(e,i){var o=e||"";return i=i||"","object"==typeof i?t.each(i,function(t,e){o+="-"+t+"-"+e}):"string"==typeof i&&(o+=i),o}function r(e,i,o){var a="",l=i.dropShadow&&i.dropShadowSteps?+i.dropShadowSteps:0;if(t.cluetip.boxShadow)return l&&(a="1px 1px "+l+"px rgba(0,0,0,0.5)"),e.css(t.cluetip.boxShadow,a),!1;var n=e.find(".cluetip-drop-shadow");if(l==n.length)return n;n.remove();for(var s=[],c=0;l>c;)s[c++]='<div style="top:'+c+"px;left:"+c+'px;"></div>';return o=t(s.join("")).css({position:"absolute",backgroundColor:"#000",zIndex:g-1,opacity:.1}).addClass("cluetip-drop-shadow").prependTo(e)}var p,d,f,h,m,v;if("object"==typeof s&&(c=s,s=null),"destroy"==s)return this.each(function(){var e=t(this),i=e.data("cluetip");i&&(t(i.selector).remove(),t.removeData(this,"title"),t.removeData(this,"cluetip")),i.title&&e.attrProp("title",i.title),e.unbind(".cluetip").unbind("cluetipMoc")}),t('[id^="cluetip"]').length||t(document).unbind(".cluetip"),this;c=t.extend(!0,{},t.cluetip.defaults,c||{}),a++;var g,b=t.cluetip.backCompat||!c.multiple?"cluetip":"cluetip-"+a,x="#"+b,w=t.cluetip.backCompat?"#":".",y=t.cluetip.setup.insertionType,C=t.cluetip.setup.insertionElement||"body";y=/appendTo|prependTo|insertBefore|insertAfter/.test(y)?y:"appendTo",p=t(x),p.length||(p=t(t.cluetip.template)[y](C).attr("id",b).css({position:"absolute",display:"none"}),g=+c.cluezIndex,f=p.find(w+"cluetip-outer").css({position:"relative",zIndex:g}),d=p.find(w+"cluetip-inner"),h=p.find(w+"cluetip-title"),p.bind("mouseenter mouseleave",function(e){t(this).data("entered","mouseenter"===e.type)})),e=t("#cluetip-waitimage"),!e.length&&c.waitImage&&(e=t("<div></div>").attr("id","cluetip-waitimage").css({position:"absolute"}),e.insertBefore(p).hide());var S=(parseInt(p.css("paddingLeft"),10)||0)+(parseInt(p.css("paddingRight"),10)||0);return this.each(function(a){function b(){return!1}function y(t,e){var i=t.status;e.beforeSend(t.xhr,e),"error"==i?e[i](t.xhr,t.textStatus):"success"==i&&e[i](t.data,t.textStatus,t.xhr),e.complete(t.xhr,e.textStatus)}var I,T=this,k=t(this),j=t.extend(!0,{},c,t.metadata?k.metadata():t.meta?k.data():k.data("cluetip")||{}),B=!1,z=!1,P=null,O=j[j.attribute]||("href"==j.attribute?k.attr(j.attribute):k.attrProp(j.attribute)||k.attr(j.attribute)),M=j.cluetipClass;if(g=+j.cluezIndex,k.data("cluetip",{title:T.title,zIndex:g,selector:x,cursor:T.style.cursor||""}),j.arrows&&!p.find(".cluetip-arrows").length&&p.append('<div class="cluetip-arrows ui-state-default"></div>'),!O&&!j.splitTitle&&!s)return!0;j.local&&j.localPrefix&&(O=j.localPrefix+O),j.local&&j.hideLocal&&O&&t(O+":first").hide();var A,L,N,E,Y,H,X,D,R,W,$,q,Q,U,F=parseInt(j.topOffset,10),G=parseInt(j.leftOffset,10),J=isNaN(parseInt(j.height,10))?"auto":/\D/g.test(j.height)?j.height:j.height+"px",K=parseInt(j.width,10)||275,V=K+S+j.dropShadowSteps,Z=this.offsetWidth,_="title"!=j.attribute?k.attr(j.titleAttribute)||"":"";j.escapeTitle&&(_=n(_)),j.splitTitle&&(U=_.split(j.splitTitle),_=j.showTitle||""===U[0]?U.shift():""),k.bind("mouseenter mouseleave",function(t){var e=k.data("cluetip");e.entered="entered"===t.type,k.data("cluetip",e)});var te=function(i){var n,r,v,g=j.onActivate.call(T,i);if(g===!1)return!1;if(z=!0,p=t(x).css({position:"absolute"}),f=p.find(w+"cluetip-outer"),d=p.find(w+"cluetip-inner"),h=p.find(w+"cluetip-title"),m=p.find(w+"cluetip-arrows"),p.removeClass().css({width:K}),O==k.attr("href")&&k.css("cursor",j.cursor),j.hoverClass&&k.addClass(j.hoverClass),E=H=k.offset().top,Y=E+k.inner_Height(),W=k.offset().left,"relative"===t(C).css("position")&&(W-=t(C)[0].getBoundingClientRect().left),Z=k.outer_Width(),i.type==focus||"mouse"==j.positionBy&&!i.pageX?(q=W+Z/2+G,p.css({left:$}),D=H+F):(q=i.pageX,D=i.pageY),"area"!=T.tagName.toLowerCase()&&(N=t(document).scrollTop(),Q=t(window).width()),"fixed"==j.positionBy?($=Z+W+G,p.css({left:$})):($=Z>W&&W>V||W+Z+V+G>Q?W-V-G:Z+W+G,("area"==T.tagName.toLowerCase()||"mouse"==j.positionBy||Z+V>Q)&&(q+20+V>Q?(p.addClass("cluetip-"+M),$=q-V-G>=0?q-V-G-parseInt(p.css("marginLeft"),10)+parseInt(d.css("marginRight"),10):q-V/2):$=q+G),n=0>$?i.pageY+F:i.pageY,(0>$||"bottomTop"==j.positionBy||"topBottom"==j.positionBy)&&($=q+V/2>Q?Q/2-V/2:Math.max(q-V/2,0))),m.css({zIndex:k.data("cluetip").zIndex+1}),p.css({left:$,zIndex:k.data("cluetip").zIndex}),L=t(window).height(),s)I="function"==typeof s?s.call(T):s,d.html(I),ee(n);else if(U){var b=U.length;if(d.html(b?U[0]:""),b>1)for(var S=1;b>S;S++)d.append('<div class="split-body">'+U[S]+"</div>");ee(n)}else if(j.local||0===O.indexOf("#")){if(j.local){var P=t(O+(/^#\S+$/.test(O)?"":":eq("+a+")")).clone(!0).show();j.localIdSuffix&&P.attr("id",P[0].id+j.localIdSuffix),d.html(P),ee(n)}}else if(/\.(jpe?g|tiff?|gif|png)(?:\?.*)?$/i.test(O))d.html('<img src="'+O+'" alt="'+_+'" />'),ee(n);else{var A=j.ajaxSettings.beforeSend,X=j.ajaxSettings.error,R=j.ajaxSettings.success,J=j.ajaxSettings.complete;v=u(O,j.ajaxSettings.data);var te={cache:j.ajaxCache,url:O,beforeSend:function(t,i){A&&A.call(T,t,p,d,i),f.children().empty(),j.waitImage&&e.css({top:D+20,left:q+20,zIndex:k.data("cluetip").zIndex-1}).show()},error:function(t,e){c.ajaxCache&&!o[v]&&(o[v]={status:"error",textStatus:e,xhr:t}),z&&(X?X.call(T,t,e,p,d):d.html("<i>sorry, the contents could not be loaded</i>"))},success:function(t,e,i){c.ajaxCache&&!o[v]&&(o[v]={status:"success",data:t,textStatus:e,xhr:i}),B=j.ajaxProcess.call(T,t),"object"==typeof B&&null!==B&&(_=B.title,B=B.content),z&&(R&&R.call(T,t,e,p,d),d.html(B))},complete:function(i,o){J&&J.call(T,i,o,p,d);var a=d[0].getElementsByTagName("img");l=a.length;for(var s=0,c=a.length;c>s;s++)a[s].complete&&l--;l?t(a).bind("load.ct error.ct",function(){l--,0===l&&(e.hide(),t(a).unbind(".ct"),z&&ee(n))}):(e.hide(),z&&ee(n))}};r=t.extend(!0,{},j.ajaxSettings,te),o[v]?y(o[v],r):t.ajax(r)}};k.unbind("showCluetip.cluetip",te).bind("showCluetip.cluetip",te);var ee=function(e){var o,a,l,n=_||j.showTitle&&"&nbsp;",s="",c="",u=!1,g={bottom:function(t){t.appendTo(d)},top:function(t){t.prependTo(d)},title:function(t){t.prependTo(h)}};if(p.addClass("cluetip-"+M),j.truncate){var b=d.text().slice(0,j.truncate)+"...";d.html(b)}n?h.show().html(n):h.hide(),j.sticky&&(g[j.closePosition]&&(o=t('<div class="cluetip-close"><a href="#">'+j.closeText+"</a></div>"),g[j.closePosition](o),o.bind("click.cluetip",function(){return oe(),!1})),j.mouseOutClose&&(k.unbind("mouseleave.cluetipMoc"),p.unbind("mouseleave.cluetipMoc"),("both"==j.mouseOutClose||"cluetip"==j.mouseOutClose||j.mouseOutClose===!0)&&p.bind("mouseleave.cluetipMoc",ae),("both"==j.mouseOutClose||"link"==j.mouseOutClose)&&k.bind("mouseleave.cluetipMoc",ae))),f.css({zIndex:k.data("cluetip").zIndex,overflow:"auto"==J?"visible":"auto",height:J}),A="auto"==J?Math.max(p.outer_Height(),p.height()):parseInt(J,10),X=H,R=N+L,u=q>$&&Math.max($,0)+V>q,"fixed"==j.positionBy?X=H-j.dropShadowSteps+F:"topBottom"==j.positionBy||"bottomTop"==j.positionBy||u?("topBottom"==j.positionBy?c=R>H+A+F&&A+F>D-N?"bottom":"top":("bottomTop"==j.positionBy||u)&&(c=H+A+F>R&&D-N>A+F?"top":"bottom"),j.snapToEdge?"top"==c?X=E-A-F:"bottom"==c&&(X=Y+F):"top"==c?X=D-A-F:"bottom"==c&&(X=D+F)):X=H+A+F>R?A>=L?N:R-A-F:"block"==k.css("display")||"area"==T.tagName.toLowerCase()||"mouse"==j.positionBy?e-F:H-j.dropShadowSteps,""===c&&(c=W>$?"left":"right"),a=" clue-"+c+"-"+M+" cluetip-"+M,"rounded"==M&&(a+=" ui-corner-all"),p.css({top:X+"px"}).attrProp({className:i+a}),j.arrows?(/(left|right)/.test(c)&&(l=p.height()-m.height(),s=$>=0&&e>0?H-X-j.dropShadowSteps:0,s=l>s?s:l,s+="px"),m.css({top:s}).show()):m.hide(),v=r(p,j),v&&v.length&&v.hide().css({height:A,width:K,zIndex:k.data("cluetip").zIndex-1}).show(),P||p.hide(),clearTimeout(P),P=null,p[j.fx.open](j.fx.openSpeed||0),t.fn.bgiframe&&p.bgiframe(),j.onShow.call(T,p,d)},ie=function(){z=!1,e.hide(),(!j.sticky||/click|toggle/.test(j.activation))&&(j.delayedClose>0?(clearTimeout(P),P=null,P=setTimeout(oe,j.delayedClose)):(oe(),clearTimeout(P))),j.hoverClass&&k.removeClass(j.hoverClass)},oe=function(e){var i=e&&e.data("cluetip")?e:k,o=i.data("cluetip")&&i.data("cluetip").selector,a=o||"div.cluetip",l=t(a),n=l.find(w+"cluetip-inner"),s=l.find(w+"cluetip-arrows");l.hide().removeClass(),j.onHide.call(i[0],l,n),o&&(i.removeClass("cluetip-clicked"),k.css("cursor",k.data("cluetip").cursor)),o&&_&&i.attrProp(j.titleAttribute,_),j.arrows&&s.css({top:""}),v&&v.hide()},ae=function(){var t=this;clearTimeout(P),P=setTimeout(function(){var e=k.data("cluetip").entered,i=p.data("entered"),o=!1;"both"==j.mouseOutClose&&(e||i)?o=!0:j.mouseOutClose!==!0&&"cluetip"!=j.mouseOutClose||!i?"link"==j.mouseOutClose&&e&&(o=!0):o=!0,o||oe.call(t)},j.delayedClose)};if(t(document).unbind("hideCluetip.cluetip").bind("hideCluetip.cluetip",function(e){oe(t(e.target))}),/click|toggle/.test(j.activation))k.bind("click.cluetip",function(e){return p.is(":hidden")||!k.is(".cluetip-clicked")?(te(e),t(".cluetip-clicked").removeClass("cluetip-clicked"),k.addClass("cluetip-clicked")):ie(e),!1});else if("focus"==j.activation)k.bind("focus.cluetip",function(t){k.attrProp("title",""),te(t)}),k.bind("blur.cluetip",function(t){k.attrProp("title",k.data("cluetip").title),ie(t)});else{k[j.clickThrough?"unbind":"bind"]("click.cluetip",b);var le=function(t){if(j.tracking){var e=$-t.pageX,i=X?X-t.pageY:H-t.pageY;k.bind("mousemove.cluetip",function(t){p.css({left:t.pageX+e,top:t.pageY+i})})}};t.fn.hoverIntent&&j.hoverIntent?k.hoverIntent({sensitivity:j.hoverIntent.sensitivity,interval:j.hoverIntent.interval,over:function(t){te(t),le(t)},timeout:j.hoverIntent.timeout,out:function(t){ie(t),k.unbind("mousemove.cluetip")}}):k.bind("mouseenter.cluetip",function(t){te(t),le(t)}).bind("mouseleave.cluetip",function(t){ie(t),k.unbind("mousemove.cluetip")}),k.bind("mouseover.cluetip",function(){k.attrProp("title","")}).bind("mouseleave.cluetip",function(){k.attrProp("title",k.data("cluetip").title)})}}),this},function(){t.support=t.support||{};for(var e=document.createElement("div"),i=e.style,o=["boxShadow"],a=["moz","Moz","webkit","o"],l=0,n=o.length;n>l;l++){var s=o[l],c=s.charAt(0).toUpperCase()+s.slice(1);if(i[s]!==void 0)t.cluetip[s]=s;else for(var u=0,r=a.length;r>u;u++)if(i[a[u]+c]!==void 0){t.cluetip[s]=a[u]+c;break}t.support[s]||(t.support[s]=t.cluetip[s])}e=null}(),t.fn.cluetip.defaults=t.cluetip.defaults})(jQuery);