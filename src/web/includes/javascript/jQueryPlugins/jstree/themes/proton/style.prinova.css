.dataTreeNodeSelected {
}
.dataTreeNodeHover {
}
.dataTreeNodeStatic {
    color: #000
}
.dataTreeNodeExpand {
    height: auto;
    clear: both;
}
.dataTreeNodeAttribute {
    border: 1px dotted #FFF;
    padding: 5px;
    float: left;
    margin: 0px 20px 0px 20px;
    user-select: none
}
.dataTreeNodeAttribute:hover {
    border: 1px dotted #bbb;
}

.dataTreeNodeElement {
    border: dotted 1px #FFF;
    padding: 5px;
    color: #000;
    margin-left: 20px;
    margin-right:20px;
    float: left;
    user-select: none
}
.dataTreeNodeElement:hover {
    border-style: dotted;
    border-width: 1px;
    border-color: #bbb;
}

.dataTreeNodeElementSelected {
    border: 1px dashed #3392e3;
}

.dataTreeNodeElementSelected:hover {
    border: 1px solid #3392e3;
}

.dataTreeNodeAttributeSelected {
    border: 1px dashed #3392e3;
}

.dataTreeNodeAttributeSelected:hover {
    border: 1px solid #3392e3;
}

.nodeLabel {
    margin-left:7px;
    font-weight: bolder;
    text-decoration: underline;
}

.nodeLabelDisabled {
    color: #ff0008;
}

.nodeLabelDisabledHover {
    color: #ffd4cf;
}

.dataSourceTreeTable {
    color: #414141;
    border: solid 1px #EEE;
    border-collapse: collapse;
    margin-left: 25px;
}

.dataSourceTreeTable thead {
    text-decoration: underline;
    font-weight: bold;
    background-color: #EEE;
    color: #555;
    cursor: default;
}

.dataSourceTreeTable td {
    padding-right: 50px;
    padding-left: 10px;
}

.dataSourceTreeTableEven {
    background-color: #f7f7f7;
}

.dataSourceTreeTableOdd {
    background-color: #FFF;
}

.dataSourceTreeTableEven:hover {
    background-color: #E5E5E5;
}

.dataSourceTreeTableOdd:hover {
    background-color: #E5E5E5;
}

.dataSourceTreeTableSelected {
    background-color: #3392e3;
    color: #FFF;
}