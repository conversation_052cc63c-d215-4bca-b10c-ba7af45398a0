.ibutton-container {
  position: relative;
  height: 30px;
  cursor: pointer;
  overflow: hidden;
  /* set max width to that of sprite */
  max-width: 400px;
  /* prevent text selection */
  -o-user-select: none;
  -moz-user-select: none;
  -moz-user-focus: ignore;
  -moz-user-input: disabled;
  /* set default width based on ON/OFF labels */
  width: 89px;
  border: solid 1px #6d3075;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: #6d3075;
}

.formControl.horizontal-control .ibutton-container {
    margin: 5px 0;
}

.ibutton-container input {
  position: absolute;
	top: 0;
	left: 0;

	/* hide the element */
	filter:alpha(opacity=0);
	-moz-opacity: 0.0;
	opacity: 0.0;

	/* allow checking of input if visible */
	-moz-user-input: enabled  !important;
}

.ibutton-handle {
  display: block;
  height: 24px;
  cursor: inherit;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  padding-left: 3px;
  width: 24px;
  margin: 3px 0 0;
  background: #6d3075;
}

.ibutton-handle-right {
  height: 100%;
  width: 100%;
  padding-right: 3px;
  z-index: 3;
  background: #6d3075;
}

.ibutton-handle-middle {
  height: 100%;
  width: 100%;
  background: #fff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  z-index: 3;
  -webkit-box-shadow: 0 0 8px 0 rgba(0, 0, 0, .3);
  -moz-box-shadow: 0 0 8px 0 rgba(0, 0, 0, .3);
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, .3);
}

div.ibutton-label-on, div.ibutton-label-off {
  white-space: nowrap;
  font-size: 12px;
  line-height: 30px;
  font-weight: bold;
  cursor: inherit;
  display: block;
  height: 30px;
  position: absolute;
  width: auto;
  top: 0;
  overflow: hidden;
  background: #6d3075;
}

div.ibutton-label-on {
  color: #fff;
  /* PRINOVA CUSTOM text-shadow: 0 -1px 2px rgba(153, 153, 153, 0.4); */
  left: 0;
  z-index: 1;
  text-align: center;
}

div.ibutton-label-on span {

}

div.ibutton-label-off {
  color: #fff;
  /* PRINOVA CUSTOM text-shadow: 0 -1px 2px rgba(153, 153, 153, 0.4); */
  text-align: right;
  right: 0;
	/* the off label needs to near the left edge (ideally just 5px away)
	 * it just needs to be close enough that it won't show under the handle if dragged to the left
	 */
   width: 95%;
}

div.ibutton-label-off span {
  padding-right: 15px;
}

/* create an outline when button gets focus via keyboard */
.ibutton-container label {
	cursor: inherit;
	font-size: 12px;
	font-weight: normal; /* PRINOVA CUSTOM */
}

.ibutton-focus label {
	/* we must use border, since outline doesn't work in IE */
	border: 1px dotted #fff !important;
	padding: 0 2px;
}

.ibutton-focus div.ibutton-label-on span label {
	/* use white for more contrast */
	border-color: #fff !important;
}

/* add padding to right/left so that text gets clipped before absolute edge */
.ibutton-padding-left, .ibutton-padding-right {
	position: absolute;
	top: 4px;
	z-index: 2;
	width: 3px;
	height: 32px;
}

.ibutton-padding-left {
	left: 0;
}

.ibutton-padding-right {
	right: 0;
	background-position: 100% -4px;
}

/* change the styles of the handle when being dragged */
.ibutton-active-handle .ibutton-handle-middle {
	background: #f1edf4;
}

/* styles to use when the button is disabled */
.ibutton-disabled {
    background: #dadada;
    border-color: #dadada;
	cursor: not-allowed !important; /* cursor options: default or not-allowed */
}

.ibutton-disabled .ibutton-handle-middle {
	background: #f5f5f5;
}

.ibutton-disabled div.ibutton-label-on > *,
.ibutton-disabled div.ibutton-label-off > * {
	opacity: 0.5;
}
