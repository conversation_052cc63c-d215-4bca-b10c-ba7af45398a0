/** userMenu r1 // 2016.09.27 // jQuery 1.11 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.11 or later
 * 
 */
(function($) {
	
	$.userMenu = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return userMenu_component.inst[o.attr('id')] || null; 
		},
		defaults : {

		}
	};
	
	$.fn.userMenu = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new userMenu_component().init(this, conf);
		});
	};
	
	function userMenu_component() {
		return {
			
		data : $.extend({},$.userMenu.defaults),	

		init : function(elem, conf) {
			var _this = this;

			userMenu_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);
			
			_this.is_sso_mode 			= $(_this.targetEle).attr('is_sso_mode') == "true";
			_this.applies_homepage 		= $(_this.targetEle).attr('applies_homepage') == "true";
			_this.applies_user_settings = $(_this.targetEle).attr('applies_user_settings') == "true";
			_this.applies_connected		= $(_this.targetEle).attr('applies_connected') == "true";
			_this.applies_rationalizer	= $(_this.targetEle).attr('applies_rationalizer') == "true";
			_this.applies_job_center    = $(_this.targetEle).attr('applies_job_center') == "true";
			
			_this.permission			= {};
			_this.permission.touchpoint_exchange = $(_this.targetEle).attr('touchpoint_exchange_permission') == "true";
			_this.permission.messagepoint_exchange = $(this.targetEle).attr('messagepoint_exchange_permission') == "true";
			
			$(_this.targetEle).html("<i class=\"fa fa-bars userMenu_icon\">&nbsp;</i>")

			_this.targetEle.click( function() {
				_this.popupUserMenu();
			});

		},

		popupUserMenu : function() {
			var _this = this;
			
			function fmtURL(url) {
				return url.replace("//","/");
			}

			$(_this.targetEle)
				.popupFactory({
					title				: client_messages.title.take_me_to,
					popup_id			: "userMenu",
					popupLocation		: "bottom-left",
					trigger				: "instant",	
					width				: 150,
					fnSetContent		: function(o) {
						
						var popupHTML = "<div align=\"left\" class=\"userMenu_container\">";
						
						if ( _this.applies_homepage )
							popupHTML += 	"<div id=\"userMenu_homepage\" class=\"userMenu_item\">" +
												client_messages.text.homepage +
											"</div>";
						if ( _this.applies_user_settings )
							popupHTML += 	"<div id=\"userMenu_mysettings\" class=\"userMenu_item\">" +
												client_messages.text.my_settings +
											"</div>";
						if ( _this.applies_connected )
							popupHTML += 	"<div id=\"userMenu_connected\" class=\"userMenu_item\">" +
												client_messages.text.connected +
											"</div>";
						if ( _this.applies_rationalizer )
							popupHTML += 	"<div id=\"userMenu_rationalizer\" class=\"userMenu_item\">" +
												client_messages.text.rationalizer +
											"</div>";
						if ( _this.applies_job_center )
						    popupHTML +=    "<div id=\"userMenu_jobcenter\" class=\"userMenu_item\">" +
                                                client_messages.text.job_center +
                                            "</div>";
                        if ( _this.permission.messagepoint_exchange )
                            popupHTML += 		"<div id=\"userMenu_mpExchange\" class=\"userMenu_item\">" +
                                client_messages.text.messagepoint_exchange +
                                "</div>";
						if ( _this.permission.touchpoint_exchange )
							popupHTML += 		"<div id=\"userMenu_tpExchange\" class=\"userMenu_item\">" +
													client_messages.text.touchpoint_exchange +
												"</div>";
						popupHTML += 		"<div id=\"userMenu_signout\" class=\"userMenu_item\">" +
												(_this.is_sso_mode ? client_messages.text.quit : client_messages.text.signout) +
											"</div>";				
						
						popupHTML +=	"</div>";
						
						popupHTML = $(popupHTML);
						
						$(popupHTML).find('.userMenu_item').click( function() {
							
							var id = parseId(this);
							
							if ( id == "mysettings" ) {
								popItUpWithDimensions2(fmtURL(context + "/user/settings_edit.form?tk=" + getParam('tk')), 1010, 735);
							} else if ( id == "dashboards" ) {
								if ( mp_perm_GLOBAL_DASHBOARD_VIEW )
									getTopFrame().location = fmtURL(context + "/dashboards/global_dashboard.form?tk=" + getParam('tk'));
								else
									getTopFrame().location = fmtURL(context + "/dashboards/my_dashboard.form?tk=" + getParam('tk'));
							} else if ( id == "connected" ) {
								getTopFrame().location = fmtURL(context + "/touchpoints/touchpoint_communications_list.form?tk=" + getParam('tk'));
							} else if ( id == "rationalizer" ) {
                                getTopFrame().location = fmtURL(context + "/rationalizer/rationalizer_documents_list.form?tk=" + getParam('tk'));
                            } else if ( id == "jobcenter") {
							    getTopFrame().location = fmtURL(context + "/job_center/job_center.form?tk=" + getParam('tk'));
							} else if ( id == "mpExchange" ) {
								popItUpWithDimensions2(fmtURL(context + "/tpadmin/touchpoint_exchange_list.form?isTpExchange=false&tk=" + getParam('tk')), 1010, 735);
                            } else if ( id == "tpExchange" ) {
                                popItUpWithDimensions2(fmtURL(context + "/tpadmin/touchpoint_exchange_list.form?isTpExchange=true&tk=" + getParam('tk')), 1010, 735);
							} else if ( id == "signout" ) {
								getTopFrame().location = fmtURL(context + "/signout?tk=" + getParam('tk'));
							}
							
						});

						return popupHTML;
					}
				});
			
		}


		}; // end component
	};
	
	// instance manager
	userMenu_component.inst = {};

})(jQuery);


