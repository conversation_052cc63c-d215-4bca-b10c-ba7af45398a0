$.fn.dataTableExt.afnStaticPageChangeCallback = [];

(function ($) {
    /**
     * Manages static paging events for a DataTable
     * @constructor
     * @param {object} oSettings - The DataTables instance setting object
     */
    var StaticPaging = function (oSettings) {

        var _this = this;


        /**
         * Initializes the plug-in instance by injecting the draw callback function that manages row visiblity
         * @function
         */
        this.fnInitialize = function () {

            oSettings.aoPreDrawCallback.push({
                fn: function (oSettings) {

                    // reset visibility for previously hidden rows
                    $(oSettings.aiDisplay).each(function (index, value) {
                        if ($(oSettings.aoData[value].nTr).data('deleted') !== true) {
                            $(oSettings.aoData[value].nTr).removeClass('staticPaging_hidden')[0].style.display = "table-row";
                        }
                    });

                    tableBody = oSettings.nTBody;
                }
            });

            oSettings.aoDrawCallback.push({
                fn: function (oSettings) {

                    var currentIds = {};

                    // determine the ids for visible rows
                    $('#' + oSettings.sTableId + ' > tbody > tr').each(function (index, value) {
                        currentIds[($(value).attr('id'))] = value;
                    });

                    var selector = '#' + oSettings.sTableId + ' tbody:last';

                    // reattach hidden rows detached by datatables to prevent server side binding issues
                    $(oSettings.aiDisplay).each(function (index, value) {

                        var nodeRow = oSettings.aoData[value].nTr;

                        if ($(nodeRow).data('deleted') == true) {
                            return true;
                        }

                        if (!currentIds.hasOwnProperty(nodeRow.id)) {
                            $(nodeRow).addClass('staticPaging_hidden')[0].style.display = "none";

                            if (!$.contains(document, nodeRow)) {
                                $(selector).append(nodeRow);
                            }


                        }
                    });

                    // apply row stripe styling
                    $('#' + oSettings.sTableId + ' > tbody > tr:visible').each(function(index, value) {
                       $(value).removeClass('odd even').addClass(index % 2 == 0 ? 'even' : 'odd');
                    });

                    // execute user callbacks
                    _.each($.fn.dataTableExt.afnStaticPageChangeCallback, function (callback) {
                        callback(oSettings);
                    });
                },
                sName: 'staticPaging'
            });
        };
    };

    /** Register a new feature with DataTables */
    $.fn.dataTableExt.aoFeatures.push({
        "fnInit": function (oSettings) {

            var instance = new StaticPaging(oSettings);
            instance.fnInitialize();

            // return null since this plug-in does not inject any DOM elements
            return null;
        },
        "cFeature": "P",
        "sFeature": "StaticPaging"
    });
})(jQuery);