/*
 *  File:         demo_table.css
 *  CVS:          $Id$
 *  Description:  CSS descriptions for DataTables demo pages
 *  Author:       <PERSON>
 *  Created:      Tue May 12 06:47:22 BST 2009
 *  Modified:     $Date$ by $Author$
 *  Language:     CSS
 *  Project:      DataTables
 *
 *  Copyright 2009 <PERSON>. All Rights Reserved.
 *
 * ***************************************************************************
 * DESCRIPTION
 *
 * The styles given here are suitable for the demos that are used with the standard DataTables
 * distribution (see www.datatables.net). You will most likely wish to modify these styles to
 * meet the layout requirements of your site.
 *
 * Common issues:
 *   'full_numbers' pagination - I use an extra selector on the body tag to ensure that there is
 *     no conflict between the two pagination types. If you want to use full_numbers pagination
 *     ensure that you either have "example_alt_pagination" as a body class name, or better yet,
 *     modify that selector.
 *   Note that the path used for Images is relative. All images are by default located in
 *     ../images/ - relative to this CSS file.
 */


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * START Prinova custom
 */
.dataTables_wrapper #iFrameSrc {
    display: none;
}
.dataTables_wrapper .ColVis_Button {
    border: none;
}
.dataTables_wrapper .ColVis {
    padding-right: 3px;
    margin-top: 6px;
}
.dataTables_wrapper div.ui-widget-header {
    height: 56px;
    border-color: #dadada;
    border-top: 0;
}

.dataTables_wrapper > .dataTablesContentWrapper {
}

.actionsBarContainer + .dataTables_wrapper .dataTablesContentWrapper.staticData,
.dataTables_wrapper > .dataTablesContentWrapper.staticData,
.actionsBarFilterContainer + .dataTables_wrapper .dataTablesContentWrapper.staticData {
    min-height: initial;
}

.actionsBarContainer + .dataTables_wrapper .dataTablesContentWrapper:not(.staticData),
.dataTables_wrapper > .dataTablesContentWrapper:not(.staticData),
.actionsBarFilterContainer + .dataTables_wrapper .dataTablesContentWrapper:not(.staticData) {
    min-height: 491px;
}

.dataTables_wrapper > .dataTablesContentWrapper > table {
    position: relative;
}

.dataTables_wrapper > .dataTablesContentWrapper > table tr td:first-child,
.dataTables_wrapper > .dataTablesContentWrapper > table tr th:first-child {
}

.dataTables_wrapper > .dataTablesContentWrapper > table tr td:last-child,
.dataTables_wrapper > .dataTablesContentWrapper > table tr th:last-child {
}

.filterSuppress {
    display: none;
}

.colVisSuppress {
    display: none;
}

.dataTables_wrapper div.fg-toolbar:last-child, .dataTables_wrapper th.fg-toolbar {
    border-top: solid 1px #dadada;
    margin-top: -1px;
    background: #fff;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    color: #737373;
}

.dataTables_wrapper th {
}

.dataTables_wrapper tbody tr  {
}

.dataTables_wrapper > table.contentTable {
    border-right: 1px solid #bbb;
}
.dataTables_wrapper table.hasListTableHeader {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}
.dataTables_wrapper .listTableFooter {
}
.dataTables_wrapper a:not(.btn):not(.page-link) {
    text-decoration: none;
    color: inherit;
}
.dataTables_wrapper td a:hover,
.dataTables_wrapper td a:focus {
}
.dataTables_wrapper .dataTablesContentWrapper td .fa {
}
#lteIE8 .dataTables_wrapper a {

}
.dataTable_icon_align + .dataTable_icon_align {
    margin: 0 6px;
}
.dataTables_wrapper th div {
}
.DataTables_sort_wrapper {
    text-align: center;
}
.drillDownToggleContainer .DataTables_sort_wrapper, .DataTables_sort_icon {
    display: none;
}
.drillDownToggleContainer {
    width: 1%;
}

.dataTables_wrapper table.dataTable_borderless, .dataTables_wrapper table.dataTable_borderless TD,
table.dataTables_wrapper .dataTable_borderless TR {
    border: none;
    padding: 0;
}

.dataTables_wrapper .dataTableGraphicIcon {
    max-width: 300px;
    max-height: 50px;
}

.dataTables_wrapper .dataTableLink {
    color: inherit;
}

.dataTables_wrapper .dataTableSecondaryLink {
}

.dataTables_wrapper .dataTableLink + .fa,
.dataTables_wrapper .dataTableSecondaryLink + .fa {
}

.dataTables_wrapper .dataTableErrorLink {
    color: #a21931;
}
.dataTables_wrapper .dataTableDisabledLink {
    color: #aaa;
}
.dataTables_wrapper .dataTableSuppressedLink {
    text-decoration: line-through;
}

.dataTables_wrapper .dataTables_length {
}

.dataTables_wrapper .dataTables_length table {
    height: 56px;
}

.dataTables_wrapper .dataTables_length tr td {
    border: none;
    font-size: 12px;
}

.dataTables_wrapper .dataTables_length tr > .entriesToggle {
    border-left: 1px solid #dadada;
    padding: 0 7px 0 8px;
    cursor: pointer;
}

.dataTables_wrapper .dataTables_length tr > .entriesToggle:hover,
.dataTables_wrapper .dataTables_length tr > .entriesToggle:focus {
    background: #f5f5f5;
}

.dataTables_wrapper .dataTables_paginate {
}
.dataTables_wrapper #allCheck {
    position: relative;
    left: -12px;
    top: -2px;
}
.dataTables_wrapper .dataTables_paginate .ui-state-default {
    background: none;
}
.dataTablesSearchMatch {
    background-color: #FFFF00;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}
.dataTables_wrapper .non-default td {
    white-space: normal;
}

.dataTables_wrapper .non-default td .fa + .fa {
}

.dataTables_wrapper table.async td,
.dataTables_wrapper table.async th {
    white-space: nowrap;
}

.dataTables_wrapper table.single-select tr.odd.row_selected,
.dataTables_wrapper table.single-select tr.even.row_selected,
.dataTables_wrapper table.single-select tr.odd:hover,
.dataTables_wrapper table.single-select tr.even:hover,
.dataTables_wrapper table.multi-select tr.odd.row_selected,
.dataTables_wrapper table.multi-select tr.even.row_selected,
.dataTables_wrapper table.multi-select tr.odd:hover,
.dataTables_wrapper table.multi-select tr.even:hover,
.dataTables_wrapper table.multi-select tr.drill-down td {
}

.dataTables_wrapper table.single-select tr.odd.row_selected,
.dataTables_wrapper table.single-select tr.even.row_selected,
.dataTables_wrapper table.multi-select tr.odd.row_selected,
.dataTables_wrapper table.multi-select tr.even.row_selected {
    background: #ffffdc;
}

/* Prinova custom: Table Tools */
.dataTables_wrapper .tableToolsContainer {
    position: absolute;
    opacity: 0.70;
}
.dataTables_wrapper .tableToolsContainer:hover {
    opacity: 1;
}
.dataTables_wrapper .dataTables_transparent {
    opacity: 0;
}
.dataTables_wrapper .DTTT_button {
    display: block;
    float: none;
}
.dataTables_wrapper .tableToolsContainer, .DTTT_collection {
    font-size: 10px;
}
.dataTables_wrapper .DTTT_button, .DTTT_collection .DTTT_button {
    border-color: #bbb;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    background: url('images/dataTable_secondary_header_grad.gif') repeat-x center top;
    height: 12px;
}
.DTTT_collection .DTTT_button {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}
.dataTables_wrapper .DTTT_button:hover, .DTTT_collection .DTTT_button:hover {
    border-color: #2a2a2a;
}
.dataTables_wrapper .DTTT_button:hover span, .DTTT_collection .DTTT_button:hover span {
    color: #666;
}

.dataTables_wrapper .DTTT_button:first-child {
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
    border-bottom: none;
}
.dataTables_wrapper .DTTT_button:last-child {
    -webkit-border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    border-radius: 0 0 3px 3px;
    border-top: none;
}


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * END Prinova custom
 */

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * DataTables features
 */

.dataTables_wrapper {
    position: relative;
    clear: both;
    zoom: 1; /* Feeling sorry for IE */
}

/*
IE8 autowidth bug fix
*/
.col-vis-width {
    width: 100%;

}

.col-vis-height {
    height: 29px;
}


/*
Datatables Rows
*/
.contentObjectIdContext:hover {
    /*background-color: #d3e1ff !important;
    cursor: pointer;
    *cursor: hand;*/
}

.rowIdContext:hover {
    border: 1px solid #eee !important;
    /*cursor: pointer;*/
    /**cursor: hand;*/
}

.rowSelected {
    /*background-color: #ebecfe !important;*/
}

div.innerDetails { display: none; margin-bottom: 10px; }

.feature-checkmark-icon {
    background-clip: border-box;
    background-origin: padding-box;
    background: transparent url('images/feature_checkbox.gif') no-repeat scroll center center;
    height: 12px;
}


.css_right {
    float: right
}




/*
Datatables DrillDown
*/
.drilldown {
    padding: 0 0 25px;
    width: 100%;
}
.block {
    display: inline;
    float: left;
    margin: 5px 1px 0 0;
    position: relative;
    width: 209px;
}
.drilldownheader {
    border-bottom: 1px solid #e1e1e1;
    font-size: 10px;
}
.drilldownimg {
    background-size: 50%;
    height: 70px;
}
.drilldowntext {
    font-size: 10px;
}
.drilldown2 {
    background: none repeat scroll 0 0 #F0F0EB;
    width: 25%;
}
.drilldown3 {
    background: none repeat scroll 0 0 #eaeaea;
    width: 25%;
}
.drilldown4 {
    background: none repeat scroll 0 0 #E8E8E2;
    width: 25%;
}
.drilldown1 {
    background: none;
    width: 1%;
}
tr.drilldown td {
    background: #fdfdfd;
}
.block-frame {
    min-height: 288px;
    padding: 12px 0 0;
}

.drill-down-icon {
    -webkit-transition: -webkit-transform .1s linear;
    transition: transform .1s linear;
}

.drill-down-close-icon {
    transform: rotate(90deg);
}


/*
Datatables Row Grouping
*/
td.group {
    background-color: #eeeeee;
    border-bottom: 1px solid #A19B9E;
    border-top: 1px solid #A19B9E;
}

td.details {
    background-color: #d1cfd0;
    /*border: 2px solid #A19B9E;*/
}

.expanded-group {
    background-clip: border-box;
    background-origin: padding-box;
    background: transparent no-repeat scroll 10px 1px;
    height: 20px;
    padding-left: 35px !important;
}
.collapsed-group {
    background-clip: border-box;
    background-origin: padding-box;
    background: transparent no-repeat scroll 10px 1px;
    height: 20px;
    padding-left: 35px !important;
}



/*
Dynamic Nav
*/
.dynamic-column {
    width: 720px;
    float: left;
    padding-bottom: 100px !important;
    /*padding-bottom: 1px !important;*/
    /*border: 1px solid white;*/
    border: none;
}
.portlet {
    margin: 0 0 0 0;
}
.ui-sortable-placeholder {
    border: 1px dotted black;
    visibility: visible !important;
    height: 50px !important;
    background-color: #eee;
}
.ui-sortable-placeholder * {
    visibility: hidden;
}







.dataTables_processing {
/*    position: absolute;
    top: 50%;
    left: 50%;
    width: 175px;
    height: 25px;
    margin-left: -87px;
    margin-top: -5px;
    padding: 14px 0 2px 0;
    border: 1px solid #ddd;
    text-align: center;
    color: #2a2a2a;
    font-size: 14px;
    background-color: white;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    z-index: 49;*/
}

.dataTables_filter {
    width: 50%;
    float: right;
    text-align: right;
    /* PRINOVA - TEMP POS */
    padding: 5px 20px 5px 5px;
}

.dataTables_info {
}

.dataTables_paginate {
}

/* Pagination nested */
.paginate_disabled_previous, .paginate_enabled_previous,
.paginate_disabled_next, .paginate_enabled_next {
    height: 19px;
    float: left;
    cursor: pointer;
    *cursor: hand;
    color: #111 !important;
}
.paginate_disabled_previous:hover, .paginate_enabled_previous:hover,
.paginate_disabled_next:hover, .paginate_enabled_next:hover {
    text-decoration: none !important;
}
.paginate_disabled_previous:active, .paginate_enabled_previous:active,
.paginate_disabled_next:active, .paginate_enabled_next:active {
    outline: none;
}

.paginate_disabled_previous,
.paginate_disabled_next {
    color: #666 !important;
}
.paginate_disabled_previous, .paginate_enabled_previous {
    padding-left: 23px;
}
.paginate_disabled_next, .paginate_enabled_next {
    padding-right: 23px;
    margin-left: 10px;
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * DataTables display
 */
table.display {
}

table.display thead th {
}

table.display thead th.sorting_disabled {
}

table.scroll-er thead th {
    white-space: nowrap;
}

table.non-default thead th {
}

.actionsBarContainer + .dataTables_wrapper table.non-default thead th,
.actionsBarFilterContainer + .dataTables_wrapper table.non-default thead th {
    border-top: 0;
}

table.non-default thead .dataTable_header_icon {
    font-size: 14px;
}

table.non-default thead th .ui-icon {
    position: absolute;
    top: 10px;
    right: 16px;
}

table.non-default thead th.drillDownToggleContainer {
    border-right: 0;
}

table.scroll-er thead th {
    /*style from designer*/
    padding: 2px 17px 2px 8px;
    font-weight: bold;
    cursor: pointer;
    * cursor: hand;
    text-align: center;
}

table.display tfoot th {
    padding: 3px 18px 3px 10px;
    border-top: 1px solid black;
    font-weight: bold;
}

table.display tr.heading2 td {
    border-bottom: 1px solid #aaa;
}

table.display td {
}

table.display td.center {
}



/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * DataTables sorting
 */

th:active {
    outline: none;
}




/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * DataTables row classes
 */
table.display tr.odd.gradeA {
    background-color: #ddffdd;
}

table.display tr.even.gradeA {
    background-color: #eeffee;
}

table.display tr.odd.gradeC {
    background-color: #ddddff;
}

table.display tr.even.gradeC {
    background-color: #eeeeff;
}

table.display tr.odd.gradeX {
    background-color: #ffdddd;
}

table.display tr.even.gradeX {
    background-color: #ffeeee;
}

table.display tr.odd.gradeU {
    background-color: #ddd;
}

table.display tr.even.gradeU {
    background-color: #eee;
}


table.non-default tr.odd,
tr.odd,
td.odd {
}

table.non-default tr.even,
tr.even,
td.even {
}


/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * Misc
 */

.dataTables_scroll {
    clear: both;
}

.dataTables_scrollBody {
    *margin-top: -1px;
}

.top, .bottom {
    padding: 15px;
    background-color: #F5F5F5;
    border: 1px solid #CCCCCC;
}

.top .dataTables_info {
    float: none;
}

.clear {
    clear: both;
}

.dataTables_empty {
    text-align: center;
}

tfoot input {
    margin: 0.5em 0;
    width: 100%;
    color: inherit;
}

tfoot input.search_init {
    color: #2a2a2a;
}








.example_alt_pagination div.dataTables_info {
    width: 40%;
}

.paging_full_numbers {
}

.paging_full_numbers .fg-button {
    display: inline-block;
    width: 32px;
    height: 32px;
    margin-left: 4px;
    line-height: 32px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 0;
    text-align: center;
    font-size: 18px;
    color: #737373;
    cursor: pointer;
}

.paging_full_numbers span > .fg-button {
    font-size: 14px;
}

.paging_full_numbers span > .ellipsis {
    padding: 0 4px;
    font-size: 12px;
}

.paging_full_numbers .fg-button:not(.fg-current):hover,
.paging_full_numbers .fg-button:not(.fg-current):focus {
    background: #d6c7de;
}

.paging_full_numbers .fg-button:not(.fg-current):active {
    background: #f1edf4;
}

.paging_full_numbers .fg-button.fg-current,
.paging_full_numbers .fg-button.fg-disabled {
    cursor: not-allowed;
}

.paging_full_numbers .fg-button.fg-current {
    cursor: default;
}

.paging_full_numbers .fg-button.fg-disabled {
    color: #dadada;
}

.paging_full_numbers .fg-button.fg-disabled:hover,
.paging_full_numbers .fg-button.fg-disabled:focus,
.paging_full_numbers .fg-button.fg-disabled:active {
    background: none;
}

.paging_full_numbers .fg-button.fg-current {
    background: #d6c7de;
    color: #6d3075;
}

.paging_full_numbers a:active {
    outline: none
}

.paging_full_numbers a:hover {
    text-decoration: none;
}

.paging_full_numbers a.paginate_button,
.paging_full_numbers a.paginate_active {
    border: 1px solid #aaa;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    padding: 2px 5px;
    margin: 0 3px;
    cursor: pointer;
    *cursor: hand;
    color: #333 !important;
}

.paging_full_numbers a.paginate_button {
    background-color: #ddd;
}

.paging_full_numbers a.paginate_button:hover {
    background-color: #ccc;
    text-decoration: none !important;
}

/*
 * Sorting classes for columns
 */
/* For the standard odd/even */
tr.odd td.sorting_1 {
}

tr.odd td.sorting_2 {
    background-color: #DADCFF;
}

tr.odd td.sorting_3 {
    background-color: #E0E2FF;
}

tr.even td.sorting_1 {
}

tr.even td.sorting_2 {
    background-color: #F2F3FF;
}

tr.even td.sorting_3 {
    background-color: #F9F9FF;
}




/* For the Conditional-CSS grading rows */
/*
 	Colour calculations (based off the main row colours)
  Level 1:
		dd > c4
		ee > d5
	Level 2:
	  dd > d1
	  ee > e2
 */
tr.odd.gradeA td.sorting_1 {
    background-color: #c4ffc4;
}

tr.odd.gradeA td.sorting_2 {
    background-color: #d1ffd1;
}

tr.odd.gradeA td.sorting_3 {
    background-color: #d1ffd1;
}

tr.even.gradeA td.sorting_1 {
    background-color: #d5ffd5;
}

tr.even.gradeA td.sorting_2 {
    background-color: #e2ffe2;
}

tr.even.gradeA td.sorting_3 {
    background-color: #e2ffe2;
}

tr.odd.gradeC td.sorting_1 {
    background-color: #c4c4ff;
}

tr.odd.gradeC td.sorting_2 {
    background-color: #d1d1ff;
}

tr.odd.gradeC td.sorting_3 {
    background-color: #d1d1ff;
}

tr.even.gradeC td.sorting_1 {
    background-color: #d5d5ff;
}

tr.even.gradeC td.sorting_2 {
    background-color: #e2e2ff;
}

tr.even.gradeC td.sorting_3 {
    background-color: #e2e2ff;
}

tr.odd.gradeX td.sorting_1 {
    background-color: #ffc4c4;
}

tr.odd.gradeX td.sorting_2 {
    background-color: #ffd1d1;
}

tr.odd.gradeX td.sorting_3 {
    background-color: #ffd1d1;
}

tr.even.gradeX td.sorting_1 {
    background-color: #ffd5d5;
}

tr.even.gradeX td.sorting_2 {
    background-color: #ffe2e2;
}

tr.even.gradeX td.sorting_3 {
    background-color: #ffe2e2;
}

tr.odd.gradeU td.sorting_1 {
    background-color: #c4c4c4;
}

tr.odd.gradeU td.sorting_2 {
    background-color: #d1d1d1;
}

tr.odd.gradeU td.sorting_3 {
    background-color: #d1d1d1;
}

tr.even.gradeU td.sorting_1 {
    background-color: #d5d5d5;
}

tr.even.gradeU td.sorting_2 {
    background-color: #e2e2e2;
}

tr.even.gradeU td.sorting_3 {
    background-color: #e2e2e2;
}


/*
 * Row highlighting example
 */
.ex_highlight #example tbody tr.even:hover, #example tbody tr.even td.highlighted {
    background-color: #ECFFB3;
}

.ex_highlight #example tbody tr.odd:hover, #example tbody tr.odd td.highlighted {
    background-color: #E6FF99;
}

.ex_highlight_row #example tr.even:hover {
    background-color: #ECFFB3;
}

.ex_highlight_row #example tr.even:hover td.sorting_1 {
    background-color: #DDFF75;
}

.ex_highlight_row #example tr.even:hover td.sorting_2 {
    background-color: #E7FF9E;
}

.ex_highlight_row #example tr.even:hover td.sorting_3 {
    background-color: #E2FF89;
}

.ex_highlight_row #example tr.odd:hover {
    background-color: #E6FF99;
}

.ex_highlight_row #example tr.odd:hover td.sorting_1 {
    background-color: #D6FF5C;
}

.ex_highlight_row #example tr.odd:hover td.sorting_2 {
    background-color: #E0FF84;
}

.ex_highlight_row #example tr.odd:hover td.sorting_3 {
    background-color: #DBFF70;
}


/*
 * KeyTable
 */
table.KeyTable td {
    border: 3px solid transparent;
}

table.KeyTable td.focus {
    border: 3px solid #3366FF;
}

table.display tr.gradeA {
    background-color: #eeffee;
}

table.display tr.gradeC {
    background-color: #ddddff;
}

table.display tr.gradeX {
    background-color: #ffdddd;
}

table.display tr.gradeU {
    background-color: #ddd;
}

div.box {
    height: 100px;
    padding: 10px;
    overflow: auto;
    border: 1px solid #8080FF;
    background-color: #E5E5FF;
}



.white-background {
    background-color: white;
}





/*
Data Model Application
*/
.data-model {
    margin-top: 2px;
    padding-bottom: 5px;
}
table.data-source-info td {
    padding: 3px 2px;
}
table.data-records-info td {
    padding: 3px 2px;
}
table.data-elements-info td {
    padding: 3px 12px;
}
.data-record-table-header {
    color: #555555;
    padding: 5px 0 2px 10px;
    font-weight: bold;
    display: inline-block;
    float: left;
    zoom: 1; /*IE6 and IE7*/
    *display: inline; /*IE6 and IE7*/
    *clear: both; /*IE6 and IE7*/
}
.data-record-table-header2 {
    color: #555555;
    padding: 1px 0 0 0;
    font-weight: bold;
    display: inline-block;
    float: right;
    zoom: 1; /*IE6 and IE7*/
    *display: inline; /*IE6 and IE7*/
    *clear: both; /*IE6 and IE7*/
}
.data-element-table-header {
    color: #555555;
    padding: 5px 0 2px 10px;
    font-weight: bold;
    text-align: left !important;
}
.header-with-sort {
    margin-left: 17px;
    height: 0;
    /*display: inline;*/
    white-space: nowrap;
}
.header-with-sort2 {
    *float: left; /*IE6 and IE7*/
    display: inline;
    height: 0;
    margin-top: 0;
    vertical-align: top;
}
table.non-default div.column-header {
    /* styles from designer */
    white-space: nowrap;
}
table.scroll-er div.column-header {
    *float: left; /*IE6 and IE7*/
    display: inline;
    text-align: center;
    /* styles from designer */
}
.column-header {
}

table.non-default td {
}

table.non-default td.drillDownToggleContainer {
}

table.non-default > tbody > tr > .drillDownToggleContainer {
}

table.non-default th {
}

table.non-default td.drillDown-cell-merge, table.non-default th.drillDown-cell-merge {
}

table.non-default td.drillDown-connected-cell {
    border-bottom: none;
}

.data-source-box {
    border: 1px solid #d4d4d4;
    background-color: #f8f8f8;
    border-radius: 10px;
    padding: 3px;
    margin-top: 8px;
}
.iframe-box {
    border: 1px solid #eee;
    background-color: #eee;
    border-radius: 10px;
    padding: 0;
}
.ds-right {
    display: inline-block;
    zoom: 1; /*IE6 and IE7*/
    *display: inline; /*IE6 and IE7*/
}
.ds-left {
    display: inline-block;
    zoom: 1; /*IE6 and IE7*/
    *display: inline; /*IE6 and IE7*/
}
.ds-info-iframe {
    background-color: #ffffff;
}
.dr-info-iframe {
    background-color: #ffffff;
    padding: 2px;
}
.de-info-iframe {
    background-color: #ffffff;
}
.center {
}
.variable-header {
    background-color: #eeeeee;
    font-weight: bold;
    color: #555555;
    padding-left: 15px !important;
    text-align: left;
}

/*
DataTable ListHeader title
*/
.non-default-dt-table-header {
    *float: left; /*IE6 and IE7*/
    /*display: inline;*/
    /* styles from designer */
    color: #414141;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin: 5px 0 5px 10px;

    /*disable text highlighting/selection for the table header text*/
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    top: 3px;
    position: relative;

    display: inline;
}

.default-dt-table-header {
    *float: left; /*IE6 and IE7*/
    display: inline;
}

div.dr-info-iframe td {
    /* styles from designer */
    font-size: 12px;
    letter-spacing: 0.25px;
    font-weight: 400;
    color: #333;
}

div.de-info-iframe td {
    /* styles from designer */
    font-size: 12px;
    letter-spacing: 0.25px;
    font-weight: 400;
    color: #333;
}

/*async table context menu*/
.disabled {
    color: gray;
}

/* Tabe sorting icons */

table > thead > tr > th .ui-icon.ui-icon-triangle-1-n,
table > thead > tr > th .ui-icon.ui-icon-triangle-1-s,
table > thead > tr > th .ui-icon.ui-icon-carat-2-n-s {
    width: 6px;
    height: 18px;
    background-image: none;
    font: normal normal normal 10px/1 FontAwesome;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-indent: inherit;
}

table > thead > tr > th .ui-icon-triangle-1-n::before,
table > thead > tr > th .ui-icon-carat-2-n-s::before {
    content: "\f106";
    display: block;
    height: 8px;
}

table > thead > tr > th .ui-icon-triangle-1-s::before,
table > thead > tr > th .ui-icon-carat-2-n-s::after {
    content: "\f107";
    display: block;
    height: 8px;
}

table > thead > tr > th .ui-icon-triangle-1-n::before,
table > thead > tr > th .ui-icon-triangle-1-s::before {
    margin-top: 4px;
}

