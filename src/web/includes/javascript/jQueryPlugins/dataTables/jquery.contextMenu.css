/* Generic context menu styles */

.contextMenuContainer {
    position: relative;
    background: #fff;
    margin: 8px 8px 8px;
    z-index: 9999;
    padding: 8px 0;
    border: 0;
    border-radius: 3px;
    -moz-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

.contextMenuContainer::before {
    content: "\00a0";
    position: absolute;
    top: -8px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    z-index: 9999;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.contextMenuContainer.rightPosition::before {
    top: 12px;
    left: -8px;
    margin-left: 0;
    border-left: 0;
    border-right: 8px solid #fff;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

.contextMenuContainer.windowMenu {
    margin: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.contextMenu {
    position: absolute;
    min-width: 130px;
    z-index: 99999;
    background: #fff;
    padding: 6px 0;
    margin: 0 8px;
    text-align: left;
    border-radius: 3px;
    -moz-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

.contextMenuContainer .contextMenu {
    position: static;
    padding: 0;
    margin: 0;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.contextMenuHeader {
    position: relative;
    background: none;
    height: auto;
    border-bottom: 1px solid #dadada;
    margin: 0 0 8px;
    padding: 0 0 8px;
    text-align: left;
    color: #6d3075;
}

.contextMenuHeaderText {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 20px 8px;
    line-height: 14px;
}

.contextMenu li {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contextMenu a,
.contextMenu .itemText {
    display: block;
    font-size: 12px;
    text-decoration: none;
    line-height: 32px;
    padding: 0 18px;
    color: inherit;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
}

/* FB24698 - Windows issue with unknown text color cascade - !important resolves */
.contextMenu a.disabled,
.contextMenu .itemText.disabled {
    color: #a2a2a2 !important;
}

.contextMenu li.contextMenuCategory {
    display: block;
    font-size: 12px;
    font-weight: 600;
    line-height: 32px;
    padding: 0 18px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #444;
}

.contextMenuLabel {
    font-size: 12px;
}

.contextMenu li:not(.contextMenuCategory) a:not(.disabled).hover,
.contextMenu li:not(.contextMenuCategory) a:not(.disabled):hover {
    background: #6d3075;
    color: #FFF;
}

.contextMenu li:not(.contextMenuCategory) a.disabled.hover,
.contextMenu li:not(.contextMenuCategory) a.disabled:hover {
    cursor: not-allowed;
}

.contextMenu li:not(.contextMenuCategory).hover a,
.contextMenu li:not(.contextMenuCategory):hover a,
.contextMenu li:not(.contextMenuCategory).hover .itemText,
.contextMenu li:not(.contextMenuCategory, .contextMenuItem_selected):hover .itemText {
    color: #fff;
}

.contextMenu .contextMenuItem_selected a,
.contextMenu .contextMenuItem_selected .itemText {
    color: #6d3075;
}

.contextMenu .contextMenuItem:hover .itemText {
    color: #fff;
}

.contextMenu .contextMenuItem:hover {
    background: #6d3075;
}

.contextMenu li.disabled a {
    color: #444;
    opacity: 0.5;
    cursor: default;
}

.contextMenu li:not(.contextMenuCategory).hover.disabled a,
.contextMenu li:not(.contextMenuCategory).disabled:hover a{
    background-color: transparent;
}

.contextMenu li.separator {
    margin: 8px 0;
    border-top: solid 1px #dadada;
}