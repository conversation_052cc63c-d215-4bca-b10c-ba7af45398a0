var singleSelect = false;
var multiSelect = false;
var iFrameAction;  		// Function placeholder
var customContextJSFunction;  	// Function placeholder
var customCallback;  	// Function placeholder

// Init Javascript
$( function() {
	initContextMenu();
}); //end init JS

function initContextMenu() {

	var validContextMenu = $('ul.contextMenu');
	var validTable = $('table.display');

	if (validContextMenu !== null) {
		if ($('ul.contextMenu').length) {
			
			if (validTable !== null) {

				$('ul.contextMenu').each(function() {

					var cmWrapper = $(this).parent();
					
					$('table.display').each(function() {
						if (isTableType(this, 'single-select'))
							singleSelect = true;
						if (isTableType(this, 'multi-select'))
							multiSelect = true;
						
						var dtWrapper = $(this).parent();
						
						if (singleSelect || multiSelect)
						{

							var tableId = $(dtWrapper).find('table.dataTable').attr('id');
							var cmId = $(cmWrapper).attr('id');

							if ( parseId(cmId) == tableId ) {
								
								$('tbody:first > tr', this).each(function() {
									
									if ( $(this).find('.dataTables_empty').length > 0 )
										return;

									$(this).contextMenu({
								    	menu: "contextMenuContainer_"+parseId(cmId)
									},
									
									function(entry, action, el, pos) {
									    var anSelected = fnGetSelected( el );
									    
									    for (var i = 0; i < anSelected.length; i++)
									    	var iRow = anSelected[i].rowIndex;

									    var contextMenuEntry = entry,
                                        	actionType = action.split(':')[0];

									    if (multiSelect || singleSelect && action.indexOf(':') > -1) {

									    	//make sure that a row is selected and the entry is not disabled
									    	if ($(el).hasClass(rowSelected) && !$(contextMenuEntry).hasClass('disabled')) {
										    	var actionId = action.split(':')[1];

										    	if (actionType == "infoAction") {
													infoItemAction(actionId, -1);
												}
										    	else if (actionType == "submitAction") {
													submitAction(actionId, null);
												}
										    	else if (actionType == "actionSelected") {
													actionSelected(actionId);
												}
										    	else if (actionType == "iFrameAction") {
													if ($.isFunction(iFrameAction))
														iFrameAction(actionId, contextMenuEntry);
												}
												else if (actionType == "customContextJSFunction"){
													customContextJSFunction(actionId);
												}
									    	}

									    } else if (singleSelect) {

										    if ($(el).hasClass(rowSelected)) {

												var token = gup('tk');	
												
												var dsid = gup('dsid');
												
												if (dsid == "")
												{
													//hack code: will have to change this later...
													//this is used for the case when there is no dsid found in the url
													//due to either no data source selection or first data source use
													//better soln: append the first found datasource id to the url param in the controller
													var url = $(el).attr("iframesrc");
													dsid = gupfcu('dsid', url);		
												}
												//console.log('dsid: ' + dsid);							
												
											    var obj_id = $(el).attr("id");
											    //console.log('obj_id: ' + obj_id);
											    	
											    var ifp_obj = {};
											    ifp_obj.closeBtnId = "cancelBtn_button";
											    ifp_obj.onSave = customCallback;
											    ifp_obj.closeBeforeOnSave = true;

												//hack code: will have to change this later...
											    if (action == 'updaterecord') {
											    	//console.log('---action: update record---');
											    	ifp_obj.width = 800;
												    ifp_obj.height = 200;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.edit_data_record;
											    	ifp_obj.src = "data_record_edit.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.drid = obj_id;
											    	ifp_params_obj.tk = token;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;

											    	$(contextMenuEntry).iFramePopup(ifp_obj);

										    	} else if (action == 'deleterecord') {
											    	//console.log('---action: delete record---');
											    	ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.delete_deta_record;
											    	ifp_obj.src = "data_record_delete.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.drid = obj_id;
											    	ifp_params_obj.tk = token;

											    	ifp_params_obj.parameter = "drid";
											    	ifp_params_obj.successParameter = "dsid";
											    	ifp_params_obj.cancelView = "datarecords.jsp";
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;

											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
										    	} else if (action == 'insertelement') {
								    				//console.log('---action: insert element---');
								    				ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.insert_data_element;
											    	ifp_obj.src = "data_element_edit.form";
											    	
											    	//must do this for IE7/8 only, since 'new' is a taken operator
											    	var ifp_params_obj = {
											    			"new"	:	"true"
											    	};
											    	
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.drid = obj_id;					    	
											    	ifp_params_obj.tk = token;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;

											    	$(contextMenuEntry).iFramePopup(ifp_obj);
							    				} else if (action == 'updateelement') {
								    				//console.log('---action: update element---');
								    				ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.edit_data_element;
											    	ifp_obj.src = "data_element_edit.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	
											    	var drid = gup('drid');
													
											    	ifp_params_obj.drid = drid;
											    	ifp_params_obj.deid = obj_id;
											    	ifp_params_obj.tk = token;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;				    	
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);

							    				} else if (action == 'deleteelement') {
								    				//console.log('---action: delete element---');
								    				ifp_obj.width = 800;
												    ifp_obj.height = 100;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.delete_data_element;
											    	ifp_obj.src = "data_element_delete.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	
											    	var drid = gup('drid');
													
													ifp_params_obj.drid = drid;
											    	ifp_params_obj.deid = obj_id;
											    	ifp_params_obj.tk = token;

											    	ifp_params_obj.parameter = "deid";
											    	ifp_params_obj.successParameter = "drid";
											    	ifp_params_obj.cancelView = "datarecords.jsp";
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;

											    	$(contextMenuEntry).iFramePopup(ifp_obj);

							    				} else if (action == 'updatexmldatatag') {
											    	//console.log('---action: update xml data tag---');
											    	ifp_obj.width = 800;
												    ifp_obj.height = 330;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.edit_data_tag;
											    	ifp_obj.src = "xml_data_tag_definition_edit.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.xdtid = obj_id;
											    	ifp_params_obj.tk = token;
											    	ifp_params_obj.action=action;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
											    } else if (action == 'deletexmldatatag') {
											    	ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.delete_data_tag;
											    	ifp_obj.src = "xml_data_tag_definition_delete.form";
											    	
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.xdtid = obj_id;
											    	ifp_params_obj.tk = token;
											    	ifp_params_obj.parameter = "xdtid";
											    	ifp_params_obj.successParameter = "dsid";
											    	ifp_params_obj.cancelView = "datasources.form";
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
											    } else if (action == 'insertxmldatatag') {
											    	//console.log('---action: update xml data tag---');
								    				ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.add_xml_data_tag;
											    	ifp_obj.src = "xml_data_tag_definition_edit.form";
											    	var ifp_params_obj = {
											    			"new"	:	"true"
											    	};
											    	
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.parentid = obj_id;
											    	ifp_params_obj.tk = token;
											    	ifp_params_obj.action=action;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
											    } else if (action == 'uploadxmldatatags') {
											    	//console.log('---action: upload xml data tags---');
								    				ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.insert_data_tag;
											    	ifp_obj.src = "xml_data_tag_upload_edit.form";
											    	var ifp_params_obj = {
											    			"new"	:	"true"
											    	};
											    	
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.parentid = obj_id;
											    	ifp_params_obj.tk = token;
											    	ifp_params_obj.action=action;
											    	
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
											    } else if (action == 'updatexmldataelement' || action == 'updatexmlattribute') {
											    	//console.log('---action: update xml data tag---');
											    	ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
												    ifp_obj.src = "xml_data_element_edit.form";
											    	var xdtid = gup('xdtid');
											    	var ifp_params_obj = {};
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.xdtid = xdtid;
											    	ifp_params_obj.deid = obj_id;
											    	ifp_params_obj.tk = token;
											    	if(action == 'updatexmldataelement') {
												    	ifp_obj.title = client_messages.title.edit_data_xml_element;
												    	ifp_params_obj.isAttributeDataElement="false";
												    } else {
												    	ifp_obj.title = client_messages.title.edit_attribute;
												    	ifp_params_obj.isAttributeDataElement="true";
												    }
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
											    } else if (action == 'deletexmldataelement') {
											    	ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.title = client_messages.title.delete_data_xml_element;
											    	ifp_obj.src = "xml_data_element_delete.form";
											    	var ifp_params_obj = {};
											    	var xdtid = gup('xdtid');
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.xdtid = xdtid;
											    	ifp_params_obj.deid = obj_id;
											    	ifp_params_obj.tk = token;
											    	
											    	ifp_params_obj.parameter = "deid";
											    	ifp_params_obj.successParameter = "xdtid";
											    	ifp_params_obj.cancelView = "datasources.form";
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
											    	
										    	} else if (action == 'insertxmldataelement' || action == 'insertxmlattribute') {
								    				ifp_obj.width = 800;
												    ifp_obj.displayOnInit = true;
											    	ifp_obj.src = "xml_data_element_edit.form";
											    	
											    	//must do this for IE7/8 only, since 'new' is a taken operator
											    	var ifp_params_obj = {
											    			"new"	:	"true"
											    	};
											    	
											    	ifp_params_obj.dsid = dsid;
											    	ifp_params_obj.xdtid = obj_id;					    	
											    	ifp_params_obj.tk = token;
											    	if(action == 'insertxmlattribute'){
											    		ifp_params_obj.isAttributeDataElement="true";
											    		ifp_obj.title = client_messages.title.insert_attribute;
											    	} else {
											    		ifp_params_obj.isAttributeDataElement="false";
											    		ifp_obj.title = client_messages.title.insert_data_element;
											    	}
											    	ifp_obj.appliedParams = ifp_params_obj;
											    	  	
											    	$(contextMenuEntry).iFramePopup(ifp_obj);
				
							    				}
									    	}//end single-select rowselected
									    }//end single-select If
									    
									}); //end CM init
								});
								
							}
						}

						//$(this).disableContextMenu();
					}); //end multiple instance Table
				}); //end multiple instance CM
			} //end null Table
		}//end null CM
	}//end null CM	
}

function hideDisabledContextMenus(){
	$(".contextMenu a.disabled").each(function (){
		$(this).hide();
	});

	// Hide the line separators
	$('li.separator').each(function(){
		let prevAllHidden = true, nextAllHidden = true;
		$(this).prevUntil('li.separator').each(function(){
			if($(this).find('a').css("display") !== "none" &&
				$(this).find('a').attr('id') !== 'separator'){
					prevAllHidden = false;
			}
		});
		$(this).nextAll('li').each(function(){
			if($(this).find('a').css("display") !== "none" &&
				$(this).find('a').attr('id') !== 'separator' &&
				!$(this).hasClass('separator')){
					nextAllHidden = false;
			}
		});

		if(prevAllHidden || nextAllHidden){
			$(this).hide();
			if($(this).prev('li').find('a').attr('id') === 'separator'){
				$(this).prev('li').hide();
			}
		}else{
			$(this).show();
			if($(this).prev('li').find('a').attr('id') === 'separator') {
				$(this).prev('li').show();
			}
		}
	});
}





