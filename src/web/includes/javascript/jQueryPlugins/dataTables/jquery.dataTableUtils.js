//DataTables Plugin PoC/Test
var rowSelected = 'row_selected';
var anOpen = [];
var listItemClickHandler; 		// Function placeholder
var headerLabelOverride; 		// Function placeholder
var validateActionReq; 			// Function placeholder
var postListRenderFlagInjection;// Function placeholder
var postListDrawCallback;		// Function placeholder

var isDataTableInitComplete = false;
var adjustIFrameHeightOverridden = false;

var dataTableInitCallbacks = [];
var search_timer = null;

$(function () {
    $('body').append('<div style="display: none;" id="data-table-load-complete" value="false" />');

    $('table.display').each(function () {
        var currentTable = this;

        setTimeout( function() {

            if (!$(currentTable).hasClass('dataTable')) {
                initDataTable(currentTable);

            }
        }, 100 );
    });
});

/*************************************************************************
 DATATABLES PLUGIN INITIALIZATION
 *************************************************************************/
function initDataTable(nTable) {
    var tablePropAttr = {};
    var instanceAttr = {};
    var standardTableOptions = {};

    var currentTable = nTable;
    var $currentTable = $(nTable);
    var currentDatatable;
    var tableId = $(currentTable).attr('id');

    if (!isTableType(currentTable, 'display'))
        return;

    if ($('#listSearchInput, #listSearchInput_' + tableId).attr('autocomplete') == undefined)
        $('#listSearchInput, #listSearchInput_' + tableId).attr('autocomplete', 'off');

    var paginationInfo = client_messages.text.showing + '<span class="entriesRangeCountText"><b class=\"DataTables_start_index\">_START_</b> ' + client_messages.text.range_to + ' <b id="' + tableId + '_end_item" class=\"DataTables_end_index\">_END_</b> ' + client_messages.text.of + ' <b id="' + tableId + '_total">_TOTAL_</b> ' + client_messages.text.entries + '.</span>';

    if (isTableType(currentTable, 'lengthSelect'))
        paginationInfo = '<label class="entriesRangeCountText m-0">' + client_messages.text.showing + ' <b class=\"DataTables_start_index\">_START_</b> ' + client_messages.text.range_to + ' <b id="' + tableId + '_end_item" class=\"DataTables_end_index\">_END_</b> ' + client_messages.text.of + ' <b id="' + tableId + '_total">_TOTAL_</b> ' + client_messages.text.entries + '.</label>';

    var isCardViewActive = $('.cardViewToggle').length != 0 && $('.cardViewToggle').is('.active');
    if ( isCardViewActive )
        $('.cardViewVisToggle').show();

    var pagingControlResources = {
        oPaginate: {
            sFirst: '<i class="far fa-angle-double-left"></i><span class="sr-only">' + client_messages.text.first + '</span>',
            sLast: '<i class="far fa-angle-double-right"></i><span class="sr-only">' + client_messages.text.last + '</span>',
            sNext: '<i class="far fa-angle-right"></i><span class="sr-only">' + client_messages.text.next + '</span>',
            sPrevious: '<i class="far fa-angle-left"></i><span class="sr-only">' + client_messages.text.previous + '</span>'
        },
        sLengthMenu: isTableType(currentTable, 'lengthSelect') ? ('<span class="d-flex align-items-center fs-xs"><select id="' + tableId + '_entry-select" class="custom-select custom-select-sm bg-lightest border-0 fs-xs">' +
            '<option value="10">10</option>' +
            '<option value="20">20</option>' +
            '<option value="30">30</option>' +
            '<option value="50">50</option>' +
            '<option value=' + (isTableType(currentTable, 'permitsShowAll') && !isCardViewActive ? "-1" : "100") + '>' + (isTableType(currentTable, 'permitsShowAll') && !isCardViewActive ? client_messages.text.all : '100') + '</option>' +
            ($(currentTable).is("[max_page_size]") && parseInt($(currentTable).attr("max_page_size")) >= 500 ? '<option value="500">500</option>' : '') +
            ($(currentTable).is("[max_page_size]") && parseInt($(currentTable).attr("max_page_size")) >= 1000 ? '<option value="1000">1000</option>' : '') +
            ($(currentTable).is("[max_page_size]") && parseInt($(currentTable).attr("max_page_size")) >= 1500 ? '<option value="1500">1500</option>' : '') +
            ($(currentTable).is("[max_page_size]") && parseInt($(currentTable).attr("max_page_size")) >= 3000 ? '<option value="3000">3000</option>' : '') +
            '</select>' + '<span class="entriesPerPageText flex-shrink-0 pl-1 ml-2"><span class="text-capitalize">' + client_messages.text.entries + ' </span>' + client_messages.text.per_page + '.</span></span>') : '',
        sZeroRecords: client_messages.text.no_matching_entries,
        sProcessing: '<div class="progress-loader-container d-flex justify-content-center align-items-middle"><div class="progress-loader progress-loader-lg mb-5"><i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i></div></div>',
        sInfoEmpty: client_messages.text.showing_zero_entries,
        sInfo: paginationInfo,
        sInfoFiltered: ''
    };

    // TABLE PROPERTIES ATTRIBUTES (Scroll, Column Visibility, Column Reorder)
    if (isTableType(currentTable, 'scroll-er')) {
        tablePropAttr.sScrollY = "100px";
        tablePropAttr.bScrollInfinite = false;
        tablePropAttr.bScrollCollapse = false;
        tablePropAttr.bScrollAutoCss = true;
        tablePropAttr.oScroller = {serverWait: 100, trace: false};
    }
    if (isTableType(currentTable, 'col-vis'))
        tablePropAttr.oColVis = getOColVis(currentTable);

    if (isTableType(currentTable, 'col-reorder'))
        tablePropAttr.oColReorder = {iFixedColumns: $('thead', currentTable).attr('numunreorderablecols')};

    if (isTableType(currentTable, 'table-tools-buttons')) {

        var columnSelector = ':visible',
            isSelectable = false;

        if (isTableType(currentTable, 'single-select') || isTableType(currentTable, 'multi-select')) {
            columnSelector += ':not(:first-child)';
            isSelectable = true;
        }

        if (isTableType(currentTable, 'drill-down')) {

            if (isSelectable)
                columnSelector += ':not(:nth-child(2))';

            else columnSelector += ':not(:first-child)';

        }

        var exportConfig = {
            columns: [columnSelector]
        };

        // Export file name will use page title if no explicit value is provided
        exportFilename = $(currentTable).attr('exportFilename') != undefined ? $(currentTable).attr('exportFilename') : '';

        tablePropAttr.buttons = {
            buttons: [
                {
                    extend: 'copy',
                    className: 'dropdown-item',
                    text: '<i class="far fa-copy fs-md fa-fw mr-2"></i> ' + client_messages.text.copy,
                    exportOptions: exportConfig,
                    title: exportFilename
                },
                {
                    extend: 'csv',
                    className: 'dropdown-item',
                    text: '<i class="far fa-file-csv fs-md fa-fw mr-2"></i> ' + client_messages.text.csv,
                    exportOptions: exportConfig,
                    title: exportFilename
                },
                {
                    extend: 'excel',
                    className: 'dropdown-item',
                    text: '<i class="far fa-file-excel fs-md fa-fw mr-2"></i> ' + client_messages.text.excel,
                    exportOptions: exportConfig,
                    title: exportFilename
                },
                {
                    extend: 'pdf',
                    className: 'dropdown-item',
                    text: '<i class="far fa-file-pdf fs-md fa-fw mr-2"></i> ' + client_messages.text.pdf,
                    exportOptions: exportConfig,
                    title: exportFilename
                },
                {
                    extend: 'print',
                    className: 'dropdown-item',
                    text: '<i class="far fa-print fs-md fa-fw mr-2"></i> ' + client_messages.text.print,
                    exportOptions: exportConfig,
                    title: exportFilename
                }
            ],
            dom: {
                container: {
                    className: 'dropdown-menu dropdown-menu-right'
                }
            }
        };

    }

    // ASYNC TABLE: INIT
    if (isTableType(currentTable, 'async')) {

        // ASYNC TABLE: Async attributes (defined per instance)
        if (typeof getAsyncExtListTableConfig == 'function') {
            var asyncDataObj = getAsyncExtListTableConfig($(currentTable).attr('id'));

            var ajaxSrc = asyncDataObj.ajaxSource;
            instanceAttr.sAjaxSource = context + "/" + ajaxSrc;
            instanceAttr.sServerMethod = isTableType(currentTable, 'serverMethod_POST') ? 'POST' : 'GET';

            // https://datatables.net/reference/option/rowId
            if (asyncDataObj.rowId) {
                instanceAttr.rowId = asyncDataObj.rowId;
            }

            var userDefRow = [];
            var colVisToggleCols = [];

            if (isTableType(currentTable, 'single-select') || isTableType(currentTable, 'multi-select')) {

                colVisToggleCols.push(0);

                var selectionToggleRow = {
                    bSortable: false,
                    sWidth: '1%',
                    mDataProp: 'unspecified',
                    sClass: (isTableType(currentTable, 'drill-down') ? 'pr-0' : ''),
                    render: function (data, type, full, meta) {

                        var controlType = 'checkbox',
                            controlClass = 'row-control custom-control custom-checkbox no-text',
                            controlId = 'ctrl_' + meta.row + '_' + meta.settings.sTableId,
                            controlGroup = null;

                        return '<div class="' + controlClass + '">' +
                            '<input id="' + controlId + '" type="' + controlType + '" class="custom-control-input" ' + (controlGroup ? 'name="' + controlGroup + '"' : '') + '>' +
                            '<label for="' + controlId + '" class="custom-control-label py-2">' +
                            '<span class="sr-only">' + client_messages.text.select + '</span>' +
                            '</label>' +
                            '</div>';

                    }
                };

                userDefRow.push(selectionToggleRow);

            }

            if (isTableType(currentTable, 'drill-down')) {

                colVisToggleCols.push(1);

                var drillToggleRow = {};
                drillToggleRow.bSortable = false;
                drillToggleRow.sWidth = "1%";
                drillToggleRow.mDataProp = "unspecified";
                drillToggleRow.sClass = "control drillDownToggleContainer";
                drillToggleRow.sDefaultContent = "<button class='btn btn-lg bg-transparent border-0 p-0' aria-label=" + client_messages.label.toggle_details + " style='line-height: 1;' type='button'><i class='drill-down-icon far fa-angle-right drill-down-open-icon' aria-hidden='true'></i></button>";
                userDefRow.push(drillToggleRow);

            }

            var increment = 0,
                decrement = 0;

            if (isTableType(currentTable, 'drill-down'))
                increment += 1;

            if (isTableType(currentTable, 'single-select') || isTableType(currentTable, 'multi-select'))
                increment += 1;

            for (var colIndex = 0; colIndex < asyncDataObj.columns.length; colIndex++) {
                var currentColumn = asyncDataObj.columns[colIndex];

                if (currentColumn.applied === false)
                    decrement += 1;

                if (currentColumn.applied == undefined || currentColumn.applied) {
                    var row = {};
                    row.bSortable = currentColumn.sort;
                    row.sWidth = currentColumn.width;
                    row.sTitle = currentColumn.columnName;
                    row.mDataProp = currentColumn.columnMap;
                    row.sDefaultContent = "&nbsp;";
                    row.render = function (data, type, full, meta) {
                        var colType = meta.settings.aoColumns[meta.col].mDataProp;
                        if ((colType == "name" || colType == "recipient") && $.isFunction(postListRenderFlagInjection)) {
                            var oObj = {aData: full};
                            return postListRenderFlagInjection(oObj, currentTable);
                        } else {
                            return data;
                        }
                    };

                    userDefRow.push(row);

                    if (isTableType(currentTable, 'col-vis') && !currentColumn.colVisToggle)
                        colVisToggleCols.push(parseInt(colIndex) + increment - decrement);

                }
            }

            instanceAttr.aoColumns = userDefRow;

            if (isTableType(currentTable, 'col-vis'))
                tablePropAttr.oColVis.aiExclude = colVisToggleCols;

        }

        // TIMEOUT: Detect timeout on list request; reload page
        $.fn.dataTableExt.sErrMode = function (settings, helpPage, message) {
            if (getParam('dtReload') != "true") { // helpPage == 1 &&
                var targetURL = getTopFrame().document.URL;
                targetURL = targetURL.replace("#", "");
                targetURL = addParam(targetURL, "dtReload=true");
                getTopFrame().location.href = targetURL;
            } else {
                alert(message);
            }
        };

        // ASYNC TABLE: Init plug-in instance
        currentDatatable = $(currentTable).DataTable($.extend(instanceAttr, tablePropAttr, {
            fnInitComplete: function (o, json) {
                $(currentTable).addClass('non-default');
                invokeTableInitCallbacks($currentTable.attr('id'));

                $('#data-table-load-complete').attr('value','true');
                // Access the DataTable API
                var api = this.api();
                // Attach the processing event handler
                api.on('processing.dt', function(e, settings, processing) {
                    if (processing) {
                        $('#data-table-load-complete').attr('value','false');
                    } else {
                        $('#data-table-load-complete').attr('value','true');
                    }
                });



            },
            fnPreDrawCallback: function (oSettings) {
                // Wrong page: Default loaded sub-page has no entries, return to first page
                if (oSettings._iDisplayStart >= oSettings._iRecordsTotal && oSettings._iDisplayStart > 0) {
                    setTimeout(function () {
                        $(currentTable).DataTable().ajax.reload(null, true);
                    }, 250);
                    return false;
                }
                return true;
            },
            fnDrawCallback: function (o) {
                var tableId = $(currentTable).attr('id');

                if (!isDataTableInitComplete) {
                    $('#listSearchInput, #listSearchInput_' + tableId).val($('#' + tableId + '_filter input').val());
                }

                // ALL filter: Set end count to total count (otherwise uses iDisplayLength, 0)
                if (o._iDisplayLength == 0)
                    $(currentTable).parent().find('.DataTables_end_index').html(o._iRecordsTotal);

                tokenInjectionTargets.push(o.nTable); // CSRF injection
                setRowSelectOperations(currentTable);
                initContextMenu();
                appendIFrameData(currentTable);

                if ($.isFunction(listItemClickHandler))
                    $(o.nTable).find('tr').click(function () {
                        listItemClickHandler(this);
                    });

                if ($('#listSearchInput').val() != "" || $('#listSearchInput_' + tableId).val() != "") {
                    $(o.nTable).find('.dataTableItemName').each(function () {
                        var searchInput = "";
                        if ($('#listSearchInput').val() && $('#listSearchInput').val() != "") {
                            searchInput = $('#listSearchInput').val();
                        } else if ($('#listSearchInput_' + tableId).val() && $('#listSearchInput_' + tableId).val() != "") {
                            searchInput = $('#listSearchInput_' + tableId).val();
                        }
                        if (searchInput !== "") {
                            $(this).html(highlightListItemSearchValue($(this).text(), searchInput));
                        }
                    });
                }

                $(o.nTable).find('.txtFmtTip').each(function () {
                    initTip(this);
                });

                $(o.nTable).closest('.dataTables_wrapper').each( function() {

                    var $this = $(this),
                        pageToggleCount = $this.find('.pagination .page-item:not(.first,.previous,.next,.last)').length;

                    if (pageToggleCount <= 1)
                        $this.find('.dataTables_paginate').parent().addClass('d-none');

                    else $this.find('.dataTables_paginate').parent().removeClass('d-none');

                    if (o.aoData.length < 1)
                        $this.find('.datatable-export-buttons').addClass('d-none');

                    else $this.find('.datatable-export-buttons').removeClass('d-none');

                });

                // INIT DRILL DOWNS
                if (isTableType(currentTable, 'drill-down')) {
                    setDrillDownListener(o.oInstance);
                }

                if (isTableType(currentTable, 'single-select') || isTableType(currentTable, 'multi-select')) {

                    var $tableSelectionControl = o.oInstance.find('.header-control'),
                        $checkbox = $tableSelectionControl.children('input');

                    if (!$tableSelectionControl.length)
                        setRowSelectionControl(o.oInstance);

                    else
                        $checkbox.prop('indeterminate', false).removeClass('indeterminate').attr('checked', false);

                    if (o.oInstance.find('.dataTables_empty').length)
                        $checkbox.attr('disabled', true);

                    else
                        $checkbox.attr('disabled', false);

                }

                if ($.isFunction(validateActionReq))
                    validateActionReq();

                if ($.isFunction(postListDrawCallback))
                    postListDrawCallback(o.nTable, o);

                isDataTableInitComplete = true;
            },
            fnHeaderCallback: function (nHead, aData, iStart, iEnd, aiDisplay) {
                if ($.isFunction(headerLabelOverride))
                    headerLabelOverride(nHead, aData, iStart, iEnd, aiDisplay);
            },
            stateSaveCallback: function (oSettings, oData) {
                var localStorageId = 'DataTables_' + window.location.pathname;
                var colvis = oSettings.aoColumns.map(e => ({ mDataProp: e.mDataProp, sTitle: e.sTitle, bVisible: e.bVisible}));

                if ($(currentTable).attr('id') !== null) {
                    localStorageId = 'DataTables_' + $(currentTable).attr('id') + '_' + window.location.pathname;
                    localColVisStorageId = 'DataTables_' + $(currentTable).attr('id') + '_colvis_' + window.location.pathname;
                }

                if (_.isFunction(window["dataTables_stateStorageIdentifier"])) {
                    localStorageId += "_" + dataTables_stateStorageIdentifier();
                    localColVisStorageId += "_" + dataTables_stateStorageIdentifier();
                }

                if (!($.browser.msie == true && parseInt($.browser.version) == 7)){
                    localStorage.setItem(localStorageId, JSON.stringify(oData));
                    localStorage.setItem(localColVisStorageId, JSON.stringify(colvis));
                }
            },
            stateLoadCallback: function (oSettings) {
                var localStorageId = 'DataTables_' + window.location.pathname;
                if ($(currentTable).attr('id') !== null) {
                    localStorageId = 'DataTables_' + $(currentTable).attr('id') + '_' + window.location.pathname;
                }

                if (_.isFunction(window["dataTables_stateStorageIdentifier"])) {
                    localStorageId += "_" + dataTables_stateStorageIdentifier();
                }

                if (!($.browser.msie == true && parseInt($.browser.version) == 7)) {
                    var savedSettings =  JSON.parse(localStorage.getItem(localStorageId));
                    if (savedSettings && savedSettings.columns && oSettings.aoColumns && oSettings.aoColumns.length !== savedSettings.columns.length && localStorageId.includes("touchpoint_communications_lis")) {
                        delete savedSettings.columns;
                    }

                    return savedSettings;
                }
            },
            fnServerParams: function (aoData) {
                if (typeof getAsyncExtParams == 'function') {
                    var extAsyncParams = getAsyncExtParams($(currentTable).attr('id'));
                    for (var currentIndex = 0; currentIndex < extAsyncParams.length; currentIndex++)
                        aoData.push(extAsyncParams[currentIndex]);
                }
            },
            iDisplayLength: isTableType(currentTable, 'mobileView') ? 20 : 10,
            bPaginate: isTableType(currentTable, 'async-paginate'),
            bLengthChange: true,
            bAutoWidth: true,
            bSort: true,
            order: [],
            bJQueryUI: false,
            bStateSave: true,
            bDeferRender: true,
            bProcessing: true,
            bServerSide: true,
            bFilter: true,
            sDom: getSDom(currentTable),
            sPaginationType: isTableType(currentTable, 'simplePagination') ? 'numbers' : 'full_numbers',
            oLanguage: pagingControlResources,
            iStateDuration: -1,
        }));

        // DATA TABLES
        var tableWrapper = $(currentTable).closest('.dataTables_wrapper');
        // Fix: Init display uses opacity:0 instead of hide; otherwise, 'Copy' won't work
        $(tableWrapper).find('.tableToolsContainer').addClass('dataTables_transparent');
        // Position: Top right on table
        $(tableWrapper).hoverIntent({
            sensitivity: 25, // number = sensitivity threshold (must be 1 or higher)
            interval: 350,   // number = milliseconds of polling interval
            over: function (e) {
                // Empty table: Don't show table tools
                if ($('.dataTables_empty').is(':visible'))
                    return;
                $(tableWrapper).find('.tableToolsContainer').css({left: $(tableWrapper).width() + 'px'});
                $(tableWrapper).find('.tableToolsContainer').removeClass('dataTables_transparent').show();
            },
            out: function () {
                // Tools display: Don't hide when 'Save' submenu opened
                if (!$('.DTTT_collection_background').is(':visible'))
                    $(tableWrapper).find('.tableToolsContainer').hide();
            }
        });
        $(document).click(function (e) {
            // Tools display: Hide if 'Save' submenu is closed
            if ($(eventTarget(e)).is('.DTTT_collection_background'))
                $(tableWrapper).find('.tableToolsContainer').hide();
        });
        // IE 8 Hack: Hide tools on timeout (permit time to init)
        setTimeout(function () {
            $(tableWrapper).find('.tableToolsContainer').hide();
        }, 50);

        // SEARCH: Init search filter input
        var tableId = $(currentTable).attr('id');
        $('#listSearchInput, #listSearchInput_' + tableId).val($('#' + tableId + '_filter input').val());
        $('#listSearchInput, #listSearchInput_' + tableId)
            .keyup(function (e) {
                var _this = this;
                clearTimeout(search_timer);
                search_timer = setTimeout( function() { triggerSearch(tableId, _this);}, 1000);
            })
            .bind('paste', function () {
                var _this = this;
                setTimeout(function () {
                    oTable = $('#' + tableId).dataTable();
                    oTable.fnFilter($(_this).val());
                }, 0);

            })
            .keypress(function (e) {
                if (e.keyCode == 13)
                    e.preventDefault();
            });
        if (getParam('reset') == 'true') {
            oTable = $('#' + tableId).dataTable();
            if (oTable.fnSettings() != null)
                oTable.fnSettings()._iDisplayStart = 0;
            oTable.fnFilter('');
        }

    } else {

        var tableId = $(currentTable).attr('id');
        var paginate = $currentTable.hasClass('paginate');

        standardTableOptions = {
            fnInitComplete: function (o, json) {
                setTableTitle(o, currentTable);

                if (isTableType(currentTable, 'default') && !isTableType(currentTable, 'drill-down'))
                    $('thead', currentTable).hide();

                if (isTableType(currentTable, 'default') && !isTableType(currentTable, 'staticData')) {
                    $(currentTable).addClass('default');
                } else {
                    $(currentTable).addClass('non-default');
                    $('thead tr', currentTable).show();
                }

                if (isTableType(currentTable, 'staticData')) {
                    var $rows = $(currentTable).find('tr');
                    $rows.removeClass('contentTableContentTR');
                    $rows.each(function (index, row) {
                        $(row).addClass(index % 2 === 0 ? "even" : "odd");
                    });
                    $(currentTable).parent('.dataTablesContentWrapper').addClass('staticData');
                }

                if ($(currentTable).hasClass('paginate'))
                    $(currentTable).addClass('non-default');

                setRowSelectOperations(currentTable);

                $("tbody, thead", currentTable).css('display', '');

                invokeTableInitCallbacks($currentTable.attr('id'));
                initContextMenu();
            },
            fnStateSave: function (oSettings, oData) {
                var localStorageId = 'DataTables_' + window.location.pathname;
                if ($(currentTable).attr('id') !== null) {
                    localStorageId = 'DataTables_' + $(currentTable).attr('id') + '_' + window.location.pathname;
                }

                if (_.isFunction(window["dataTables_stateStorageIdentifier"])) {
                    localStorageId += "_" + dataTables_stateStorageIdentifier();
                }

                if (!($.browser.msie == true && parseInt($.browser.version) == 7))
                    localStorage.setItem(localStorageId, JSON.stringify(oData));
            },
            fnDrawCallback: function (o) {
                if (isTableType(currentTable, 'drill-down')) {
                    setDrillDownListener(o.oInstance);
                }

                if (isTableType(currentTable, 'single-select') || isTableType(currentTable, 'multi-select')) {

                    var $tableSelectionControl = o.oInstance.find('.header-control'),
                        $checkbox = $tableSelectionControl.children('input');

                    if (!$tableSelectionControl.length)
                        setRowSelectionControl(o.oInstance);

                    else
                        $checkbox.prop('indeterminate', false).removeClass('indeterminate').attr('checked', false);

                    if (o.oInstance.find('.dataTables_empty').length)
                        $checkbox.attr('disabled', true);

                    else
                        $checkbox.attr('disabled', false);

                }

            },
            fnStateLoad: function (oSettings) {
                var localStorageId = 'DataTables_' + window.location.pathname;
                if ($(currentTable).attr('id') !== null) {
                    localStorageId = 'DataTables_' + $(currentTable).attr('id') + '_' + window.location.pathname;
                }

                if (_.isFunction(window["dataTables_stateStorageIdentifier"])) {
                    localStorageId += "_" + dataTables_stateStorageIdentifier();
                }

                if (!($.browser.msie == true && parseInt($.browser.version) == 7))
                    return JSON.parse(localStorage.getItem(localStorageId));
            },
            aoColumns: getAOColumns(currentTable),
            bSort: (!isTableType(currentTable, 'default') && isTableType(currentTable, 'drill-down')) || isTableType(currentTable, 'sortable'),
            order: [],
            bPaginate: false,
            bAutoWidth: false,
            bJQueryUI: false,
            sDom: getSDom(currentTable),
            bStateSave: true

        };

        if (paginate) {
            $.extend(standardTableOptions, {
                bPaginate: true,
                iDisplayLength: isTableType(currentTable, 'mobileView') ? 20 : 10,
                bLengthChange: true,
                sPaginationType: isTableType(currentTable, 'simplePagination') ? 'numbers' : 'full_numbers',
                oLanguage: pagingControlResources
            });
        }

        // STANDARD TABLE INIT
        currentDatatable = $(currentTable).DataTable($.extend(tablePropAttr, standardTableOptions));

        // DATA TABLES
        var tableWrapper = $(currentTable).closest('.dataTables_wrapper');
        // Fix: Init display uses opacity:0 instead of hide; otherwise, 'Copy' won't work
        $(tableWrapper).find('.tableToolsContainer').addClass('dataTables_transparent');
        // Position: Top right on table
        $(tableWrapper).find('.tableToolsContainer').css({left: $(tableWrapper).width() + 'px'});
        $(tableWrapper).hoverIntent({
            sensitivity: 25, // number = sensitivity threshold (must be 1 or higher)
            interval: 350,   // number = milliseconds of polling interval
            over: function (e) {
                // Empty table: Don't show table tools
                if ($('.dataTables_empty').is(':visible'))
                    return;
                $(tableWrapper).find('.tableToolsContainer').removeClass('dataTables_transparent').show();
            },
            out: function () {
                // Tools display: Don't hide when 'Save' submenu opened
                if (!$('.DTTT_collection_background').is(':visible'))
                    $(tableWrapper).find('.tableToolsContainer').hide();
            }
        });
        $(document).click(function (e) {
            // Tools display: Hide if 'Save' submenu is closed
            if ($(eventTarget(e)).is('.DTTT_collection_background'))
                $(tableWrapper).find('.tableToolsContainer').hide();
        });
        // IE 8 Hack: Hide tools on timeout (permit time to init)
        setTimeout(function () {
            $(tableWrapper).find('.tableToolsContainer').hide();
        }, 50);

        // SEARCH: Init search filter input

        $('#listSearchInput, #listSearchInput_' + tableId)
            .val($('#' + tableId + '_filter input').val())
            .keyup(function (e) {
                var _this = this;
                clearTimeout( search_timer );
                search_timer = setTimeout( function() { triggerSearch(tableId, _this);}, 1000);
            })
            .bind('paste', function () {
                var _this = this;
                setTimeout(function () {
                    oTable = $('#' + tableId).dataTable();
                    oTable.fnFilter($(_this).val());
                }, 0);
            })
            .keypress(function (e) {
                if (e.keyCode == 13)
                    e.preventDefault();
            });
        if (getParam('reset') == 'true') {
            oTable = $('#' + tableId).dataTable();
            if (oTable.fnSettings() != null)
                oTable.fnSettings()._iDisplayStart = 0;
            oTable.fnFilter('');
        }
    }


    function hideColumns(currentTable, currentDatatable) {
        if (currentDatatable.context.length) {
            if(currentDatatable.context[0].aoColumns) {
                var columns = currentDatatable.context[0].aoColumns;
                var localStorageId = 'DataTables_' + window.location.pathname;
                if ($(currentTable).attr('id') !== null) {
                    localColVisStorageId = 'DataTables_' + $(currentTable).attr('id') + '_colvis_' + window.location.pathname;
                }

                if (_.isFunction(window["dataTables_stateStorageIdentifier"])) {
                    localColVisStorageId += "_" + dataTables_stateStorageIdentifier();
                }
                if (!($.browser.msie == true && parseInt($.browser.version) == 7)) {
                    if(JSON.parse(localStorage.getItem(localColVisStorageId))){
                        var colvis = JSON.parse(localStorage.getItem(localColVisStorageId));
                        colvis.map(function(c){
                            // Getting the index of column based on column title
                            if(columns.find(e => e.sTitle === c.sTitle)){
                                var idx = columns.find(e => e.sTitle === c.sTitle).idx;
                                currentDatatable.columns(idx).visible(c.bVisible)
                            };
                        });
                    }
                }
            }
        }
    }

    hideColumns(currentTable, currentDatatable);

    // Position column visibility toggle
    $('.colVisToggleContainer').append($('.dataTables_wrapper div.ColVis'));
    $('.colVisToggleContainer div.ColVis').css({position: 'static', width: 'auto', height: 'auto'}).show();

    if (isTableType(currentTable, 'table-tools-buttons')) {

        var $exportButtons = $('<div class="datatable-export-buttons dropdown dropup d-none"></div>'),
            btnClass = 'btn btn-lightest btn-sm text-dark text-uppercase font-weight-bold fs-xs dropdown-toggle';

        currentDatatable.buttons().container().appendTo($exportButtons);
        $exportButtons.prepend('<button class="' + btnClass + '" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">' + client_messages.text.save_as + '</button>');

        $('#' + tableId + '_wrapper').find('.datatable-entries-info').after($exportButtons);

    }

}

function invokeTableInitCallbacks(tableId) {
    if (!(dataTableInitCallbacks instanceof Array) || dataTableInitCallbacks.length == 0) {
        return;
    }

    dataTableInitCallbacks.forEach(function (item, index) {
        if (typeof item === 'function') {
            item();
        }

        if (typeof item === 'object' && item !== null) {
            if (item['table'] === tableId && item.hasOwnProperty('callback')) {
                item['callback']();
            }
        }
    })

}

function tableToolsCellRender(sValue, iColumn, nTr, iDataIndex, type) {
    var returnValue = $("<div/>").append(sValue);
    // Truncation: Replace truncated values with their tool tip values
    $(returnValue).find('.txtFmtTip').each(function () {
        var titleTxt = $(this).attr('title');
        var tipEle = $("<div/>").append(titleTxt);

        $(this).text($(tipEle).find('.txtFmtTipText').text());
    });
    $(returnValue).find('*').each(function () {
        // Truncation: Replace item name truncated values with their fill value
        if ($(this).attr("itemName") != undefined)
            $(this).text($(this).attr("itemName"));
        // Icons: Replace value with text value used for reporting
        if ($(this).attr("reportLabel") != undefined)
            $(this).text($(this).attr("reportLabel"));
    });

    return $(returnValue).text();
}

function getAOColumns(T) {
    var arr = [];
    $('th', T).each(function () {
        if ($(this).attr('ssortDataType') == '') {
            arr.push({bSortable: $(this).hasClass('sortable'), bVisible: true});
        } else {
            if ($(this).attr('stype') == '') {
                arr.push({
                    bSortable: $(this).hasClass('sortable'),
                    bVisible: true,
                    sSortDataType: $(this).attr('ssortdatatype')
                });
            } else {
                arr.push({
                    bSortable: $(this).hasClass('sortable'),
                    bVisible: true,
                    sSortDataType: $(this).attr('ssortdatatype'),
                    sType: $(this).attr('stype')
                });
            }
        }
    });
    return arr;
}

function setTableTitle(o, T) {
    var tableTitle = "";

    tableTitle = getTableTitle(T);

    if (!isTableType(T, 'default') || isTableType(T, 'async'))
        $("div.non-default-dt-table-header", o.nTableWrapper).text(tableTitle);
    else
        $("div.default-dt-table-header", o.nTableWrapper).text(tableTitle);
}

function getTableTitle(T) {
    var nTableTitle = "";

    if (!isTableType(T, 'async')) {
        nTableTitle = $('tr.contentTableHeaderTR td:first', T).text();
        if (!isTableType(T, 'drill-down')) {
            $('.contentTableHeaderTR', T).hide();
        }

        if (nTableTitle == "")
            nTableTitle = $('tr.contentTableHeaderTR_minimal td:first', T).text();
    } else {
        nTableTitle = $(T).attr('tabletitle');
    }

    return nTableTitle;
}

function getSDom(T) {

    var str = '';
    var headerDivStart = '<"H"';
    var nTableTitleDiv = '<"dt-table-header">';
    var searchFilter = '';
    var headerDivEnd = '>';
    var tableWrapper = '<"dataTablesContentWrapper"';
    var nTable = 't';
    var nProcessing = '';
    var scroller = '';
    var nColVis = '';
    var nColReorder = '';
    var nPagination = '';
    var nTableTools = '';
    var footerDivStart = '';
    var footerInfo = '';
    var footerDivEnd = '';
    var tableToolsButtons = '';

    if (!isTableType(T, 'default') || isTableType(T, 'async'))
        nTableTitleDiv = '<"non-default-dt-table-header listTableHeader">';
    else
        nTableTitleDiv = '<"default-dt-table-header listTableHeader">';


    var tableTitle = getTableTitle(T);

    if (tableTitle == "" && !isTableType(T, 'search-filter')) {
        headerDivStart = "";
        nTableTitleDiv = "";
        headerDivEnd = "";
    }
    if (isTableType(T, 'async') || isTableType(T, 'paginate')) {
        footerDivStart = '<"d-flex align-items-center pt-1 pb-1"F"';

        footerInfo = '<"d-flex align-items-baseline mr-auto datatable-entries-info"<"fs-xs"i>>';
        if (isTableType(T, 'lengthSelect'))
            footerInfo = '<"d-flex align-items-baseline mr-auto datatable-entries-info"<"mr-2 pr-1 ml-1"l><"fs-xs"i>>';

        footerDivEnd = '>';
        nProcessing = 'r';
        nPagination = '<"ml-4 mr-1" p>';
    }

    if (isTableType(T, 'col-vis'))
        nColVis = 'C';
    if (isTableType(T, 'col-reorder'))
        nColReorder = 'R';
    if (isTableType(T, 'search-filter'))
        searchFilter = 'f';
    if (isTableType(T, 'scroll-er'))
        scroller = "S";
    if (isTableType(T, 'table-tools-buttons'))
        tableToolsButtons = 'B';

    if (!isTableType(T, 'async'))
        str += headerDivStart + nTableTitleDiv + (!isTableType(T, 'async') ? searchFilter : "") + headerDivEnd
    str += nProcessing +
        nTableTools +
        tableWrapper + nTable + '>' + scroller + (isTableType(T, 'async') ? "<\"colVisSuppress\" " + nColVis + ">" : nColVis) + nColReorder +
        footerDivStart + (isTableType(T, 'async') ? "<\"d-none\" " + searchFilter + ">" : "") + footerInfo + tableToolsButtons + nPagination + footerDivEnd;

    str += "P";

    return str;
}

function getOColVis(T) {

    var excludeIndexArray = [1];
    var colvisRow = null;

    if ($(T).find(".contentListTableContentTR:last td[colvisable='false']").length != 0)
        colvisRow = $(T).find(".contentListTableContentTR:last");
    else if ($(T).find(".listTableHeaderTR:last td[colvisable='false']").length != 0)
        colvisRow = $(T).find(".listTableHeaderTR:last");
    else if ($(T).find(".listTableTR:last td[colvisable='false']").length != 0)
        colvisRow = $(T).find(".listTableTR:last");

    if (colvisRow != null)
        for (var i = 0; i < $(colvisRow).find('td,th').length; i++)
            if ($($(colvisRow).find('td,th')[i]).attr('colvisable') == "false" && i != 1)
                excludeIndexArray[excludeIndexArray.length] = i;

    var obj = {
        aiExclude: excludeIndexArray,
        sAlign: isTableType(T, 'async') ? "left" : "right",
        sSize: "css",
        activate: "click",
        buttonText: client_messages.label.toggle_columns
    };
    return obj;
}

function isTableType(T, type) {
    var result = true;

    if (type == "default") {
        $('tbody:first > tr', T).each(function () {
            if ($(this).hasClass('listHeader') || $(this).hasClass('listHeader_minimal') || $(this).hasClass('contentListTableContentTR')) {
                result = false;
                return false;
            }
        });
    }
    else if (!$(T).hasClass(type)) {
        result = false;
    }

    return result;
}

function checkRowSelectionBinding(R) {
    var $row = $(R),
        $table = $row.closest('table'),
        $rowSelectCheckbox = $("input[id^='listItemCheck']", R);
    if ($rowSelectCheckbox !== null)
        $rowSelectCheckbox.attr('checked', 'checked');
    $row.children().first().find('.custom-control-input').attr('checked', true);

    var $thCheckbox = $row.closest('table').find('.header-control').children('input'),
        $tdCheckbox = $table.find('.row-control').children('input');

    if ($tdCheckbox.filter(':checked').length < $tdCheckbox.length)
        $thCheckbox.prop('indeterminate', true).addClass('indeterminate').attr('checked', false);

    else
        $thCheckbox.prop('indeterminate', false).removeClass('indeterminate').attr('checked', true);

}

function uncheckRowSelectionBinding(R) {
    var $row = $(R),
        $table = $row.closest('table'),
        $rowSelectCheckbox = $("input[id^='listItemCheck']", R);
    if ($rowSelectCheckbox !== null)
        $rowSelectCheckbox.removeAttr('checked');
    $row.children().first().find('.custom-control-input').attr('checked', false);

    var $thCheckbox = $row.closest('table').find('.header-control').children('input');

    if (!$table.find('.row-control').children('input').filter(':checked').length)
        $thCheckbox.prop('indeterminate', false).removeClass('indeterminate').attr('checked', false);

    else
        $thCheckbox.prop('indeterminate', true).addClass('indeterminate').attr('checked', false);
}

function selectSingleRow(event, R, T) {
    var rowSelectedFlag = false;
    if ($(R).hasClass(rowSelected))
        rowSelectedFlag = true;

    // Right click may select a row but not deselect
    if (rowSelectedFlag && event.which === 3)
        return;

    // Deselect all rows
    if (!isTableType(T, 'drag-and-drop') || (isTableType(T, 'drag-and-drop') && dataTableMouseAction != 'drag')) {
        $('tbody:first > tr', T).each(function () {
            $(this).removeClass(rowSelected);
            uncheckRowSelectionBinding(this);
        });
    }

    // Select target row
    if (!rowSelectedFlag) {
        $(R).addClass(rowSelected);
        checkRowSelectionBinding(R);
    }
}

function toggleRowSelection(R, event) {
    var rowSelectedFlag = false;

    if ($(R).hasClass(rowSelected))
        rowSelectedFlag = true;

    // Right click may select a row but not deselect
    if (event)
        if (rowSelectedFlag && event.which === 3)
            return;

    if (rowSelectedFlag) {
        $(R).removeClass(rowSelected);
        uncheckRowSelectionBinding(R);
    } else {
        $(R).addClass(rowSelected);
        checkRowSelectionBinding(R);
    }
}

// Row Select Actions: Single select, multi select (ctrl+click, shift+click)
//		Type - Table declares types of select actions permitted
function rowSelectAction(event, R, T) {
    var singleSelect = true;
    if (isTableType(T, 'single-select'))
        singleSelect = true;
    else if (isTableType(T, 'multi-select'))
        singleSelect = false;
    else
        singleSelect = true;

    var clickTarget = eventTarget(event);

    if ($(clickTarget).is('.dataTables_empty') || $(clickTarget).is('.row-control .custom-control-label')) {
        return;
    } else if ($(clickTarget).is('.drillDownToggleContainer') || ($(clickTarget).is('a') && event.which !== 3) || $(clickTarget).closest('.drillDownToggleContainer').length > 0) {
        return;
    } else if (singleSelect) {
        selectSingleRow(event, R, T);
    } else {
        if (event.shiftKey) {

            var rowIndex1 = 0;
            var rowIndex2 = 0;
            $('tbody:first > tr', T).each(function () {
                if ($(this).hasClass(rowSelected)) {
                    rowIndex1 = this.rowIndex;
                    return false;
                }
            });
            rowIndex2 = R.rowIndex;

            if (rowIndex1 == 0) {
                selectSingleRow(event, R, T);
            } else {
                $('tbody:first > tr', T).each(function () {

                    if (rowIndex1 < rowIndex2) {
                        if (this.rowIndex >= rowIndex1 && this.rowIndex <= rowIndex2) {
                            $(this).addClass(rowSelected);
                            checkRowSelectionBinding(this);
                        }
                    } else if (rowIndex1 > rowIndex2) {
                        if (this.rowIndex >= rowIndex2 && this.rowIndex <= rowIndex1) {
                            $(this).addClass(rowSelected);
                            checkRowSelectionBinding(this);
                        }
                    }
                });
            }
        } else {

            toggleRowSelection(R, event);

        }
    }

    // Custom callback for action system
    if ($.isFunction(validateActionReq))
        validateActionReq();
}

/* Get the rows which are currently selected (for Context Menu)*/
function fnGetSelected(T) {
    var aReturn = new Array();
    $('tbody:first > tr', T).each(function () {
        aReturn.push(this);
    });
    return aReturn;
}

var dataTableMouseAction;

function setRowSelectOperations(T) {
    if (isTableType(T, 'single-select') || isTableType(T, 'multi-select')) {
        $('tbody:first > tr', T).each(function () {

            // Nested table: Skip
            if ($(this).closest('table:not(.multi-select)').closest('table.multi-select').length != 0)
                return;

            if (isTableType(T, 'drag-and-drop')) {
                $(this).mouseup(function (event) {
                    dataTableMouseAction = '';
                    //left or right-click
                    if (event.which == 1 || event.which == 3)
                        rowSelectAction(event, this, T);
                });
                $(this).mousedown(function () {
                    dataTableMouseAction = 'click';
                });
                $(this).mousemove(function () {
                    dataTableMouseAction = 'drag';
                });
            } else {
                //left or right-click
                $(this).mousedown(function (event) {
                    if (event.which == 1 || event.which == 3)
                        rowSelectAction(event, this, T);
                });
            }
        });
        $('tbody:not(:first) tr', T).each(function () {

            // Nested table: Skip
            if ($(this).closest('table:not(.multi-select)').closest('table.multi-select').length != 0)
                return;

            if (isTableType(T, 'drag-and-drop')) {
                $(this).mouseup(function (event) {
                    dataTableMouseAction = '';
                    if (event.which == 1 || event.which == 3)
                        rowSelectAction(event, $(this).closest('.odd,.even'), T);
                });
                $(this).mousedown(function () {
                    dataTableMouseAction = 'click';
                });
                $(this).mousemove(function () {
                    dataTableMouseAction = 'drag';
                });
            } else {
                $(this).mousedown(function (event) {
                    if (event.which == 1 || event.which == 3)
                        rowSelectAction(event, $(this).closest('.odd,.even'), T);
                });
            }
        });
    }
}


function setDrillDownListener(T) {
    /* Add event listener for opening and closing details
     * Note that the indicator for showing which row is open is not controlled by DataTables,
     * rather it is done here
     */
    $('td.drillDownToggleContainer', T).click(function (e) {

        var nTr = this.parentNode;
        var i = $.inArray(nTr, anOpen);

        if ($(nTr).hasClass('drillDownDisabled') || $(this).is('.drillDownDisabled')) {
            $(nTr).find('.drill-down-icon').removeClass('drill-down-open-icon');
            return;
        }

        var drillDownClass = 'details';
        if ($(nTr).hasClass('odd'))
            drillDownClass = 'odd';
        else if ($(nTr).hasClass('even'))
            drillDownClass = 'even';

        if (i === -1) {

            //Allow bulk review of data model elements
            var title = getTableTitle(T);
            if (title.indexOf("Data Elements") == -1 && !isTableType(T, 'multi-drill-down'))
                _fnClearDrillDownMenus(T);

            $('.drill-down-icon', this).attr('class', "drill-down-icon far fa-angle-right drill-down-close-icon");

            // Display: Add drill down visual to first two cells (Remove bottom border - visual tie to drill-down contents)
            $(nTr).find('td:first, td:nth-child(2)').addClass('drillDown-connected-cell');

            var nDetailsRow = T.fnOpen(nTr, fnFormatDetails(T, nTr), drillDownClass);


            $('.innerDetails', nDetailsRow).slideDown(function () {
                if (window != getTopFrame())
                    adjustParentIFrameHeight(window);
            });
            anOpen.push(nTr);

            //give same class to injected tr (drill down) as its parent
            $(nTr).next().attr('class', $(nTr).attr('class'));
            $(nTr).next().removeClass(rowSelected);
            //also append a new class to identify any newly open drill downs
            $(nTr).next().addClass('drill-down');


        } else {

            $('.drill-down-icon', this).attr('class', "drill-down-icon far fa-angle-right drill-down-open-icon");

            // Display: Restore default table cell visuals
            $(nTr).find('td:first, td:nth-child(2)').removeClass('drillDown-connected-cell');

            $('.innerDetails', $(nTr).next().get(0)).slideUp(function () {
                T.fnClose(nTr);
                anOpen.splice(i, 1);

                window.parent.adjustIFrameHeight(window.frameElement);
                common.refreshParentIframeHeight();
            });
        }

    });
}

function setRowSelectionControl($table) {

    $($table).on('change', '.row-control .custom-control-input', function () {

        var $currentRow = $(this).closest('tr'),
            $selectedRows;

        toggleRowSelection($currentRow);

        $selectedRows = $currentRow.siblings().filter('.' + rowSelected);

        if (isTableType($table, 'single-select') && $selectedRows.length > 0) {

            $selectedRows.each(function () {

                toggleRowSelection($(this));

            });

        }

        if ($.isFunction(validateActionReq))
            validateActionReq();

    });

    if (isTableType($table, 'multi-select')) {

        var selectAllHtml = '';
        if(isTableType($table, 'select-all-pages')) {
            selectAllHtml = '<div style="display: inline-block; padding-left: 10px; cursor: pointer;">' +
                '<i id="select_all_' + $table.attr('id') + '" onclick="showSelectAllDrillDown(this)" class="far fa-angle-down"></i>' +
                '</div>';

            selectAllHtml = '<div style="display: inline-block; padding-left: 10px; cursor: pointer;">' +
                '<i id="select_all_' + $table.attr('id') + '" onclick="showSelectAllDrillDown(this)" class="far fa-angle-down"></i>' +
                '</div> ' +
                '<div id="select_all_drill_down_' + $table.attr('id') + '" class="dropdown-menu">' +
                '     <div id="all_in_page_' + $table.attr('id') + '" onclick="applySelectAllInCurrentPage(this)"  style="cursor: pointer;" class="dropdown-item">' +
                '         <div class="custom-control custom-checkbox">' +
                '             <input type="checkbox" class="custom-control-input" id="checkbox_all_in_page_' + $table.attr('id') + '">' +
                '             <label class="custom-control-label" style="padding-left: 3px;" for="checkbox_all_in_page_' + $table.attr('id') + '" id="label_all_in_page_' + $table.attr('id') + '"></label>' +
                '         </div>' +
                '     </div>' +
                '     <div id="all_pages_' + $table.attr('id') + '"onclick="applySelectAllInAllPages(this)" style="cursor: pointer;" class="dropdown-item">' +
                '         <div class="custom-control custom-checkbox">' +
                '             <input type="checkbox" class="custom-control-input" id="checkbox_all_pages_' + $table.attr('id') + '">' +
                '             <label class="custom-control-label" style="padding-left: 3px;" for="checkbox_all_pages_' + $table.attr('id') + '" id="label_all_pages_' + $table.attr('id') + '"></label>' +
                '         </div>' +
                '     </div>' +
                ' </div>' +
                '</div>';
        }

        var controlId = 'header_ctrl_' + $table.attr('id'),
            $thControl = $('<div style="display: inline-block;" class="header-control custom-control no-text custom-checkbox">' +
                '<input id="' + controlId + '" type="checkbox" class="custom-control-input">' +
                '<label for="' + controlId + '" class="custom-control-label py-2">' +
                '<span class="sr-only">' + client_messages.text.select_all_items + '</span>' +
                '</label>' +
                selectAllHtml +
                '</div>');

        if ($table.find('.dataTables_empty').length)
            $thControl.children('input').attr('disabled', true);

        $thControl.on('change', function (){
            var tableId = $table.prop('id');
            if($('#header_ctrl_' + tableId).prop('checked') && !$('#checkbox_all_pages_' + tableId).prop('checked')) {
                $('#checkbox_all_in_page_' + tableId).prop('checked', true);
            } else if($('#checkbox_all_pages_' + tableId).prop('checked') && !$('#header_ctrl_' + tableId).prop('checked')) {
                $('#select_all_' + tableId).removeClass("fa-angle-double-down").addClass("fa-angle-down");
                deselectAllElementsInAllPages(tableId);
                $('#checkbox_all_in_page_' + tableId).prop('checked', false);
                $('#checkbox_all_pages_' + tableId).prop('checked', false);
            } else {
                $('#checkbox_all_in_page_' + tableId).prop('checked', false);
            }
            $('#select_all_drill_down_' + tableId).hide();
        });

        $thControl.children('input').on('click', function () {

            var $this = $(this);

            if ($this.is('.indeterminate'))
                $this.removeClass('indeterminate').attr('checked', false);

        }).on('change', function () {

            var $rows = $table.find('tbody tr'),
                $selectedRows = $rows.filter('.' + rowSelected);

            if ($selectedRows.length < $rows.length && $selectedRows.length != 0) {

                $selectedRows.each(function () {

                    toggleRowSelection($(this));

                });

            } else {

                $rows.each(function () {

                    toggleRowSelection($(this));

                });

            }

            if ($.isFunction(validateActionReq))
                validateActionReq();

        });

        $table.children('thead').find('th').first().append($thControl);

    }

}

function toggleRowDrillDown(nTr, enabled) {
    // Reset the drill down if it is visible
    if ($(nTr).find('.drill-down-icon').hasClass('drill-down-close-icon')) {
        $(nTr).find('td.drillDownToggleContainer').click();
    }
    if (enabled) {
        $(nTr).removeClass('drillDownDisabled');
        $(nTr).find('.drill-down-icon').addClass('drill-down-open-icon');
    } else {
        $(nTr).addClass('drillDownDisabled');
        $(nTr).find('.drill-down-icon').removeClass('drill-down-open-icon');
    }
}

function fnFormatDetails(T, nTr) {

//	<!-- datamodel_datarecords.jsp?dsid=' + dsid + '&drid=' + drid + '&iframetarget=1&tk=' + token + ' -->

    var ifId = 'iFrameId';
    var ifSrc = 'iFrameSrc';

    if (isTableType(T, 'async')) {
        ifId = ifId.toLowerCase();
        ifSrc = ifSrc.toLowerCase();
    }

    var iFrameId = $(nTr).attr(ifId);
    var iFrameSrc = $(nTr).attr(ifSrc);

    var token = gup('tk');

    var src = "";
    src = iFrameSrc + '&iframetarget=1&tk=' + token;

    if (!isTableType(T, 'async')) {
        _.defer(function () {
            $('.iFrameLoadingContainer').hide();
            $('#' + iFrameId).show();
        });
    } else {

        if (!adjustIFrameHeightOverridden) {
            var original = window['adjustIFrameHeight'];

            window['adjustIFrameHeight'] = function (ele) {
                $('.iFrameLoadingContainer').fadeOut('fast', function () {
                    var $ele = $(ele);
                    $ele.show();
                    $ele.height(Math.min($ele.contents().find('body').height() + 10, 70));

                    if (_.isFunction(original)) {
                        original(ele);
                    }

                    if (window !== getTopFrame()) {
                        common.refreshParentIframeHeight();
                    }
                });


            }

            adjustIFrameHeightOverridden = true;
        }
    }

    var sOut =
        '<div class="innerDetails">' +
        '<div class="iFrameLoadingContainer position-relative py-3"><div class="progress-loader-container d-flex justify-content-center align-items-middle"><div class="progress-loader"><i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i></div></div></div>' +
        '<iframe id="' + iFrameId + '" src="' + src + '" frameborder="0" width="100%" scrolling="no" onload="adjustIFrameHeight(this)" style=\"display:none;\"></iframe>' +
        '</div>';

    return sOut;
}

//Get URL params
function gup(name, fromHref) {
    name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
    var regexS = "[\\?&]" + name + "=([^&#;]*)";
    var regex = new RegExp(regexS);
    var results = regex.exec(fromHref || window.location.href);
    if (results == null)
        return "";
    else
        return results[1];
}

//Get URL params from parent
function gupfp(name) {
    name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
    var regexS = "[\\?&]" + name + "=([^&#;]*)";
    var regex = new RegExp(regexS);
    var results = regex.exec(window.parent.location.href);
    if (results == null)
        return "";
    else
        return results[1];
}

//Get URL params from custom url
function gupfcu(name, url) {
    name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
    var regexS = "[\\?&]" + name + "=([^&#;]*)";
    var regex = new RegExp(regexS);
    var results = regex.exec(url);
    if (results == null)
        return "";
    else
        return results[1];
}

function adjustIFrameHeight(ele) {
    var wrapper = $(ele).parent('.dataTablesContentWrapper');
    wrapper.css('min-height', 0);
    var contentHeight = $(ele.contentWindow.document.body).find('#page').height();
    $(ele).css('height', contentHeight + 'px');
    $(ele).show();
}

function adjustParentIFrameHeight(currWindow) {
    if (isFrameAccessible(currWindow) && currWindow != undefined) {
        var parentIFrame = currWindow.parent.document.getElementById('data-model-data-records');
        if (parentIFrame == null)
            parentIFrame = currWindow.parent.document.getElementById('lookup-table-id');
        if (parentIFrame == null)
            parentIFrame = currWindow.parent.document.getElementById('project-id');
        var height = $(parentIFrame).contents().find('body').outerHeight() + 0;

        if (parentIFrame !== null) {
            $(parentIFrame).height(height);
        }

        if (currWindow.parent != getTopFrame()) {
            adjustParentIFrameHeight(currWindow.parent);
        }
    }
}

//PRINOVA CUSTOM: fix the sorting/colreorder/colvis/searchfilter bugs with the drilldown
function _fnClearDrillDownMenus(T) {
    var nodeToCollapse = null;

    $('div', T).each(function () {
        if ($(this).has('.drill-down-close-icon').length) {
            nodeToCollapse = this.parentNode;

            var nTr = nodeToCollapse.parentNode;
            var i = $.inArray(nTr, anOpen);

            $(nTr).removeClass(rowSelected);

            // Display: Restore default table cell visuals
            $(nTr).find('td:first, td:nth-child(2)').removeClass('drillDown-connected-cell');

            $('.drill-down-icon', nodeToCollapse).attr('class', "drill-down-icon far fa-angle-right drill-down-open-icon");
            $('.innerDetails', $(nTr).next().get(0)).slideUp(function () {
                T.fnClose(nTr);
                anOpen.splice(i, 1);
            });
        }
    });
}

//for Async component
function appendIFrameData(T) {
    $('tbody:first > tr', T).each(function () {
        var row = $(this);

        $('input.iframe-data', this).each(function () {
            var name = $(this).attr('id');
            var value = $(this).val();
            $(row).attr(name, value);
        });
    });
}

function highlightListItemSearchValue(o, value, highlightPerWord, fuzzyHighlights) {

    function highlightTerm(s, term, exactMatch) {
        try {
            var termRegexString = term;
            if (typeof s === 'string' || s instanceof String) {
                termRegexString = termRegexString.replace(/\(/g, '\\(').replace(/\)/g, '\\)').replace(/\*/g, '\\*').replace(/\./g, '\\.');
                var regex = new RegExp('(' + termRegexString + ')', 'gi');
                if (exactMatch) {
                    termRegexString = '\\b' + termRegexString + '\\b|\\b' +termRegexString +'$';
                    regex = new RegExp('(' + termRegexString + ')', 'gi');
                }
                return s.replace(regex, "<span class=\"dataTablesSearchMatch\">$1</span>");
            }
        } catch (e) {
            console.log('Highlighting "' + term + '" term has failed.')
        }
    }

    if (value == undefined || value.length == 0)
        return o;

    var terms = highlightPerWord ? value.split(' ') : new Array();
    if ( !highlightPerWord || highlightPerWord == false )
        terms[0] = value;

    if ( fuzzyHighlights != undefined ) {
        var highlightEle = $("<div></div>").append(fuzzyHighlights);
        $(highlightEle).find('highlight').each( function() {
            if ( highlightPerWord) {
                terms[terms.length] = $(this).html();
            }
        });
    }

    for ( var i = 0; i < terms.length; i++ )
        o = highlightTerm(o, terms[i], !highlightPerWord);

    return o;
}

function triggerSearch(tableId, input) {
    var t = $('#' + tableId).dataTable();
    t.fnFilter($(input).val());
}

function showSelectAllDrillDown(element) {
    var tableId = $(element).attr('id').replace('select_all_', '');

    $('#label_all_in_page_' + tableId).text(client_messages.text.select + ' ' + $('#' + tableId + '_end_item').text() + ' ' + client_messages.text.select_on_this_page);
    $('#label_all_pages_' + tableId).text(client_messages.text.select_all + ' ' + $('#' + tableId + '_total').text());

    $('#select_all_drill_down_' + tableId).show();

}

function applySelectAllInCurrentPage(element) {
    var tableId = $(element).attr('id').replace('all_in_page_', '');
    $('#select_all_' + tableId).removeClass("fa-angle-double-down").addClass("fa-angle-down");
    selectAllElementsInPage(element);
    $('#checkbox_all_pages_' + tableId).prop('checked', false);
    $('#select_all_drill_down_' + tableId).hide();
}
function applySelectAllInAllPages(element) {
    var tableId = $(element).attr('id').replace('all_pages_', '');
    $('#select_all_' + tableId).removeClass("fa-angle-down").addClass("fa-angle-double-down");
    selectAllElementsInAllPages(element);
    $('#checkbox_all_in_page_' + tableId).prop('checked', false);
    $('#select_all_drill_down_' + tableId).hide();
}

function selectAllElementsInPage(element) {
    $("input[id$='rationalizerConsolidate']").each(
        function () {
            $(this).prop('checked', true);
        }
    );
    $("input[id^='listItemCheck']").each(
        function () {
            $(this).prop('checked', true);
        }
    );
    var tableId = $(element).attr('id').replace('all_in_page_', '');
    $('#' + tableId + ' > tbody > tr').each(function () {
        $(this).addClass(rowSelected);
    });


    if ($.isFunction(validateActionReq))
         validateActionReq(element);
}

function selectAllElementsInAllPages(element) {
    $("input[id$='rationalizerConsolidate']").each(
        function () {
            $(this).prop('checked', true);
        }
    );
    $("input[id^='listItemCheck']").each(
        function () {
            $(this).prop('checked', true);
        }
    );

    var tableId = $(element).attr('id').replace('all_pages_', '');

    $('#checkbox_all_in_page_' + tableId).prop('checked', false);

    $('#' + tableId + ' > tbody > tr').each(function () {
        $(this).addClass(rowSelected);
    });

    if ($.isFunction(validateActionReq))
        validateActionReq(element);
}

function deselectAllElementsInAllPages(tableId) {
    $("input[id$='rationalizerConsolidate']").each(
        function () {
            $(this).prop('checked', false);
        }
    );
    $("input[id^='listItemCheck']").each(
        function () {
            $(this).prop('checked', false);
        }
    );

    if ($.isFunction(validateActionReq))
        validateActionReq(element);
}