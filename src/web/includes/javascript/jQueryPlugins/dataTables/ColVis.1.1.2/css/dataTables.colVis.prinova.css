/*
 * An alternative styling for ColVis
 * Note you will likely have to change the path for the background image used by jQuery UI theming:
 *   ../../../../examples/examples_support/themes/smoothness
 */

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * ColVis styles
 */

/* PRINOVA CUSTOM */

div.ColVis_collectionBackground {
	position: fixed;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-color: black;
	z-index: 1100;
}

ul.ColVis_collection {
	position: relative;
	list-style: none;
	min-width: 150px;
	padding: 8px 0;
	margin: 8px 0 0 -16px;
	background: #fff;
	z-index: 2002;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	-webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
	box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

ul.ColVis_collection::before {
	content: "\00a0";
	position: absolute;
	top: -8px;
	left: 24px;
	margin-left: -8px;
	width: 0;
	height: 0;
	z-index: 9001;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid white;
}

ul.ColVis_collection li {
	display: block;
	padding: 0 18px;
	border: 0;
	background: #fff;
	line-height: 32px;
	cursor: pointer;
	color: #444;
}

ul.ColVis_collection li:hover {
	background: #f5f5f5;
}

ul.ColVis_collection li.ColVis_Button:hover {
	border: 1px solid #999;
	background-color: #f0f0f0;
}

ul.ColVis_collection li span {
	display: inline-block;
	font-weight: 300;
	padding: 0 0 0 9px;
	cursor: pointer;
}

ul.ColVis_collection li input {
	margin: 0;
}

ul.ColVis_collection li.ColVis_Special {
	border-color: #555;
	background: rgb(237,237,237); /* Old browsers */
	background: -webkit-linear-gradient(top, rgba(237,237,237,1) 0%,rgba(214,214,214,1) 77%,rgba(232,232,232,1) 100%); /* Chrome10+,Safari5.1+ */
	background:    -moz-linear-gradient(top, rgba(237,237,237,1) 0%, rgba(214,214,214,1) 77%, rgba(232,232,232,1) 100%); /* FF3.6+ */
	background:     -ms-linear-gradient(top, rgba(237,237,237,1) 0%,rgba(214,214,214,1) 77%,rgba(232,232,232,1) 100%); /* IE10+ */
	background:      -o-linear-gradient(top, rgba(237,237,237,1) 0%,rgba(214,214,214,1) 77%,rgba(232,232,232,1) 100%); /* Opera 11.10+ */
	background:         linear-gradient(to bottom, rgba(237,237,237,1) 0%,rgba(214,214,214,1) 77%,rgba(232,232,232,1) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ededed', endColorstr='#e8e8e8',GradientType=0 ); /* IE6-9 */
}

ul.ColVis_collection li.ColVis_Special:hover {
	background: #e2e2e2; /* Old browsers */
	background: -webkit-linear-gradient(top, #d0d0d0 0%,#d5d5d5 89%,#e2e2e2 100%); /* Chrome10+,Safari5.1+ */
	background:    -moz-linear-gradient(top, #d0d0d0 0%,#d5d5d5 89%,#e2e2e2 100%); /* FF3.6+ */
	background:     -ms-linear-gradient(top, #d0d0d0 0%,#d5d5d5 89%,#e2e2e2 100%); /* IE10+ */
	background:      -o-linear-gradient(top, #d0d0d0 0%,#d5d5d5 89%,#e2e2e2 100%); /* Opera 11.10+ */
	background:         linear-gradient(top, #d0d0d0 0%,#d5d5d5 89%,#e2e2e2 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f3f3f3', endColorstr='#e2e2e2',GradientType=0 ); /* IE6-9 */
}

















.ColVis {
	position: absolute;
	right: 0;
	top: 0;
	width: 17px;
	height: 15px;
}

.ColVis_MasterButton {
	height: 99%;
	width: 100%;
	/*border-left-width: 0;*/
	border-right: 1px solid #bbb;
	cursor: pointer;
	*cursor: hand;
	background: transparent;
	z-index: 1;
}
.ColVis_Button.ui-state-default {
	background: transparent;
}

.ShowHideColumn_Btn {
	width: 10px;
	border: none;
}

.ShowHideColumn_Div {
	text-align: left;
	position: relative;
	border: none;
	cursor: pointer;
	*cursor: hand;
	margin-bottom: 1px;
	padding: 1px;
	-webkit-border-radius: 4px;  
    -moz-border-radius: 4px;  
    border-radius: 4px;
}

.ShowHideColumn_Div:hover {
	border: none;
	background-color: #f0f0f0;
}

.ShowHideColumn_Label {
	font-size: 1em;
	position: relative;
	top: -2px;
	padding-left: 4px;
}	

button.ColVis_Button::-moz-focus-inner { 
	border: none !important;
	padding: 0;
}

.ColVis_text_hover {
	border: 1px solid #999;
	background-color: #f0f0f0;
}

div.ColVis_collectionBackground {
	background-color: black;
	z-index: 1100;
}

div.ColVis_collection {
	font-size: 12px;
	position: relative;
	width: 140px;
	padding: 3px;
	z-index: 1102;
	border: 1px solid #bbb;
	background-color: #fefefe;
	-moz-box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.15);
	-webkit-box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.15);
	box-shadow:0px 3px 3px 0px rgba(119, 119, 119, 0.15);
	-webkit-border-radius: 4px;  
    -moz-border-radius: 4px;  
    border-radius: 4px;
	margin-left: -4px;
	margin-top: 4px;
}

div.ColVis_collection button.ColVis_Button {
	height: 30px;
	width: 100%;
	margin-right: 3px;
	margin-bottom: 2px;
	padding: 3px 5px;
	cursor: pointer;
	*cursor: hand;
	text-align: left;
}

div.ColVis_collection button.ColVis_Button:hover {
	border: 1px solid #999;
	background-color: #f0f0f0;
}

div.ColVis_catcher {
	position: absolute;
	z-index: 1101;
}

span.ColVis_radio {
	display: inline-block;
	width: 20px;
}

button.ColVis_Restore {
	margin-top: 15px;
}

button.ColVis_Restore span {
	display: inline-block;
	padding-left: 10px;
	text-align: left;
}

.disabled {
	color: #999;
}



/*
 * Styles needed for DataTables scrolling
 */
div.dataTables_scrollHead {
	position: relative;
	overflow: hidden;
}

div.dataTables_scrollBody {
	overflow-y: scroll;
}

div.dataTables_scrollFoot {
	overflow: hidden;
}
