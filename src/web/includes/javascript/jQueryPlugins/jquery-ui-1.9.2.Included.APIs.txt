[PLEASE MAINTAIN WHEN UPGRADING JQUERY UI]

UI Core (All: Core, Widget, Mouse, Position)

A required dependency, contains basic functions and initializers.
UI Core The core of jQuery UI, required for all interactions and widgets.

Interactions (All)

These add basic behaviors to any element and are used by many components below.
Draggable Makes any element on the page draggable.
Droppable Generated drop targets for draggable elements.
Resizable Makes any element on the page resizable.
Selectable Makes a list of elements mouse selectable by dragging a box or clicking on them.
Sortable Makes a list of items sortable

Widgets (All)

Accordion 		Displays collapsible content panels for presenting information in a limited amount of space.
Autocomplete 	Lists suggested words as the user is typing.
But<PERSON> 			Enhances a form with themable buttons.
Datepicker 		Displays a calendar from an input or inline for selecting dates.
Dialog 			Displays customizable dialog windows.
Menu 			Creates nestable menus.
Progressbar 	Displays a status indicator for loading state, standard percentage, and other progress indicators.
Slider 			Displays a flexible slider with ranges and accessibility via keyboard.
Spinner 		Displays buttons to easily input numbers via the keyboard or mouse.
Tabs 			Transforms a set of container elements into a tab structure.
Tooltip 		Shows additional information for any element on hover or focus. 

Effects (All)

A rich effect API and ready to use effects.
Effects Core Extends the internal jQuery effects, includes morphing, easing and is required by all other effects.
Effect "Blind" Blinds the element.
Effect "Bounce" Bounces an element horizontally or vertically n-times.
Effect "Clip" Clips the element on and off like an old TV.
Effect "Drop" A Drop out effect by moving the element in one direction and hiding it at the same time.
Effect "Explode" The element explodes in all directions into n pieces. Also supports imploding again.
Effect "Fade" Fades the element. 
Effect "Fold" Folds the element first horizontally and then vertically.
Effect "Highlight" Highlights the background of the element in a defined color for a custom duration.
Effect "Pulsate" The element pulsates n times by changing the opacity to zero and back.
Effect "Scale" Grow or shrink any element and it's content and restore it again.
Effect "Shake" Shakes the element horizontally or vertically n times.
Effect "Slide" The element slides in and out of the viewport.
Effect "Transfer" Transfer effect from one element to another.
