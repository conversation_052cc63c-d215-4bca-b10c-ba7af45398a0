ace.define("ace/ext/hardwrap",["require","exports","module","ace/range"],function(e,t,n){"use strict";function i(e,t){function d(e,t,n){if(e.length<t)return;var r=e.slice(0,t),i=e.slice(t),s=/^(?:(\s+)|(\S+)(\s+))/.exec(i),o=/(?:(\s+)|(\s+)(\S+))$/.exec(r),u=0,a=0;o&&!o[2]&&(u=t-o[1].length,a=t),s&&!s[2]&&(u||(u=t),a=t+s[1].length);if(u)return{start:u,end:a};if(o&&o[2]&&o.index>n)return{start:o.index,end:o.index+o[3].length}}var n=t.column||e.getOption("printMarginColumn"),i=Math.min(t.startRow,t.endRow),s=Math.max(t.startRow,t.endRow),o=e.session;while(i<=s){var u=o.getLine(i);if(u.length>n){var a=d(u,n,5);a&&o.replace(new r(i,a.start,i,a.end),"\n"),s++}else if(/\S/.test(u)&&i!=s){var f=o.getLine(i+1);if(f&&/\S/.test(f)){var l=u.replace(/\s+$/,""),c=f.replace(/^\s+/,""),h=l+" "+c,a=d(h,n,5);if(a&&a.start>l.length||h.length<n){var p=new r(i,l.length,i+1,f.length-c.length);o.replace(p," "),i--,s--}}}i++}}var r=e("../range").Range;t.hardWrap=i}),ace.define("ace/keyboard/vim",["require","exports","module","ace/range","ace/lib/event_emitter","ace/lib/dom","ace/lib/oop","ace/lib/keys","ace/lib/event","ace/search","ace/lib/useragent","ace/search_highlight","ace/commands/multi_select_commands","ace/mode/text","ace/ext/hardwrap","ace/multi_select"],function(e,t,n){"use strict";function r(){function t(e){return typeof e!="object"?e+"":"line"in e?e.line+":"+e.ch:"anchor"in e?t(e.anchor)+"->"+t(e.head):Array.isArray(e)?"["+e.map(function(e){return t(e)})+"]":JSON.stringify(e)}var e="";for(var n=0;n<arguments.length;n++){var r=arguments[n],i=t(r);e+=i+"  "}console.log(e)}function g(e){return{row:e.line,column:e.ch}}function y(e){return new x(e.row,e.column)}function N(e){e.setOption("disableInput",!0),e.setOption("showCursorWhenSelecting",!1),m.signal(e,"vim-mode-change",{mode:"normal"}),e.on("cursorActivity",sr),ot(e),m.on(e.getInputField(),"paste",D(e))}function C(e){e.setOption("disableInput",!1),e.off("cursorActivity",sr),m.off(e.getInputField(),"paste",D(e)),e.state.vim=null}function k(e,t){this==m.keyMap.vim&&m.rmClass(e.getWrapperElement(),"cm-fat-cursor"),(!t||t.attach!=L)&&C(e)}function L(e,t){this==m.keyMap.vim&&m.addClass(e.getWrapperElement(),"cm-fat-cursor"),(!t||t.attach!=L)&&N(e)}function A(e,t){if(!t)return undefined;if(this[e])return this[e];var n=_(e);if(!n)return!1;var r=m.Vim.findKey(t,n);return typeof r=="function"&&m.signal(t,"vim-keypress",n),r}function _(e){if(e.charAt(0)=="'")return e.charAt(1);var t=e.split(/-(?!$)/),n=t[t.length-1];if(t.length==1&&t[0].length==1)return!1;if(t.length==2&&t[0]=="Shift"&&n.length==1)return!1;var r=!1;for(var i=0;i<t.length;i++){var s=t[i];s in O?t[i]=O[s]:r=!0,s in M&&(t[i]=M[s])}return r?(K(n)&&(t[t.length-1]=n.toLowerCase()),"<"+t.join("-")+">"):!1}function D(e){var t=e.state.vim;return t.onPasteFn||(t.onPasteFn=function(){t.insertMode||(e.setCursor(kt(e.getCursor(),0,1)),xt.enterInsertMode(e,{},t))}),t.onPasteFn}function j(e,t){var n=[];for(var r=e;r<e+t;r++)n.push(String.fromCharCode(r));return n}function X(e,t){return t>=e.firstLine()&&t<=e.lastLine()}function V(e){return/^[a-z]$/.test(e)}function $(e){return"()[]{}".indexOf(e)!=-1}function J(e){return P.test(e)}function K(e){return z.test(e)}function Q(e){return/^\s*$/.test(e)}function G(e){return".?!".indexOf(e)!=-1}function Y(e,t){for(var n=0;n<t.length;n++)if(t[n]==e)return!0;return!1}function et(e,t,n,r,i){if(t===undefined&&!i)throw Error("defaultValue is required unless callback is provided");n||(n="string"),Z[e]={type:n,defaultValue:t,callback:i};if(r)for(var s=0;s<r.length;s++)Z[r[s]]=Z[e];t&&tt(e,t)}function tt(e,t,n,r){var i=Z[e];r=r||{};var s=r.scope;if(!i)return new Error("Unknown option: "+e);if(i.type=="boolean"){if(t&&t!==!0)return new Error("Invalid argument: "+e+"="+t);t!==!1&&(t=!0)}i.callback?(s!=="local"&&i.callback(t,undefined),s!=="global"&&n&&i.callback(t,n)):(s!=="local"&&(i.value=i.type=="boolean"?!!t:t),s!=="global"&&n&&(n.state.vim.options[e]={value:t}))}function nt(e,t,n){var r=Z[e];n=n||{};var i=n.scope;if(!r)return new Error("Unknown option: "+e);if(r.callback){var s=t&&r.callback(undefined,t);if(i!=="global"&&s!==undefined)return s;if(i!=="local")return r.callback();return}var s=i!=="global"&&t&&t.state.vim.options[e];return(s||i!=="local"&&r||{}).value}function st(){this.latestRegister=undefined,this.isPlaying=!1,this.isRecording=!1,this.replaySearchQueries=[],this.onRecordingDone=undefined,this.lastInsertModeChanges=it()}function ot(e){return e.state.vim||(e.state.vim={inputState:new ct,lastEditInputState:undefined,lastEditActionCommand:undefined,lastHPos:-1,lastHSPos:-1,lastMotion:null,marks:{},fakeCursor:null,insertMode:!1,insertModeRepeat:undefined,visualMode:!1,visualLine:!1,visualBlock:!1,lastSelection:null,lastPastedText:null,sel:{},options:{}}),e.state.vim}function at(){ut={searchQuery:null,searchIsReversed:!1,lastSubstituteReplacePart:undefined,jumpList:rt(),macroModeState:new st,lastCharacterSearch:{increment:0,forward:!0,selectedCharacter:""},registerController:new vt({}),searchHistoryController:new mt,exCommandHistoryController:new mt};for(var e in Z){var t=Z[e];t.value=t.defaultValue}}function ct(){this.prefixRepeat=[],this.motionRepeat=[],this.operator=null,this.operatorArgs=null,this.motion=null,this.motionArgs=null,this.keyBuffer=[],this.registerName=null}function ht(e,t){e.state.vim.inputState=new ct,m.signal(e,"vim-command-done",t)}function pt(e,t,n){this.clear(),this.keyBuffer=[e||""],this.insertModeChanges=[],this.searchQueries=[],this.linewise=!!t,this.blockwise=!!n}function dt(e,t){var n=ut.registerController.registers;if(!e||e.length!=1)throw Error("Register name must be 1 character");n[e]=t,U.push(e)}function vt(e){this.registers=e,this.unnamedRegister=e['"']=new pt,e["."]=new pt,e[":"]=new pt,e["/"]=new pt}function mt(){this.historyBuffer=[],this.iterator=0,this.initialPrefix=null}function bt(e,t){yt[e]=t}function wt(e,t){var n=[];for(var r=0;r<t;r++)n.push(e);return n}function St(e,t){Et[e]=t}function Tt(e,t){xt[e]=t}function Nt(e,t){var n=e.state.vim,r=n.insertMode||n.visualMode,i=Math.min(Math.max(e.firstLine(),t.line),e.lastLine()),s=Ft(e,i)-1+!!r,o=Math.min(Math.max(0,t.ch),s);return x(i,o)}function Ct(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function kt(e,t,n){return typeof t=="object"&&(n=t.ch,t=t.line),x(e.line+t,e.ch+n)}function Lt(e,t,n,r){var i,s=[],o=[];for(var u=0;u<t.length;u++){var a=t[u];if(n=="insert"&&a.context!="insert"||a.context&&a.context!=n||r.operator&&a.type=="action"||!(i=At(e,a.keys)))continue;i=="partial"&&s.push(a),i=="full"&&o.push(a)}return{partial:s.length&&s,full:o.length&&o}}function At(e,t){if(t.slice(-11)=="<character>"){var n=t.length-11,r=e.slice(0,n),i=t.slice(0,n);return r==i&&e.length>n?"full":i.indexOf(r)==0?"partial":!1}return e==t?"full":t.indexOf(e)==0?"partial":!1}function Ot(e){var t=/^.*(<[^>]+>)$/.exec(e),n=t?t[1]:e.slice(-1);if(n.length>1)switch(n){case"<CR>":n="\n";break;case"<Space>":n=" ";break;default:n=""}return n}function Mt(e,t,n){return function(){for(var r=0;r<n;r++)t(e)}}function _t(e){return x(e.line,e.ch)}function Dt(e,t){return e.ch==t.ch&&e.line==t.line}function Pt(e,t){return e.line<t.line?!0:e.line==t.line&&e.ch<t.ch?!0:!1}function Ht(e,t){return arguments.length>2&&(t=Ht.apply(undefined,Array.prototype.slice.call(arguments,1))),Pt(e,t)?e:t}function Bt(e,t){return arguments.length>2&&(t=Bt.apply(undefined,Array.prototype.slice.call(arguments,1))),Pt(e,t)?t:e}function jt(e,t,n){var r=Pt(e,t),i=Pt(t,n);return r&&i}function Ft(e,t){return e.getLine(t).length}function It(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function qt(e){return e.replace(/([.?*+$\[\]\/\\(){}|\-])/g,"\\$1")}function Rt(e,t,n){var r=Ft(e,t),i=(new Array(n-r+1)).join(" ");e.setCursor(x(t,r)),e.replaceRange(i,e.getCursor())}function Ut(e,t){var n=[],r=e.listSelections(),i=_t(e.clipPos(t)),s=!Dt(t,i),o=e.getCursor("head"),u=Wt(r,o),a=Dt(r[u].head,r[u].anchor),f=r.length-1,l=f-u>u?f:0,c=r[l].anchor,h=Math.min(c.line,i.line),p=Math.max(c.line,i.line),d=c.ch,v=i.ch,m=r[l].head.ch-d,g=v-d;m>0&&g<=0?(d++,s||v--):m<0&&g>=0?(d--,a||v++):m<0&&g==-1&&(d--,v++);for(var y=h;y<=p;y++){var b={anchor:new x(y,d),head:new x(y,v)};n.push(b)}return e.setSelections(n),t.ch=v,c.ch=d,c}function zt(e,t,n){var r=[];for(var i=0;i<n;i++){var s=kt(t,i,0);r.push({anchor:s,head:s})}e.setSelections(r,0)}function Wt(e,t,n){for(var r=0;r<e.length;r++){var i=n!="head"&&Dt(e[r].anchor,t),s=n!="anchor"&&Dt(e[r].head,t);if(i||s)return r}return-1}function Xt(e,t){var n=t.lastSelection,r=function(){var t=e.listSelections(),n=t[0],r=t[t.length-1],i=Pt(n.anchor,n.head)?n.anchor:n.head,s=Pt(r.anchor,r.head)?r.head:r.anchor;return[i,s]},i=function(){var t=e.getCursor(),r=e.getCursor(),i=n.visualBlock;if(i){var s=i.width,o=i.height;r=x(t.line+o,t.ch+s);var u=[];for(var a=t.line;a<r.line;a++){var f=x(a,t.ch),l=x(a,r.ch),c={anchor:f,head:l};u.push(c)}e.setSelections(u)}else{var h=n.anchorMark.find(),p=n.headMark.find(),d=p.line-h.line,v=p.ch-h.ch;r={line:r.line+d,ch:d?r.ch:v+r.ch},n.visualLine&&(t=x(t.line,0),r=x(r.line,Ft(e,r.line))),e.setSelection(t,r)}return[t,r]};return t.visualMode?r():i()}function Vt(e,t){var n=t.sel.anchor,r=t.sel.head;t.lastPastedText&&(r=e.posFromIndex(e.indexFromPos(n)+t.lastPastedText.length),t.lastPastedText=null),t.lastSelection={anchorMark:e.setBookmark(n),headMark:e.setBookmark(r),anchor:_t(n),head:_t(r),visualMode:t.visualMode,visualLine:t.visualLine,visualBlock:t.visualBlock}}function $t(e,t,n){var r=e.state.vim.sel,i=r.head,s=r.anchor,o;return Pt(n,t)&&(o=n,n=t,t=o),Pt(i,s)?(i=Ht(t,i),s=Bt(s,n)):(s=Ht(t,s),i=Bt(i,n),i=kt(i,0,-1),i.ch==-1&&i.line!=e.firstLine()&&(i=x(i.line-1,Ft(e,i.line-1)))),[s,i]}function Jt(e,t,n){var r=e.state.vim;t=t||r.sel;var n=n||r.visualLine?"line":r.visualBlock?"block":"char",i=Kt(e,t,n);e.setSelections(i.ranges,i.primary),or(e)}function Kt(e,t,n,r){var i=_t(t.head),s=_t(t.anchor);if(n=="char"){var o=!r&&!Pt(t.head,t.anchor)?1:0,u=Pt(t.head,t.anchor)?1:0;return i=kt(t.head,0,o),s=kt(t.anchor,0,u),{ranges:[{anchor:s,head:i}],primary:0}}if(n=="line"){if(!Pt(t.head,t.anchor)){s.ch=0;var a=e.lastLine();i.line>a&&(i.line=a),i.ch=Ft(e,i.line)}else i.ch=0,s.ch=Ft(e,s.line);return{ranges:[{anchor:s,head:i}],primary:0}}if(n=="block"){var f=Math.min(s.line,i.line),l=Math.min(s.ch,i.ch),c=Math.max(s.line,i.line),h=Math.max(s.ch,i.ch)+1,p=c-f+1,d=i.line==f?0:p-1,v=[];for(var m=0;m<p;m++)v.push({anchor:x(f+m,l),head:x(f+m,h)});return{ranges:v,primary:d}}}function Qt(e){var t=e.getCursor("head");return e.getSelection().length==1&&(t=Ht(t,e.getCursor("anchor"))),t}function Gt(e,t){var n=e.state.vim;t!==!1&&e.setCursor(Nt(e,n.sel.head)),Vt(e,n),n.visualMode=!1,n.visualLine=!1,n.visualBlock=!1,n.insertMode||m.signal(e,"vim-mode-change",{mode:"normal"}),ur(n)}function Yt(e,t,n){var r=e.getRange(t,n);if(/\n\s*$/.test(r)){var i=r.split("\n");i.pop();var s;for(var s=i.pop();i.length>0&&s&&Q(s);s=i.pop())n.line--,n.ch=0;s?(n.line--,n.ch=Ft(e,n.line)):n.ch=0}}function Zt(e,t,n){t.ch=0,n.ch=0,n.line++}function en(e){if(!e)return 0;var t=e.search(/\S/);return t==-1?e.length:t}function tn(e,t,n,r,i){var s=Qt(e),o=e.getLine(s.line),u=s.ch,a=i?H[0]:B[0];while(!a(o.charAt(u))){u++;if(u>=o.length)return null}r?a=B[0]:(a=H[0],a(o.charAt(u))||(a=H[1]));var f=u,l=u;while(a(o.charAt(f))&&f<o.length)f++;while(a(o.charAt(l))&&l>=0)l--;l++;if(t){var c=f;while(/\s/.test(o.charAt(f))&&f<o.length)f++;if(c==f){var h=l;while(/\s/.test(o.charAt(l-1))&&l>0)l--;l||(l=h)}}return{start:x(s.line,l),end:x(s.line,f)}}function nn(e,t,n){var r=t;if(!m.findMatchingTag||!m.findEnclosingTag)return{start:r,end:r};var i=m.findMatchingTag(e,t)||m.findEnclosingTag(e,t);return!i||!i.open||!i.close?{start:r,end:r}:n?{start:i.open.from,end:i.close.to}:{start:i.open.to,end:i.close.from}}function rn(e,t,n){Dt(t,n)||ut.jumpList.add(e,t,n)}function sn(e,t){ut.lastCharacterSearch.increment=e,ut.lastCharacterSearch.forward=t.forward,ut.lastCharacterSearch.selectedCharacter=t.selectedCharacter}function an(e,t,n,r){var i=_t(e.getCursor()),s=n?1:-1,o=n?e.lineCount():-1,u=i.ch,a=i.line,f=e.getLine(a),l={lineText:f,nextCh:f.charAt(u),lastCh:null,index:u,symb:r,reverseSymb:(n?{")":"(","}":"{"}:{"(":")","{":"}"})[r],forward:n,depth:0,curMoveThrough:!1},c=on[r];if(!c)return i;var h=un[c].init,p=un[c].isComplete;h&&h(l);while(a!==o&&t){l.index+=s,l.nextCh=l.lineText.charAt(l.index);if(!l.nextCh){a+=s,l.lineText=e.getLine(a)||"";if(s>0)l.index=0;else{var d=l.lineText.length;l.index=d>0?d-1:0}l.nextCh=l.lineText.charAt(l.index)}p(l)&&(i.line=a,i.ch=l.index,t--)}return l.nextCh||l.curMoveThrough?x(a,l.index):i}function fn(e,t,n,r,i){var s=t.line,o=t.ch,u=e.getLine(s),a=n?1:-1,f=r?B:H;if(i&&u==""){s+=a,u=e.getLine(s);if(!X(e,s))return null;o=n?0:u.length}for(;;){if(i&&u=="")return{from:0,to:0,line:s};var l=a>0?u.length:-1,c=l,h=l;while(o!=l){var p=!1;for(var d=0;d<f.length&&!p;++d)if(f[d](u.charAt(o))){c=o;while(o!=l&&f[d](u.charAt(o)))o+=a;h=o,p=c!=h;if(c==t.ch&&s==t.line&&h==c+a)continue;return{from:Math.min(c,h+1),to:Math.max(c,h),line:s}}p||(o+=a)}s+=a;if(!X(e,s))return null;u=e.getLine(s),o=a>0?0:u.length}}function ln(e,t,n,r,i,s){var o=_t(t),u=[];(r&&!i||!r&&i)&&n++;var a=!r||!i;for(var f=0;f<n;f++){var l=fn(e,t,r,s,a);if(!l){var c=Ft(e,e.lastLine());u.push(r?{line:e.lastLine(),from:c,to:c}:{line:0,from:0,to:0});break}u.push(l),t=x(l.line,r?l.to-1:l.from)}var h=u.length!=n,p=u[0],d=u.pop();return r&&!i?(!h&&(p.from!=o.ch||p.line!=o.line)&&(d=u.pop()),x(d.line,d.from)):r&&i?x(d.line,d.to-1):!r&&i?(!h&&(p.to!=o.ch||p.line!=o.line)&&(d=u.pop()),x(d.line,d.to)):x(d.line,d.from)}function cn(e,t,n,r,i){var s=t,o=x(s.line+n.repeat-1,Infinity),u=e.clipPos(o);return u.ch--,i||(r.lastHPos=Infinity,r.lastHSPos=e.charCoords(u,"div").left),o}function hn(e,t,n,r){var i=e.getCursor(),s=i.ch,o;for(var u=0;u<t;u++){var a=e.getLine(i.line);o=vn(s,a,r,n,!0);if(o==-1)return null;s=o}return x(e.getCursor().line,o)}function pn(e,t){var n=e.getCursor().line;return Nt(e,x(n,t-1))}function dn(e,t,n,r){if(!Y(n,R))return;t.marks[n]&&t.marks[n].clear(),t.marks[n]=e.setBookmark(r)}function vn(e,t,n,r,i){var s;return r?(s=t.indexOf(n,e+1),s!=-1&&!i&&(s-=1)):(s=t.lastIndexOf(n,e-1),s!=-1&&!i&&(s+=1)),s}function mn(e,t,n,r,i){function c(t){return!/\S/.test(e.getLine(t))}function h(e,t,n){return n?c(e)!=c(e+t):!c(e)&&c(e+t)}function p(t){r=r>0?1:-1;var n=e.ace.session.getFoldLine(t);n&&t+r>n.start.row&&t+r<n.end.row&&(r=(r>0?n.end.row:n.start.row)-t)}var s=t.line,o=e.firstLine(),u=e.lastLine(),a,f,l=s;if(r){while(o<=l&&l<=u&&n>0)p(l),h(l,r)&&n--,l+=r;return new x(l,0)}var d=e.state.vim;if(d.visualLine&&h(s,1,!0)){var v=d.sel.anchor;h(v.line,-1,!0)&&(!i||v.line!=s)&&(s+=1)}var m=c(s);for(l=s;l<=u&&n;l++)h(l,1,!0)&&(!i||c(l)!=m)&&n--;f=new x(l,0),l>u&&!m?m=!0:i=!1;for(l=s;l>o;l--)if(!i||c(l)==m||l==s)if(h(l,-1,!0))break;return a=new x(l,0),{start:a,end:f}}function gn(e,t,n,r){function i(e,t){if(t.pos+t.dir<0||t.pos+t.dir>=t.line.length){t.ln+=t.dir;if(!X(e,t.ln)){t.line=null,t.ln=null,t.pos=null;return}t.line=e.getLine(t.ln),t.pos=t.dir>0?0:t.line.length-1}else t.pos+=t.dir}function s(e,t,n,r){var s=e.getLine(t),o=s==="",u={line:s,ln:t,pos:n,dir:r},a={ln:u.ln,pos:u.pos},f=u.line==="";i(e,u);while(u.line!==null){a.ln=u.ln,a.pos=u.pos;if(u.line===""&&!f)return{ln:u.ln,pos:u.pos};if(o&&u.line!==""&&!Q(u.line[u.pos]))return{ln:u.ln,pos:u.pos};G(u.line[u.pos])&&!o&&(u.pos===u.line.length-1||Q(u.line[u.pos+1]))&&(o=!0),i(e,u)}var s=e.getLine(a.ln);a.pos=0;for(var l=s.length-1;l>=0;--l)if(!Q(s[l])){a.pos=l;break}return a}function o(e,t,n,r){var s=e.getLine(t),o={line:s,ln:t,pos:n,dir:r},u={ln:o.ln,pos:null},a=o.line==="";i(e,o);while(o.line!==null){if(o.line===""&&!a)return u.pos!==null?u:{ln:o.ln,pos:o.pos};if(!(!G(o.line[o.pos])||u.pos===null||o.ln===u.ln&&o.pos+1===u.pos))return u;o.line!==""&&!Q(o.line[o.pos])&&(a=!1,u={ln:o.ln,pos:o.pos}),i(e,o)}var s=e.getLine(u.ln);u.pos=0;for(var f=0;f<s.length;++f)if(!Q(s[f])){u.pos=f;break}return u}var u={ln:t.line,pos:t.ch};while(n>0)r<0?u=o(e,u.ln,u.pos,r):u=s(e,u.ln,u.pos,r),n--;return x(u.ln,u.pos)}function yn(e,t,n,r){var i=t,s,o,u={"(":/[()]/,")":/[()]/,"[":/[[\]]/,"]":/[[\]]/,"{":/[{}]/,"}":/[{}]/,"<":/[<>]/,">":/[<>]/}[n],a={"(":"(",")":"(","[":"[","]":"[","{":"{","}":"{","<":"<",">":"<"}[n],f=e.getLine(i.line).charAt(i.ch),l=f===a?1:0;s=e.scanForBracket(x(i.line,i.ch+l),-1,undefined,{bracketRegex:u}),o=e.scanForBracket(x(i.line,i.ch+l),1,undefined,{bracketRegex:u});if(!s||!o)return{start:i,end:i};s=s.pos,o=o.pos;if(s.line==o.line&&s.ch>o.ch||s.line>o.line){var c=s;s=o,o=c}return r?o.ch+=1:s.ch+=1,{start:s,end:o}}function bn(e,t,n,r){var i=_t(t),s=e.getLine(i.line),o=s.split(""),u,a,f,l,c=o.indexOf(n);i.ch<c?i.ch=c:c<i.ch&&o[i.ch]==n&&(a=i.ch,--i.ch);if(o[i.ch]==n&&!a)u=i.ch+1;else for(f=i.ch;f>-1&&!u;f--)o[f]==n&&(u=f+1);if(u&&!a)for(f=u,l=o.length;f<l&&!a;f++)o[f]==n&&(a=f);return!u||!a?{start:i,end:i}:(r&&(--u,++a),{start:x(i.line,u),end:x(i.line,a)})}function wn(){}function En(e){var t=e.state.vim;return t.searchState_||(t.searchState_=new wn)}function Sn(e){return Tn(e,"/")}function xn(e){return Nn(e,"/")}function Tn(e,t){var n=Nn(e,t)||[];if(!n.length)return[];var r=[];if(n[0]!==0)return;for(var i=0;i<n.length;i++)typeof n[i]=="number"&&r.push(e.substring(n[i]+1,n[i+1]));return r}function Nn(e,t){t||(t="/");var n=!1,r=[];for(var i=0;i<e.length;i++){var s=e.charAt(i);!n&&s==t&&r.push(i),n=!n&&s=="\\"}return r}function Cn(e){var t="|(){",n="}",r=!1,i=[];for(var s=-1;s<e.length;s++){var o=e.charAt(s)||"",u=e.charAt(s+1)||"",a=u&&t.indexOf(u)!=-1;r?((o!=="\\"||!a)&&i.push(o),r=!1):o==="\\"?(r=!0,u&&n.indexOf(u)!=-1&&(a=!0),(!a||u==="\\")&&i.push(o)):(i.push(o),a&&u!=="\\"&&i.push("\\"))}return i.join("")}function Ln(e){var t=!1,n=[];for(var r=-1;r<e.length;r++){var i=e.charAt(r)||"",s=e.charAt(r+1)||"";kn[i+s]?(n.push(kn[i+s]),r++):t?(n.push(i),t=!1):i==="\\"?(t=!0,J(s)||s==="$"?n.push("$"):s!=="/"&&s!=="\\"&&n.push("\\")):(i==="$"&&n.push("$"),n.push(i),s==="/"&&n.push("\\"))}return n.join("")}function On(e){var t=new m.StringStream(e),n=[];while(!t.eol()){while(t.peek()&&t.peek()!="\\")n.push(t.next());var r=!1;for(var i in An)if(t.match(i,!0)){r=!0,n.push(An[i]);break}r||n.push(t.next())}return n.join("")}function Mn(e,t,n){var r=ut.registerController.getRegister("/");r.setText(e);if(e instanceof RegExp)return e;var i=xn(e),s,o;if(!i.length)s=e;else{s=e.substring(0,i[0]);var u=e.substring(i[0]);o=u.indexOf("i")!=-1}if(!s)return null;nt("pcre")||(s=Cn(s)),n&&(t=/^[^A-Z]*$/.test(s));var a=new RegExp(s,t||o?"im":"m");return a}function _n(e){typeof e=="string"&&(e=document.createElement(e));for(var t,n=1;n<arguments.length;n++){if(!(t=arguments[n]))continue;typeof t!="object"&&(t=document.createTextNode(t));if(t.nodeType)e.appendChild(t);else for(var r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;r[0]==="$"?e.style[r.slice(1)]=t[r]:e.setAttribute(r,t[r])}}return e}function Dn(e,t){var n=_n("span",{$color:"red",$whiteSpace:"pre"},t);e.openNotification?e.openNotification(n,{bottom:!0,duration:5e3}):alert(n.innerText)}function Pn(e,t){return _n(document.createDocumentFragment(),_n("span",{$fontFamily:"monospace",$whiteSpace:"pre"},e,_n("input",{type:"text",autocorrect:"off",autocapitalize:"off",spellcheck:"false"})),t&&_n("span",{$color:"#888"},t))}function Hn(e,t){var n=(t.prefix||"")+" "+(t.desc||""),r=Pn(t.prefix,t.desc);e.openDialog?e.openDialog(r,t.onClose,{onKeyDown:t.onKeyDown,onKeyUp:t.onKeyUp,bottom:!0,selectValueOnOpen:!1,value:t.value}):t.onClose(prompt(n,""))}function Bn(e,t){if(e instanceof RegExp&&t instanceof RegExp){var n=["global","multiline","ignoreCase","source"];for(var r=0;r<n.length;r++){var i=n[r];if(e[i]!==t[i])return!1}return!0}return!1}function jn(e,t,n,r){if(!t)return;var i=En(e),s=Mn(t,!!n,!!r);if(!s)return;return In(e,s),Bn(s,i.getQuery())?s:(i.setQuery(s),s)}function Fn(e){if(e.source.charAt(0)=="^")var t=!0;return{token:function(n){if(t&&!n.sol()){n.skipToEnd();return}var r=n.match(e,!1);if(r){if(r[0].length==0)return n.next(),"searching";if(!n.sol()){n.backUp(1);if(!e.exec(n.next()+r[0]))return n.next(),null}return n.match(e),"searching"}while(!n.eol()){n.next();if(n.match(e,!1))break}},query:e}}function In(e,t){var n=En(e),r=n.getOverlay();if(!r||t!=r.query)r&&e.removeOverlay(r),r=Fn(t),e.addOverlay(r),e.showMatchesOnScrollbar&&(n.getScrollbarAnnotate()&&n.getScrollbarAnnotate().clear(),n.setScrollbarAnnotate(e.showMatchesOnScrollbar(t))),n.setOverlay(r)}function qn(e,t,n,r){return r===undefined&&(r=1),e.operation(function(){var i=e.getCursor(),s=e.getSearchCursor(n,i);for(var o=0;o<r;o++){var u=s.find(t);if(o==0&&u&&Dt(s.from(),i)){var a=t?s.from():s.to();u=s.find(t),u&&!u[0]&&Dt(s.from(),a)&&e.getLine(a.line).length==a.ch&&(u=s.find(t))}if(!u){s=e.getSearchCursor(n,t?x(e.lastLine()):x(e.firstLine(),0));if(!s.find(t))return}}return s.from()})}function Rn(e,t,n,r,i){return r===undefined&&(r=1),e.operation(function(){var s=e.getCursor(),o=e.getSearchCursor(n,s),u=o.find(!t);!i.visualMode&&u&&Dt(o.from(),s)&&o.find(!t);for(var a=0;a<r;a++){u=o.find(t);if(!u){o=e.getSearchCursor(n,t?x(e.lastLine()):x(e.firstLine(),0));if(!o.find(t))return}}return[o.from(),o.to()]})}function Un(e){var t=En(e);e.removeOverlay(En(e).getOverlay()),t.setOverlay(null),t.getScrollbarAnnotate()&&(t.getScrollbarAnnotate().clear(),t.setScrollbarAnnotate(null))}function zn(e,t,n){return typeof e!="number"&&(e=e.line),t instanceof Array?Y(e,t):typeof n=="number"?e>=t&&e<=n:e==t}function Wn(e){var t=e.ace.renderer;return{top:t.getFirstFullyVisibleRow(),bottom:t.getLastFullyVisibleRow()}}function Xn(e,t,n){if(n=="'"||n=="`")return ut.jumpList.find(e,-1)||x(0,0);if(n==".")return Vn(e);var r=t.marks[n];return r&&r.find()}function Vn(e){var t=e.ace.session.$undoManager;if(t&&t.$lastDelta)return y(t.$lastDelta.end)}function Qn(e,t,n,r,i,s,o,u,a){function p(){e.operation(function(){while(!f)d(),g();y()})}function d(){var t=e.getRange(s.from(),s.to()),n=t.replace(o,u),r=s.to().line;s.replace(n),c=s.to().line,i+=c-r,h=c<r}function v(){var e=l&&_t(s.to()),t=s.findNext();return t&&!t[0]&&e&&Dt(s.from(),e)&&(t=s.findNext()),t}function g(){while(v()&&zn(s.from(),r,i)){if(!n&&s.from().line==c&&!h)continue;e.scrollIntoView(s.from(),30),e.setSelection(s.from(),s.to()),l=s.from(),f=!1;return}f=!0}function y(t){t&&t(),e.focus();if(l){e.setCursor(l);var n=e.state.vim;n.exMode=!1,n.lastHPos=n.lastHSPos=l.ch}a&&a()}function b(t,n,r){m.e_stop(t);var i=m.keyName(t);switch(i){case"Y":d(),g();break;case"N":g();break;case"A":var s=a;a=undefined,e.operation(p),a=s;break;case"L":d();case"Q":case"Esc":case"Ctrl-C":case"Ctrl-[":y(r)}return f&&y(r),!0}e.state.vim.exMode=!0;var f=!1,l,c,h;g();if(f){Dn(e,"No matches for "+o.source);return}if(!t){p(),a&&a();return}Hn(e,{prefix:_n("span","replace with ",_n("strong",u)," (y/n/a/q/l)"),onKeyDown:b})}function Gn(e){var t=e.state.vim,n=ut.macroModeState,r=ut.registerController.getRegister("."),i=n.isPlaying,s=n.lastInsertModeChanges;i||(e.off("change",ir),m.off(e.getInputField(),"keydown",lr)),!i&&t.insertModeRepeat>1&&(cr(e,t,t.insertModeRepeat-1,!0),t.lastEditInputState.repeatOverride=t.insertModeRepeat),delete t.insertModeRepeat,t.insertMode=!1,e.setCursor(e.getCursor().line,e.getCursor().ch-1),e.setOption("keyMap","vim"),e.setOption("disableInput",!0),e.toggleOverwrite(!1),r.setText(s.changes.join("")),m.signal(e,"vim-mode-change",{mode:"normal"}),n.isRecording&&nr(n)}function Yn(e){w.unshift(e)}function Zn(e,t,n,r,i){var s={keys:e,type:t};s[t]=n,s[t+"Args"]=r;for(var o in i)s[o]=i[o];Yn(s)}function er(e,t,n,r){var i=ut.registerController.getRegister(r);if(r==":"){i.keyBuffer[0]&&Kn.processCommand(e,i.keyBuffer[0]),n.isPlaying=!1;return}var s=i.keyBuffer,o=0;n.isPlaying=!0,n.replaySearchQueries=i.searchQueries.slice(0);for(var u=0;u<s.length;u++){var a=s[u],f,l;while(a){f=/<\w+-.+?>|<\w+>|./.exec(a),l=f[0],a=a.substring(f.index+l.length),m.Vim.handleKey(e,l,"macro");if(t.insertMode){var c=i.insertModeChanges[o++].changes;ut.macroModeState.lastInsertModeChanges.changes=c,hr(e,c,1),Gn(e)}}}n.isPlaying=!1}function tr(e,t){if(e.isPlaying)return;var n=e.latestRegister,r=ut.registerController.getRegister(n);r&&r.pushText(t)}function nr(e){if(e.isPlaying)return;var t=e.latestRegister,n=ut.registerController.getRegister(t);n&&n.pushInsertModeChanges&&n.pushInsertModeChanges(e.lastInsertModeChanges)}function rr(e,t){if(e.isPlaying)return;var n=e.latestRegister,r=ut.registerController.getRegister(n);r&&r.pushSearchQuery&&r.pushSearchQuery(t)}function ir(e,t){var n=ut.macroModeState,r=n.lastInsertModeChanges;if(!n.isPlaying)while(t){r.expectCursorActivityForChange=!0;if(r.ignoreCount>1)r.ignoreCount--;else if(t.origin=="+input"||t.origin=="paste"||t.origin===undefined){var i=e.listSelections().length;i>1&&(r.ignoreCount=i);var s=t.text.join("\n");r.maybeReset&&(r.changes=[],r.maybeReset=!1),s&&(e.state.overwrite&&!/\n/.test(s)?r.changes.push([s]):r.changes.push(s))}t=t.next}}function sr(e){var t=e.state.vim;if(t.insertMode){var n=ut.macroModeState;if(n.isPlaying)return;var r=n.lastInsertModeChanges;r.expectCursorActivityForChange?r.expectCursorActivityForChange=!1:r.maybeReset=!0}else e.curOp.isVimOp||ar(e,t);t.visualMode&&or(e)}function or(e){}function ur(e){}function ar(e,t,n){var r=e.getCursor("anchor"),i=e.getCursor("head");t.visualMode&&!e.somethingSelected()?Gt(e,!1):!t.visualMode&&!t.insertMode&&e.somethingSelected()&&(t.visualMode=!0,t.visualLine=!1,m.signal(e,"vim-mode-change",{mode:"visual"}));if(t.visualMode){var s=Pt(i,r)?0:-1,o=Pt(i,r)?-1:0;i=kt(i,0,s),r=kt(r,0,o),t.sel={anchor:r,head:i},dn(e,t,"<",Ht(i,r)),dn(e,t,">",Bt(i,r))}else!t.insertMode&&!n&&(t.lastHPos=e.getCursor().ch)}function fr(e){this.keyName=e}function lr(e){function i(){return n.maybeReset&&(n.changes=[],n.maybeReset=!1),n.changes.push(new fr(r)),!0}var t=ut.macroModeState,n=t.lastInsertModeChanges,r=m.keyName(e);if(!r)return;(r.indexOf("Delete")!=-1||r.indexOf("Backspace")!=-1)&&m.lookupKey(r,"vim-insert",i)}function cr(e,t,n,r){function u(){s?gt.processAction(e,t,t.lastEditActionCommand):gt.evalInput(e,t)}function a(n){if(i.lastInsertModeChanges.changes.length>0){n=t.lastEditActionCommand?n:1;var r=i.lastInsertModeChanges;hr(e,r.changes,n)}}var i=ut.macroModeState;i.isPlaying=!0;var s=!!t.lastEditActionCommand,o=t.inputState;t.inputState=t.lastEditInputState;if(s&&t.lastEditActionCommand.interlaceInsertRepeat)for(var f=0;f<n;f++)u(),a(1);else r||u(),a(n);t.inputState=o,t.insertMode&&!r&&Gn(e),i.isPlaying=!1}function hr(e,t,n){function r(t){return typeof t=="string"?m.commands[t](e):t(e),!0}var i=e.getCursor("head"),s=ut.macroModeState.lastInsertModeChanges.visualBlock;s&&(zt(e,i,s+1),n=e.listSelections().length,e.setCursor(i));for(var o=0;o<n;o++){s&&e.setCursor(kt(i,o,0));for(var u=0;u<t.length;u++){var a=t[u];if(a instanceof fr)m.lookupKey(a.keyName,"vim-insert",r);else if(typeof a=="string"){var f=e.getCursor();e.replaceRange(a,f,f)}else{var l=e.getCursor(),c=kt(l,0,a[0].length);e.replaceRange(a[0],l,c)}}}s&&e.setCursor(kt(i,0,1))}function dr(e,t,n){t.length>1&&t[0]=="n"&&(t=t.replace("numpad","")),t=pr[t]||t;var r="";return n.ctrlKey&&(r+="C-"),n.altKey&&(r+="A-"),(r||t.length>1)&&n.shiftKey&&(r+="S-"),r+=t,r.length>1&&(r="<"+r+">"),r}function mr(e){var t=new e.constructor;return Object.keys(e).forEach(function(n){var r=e[n];Array.isArray(r)?r=r.slice():r&&typeof r=="object"&&r.constructor!=Object&&(r=mr(r)),t[n]=r}),e.sel&&(t.sel={head:e.sel.head&&_t(e.sel.head),anchor:e.sel.anchor&&_t(e.sel.anchor)}),t}function gr(e,t,n){var r=!1,i=T.maybeInitVimState_(e),s=i.visualBlock||i.wasInVisualBlock,o=e.ace.inMultiSelectMode;i.wasInVisualBlock&&!o?i.wasInVisualBlock=!1:o&&i.visualBlock&&(i.wasInVisualBlock=!0);if(t=="<Esc>"&&!i.insertMode&&!i.visualMode&&o)e.ace.exitMultiSelectMode();else if(s||!o||e.ace.inVirtualSelectionMode)r=T.handleKey(e,t,n);else{var u=mr(i);e.operation(function(){e.ace.forEachSelection(function(){var i=e.ace.selection;e.state.vim.lastHPos=i.$desiredColumn==null?i.lead.column:i.$desiredColumn;var s=e.getCursor("head"),o=e.getCursor("anchor"),a=Pt(s,o)?0:-1,f=Pt(s,o)?-1:0;s=kt(s,0,a),o=kt(o,0,f),e.state.vim.sel.head=s,e.state.vim.sel.anchor=o,r=vr(e,t,n),i.$desiredColumn=e.state.vim.lastHPos==-1?null:e.state.vim.lastHPos,e.virtualSelectionMode()&&(e.state.vim=mr(u))}),e.curOp.cursorActivity&&!r&&(e.curOp.cursorActivity=!1)},!0)}return r&&!i.visualMode&&!i.insert&&i.visualMode!=e.somethingSelected()&&ar(e,i,!0),r}function br(e,t){t.off("beforeEndOperation",br);var n=t.state.cm.vimCmd;n&&t.execCommand(n.exec?n:n.name,n.args),t.curOp=t.prevOp}var i=e("../range").Range,s=e("../lib/event_emitter").EventEmitter,o=e("../lib/dom"),u=e("../lib/oop"),a=e("../lib/keys"),f=e("../lib/event"),l=e("../search").Search,c=e("../lib/useragent"),h=e("../search_highlight").SearchHighlight,p=e("../commands/multi_select_commands"),d=e("../mode/text").Mode.prototype.tokenRe,v=e("../ext/hardwrap").hardWrap;e("../multi_select");var m=function(e){this.ace=e,this.state={},this.marks={},this.$uid=0,this.onChange=this.onChange.bind(this),this.onSelectionChange=this.onSelectionChange.bind(this),this.onBeforeEndOperation=this.onBeforeEndOperation.bind(this),this.ace.on("change",this.onChange),this.ace.on("changeSelection",this.onSelectionChange),this.ace.on("beforeEndOperation",this.onBeforeEndOperation)};m.Pos=function(e,t){if(!(this instanceof x))return new x(e,t);this.line=e,this.ch=t},m.defineOption=function(e,t,n){},m.commands={redo:function(e){e.ace.redo()},undo:function(e){e.ace.undo()},newlineAndIndent:function(e){e.ace.insert("\n")}},m.keyMap={},m.addClass=m.rmClass=function(){},m.e_stop=m.e_preventDefault=f.stopEvent,m.keyName=function(e){var t=a[e.keyCode]||e.key||"";return t.length==1&&(t=t.toUpperCase()),t=f.getModifierString(e).replace(/(^|-)\w/g,function(e){return e.toUpperCase()})+t,t},m.keyMap["default"]=function(e){return function(t){var n=t.ace.commands.commandKeyBinding[e.toLowerCase()];return n&&t.ace.execCommand(n)!==!1}},m.lookupKey=function wr(e,t,n){t||(t="default"),typeof t=="string"&&(t=m.keyMap[t]);var r=typeof t=="function"?t(e):t[e];if(r===!1)return"nothing";if(r==="...")return"multi";if(r!=null&&n(r))return"handled";if(t.fallthrough){if(!Array.isArray(t.fallthrough))return wr(e,t.fallthrough,n);for(var i=0;i<t.fallthrough.length;i++){var s=wr(e,t.fallthrough[i],n);if(s)return s}}},m.findMatchingTag=function(e,t){},m.signal=function(e,t,n){return e._signal(t,n)},m.on=f.addListener,m.off=f.removeListener,m.isWordChar=function(e){return e<""?/^\w$/.test(e):(d.lastIndex=0,d.test(e))},function(){u.implement(m.prototype,s),this.destroy=function(){this.ace.off("change",this.onChange),this.ace.off("changeSelection",this.onSelectionChange),this.ace.off("beforeEndOperation",this.onBeforeEndOperation),this.removeOverlay()},this.virtualSelectionMode=function(){return this.ace.inVirtualSelectionMode&&this.ace.selection.index},this.onChange=function(e){var t={text:e.action[0]=="i"?e.lines:[]},n=this.curOp=this.curOp||{};n.changeHandlers||(n.changeHandlers=this._eventRegistry.change&&this._eventRegistry.change.slice()),n.lastChange?n.lastChange.next=n.lastChange=t:n.lastChange=n.change=t,this.$updateMarkers(e)},this.onSelectionChange=function(){var e=this.curOp=this.curOp||{};e.cursorActivityHandlers||(e.cursorActivityHandlers=this._eventRegistry.cursorActivity&&this._eventRegistry.cursorActivity.slice()),this.curOp.cursorActivity=!0,this.ace.inMultiSelectMode&&this.ace.keyBinding.removeKeyboardHandler(p.keyboardHandler)},this.operation=function(e,t){if(!t&&this.curOp||t&&this.curOp&&this.curOp.force)return e();(t||!this.ace.curOp)&&this.curOp&&this.onBeforeEndOperation();if(!this.ace.curOp){var n=this.ace.prevOp;this.ace.startOperation({command:{name:"vim",scrollIntoView:"cursor"}})}var r=this.curOp=this.curOp||{};this.curOp.force=t;var i=e();return this.ace.curOp&&this.ace.curOp.command.name=="vim"&&(this.state.dialog&&(this.ace.curOp.command.scrollIntoView=!1),this.ace.endOperation(),!r.cursorActivity&&!r.lastChange&&n&&(this.ace.prevOp=n)),(t||!this.ace.curOp)&&this.curOp&&this.onBeforeEndOperation(),i},this.onBeforeEndOperation=function(){var e=this.curOp;e&&(e.change&&this.signal("change",e.change,e),e&&e.cursorActivity&&this.signal("cursorActivity",null,e),this.curOp=null)},this.signal=function(e,t,n){var r=n?n[e+"Handlers"]:(this._eventRegistry||{})[e];if(!r)return;r=r.slice();for(var i=0;i<r.length;i++)r[i](this,t)},this.firstLine=function(){return 0},this.lastLine=function(){return this.ace.session.getLength()-1},this.lineCount=function(){return this.ace.session.getLength()},this.setCursor=function(e,t){typeof e=="object"&&(t=e.ch,e=e.line);var n=!this.curOp&&!this.ace.inVirtualSelectionMode;this.ace.inVirtualSelectionMode||this.ace.exitMultiSelectMode(),this.ace.session.unfold({row:e,column:t}),this.ace.selection.moveTo(e,t),n&&(this.ace.renderer.scrollCursorIntoView(),this.ace.endOperation())},this.getCursor=function(e){var t=this.ace.selection,n=e=="anchor"?t.isEmpty()?t.lead:t.anchor:e=="head"||!e?t.lead:t.getRange()[e];return y(n)},this.listSelections=function(e){var t=this.ace.multiSelect.rangeList.ranges;return!t.length||this.ace.inVirtualSelectionMode?[{anchor:this.getCursor("anchor"),head:this.getCursor("head")}]:t.map(function(e){return{anchor:this.clipPos(y(e.cursor==e.end?e.start:e.end)),head:this.clipPos(y(e.cursor))}},this)},this.setSelections=function(e,t){var n=this.ace.multiSelect,r=e.map(function(e){var t=g(e.anchor),n=g(e.head),r=i.comparePoints(t,n)<0?new i.fromPoints(t,n):new i.fromPoints(n,t);return r.cursor=i.comparePoints(r.start,n)?r.end:r.start,r});if(this.ace.inVirtualSelectionMode){this.ace.selection.fromOrientedRange(r[0]);return}t?r[t]&&r.push(r.splice(t,1)[0]):r=r.reverse(),n.toSingleRange(r[0].clone());var s=this.ace.session;for(var o=0;o<r.length;o++){var u=s.$clipRangeToDocument(r[o]);n.addRange(u)}},this.setSelection=function(e,t,n){var r=this.ace.selection;r.moveTo(e.line,e.ch),r.selectTo(t.line,t.ch),n&&n.origin=="*mouse"&&this.onBeforeEndOperation()},this.somethingSelected=function(e){return!this.ace.selection.isEmpty()},this.clipPos=function(e){var t=this.ace.session.$clipPositionToDocument(e.line,e.ch);return y(t)},this.foldCode=function(e){this.ace.session.$toggleFoldWidget(e.line,{})},this.markText=function(e){return{clear:function(){},find:function(){}}},this.$updateMarkers=function(e){var t=e.action=="insert",n=e.start,r=e.end,s=(r.row-n.row)*(t?1:-1),o=(r.column-n.column)*(t?1:-1);t&&(r=n);for(var u in this.marks){var a=this.marks[u],f=i.comparePoints(a,n);if(f<0)continue;if(f===0&&t){if(a.bias!=1){a.bias=-1;continue}f=1}var l=t?f:i.comparePoints(a,r);if(l>0){a.row+=s,a.column+=a.row==r.row?o:0;continue}!t&&l<=0&&(a.row=n.row,a.column=n.column,l===0&&(a.bias=1))}};var e=function(e,t,n,r){this.cm=e,this.id=t,this.row=n,this.column=r,e.marks[this.id]=this};e.prototype.clear=function(){delete this.cm.marks[this.id]},e.prototype.find=function(){return y(this)},this.setBookmark=function(t,n){var r=new e(this,this.$uid++,t.line,t.ch);if(!n||!n.insertLeft)r.$insertRight=!0;return this.marks[r.id]=r,r},this.moveH=function(e,t){if(t=="char"){var n=this.ace.selection;n.clearSelection(),n.moveCursorBy(0,e)}},this.findPosV=function(e,t,n,r){if(n=="page"){var i=this.ace.renderer,s=i.layerConfig;t*=Math.floor(s.height/s.lineHeight),n="line"}if(n=="line"){var o=this.ace.session.documentToScreenPosition(e.line,e.ch);r!=null&&(o.column=r),o.row+=t,o.row=Math.min(Math.max(0,o.row),this.ace.session.getScreenLength()-1);var u=this.ace.session.screenToDocumentPosition(o.row,o.column);return y(u)}debugger},this.charCoords=function(e,t){if(t=="div"||!t){var n=this.ace.session.documentToScreenPosition(e.line,e.ch);return{left:n.column,top:n.row}}if(t=="local"){var r=this.ace.renderer,n=this.ace.session.documentToScreenPosition(e.line,e.ch),i=r.layerConfig.lineHeight,s=r.layerConfig.characterWidth,o=i*n.row;return{left:n.column*s,top:o,bottom:o+i}}},this.coordsChar=function(e,t){var n=this.ace.renderer;if(t=="local"){var r=Math.max(0,Math.floor(e.top/n.lineHeight)),i=Math.max(0,Math.floor(e.left/n.characterWidth)),s=n.session.screenToDocumentPosition(r,i);return y(s)}if(t=="div")throw"not implemented"},this.getSearchCursor=function(e,t,n){var r=!1,i=!1;e instanceof RegExp&&!e.global&&(r=!e.ignoreCase,e=e.source,i=!0),e=="\\n"&&(e="\n",i=!1);var s=new l;t.ch==undefined&&(t.ch=Number.MAX_VALUE);var o={row:t.line,column:t.ch},u=this,a=null;return{findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(t){s.setOptions({needle:e,caseSensitive:r,wrap:!1,backwards:t,regExp:i,start:a||o});var n=s.find(u.ace.session);return a=n,a&&[!a.isEmpty()]},from:function(){return a&&y(a.start)},to:function(){return a&&y(a.end)},replace:function(e){a&&(a.end=u.ace.session.doc.replace(a,e))}}},this.scrollTo=function(e,t){var n=this.ace.renderer,r=n.layerConfig,i=r.maxHeight;i-=(n.$size.scrollerHeight-n.lineHeight)*n.$scrollPastEnd,t!=null&&this.ace.session.setScrollTop(Math.max(0,Math.min(t,i))),e!=null&&this.ace.session.setScrollLeft(Math.max(0,Math.min(e,r.width)))},this.scrollInfo=function(){return 0},this.scrollIntoView=function(e,t){if(e){var n=this.ace.renderer,r={top:0,bottom:t};n.scrollCursorIntoView(g(e),n.lineHeight*2/n.$size.scrollerHeight,r)}},this.getLine=function(e){return this.ace.session.getLine(e)},this.getRange=function(e,t){return this.ace.session.getTextRange(new i(e.line,e.ch,t.line,t.ch))},this.replaceRange=function(e,t,n){n||(n=t);var r=new i(t.line,t.ch,n.line,n.ch);return this.ace.session.$clipRangeToDocument(r),this.ace.session.replace(r,e)},this.replaceSelection=this.replaceSelections=function(e){var t=this.ace.selection;if(this.ace.inVirtualSelectionMode){this.ace.session.replace(t.getRange(),e[0]||"");return}t.inVirtualSelectionMode=!0;var n=t.rangeList.ranges;n.length||(n=[this.ace.multiSelect.getRange()]);for(var r=n.length;r--;)this.ace.session.replace(n[r],e[r]||"");t.inVirtualSelectionMode=!1},this.getSelection=function(){return this.ace.getSelectedText()},this.getSelections=function(){return this.listSelections().map(function(e){return this.getRange(e.anchor,e.head)},this)},this.getInputField=function(){return this.ace.textInput.getElement()},this.getWrapperElement=function(){return this.ace.container};var t={indentWithTabs:"useSoftTabs",indentUnit:"tabSize",tabSize:"tabSize",firstLineNumber:"firstLineNumber",readOnly:"readOnly"};this.setOption=function(e,n){this.state[e]=n;switch(e){case"indentWithTabs":e=t[e],n=!n;break;case"keyMap":this.state.$keyMap=n;return;default:e=t[e]}e&&this.ace.setOption(e,n)},this.getOption=function(e,n){var r=t[e];r&&(n=this.ace.getOption(r));switch(e){case"indentWithTabs":return e=t[e],!n;case"keyMap":return this.state.$keyMap}return r?n:this.state[e]},this.toggleOverwrite=function(e){return this.state.overwrite=e,this.ace.setOverwrite(e)},this.addOverlay=function(e){if(!this.$searchHighlight||!this.$searchHighlight.session){var t=new h(null,"ace_highlight-marker","text"),n=this.ace.session.addDynamicMarker(t);t.id=n.id,t.session=this.ace.session,t.destroy=function(e){t.session.off("change",t.updateOnChange),t.session.off("changeEditor",t.destroy),t.session.removeMarker(t.id),t.session=null},t.updateOnChange=function(e){var n=e.start.row;n==e.end.row?t.cache[n]=undefined:t.cache.splice(n,t.cache.length)},t.session.on("changeEditor",t.destroy),t.session.on("change",t.updateOnChange)}var r=new RegExp(e.query.source,"gmi");this.$searchHighlight=e.highlight=t,this.$searchHighlight.setRegexp(r),this.ace.renderer.updateBackMarkers()},this.removeOverlay=function(e){this.$searchHighlight&&this.$searchHighlight.session&&this.$searchHighlight.destroy()},this.getScrollInfo=function(){var e=this.ace.renderer,t=e.layerConfig;return{left:e.scrollLeft,top:e.scrollTop,height:t.maxHeight,width:t.width,clientHeight:t.height,clientWidth:t.width}},this.getValue=function(){return this.ace.getValue()},this.setValue=function(e){return this.ace.setValue(e,-1)},this.getTokenTypeAt=function(e){var t=this.ace.session.getTokenAt(e.line,e.ch);return t&&/comment|string/.test(t.type)?"string":""},this.findMatchingBracket=function(e){var t=this.ace.session.findMatchingBracket(g(e));return{to:t&&y(t)}},this.indentLine=function(e,t){t===!0?this.ace.session.indentRows(e,e,"	"):t===!1&&this.ace.session.outdentRows(new i(e,0,e,0))},this.indexFromPos=function(e){return this.ace.session.doc.positionToIndex(g(e))},this.posFromIndex=function(e){return y(this.ace.session.doc.indexToPosition(e))},this.focus=function(e){return this.ace.textInput.focus()},this.blur=function(e){return this.ace.blur()},this.defaultTextHeight=function(e){return this.ace.renderer.layerConfig.lineHeight},this.scanForBracket=function(e,t,n,r){var i=r.bracketRegex.source,s=/paren|text|operator|tag/;if(t==1)var o=this.ace.session.$findClosingBracket(i.slice(1,2),g(e),s);else var o=this.ace.session.$findOpeningBracket(i.slice(-2,-1),{row:e.line,column:e.ch+1},s);return o&&{pos:y(o)}},this.refresh=function(){return this.ace.resize(!0)},this.getMode=function(){return{name:this.getOption("mode")}},this.execCommand=function(e){e=="indentAuto"?this.ace.execCommand("autoindent"):console.log(e+" is not implemented")},this.getLineNumber=function(e){return e.row},this.getLineHandle=function(e){return{text:this.ace.session.getLine(e),row:e}}}.call(m.prototype);var b=m.StringStream=function(e,t){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0};b.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return this.pos==this.lineStart},peek:function(){return this.string.charAt(this.pos)||undefined},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(e){var t=this.string.charAt(this.pos);if(typeof e=="string")var n=t==e;else var n=t&&(e.test?e.test(t):e(t));if(n)return++this.pos,t},eatWhile:function(e){var t=this.pos;while(this.eat(e));return this.pos>t},eatSpace:function(){var e=this.pos;while(/[\s\u00a0]/.test(this.string.charAt(this.pos)))++this.pos;return this.pos>e},skipToEnd:function(){this.pos=this.string.length},skipTo:function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},backUp:function(e){this.pos-=e},column:function(){throw"not implemented"},indentation:function(){throw"not implemented"},match:function(e,t,n){if(typeof e!="string"){var s=this.string.slice(this.pos).match(e);return s&&s.index>0?null:(s&&t!==!1&&(this.pos+=s[0].length),s)}var r=function(e){return n?e.toLowerCase():e},i=this.string.substr(this.pos,e.length);if(r(i)==r(e))return t!==!1&&(this.pos+=e.length),!0},current:function(){return this.string.slice(this.start,this.pos)},hideFirstChars:function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}}},m.defineExtension=function(e,t){m.prototype[e]=t},o.importCssString(".normal-mode .ace_cursor{    border: none;    background-color: rgba(255,0,0,0.5);}.normal-mode .ace_hidden-cursors .ace_cursor{  background-color: transparent;  border: 1px solid red;  opacity: 0.7}.ace_dialog {  position: absolute;  left: 0; right: 0;  background: inherit;  z-index: 15;  padding: .1em .8em;  overflow: hidden;  color: inherit;}.ace_dialog-top {  border-bottom: 1px solid #444;  top: 0;}.ace_dialog-bottom {  border-top: 1px solid #444;  bottom: 0;}.ace_dialog input {  border: none;  outline: none;  background: transparent;  width: 20em;  color: inherit;  font-family: monospace;}","vimMode",!1),function(){function e(e,t,n){var r=e.ace.container,i;return i=r.appendChild(document.createElement("div")),n?i.className="ace_dialog ace_dialog-bottom":i.className="ace_dialog ace_dialog-top",typeof t=="string"?i.innerHTML=t:i.appendChild(t),i}function t(e,t){e.state.currentNotificationClose&&e.state.currentNotificationClose(),e.state.currentNotificationClose=t}m.defineExtension("openDialog",function(n,r,i){function a(e){if(typeof e=="string")f.value=e;else{if(o)return;if(e&&e.type=="blur"&&document.activeElement===f)return;u.state.dialog=null,o=!0,s.parentNode.removeChild(s),u.focus(),i.onClose&&i.onClose(s);var t=u;t.state.vim&&(t.state.vim.status=null,t.ace._signal("changeStatus"),t.ace.renderer.$loop.schedule(t.ace.renderer.CHANGE_CURSOR))}}if(this.virtualSelectionMode())return;i||(i={}),t(this,null);var s=e(this,n,i.bottom),o=!1,u=this;this.state.dialog=s;var f=s.getElementsByTagName("input")[0],l;if(f)i.value&&(f.value=i.value,i.selectValueOnOpen!==!1&&f.select()),i.onInput&&m.on(f,"input",function(e){i.onInput(e,f.value,a)}),i.onKeyUp&&m.on(f,"keyup",function(e){i.onKeyUp(e,f.value,a)}),m.on(f,"keydown",function(e){if(i&&i.onKeyDown&&i.onKeyDown(e,f.value,a))return;e.keyCode==13&&r(f.value);if(e.keyCode==27||i.closeOnEnter!==!1&&e.keyCode==13)f.blur(),m.e_stop(e),a()}),i.closeOnBlur!==!1&&m.on(f,"blur",a),f.focus();else if(l=s.getElementsByTagName("button")[0])m.on(l,"click",function(){a(),u.focus()}),i.closeOnBlur!==!1&&m.on(l,"blur",a),l.focus();return a}),m.defineExtension("openNotification",function(n,r){function a(){if(s)return;s=!0,clearTimeout(o),i.parentNode.removeChild(i)}if(this.virtualSelectionMode())return;t(this,a);var i=e(this,n,r&&r.bottom),s=!1,o,u=r&&typeof r.duration!="undefined"?r.duration:5e3;return m.on(i,"click",function(e){m.e_preventDefault(e),a()}),u&&(o=setTimeout(a,u)),a})}();var w=[{keys:"<Left>",type:"keyToKey",toKeys:"h"},{keys:"<Right>",type:"keyToKey",toKeys:"l"},{keys:"<Up>",type:"keyToKey",toKeys:"k"},{keys:"<Down>",type:"keyToKey",toKeys:"j"},{keys:"<Space>",type:"keyToKey",toKeys:"l"},{keys:"<BS>",type:"keyToKey",toKeys:"h",context:"normal"},{keys:"<Del>",type:"keyToKey",toKeys:"x",context:"normal"},{keys:"<C-Space>",type:"keyToKey",toKeys:"W"},{keys:"<C-BS>",type:"keyToKey",toKeys:"B",context:"normal"},{keys:"<S-Space>",type:"keyToKey",toKeys:"w"},{keys:"<S-BS>",type:"keyToKey",toKeys:"b",context:"normal"},{keys:"<C-n>",type:"keyToKey",toKeys:"j"},{keys:"<C-p>",type:"keyToKey",toKeys:"k"},{keys:"<C-[>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-c>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-[>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"<C-c>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"<C-Esc>",type:"keyToKey",toKeys:"<Esc>"},{keys:"<C-Esc>",type:"keyToKey",toKeys:"<Esc>",context:"insert"},{keys:"s",type:"keyToKey",toKeys:"cl",context:"normal"},{keys:"s",type:"keyToKey",toKeys:"c",context:"visual"},{keys:"S",type:"keyToKey",toKeys:"cc",context:"normal"},{keys:"S",type:"keyToKey",toKeys:"VdO",context:"visual"},{keys:"<Home>",type:"keyToKey",toKeys:"0"},{keys:"<End>",type:"keyToKey",toKeys:"$"},{keys:"<PageUp>",type:"keyToKey",toKeys:"<C-b>"},{keys:"<PageDown>",type:"keyToKey",toKeys:"<C-f>"},{keys:"<CR>",type:"keyToKey",toKeys:"j^",context:"normal"},{keys:"<Ins>",type:"action",action:"toggleOverwrite",context:"insert"},{keys:"H",type:"motion",motion:"moveToTopLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"M",type:"motion",motion:"moveToMiddleLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"L",type:"motion",motion:"moveToBottomLine",motionArgs:{linewise:!0,toJumplist:!0}},{keys:"h",type:"motion",motion:"moveByCharacters",motionArgs:{forward:!1}},{keys:"l",type:"motion",motion:"moveByCharacters",motionArgs:{forward:!0}},{keys:"j",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,linewise:!0}},{keys:"k",type:"motion",motion:"moveByLines",motionArgs:{forward:!1,linewise:!0}},{keys:"gj",type:"motion",motion:"moveByDisplayLines",motionArgs:{forward:!0}},{keys:"gk",type:"motion",motion:"moveByDisplayLines",motionArgs:{forward:!1}},{keys:"w",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!1}},{keys:"W",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!1,bigWord:!0}},{keys:"e",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!0,inclusive:!0}},{keys:"E",type:"motion",motion:"moveByWords",motionArgs:{forward:!0,wordEnd:!0,bigWord:!0,inclusive:!0}},{keys:"b",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1}},{keys:"B",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1,bigWord:!0}},{keys:"ge",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!0,inclusive:!0}},{keys:"gE",type:"motion",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!0,bigWord:!0,inclusive:!0}},{keys:"{",type:"motion",motion:"moveByParagraph",motionArgs:{forward:!1,toJumplist:!0}},{keys:"}",type:"motion",motion:"moveByParagraph",motionArgs:{forward:!0,toJumplist:!0}},{keys:"(",type:"motion",motion:"moveBySentence",motionArgs:{forward:!1}},{keys:")",type:"motion",motion:"moveBySentence",motionArgs:{forward:!0}},{keys:"<C-f>",type:"motion",motion:"moveByPage",motionArgs:{forward:!0}},{keys:"<C-b>",type:"motion",motion:"moveByPage",motionArgs:{forward:!1}},{keys:"<C-d>",type:"motion",motion:"moveByScroll",motionArgs:{forward:!0,explicitRepeat:!0}},{keys:"<C-u>",type:"motion",motion:"moveByScroll",motionArgs:{forward:!1,explicitRepeat:!0}},{keys:"gg",type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!1,explicitRepeat:!0,linewise:!0,toJumplist:!0}},{keys:"G",type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!0,explicitRepeat:!0,linewise:!0,toJumplist:!0}},{keys:"0",type:"motion",motion:"moveToStartOfLine"},{keys:"^",type:"motion",motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"+",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,toFirstChar:!0}},{keys:"-",type:"motion",motion:"moveByLines",motionArgs:{forward:!1,toFirstChar:!0}},{keys:"_",type:"motion",motion:"moveByLines",motionArgs:{forward:!0,toFirstChar:!0,repeatOffset:-1}},{keys:"$",type:"motion",motion:"moveToEol",motionArgs:{inclusive:!0}},{keys:"%",type:"motion",motion:"moveToMatchedSymbol",motionArgs:{inclusive:!0,toJumplist:!0}},{keys:"f<character>",type:"motion",motion:"moveToCharacter",motionArgs:{forward:!0,inclusive:!0}},{keys:"F<character>",type:"motion",motion:"moveToCharacter",motionArgs:{forward:!1}},{keys:"t<character>",type:"motion",motion:"moveTillCharacter",motionArgs:{forward:!0,inclusive:!0}},{keys:"T<character>",type:"motion",motion:"moveTillCharacter",motionArgs:{forward:!1}},{keys:";",type:"motion",motion:"repeatLastCharacterSearch",motionArgs:{forward:!0}},{keys:",",type:"motion",motion:"repeatLastCharacterSearch",motionArgs:{forward:!1}},{keys:"'<character>",type:"motion",motion:"goToMark",motionArgs:{toJumplist:!0,linewise:!0}},{keys:"`<character>",type:"motion",motion:"goToMark",motionArgs:{toJumplist:!0}},{keys:"]`",type:"motion",motion:"jumpToMark",motionArgs:{forward:!0}},{keys:"[`",type:"motion",motion:"jumpToMark",motionArgs:{forward:!1}},{keys:"]'",type:"motion",motion:"jumpToMark",motionArgs:{forward:!0,linewise:!0}},{keys:"['",type:"motion",motion:"jumpToMark",motionArgs:{forward:!1,linewise:!0}},{keys:"]p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!0,isEdit:!0,matchIndent:!0}},{keys:"[p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!1,isEdit:!0,matchIndent:!0}},{keys:"]<character>",type:"motion",motion:"moveToSymbol",motionArgs:{forward:!0,toJumplist:!0}},{keys:"[<character>",type:"motion",motion:"moveToSymbol",motionArgs:{forward:!1,toJumplist:!0}},{keys:"|",type:"motion",motion:"moveToColumn"},{keys:"o",type:"motion",motion:"moveToOtherHighlightedEnd",context:"visual"},{keys:"O",type:"motion",motion:"moveToOtherHighlightedEnd",motionArgs:{sameLine:!0},context:"visual"},{keys:"d",type:"operator",operator:"delete"},{keys:"y",type:"operator",operator:"yank"},{keys:"c",type:"operator",operator:"change"},{keys:"=",type:"operator",operator:"indentAuto"},{keys:">",type:"operator",operator:"indent",operatorArgs:{indentRight:!0}},{keys:"<",type:"operator",operator:"indent",operatorArgs:{indentRight:!1}},{keys:"g~",type:"operator",operator:"changeCase"},{keys:"gu",type:"operator",operator:"changeCase",operatorArgs:{toLower:!0},isEdit:!0},{keys:"gU",type:"operator",operator:"changeCase",operatorArgs:{toLower:!1},isEdit:!0},{keys:"n",type:"motion",motion:"findNext",motionArgs:{forward:!0,toJumplist:!0}},{keys:"N",type:"motion",motion:"findNext",motionArgs:{forward:!1,toJumplist:!0}},{keys:"gn",type:"motion",motion:"findAndSelectNextInclusive",motionArgs:{forward:!0}},{keys:"gN",type:"motion",motion:"findAndSelectNextInclusive",motionArgs:{forward:!1}},{keys:"x",type:"operatorMotion",operator:"delete",motion:"moveByCharacters",motionArgs:{forward:!0},operatorMotionArgs:{visualLine:!1}},{keys:"X",type:"operatorMotion",operator:"delete",motion:"moveByCharacters",motionArgs:{forward:!1},operatorMotionArgs:{visualLine:!0}},{keys:"D",type:"operatorMotion",operator:"delete",motion:"moveToEol",motionArgs:{inclusive:!0},context:"normal"},{keys:"D",type:"operator",operator:"delete",operatorArgs:{linewise:!0},context:"visual"},{keys:"Y",type:"operatorMotion",operator:"yank",motion:"expandToLine",motionArgs:{linewise:!0},context:"normal"},{keys:"Y",type:"operator",operator:"yank",operatorArgs:{linewise:!0},context:"visual"},{keys:"C",type:"operatorMotion",operator:"change",motion:"moveToEol",motionArgs:{inclusive:!0},context:"normal"},{keys:"C",type:"operator",operator:"change",operatorArgs:{linewise:!0},context:"visual"},{keys:"~",type:"operatorMotion",operator:"changeCase",motion:"moveByCharacters",motionArgs:{forward:!0},operatorArgs:{shouldMoveCursor:!0},context:"normal"},{keys:"~",type:"operator",operator:"changeCase",context:"visual"},{keys:"<C-w>",type:"operatorMotion",operator:"delete",motion:"moveByWords",motionArgs:{forward:!1,wordEnd:!1},context:"insert"},{keys:"<C-w>",type:"idle",context:"normal"},{keys:"<C-i>",type:"action",action:"jumpListWalk",actionArgs:{forward:!0}},{keys:"<C-o>",type:"action",action:"jumpListWalk",actionArgs:{forward:!1}},{keys:"<C-e>",type:"action",action:"scroll",actionArgs:{forward:!0,linewise:!0}},{keys:"<C-y>",type:"action",action:"scroll",actionArgs:{forward:!1,linewise:!0}},{keys:"a",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"charAfter"},context:"normal"},{keys:"A",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"eol"},context:"normal"},{keys:"A",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"endOfSelectedArea"},context:"visual"},{keys:"i",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"inplace"},context:"normal"},{keys:"gi",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"lastEdit"},context:"normal"},{keys:"I",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"firstNonBlank"},context:"normal"},{keys:"gI",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"bol"},context:"normal"},{keys:"I",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{insertAt:"startOfSelectedArea"},context:"visual"},{keys:"o",type:"action",action:"newLineAndEnterInsertMode",isEdit:!0,interlaceInsertRepeat:!0,actionArgs:{after:!0},context:"normal"},{keys:"O",type:"action",action:"newLineAndEnterInsertMode",isEdit:!0,interlaceInsertRepeat:!0,actionArgs:{after:!1},context:"normal"},{keys:"v",type:"action",action:"toggleVisualMode"},{keys:"V",type:"action",action:"toggleVisualMode",actionArgs:{linewise:!0}},{keys:"<C-v>",type:"action",action:"toggleVisualMode",actionArgs:{blockwise:!0}},{keys:"<C-q>",type:"action",action:"toggleVisualMode",actionArgs:{blockwise:!0}},{keys:"gv",type:"action",action:"reselectLastSelection"},{keys:"J",type:"action",action:"joinLines",isEdit:!0},{keys:"gJ",type:"action",action:"joinLines",actionArgs:{keepSpaces:!0},isEdit:!0},{keys:"p",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!0,isEdit:!0}},{keys:"P",type:"action",action:"paste",isEdit:!0,actionArgs:{after:!1,isEdit:!0}},{keys:"r<character>",type:"action",action:"replace",isEdit:!0},{keys:"@<character>",type:"action",action:"replayMacro"},{keys:"q<character>",type:"action",action:"enterMacroRecordMode"},{keys:"R",type:"action",action:"enterInsertMode",isEdit:!0,actionArgs:{replace:!0},context:"normal"},{keys:"R",type:"operator",operator:"change",operatorArgs:{linewise:!0,fullLine:!0},context:"visual",exitVisualBlock:!0},{keys:"u",type:"action",action:"undo",context:"normal"},{keys:"u",type:"operator",operator:"changeCase",operatorArgs:{toLower:!0},context:"visual",isEdit:!0},{keys:"U",type:"operator",operator:"changeCase",operatorArgs:{toLower:!1},context:"visual",isEdit:!0},{keys:"<C-r>",type:"action",action:"redo"},{keys:"m<character>",type:"action",action:"setMark"},{keys:'"<character>',type:"action",action:"setRegister"},{keys:"zz",type:"action",action:"scrollToCursor",actionArgs:{position:"center"}},{keys:"z.",type:"action",action:"scrollToCursor",actionArgs:{position:"center"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"zt",type:"action",action:"scrollToCursor",actionArgs:{position:"top"}},{keys:"z<CR>",type:"action",action:"scrollToCursor",actionArgs:{position:"top"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:"z-",type:"action",action:"scrollToCursor",actionArgs:{position:"bottom"}},{keys:"zb",type:"action",action:"scrollToCursor",actionArgs:{position:"bottom"},motion:"moveToFirstNonWhiteSpaceCharacter"},{keys:".",type:"action",action:"repeatLastEdit"},{keys:"<C-a>",type:"action",action:"incrementNumberToken",isEdit:!0,actionArgs:{increase:!0,backtrack:!1}},{keys:"<C-x>",type:"action",action:"incrementNumberToken",isEdit:!0,actionArgs:{increase:!1,backtrack:!1}},{keys:"<C-t>",type:"action",action:"indent",actionArgs:{indentRight:!0},context:"insert"},{keys:"<C-d>",type:"action",action:"indent",actionArgs:{indentRight:!1},context:"insert"},{keys:"a<character>",type:"motion",motion:"textObjectManipulation"},{keys:"i<character>",type:"motion",motion:"textObjectManipulation",motionArgs:{textObjectInner:!0}},{keys:"/",type:"search",searchArgs:{forward:!0,querySrc:"prompt",toJumplist:!0}},{keys:"?",type:"search",searchArgs:{forward:!1,querySrc:"prompt",toJumplist:!0}},{keys:"*",type:"search",searchArgs:{forward:!0,querySrc:"wordUnderCursor",wholeWordOnly:!0,toJumplist:!0}},{keys:"#",type:"search",searchArgs:{forward:!1,querySrc:"wordUnderCursor",wholeWordOnly:!0,toJumplist:!0}},{keys:"g*",type:"search",searchArgs:{forward:!0,querySrc:"wordUnderCursor",toJumplist:!0}},{keys:"g#",type:"search",searchArgs:{forward:!1,querySrc:"wordUnderCursor",toJumplist:!0}},{keys:":",type:"ex"}],E=w.length,S=[{name:"colorscheme",shortName:"colo"},{name:"map"},{name:"imap",shortName:"im"},{name:"nmap",shortName:"nm"},{name:"vmap",shortName:"vm"},{name:"unmap"},{name:"write",shortName:"w"},{name:"undo",shortName:"u"},{name:"redo",shortName:"red"},{name:"set",shortName:"se"},{name:"setlocal",shortName:"setl"},{name:"setglobal",shortName:"setg"},{name:"sort",shortName:"sor"},{name:"substitute",shortName:"s",possiblyAsync:!0},{name:"nohlsearch",shortName:"noh"},{name:"yank",shortName:"y"},{name:"delmarks",shortName:"delm"},{name:"registers",shortName:"reg",excludeFromCommandHistory:!0},{name:"vglobal",shortName:"v"},{name:"global",shortName:"g"}],x=m.Pos,T=function(){return lt};m.defineOption("vimMode",!1,function(e,t,n){t&&e.getOption("keyMap")!="vim"?e.setOption("keyMap","vim"):!t&&n!=m.Init&&/^vim/.test(e.getOption("keyMap"))&&e.setOption("keyMap","default")});var O={Shift:"S",Ctrl:"C",Alt:"A",Cmd:"D",Mod:"A",CapsLock:""},M={Enter:"CR",Backspace:"BS",Delete:"Del",Insert:"Ins"},P=/[\d]/,H=[m.isWordChar,function(e){return e&&!m.isWordChar(e)&&!/\s/.test(e)}],B=[function(e){return/\S/.test(e)}],F=j(65,26),I=j(97,26),q=j(48,10),R=[].concat(F,I,q,["<",">"]),U=[].concat(F,I,q,["-",'"',".",":","_","/"]),z;try{z=new RegExp("^[\\p{Lu}]$","u")}catch(W){z=/^[A-Z]$/}var Z={};et("filetype",undefined,"string",["ft"],function(e,t){if(t===undefined)return;if(e===undefined){var n=t.getOption("mode");return n=="null"?"":n}var n=e==""?"null":e;t.setOption("mode",n)});var rt=function(){function s(s,o,u){function l(n){var r=++t%e,o=i[r];o&&o.clear(),i[r]=s.setBookmark(n)}var a=t%e,f=i[a];if(f){var c=f.find();c&&!Dt(c,o)&&l(o)}else l(o);l(u),n=t,r=t-e+1,r<0&&(r=0)}function o(s,o){t+=o,t>n?t=n:t<r&&(t=r);var u=i[(e+t)%e];if(u&&!u.find()){var a=o>0?1:-1,f,l=s.getCursor();do{t+=a,u=i[(e+t)%e];if(u&&(f=u.find())&&!Dt(l,f))break}while(t<n&&t>r)}return u}function u(e,n){var r=t,i=o(e,n);return t=r,i&&i.find()}var e=100,t=-1,n=0,r=0,i=new Array(e);return{cachedCursor:undefined,add:s,find:u,move:o}},it=function(e){return e?{changes:e.changes,expectCursorActivityForChange:e.expectCursorActivityForChange}:{changes:[],expectCursorActivityForChange:!1}};st.prototype={exitMacroRecordMode:function(){var e=ut.macroModeState;e.onRecordingDone&&e.onRecordingDone(),e.onRecordingDone=undefined,e.isRecording=!1},enterMacroRecordMode:function(e,t){var n=ut.registerController.getRegister(t);n&&(n.clear(),this.latestRegister=t,e.openDialog&&(this.onRecordingDone=e.openDialog(document.createTextNode("(recording)["+t+"]"),null,{bottom:!0})),this.isRecording=!0)}};var ut,ft,lt={buildKeyMap:function(){},getRegisterController:function(){return ut.registerController},resetVimGlobalState_:at,getVimGlobalState_:function(){return ut},maybeInitVimState_:ot,suppressErrorLogging:!1,InsertModeKey:fr,map:function(e,t,n){Kn.map(e,t,n)},unmap:function(e,t){Kn.unmap(e,t)},noremap:function(e,t,n){function r(e){return e?[e]:["normal","insert","visual"]}var i=r(n),s=w.length,o=E;for(var u=s-o;u<s&&i.length;u++){var a=w[u];if(a.keys==t&&(!n||!a.context||a.context===n)&&a.type.substr(0,2)!=="ex"&&a.type.substr(0,3)!=="key"){var f={};for(var l in a)f[l]=a[l];f.keys=e,n&&!f.context&&(f.context=n),this._mapCommand(f);var c=r(a.context);i=i.filter(function(e){return c.indexOf(e)===-1})}}},mapclear:function(e){var t=w.length,n=E,r=w.slice(0,t-n);w=w.slice(t-n);if(e)for(var i=r.length-1;i>=0;i--){var s=r[i];if(e!==s.context)if(s.context)this._mapCommand(s);else{var o=["normal","insert","visual"];for(var u in o)if(o[u]!==e){var a={};for(var f in s)a[f]=s[f];a.context=o[u],this._mapCommand(a)}}}},setOption:tt,getOption:nt,defineOption:et,defineEx:function(e,t,n){if(!t)t=e;else if(e.indexOf(t)!==0)throw new Error('(Vim.defineEx) "'+t+'" is not a prefix of "'+e+'", command not registered');Jn[e]=n,Kn.commandMap_[t]={name:e,shortName:t,type:"api"}},handleKey:function(e,t,n){var r=this.findKey(e,t,n);if(typeof r=="function")return r()},findKey:function(e,t,n){function i(){var r=ut.macroModeState;if(r.isRecording){if(t=="q")return r.exitMacroRecordMode(),ht(e),!0;n!="mapping"&&tr(r,t)}}function s(){if(t=="<Esc>")return ht(e),r.visualMode?Gt(e):r.insertMode&&Gn(e),!0}function o(n){var r;while(n)r=/<\w+-.+?>|<\w+>|./.exec(n),t=r[0],n=n.substring(r.index+t.length),m.Vim.handleKey(e,t,"mapping")}function u(){if(s())return!0;var n=r.inputState.keyBuffer=r.inputState.keyBuffer+t,i=t.length==1,o=gt.matchCommand(n,w,r.inputState,"insert");while(n.length>1&&o.type!="full"){var n=r.inputState.keyBuffer=n.slice(1),u=gt.matchCommand(n,w,r.inputState,"insert");u.type!="none"&&(o=u)}if(o.type=="none")return ht(e),!1;if(o.type=="partial")return ft&&window.clearTimeout(ft),ft=window.setTimeout(function(){r.insertMode&&r.inputState.keyBuffer&&ht(e)},nt("insertModeEscKeysTimeout")),!i;ft&&window.clearTimeout(ft);if(i){var a=e.listSelections();for(var f=0;f<a.length;f++){var l=a[f].head;e.replaceRange("",kt(l,0,-(n.length-1)),l,"+input")}ut.macroModeState.lastInsertModeChanges.changes.pop()}return ht(e),o.command}function a(){if(i()||s())return!0;var n=r.inputState.keyBuffer=r.inputState.keyBuffer+t;if(/^[1-9]\d*$/.test(n))return!0;var o=/^(\d*)(.*)$/.exec(n);if(!o)return ht(e),!1;var u=r.visualMode?"visual":"normal",a=o[2]||o[1];r.inputState.operatorShortcut&&r.inputState.operatorShortcut.slice(-1)==a&&(a=r.inputState.operatorShortcut);var f=gt.matchCommand(a,w,r.inputState,u);if(f.type=="none")return ht(e),!1;if(f.type=="partial")return!0;r.inputState.keyBuffer="";var o=/^(\d*)(.*)$/.exec(n);return o[1]&&o[1]!="0"&&r.inputState.pushRepeatDigit(o[1]),f.command}var r=ot(e),f;return r.insertMode?f=u():f=a(),f===!1?undefined:f===!0?function(){return!0}:function(){if((f.operator||f.isEdit)&&e.getOption("readOnly"))return;return e.operation(function(){e.curOp.isVimOp=!0;try{f.type=="keyToKey"?o(f.toKeys):gt.processCommand(e,r,f)}catch(t){throw e.state.vim=undefined,ot(e),m.Vim.suppressErrorLogging||console.log(t),t}return!0})}},handleEx:function(e,t){Kn.processCommand(e,t)},defineMotion:bt,defineAction:Tt,defineOperator:St,mapCommand:Zn,_mapCommand:Yn,defineRegister:dt,exitVisualMode:Gt,exitInsertMode:Gn};ct.prototype.pushRepeatDigit=function(e){this.operator?this.motionRepeat=this.motionRepeat.concat(e):this.prefixRepeat=this.prefixRepeat.concat(e)},ct.prototype.getRepeat=function(){var e=0;if(this.prefixRepeat.length>0||this.motionRepeat.length>0)e=1,this.prefixRepeat.length>0&&(e*=parseInt(this.prefixRepeat.join(""),10)),this.motionRepeat.length>0&&(e*=parseInt(this.motionRepeat.join(""),10));return e},pt.prototype={setText:function(e,t,n){this.keyBuffer=[e||""],this.linewise=!!t,this.blockwise=!!n},pushText:function(e,t){t&&(this.linewise||this.keyBuffer.push("\n"),this.linewise=!0),this.keyBuffer.push(e)},pushInsertModeChanges:function(e){this.insertModeChanges.push(it(e))},pushSearchQuery:function(e){this.searchQueries.push(e)},clear:function(){this.keyBuffer=[],this.insertModeChanges=[],this.searchQueries=[],this.linewise=!1},toString:function(){return this.keyBuffer.join("")}},vt.prototype={pushText:function(e,t,n,r,i){if(e==="_")return;r&&n.charAt(n.length-1)!=="\n"&&(n+="\n");var s=this.isValidRegister(e)?this.getRegister(e):null;if(!s){switch(t){case"yank":this.registers[0]=new pt(n,r,i);break;case"delete":case"change":n.indexOf("\n")==-1?this.registers["-"]=new pt(n,r):(this.shiftNumericRegisters_(),this.registers[1]=new pt(n,r))}this.unnamedRegister.setText(n,r,i);return}var o=K(e);o?s.pushText(n,r):s.setText(n,r,i),this.unnamedRegister.setText(s.toString(),r)},getRegister:function(e){return this.isValidRegister(e)?(e=e.toLowerCase(),this.registers[e]||(this.registers[e]=new pt),this.registers[e]):this.unnamedRegister},isValidRegister:function(e){return e&&Y(e,U)},shiftNumericRegisters_:function(){for(var e=9;e>=2;e--)this.registers[e]=this.getRegister(""+(e-1))}},mt.prototype={nextMatch:function(e,t){var n=this.historyBuffer,r=t?-1:1;this.initialPrefix===null&&(this.initialPrefix=e);for(var i=this.iterator+r;t?i>=0:i<n.length;i+=r){var s=n[i];for(var o=0;o<=s.length;o++)if(this.initialPrefix==s.substring(0,o))return this.iterator=i,s}if(i>=n.length)return this.iterator=n.length,this.initialPrefix;if(i<0)return e},pushInput:function(e){var t=this.historyBuffer.indexOf(e);t>-1&&this.historyBuffer.splice(t,1),e.length&&this.historyBuffer.push(e)},reset:function(){this.initialPrefix=null,this.iterator=this.historyBuffer.length}};var gt={matchCommand:function(e,t,n,r){var i=Lt(e,t,r,n);if(!i.full&&!i.partial)return{type:"none"};if(!i.full&&i.partial)return{type:"partial"};var s;for(var o=0;o<i.full.length;o++){var u=i.full[o];s||(s=u)}if(s.keys.slice(-11)=="<character>"){var a=Ot(e);if(/<C-.>/.test(a)||!a)return{type:"none"};n.selectedCharacter=a}return{type:"full",command:s}},processCommand:function(e,t,n){t.inputState.repeatOverride=n.repeatOverride;switch(n.type){case"motion":this.processMotion(e,t,n);break;case"operator":this.processOperator(e,t,n);break;case"operatorMotion":this.processOperatorMotion(e,t,n);break;case"action":this.processAction(e,t,n);break;case"search":this.processSearch(e,t,n);break;case"ex":case"keyToEx":this.processEx(e,t,n);break;default:}},processMotion:function(e,t,n){t.inputState.motion=n.motion,t.inputState.motionArgs=Ct(n.motionArgs),this.evalInput(e,t)},processOperator:function(e,t,n){var r=t.inputState;if(r.operator){if(r.operator==n.operator){r.motion="expandToLine",r.motionArgs={linewise:!0},this.evalInput(e,t);return}ht(e)}r.operator=n.operator,r.operatorArgs=Ct(n.operatorArgs),n.keys.length>1&&(r.operatorShortcut=n.keys),n.exitVisualBlock&&(t.visualBlock=!1,Jt(e)),t.visualMode&&this.evalInput(e,t)},processOperatorMotion:function(e,t,n){var r=t.visualMode,i=Ct(n.operatorMotionArgs);i&&r&&i.visualLine&&(t.visualLine=!0),this.processOperator(e,t,n),r||this.processMotion(e,t,n)},processAction:function(e,t,n){var r=t.inputState,i=r.getRepeat(),s=!!i,o=Ct(n.actionArgs)||{};r.selectedCharacter&&(o.selectedCharacter=r.selectedCharacter),n.operator&&this.processOperator(e,t,n),n.motion&&this.processMotion(e,t,n),(n.motion||n.operator)&&this.evalInput(e,t),o.repeat=i||1,o.repeatIsExplicit=s,o.registerName=r.registerName,ht(e),t.lastMotion=null,n.isEdit&&this.recordLastEdit(t,r,n),xt[n.action](e,o,t)},processSearch:function(e,t,n){function a(r,i,s){ut.searchHistoryController.pushInput(r),ut.searchHistoryController.reset();try{jn(e,r,i,s)}catch(o){Dn(e,"Invalid regex: "+r),ht(e);return}gt.processMotion(e,t,{type:"motion",motion:"findNext",motionArgs:{forward:!0,toJumplist:n.searchArgs.toJumplist}})}function f(e){a(e,!0,!0);var t=ut.macroModeState;t.isRecording&&rr(t,e)}function l(t,n,i){var s=m.keyName(t),o,a;s=="Up"||s=="Down"?(o=s=="Up"?!0:!1,a=t.target?t.target.selectionEnd:0,n=ut.searchHistoryController.nextMatch(n,o)||"",i(n),a&&t.target&&(t.target.selectionEnd=t.target.selectionStart=Math.min(a,t.target.value.length))):s!="Left"&&s!="Right"&&s!="Ctrl"&&s!="Alt"&&s!="Shift"&&ut.searchHistoryController.reset();var f;try{f=jn(e,n,!0,!0)}catch(t){}f?e.scrollIntoView(qn(e,!r,f),30):(Un(e),e.scrollTo(u.left,u.top))}function c(t,n,r){var i=m.keyName(t);i=="Esc"||i=="Ctrl-C"||i=="Ctrl-["||i=="Backspace"&&n==""?(ut.searchHistoryController.pushInput(n),ut.searchHistoryController.reset(),jn(e,o),Un(e),e.scrollTo(u.left,u.top),m.e_stop(t),ht(e),r(),e.focus()):i=="Up"||i=="Down"?m.e_stop(t):i=="Ctrl-U"&&(m.e_stop(t),r(""))}if(!e.getSearchCursor)return;var r=n.searchArgs.forward,i=n.searchArgs.wholeWordOnly;En(e).setReversed(!r);var s=r?"/":"?",o=En(e).getQuery(),u=e.getScrollInfo();switch(n.searchArgs.querySrc){case"prompt":var h=ut.macroModeState;if(h.isPlaying){var p=h.replaySearchQueries.shift();a(p,!0,!1)}else Hn(e,{onClose:f,prefix:s,desc:"(JavaScript regexp)",onKeyUp:l,onKeyDown:c});break;case"wordUnderCursor":var d=tn(e,!1,!0,!1,!0),v=!0;d||(d=tn(e,!1,!0,!1,!1),v=!1);if(!d)return;var p=e.getLine(d.start.line).substring(d.start.ch,d.end.ch);v&&i?p="\\b"+p+"\\b":p=qt(p),ut.jumpList.cachedCursor=e.getCursor(),e.setCursor(d.start),a(p,!0,!1)}},processEx:function(e,t,n){function r(t){ut.exCommandHistoryController.pushInput(t),ut.exCommandHistoryController.reset(),Kn.processCommand(e,t)}function i(t,n,r){var i=m.keyName(t),s,o;if(i=="Esc"||i=="Ctrl-C"||i=="Ctrl-["||i=="Backspace"&&n=="")ut.exCommandHistoryController.pushInput(n),ut.exCommandHistoryController.reset(),m.e_stop(t),ht(e),r(),e.focus();i=="Up"||i=="Down"?(m.e_stop(t),s=i=="Up"?!0:!1,o=t.target?t.target.selectionEnd:0,n=ut.exCommandHistoryController.nextMatch(n,s)||"",r(n),o&&t.target&&(t.target.selectionEnd=t.target.selectionStart=Math.min(o,t.target.value.length))):i=="Ctrl-U"?(m.e_stop(t),r("")):i!="Left"&&i!="Right"&&i!="Ctrl"&&i!="Alt"&&i!="Shift"&&ut.exCommandHistoryController.reset()}n.type=="keyToEx"?Kn.processCommand(e,n.exArgs.input):t.visualMode?Hn(e,{onClose:r,prefix:":",value:"'<,'>",onKeyDown:i,selectValueOnOpen:!1}):Hn(e,{onClose:r,prefix:":",onKeyDown:i})},evalInput:function(e,t){var n=t.inputState,r=n.motion,i=n.motionArgs||{},s=n.operator,o=n.operatorArgs||{},u=n.registerName,a=t.sel,f=_t(t.visualMode?Nt(e,a.head):e.getCursor("head")),l=_t(t.visualMode?Nt(e,a.anchor):e.getCursor("anchor")),c=_t(f),h=_t(l),p,d,v;s&&this.recordLastEdit(t,n),n.repeatOverride!==undefined?v=n.repeatOverride:v=n.getRepeat();if(v>0&&i.explicitRepeat)i.repeatIsExplicit=!0;else if(i.noRepeat||!i.explicitRepeat&&v===0)v=1,i.repeatIsExplicit=!1;n.selectedCharacter&&(i.selectedCharacter=o.selectedCharacter=n.selectedCharacter),i.repeat=v,ht(e);if(r){var m=yt[r](e,f,i,t,n);t.lastMotion=yt[r];if(!m)return;if(i.toJumplist){!s&&e.ace.curOp!=null&&(e.ace.curOp.command.scrollIntoView="center-animate");var g=ut.jumpList,y=g.cachedCursor;y?(rn(e,y,m),delete g.cachedCursor):rn(e,f,m)}m instanceof Array?(d=m[0],p=m[1]):p=m,p||(p=_t(f));if(t.visualMode){if(!t.visualBlock||p.ch!==Infinity)p=Nt(e,p);d&&(d=Nt(e,d)),d=d||h,a.anchor=d,a.head=p,Jt(e),dn(e,t,"<",Pt(d,p)?d:p),dn(e,t,">",Pt(d,p)?p:d)}else s||(p=Nt(e,p),e.setCursor(p.line,p.ch))}if(s){if(o.lastSel){d=h;var b=o.lastSel,w=Math.abs(b.head.line-b.anchor.line),E=Math.abs(b.head.ch-b.anchor.ch);b.visualLine?p=x(h.line+w,h.ch):b.visualBlock?p=x(h.line+w,h.ch+E):b.head.line==b.anchor.line?p=x(h.line,h.ch+E):p=x(h.line+w,h.ch),t.visualMode=!0,t.visualLine=b.visualLine,t.visualBlock=b.visualBlock,a=t.sel={anchor:d,head:p},Jt(e)}else t.visualMode&&(o.lastSel={anchor:_t(a.anchor),head:_t(a.head),visualBlock:t.visualBlock,visualLine:t.visualLine});var S,T,N,C,k;if(t.visualMode){S=Ht(a.head,a.anchor),T=Bt(a.head,a.anchor),N=t.visualLine||o.linewise,C=t.visualBlock?"block":N?"line":"char",k=Kt(e,{anchor:S,head:T},C);if(N){var L=k.ranges;if(C=="block")for(var A=0;A<L.length;A++)L[A].head.ch=Ft(e,L[A].head.line);else C=="line"&&(L[0].head=x(L[0].head.line+1,0))}}else{S=_t(d||h),T=_t(p||c);if(Pt(T,S)){var O=S;S=T,T=O}N=i.linewise||o.linewise,N?Zt(e,S,T):i.forward&&Yt(e,S,T),C="char";var M=!i.inclusive||N;k=Kt(e,{anchor:S,head:T},C,M)}e.setSelections(k.ranges,k.primary),t.lastMotion=null,o.repeat=v,o.registerName=u,o.linewise=N;var _=Et[s](e,o,k.ranges,h,p);t.visualMode&&Gt(e,_!=null),_&&e.setCursor(_)}},recordLastEdit:function(e,t,n){var r=ut.macroModeState;if(r.isPlaying)return;e.lastEditInputState=t,e.lastEditActionCommand=n,r.lastInsertModeChanges.changes=[],r.lastInsertModeChanges.expectCursorActivityForChange=!1,r.lastInsertModeChanges.visualBlock=e.visualBlock?e.sel.head.line-e.sel.anchor.line:0}},yt={moveToTopLine:function(e,t,n){var r=Wn(e).top+n.repeat-1;return x(r,en(e.getLine(r)))},moveToMiddleLine:function(e){var t=Wn(e),n=Math.floor((t.top+t.bottom)*.5);return x(n,en(e.getLine(n)))},moveToBottomLine:function(e,t,n){var r=Wn(e).bottom-n.repeat+1;return x(r,en(e.getLine(r)))},expandToLine:function(e,t,n){var r=t;return x(r.line+n.repeat-1,Infinity)},findNext:function(e,t,n){var r=En(e),i=r.getQuery();if(!i)return;var s=!n.forward;return s=r.isReversed()?!s:s,In(e,i),qn(e,s,i,n.repeat)},findAndSelectNextInclusive:function(e,t,n,r,i){var s=En(e),o=s.getQuery();if(!o)return;var u=!n.forward;u=s.isReversed()?!u:u;var a=Rn(e,u,o,n.repeat,r);if(!a)return;if(i.operator)return a;var f=a[0],l=x(a[1].line,a[1].ch-1);if(r.visualMode){if(r.visualLine||r.visualBlock)r.visualLine=!1,r.visualBlock=!1,m.signal(e,"vim-mode-change",{mode:"visual",subMode:""});var c=r.sel.anchor;if(c)return s.isReversed()?n.forward?[c,f]:[c,l]:n.forward?[c,l]:[c,f]}else r.visualMode=!0,r.visualLine=!1,r.visualBlock=!1,m.signal(e,"vim-mode-change",{mode:"visual",subMode:""});return u?[l,f]:[f,l]},goToMark:function(e,t,n,r){var i=Xn(e,r,n.selectedCharacter);return i?n.linewise?{line:i.line,ch:en(e.getLine(i.line))}:i:null},moveToOtherHighlightedEnd:function(e,t,n,r){if(r.visualBlock&&n.sameLine){var i=r.sel;return[Nt(e,x(i.anchor.line,i.head.ch)),Nt(e,x(i.head.line,i.anchor.ch))]}return[r.sel.head,r.sel.anchor]},jumpToMark:function(e,t,n,r){var i=t;for(var s=0;s<n.repeat;s++){var o=i;for(var u in r.marks){if(!V(u))continue;var a=r.marks[u].find(),f=n.forward?Pt(a,o):Pt(o,a);if(f)continue;if(n.linewise&&a.line==o.line)continue;var l=Dt(o,i),c=n.forward?jt(o,a,i):jt(i,a,o);if(l||c)i=a}}return n.linewise&&(i=x(i.line,en(e.getLine(i.line)))),i},moveByCharacters:function(e,t,n){var r=t,i=n.repeat,s=n.forward?r.ch+i:r.ch-i;return x(r.line,s)},moveByLines:function(e,t,n,r){var i=t,s=i.ch;switch(r.lastMotion){case this.moveByLines:case this.moveByDisplayLines:case this.moveByScroll:case this.moveToColumn:case this.moveToEol:s=r.lastHPos;break;default:r.lastHPos=s}var o=n.repeat+(n.repeatOffset||0),u=n.forward?i.line+o:i.line-o,a=e.firstLine(),f=e.lastLine();if(u<a&&i.line==a)return this.moveToStartOfLine(e,t,n,r);if(u>f&&i.line==f)return cn(e,t,n,r,!0);var l=e.ace.session.getFoldLine(u);return l&&(n.forward?u>l.start.row&&(u=l.end.row+1):u=l.start.row),n.toFirstChar&&(s=en(e.getLine(u)),r.lastHPos=s),r.lastHSPos=e.charCoords(x(u,s),"div").left,x(u,s)},moveByDisplayLines:function(e,t,n,r){var i=t;switch(r.lastMotion){case this.moveByDisplayLines:case this.moveByScroll:case this.moveByLines:case this.moveToColumn:case this.moveToEol:break;default:r.lastHSPos=e.charCoords(i,"div").left}var s=n.repeat,o=e.findPosV(i,n.forward?s:-s,"line",r.lastHSPos);if(o.hitSide)if(n.forward)var u=e.charCoords(o,"div"),a={top:u.top+8,left:r.lastHSPos},o=e.coordsChar(a,"div");else{var f=e.charCoords(x(e.firstLine(),0),"div");f.left=r.lastHSPos,o=e.coordsChar(f,"div")}return r.lastHPos=o.ch,o},moveByPage:function(e,t,n){var r=t,i=n.repeat;return e.findPosV(r,n.forward?i:-i,"page")},moveByParagraph:function(e,t,n){var r=n.forward?1:-1;return mn(e,t,n.repeat,r)},moveBySentence:function(e,t,n){var r=n.forward?1:-1;return gn(e,t,n.repeat,r)},moveByScroll:function(e,t,n,r){var i=e.getScrollInfo(),s=null,o=n.repeat;o||(o=i.clientHeight/(2*e.defaultTextHeight()));var u=e.charCoords(t,"local");n.repeat=o;var s=yt.moveByDisplayLines(e,t,n,r);if(!s)return null;var a=e.charCoords(s,"local");return e.scrollTo(null,i.top+a.top-u.top),s},moveByWords:function(e,t,n){return ln(e,t,n.repeat,!!n.forward,!!n.wordEnd,!!n.bigWord)},moveTillCharacter:function(e,t,n){var r=n.repeat,i=hn(e,r,n.forward,n.selectedCharacter),s=n.forward?-1:1;return sn(s,n),i?(i.ch+=s,i):null},moveToCharacter:function(e,t,n){var r=n.repeat;return sn(0,n),hn(e,r,n.forward,n.selectedCharacter)||t},moveToSymbol:function(e,t,n){var r=n.repeat;return an(e,r,n.forward,n.selectedCharacter)||t},moveToColumn:function(e,t,n,r){var i=n.repeat;return r.lastHPos=i-1,r.lastHSPos=e.charCoords(t,"div").left,pn(e,i)},moveToEol:function(e,t,n,r){return cn(e,t,n,r,!1)},moveToFirstNonWhiteSpaceCharacter:function(e,t){var n=t;return x(n.line,en(e.getLine(n.line)))},moveToMatchedSymbol:function(e,t){var n=t,r=n.line,i=n.ch,s=e.getLine(r),o;for(;i<s.length;i++){o=s.charAt(i);if(o&&$(o)){var u=e.getTokenTypeAt(x(r,i+1));if(u!=="string"&&u!=="comment")break}}if(i<s.length){var a=/[<>]/.test(s[i])?/[(){}[\]<>]/:/[(){}[\]]/,f=e.findMatchingBracket(x(r,i+1),{bracketRegex:a});return f.to}return n},moveToStartOfLine:function(e,t){return x(t.line,0)},moveToLineOrEdgeOfDocument:function(e,t,n){var r=n.forward?e.lastLine():e.firstLine();return n.repeatIsExplicit&&(r=n.repeat-e.getOption("firstLineNumber")),x(r,en(e.getLine(r)))},textObjectManipulation:function(e,t,n,r){var i={"(":")",")":"(","{":"}","}":"{","[":"]","]":"[","<":">",">":"<"},s={"'":!0,'"':!0,"`":!0},o=n.selectedCharacter;o=="b"?o="(":o=="B"&&(o="{");var u=!n.textObjectInner,a;if(i[o])a=yn(e,t,o,u);else if(s[o])a=bn(e,t,o,u);else if(o==="W")a=tn(e,u,!0,!0);else if(o==="w")a=tn(e,u,!0,!1);else if(o==="p"){a=mn(e,t,n.repeat,0,u),n.linewise=!0;if(r.visualMode)r.visualLine||(r.visualLine=!0);else{var f=r.inputState.operatorArgs;f&&(f.linewise=!0),a.end.line--}}else{if(o!=="t")return null;a=nn(e,t,u)}return e.state.vim.visualMode?$t(e,a.start,a.end):[a.start,a.end]},repeatLastCharacterSearch:function(e,t,n){var r=ut.lastCharacterSearch,i=n.repeat,s=n.forward===r.forward,o=(r.increment?1:0)*(s?-1:1);e.moveH(-o,"char"),n.inclusive=s?!0:!1;var u=hn(e,i,s,r.selectedCharacter);return u?(u.ch+=o,u):(e.moveH(o,"char"),t)}},Et={change:function(e,t,n){var r,i,s=e.state.vim,o=n[0].anchor,u=n[0].head;if(!s.visualMode){i=e.getRange(o,u);var a=s.lastEditInputState||{};if(a.motion=="moveByWords"&&!Q(i)){var f=/\s+$/.exec(i);f&&a.motionArgs&&a.motionArgs.forward&&(u=kt(u,0,-f[0].length),i=i.slice(0,-f[0].length))}var l=new x(o.line-1,Number.MAX_VALUE),c=e.firstLine()==e.lastLine();u.line>e.lastLine()&&t.linewise&&!c?e.replaceRange("",l,u):e.replaceRange("",o,u),t.linewise&&(c||(e.setCursor(l),m.commands.newlineAndIndent(e)),o.ch=Number.MAX_VALUE),r=o}else if(t.fullLine)u.ch=Number.MAX_VALUE,u.line--,e.setSelection(o,u),i=e.getSelection(),e.replaceSelection(""),r=o;else{i=e.getSelection();var h=wt("",n.length);e.replaceSelections(h),r=Ht(n[0].head,n[0].anchor)}ut.registerController.pushText(t.registerName,"change",i,t.linewise,n.length>1),xt.enterInsertMode(e,{head:r},e.state.vim)},"delete":function(e,t,n){var r,i,s=e.state.vim;if(!s.visualBlock){var o=n[0].anchor,u=n[0].head;t.linewise&&u.line!=e.firstLine()&&o.line==e.lastLine()&&o.line==u.line-1&&(o.line==e.firstLine()?o.ch=0:o=x(o.line-1,Ft(e,o.line-1))),i=e.getRange(o,u),e.replaceRange("",o,u),r=o,t.linewise&&(r=yt.moveToFirstNonWhiteSpaceCharacter(e,o))}else{i=e.getSelection();var a=wt("",n.length);e.replaceSelections(a),r=n[0].anchor}return ut.registerController.pushText(t.registerName,"delete",i,t.linewise,s.visualBlock),Nt(e,r)},indent:function(e,t,n){var r=e.state.vim,i=n[0].anchor.line,s=r.visualBlock?n[n.length-1].anchor.line:n[0].head.line,o=r.visualMode?t.repeat:1;t.linewise&&s--;for(var u=i;u<=s;u++)for(var a=0;a<o;a++)e.indentLine(u,t.indentRight);return yt.moveToFirstNonWhiteSpaceCharacter(e,n[0].anchor)},indentAuto:function(e,t,n){return n.length>1&&e.setSelection(n[0].anchor,n[n.length-1].head),e.execCommand("indentAuto"),yt.moveToFirstNonWhiteSpaceCharacter(e,n[0].anchor)},changeCase:function(e,t,n,r,i){var s=e.getSelections(),o=[],u=t.toLower;for(var a=0;a<s.length;a++){var f=s[a],l="";if(u===!0)l=f.toLowerCase();else if(u===!1)l=f.toUpperCase();else for(var c=0;c<f.length;c++){var h=f.charAt(c);l+=K(h)?h.toLowerCase():h.toUpperCase()}o.push(l)}return e.replaceSelections(o),t.shouldMoveCursor?i:!e.state.vim.visualMode&&t.linewise&&n[0].anchor.line+1==n[0].head.line?yt.moveToFirstNonWhiteSpaceCharacter(e,r):t.linewise?r:Ht(n[0].anchor,n[0].head)},yank:function(e,t,n,r){var i=e.state.vim,s=e.getSelection(),o=i.visualMode?Ht(i.sel.anchor,i.sel.head,n[0].head,n[0].anchor):r;return ut.registerController.pushText(t.registerName,"yank",s,t.linewise,i.visualBlock),o}},xt={jumpListWalk:function(e,t,n){if(n.visualMode)return;var r=t.repeat,i=t.forward,s=ut.jumpList,o=s.move(e,i?r:-r),u=o?o.find():undefined;u=u?u:e.getCursor(),e.setCursor(u),e.ace.curOp.command.scrollIntoView="center-animate"},scroll:function(e,t,n){if(n.visualMode)return;var r=t.repeat||1,i=e.defaultTextHeight(),s=e.getScrollInfo().top,o=i*r,u=t.forward?s+o:s-o,a=_t(e.getCursor()),f=e.charCoords(a,"local");if(t.forward)u>f.top?(a.line+=(u-f.top)/i,a.line=Math.ceil(a.line),e.setCursor(a),f=e.charCoords(a,"local"),e.scrollTo(null,f.top)):e.scrollTo(null,u);else{var l=u+e.getScrollInfo().clientHeight;l<f.bottom?(a.line-=(f.bottom-l)/i,a.line=Math.floor(a.line),e.setCursor(a),f=e.charCoords(a,"local"),e.scrollTo(null,f.bottom-e.getScrollInfo().clientHeight)):e.scrollTo(null,u)}},scrollToCursor:function(e,t){var n=e.getCursor().line,r=e.charCoords(x(n,0),"local"),i=e.getScrollInfo().clientHeight,s=r.top,o=r.bottom-s;switch(t.position){case"center":s=s-i/2+o;break;case"bottom":s=s-i+o}e.scrollTo(null,s)},replayMacro:function(e,t,n){var r=t.selectedCharacter,i=t.repeat,s=ut.macroModeState;r=="@"?r=s.latestRegister:s.latestRegister=r;while(i--)er(e,n,s,r)},enterMacroRecordMode:function(e,t){var n=ut.macroModeState,r=t.selectedCharacter;ut.registerController.isValidRegister(r)&&n.enterMacroRecordMode(e,r)},toggleOverwrite:function(e){e.state.overwrite?(e.toggleOverwrite(!1),e.setOption("keyMap","vim-insert"),m.signal(e,"vim-mode-change",{mode:"insert"})):(e.toggleOverwrite(!0),e.setOption("keyMap","vim-replace"),m.signal(e,"vim-mode-change",{mode:"replace"}))},enterInsertMode:function(e,t,n){if(e.getOption("readOnly"))return;n.insertMode=!0,n.insertModeRepeat=t&&t.repeat||1;var r=t?t.insertAt:null,i=n.sel,s=t.head||e.getCursor("head"),o=e.listSelections().length;if(r=="eol")s=x(s.line,Ft(e,s.line));else if(r=="bol")s=x(s.line,0);else if(r=="charAfter")s=kt(s,0,1);else if(r=="firstNonBlank")s=yt.moveToFirstNonWhiteSpaceCharacter(e,s);else if(r=="startOfSelectedArea"){if(!n.visualMode)return;n.visualBlock?(s=x(Math.min(i.head.line,i.anchor.line),Math.min(i.head.ch,i.anchor.ch)),o=Math.abs(i.head.line-i.anchor.line)+1):i.head.line<i.anchor.line?s=i.head:s=x(i.anchor.line,0)}else if(r=="endOfSelectedArea"){if(!n.visualMode)return;n.visualBlock?(s=x(Math.min(i.head.line,i.anchor.line),Math.max(i.head.ch+1,i.anchor.ch)),o=Math.abs(i.head.line-i.anchor.line)+1):i.head.line>=i.anchor.line?s=kt(i.head,0,1):s=x(i.anchor.line,0)}else if(r=="inplace"){if(n.visualMode)return}else r=="lastEdit"&&(s=Vn(e)||s);e.setOption("disableInput",!1),t&&t.replace?(e.toggleOverwrite(!0),e.setOption("keyMap","vim-replace"),m.signal(e,"vim-mode-change",{mode:"replace"})):(e.toggleOverwrite(!1),e.setOption("keyMap","vim-insert"),m.signal(e,"vim-mode-change",{mode:"insert"})),ut.macroModeState.isPlaying||(e.on("change",ir),m.on(e.getInputField(),"keydown",lr)),n.visualMode&&Gt(e),zt(e,s,o)},toggleVisualMode:function(e,t,n){var r=t.repeat,i=e.getCursor(),s;n.visualMode?n.visualLine^t.linewise||n.visualBlock^t.blockwise?(n.visualLine=!!t.linewise,n.visualBlock=!!t.blockwise,m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""}),Jt(e)):Gt(e):(n.visualMode=!0,n.visualLine=!!t.linewise,n.visualBlock=!!t.blockwise,s=Nt(e,x(i.line,i.ch+r-1)),n.sel={anchor:i,head:s},m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""}),Jt(e),dn(e,n,"<",Ht(i,s)),dn(e,n,">",Bt(i,s)))},reselectLastSelection:function(e,t,n){var r=n.lastSelection;n.visualMode&&Vt(e,n);if(r){var i=r.anchorMark.find(),s=r.headMark.find();if(!i||!s)return;n.sel={anchor:i,head:s},n.visualMode=!0,n.visualLine=r.visualLine,n.visualBlock=r.visualBlock,Jt(e),dn(e,n,"<",Ht(i,s)),dn(e,n,">",Bt(i,s)),m.signal(e,"vim-mode-change",{mode:"visual",subMode:n.visualLine?"linewise":n.visualBlock?"blockwise":""})}},joinLines:function(e,t,n){var r,i;if(n.visualMode){r=e.getCursor("anchor"),i=e.getCursor("head");if(Pt(i,r)){var s=i;i=r,r=s}i.ch=Ft(e,i.line)-1}else{var o=Math.max(t.repeat,2);r=e.getCursor(),i=Nt(e,x(r.line+o-1,Infinity))}var u=0;for(var a=r.line;a<i.line;a++){u=Ft(e,r.line);var s=x(r.line+1,Ft(e,r.line+1)),f=e.getRange(r,s);f=t.keepSpaces?f.replace(/\n\r?/g,""):f.replace(/\n\s*/g," "),e.replaceRange(f,r,s)}var l=x(r.line,u);n.visualMode&&Gt(e,!1),e.setCursor(l)},newLineAndEnterInsertMode:function(e,t,n){n.insertMode=!0;var r=_t(e.getCursor());if(r.line===e.firstLine()&&!t.after)e.replaceRange("\n",x(e.firstLine(),0)),e.setCursor(e.firstLine(),0);else{r.line=t.after?r.line:r.line-1,r.ch=Ft(e,r.line),e.setCursor(r);var i=m.commands.newlineAndIndentContinueComment||m.commands.newlineAndIndent;i(e)}this.enterInsertMode(e,{repeat:t.repeat},n)},paste:function(e,t,n){var r=_t(e.getCursor()),i=ut.registerController.getRegister(t.registerName),s=i.toString();if(!s)return;if(t.matchIndent){var o=e.getOption("tabSize"),u=function(e){var t=e.split("	").length-1,n=e.split(" ").length-1;return t*o+n*1},a=e.getLine(e.getCursor().line),f=u(a.match(/^\s*/)[0]),l=s.replace(/\n$/,""),c=s!==l,h=u(s.match(/^\s*/)[0]),s=l.replace(/^\s*/gm,function(t){var n=f+(u(t)-h);if(n<0)return"";if(e.getOption("indentWithTabs")){var r=Math.floor(n/o);return Array(r+1).join("	")}return Array(n+1).join(" ")});s+=c?"\n":""}if(t.repeat>1)var s=Array(t.repeat+1).join(s);var p=i.linewise,d=i.blockwise;if(d){s=s.split("\n"),p&&s.pop();for(var v=0;v<s.length;v++)s[v]=s[v]==""?" ":s[v];r.ch+=t.after?1:0,r.ch=Math.min(Ft(e,r.line),r.ch)}else p?n.visualMode?s=n.visualLine?s.slice(0,-1):"\n"+s.slice(0,s.length-1)+"\n":t.after?(s="\n"+s.slice(0,s.length-1),r.ch=Ft(e,r.line)):r.ch=0:r.ch+=t.after?1:0;var m,g;if(n.visualMode){n.lastPastedText=s;var y,b=Xt(e,n),w=b[0],E=b[1],S=e.getSelection(),T=e.listSelections(),N=(new Array(T.length)).join("1").split("1");n.lastSelection&&(y=n.lastSelection.headMark.find()),ut.registerController.unnamedRegister.setText(S),d?(e.replaceSelections(N),E=x(w.line+s.length-1,w.ch),e.setCursor(w),Ut(e,E),e.replaceSelections(s),m=w):n.visualBlock?(e.replaceSelections(N),e.setCursor(w),e.replaceRange(s,w,w),m=w):(e.replaceRange(s,w,E),m=e.posFromIndex(e.indexFromPos(w)+s.length-1)),y&&(n.lastSelection.headMark=e.setBookmark(y)),p&&(m.ch=0)}else if(d){e.setCursor(r);for(var v=0;v<s.length;v++){var C=r.line+v;C>e.lastLine()&&e.replaceRange("\n",x(C,0));var k=Ft(e,C);k<r.ch&&Rt(e,C,r.ch)}e.setCursor(r),Ut(e,x(r.line+s.length-1,r.ch)),e.replaceSelections(s),m=r}else e.replaceRange(s,r),p&&t.after?m=x(r.line+1,en(e.getLine(r.line+1))):p&&!t.after?m=x(r.line,en(e.getLine(r.line))):!p&&t.after?(g=e.indexFromPos(r),m=e.posFromIndex(g+s.length-1)):(g=e.indexFromPos(r),m=e.posFromIndex(g+s.length));n.visualMode&&Gt(e,!1),e.setCursor(m)},undo:function(e,t){e.operation(function(){Mt(e,m.commands.undo,t.repeat)(),e.setCursor(e.getCursor("anchor"))})},redo:function(e,t){Mt(e,m.commands.redo,t.repeat)()},setRegister:function(e,t,n){n.inputState.registerName=t.selectedCharacter},setMark:function(e,t,n){var r=t.selectedCharacter;dn(e,n,r,e.getCursor())},replace:function(e,t,n){var r=t.selectedCharacter,i=e.getCursor(),s,o,u=e.listSelections();if(n.visualMode)i=e.getCursor("start"),o=e.getCursor("end");else{var a=e.getLine(i.line);s=i.ch+t.repeat,s>a.length&&(s=a.length),o=x(i.line,s)}if(r=="\n")n.visualMode||e.replaceRange("",i,o),(m.commands.newlineAndIndentContinueComment||m.commands.newlineAndIndent)(e);else{var f=e.getRange(i,o);f=f.replace(/[^\n]/g,r);if(n.visualBlock){var l=(new Array(e.getOption("tabSize")+1)).join(" ");f=e.getSelection(),f=f.replace(/\t/g,l).replace(/[^\n]/g,r).split("\n"),e.replaceSelections(f)}else e.replaceRange(f,i,o);n.visualMode?(i=Pt(u[0].anchor,u[0].head)?u[0].anchor:u[0].head,e.setCursor(i),Gt(e,!1)):e.setCursor(kt(o,0,-1))}},incrementNumberToken:function(e,t){var n=e.getCursor(),r=e.getLine(n.line),i=/(-?)(?:(0x)([\da-f]+)|(0b|0|)(\d+))/gi,s,o,u,a;while((s=i.exec(r))!==null){o=s.index,u=o+s[0].length;if(n.ch<u)break}if(!t.backtrack&&u<=n.ch)return;if(!s)return;var f=s[2]||s[4],l=s[3]||s[5],c=t.increase?1:-1,h={"0b":2,0:8,"":10,"0x":16}[f.toLowerCase()],p=parseInt(s[1]+l,h)+c*t.repeat;a=p.toString(h);var d=f?(new Array(l.length-a.length+1+s[1].length)).join("0"):"";a.charAt(0)==="-"?a="-"+f+d+a.substr(1):a=f+d+a;var v=x(n.line,o),m=x(n.line,u);e.replaceRange(a,v,m),e.setCursor(x(n.line,o+a.length-1))},repeatLastEdit:function(e,t,n){var r=n.lastEditInputState;if(!r)return;var i=t.repeat;i&&t.repeatIsExplicit?n.lastEditInputState.repeatOverride=i:i=n.lastEditInputState.repeatOverride||i,cr(e,n,i,!1)},indent:function(e,t){e.indentLine(e.getCursor().line,t.indentRight)},exitInsertMode:Gn},on={"(":"bracket",")":"bracket","{":"bracket","}":"bracket","[":"section","]":"section","*":"comment","/":"comment",m:"method",M:"method","#":"preprocess"},un={bracket:{isComplete:function(e){if(e.nextCh===e.symb){e.depth++;if(e.depth>=1)return!0}else e.nextCh===e.reverseSymb&&e.depth--;return!1}},section:{init:function(e){e.curMoveThrough=!0,e.symb=(e.forward?"]":"[")===e.symb?"{":"}"},isComplete:function(e){return e.index===0&&e.nextCh===e.symb}},comment:{isComplete:function(e){var t=e.lastCh==="*"&&e.nextCh==="/";return e.lastCh=e.nextCh,t}},method:{init:function(e){e.symb=e.symb==="m"?"{":"}",e.reverseSymb=e.symb==="{"?"}":"{"},isComplete:function(e){return e.nextCh===e.symb?!0:!1}},preprocess:{init:function(e){e.index=0},isComplete:function(e){if(e.nextCh==="#"){var t=e.lineText.match(/^#(\w+)/)[1];if(t==="endif"){if(e.forward&&e.depth===0)return!0;e.depth++}else if(t==="if"){if(!e.forward&&e.depth===0)return!0;e.depth--}if(t==="else"&&e.depth===0)return!0}return!1}}};et("pcre",!0,"boolean"),wn.prototype={getQuery:function(){return ut.query},setQuery:function(e){ut.query=e},getOverlay:function(){return this.searchOverlay},setOverlay:function(e){this.searchOverlay=e},isReversed:function(){return ut.isReversed},setReversed:function(e){ut.isReversed=e},getScrollbarAnnotate:function(){return this.annotate},setScrollbarAnnotate:function(e){this.annotate=e}};var kn={"\\n":"\n","\\r":"\r","\\t":"	"},An={"\\/":"/","\\\\":"\\","\\n":"\n","\\r":"\r","\\t":"	","\\&":"&"},$n=function(){this.buildCommandMap_()};$n.prototype={processCommand:function(e,t,n){var r=this;e.operation(function(){e.curOp.isVimOp=!0,r._processCommand(e,t,n)})},_processCommand:function(e,t,n){var r=e.state.vim,i=ut.registerController.getRegister(":"),s=i.toString();r.visualMode&&Gt(e);var o=new m.StringStream(t);i.setText(t);var u=n||{};u.input=t;try{this.parseInput_(e,o,u)}catch(a){throw Dn(e,a.toString()),a}var f,l;if(!u.commandName)u.line!==undefined&&(l="move");else{f=this.matchCommand_(u.commandName);if(f){l=f.name,f.excludeFromCommandHistory&&i.setText(s),this.parseCommandArgs_(o,u,f);if(f.type=="exToKey"){for(var c=0;c<f.toKeys.length;c++)m.Vim.handleKey(e,f.toKeys[c],"mapping");return}if(f.type=="exToEx"){this.processCommand(e,f.toInput);return}}}if(!l){Dn(e,'Not an editor command ":'+t+'"');return}try{Jn[l](e,u),(!f||!f.possiblyAsync)&&u.callback&&u.callback()}catch(a){throw Dn(e,a.toString()),a}},parseInput_:function(e,t,n){t.eatWhile(":"),t.eat("%")?(n.line=e.firstLine(),n.lineEnd=e.lastLine()):(n.line=this.parseLineSpec_(e,t),n.line!==undefined&&t.eat(",")&&(n.lineEnd=this.parseLineSpec_(e,t)));var r=t.match(/^(\w+|!!|@@|[!#&*<=>@~])/);return r?n.commandName=r[1]:n.commandName=t.match(/.*/)[0],n},parseLineSpec_:function(e,t){var n=t.match(/^(\d+)/);if(n)return parseInt(n[1],10)-1;switch(t.next()){case".":return this.parseLineSpecOffset_(t,e.getCursor().line);case"$":return this.parseLineSpecOffset_(t,e.lastLine());case"'":var r=t.next(),i=Xn(e,e.state.vim,r);if(!i)throw new Error("Mark not set");return this.parseLineSpecOffset_(t,i.line);case"-":case"+":return t.backUp(1),this.parseLineSpecOffset_(t,e.getCursor().line);default:return t.backUp(1),undefined}},parseLineSpecOffset_:function(e,t){var n=e.match(/^([+-])?(\d+)/);if(n){var r=parseInt(n[2],10);n[1]=="-"?t-=r:t+=r}return t},parseCommandArgs_:function(e,t,n){if(e.eol())return;t.argString=e.match(/.*/)[0];var r=n.argDelimiter||/\s+/,i=It(t.argString).split(r);i.length&&i[0]&&(t.args=i)},matchCommand_:function(e){for(var t=e.length;t>0;t--){var n=e.substring(0,t);if(this.commandMap_[n]){var r=this.commandMap_[n];if(r.name.indexOf(e)===0)return r}}return null},buildCommandMap_:function(){this.commandMap_={};for(var e=0;e<S.length;e++){var t=S[e],n=t.shortName||t.name;this.commandMap_[n]=t}},map:function(e,t,n){if(e!=":"&&e.charAt(0)==":"){if(n)throw Error("Mode not supported for ex mappings");var r=e.substring(1);t!=":"&&t.charAt(0)==":"?this.commandMap_[r]={name:r,type:"exToEx",toInput:t.substring(1),user:!0}:this.commandMap_[r]={name:r,type:"exToKey",toKeys:t,user:!0}}else if(t!=":"&&t.charAt(0)==":"){var i={keys:e,type:"keyToEx",exArgs:{input:t.substring(1)}};n&&(i.context=n),w.unshift(i)}else{var i={keys:e,type:"keyToKey",toKeys:t};n&&(i.context=n),w.unshift(i)}},unmap:function(e,t){if(e!=":"&&e.charAt(0)==":"){if(t)throw Error("Mode not supported for ex mappings");var n=e.substring(1);if(this.commandMap_[n]&&this.commandMap_[n].user){delete this.commandMap_[n];return}}else{var r=e;for(var i=0;i<w.length;i++)if(r==w[i].keys&&w[i].context===t){w.splice(i,1);return}}}};var Jn={colorscheme:function(e,t){if(!t.args||t.args.length<1){Dn(e,e.getOption("theme"));return}e.setOption("theme",t.args[0])},map:function(e,t,n){var r=t.args;if(!r||r.length<2){e&&Dn(e,"Invalid mapping: "+t.input);return}Kn.map(r[0],r[1],n)},imap:function(e,t){this.map(e,t,"insert")},nmap:function(e,t){this.map(e,t,"normal")},vmap:function(e,t){this.map(e,t,"visual")},unmap:function(e,t,n){var r=t.args;if(!r||r.length<1){e&&Dn(e,"No such mapping: "+t.input);return}Kn.unmap(r[0],n)},move:function(e,t){gt.processCommand(e,e.state.vim,{type:"motion",motion:"moveToLineOrEdgeOfDocument",motionArgs:{forward:!1,explicitRepeat:!0,linewise:!0},repeatOverride:t.line+1})},set:function(e,t){var n=t.args,r=t.setCfg||{};if(!n||n.length<1){e&&Dn(e,"Invalid mapping: "+t.input);return}var i=n[0].split("="),s=i[0],o=i[1],u=!1;if(s.charAt(s.length-1)=="?"){if(o)throw Error("Trailing characters: "+t.argString);s=s.substring(0,s.length-1),u=!0}o===undefined&&s.substring(0,2)=="no"&&(s=s.substring(2),o=!1);var a=Z[s]&&Z[s].type=="boolean";a&&o==undefined&&(o=!0);if(!a&&o===undefined||u){var f=nt(s,e,r);f instanceof Error?Dn(e,f.message):f===!0||f===!1?Dn(e," "+(f?"":"no")+s):Dn(e,"  "+s+"="+f)}else{var l=tt(s,o,e,r);l instanceof Error&&Dn(e,l.message)}},setlocal:function(e,t){t.setCfg={scope:"local"},this.set(e,t)},setglobal:function(e,t){t.setCfg={scope:"global"},this.set(e,t)},registers:function(e,t){var n=t.args,r=ut.registerController.registers,i="----------Registers----------\n\n";if(!n)for(var s in r){var o=r[s].toString();o.length&&(i+='"'+s+"    "+o+"\n")}else{var s;n=n.join("");for(var u=0;u<n.length;u++){s=n.charAt(u);if(!ut.registerController.isValidRegister(s))continue;var a=r[s]||new pt;i+='"'+s+"    "+a.toString()+"\n"}}Dn(e,i)},sort:function(e,t){function u(){if(t.argString){var e=new m.StringStream(t.argString);e.eat("!")&&(n=!0);if(e.eol())return;if(!e.eatSpace())return"Invalid arguments";var u=e.match(/([dinuox]+)?\s*(\/.+\/)?\s*/);if(!u&&!e.eol())return"Invalid arguments";if(u[1]){r=u[1].indexOf("i")!=-1,i=u[1].indexOf("u")!=-1;var a=u[1].indexOf("d")!=-1||u[1].indexOf("n")!=-1&&1,f=u[1].indexOf("x")!=-1&&1,l=u[1].indexOf("o")!=-1&&1;if(a+f+l>1)return"Invalid arguments";s=a&&"decimal"||f&&"hex"||l&&"octal"}u[2]&&(o=new RegExp(u[2].substr(1,u[2].length-2),r?"i":""))}}function E(e,t){if(n){var i;i=e,e=t,t=i}r&&(e=e.toLowerCase(),t=t.toLowerCase());var o=s&&d.exec(e),u=s&&d.exec(t);return o?(o=parseInt((o[1]+o[2]).toLowerCase(),v),u=parseInt((u[1]+u[2]).toLowerCase(),v),o-u):e<t?-1:1}function S(e,t){if(n){var i;i=e,e=t,t=i}return r&&(e[0]=e[0].toLowerCase(),t[0]=t[0].toLowerCase()),e[0]<t[0]?-1:1}var n,r,i,s,o,a=u();if(a){Dn(e,a+": "+t.argString);return}var f=t.line||e.firstLine(),l=t.lineEnd||t.line||e.lastLine();if(f==l)return;var c=x(f,0),h=x(l,Ft(e,l)),p=e.getRange(c,h).split("\n"),d=o?o:s=="decimal"?/(-?)([\d]+)/:s=="hex"?/(-?)(?:0x)?([0-9a-f]+)/i:s=="octal"?/([0-7]+)/:null,v=s=="decimal"?10:s=="hex"?16:s=="octal"?8:null,g=[],y=[];if(s||o)for(var b=0;b<p.length;b++){var w=o?p[b].match(o):null;w&&w[0]!=""?g.push(w):!o&&d.exec(p[b])?g.push(p[b]):y.push(p[b])}else y=p;g.sort(o?S:E);if(o)for(var b=0;b<g.length;b++)g[b]=g[b].input;else s||y.sort(E);p=n?g.concat(y):y.concat(g);if(i){var T=p,N;p=[];for(var b=0;b<T.length;b++)T[b]!=N&&p.push(T[b]),N=T[b]}e.replaceRange(p.join("\n"),c,h)},vglobal:function(e,t){this.global(e,t)},global:function(e,t){var n=t.argString;if(!n){Dn(e,"Regular Expression missing from global");return}var r=t.commandName[0]==="v",i=t.line!==undefined?t.line:e.firstLine(),s=t.lineEnd||t.line||e.lastLine(),o=Sn(n),u=n,a;o.length&&(u=o[0],a=o.slice(1,o.length).join("/"));if(u)try{jn(e,u,!0,!0)}catch(f){Dn(e,"Invalid regex: "+u);return}var l=En(e).getQuery(),c=[];for(var h=i;h<=s;h++){var p=e.getLineHandle(h),d=l.test(p.text);d!==r&&c.push(a?p:p.text)}if(!a){Dn(e,c.join("\n"));return}var v=0,m=function(){if(v<c.length){var t=c[v++],n=e.getLineNumber(t);if(n==null){m();return}var r=n+1+a;Kn.processCommand(e,r,{callback:m})}};m()},substitute:function(e,t){if(!e.getSearchCursor)throw new Error("Search feature not available. Requires searchcursor.js or any other getSearchCursor implementation.");var n=t.argString,r=n?Tn(n,n[0]):[],i,s="",o,u,a,f=!1,l=!1;if(r.length)i=r[0],nt("pcre")&&i!==""&&(i=(new RegExp(i)).source),s=r[1],s!==undefined&&(nt("pcre")?s=On(s.replace(/([^\\])&/g,"$1$$&")):s=Ln(s),ut.lastSubstituteReplacePart=s),o=r[2]?r[2].split(" "):[];else if(n&&n.length){Dn(e,"Substitutions should be of the form :s/pattern/replace/");return}o&&(u=o[0],a=parseInt(o[1]),u&&(u.indexOf("c")!=-1&&(f=!0),u.indexOf("g")!=-1&&(l=!0),nt("pcre")?i=i+"/"+u:i=i.replace(/\//g,"\\/")+"/"+u));if(i)try{jn(e,i,!0,!0)}catch(c){Dn(e,"Invalid regex: "+i);return}s=s||ut.lastSubstituteReplacePart;if(s===undefined){Dn(e,"No previous substitute regular expression");return}var h=En(e),p=h.getQuery(),d=t.line!==undefined?t.line:e.getCursor().line,v=t.lineEnd||d;d==e.firstLine()&&v==e.lastLine()&&(v=Infinity),a&&(d=v,v=d+a-1);var m=Nt(e,x(d,0)),g=e.getSearchCursor(p,m);Qn(e,f,l,d,v,g,p,s,t.callback)},redo:m.commands.redo,undo:m.commands.undo,write:function(e){m.commands.save?m.commands.save(e):e.save&&e.save()},nohlsearch:function(e){Un(e)},yank:function(e){var t=_t(e.getCursor()),n=t.line,r=e.getLine(n);ut.registerController.pushText("0","yank",r,!0,!0)},delmarks:function(e,t){if(!t.argString||!It(t.argString)){Dn(e,"Argument required");return}var n=e.state.vim,r=new m.StringStream(It(t.argString));while(!r.eol()){r.eatSpace();var i=r.pos;if(!r.match(/[a-zA-Z]/,!1)){Dn(e,"Invalid argument: "+t.argString.substring(i));return}var s=r.next();if(r.match("-",!0)){if(!r.match(/[a-zA-Z]/,!1)){Dn(e,"Invalid argument: "+t.argString.substring(i));return}var o=s,u=r.next();if(!(V(o)&&V(u)||K(o)&&K(u))){Dn(e,"Invalid argument: "+o+"-");return}var a=o.charCodeAt(0),f=u.charCodeAt(0);if(a>=f){Dn(e,"Invalid argument: "+t.argString.substring(i));return}for(var l=0;l<=f-a;l++){var c=String.fromCharCode(a+l);delete n.marks[c]}}else delete n.marks[s]}}},Kn=new $n;m.keyMap.vim={attach:L,detach:k,call:A},et("insertModeEscKeysTimeout",200,"number"),m.keyMap["vim-insert"]={fallthrough:["default"],attach:L,detach:k,call:A},m.keyMap["vim-replace"]={Backspace:"goCharLeft",fallthrough:["vim-insert"],attach:L,detach:k,call:A},at(),m.Vim=T(),T=m.Vim;var pr={"return":"CR",backspace:"BS","delete":"Del",esc:"Esc",left:"Left",right:"Right",up:"Up",down:"Down",space:"Space",home:"Home",end:"End",pageup:"PageUp",pagedown:"PageDown",enter:"CR"},vr=T.handleKey.bind(T);T.handleKey=function(e,t,n){return e.operation(function(){return vr(e,t,n)},!0)},t.CodeMirror=m;var yr=T.maybeInitVimState_;t.handler={$id:"ace/keyboard/vim",drawCursor:function(e,t,n,r,s){var u=this.state.vim||{},a=n.characterWidth,f=n.lineHeight,l=t.top,c=t.left;if(!u.insertMode){var h=r.cursor?i.comparePoints(r.cursor,r.start)<=0:s.selection.isBackwards()||s.selection.isEmpty();!h&&c>a&&(c-=a)}!u.insertMode&&u.status&&(f/=2,l+=f),o.translate(e,c,l),o.setStyle(e.style,"width",a+"px"),o.setStyle(e.style,"height",f+"px")},handleKeyboard:function(e,t,n,r,i){var s=e.editor,o=s.state.cm,u=yr(o);if(r==-1)return;u.insertMode||(t==-1?(n.charCodeAt(0)>255&&e.inputKey&&(n=e.inputKey,n&&e.inputHash==4&&(n=n.toUpperCase())),e.inputChar=n):t==4||t==0?e.inputKey==n&&e.inputHash==t&&e.inputChar?(n=e.inputChar,t=-1):(e.inputChar=null,e.inputKey=n,e.inputHash=t):e.inputChar=e.inputKey=null);if(n=="c"&&t==1&&!c.isMac&&s.getCopyText())return s.once("copy",function(){s.selection.clearSelection()}),{command:"null",passEvent:!0};if(n=="esc"&&!u.insertMode&&!u.visualMode&&!o.ace.inMultiSelectMode){var a=En(o),f=a.getOverlay();f&&o.removeOverlay(f)}if(t==-1||t&1||t===0&&n.length>1){var l=u.insertMode,h=dr(t,n,i||{});u.status==null&&(u.status="");var p=gr(o,h,"user");u=yr(o),p&&u.status!=null?u.status+=h:u.status==null&&(u.status=""),o._signal("changeStatus");if(!p&&(t!=-1||l))return;return{command:"null",passEvent:!p}}},attach:function(e){function n(){var n=yr(t).insertMode;t.ace.renderer.setStyle("normal-mode",!n),e.textInput.setCommandMode(!n),e.renderer.$keepTextAreaAtCursor=n,e.renderer.$blockCursor=!n}e.state||(e.state={});var t=new m(e);e.state.cm=t,e.$vimModeHandler=this,m.keyMap.vim.attach(t),yr(t).status=null,t.on("vim-command-done",function(){if(t.virtualSelectionMode())return;yr(t).status=null,t.ace._signal("changeStatus"),t.ace.session.markUndoGroup()}),t.on("changeStatus",function(){t.ace.renderer.updateCursor(),t.ace._signal("changeStatus")}),t.on("vim-mode-change",function(){if(t.virtualSelectionMode())return;n(),t._signal("changeStatus")}),n(),e.renderer.$cursorLayer.drawCursor=this.drawCursor.bind(t)},detach:function(e){var t=e.state.cm;m.keyMap.vim.detach(t),t.destroy(),e.state.cm=null,e.$vimModeHandler=null,e.renderer.$cursorLayer.drawCursor=null,e.renderer.setStyle("normal-mode",!1),e.textInput.setCommandMode(!1),e.renderer.$keepTextAreaAtCursor=!0},getStatusText:function(e){var t=e.state.cm,n=yr(t);if(n.insertMode)return"INSERT";var r="";return n.visualMode&&(r+="VISUAL",n.visualLine&&(r+=" LINE"),n.visualBlock&&(r+=" BLOCK")),n.status&&(r+=(r?" ":"")+n.status),r}},T.defineOption({name:"wrap",set:function(e,t){t&&t.ace.setOption("wrap",e)},type:"boolean"},!1),T.defineEx("write","w",function(){console.log(":write is not implemented")}),w.push({keys:"zc",type:"action",action:"fold",actionArgs:{open:!1}},{keys:"zC",type:"action",action:"fold",actionArgs:{open:!1,all:!0}},{keys:"zo",type:"action",action:"fold",actionArgs:{open:!0}},{keys:"zO",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"za",type:"action",action:"fold",actionArgs:{toggle:!0}},{keys:"zA",type:"action",action:"fold",actionArgs:{toggle:!0,all:!0}},{keys:"zf",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"zd",type:"action",action:"fold",actionArgs:{open:!0,all:!0}},{keys:"<C-A-k>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorAbove"}},{keys:"<C-A-j>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorBelow"}},{keys:"<C-A-S-k>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorAboveSkipCurrent"}},{keys:"<C-A-S-j>",type:"action",action:"aceCommand",actionArgs:{name:"addCursorBelowSkipCurrent"}},{keys:"<C-A-h>",type:"action",action:"aceCommand",actionArgs:{name:"selectMoreBefore"}},{keys:"<C-A-l>",type:"action",action:"aceCommand",actionArgs:{name:"selectMoreAfter"}},{keys:"<C-A-S-h>",type:"action",action:"aceCommand",actionArgs:{name:"selectNextBefore"}},{keys:"<C-A-S-l>",type:"action",action:"aceCommand",actionArgs:{name:"selectNextAfter"}}),w.push({keys:"gq",type:"operator",operator:"hardWrap"}),T.defineOperator("hardWrap",function(e,t,n,r,i){var s=n[0].anchor.line,o=n[0].head.line;return t.linewise&&o--,v(e.ace,{startRow:s,endRow:o}),x(o,0)}),et("textwidth",undefined,"number",["tw"],function(e,t){if(t===undefined)return;if(e===undefined){var n=t.ace.getOption("printMarginColumn");return n}var r=Math.round(e);r>1&&t.ace.setOption("printMarginColumn",r)}),xt.aceCommand=function(e,t,n){e.vimCmd=t,e.ace.inVirtualSelectionMode?e.ace.on("beforeEndOperation",br):br(null,e.ace)},xt.fold=function(e,t,n){e.ace.execCommand(["toggleFoldWidget","toggleFoldWidget","foldOther","unfoldall"][(t.all?2:0)+(t.open?1:0)])},t.handler.defaultKeymap=w,t.handler.actions=xt,t.Vim=T});                (function() {
                    ace.require(["ace/keyboard/vim"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            