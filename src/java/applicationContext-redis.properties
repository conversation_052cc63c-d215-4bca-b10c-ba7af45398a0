session.messagepoint.redis.host=127.0.0.1
session.messagepoint.redis.port=6379
# mode: standalone / master-replica / cluster / sentinel
# absent, empty value or any other non-listed value is treated as standalone
session.messagepoint.redis.mode=standalone
session.messagepoint.redis.namespace=trunk
session.messagepoint.redis.replica.host=127.0.0.1
session.messagepoint.redis.replica.port=6380
session.messagepoint.redis.cluster.max-redirects=6
session.messagepoint.redis.use_ssl=false
session.messagepoint.redis.ssl.protocols=TLSv1.3,TLSv1.2
session.messagepoint.redis.start_tls=false
session.messagepoint.redis.ping-before-activate=true
session.messagepoint.redis.pool-config.min-idle=4
session.messagepoint.redis.pool-config.max-idle=16
session.messagepoint.redis.pool-config.max-total=20
# Timeout values in milliseconds
# Timeout options:
#     * -1 or less to wait forever
#     * 0 to use default values
#     * greater than 0)
session.messagepoint.redis.connection_timeout=60000
session.messagepoint.redis.default_command_timeout=60000
session.messagepoint.redis.meta_command_timeout=60000
# Leave username and/or password empty if they aren't used
session.messagepoint.redis.username=
session.messagepoint.redis.password=
# Master-Replica mode doesn't support pubsub connections
# This alternative configuration will be used instead
# Falls back to same configurations without .pubsub when empty or absent
session.messagepoint.redis.pubsub.host=
session.messagepoint.redis.pubsub.port=
session.messagepoint.redis.pubsub.mode=
