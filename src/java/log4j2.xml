<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%msg%n"/>
        </Console>
        <Routing name="TaskLog">
            <Routes pattern="$${ctx:TaskLogFileName}">
                <Route>
                    <File name="${ctx:TaskLogFileName}" fileName="${ctx:TaskLogFileName}">
                        <PatternLayout>
                            <Pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Pattern>
                        </PatternLayout>
                    </File>
                </Route>
                <Route key="$${ctx:TaskLogFileName}">
                    <Null />
                </Route>
            </Routes>
            <IdlePurgePolicy timeToLive="2" timeUnit="minutes"/>
        </Routing>
    </Appenders>
    <Loggers>
        <Root level="All">
            <AppenderRef ref="Console" level="error" />
            <AppenderRef ref="TaskLog" level="info" />
        </Root>

                <!-- Messagepoint Application Loggers -->
        <Logger name="com.prinova.messagepoint.controller.HttpSessionTimeoutListener" level="info" additivity="false">
            <AppenderRef ref="Console" />
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.controller.MessagepointAuthenticationProcessingFilter" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.controller.MessagepointAuthenticationFailureHandler" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="elasticsearchlogger" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.services.backgroundtask" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Util loggers -->
        <Logger name="com.prinova.messagepoint.util.UserUtil" level="info" additivity="false">
            <AppenderRef ref="Console" />
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.util.ApplicationUtil" level="info" additivity="false">
            <AppenderRef ref="Console" />
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.util.DataSecurityUtils" level="info" additivity="false">
            <AppenderRef ref="Console" />
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.util.EmailManager" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.model.openidconnect.OIDCSigningKeyPair" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>

        <!-- Migration Loggers -->
        <Logger name="com.prinova.messagepoint.initialization.InitializeDatabase" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.initialization.InitializeSchema" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.initialization.CopySchema" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.migrate" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Job Execution Loggers -->
        <Logger name="com.prinova.messagepoint.model.testing" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.model.deliveryevent" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.model.util" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TaskLog" />
        </Logger>
        <Logger name="com.prinova.messagepoint.job" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.email" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.services.job" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.model.manager" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.UploadProcessedJobEndpoint" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.MessagePointStartUp" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.connected.nojob.ConnectedInteractiveDataService" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.model.admin.deserver.DEServer" level="warn" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Authentication Loggers -->
        <Logger name="com.prinova.messagepoint.controller.MessagepointAuthenticationFailureHandler" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.controller.MessagepointAuthenticationSuccessHandler" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.security.sso.SSOFilter" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.auth.MpOpenIdConnectDispatcherServlet" level="info" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>

        <!-- WS Client Loggers -->
        <Logger name="com.prinova.messagepoint.platform.ws.client.DEWSClientWrapper" level="warn" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.client.MpCustomerClientWrapper" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.client.WsClientDEWS" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.client.WsClientMessagepoint" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>

        <!-- added temporarily for troubleshooting purposes -->
        <Logger name="com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerPMGRProvider" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.util.RedisUtil" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.redis.*" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.services.backgroundtask.DeleteDBSchemaBackgroundTask" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.client.SoapLoggingInterceptor" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
        <Logger name="com.prinova.messagepoint.platform.ws.client.CustomFaultMessageResolver" level="warn" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>
    </Loggers>
</Configuration>
