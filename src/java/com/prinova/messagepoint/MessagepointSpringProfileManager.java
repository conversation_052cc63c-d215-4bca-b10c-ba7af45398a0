package com.prinova.messagepoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.stream.Stream;

@Configuration
public class MessagepointSpringProfileManager {

    @Autowired
    Environment environment;

    public boolean isSpringSessionProfileActive() {
        if (environment != null) {
            return Stream.of(environment.getActiveProfiles()).anyMatch(x -> x.toLowerCase().equals("spring-session"));
        }

        return false;
    }
}
