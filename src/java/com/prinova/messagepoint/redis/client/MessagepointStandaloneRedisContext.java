package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.RedisMode;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.StringUtils;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_STANDALONE)
public class MessagepointStandaloneRedisContext extends MessagepointRedisContextBase {

    private static final Log logger = LogUtil.getLog(MessagepointStandaloneRedisContext.class);

    @Autowired
    private MessagepointRedisConfiguration redisConfig;

    @Override
    public LettucePoolingClientConfiguration getLettuceClientConfiguration() {
        return createBaseClientConfigurationBuilder()
                .readFrom(redisConfig.getOptimalReadFrom(RedisMode.STANDALONE)) // Will return MASTER
                .clientOptions(getClientOptions())
                .build();
    }

    @Override
    public RedisStandaloneConfiguration getRedisConfiguration() {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setHostName(redisConfig.getHost());
        redisStandaloneConfiguration.setPort(redisConfig.getPort());

        //if (StringUtils.hasText(redisConfig.getUsername()))
            //redisStandaloneConfiguration.setUsername(redisConfig.getUsername());
        if (StringUtils.hasText(redisConfig.getPassword()))
            redisStandaloneConfiguration.setPassword(redisConfig.getPassword());

        return redisStandaloneConfiguration;
    }

    @Override
    public LettuceConnectionFactory getLettuceConnectionFactory() {
        return createConnectionFactory(getRedisConfiguration(), getLettuceClientConfiguration());
    }

    @Override
    protected MessagepointRedisConfiguration getRedisConfig() {
        return redisConfig;
    }
}
