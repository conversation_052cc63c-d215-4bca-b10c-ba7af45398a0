package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_SENTINEL)
public class MessagepointSentinelRedisContext extends MessagepointRedisContextBase {

    private static final Log logger = LogUtil.getLog(MessagepointSentinelRedisContext.class);

    @Autowired
    private MessagepointRedisConfiguration redisConfig;

    @Override
    public LettucePoolingClientConfiguration getLettuceClientConfiguration() {
        return createBaseClientConfigurationBuilder()
                .readFrom(ReadFrom.REPLICA_PREFERRED)
                .clientOptions(getClientOptions())
                .build();
    }

    @Override
    public RedisSentinelConfiguration getRedisConfiguration() {
        Set<String> hostAndPorts = new HashSet<>();
        hostAndPorts.add(redisConfig.getHost() + ":" + redisConfig.getPort());
        hostAndPorts.add(redisConfig.getReplicaHost() + ":" + redisConfig.getReplicaPort());

        RedisSentinelConfiguration redisSentinelConfiguration = new RedisSentinelConfiguration(redisConfig.getRedisNamespace(), hostAndPorts);

        if (StringUtils.hasText(redisConfig.getPassword()))
            redisSentinelConfiguration.setPassword(redisConfig.getPassword());

        return redisSentinelConfiguration;
    }

    @Override
    public LettuceConnectionFactory getLettuceConnectionFactory() {
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(getRedisConfiguration(), getLettuceClientConfiguration());
        connectionFactory.setValidateConnection(true);
        connectionFactory.setShareNativeConnection(true);
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

    @Override
    protected MessagepointRedisConfiguration getRedisConfig() {
        return redisConfig;
    }
}
