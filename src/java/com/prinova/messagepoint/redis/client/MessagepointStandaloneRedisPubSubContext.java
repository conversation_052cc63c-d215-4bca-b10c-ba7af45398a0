package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import org.apache.commons.logging.Log;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.StringUtils;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_STANDALONE)
public class MessagepointStandaloneRedisPubSubContext extends MessagepointRedisPubSubContext {

    private static final Log logger = LogUtil.getLog(MessagepointStandaloneRedisPubSubContext.class);

    @Autowired
    private MessagepointRedisConfiguration redisConfig;

    private ClientOptions getClientOptions() {
        ClientOptions.Builder builder = ClientOptions.builder()
                .autoReconnect(true)
                .pingBeforeActivateConnection(redisConfig.isPingBeforeActivate())
                .socketOptions(redisConfig.getSocketOptions());

        SslOptions sslOptions = redisConfig.getSslOptions();
        if (sslOptions != null)
            builder.sslOptions(sslOptions);

        TimeoutOptions timeoutOptions = redisConfig.getTimeoutOptions();
        if (timeoutOptions != null)
            builder.timeoutOptions(timeoutOptions);

        return builder.build();
    }

    @Override
    public LettucePoolingClientConfiguration getLettuceClientConfiguration() {
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
                LettucePoolingClientConfiguration.builder()
                        .readFrom(ReadFrom.REPLICA_PREFERRED)
                        .poolConfig(new GenericObjectPoolConfig<>())
                        .clientOptions(getClientOptions())
                        .clientResources(redisConfig.getClientResources())
                        .commandTimeout(redisConfig.getDefaultCommandTimeout());

        if (redisConfig.isStartTLS())
            builder.useSsl().disablePeerVerification().startTls();
        else if (redisConfig.isUseSSL())
            builder.useSsl().disablePeerVerification();

        return builder.build();
    }

    @Override
    public RedisStandaloneConfiguration getRedisConfiguration() {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setHostName(redisConfig.getPubSubHost());
        redisStandaloneConfiguration.setPort(redisConfig.getPubSubPort());

        //if (StringUtils.hasText(redisConfig.getUsername()))
            //redisStandaloneConfiguration.setUsername(redisConfig.getUsername());
        if (StringUtils.hasText(redisConfig.getPassword()))
            redisStandaloneConfiguration.setPassword(redisConfig.getPassword());

        return redisStandaloneConfiguration;
    }

    @Override
    public LettuceConnectionFactory getPubSubConnectionFactory() {
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(getRedisConfiguration(), getLettuceClientConfiguration());
        connectionFactory.setValidateConnection(true);
        //connectionFactory.setEagerInitialization(true);
        connectionFactory.setShareNativeConnection(true);
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

}
