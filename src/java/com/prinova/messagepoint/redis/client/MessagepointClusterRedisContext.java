package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.RedisMode;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_CLUSTER)
public class MessagepointClusterRedisContext extends MessagepointRedisContextBase {

    private static final Log logger = LogUtil.getLog(MessagepointClusterRedisContext.class);

    @Autowired
    private MessagepointRedisConfiguration redisConfig;

    private ClusterTopologyRefreshOptions getClusterTopologyRefreshOptions() {
        return ClusterTopologyRefreshOptions.builder()
                .dynamicRefreshSources(true)
                .enableAllAdaptiveRefreshTriggers()
                .enablePeriodicRefresh()
                .build();
    }

    private ClusterClientOptions getClusterClientOptions() {
        ClusterClientOptions.Builder builder = ClusterClientOptions.builder()
                .autoReconnect(true)
                .pingBeforeActivateConnection(redisConfig.isPingBeforeActivate())
                .validateClusterNodeMembership(false)
                .topologyRefreshOptions(getClusterTopologyRefreshOptions())
                .socketOptions(redisConfig.getSocketOptions())
                .nodeFilter(node -> !(
                        node.is(RedisClusterNode.NodeFlag.FAIL) ||
                                node.is(RedisClusterNode.NodeFlag.EVENTUAL_FAIL) ||
                                node.is(RedisClusterNode.NodeFlag.HANDSHAKE) ||
                                node.is(RedisClusterNode.NodeFlag.NOADDR)));

        if (redisConfig.getMaxRedirects() > 0)
            builder.maxRedirects(redisConfig.getMaxRedirects());

        SslOptions sslOptions = redisConfig.getSslOptions();
        if (sslOptions != null)
            builder.sslOptions(sslOptions);

        TimeoutOptions timeoutOptions = redisConfig.getTimeoutOptions();
        if (timeoutOptions != null)
            builder.timeoutOptions(timeoutOptions);

        return builder.build();
    }

    @Override
    public LettucePoolingClientConfiguration getLettuceClientConfiguration() {
        return createBaseClientConfigurationBuilder()
                .clientOptions(getClusterClientOptions())
                .readFrom(redisConfig.getOptimalReadFrom(RedisMode.CLUSTER))
                .build();
    }

    @Override
    public RedisClusterConfiguration getRedisConfiguration() {
        List<String> clusterNodes = redisConfig.getClusterNodes();

        if (clusterNodes == null || clusterNodes.isEmpty()
                || clusterNodes.get(0) == null || clusterNodes.get(0).isEmpty()) {
            clusterNodes = new ArrayList<>();
            clusterNodes.add(redisConfig.getHost() + ":" + redisConfig.getPort());
            redisConfig.setClusterNodes(clusterNodes);
        }

        RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(clusterNodes);
        if (redisConfig.getMaxRedirects() > 0)
            redisClusterConfiguration.setMaxRedirects(redisConfig.getMaxRedirects());
        //if (StringUtils.hasText(redisConfig.getUsername()))
        //    redisClusterConfiguration.setUsername(redisConfig.getUsername());
        if (StringUtils.hasText(redisConfig.getPassword()))
            redisClusterConfiguration.setPassword(redisConfig.getPassword());

        return redisClusterConfiguration;
    }

    @Override
    public LettuceConnectionFactory getLettuceConnectionFactory() {
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(getRedisConfiguration(), getLettuceClientConfiguration());
        connectionFactory.setValidateConnection(true);
        //connectionFactory.setEagerInitialization(true);
        connectionFactory.setShareNativeConnection(true);
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

    @Override
    protected MessagepointRedisConfiguration getRedisConfig() {
        return redisConfig;
    }
}
