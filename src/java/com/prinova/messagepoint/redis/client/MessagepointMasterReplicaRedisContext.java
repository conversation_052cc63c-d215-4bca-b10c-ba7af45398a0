package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.RedisMode;
import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_MASTER_REPLICA)
public class MessagepointMasterReplicaRedisContext extends MessagepointRedisContextBase {

    private static final Log logger = LogUtil.getLog(MessagepointMasterReplicaRedisContext.class);

    @Autowired
    private MessagepointRedisConfiguration redisConfig;

    @Override
    public LettucePoolingClientConfiguration getLettuceClientConfiguration() {
        return createBaseClientConfigurationBuilder()
                .readFrom(redisConfig.getOptimalReadFrom(RedisMode.MASTER_REPLICA))
                .clientOptions(getClientOptions())
                .build();
    }

    @Override
    public RedisStaticMasterReplicaConfiguration getRedisConfiguration() {
        RedisStaticMasterReplicaConfiguration redisMasterReplicaConfiguration = new RedisStaticMasterReplicaConfiguration(redisConfig.getHost(), redisConfig.getPort());

        redisMasterReplicaConfiguration.addNode(redisConfig.getReplicaHost(), redisConfig.getReplicaPort());

        if (StringUtils.hasText(redisConfig.getPassword()))
            redisMasterReplicaConfiguration.setPassword(redisConfig.getPassword());

        return redisMasterReplicaConfiguration;
    }

    @Override
    public LettuceConnectionFactory getLettuceConnectionFactory() {
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(getRedisConfiguration(), getLettuceClientConfiguration());
        connectionFactory.setValidateConnection(true);
        connectionFactory.setShareNativeConnection(true);
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

    @Override
    protected MessagepointRedisConfiguration getRedisConfig() {
        return redisConfig;
    }
}
