package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;

public abstract class MessagepointRedisContextBase extends MessagepointRedisContext {

    protected abstract MessagepointRedisConfiguration getRedisConfig();

    protected LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder
    createBaseClientConfigurationBuilder() {

        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
                LettucePoolingClientConfiguration.builder()
                        .readFrom(getRedisConfig().getOptimalReadFrom())
                        .poolConfig(getRedisConfig().poolConfig())
                        .clientResources(getRedisConfig().getClientResources())
                        .commandTimeout(getRedisConfig().getDefaultCommandTimeout());

        // SSL/TLS configuration
        if (getRedisConfig().isStartTLS()) {
            builder.useSsl().disablePeerVerification().startTls();
        } else if (getRedisConfig().isUseSSL()) {
            builder.useSsl().disablePeerVerification();
        }

        return builder;
    }

    protected LettuceConnectionFactory createConnectionFactory(
            RedisConfiguration redisConfiguration,
            LettuceClientConfiguration clientConfiguration) {

        LettuceConnectionFactory connectionFactory =
                new LettuceConnectionFactory(redisConfiguration, clientConfiguration);

        connectionFactory.setValidateConnection(true);
        connectionFactory.setShareNativeConnection(true);

        // Enhanced connection factory settings
        //connectionFactory.setEagerInitialization(false); // Lazy initialization for better startup

        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }
}
