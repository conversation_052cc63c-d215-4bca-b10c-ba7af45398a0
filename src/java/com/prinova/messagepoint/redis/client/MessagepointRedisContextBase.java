package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;

public abstract class MessagepointRedisContextBase extends MessagepointRedisContext {

    protected abstract MessagepointRedisConfiguration getRedisConfig();

    protected ClientOptions getClientOptions() {
        ClientOptions.Builder builder = ClientOptions.builder()
                .autoReconnect(true)
                .pingBeforeActivateConnection(getRedisConfig().isPingBeforeActivate())
                .socketOptions(getRedisConfig().getSocketOptions());

        SslOptions sslOptions = getRedisConfig().getSslOptions();
        if (sslOptions != null)
            builder.sslOptions(sslOptions);

        TimeoutOptions timeoutOptions = getRedisConfig().getTimeoutOptions();
        if (timeoutOptions != null)
            builder.timeoutOptions(timeoutOptions);

        return builder.build();
    }

    protected LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder
    createBaseClientConfigurationBuilder() {

        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
                LettucePoolingClientConfiguration.builder()
                        .readFrom(getRedisConfig().getOptimalReadFrom())
                        .poolConfig(getRedisConfig().poolConfig())
                        .clientResources(getRedisConfig().getClientResources())
                        .commandTimeout(getRedisConfig().getDefaultCommandTimeout());

        // SSL/TLS configuration
        if (getRedisConfig().isStartTLS()) {
            builder.useSsl().disablePeerVerification().startTls();
        } else if (getRedisConfig().isUseSSL()) {
            builder.useSsl().disablePeerVerification();
        }

        return builder;
    }

    protected LettuceConnectionFactory createConnectionFactory(
            RedisConfiguration redisConfiguration,
            LettuceClientConfiguration clientConfiguration) {

        LettuceConnectionFactory connectionFactory =
                new LettuceConnectionFactory(redisConfiguration, clientConfiguration);

        connectionFactory.setValidateConnection(true);
        connectionFactory.setShareNativeConnection(true);

        // Enhanced connection factory settings
        //connectionFactory.setEagerInitialization(false); // Lazy initialization for better startup

        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

    /**
     * Adds common client options (SSL, timeouts) to any client options builder
     */
    protected <T extends ClientOptions.Builder> void addCommonClientOptions(T builder) {
        // Add SSL options if configured
        SslOptions sslOptions = getRedisConfig().getSslOptions();
        if (sslOptions != null) {
            builder.sslOptions(sslOptions);
        }

        // Add timeout options if configured
        TimeoutOptions timeoutOptions = getRedisConfig().getTimeoutOptions();
        if (timeoutOptions != null) {
            builder.timeoutOptions(timeoutOptions);
        }

        // Add socket options
        builder.socketOptions(getRedisConfig().getSocketOptions());
    }

}
