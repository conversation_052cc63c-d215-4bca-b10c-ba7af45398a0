package com.prinova.messagepoint.redis.client;

import com.prinova.messagepoint.redis.config.MessagepointRedisConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.masterreplica.MasterReplica;
import io.lettuce.core.masterreplica.StatefulRedisMasterReplicaConnection;
import io.lettuce.core.pubsub.StatefulRedisPubSubConnection;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.lang.NonNull;

import java.lang.reflect.Field;
import java.util.List;

@Configuration
@Qualifier(MessagepointRedisConfiguration.REDIS_MODE_MASTER_REPLICA)
public class MessagepointMasterReplicaRedisConnection implements MessagepointRedisConnection {

    private static final Log logger = LogUtil.getLog(MessagepointMasterReplicaRedisConnection.class);

    private final LettuceConnectionFactory lettuceConnectionFactory;
    private final MessagepointRedisConfiguration redisConfig;

    public MessagepointMasterReplicaRedisConnection(@NonNull LettuceConnectionFactory redisConnectionFactory,
                                                    @NonNull MessagepointRedisConfiguration redisConfig) {
        this.lettuceConnectionFactory = redisConnectionFactory;
        this.redisConfig = redisConfig;
    }

    private RedisClient getRedisClient() {
        RedisClient client = null;

        try {
            Field clientField = LettuceConnectionFactory.class.getDeclaredField("client");
            clientField.setAccessible(true);
            client = (RedisClient) clientField.get(lettuceConnectionFactory);
        } catch (IllegalAccessException| NoSuchFieldException e) {
            logger.error("Error while requesting a Redis connection", e);
        }

        return client;
    }

    @Override
    public StatefulRedisMasterReplicaConnection<byte[], byte[]> getRedisConnection() {
        RedisClient client = getRedisClient();
        RedisURI masterURI = redisConfig.getRedisURI();
        RedisURI replicaURI = redisConfig.getRedisReplicaURI();
        Iterable<RedisURI> uris = List.of(masterURI, replicaURI);
        return client != null ? MasterReplica.connect(client, new ByteArrayCodec(), uris) : null;
    }

}
