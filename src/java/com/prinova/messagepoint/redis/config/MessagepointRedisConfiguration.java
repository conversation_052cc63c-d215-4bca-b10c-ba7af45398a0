package com.prinova.messagepoint.redis.config;

import com.prinova.messagepoint.classpath.MessagepointPropertySourceFactory;
import com.prinova.messagepoint.redis.CommandTimeoutSource;
import com.prinova.messagepoint.redis.RedisMode;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringUtil;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisURI;
import io.lettuce.core.SocketOptions;
import io.lettuce.core.SslOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.Delay;
import io.lettuce.core.resource.DirContextDnsResolver;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.util.ObjectUtils;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.naming.spi.NamingManager;
import java.time.Duration;
import java.util.Hashtable;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Configuration
@PropertySource( factory = MessagepointPropertySourceFactory.class, name = "applicationContext-redis.properties", value = "classpath:applicationContext-redis.properties")
public class MessagepointRedisConfiguration {

    private static final Log logger = LogUtil.getLog(MessagepointRedisConfiguration.class);

    public final static String REDIS_MODE_STANDALONE = "redis.mode.standalone";
    public final static String REDIS_MODE_CLUSTER = "redis.mode.cluster";
    public final static String REDIS_MODE_MASTER_REPLICA = "redis.mode.master-replica";
    public final static String REDIS_MODE_SENTINEL = "redis.mode.sentinel";

    private int minIdle;

    private int maxIdle;

    private int maxTotal;

    private String host;

    private int port;

    private String replicaHost;

    private int replicaPort;

    private List<String> clusterNodes;

    private int maxRedirects;

    private String redisNamespace;

    private boolean useSSL;

    private boolean startTLS;

    private Duration connectionTimeout;

    private Duration defaultCommandTimeout;

    private Duration metaCommandTimeout;

    private RedisMode mode;

    private String redisMode;

    private String pubSubHost;

    private int pubSubPort;

    private RedisMode pubSubMode;

    private String redisPubSubMode;

    private List<String> sslProtocols;

    private String username;

    private String password;

    private boolean pingBeforeActivate;

    @Bean(name="redisNamespace")
    public String getRedisNamespace() {
        return ObjectUtils.isEmpty(redisNamespace) ? "prinova" : redisNamespace;
    }

    @Bean
    public GenericObjectPoolConfig<?> poolConfig() {
        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMinIdle(getMinIdle());
        poolConfig.setMaxIdle(getMaxIdle());
        poolConfig.setMaxTotal(getMaxTotal());
        return poolConfig;
    }

    public int getMinIdle() {
        return minIdle;
    }

    @Value("${session.messagepoint.redis.pool-config.min-idle:4}")
    public void setMinIdle(int minIdle) {
        this.minIdle = minIdle;
    }

    public int getMaxIdle() {
        return maxIdle;
    }

    @Value("${session.messagepoint.redis.pool-config.max-idle:16}")
    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMaxTotal() {
        return maxTotal;
    }

    @Value("${session.messagepoint.redis.pool-config.max-total:20}")
    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public String getHost() {
        return host;
    }

    @Value("${session.messagepoint.redis.host:127.0.0.1}")
    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    @Value("${session.messagepoint.redis.port:6379}")
    public void setPort(String portStr) {
        int port = 6379;

        if (!StringUtil.isEmptyOrNull(portStr)) {
            try {
                port = Integer.parseInt(portStr);
            } catch (NumberFormatException e) {
                // do nothing - fallback to default port 6379
            }
        }

        this.port = port;
    }

    public String getReplicaHost() {
        return replicaHost;
    }

    @Value("${session.messagepoint.redis.replica.host:127.0.0.1}")
    public void setReplicaHost(String replicaHost) {
        this.replicaHost = replicaHost;
    }

    public int getReplicaPort() {
        return replicaPort;
    }

    @Value("${session.messagepoint.redis.replica.port:6380}")
    public void setReplicaPort(String replicaPortStr) {
        int replicaPort = 6380;

        if (!StringUtil.isEmptyOrNull(replicaPortStr)) {
            try {
                replicaPort = Integer.parseInt(replicaPortStr);
            } catch (NumberFormatException e) {
                // do nothing - fallback to default port 6380
            }
        }

        this.replicaPort = replicaPort;
    }

    public List<String> getClusterNodes() {
        return clusterNodes;
    }

    @Value("#{'${session.messagepoint.redis.cluster.nodes:}'.split(',')}")
    public void setClusterNodes(List<String> clusterNodes) {
        if (clusterNodes == null || clusterNodes.isEmpty()
                || clusterNodes.get(0) == null || clusterNodes.get(0).isEmpty()) {
            this.clusterNodes = List.of(getHost() + ":" + getPort());
        } else {
            this.clusterNodes = clusterNodes;
        }
    }

    public int getMaxRedirects() {
        return maxRedirects;
    }

    @Value("${session.messagepoint.redis.cluster.max-redirects:-1}")
    public void setMaxRedirects(int maxRedirects) {
        this.maxRedirects = maxRedirects;
    }

    @Value("${session.messagepoint.redis.namespace:}")
    public void setRedisNamespace(String redisNamespace) {
        this.redisNamespace = redisNamespace;
    }

    public boolean isUseSSL() {
        return useSSL;
    }

    @Value("${session.messagepoint.redis.use_ssl:false}")
    public void setUseSSL(boolean useSSL) {
        this.useSSL = useSSL;
    }

    public boolean isStartTLS() {
        return startTLS;
    }

    @Value("${session.messagepoint.redis.start_tls:false}")
    public void setStartTLS(boolean startTLS) {
        this.startTLS = startTLS;
    }

    public Duration getConnectionTimeout() {
        return connectionTimeout;
    }

    @Value("${session.messagepoint.redis.connection_timeout:0}")
    public void setConnectionTimeout(Long connectionTimeout) {
        this.connectionTimeout = (connectionTimeout != null && connectionTimeout > 0L) ?
                Duration.ofMillis(connectionTimeout) :
                RedisURI.DEFAULT_TIMEOUT_DURATION;
    }

    public Duration getDefaultCommandTimeout() {
        return defaultCommandTimeout;
    }

    @Value("${session.messagepoint.redis.default_command_timeout:0}")
    public void setDefaultCommandTimeout(Long defaultCommandTimeout) {
        this.defaultCommandTimeout = (defaultCommandTimeout != null && defaultCommandTimeout > 0L) ?
                Duration.ofMillis(defaultCommandTimeout) :
                RedisURI.DEFAULT_TIMEOUT_DURATION;
    }

    public Duration getMetaCommandTimeout() {
        return metaCommandTimeout;
    }

    @Value("${session.messagepoint.redis.meta_command_timeout:0}")
    public void setMetaCommandTimeout(Long metaCommandTimeout) {
        this.metaCommandTimeout = (metaCommandTimeout != null && !metaCommandTimeout.equals(0L)) ?
                Duration.ofMillis(metaCommandTimeout > 0L ? metaCommandTimeout : 0L) :
                getDefaultCommandTimeout();
    }

    public RedisMode getMode() {
        return mode;
    }

    public String getRedisMode() {
        return redisMode;
    }

    @Value("${session.messagepoint.redis.mode:standalone}")
    public void setRedisMode(String redisMode) {
        this.mode = RedisMode.fromString(redisMode);
        this.redisMode = (
                switch (mode) {
                    case STANDALONE -> MessagepointRedisConfiguration.REDIS_MODE_STANDALONE;
                    case CLUSTER -> MessagepointRedisConfiguration.REDIS_MODE_CLUSTER;
                    case SENTINEL -> MessagepointRedisConfiguration.REDIS_MODE_SENTINEL;
                    case MASTER_REPLICA -> MessagepointRedisConfiguration.REDIS_MODE_MASTER_REPLICA;
                });
    }

    public String getPubSubHost() {
        if (StringUtil.isEmptyOrNull(pubSubHost)) {
            pubSubHost = getHost();
        }

        return pubSubHost;
    }

    @Value("${session.messagepoint.redis.pubsub.host:}")
    public void setPubSubHost(String pubSubHost) {
        this.pubSubHost = (!StringUtil.isEmptyOrNull(pubSubHost) ? pubSubHost : getHost());
    }

    public int getPubSubPort() {
        if (pubSubPort <= 0) {
            pubSubPort = getPort();
        }

        return pubSubPort;
    }

    @Value("${session.messagepoint.redis.pubsub.port:0}")
    public void setPubSubPort(String pubSubPortStr) {
        int pubSubPort = 0;
        if (!StringUtil.isEmptyOrNull(pubSubPortStr)) {
            try {
                pubSubPort = Integer.parseInt(pubSubPortStr);
            } catch (NumberFormatException e) {
                // do nothing - fallback to default Redis port
            }
        }

        this.pubSubPort = (pubSubPort > 0 ? pubSubPort : getPort());
    }

    public RedisMode getPubSubMode() {
        if (pubSubMode == null) {
            pubSubMode = getMode();
        }

        return pubSubMode;
    }

    public String getRedisPubSubMode() {
        if (StringUtil.isEmptyOrNull(redisPubSubMode)) {
            this.redisPubSubMode = getRedisMode();
            this.pubSubMode = RedisMode.fromString(redisPubSubMode);

            this.redisPubSubMode = (
                    switch (this.pubSubMode) {
                        case STANDALONE, MASTER_REPLICA -> MessagepointRedisConfiguration.REDIS_MODE_STANDALONE;
                        case CLUSTER -> MessagepointRedisConfiguration.REDIS_MODE_CLUSTER;
                        case SENTINEL -> MessagepointRedisConfiguration.REDIS_MODE_SENTINEL;
                    });
            this.pubSubMode = RedisMode.fromString(this.redisPubSubMode);
        }


        return redisPubSubMode;
    }

    @Value("${session.messagepoint.redis.pubsub.mode:}")
    public void setRedisPubSubMode(String redisPubSubMode) {
        if (StringUtil.isEmptyOrNull(redisPubSubMode)) {
            redisPubSubMode = getRedisMode();
        }

        this.redisPubSubMode = redisPubSubMode;
        this.pubSubMode = RedisMode.fromString(redisPubSubMode);
        // converts Master-Replica into Standalone
        this.redisPubSubMode = (
                switch (this.pubSubMode) {
                    case STANDALONE, MASTER_REPLICA -> MessagepointRedisConfiguration.REDIS_MODE_STANDALONE;
                    case CLUSTER -> MessagepointRedisConfiguration.REDIS_MODE_CLUSTER;
                    case SENTINEL -> MessagepointRedisConfiguration.REDIS_MODE_SENTINEL;
                });
        this.pubSubMode = RedisMode.fromString(this.redisPubSubMode);
    }

    public List<String> getSslProtocols() {
        return sslProtocols;
    }

    @Value("#{'${session.messagepoint.redis.ssl.protocols:}'.split(',')}")
    public void setSslProtocols(List<String> sslProtocols) {
        this.sslProtocols = sslProtocols;
    }

    public String getUsername() {
        return username;
    }

    @Value("${session.messagepoint.redis.username:}")
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    @Value("${session.messagepoint.redis.password:}")
    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isPingBeforeActivate() {
        return pingBeforeActivate;
    }

    @Value("${session.messagepoint.redis.ping-before-activate:true}")
    public void setPingBeforeActivate(boolean pingBeforeActivate) {
        this.pingBeforeActivate = pingBeforeActivate;
    }

    public SslOptions getSslOptions() {
        if (isUseSSL()) {
            SslOptions.Builder builder = SslOptions.builder();
            if (!getSslProtocols().isEmpty()) {
                builder.protocols(getSslProtocols().toArray(new String[0]));
            }
            return builder.build();
        }

        return null;
    }

    public TimeoutOptions getTimeoutOptions() {
        return TimeoutOptions.builder()
                .timeoutSource(new CommandTimeoutSource(
                        getDefaultCommandTimeout(),
                        getMetaCommandTimeout()))
                .build();
    }

    @Bean(destroyMethod = "shutdown")
    public ClientResources getClientResources() {
        boolean isJndiDnsAvailable = false;
        try {
            Hashtable<String, String> env = new Hashtable<>();
            env.put(InitialContext.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.dns.DnsContextFactory");
            NamingManager.getInitialContext(env);
            isJndiDnsAvailable = true;
        } catch (NamingException e) {
            logger.warn("Unable to load module com.sun.jndi.dns", e);
        }

        DefaultClientResources.Builder clientResourcesBuilder = DefaultClientResources.builder()
                .reconnectDelay(Delay.fullJitter(
                        Duration.ofMillis(100),
                        Duration.ofSeconds(10),
                        100, TimeUnit.MILLISECONDS));

        if (isJndiDnsAvailable) {
            clientResourcesBuilder.dnsResolver(new DirContextDnsResolver());
        }

        return clientResourcesBuilder.build();
    }

    public SocketOptions getSocketOptions() {
        return SocketOptions.builder()
                .keepAlive(true)
                .connectTimeout(getConnectionTimeout())
                .build();
    }

    @Bean
    public RedisURI getRedisURI() {
        RedisURI.Builder builder = RedisURI.builder();
        builder.withHost(getHost());
        builder.withPort(getPort());
        builder.withSsl(isUseSSL());
        builder.withStartTls(isStartTLS());
        builder.withTimeout(getConnectionTimeout());
        if (getUsername() != null && !getUsername().isEmpty() && getPassword() != null && !getPassword().isEmpty())
            builder.withAuthentication(getUsername(), getPassword());
        else if (getPassword() != null && !getPassword().isEmpty())
            builder.withPassword(getPassword().toCharArray());
        return builder.build();
    }

    public RedisURI getRedisReplicaURI() {
        RedisURI.Builder builder = RedisURI.builder();
        builder.withHost(getReplicaHost());
        builder.withPort(getReplicaPort());
        builder.withSsl(isUseSSL());
        builder.withStartTls(isStartTLS());
        builder.withTimeout(getConnectionTimeout());
        if (getUsername() != null && !getUsername().isEmpty() && getPassword() != null && !getPassword().isEmpty())
            builder.withAuthentication(getUsername(), getPassword());
        else if (getPassword() != null && !getPassword().isEmpty())
            builder.withPassword(getPassword().toCharArray());
        return builder.build();
    }

    private String readPreference;
    private boolean readPreferenceFallbackEnabled;
    private boolean readPreferenceAutoDetect;

    @Value("${session.messagepoint.redis.read-preference:REPLICA_PREFERRED}")
    public void setReadPreference(String readPreference) {
        this.readPreference = readPreference;
    }

    @Value("${session.messagepoint.redis.read-preference.fallback-enabled:true}")
    public void setReadPreferenceFallbackEnabled(boolean fallbackEnabled) {
        this.readPreferenceFallbackEnabled = fallbackEnabled;
    }

    @Value("${session.messagepoint.redis.read-preference.auto-detect:true}")
    public void setReadPreferenceAutoDetect(boolean autoDetect) {
        this.readPreferenceAutoDetect = autoDetect;
    }

    public ReadFrom getOptimalReadFrom() {
        return getOptimalReadFrom(getMode());
    }

    public ReadFrom getOptimalReadFrom(RedisMode mode) {
        if (readPreferenceAutoDetect) {
            return switch (mode) {
                case STANDALONE -> ReadFrom.MASTER; // No replicas in standalone
                case MASTER_REPLICA, CLUSTER, SENTINEL -> parseReadFrom(readPreference);
            };
        }
        return parseReadFrom(readPreference);
    }

    private ReadFrom parseReadFrom(String preference) {
        return switch (preference.toUpperCase()) {
            case "MASTER" -> ReadFrom.MASTER;
            case "REPLICA" -> ReadFrom.REPLICA;
            case "REPLICA_PREFERRED" -> ReadFrom.REPLICA_PREFERRED;
            case "MASTER_PREFERRED" -> ReadFrom.MASTER_PREFERRED;
            case "NEAREST", "LOWEST_LATENCY" -> ReadFrom.LOWEST_LATENCY;
            case "ANY" -> ReadFrom.ANY;
            default -> ReadFrom.REPLICA_PREFERRED;
        };
    }

}