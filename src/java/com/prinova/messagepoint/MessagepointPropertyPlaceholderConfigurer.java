package com.prinova.messagepoint;

import com.prinova.messagepoint.util.PropertyUtils;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MessagepointPropertyPlaceholderConfigurer extends PropertyPlaceholderConfigurer {

    @Override
    public void setLocations(Resource... locations) {

        List<Resource> resourceList = new ArrayList<>();
        HashMap<String, String> runtimePropertyFiles = PropertyUtils.getRuntimePropertyFileLocations();

        for (Resource resource : locations) {
            if (runtimePropertyFiles.containsKey(resource.getFilename())) {
                resourceList.add(new FileSystemResource(runtimePropertyFiles.get(resource.getFilename())));
            } else {
                resourceList.add(resource);
            }
        }

        super.setLocations(resourceList.toArray(new Resource[0]));
    }
}
