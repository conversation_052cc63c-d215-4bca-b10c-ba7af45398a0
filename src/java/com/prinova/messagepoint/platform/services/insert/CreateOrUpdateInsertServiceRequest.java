package com.prinova.messagepoint.platform.services.insert;

import java.util.List;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;


public class CreateOrUpdateInsertServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -906383912687739881L;
	private int action;
	private long insertId=0;
	private String name;
	private String description;
	private int deliveryTypeId;
	private String stockId;
	private String weight;
	private String uploadedFrontContentFilePath;
	private String uploadedBackContentFilePath;	
	
	private List<Document> documents;
	private User requestor;
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public String getStockId() {
		return stockId;
	}
	public void setStockId(String stockId) {
		this.stockId = stockId;
	}
	public String getWeight() {
		return weight;
	}
	public void setWeight(String weight) {
		this.weight = weight;
	}
	public long getInsertId() {
		return insertId;
	}
	public void setInsertId(long insertId) {
		this.insertId = insertId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}
	public String getUploadedFrontContentFilePath() {
		return uploadedFrontContentFilePath;
	}
	public void setUploadedFrontContentFilePath(String uploadedFrontContentFilePath) {
		this.uploadedFrontContentFilePath = uploadedFrontContentFilePath;
	}
	public String getUploadedBackContentFilePath() {
		return uploadedBackContentFilePath;
	}
	public void setUploadedBackContentFilePath(String uploadedBackContentFilePath) {
		this.uploadedBackContentFilePath = uploadedBackContentFilePath;
	}
	public int getDeliveryTypeId() {
		return deliveryTypeId;
	}
	public void setDeliveryTypeId(int deliveryTypeId) {
		this.deliveryTypeId = deliveryTypeId;
	}
	public List<Document> getDocuments() {
		return documents;
	}
	public void setDocuments(List<Document> documents) {
		this.documents = documents;
	}

}
