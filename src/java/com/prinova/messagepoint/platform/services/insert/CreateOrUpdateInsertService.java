package com.prinova.messagepoint.platform.services.insert;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.List;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.core.io.FileSystemResourceLoader;
import org.springframework.core.io.Resource;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.WeightUtil;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.FileUtil;

public class CreateOrUpdateInsertService extends AbstractService {
	public static final String SERVICE_NAME = "insert.CreateOrUpdateInsertService";
	private static final Log log = LogUtil.getLog(CreateOrUpdateInsertService.class);
	private static final int ACTION_CREATE = 1;
	private static final int ACTION_UPDATE_OVERVIEW = 2;
	private static final int ACTION_UPLOAD_CONTENT_IMAGE = 3;
	private static final int ACTION_NO_CHANGES = -9;
	public static final BigInteger ZERO = new BigInteger("0");

	public void execute(ServiceExecutionContext context) {
		CreateOrUpdateInsertServiceRequest request = (CreateOrUpdateInsertServiceRequest) context.getRequest();
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			Insert i;
			if (request.getInsertId() == 0) {
				i = new Insert();
				i.setCreated(DateUtil.now());
				i.setCreatedBy(request.getRequestor().getId());
			} else {
				i = Insert.findById(request.getInsertId());
			}
			switch (request.getAction()) {
			case 1:
				mapFromRequest(i, request);
				versionManage(i, request);
				i.save();
				break;
			case 2:
				mapFromRequest(i, request);
				i.save();
				break;
			case 3:
				boolean savePending = uploadContentImages(i, request);
				if (savePending) {
					i.save();
				}
				break;
			default:
				break;
			}
			
			if (i != null) {
				context.getResponse().setResultValueBean(i);
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateOrUpdateInsertService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e);
		}

	}

	private void mapFromRequest(Insert i, CreateOrUpdateInsertServiceRequest request) {
		i.setName(request.getName());
		i.setDescription(request.getDescription());
		i.setDeliveryTypeId(request.getDeliveryTypeId());
		i.setStockId(request.getStockId());
		try {
			i.setWeight(WeightUtil.hydrateWeight(request.getWeight()));
		} catch (ParseException e) {
			// this exception should already been handled in the validate() method, so should not end up here
		}
		if (i.getDocuments() != null) {
			i.getDocuments().clear();
		}
		if (request.getDocuments() != null) {
			for (Document d : request.getDocuments()) {
				i.getDocuments().add(d);
			}
		}
		i.setUpdated(DateUtil.now());
		i.setUpdatedBy(request.getRequestor().getId());
	}

	private boolean uploadContentImages(Insert i, CreateOrUpdateInsertServiceRequest request) throws IOException {
		String fromPathFront = request.getUploadedFrontContentFilePath();
		String fromPathBack = request.getUploadedBackContentFilePath();
		boolean needsSave = false;
		if (fromPathFront != null && !fromPathFront.trim().isEmpty()) {
			String newFrontFilePath = uploadImage(i, fromPathFront);
			i.setFrontContentPath(newFrontFilePath);
			needsSave = true;
		}
		if (fromPathBack != null && !fromPathBack.trim().isEmpty()) {
			String newBackFilePath = uploadImage(i, fromPathBack);
			i.setBackContentPath(newBackFilePath);
			needsSave = true;
		}
		return needsSave;
	}

	private String uploadImage(Insert owner, String sourceFilePath) throws IOException {
		Resource fromFile = new FileResourceLoader().getResourceByPath(sourceFilePath);
		String fileName = fromFile.getFilename();
		String destFilePath = owner.getLocalPath(fileName);
		File srcFile = new File(sourceFilePath.trim());
		File destFile = new File(destFilePath.trim());

		if (!srcFile.exists()) {
			String errStr = " File does not exist :"+ srcFile;
			log.error(errStr);
			log.error("source file name in request:" + sourceFilePath);
			throw new IOException(errStr);
		}

		if (!destFile.getParentFile().exists()) {
			destFile.getParentFile().mkdirs();
		}
		FileUtil.copy(srcFile, destFile);
		return destFilePath;
	}

	private void versionManage(Insert i, CreateOrUpdateInsertServiceRequest request) {
		i.setLockedFor(request.getRequestor().getId());
		i.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_INACTIVE));
		i.setStartDate(DateUtil.now());
	}

	/**
	 * Validation
	 */

	public void validate(ServiceExecutionContext context) {
		CreateOrUpdateInsertServiceRequest request = (CreateOrUpdateInsertServiceRequest) context.getRequest();

		checkName(context, request);
		checkWeight(context, request);
//		checkUploadContentFiles(context, request);
		checkUpdator(context, request);
	}

	private void checkName(ServiceExecutionContext context, CreateOrUpdateInsertServiceRequest request) {
		if (request.getAction() == ACTION_CREATE || request.getAction() == ACTION_UPDATE_OVERVIEW) {
			if (request.getName() == null || request.getName().trim().isEmpty()) {
				this.getResponse(context).addErrorMessage("Insert id = " + request.getInsertId(),
						ApplicationErrorMessages.INSERT_NAME_IS_REQUIRED,
						SERVICE_NAME,
						context.getLocale());
			}
		}
	}

	private void checkWeight(ServiceExecutionContext context, CreateOrUpdateInsertServiceRequest request) {
		try {
			if (request.getAction() == ACTION_CREATE || request.getAction() == ACTION_UPDATE_OVERVIEW) {
				int weight = WeightUtil.hydrateWeight(request.getWeight());
				if (weight == 0) {
					this.getResponse(context).addErrorMessage("Insert id = " + request.getInsertId(),
							ApplicationErrorMessages.INSERT_WEIGHT_IS_REQUIRED,
							SERVICE_NAME,
							context.getLocale());
				}
			}
		} catch (ParseException e) {
			log.error("Input error -  " + request.getWeight() + " - cannot be used as weight of an insert.");
			this.getResponse(context).addErrorMessage(request.getWeight(),
					ApplicationErrorMessages.INSERT_CANNOT_PARSE_WEIGHT,
					SERVICE_NAME + e,
					context.getLocale());
		}
	}

	private void checkUpdator(ServiceExecutionContext context, CreateOrUpdateInsertServiceRequest request) {
		try {
			if (request.getAction() == ACTION_UPDATE_OVERVIEW || request.getAction() == ACTION_UPLOAD_CONTENT_IMAGE) {
				Insert i = Insert.findById(request.getInsertId());
				if (request.getRequestor().getId() != i.getLockedFor()) {
					this.getResponse(context).addErrorMessage(request.getName(),
							ApplicationErrorMessages.INSERT_NOT_AUTHORIZED_TO_UPDATE,
							SERVICE_NAME,
							context.getLocale());
				}
			}
		} catch (Exception e) {
			log.error("Input error - cannot upload back page of the insert content " + e.getMessage());
			this.getResponse(context).addErrorMessage(request.getName(),
					ApplicationErrorMessages.INSERT_NOT_AUTHORIZED_TO_UPDATE,
					SERVICE_NAME + e,
					context.getLocale());
		}
	}

	/**
	 * Create service request
	 */

	public static ServiceExecutionContext createContextForNew(String name, String description, 
			int deliveryType, String stockId, String weight, List<Document> documents, User requestor) {

		ServiceExecutionContext context = createContextForUpdate(0,
				name,
				description,
				deliveryType,
				stockId,
				weight,
				documents,
				requestor);
		((CreateOrUpdateInsertServiceRequest) context.getRequest()).setAction(ACTION_CREATE);

		return context;

	}

	public static ServiceExecutionContext createContextForUpdate(long insertId, String name, String description,
			int deliveryTypeId, String stockId, String weight, List<Document> documents, User requestor) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateInsertServiceRequest request = new CreateOrUpdateInsertServiceRequest();
		context.setRequest(request);
		request.setInsertId(insertId);
		request.setName(name);
		request.setDescription(description);
		request.setDeliveryTypeId(deliveryTypeId);
		request.setStockId(stockId);
		request.setWeight(weight);
		request.setDocuments(documents);
		request.setRequestor(requestor);
		request.setAction(ACTION_UPDATE_OVERVIEW);
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	public static ServiceExecutionContext createContextForUploadContentImage(long insertId,
			String uploadedFrontContentFilePath, String uploadedBackContentFilePath, User requestor) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateInsertServiceRequest request = new CreateOrUpdateInsertServiceRequest();
		context.setRequest(request);
		request.setInsertId(insertId);
		request.setUploadedFrontContentFilePath(uploadedFrontContentFilePath);
		request.setUploadedBackContentFilePath(uploadedBackContentFilePath);
		request.setRequestor(requestor);
		request.setAction(ACTION_UPLOAD_CONTENT_IMAGE);
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	static class FileResourceLoader extends FileSystemResourceLoader {
		public Resource getResourceByPath(String path) {
			return super.getResourceByPath(path);
		}
	}

}
