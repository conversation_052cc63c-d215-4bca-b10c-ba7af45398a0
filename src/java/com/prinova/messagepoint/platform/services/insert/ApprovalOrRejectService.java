package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.Approval;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.factory.ApprovalFactory;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.approval.UpdateApprovalService;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class ApprovalOrRejectService extends AbstractService {
	public static final String SERVICE_NAME = "insert.ApproveOrRejectService";
	private static final Log log = LogUtil.getLog(ApprovalOrRejectService.class);
	public static final int MODEL_TYPE_INSERT = ItemType.ITEM_TYPE_INSERT;

	public void execute(ServiceExecutionContext context) {
		ApprovalOrRejectServiceRequest request = (ApprovalOrRejectServiceRequest) context.getRequest();
		try {
			List<StateProviderVersionModel> models = request.getModels();
			if (models == null || models.isEmpty()) {
				return;
			}
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			for (StateProviderVersionModel model : models) {
				Approval approval = ApprovalFactory.createAndAddInstance(model,
						WorkflowState.findById(WorkflowState.STATE_PRODUCTION),
						request.getUserNote(),
						request.getRequestorId());
				ServiceExecutionContext context2 = UpdateApprovalService.createContext(model, approval, request.getApprovalType());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(UpdateApprovalService.SERVICE_NAME, UpdateApprovalService.class);
				service.execute(context2);
				if (!context2.getResponse().isSuccessful()) {
					throw new Exception("UpdateApprovalService Error");
				}
				if(model instanceof Insert){
					Insert insert = (Insert)model;
					if(insert.isInProduction()){
						for(Document doc :insert.getDocuments()){
							if(!doc.isTpContentChanged()){
								doc.setTpContentChanged(true);
								doc.save();
							}
						}
						model.setLockedFor(null);
					}
				}else{
					model.setLockedFor(null);
				}
			}

			// Update the models' task status
			TaskUtil.updateStatus(models, TaskStatus.ID_COMPLETE, User.findById(request.getRequestorId()));
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ApprovalOrRejectService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		ApprovalOrRejectServiceRequest request = (ApprovalOrRejectServiceRequest) context.getRequest();
		try {
			for (StateProviderVersionModel model : request.getModels()) {
				if (model.getStatus().getId() != VersionStatus.ONE_VERSION_WAITING_APPROVAL) {
					this.getResponse(context).addErrorMessage(model.getName(),
							ApplicationErrorMessages.INSERT_IS_NOT_WAITING_FOR_APPROVAL,
							SERVICE_NAME,
							context.getLocale());
				}
				if ( !model.getLockedFor().equals(request.getRequestorId()) ) {
					this.getResponse(context).addErrorMessage(model.getName(),
							ApplicationErrorMessages.INSERT_NOT_AUTHORIZED_TO_UPDATE,
							SERVICE_NAME,
							context.getLocale());
				}
			}
		} catch (FinderException fe) {
			this.getResponse(context).addErrorMessage("",
					ApplicationErrorMessages.INSERT_NOT_FOUND,
					SERVICE_NAME,
					context.getLocale());
		}
	}
		

	public static ServiceExecutionContext createContextForInsert(List<Insert> inserts, 
																 String approvalType,
																 Long requestorId, 
																 String userNote) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ApprovalOrRejectServiceRequest request = new ApprovalOrRejectServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setApprovalType(approvalType);
		request.setModelType(ItemType.ITEM_TYPE_INSERT);
		for (Insert insert : inserts) {
			request.getModelIds().add(insert.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

	public static ServiceExecutionContext createContextForInsertSchedule(List<InsertSchedule> insertSchedules, 
																		 String approvalType,
																		 Long requestorId, 
																		 String userNote) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ApprovalOrRejectServiceRequest request = new ApprovalOrRejectServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setApprovalType(approvalType);
		request.setModelType(ItemType.ITEM_TYPE_INSERT_SCHEDULE);
		for (InsertSchedule insertSchedule : insertSchedules) {
			request.getModelIds().add(insertSchedule.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}