package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.controller.insert.InsertScheduleControllerUtil;
import com.prinova.messagepoint.model.Approval;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ParameterGroupInstance;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleBinAssignment;
import com.prinova.messagepoint.model.insert.InsertScheduleCollection;
import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.parameter.CloneParameterGroupInstanceCollectionService;
import com.prinova.messagepoint.platform.services.parameter.CreateParameterGroupInstanceCollectionService;
import com.prinova.messagepoint.platform.services.parameter.UpdateParameterGroupInstanceCollectionService;
import com.prinova.messagepoint.util.DateUtil;

public class CreateOrUpdateInsertScheduleService extends AbstractService {
	
	public static final String SERVICE_NAME = "insert.CreateOrUpdateInsertScheduleService";
	
	private static final Log log = LogUtil.getLog(CreateOrUpdateInsertScheduleService.class);
	
	private static final int ACTION_CREATE = 1;
	private static final int ACTION_UPDATE_OVERVIEW = 2;
	private static final int ACTION_UPDATE_BIN_ASSIGNMENTS = 3;
	private static final int ACTION_ADD_DATA_VALUES = 4;
	private static final int ACTION_REMOVE_DATA_VALUES = 5;
	private static final int ACTION_UPDATE_RATE_SCHEDULES = 6;
	private static final int ACTION_NEW_RANGE = 7;
	private static final int ACTION_UPDATE_TIMING = 8;
	private static final int ACTION_RELEASE_FOR_USE = 9;
	private static final int ACTION_CLONE = 10;
	
	public void execute(ServiceExecutionContext context) {
		CreateOrUpdateInsertScheduleServiceRequest request = (CreateOrUpdateInsertScheduleServiceRequest) context.getRequest();
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			InsertSchedule insertSchedule = null;
			InsertScheduleCollection insertScheduleCollection = null;
			
			Long requestorId = request.getRequestorId();
			
			switch (request.getAction()) {
				case ACTION_CREATE:
					insertScheduleCollection = createInsertScheduleCollection(requestorId, request.getDocument());
					insertSchedule = createInsertSchedule(requestorId, request.getNumberOfBins());
					loadPropertiesFromRequest(insertSchedule, request);
					loadDefaultRateSchedule(insertSchedule, request.getDocument());
					insertSchedule.setLockedFor(requestorId);
					insertSchedule.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_SETUP));
					insertSchedule.setScheduleCollection(insertScheduleCollection);
					insertScheduleCollection.getInsertSchedules().add(insertSchedule);
					insertScheduleCollection.save();
					
					break;
				case ACTION_RELEASE_FOR_USE:
					if (request.getInsertScheduleId() > 0) {
						List<InsertSchedule> insertSchedules = new ArrayList<>();
						insertSchedules.add(InsertSchedule.findById(request.getInsertScheduleId()));
					} 
					List<InsertSchedule> insertSchedules = request.getInsertSchedules();
					for (InsertSchedule schedule : insertSchedules) {
						schedule.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_INACTIVE));
						schedule.setLockedFor(null);
						schedule.save();
					}
					break;
				case ACTION_UPDATE_OVERVIEW:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertSchedule.setLockedFor(requestorId);
					loadPropertiesFromRequest(insertSchedule, request);
					insertSchedule.save();
					break;
				case ACTION_UPDATE_TIMING:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertSchedule.setLockedFor(requestorId);
					for (InsertScheduleBinAssignment binAssignment : insertSchedule.getBinAssignmentsSortedOnInsertName()) {
						if (request.getBinAssignments().contains(binAssignment)) {
							Date inputStartDate = request.getStartDates().get(request.getBinAssignments().indexOf(binAssignment));
							Date inputEndDate = request.getEndDates().get(request.getBinAssignments().indexOf(binAssignment));
							binAssignment.setStartDate(inputStartDate);
							binAssignment.setEndDate(inputEndDate);
						}
					}
					insertSchedule.save();
					break;
				case ACTION_ADD_DATA_VALUES:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertSchedule.setLockedFor(requestorId);
					List<String> itemValues = new ArrayList<>(Arrays.asList(request.getDataValues()));
					if (insertSchedule.getParameterGroupInstanceCollection() == null) {					
						ServiceExecutionContext collectionServiceContext = CreateParameterGroupInstanceCollectionService.createContext(insertSchedule.getName() + " Data Values", itemValues, false, insertSchedule.getParameterGroup().getId(), false, null);
						Service createCollectionService = MessagepointServiceFactory.getInstance().lookupService(CreateParameterGroupInstanceCollectionService.SERVICE_NAME, CreateParameterGroupInstanceCollectionService.class);
						createCollectionService.execute(collectionServiceContext);
						if (collectionServiceContext.getResponse().isSuccessful()) {
							long pgCollectionId = (Long)collectionServiceContext.getResponse().getResultValueBean();
							ParameterGroupInstanceCollection pgCollection = ParameterGroupInstanceCollection.findById(pgCollectionId);
							insertSchedule.setParameterGroupInstanceCollection(pgCollection);
							insertSchedule.save();
						} else {
							throw new Exception(collectionServiceContext.getResponse().getMessagesToString());
						}
					} else {
						if (!handleCloneSelectors(insertSchedule, itemValues, ACTION_ADD_DATA_VALUES)) {
							ServiceExecutionContext updateCollectionContext = UpdateParameterGroupInstanceCollectionService.createContextToAddItem(insertSchedule.getParameterGroup().getId(), insertSchedule.getParameterGroupInstanceCollection().getId(), request.getDataValues(), false, false);
				
							Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
																									 UpdateParameterGroupInstanceCollectionService.class);
							service.execute(updateCollectionContext);
				
							if (!updateCollectionContext.getResponse().isSuccessful()) {
								log.error(" unexpected exception when invoking UpdateParameterGroupInstanceCollectionService execute method");
								throw new Exception(updateCollectionContext.getResponse().getMessagesToString());
							}
						}
					}
					break;
				case ACTION_REMOVE_DATA_VALUES:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertSchedule.setLockedFor(requestorId);
					List<String> removableItemValues = new ArrayList<>(Arrays.asList(request.getDataValues()));
					if (!handleCloneSelectors(insertSchedule, removableItemValues, ACTION_REMOVE_DATA_VALUES)) {
						ServiceExecutionContext updateCollectionContext = UpdateParameterGroupInstanceCollectionService.createContextToRemoveItemsByValue(insertSchedule.getParameterGroup().getId(), insertSchedule.getParameterGroupInstanceCollection().getId(), removableItemValues, false, false, null);
						
						Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
																								 UpdateParameterGroupInstanceCollectionService.class);
						service.execute(updateCollectionContext);
			
						if (!updateCollectionContext.getResponse().isSuccessful()) {
							log.error(" unexpected exception when invoking UpdateParameterGroupInstanceCollectionService execute method");
							throw new Exception(updateCollectionContext.getResponse().getMessagesToString());
						}
						
						if (insertSchedule.getParameterGroupInstanceCollection().getParameterGroupInstances().isEmpty()) {
							long collectionId = insertSchedule.getParameterGroupInstanceCollection().getId();
							insertSchedule.setParameterGroupInstanceCollection(null);
							insertSchedule.save();
							ParameterGroupInstanceCollection.delete(collectionId);
						}
					}					
					break;
				case ACTION_UPDATE_BIN_ASSIGNMENTS:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertSchedule.setLockedFor(requestorId);
					if (request.isDefaultSchedule()) {
						if (insertSchedule.getScheduleCollection() != null && 
							insertSchedule.getScheduleCollection().getDocument() != null && 
							insertSchedule.getScheduleCollection().getDocument().getDefaultInsertSchedule() != null) {
							InsertSchedule defaultInsertSchedule = insertSchedule.getScheduleCollection().getDocument().getDefaultInsertSchedule();
							defaultInsertSchedule.setDefaultSchedule(false);
							defaultInsertSchedule.save();
						}
					}
					insertSchedule.setDefaultSchedule(request.isDefaultSchedule());
		    		int numberOfBins = insertSchedule.getNumberOfBins();
		    		if (numberOfBins > 0 && insertSchedule.getBinAssignments() != null) {
		    			List<InsertScheduleBinAssignment> binAssignments = insertSchedule.getBinAssignmentsSortedOnBinNo();
		    			
		    			Map<Insert, Date> startDates = new HashMap<>();
		    			Map<Insert, Date> endDates = new HashMap<>();
		    			extractTimings(binAssignments, startDates, endDates);
		    			
		    			String binAvailabilitiesString = request.getBinAvailabilities();
		    			String[] binAvailabilities = binAvailabilitiesString.split(",");
		    			for (int i = 0; i < numberOfBins; i++) {
		    				InsertScheduleBinAssignment binAssignment = binAssignments.get(i);
		    				long binAvailability = Long.parseLong(binAvailabilities[i].trim());
		    				if (binAvailability > 0) {
		    					Insert insert = Insert.findById(binAvailability);
		    					binAssignment.setInsert(insert);
		    					if (insert.getDeliveryType().getId() == Insert.INSERT_DELIVERY_TYPE_OPTIONAL) {
		    						int priorityIndex = request.getOptionalInserts().indexOf(insert);
		    						binAssignment.setPriority(priorityIndex + 1);
		    					}
	    						binAssignment.setStartDate(startDates.get(insert));
	    						binAssignment.setEndDate(endDates.get(insert));
		    				} else {
		    					if (binAssignment.isAvailable()) {
		    						binAssignment.setInsert(null);
		    						binAssignment.setPriority(0);
		    						binAssignment.setStartDate(null);
		    						binAssignment.setEndDate(null);
		    					}
		    				}
						}
		    			insertSchedule.getBinAssignments().clear();
		    			insertSchedule.getBinAssignments().addAll(binAssignments);
		    		}
					break;
				case ACTION_UPDATE_RATE_SCHEDULES:
					if (request.getInsertRateSchedules() != null) {
						insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
						insertSchedule.setLockedFor(requestorId);
						insertSchedule.getInsertRateSchedules().clear();
						insertSchedule.getInsertRateSchedules().putAll(request.getInsertRateSchedules());
						insertSchedule.save();
					}
					break;
				case ACTION_NEW_RANGE:
					insertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					insertScheduleCollection = insertSchedule.getScheduleCollection();
					InsertSchedule newInsertSchedule = createInsertSchedule(requestorId, request.getNumberOfBins());

					newInsertSchedule.setLockedFor(requestorId);
					newInsertSchedule.setName(request.getName());
					newInsertSchedule.setScheduleId(request.getScheduleId());
					newInsertSchedule.setKeywords(request.getKeywords());
					newInsertSchedule.setNumberOfBins(request.getNumberOfBins());
					if (request.getBinAvailabilities() != null && !request.getBinAvailabilities().trim().isEmpty()) {
						dehydrateBinAvailabilities(newInsertSchedule, request.getBinAvailabilities());
					}
					newInsertSchedule.setStartDate(request.getStartDate());
					newInsertSchedule.setEndDate(request.getEndDate());
					newInsertSchedule.setDescription(request.getDescription());
					newInsertSchedule.setUpdated(DateUtil.now());
					newInsertSchedule.setUpdatedBy(requestorId);

					newInsertSchedule.setLockedFor(requestorId);
					newInsertSchedule.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_INACTIVE));

					InsertScheduleControllerUtil.processTimingForNewInsertSchedule(insertSchedule, newInsertSchedule);
//					InsertScheduleControllerUtil.cloneBinAssignments(insertSchedule, newInsertSchedule);
					newInsertSchedule.setParameterGroupInstanceCollection(insertSchedule.getParameterGroupInstanceCollection());
					if (insertSchedule.getInsertRateSchedules() != null && !insertSchedule.getInsertRateSchedules().isEmpty()) {
						newInsertSchedule.getInsertRateSchedules().putAll(insertSchedule.getInsertRateSchedules());
					}
					newInsertSchedule.save();
					newInsertSchedule.setScheduleCollection(insertScheduleCollection);
					insertScheduleCollection.getInsertSchedules().add(newInsertSchedule);
					insertSchedule.setNextSchedule(newInsertSchedule);
					newInsertSchedule.setPreviousSchedule(insertSchedule);
					insertScheduleCollection.save();
					insertSchedule = newInsertSchedule;
					
					break;
				case ACTION_CLONE:
					
					InsertSchedule cloneFromInsertSchedule = InsertSchedule.findById(request.getInsertScheduleId());
					
					insertSchedule = (InsertSchedule)cloneFromInsertSchedule.clone();
					insertSchedule.setName( cloneFromInsertSchedule.getName() + " Clone" );
					insertSchedule.setLockedFor(requestorId);
					insertSchedule.setLastEditor(requestorId);
					insertSchedule.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_INACTIVE));
					insertSchedule.getApprovals().clear();
					insertSchedule.setApprovals(new HashSet<>());
					insertSchedule.save();
					
				default:
					break;
			}
			
			if (insertSchedule != null) {
				context.getResponse().setResultValueBean(insertSchedule);
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateOrUpdateInsertService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e);
		}
	}

	private void loadDefaultRateSchedule(InsertSchedule insertSchedule, Document insertScheduleDocument) {
		if (insertScheduleDocument != null && insertScheduleDocument.getDefaultRateScheduleCollection() != null) {
			RateSchedule defaultRateSchedule = insertScheduleDocument.getDefaultRateScheduleCollection().getRateScheduleForDate(insertSchedule.getStartDate());
			if (defaultRateSchedule != null) {
				insertSchedule.getInsertRateSchedules().put(Long.valueOf(RateSchedule.DEFAULT_NUMBER_OF_SHEETS), insertScheduleDocument.getDefaultRateScheduleCollection());
			}
		}
	}
	
	private void extractTimings(List<InsertScheduleBinAssignment> binAssignments, Map<Insert, Date> startDates, Map<Insert, Date> endDates) {
		for (InsertScheduleBinAssignment assignment : binAssignments) {
			if (assignment.getInsert() != null) {
				if (startDates.get(assignment.getStartDate()) == null) {
					startDates.put(assignment.getInsert(), assignment.getStartDate());
					endDates.put(assignment.getInsert(), assignment.getEndDate());
				}
			}
		}
	}
	
	private boolean handleCloneSelectors(InsertSchedule insertSchedule, List<String> itemValues, int action) throws Exception {
		if (insertSchedule.getParameterGroupInstanceCollection() != null && 
				insertSchedule.getPreviousSchedule() != null && insertSchedule.getPreviousSchedule().getParameterGroupInstanceCollection() != null &&
				insertSchedule.getPreviousSchedule().getParameterGroupInstanceCollection().getId() == insertSchedule.getParameterGroupInstanceCollection().getId()) {
			// Cloning the Collection
			ParameterGroupInstanceCollection clonedCollection = cloneCollection(insertSchedule.getParameterGroupInstanceCollection());
			insertSchedule.setParameterGroupInstanceCollection(clonedCollection);
			insertSchedule.save();
			InsertSchedule nextSchedule = insertSchedule.getNextSchedule();
			while (nextSchedule != null) {
				nextSchedule.setParameterGroupInstanceCollection(clonedCollection);
				nextSchedule.save();
				nextSchedule = nextSchedule.getNextSchedule();
			}
			
			//Updating the cloned Collection
			if (action == ACTION_ADD_DATA_VALUES) {
				ParameterGroupInstance.removeEmptyValuesFromEnd(itemValues);
				List<List<String>> flatValuesToAdd = ParameterGroupInstance.flattenValue(itemValues);
				for (List<String> flatValue : flatValuesToAdd) {
					ParameterGroupInstance newPGInstance = ParameterGroupInstance.createParameterGroupInstance(insertSchedule.getParameterGroup().getId(), flatValue);
					clonedCollection.getParameterGroupInstances().add(newPGInstance);					
				}

			} else if (action == ACTION_REMOVE_DATA_VALUES) {
				ParameterGroupInstance.removeEmptyValuesFromEnd(itemValues);
				List<List<String>> flatValuesToRemove = ParameterGroupInstance.flattenValue(itemValues);

				Set<ParameterGroupInstance> removablePGInstances = new HashSet<>();
				for (List<String> flatValue : flatValuesToRemove) {
					for (ParameterGroupInstance pgInstance : clonedCollection.getParameterGroupInstances()) {
						if (pgInstance.valuesEqualTo(flatValue)) {
							removablePGInstances.add(pgInstance);
							break;
						}
					}
				}
				clonedCollection.getParameterGroupInstances().removeAll(removablePGInstances);
			}
			clonedCollection.save();
			return true;
		} else {
			return false;
		}
	}
	
	private ParameterGroupInstanceCollection cloneCollection(ParameterGroupInstanceCollection collection) throws Exception {
		ServiceExecutionContext cloneCollectionServiceContext = CloneParameterGroupInstanceCollectionService.createContext(collection.getId());
		Service cloneCollectionService = MessagepointServiceFactory.getInstance().lookupService(CloneParameterGroupInstanceCollectionService.SERVICE_NAME, CloneParameterGroupInstanceCollectionService.class);
		cloneCollectionService.execute(cloneCollectionServiceContext);
		if (cloneCollectionServiceContext.getResponse().isSuccessful()) {
			long pgCollectionId = (Long)cloneCollectionServiceContext.getResponse().getResultValueBean();
			ParameterGroupInstanceCollection pgCollection = ParameterGroupInstanceCollection.findById(pgCollectionId);
			return pgCollection;
		} else {
			throw new Exception(cloneCollectionServiceContext.getResponse().getMessagesToString());
		}
	}
	
	private InsertScheduleCollection createInsertScheduleCollection(Long requestorId, Document document) {
		InsertScheduleCollection insertScheduleCollection = new InsertScheduleCollection();
		insertScheduleCollection.setCreated(DateUtil.now());
		insertScheduleCollection.setCreatedBy(requestorId);
		insertScheduleCollection.setDocument(document);
		return insertScheduleCollection;		
	}
	
	private InsertSchedule createInsertSchedule(Long requestorId, int numberOfBins) {
		InsertSchedule insertSchedule = new InsertSchedule();
		insertSchedule.setCreated(DateUtil.now());
		insertSchedule.setCreatedBy(requestorId);
		for (int i = 0; i < numberOfBins; i++) {
			InsertScheduleBinAssignment binAssignment = new InsertScheduleBinAssignment();
			binAssignment.setBinNo(i+1);
			binAssignment.setInsertSchedule(insertSchedule);
			insertSchedule.getBinAssignments().add(binAssignment);
		}
		return insertSchedule;
	}
	
	private void loadPropertiesFromRequest(InsertSchedule insertSchedule, CreateOrUpdateInsertScheduleServiceRequest request) {
		insertSchedule.setName(request.getName());
		insertSchedule.setScheduleId(request.getScheduleId());
		insertSchedule.setKeywords(request.getKeywords());
		insertSchedule.setNumberOfBins(request.getNumberOfBins());
		dehydrateBinAvailabilities(insertSchedule, request.getBinAvailabilities());
		insertSchedule.setStartDate(request.getStartDate());
		insertSchedule.setEndDate(request.getEndDate());
		insertSchedule.setDescription(request.getDescription());
		insertSchedule.setUpdated(DateUtil.now());
		insertSchedule.setUpdatedBy(request.getRequestorId());
	}

	private void dehydrateBinAvailabilities(InsertSchedule insertShedule, String binAvailabilities) {
		if (binAvailabilities == null) {
			return;
		}
		String[] binAvailabilitiesArray = binAvailabilities.split(InsertScheduleBinAssignment.BIN_ASSIGNMENT_AVAILABILITY_SEPARATOR_STRING);
		List<InsertScheduleBinAssignment> currentBinAssignments = insertShedule.getBinAssignmentsSortedOnBinNo();
		
		if (binAvailabilitiesArray.length > currentBinAssignments.size()) {
			int binNo = currentBinAssignments.size() + 1;
			while (binAvailabilitiesArray.length > currentBinAssignments.size()) {
				InsertScheduleBinAssignment newBinAssignment = new InsertScheduleBinAssignment();
				newBinAssignment.setBinNo(binNo);
				currentBinAssignments.add(newBinAssignment);
				binNo++;
			}
		} else if (binAvailabilitiesArray.length < currentBinAssignments.size()) {
			while (binAvailabilitiesArray.length < currentBinAssignments.size()) {
				currentBinAssignments.get(currentBinAssignments.size() - 1).setInsertSchedule(null);
				insertShedule.getBinAssignments().remove(currentBinAssignments.get(currentBinAssignments.size() - 1));
				currentBinAssignments.remove(currentBinAssignments.size() - 1);
			}
		}
		for (int i = 0; i < currentBinAssignments.size(); i++) {
			currentBinAssignments.get(i).setInsertSchedule(insertShedule);
			if (binAvailabilitiesArray[i] != null && binAvailabilitiesArray[i].trim().equals(InsertScheduleBinAssignment.BIN_ASSIGNMENT_UNAVAILABLE_STRING)) {
				currentBinAssignments.get(i).setAvailable(false);
				currentBinAssignments.get(i).setInsert(null);
			} else {
				currentBinAssignments.get(i).setAvailable(true);
				if (!binAvailabilitiesArray[i].trim().isEmpty())
				{
					long binAvailability = Long.parseLong(binAvailabilitiesArray[i].trim());
					if (binAvailability > 0) {
						Insert insert = Insert.findById(binAvailability);
						currentBinAssignments.get(i).setInsert(insert);
					}
				}
			}
		}
		insertShedule.getBinAssignments().clear();
		insertShedule.getBinAssignments().addAll(currentBinAssignments);
	}	

	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContextForNew(String name, 
															  String scheduleId,
															  String keywords,
															  int numberOfBins,
															  String binAvailabilities,
															  Date startDate,
															  Date endDate,
															  String description,
															  Document document,
															  Long requestorId) {

		ServiceExecutionContext context = createContext(ACTION_CREATE,
														0,
														name, 
														scheduleId,
														keywords,
														numberOfBins,
														binAvailabilities,
														startDate,
														endDate,
														description,
														document,
														null,
														null,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}

	public static ServiceExecutionContext createContextForNewRange(long insertScheduleId,
																   String name, 
																   String scheduleId,
																   String keywords,
																   int numberOfBins,
																   String binAvailabilities,
																   Date startDate,
																   Date endDate,
																   String description,
																   Document document,
																   Long requestorId) {

		ServiceExecutionContext context = createContext(ACTION_NEW_RANGE,
														insertScheduleId,
														name, 
														scheduleId,
														keywords,
														numberOfBins,
														binAvailabilities,
														startDate,
														endDate,
														description,
														document,
														null,
														null,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}

	public static ServiceExecutionContext createContextForUpdateOverview(long insertScheduleId,
																		 String name, 
																		 String scheduleId,
																		 String keywords,
																		 int numberOfBins,
																		 String binAvailabilities,
																		 Date startDate,
																		 Date endDate,
																		 String description,
																		 Long requestorId) {

		ServiceExecutionContext context = createContext(ACTION_UPDATE_OVERVIEW,
														insertScheduleId,
														name, 
														scheduleId,
														keywords,
														numberOfBins,
														binAvailabilities,
														startDate,
														endDate,
														description,
														null,
														null,
														null,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}
		
	public static ServiceExecutionContext createContextForUpdateTiming(long insertScheduleId,
																	   List<InsertScheduleBinAssignment> binAssignments,
																	   List<Date> startDates,
																	   List<Date> endDates,
																	   Long requestorId) {

			ServiceExecutionContext context = createContext(ACTION_UPDATE_TIMING,
															insertScheduleId,
															"", 
															"",
															"",
															-1,
															"",
															null,
															null,
															"",
															null,
															null,
															null,
															null,
															binAssignments,
															startDates,
															endDates,
															false,
															null,
															requestorId);
	return context;
	}

	public static ServiceExecutionContext createContextForUpdateBinAssignments(long insertScheduleId,
			   																   String binAvailabilities,
																			   List<Insert> optionalInserts,
																			   boolean defaultSchedule,
																			   Long requestorId) {
		
		ServiceExecutionContext context = createContext(ACTION_UPDATE_BIN_ASSIGNMENTS,
														insertScheduleId,
														"", 
														"",
														"",
														-1,
														binAvailabilities,
														null,
														null,
														"",
														null,
														optionalInserts,
														null,
														null,
														null,
														null,
														null,
														defaultSchedule,
														null,
														requestorId);
		return context;
	}
	
	public static ServiceExecutionContext createContextForAddDataValues(long insertScheduleId,
																		String[] dataValues,
																		Long requestorId) {
		ServiceExecutionContext context = createContext(ACTION_ADD_DATA_VALUES,
														insertScheduleId,
														"", 
														"",
														"",
														-1,
														"",
														null,
														null,
														"",
														null,
														null,
														dataValues,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}
	
	public static ServiceExecutionContext createContextForRemoveDataValues(long insertScheduleId,
																		   String[] dataValues,
																		   Long requestorId) {
		ServiceExecutionContext context = createContext(ACTION_REMOVE_DATA_VALUES,
														insertScheduleId,
														"", 
														"",
														"",
														-1,
														"",
														null,
														null,
														"",
														null,
														null,
														dataValues,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}

	public static ServiceExecutionContext createContextForReleaseForUse(List<InsertSchedule> insertSchedules,
			  															Long requestorId) {
		ServiceExecutionContext context = createContext(ACTION_RELEASE_FOR_USE,
														-1,
														"", 
														"",
														"",
														-1,
														"",
														null,
														null,
														"",
														null,
														null,
														null,
														null,
														null,
														null,
														null,
														false,
														insertSchedules,
														requestorId);
		return context;
	}

	public static ServiceExecutionContext createContextForUpdateRateSchedules(long insertScheduleId,
																			  Map<Long, RateScheduleCollection> insertRateSchedules,
																			  Long requestorId) {
		ServiceExecutionContext context = createContext(ACTION_UPDATE_RATE_SCHEDULES,
														insertScheduleId,
														"", 
														"",
														"",
														-1,
														"",
														null,
														null,
														"",
														null,
														null,
														null,
														insertRateSchedules,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}
	
	public static ServiceExecutionContext createContextForCloneSchedule(long insertScheduleId,
																			  Long requestorId) {
		ServiceExecutionContext context = createContext(ACTION_CLONE,
														insertScheduleId,
														"", 
														"",
														"",
														-1,
														"",
														null,
														null,
														"",
														null,
														null,
														null,
														null,
														null,
														null,
														null,
														false,
														null,
														requestorId);
		return context;
	}

	public static ServiceExecutionContext createContext(int action,
														long insertScheduleId, 
														String name, 
														String scheduleId,
														String keywords,
														int numberOfBins,
														String binAvailabilities,
														Date startDate,
														Date endDate,
														String description,
														Document document,
														List<Insert> optionalInserts,
														String[] dataValues,
														Map<Long, RateScheduleCollection> insertRateSchedules,
														List<InsertScheduleBinAssignment> binAssignments,
														List<Date> startDates,
														List<Date> endDates,
														boolean defaultSchedule,
														List<InsertSchedule> insertSchedules,
														Long requestorId) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateInsertScheduleServiceRequest request = new CreateOrUpdateInsertScheduleServiceRequest();
		context.setRequest(request);
		
		request.setAction(action);
		request.setInsertScheduleId(insertScheduleId);
		request.setName(name);
		request.setScheduleId(scheduleId);
		request.setKeywords(keywords);
		request.setNumberOfBins(numberOfBins);
		request.setBinAvailabilities(binAvailabilities);
		request.setStartDate(startDate);
		request.setEndDate(endDate);
		request.setDescription(description);
		request.setDocument(document);
		request.setOptionalInserts(optionalInserts);
		request.setDataValues(dataValues);
		request.setInsertRateSchedules(insertRateSchedules);
		request.setBinAssignments(binAssignments);
		request.setStartDates(startDates);
		request.setEndDates(endDates);
		request.setDefaultSchedule(defaultSchedule);
		request.setRequestorId(requestorId);
		request.setInsertSchedules(insertSchedules);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}