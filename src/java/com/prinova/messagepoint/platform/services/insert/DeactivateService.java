package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class DeactivateService extends AbstractService {
	public static final String SERVICE_NAME = "insert.DeactivateService";
	private static final Log log = LogUtil.getLog(DeactivateService.class);

	public void execute(ServiceExecutionContext context) {
		DeactivateServiceRequest request = (DeactivateServiceRequest) context.getRequest();
		try {
			List<StateProviderVersionModel> models = request.getModels();
			if (models == null || models.isEmpty()) {
				return;
			}
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			for (StateProviderVersionModel model : models) {
				model.setLockedFor(request.getAssignedToUserId());
				model.setStatus(VersionStatus.findById(VersionStatus.ONE_VERSION_INACTIVE));
				model.save();

			}

			// Update the models' task status
			TaskUtil.updateStatus(models, TaskStatus.ID_TO_DO, User.findById(request.getRequestorId()));
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ApprovalOrRejectService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		DeactivateServiceRequest request = (DeactivateServiceRequest) context.getRequest();
		try {
			for (StateProviderVersionModel model : request.getModels()) {
				if (model.getStatus().getId() != VersionStatus.ONE_VERSION_ACTIVE) {
					this.getResponse(context).addErrorMessage(model.getName(),
							ApplicationErrorMessages.INSERT_IS_NOT_IN_ACTIVE_STATE_TOBE_DEACTIVATED,
							SERVICE_NAME,
							context.getLocale());
				}
			}
		} catch (FinderException fe) {
			this.getResponse(context).addErrorMessage("",
					ApplicationErrorMessages.INSERT_NOT_FOUND,
					SERVICE_NAME,
					context.getLocale());
		}
	}
	public static ServiceExecutionContext createContextForInsert(List<Insert> inserts, 
																 Long assignedToUserId,
																 Long requestorId, 
																 String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		DeactivateServiceRequest request = new DeactivateServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setAssignedToUserId(assignedToUserId);
		request.setModelType(ItemType.ITEM_TYPE_INSERT);
		for (Insert insert : inserts) {
			request.getModelIds().add(insert.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}	
	
	public static ServiceExecutionContext createContextForInsertSchedule(List<InsertSchedule> insertSchedules, 
																		 Long assignedToUserId,
																		 Long requestorId, 
																		 String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		DeactivateServiceRequest request = new DeactivateServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setAssignedToUserId(assignedToUserId);
		request.setModelType(ItemType.ITEM_TYPE_INSERT_SCHEDULE);
		for (InsertSchedule insertSchedule : insertSchedules) {
			request.getModelIds().add(insertSchedule.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}	
}