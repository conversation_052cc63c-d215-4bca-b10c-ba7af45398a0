package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleCollection;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class DiscardService extends AbstractService {
	
	public static final String SERVICE_NAME = "insert.DiscardService";
	
	private static final Log log = LogUtil.getLog(DiscardService.class);
	
	public void execute(ServiceExecutionContext context) {
		DiscardServiceRequest request = (DiscardServiceRequest) context.getRequest();
		try {
			List<StateProviderVersionModel> models = request.getModels();
			if (models == null || models.isEmpty()) {
				return;
			}
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			for (StateProviderVersionModel model : models) {
				if (model instanceof InsertSchedule) {
					// If it is associated with task, delete the task
					Task task = Task.findOneByItemType(TaskListItemFilterType.ID_INSERT_SCHEDULE, model.getId());
					if(task != null){
						task.delete();
					}

					// Remove schedule from linked list
					InsertSchedule insertSchedule = (InsertSchedule) model;
					InsertSchedule previousSchedule = insertSchedule.getPreviousSchedule();
					InsertSchedule nextSchedule = insertSchedule.getNextSchedule();
					if (previousSchedule != null) { 
						if ( nextSchedule != null )
							previousSchedule.setNextSchedule(nextSchedule);
						else
							previousSchedule.setNextSchedule(null);	
					}
					
					if ( nextSchedule != null ) {
						if ( previousSchedule != null )
							nextSchedule.setPreviousSchedule(previousSchedule);
						else
							nextSchedule.setPreviousSchedule(null);
					}
					
					// Remove schedule from collection
					InsertScheduleCollection scheduleCollection = insertSchedule.getScheduleCollection();
					insertSchedule.setScheduleCollection(null);
					insertSchedule.save();
					scheduleCollection.getInsertSchedules().remove(insertSchedule);
					
					if (scheduleCollection.getInsertSchedules().isEmpty() || (previousSchedule == null && nextSchedule == null) )
						scheduleCollection.delete();
					else
						scheduleCollection.save();
					
				} else if (model instanceof Insert) {
					// If it is associated with task, delete the task
					Task task = Task.findOneByItemType(TaskListItemFilterType.ID_INSERT, model.getId());
					if(task != null){
						task.delete();
					}

					Insert insert = (Insert) model;
					Tag.removeInsertFromAllTags(insert);
				}
				HibernateUtil.getManager().sessionSafeDelete(model);
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking DiscardService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContextForInsert(List<Insert> inserts) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		DiscardServiceRequest request = new DiscardServiceRequest();
		context.setRequest(request);
		request.setModelType(ItemType.ITEM_TYPE_INSERT);
		for (Insert insert : inserts) {
			request.getModelIds().add(insert.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}	

	public static ServiceExecutionContext createContextForInsertSchedule(List<InsertSchedule> insertSchedules) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		DiscardServiceRequest request = new DiscardServiceRequest();
		context.setRequest(request);
		request.setModelType(ItemType.ITEM_TYPE_INSERT_SCHEDULE);
		for (InsertSchedule insertSchedule : insertSchedules) {
			request.getModelIds().add(insertSchedule.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}	
}