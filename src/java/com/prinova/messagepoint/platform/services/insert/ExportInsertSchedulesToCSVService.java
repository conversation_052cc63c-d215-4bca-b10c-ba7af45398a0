package com.prinova.messagepoint.platform.services.insert;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertDeliveryType;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleBinAssignment;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.UserUtil;

public class ExportInsertSchedulesToCSVService extends AbstractService {
	
	public static final String SERVICE_NAME = "insert.ExportInsertSchedulesToCSVService";
	private static final Log log = LogUtil.getLog(ExportInsertSchedulesToCSVService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			ExportInsertSchedulesToCSVServiceRequest request = (ExportInsertSchedulesToCSVServiceRequest)context.getRequest();
			
			String insertIdsString = request.getInsertScheduleIds();
			String[] insertIdsArray = insertIdsString.split(",");
			
			StringBuffer CSV = new StringBuffer();
			
			List<InsertSchedule> insertSchedules = new ArrayList<>();
			int maxBinNumbers = 0;
			
			for (int i = 0; i < insertIdsArray.length; i++) {
				long insertScheduleId = Long.parseLong(insertIdsArray[i].trim());
				InsertSchedule insertSchedule = InsertSchedule.findById(insertScheduleId);
				if (insertSchedule.getNumberOfBins() > maxBinNumbers) {
					maxBinNumbers = insertSchedule.getNumberOfBins();
				}
				insertSchedules.add(insertSchedule);
			}
			
			if (request.getIncludeHeader()) {
				generateHeader(maxBinNumbers, CSV);
			}
			
			for (InsertSchedule insertSchedule : insertSchedules) {
				convertToCSV(insertSchedule, CSV);
			}
			
			String filePath = saveCSVToFile(CSV);
			
			((ExportInsertSchedulesToCSVServiceResponse)context.getResponse()).setFilePath(filePath);
			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ExportInsertSchedulesToCSVService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	private void generateHeader(int maxBinNumbers, StringBuffer CSV) {
		CSV.append(ApplicationUtil.getMessage("page.label.schedule.ID")).append(",");
		CSV.append(ApplicationUtil.getMessage("page.label.name")).append(",");
		CSV.append(ApplicationUtil.getMessage("page.label.start.date")).append(",");
		CSV.append(ApplicationUtil.getMessage("page.label.end.date")).append(",");
		CSV.append(ApplicationUtil.getMessage("page.label.number.of.bins")).append(",");
		for (int i = 1; i <= maxBinNumbers; i++) {
			CSV.append(ApplicationUtil.getMessage("page.label.bin.no")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.status")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.insert.name")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.stock.id")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.Delivery")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.weight")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.insert.start.date")).append("(").append(i).append("),");
			CSV.append(ApplicationUtil.getMessage("page.label.insert.end.date")).append("(").append(i).append("),");
		}
		CSV.deleteCharAt(CSV.length() - 1);
		CSV.append("\n");
	}
	
	private void convertToCSV(InsertSchedule insertSchedule, StringBuffer CSV) {
		CSV.append(insertSchedule.getScheduleId()).append(",");
		CSV.append(insertSchedule.getName()).append(",");
		CSV.append((insertSchedule.getStartDate() == null) ? "" : DateUtil.formatDateYYYYMMDD(insertSchedule.getStartDate()) + ",");
		CSV.append((insertSchedule.getEndDate() == null) ? "" : DateUtil.formatDateYYYYMMDD(insertSchedule.getEndDate()) + ",");
		CSV.append(insertSchedule.getNumberOfBins()).append(",");
		List<InsertScheduleBinAssignment> binAssignmentsSorted = insertSchedule.getBinAssignmentsSortedOnBinNo();
		int startingBinNumber = Integer.valueOf(ApplicationUtil.getProperty(SystemPropertyKeys.Insert.KEY_StartingBinNumber));
		for (int i = 0; i < insertSchedule.getNumberOfBins(); i++) {
			CSV.append(startingBinNumber + i).append(",");
			Insert insert = binAssignmentsSorted.get(i).getInsert();
			String status =  "";
			if (!binAssignmentsSorted.get(i).isAvailable()) {
				status = ApplicationUtil.getMessage("page.label.unavailable");
			} else {
				if (insert == null) {
					status = ApplicationUtil.getMessage("page.label.available");
				} else {
					if (insert.getDeliveryType().getId() == InsertDeliveryType.ID_NON_SELECTABLE) {
						status = ApplicationUtil.getMessage("page.label.reserved");
					} else {
						status = ApplicationUtil.getMessage("page.label.assigned");
					}
				}
			}
			CSV.append(status).append(",");
			String name = (insert == null) ? "" : insert.getName();
			CSV.append(name).append(",");
			String stockId = (insert == null) ? "" : insert.getStockId();
			CSV.append(stockId).append(",");
			String delivery = (insert == null) ? "" : insert.getDeliveryType().getDisplayText();
			CSV.append(delivery).append(",");
			String weight = (insert == null) ? "" : insert.getDehydratedWeight();
			CSV.append(weight).append(",");
			String startDate = (insert == null || binAssignmentsSorted.get(i).getStartDate() == null) ? "" : DateUtil.formatDateYYYYMMDD(binAssignmentsSorted.get(i).getStartDate());
			CSV.append(startDate).append(",");
			String endDate = (insert == null || binAssignmentsSorted.get(i).getEndDate() == null) ? "" : DateUtil.formatDateYYYYMMDD(binAssignmentsSorted.get(i).getEndDate());
			CSV.append(endDate).append(",");
		}
		CSV.deleteCharAt(CSV.length() - 1);
		CSV.append("\n");
	}
	
	private String saveCSVToFile(StringBuffer CSV) throws Exception {
		User principalUser = UserUtil.getPrincipalUser();
		String outputFileName = "";
		if (principalUser != null) {
			if (principalUser.getFirstName() != null && !principalUser.getFirstName().trim().isEmpty()) {
				outputFileName = StringUtil.replaceAll(principalUser.getFirstName(), "'", "");
				outputFileName = StringUtil.replaceAll(outputFileName, " ", "_");
			}
			if (principalUser.getLastName() != null && !principalUser.getLastName().trim().isEmpty()) {
				String lastName = StringUtil.replaceAll(principalUser.getLastName(), "'", "");
				lastName = StringUtil.replaceAll(lastName, " ", "_");
				if (outputFileName.trim().isEmpty()) {
					outputFileName = lastName;
				} else {
					outputFileName = outputFileName + "_" + lastName;
				}
			}
		}
		outputFileName = outputFileName + "_" + Long.valueOf(System.currentTimeMillis()).toString() + ".csv";
		File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_InsertScheduleExportDir) + File.separator);
		
		outputDirectory.mkdirs();
		String outputFilePath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_InsertScheduleExportDir) + outputFileName;
		File file = new File(outputFilePath);
		FileWriter fileWriter = new FileWriter(file);
		fileWriter.write(CSV.toString());
		fileWriter.close();
		return outputFilePath;
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub		
	}
	
	public static ServiceExecutionContext createContext(String insertScheduleIds, Boolean includeHeader) {
		
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		ExportInsertSchedulesToCSVServiceRequest request = new ExportInsertSchedulesToCSVServiceRequest();
		
		request.setInsertScheduleIds(insertScheduleIds);
		request.setIncludeHeader(includeHeader);

		context.setRequest(request);	
		
		ExportInsertSchedulesToCSVServiceResponse serviceResp = new ExportInsertSchedulesToCSVServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}
}