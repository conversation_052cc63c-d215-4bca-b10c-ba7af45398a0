package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleBinAssignment;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CreateOrUpdateInsertScheduleServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = -1980988709419760331L;

	private int action;
	private long insertScheduleId;
	private String name;
	private String scheduleId;
	private String keywords;
	private int numberOfBins;
	private String binAvailabilities;
	private Date startDate;
	private Date endDate;
	private String description;
	private Document document;
	private Long requestorId;
	private List<Insert> optionalInserts = new ArrayList<>();
	private String[] dataValues;
	private Map<Long, RateScheduleCollection> insertRateSchedules;
	private List<InsertScheduleBinAssignment> binAssignments = new ArrayList<>();
	private List<Date> startDates = new ArrayList<>();
	private List<Date> endDates = new ArrayList<>();
	private boolean defaultSchedule;
	private List<InsertSchedule> insertSchedules;
	
	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}
	public long getInsertScheduleId() {
		return insertScheduleId;
	}
	public void setInsertScheduleId(long insertScheduleId) {
		this.insertScheduleId = insertScheduleId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getScheduleId() {
		return scheduleId;
	}
	public void setScheduleId(String scheduleId) {
		this.scheduleId = scheduleId;
	}
	public String getKeywords() {
		return keywords;
	}
	public void setKeywords(String keywords) {
		this.keywords = keywords;
	}
	public int getNumberOfBins() {
		return numberOfBins;
	}
	public void setNumberOfBins(int numberOfBins) {
		this.numberOfBins = numberOfBins;
	}
	public String getBinAvailabilities() {
		return binAvailabilities;
	}
	public void setBinAvailabilities(String binAvailabilities) {
		this.binAvailabilities = binAvailabilities;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	public Long getRequestorId() {
		return requestorId;
	}
	public void setRequestorId(Long requestorId) {
		this.requestorId = requestorId;
	}
	public List<Insert> getOptionalInserts() {
		return optionalInserts;
	}
	public void setOptionalInserts(List<Insert> optionalInserts) {
		this.optionalInserts = optionalInserts;
	}
	public String[] getDataValues() {
		return dataValues;
	}
	public void setDataValues(String[] dataValues) {
		this.dataValues = dataValues;
	}
	public Map<Long, RateScheduleCollection> getInsertRateSchedules() {
		return insertRateSchedules;
	}
	public void setInsertRateSchedules(
			Map<Long, RateScheduleCollection> insertRateSchedules) {
		this.insertRateSchedules = insertRateSchedules;
	}
	public List<InsertScheduleBinAssignment> getBinAssignments() {
		return binAssignments;
	}
	public void setBinAssignments(List<InsertScheduleBinAssignment> binAssignments) {
		this.binAssignments = binAssignments;
	}
	public List<Date> getStartDates() {
		return startDates;
	}
	public void setStartDates(List<Date> startDates) {
		this.startDates = startDates;
	}
	public List<Date> getEndDates() {
		return endDates;
	}
	public void setEndDates(List<Date> endDates) {
		this.endDates = endDates;
	}
	public boolean isDefaultSchedule() {
		return defaultSchedule;
	}
	public void setDefaultSchedule(boolean defaultSchedule) {
		this.defaultSchedule = defaultSchedule;
	}
	public List<InsertSchedule> getInsertSchedules() {
		return insertSchedules;
	}
	public void setInsertSchedules(List<InsertSchedule> insertSchedules) {
		this.insertSchedules = insertSchedules;
	}
}