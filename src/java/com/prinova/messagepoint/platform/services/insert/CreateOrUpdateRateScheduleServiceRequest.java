package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CreateOrUpdateRateScheduleServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 943194648602381126L;

	private int 				action;
	private long 				rateScheduleId;
	private String 				name;
	private String 				description;
	private Date 				startDate;
	private String 				envelopName;
	private String 				envelopWeight;
	private int 				weightUnitId;
    private List<String> 		thresholdWeights 	= new ArrayList<>();
	private List<String> 		thresholdRates 		= new ArrayList<>();
	private List<RateSchedule> 	rateSchedules 		= new ArrayList<>();
	private Integer				firstPageWeight;
	private Integer				otherPagesWeight;
	private User 				requestor;

	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}

	public long getRateScheduleId() {
		return rateScheduleId;
	}
	public void setRateScheduleId(long rateScheduleId) {
		this.rateScheduleId = rateScheduleId;
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getEnvelopName() {
		return envelopName;
	}
	public void setEnvelopName(String envelopName) {
		this.envelopName = envelopName;
	}

	public String getEnvelopWeight() {
		return envelopWeight;
	}
	public void setEnvelopWeight(String envelopWeight) {
		this.envelopWeight = envelopWeight;
	}

	public int getWeightUnitId() {
		return weightUnitId;
	}
	public void setWeightUnitId(int weightUnitId) {
		this.weightUnitId = weightUnitId;
	}

	public List<String> getThresholdWeights() {
		return thresholdWeights;
	}
	public void setThresholdWeights(List<String> thresholdWeights) {
		this.thresholdWeights = thresholdWeights;
	}

	public List<String> getThresholdRates() {
		return thresholdRates;
	}
	public void setThresholdRates(List<String> thresholdRates) {
		this.thresholdRates = thresholdRates;
	}

	public List<RateSchedule> getRateSchedules() {
		return rateSchedules;
	}
	public void setRateSchedules(List<RateSchedule> rateSchedules) {
		this.rateSchedules = rateSchedules;
	}

	public Integer getFirstPageWeight() {
		return firstPageWeight;
	}
	public void setFirstPageWeight(Integer firstPageWeight) {
		this.firstPageWeight = firstPageWeight;
	}

	public Integer getOtherPagesWeight() {
		return otherPagesWeight;
	}
	public void setOtherPagesWeight(Integer otherPagesWeight) {
		this.otherPagesWeight = otherPagesWeight;
	}

	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
}