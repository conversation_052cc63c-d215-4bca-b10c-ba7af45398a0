package com.prinova.messagepoint.platform.services.insert;

import java.util.*;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleListFilterType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class GetInsertScheduleListService extends AbstractService {

	public static final String SERVICE_NAME = "insert.GetInsertScheduleListService";
	
	private static final Log log = LogUtil.getLog(GetInsertScheduleListService.class);
	
	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)	
	public void execute(ServiceExecutionContext context) {
		GetInsertScheduleListServiceRequest request = (GetInsertScheduleListServiceRequest) context.getRequest();
		List<InsertSchedule> list = new ArrayList<>();
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			ArrayList<MessagepointCriterion> searchCritList = new ArrayList<>();

			// Create search criteria
			String nameSearchStr = request.getNameSearchStr();
			String keywordSearchStr = request.getKeywordSearchStr();
			String idSearchStr = request.getScheduleIdSearchStr();
			if (nameSearchStr != null && !nameSearchStr.trim().isEmpty()) {
				searchCritList.add(MessagepointRestrictions.ilike("name", nameSearchStr.trim(), MessagepointRestrictions.MatchMode.ANYWHERE));
			}
			if (keywordSearchStr != null && !keywordSearchStr.trim().isEmpty()) {
				searchCritList.add(MessagepointRestrictions.ilike("keywords", keywordSearchStr.trim(), MessagepointRestrictions.MatchMode.ANYWHERE));
			}
			if (idSearchStr != null && !idSearchStr.trim().isEmpty()) {
				searchCritList.add(MessagepointRestrictions.ilike("scheduleId", idSearchStr.trim(), MessagepointRestrictions.MatchMode.ANYWHERE));
			}

			// Create date filter criteria
			Date searchDate = DateUtil.getDateWithZeroTime(request.getDateFilter());
			searchCritList.add(MessagepointRestrictions.le("startDate", searchDate));
			searchCritList.add(MessagepointRestrictions.or(MessagepointRestrictions.isNull("endDate"),MessagepointRestrictions.ge("endDate", searchDate)));

			// Create status filter criteria
			if (request.getFilterId() > 0) {
				MessagepointCriterion statusI = MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_INACTIVE);
				MessagepointCriterion statusW = MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_WAITING_APPROVAL);
				MessagepointCriterion statusInactive = MessagepointRestrictions.or(statusI, statusW);
				switch (request.getFilterId()) {
				case InsertScheduleListFilterType.ID_INACTIVE:
					searchCritList.add(statusInactive);
					break;
				case InsertScheduleListFilterType.ID_ACTIVE:
					searchCritList.add(MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_ACTIVE));
					break;
//				case InsertScheduleListFilterType.ID_MY_INACTIVE:
//					searchCritList.add(statusInactive);
//					searchCritList.add(MessagepointRestrictions.eq("lockedFor", request.getRequestor().getId()));
//					break;
				case InsertScheduleListFilterType.ID_ALL:
					searchCritList.add(MessagepointRestrictions.ne("status.id", VersionStatus.ONE_VERSION_ARCHIVE));
					searchCritList.add(MessagepointRestrictions.ne("status.id", VersionStatus.ONE_vERSION_REMOVED));
					searchCritList.add(MessagepointRestrictions.ne("status.id", VersionStatus.ONE_VERSION_SETUP));
					break;
//				case InsertScheduleListFilterType.ID_SETUP:
//					searchCritList.add(MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_SETUP));
//					break;
				default:
					break;
				}
			}

			if (!request.isGlobal()) {
				Map<String,String> aliasJoinMap = new LinkedHashMap<>();
				aliasJoinMap.put("scheduleCollection", "sc");
				aliasJoinMap.put("sc.document", "doc");
				searchCritList.add(MessagepointRestrictions.eq("doc.id", request.getDocumentId()));
				list = HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, searchCritList, aliasJoinMap, MessagepointOrder.asc("name"));
			} else {
				list = HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, searchCritList, MessagepointOrder.asc("name"));
			}
			
			context.getResponse().setResultValueBean(list);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GetInsertScheduleListService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContext(long documentId, 
														int filterId,
														Date dateFilter,
														String nameSearchStr,
														String keywordSearchStr,
														String scheduleIdSearchStr,
														boolean isGlobal,
														User requestor) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GetInsertScheduleListServiceRequest request = new GetInsertScheduleListServiceRequest();
		context.setRequest(request);

		request.setDocumentId(documentId);
		request.setFilterId(filterId);
		request.setDateFilter(dateFilter);
		request.setNameSearchStr(nameSearchStr);
		request.setKeywordSearchStr(keywordSearchStr);
		request.setScheduleIdSearchStr(scheduleIdSearchStr);
		request.setGlobal(isGlobal);
		request.setRequestor(requestor);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}