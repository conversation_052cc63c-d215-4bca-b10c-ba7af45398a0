package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class GetInsertListServiceRequest implements ServiceRequest{
	private static final long serialVersionUID = -6975943558048093870L;
	private int listBy;
	private long listById;
	private int filterId;
	private String nameSearchStr;
	private String idSearchStr;
	private User requestor;
	public String getNameSearchStr() {
		return nameSearchStr;
	}
	public void setNameSearchStr(String nameSearchStr) {
		this.nameSearchStr = nameSearchStr;
	}
	public String getIdSearchStr() {
		return idSearchStr;
	}
	public void setIdSearchStr(String idSearchStr) {
		this.idSearchStr = idSearchStr;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public long getListById() {
		return listById;
	}
	public void setListById(long listById) {
		this.listById = listById;
	}
	public int getFilterId() {
		return filterId;
	}
	public void setFilterId(int filterId) {
		this.filterId = filterId;
	}
	public int getListBy() {
		return listBy;
	}
	public void setListBy(int listBy) {
		this.listBy = listBy;
	}
}
