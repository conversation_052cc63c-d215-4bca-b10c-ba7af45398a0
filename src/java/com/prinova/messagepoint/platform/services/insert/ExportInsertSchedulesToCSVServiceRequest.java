package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportInsertSchedulesToCSVServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -4885884378595679868L;

	private String insertScheduleIds;
	private Boolean includeHeader;

	public String getInsertScheduleIds() {
		return insertScheduleIds;
	}
	public void setInsertScheduleIds(String insertScheduleIds) {
		this.insertScheduleIds = insertScheduleIds;
	}
	public Boolean getIncludeHeader() {
		return includeHeader;
	}
	public void setIncludeHeader(Boolean includeHeader) {
		this.includeHeader = includeHeader;
	}
}