package com.prinova.messagepoint.platform.services.insert;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.admin.ItemType;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class DeleteService extends AbstractService {
	public static final String SERVICE_NAME = "insert.DeleteService";
	private static final Log log = LogUtil.getLog(DeleteService.class);
	public void execute(ServiceExecutionContext context) {
		DeleteServiceRequest request = (DeleteServiceRequest) context.getRequest();
		try {
			List<StateProviderVersionModel> models = request.getModels();
			if (models == null || models.isEmpty()) {
				return;
			}
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			for (StateProviderVersionModel model : models) {
				model.setLastEditor(null);
				model.setLockedFor(null);
				model.setStatus(VersionStatus.findById(VersionStatus.ONE_vERSION_REMOVED));
				model.save();

				// If it is associated with task, delete the task
				Task task = Task.findOneByItemType(TaskListItemFilterType.ID_INSERT, model.getId());
				if(task != null){
					task.delete();
				}
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking DeleteService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		DeleteServiceRequest request = (DeleteServiceRequest) context.getRequest();
		try {
			final boolean roleInsertArchiveAllowed = UserUtil.isAllowed(request.getRequestorId(), Permission.ROLE_INSERT_ARCHIVE);
			for (StateProviderVersionModel model : request.getModels()) {
				if (model.getStatus().getId() != VersionStatus.ONE_VERSION_INACTIVE && model.getStatus().getId() != VersionStatus.ONE_VERSION_ARCHIVE) {
					this.getResponse(context).addErrorMessage(model.getName(),
							ApplicationErrorMessages.INSERT_IS_NOT_IN_STATE_TO_ARCHIVE,
							SERVICE_NAME,
							context.getLocale());
				}
				if ((model.getLockedFor()!=null && !model.getLockedFor().equals(request.getRequestorId())) || !roleInsertArchiveAllowed) {
					this.getResponse(context).addErrorMessage(model.getName(),
							ApplicationErrorMessages.INSERT_NOT_AUTHORIZED_TO_UPDATE,
							SERVICE_NAME,
							context.getLocale());
				}
			}
		} catch (FinderException fe) {
			this.getResponse(context).addErrorMessage("",
					ApplicationErrorMessages.INSERT_NOT_FOUND,
					SERVICE_NAME,
					context.getLocale());
		}
	}
	public static ServiceExecutionContext createContextForInsert(List<Insert> inserts, 
																 Long requestorId, 
																 String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		DeleteServiceRequest request = new DeleteServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setModelType(ItemType.ITEM_TYPE_INSERT);
		for (Insert insert : inserts) {
			request.getModelIds().add(insert.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}	

	public static ServiceExecutionContext createContextForInsertSchedule(List<InsertSchedule> insertSchedules, 
																		 Long requestorId, 
																		 String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		DeleteServiceRequest request = new DeleteServiceRequest();
		context.setRequest(request);
		request.setRequestorId(requestorId);
		request.setUserNote(userNote);
		request.setModelType(ItemType.ITEM_TYPE_INSERT_SCHEDULE);
		for (InsertSchedule insertSchedule : insertSchedules) {
			request.getModelIds().add(insertSchedule.getId());
		}
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}	
}