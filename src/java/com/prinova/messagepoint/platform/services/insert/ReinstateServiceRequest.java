package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.util.InsertUtil;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ReinstateServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -3647318757946408518L;
	private int modelType;
	private List<Long> modelIds= new ArrayList<>();
	private Long assignedToUserId;		
	private Long requestorId;
	private String userNote;
	private List<StateProviderVersionModel> models;	
	public int getModelType() {
		return modelType;
	}
	public void setModelType(int modelType) {
		this.modelType = modelType;
	}
	public Long getRequestorId() {
		return requestorId;
	}
	public void setRequestorId(Long requestorId) {
		this.requestorId = requestorId;
	}
	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}
	public Long getAssignedToUserId() {
		return assignedToUserId;
	}
	public void setAssignedToUserId(Long assignedToUserId) {
		this.assignedToUserId = assignedToUserId;
	}
	public List<Long> getModelIds() {
		return modelIds;
	}
	public void setModelIds(List<Long> modelIds) {
		this.modelIds = modelIds;
	}
	public List<StateProviderVersionModel> getModels() throws FinderException {
		if (models==null) {
			models = InsertUtil.toModels(getModelType(), getModelIds());
		}
		return models;
	}	

}



