package com.prinova.messagepoint.platform.services.insert;

import java.util.Date;
import java.util.List;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.model.insert.RateScheduleDetail;
import com.prinova.messagepoint.model.insert.WeightUtil;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;

public class CreateOrUpdateRateScheduleService extends AbstractService{

	public static final String SERVICE_NAME = "insert.CreateOrUpdateRateScheduleService";
	
	private static final Log log = LogUtil.getLog(CreateOrUpdateRateScheduleService.class);
	
	public static final int ACTION_CREATE 					= 1;
	public static final int ACTION_UPDATE 					= 2;
	public static final int ACTION_NEW_RANGE 				= 3;
	public static final int ACTION_DISCARD 					= 4;
	public static final int ACTION_DISCONTINUE 				= 5;
	public static final int ACTION_CLONE 					= 8;
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			CreateOrUpdateRateScheduleServiceRequest request =(CreateOrUpdateRateScheduleServiceRequest) context.getRequest();
			
			RateSchedule rateSchedule = null;
			RateScheduleCollection rateScheduleCollection = null;
			List<RateSchedule> rateSchedules = null;
			
			switch (request.getAction()) {
				case ACTION_CREATE:
					rateScheduleCollection = createRateScheduleCollection(request.getRequestor());
					updateRateScheduleCollectionProperties(rateScheduleCollection, request.getEnvelopName(), request.getEnvelopWeight(), request.getWeightUnitId());
					rateSchedule = createRateSchedule(request.getRequestor());
					updateRateScheduleProperties(rateSchedule, request);
					rateSchedule.setRateScheduleCollection(rateScheduleCollection);
					rateScheduleCollection.getRateSchedules().add(rateSchedule);
					rateScheduleCollection.save();
					break;
				case ACTION_CLONE:
					rateScheduleCollection = createRateScheduleCollection(request.getRequestor());
					updateRateScheduleCollectionProperties(rateScheduleCollection, request.getEnvelopName(), request.getEnvelopWeight(), request.getWeightUnitId());
					rateSchedule = createRateSchedule(request.getRequestor());
					updateRateScheduleProperties(rateSchedule, request);
					rateSchedule.setRateScheduleCollection(rateScheduleCollection);
					rateScheduleCollection.getRateSchedules().add(rateSchedule);
					rateScheduleCollection.save();
					break;
				case ACTION_UPDATE:
					rateSchedule = RateSchedule.findById(request.getRateScheduleId());
					updateRateScheduleProperties(rateSchedule, request);
					if (rateSchedule.getPreviousSchedule() != null) {
						Date startDate = rateSchedule.getStartDate();
						Date expectingPreviousEndDate = DateUtil.previousDay(startDate);   
						RateSchedule previousRateSchedule = rateSchedule.getPreviousSchedule();
						Date previousEndDate = previousRateSchedule.getEndDate();
						if (!previousEndDate.equals(expectingPreviousEndDate)) {
							previousRateSchedule.setEndDate(expectingPreviousEndDate);
							previousRateSchedule.save();
						}
					}
					rateSchedule.save();
					rateScheduleCollection = rateSchedule.getRateScheduleCollection();
					updateRateScheduleCollectionProperties(rateScheduleCollection, request.getEnvelopName(), request.getEnvelopWeight(), request.getWeightUnitId());
					rateScheduleCollection.save();
					break;
				case ACTION_NEW_RANGE:
					rateSchedule = RateSchedule.findById(request.getRateScheduleId());
					RateSchedule newRateSchedule = createRateSchedule(request.getRequestor());
					updateRateScheduleProperties(newRateSchedule, request);
					newRateSchedule.save();
					rateScheduleCollection = rateSchedule.getRateScheduleCollection();
					newRateSchedule.setRateScheduleCollection(rateScheduleCollection);
					rateScheduleCollection.getRateSchedules().add(newRateSchedule);
					rateSchedule.setNextSchedule(newRateSchedule);
					rateSchedule.setEndDate(DateUtil.previousDay(newRateSchedule.getStartDate()));
					newRateSchedule.setPreviousSchedule(rateSchedule);
					rateScheduleCollection.save();
					rateSchedule = newRateSchedule;
					break;
				case ACTION_DISCARD:
					rateSchedules = request.getRateSchedules();
					if (rateSchedules != null) {
						for (RateSchedule rateScheduleItem: rateSchedules) {
							RateSchedule previousSchedule = rateScheduleItem.getPreviousSchedule();
							if (previousSchedule == null) {
								rateScheduleCollection = rateScheduleItem.getRateScheduleCollection();
								rateScheduleCollection.delete();
							} else {
								previousSchedule.setEndDate(null);
								previousSchedule.setNextSchedule(null);
								rateScheduleCollection = rateScheduleItem.getRateScheduleCollection();
								rateScheduleItem.setRateScheduleCollection(null);
								rateScheduleCollection.getRateSchedules().remove(rateScheduleItem);
								rateScheduleCollection.save();
							}
						}
					}
					break;
				case ACTION_DISCONTINUE:
					rateSchedules = request.getRateSchedules();
					if (rateSchedules != null) {
						for (RateSchedule rateScheduleItem: rateSchedules) {
							rateScheduleCollection = rateScheduleItem.getRateScheduleCollection();
							RateSchedule lastRateSchedule = rateScheduleCollection.getLastRateSchedule();
							if (lastRateSchedule != null) {
								lastRateSchedule.setEndDate(DateUtil.yesterdayWithZeroTime());
								lastRateSchedule.save();
							}
						}
						rateSchedule = rateSchedules.get(0);
					}
					break;
				default:
					break;
			}
			
			context.getResponse().setResultValueBean(rateSchedule);
			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateOrUpdateRateScheduleService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e);
		}		
	}

	private RateScheduleCollection createRateScheduleCollection(User requestor) {
		RateScheduleCollection rateScheduleCollection = new RateScheduleCollection();
		rateScheduleCollection.setCreated(DateUtil.now());
		rateScheduleCollection.setCreatedBy(requestor.getId());
		return rateScheduleCollection;
	}

	private void updateRateScheduleCollectionProperties(RateScheduleCollection rateScheduleCollection, String envelopeName, String envelopeWeight, int weightUnitId) throws Exception {
		rateScheduleCollection.setEnvelopeName(envelopeName);
		rateScheduleCollection.setWeightUnitId(weightUnitId);
		rateScheduleCollection.setEnvelopeWeight(WeightUtil.hydrateWeight(envelopeWeight));
	}
	
	private RateSchedule createRateSchedule(User requestor) {
		RateSchedule rateSchedule = new RateSchedule();
		rateSchedule.setCreated(DateUtil.now());
		rateSchedule.setCreatedBy(requestor.getId());
		return rateSchedule;
	}

	private void updateRateScheduleProperties(RateSchedule rateSchedule, CreateOrUpdateRateScheduleServiceRequest request) throws Exception {
		rateSchedule.setName(request.getName());
		rateSchedule.setDescription(request.getDescription());
		rateSchedule.setStartDate(request.getStartDate());
		rateSchedule.setFirstPageWeight(request.getFirstPageWeight());
		rateSchedule.setOtherPagesWeight(request.getOtherPagesWeight());
		rateSchedule.setUpdated(DateUtil.now());
		rateSchedule.setUpdatedBy(request.getRequestor().getId());
		loadRateScheduleDetailsFromRequest(rateSchedule, request);
	}

	private void loadRateScheduleDetailsFromRequest(RateSchedule rateSchedule, CreateOrUpdateRateScheduleServiceRequest request) throws Exception {
		if (request.getThresholdRates() == null || request.getThresholdRates().isEmpty()) {
			return;
		}

		List<RateScheduleDetail> currentRSDetails = rateSchedule.getRateScheduleDetailsSorted();
		if (request.getThresholdRates().size() > currentRSDetails.size()) {
			while (request.getThresholdRates().size() > currentRSDetails.size()) {
				RateScheduleDetail newRateScheduleDetail = new RateScheduleDetail();
				currentRSDetails.add(newRateScheduleDetail);
			}
		} else if (request.getThresholdRates().size() < currentRSDetails.size()) {
			while (request.getThresholdRates().size() < currentRSDetails.size()) {
				currentRSDetails.get(currentRSDetails.size() - 1).setRateSchedule(null);
				rateSchedule.getRateScheduleDetails().remove(currentRSDetails.get(currentRSDetails.size() - 1));
				rateSchedule.save();
				currentRSDetails.remove(currentRSDetails.size() - 1);
			}
		}
		for (int i = 0; i < currentRSDetails.size(); i++) {
			currentRSDetails.get(i).setWeight(WeightUtil.hydrateWeight(request.getThresholdWeights().get(i)));
			currentRSDetails.get(i).setRate(WeightUtil.hydrateWeight(request.getThresholdRates().get(i)));
			currentRSDetails.get(i).setRateSchedule(rateSchedule);
		}
		rateSchedule.getRateScheduleDetails().clear();
		rateSchedule.getRateScheduleDetails().addAll(currentRSDetails);
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub	
	}

	public static ServiceExecutionContext createContextForDiscard(List<RateSchedule> rateSchedules,
			   													  User requestor){

		ServiceExecutionContext context = createContext(ACTION_DISCARD,
														-1L,
														"",
														"",
														null,
														"",
														"",
														-1,
														null,
														null,
														rateSchedules,
														null,
														null,
														requestor);
		return context;
	}

	public static ServiceExecutionContext createContextForDiscontinue(List<RateSchedule> rateSchedules,
			   														  User requestor){

		ServiceExecutionContext context = createContext(ACTION_DISCONTINUE,
														-1L,
														"",
														"",
														null,
														"",
														"",
														-1,
														null,
														null,
														rateSchedules,
														null,
														null,
														requestor);
		return context;
	}

	public static ServiceExecutionContext createContextForCreate(String name,
																 String description,
																 Date startDate,
																 Date endDate,
																 String envelopName,
																 String envelopWeight,
																 int weightUnitId,
																 List<String> thresholdWeights,
																 List<String> thresholdRates,
																 Integer firstPageWeight,
																 Integer otherPagesWeight,
		 														 User requestor){
		
		ServiceExecutionContext context = createContext(ACTION_CREATE,
														-1L,
														name,
														description,
														startDate,
														envelopName,
														envelopWeight,
														weightUnitId,
														thresholdWeights,
														thresholdRates,
														null,
														firstPageWeight,
														otherPagesWeight,
														requestor);
		return context;
	}
	
	public static ServiceExecutionContext createContextForClone( long rateScheduleId,
																 String name,
																 String description,
																 Date startDate,
																 Date endDate,
																 String envelopName,
																 String envelopWeight,
																 int weightUnitId,
																 List<String> thresholdWeights,
																 List<String> thresholdRates,
																 Integer firstPageWeight,
																 Integer otherPagesWeight,
																 User requestor){

		ServiceExecutionContext context = createContext(ACTION_CLONE,
														rateScheduleId,
														name,
														description,
														startDate,
														envelopName,
														envelopWeight,
														weightUnitId,
														thresholdWeights,
														thresholdRates,
														null,
														firstPageWeight,
														otherPagesWeight,
														requestor);
		return context;
	}

	public static ServiceExecutionContext createContextForUpdate(long rateScheduleId,
																 String name,
																 String description,
																 Date startDate,
																 Date endDate,
																 String envelopName,
																 String envelopWeight,
																 int weightUnitId,
																 List<String> thresholdWeights,
																 List<String> thresholdRates,
																 Integer firstPageWeight,
																 Integer otherPagesWeight,
		 														 User requestor){
		
		ServiceExecutionContext context = createContext(ACTION_UPDATE,
														rateScheduleId,
														name,
														description,
														startDate,
														envelopName,
														envelopWeight,
														weightUnitId,
														thresholdWeights,
														thresholdRates,
														null,
														firstPageWeight,
														otherPagesWeight,
														requestor);
		return context;
	}
	
	public static ServiceExecutionContext createContextForNewRange(long rateScheduleId,
																   String name,
																   String description,
																   Date startDate,
																   Date endDate,
																   List<String> thresholdWeights,
																   List<String> thresholdRates,
																   Integer firstPageWeight,
																   Integer otherPagesWeight,
		 														   User requestor){

		ServiceExecutionContext context = createContext(ACTION_NEW_RANGE,
														rateScheduleId,
														name,
														description,
														startDate,
														"",
														"",
														-1,
														thresholdWeights,
														thresholdRates,
														null,
														firstPageWeight,
														otherPagesWeight,
														requestor);
		return context;
	}

	private static ServiceExecutionContext createContext(int action,
														 long rateScheduleId,
														 String name,
														 String description,
														 Date startDate,
														 String envelopName,
														 String envelopWeight,
														 int weightUnitId,
														 List<String> thresholdWeights,
														 List<String> thresholdRates,
														 List<RateSchedule> rateSchedules,
														 Integer firstPageWeight,
														 Integer otherPagesWeight,
														 User requestor){

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateRateScheduleServiceRequest request = new CreateOrUpdateRateScheduleServiceRequest();
		context.setRequest(request);
		
		request.setAction(action);
		request.setRateScheduleId(rateScheduleId);
		request.setName(name);
		request.setDescription(description);
		request.setStartDate(startDate);
		request.setEnvelopName(envelopName);
		request.setEnvelopWeight(envelopWeight);
		request.setWeightUnitId(weightUnitId);
		request.setThresholdWeights(thresholdWeights);
		request.setThresholdRates(thresholdRates);
		request.setRateSchedules(rateSchedules);
		request.setFirstPageWeight(firstPageWeight);
		request.setOtherPagesWeight(otherPagesWeight);
		request.setRequestor(requestor);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}