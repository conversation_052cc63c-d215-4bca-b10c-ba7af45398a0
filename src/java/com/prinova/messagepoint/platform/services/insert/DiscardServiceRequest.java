package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.util.InsertUtil;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class DiscardServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = 1718078618053326597L;

	private int modelType;
	private List<Long> modelIds= new ArrayList<>();
	private List<StateProviderVersionModel> models;	

	public int getModelType() {
		return modelType;
	}
	public void setModelType(int modelType) {
		this.modelType = modelType;
	}
	public List<Long> getModelIds() {
		return modelIds;
	}
	public void setModelIds(List<Long> modelIds) {
		this.modelIds = modelIds;
	}
	public List<StateProviderVersionModel> getModels() throws FinderException {
		if (models==null) {
			models = InsertUtil.toModels(getModelType(), getModelIds());
		}
		return models;
	}
}