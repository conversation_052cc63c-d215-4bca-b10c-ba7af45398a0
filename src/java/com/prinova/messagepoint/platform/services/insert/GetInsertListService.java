package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.model.folder.FavoritesFolder;
import com.prinova.messagepoint.model.folder.UserFolder;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertListFilterType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.HibernateUtil;

public class GetInsertListService extends AbstractService {
	public static final String SERVICE_NAME = "insert.GetInsertListService";
	private static final Log log = LogUtil.getLog(GetInsertListService.class);
	public static int LIST_BY_ALL = 1;
	public static int LIST_BY_TOUCHPOINT = 2;
	public static int LIST_BY_NO_TOUCHPOINT = 4;

	@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)	
	public void execute(ServiceExecutionContext context) {
		GetInsertListServiceRequest request = (GetInsertListServiceRequest) context.getRequest();
		List<Insert> list = new ArrayList<>();
		try {

			// create search criteria
			String nameSearchStr = request.getNameSearchStr();
			String idSearchStr = request.getIdSearchStr();
			ArrayList<MessagepointCriterion> searchCritList = new ArrayList<>();
			
			if (nameSearchStr != null && !nameSearchStr.trim().isEmpty()) {
				searchCritList.add(MessagepointRestrictions.ilike("name", nameSearchStr.trim(), MessagepointRestrictions.MatchMode.ANYWHERE));
			}
			if (idSearchStr != null && !idSearchStr.trim().isEmpty()) {
				searchCritList.add(MessagepointRestrictions.ilike("stockId", idSearchStr.trim(), MessagepointRestrictions.MatchMode.ANYWHERE));
			}
			// create status filter criteria
			// INACTIVE_ID = 1;
			// ACTIVE_ID = 2;
			// ARCHIVED_ID = 3;
			// MY_INACTIVE_ID = 4;
			// ALL_ID = 5;
			if (request.getFilterId() > 0) {
				MessagepointCriterion statusI = MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_INACTIVE);
				MessagepointCriterion statusW = MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_WAITING_APPROVAL);
				MessagepointCriterion statusInactive = MessagepointRestrictions.or(statusI, statusW);
				switch (request.getFilterId()) {
				case InsertListFilterType.ID_INACTIVE:
					searchCritList.add(statusInactive);
					break;
				case InsertListFilterType.ID_ACTIVE:
					searchCritList.add(MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_ACTIVE));
					break;
				case InsertListFilterType.ID_ARCHIVED:
					searchCritList.add(MessagepointRestrictions.eq("status.id", VersionStatus.ONE_VERSION_ARCHIVE));
					break;
//				case InsertListFilterType.ID_MY_INACTIVE:
//					searchCritList.add(statusInactive);
//					searchCritList.add(MessagepointRestrictions.eq("lockedFor", request.getRequestor().getId()));
//					break;
//				case InsertListFilterType.ID_ALL:
//					searchCritList.add(MessagepointRestrictions.ne("status.id", VersionStatus.ONE_VERSION_ARCHIVE));
//					searchCritList.add(MessagepointRestrictions.ne("status.id", VersionStatus.ONE_vERSION_REMOVED));
//					break;
				default:
					break;
				}
			}
			// get the inserts according to listBy type
			switch (request.getListBy()) {
			case 1: // all
				list = HibernateUtil.getManager().getObjectsAdvanced(Insert.class, searchCritList, MessagepointOrder.asc("name"));
//				list = Insert.findAll();
				break;
			case 2: // touchpoint
				if (request.getListById()>0) {
					Map<String,String> aliasJoinMap = new HashMap<>();
					aliasJoinMap.put("documents", "document");
					searchCritList.add(MessagepointRestrictions.eq("document.id", request.getListById()));
					list = HibernateUtil.getManager().getObjectsAdvanced(Insert.class, searchCritList, aliasJoinMap, MessagepointOrder.asc("name"));					
				}
				break;
			case 3: // folder
				if (request.getListById()>0) {
					UserFolder folder = UserFolder.findById(request.getListById());
					List<Insert> folderInserts = new ArrayList<>();
					if (folder instanceof FavoritesFolder)
						folderInserts = folder.getInsertsSortedByName();
					else
						folderInserts = folder.getInsertSortedByTime();
					List<Insert> templist = HibernateUtil.getManager().getObjectsAdvanced(Insert.class, searchCritList);
					if (folderInserts!=null) {
						for (Insert i : folderInserts) {
							if (templist.contains(i)) {
								list.add(i);
							}
						}
					}
				}
				break;
			case 4: // no touchpoint
				if (request.getListById()>0) {
					searchCritList.add(MessagepointRestrictions.isEmpty("documents"));
					list = HibernateUtil.getManager().getObjectsAdvanced(Insert.class, searchCritList, MessagepointOrder.asc("name"));					
				}
				break;
			default:
				break;
			}
			context.getResponse().setResultValueBean(list);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GetInsertListService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {

	}

	public static ServiceExecutionContext createContext(int listBy, long listById, int filterId,
			String nameSearchStr, String idSearchStr,
			User requestor) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GetInsertListServiceRequest request = new GetInsertListServiceRequest();
		context.setRequest(request);
		request.setListBy(listBy);
		request.setListById(listById);
		request.setFilterId(filterId);
		request.setNameSearchStr(nameSearchStr);
		request.setIdSearchStr(idSearchStr);
		request.setRequestor(requestor);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

}
