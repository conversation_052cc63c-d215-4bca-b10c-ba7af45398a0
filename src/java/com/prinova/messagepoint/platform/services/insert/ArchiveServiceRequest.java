package com.prinova.messagepoint.platform.services.insert;

import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.exceptionhandler.FinderException;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.util.InsertUtil;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ArchiveServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -4378192620404243582L;
	private int modelType;
	private List<Long> modelIds= new ArrayList<>();
	private Long requestorId;
	private String userNote;
	private List<StateProviderVersionModel> models;	
	public int getModelType() {
		return modelType;
	}
	public void setModelType(int modelType) {
		this.modelType = modelType;
	}
	public List<Long> getModelIds() {
		return modelIds;
	}
	public void setModelIds(List<Long> modelIds) {
		this.modelIds = modelIds;
	}
	public Long getRequestorId() {
		return requestorId;
	}
	public void setRequestorId(Long requestorId) {
		this.requestorId = requestorId;
	}
	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}
	public List<StateProviderVersionModel> getModels() throws FinderException {
		if (models == null) {
			models = InsertUtil.toModels(getModelType(), getModelIds());
		}
		return models;
	}

}

