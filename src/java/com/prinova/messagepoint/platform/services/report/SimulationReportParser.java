package com.prinova.messagepoint.platform.services.report;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;

import com.prinova.messagepoint.util.XmlParserConfigUtil;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import com.prinova.messagepoint.model.simulation.ConditionCoverageReport;
import com.prinova.messagepoint.model.simulation.MessageCoverageReport;
import com.prinova.messagepoint.model.simulation.RuleCoverageReport;
import com.prinova.messagepoint.model.simulation.SimulationCoverageReport;
import com.prinova.messagepoint.model.simulation.SimulationReport;
import com.prinova.messagepoint.model.simulation.TargetGroupCoverageReport;
import com.prinova.messagepoint.model.simulation.ZoneCoverageReport;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointXmlParser;

public class SimulationReportParser extends MessagePointXmlParser 
{
	private static final int COVERAGE_REPORT = 1;
	private static final int SEGMENTATION_REPORT = 2;
	
	SimulationReport simReport;
	
	public SimulationReport ParseReportFile( File simReportFile ) throws Exception
	{
		if( simReportFile == null ) throw new IllegalArgumentException( "Simulation report is null" );

		InputStream inputStream = new FileInputStream( simReportFile );
		DocumentBuilder builder = XmlParserConfigUtil.getDocumentBuilderFactory().newDocumentBuilder();
		this.document = builder.parse( inputStream );

		NodeList nodeList = this.document.getElementsByTagName("SimulationReport");
		if ( nodeList.getLength() >= 1 )
		{
			ParseSimulationReport(nodeList.item(0));
		}
		else if ( nodeList.getLength() == 0 )
		{
			LogUtil.getLog(this.getClass()).error("Missing SimulationReport document node in XML file");
		}
		
		return simReport;
	}

	private void ParseSimulationReport( Node root )
	{
		for( Node jobNode : getNode(root.getChildNodes(), "Job") )
		{
			ParseJobNode( jobNode );
			break; // should only ever be one job node.
		}
	}
	
	private void ParseJobNode( Node jobNode )
	{
		Long jobId = Long.parseLong(getAttribute( jobNode, "id"));
		long simType = Long.parseLong(getAttribute(jobNode, "simulationtype") );
		
		switch((int) simType)
		{
		case COVERAGE_REPORT:
			ParseCoverageReport( jobNode, jobId );
			break;
			
		case SEGMENTATION_REPORT:
			ParseSegmentationReport( jobNode, jobId );
			break;
		}	
	}
	
	private void ParseCoverageReport( Node jobNode, Long jobId )
	{
		SimulationCoverageReport scr = new SimulationCoverageReport();
		String customers = getAttribute(jobNode, "customers");
		Long customerCount = Long.valueOf(0);
		if (!customers.isEmpty())
		{
			customerCount = Long.parseLong(getAttribute(jobNode, "customers"));
			scr.setCustomerCount(customerCount);
		}
		
		simReport = scr;
		simReport.setJobId(jobId);	

		for( Node docNode : getNode(jobNode.getChildNodes(), "Document") )
		{
			ParseDocumentCoverage( docNode, scr );
		}
	}
	
	private void ParseDocumentCoverage( Node documentNode, SimulationCoverageReport simCoverReport )
	{
		Map<Long,TargetGroupCoverageReport> targetGroups = new HashMap<>();
		
		for( Node tgNode : getNode(documentNode.getChildNodes(), "TargetGroup") )
		{
			TargetGroupCoverageReport tgcr = ParseTargetGroupCoverageReport( tgNode );
			targetGroups.put(tgcr.getTargetGroupId(), tgcr);
		}
		
		for( Node zoneNode : getNode(documentNode.getChildNodes(), "Zone") )
		{
			ZoneCoverageReport zoneCovRep = ParseZoneCoverageReport( zoneNode, targetGroups );
			simCoverReport.getZoneCoverageReports().add(zoneCovRep);
			zoneCovRep.setSimulationCoverageReport(simCoverReport);
		}
	}

	private ZoneCoverageReport ParseZoneCoverageReport( Node zoneNode, Map<Long, TargetGroupCoverageReport> targetGroups )
	{
		ZoneCoverageReport zoneCovReport = new ZoneCoverageReport();

		long zoneId =  Long.parseLong(getAttribute( zoneNode, "refid"));
		long qualified =  Long.parseLong(getAttribute( zoneNode, "messagesqualified"));
		long qualifyAttempts = Long.parseLong(getAttribute( zoneNode, "qualificationattempts"));
		long dataGroupCount = Long.parseLong(getAttribute( zoneNode, "datagroupcount"));

		zoneCovReport.setZoneId(zoneId);
		zoneCovReport.setMessagesQualified(qualified);
		zoneCovReport.setQualificationAttempts(qualifyAttempts);
		zoneCovReport.setDataGroupCount(dataGroupCount);
		
		for( Node messageNode : getNode(zoneNode.getChildNodes(), "Message") )
		{
			MessageCoverageReport mcr = ParseMessageCoverageReport( messageNode, targetGroups );
			zoneCovReport.getMessageCoverageReports().add(mcr);
			mcr.setZoneCoverageReport(zoneCovReport);
		}
		
		return zoneCovReport;
	}
	
	private MessageCoverageReport ParseMessageCoverageReport( Node messageNode, Map<Long, TargetGroupCoverageReport> targetGroups )
	{
		MessageCoverageReport mcr = new MessageCoverageReport();
		
		long contentObjectId =  Long.parseLong(getAttribute( messageNode, "refid"));
		long attempts = Long.parseLong(getAttribute( messageNode, "attempts"));
		long qualified = Long.parseLong(getAttribute( messageNode, "qualified"));
		long disqualified = Long.parseLong(getAttribute( messageNode, "disqualified"));
		
		mcr.setContentObjectId(contentObjectId);
		mcr.setAttempts(attempts);
		mcr.setQualified(qualified);
		mcr.setDisqualified(disqualified);
		
		for( Node targetGroupNode : getNode(messageNode.getChildNodes(), "TargetGroup") )
		{
			Long tgId = Long.parseLong(getAttribute( targetGroupNode, "refid"));
			boolean isExclude = getAttribute(targetGroupNode, "isexclude").equals("true");

			TargetGroupCoverageReport tgcr = targetGroups.get(tgId);
			TargetGroupCoverageReport msgTgcr = tgcr.deepCopy();

			msgTgcr.setModelId(contentObjectId);
			msgTgcr.setExclude(isExclude);

			mcr.getTargetGroupCoverageReports().add(msgTgcr);
		}
		
		return mcr;
	}
	
	private TargetGroupCoverageReport ParseTargetGroupCoverageReport( Node targetGroupNode )
	{
		TargetGroupCoverageReport tgcr = new TargetGroupCoverageReport();

		long groupId =  Long.parseLong(getAttribute( targetGroupNode, "refid"));
		long attempts =  Long.parseLong(getAttribute( targetGroupNode, "attempts"));
		long passed =  Long.parseLong(getAttribute( targetGroupNode, "passed"));
		boolean isExclude = Boolean.parseBoolean(getAttribute(targetGroupNode, "exclude"));
		boolean isOrRules = Boolean.parseBoolean(getAttribute(targetGroupNode, "or", "false"));

		tgcr.setTargetGroupId(groupId);
		tgcr.setAttempts(attempts);
		tgcr.setPasses(passed);
		tgcr.setExclude(isExclude);
		tgcr.setOrRules(isOrRules);
		
		for( Node ruleNode : getNode(targetGroupNode.getChildNodes(), "Rule") )
		{
			RuleCoverageReport ruleCovRep = ParseRuleCoverageReport( ruleNode );
			tgcr.getRuleCoverageReports().add(ruleCovRep);
			ruleCovRep.setTargetGroupCoverageReport(tgcr);
		}
		
		return tgcr;
	}

	private RuleCoverageReport ParseRuleCoverageReport( Node ruleNode )
	{
		RuleCoverageReport rcr = new RuleCoverageReport();

		long ruleId =  Long.parseLong(getAttribute( ruleNode, "refid"));
		long attempts =  Long.parseLong(getAttribute( ruleNode, "attempts"));
		long passed =  Long.parseLong(getAttribute( ruleNode, "passed"));
		boolean isOr = Boolean.parseBoolean(getAttribute( ruleNode, "or"));

		rcr.setRuleInstanceId(ruleId);
		rcr.setAttempts(attempts);
		rcr.setPasses(passed);
		rcr.setOrConditions(isOr);
		
		for( Node condNode : getNode(ruleNode.getChildNodes(), "Condition") )
		{
			ConditionCoverageReport condCovRep = ParseConditionCoverageReport( condNode );
			rcr.getConditionCoverageReports().add(condCovRep);
			condCovRep.setRuleCoverageReport(rcr);
		}

		return rcr;
	}

	private ConditionCoverageReport ParseConditionCoverageReport( Node condNode )
	{
		ConditionCoverageReport ccr = new ConditionCoverageReport();
		
		long condId =  Long.parseLong(getAttribute( condNode, "refid"));
		long attempts =  Long.parseLong(getAttribute( condNode, "attempts"));
		long passed =  Long.parseLong(getAttribute( condNode, "passed"));
		long dataGroupId = Long.parseLong(getAttribute( condNode, "datagroupid"));

		ccr.setConditionInstanceId(condId);
		ccr.setAttempts(attempts);
		ccr.setPasses(passed);
		ccr.setDataGroupId(dataGroupId);

		return ccr;
	}
	
	private void ParseSegmentationReport( Node jobNode, Long jobId )
	{
		// Unimplemented for now
	}
}
