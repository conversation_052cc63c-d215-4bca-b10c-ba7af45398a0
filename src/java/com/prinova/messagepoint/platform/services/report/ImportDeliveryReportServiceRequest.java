package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.job.JobZipFile;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ImportDeliveryReportServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = -7722481324001469225L;
	private JobZipFile jobBundle;
	public JobZipFile getJobBundle() {
		return jobBundle;
	}
	public void setJobBundle(JobZipFile jobBundle) {
		this.jobBundle = jobBundle;
	}
	

}
