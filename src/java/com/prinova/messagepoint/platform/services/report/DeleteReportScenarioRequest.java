package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class DeleteReportScenarioRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -1309370123056464013L;
	private Long reportScenarioId;
	private Long userId;
	private User user;	
	private Class<? extends AbstractReportScenario> clazz;

	public Long getReportScenarioId(){
		return reportScenarioId;
	}
	
	public void setReportScenarioId(Long reportScenarioId){
		this.reportScenarioId = reportScenarioId;
	}
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	/*
	 * Lazy getter to get the default user by id
	 */
	public User getUser() {
		if (user == null) {
			this.user = User.findById(getUserId());
		}
		return this.user;
	}
	
	public void setUser (User user) {
		this.user = user;
	}	
	
	public Class<? extends AbstractReportScenario> getClazz(){
		return clazz;
	}
	public void setClazz(Class<? extends AbstractReportScenario> clazz){
		this.clazz = clazz;
	}	
}
