package com.prinova.messagepoint.platform.services.report;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.model.scenario.OperationsReportScenario;
import com.prinova.messagepoint.model.scenario.ReportScenario;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.UserUtil;

public class BulkDeleteReportScenarioService extends AbstractService {
	public static final String SERVICE_NAME = "report.BulkDeleteReportScenarioService";
	private static final Log log = LogUtil.getLog(BulkDeleteReportScenarioService.class);

	public void execute(ServiceExecutionContext context) {
		BulkReportScenarioActionServiceRequest request = (BulkReportScenarioActionServiceRequest) context.getRequest();
		List<? extends AbstractReportScenario> reportList = request.getReportScenarioList();
		if(reportList == null || reportList.isEmpty()){
			return;
		}
		try {
			validate(context);
			if(hasValidationError(context)){
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			for(AbstractReportScenario report: reportList){
				ServiceExecutionContext context2;
				if(report instanceof OperationsReportScenario){
					context2 = DeleteReportScenarioService.createContext(OperationsReportScenario.class, report.getId(), requestor.getId());
				}else if(report instanceof TpDeliveryReportScenario){
					context2 = DeleteReportScenarioService.createContext(TpDeliveryReportScenario.class, report.getId(), requestor.getId());
				}else{
					context2 = DeleteReportScenarioService.createContext(ReportScenario.class, report.getId(), requestor.getId());
				}
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(DeleteReportScenarioService.SERVICE_NAME, DeleteReportScenarioService.class);
				service.execute(context2);
				if(!context2.getResponse().isSuccessful()){
					context2.getResponse().mergeResultMessages(context2.getResponse());
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkDeleteReportScenarioService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}	
	}

	public void validate(ServiceExecutionContext context) {		
	}
	
	public static SimpleExecutionContext createContext(List<? extends AbstractReportScenario> reportList, Class<? extends AbstractReportScenario> clazz) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		BulkReportScenarioActionServiceRequest request = new BulkReportScenarioActionServiceRequest();
		context.setRequest(request);
		
		request.setReportScenarioList(reportList);
		request.setClazz(clazz);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	
}
