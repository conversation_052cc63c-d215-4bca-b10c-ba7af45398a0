package com.prinova.messagepoint.platform.services.report;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import com.prinova.messagepoint.model.simulation.SimulationCoverageReport;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SystemState;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.model.simulation.SimulationReport;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;

import java.util.Date;

public class ImportSimulationStatsService extends AbstractService 
{
	public static final String SERVICE_NAME = "report.ImportSimulationStatsService";
	private static final Log log = LogUtil.getLog(ImportSimulationStatsService.class);
	
	public static ServiceExecutionContext createContext(File simReportFile, long jobid, Locale locale, boolean updateJobStatus)
	{
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ImportSimulationStatsServiceRequest request = new ImportSimulationStatsServiceRequest();
		context.setRequest(request);

		request.setSimulationReportFile(simReportFile);
		request.setJobid(jobid);
		request.setUpdateJobStatus(updateJobStatus);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		if (locale == null) {
			context.setLocale(new Locale(MessagepointLocale.getDefaultSystemLanguageCode()));
		}

		return context;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public void execute(ServiceExecutionContext context) 
	{
		validate(context);
		if (hasValidationError(context)) {
			return;
		}
		
		try
		{
			ImportSimulationStatsServiceRequest request = (ImportSimulationStatsServiceRequest) context.getRequest();
			long jobId = request.getJobid();
			File simReportFile = request.getSimulationReportFile();

			// Delete any existing simulation reports for the this job 
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("jobId", jobId));
			HibernateUtil.getManager().deleteObjects(SimulationCoverageReport.class, critList);

			// parse report and save to database.
			SimulationReportParser parser = new SimulationReportParser();
			SimulationReport sr = parser.ParseReportFile(simReportFile);
			
			HibernateUtil.getManager().saveObject(sr,true);
			
			// now update the simulation completed time
			Job job = HibernateUtil.getManager().getObject(Job.class, jobId);
			if ( job != null )
			{
				critList.clear();
				critList.add(MessagepointRestrictions.eq("job", job));
				List<DeliveryEvent> events = HibernateUtil.getManager().getObjectsAdvanced(DeliveryEvent.class, critList);
				if ( events != null && !events.isEmpty())
				{
					DeliveryEvent de = events.get(0);
					if (request.isUpdateJobStatus() == true) {
						de.getJob().setStatus(SystemState.STATUS_TYPE_COMPLETED);
					}
					Simulation sim = (Simulation) de.getItem();
					if ( sim != null )
					{
						sim.setComplete(true);
						Date now = new Date();
						sim.setCompletedDate(now);
						HibernateUtil.getManager().saveObject(sim,false);
					}
				}
			}
		}
		catch( Exception e )
		{
			log.error("ImportSimulationStatsServer caught error: ", e);
			
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	public void validate(ServiceExecutionContext context) 
	{
	}
}
