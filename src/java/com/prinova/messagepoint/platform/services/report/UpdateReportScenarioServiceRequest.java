package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateReportScenarioServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -615574433190470035L;

	private  AbstractReportScenario reportScenario;

	public AbstractReportScenario getReportScenario() {
		return reportScenario;
	}
	public void setReportScenario(AbstractReportScenario reportScenario) {
		this.reportScenario = reportScenario;
	}
}
