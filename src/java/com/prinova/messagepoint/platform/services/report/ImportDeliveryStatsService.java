package com.prinova.messagepoint.platform.services.report;

import java.util.Locale;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.LogUtil;

public class ImportDeliveryStatsService extends AbstractService {
	public static final String SERVICE_NAME = "report.ImportDeliveryStatsService";
	private static final Log log = LogUtil.getLog(ImportDeliveryStatsService.class);

	public static ServiceExecutionContext createContext(
			SplitReportFile combinedMsgDeliveryFile, 
			long jobid, 
			int partid,
			String transactionId,
			Locale locale,
			boolean updateJobStatus)
	{
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ImportDeliveryStatsServiceRequest request = new ImportDeliveryStatsServiceRequest();
		context.setRequest(request);

		request.setCombinedMsgDeliveryFile(combinedMsgDeliveryFile);
		request.setJobid(jobid);
		request.setPartid(partid);
		request.setTransactionId(transactionId);
		request.setUpdateJobStatus(updateJobStatus);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		if (locale == null) {
			context.setLocale(new Locale(MessagepointLocale.getDefaultSystemLanguageCode()));
		}

		return context;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public void execute(ServiceExecutionContext context) 
	{
		validate(context);
		if (hasValidationError(context)) {
			return;
		}

		ImportDeliveryStatsWorker worker = new ImportDeliveryStatsWorker(context);
		try
		{
			worker.run();
		
		} catch (Exception e) 
		{
			log.error(" unexpected exception when invoking " + SERVICE_NAME + " execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
		}
	}

	public void validate(ServiceExecutionContext context) 
	{
	}
}
