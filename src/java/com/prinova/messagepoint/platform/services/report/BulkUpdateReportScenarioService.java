package com.prinova.messagepoint.platform.services.report;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class BulkUpdateReportScenarioService extends AbstractService {
	public static final String SERVICE_NAME = "report.BulkUpdateReportScenarioService";
	private static final Log log = LogUtil.getLog(BulkUpdateReportScenarioService.class);

	public void execute(ServiceExecutionContext context) {
		BulkReportScenarioActionServiceRequest request = (BulkReportScenarioActionServiceRequest) context.getRequest();
		List<? extends AbstractReportScenario> reportList = request.getReportScenarioList();
		if(reportList == null || reportList.isEmpty()){
			return;
		}
		try {
			validate(context);
			if(hasValidationError(context)){
				return;
			}
			for(AbstractReportScenario report: reportList){
				ServiceExecutionContext context2;
				context2 = UpdateReportScenarioService.createContext(report);
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(UpdateReportScenarioService.SERVICE_NAME, UpdateReportScenarioService.class);
				service.execute(context2);
				if(!context2.getResponse().isSuccessful()){
					context2.getResponse().mergeResultMessages(context2.getResponse());
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkUpdateReportScenarioService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}	
	}

	public void validate(ServiceExecutionContext context) {		
	}
	
	public static SimpleExecutionContext createContext(List<? extends AbstractReportScenario> reportList, Class<? extends AbstractReportScenario> clazz) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		BulkReportScenarioActionServiceRequest request = new BulkReportScenarioActionServiceRequest();
		context.setRequest(request);
		
		request.setReportScenarioList(reportList);
		request.setClazz(clazz);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	
}
