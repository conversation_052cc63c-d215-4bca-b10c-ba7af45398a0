package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ImportInsertStatsServiceRequest implements ServiceRequest
{
	private static final long serialVersionUID = -8451489303012938082L;

	private SplitReportFile CombinedInsDeliveryFile;
	private long jobId;
	private int partId;
	
	public SplitReportFile getCombinedInsDeliveryFile()
	{
		return CombinedInsDeliveryFile;
	}
	public void setCombinedInsDeliveryFile(SplitReportFile combinedInsDeliveryFile)
	{
		CombinedInsDeliveryFile = combinedInsDeliveryFile;
	}
	
	public long getJobId()
	{
		return jobId;
	}
	public void setJobId(long jobid)
	{
		this.jobId = jobid;
	}

	public int getPartId()
	{
		return partId;
	}
	public void setPartId(int partid)
	{
		this.partId = partid;
	}

}
