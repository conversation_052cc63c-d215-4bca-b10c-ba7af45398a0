package com.prinova.messagepoint.platform.services.report;

import java.io.File;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ImportSimulationStatsServiceRequest implements ServiceRequest
{
	private static final long serialVersionUID = 1L;

	private File simulationReportFile;
	private long jobid;
	private boolean updateJobStatus = false;

	public File getSimulationReportFile() {
		return simulationReportFile;
	}
	public void setSimulationReportFile(File simulationReportFile) {
		this.simulationReportFile = simulationReportFile;
	}

	public long getJobid() {
		return jobid;
	}
	public void setJobid(long jobid) {
		this.jobid = jobid;
	}
	public boolean isUpdateJobStatus() {
		return updateJobStatus;
	}
	public void setUpdateJobStatus(boolean updateJobStatus) {
		this.updateJobStatus = updateJobStatus;
	}
}