package com.prinova.messagepoint.platform.services.report;

import java.io.PrintWriter;
import java.io.StringWriter;

import org.apache.commons.logging.Log;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.reports.processor.ScenarioProcessor;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class UpdateReportScenarioService extends AbstractService {

	public static final String SERVICE_NAME = "report.UpdateReportScenarioService";
	private static final Log log = LogUtil.getLog(UpdateReportScenarioService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateReportScenarioServiceRequest request = (UpdateReportScenarioServiceRequest) context.getRequest();

			AbstractReportScenario reportScenario = request.getReportScenario();
			reportScenario.reset();
			// HibernateUtil.getManager().saveObject(reportScenario);

			ScenarioRunner sr = new ScenarioRunner(reportScenario);
			MessagePointRunnableUtil.startThreadAndWait(sr);

	    	// Audit (Execute: Report)
	    	AuditEventUtil.push(AuditEventType.ID_EXECUTE, AuditObjectType.ID_REPORT, reportScenario.getName(), reportScenario.getId(), AuditActionType.ID_REPORT_RUN, null);			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateReportScenarioService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private static class ScenarioRunner extends MessagePointRunnable {

		AbstractReportScenario reportScenario;
		
		//pass the securitycontext to scenariorunner
		//when the runner starts in a new thread
		SecurityContext context;

		public ScenarioRunner(AbstractReportScenario rs) {
			reportScenario = rs;
			context = SecurityContextHolder.getContext();
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			try {
				SecurityContextHolder.setContext(context);
				SimpleExecutionContext context = new SimpleExecutionContext();
				context.setServiceName(ScenarioProcessor.SERVICE_NAME);
				context.setServiceProperty("scenario", reportScenario);
				Service service = MessagepointServiceFactory.getInstance().lookupService(ScenarioProcessor.SERVICE_NAME, ScenarioProcessor.class);
				service.execute(context);
				if (context.getResponse().isSuccessful()) {
					setReportScenario((AbstractReportScenario) context.getResponse().getResultValueBean());
				}

			} catch (Throwable t) {
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				t.printStackTrace(pw);
				log.error("Scenario Processor caught exception: " + sw);
			}
		}

		public AbstractReportScenario getReportScenario() {
			return reportScenario;
		}

		public void setReportScenario(AbstractReportScenario scenario) {
			this.reportScenario = scenario;
		}

	}

	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(AbstractReportScenario reportScenario) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateReportScenarioServiceRequest request = new UpdateReportScenarioServiceRequest();
		context.setRequest(request);

		request.setReportScenario(reportScenario);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
