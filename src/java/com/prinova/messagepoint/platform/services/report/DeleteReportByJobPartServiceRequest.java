package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.platform.services.ServiceRequest;

public class DeleteReportByJobPartServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -3078739030545009588L;
	private long jobId;
	private int partId;
	private String transactionId;

	public long getJobId() {
		return jobId;
	}
	public void setJobId(long jobId) {
		this.jobId = jobId;
	}

	public int getPartId() {
		return partId;
	}
	public void setPartId(int partId) {
		this.partId = partId;
	}
	
	public String getTransactionId() {
		return transactionId;
	}
	public void setTransactionId(String id) {
		transactionId = id;
	}

}
