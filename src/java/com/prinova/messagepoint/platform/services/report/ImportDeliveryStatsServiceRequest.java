package com.prinova.messagepoint.platform.services.report;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ImportDeliveryStatsServiceRequest implements ServiceRequest
{
	private static final long serialVersionUID = 4698415038616129299L;
	private SplitReportFile CombinedMsgDeliveryFile;
	private long jobid;
	private int partid;
	String transactionId;
	private boolean firstFile = false;
	private boolean updateJobStatus = false;
	
	public SplitReportFile getCombinedMsgDeliveryFile() {
		return CombinedMsgDeliveryFile;
	}
	public void setCombinedMsgDeliveryFile(SplitReportFile combinedMsgDeliveryFile) {
		CombinedMsgDeliveryFile = combinedMsgDeliveryFile;
	}
	
	public long getJobid() {
		return jobid;
	}
	public void setJobid(long jobid) {
		this.jobid = jobid;
	}
	
	public int getPartid() {
		return partid;
	}
	public void setPartid(int partid) {
		this.partid = partid;
	}
	
	public String getTransactionId()
	{ 
		return transactionId;
	}
	public void setTransactionId(String val)
	{
		transactionId = val;
	}
	
	public boolean isFirstFile()
	{
		return firstFile;
	}
	public void setFirstFile( boolean value )
	{
		firstFile = value;
	}

	public boolean isUpdateJobStatus() {
		return updateJobStatus;
	}
	public void setUpdateJobStatus(boolean updateJobStatus) {
		this.updateJobStatus = updateJobStatus;
	}
}