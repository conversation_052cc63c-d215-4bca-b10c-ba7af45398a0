package com.prinova.messagepoint.platform.services.report;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.model.admin.EventType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.reports.model.report.BatchOperationStatistics;
import com.prinova.messagepoint.reports.model.report.insert.BatchStatistics;
import com.prinova.messagepoint.reports.model.report.insert.JobInsert;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertRecipient;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertSchedule;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertScheduleStatistic;
import com.prinova.messagepoint.reports.model.report.insert.JobInsertStatistic;
import com.prinova.messagepoint.reports.model.report.insert.JobRateSchedule;
import com.prinova.messagepoint.reports.model.report.insert.JobRateScheduleStatistic;
import com.prinova.messagepoint.util.HibernateUtil;

public class DeleteReportByJobPartService extends AbstractService {
	public static final String SERVICE_NAME = "report.DeleteReportByJobPartService";
	private static final Log log = LogUtil.getLog(DeleteReportByJobPartService.class);
	
	public static ServiceExecutionContext createContext(long jobId, int partId, String transactionId, Locale locale) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		DeleteReportByJobPartServiceRequest request = new DeleteReportByJobPartServiceRequest();
		context.setRequest(request);

		request.setJobId(jobId);
		request.setPartId(partId);
		request.setTransactionId(transactionId);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		if (locale == null) {
			context.setLocale(new Locale(MessagepointLocale.getDefaultSystemLanguageCode()));
		}

		return context;
	}

	@Transactional (propagation=Propagation.REQUIRED, readOnly=false)
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			DeleteReportByJobPartServiceRequest request = (DeleteReportByJobPartServiceRequest) context.getRequest();
			long jobId = request.getJobId();
			int partId = (int) request.getPartId();
			long batchId = request.getPartId();
			String transactionId = request.getTransactionId();
			
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("jobId", jobId));
			critList.add(MessagepointRestrictions.eq("partId", partId));
			critList.add(MessagepointRestrictions.eq("transactionId", transactionId));

			HibernateObjectManager hom = HibernateUtil.getManager();
			
			LogUtil.getLog(this.getClass()).info("Deleting data for job " + jobId + " batch " + partId);
			Session session = hom.getSession();
			NativeQuery deleteReportPs = session.createNativeQuery("DELETE FROM report where job_id = " + jobId + " and part_id = " + partId + " and transaction_id = '" + transactionId + "'");
			NativeQuery deleteCustomerPs = session.createNativeQuery("DELETE FROM customer where job_id = " + jobId + " and part_id = " + partId + " and transaction_id = '" + transactionId + "'");
			NativeQuery deleteCustomerFieldPs = session.createNativeQuery("DELETE FROM customer_field where job_id = " + jobId + " and part_id = " + partId + " and transaction_id = '" + transactionId + "'");
			NativeQuery deleteVariablePs = session.createNativeQuery("DELETE FROM executed_variable where job_id = " + jobId + " and part_id = " + partId + " and transaction_id = '" + transactionId + "'");
			NativeQuery deleteGraphicPs = session.createNativeQuery("DELETE FROM report_graphic where job_id = " + jobId + " and batch_id = " + partId + " and transaction_id = '" + transactionId + "'");
			NativeQuery deleteJobStatisticsVariant = session.createNativeQuery("DELETE FROM job_statistics_variant where job_id = " + jobId + " and part_id = " + partId + " and transaction_id = '" + transactionId + "'");

			deleteCustomerFieldPs.executeUpdate();
			deleteCustomerPs.executeUpdate();
			deleteVariablePs.executeUpdate();
			deleteReportPs.executeUpdate();
			deleteGraphicPs.executeUpdate();
			deleteJobStatisticsVariant.executeUpdate();
			
			Job j = hom.getObject(Job.class, jobId);

			critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.eq("jobId", jobId));

			// delete insert reporting data
			if ( partId == 1 || 
				 j.getEventTypeId() != EventType.TYPE_PRODUCTION ) 
			{
				// These objects are created at the job level, and should only be removed
				// when appropriate
				hom.deleteObjects(JobInsert.class, critList);
				hom.deleteObjects(JobInsertSchedule.class, critList);
				hom.deleteObjects(JobRateSchedule.class, critList);
			}
						
			// locate the batch statistics that the stats are based on
			critList.add(MessagepointRestrictions.eq("batchId", batchId));

			hom.deleteObjects(BatchOperationStatistics.class, critList);
			BatchStatistics batchStat = hom.getObjectUnique(BatchStatistics.class, critList);
			
			if ( batchStat != null )
			{		
				List<MessagepointCriterion> critList2 = new ArrayList<>();
				critList2.add(MessagepointRestrictions.eq("batchStatistics", batchStat));

				hom.deleteObjects(JobInsertRecipient.class, critList2);
				hom.deleteObjects(JobInsertStatistic.class, critList2);
				hom.deleteObjects(JobInsertScheduleStatistic.class, critList2);
				hom.deleteObjects(JobRateScheduleStatistic.class, critList2);
			
				hom.deleteObject(batchStat);
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking " + SERVICE_NAME + " execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {

	}
}
