package com.prinova.messagepoint.platform.services.report;

import java.util.Locale;

import org.apache.commons.logging.Log;

import com.prinova.messagepoint.batch.SplitReportFile;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.reports.InsertReportParser;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;

public class ImportInsertDeliveryStatsService extends AbstractService
{
	public static final String SERVICE_NAME = "report.ImportInsertDeliveryStatsService";
	private static final Log log = LogUtil.getLog(ImportInsertDeliveryStatsService.class);
	public static final String STRING_NO_MESSAGES_ERROR = "NO MESSAGES";
	
	public void execute(ServiceExecutionContext context)
	{
		validate(context);
		if (hasValidationError(context)) 
		{
			return;
		}
		
		try {
			ImportInsertStatsServiceRequest request = (ImportInsertStatsServiceRequest) context.getRequest();

			SplitReportFile reader = request.getCombinedInsDeliveryFile();
			Job job = HibernateUtil.getManager().getObject(Job.class, request.getJobId());

			InsertReportParser irp = new InsertReportParser(request.getJobId(), request.getPartId(), reader);
			
			while( !irp.isDone() )
			{
				Object o = irp.parse();
				if ( o != null )
					HibernateUtil.getManager().saveObject(o, true);
			}
			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking " + SERVICE_NAME + " execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}	
	}

	public void validate(ServiceExecutionContext context)
	{
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext( 
			SplitReportFile combinedInsDeliveryFile, 
			long jobid, 
			int partid, 
			Locale locale )
	{
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ImportInsertStatsServiceRequest request = new ImportInsertStatsServiceRequest();
		context.setRequest(request);

		request.setCombinedInsDeliveryFile(combinedInsDeliveryFile);
		request.setJobId(jobid);
		request.setPartId(partid);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		if (locale == null) 
		{
			context.setLocale(new Locale(MessagepointLocale.getDefaultSystemLanguageCode()));
		}

		return context;
	}	
}
