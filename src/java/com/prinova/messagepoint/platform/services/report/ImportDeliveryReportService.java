package com.prinova.messagepoint.platform.services.report;

import java.io.File;
import java.util.List;
import java.util.Locale;

import com.prinova.messagepoint.connected.util.SupportingDataUtil;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.interceptor.jobs.JobCompletedEvent;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;

import com.prinova.messagepoint.job.JobZipFile;
import com.prinova.messagepoint.model.DeliverableMessagePointModel;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentProductionEvent;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SystemState;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.job.UpdateDeliveryEventStatusService;
import com.prinova.messagepoint.reports.DeliveryEventReportParser;
import com.prinova.messagepoint.reports.model.JobMetadataDetail;
import com.prinova.messagepoint.reports.model.ReportMetaDataXMLParser;
import com.prinova.messagepoint.util.HibernateUtil;

public class ImportDeliveryReportService extends AbstractService implements ApplicationEventPublisherAware{

	public static final String SERVICE_NAME = "report.ImportDeliveryReportService";
	private static final Log log = LogUtil.getLog(ImportDeliveryReportService.class);
	
	private ApplicationEventPublisher eventPublisher;
	
	public void execute(ServiceExecutionContext context)
	{
		DeliveryEvent deliveryEvent = null;
		JobZipFile jobBundle = null;
		try {
			ImportDeliveryReportServiceRequest request = (ImportDeliveryReportServiceRequest) context.getRequest();
			jobBundle = request.getJobBundle();
			File deliveryReport = jobBundle.getOutputDeliveryReportFile();
			deliveryEvent = (DeliveryEvent) ((SimpleExecutionContext) context).getServiceProperty("DELIVERY_EVENT");

			saveJobMetaDataDetails(deliveryReport);
			markJobComplete(jobBundle, deliveryEvent);

		} catch (Exception e) {
			markJobErrorStatus(jobBundle);
			log.error(" unexpected exception when invoking " + SERVICE_NAME + " execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		DeliveryEvent deliveryEvent = null;
		JobZipFile jobBundle = null;

		try {
			ImportDeliveryReportServiceRequest request = (ImportDeliveryReportServiceRequest) context.getRequest();
			jobBundle = request.getJobBundle();

			/**
			 * Error if the job bundle does not have delivery report
			 */
			if (jobBundle.getOutputDeliveryReportFile() == null) {
				this.getResponse(context).addErrorMessage(" job id = " + jobBundle.getJob().getId(),
						ApplicationErrorMessages.IDR_JOB_BUNDLE_DOES_NOT_CONTAIN_DELIVERY_REPORT,
						"",
						context.getLocale());
				return;
			}
			/**
			 * Error if delivery event is not included in the bundle
			 * 
			 * Note: the parse process might save the job or the deliveryEvent.item. So need to check if they are marked
			 * in error.
			 */
			deliveryEvent = (new DeliveryEventReportParser()).parse(jobBundle.getOutputDeliveryReportFile(),
					jobBundle.getPartNumber());
			if (deliveryEvent == null) {
				if (jobBundle.getOutputDeliveryReportFile() == null) {
					this.getResponse(context).addErrorMessage(" job id = " + jobBundle.getJob().getId(),
							ApplicationErrorMessages.IDR_JOB_BUNDLE_DOES_NOT_CONTAIN_DELIVERY_EVENT,
							"",
							context.getLocale());
					return;
				}
			}

/*
			if (deliveryEvent.getJob().getStatus().getId() == SystemState.STATUS_TYPE_ERROR
					|| deliveryEvent.getItem().isError()) {
				this.getResponse(context).addErrorMessage(" job id = " + jobBundle.getJob().getId(),
						ApplicationErrorMessages.IDR_DELIVERY_EVENT_REPORT_PARSER_ERROR,
						"",
						context.getLocale());
				return;
			}
*/
			/**
			 * Error if the delivery event job id does not match that of the job bundle
			 */
			if (deliveryEvent.getJob().getId() != jobBundle.getJob().getId()) {
				if (jobBundle.getOutputDeliveryReportFile() == null) {
					this.getResponse(context).addErrorMessage(" job bundle id = " + jobBundle.getJob().getId()
							+ " delivery event job id = " + deliveryEvent.getJob().getId(),
							ApplicationErrorMessages.IDR_JOB_BUNDLE_ID_DOES_NOT_MATCH_DELIVERY_EVENT_JOB_ID,
							"",
							context.getLocale());
					return;
				}
			}

			/***********************************************************************************************************
			 * Add deliveryEvent to the execution context so the execute method can get it from there
			 */
			((SimpleExecutionContext) context).setServiceProperty("DELIVERY_EVENT", deliveryEvent);

		} catch (Exception e) {
			markJobErrorStatus(jobBundle);
			log.error(" unexpected exception when invoking " + SERVICE_NAME + " validate method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
		}
	}

	public static ServiceExecutionContext createContext(JobZipFile jobBundle, Locale locale) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ImportDeliveryReportServiceRequest request = new ImportDeliveryReportServiceRequest();
		context.setRequest(request);

		request.setJobBundle(jobBundle);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		if (locale == null) {
			context.setLocale(new Locale(MessagepointLocale.getDefaultSystemLanguageCode()));
		}

		return context;
	}

	private void saveJobMetaDataDetails(File deliveryReport) throws Exception {
		ReportMetaDataXMLParser parser = new ReportMetaDataXMLParser(deliveryReport);
		List<JobMetadataDetail> details = parser.getJobMetadataDetails();
		HibernateUtil.getManager().saveObjects(details.toArray());
	}

	private void markJobErrorStatus(JobZipFile jobBundle) 
	{
		Job job = jobBundle.getJob();

		if (job != null && !job.isError()) {
			job.setStatus(SystemState.STATUS_TYPE_ERROR);
			HibernateUtil.getManager().saveObject(job);
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDeliveryEventStatusService.SERVICE_NAME, UpdateDeliveryEventStatusService.class);
			ServiceExecutionContext updateContext = UpdateDeliveryEventStatusService.createContext(job.getEventId(), SystemState.STATUS_TYPE_ERROR, 1L);
			service.execute(updateContext);
		}
	}

	private void markJobComplete(JobZipFile jobBundle, DeliveryEvent deliveryEvent) {
	    // Fetch the delivery event from the database again.  It seems that on the "quick round trip"
	    // that the model's event item is not synchronized with the current session.  Don't
	    // know why.  This just masks the problem, but I don't know what else to do.
	    DeliveryEvent de = HibernateUtil.getManager().getObject(DeliveryEvent.class, deliveryEvent.getId());
	    DeliverableMessagePointModel model = de.getItem();
	    Job job = deliveryEvent.getJob();
	    
		/*model.isRequiresMessageDeliveryFile() && Note: its returned as false my DocumentPreview only*/
		if ( !job.isError() ){
			job.setStatus(SystemState.STATUS_TYPE_COMPLETED);
			HibernateUtil.getManager().saveObject(job);

			// after successful delivery, need to reset Document's tpContentChanged to false,
			// it will stay as false until next time any tp content changes
			if(model instanceof DocumentProductionEvent){
					if(((DocumentProductionEvent) model).isResetTPContentChangedFlag()){
						Document doc = Document.findById(model.getDocumentId());
						if (doc != null)
							doc.setTpContentChanged(false);
					}
			}
		}

		// only change the completion status if job doesn't have an external email proof configured
		if ( !model.hasExternalEmailProof() ) {
				model.setComplete(true);
		}

		if (model instanceof CommunicationProof) {
			if (((CommunicationProof) model).getDocument().isCommunicationUseBeta()) {
				SupportingDataUtil.logCommProofCompleted((CommunicationProof) model);
			}
		}

		HibernateUtil.getManager().saveObject(model);
		HibernateUtil.getManager().saveObject(deliveryEvent);
		
	    /***********************************************************************************************************
		 * Send a job completed application event for event listener to do the follow up jobs
		 **********************************************************************************************************/
		this.eventPublisher.publishEvent(new JobCompletedEvent(deliveryEvent));
	}
	
	@Override
	public void setApplicationEventPublisher(ApplicationEventPublisher eventPublisher) {
		this.eventPublisher = eventPublisher;
	}
}
