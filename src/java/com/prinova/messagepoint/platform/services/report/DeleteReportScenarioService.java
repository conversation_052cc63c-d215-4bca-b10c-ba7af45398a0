package com.prinova.messagepoint.platform.services.report;

import java.io.File;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.model.scenario.OperationsReportScenario;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class DeleteReportScenarioService extends AbstractService {
	public static final String SERVICE_NAME = "report.DeleteReportScenarioService";
	
	public void execute(ServiceExecutionContext context) {
		validate(context);
		if(hasValidationError(context)){
			return;
		}
		
		// Find the report scenario by id
		DeleteReportScenarioRequest request = (DeleteReportScenarioRequest)context.getRequest();
		AbstractReportScenario report = HibernateUtil.getManager().getObject(request.getClazz(), getReportScenarioId(context));
		
		// Clean the folder for the report scenario
		if(report instanceof OperationsReportScenario){
			OperationsReportScenario operationsReport = (OperationsReportScenario)report;
			String xmlReportPath = operationsReport.getXMLPath();
			if(xmlReportPath != null){
				File reportFolder = new File(xmlReportPath).getParentFile();
				FileUtil.deleteDir(reportFolder);
			}
		}else if(report instanceof TpDeliveryReportScenario){
			TpDeliveryReportScenario tpDeliveryReport = (TpDeliveryReportScenario)report;
			String xmlReportPath = tpDeliveryReport.getXMLPath();
			if(xmlReportPath != null){
				File reportFolder = new File(xmlReportPath).getParentFile();
				FileUtil.deleteDir(reportFolder);
			}			
		}
		// Delete the report scenario
		try {
			if (report != null) {
				HibernateUtil.getManager().sessionSafeDelete(report);
			}
		} catch (Exception e) {
			context.getResponse().addErrorMessage(String.valueOf(getReportScenarioId(context)),
					ApplicationErrorMessages.MESSAGE_DELETE_FAILED,
					"",
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}		
	}

	public void validate(ServiceExecutionContext context) {
	}
	
	public static SimpleExecutionContext createContext(Class<? extends AbstractReportScenario> clazz, long reportScenarioId, long userId) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(DeleteReportScenarioService.class.getName());
		context.setResponse(new SimpleServiceResponse());
		context.setRequest(DeleteReportScenarioService.createRequest(clazz, reportScenarioId, userId));
		return context;
	}
	
	public static DeleteReportScenarioRequest createRequest(Class<? extends AbstractReportScenario> clazz, long id, long userId) {
		DeleteReportScenarioRequest request = new DeleteReportScenarioRequest();
		request.setReportScenarioId(id);
		request.setUserId(Long.valueOf(userId));
		request.setClazz(clazz);
		return request;
	}
	
	private long getReportScenarioId(ServiceExecutionContext context){
		return ((DeleteReportScenarioRequest) context.getRequest()).getReportScenarioId();
	}

}
