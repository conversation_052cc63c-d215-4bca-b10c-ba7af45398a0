package com.prinova.messagepoint.platform.services.report;

import java.util.List;

import com.prinova.messagepoint.model.scenario.AbstractReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class BulkReportScenarioActionServiceRequest extends
		SimpleServiceRequest {
	
	private static final long serialVersionUID = -1309370123056464013L;
	private List<? extends AbstractReportScenario> reportList;
	private Class<? extends AbstractReportScenario> clazz;
	private User requestor;
	
	public List<? extends AbstractReportScenario> getReportScenarioList(){
		return reportList;
	}
	
	public void setReportScenarioList(List<? extends AbstractReportScenario> reportList){
		this.reportList = reportList;
	}		
	
	public User getRequestor() {
		return requestor;
	}

	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}	

	public Class<? extends AbstractReportScenario> getClazz(){
		return clazz;
	}
	public void setClazz(Class<? extends AbstractReportScenario> clazz){
		this.clazz = clazz;
	}	
}
