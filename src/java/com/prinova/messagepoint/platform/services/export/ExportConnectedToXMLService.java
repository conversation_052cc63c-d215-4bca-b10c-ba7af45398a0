package com.prinova.messagepoint.platform.services.export;

import java.util.*;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ApprovalType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionHistory;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionStateType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowApprovalDetail;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

public class ExportConnectedToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportConnectedToXMLService";
	private static final Log log = LogUtil.getLog(ExportConnectedToXMLService.class);

    private User requestor;
	private long reportTimestamp;
	private List<Long> list;
	
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			GenerateAuditReportServiceRequest request = (GenerateAuditReportServiceRequest) context.getRequest();
	    	requestor = request.getRequestor();
	    	setList(request.getList());
	    	org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			try{
				generateXML(request, document);
				String filePath = ExportUtil.saveXMLToFile(document, requestor, AuditReportType.ID_CONNECTED_REPORT, reportTimestamp);
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);
			}catch (Exception e){
				this.getResponse(context).addErrorMessage(
				        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
						context.getLocale() );
				
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(null);
			}
		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportConnectedToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	private org.dom4j.Document initializeWriter( )
	throws Exception 
	{
		return DocumentHelper.createDocument();
	}

	private void generateXML(GenerateAuditReportServiceRequest request,org.dom4j.Document document) throws Exception 
	{
		requestor = User.findById(requestor.getId());
		
		Element auditElm = document.addElement("ConnectedAudit");
		auditElm.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		buildMetaDataElm(auditElm, requestor);
		
		if ( !list.isEmpty() && !list.isEmpty())
		{
			Element connectedsElement = auditElm.addElement("Orders");
			for(Long instanceId:list){
				Communication instance = Communication.findById(instanceId);
				Element connectedElement = connectedsElement.addElement("Order");
				createCommunicationTag(instance, connectedElement);
			}
		}
	}

	/**
	 * Build the "Metadata" element
     */
	private void buildMetaDataElm(Element parentElm, User requestor){
		// Build the "Metadata" element
		Element metadataElm = DocumentHelper.createElement("Metadata");
		parentElm.add(metadataElm);
		// - Build the "User" element
		metadataElm.add(DocumentHelper.createElement("User").addText(requestor.getName()));
		// - Build the "RequestDate" element
		metadataElm.add(DocumentHelper.createElement("RequestDate").addText(DateUtil.formatDateForXMLOutput(DateUtil.now())));

		Element intanceNameElement = metadataElm.addElement("InstanceName");
		intanceNameElement.addText(Node.getCurrentNodeName());

		Element systemDefaultLocaleElement = metadataElm.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		metadataElm.add(DocumentHelper.createElement("ReportType").addText("Connected Audit Report"));
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, Communication instance, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			Element descriptElm = verHisElm.addElement("Descript");
			if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
				User user = User.findById(actionHis.getUserId());
				if (user != null)
					descriptElm.addAttribute("user", user.getName());
				else
					descriptElm.addAttribute("user", "Uknown");
			}else{	// Auto approve user
				descriptElm.addAttribute("user", "System");
			}
			if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation

				descriptElm.addAttribute("action", "Activated");
			}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
				descriptElm.addAttribute("action", "Release for Approval");
			}else{
				if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
					descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}else{	// Auto approve user
					descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}
			}
			if(actionHis.getAssignedTo() != null){
				User user = User.findById(actionHis.getAssignedTo());
				if (user != null)
					descriptElm.addAttribute("assignedTo", user.getName());
			}
			if(actionHis.getNotes() != null){
				descriptElm.addAttribute("notes", actionHis.getNotes());
			}
			descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
			switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
						if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
							descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
						}else{	// All of
							if(!wfAction.isActionApproved()){
								descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
							}else{
								if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
									descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
								}else{							
									// If action is approved but the user is not the last to approve, stay in current state
									List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
									Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                        public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                            Date approvedDate1 = o1.getApprovedDate();
                                            Date approvedDate2 = o2.getApprovedDate();
                                            if (approvedDate1 == null && approvedDate2 == null) {
                                                return 0;
                                            } else if (approvedDate1 == null) {
                                                return 1;
                                            } else if (approvedDate2 == null) {
                                                return -1;
                                            } else {
                                                return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                            }
                                        }
                                    });
									if(!approvalDetailsSorted.isEmpty()){	// At least one approver
										for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
											ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
											if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
												continue;
											}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												break;
											}else{
												if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
													descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
												}else{
													descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												}
												break;
											}
										}
									}
								}
							}
						}	
					}
				}
			}
		}	
	}
	
	
	public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);

        context.setRequest(request);

        ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
	
	public static ServiceExecutionContext createContext(
			long auditReportId,
	        List<Long> list,
			User requestor)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateAuditReportServiceRequest request = new GenerateAuditReportServiceRequest();
		request.setList(list);
		request.setRequestor(requestor);

		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	
	private void createCommunicationTag(Communication instance, Element connectedElement)
	{
		connectedElement.addAttribute("id", Long.toString(instance.getId()));
		connectedElement.addAttribute("guid", instance.getGuid());
		
		
		connectedElement.addAttribute("nextaction", instance.getActionRequired());
		if(instance.isActive()){
			connectedElement.addAttribute("assignedto", "N/A");
		}else{
			connectedElement.addAttribute("assignedto", instance.getAssignedToUserName(true) != null?instance.getAssignedToUserName(true):"N/A");
		}

		connectedElement.addAttribute("defaultsystemlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
		connectedElement.addAttribute("defaultsystemlocale", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		connectedElement.addAttribute("created", DateUtil.formatDateForXMLOutput(instance.getCreated()));
		connectedElement.addAttribute("createdby", instance.getCreatedByName());
		ConfigurableWorkflowAction wfAction = instance.getWorkflowAction();
		ConfigurableWorkflowInstance wfInst = null;
		Date lastActivationDate = null;
		String finalApprover = "";
		if (instance.isActive()) {
			ConfigurableWorkflowAction action = instance.getWorkflowAction();
			if (action != null) {
				ConfigurableWorkflowApprovalDetail approvalDetail = action.getApprovalDetailByApproved();
				if (approvalDetail != null) {
					lastActivationDate = approvalDetail.getApprovedDate();
					finalApprover = approvalDetail.getUser().getFullName();
				}
			}
		}
		connectedElement.addAttribute("lastactivationdate", DateUtil.formatDateForXMLOutput(lastActivationDate));
		connectedElement.addAttribute("lastproductiondate", DateUtil.formatDateForXMLOutput(instance.getLastProductionDate()));

		ConfigurableWorkflow wf = instance.getQualifiedConfigurableWorkflow();

		if( wfAction != null ) {	// Touchpoint selection participate in previous workflow before.
			// Need to check whether the touchpoint selection has been rejected, if yes, this message need to ready for involving in the new workflow instance.
			if( wfAction.isActionRejected() ) {
				if ( wf != null )
					wfInst = wf.findActiveInstance();
			} else {
				// By activate (Workflow does not have step)
				if ( wfAction.getConfigurableWorkflowStep() != null ) 
					wfInst = wfAction.getConfigurableWorkflowStep().getConfigurableWorkflowInstance();
			}					
		} else {
			if ( wf != null )
				wfInst = wf.findActiveInstance();
		}
		// Status
		switch (instance.getCommunicationOrderStatusType()) {
		case WORKING_COPY:
			connectedElement.addAttribute("status", ApplicationUtil.getMessage("page.label.working.copy"));
			break;
		case PENDING_APPROVAL:
			connectedElement.addAttribute("status", ApplicationUtil.getMessage("page.label.pending"));
			break;
		case ACTIVE:
			String statusStr = ApplicationUtil.getMessage("page.label.active");
			connectedElement.addAttribute("status", statusStr);
			break;
		default:	
			;
		}

		connectedElement.addAttribute("finalapprover", finalApprover);
		
		CommunicationOrderEntryItem indicator = instance.getIndicatorOrderEntryItem();
		if ( indicator == null || !instance.getDocument().isCommunicationOrderEntryEnabled() )
			indicator = instance.getPrimaryDriverOrderEntryItem();
		
		Element recipientElement = connectedElement.addElement("Recipient");
		String recipient = "No value";
		if ( indicator != null )
			recipient = indicator.getDisplayValue();

		recipientElement.addText(recipient != null ? recipient : "No value");
		
		CommunicationProof latestProof = instance.getLatestProof();
		if ( latestProof != null ) {
			if ( !latestProof.isComplete() && !latestProof.isError() ) {
				connectedElement.addAttribute("proof", ApplicationUtil.getMessage("page.text.in.process"));
			} else if ( latestProof.isNoMatchingRecipientError() ) {
				connectedElement.addAttribute("proof", ApplicationUtil.getMessage("client_messages.text.unable_to_generate_proof") + " " + ApplicationUtil.getMessage("page.label.error"));
			} else if ( latestProof.isNoProductionContentError() ) {
				connectedElement.addAttribute("proof", ApplicationUtil.getMessage("client_messages.text.unable_to_generate_preproof") + " " + ApplicationUtil.getMessage("page.label.error"));
			} else if ( !latestProof.isError() && latestProof.getOutputPath() == null && !instance.getDocument().isCommunicationWebServiceCompositionResultsEnabled() && !latestProof.getIsEmailOrWebPreview() ) {
				connectedElement.addAttribute("proof", latestProof.getClass().getName() + " " + ApplicationUtil.getMessage("page.label.error"));
			} else if ( !latestProof.isError() ) {
				if ( !latestProof.getIsEmailOrWebPreview() ) {
					String params = "";
					if ( instance.getDocument().isCommunicationWebServiceCompositionResultsEnabled() ) {
						params = "?comm_proof_id=" + latestProof.getId() +
								"&comm_proof_type=proof" +
								"&type=remote_comm_pdf";
					} else {
						params = "?file=" + latestProof.getOutputPath();
					}
					
					connectedElement.addAttribute("proof", ApplicationUtil.getMessage("page.label.complete"));
				} else {
					connectedElement.addAttribute("proof", ApplicationUtil.getMessage("page.label.complete"));
				}
			} else if ( latestProof.isError() ) {
				connectedElement.addAttribute("proof", ApplicationUtil.getMessage("page.label.error"));
			}
		}else
			connectedElement.addAttribute("proof", "None");
		
	}
	
	
	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
}
