package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerSharedContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerActionTypeEnum;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.sun.xml.txw2.output.IndentingXMLStreamWriter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.CONTENTS_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.CONTENT_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.DATE_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.DOCUMENT_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.HISTORIES_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.HISTORY_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.LAST_ACTION_BY_USER_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.LAST_ACTION_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.MARKUP_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.METADATA_CONNECTOR_ATTRIBUTE;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.METADATA_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.NAME_ATTRIBUTE;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.SHARED_CONTENTS_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.SHARED_CONTENT_XML_ELEMENT;
import static ai.mpr.marcie.content.rationalizer.xml.XmlProcessingConstants.TEXT_XML_ELEMENT;
import static com.prinova.messagepoint.platform.services.export.ExportUtil.EXTENSION_XML_TYPE;
import static com.prinova.messagepoint.util.RationalizerUtil.constructLastActionUser;


public class ExportRationalizerDocumentToXMLService extends AbstractService {

    public static final String SERVICE_NAME = "export.ExportRationalizerDocumentToXMLService";
    private static final int DOCUMENT_CONTENT_BATCH_SIZE = 1000;
    private static final int SHARED_CONTENT_BATCH_SIZE = 500;
    private static final Log log = LogUtil.getLog(ExportRationalizerDocumentToXMLService.class);
    private static final String UTF_8 = "UTF-8";
    private static final String VERSION = "1.0";
    private static final String DOCUMENTS_XML_ELEMENT = "Documents";
    public static final String DOCUMENT_FILE_NAME_ATTRIBUTE = "filename";
    private static final String RATIONALIZER_XML_ELEMENT = "Rationalizer";
    private static final String GUID = "guid";
    private static final String IS_FROM_MANIFEST = "isFromManifest";
    private static final int BUFFER_SIZE = 10 * 1024 * 1024;
    private static final int NO_CONTENT_HISTORY_TYPE = 0;
    private static final int ALL_CONTENT_HISTORY_TYPE = 1;
    private static final int ONLY_ORIGINAL_CONTENT_HISTORY_TYPE = 2;

    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            ExportRationalizerObjectToXMLServiceRequest request = (ExportRationalizerObjectToXMLServiceRequest) context.getRequest();
            RationalizerApplication app = RationalizerApplication.findById(request.getTargeObjectId());
            if (app == null || app.getParsedDocumentFormDefinition() == null) {
                this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                return;
            }
            User requestor = request.getRequestor();
            statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();

            File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            }

            String exportName = app.getName();
            if (exportName != null) {
                exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            }

            String filename = ExportUtil.getMessagepointObjectExportFileName(requestor, ExportUtil.ExportType.RATIONALIZERDOCUMENT, request.getExportId(), exportName, EXTENSION_XML_TYPE);
            String outputFilePath = outputDirectory.getPath() + File.separator + filename;
            File exportFile = new File(outputFilePath);
            exportFile.createNewFile();

            generateXML(Paths.get(outputFilePath), app, request.getExportOptions().getExportHistoryType());

            context.getResponse().setResultValueBean(request.getExportId());
            ((ExportToXMLServiceResponse) context.getResponse()).setFilePath(outputFilePath);
        } catch (Exception ex) {
            log.error(" unexpected exception when invoking ExportRationalizerDocumentToXMLService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        }
    }

    @Override
    public void validate(ServiceExecutionContext context) {
    }

    public static ServiceExecutionContext createContext(String exportId, long imageId, ExportImportOptions exportImportOptions, User requestor,
                                                        StatusPollingBackgroundTask statusPollingBackgroundTask) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportRationalizerObjectToXMLServiceRequest request = new ExportRationalizerObjectToXMLServiceRequest(exportId, imageId,
                exportImportOptions.isIncludeImagePathOnly(), requestor, statusPollingBackgroundTask);
        context.setRequest(request);
        request.setExportOptions(exportImportOptions);

        ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    private void generateXML(Path path, RationalizerApplication app, int historyExportType) throws IOException, XMLStreamException {
        try (OutputStream os = Files.newOutputStream(path)) {
            try (BufferedOutputStream bos = new BufferedOutputStream(os, BUFFER_SIZE)) {
                XMLOutputFactory outputFactory = XMLOutputFactory.newFactory();
                XMLStreamWriter writer = new IndentingXMLStreamWriter(outputFactory.createXMLStreamWriter(bos, UTF_8));
                writer.writeStartDocument(UTF_8, VERSION);
                writer.writeStartElement(RATIONALIZER_XML_ELEMENT);
                writer.writeStartElement(DOCUMENTS_XML_ELEMENT);

                final Map<Long, Integer> documentIdToContentsCountMap = app.computeDocumentIdToContentsCountMap();
                final LinkedList<Pair<Long, Integer>> documentIdToContentsCountPairs = documentIdToContentsCountMap.entrySet().stream()
                        .map(entry -> Pair.of(entry.getKey(), entry.getValue()))
                        .collect(Collectors.toCollection(LinkedList::new));
                final double totalContents = documentIdToContentsCountPairs.stream()
                        .mapToDouble(pair -> pair.getValue().doubleValue())
                        .sum();
                double processedContents = 0;

                int crtBatchSize = DOCUMENT_CONTENT_BATCH_SIZE;
                Set<Long> tmpDocumentIds = new LinkedHashSet<>();
                do {

                    if (!documentIdToContentsCountPairs.isEmpty()) {
                        final Pair<Long, Integer> tmpDocumentIdToContentsCount = documentIdToContentsCountPairs.remove(0);
                        final Long tmpDocumentId = tmpDocumentIdToContentsCount.getKey();
                        tmpDocumentIds.add(tmpDocumentId);

                        final Integer tmpContentsCount = tmpDocumentIdToContentsCount.getValue();
                        crtBatchSize-= tmpContentsCount;

                        processedContents += tmpContentsCount;
                    }

                    boolean processNextSlice = false;
                    if (crtBatchSize <= 0) {
                        crtBatchSize = DOCUMENT_CONTENT_BATCH_SIZE;

                        processNextSlice = true;
                    }
                    processNextSlice = processNextSlice || documentIdToContentsCountPairs.isEmpty();

                    if (processNextSlice) {
                        List<RationalizerDocument> letters =
                                RationalizerApplication.findDocumentsByApplicationAndDocumentIdsEagerly(app, tmpDocumentIds);
                        tmpDocumentIds = new LinkedHashSet<>();

                        List<RationalizerDocumentContent> rationalizerDocumentContents = app.findRationalizerDocumentContentsInOrderEagerly(letters);

                        Map<Long, List<RationalizerDocumentContent>> contentsMap = new HashMap<>();
                        rationalizerDocumentContents.forEach(s ->
                                contentsMap.computeIfAbsent(s.getRationalizerDocument().getId(), k -> new ArrayList<>())
                                        .add(s));

                        if (!letters.isEmpty()) {
                            for (RationalizerDocument doc : letters) {
                                generateDocumentXml(writer, contentsMap, doc, historyExportType);
                            }
                        }
                        writer.flush();

                        double percentInDouble = (processedContents / totalContents) * 100;
                        this.statusPollingBackgroundTask.setProgressInPercentInThread((int) percentInDouble);
                    }
                } while (!documentIdToContentsCountPairs.isEmpty());

                writer.writeEndElement();

                writer.writeStartElement(SHARED_CONTENTS_XML_ELEMENT);
                int sharedContentCount = app.computeSharedContentCount();
                int sharedContentTotalPages = (sharedContentCount % SHARED_CONTENT_BATCH_SIZE) == 0 ? sharedContentCount / SHARED_CONTENT_BATCH_SIZE : (sharedContentCount / SHARED_CONTENT_BATCH_SIZE) + 1;
                for (int pageNo = 1; pageNo <= sharedContentTotalPages; pageNo++) {
                    List<RationalizerSharedContent> sharedContentList = RationalizerSharedContent.findPaginatedByApplication(app, pageNo, SHARED_CONTENT_BATCH_SIZE);
                    for (RationalizerSharedContent sharedContent : sharedContentList) {
                        generateSharedContentXml(writer, sharedContent, historyExportType);
                    }
                }
                writer.writeEndElement();

                writer.writeEndDocument();
                writer.flush();
                bos.flush();
                writer.close();
            }
        }
    }

    private void generateDocumentXml(XMLStreamWriter writer, Map<Long, List<RationalizerDocumentContent>> contentsMap, RationalizerDocument doc, int historyExportType) throws XMLStreamException {
        writer.writeStartElement(DOCUMENT_XML_ELEMENT);
        writer.writeAttribute(NAME_ATTRIBUTE, doc.getName());
        if (doc.getFileName() != null) {
            writer.writeAttribute(DOCUMENT_FILE_NAME_ATTRIBUTE, doc.getFileName());
        }
        generateDocumentMetadata(writer, doc);
        List<RationalizerDocumentContent> contents = contentsMap.get(doc.getId());
        if (!CollectionUtils.isEmpty(contents)) {
            writer.writeStartElement(CONTENTS_XML_ELEMENT);
            for (RationalizerDocumentContent content : contents) {
                generateContentXml(writer, content, historyExportType);
            }
            writer.writeEndElement();
        }
        writer.writeEndElement();
    }

    private void generateContentXml(XMLStreamWriter writer, RationalizerDocumentContent content, int historyExportType) throws XMLStreamException {
        if (content != null) {
            writer.writeStartElement(CONTENT_XML_ELEMENT);
            writer.writeAttribute(GUID, content.getGuid());
            generateContentMetadataXml(writer, content);
            String markupContent = content.computeNormalizedMarkupContent();
            String textContent = ContentObjectContentUtil.getUnformattedTextContentForMarcie(markupContent);
            if (textContent != null) {
                writer.writeStartElement(TEXT_XML_ELEMENT);
                writer.writeCData(escapeForCDATA(textContent));
                writer.writeEndElement();
            }
            if (markupContent != null) {
                writer.writeStartElement(MARKUP_XML_ELEMENT);
                writer.writeCData(escapeForCDATA(markupContent));
                writer.writeEndElement();
            }

            final RationalizerSharedContent tmpRationalizerSharedContent = content.computeRationalizerSharedContent();
            if (tmpRationalizerSharedContent != null) {
                writer.writeStartElement(SHARED_CONTENT_XML_ELEMENT);
                writer.writeAttribute("guid", tmpRationalizerSharedContent.getGuid());
                writer.writeEndElement();
            }

            generateContentHistoryXml(writer, content, historyExportType);

            writer.writeEndElement();
        }
    }

    private void generateContentMetadataXml(XMLStreamWriter writer, RationalizerDocumentContent content) throws XMLStreamException {
        Set<MetadataFormItem> formItems = content.getParsedContentForm().getFormItems();
        generateMetadataXml(writer, new ArrayList<>(formItems));
    }

    private void generateDocumentMetadata(XMLStreamWriter writer, RationalizerDocument doc) throws XMLStreamException {
        Set<MetadataFormItem> formItems = doc.getParsedDocumentForm().getFormItems();
        generateMetadataXml(writer, new ArrayList<>(formItems));
    }

    private void generateMetadataXml(XMLStreamWriter writer, List<MetadataFormItem> formItems) throws XMLStreamException {
        Collections.sort(formItems, new MetadataFormItemComparator());
        for (MetadataFormItem itemForm : formItems) {
            writer.writeStartElement(METADATA_XML_ELEMENT);
            writer.writeAttribute(METADATA_CONNECTOR_ATTRIBUTE, itemForm.getItemDefinition().getName());
            if(itemForm.getIsManifestItem()) {
                writer.writeAttribute(IS_FROM_MANIFEST, itemForm.getIsManifestItem().toString());
            }
            if (itemForm.getValue() != null) {
                writer.writeCData(escapeForCDATA(itemForm.getValue()));
            } else {
                writer.writeCData("");
            }
            writer.writeEndElement();
        }
    }

    private void generateContentHistoryXml(XMLStreamWriter writer, RationalizerDocumentContent content, int historyExportType) throws XMLStreamException {
        if (historyExportType == NO_CONTENT_HISTORY_TYPE) {
            return;
        } else if (historyExportType == ALL_CONTENT_HISTORY_TYPE) {
            generateAllContentHistoryXml(writer, content);
        } else if (historyExportType == ONLY_ORIGINAL_CONTENT_HISTORY_TYPE) {
            generateOnlyOriginalContentHistoryXml(writer, content);
        }
    }

    private void generateOnlyOriginalContentHistoryXml(XMLStreamWriter writer, RationalizerDocumentContent content) throws XMLStreamException {
        List<HistoricalRationalizerDocumentContent> allHistoryByContent = HistoricalRationalizerDocumentContent.findAllByContent(content);
        if (CollectionUtils.isNotEmpty(allHistoryByContent)) {
            writer.writeStartElement(HISTORIES_XML_ELEMENT);
            HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent = allHistoryByContent.get(allHistoryByContent.size() - 1);
            generateContentHistoryXml(writer, historicalRationalizerDocumentContent);
            writer.writeEndElement();
        }
    }

    private void generateAllContentHistoryXml(XMLStreamWriter writer, RationalizerDocumentContent content) throws XMLStreamException {
        List<HistoricalRationalizerDocumentContent> allContentHistoryList = HistoricalRationalizerDocumentContent.findAllByContent(content);
        if (CollectionUtils.isNotEmpty(allContentHistoryList)) {
            writer.writeStartElement(HISTORIES_XML_ELEMENT);
            for (HistoricalRationalizerDocumentContent crtContentHistory : allContentHistoryList) {
                generateContentHistoryXml(writer, crtContentHistory);
            }
            writer.writeEndElement();
        }
    }

    private void generateContentHistoryXml(XMLStreamWriter writer, HistoricalRationalizerDocumentContent historicalRationalizerDocumentContent) throws XMLStreamException {
        writer.writeStartElement(HISTORY_XML_ELEMENT);
        if (StringUtils.isNotEmpty(historicalRationalizerDocumentContent.getTextContent())) {
            writer.writeStartElement(TEXT_XML_ELEMENT);
            writer.writeCData(escapeForCDATA(historicalRationalizerDocumentContent.getTextContent()));
            writer.writeEndElement();
        }
        if (StringUtils.isNotEmpty(historicalRationalizerDocumentContent.getMarkupContent())) {
            writer.writeStartElement(MARKUP_XML_ELEMENT);
            writer.writeCData(escapeForCDATA(historicalRationalizerDocumentContent.getMarkupContent()));
            writer.writeEndElement();
        }
        if (StringUtils.isNotEmpty(historicalRationalizerDocumentContent.getCreated().toString())) {
            writer.writeStartElement(DATE_XML_ELEMENT);
            writer.writeCData(DateUtil.formatDateForXMLOutput(historicalRationalizerDocumentContent.getCreated()));
            writer.writeEndElement();
        }

        String lastAction = StringUtils.isEmpty(historicalRationalizerDocumentContent.getLastActionName()) ? RationalizerActionTypeEnum.UPDATE.getActionName() :
                historicalRationalizerDocumentContent.getLastActionName();
        if (StringUtils.isNotEmpty(lastAction)) {
            writer.writeStartElement(LAST_ACTION_XML_ELEMENT);
            writer.writeCData(lastAction);
            writer.writeEndElement();
        }

        String lastActionUserName = StringUtils.isEmpty(historicalRationalizerDocumentContent.getLastActionBy()) ? constructLastActionUser(historicalRationalizerDocumentContent) :
                historicalRationalizerDocumentContent.getLastActionBy();
        if (StringUtils.isNotEmpty(lastActionUserName)) {
            writer.writeStartElement(LAST_ACTION_BY_USER_XML_ELEMENT);
            writer.writeCData(lastActionUserName);
            writer.writeEndElement();
        }

        Long rationalizerSharedContentId = historicalRationalizerDocumentContent.getRationalizerSharedContentId();
        if (rationalizerSharedContentId != null) {
            RationalizerSharedContent shared = RationalizerSharedContent.findById(rationalizerSharedContentId);
            if(shared != null) {
                writer.writeStartElement(SHARED_CONTENT_XML_ELEMENT);
                writer.writeAttribute("guid", shared.getGuid());
                writer.writeEndElement();
            }
        }
        writer.writeEndElement();
    }

    private void generateSharedContentXml(XMLStreamWriter writer, RationalizerSharedContent sharedContent, int historyExportType) throws XMLStreamException {
        writer.writeStartElement(SHARED_CONTENT_XML_ELEMENT);
        writer.writeAttribute(NAME_ATTRIBUTE, sharedContent.getName());
        writer.writeAttribute(GUID, sharedContent.getGuid());

        Set<MetadataFormItem> formItems = sharedContent.getParsedContentForm().getFormItems();
        generateMetadataXml(writer, new ArrayList<>(formItems));

        String sharedText = sharedContent.getTextContent();
        if (StringUtils.isNotEmpty(sharedText)) {
            writer.writeStartElement(TEXT_XML_ELEMENT);
            writer.writeCData(escapeForCDATA(sharedText));
            writer.writeEndElement();
        }
        String sharedMarkup = sharedContent.computeNormalizedMarkupContent();
        if (StringUtils.isNotEmpty(sharedMarkup)) {
            writer.writeStartElement(MARKUP_XML_ELEMENT);
            writer.writeCData(escapeForCDATA(sharedMarkup));
            writer.writeEndElement();
        }

        generateContentsForSharedObjectXml(writer, sharedContent);
        generateSharedContentHistoryXml(writer, sharedContent, historyExportType);

        writer.writeEndElement();
    }

    private void generateContentsForSharedObjectXml(XMLStreamWriter writer, RationalizerSharedContent sharedContent) throws XMLStreamException {
        writer.writeStartElement(CONTENTS_XML_ELEMENT);
        for (RationalizerDocumentContent content : sharedContent.shallowCopyOfContents()) {
            writer.writeStartElement(CONTENT_XML_ELEMENT);
            writer.writeAttribute(GUID, String.valueOf(content.getGuid()));
            writer.writeEndElement();
        }

        writer.writeEndElement();
    }


    private void generateSharedContentHistoryXml(XMLStreamWriter writer, RationalizerSharedContent sharedContent, int historyExportType) throws XMLStreamException {
        if (historyExportType == NO_CONTENT_HISTORY_TYPE) {
            return;
        } else if (historyExportType == ALL_CONTENT_HISTORY_TYPE) {
            generateAllSharedContentHistoryXml(writer, sharedContent);
        } else if (historyExportType == ONLY_ORIGINAL_CONTENT_HISTORY_TYPE) {
            generateOnlyOriginalSharedContentHistoryXml(writer, sharedContent);
        }
    }

    private void generateOnlyOriginalSharedContentHistoryXml(XMLStreamWriter writer, RationalizerSharedContent content) throws XMLStreamException {
        List<HistoricalRationalizerSharedContent> allHistoryByContent = HistoricalRationalizerSharedContent.findAllBySharedContent(content);
        if (CollectionUtils.isNotEmpty(allHistoryByContent)) {
            writer.writeStartElement(HISTORIES_XML_ELEMENT);
            HistoricalRationalizerSharedContent historicalRationalizerSharedContent = allHistoryByContent.get(allHistoryByContent.size() - 1);
            generateSharedContentHistoryXml(writer, historicalRationalizerSharedContent);
            writer.writeEndElement();
        }
    }

    private void generateAllSharedContentHistoryXml(XMLStreamWriter writer, RationalizerSharedContent content) throws XMLStreamException {
        List<HistoricalRationalizerSharedContent> allsharedHistoryList = HistoricalRationalizerSharedContent.findAllBySharedContent(content);
        if (CollectionUtils.isNotEmpty(allsharedHistoryList)) {
            writer.writeStartElement(HISTORIES_XML_ELEMENT);
            for (HistoricalRationalizerSharedContent crtContentHistory : allsharedHistoryList) {
                generateSharedContentHistoryXml(writer, crtContentHistory);
            }
            writer.writeEndElement();
        }
    }

    private void generateSharedContentHistoryXml(XMLStreamWriter writer, HistoricalRationalizerSharedContent historicalRationalizerSharedContent) throws XMLStreamException {
        writer.writeStartElement(HISTORY_XML_ELEMENT);
        if (StringUtils.isNotEmpty(historicalRationalizerSharedContent.getTextContent())) {
            writer.writeStartElement(TEXT_XML_ELEMENT);
            writer.writeCData(escapeForCDATA(historicalRationalizerSharedContent.getTextContent()));
            writer.writeEndElement();
        }
        if (StringUtils.isNotEmpty(historicalRationalizerSharedContent.getMarkupContent())) {
            writer.writeStartElement(MARKUP_XML_ELEMENT);
            writer.writeCData((historicalRationalizerSharedContent.getMarkupContent()));
            writer.writeEndElement();
        }
        if (StringUtils.isNotEmpty(historicalRationalizerSharedContent.getCreated().toString())) {
            writer.writeStartElement(DATE_XML_ELEMENT);
            writer.writeCData(DateUtil.formatDateForXMLOutput(historicalRationalizerSharedContent.getCreated()));
            writer.writeEndElement();
        }

        String lastAction = StringUtils.isEmpty(historicalRationalizerSharedContent.getLastActionName()) ? RationalizerActionTypeEnum.UPDATE.getActionName() :
                historicalRationalizerSharedContent.getLastActionName();
        if (StringUtils.isNotEmpty(lastAction)) {
            writer.writeStartElement(LAST_ACTION_XML_ELEMENT);
            writer.writeCData(lastAction);
            writer.writeEndElement();
        }

        String lastActionUserName = StringUtils.isEmpty(historicalRationalizerSharedContent.getLastActionBy()) ? constructLastActionUser(historicalRationalizerSharedContent) :
                historicalRationalizerSharedContent.getLastActionBy();
        if (StringUtils.isNotEmpty(lastActionUserName)) {
            writer.writeStartElement(LAST_ACTION_BY_USER_XML_ELEMENT);
            writer.writeCData(lastActionUserName);
            writer.writeEndElement();
        }

        writer.writeEndElement();
    }

    static class MetadataFormItemComparator implements Comparator<MetadataFormItem> {
        private final Pattern sortPattern = Pattern.compile("^\\d+");

        @Override
        public int compare(MetadataFormItem o1, MetadataFormItem o2) {
            if (o1 == null || o2 == null) {
                return 0;
            }

            if (o1.getItemDefinition() == null || o2.getItemDefinition() == null) {
                return 0;
            }

            String firstItem = o1.getItemDefinition().getPrimaryConnector();
            String secondItem = o2.getItemDefinition().getPrimaryConnector();

            if (firstItem == null || secondItem == null) {
                return 0;
            }

            Matcher m = sortPattern.matcher(firstItem);
            Integer number1;
            if (!m.find()) {
                return firstItem.compareTo(secondItem);
            } else {
                int number2;
                number1 = Integer.parseInt(m.group());
                m = sortPattern.matcher(secondItem);
                if (!m.find()) {
                    return firstItem.compareTo(secondItem);
                } else {
                    number2 = Integer.parseInt(m.group());
                    int comparison = number1.compareTo(number2);
                    if (comparison != 0) {
                        return comparison;
                    } else {
                        return firstItem.compareTo(secondItem);
                    }
                }
            }
        }
    }
    private String escapeForCDATA(String value) {
        String regex = "[\\p{C}]";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        String sanitizedText = matcher.replaceAll("");
        return sanitizedText;
    }
}
