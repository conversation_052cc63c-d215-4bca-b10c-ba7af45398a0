package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GenerateMessageAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateMessageAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateMessageAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateMessageAuditReportServiceRequest request = (GenerateMessageAuditReportServiceRequest) context.getRequest();

			MessageAuditReportRunner messageExportRunner = new MessageAuditReportRunner(request.getAuditReportId(),
                    request.getTpSelection(),
                    request.getSelectionStatus(),
                    request.getContentObjectlist(),
                    request.isDateFilterEnabled(),
                    request.getStartDate(),
                    request.isIncludeTargeting(),
                    request.isIncludeContent(),
                    request.isIncludeAllMessages(),
                    request.getRequestor(),
                    request.getAuditReportTypeId(),
                    request.getDocument());
	   		MessagePointRunnableUtil.startThread(messageExportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateMessageAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class MessageAuditReportRunner extends MessagePointRunnable {

		private final Document document;
		private long auditReportId;
		private TouchpointSelection tpSelection;
		private int selectionStatus;
		private List<ContentObject> contentObjectlist = new ArrayList<>();
		private Date startDate;
		private boolean includeTargeting;
		private boolean includeContent;
		private boolean includeAllMessages;
		private User requestor;
		private boolean dateFilterEnabled;
		private int auditReportTypeId;
		
		public MessageAuditReportRunner(long auditReportId,
										TouchpointSelection tpSelection,
										int selectionStatus,
										List<ContentObject> contentObjectlist,
										boolean dateFilterEnabled,
										Date startDate,
										boolean includeTargeting,
										boolean includeContent,
										boolean includeAllMessages,
										User requestor,
										int auditReportTypeId,
										Document document) {
			
			this.auditReportId = auditReportId;
			this.tpSelection = tpSelection;
			this.selectionStatus = selectionStatus;
			this.contentObjectlist = contentObjectlist;
			this.startDate = startDate;
			this.dateFilterEnabled = dateFilterEnabled;
			this.includeTargeting = includeTargeting;
			this.includeContent = includeContent;
			this.includeAllMessages = includeAllMessages;
			this.requestor = requestor;
			this.auditReportTypeId = auditReportTypeId;
			this.document = document;
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {

			LogUtil.getLog(MessageAuditReportRunner.class).info("Begin Message Audit Report Request: " + DateFormat.getDateTimeInstance().format(new Date()) );


			try {
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
		    	ServiceExecutionContext exportServiceContext = ExportMessageToXMLService.createContext(getTpSelection(),
																											getSelectionStatus(),
																										   	getContentObjectlist(),
																										   	isDateFilterEnabled(),
																										   	getStartDate(),
																										   	isIncludeTargeting(),
																										   	isIncludeContent(),
																										   	isIncludeAllMessages(),
																										   	getRequestor(),
																											getAuditReportTypeId(),
						 																					getDocument()
				);

		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportMessageToXMLService.SERVICE_NAME, ExportMessageToXMLService.class);
		    	exportService.execute(exportServiceContext);

		    	String xmlFilePath = ((ExportToXMLServiceResponse)exportServiceContext.getResponse()).getFilePath();
		    	
				// ******************************************
				// ********* Generate XSLT ******************
				// ******************************************

				String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, getRequestor(), getAuditReportTypeId(), 0);
		    	
				// *********************************************************
				// ********* Update the AuditReport object *****************
				// *********************************************************

				ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(getAuditReportId(),
																																	getAuditReportTypeId(),
																																	xmlFilePath, 
																																	xhtmlFilePath);
				Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				updateReportService.execute(updateReportServiceContext);

				LogUtil.getLog(MessageAuditReportRunner.class).info("End Message Audit Report Request: " + DateFormat.getDateTimeInstance().format(new Date()) );


			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Message Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);

				LogUtil.getLog(MessageAuditReportRunner.class).info("Failed Message Audit Report Request: " + DateFormat.getDateTimeInstance().format(new Date()) );

			}
		}

		public Document getDocument() {
			return document;
		}

		public long getAuditReportId() {
			return auditReportId;
		}

		public void setAuditReportId(long auditReportId) {
			this.auditReportId = auditReportId;
		}

		public int getAuditReportTypeId() {
			return auditReportTypeId;
		}

		public void setAuditReportTypeId(int auditReportTypeId) {
			this.auditReportTypeId = auditReportTypeId;
		}

		public TouchpointSelection getTpSelection() {
			return tpSelection;
		}

		public void setTpSelection(TouchpointSelection tpSelection) {
			this.tpSelection = tpSelection;
		}

		public int getSelectionStatus() {
			return selectionStatus;
		}

		public void setSelectionStatus(int selectionStatus) {
			this.selectionStatus = selectionStatus;
		}

		public List<ContentObject> getContentObjectlist() {
			return contentObjectlist;
		}

		public void setContentObjectlist(List<ContentObject> contentObjectlist) {
			this.contentObjectlist = contentObjectlist;
		}

		public Date getStartDate() {
			return startDate;
		}

		public void setStartDate(Date startDate) {
			this.startDate = startDate;
		}

		public boolean isIncludeTargeting() {
			return includeTargeting;
		}

		public void setIncludeTargeting(boolean includeTargeting) {
			this.includeTargeting = includeTargeting;
		}

		public boolean isIncludeContent() {
			return includeContent;
		}

		public void setIncludeContent(boolean includeContent) {
			this.includeContent = includeContent;
		}

		public User getRequestor() {
			return requestor;
		}

		public void setRequestor(User requestor) {
			this.requestor = requestor;
		}

		public boolean isDateFilterEnabled() {
			return dateFilterEnabled;
		}

		public void setDateFilterEnabled(boolean dateFilterEnabled) {
			this.dateFilterEnabled = dateFilterEnabled;
		}

		public boolean isIncludeAllMessages() {
			return includeAllMessages;
		}

		public void setIncludeAllMessages(boolean includeAllMessages) {
			this.includeAllMessages = includeAllMessages;
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(long auditReportId,
														TouchpointSelection tpSelection,
														int selectionStatus,
														List<ContentObject> contentObjectlist,
														boolean dateFilterEnabled,
														Date startDate,
														boolean includeTargeting,
														boolean includeContent,
														boolean includeAllMessages,
														User requestor,
														int auditReportTypeId,
														Document document) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateMessageAuditReportServiceRequest request = new GenerateMessageAuditReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setTpSelection(tpSelection);
		request.setSelectionStatus(selectionStatus);
		request.setContentObjectlist(contentObjectlist);
		request.setDateFilterEnabled(dateFilterEnabled);
		request.setStartDate(startDate);
		request.setIncludeTargeting(includeTargeting);
		request.setIncludeContent(includeContent);
		request.setIncludeAllMessages(includeAllMessages);
		request.setRequestor(requestor);
		request.setAuditReportTypeId(auditReportTypeId);
		request.setDocument(document);
		
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}