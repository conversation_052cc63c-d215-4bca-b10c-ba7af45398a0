package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportInsertScheduleToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -8853366963609315170L;
	
	private List<InsertSchedule> insertScheduleList= new ArrayList<>();
	private Date startDate;
	private Boolean includeInsertTargeting;
	private User requestor;
	
	public List<InsertSchedule> getInsertScheduleList() {
		return insertScheduleList;
	}
	public void setInsertScheduleList(List<InsertSchedule> insertScheduleList) {
		this.insertScheduleList = insertScheduleList;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Boolean getIncludeInsertTargeting() {
		return includeInsertTargeting;
	}
	public void setIncludeInsertTargeting(Boolean includeInsertTargeting) {
		this.includeInsertTargeting = includeInsertTargeting;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
}