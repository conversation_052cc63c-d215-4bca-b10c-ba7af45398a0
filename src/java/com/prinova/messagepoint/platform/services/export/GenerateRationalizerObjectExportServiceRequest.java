package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.List;

public class GenerateRationalizerObjectExportServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 8305058496404954839L;
	
	private String 		exportId;
	private long		targetObjectId;
	private ExportImportOptions exportOptions;
	private Class<?> 	targetClass;
	private User		requestingUser;
	private int			exportType 				= 0;
	private List<String> selectedIds;

	public String getExportId() {
		return exportId;
	}
	public void setExportId(String exportId) {
		this.exportId = exportId;
	}

	public long getTargetObjectId() {
		return targetObjectId;
	}
	public void setTargetObjectId(long targetObjectId) {
		this.targetObjectId = targetObjectId;
	}

	public Class<?> getTargetClass() {
		return targetClass;
	}
	public void setTargetClass(Class<?> targetClass) {
		this.targetClass = targetClass;
	}

	public User getRequestingUser() {
		return requestingUser;
	}
	public void setRequestingUser(User requestingUser) {
		this.requestingUser = requestingUser;
	}

	public int getExportType() {
		return exportType;
	}
	public void setExportType(int exportType) {
		this.exportType = exportType;
	}

	public List<String> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<String> selectedIds) {
		this.selectedIds = selectedIds;
	}
	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}
	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}
	
}