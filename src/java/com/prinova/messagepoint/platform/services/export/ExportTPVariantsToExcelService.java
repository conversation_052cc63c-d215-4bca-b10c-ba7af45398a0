package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringUtil;

 
public class ExportTPVariantsToExcelService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportTPVariantsToExcelService";
    
    private static final Log log = LogUtil.getLog(ExportTPVariantsToExcelService.class);
    private User requestor = null;
	private boolean includeImagePathOnly = false;
	private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportDynamicContentObjectServiceRequest request = (ExportDynamicContentObjectServiceRequest) context.getRequest();
            
            Document document = Document.findById(request.getImageId());
            requestor = request.getRequestor();
            includeImagePathOnly = request.isIncludeImagePathOnly();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            //outputDirectory.mkdirs();
    	    
            File file = new File(outputDirectory + File.separator + ExportUtil.EXCEL_EXPORT_TEMPLATE_TP_VARIANTS);
            FileInputStream fileStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(fileStream);
            
            generateWorkbook(document, workbook, requestor);
          
            String exportName = document.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook, 
            		requestor, 
            		ExportUtil.ExportType.TPVARIANTS, 
            		request.getExportId(),
            		exportName);

            ((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
            log.error(" unexpected exception when invoking ExportTPVariantsToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateWorkbook( Document document, XSSFWorkbook workbook, User requestor ) throws Exception 
    {
    	XSSFSheet sheet = workbook.getSheet("variants");
    	
    	List<MessagepointLocale> languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        List<String> languageCodes = new ArrayList<>();
        
        for (MessagepointLocale locale : languages) 
		{
        	languageCodes.add(locale.getLanguageCode());
		}
        
    	int counter = 0;
    	MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        
        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        
        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(document.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(document.getGuid());
        
        
        int rowCounter = 8;
        List<ParameterGroupTreeNode> ChildNodes = document.getMasterTouchpointSelection().getParameterGroupTreeNode().getChildren();
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList();
        if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode topNode : ChildNodes) 
			{	
				rowCounter = getRowsForPgNode(sheet, rowCounter, document, null, topNode, languages, cellRangeAddressList, counter);
		    }
		}
        generateChildPgtnRow(sheet, rowCounter, document, null, null, languages, cellRangeAddressList, counter);
    }
    
	private void generateChildPgtnRow(XSSFSheet sheet, int rowCounter, Document document, 
			Map<String, ContentVO> masterContent, ParameterGroupTreeNode childNode, 
			List<MessagepointLocale> languages, CellRangeAddressList cellRangeAddressList, int imageRowCounter) throws Exception {
		
		Row dataRow = sheet.createRow(rowCounter);
		if(childNode != null){
			dataRow.createCell(0).setCellValue(childNode.getGuid());
			dataRow.createCell(1).setCellValue(childNode.getId());
			dataRow.createCell(2).setCellValue(childNode.getParentNode()!= null?childNode.getParentNode().getId():0);
	        dataRow.createCell(3).setCellValue(childNode.getName());
	        
	        org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
            xmlDoc.setXMLEncoding("UTF-8");
	        Element element = xmlDoc.addElement("variant");
	        if(childNode.getParameterGroupInstanceCollection() != null)
	        	createDataValueTag(childNode.getParameterGroupInstanceCollection(), element);
	        String value = element.getStringValue();
	        dataRow.createCell(4).setCellValue(value);
	    }else{ // Master
			dataRow.createCell(0).setCellValue("");
			dataRow.createCell(1).setCellValue(1);
	        dataRow.createCell(2).setCellValue("");
	        dataRow.createCell(3).setCellValue("Master");
	        dataRow.createCell(4).setCellValue("");
	    }
		sheet.setColumnWidth(0, 10000); // Manually set column width for GUID field
		sheet.setColumnWidth(1, 9000); // Manually set column width for GUID field
	}

	public boolean getIncludeImagePathOnly()
	{
		return includeImagePathOnly;
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long imageId, boolean includeImagePathOnly, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportDynamicContentObjectServiceRequest request = ExportDynamicContentObjectServiceRequest.createExcelExportRequest(exportId, imageId, includeImagePathOnly, requestor);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
    
    public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
    public static String generateSelectionDataString(ParameterGroupTreeNode childNode){
    	org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
        xmlDoc.setXMLEncoding("UTF-8");
        Element element = xmlDoc.addElement("variant");
        String returnString = "";
        if(childNode.getParameterGroupInstanceCollection() != null)
			try {
				createDataValueTag(childNode.getParameterGroupInstanceCollection(), element);
			} catch (Exception e) {
				log.error("Error: ", e);
			}
        returnString = element.getStringValue();
        return returnString;
    }
    
	public static void createDataValueTag(
	        ParameterGroupInstanceCollection pgiCollection,
	        Element dataValuesElement)
	throws Exception 
	{
		List<String> pgiPathValues = new ArrayList<>();
		createValueTag(pgiCollection.getId(), dataValuesElement, pgiPathValues, 1, false);
		if(dataValuesElement.getText().isEmpty()){
			StringBuilder valueString = new StringBuilder();
			for (String pathValue : pgiPathValues) {
				if (valueString.length() > 0) {
					valueString.append(";");
				}
				valueString.append(pathValue);
			}
		}
	}
	
	private static boolean createValueTag(
	        long pgiCollectionId,
	        Element dataValueElement,
	        List<String> pgiPathValues,
	        int level,
	        boolean secondValueTag)
	throws Exception
	{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {
                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					secondValueTag = createValueTag(pgiCollectionId, dataValueElement, pathValues, level + 1, secondValueTag);
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				StringBuilder valueString = new StringBuilder();
				
				if (secondValueTag)
					valueString = new StringBuilder("|");
				else
					secondValueTag = true;
				
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				dataValueElement.addText(valueString.toString());
			}
		}
		return secondValueTag;
	}
	
	public static String createValueCell(ParameterGroupInstanceCollection pgiCollection)
	throws Exception 
	{
		List<String> pgiPathValues = new ArrayList<>();
		String returnValues = "";
		returnValues = createValue(returnValues, pgiCollection.getId(), pgiPathValues, 1);
		return returnValues;
	}
	
	private static String createValue(String returnValue, long pgiCollectionId, List<String> pgiPathValues, int level)throws Exception{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		StringBuilder valueString = new StringBuilder();
		
		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {

                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					childIthItemValues.add(createValue(returnValue, pgiCollectionId, pathValues, level + 1));
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if (valueString.length() > 0) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				//valueCell.setCellValue(valueString);
				
				//pgiPathValues.add(valueString);
			}
			else{
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
			}
		}
		returnValue = returnValue.concat(valueString.toString());
		return returnValue;
	}
	
	public int getRowsForPgNode(XSSFSheet sheet, int rowCounter, Document document, 
			Map<String, ContentVO> masterContent, ParameterGroupTreeNode currentNode, 
			List<MessagepointLocale> languages, CellRangeAddressList cellRangeAddressList, int imageRowCounter) throws Exception {
		TouchpointSelection tpSelection = TouchpointSelection.findByPGTN(currentNode.getId());
		if(tpSelection != null && !tpSelection.isDeleted()){
			generateChildPgtnRow(sheet, rowCounter, document, masterContent, currentNode, languages, cellRangeAddressList, imageRowCounter);
			rowCounter++;
			if (currentNode.getChildren() != null && !currentNode.getChildren().isEmpty()) {
				for (ParameterGroupTreeNode childNode : currentNode.getChildren()){
						rowCounter = getRowsForPgNode(sheet, rowCounter, document, masterContent, childNode, languages, cellRangeAddressList, imageRowCounter);
				}
			}
		}
		return rowCounter;
	}	
}
