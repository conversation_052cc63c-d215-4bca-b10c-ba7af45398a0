package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.List;

public class ExportMessagepointObjectToExcelServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -1553522279771063788L;

	private String 		exportId;
    private long 		targeObjectId;
    private List<Long>	selectedIds;
    private boolean		includeImagePathOnly;
    private User 		requestor;
    private ExportImportOptions exportOptions;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    
    public ExportMessagepointObjectToExcelServiceRequest(String exportId, long targeObjectId, List<Long> selectedIds, boolean includeImagePathOnly, User user )
    {
    	this(exportId, targeObjectId, selectedIds, includeImagePathOnly, user, null);
    }

    public ExportMessagepointObjectToExcelServiceRequest(String exportId, long targeObjectId, List<Long> selectedIds,
                                                         boolean includeImagePathOnly, User user, StatusPollingBackgroundTask statusPollingBackgroundTask )
    {
        this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        this.selectedIds = selectedIds;
        this.includeImagePathOnly = includeImagePathOnly;
        this.requestor = user;
        this.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
    }

    public ExportMessagepointObjectToExcelServiceRequest(String exportId, long targeObjectId, List<Long> selectedIds, User user, ExportImportOptions exportOptions )
    {
    	this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        this.selectedIds = selectedIds;
        if(exportOptions!=null)
        	this.includeImagePathOnly = exportOptions.isIncludeImagePathOnly();
        this.requestor = user;
        this.exportOptions = exportOptions;
    }

	public boolean isIncludeImagePathOnly() {
		return includeImagePathOnly;
	}

	public String getExportId() {
		return exportId;
	}

	public long getTargeObjectId() {
        return targeObjectId;
    }

    public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public User getRequestor() {
        return requestor;
    }

	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }
}
