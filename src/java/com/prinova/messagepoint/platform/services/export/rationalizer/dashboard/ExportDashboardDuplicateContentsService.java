package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.action.duplicates.ContentWorkQueueWithFirstId;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class ExportDashboardDuplicateContentsService extends ExportDashboardContentsService {

    @Override
    public List<ReportContentDTO> getContentsForReport(ExportRationalizerDashboardToExcelServiceRequest request, RationalizerApplication application) {
        List<ReportContentDTO> contentList = new ArrayList<>();
        List<String> selectedIds = request.getSelectedIds();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(application);

        if (CollectionUtils.isEmpty(selectedIds)) {
            List<Map<String, String>> sourceMetadataFiltersList = RationalizerUtil.processDashboardTreeSelections(application.getId(), request.getSourceSelectionBranches());
            List<Map<String, String>> targetMetadataFiltersList = RationalizerUtil.processDashboardTreeSelections(application.getId(), request.getTargetSelectionBranches());

            List<DashboardFilter>  dashboardResultsFilterList = application.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
            Map<String, Integer> duplicatesHashCountsMap = rationalizerElasticSearchHandler.searchDuplicatesWithCompareContext(sourceMetadataFiltersList, targetMetadataFiltersList, dashboardResultsFilterList);

            if (MapUtils.isNotEmpty(duplicatesHashCountsMap)) {
                List guidsPerPage = new LinkedList();
                Map<String, ContentWorkQueueWithFirstId.CountAndFirstGuid> hashGuidsPairForFilters = rationalizerElasticSearchHandler.searchHashGuidsPairForFilters(sourceMetadataFiltersList, dashboardResultsFilterList);
                for(String dupKey :duplicatesHashCountsMap.keySet()) {
                    ContentWorkQueueWithFirstId.CountAndFirstGuid countAndFirstGuid = hashGuidsPairForFilters.get(dupKey);
                    if(countAndFirstGuid != null) {
                        guidsPerPage.add(countAndFirstGuid.getFirstGuid());
                    }
                }
                List<RationalizerContent> contents = RationalizerContent.findRationalizerContentsByElasticSearchGuids(guidsPerPage);
                for (RationalizerContent content : contents) {
                    final String hashCode = content.getHashCode();
                    String text = content.getTextContent();
                    String elasticSearchGuid = content.buildElasticSearchGuid();
                    contentList.add(new ReportContentDTO(hashCode, text, elasticSearchGuid));
                }
            }
        } else {
            final List<RationalizerContent> rationalizerContents = RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);
            for (RationalizerContent rationalizerContent : rationalizerContents) {
                contentList.add(new ReportContentDTO(rationalizerContent.getHashCode(), rationalizerContent.getTextContent(), rationalizerContent.buildElasticSearchGuid()));
            }
        }

        return contentList;
    }
}
