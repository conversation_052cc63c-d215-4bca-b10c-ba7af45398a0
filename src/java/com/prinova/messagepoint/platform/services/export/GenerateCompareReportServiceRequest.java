package com.prinova.messagepoint.platform.services.export;

import java.util.List;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class GenerateCompareReportServiceRequest extends SimpleServiceRequest {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1254728713902449192L;
	private long auditReportId;
	private TouchpointSelection tpSelection;
	private User requestor;
	private long localContext;
	private Document document;
	private boolean syncFromOrigin;
	private boolean syncWithParent;
	private long syncSibling;
	private int	contentObjectFilter;
	private long otherInstanceId;
	private long instanceId;
	private boolean syncMultiWay;
	private long siblingParentInstanceId;
	private long siblingParentId;
	private long parentInstanceId;
	private long parentId;
	
	private List<String> selectedObjects;
	
	
	public long getAuditReportId() {
		return auditReportId;
	}
	public void setAuditReportId(long auditReportId) {
		this.auditReportId = auditReportId;
	}
	public TouchpointSelection getTpSelection() {
		return tpSelection;
	}
	public void setTpSelection(TouchpointSelection tpSelection) {
		this.tpSelection = tpSelection;
	}
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public long getLocalContext() {
		return localContext;
	}
	public void setLocalContext(long localContext) {
		this.localContext = localContext;
	}
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	public boolean isSyncFromOrigin() {
		return syncFromOrigin;
	}
	public void setSyncFromOrigin(boolean syncFromOrigin) {
		this.syncFromOrigin = syncFromOrigin;
	}
	public boolean isSyncWithParent() {
		return syncWithParent;
	}
	public void setSyncWithParent(boolean syncWithParent) {
		this.syncWithParent = syncWithParent;
	}
	public long getSyncSibling() {
		return syncSibling;
	}
	public void setSyncSibling(long syncSibling) {
		this.syncSibling = syncSibling;
	}
	public int getContentObjectFilter() {
		return contentObjectFilter;
	}
	public void setContentObjectFilter(int contentObjectFilter) {
		this.contentObjectFilter = contentObjectFilter;
	}
	public long getOtherInstanceId(){
		return otherInstanceId;
	}
	public void setOtherInstanceId(long otherInstanceId) {
		this.otherInstanceId = otherInstanceId;
		
	}
	public long getInstanceId() {
		return instanceId;
	}
	public void setInstanceId(long instanceId) {
		this.instanceId = instanceId;
	}
	public boolean isSyncMultiWay() {
		return syncMultiWay;
	}
	public void setSyncMultiWay(boolean syncMultiWay) {
		this.syncMultiWay = syncMultiWay;
	}
	public List<String> getSelectedObjects() {
		return selectedObjects;
	}
	public void setSelectedObjects(List<String> selectedObjects) {
		this.selectedObjects = selectedObjects;
	}
	public long getSiblingParentInstanceId() {
		return siblingParentInstanceId;
	}
	public void setSiblingParentInstanceId(long siblingParentInstanceId) {
		this.siblingParentInstanceId = siblingParentInstanceId;
	}
	public long getSiblingParentId() {
		return siblingParentId;
	}
	public void setSiblingParentId(long siblingParentId) {
		this.siblingParentId = siblingParentId;
	}
	public long getParentInstanceId() {
		return parentInstanceId;
	}
	public void setParentInstanceId(long parentInstanceId) {
		this.parentInstanceId = parentInstanceId;
	}
	public long getParentId() {
		return parentId;
	}
	public void setParentId(long parentId) {
		this.parentId = parentId;
	}
}