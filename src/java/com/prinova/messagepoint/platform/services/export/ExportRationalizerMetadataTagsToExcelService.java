package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameComparator;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;


public class ExportRationalizerMetadataTagsToExcelService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportRationalizerMetadataTagsToExcelService";
    
    private static final Log log = LogUtil.getLog(ExportRationalizerMetadataTagsToExcelService.class);
    private User requestor = null;
	private boolean includeImagePathOnly = false;
	private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportRationalizerObjectToExcelServiceRequest request = (ExportRationalizerObjectToExcelServiceRequest) context.getRequest();
            RationalizerApplication app = RationalizerApplication.findById(request.getTargeObjectId());
            if(app == null || app.getParsedDocumentFormDefinition() == null)
            	return;
            
            requestor = request.getRequestor();
            includeImagePathOnly = request.isIncludeImagePathOnly();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            if(!outputDirectory.exists())
            	outputDirectory.mkdirs();
    	    
            XSSFWorkbook workbook = new XSSFWorkbook();
            generateWorkbook(app, workbook, requestor);
            String exportName = app.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook, 
            		requestor, 
            		ExportUtil.ExportType.RATIONALIZERMETADATA, 
            		request.getExportId(),
            		exportName);

            ((ExportRationalizerObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
            log.error(" unexpected exception when invoking ExportRationalizerMetadataTagsToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateWorkbook( RationalizerApplication app, XSSFWorkbook workbook, User requestor ) throws Exception 
    {
    	/*
    	 * Document metadata sheet
    	 */
    	XSSFSheet sheet = workbook.createSheet("Document_Metadata");
    	Row headerRow = sheet.createRow(0);
    	XSSFCellStyle style = workbook.createCellStyle();
    	style.setFillPattern(SOLID_FOREGROUND);
    	style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
    	style.setFillBackgroundColor(IndexedColors.SKY_BLUE.getIndex());
    	style.setFillPattern(SOLID_FOREGROUND);
    	Font font = workbook.createFont();//Create font
		font.setBold(true);//Make font bold
        style.setFont(font);//set it to bold
        
        headerRow.setRowStyle(style);
        headerRow.createCell(0).setCellValue("Metadata");
        headerRow.getCell(0).setCellStyle(style);
        headerRow.createCell(1).setCellValue("Value");
        headerRow.getCell(1).setCellStyle(style);
        headerRow.createCell(2).setCellValue("Count");
        headerRow.getCell(2).setCellStyle(style);
        
		List<RationalizerDocument> documents 							= RationalizerDocument.findByApplication(app);

		Map<String, List<MetadataFormItem>> defItemMap 					= new HashMap<>();
		Map<String, List<MetadataFormItem>> contentDefItemMap 			= new HashMap<>();
		List<MetadataFormItemDefinition> documentFormItemDefinitions 	= new ArrayList<>();
		List<MetadataFormItemDefinition> contentFormItemDefinitions 	= new ArrayList<>();

		for ( RationalizerDocument currentDocument: documents ) {
			
			// DOCUMENT METADATA
			if ( currentDocument.getParsedDocumentForm() != null ) {
				for ( MetadataFormItem currentItem: currentDocument.getParsedDocumentForm().getFormItems() ) {
					MetadataFormItemDefinition currentItemDefinition = currentItem.getItemDefinition();
					if ( !documentFormItemDefinitions.contains(currentItemDefinition) )
						documentFormItemDefinitions.add(currentItemDefinition);
					List<MetadataFormItem> newItemList = new ArrayList<>();
					newItemList.add(currentItem);
					String currentConnector = currentItemDefinition.getPrimaryConnector().trim();
					if ( !defItemMap.containsKey( currentConnector ) )
						defItemMap.put( currentConnector, newItemList );
					else 
						defItemMap.get( currentConnector ).addAll( newItemList);
				}
			}
			
			// CONTENT METADATA
			for ( RationalizerDocumentContent currentContent: currentDocument.shallowCopyOfContents() ) {
			
				if ( currentContent.getParsedContentForm() != null ) {
					for ( MetadataFormItem currentItem: currentContent.getParsedContentForm().getFormItems() ) {
						MetadataFormItemDefinition currentItemDefinition = currentItem.getItemDefinition();
						if ( !contentFormItemDefinitions.contains(currentItemDefinition) )
							contentFormItemDefinitions.add(currentItemDefinition);
						List<MetadataFormItem> newItemList = new ArrayList<>();
						newItemList.add(currentItem);
						String currentConnector = currentItemDefinition.getPrimaryConnector().trim();
						if ( !contentDefItemMap.containsKey( currentConnector ) )
							contentDefItemMap.put( currentConnector, newItemList );
						else 
							contentDefItemMap.get( currentConnector ).addAll( newItemList);
					}
				}
			}
		}
		
		Collections.sort(documentFormItemDefinitions, new IdentifiableMessagepointModelNameComparator());
		Collections.sort(contentFormItemDefinitions, new IdentifiableMessagepointModelNameComparator());
		
		Map<String, List<String>> map = new HashMap<>();
		Map<String, Map<String,Integer>> KeyValueMap = new HashMap<>();
		int rowNum = 1;
		for ( String mapKey: defItemMap.keySet() ) {
			List<String> valuesArray = new ArrayList<>();
			Map<String,Integer> valuesMap = new HashMap<>();
			for ( MetadataFormItem currentFormItem : defItemMap.get(mapKey) ) {
				if ( currentFormItem.getValue() == null )
					continue;
				String[] currentValuesArray = currentFormItem.getValue().split(",");
				
				for ( int i = 0; i < currentValuesArray.length; i++ ) {
					String currentValue = currentValuesArray[i].trim();
					if ( !valuesMap.containsKey( currentValue ) ) {
						valuesMap.put( currentValue, 1 );
						valuesArray.add( currentValue );
					} else {
						valuesMap.put(currentValue, valuesMap.get(currentValue) +1);
					}
				}
			}
			List<String> valuesByCount = new ArrayList<>(valuesArray);
			final Map<String,Integer> finalValuesMap = valuesMap;
		    Collections.sort(valuesByCount, new Comparator<>() {
                public int compare(String o1, String o2) {
                    if (finalValuesMap.get(o1) < finalValuesMap.get(o2)) return -1;
                    if (finalValuesMap.get(o1) > finalValuesMap.get(o2)) return 1;
                    return 0;
                }
            });
		    valuesMap = finalValuesMap;
			map.put(mapKey, valuesByCount);
			Map<String,Integer> sortedValuesMap = new HashMap<>();
		    for ( String key: valuesByCount ) {
		    	if(valuesMap.containsKey(key))
		    		sortedValuesMap.put(key, valuesMap.get(key));
		    }
			KeyValueMap.put(mapKey, sortedValuesMap);
		}

		for ( MetadataFormItemDefinition currentItem : documentFormItemDefinitions ) {
			if ( map.containsKey(currentItem.getPrimaryConnector().trim()) ) {
				Row dataHeaderRow = sheet.createRow(rowNum);
				dataHeaderRow.createCell(0).setCellValue(currentItem.getPrimaryConnector().trim());
				rowNum++;
				for ( String value : map.get(currentItem.getPrimaryConnector().trim()) ) {
					Row dataRow = sheet.createRow(rowNum);
					dataRow.createCell(1).setCellValue(value);
					
					dataRow.createCell(2).setCellValue(KeyValueMap.get(currentItem.getPrimaryConnector().trim()).get(value));
					rowNum++;
					
				}
			}
		}
		
		/*
    	 * Content metadata sheet
    	 */
		XSSFSheet content_sheet = workbook.createSheet("Content_Metadata");
		Row conentHeaderRow = content_sheet.createRow(0);
		conentHeaderRow.setRowStyle(style);
		conentHeaderRow.createCell(0).setCellValue("Metadata");
		conentHeaderRow.getCell(0).setCellStyle(style);
		conentHeaderRow.createCell(1).setCellValue("Value");
		conentHeaderRow.getCell(1).setCellStyle(style);
		conentHeaderRow.createCell(2).setCellValue("Count");
		conentHeaderRow.getCell(2).setCellStyle(style);
		int contentRowColumn = 1;

		Map<String, List<String>> contentMap = new HashMap<>();
		
		Map<String, Map<String,Integer>> contentKeyValueMap = new HashMap<>();
		final String resultForEmptyValue = "<i>" + ApplicationUtil.getMessage("page.label.none") + "</i>";
		for ( String mapKey: contentDefItemMap.keySet() ) {
			List<String> valuesArray = new ArrayList<>();
			Map<String,Integer> contentValuesMap = new HashMap<>();
			for ( MetadataFormItem currentFormItem : contentDefItemMap.get(mapKey) ) {
				if ( currentFormItem.getValue() == null )
					continue;

				String currentValue = currentFormItem.getDisplayValue().trim();

				if (resultForEmptyValue.equals(currentValue)) {
					currentValue = StringUtils.EMPTY;
				}
				currentValue = StringEscapeUtils.unescapeHtml4(currentValue);

				if (!contentValuesMap.containsKey(currentValue)) {
					contentValuesMap.put(currentValue, 1);
					valuesArray.add(currentValue);
				} else {
					contentValuesMap.put(currentValue, contentValuesMap.get(currentValue) + 1);
				}
			}
			List<String> valuesByCount = new ArrayList<>(valuesArray);
			final Map<String,Integer> finalcontentValuesMap = contentValuesMap;
		    Collections.sort(valuesByCount, new Comparator<>() {
                public int compare(String o1, String o2) {
                    if (finalcontentValuesMap.get(o1) < finalcontentValuesMap.get(o2)) return -1;
                    if (finalcontentValuesMap.get(o1) > finalcontentValuesMap.get(o2)) return 1;
                    return 0;
                }
            });
		    contentValuesMap = finalcontentValuesMap;
		    contentMap.put(mapKey, valuesByCount);
		    Map<String,Integer> sortedValuesMap = new HashMap<>();
		    for ( String key:valuesByCount ) {
		    	if ( contentValuesMap.containsKey(key) )
		    		sortedValuesMap.put(key, contentValuesMap.get(key));
		    }
		    contentKeyValueMap.put(mapKey, sortedValuesMap);
		}
		for ( MetadataFormItemDefinition currentItem : contentFormItemDefinitions ) {
			if ( contentMap.containsKey( currentItem.getPrimaryConnector().trim() ) ) {
				Row dataHeaderRow = content_sheet.createRow(contentRowColumn);
				dataHeaderRow.createCell(0).setCellValue( currentItem.getPrimaryConnector().trim() );
				contentRowColumn++;
				for ( String value : contentMap.get( currentItem.getPrimaryConnector().trim() ) ) {
					Row dataRow = content_sheet.createRow( contentRowColumn );
					dataRow.createCell(1).setCellValue(value );
					dataRow.createCell(2).setCellValue( contentKeyValueMap.get(currentItem.getPrimaryConnector().trim()).get(value) );
					contentRowColumn++;
				}
			}
		}
		/*
    	 * Document tags sheet
    	 */
		XSSFSheet documentTags_sheet = workbook.createSheet("Document_tags");
		Row tagHeaderRow = documentTags_sheet.createRow(0);
		tagHeaderRow.setRowStyle(style);
		tagHeaderRow.createCell(0).setCellValue("Tag");
		tagHeaderRow.getCell(0).setCellStyle(style);
		tagHeaderRow.createCell(1).setCellValue("Count");
		tagHeaderRow.getCell(1).setCellStyle(style);
		List<TagCloud> documentTagCloudList = TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_DOCUMENT, -1, app.getId());
		int documentTagRow = 1;
		Map<String,Integer> tagNameMap = new HashMap<>();
		for ( RationalizerDocument doc:RationalizerDocument.findByTag(app) ) {
			if(doc.getMetatags().isEmpty() || doc.getMetatags() == null)
				continue;
			String[] tagValuesArray = doc.getMetatags().split(" ");
			for(String value : tagValuesArray){
				if ( !tagNameMap.containsKey( value.trim() ) ) {
					tagNameMap.put( value.trim(), 1 );
					
				} else {
					tagNameMap.put(value.trim(), tagNameMap.get(value.trim()) +1);
				}
			}
		}
		for ( TagCloud tagCloud : documentTagCloudList ) {
			Row tagRow = documentTags_sheet.createRow(documentTagRow);
			tagRow.createCell(0).setCellValue(tagCloud.getName());
			if ( tagNameMap.containsKey(tagCloud.getName().trim()) )
				tagRow.createCell(1).setCellValue(tagNameMap.get(tagCloud.getName().trim()));
			documentTagRow++;
		}
		/*
    	 * Content tags sheet
    	 */
		XSSFSheet contentTags_sheet = workbook.createSheet("Content_tags");
		Row contentTagHeaderRow = contentTags_sheet.createRow(0);
		contentTagHeaderRow.setRowStyle(style);
		contentTagHeaderRow.createCell(0).setCellValue("Tag");
		contentTagHeaderRow.getCell(0).setCellStyle(style);
		contentTagHeaderRow.createCell(1).setCellValue("Count");
		contentTagHeaderRow.getCell(1).setCellStyle(style);
		List<TagCloud> contentTagCloudList = TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_CONTENT, -1, app.getId());
		int contentTagRow = 1;
		Map<String,Integer> contentTagNameMap = new HashMap<>();
		for(RationalizerDocumentContent content:RationalizerDocumentContent.findByTag(app)){
			if(content.getMetatags().isEmpty() || content.getMetatags() == null || content.getRationalizerDocument().getRationalizerApplication() != app)
				continue;
			String[] tagValuesArray = content.getMetatags().split(" ");
			for(String value : tagValuesArray){
				if ( !contentTagNameMap.containsKey( value.trim() ) ) {
					contentTagNameMap.put( value.trim(), 1 );
					
				} else {
					contentTagNameMap.put(value.trim(), contentTagNameMap.get(value.trim()) +1);
				}
			}
		}
		for(TagCloud tagCloud : contentTagCloudList){
			Row tagRow = contentTags_sheet.createRow(contentTagRow);
			tagRow.createCell(0).setCellValue(tagCloud.getName());
			if(contentTagNameMap.containsKey(tagCloud.getName().trim()))
				tagRow.createCell(1).setCellValue(contentTagNameMap.get(tagCloud.getName().trim()));
			contentTagRow++;
		}
    }
    
	public boolean getIncludeImagePathOnly()
	{
		return includeImagePathOnly;
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long imageId, boolean includeImagePathOnly, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportRationalizerObjectToExcelServiceRequest request = new ExportRationalizerObjectToExcelServiceRequest(exportId, imageId, null, includeImagePathOnly, requestor);

        context.setRequest(request);

        ExportRationalizerObjectToExcelServiceResponse serviceResp = new ExportRationalizerObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
    
    public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
}
