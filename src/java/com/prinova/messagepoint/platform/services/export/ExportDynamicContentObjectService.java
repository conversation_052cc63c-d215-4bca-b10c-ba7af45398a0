package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.export.utils.DynamicXMLExportUtils;
import com.prinova.messagepoint.platform.services.export.utils.ExcelExportUtils;
import com.prinova.messagepoint.util.LogUtil;

import java.util.*;

public class ExportDynamicContentObjectService extends AbstractService {
    public static final String SERVICE_NAME = "export.ExportDynamicContentObjectService";

    private ExportDynamicContentObjectServiceRequest request = null;
    private ContentObject contentObject = null;

    public static ServiceExecutionContext createContext(ContentObject targetContentObject, String exportId, ExportUtil.ExportFormat format, boolean includeImagePathOnly, User user) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportDynamicContentObjectServiceRequest req = ExportDynamicContentObjectServiceRequest.createNewRequest(targetContentObject,
                exportId,
                format,
                includeImagePathOnly,
                user);

        context.setRequest(req);

        return context;
    }

    @Override
    public void execute(ServiceExecutionContext context) {
        validate(context);
        if (hasValidationError(context))
        {
            return;
        }

        try {
            request = (ExportDynamicContentObjectServiceRequest) context.getRequest();

            contentObject = request.getContentObject();

            if (contentObject == null) {
                return;
            }

            Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> allAssociations = ContentObjectVariantContentExport.loadContentObjectVariantContent(contentObject);

            String reportPath = null;

            ExportUtil.ExportType exportType = getExportType(contentObject);

            switch (exportType) {
                case DYNAMICIMAGE:
                    reportPath = generateDynamicImageExport(contentObject, allAssociations, request.getExportFormat());
                    break;
                case DYNAMICMESSAGE:
                    reportPath = generateDynamicMessageExport(contentObject, allAssociations);
                    break;
                case DYNAMICSMARTTEXT:
                case DYNAMICSMARTCANVAS:
                    reportPath = generateDynamicSmartTextExport(contentObject, allAssociations);
                    break;
            }

            ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
            serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
            serviceResp.setFilePath(reportPath);
            context.setResponse(serviceResp);
        } catch (Exception e) {
            LogUtil.getLog(ExportDynamicContentObjectService.class).error("Error", e);
        }
    }

    private String generateDynamicMessageExport(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData) throws Exception {

        if (request.getExportFormat() == ExportUtil.ExportFormat.EXCEL) {
            byte[] exportFileContent = ExcelExportUtils.generateDynamicMessageExcelWorkbook(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName(),
                    ExportUtil.EXTENSION_EXCEL_TYPE);
        } else {
            org.dom4j.Document exportFileContent = DynamicXMLExportUtils.generateDynamicMessageXML(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportXMLToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName());
        }

    }

    private String generateDynamicImageExport(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, ExportUtil.ExportFormat exportFormat) throws Exception {

        if (request.getExportFormat() == ExportUtil.ExportFormat.EXCEL) {
            byte[] exportFileContent = ExcelExportUtils.generateDynamicImageLibraryExcelWorkbook(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName(),
                    ExportUtil.EXTENSION_EXCEL_TYPE);
        } else {
            org.dom4j.Document exportFileContent = DynamicXMLExportUtils.generateDynamicImageLibraryXML(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportXMLToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName());
        }

    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    private String generateDynamicSmartTextExport(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData) throws Exception {

        if (request.getExportFormat() == ExportUtil.ExportFormat.EXCEL) {
            byte[] exportFileContent = ExcelExportUtils.generateDynamicSmartTextExcelWorkbook(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName(),
                    ExportUtil.EXTENSION_EXCEL_TYPE);
        } else {
            org.dom4j.Document exportFileContent = DynamicXMLExportUtils.generateDynamicSmartTextXML(contentObject, variantData, request);
            return ExportUtil.saveMessagepointObjectExportXMLToFile(exportFileContent,
                    this.request.getRequestor(),
                    getExportType(contentObject),
                    request.getExportId(),
                    contentObject.getName());
        }

    }

    public static ExportUtil.ExportType getExportType(ContentObject exportObject) {

        if (exportObject.isLocalSmartText() || exportObject.isGlobalSmartText()) {
            return ExportUtil.ExportType.DYNAMICSMARTTEXT;
        } else if (exportObject.isLocalSmartCanvas() || exportObject.isGlobalSmartCanvas()) {
            return ExportUtil.ExportType.DYNAMICSMARTCANVAS;
        } else if (exportObject.isLocalImage() || exportObject.isGlobalImage()) {
            return ExportUtil.ExportType.DYNAMICIMAGE;
        } else {
            return ExportUtil.ExportType.DYNAMICMESSAGE;
        }


    }
}

