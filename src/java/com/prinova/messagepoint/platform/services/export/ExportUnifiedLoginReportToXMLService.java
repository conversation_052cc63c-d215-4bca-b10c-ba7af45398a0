package com.prinova.messagepoint.platform.services.export;

import java.io.File;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.UnifiedLoginReport;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class ExportUnifiedLoginReportToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportUnifiedLoginReportToXMLService";
	
	private static final Log log = LogUtil.getLog(ExportUnifiedLoginReportToXMLService.class);
	
	public static final String TRANSITION_FILE_EXT 	= ".inProcess";
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context))
				return;

			ExportUnifiedLoginReportToXMLServiceRequest request = (ExportUnifiedLoginReportToXMLServiceRequest) context.getRequest();
			
			// *****************************************
			// ********* Generate XML ******************
			// *****************************************
			UnifiedLoginReport unifiedLoginReport = new UnifiedLoginReport();
			unifiedLoginReport.setRequestor(request.getUser());
			unifiedLoginReport.setIncludePincLicenseStatus(request.isIncludePincLicenseStatus());
			unifiedLoginReport.generateUnifiedLoginReportXML();
			
			String inProcessReportFilePath = UnifiedLoginReport.getReportXmlFilePath(request.getReportId(), request.getUser().getId()) + TRANSITION_FILE_EXT;
			String completedReportFilePath = UnifiedLoginReport.getReportXmlFilePath(request.getReportId(), request.getUser().getId());
			unifiedLoginReport.writeToXMLFile( inProcessReportFilePath );
			File inProcessReportFile = new File( inProcessReportFilePath );
			inProcessReportFile.renameTo( new File(completedReportFilePath) );
			context.getResponse().setResultValueBean(request.getReportId());

		}catch (Exception e){
			log.error(" unexpected exception when invoking ExportUnifiedLoginReportToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(long reportId, User user, boolean includePincLicenseStatus)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportUnifiedLoginReportToXMLServiceRequest request = new ExportUnifiedLoginReportToXMLServiceRequest();

		request.setReportId(reportId);
		request.setUser(user);
		request.setIncludePincLicenseStatus(includePincLicenseStatus);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}
