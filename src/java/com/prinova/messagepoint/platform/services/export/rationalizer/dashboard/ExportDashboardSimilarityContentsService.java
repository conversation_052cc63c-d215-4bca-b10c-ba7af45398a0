package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MAX_SIMILARITY;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MIN_SIMILARITY_FOR_DASHBOARD;

public class ExportDashboardSimilarityContentsService extends ExportDashboardContentsService {

    @Override
    public List<ReportContentDTO> getContentsForReport(ExportRationalizerDashboardToExcelServiceRequest request, RationalizerApplication application) {
        List<ReportContentDTO> contentList = new ArrayList<>();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(application);

        List<String> selectedIds = request.getSelectedIds();
        if (CollectionUtils.isEmpty(selectedIds)) {
            List<Map<String, String>> sourceMetadataFiltersList = RationalizerUtil.processDashboardTreeSelections(application.getId(), request.getSourceSelectionBranches());
            List<Map<String, String>> targetMetadataFiltersList = RationalizerUtil.processDashboardTreeSelections(application.getId(), request.getTargetSelectionBranches());
            List<DashboardFilter> dashboardResultsFilterList = application.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);

            Map<String, Integer> similaritiesMap = rationalizerElasticSearchHandler.searchSimilaritiesCountsWithCompareContext(
                    sourceMetadataFiltersList, targetMetadataFiltersList, dashboardResultsFilterList, MIN_SIMILARITY_FOR_DASHBOARD, MAX_SIMILARITY);

            if (MapUtils.isNotEmpty(similaritiesMap)) {
                List<RationalizerContent> contents = RationalizerContent.findFirstRationalizerContentsByHashes(
                        rationalizerElasticSearchHandler,
                        similaritiesMap.keySet()
                );

                for (RationalizerContent content : contents) {
                    String currentHash = content.getHashCode();
                    String text = content.getTextContent();
                    String id = content.buildElasticSearchGuid();

                    contentList.add(new ReportContentDTO(currentHash, text, id));
                }
            }
        } else {
            final List<RationalizerContent> rationalizerContents = RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);
            for (RationalizerContent rationalizerContent : rationalizerContents) {
                contentList.add(new ReportContentDTO(rationalizerContent.getHashCode(), rationalizerContent.getTextContent(), rationalizerContent.buildElasticSearchGuid()));
            }
        }

        return contentList;
    }
}
