package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.ArrayList;
import java.util.List;

public class GenerateSmartTextAuditReportServiceRequest extends SimpleServiceRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1866447469567342103L;
	/**
	 * 
	 */
	
	private long auditReportId;
	private List<Long> list = new ArrayList<>();
	private User requestor;
	private int auditReportTypeId;
	
	public long getAuditReportId() {
		return auditReportId;
	}
	public void setAuditReportId(long auditReportId) {
		this.auditReportId = auditReportId;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public List<Long> getList() {
		return list;
	}
	public void setList(List<Long> list) {
		this.list = list;
	}
	public int getAuditReportTypeId() {
		return auditReportTypeId;
	}
	public void setAuditReportTypeId(int auditReportTypeId) {
		this.auditReportTypeId = auditReportTypeId;
	}
}