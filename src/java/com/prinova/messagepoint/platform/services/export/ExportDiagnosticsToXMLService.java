package com.prinova.messagepoint.platform.services.export;

import java.io.File;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.DiagnosticsReport;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class ExportDiagnosticsToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportDiagnosticsToXMLService";
	
	private static final Log log = LogUtil.getLog(ExportDiagnosticsToXMLService.class);
	
	public static final String TRANSITION_FILE_EXT 	= ".inProcess";
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context))
				return;

			ExportDiagnosticsToXMLServiceRequest request = (ExportDiagnosticsToXMLServiceRequest) context.getRequest();
			
			// *****************************************
			// ********* Generate XML ******************
			// *****************************************
			DiagnosticsReport diagnosticsReport = new DiagnosticsReport();
			diagnosticsReport.setRequestor(request.getUser());
			diagnosticsReport.generateDiagnosticsXML();
			
			String completedReportFilePath = DiagnosticsReport.getReportXmlFilePath(request.getReportId(), request.getUser().getId());
			String inProcessReportFilePath = completedReportFilePath + TRANSITION_FILE_EXT;
			
			diagnosticsReport.writeToXMLFile( inProcessReportFilePath );
			File inProcessReportFile = new File( inProcessReportFilePath );
			inProcessReportFile.renameTo( new File(completedReportFilePath) );
					
			context.getResponse().setResultValueBean(request.getReportId());

		}catch (Exception e){
			log.error(" unexpected exception when invoking ExportDiagnosticsToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(long reportId, User user)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportDiagnosticsToXMLServiceRequest request = new ExportDiagnosticsToXMLServiceRequest();

		request.setReportId(reportId);
		request.setUser(user);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}
