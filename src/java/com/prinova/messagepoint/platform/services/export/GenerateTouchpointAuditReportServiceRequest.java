package com.prinova.messagepoint.platform.services.export;

import java.util.Date;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class GenerateTouchpointAuditReportServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = 5751258636753621202L;
	
	private long auditReportId;
	private User requestor;
	private Date fromDate;
	private Date toDate;
	private Document document;
	
	public long getAuditReportId() {
		return auditReportId;
	}
	public void setAuditReportId(long auditReportId) {
		this.auditReportId = auditReportId;
	}	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}		
	public Date getFromDate() {
		return fromDate;
	}
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	public Date getToDate() {
		return toDate;
	}
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}	
	public Document getDocument(){
		return document;
	}
	public void setDocument(Document document){
		this.document = document;
	}
}
