package com.prinova.messagepoint.platform.services.export;

import java.util.*;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.project.ProjectTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.workflow.ApprovalType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionHistory;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionStateType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowApprovalDetail;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.StringUtil;

public class ExportProjectToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportProjectToXMLService";
	private static final Log log = LogUtil.getLog(ExportProjectToXMLService.class);

    private User requestor;
	@SuppressWarnings("unused")
	private boolean includeAllProjects = false;
	private long reportTimestamp;
	private List<Long> projectList;
	
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportProjectToXMLServiceRequest request = (ExportProjectToXMLServiceRequest) context.getRequest();
	    	requestor = request.getRequestor();
	    	projectList =  request.getProjectList();
	    	includeAllProjects = request.isIncludeAllProjects();
	    	reportTimestamp = request.getReportTimestamp();	
			if(reportTimestamp <= 0){
				reportTimestamp = System.currentTimeMillis();
			}

			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			try{
				generateXML(request, document);
			}catch (Exception e){
				this.getResponse(context).addErrorMessage(
				        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
						context.getLocale() );
				
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(null);
			}
			String filePath = ExportUtil.saveXMLToFile(document, requestor, AuditReportType.ID_PROJECT_AUDIT_REPORT, reportTimestamp);
			if(filePath != null)
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);

		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportProjectToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	private org.dom4j.Document initializeWriter( )
	{
		return DocumentHelper.createDocument();
	}

	private void generateXML(ExportProjectToXMLServiceRequest request,org.dom4j.Document document) throws Exception 
	{
		requestor = User.findById(requestor.getId());
		
		Element prjAuditElm = document.addElement("ProjectAudit");
		prjAuditElm.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		//prjAuditElm.addAttribute("type", "audit");
		// - Build the "Metadata" element
		buildMetaDataElm(prjAuditElm, requestor);
		
		//List<Project> projectList = this.projectList;
		//Collections.sort(projectList, new ObjectNameComparator());
		
				
		for (Long projectId : projectList) 
		{
			Project project = Project.findById(projectId);
			Element prjElm = prjAuditElm.addElement("Project");
			prjElm.addAttribute("id", Long.toString(project.getId()));
			
			// Project Details
			Element nameElement = prjElm.addElement("Name");
			nameElement.addText(project.getName());	
			
			Element descElement = prjElm.addElement("Requirement");
			descElement.addText(project.getDescription() != null ? StringUtil.truncate(project.getDescription(), 100) : "");	
			
			Element dueElement = prjElm.addElement("DueDate");
			dueElement.addText((project.getDueDate() != null? project.getDueDate().toString(): ""));
			
			Element ownerElement = prjElm.addElement("Owner");
			User owner = User.findById(project.getOwnerId());
			ownerElement.addText(owner.getName());	
			MetadataForm metadataForm = null; 
			if(project.getMetadataForm() != null)
				metadataForm = MetadataForm.findById(project.getMetadataForm().getId());
			// Project MetadataForm
			if(metadataForm != null && !metadataForm.getFormItemsInOrder().isEmpty()){
				Element metaItemsEle = prjElm.addElement("MetadataFormItems");
				for(MetadataFormItem item:metadataForm.getFormItemsInOrder()){
					Element itemElm = metaItemsEle.addElement("MetadataformItem");
					itemElm.addAttribute("id", Long.toString(item.getId()));
					
					Element itemLabelElement = itemElm.addElement("ItemLabel");
					itemLabelElement.addText(item.getItemDefinition().getName());
					Element itemValueElement = itemElm.addElement("ItemValue");
					itemValueElement.addText((item.getValue() == null? "": item.getValue()));
				}
			}
			
			if(project.getWorkflowInstance() != null && !project.getWorkflowInstance().getWorkflowStepsOrdered().isEmpty()){
				ConfigurableWorkflowInstance wfInstance = ConfigurableWorkflowInstance.findById(project.getWorkflowInstance().getId());
				Element wfStepsEle = prjElm.addElement("WorkFlowSteps");
				for(ConfigurableWorkflowStep wfStep :wfInstance.getWorkflowStepsOrdered()){
					Element wfStepEle = wfStepsEle.addElement("WorkFlowStep");
					wfStepEle.addAttribute("id", Long.toString(wfStep.getId()));
					Element wfStepNameEle = wfStepEle.addElement("Name");
					String state = wfStep.getState();
					wfStepNameEle.addText(state);
				}
			}
			
			// Project Tasks
			
			List<ProjectTask> tasks = project.getProjectTasksOrdered();
			Element tasksEle = prjElm.addElement("ProjectTasks");
//			boolean hasTaskViewAllPermission 	= UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL);
			for (ProjectTask prjTask : tasks) {
				Task task = Task.findById(prjTask.getTask().getId());
//				if ( task.isMine() || (!task.isMine() && hasTaskViewAllPermission) ) {
					
					Element taskEle = tasksEle.addElement("ProjectTask");
					taskEle.addAttribute("id", Long.toString(task.getId()));
					
					Element taskNameElement = taskEle.addElement("ObjectName");
					taskNameElement.addText(task.getItemName());
					Element taskRequirementElement = taskEle.addElement("Requirement");
					taskRequirementElement.addText(task.getRequirement() != null ? StringUtil.truncate(new String(task.getRequirement(), "UTF-8"), 100) : "" );
					Element createdDateElement = taskEle.addElement("CreatedDate");
					createdDateElement.addText(task.getCreatedDate() == null? "":DateUtil.formatDateForXMLOutput(task.getCreated()));
					Element taskDueElement = taskEle.addElement("DueDate");
					taskDueElement.addText(task.getDueDate() == null? "":task.getDueDate().toString());
					long currentStepId = 0L;
					// Status Description
					// Complete
					String statusDesc = ApplicationUtil.getMessage("page.label.pending.initial.task.complete");
					if(task.getWorkflowAction() != null){
						if(task.getWorkflowAction().isActive()){
							statusDesc = ApplicationUtil.getMessage("page.label.complete");
						}else{
							if(!task.isComplete()){
								statusDesc = ApplicationUtil.getMessage("page.label.pending.task.complete");
							}else{
								statusDesc = ApplicationUtil.getMessage("page.label.pending");
								statusDesc = statusDesc.concat("" + task.getWorkflowAction().getConfigurableWorkflowStep().getState());
							}
						}
						currentStepId = task.getWorkflowAction().getConfigurableWorkflowStep().getId();
						taskEle.addAttribute("currentstepid", Long.toString(currentStepId));
						
					}else{
						taskEle.addAttribute("currentstepid", Long.toString(0L));
					}
					
					Element statusElement = taskEle.addElement("Status");
					statusElement.addText(!task.isComplete() ? "Active" : "Complete" );
					Element statusDscElement = taskEle.addElement("StatusDescription");
					statusDscElement.addText(statusDesc);
					
					Element taskownerElement = taskEle.addElement("Assignee");
					User taskOwner = User.findById(task.getAssignee().getId());
					taskownerElement.addText(taskOwner.getName());	
					
					
					
					if(!task.getPreviousActionListWithLatestInStep().isEmpty()){
						Element approvalsElement = taskEle.addElement("Approvals");
						List<ConfigurableWorkflowActionHistory> msgActionHisList = ConfigurableWorkflowActionHistory.findAllByModel(HibernateUtil.getManager().getObject(Task.class, task.getId()));
						ConfigurableWorkflowActionHistory.sortByChronOrder(msgActionHisList, true);
						buildApprovalHistElm(approvalsElement, task, msgActionHisList);
					}
					if(!prjTask.getAppliedWorkflowSteps().isEmpty()){
						
						Element taskStepsEle = taskEle.addElement("TaskWFSteps");
						for(ConfigurableWorkflowStep wfStep :prjTask.getAppliedWorkflowSteps()){
							Element taskStepEle = taskStepsEle.addElement("TaskWFStep");
							taskStepEle.addAttribute("id", Long.toString(wfStep.getId()));
							taskStepEle.addAttribute("taskid", Long.toString(prjTask.getTask().getId()));
							taskStepEle.addAttribute("status", prjTask.getTask().getStatus());
							if(currentStepId == wfStep.getId() && !prjTask.getTask().isComplete() && prjTask.getTask().getAssignee() != null){
								Element assigneeEle = taskStepEle.addElement("Assigned");
								assigneeEle.addText(prjTask.getTask().getAssignee().getName());
							}
							Element approversEle = taskStepEle.addElement("Approvers");
							for(User approver:wfStep.getApprovalUsers()){
								Element approverEle = approversEle.addElement("Approver");
								approverEle.addAttribute("id", Long.toString(approver.getId()));
								Element approverNameElement = approverEle.addElement("Name");
								approverNameElement.addText(approver.getName());
							}
						}
					}
					// Task Metadataform
					if(task.getMetadataForm() != null && !task.getMetadataForm().getFormItemsInOrder().isEmpty()){
						Element taskmetaEle = taskEle.addElement("TaskMetadataFormItems");
						
						for(MetadataFormItem item:task.getMetadataForm().getFormItemsInOrder()){
							Element taskitemElm = taskmetaEle.addElement("TaskMetadataformItem");
							taskitemElm.addAttribute("id", Long.toString(item.getId()));
							
							Element taskItemLabelElement = taskitemElm.addElement("ItemLabel");
							taskItemLabelElement.addText(item.getItemDefinition().getName());
							Element taskItemValueElement = taskitemElm.addElement("ItemValue");
							taskItemValueElement.addText(item.getValue() != null? item.getValue():"");
						}
					}
					// Task Workflow
//						if(task.getWorkflowAction().getConfigurableWorkflowStep() != null){
//							
//						}
					// Global Smart text
					if(!task.getGlobalSmartTexts().isEmpty()){
						Element taskSTsEle = taskEle.addElement("TaskSmartTexts");
						
						for(ContentObject item:task.getGlobalSmartTexts()){
							Element taskSTElm = taskSTsEle.addElement("TaskSmartText");
							taskSTElm.addAttribute("id", Long.toString(item.getId()));
							Element taskSTLabelElement = taskSTElm.addElement("Name");
							taskSTLabelElement.addText(item.getName());
						}
					}
					
					// Global Image library
					if(!task.getGlobalImages().isEmpty()){
						Element taskILsEle = taskEle.addElement("TaskImageLibraries");
						
						for(ContentObject item:task.getGlobalImages()){
							Element taskILElm = taskILsEle.addElement("TaskImageLibrary");
							taskILElm.addAttribute("id", Long.toString(item.getId()));
							Element taskILLabelElement = taskILElm.addElement("Name");
							taskILLabelElement.addText(item.getName());
						}						
					}
					
					// Target groups
					if(!task.getTargetGroups().isEmpty()){
						Element targetGroupsEle = taskEle.addElement("TargetGroups");
						
						for(TargetGroup item:task.getTargetGroups()){
							Element targetGroupEle = targetGroupsEle.addElement("TargetGroup");
							targetGroupEle.addAttribute("id", Long.toString(item.getId()));
							Element taskgroupNameElement = targetGroupEle.addElement("Name");
							taskgroupNameElement.addText(item.getName());
						}
					}
					List<Insert> inserts = new ArrayList<>();
					Map<Long, List<InsertSchedule>> docInsertSchMap = new HashMap<>();
					for(IdentifiableMessagePointModel item : task.getItems()){
						if(item instanceof Insert){
							inserts.add((Insert)item);
						}
						if(item instanceof InsertSchedule){
							InsertSchedule insertSchedule = (InsertSchedule)item;
							Long docId = insertSchedule.getScheduleCollection().getDocument().getId();
							if(!docInsertSchMap.containsKey(docId)){
								docInsertSchMap.put(docId, new ArrayList<>());
							}
							docInsertSchMap.get(docId).add(insertSchedule);
						}
					}
					if(!inserts.isEmpty()){
						Element insertsEle = taskEle.addElement("Inserts");
						
						for(Insert item:inserts){
							Element insertEle = insertsEle.addElement("Insert");
							insertEle.addAttribute("id", Long.toString(item.getId()));
							Element insertNameEle = insertEle.addElement("Name");
							insertNameEle.addText(item.getName());
						}
					}
					if(!task.getDocuments().isEmpty()){
						Element tpsEle = taskEle.addElement("Touchpoints");
						for(Document doc: task.getDocuments()){
							Element tpEle = tpsEle.addElement("Touchpoint");
							tpEle.addAttribute("id", Long.toString(doc.getId()));
							Element tpLabelElement = tpEle.addElement("Name");
							tpLabelElement.addText(doc.getName());
							
							// TP Messages
							if(!task.getDocMessages(doc.getId()).isEmpty()){
								Element msgsEle = tpEle.addElement("Messages");
								
								for(ContentObject item:task.getDocMessages(doc.getId())){
									Element msgEle = msgsEle.addElement("Message");
									msgEle.addAttribute("id", Long.toString(item.getId()));
									Element msgNameElement = msgEle.addElement("Name");
									msgNameElement.addText(item.getName());
								}
							}
							if(!task.getDocLocalSmartTextsAndImages(doc.getId()).isEmpty()){
								Element localTxstEle = tpEle.addElement("LocalTexts");
								
								for(ContentObject item:task.getDocLocalSmartTextsAndImages(doc.getId())){
									Element localTxtEle = localTxstEle.addElement("LocalText");
									localTxtEle.addAttribute("id", Long.toString(item.getId()));
									Element localTxtNameElement = localTxtEle.addElement("Name");
									localTxtNameElement.addText(item.getName());
								}
							}
							if(!task.getDocSelections(doc.getId()).isEmpty()){
								Element selectionsEle = tpEle.addElement("Selections");
								
								for(TouchpointSelection item:task.getDocSelections(doc.getId())){
									Element selectionEle = selectionsEle.addElement("Selection");
									selectionEle.addAttribute("id", Long.toString(item.getId()));
									Element selectionNameElement = selectionEle.addElement("Name");
									selectionNameElement.addText(item.getName());
								}						
							}
							// Insert Schedule
							if(docInsertSchMap.containsKey(doc.getId()) && !docInsertSchMap.get(doc.getId()).isEmpty()){
								Element insertSchedulesEle = tpEle.addElement("InsertSchedules");
								
								for(InsertSchedule item:docInsertSchMap.get(doc.getId())){
									Element insertScheduleEle = insertSchedulesEle.addElement("InsertSchedule");
									insertScheduleEle.addAttribute("id", Long.toString(item.getId()));
									Element insertScheduleNameEle = insertScheduleEle.addElement("Name");
									insertScheduleNameEle.addText(item.getName());
								}	
							}
						}
					}
//				}
			}
		}
	}

	/**
	 * Build the "Metadata" element
     */
	private void buildMetaDataElm(Element parentElm, User requestor){
		// Build the "Metadata" element
		Element metadataElm = DocumentHelper.createElement("Metadata");
		parentElm.add(metadataElm);
		// - Build the "User" element
		metadataElm.add(DocumentHelper.createElement("User").addText(requestor.getName()));
		// - Build the "RequestDate" element
		metadataElm.add(DocumentHelper.createElement("RequestDate").addText(DateUtil.formatDateForXMLOutput(DateUtil.now())));

		Element intanceNameElement = metadataElm.addElement("InstanceName");
		intanceNameElement.addText(Node.getCurrentNodeName());

		Element systemDefaultLocaleElement = metadataElm.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		metadataElm.add(DocumentHelper.createElement("ReportType").addText("Project Audit Report"));
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, Task instance, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			Element descriptElm = verHisElm.addElement("Descript");
			if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
				User user = User.findById(actionHis.getUserId());
				if (user != null)
					descriptElm.addAttribute("user", user.getName());
				else
					descriptElm.addAttribute("user", "Uknown");
			}else{	// Auto approve user
				descriptElm.addAttribute("user", "System");
			}
			if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation
				descriptElm.addAttribute("action", "Activated");
			}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
				descriptElm.addAttribute("action", "Release for Approval");
			}else{
				if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
					descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}else{	// Auto approve user
					descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}
			}
			if(actionHis.getAssignedTo() != null){
				User user = User.findById(actionHis.getAssignedTo());
				if (user != null)
					descriptElm.addAttribute("assignedTo", user.getName());
			}
			if(actionHis.getNotes() != null){
				descriptElm.addAttribute("notes", actionHis.getNotes());
			}
			descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
			switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
						if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
							descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
						}else{	// All of
							if(!wfAction.isActionApproved()){
								descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
							}else{
								if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
									descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
								}else{							
									// If action is approved but the user is not the last to approve, stay in current state
									List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
									Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                        public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                            Date approvedDate1 = o1.getApprovedDate();
                                            Date approvedDate2 = o2.getApprovedDate();
                                            if (approvedDate1 == null && approvedDate2 == null) {
                                                return 0;
                                            } else if (approvedDate1 == null) {
                                                return 1;
                                            } else if (approvedDate2 == null) {
                                                return -1;
                                            } else {
                                                return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                            }
                                        }
                                    });
									if(!approvalDetailsSorted.isEmpty()){	// At least one approver
										for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
											ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
											if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
												continue;
											}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												break;
											}else{
												if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
													descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
												}else{
													descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												}
												break;
											}
										}
									}
								}
							}
						}	
					}
				}
			}
		}	
	}

	
	public static ServiceExecutionContext createContext(
	        List<Long> projectList,
	        boolean includeAllProjects,
			User requestor,
			long reportTimestamp)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportProjectToXMLServiceRequest request = new ExportProjectToXMLServiceRequest();

		
		request.setProjectList(projectList);
		request.setIncludeAllProjects(includeAllProjects);
		request.setRequestor(requestor);
		request.setReportTimestamp(reportTimestamp);

		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
}
