package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentSection;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tenant.Tenant;
import com.prinova.messagepoint.model.util.ReportsURIResolver;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.PathUtil;
import com.prinova.messagepoint.util.StringUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.prinova.messagepoint.util.RationalizerUtil.PATH_SEPARATOR;

public class ExportUtil {
    private static final Log log = LogUtil.getLog(ExportUtil.class);

    public enum ExportType {
        DOCUMENT,
        DYNAMICIMAGE,
        DYNAMICSMARTTEXT,
        DYNAMICMESSAGE,
        TPVARIANTS,
        TPMESSAGES,
        VARIANTMETADATA,
        PROJECT,
        IMAGELIBRARY,
        SMARTTEXT,
        RATIONALIZERDOCUMENT,
        RATIONALIZERQEURY,
        RATIONALIZERMETADATA,
        USER,
        ROLE,
        MESSAGESWF,
        JOBPERFORMANCEREPORT,
        CONNECTED,
        RATIONALIZER_DASHBOARD_DUP,
        DYNAMICSMARTCANVAS,
        RATIONALIZER_DASHBOARD_SIM,
        VARIANTCONTENT
    }

    public enum ExportFormat {
        XML,
        EXCEL
    }

    private static final Pattern injectI18NcharsPattern = Pattern.compile(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"));

    private static final String EXTENSION_XML = "xml";
    private static final String EXTENSION_HTML = "html";
    private static final String EXTENSION_EXCEL = "xlsx";
    private static final String EXTENSION_ZIP = "zip";

    public static final int EXTENSION_XML_TYPE = 0;
    public static final int EXTENSION_HTML_TYPE = 1;
    public static final int EXTENSION_EXCEL_TYPE = 2;

    public static final String EXPORT_OBJECT_TOUCHPOINT = "TP";
    public static final String EXPORT_OBJECT_DYNAMIC_IMAGE = "DI";
    public static final String EXPORT_OBJECT_DYNAMIC_SMARTTEXT = "DST";
    public static final String EXPORT_OBJECT_DYNAMIC_SMARTCANVAS = "DSC";
    public static final String EXPORT_OBJECT_DYNAMIC_MESSAGE = "DM";
    public static final String EXPORT_OBJECT_TP_VARIANTS = "TPV";
    public static final String EXPORT_OBJECT_TP_MESSAGES = "TPM";
    public static final String EXPORT_OBJECT_VARIANT_METADATA = "VM";
    public static final String EXPORT_OBJECT_PROJECT = "PJ";
    public static final String EXPORT_OBJECT_IMAGE_LIBRARY = "IL";
    public static final String EXPORT_OBJECT_SMART_TEXT = "ST";
    public static final String EXPORT_OBJECT_RATIONALIZER_DOCUMENT = "RDOC";
    private static final String EXPORT_OBJECT_RATIONALIZER_QUERY = "RQRY";
    private static final String EXPORT_OBJECT_RATIONALIZER_METADATA = "RMD";
    public static final String EXPORT_OBJECT_USER_UPDATE = "USERUP";
    public static final String EXPORT_OBJECT_ROLE = "ROLE";
    private static final String EXPORT_OBJECT_MESSAGES_WF = "MWF";
    public static final String EXPORT_OBJECT_JOB_PERFORMANCE_REPORT = "JOBPERFREP";
	public final static String EXPORT_OBJECT_CONNECTED					= "CNN";
    public static final String EXPORT_OBJECT_RATIONALIZER_DASHBOARD_DUPLICATES = "DUP";
    public static final String EXPORT_OBJECT_RATIONALIZER_DASHBOARD_SIMILARITIES = "SIM";
    public static final String EXPORT_OBJECT_VARIANT_CONTENT = "VC";

    private static final String AUDIT_REPORTS_FOLDER = "auditreports";

    public static final String REPORTS_FOLDER = "reports";
    public static final String EXCEL_EXPORT_TEMPLATE_DYNAMIC_IMAGE = "dynamic_image_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_DYNAMIC_SMARTTEXT = "dynamic_smart_text_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_DYNAMIC_MESSAGE = "dynamic_message_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_TP_VARIANTS = "tp_variants_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_TP_MESSAGES = "tp_messages_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_VARIANT_METADATA = "variant_metadata_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_RATIONALIZER_DOCUMENT = "rationalizer_document_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_RATIONALIZER_RESULT = "rationalizer_result_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_RATIONALIZER_METADATA = "rationalizer_metadata_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_USERS_UPDATE = "users_update_export_template.xlsx";
    public static final String EXPORT_EXCEL_TEMPLATES_DIR = ApplicationUtil.getRootPath() + "/WEB-INF/templates/";
    public static final String EXCEL_EXPORT_TEMPLATE_ROLES = "roles_export_template.xlsx";
    public static final String EXCEL_EXPORT_TEMPLATE_MESSAGES_APPROVAL = "messages_approval_export_template.xlsx";

    private static final String RATIONALIZER_ORIGINAL_DOCUMENTS_ZIP_PATH = "original_documents/";
    private static final String DOT = ".";


    public static String saveMessagepointObjectExportXMLToFile(org.dom4j.Document document, User user, ExportUtil.ExportType type,
                                                               String uniqueId, String exportName) throws Exception {
        // TODO:  Create new export dir key
        File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
        String filename = getMessagepointObjectExportFileName(user, type, uniqueId, exportName, EXTENSION_XML_TYPE);
        outputDirectory.mkdirs();

        String outputFilePath = outputDirectory.getPath() + File.separator + filename;
        File file = new File(outputFilePath);
        OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(file), ApplicationLanguageUtils.XML_ENCODING);
        OutputFormat prettyFormat = OutputFormat.createPrettyPrint();
        prettyFormat.setEncoding(ApplicationLanguageUtils.XML_ENCODING);
        XMLWriter writer = new XMLWriter(fileWriter, prettyFormat);

        writer.write(document);
        writer.close();

        return outputFilePath;
    }

    public static String saveMessagepointObjectExportToFile(byte[] fileContent, User user, ExportUtil.ExportType type, String uniqueId, String exportName, int extensionType) throws Exception {
        // TODO:  Create new export dir key
        File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
        String filename = getMessagepointObjectExportFileName(user, type, uniqueId, exportName, extensionType);
        outputDirectory.mkdirs();
        String outputFilePath = outputDirectory.getPath() + File.separator + filename;

        File file = new File(outputFilePath);
        FileUtils.writeByteArrayToFile(file, fileContent);

        return outputFilePath;
    }

    public static String saveMessagepointObjectExportExcelToFile(XSSFWorkbook workbook, User user, ExportUtil.ExportType type, String uniqueId, String exportName) throws Exception {
        // TODO:  Create new export dir key
        File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
        String filename = getMessagepointObjectExportFileName(user, type, uniqueId, exportName);
        outputDirectory.mkdirs();
        String outputFilePath = outputDirectory.getPath() + File.separator + filename;

        File file = new File(outputFilePath);
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();

        return outputFilePath;
    }

    public static String saveMessagepointObjectExportExcelToFile(XSSFWorkbook workbook, User user, ExportUtil.ExportType type, String uniqueId,
                                                                 String exportName, ExportImportOptions exportImportOptions) throws Exception {
        // TODO:  Create new export dir key
        File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
        String filename = getMessagepointObjectExportFileName(user, type, uniqueId, exportName, exportImportOptions);
        outputDirectory.mkdirs();
        String outputFilePath = outputDirectory.getPath() + File.separator + filename;

        File file = new File(outputFilePath);
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();

        return outputFilePath;
    }

    /*
     * Audit Report
     */
    public static String saveXMLToFile(org.dom4j.Document document, User user, int auditReportTypeId, long reportTimestamp) throws Exception {
        File outputDirectory;
        if (auditReportTypeId == AuditReportType.ID_MESSAGE_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_LOCAL_SMART_TEXT_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_LOCAL_IMAGE_AUDIT_REPORT) {
            outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessageExportDir) + File.separator);
        } else if (auditReportTypeId == AuditReportType.ID_INSERT_AUDIT_REPORT) {
            outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_InsertScheduleExportDir) + File.separator);
        } else {    // Touchpoint Audit Report
            outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_AuditReportDir) + File.separator + Tenant.getPrimary().getId() + File.separator);
        }

        String outputFileName = generateFileName(user, EXTENSION_XML, auditReportTypeId, reportTimestamp);

        outputDirectory.mkdirs();
        String outputFilePath = outputDirectory.getPath() + File.separator + outputFileName;
        File file = new File(outputFilePath);
        OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(file), ApplicationLanguageUtils.XML_ENCODING);
        OutputFormat prettyFormat = OutputFormat.createPrettyPrint();
        prettyFormat.setEncoding(ApplicationLanguageUtils.XML_ENCODING);
        XMLWriter writer = new XMLWriter(fileWriter, prettyFormat);
        writer.write(document);
        writer.close();
        return outputFilePath;
    }

    /*
     * Audit Report: xsl file dealing
     */
    public static String transformXML(String xmlFilePath, User user, int auditReportTypeId, long reportTimestamp) throws Exception {
        // Check a specific folder to see if there is a custom xsl file in there. If there is one, use that, if not, use the default xsl file.

        // Load default xsl file.

        String defaultXSLPath = "";

        if (auditReportTypeId == AuditReportType.ID_MESSAGE_AUDIT_REPORT || auditReportTypeId == AuditReportType.ID_LOCAL_SMART_TEXT_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_LOCAL_IMAGE_AUDIT_REPORT || auditReportTypeId == AuditReportType.ID_LOCAL_SMART_CANVAS_AUDIT_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/messageExportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_INSERT_AUDIT_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/insertScheduleExportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_PROJECT_AUDIT_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/projectExportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_IMAGE_LIBRARY_AUDIT_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/imageLibraryExportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_SMART_TEXT_AUDIT_REPORT || auditReportTypeId == AuditReportType.ID_SMART_CANVAS_AUDIT_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/smartTextExportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_TOUCHPOINT_COMPARE_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/compareReportToXHTML.xsl";
        } else if (auditReportTypeId == AuditReportType.ID_CONNECTED_REPORT) {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/connectedExportToXHTML.xsl";
        } else {
            defaultXSLPath = ApplicationUtil.getRootPath() + "/WEB-INF/xslt/touchpointAuditReportsToXHTML.xsl";
        }

        Source xsltSource = new StreamSource(new InputStreamReader(new FileInputStream(new File(defaultXSLPath)), ApplicationLanguageUtils.XML_ENCODING));

        File f = new File(xmlFilePath);
        InputStreamReader xmlStream = new InputStreamReader(new FileInputStream(f), ApplicationLanguageUtils.XML_ENCODING);
        Source xmlSource = new StreamSource(xmlStream);

        TransformerFactory transFact = TransformerFactory.newInstance("net.sf.saxon.TransformerFactoryImpl", null);
        transFact.setURIResolver(new ReportsURIResolver(user.getAppLocaleLangCode()));
        Transformer trans = transFact.newTransformer(xsltSource);

        // this sets character output as HTML entities
        trans.setOutputProperty(OutputKeys.ENCODING, "US_ASCII");

        String outputFileName = generateFileName(user, EXTENSION_HTML, auditReportTypeId, reportTimestamp);

        String outputFileDirNameRelative = "";
        String outputFileDirNameFull = "";
        File outputDirectory = null;
        if (auditReportTypeId == AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_PROJECT_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_IMAGE_LIBRARY_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_CONNECTED_REPORT
                || auditReportTypeId == AuditReportType.ID_SMART_TEXT_AUDIT_REPORT
                || auditReportTypeId == AuditReportType.ID_MESSAGE_AUDIT_REPORT) {
            outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_AuditReportDir) + File.separator + Tenant.getPrimary().getId());
            outputFileDirNameRelative = outputDirectory.getPath();
            outputFileDirNameFull = outputDirectory.getAbsolutePath();
        } else {
            // messages, compareReports
            outputFileDirNameRelative = File.separator + ApplicationUtil.getProperty("message.folder.application") + File.separator + AUDIT_REPORTS_FOLDER;
            outputFileDirNameFull = ApplicationUtil.getRootPath() + outputFileDirNameRelative;
        }
        outputFileDirNameRelative = outputFileDirNameRelative.replace("\\", "/");
        outputFileDirNameFull = outputFileDirNameFull.replace("\\", "/");
        File outputFileDir = new File(outputFileDirNameFull);
        outputFileDir.mkdirs();

        String outputFilePathRelative = outputFileDirNameRelative + "/" + outputFileName;
        String outputFilePathFull = outputFileDirNameFull + "/" + outputFileName;

        File file = new File(outputFilePathFull);
        OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(file), ApplicationLanguageUtils.XML_ENCODING);

        trans.transform(xmlSource, new StreamResult(fileWriter));
        fileWriter.close();

        return outputFilePathRelative;
    }


    public static String getMessagepointObjectExportFileName(User user, ExportType type, String uniqueId,
                                                             String exportName, ExportImportOptions exportImportOptions) {

        String safeName = injectI18NcharsPattern.matcher(exportName).replaceAll("");
        StringBuilder outputFileName = new StringBuilder();

        if (type == ExportType.DOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_TOUCHPOINT + "_");
        } else if (type == ExportType.DYNAMICIMAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_IMAGE + "_");
        } else if (type == ExportType.DYNAMICSMARTTEXT) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTTEXT + "_");
        } else if (type == ExportType.DYNAMICSMARTCANVAS) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTCANVAS + "_");
        } else if (type == ExportType.DYNAMICMESSAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_MESSAGE + "_");
        } else if (type == ExportType.TPVARIANTS) {
            outputFileName.append(EXPORT_OBJECT_TP_VARIANTS + "_");
        } else if (type == ExportType.TPMESSAGES) {
            outputFileName.append(EXPORT_OBJECT_TP_MESSAGES + "_");
        } else if (type == ExportType.VARIANTMETADATA) {
            outputFileName.append(EXPORT_OBJECT_VARIANT_METADATA + "_");
        } else if (type == ExportType.RATIONALIZERDOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DOCUMENT + "_");
        } else if (type == ExportType.RATIONALIZERQEURY) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_QUERY + "_");
        } else if (type == ExportType.RATIONALIZERMETADATA) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_METADATA + "_");
        } else if (type == ExportType.USER) {
            outputFileName.append(EXPORT_OBJECT_USER_UPDATE + "_");
        } else if (type == ExportType.JOBPERFORMANCEREPORT) {
            outputFileName.append(EXPORT_OBJECT_JOB_PERFORMANCE_REPORT + "_");
        } else if (type == ExportType.RATIONALIZER_DASHBOARD_DUP) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_DUPLICATES + "_");
        } else if (type == ExportType.RATIONALIZER_DASHBOARD_SIM) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_SIMILARITIES + "_");
        }
        String ext = EXTENSION_XML;
        if (exportImportOptions.getExportExtType() == EXTENSION_HTML_TYPE) {
            ext = EXTENSION_EXCEL;
        } else if (exportImportOptions.getExportExtType() == EXTENSION_EXCEL_TYPE) {
            ext = EXTENSION_HTML;
        }
        outputFileName.append(user.getId()).append("_").append(safeName).append("_").append(uniqueId).append(DOT).append(ext);

        return outputFileName.toString();
    }

    public static String getMessagepointObjectExportFileName(User user, ExportType type, String uniqueId, String exportName, int extType) {
        String safeName = injectI18NcharsPattern.matcher(exportName).replaceAll("");

        StringBuilder outputFileName = new StringBuilder();

        if (type == ExportType.DOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_TOUCHPOINT + "_");
        } else if (type == ExportType.DYNAMICIMAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_IMAGE + "_");
        } else if (type == ExportType.DYNAMICSMARTTEXT) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTTEXT + "_");
        } else if (type == ExportType.DYNAMICSMARTCANVAS) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTCANVAS + "_");
        } else if (type == ExportType.DYNAMICMESSAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_MESSAGE + "_");
        } else if (type == ExportType.TPVARIANTS) {
            outputFileName.append(EXPORT_OBJECT_TP_VARIANTS + "_");
        } else if (type == ExportType.TPMESSAGES) {
            outputFileName.append(EXPORT_OBJECT_TP_MESSAGES + "_");
        } else if (type == ExportType.VARIANTMETADATA) {
            outputFileName.append(EXPORT_OBJECT_VARIANT_METADATA + "_");
        } else if (type == ExportType.RATIONALIZERDOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DOCUMENT + "_");
        } else if (type == ExportType.RATIONALIZERQEURY) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_QUERY + "_");
        } else if (type == ExportType.RATIONALIZERMETADATA) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_METADATA + "_");
        } else if (type == ExportType.USER) {
            outputFileName.append(EXPORT_OBJECT_USER_UPDATE + "_");
        } else if (type == ExportType.USER) {
            outputFileName.append(EXPORT_OBJECT_ROLE + "_");
        } else if (type == ExportType.JOBPERFORMANCEREPORT) {
            outputFileName.append(EXPORT_OBJECT_JOB_PERFORMANCE_REPORT + "_");
        }
        String ext = EXTENSION_XML;
        if (extType == EXTENSION_HTML_TYPE) {
            ext = EXTENSION_HTML;
        } else if (extType == EXTENSION_EXCEL_TYPE) {
            ext = EXTENSION_EXCEL;
        }
        outputFileName.append(user.getId()).append("_").append(safeName).append("_").append(uniqueId).append(DOT).append(ext);

        return outputFileName.toString();
    }

    public static String getMessagepointObjectExportFileName(User user, ExportType type, String uniqueId, String exportName) {
        String safeName = injectI18NcharsPattern.matcher(exportName).replaceAll("");

        StringBuilder outputFileName = new StringBuilder();
        String ext = EXTENSION_EXCEL;
        if (type == ExportType.DOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_TOUCHPOINT + "_");
            ext = EXTENSION_XML;

        } else if (type == ExportType.DYNAMICIMAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_IMAGE + "_");
        } else if (type == ExportType.DYNAMICSMARTTEXT) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTTEXT + "_");
        } else if (type == ExportType.DYNAMICSMARTCANVAS) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_SMARTCANVAS + "_");
        } else if (type == ExportType.DYNAMICMESSAGE) {
            outputFileName.append(EXPORT_OBJECT_DYNAMIC_MESSAGE + "_");
        } else if (type == ExportType.TPVARIANTS) {
            outputFileName.append(EXPORT_OBJECT_TP_VARIANTS + "_");
        } else if (type == ExportType.TPMESSAGES) {
            outputFileName.append(EXPORT_OBJECT_TP_MESSAGES + "_");
        } else if (type == ExportType.VARIANTMETADATA) {
            outputFileName.append(EXPORT_OBJECT_VARIANT_METADATA + "_");
        } else if (type == ExportType.RATIONALIZERDOCUMENT) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DOCUMENT + "_");
        } else if (type == ExportType.RATIONALIZERQEURY) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_QUERY + "_");
        } else if (type == ExportType.RATIONALIZERMETADATA) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_METADATA + "_");
        } else if (type == ExportType.USER) {
            outputFileName.append(EXPORT_OBJECT_USER_UPDATE + "_");
        } else if (type == ExportType.ROLE) {
            outputFileName.append(EXPORT_OBJECT_ROLE + "_");
        } else if (type == ExportType.MESSAGESWF) {
            outputFileName.append(EXPORT_OBJECT_MESSAGES_WF + "_");
        } else if (type == ExportType.JOBPERFORMANCEREPORT) {
            outputFileName.append(EXPORT_OBJECT_JOB_PERFORMANCE_REPORT + "_");
        } else if (type == ExportType.RATIONALIZER_DASHBOARD_DUP) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_DUPLICATES + "_");
        } else if (type == ExportType.RATIONALIZER_DASHBOARD_SIM) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_SIMILARITIES + "_");
        } else if (type == ExportType.VARIANTCONTENT) {
            outputFileName.append(EXPORT_OBJECT_VARIANT_CONTENT + "_");
        }

        outputFileName.append(user.getId()).append("_").append(safeName).append("_").append(uniqueId).append(DOT).append(ext);

        return outputFileName.toString();
    }

    public static String getMessagepointObjectExportFileName(ExportType type, long applicationId, String exportObjectName, String uniqueId) {
        String safeName = injectI18NcharsPattern.matcher(exportObjectName).replaceAll("");
        StringBuilder outputFileName = new StringBuilder();
        String ext = EXTENSION_EXCEL;
        if (type == ExportType.RATIONALIZER_DASHBOARD_DUP) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_DUPLICATES + "_");
        } else if (type == ExportType.RATIONALIZER_DASHBOARD_SIM) {
            outputFileName.append(EXPORT_OBJECT_RATIONALIZER_DASHBOARD_SIMILARITIES + "_");
        }
        outputFileName.append(applicationId).append("_").append(safeName).append("_").append(uniqueId).append(DOT).append(ext);

        return outputFileName.toString();
    }

    public static String generateFileName(User user, String extension, int auditReportTypeId, long reportTimestamp) {
        String outputFileName = "";
        if (user != null) {
            if (user.getFirstName() != null && !user.getFirstName().trim().isEmpty()) {
                outputFileName = StringUtil.replaceAll(user.getFirstName(), "'", "");
            }
            if (user.getLastName() != null && !user.getLastName().trim().isEmpty()) {
                String lastName = StringUtil.replaceAll(user.getLastName(), "'", "");
                if (outputFileName.trim().isEmpty()) {
                    outputFileName = lastName;
                } else {
                    outputFileName = outputFileName + "_" + lastName;
                }
            }
        }

        outputFileName = Tenant.getPrimary().getName() + "_" + outputFileName;

        if (auditReportTypeId == AuditReportType.ID_MESSAGE_AUDIT_REPORT) {
            outputFileName = outputFileName + "_Message";
        } else if (auditReportTypeId == AuditReportType.ID_LOCAL_SMART_TEXT_AUDIT_REPORT) {
            outputFileName = outputFileName + "_LocalSmartText";
        } else if (auditReportTypeId == AuditReportType.ID_LOCAL_IMAGE_AUDIT_REPORT) {
            outputFileName = outputFileName + "_LocalImage";
        } else if (auditReportTypeId == AuditReportType.ID_INSERT_AUDIT_REPORT) {
            outputFileName = outputFileName + "_InsertSchedule";
        } else if (auditReportTypeId == AuditReportType.ID_PROJECT_AUDIT_REPORT) {
            outputFileName = outputFileName + "_Project_" + reportTimestamp;
        } else if (auditReportTypeId == AuditReportType.ID_IMAGE_LIBRARY_AUDIT_REPORT) {
            outputFileName = outputFileName + "_ImageLibrary";
        } else if (auditReportTypeId == AuditReportType.ID_SMART_TEXT_AUDIT_REPORT) {
            outputFileName = outputFileName + "_SmartText";
        } else if (auditReportTypeId == AuditReportType.ID_TOUCHPOINT_COMPARE_REPORT) {
            outputFileName = outputFileName + "_TPCompareReport";
        } else if (auditReportTypeId == AuditReportType.ID_CONNECTED_REPORT) {
        	outputFileName = outputFileName + "_ConnectedReport";
        } else {    // Touchpoint Audit Report
            outputFileName = "TouchpointAuditReport_" + reportTimestamp;
        }

        outputFileName = outputFileName + DOT + extension;
        return outputFileName;
    }

    public static void createDataValueTag(ParameterGroupInstanceCollection pgiCollection, Element dataValuesElement) {
        Element dataValueElement = dataValuesElement.addElement("DataValue");
        dataValueElement.addAttribute("id", Long.toString(pgiCollection.getId()));

        List<String> pgiPathValues = new ArrayList<>();
        createValueTag(pgiCollection.getId(), dataValueElement, pgiPathValues, 1);
    }

    private static void createValueTag(long pgiCollectionId, Element dataValueElement, List<String> pgiPathValues, int level) {
        List<String> ithItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
        boolean createValueTag = false;
        List<String> valueTagValues = new ArrayList<>();
        if (ithItemValues != null && !ithItemValues.isEmpty()) {
            for (String IthItemValue : ithItemValues) {
                List<String> pathValues = new ArrayList<>(pgiPathValues);
                pathValues.add(IthItemValue);
                List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
                if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
                    createValueTag(pgiCollectionId, dataValueElement, pathValues, level + 1);
                } else {
                    createValueTag = true;
                    valueTagValues.add(IthItemValue);
                }
            }
            if (createValueTag) {
                Element valueElement = dataValueElement.addElement("Value");
                StringBuilder valueString = new StringBuilder();
                if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
                    for (String pathValue : pgiPathValues) {
                        if (valueString.length() > 0) {
                            valueString.append(";");
                        }
                        valueString.append(pathValue);
                    }
                }
                if (valueTagValues != null && !valueTagValues.isEmpty()) {
                    if (valueString.length() > 0) {
                        valueString.append(";");
                    }
                    for (String valueTagValue : valueTagValues) {
                        valueString.append(valueTagValue).append(",");
                    }
                    if (valueString.toString().endsWith(",")) {
                        valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
                    }
                }
                valueElement.addText(valueString.toString());
            }
        }
    }

    /**
     * Create the "Touchpoint" element
     *
     */
    public static void createTPSectionsTag(Document document, Element touchpointElement, User requestor, boolean isVisibilityApplied) {
        // Build the "Sections" element
        List<DocumentSection> documentSections = document.getDocumentSectionsByOrder();
        if (documentSections != null && !documentSections.isEmpty()) {
            Element sectionsElement = touchpointElement.addElement("Sections");
            for (DocumentSection documentSection : documentSections) {
                // - Build the "Section" element
                Element sectionElement = sectionsElement.addElement("Section");
                sectionElement.addAttribute("id", Long.toString(documentSection.getId()));
                // - Build the "Name" element
                Element sectionNameElement = sectionElement.addElement("Name");
                sectionNameElement.addText(documentSection.getName());
                // - Build the "Zones" element
                List<Zone> sectionZones = documentSection.getZonesList();
                if (sectionZones == null || sectionZones.isEmpty()) {
                    continue;
                }
                Element zonesElement = sectionElement.addElement("Zones");
                for (Zone zone : documentSection.getZonesList()) {
                    if (!zone.isVisible(requestor) && isVisibilityApplied) {
                        continue;
                    }
                    // -- Build the "Zone" element
                    Element zoneElement = zonesElement.addElement("Zone");
                    zoneElement.addAttribute("id", Long.toString(zone.getId()));
                    ContentType zoneContentType = zone.getContentType();
                    zoneElement.addAttribute("type", zoneContentType.getLocaledString());

                    if (zoneContentType.getId() == ContentType.GRAPHIC ||
                        zoneContentType.getId() == ContentType.TEXT_OR_GRAPHIC) {
                            zoneElement.addAttribute("graphictype", zone.getGraphicType());
                    }
                    // --- Build the "Name" element
                    Element zoneNameElement = zoneElement.addElement("Name");
                    zoneNameElement.addText(zone.getFriendlyName());
                    if (!zone.isMultipart() || zone.getParts() == null || zone.getParts().isEmpty()) {
                        continue;
                    }
                    // --- Build the "Parts" element
                    Element partsElement = zoneElement.addElement("Parts");
                    partsElement.addAttribute("count", Integer.toString(zone.getParts().size()));
                    Integer partNum = 1;
                    for (ZonePart zonePart : zone.getPartsInSequenceOrder()) {
                        // ---- Build the "Part" element
                        Element partElement = partsElement.addElement("Part");
                        partElement.addAttribute("id", Long.toString(zonePart.getId()));
                        partElement.addAttribute("number", (partNum++).toString());
                        ContentType zonePartContentType = zonePart.getContentType();
                        partElement.addAttribute("type", zonePartContentType.getLocaledString());

                        if (zonePartContentType.getId() == ContentType.GRAPHIC ||
                                zonePartContentType.getId() == ContentType.TEXT_OR_GRAPHIC) {
                            partElement.addAttribute("graphictype", zonePart.getGraphicType());
                        }
                        partElement.addText(zonePart.getName());
                    }
                }
            }
        }
    }

    public static String zipDirectory(String dirPath, String xmlFileName, File zipFile) throws IOException {
        byte[] buffer = new byte[4096];
        File dir= new File(dirPath);
        FileOutputStream fout = new FileOutputStream(zipFile);
        ZipOutputStream zout = new ZipOutputStream(fout);
        if(StringUtils.isNoneEmpty(xmlFileName)) {
            FileInputStream fin = new FileInputStream(xmlFileName);
            File xml = new File(xmlFileName);
            zout.putNextEntry(new ZipEntry(xml.getName()));
            int length;
            while ((length = fin.read(buffer)) > 0) {
                zout.write(buffer, 0, length);
            }
            zout.closeEntry();
        }
        zipSubDirectory("", dir, zout);
        zout.close();
        return zipFile.getPath();
    }

    private static void zipSubDirectory(String basePath, File dir, ZipOutputStream zout) throws IOException {
        byte[] buffer = new byte[4096];
        File[] files = dir.listFiles();
        for (File file : files != null ? files : new File[0]) {
            if (file.isDirectory()) {
                String path = basePath + file.getName() + PATH_SEPARATOR;
                zout.putNextEntry(new ZipEntry(path));
                zipSubDirectory(path, file, zout);
                zout.closeEntry();
            } else {
                FileInputStream fin = new FileInputStream(file);
                zout.putNextEntry(new ZipEntry(basePath + file.getName()));
                int length;
                while ((length = fin.read(buffer)) > 0) {
                    zout.write(buffer, 0, length);
                }
                zout.closeEntry();
                fin.close();
            }
        }
    }
    public static String encodeFile(String fileLocation) {
        PathUtil path = new PathUtil(fileLocation, false);
        File sourceFile = path.file(false);

        try {
            byte[] buf = FileUtils.readFileToByteArray(sourceFile);
            String encodedContent = Base64.encodeBase64String(buf);
            encodedContent = encodedContent.replace("\n", "");
            return encodedContent;
        } catch (IOException e) {
            log.error(" unexpected exception when invoking ExportUtil.encodeFile(String fileLocation)", e);
        }

        return "";
    }
}