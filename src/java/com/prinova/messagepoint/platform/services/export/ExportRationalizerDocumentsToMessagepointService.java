package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.rationalizer.dto.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.RandomGUID;
import com.prinova.messagepoint.util.StringUtil;
import com.sun.xml.txw2.output.IndentingXMLStreamWriter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.xml.stream.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.prinova.messagepoint.platform.services.export.RationalizerTextHashUtil.*;
import static com.prinova.messagepoint.platform.services.rationalizer.dto.MessageStateEnum.*;
import static org.apache.xmlbeans.impl.common.XmlReaderToWriter.write;

public class ExportRationalizerDocumentsToMessagepointService extends AbstractService {

    public static final String TAG_REFERENCE_DATA = "ReferenceData";
    public static final String TAG_STYLE = "Style";
    public static final String TAG_TEXT_STYLE_FONT = "TextStyleFont";
    public static final String TAG_FONT = "Font";
    public static final  String TAG_TOGGLE_COLOR_VALUES = "ToggleColorValues";
    public static final  String TAG_TOGGLE_COLOR_VALUE = "ToggleColorValue";
    public static final  String TAG_TOGGLE_POINT_SIZE_VALUES = "TogglePointSizeValues";
    public static final  String TAG_TOGGLE_POINT_SIZE_VALUE = "TogglePointSizeValue";
    public static int MP_COMPOSER_CONNECTOR_TYPE = 19;
    public static int SEFAS_CONNECTOR_TYPE = 18;

    public static final String SERVICE_NAME = "export.ExportRationalizerDocumentsToMessagepointService";
    private static final String CDATA_EVENT = "http://java.sun.com/xml/stream/properties/report-cdata-event";
    private static final Log log = LogUtil.getLog(ExportRationalizerDocumentsToMessagepointService.class);
    private static final String RAPORT_PREFIX_NAME = "RTP";
    private static final int BUFFER_SIZE = 10 * 1024 * 1024;
    private static final String UTF_8 = "UTF-8";
    private static final String ZONE_TAG = "Zone";
    private static final String TEXT_STYLES_TAG = "TextStyles";
    private static final String ALTERNATE_LAYOUTS_TAG = "AlternateLayouts";
    private static final String Set = "Set";

    private XMLStreamWriter streamWriter;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;
    private RationalizerApplication rationalizerApplication;
    private TouchpointExportUserPreferencesDto userPreferencesDto;
    private Map<String, ContentStyleSetDto> rationalizerContentStyleSets = new HashMap<>();
    private Map<IdDnaPair, EmbeddedContentConfigDto> messagepointCollision = new HashMap<>();

    @Override
    public void execute(ServiceExecutionContext context) {
        OutputStream os = null;
        BufferedOutputStream bos = null;
        InputStream is = null;
        BufferedInputStream bis = null;
        boolean isUpdateMode = true;
        try {
            ExportRationalizerToMessagepointXMLServiceRequest contextRequest = (ExportRationalizerToMessagepointXMLServiceRequest) context.getRequest();
            this.statusPollingBackgroundTask = contextRequest.getStatusPollingBackgroundTask();
            this.rationalizerApplication = RationalizerApplication.findById(contextRequest.getRationalizerApplicationId());
            this.userPreferencesDto = contextRequest.getUserPreferencesDto();

            isUpdateMode = userPreferencesDto.isUpdateTouchpointOnExport();
            String inputFilePath = contextRequest.getUserPreferencesDto().getUploadedXmlFilePath();
            Path outputPath = createOutputPath();
            os = Files.newOutputStream(outputPath);
            bos = new BufferedOutputStream(os, BUFFER_SIZE);
            XMLOutputFactory outputFactory = XMLOutputFactory.newInstance();
            outputFactory.setProperty(XMLOutputFactory.IS_REPAIRING_NAMESPACES, true);
            this.streamWriter = new IndentingXMLStreamWriter(outputFactory.createXMLStreamWriter(bos, UTF_8));

            Path inputPath = Paths.get(inputFilePath);
            is = Files.newInputStream(inputPath);
            bis = new BufferedInputStream(is, BUFFER_SIZE);
            XMLInputFactory inputFactory = XMLInputFactory.newInstance();
            inputFactory.setProperty(CDATA_EVENT, true);
            XMLStreamReader streamReader = inputFactory.createXMLStreamReader(bis, UTF_8);

            List<String> selectorsIdsList = readAllSelectorsIdsFromInputFile(streamReader);
            String selectionRefId = readSelectionRefId(streamReader);

            if (CollectionUtils.isEmpty(selectorsIdsList) || StringUtils.isEmpty(selectionRefId) || !selectorsIdsList.contains(selectionRefId)) {
                log.error("The XML touchpoint export contains invalid selectorsRefId");
                statusPollingBackgroundTask.setError(true);
                statusPollingBackgroundTask.setActive(false);
                return;
            }

            is = Files.newInputStream(inputPath);
            bis = new BufferedInputStream(is, BUFFER_SIZE);
            streamReader = inputFactory.createXMLStreamReader(bis, UTF_8);

            if (userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerReferenceDataSourceExists()) {
                copyAllUntilTagExclusive(streamReader, streamWriter, "DataSources");
                skipEntireNodeInReader(streamReader, "DataSources");
                writeDataSources();
            }

            if(userPreferencesDto.getRationalizerContentStylesMap() != null && !userPreferencesDto.getRationalizerContentStylesMap().isEmpty()) {
                // write text styles with fonts
                copyAllUntilTagExclusive(streamReader, streamWriter, TAG_REFERENCE_DATA);
                writeTextStylesAndFonts(streamReader);
                skipEntireNodeInReader(streamReader, TAG_REFERENCE_DATA);
            }

            if (userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerReferenceDataSourceExists()) {
                copyAllUntilTagExclusive(streamReader, streamWriter, "UserVariables");
                skipEntireNodeInReader(streamReader, "UserVariables");
                writeUserVariables();
            }


            copyAllUntilTagExclusive(streamReader, streamWriter, ZONE_TAG);
            writeZoneWithStyle(streamReader, streamWriter);
            copyAllUntilTagExclusive(streamReader, streamWriter, "Selections");
            skipEntireNodeInReader(streamReader, "Selections");
            writeSelectionsNode(selectionRefId, isUpdateMode);
            copyAllUntilMessagesOrEmbeddedContentsTags(streamReader, streamWriter);
            writeEmbeddedContentsTag(getDefaultLanguage(this.userPreferencesDto));
            if(isUpdateMode) {
                copyEntireElement(streamReader, streamWriter, "ContentLibrary");
            }
            writeMessages(isUpdateMode, this.userPreferencesDto.getLanguageList());
            streamWriter.writeEndDocument();
            statusPollingBackgroundTask.setProgressInPercentInThread(90);
            streamWriter.flush();
            ((ExportToXMLServiceResponse) context.getResponse()).setFilePath(outputPath.toString());
            streamWriter.close();
            streamReader.close();
        } catch (Exception ex) {
            log.error(" unexpected exception when invoking ExportRationalizerDocumentsToMessagepointService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (bos != null) {
                    bos.close();
                }
                if (is != null) {
                    is.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                log.error("Unexpected while closing the opened streams of ExportRationalizerDocumentsToMessagepointService ", e);
            }
        }
    }

    private void writeTextStylesAndFonts(XMLStreamReader streamReader) throws XMLStreamException {
        if (!userPreferencesDto.getRationalizerContentStylesMap().isEmpty()) {
            // <ReferenceData>
            streamWriter.writeStartElement(TAG_REFERENCE_DATA);
            // Write Rationalizer Fonts
            streamWriter.writeStartElement("Fonts");
            copyMessagepointFontTags(streamReader);
            processContentStyles();
            writeRationalizerContentStylesFonts();
            streamWriter.writeEndElement();
            // Write Rationalizer styles inside Text Styles
            streamWriter.writeStartElement("TextStyles");
            copyMessagepointTextStyles(streamReader);
            writeContentStyles();
            streamWriter.writeEndElement();
            // Copy remaining styles
            copyEntireElement(streamReader, streamWriter, "ParagraphStyles");
            copyEntireElement(streamReader, streamWriter, "ListStyles");
            copyEntireElement(streamReader, streamWriter, "DataGroups");
            // </ReferenceData>
            streamWriter.writeEndElement();
        } else {
            copyEntireElement(streamReader, streamWriter, TAG_REFERENCE_DATA);
        }

    }

    private void writeContentStyles() throws XMLStreamException {
        for(ContentStyleSetDto contentStyleSet: rationalizerContentStyleSets.values()) {
            String fontName = contentStyleSet.getFontName();
            if (fontName != null) {
                Long id = contentStyleSet.getId();
                streamWriter.writeStartElement("Style");
                streamWriter.writeAttribute("id", String.valueOf(id));
                String ftName = contentStyleSet.getFontName().replaceAll("[\\p{Punct}&&[^_]]", "");
                streamWriter.writeAttribute("name", "R_" + ftName + Set);
                streamWriter.writeAttribute("webname", ftName);
                streamWriter.writeAttribute("color", "cmyk(0;0;0;100)");
                streamWriter.writeAttribute("identifier", contentStyleSet.getIdentifier());
                streamWriter.writeAttribute("istogglebold", "true");
                streamWriter.writeAttribute("istoggleitalic", "true");
                streamWriter.writeAttribute("istoggleunderline", "true");
                streamWriter.writeAttribute("istogglecolor", "true");
                streamWriter.writeAttribute("istogglepointsize", "true");
                streamWriter.writeAttribute("isapplytextstylefont", "false");

                // write color set
                Set<String> colorSet = contentStyleSet.getColorsSet();
                if (!colorSet.isEmpty()) {
                    streamWriter.writeStartElement("ToggleColorValues");

                    for (String color : colorSet) {
                        streamWriter.writeStartElement("ToggleColorValue");
                        streamWriter.writeCData("cmyk(" + StringUtils.join(ContentStyleConfigDto.getCmykColor(color), ";") + ")");
                        streamWriter.writeEndElement();
                    }
                    streamWriter.writeEndElement();
                }

                // write font sizes set
                Set<String> fontSizeSet = contentStyleSet.getFontSizesSet();
                if (!fontSizeSet.isEmpty()) {
                    streamWriter.writeStartElement("TogglePointSizeValues");

                    for (String fontSize : fontSizeSet) {
                        streamWriter.writeStartElement("TogglePointSizeValue");
                        streamWriter.writeCData(fontSize);
                        streamWriter.writeEndElement();
                    }
                    streamWriter.writeEndElement();
                }

                streamWriter.writeStartElement("TextStyleFont");
                streamWriter.writeStartElement("Name");
                streamWriter.writeCData(fontName);
                streamWriter.writeEndElement(); // Name
                streamWriter.writeEndElement(); // TextStyleFont

                streamWriter.writeStartElement("Font");
                streamWriter.writeAttribute("refid", String.valueOf(id));
                streamWriter.writeAttribute("propertyrefid", String.valueOf(id));
                streamWriter.writeEndElement(); // Font
                streamWriter.writeEndElement(); // Style
            }
        }
    }

    /**
     * Check if the rationalizer text style exists in touchpoint.
     * If the font size does not match but the font name and color matches then the font size will be added to the existing touchpoint style.
     * @param rationalizerFontName rationalizer font name
     * @param rationalizerFontColor rationalizer font color
     * @param  rationalizerFontSize rationalizer font size
     * @return touchpoint style identifier if the rationalizer font name and color match
     */
    private String getTouchpointStyleIdentifier(String rationalizerFontName, String rationalizerFontColor, String rationalizerFontSize) {
        for(TextStyleConfigDto touchpointStyle : userPreferencesDto.getTouchpointTextStyles()) {
            String fontName = touchpointStyle.getFontName();
            if(fontName != null && fontName.equals(rationalizerFontName)) {
                if(touchpointStyle.isToggleBold() && touchpointStyle.isToggleItalic() && touchpointStyle.isToggleUnderline()) {
                    Set<String> touchpointColors = touchpointStyle.getToggleColorValues();
                    if(rationalizerFontColor != null) {
                        if(CollectionUtils.isEmpty(touchpointColors)) {
                            Set<String> colors = new HashSet<>();
                            colors.add("cmyk(" + StringUtils.join(ContentStyleConfigDto.getCmykColor(rationalizerFontColor), ";") + ")");
                            touchpointStyle.setToggleColorValues(colors);
                        } else {
                            if(!touchpointColors.contains("cmyk(" + StringUtils.join(ContentStyleConfigDto.getCmykColor(rationalizerFontColor), ";") + ")")) {
                                touchpointStyle.getToggleColorValues().add("cmyk(" + StringUtils.join(ContentStyleConfigDto.getCmykColor(rationalizerFontColor), ";") + ")");
                            }
                        }
                    }
                    List<Double> togglePointSizeValue = touchpointStyle.getTogglePointSizeValue();
                    if(CollectionUtils.isEmpty(togglePointSizeValue)) {
                        List<Double> fontsSizes = new ArrayList<>();
                        fontsSizes.add(Double.parseDouble(rationalizerFontSize));
                        touchpointStyle.setTogglePointSizeValue(fontsSizes);
                    } else if(rationalizerFontSize != null && !togglePointSizeValue.contains(Double.parseDouble(rationalizerFontSize))) {
                        touchpointStyle.getTogglePointSizeValue().add(Double.parseDouble(rationalizerFontSize));
                    }
                    return touchpointStyle.getTouchpointIdentifier();
                }
            }
        }
        return null;
    }

    private void processContentStyles() {
        Map<Integer, ContentStyleConfigDto> contentStyleConfigDtoMap = userPreferencesDto.getRationalizerContentStylesMap();
        for (ContentStyleConfigDto contentStyleConfigDto : contentStyleConfigDtoMap.values()) {
            String fontName = contentStyleConfigDto.getFontName();
            if (fontName != null) {
                String colorStyleConfigDto = contentStyleConfigDto.getFontColor();
                String fontSizeStyleConfigDto = contentStyleConfigDto.getFontSize();
                String touchpointStyleIdentifier = getTouchpointStyleIdentifier(fontName, colorStyleConfigDto, fontSizeStyleConfigDto);
                if (touchpointStyleIdentifier == null) {
                    if (rationalizerContentStyleSets.containsKey(fontName)) {
                        ContentStyleSetDto style = rationalizerContentStyleSets.get(fontName);
                        if (colorStyleConfigDto != null && !style.getColorsSet().contains(colorStyleConfigDto)) {
                            style.getColorsSet().add(colorStyleConfigDto);
                        }
                        if (fontSizeStyleConfigDto != null && !style.getFontSizesSet().contains(fontSizeStyleConfigDto)) {
                            style.getFontSizesSet().add(fontSizeStyleConfigDto);
                        }
                        contentStyleConfigDto.setContentStyleSetIdentifier(style.getIdentifier());
                    } else {
                        ContentStyleSetDto contentStyleSetDto = createRationalizerStyle(contentStyleConfigDto);
                        rationalizerContentStyleSets.put(fontName, contentStyleSetDto);
                    }

                } else {
                    contentStyleConfigDto.setContentStyleSetIdentifier(touchpointStyleIdentifier);
                }
            }
        }
    }

    private ContentStyleSetDto createRationalizerStyle(ContentStyleConfigDto contentStyleConfigDto) {
        ContentStyleSetDto contentStyleSetDto = new ContentStyleSetDto();
        contentStyleSetDto.setId(contentStyleConfigDto.getId());
        contentStyleSetDto.setFontName(contentStyleConfigDto.getFontName());
        if (contentStyleConfigDto.getFontColor() != null) {
            contentStyleSetDto.getColorsSet().add(contentStyleConfigDto.getFontColor());
        }
        if (contentStyleConfigDto.getFontSize() != null) {
            contentStyleSetDto.getFontSizesSet().add((contentStyleConfigDto.getFontSize()));
        }
        contentStyleConfigDto.setContentStyleSetIdentifier(contentStyleSetDto.getIdentifier());
        return contentStyleSetDto;
    }

    private void copyMessagepointTextStyles(XMLStreamReader streamReader) throws XMLStreamException {
        for (TextStyleConfigDto contentStyle : userPreferencesDto.getTouchpointTextStyles()) {
            try {
                streamWriter.writeStartElement(TAG_STYLE);
                for (Map.Entry<String, String> crtEntry : contentStyle.getStyleAttributes().entrySet()) {
                    streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
                }

                if (contentStyle.getTextStyleFont() != null) {
                    streamWriter.writeStartElement(TAG_TEXT_STYLE_FONT);
                    streamWriter.writeStartElement("Name");
                    streamWriter.writeCData(contentStyle.getTextStyleFont());
                    streamWriter.writeEndElement();
                    streamWriter.writeEndElement();
                }
                if (contentStyle.getToggleColorValues() != null) {
                    streamWriter.writeStartElement(TAG_TOGGLE_COLOR_VALUES);
                    for (String colorValue : contentStyle.getToggleColorValues()) {
                        streamWriter.writeStartElement(TAG_TOGGLE_COLOR_VALUE);
                        streamWriter.writeCData(colorValue);
                        streamWriter.writeEndElement();
                    }
                    streamWriter.writeEndElement();
                }
                if (contentStyle.getTogglePointSizeValue() != null) {
                    streamWriter.writeStartElement(TAG_TOGGLE_POINT_SIZE_VALUES);
                    for (Double pointSize : contentStyle.getTogglePointSizeValue()) {
                        streamWriter.writeStartElement(TAG_TOGGLE_POINT_SIZE_VALUE);
                        streamWriter.writeCData(pointSize.toString());
                        streamWriter.writeEndElement();
                    }
                    streamWriter.writeEndElement();
                }
                streamWriter.writeStartElement(TAG_FONT);
                for (Map.Entry<String, String> crtEntry : contentStyle.getFontAttributes().entrySet()) {
                    streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
                }
                streamWriter.writeEndElement();

                streamWriter.writeEndElement();
            } catch (Exception ex){
                TextStyleConfigDto i = contentStyle;
            }

        }
    }

    private void writeZoneWithStyle(XMLStreamReader streamReader, XMLStreamWriter writer) throws XMLStreamException {
        String zoneId ="";
        List<Long> styleIds = new ArrayList<>();
        while (streamReader.hasNext()) {
            if (streamReader.isStartElement() && streamReader.getLocalName().equals(ZONE_TAG)) {
                zoneId = streamReader.getAttributeValue(0);
                styleIds = getStyleIdsForZoneId(zoneId);
            }
            write(streamReader, writer);
            streamReader.next();
            if (streamReader.isStartElement() && streamReader.getLocalName().equals(TEXT_STYLES_TAG)) {
                if(!styleIds.isEmpty()) {
                    linkTextStyleToZone(styleIds, streamReader, writer);
                }
            }
            if (streamReader.isStartElement() && streamReader.getLocalName().equals(ALTERNATE_LAYOUTS_TAG)) {
                break;
            }
        }
    }

    private List<Long> getStyleIdsForZoneId(String zoneId){
        List<Long> styleIds = new ArrayList<>();
        if(rationalizerContentStyleSets != null) {
            TouchpointExportSelectionNode masterNode = userPreferencesDto.getTouchpointMatchingStatsDto().getTreeComparisonResultNode();
            for (MessageConfigDto crtMessage : masterNode.getMessageConfigDtoList()) {
                String zoneRefId = crtMessage.getVersionConfigDto().getDeliveryDto().getZoneRefId();
                if (zoneRefId != null && zoneRefId.equals(zoneId)) {
                    for (ContentConfigDto content : crtMessage.getContentConfigDtoList()) {
                        Document document = Jsoup.parse(content.getContentHtml());
                        Map<Integer, ContentStyleConfigDto> contentStylesMap = content.extractContentStyles(document.select("span[style]"));
                    long styleId = -1;
                        for (ContentStyleConfigDto style : contentStylesMap.values()) {
                        for ( ContentStyleSetDto rationalizerStyle: rationalizerContentStyleSets.values()) {
                           styleId = getRationlizerStyleId(rationalizerStyle, style);
                           if(styleId >0 && !styleIds.contains(styleId)){
                               styleIds.add(styleId);
                           }
                        }
                        if(styleId < 0 ){
                            styleId = getTouchpointStyleId(style);
                            if(styleId >0 && !styleIds.contains(styleId)){
                                styleIds.add(styleId);
                            }
                            }
                        }
                    }
                }
            }
        }
        return styleIds;
    }

    private long getRationlizerStyleId(ContentStyleSetDto rationalizerStyle, ContentStyleConfigDto createdStyle){
        if(rationalizerStyle.getFontName()!= null && rationalizerStyle.getFontName().equals(createdStyle.getFontName())) {
                return rationalizerStyle.getId();
        }
        return -1;
    }

    private long getTouchpointStyleId( ContentStyleConfigDto createdStyle){
        for (TextStyleConfigDto touchpointStyle : userPreferencesDto.getTouchpointTextStyles()) {
            String rationalizerFontName = createdStyle.getFontName();
            String touchpointFontName = touchpointStyle.getFontName();
            if ( rationalizerFontName != null && touchpointFontName!= null && touchpointFontName.equals(rationalizerFontName)) {
                return Long.parseLong(touchpointStyle.getStyleAttributes().get("id"));
            }
        }
        return -1;
    }

    private void writeRationalizerContentStylesFonts() throws XMLStreamException {
        Map<Long, String> processedFonts = new HashMap<>();
        for(ContentStyleSetDto contentStyle: rationalizerContentStyleSets.values()) {
            Long id = contentStyle.getId();
            String fontName = contentStyle.getFontName();
            if(fontName != null) {
                if (!processedFonts.containsKey(id)) {
                    streamWriter.writeStartElement("Font");
                    streamWriter.writeAttribute("id", String.valueOf(id));
                    streamWriter.writeAttribute("name", fontName);
                    streamWriter.writeAttribute("htmldescription", fontName);
                    streamWriter.writeStartElement("Property");
                    streamWriter.writeAttribute("id", String.valueOf(id));
                    streamWriter.writeAttribute("pointsize", "0");
                    streamWriter.writeAttribute("bold", "false");
                    streamWriter.writeAttribute("italics", "false");
                    streamWriter.writeAttribute("underline", "false");
                    streamWriter.writeEndElement(); // Property
                    streamWriter.writeEndElement(); // Font
                }
                processedFonts.put(id, fontName);
            }
        }

    }

    private void linkTextStyleToZone(List<Long> styleIds, XMLStreamReader streamReader, XMLStreamWriter writer ) throws XMLStreamException {

        while (streamReader.hasNext()) {
            if (streamReader.isStartElement() && TEXT_STYLES_TAG.equals(streamReader.getLocalName())) {
                writeNewStyle(styleIds, writer);
                break;
            }
            streamReader.next();
        };
        skipTextStylesTag(streamReader);
    }

    private void writeNewStyle(List<Long> styleIds,  XMLStreamWriter writer) throws XMLStreamException{
        writer.writeStartElement(TEXT_STYLES_TAG);
        for (long id: styleIds) {
            writer.writeStartElement("Style");
            writer.writeAttribute("refid", String.valueOf(id));
            writer.writeEndElement();
        }
    }

    private void skipTextStylesTag( XMLStreamReader streamReader) throws XMLStreamException{
        if (streamReader.isStartElement() && TEXT_STYLES_TAG.equals(streamReader.getLocalName())) {
            streamReader.next();
        }
    }

    private void copyMessagepointFontTags(XMLStreamReader streamReader) throws XMLStreamException {
        // skip ReferenceData/Fonts
        streamReader.nextTag();
        if (streamReader.isStartElement() && "Fonts".equals(streamReader.getLocalName())) {
            streamReader.next();
            if(streamReader.isEndElement()){
                return;
            }
            while(streamReader.hasNext()) {
                write(streamReader, streamWriter);
                streamReader.next();
                if (streamReader.isEndElement() && "Fonts".equals(streamReader.getLocalName())) {
                    break;
                }
            }
        }
    }

    private TouchpointLanguageDto getDefaultLanguage(TouchpointExportUserPreferencesDto userPreferencesDto) {
        for(TouchpointLanguageDto language: userPreferencesDto.getLanguageList()) {
            if(language.isDefaultLanguage()) {
                return language;
            }
        }
        return null;
    }

    public static ServiceExecutionContext createContext(Long rationalizerAppId, User requester, StatusPollingBackgroundTask statusPollingBackgroundTask, TouchpointExportUserPreferencesDto userPreferencesDto) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportRationalizerToMessagepointXMLServiceRequest request = new ExportRationalizerToMessagepointXMLServiceRequest(rationalizerAppId, requester, statusPollingBackgroundTask, userPreferencesDto);
        context.setRequest(request);

        ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    private List<String> readAllSelectorsIdsFromInputFile(XMLStreamReader reader) throws XMLStreamException {
        List<String> result = new ArrayList<>();
        while (reader.hasNext()) {
            if (reader.isStartElement() && "Selector".equals(reader.getLocalName())) {
                String id = reader.getAttributeValue(0);
                result.add(id);
            }
            if (reader.isEndElement() && "Selectors".equals(reader.getLocalName())) {
                break;
            }
            reader.next();
        }
        return result;
    }

    private String readSelectionRefId(XMLStreamReader reader) throws XMLStreamException {
        while (reader.hasNext()) {

            if (reader.isStartElement() && "Selections".equals(reader.getLocalName())) {
                return reader.getAttributeValue(0);
            }
            reader.next();
        }
        return null;
    }


    private void writeDataSources() throws XMLStreamException {
        streamWriter.writeStartElement("DataSources");
        if (CollectionUtils.isEmpty(userPreferencesDto.getTouchpointMatchingStatsDto().getDataSourcesList())) {
            streamWriter.writeEndElement();
            return;
        }

        for (DataSourceDto crtDataSource : userPreferencesDto.getTouchpointMatchingStatsDto().getDataSourcesList()) {
            writeDataSource(crtDataSource);
        }

        streamWriter.writeEndElement();
    }

    private void writeDataSource(DataSourceDto crtDataSource) throws XMLStreamException {
        streamWriter.writeStartElement("DataSource");
        for (Map.Entry<String, String> crtEntry : crtDataSource.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeStartElement("Encoding");
        streamWriter.writeCharacters(crtDataSource.getEncoding());
        streamWriter.writeEndElement();
        writeRecordingLayoutElement(crtDataSource.getRecordLayoutDto());
        streamWriter.writeEndElement();
    }

    private void writeRecordingLayoutElement(RecordLayoutDto recordLayoutDto) throws XMLStreamException {
        streamWriter.writeStartElement("RecordLayout");
        for (Map.Entry<String, String> crtEntry : recordLayoutDto.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeStartElement("IndicatorLayout");
        streamWriter.writeStartElement("IndicatorSegment");
        if (MapUtils.isNotEmpty(recordLayoutDto.getIndicatorSegmentAttributes())) {
            for (Map.Entry<String, String> crtEntry : recordLayoutDto.getIndicatorSegmentAttributes().entrySet()) {
                streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
            }
        }
        streamWriter.writeEndElement();
        streamWriter.writeEndElement();

        if(recordLayoutDto.getRecordDtoList() != null) {
            writeRecordList(recordLayoutDto.getRecordDtoList());
        }

        if(recordLayoutDto.getDataTagListDto() != null) {
            writeGenericNode(recordLayoutDto.getDataTagListDto());
        }

        streamWriter.writeEndElement();
    }

    private void writeRecordList(List<RecordDto> recordDtoList) throws XMLStreamException {
        streamWriter.writeStartElement("RecordList");
        if (CollectionUtils.isEmpty(recordDtoList)) {
            streamWriter.writeEndElement();
            return;
        }

        for (RecordDto crtRecord : recordDtoList) {
            writeRecord(crtRecord);
        }

        streamWriter.writeEndElement();
    }

    private void writeRecord(RecordDto crtRecord) throws XMLStreamException {
        streamWriter.writeStartElement("Record");
        for (Map.Entry<String, String> crtEntry : crtRecord.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeStartElement("Indicator");
        streamWriter.writeCData(crtRecord.getIndicator());
        streamWriter.writeEndElement();
        if(!StringUtil.isEmptyOrNull(crtRecord.getPositionInRecordSet())) {
            streamWriter.writeStartElement("PositionInRecordSet");
            streamWriter.writeCharacters(crtRecord.getPositionInRecordSet());
            streamWriter.writeEndElement();
        }
        writeFieldsList(crtRecord.getFieldDtoList());
        streamWriter.writeEndElement();
    }

    private void writeFieldsList(List<FieldDto> filedDtoList) throws XMLStreamException {
        streamWriter.writeStartElement("FieldList");
        if (CollectionUtils.isEmpty(filedDtoList)) {
            streamWriter.writeEndElement();
            return;
        }

        for (FieldDto crtField : filedDtoList) {
            writeField(crtField);
        }

        streamWriter.writeEndElement();
    }

    private void writeField(FieldDto crtField) throws XMLStreamException {
        streamWriter.writeStartElement("Field ");
        for (Map.Entry<String, String> crtEntry : crtField.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeStartElement("FieldName");
        streamWriter.writeCharacters(crtField.getName());
        streamWriter.writeEndElement();
        streamWriter.writeStartElement("Format");
        streamWriter.writeCharacters(crtField.getFormat());
        streamWriter.writeEndElement();
        streamWriter.writeEndElement();
    }

    private void writeUserVariables() throws XMLStreamException {
        streamWriter.writeStartElement("UserVariables");

        if (CollectionUtils.isNotEmpty(userPreferencesDto.getTouchpointMatchingStatsDto().getMessagepointUnmatchedVariables())) {
            for (UserVariableDto crtVariable : userPreferencesDto.getTouchpointMatchingStatsDto().getMessagepointUnmatchedVariables()) {
                writeUserVariable(crtVariable);
            }
        }
        if (CollectionUtils.isNotEmpty(userPreferencesDto.getTouchpointMatchingStatsDto().getMatchedVariables())) {
            for (UserVariableDto crtVariable : userPreferencesDto.getTouchpointMatchingStatsDto().getMatchedVariables()) {
                writeUserVariable(crtVariable);
            }
        }
        if (CollectionUtils.isNotEmpty(userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerUnmatchedVariables())) {
            for (UserVariableDto crtVariable : userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerUnmatchedVariables()) {
                writeUserVariable(crtVariable);
            }
        }

        streamWriter.writeEndElement();
    }

    private void writeUserVariable(UserVariableDto crtVariable) throws XMLStreamException {
        streamWriter.writeStartElement("UserVariable");
        for (Map.Entry<String, String> crtEntry : crtVariable.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeStartElement("Name");
        streamWriter.writeCData(crtVariable.getName());
        streamWriter.writeEndElement();
        streamWriter.writeStartElement("FriendlyName");
        String friendlyName = crtVariable.getFriendlyName() == null ? "" : crtVariable.getFriendlyName();
        streamWriter.writeCData(friendlyName);
        streamWriter.writeEndElement();
        writeDataElement(crtVariable.getDataElementDto());
        streamWriter.writeEndElement();
    }

    private void writeDataElement(UserVariableDataElementDto dataElementDto) throws XMLStreamException {
        streamWriter.writeStartElement("DataElement");
        for (Map.Entry<String, String> crtEntry : dataElementDto.getAttributes().entrySet()) {
            streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
        }
        streamWriter.writeCData(dataElementDto.getValue());
        streamWriter.writeEndElement();
    }

    private void writeSelectionsNode(String selectorRefId, boolean isUpdateTouchpointModeEnabled) throws XMLStreamException {
        streamWriter.writeStartElement("Selections");
        streamWriter.writeAttribute("selectorrefid", selectorRefId);
        streamWriter.writeAttribute("selectiontype", "Message");
        writeSelectionTree(isUpdateTouchpointModeEnabled);
        streamWriter.writeEndElement();
    }

    private void writeSelectionTree(boolean isUpdateTouchpointModeEnabled) throws XMLStreamException {
        TouchpointExportSelectionNode masterNode = userPreferencesDto.getTouchpointMatchingStatsDto().getTreeComparisonResultNode();
        writeSelectionNode(masterNode);

        if (CollectionUtils.isNotEmpty(masterNode.getChildren())) {
            for (TouchpointExportSelectionNode crtChild : masterNode.getChildren()) {
                if(isUpdateTouchpointModeEnabled) {
                    writeSelectionForCrtObjectRecursively(crtChild);
                } else {
                    if(crtChild.getState() != VariantStateEnum.MESSAGEPOINT_UNMATCHED) {
                        writeSelectionForCrtObjectRecursively(crtChild);
                    }
                }
            }
        }
        streamWriter.writeEndElement();
    }

    private void writeSelectionForCrtObjectRecursively(TouchpointExportSelectionNode treeNode) throws XMLStreamException {
        writeSelectionNode(treeNode);
        if (CollectionUtils.isNotEmpty(treeNode.getChildren())) {
            for (TouchpointExportSelectionNode crtChild : treeNode.getChildren()) {
                writeSelectionForCrtObjectRecursively(crtChild);
            }
        }
        streamWriter.writeEndElement();
    }

    private void writeSelectionNode(TouchpointExportSelectionNode treeNode) throws XMLStreamException {
        String name = treeNode.getName();
        if (name == null) {
            name = "NO VALUE";
        } else if(name.isEmpty()){
            name = "NO VALUE";
        }
        streamWriter.writeStartElement("Selection");
        String dna = treeNode.getAttributes().get("dna");
        String pgtndna = treeNode.getAttributes().get("pgtndna");
        if (StringUtils.isEmpty(dna)) {
            dna = RandomGUID.getGUID().toLowerCase();
        }
        if (StringUtils.isEmpty(pgtndna)) {
            pgtndna = RandomGUID.getGUID().toLowerCase();
        }
        Long treeNodeId = treeNode.getId();
        if (CollectionUtils.isNotEmpty(treeNode.getMessageConfigDtoList())) {
            streamWriter.writeAttribute("id", "" + treeNodeId);
            streamWriter.writeAttribute("dna", dna);
            streamWriter.writeAttribute("pgtndna", pgtndna);

            String dataValueRefid = treeNode.getAttributes().get("datavaluerefid");
            if (!StringUtils.isEmpty(dataValueRefid)) {
                streamWriter.writeAttribute("datavaluerefid", dataValueRefid);
            }
        } else {
            Map<String, String> attributes = treeNode.getAttributes();
            if (treeNodeId == null) {
                treeNodeId = generateUniqueLongId();
            }
            streamWriter.writeAttribute("id", "" + treeNodeId);
            attributes.remove("id");
            streamWriter.writeAttribute("dna", dna);
            streamWriter.writeAttribute("pgtndna", pgtndna);
            attributes.remove("dna");
            attributes.remove("pgtndna");
            for (Map.Entry<String, String> attribute : attributes.entrySet()) {
                streamWriter.writeAttribute(attribute.getKey(), attribute.getValue());
            }
        }
        streamWriter.writeStartElement("Name");
        streamWriter.writeCData(name);
        streamWriter.writeEndElement();

        // write generic children
        if (CollectionUtils.isNotEmpty(treeNode.getOtherChildren())) {
            for (GenericXmlElementDto genericXmlElementDto : treeNode.getOtherChildren()) {
                writeGenericNode(genericXmlElementDto);
            }
        }

        streamWriter.writeStartElement("Messages");
        if (CollectionUtils.isNotEmpty(treeNode.getMessageConfigDtoList())) {
            for (MessageConfigDto crtMessage : treeNode.getMessageConfigDtoList()) {
                writeSelectionMessage(crtMessage);
            }
        }
        streamWriter.writeEndElement();
    }

    private void writeGenericNode(GenericXmlElementDto genericXmlElementDto) throws XMLStreamException {
        streamWriter.writeStartElement(genericXmlElementDto.getTagName());
        // write attributes
        if (genericXmlElementDto.getAttributes() != null && !genericXmlElementDto.getAttributes().isEmpty()) {
            Map<String, String> attributes = genericXmlElementDto.getAttributes();
            for (Map.Entry<String, String> attribute : attributes.entrySet()) {
                streamWriter.writeAttribute(attribute.getKey(), attribute.getValue());
            }
        }
        // write children
        if (!CollectionUtils.isEmpty(genericXmlElementDto.getChildren())) {
            for (GenericXmlElementDto child : genericXmlElementDto.getChildren()) {
                writeGenericNode(child);
            }
        }

        // write text content
        if (!StringUtil.isEmptyOrNull(genericXmlElementDto.getTextContent())) {
            streamWriter.writeCData(genericXmlElementDto.getTextContent());
        }

        streamWriter.writeEndElement();
    }

    private void writeSelectionMessage(MessageConfigDto messageConfigDto) throws XMLStreamException {
        if (!userPreferencesDto.shouldIncludeMessageInExport(messageConfigDto)) {
            return;
        }

        streamWriter.writeStartElement("Message");
        TouchpointMessageCollisionDto collision = userPreferencesDto.extractCorrespondingCollisionForMessage(messageConfigDto);
        if(collision != null) {
            long messagepointMessageId = collision.getMessageCollisionDto().getMessagepointMessage().getId();
            streamWriter.writeAttribute("refid", "" + messagepointMessageId);

        } else {
            streamWriter.writeAttribute("refid", "" + messageConfigDto.getId());
        }


        streamWriter.writeAttribute("islocal", "false");
        streamWriter.writeEndElement();
    }

    private void skipEntireNodeInReader(XMLStreamReader reader, String nodeName) throws XMLStreamException {
        while (reader.hasNext()) {
            reader.next();
            if (reader.isEndElement() && nodeName.equals(reader.getLocalName())) {
                break;
            }
        }
        reader.next();
    }

    private void writeMessages(boolean isUpdateTouchpointModeEnabled, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        streamWriter.writeStartElement("Messages");

        TouchpointExportSelectionNode root = userPreferencesDto.getTouchpointMatchingStatsDto().getTreeComparisonResultNode();
        writeMessagesRecursiveForNode(root, isUpdateTouchpointModeEnabled, languages);

        if(isUpdateTouchpointModeEnabled) {
            // write messagepoint Messages if they exist
            List<GenericXmlElementDto> genericElements = userPreferencesDto.getTouchpointMatchingStatsDto().getTreeComparisonResultNode().getMessagepointSpecificElements();
            if (CollectionUtils.isNotEmpty(genericElements)) {
                for (GenericXmlElementDto genericElement : genericElements) {
                    if ("Message".equals(genericElement.getTagName())) {
                        writeGenericNode(genericElement);
                    }
                }
            }
        }

        streamWriter.writeEndElement();
    }

    private void writeMessagesRecursiveForNode(TouchpointExportSelectionNode treeNode, boolean isUpdateTouchpointModeEnabled, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        writeMessagesForNode(treeNode, isUpdateTouchpointModeEnabled, languages);
        if (CollectionUtils.isNotEmpty(treeNode.getChildren())) {
            for (TouchpointExportSelectionNode crtchild : treeNode.getChildren()) {
                writeMessagesRecursiveForNode(crtchild, isUpdateTouchpointModeEnabled, languages);
            }
        }
    }


    private void writeMessagesForNode(TouchpointExportSelectionNode treeNode, boolean isUpdateTouchpointModeEnabled, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        if (CollectionUtils.isEmpty(treeNode.getMessageConfigDtoList())) {
            return;
        }

        for (MessageConfigDto crtRationalizerMessage : treeNode.getMessageConfigDtoList()) {
            if (MATCHED_WITH_COLLISION.equals(crtRationalizerMessage.getState())) {
                if (userPreferencesDto.shouldIncludeMessageInExport(crtRationalizerMessage)) {
                    if(isUpdateTouchpointModeEnabled) {
                        TouchpointMessageCollisionDto collision = userPreferencesDto.extractCorrespondingCollisionForMessage(crtRationalizerMessage);
                        overwriteMessageInCollision(crtRationalizerMessage, collision.getMessageCollisionDto(), languages);
                    }
                }
            } else {
                if(MESSAGEPOINT_UNMATCHED.equals(crtRationalizerMessage.getState())) {
                    if(isUpdateTouchpointModeEnabled) {
                       writeMessage(crtRationalizerMessage, languages);
                    }
                } else {
                    writeMessage(crtRationalizerMessage, languages);
                }
            }
        }
    }

    private void overwriteMessageInCollision(MessageConfigDto crtRationalizerMessage, MessageCollisionDto messageCollisionDto, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        MessageConfigDto messagepointMessage = messageCollisionDto.getMessagepointMessage();
        List<ContentConfigDto> contentList = crtRationalizerMessage.getContentConfigDtoList();
        messagepointMessage.getVersionConfigDto().setContentConfigDtoList(contentList);
        writeMessage(messagepointMessage, languages);
    }

    private void writeMessage(MessageConfigDto messageDto, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        if(messageDto.getVersionConfigDto() != null || messageDto.getAttributes() != null || messageDto.getRationalizerDocumentName() != null || messageDto.getParent() != null) {
            streamWriter.writeStartElement("Message");
            writeMessageAttributes(messageDto);
            if (messageDto.getVersionConfigDto() != null) {
                streamWriter.writeStartElement("Version");
                writeMessageVersionAttributes(messageDto);
                writeMessageNameNode(messageDto);
                writeDocumentContentMetatags(messageDto.getVersionConfigDto().getMetatags());
                writeEmptyElementsForContent();
                if (messageDto.getVersionConfigDto().getDeliveryDto() != null) {
                    writeDeliveryNode(messageDto);
                }
                writeMessageContentsNode(messageDto, languages);
                streamWriter.writeEndElement();
            }
            streamWriter.writeEndElement();
        }
    }

    private void copyAllUntilTagExclusive(XMLStreamReader reader, XMLStreamWriter writer, String tag) throws XMLStreamException {
        while (reader.hasNext()) {
            write(reader, writer);
            reader.next();
            if (reader.isStartElement() && tag.equals(reader.getLocalName())) {
                break;
            }
        }
    }

    private void copyEntireElement(XMLStreamReader reader, XMLStreamWriter writer, String tagName) throws XMLStreamException {
        boolean foundTag = false;
        while (reader.hasNext() && !(reader.isEndElement() && tagName.equals(reader.getLocalName()))) {
            if (reader.isStartElement() && tagName.equals(reader.getLocalName())) {
                foundTag = true;
            }
            if (foundTag) {
                write(reader, writer);
            }
            reader.next();
        }
        // write end tag
        if (foundTag) {
            write(reader, writer);
        }
    }

    private void copyAllUntilMessagesOrEmbeddedContentsTags(XMLStreamReader reader, XMLStreamWriter writer) throws XMLStreamException {
        while (reader.hasNext()) {
            write(reader, writer);
            reader.next();
            if (reader.isEndElement() && "MpTouchpointDefinition".equals(reader.getLocalName())) {
                break;
            }
            if (reader.isStartElement() && ("EmbeddedContents".equals(reader.getLocalName()) || "Messages".equals(reader.getLocalName()))) {
                break;
            }
        }
    }

    private Path createOutputPath() {
        File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
        String filename = createOutputFileName(rationalizerApplication, DateUtil.exportDateTimeStamp());
        boolean mkDirsResult = outputDirectory.mkdirs();
        log.debug("Failed to create directory structure for " + outputDirectory.getPath());
        String outputFilePath = outputDirectory.getPath() + File.separator + filename;
        return Paths.get(outputFilePath);
    }

    private void writeMessageVersionAttributes(MessageConfigDto messageDto) throws XMLStreamException {
        if (MapUtils.isNotEmpty(messageDto.getVersionConfigDto().getAttributes())) {
            for (Map.Entry<String, String> crtEntry : messageDto.getVersionConfigDto().getAttributes().entrySet()) {
                streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
            }
        } else {
            streamWriter.writeAttribute("id", generateUniqueId());
            streamWriter.writeAttribute("guid", RandomGUID.getGUID().toLowerCase());
            streamWriter.writeAttribute("status", "Working Copy");
            streamWriter.writeAttribute("flowtype", "1");
            streamWriter.writeAttribute("contentspecializationtype", "0");
            streamWriter.writeAttribute("keepcontenttogether", "false");
            streamWriter.writeAttribute("canvasmaxwidth", "0");
            streamWriter.writeAttribute("canvasmaxheight", "0");
            streamWriter.writeAttribute("canvastrimwidth", "0");
            streamWriter.writeAttribute("canvastrimheight", "0");
            streamWriter.writeAttribute("supportstables", "true");
            streamWriter.writeAttribute("supportsforms", "true");
            streamWriter.writeAttribute("supportsbarcodes", "false");
            streamWriter.writeAttribute("supportscontentmenus", "false");
            streamWriter.writeAttribute("keepContentTogether", "false");
            streamWriter.writeAttribute("renderAsTaggedText", "false");
            streamWriter.writeAttribute("graphictype", "0");
            streamWriter.writeAttribute("origin", "New");
        }
    }

    private void writeMessageAttributes(MessageConfigDto messageDto) throws XMLStreamException {
        if (MapUtils.isNotEmpty(messageDto.getAttributes())) {
            for (Map.Entry<String, String> crtEntry : messageDto.getAttributes().entrySet()) {
                streamWriter.writeAttribute(crtEntry.getKey(), crtEntry.getValue());
            }
        } else {
            streamWriter.writeAttribute("id", "" + messageDto.getId());
            streamWriter.writeAttribute("guid", RandomGUID.getGUID().toLowerCase());
            streamWriter.writeAttribute("dna", RandomGUID.getGUID().toLowerCase());
            streamWriter.writeAttribute("type", "Selectable(Touchpoint Message)");
            streamWriter.writeAttribute("contenttype", "Text");
            streamWriter.writeAttribute("suppressed", "false");
            streamWriter.writeAttribute("onhold", "false");
            streamWriter.writeAttribute("islocal", "false");
        }
    }

    private void writeMessageContentsNode(MessageConfigDto messageDto, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        if (CollectionUtils.isEmpty(messageDto.getVersionConfigDto().getContentConfigDtoList())) {
            streamWriter.writeEmptyElement("Contents");
            return;
        }
        streamWriter.writeStartElement("Contents");
        for (ContentConfigDto crtContent : messageDto.getVersionConfigDto().getContentConfigDtoList()) {
            streamWriter.writeStartElement("Content");
            streamWriter.writeAttribute("language", crtContent.getLanguage());
            streamWriter.writeCData(resolveCollision(crtContent.getContentForExportToMessagepoint(userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerReferenceDataSourceExists(),
                    userPreferencesDto.getRationalizerContentStylesMap())));
            streamWriter.writeEndElement();
            writeSameAsDefaultContentForLanguages(streamWriter, languages);
        }
        streamWriter.writeEndElement();
    }

    private String resolveCollision(String message){
        Map<IdDnaPair, EmbeddedContentConfigDto> noCollisionsMap = userPreferencesDto.getTouchpointMatchingStatsDto().getNoCollisionsMap();
        if(MapUtils.isEmpty(messagepointCollision) && MapUtils.isEmpty(noCollisionsMap)){
            return message;
        }
        if(MapUtils.isNotEmpty(messagepointCollision) ) {
            for(IdDnaPair idDnaPair : messagepointCollision.keySet()) {
                String dna = idDnaPair.getDna();
                if(message.contains(dna)) {
                    message = message.replace(dna, messagepointCollision.get(idDnaPair).getDna());
                    message = message.replace(String.valueOf(idDnaPair.getId()), String.valueOf(messagepointCollision.get(idDnaPair).getId()));

                }
            }
        }
        if(MapUtils.isNotEmpty(noCollisionsMap) ) {
            for(IdDnaPair idDnaPair : noCollisionsMap.keySet()) {
                String dna = idDnaPair.getDna();
                if(message.contains(dna)) {
                    message = message.replace(dna, noCollisionsMap.get(idDnaPair).getDna());
                    message = message.replace(String.valueOf(idDnaPair.getId()), String.valueOf(noCollisionsMap.get(idDnaPair).getId()));

                }
            }
        }
        return message;
    }

    private void writeSameAsDefaultContentForLanguages(XMLStreamWriter streamWriter, List<TouchpointLanguageDto> languages) throws XMLStreamException {
        for(TouchpointLanguageDto language: languages) {
            if(!language.isDefaultLanguage()) {
                streamWriter.writeStartElement("Content");
                streamWriter.writeAttribute("language", language.getCode());
                streamWriter.writeAttribute("sameasdefaultlanguage", "true");
                streamWriter.writeEndElement();
            }
        }
    }

    private void writeEmptyElementsForContent() throws XMLStreamException {
        streamWriter.writeEmptyElement("Description");
        streamWriter.writeEmptyElement("Comments");
        streamWriter.writeEmptyElement("Timing");
        streamWriter.writeEmptyElement("TargetCriteria");
    }

    private void writeElementsForContent(EmbeddedContentConfigDto embeddedContentConfigDto) throws XMLStreamException {
        if (!StringUtil.isEmptyOrNull(embeddedContentConfigDto.getVersionConfigDto().getDescription())) {
            streamWriter.writeStartElement("Description");
            streamWriter.writeCharacters(embeddedContentConfigDto.getVersionConfigDto().getDescription());
            streamWriter.writeEndElement();
        } else {
            streamWriter.writeEmptyElement("Description");
        }
        streamWriter.writeEmptyElement("Comments");
        streamWriter.writeEmptyElement("Timing");
        streamWriter.writeEmptyElement("TargetCriteria");
    }

    private void writeDocumentContentMetatags(String metatags) throws XMLStreamException {
        if (StringUtil.isEmptyOrNull(metatags)) {
            streamWriter.writeEmptyElement("Metatags");
            return;
        }

        streamWriter.writeStartElement("Metatags");
        streamWriter.writeCharacters(metatags);
        streamWriter.writeEndElement();
    }

    private void writeMessageNameNode(MessageConfigDto messageDto) throws XMLStreamException {
        streamWriter.writeStartElement("Name");
        streamWriter.writeCharacters(messageDto.getVersionConfigDto().getName());
        streamWriter.writeEndElement();
    }

    private void writeEmbeddedContentsTag(TouchpointLanguageDto defaultLanguage) throws XMLStreamException {
        streamWriter.writeStartElement("EmbeddedContents");
        writeAllSharedContents(defaultLanguage);
        streamWriter.writeEndElement();
    }

    private void writeAllSharedContents(TouchpointLanguageDto defaultLanguage) throws XMLStreamException {
        List<EmbeddedContentConfigDto> matchingEmbeddedContents = userPreferencesDto.getTouchpointMatchingStatsDto().extractMatchedEmbeddedContentsList();
        if (CollectionUtils.isNotEmpty(matchingEmbeddedContents)) {
            for (EmbeddedContentConfigDto embeddedContentConfigDto : matchingEmbeddedContents) {
                writeEmbeddedContent(embeddedContentConfigDto, false, false, defaultLanguage);
            }
        }
        List<EmbeddedContentConfigDto> unmatchedRationalizerEmbeddedContents = userPreferencesDto.getTouchpointMatchingStatsDto().extractUnmatchedRationalizerEmbeddedContentsList();
        if (CollectionUtils.isNotEmpty(unmatchedRationalizerEmbeddedContents)) {
            for (EmbeddedContentConfigDto embeddedContentConfigDto : unmatchedRationalizerEmbeddedContents) {
                writeEmbeddedContent(embeddedContentConfigDto, false, false, defaultLanguage);
            }
        }

        if(userPreferencesDto.isUpdateTouchpointOnExport()) {
            List<EmbeddedContentConfigDto> unmatchedMessagepointEmbeddedContents = userPreferencesDto.getTouchpointMatchingStatsDto().extractUnmatchedMessagepointEmbeddedContentsList();
            if (CollectionUtils.isNotEmpty(unmatchedMessagepointEmbeddedContents)) {
                for (EmbeddedContentConfigDto embeddedContentConfigDto : unmatchedMessagepointEmbeddedContents) {
                    writeEmbeddedContent(embeddedContentConfigDto, false, true, defaultLanguage);
                }
            }
        }

        List<TouchpointEmbeddedContentCollisionDto> touchpointEmbeddedContentCollisionsList = userPreferencesDto.getTouchpointEmbeddedContentCollisionsList();
        if (CollectionUtils.isNotEmpty(touchpointEmbeddedContentCollisionsList)) {
            for (TouchpointEmbeddedContentCollisionDto crtCollision : touchpointEmbeddedContentCollisionsList) {
                if (ConflictActionEnum.IGNORE.getId() == crtCollision.getResolutionId()) {
                    writeEmbeddedContent(crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent(), false, true, defaultLanguage);
                    IdDnaPair idDnaPair = new IdDnaPair(crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent().getId(), crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent().getDna());
                    messagepointCollision.put(idDnaPair, crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent());
                } else if (ConflictActionEnum.OVERWRITE.getId() == crtCollision.getResolutionId()) {
                    //keeps ids and dnas from MP TP, only the content will be from RAT; messages will refer the ids and dnas from MP TP
                    List<ContentConfigDto> ratContentConfigDtoList = crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent().getVersionConfigDto().getContentConfigDtoList();
                    VersionConfigDto mpVersionConfigDto = crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent().getVersionConfigDto();
                    mpVersionConfigDto.setContentConfigDtoList(ratContentConfigDtoList);
                   // writeEmbeddedContent(crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent(), false, false, defaultLanguage);
                    writeEmbeddedContent(crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent(), false, true, defaultLanguage);
                    IdDnaPair idDnaPair = new IdDnaPair(crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent().getId(), crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent().getDna());
                    messagepointCollision.put(idDnaPair, crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent());
                } else {
                    writeEmbeddedContent(crtCollision.getEmbeddedContentCollisionDto().getMessagepointEmbeddedContent(), false, true, defaultLanguage);
                    writeEmbeddedContent(crtCollision.getEmbeddedContentCollisionDto().getRationalizerEmbeddedContent(), false, false, defaultLanguage);
                }
            }
        }
    }

    private void writeEmbeddedContent(EmbeddedContentConfigDto embeddedContentConfigDto, boolean shouldRegenerateNameAndIds, boolean unmatchedMessagepointEmbeddedContent, TouchpointLanguageDto defaultLanguage) throws XMLStreamException {
        streamWriter.writeStartElement("EmbeddedContent");
        writeEmbeddedContentAttributes(embeddedContentConfigDto, shouldRegenerateNameAndIds, unmatchedMessagepointEmbeddedContent, defaultLanguage);
        streamWriter.writeStartElement("Version");
        writeEmbeddedContentVersionAttributes(embeddedContentConfigDto.getVersionConfigDto(), unmatchedMessagepointEmbeddedContent);
        writeEmbeddedContentNameNode(embeddedContentConfigDto, shouldRegenerateNameAndIds);
        writeEmbeddedContentMetatags(embeddedContentConfigDto);
        writeElementsForContent(embeddedContentConfigDto);
        writeContentsNodeForEmbeddedContent(embeddedContentConfigDto);
        writeSelectionsNodeForEmbeddedContent(embeddedContentConfigDto);
        streamWriter.writeEndElement();
        streamWriter.writeEndElement();
    }

    private void writeSelectionsNodeForEmbeddedContent(EmbeddedContentConfigDto embeddedContentConfigDto) throws XMLStreamException {
        GenericXmlElementDto selectionsNode = null;
        selectionsNode = embeddedContentConfigDto.getVersionConfigDto().getSelectionsDto();
        if (selectionsNode == null) {
            return;
        }
        writeGenericNode(selectionsNode);
    }

    private void writeContentsNodeForEmbeddedContent(EmbeddedContentConfigDto embeddedContentConfigDto) throws XMLStreamException {
        if (CollectionUtils.isEmpty(embeddedContentConfigDto.getVersionConfigDto().getContentConfigDtoList())) {
            return;
        }
        streamWriter.writeStartElement("Contents");
        Map<String, String> contentAttributes = null;
        for (ContentConfigDto crtContent : embeddedContentConfigDto.getVersionConfigDto().getContentConfigDtoList()) {
            streamWriter.writeStartElement("Content");
            contentAttributes = crtContent.getAttributes();
            if (contentAttributes != null) {
                for (Map.Entry<String, String> entry : contentAttributes.entrySet()) {
                    streamWriter.writeAttribute(entry.getKey(), entry.getValue());
                }
            }
            String contentHtml = crtContent.getContentHtml();
            if (!StringUtil.isEmptyOrNull(contentHtml)) {
                streamWriter.writeCData(computeMarkupVersionOfContent(crtContent.getContentForExportToMessagepoint(userPreferencesDto.getTouchpointMatchingStatsDto().getRationalizerReferenceDataSourceExists(), userPreferencesDto.getRationalizerContentStylesMap())));
            }
            streamWriter.writeEndElement();
        }
        streamWriter.writeEndElement();
    }

    private void writeEmbeddedContentMetatags(EmbeddedContentConfigDto embeddedContentConfigDto) throws XMLStreamException {
        String metatags = embeddedContentConfigDto.getVersionConfigDto().getMetatags();
        if (StringUtil.isEmptyOrNull(metatags)) {
            streamWriter.writeEmptyElement("Metatags");
            return;
        }

        streamWriter.writeStartElement("Metatags");
        streamWriter.writeCharacters(metatags);
        streamWriter.writeEndElement();
    }

    private void writeEmbeddedContentNameNode(EmbeddedContentConfigDto embeddedContentConfigDto, boolean shouldRegenerateNameAndIds) throws XMLStreamException {
        streamWriter.writeStartElement("Name");
        if (shouldRegenerateNameAndIds) {
            streamWriter.writeCharacters(embeddedContentConfigDto.getVersionConfigDto().getName() + "-1");
        } else {
            streamWriter.writeCharacters(embeddedContentConfigDto.getVersionConfigDto().getName());
        }
        streamWriter.writeEndElement();
    }

    private void writeEmbeddedContentVersionAttributes(VersionConfigDto versionConfigDto, boolean unmatchedMessagepointEmbeddedContent) throws XMLStreamException {
        if(unmatchedMessagepointEmbeddedContent) {
            if(MapUtils.isNotEmpty(versionConfigDto.getAttributes())) {
                for(Map.Entry<String, String> entry: versionConfigDto.getAttributes().entrySet()) {
                    streamWriter.writeAttribute(entry.getKey(), entry.getValue());
                }
            }
        } else {
            streamWriter.writeAttribute("id", generateUniqueId());
            streamWriter.writeAttribute("guid", RandomGUID.getGUID().toLowerCase());
            streamWriter.writeAttribute("status", "Active");
            streamWriter.writeAttribute("origin", "Version Check Out");
            streamWriter.writeAttribute("advanced", "false");
            streamWriter.writeAttribute("deliverytypeid", "0");
            streamWriter.writeAttribute("usagetypeid", "1");
            streamWriter.writeAttribute("contenttypeid", "0");
            streamWriter.writeAttribute("compvarformattype", "0");
            if(userPreferencesDto.getTouchpointConnectorType() == MP_COMPOSER_CONNECTOR_TYPE || userPreferencesDto.getTouchpointConnectorType() == SEFAS_CONNECTOR_TYPE){
                streamWriter.writeAttribute("insertasparagraph", "false");
            } else {
                streamWriter.writeAttribute("insertasparagraph", "true");
            }
            streamWriter.writeAttribute("contenttrimtype", "2");
            streamWriter.writeAttribute("supportstables", "true");
            streamWriter.writeAttribute("supportsforms", "false");
            streamWriter.writeAttribute("supportsbarcodes", "false");
            streamWriter.writeAttribute("supportscontentmenus", "false");
            streamWriter.writeAttribute("keepContentTogether", "false");
            streamWriter.writeAttribute("renderAsTaggedText", "false");
        }
    }

    private void writeEmbeddedContentAttributes(EmbeddedContentConfigDto embeddedContentConfigDto, boolean shouldRegenerateIds,
                                                boolean unmatchedMessagepointEmbeddedContent, TouchpointLanguageDto defaultLanguageDto) throws XMLStreamException {
        if(unmatchedMessagepointEmbeddedContent) {
            if(MapUtils.isNotEmpty(embeddedContentConfigDto.getAttributes())) {
                for(Map.Entry<String, String> entry: embeddedContentConfigDto.getAttributes().entrySet()) {
                    streamWriter.writeAttribute(entry.getKey(), entry.getValue());
                }
            }
        } else {
            String idString = shouldRegenerateIds ? generateUniqueId() : "" + embeddedContentConfigDto.getId();
            streamWriter.writeAttribute("id", idString);
            String guid = RandomGUID.getGUID().toLowerCase();
            String dna = StringUtils.isEmpty(embeddedContentConfigDto.getDna()) ? RandomGUID.getGUID().toLowerCase() :  embeddedContentConfigDto.getDna() ;
            if (MapUtils.isNotEmpty(embeddedContentConfigDto.getAttributes()) && StringUtils.isNotEmpty(embeddedContentConfigDto.getAttributes().get("guid"))) {
                guid = embeddedContentConfigDto.getAttributes().get("guid");
            }
            streamWriter.writeAttribute("guid", guid);
            streamWriter.writeAttribute("dna", dna);
            streamWriter.writeAttribute("type", "Regular");
            streamWriter.writeAttribute("contenttype", "Text");
            streamWriter.writeAttribute("defaultlanguage", defaultLanguageDto.getCode());

            /* Export of global smart text (embeddedContent) should include following attributes

		embeddedContentElement.addAttribute("defaultlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
		embeddedContentElement.addAttribute("defaultlocale", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

             */

        }
    }


    private void writeDeliveryNode(MessageConfigDto messageDto) throws XMLStreamException {
        streamWriter.writeStartElement("Delivery");
        streamWriter.writeAttribute("type", messageDto.getVersionConfigDto().getDeliveryDto().getType());
        streamWriter.writeAttribute("zonerefid",  messageDto.getVersionConfigDto().getDeliveryDto().getZoneRefId() != null ? "" + messageDto.getVersionConfigDto().getDeliveryDto().getZoneRefId() : "");
        streamWriter.writeAttribute("priority", "" + messageDto.getVersionConfigDto().getDeliveryDto().getPriority());
        streamWriter.writeEndElement();
    }

    private String createOutputFileName(RationalizerApplication rationalizerApplication, String exportId) {
        String strPattern = "[^a-zA-Z0-9._\\-:\\\\\\s]+";
        String validExportAppName = rationalizerApplication.getName();
        validExportAppName = validExportAppName.replaceAll(strPattern, "");

        return RAPORT_PREFIX_NAME + "_" + rationalizerApplication.getId() + "_" + validExportAppName + "_" + exportId + ".xml";
    }

    private String computeMarkupVersionOfContent(String contentMarkup) {
        if (StringUtil.isEmptyOrNull(contentMarkup)) {
            return "";
        }

        if (contentMarkup.contains("</p>") || contentMarkup.contains("</div>") || contentMarkup.contains("</li>")) {
            return contentMarkup;
        }

        return "<p>" + contentMarkup + "</p>";
    }

public static class IdDnaPair {
        private long id;
        private String dna;

    public IdDnaPair(long id, String dna) {
        this.id = id;
        this.dna = dna;
    }

    public long getId() {
        return id;
    }

    public String getDna() {
        return dna;
    }
}
}
