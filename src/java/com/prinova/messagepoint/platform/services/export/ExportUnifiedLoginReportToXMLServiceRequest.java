package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportUnifiedLoginReportToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 1482969310673915917L;
	private long 				reportId;
	private User				user;
	private boolean				includePincLicenseStatus = false;

	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}
	public User getUser() {
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}

	public boolean isIncludePincLicenseStatus() {
		return includePincLicenseStatus;
	}

	public void setIncludePincLicenseStatus(boolean includePincLicenseStatus) {
		this.includePincLicenseStatus = includePincLicenseStatus;
	}
}