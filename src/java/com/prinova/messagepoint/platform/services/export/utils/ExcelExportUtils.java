package com.prinova.messagepoint.platform.services.export.utils;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameIgnoreCaseComparator;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.export.ExportDynamicContentObjectService;
import com.prinova.messagepoint.platform.services.export.ExportDynamicContentObjectServiceRequest;
import com.prinova.messagepoint.platform.services.export.ExportUtil;
import com.prinova.messagepoint.util.DateUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.*;
import java.nio.file.Paths;
import java.util.*;

public class ExcelExportUtils {
    public static void setLanguageHeaderLabels(int headerRowNum, int rowColumn, XSSFSheet sheet, List<MessagepointLocale> locales, String content) {
        Row headerRow = sheet.getRow(headerRowNum);

        for (MessagepointLocale locale : locales){
            String header = locale.getName().concat(" ").concat(content);
            String name = headerRow.getCell(rowColumn).getStringCellValue();

            XSSFTable table = sheet.getTables().get(0);
            Optional<XSSFTableColumn> tableCol = table.getColumns().stream().filter(x -> x.getName().equals(name)).findFirst();

            if (tableCol.isPresent()) {
                tableCol.get().setName(header);
                headerRow.getCell(rowColumn).setCellValue(header);
            }

            rowColumn += 5;
        }
    }

    public static byte[] generateDynamicSmartTextExcelWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, ExportDynamicContentObjectServiceRequest request) throws Exception {
        File file = getExportFileTemplate(contentObject);
        InputStream fileStream = new ByteArrayInputStream(FileUtils.readFileToByteArray(file));
        XSSFWorkbook workbook = new XSSFWorkbook(fileStream);

        generateDynamicSmartTextWorkbook(contentObject, variantData, workbook, request.getRequestor());

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);

        return byteArrayOutputStream.toByteArray();
    }

    public static byte[] generateDynamicImageLibraryExcelWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, ExportDynamicContentObjectServiceRequest request) throws Exception {
        File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);

        File file = new File(outputDirectory + File.separator + ExportUtil.EXCEL_EXPORT_TEMPLATE_DYNAMIC_IMAGE);
        InputStream fileStream = new ByteArrayInputStream(FileUtils.readFileToByteArray(file));
        XSSFWorkbook workbook = new XSSFWorkbook(fileStream);

        generateDynamicImageLibraryWorkbook(contentObject, variantData, workbook, request.getRequestor());

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);

        return byteArrayOutputStream.toByteArray();
    }

    public static byte[] generateDynamicMessageExcelWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, ExportDynamicContentObjectServiceRequest request) throws Exception {
        File file = getExportFileTemplate(contentObject);
        InputStream fileStream = new ByteArrayInputStream(FileUtils.readFileToByteArray(file));
        XSSFWorkbook workbook = new XSSFWorkbook(fileStream);

        generateDynamicMessageWorkbook(contentObject, variantData, workbook, request.getRequestor());

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);


       return byteArrayOutputStream.toByteArray();
    }

    private static void generateDynamicSmartTextWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, XSSFWorkbook workbook, User requestor) throws Exception {
        XSSFSheet sheet = workbook.getSheet("variants");

        List<String> constraintValues = new ArrayList<>();
        constraintValues.add("Empty");
        constraintValues.add("Inheriting from parent");

        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();

        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));

        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(contentObject.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(contentObject.getGuid());
        Row instanceGuidRow = sheet.getRow(6);

        ContentObjectData wc = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
        instanceGuidRow.createCell(1).setCellValue(wc != null ? wc.getGuid() : "");

        int rowCounter = 9;
        int headerRowNum = 8;

        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList();

        for ( ParameterGroupTreeNode pgTreeNode : variantData.keySet() ) {
            if (pgTreeNode != null) {
                generateChildPgtnRow(variantData.get(pgTreeNode), pgTreeNode, sheet, cellRangeAddressList, rowCounter);
                rowCounter++;
            }
        }

        if (variantData.containsKey(null)) {
            generateChildPgtnRow(variantData.get(null), null, sheet, cellRangeAddressList, rowCounter);
        }

        DataValidation dataValidation;
        DataValidationConstraint constraint;
        DataValidationHelper validationHelper;
        validationHelper=new XSSFDataValidationHelper(sheet);
        constraint =validationHelper.createExplicitListConstraint(constraintValues.toArray(new String[2]));
        dataValidation = validationHelper.createValidation(constraint, cellRangeAddressList);
        dataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidation);

        int rowColumn = 5;

        Comparator<MessagepointLocale> langComp = Comparator.comparing(MessagepointLocale::getId);
        ArrayList<MessagepointLocale> languages = new ArrayList<>(variantData.get(null).keySet());
        languages.sort(langComp);
        HashSet<String> languageCodes = new HashSet<>();

        for (ParameterGroupTreeNode pgTn : variantData.keySet()) {
            for (MessagepointLocale locale : variantData.get(pgTn).keySet()){
                languageCodes.add(locale.getCode());
            }
        }

        ExcelExportUtils.setLanguageHeaderLabels(headerRowNum, rowColumn, sheet, languages, "Content");

        for(int i = (5 + (languageCodes.size() * 5)); i <= sheet.getRow(headerRowNum).getLastCellNum(); i++){
            sheet.setColumnHidden(i, true);
        }
    }

    private static void generateDynamicMessageWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, XSSFWorkbook workbook, User requestor) throws Exception
    {
        XSSFSheet sheet = workbook.getSheet("variants");

        int counter;
        String[] imageNameList;
        if(contentObject.getContentType().isGraphic() ){	// Graphic
            XSSFSheet ref_sheet = workbook.getSheet("options");

            List<ContentObject> allImages = new ArrayList<>(ContentObject.findGlobalImagesVisibleForDocument(contentObject.getDocument(), true, false));
            imageNameList = new String[allImages.size()+3];

            imageNameList[0] = "Empty";
            imageNameList[1] = "Inheriting from parent";
            imageNameList[2] = "Uploaded";
            Row row0 = ref_sheet.createRow(0);
            row0.createCell(0).setCellValue(imageNameList[0]);
            row0.createCell(1).setCellValue(0);
            Row row1 = ref_sheet.createRow(1);
            row1.createCell(0).setCellValue(imageNameList[1]);
            row1.createCell(1).setCellValue(0);
            Row row2 = ref_sheet.createRow(2);
            row2.createCell(0).setCellValue(imageNameList[2]);
            row2.createCell(1).setCellValue(0);

            counter = 3;
            for(ContentObject image: allImages){
                Set<ContentObjectAssociation> msgCAs = image.getContentObjectAssociations();

                if(!msgCAs.isEmpty()){
                    Row dataRow = ref_sheet.createRow(counter);
                    dataRow.createCell(0).setCellValue(image.getName());
                    ContentObjectData contentObjectData = image.getContentObjectDataWorkingCentric();
                    if(contentObjectData != null){
                        dataRow.createCell(1).setCellValue(contentObjectData.getId());
                    }
                    imageNameList[counter] = image.getName();
                    counter++;
                }
            }
        }else{
            imageNameList = new String[2];

            imageNameList[0] = "Empty";
            imageNameList[1] = "Inheriting from parent";
        }


        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();

        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));

        Row nameRow = sheet.getRow(4);
        if(contentObject.getIsTouchpointLocal())
            nameRow.createCell(0).setCellValue("Local Smart Text Name");
        nameRow.createCell(1).setCellValue(contentObject.getName());
        Row guidRow = sheet.getRow(5);
        if(contentObject.getIsTouchpointLocal())
            guidRow.createCell(0).setCellValue("Local Smart Text GUID");
        guidRow.createCell(1).setCellValue(contentObject.getGuid());

        int rowCounter = 8;
        int headerRowNum = 7;

        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList();

        for ( ParameterGroupTreeNode pgTreeNode : variantData.keySet() ) {
            if (pgTreeNode != null) {
                generateChildPgtnRow(variantData.get(pgTreeNode), pgTreeNode, sheet, cellRangeAddressList, rowCounter);
                rowCounter++;
            }
        }

        if (variantData.containsKey(null)) {
            generateChildPgtnRow(variantData.get(null), null, sheet, cellRangeAddressList, rowCounter);
        }


        DataValidation dataValidation;
        DataValidationConstraint constraint;
        DataValidationHelper validationHelper;
        validationHelper=new XSSFDataValidationHelper(sheet);
        constraint =validationHelper.createExplicitListConstraint(imageNameList);
        dataValidation = validationHelper.createValidation(constraint, cellRangeAddressList);
        dataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidation);

        Comparator<MessagepointLocale> langComp = Comparator.comparing(MessagepointLocale::getId);
        ArrayList<MessagepointLocale> languages = new ArrayList<>(variantData.get(null).keySet());
        languages.sort(langComp);

        for(int i = (5 + (languages.size() * 5)); i <= sheet.getRow(headerRowNum).getLastCellNum(); i++){
            sheet.setColumnHidden(i, true);
        }

        int rowColumn = 5;

        ExcelExportUtils.setLanguageHeaderLabels(headerRowNum, rowColumn, sheet, languages, "Content");

        //sheet.setColumnHidden(0, true);
        sheet.setColumnWidth(0, 10000); // Manually set column width for GUID field
        sheet.setColumnWidth(1, 9000); // Manually set column width for GUID field

    }

    private static void generateDynamicImageLibraryWorkbook(ContentObject contentObject, Map<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> variantData, XSSFWorkbook workbook, User requestor) throws Exception
    {

        XSSFSheet sheet = workbook.getSheet("variants");
        XSSFSheet ref_sheet = workbook.getSheet("images");
        Set<Document> allDocs = contentObject.getDocuments();
        List<ContentObject> allImages = new ArrayList<>();
        long dropdownStringLength = 35;

        for(ContentObject instance: ContentObject.findAllWithObjectType(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE, ContentObject.OBJECT_TYPE_LOCAL_IMAGE)){
            if(instance.isGlobalImage()){
                allImages.add(instance);
                dropdownStringLength += instance.getName().length();
            }else{
                for(Document doc : instance.getDocuments()){
                    if(allDocs.contains(doc) && !allImages.contains(instance)){
                        allImages.add(instance);
                        dropdownStringLength += instance.getName().length();
                    }
                }
            }
        }
        allImages.sort(new IdentifiableMessagepointModelNameIgnoreCaseComparator());
        Comparator<MessagepointLocale> langComp = Comparator.comparing(MessagepointLocale::getId);
        ArrayList<MessagepointLocale> languages = new ArrayList<>(variantData.get(null).keySet());
        languages.sort(langComp);
        HashSet<String> languageCodes = new HashSet<>();

        for (ParameterGroupTreeNode pgTn : variantData.keySet())
        {
            for (MessagepointLocale locale : variantData.get(pgTn).keySet())
            {
                languageCodes.add(locale.getCode());
            }
        }
        int imageNameListCount = allImages.size()+3;
        if(dropdownStringLength > 255)
            imageNameListCount = 3;
        String[] imageNameList = new String[imageNameListCount];

        imageNameList[0] = "Empty";
        imageNameList[1] = "Inheriting from parent";
        imageNameList[2] = "Uploaded";
        Row row0 = ref_sheet.createRow(0);
        row0.createCell(0).setCellValue(imageNameList[0]);
        row0.createCell(1).setCellValue(0);
        Row row1 = ref_sheet.createRow(1);
        row1.createCell(0).setCellValue(imageNameList[1]);
        row1.createCell(1).setCellValue(0);
        Row row2 = ref_sheet.createRow(2);
        row2.createCell(0).setCellValue(imageNameList[2]);
        row2.createCell(1).setCellValue(0);

        int counter = 3;
        for(ContentObject image: allImages){
            if(dropdownStringLength < 255){
                imageNameList[counter] = image.getName();
            }
            Row dataRow = ref_sheet.createRow(counter);
            dataRow.createCell(0).setCellValue(image.getName());
            dataRow.createCell(1).setCellValue(image.getId());
            counter++;
        }
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();

        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));

        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(contentObject.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(contentObject.getGuid());
        Row instanceGuidRow = sheet.getRow(6);
        ContentObjectData wc = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
        instanceGuidRow.createCell(1).setCellValue(wc != null ? wc.getGuid() : "");

        int headerRowNum = 8;
        int rowCounter = 9;

        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList();

        for (ParameterGroupTreeNode pgTn : variantData.keySet()) {
            if (pgTn != null) {
                generateChildPgtnRow(variantData.get(pgTn), pgTn,sheet, cellRangeAddressList, rowCounter);
                rowCounter++;
            }
        }

        if (variantData.containsKey(null)) {
            generateChildPgtnRow(variantData.get(null), null, sheet, cellRangeAddressList, rowCounter);
        }

        DataValidation dataValidation;
        DataValidationConstraint constraint;
        DataValidationHelper validationHelper;
        validationHelper=new XSSFDataValidationHelper(sheet);
        constraint =validationHelper.createExplicitListConstraint(imageNameList);
        dataValidation = validationHelper.createValidation(constraint, cellRangeAddressList);
        dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        dataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidation);
        for(int i = (5 + (languageCodes.size() * 5)); i <= sheet.getRow(headerRowNum).getLastCellNum(); i++){
            sheet.setColumnHidden(i, true);
        }

        int rowColumn = 5;

        ExcelExportUtils.setLanguageHeaderLabels(headerRowNum, rowColumn, sheet, languages, "Image");

        sheet.setColumnHidden(0, false);

    }

    private static void generateChildPgtnRow(Map<MessagepointLocale, ContentObjectAssociation> variantContent, ParameterGroupTreeNode pgTreeNode, XSSFSheet sheet, CellRangeAddressList cellRangeAddressList, int rowCounter) throws Exception {

        // SelectionContent sc = null;
        Row dataRow = sheet.createRow(rowCounter);
        if(pgTreeNode != null){
            dataRow.createCell(0).setCellValue(pgTreeNode.getGuid());
            dataRow.createCell(1).setCellValue(pgTreeNode.getId());
            dataRow.createCell(2).setCellValue(pgTreeNode.getParentNode() != null ? pgTreeNode.getParentNode().getId() : 0);
            dataRow.createCell(3).setCellValue(pgTreeNode.getName());

            org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
            xmlDoc.setXMLEncoding("UTF-8");
            Element element = xmlDoc.addElement("variant");

            if (pgTreeNode.getParameterGroupInstanceCollection() != null)
            {
                createDataValueTag(pgTreeNode.getParameterGroupInstanceCollection(), element);
            }
            String value = StringEscapeUtils.escapeXml(element.getStringValue());
            if(value.length()>=32767){
                value = value.substring(0,32760) + "...";
            }
            dataRow.createCell(4).setCellValue(value);

            // sc = SelectionContent.findByPgTreeNodeId(childNode.getId());
        }else{ // Master
            dataRow.createCell(0).setCellValue("");
            dataRow.createCell(1).setCellValue(1);
            dataRow.createCell(2).setCellValue("");
            dataRow.createCell(3).setCellValue("Master");
            dataRow.createCell(4).setCellValue("");
        }
        sheet.autoSizeColumn(0); // Auto set column width for GUID field
        sheet.autoSizeColumn(1); // Auto set column width for ID field

        int cellCounter = 0;

        for(MessagepointLocale locale : variantContent.keySet().stream().sorted(Comparator.comparing(MessagepointLocale::getId)).toList()){
            int cellColumn = cellCounter + 5;
            Cell cell = dataRow.createCell(cellColumn);
            cellRangeAddressList.addCellRangeAddress(cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
            ContentObjectAssociation ca = variantContent.get(locale);
            String cellValue = DynamicContentExportUtil.getVariantExportContent(ca);
            // The maximum length of cell contents (text) is 32767 characters
            if(cellValue.length()>=32767){
                cellValue = cellValue.substring(0,32760) + "...";
            }
            cell.setCellValue(cellValue);
            cellCounter++;
            cellColumn++;
            Cell idCell = dataRow.createCell(cellColumn);
            if ((ca.getTypeId() == ContentAssociationType.ID_REFERENCES) || StringUtils.equals("Empty", cellValue)) {
                idCell.setCellValue(0);
            } else {
                idCell.setCellValue(ca.getId());
            }
            sheet.setColumnHidden(idCell.getColumnIndex(), true);
            cellCounter++;
            cellColumn++;
            Cell lanuguageCell = dataRow.createCell(cellColumn);
            lanuguageCell.setCellValue(locale.getLanguageCode());
            cellCounter++;
            cellColumn++;
            Cell localeCell = dataRow.createCell(cellColumn);
            localeCell.setCellValue(locale.getCode());
            sheet.setColumnHidden(localeCell.getColumnIndex(), true);
            cellCounter++;
            cellColumn++;
            Cell sameasparentCell = dataRow.createCell(cellColumn);
            sameasparentCell.setCellValue(ca.getTypeId()== ContentAssociationType.ID_REFERENCES);
            sheet.setColumnHidden(sameasparentCell.getColumnIndex(), true);

            cellCounter++;
        }
    }

    public static void createDataValueTag(ParameterGroupInstanceCollection pgiCollection, Element dataValuesElement)
            throws Exception
    {
        Element dataValueElement = dataValuesElement.addElement("DataValue");
        dataValueElement.addAttribute("id", Long.toString(pgiCollection.getId()));

        List<String> pgiPathValues = new ArrayList<>();
        createValueTag(pgiCollection.getId(), dataValueElement, pgiPathValues, 1, false);
    }

    private static boolean createValueTag(long pgiCollectionId, Element dataValueElement, List<String> pgiPathValues, int level, boolean secondValueTag) {
        List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
        boolean createValueTag = false;
        List<String> valueTagValues = new ArrayList<>();
        if (IthItemValues != null && !IthItemValues.isEmpty()) {
            for (String IthItemValue : IthItemValues) {
                List<String> pathValues = new ArrayList<>(pgiPathValues);
                pathValues.add(IthItemValue);
                List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
                if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
                    secondValueTag = createValueTag(pgiCollectionId, dataValueElement, pathValues, level + 1, secondValueTag);
                } else {
                    createValueTag = true;
                    valueTagValues.add(IthItemValue);
                }
            }
            if (createValueTag) {
                Element valueElement = dataValueElement.addElement("Value");
                StringBuilder valueString = new StringBuilder();

                if (secondValueTag)
                    valueString = new StringBuilder("|");
                else
                    secondValueTag = true;

                if (!pgiPathValues.isEmpty()) {
                    for (String pathValue : pgiPathValues) {
                        if (!valueString.toString().isEmpty() && !valueString.toString().equals("|")) {
                            valueString.append(";");
                        }
                        valueString.append(pathValue);
                    }
                }
                if (!valueTagValues.isEmpty()) {
                    if (!valueString.toString().isEmpty() && !valueString.toString().equals("|")) {
                        valueString.append(";");
                    }
                    for (String valueTagValue : valueTagValues) {
                        valueString.append(valueTagValue).append(",");
                    }
                    if (valueString.toString().endsWith(",")) {
                        valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
                    }
                }
                valueElement.addText(valueString.toString());
            }
        }
        return secondValueTag;
    }

    private static File getExportFileTemplate(ContentObject contentObject) {
        File result;
        ExportUtil.ExportType exportType = ExportDynamicContentObjectService.getExportType(contentObject);

        if (exportType == ExportUtil.ExportType.DYNAMICSMARTTEXT || exportType == ExportUtil.ExportType.DYNAMICSMARTCANVAS) {
            result = Paths.get(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR, ExportUtil.EXCEL_EXPORT_TEMPLATE_DYNAMIC_SMARTTEXT).toFile();
        } else if (exportType == ExportUtil.ExportType.DYNAMICIMAGE) {
            result = Paths.get(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR, ExportUtil.EXCEL_EXPORT_TEMPLATE_DYNAMIC_IMAGE).toFile();
        } else {
            result = Paths.get(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR, ExportUtil.EXCEL_EXPORT_TEMPLATE_DYNAMIC_MESSAGE).toFile();
        }

        return result;
    }


}
