package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.*;

public class ExportSmartTextToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportSmartTextToXMLService";
	private static final Log log = LogUtil.getLog(ExportSmartTextToXMLService.class);

    private User requestor;
	private long reportTimestamp;
	private List<Long> list;
	private int auditReportTypeId  = AuditReportType.ID_SMART_TEXT_AUDIT_REPORT;

	// This is used by Global Smart Text Audit Report
	// There is no Touchpoint, it means System Default Language Locale needs to be used

	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportSmartTextToXMLServiceRequest request = (ExportSmartTextToXMLServiceRequest) context.getRequest();
	    	requestor = request.getRequestor();
	    	reportTimestamp = request.getReportTimestamp();
	    	setList(request.getList());
			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			try{
				generateXML(request, document);
				String filePath = ExportUtil.saveXMLToFile(document, requestor, AuditReportType.ID_SMART_TEXT_AUDIT_REPORT, reportTimestamp);
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);
			}catch (Exception e){
				this.getResponse(context).addErrorMessage(
				        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
						context.getLocale() );
				
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(null);
			}
		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportSmartTextToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	private org.dom4j.Document initializeWriter( )
	{
		return DocumentHelper.createDocument();
	}

	private void generateXML(ExportSmartTextToXMLServiceRequest request,org.dom4j.Document document) throws Exception 
	{
		requestor = User.findById(requestor.getId());
		auditReportTypeId = request.getAuditReportTypeId();

		Element auditElm = document.addElement("SmartTextAudit");
		auditElm.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		buildMetaDataElm(auditElm, requestor, auditReportTypeId);
		
		
		//Collections.sort(list, new ObjectNameComparator());
		
		if ( !list.isEmpty() && !list.isEmpty())
		{
			
			//Collections.sort(list, new SmartTextIdComparator());		

			ContentObject contentObject = ContentObject.findById(list.get(0));
			ContentObjectData instance = contentObject.getContentObjectDataWorkingCentric();

			Element contentLibraryElement = auditElm.addElement("SmartText");
			createSmartTextTag(contentObject, contentLibraryElement);
			Element referenceDataElement = auditElm.addElement("ReferenceData");

			if (!contentObject.getParameterGroupInstanceCollections().isEmpty())
			{
				Element dataValuesElement = referenceDataElement.addElement("DataValues");
				for (ParameterGroupInstanceCollection pgiCollection : contentObject.getParameterGroupInstanceCollections())
				{
					ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
				}
			}

			if (!contentObject.getDocuments().isEmpty())
			{
				Element touchpointsElement = referenceDataElement.addElement("Touchpoints");
				for (Document messageDocument : contentObject.getDocuments())
				{
					if (messageDocument.isVisible(requestor)) 
					{
						Element touchpointElement = touchpointsElement.addElement("Touchpoint");
						touchpointElement.addAttribute("id", Long.valueOf(messageDocument.getId()).toString());
						Element touchpointNameElement = touchpointElement.addElement("Name");
						touchpointNameElement.addText(messageDocument.getName());
					}
				}
			}
		}
	}

	/**
	 * Build the "Metadata" element
     */
	private void buildMetaDataElm(Element parentElm, User requestor, int auditReportTypeId){
		// Build the "Metadata" element
		Element metadataElm = DocumentHelper.createElement("Metadata");
		parentElm.add(metadataElm);
		// - Build the "User" element
		metadataElm.add(DocumentHelper.createElement("User").addText(requestor.getName()));
		// - Build the "RequestDate" element
		metadataElm.add(DocumentHelper.createElement("RequestDate").addText(DateUtil.formatDateForXMLOutput(DateUtil.now())));

		Element intanceNameElement = metadataElm.addElement("InstanceName");
		intanceNameElement.addText(Node.getCurrentNodeName());

		Element systemDefaultLocaleElement = metadataElm.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		String auditReportTypeStr = auditReportTypeId==AuditReportType.ID_SMART_TEXT_AUDIT_REPORT?"Smart Text Audit Report":"Smart Canvas Audit Report";
		metadataElm.add(DocumentHelper.createElement("ReportType").addText(auditReportTypeStr));
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, ContentObjectData instance, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			Element descriptElm = verHisElm.addElement("Descript");
			if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
				User user = User.findById(actionHis.getUserId());
				if (user != null)
					descriptElm.addAttribute("user", user.getName());
				else
					descriptElm.addAttribute("user", "Uknown");
			}else{	// Auto approve user
				descriptElm.addAttribute("user", "System");
			}
			if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation
				descriptElm.addAttribute("action", "Activated");
			}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
				descriptElm.addAttribute("action", "Release for Approval");
			}else{
				if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
					descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}else{	// Auto approve user
					descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}
			}
			if(actionHis.getAssignedTo() != null){
				User user = User.findById(actionHis.getAssignedTo());
				if (user != null)
					descriptElm.addAttribute("assignedTo", user.getName());
			}
			if(actionHis.getNotes() != null){
				descriptElm.addAttribute("notes", actionHis.getNotes());
			}
			descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
			switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
						if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
							descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
						}else{	// All of
							if(!wfAction.isActionApproved()){
								descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
							}else{
								if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
									descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
								}else{							
									// If action is approved but the user is not the last to approve, stay in current state
									List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
									Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                        public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                            Date approvedDate1 = o1.getApprovedDate();
                                            Date approvedDate2 = o2.getApprovedDate();
                                            if (approvedDate1 == null && approvedDate2 == null) {
                                                return 0;
                                            } else if (approvedDate1 == null) {
                                                return 1;
                                            } else if (approvedDate2 == null) {
                                                return -1;
                                            } else {
                                                return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                            }
                                        }
                                    });
									if(!approvalDetailsSorted.isEmpty()){	// At least one approver
										for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
											ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
											if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
												continue;
											}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												break;
											}else{
												if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
													descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
												}else{
													descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												}
												break;
											}
										}
									}
								}
							}
						}	
					}
				}
			}
		}	
	}
	
	
	public static ServiceExecutionContext createContext(
	        List<Long> list,
			User requestor,
			int auditReportTypeId)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportSmartTextToXMLServiceRequest request = new ExportSmartTextToXMLServiceRequest();
		request.setList(list);
		request.setRequestor(requestor);
		request.setAuditReportTypeId(auditReportTypeId);
		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	
	private void createSmartTextTag(ContentObject contentObject, Element smartTextElement)
	{
		smartTextElement.addAttribute("id", Long.toString(contentObject.getId()));
		smartTextElement.addAttribute("guid", contentObject.getGuid());
		smartTextElement.addAttribute("dna", contentObject.getDna());
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null) 
		{
			smartTextElement.addAttribute("externalid", tmd.getExternalId().toString());
		}
		
		String type = "";
		if (contentObject.isDynamicVariantEnabled())
		{
			type = "Dynamic";
		}
		else if (!contentObject.isVariableContentEnabled())
		{
			type = "Global";
		}
		else
		{
			type = "Regular";
		}

		smartTextElement.addAttribute("type", type);

		String contentType = "Text";
		smartTextElement.addAttribute("contenttype", contentType);
		boolean isFreeform = contentObject.getContentTypeId() == AuditReportType.ID_LOCAL_SMART_CANVAS_AUDIT_REPORT;
		smartTextElement.addAttribute("isfreeform", Boolean.toString(isFreeform));
		smartTextElement.addAttribute("nextaction", contentObject.getActionRequired());
		if(contentObject.isFocusOnActiveData()){
			smartTextElement.addAttribute("assignedto", "N/A");
		}else{
			smartTextElement.addAttribute("assignedto", contentObject.getAssignedToUserName());
		}

		smartTextElement.addAttribute("defaultlanguage", contentObject.getDefaultContentObjectLanguageAsLocale().getLanguageCode());
		smartTextElement.addAttribute("defaultlocale", contentObject.getDefaultContentObjectLanguageAsLocale().getCode());

		ContentObjectData wip = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
		ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
		if(wip != null)
			createSTVersionTag(wip, smartTextElement);
		if(activeCopy != null)
			createSTVersionTag(activeCopy, smartTextElement);
	}
	
	private void createSTVersionTag(ContentObjectData instance, Element smartTextElement)
	{
		Element versionElement = smartTextElement.addElement("Version");

		versionElement.addAttribute("id", Long.toString(instance.getId()));
		versionElement.addAttribute("guid", instance.getGuid());
		versionElement.addAttribute("status", instance.getStatus().getLocaledString());

		String versionOrigin = instance.getCreationReason()!=null?instance.getCreationReason().getLocaledString():"";
		versionElement.addAttribute("origin", versionOrigin);
		versionElement.addAttribute("deliverytypeid", Long.toString(instance.getDeliveryType()));
		versionElement.addAttribute("usagetypeid", Long.toString(instance.getContentObject().getUsageTypeId()));
		
		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(instance.getModel().getName());

		Element metatagsElement = versionElement.addElement("Metatags");
		if (instance.getMetatags() != null && !instance.getMetatags().equalsIgnoreCase(""))
		{
			metatagsElement.addText(instance.getMetatags());
		}
		
		Element descriptionElement = versionElement.addElement("Description");
		if (instance.getDescription() != null && !instance.getDescription().equalsIgnoreCase(""))
		{
			descriptionElement.addText(instance.getDescription());
		}
		
		Element commentsElement = versionElement.addElement("Comments");
        List<ContentObjectComment> comments = new ArrayList<>(instance.getComments());
		Collections.sort(comments, new CommentIdComparator());		
		for (ContentObjectComment comment : comments)
		{
			if (comment != null && comment.getComment() != null && !comment.getComment().equalsIgnoreCase(""))
			{
				Element commentElement = commentsElement.addElement("Comment");
				commentElement.addAttribute("id", Long.toString(comment.getId()));				
				commentElement.addAttribute("created", DateUtil.formatDateForXMLOutput(comment.getCreated()));				
				commentElement.addText(comment.getComment());
			}
		}
		
		instance.getOriginObject();
		if(instance.getStatus().getId() == VersionStatus.VERSION_WIP){
			Element checkoutElement = versionElement.addElement("Checkout");
			checkoutElement.addText(instance.getCreatedByName());
			checkoutElement.addAttribute("date", DateUtil.formatDateForXMLOutput(instance.getStartDate()));
		}
		Element approvalsElement = versionElement.addElement("Approvals");
		List<ConfigurableWorkflowActionHistory> msgActionHisList = ConfigurableWorkflowActionHistory.findAllByModel(ContentObjectData.findByGuid(instance.getGuid()).getModel());
		ConfigurableWorkflowActionHistory.sortByChronOrder(msgActionHisList, true);
		buildApprovalHistElm(approvalsElement, instance, msgActionHisList);
		if (instance.isDynamicVariantEnabled())
		{
			try 
			{
				createContentTagForSelectableSmartText(instance, versionElement);
			} 
			catch (Exception e) 
			{
	            log.error(" unexpected exception when invoking createContentTagForSelectableSmartText(instance, versionElement)", e);
			}
		}
		else
		{
			Element contentsElement = versionElement.addElement("Contents");
			createContentTagForRegularSmartText(instance, contentsElement);
		}
	}

	private void createContentTagForRegularSmartText(ContentObjectData instance, Element contentsElement)
	{
        List<MessagepointLocale> languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        String defaultSystemLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();
        
        for (MessagepointLocale locale : languages) 
		{
			String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			List<ContentObjectAssociation> msgCAs = instance.getContentObjectAssociations();
			boolean missingFlag = true;
			for( ContentObjectAssociation ca : msgCAs )
			{
			    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
			    {
			        continue;
			    }
			    Content languageContent = ca.getContent();
				Element contentElement = null;
			    missingFlag = false;
		        contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				if (localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        {
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }
		        else if (languageContent != null)
		        {
		        	contentElement.addCDATA(languageContent.getContent());
		        }
		    }
			if (missingFlag)
			{
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
		        if (!localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				else
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			}
		}
	}
    
	private void createContentTagForSelectableSmartText(ContentObjectData contentObjectData, Element versionElement)
	throws Exception {
		Element selectableContentElement = versionElement.addElement("Selections");		
		if (contentObjectData.getParameterGroup() != null) {
			selectableContentElement.addAttribute("selectorrefid", Long.toString(contentObjectData.getParameterGroup().getId()));
		}

		// Default Selection
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
		defaultSelectionContentsElement.addAttribute("contenttype", "Text");			

		createContentTagForRegularSmartText(contentObjectData, defaultSelectionContentsElement);

		Set<Long> explored = new HashSet<>();
		List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObjectData.getModel(), contentObjectData.getDataType());

		for (ParameterGroupTreeNode pgtn : topLevelTreeNodes) {
			if ( explored.contains(pgtn.getId()) )
				continue;
			explored.add(pgtn.getId());

			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObjectData.getModel(), contentObjectData.getDataType(), pgtn);
			if (cas != null && !cas.isEmpty()) {
				createSmartTextSelectionTag(contentObjectData, cas, pgtn, defaultSelectionElement);
			}
		}
	}

	private void createSmartTextSelectionTag(ContentObjectData contentObjectData, List<ContentObjectAssociation> ecContentAssociations, ParameterGroupTreeNode parameterGroupTreeNode, Element element)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());

		if (touchpointSelection != null)
		{
			selectionElement.addAttribute("dna", touchpointSelection.getDna());
		}

		if(parameterGroupTreeNode != null) {
			selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
		}

		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}		
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		if(parameterGroupTreeNode.getParameterGroupInstanceCollection() != null){
//			List<String> pgiPathValues = new ArrayList<String>();
//			String returnValues = "";
//			try {
//				returnValues = createValue(returnValues, parameterGroupTreeNode.getParameterGroupInstanceCollection().getId(), pgiPathValues, 1);
//			} catch (Exception e) {
//				returnValues = "";
//			}
//			Element selectionDataValueElement = selectionElement.addElement("DataValue");
//			selectionDataValueElement.addText(returnValues);
			//ExportUtil.createDataValueTag(parameterGroupTreeNode.getParameterGroupInstanceCollection(), selectionElement);
			//if(pgInsColl != null){
				Set<ParameterGroupInstance> pgInstanceSet = parameterGroupTreeNode.getParameterGroupInstanceCollection().getParameterGroupInstances();
				Iterator<ParameterGroupInstance> iterator = pgInstanceSet.iterator();
				while(iterator.hasNext()){
					ParameterGroupInstance pgInstance = iterator.next();
					Element dataValueElm = selectionElement.addElement("DataValue");
					String value = getDataValue(pgInstance);
					dataValueElm.addAttribute("id", Long.toString(pgInstance.getId()));
					dataValueElm.addText(value);
				}
			//}
		}

		Element selectionContentsElement = selectionElement.addElement("Contents");	
		
		createContentTagForSmartTextSelection(contentObjectData, ecContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this);

		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes)
			{
				List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObjectData.getModel(), contentObjectData.getDataType(), childNode);

				createSmartTextSelectionTag(contentObjectData, cas, childNode, selectionElement);
			}
		}
	}

	/**
	 * Return the data value text
     */
	private String getDataValue(ParameterGroupInstance pgInstance){
		String dataValue = null;
		if(pgInstance.getPgItemValue1()!=null){
			dataValue = pgInstance.getPgItemValue1();
		}
		if(pgInstance.getPgItemValue2()!=null){
			dataValue += "|"+pgInstance.getPgItemValue2();
		}
		if(pgInstance.getPgItemValue3()!=null){
			dataValue += "|"+pgInstance.getPgItemValue3();
		}
		if(pgInstance.getPgItemValue4()!=null){
			dataValue += "|"+pgInstance.getPgItemValue4();
		}
		if(pgInstance.getPgItemValue5()!=null){
			dataValue += "|"+pgInstance.getPgItemValue5();
		}
		if(pgInstance.getPgItemValue6()!=null){
			dataValue += "|"+pgInstance.getPgItemValue6();
		}
		return dataValue;
	}

	private static String createValue(String returnValue, long pgiCollectionId, List<String> pgiPathValues, int level)throws Exception{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		StringBuilder valueString = new StringBuilder();

		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {

                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					childIthItemValues.add(createValue(returnValue, pgiCollectionId, pathValues, level + 1));
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if (valueString.length() > 0) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				//valueCell.setCellValue(valueString);

				//pgiPathValues.add(valueString);
			}
			else{
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
			}
		}
		returnValue = returnValue.concat(valueString.toString());
		return returnValue;
	}

	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
	public static void createContentTagForSmartTextSelection(ContentObjectData contentObjectData, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportSmartTextToXMLService edtXMLs)
	{
	    boolean selectableMessage = false;
	    if (contentObjectData.getContentObject().isDynamicVariantEnabled())
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
        {
            String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                
    	    	contentsElement.addAttribute("contenttype", "Text");
                if ( content == null) 
                    sameAsParent = true;
                
				missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
                	languageContent = content.getContent();
		        	contentElement.addCDATA(languageContent);
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
}
