package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportVariantMetadataToExcelServiceServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -1553522279771063788L;

	private String 		exportId;
    private long 		imageId;
    private boolean		includeImagePathOnly;
    private User 		requestor;
    
    public ExportVariantMetadataToExcelServiceServiceRequest(String exportId, long imageId, boolean includeImagePathOnly, User user )
    {
    	this.exportId = exportId;
        this.imageId = imageId;
        this.includeImagePathOnly = includeImagePathOnly;
        this.requestor = user;
    }

	public boolean isIncludeImagePathOnly() {
		return includeImagePathOnly;
	}

	public String getExportId() {
		return exportId;
	}

	public long getImageId() {
        return imageId;
    }
    
    public User getRequestor() {
        return requestor;
    }   
}
