package com.prinova.messagepoint.platform.services.export;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.tree.DefaultElement;
import org.springframework.orm.hibernate5.SessionHolder;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class ExportMessageToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportMessageToXMLService";
	private static final ThreadLocal<SessionHolder> mainSessionHolder = ThreadLocal.withInitial(() -> null);
	
	private User requestor;
	private final Set<Document> documents = new HashSet<>();
	private final Map<Document, List<ContentObject>> tpSelectableMessages = new HashMap<>();
	private final Map<Long, List<ContentObjectAssociation>> pgTreeNodeCoaList = new HashMap<>();
	private final Set<ParameterGroupInstanceCollection> pgiCollections = new HashSet<>();
	private TouchpointSelection tpSelection;
	private boolean userHasMessageViewPermission = false;
	private boolean includeTargeting = false;
	private boolean includeContent = false;
	private boolean includeAllMessages = false;
	private int auditReportTypeId  = 1;
	private long systemDefaultLocaleId;

	private boolean isDateFilterEnabled;
	private Date startDate;
	private List<ContentObject> messageList;

	private final Object messageListMonitor = new Object();

	private ThreadLocal<Map<ContentObject, List<ContentObjectData>>> dbMessageInstanceVersionMap = ThreadLocal.withInitial(this::getDbMessageInstanceVersionMap);
	private ArrayList<Long> imageRefIds = new ArrayList<>();

	private Map<ContentObject, List<ContentObjectData>> getDbMessageInstanceVersionMap() {
		Map<ContentObject, List<ContentObjectData>> dbMessageInstanceVersionMap = new HashMap<>();

		synchronized (messageListMonitor) {
			// Loading the MessageInstances from database because this Service is
			// run within a new Thread, so the hibernate session is disconnected.
			List<ContentObject> messagelist = messageList;
			Collections.sort(messagelist, new ContentObjectNameComparator());

			imageRefIds = new ArrayList<>();

			for (ContentObject contentObject : messagelist)
			{
				List<ContentObjectData> dbMessageInstancelist = new ArrayList<>();
				ContentObject dbMessageInstance = ContentObject.findById(contentObject.getId());

				// if the date filter is not enabled, then both current wip and
				// current production should be included
				if (!isDateFilterEnabled)
				{
					ContentObjectData wip = dbMessageInstance.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
					ContentObjectData activeCopy = dbMessageInstance.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
					if (activeCopy != null)
					{
						dbMessageInstancelist.add(activeCopy);
					}
					if (wip != null)
					{
						dbMessageInstancelist.add(wip);
					}
					if (!dbMessageInstancelist.isEmpty())
					{
						dbMessageInstanceVersionMap.put(dbMessageInstancelist.iterator().next().getContentObject(), dbMessageInstancelist);
					}
				}
				else
				{
					// if filter is enabled, qualify the active copy first. If the
					// active copy is qualified, then the working copy is qualifies
					// automaticly
					ContentObjectData activeCopy = dbMessageInstance.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
					if (activeCopy != null)
					{
						Date activatedDate = activeCopy.getActivatedDate();
						if(activatedDate != null && activatedDate.after(startDate)) {
							dbMessageInstancelist.add(activeCopy);
						}
					}
					if (!dbMessageInstancelist.isEmpty())
					{
						ContentObjectData wip = dbMessageInstance.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
						if (wip != null)
						{
							dbMessageInstancelist.add(wip);
						}
						if (!dbMessageInstancelist.isEmpty())
						{
							dbMessageInstanceVersionMap.put(dbMessageInstancelist.iterator().next().getContentObject(), dbMessageInstancelist);
						}
					}
				}

				// Check the image library reference
				for(ContentObjectData contentObjectData : dbMessageInstancelist){
					List<Long> miImageIds = contentObjectData.findAllImageReference();
					miImageIds.removeAll(imageRefIds);
					imageRefIds.addAll(miImageIds);
				}
			}
		}



		return dbMessageInstanceVersionMap;
	}

	// This is used by GenerateMessageAuditReportService
	//
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportMessageToXMLServiceRequest request = (ExportMessageToXMLServiceRequest) context.getRequest();

			isDateFilterEnabled = request.isDateFilterEnabled();
			startDate = request.getStartDate();
			messageList = request.getMessageslist();

			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			systemDefaultLocaleId = MessagepointLocale.getDefaultSystemLanguageLocale().getId();

			generateXML(request, document);

			String filePath = ExportUtil.saveXMLToFile(document, requestor, auditReportTypeId, 0);

			((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);

		}
		catch (Exception e)
		{
			LogUtil.getLog(ExportMessageToXMLService.class).error(" unexpected exception when invoking ExportMessageToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	private org.dom4j.Document initializeWriter( )
	throws Exception 
	{
		return DocumentHelper.createDocument();
	}

	private void generateXML(
	        ExportMessageToXMLServiceRequest request,
	        org.dom4j.Document document)
	throws Exception 
	{
		requestor = User.findById(request.getRequestor().getId());
		documents.clear();
		tpSelectableMessages.clear();
		pgiCollections.clear();
		tpSelection = request.getTpSelection();

		if (tpSelection != null) 
		{
			Document dbDocument = Document.findById(tpSelection.getDocument().getId());
			documents.add(dbDocument);
			tpSelectableMessages.put(dbDocument, new ArrayList<>());
		}
		includeContent = request.isIncludeContent();
		includeAllMessages = request.isIncludeAllMessages();
		includeTargeting = request.isIncludeTargeting();
		auditReportTypeId = request.getAuditReportTypeId();
		if (requestor != null && requestor.isPermitted(Permission.ID_ROLE_MESSAGE_VIEW_ALL))
		{
			userHasMessageViewPermission = true;
		}
		else
		{
			userHasMessageViewPermission = false;
		}
		


		Element messageExportElement = document.addElement("MessageExport");
		messageExportElement.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		messageExportElement.addAttribute("type", "audit");
		createMetaDataTag(request, messageExportElement);

		List<ContentObject> dbMessageInstanceVersionMapKeys = new ArrayList<>();
		if (!dbMessageInstanceVersionMap.get().isEmpty())
		{
			dbMessageInstanceVersionMapKeys.addAll(dbMessageInstanceVersionMap.get().keySet());
			Collections.sort(dbMessageInstanceVersionMapKeys, new ContentObjectNameComparator());
		}

		for (ContentObject contentObject : dbMessageInstanceVersionMapKeys)
		{
			Zone zone = contentObject.getZone();
			if(zone != null && !contentObject.getIsTouchpointLocal()){
				Document messageDocument = zone.getDocument();
				if (!documents.contains(messageDocument)) 
				{
					documents.add(messageDocument);
					tpSelectableMessages.put(messageDocument, new ArrayList<>());
				}
				if ( contentObject.isStructuredContentEnabled() ||
						contentObject.isVariantType() )
				{
					tpSelectableMessages.get(messageDocument).add(contentObject);
					pgiCollections.addAll(messageDocument.getParameterGroupInstanceCollections());
				}
				else if (contentObject.isDynamicVariantEnabled())
				{
					pgiCollections.addAll(contentObject.getParameterGroupInstanceCollections());
				}
			}
			
			// Local Content
			if(contentObject.getIsTouchpointLocal()){
				Document messageDocument = contentObject.getDocument();
				if (!documents.contains(messageDocument)) {
					documents.add(messageDocument);
					tpSelectableMessages.put(messageDocument, new ArrayList<>());
				}
				if ( contentObject.isStructuredContentEnabled() || contentObject.isVariantType() ) {
					tpSelectableMessages.get(messageDocument).add(contentObject);
					pgiCollections.addAll(messageDocument.getParameterGroupInstanceCollections());
				}else if (contentObject.isDynamicVariantEnabled()){
					pgiCollections.addAll(contentObject.getParameterGroupInstanceCollections());
				}				
			}
		}

		Element referenceDataElement = messageExportElement.addElement("ReferenceData");

		if (!pgiCollections.isEmpty())
		{
			List<ParameterGroupInstanceCollection> pgiCollectionList = new ArrayList<>(pgiCollections);
			pgiCollectionList.sort(new IdentifiableMessagepointModelIdComparator());
			Element dataValuesElement = referenceDataElement.addElement("DataValues");
			for (ParameterGroupInstanceCollection pgiCollection : pgiCollectionList)
			{
				ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
			}
		}

		if (!documents.isEmpty())
		{
			Element touchpointsElement = referenceDataElement.addElement("Touchpoints");
			for (Document messageDocument : documents)
			{
				if (messageDocument.isVisible(requestor))
				{
					createTouchpointTag(messageDocument, touchpointsElement, dbMessageInstanceVersionMapKeys);
				}
			}
		}

		if ( includeContent && !messageList.isEmpty() )
		{
            Element imageLibrariesElement = referenceDataElement.addElement("ImageLibraries");
            createImageLibraryTag(imageRefIds, imageLibrariesElement);
		}
		
		if ( !dbMessageInstanceVersionMapKeys.isEmpty())
		{
			Element messagesElement = messageExportElement.addElement("Messages");
			
			if(auditReportTypeId > 1){
				String messageType = (auditReportTypeId == AuditReportType.ID_LOCAL_IMAGE_AUDIT_REPORT)?"image":"text";
				boolean isFreeform = auditReportTypeId == AuditReportType.ID_LOCAL_SMART_CANVAS_AUDIT_REPORT;
				messagesElement.addAttribute("localtype", messageType);
				messagesElement.addAttribute("isfreeform", Boolean.toString(isFreeform));
			}
			final String schema = Node.getCurrentNode().getSchemaName();

			ExecutorService execService = getExecutorService("message");

			List<CompletableFuture<Element>> messageElements = dbMessageInstanceVersionMapKeys.stream()
					.map(msgInstance -> CompletableFuture.supplyAsync(() -> getValueFromThreadWithSession(schema, () -> createMessageTag(msgInstance, dbMessageInstanceVersionMap.get().get(msgInstance), tpSelection)), execService))
					.toList();

			for (Element messageElement : messageElements.stream().map(CompletableFuture::join).toList()) {
				messagesElement.add(messageElement);
			}

			execService.shutdown();

			buildMessagePriorityElm(messageExportElement, dbMessageInstanceVersionMapKeys, tpSelection);
		}
	}

	private void createMetaDataTag(
	        ExportMessageToXMLServiceRequest request,
	        Element messageExportElement)
	throws Exception 
	{
		TouchpointSelection selection = request.getTpSelection();
		Element metadataElement = messageExportElement.addElement("Metadata");
		Element userElement = metadataElement.addElement("User");
		if (requestor != null)
			userElement.addText(requestor.getFullName());
		else
			userElement.addText("System");
		Element requestDateElement = metadataElement.addElement("RequestDate");
		requestDateElement.addText(DateUtil.formatDateForXMLOutput(DateUtil.now()));
		Element intanceNameElement = metadataElement.addElement("InstanceName");
		intanceNameElement.addText(Node.getCurrentNodeName());

		Element systemDefaultLocaleElement = metadataElement.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		if (this.documents.size() == 1) {
			Document doc = this.documents.iterator().next();
			Element tpDefaultLocaleElement = metadataElement.addElement("TouchpointDefaultLocale");
			tpDefaultLocaleElement.addCDATA(doc.getDefaultTouchpointLanguageLocale().getName());
			tpDefaultLocaleElement.addAttribute("code", doc.getDefaultTouchpointLanguageLocale().getCode());
		}

		Element settingsElement = metadataElement.addElement("Settings");
		settingsElement.addAttribute("content", Boolean.valueOf(includeContent).toString());
		settingsElement.addAttribute("targeting", Boolean.valueOf(includeTargeting).toString());
		settingsElement.addAttribute("includeAllMessages", Boolean.valueOf(includeAllMessages).toString());

		if (request.isDateFilterEnabled())
		{
			settingsElement.addAttribute("from", DateUtil.formatDateForXMLOutput(request.getStartDate()));
		}
		
		if (selection != null)
		{
			Element seldctionElement = metadataElement.addElement("Selection");
			seldctionElement.addAttribute("refid", Long.valueOf(selection.getId()).toString());
			if (request.getSelectionStatus() > 0) 
			{
				String selectionStatus = "";
				if (request.getSelectionStatus() == TouchpointContentObjectListController.VIEW_ACTIVE)
				{
					selectionStatus = "Active";
				}
				else
				{
					selectionStatus = "Working Copy";
				}
				seldctionElement.addAttribute("status", selectionStatus);
			}
		}
	}

	private void createImageLibraryTag(List<Long> imageIds, Element imageLibrariesElement) throws Exception{

		imageIds.sort(null);

		for(Long imageId : imageIds){
			ContentObject image = ContentObject.findById(imageId);
			if (image != null) {
				Element cliElement = imageLibrariesElement.addElement("ImageLibrary");
			    cliElement.addAttribute("id", Long.valueOf(image.getId()).toString());
				cliElement.addAttribute("guid", image.getGuid());
				cliElement.addAttribute("dna", image.getDna());

				if(image.isDynamicVariantEnabled()){
			    	cliElement.addAttribute("type", "Dynamic");
			    }else{
			    	cliElement.addAttribute("type", "Regular");
			    }

				cliElement.addAttribute("islocal", image.getIsTouchpointLocal()?"true":"false");
				cliElement.addAttribute("defaultlanguage", image.getDefaultContentObjectLanguageAsLocale().getLanguageCode());
				cliElement.addAttribute("defaultlocale", image.getDefaultContentObjectLanguageAsLocale().getCode());

			    Element cliNameElement = cliElement.addElement("Name");
			    cliNameElement.addText(image.getName());
			    
			    if(image.isDynamicVariantEnabled()){
			    	this.createContentTagForSelectableImage(image, cliElement);
			    }else{
			    	this.createContentTagForRegularImage(image, cliElement);
			    }
		    }
		}
	}
	
	private void createContentTagForSelectableImage(ContentObject image, Element versionElement) throws Exception {
		Element selectableContentElement = versionElement.addElement("SelectableContent");
		createSelectorTag(image.getParameterGroup(), selectableContentElement);
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");

		createContentTagForRegularImage(image, defaultSelectionContentsElement);

		List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(image, image.getFocusOnDataType());
		Set<Long> explored = new HashSet<>();
		for (ParameterGroupTreeNode pgtn : topLevelTreeNodes) {
		    if (pgtn == null || explored.contains(pgtn.getId())) {
		        continue;
		    }
		    explored.add(pgtn.getId());
			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(image, image.getFocusOnDataType(), pgtn);
			if (cas != null && !cas.isEmpty()) {
				createSelectionTagForImage(image, cas, defaultSelectionElement, pgtn);
			}
		}
	}
	
	private void createSelectionTagForImage(ContentObject image, List<ContentObjectAssociation> clContentAssociations,
											Element element, ParameterGroupTreeNode parameterGroupTreeNode){

		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) {
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());

		if (touchpointSelection != null)
		{
			selectionElement.addAttribute("dna", touchpointSelection.getDna());
		}

		if(parameterGroupTreeNode != null) {
			selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
		}

		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null){
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		if(parameterGroupTreeNode.getParameterGroupInstanceCollection() != null){
			List<ParameterGroupInstance> pgInstanceList = new ArrayList<>(parameterGroupTreeNode.getParameterGroupInstanceCollection().getParameterGroupInstances());
			pgInstanceList.sort(new IdentifiableMessagepointModelIdComparator());
			for(ParameterGroupInstance pgInstance : pgInstanceList){
				Element dataValueElm = selectionElement.addElement("DataValue");
				String value = getDataValue(pgInstance);
				dataValueElm.addAttribute("id", Long.toString(pgInstance.getId()));
				dataValueElm.addText(value);
			}
		}

		if (includeContent){
			Element selectionContentsElement = selectionElement.addElement("Contents");
			createContentTagForSelectionForCL(image, clContentAssociations, parameterGroupTreeNode, selectionContentsElement);
		}
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty()) {
			for (ParameterGroupTreeNode childNode : ChildNodes) {
				List<ContentObjectAssociation> childContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(image, ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, childNode, null);
				createSelectionTagForImage(image, childContentAssociationList, selectionElement, childNode);
			}
		}
	}
	
	private void createContentTagForRegularImage(ContentObject cli, Element contentsElement) throws Exception {
		List<MessagepointLocale> languages = cli.getContentObjectLanguagesAsLocales();
		String defaultLocaleCode = cli.getDefaultContentObjectLanguageAsLocale().getCode();

	    for (MessagepointLocale locale : languages){
			String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			Set<ContentObjectAssociation> clCAs = cli.getContentObjectAssociations();
			boolean missingFlag = true;
			for( ContentObjectAssociation ca : clCAs ){
			    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() ) {
			        continue;
			    }
			    Content languageContent = ca.getContent();
			    
			    Element contentElement = null;

			    missingFlag = false;
		        contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				contentElement.addAttribute("coaid", String.valueOf(ca.getId()));
				contentElement.addAttribute("versiondatatype", String.valueOf(ca.getDataType()));
				if (localeCode.equalsIgnoreCase(defaultLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultLocaleCode)){
                    contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }else if (languageContent != null){
	                contentElement.addCDATA(languageContent.getImageName());
                    if ( languageContent.getAppliedImageFilename() != null && !languageContent.getAppliedImageFilename().isEmpty())
                        contentElement.addAttribute("imagename", languageContent.getAppliedImageFilename());
                    if ( languageContent.getImageUploadedDate() != null )
                        contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(languageContent.getImageUploadedDate()) );
                    else
                        contentElement.addAttribute("uploaded", "unknown");			        	
		        }
			}
			if (missingFlag){
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
		        if (!localeCode.equalsIgnoreCase(defaultLocaleCode)){
                    contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }else{
		        	contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        }
			}				    
		}
	}
	
	private void createContentTagForSelectionForCL(ContentObject cli, List<ContentObjectAssociation> clContentAssociations,
	        ParameterGroupTreeNode selection, Element contentsElement) {
		List<MessagepointLocale> languages = cli.getContentObjectLanguagesAsLocales();
		String defaultTouchpointLocaleCode = cli.getDefaultContentObjectLanguageAsLocale().getCode();

	    boolean selectableCL = false;
	    if (cli.isDynamicVariantEnabled()) {
	        selectableCL = true;
	    }

	    for (MessagepointLocale locale : languages){
            String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : clContentAssociations ){
                if ( ca.getPGTreeNode() != null && !ca.getPGTreeNode().equals(selection)){
                    continue;
                }

                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() ) {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langImageUploaded = "";
				ComplexValue imageLink = null;

                if ( content != null) {
					imageLink = content.getImageLink();
                    languageContent = content.getImageName();
                    if ( content.getAppliedImageFilename() != null )
                        langImageName = content.getAppliedImageFilename();
                    if ( content.getImageUploadedDate() != null )
                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
                    else
                        langImageUploaded = "unknown";
                } else if (ca.getReferencingImageLibrary() == null)
				{
                    sameAsParent = true;
                }
			    
                missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				contentElement.addAttribute("coaid", String.valueOf(ca.getId()));
				contentElement.addAttribute("versiondatatype", String.valueOf(ca.getDataType()));

                if (sameAsParent && selectableCL) {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } else {
					if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());

					if (ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
					{
						contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					}
					else if (ca.getReferencingImageLibrary() != null)
					{
						contentElement.addAttribute("imagelibraryrefid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
						contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
					}
					else {
						if (!langImageName.isEmpty())
							contentElement.addAttribute("imagename", langImageName);
						if (!langImageUploaded.isEmpty())
							contentElement.addAttribute("uploaded", langImageUploaded);

						contentElement.addCDATA(languageContent);

						if (imageLink != null)
						{
							Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
							imageLinkElement.addAttribute("language", languageCode);
							contentElement.addAttribute("locale", localeCode);
							imageLinkElement.addAttribute("guid", imageLink.getGuid());
							imageLinkElement.addCDATA(imageLink.getEncodedValue());
						}
					}
                }
            }
			if (missingFlag){
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
                if (selectableCL) {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }else{
					if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
						contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					else
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
                }
			}
	    }
	}	
	
	private void createTouchpointTag(
	        Document messageDocument,
	        Element touchpointsElement,
			List<ContentObject> dbMessageInstanceVersionMapKeys) {
		Element touchpointElement = touchpointsElement.addElement("Touchpoint");
		touchpointElement.addAttribute("id", Long.toString(messageDocument.getId()));
		Element touchpointNameElement = touchpointElement.addElement("Name");
		touchpointNameElement.addText(messageDocument.getName());
		
		ExportXMLUtils.createLanguages(messageDocument, touchpointElement);
		
		List<DocumentSection> documentSections = messageDocument.getDocumentSectionsByOrder();

		if (auditReportTypeId == AuditReportType.ID_MESSAGE_AUDIT_REPORT && documentSections != null && !documentSections.isEmpty())
		{
			Element sectionsElement = touchpointElement.addElement("Sections");
			for (DocumentSection documentSection : documentSections)
			{
				Element sectionElement = sectionsElement.addElement("Section");
				Element sectionNameElement = sectionElement.addElement("Name");
				sectionNameElement.addText(documentSection.getName());
				List<Zone> sectionZones = documentSection.getZonesList();

				if ( sectionZones == null || sectionZones.isEmpty() )
				{
				    continue;
				}
				
				Element zonesElement = sectionElement.addElement("Zones");
				for (Zone zone : documentSection.getZonesList()) 
				{
				    if (!zone.isVisible(requestor))
				    {
				        continue;
				    }
				    Element zoneElement = zonesElement.addElement("Zone");
				    zoneElement.addAttribute("id", Long.valueOf(zone.getId()).toString());
				    ContentType zoneContentType = zone.getContentType();
				    zoneElement.addAttribute("type", zoneContentType.getLocaledString());

				    if ( zoneContentType.getId() == ContentType.GRAPHIC ||
 			             zoneContentType.getId() == ContentType.TEXT_OR_GRAPHIC) 
				    {
				        zoneElement.addAttribute("graphictype", zone.getGraphicType());
				    }

				    Element zoneNameElement = zoneElement.addElement("Name");
				    zoneNameElement.addText(zone.getFriendlyName());
				    if ( !zone.isMultipart() || zone.getParts() == null || zone.getParts().isEmpty() )
				    {
				        continue;
				    }

				    Element partsElement = zoneElement.addElement("Parts");
				    partsElement.addAttribute("count", Integer.valueOf(zone.getParts().size()).toString());
				    Integer partNum = 1;
				    for (ZonePart zonePart : zone.getPartsInSequenceOrder())
				    {
				        Element partElement = partsElement.addElement("Part");
				        partElement.addAttribute("number", (partNum++).toString());
				        ContentType zonePartContentType = zonePart.getContentType();
				        partElement.addAttribute("type", zonePartContentType.getLocaledString());

				        if ( zonePartContentType.getId() == ContentType.GRAPHIC ||
				             zonePartContentType.getId() == ContentType.TEXT_OR_GRAPHIC) 
				        {
				            partElement.addAttribute("graphictype", zonePart.getGraphicType());
				        }
				        partElement.addText(zonePart.getName());
				    }
				}
			}
		}
		if (tpSelection != null) 
		{
			Element selectionsElement = touchpointElement.addElement("Selections");
			if (messageDocument.isEnabledForVariation()) 
			{
				createSelectorTag(messageDocument.getSelectionParameterGroup(), selectionsElement);
				
				List<TouchpointSelection> tpSelections = new ArrayList<>();
				if (includeAllMessages)
					tpSelections.addAll(tpSelection.getAllChildrenAndGrandchildren());
				else
					tpSelections.addAll(tpSelection.getAncesters());
				tpSelections.add(tpSelection);

				TouchpointSelection masterTouchpointSelection = messageDocument.getMasterTouchpointSelection();

				final String schema = Node.getCurrentNode().getSchemaName();

				final HashMap<ParameterGroupTreeNode, TouchpointSelection> allSelections = mapTpSelectionToPgTree(TouchpointSelection.findAllByDocument(tpSelection.getDocument()));

				ExecutorService execService = getExecutorService("selection");

				tpSelections.sort(new IdentifiableMessagepointModelIdComparator());

				List<CompletableFuture<SelectionIdElement>> selectionElements = tpSelections.stream()
						.map(tpSel -> CompletableFuture.supplyAsync(() -> getValueFromThreadWithSession(schema, () -> {
							Element element = createSelectionTagForTP(tpSel, dbMessageInstanceVersionMapKeys, allSelections);

							SelectionIdElement value = new SelectionIdElement();
							value.selectionId = tpSel.getId();
							value.element = element;

							return value;
						}), execService))
						.toList();

				Map<Long, SelectionIdElement> elements = selectionElements
						.stream()
						.map(CompletableFuture::join)
						.filter(Objects::nonNull)
						.collect(Collectors.toMap(x -> ((SelectionIdElement) x).selectionId, k -> k));

				execService.shutdown();

				SelectionIdElement selectionIdElement = elements.get(masterTouchpointSelection.getId());
				if(selectionIdElement != null) {
					selectionsElement.add(elements.get(masterTouchpointSelection.getId()).element);
					assembleSelectionElementsTag(masterTouchpointSelection, elements.get(masterTouchpointSelection.getId()).element, elements);
				}
				//selectionsElement.add(elements.get(tpSelection.getId()).element);
				//selectionsElement.add(value);
			}
		}
	}

	private void assembleSelectionElementsTag(TouchpointSelection tpSelection, Element selectionsElement, Map<Long, SelectionIdElement> elements) {

		List<TouchpointSelection> children = tpSelection.getChildrenOrderByName();

		for (TouchpointSelection sel : children) {
			if (elements.containsKey(sel.getId())) {
				Element element = elements.get(sel.getId()).element;
				selectionsElement.add(element);
				assembleSelectionElementsTag(sel, element, elements);
			}
		}
	}

	private <TValue extends Object> TValue getValueFromThreadWithSession(String schema, Supplier<TValue> supplier) {

		try {
			SessionHolder session = HibernateUtil.getManager().openTemporarySession(schema);
			mainSessionHolder.set(session);

			return supplier.get();
		} catch (Exception e) {
			LogUtil.getLog(ExportMessageToXMLService.class).error("Error:", e);
		} finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder.get());
		}

		return null;
	}

	private void createSelectorTag(
	        ParameterGroup parameterGroup, 
	        Element element)
	{
		Element selectorElement = element.addElement("Selector");
		Element selectorNameElement = selectorElement.addElement("Name");
		selectorNameElement.addText(parameterGroup.getName());
		Element parametersElement = selectorElement.addElement("Parameters");
		
		for (Parameter parameter : parameterGroup.getParameters()) 
		{
			Element parameterElement = parametersElement.addElement("Parameter");
			parameterElement.addText(parameter.getName());
		}

	}

	private Element createMessageTag(
	        ContentObject contentObject,
	        List<ContentObjectData> releventMsgVersions,
	        TouchpointSelection tpSelection)
	{
		Element messageElement = new DefaultElement("Message");
		messageElement.addAttribute("id", Long.valueOf(contentObject.getId()).toString());
		messageElement.addAttribute("guid", contentObject.getGuid());
		messageElement.addAttribute("dna", contentObject.getDna());

		messageElement.addAttribute("modelhash", contentObject.getHashSafe());
		{
			ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
			if (activeCopy != null) {
				messageElement.addAttribute("activecopyhash", activeCopy.getHashSafe());
			}
		}

		{
			ContentObjectData workingCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
			if (workingCopy != null) {
				messageElement.addAttribute("workingcopyhash", workingCopy.getHashSafe());
			}
		}

		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null && userHasMessageViewPermission) 
		{
			messageElement.addAttribute("externalid", tmd.getExternalId().toString());
		}

		String messageType = "";
		if (contentObject.isStructuredContentEnabled())
		{
			messageType = "Selectable(Touchpoint Content)";
		}
		else if (contentObject.isDynamicVariantEnabled())
		{
			messageType = "Selectable(Message)";
		}
		else if (contentObject.isVariantType())
		{
			messageType = "Selectable(Touchpoint Message)";
		}
		else
		{
			messageType = "Regular";
		}

		messageElement.addAttribute("type", messageType);

		String contentType = contentObject.getContentType().getLocaledString();
		messageElement.addAttribute("contenttype", contentType);

		messageElement.addAttribute("suppressed", contentObject.isSuppressed()?"true":"false");
		messageElement.addAttribute("onhold", contentObject.isOnHold()?"true":"false");
		messageElement.addAttribute("islocal", contentObject.getIsTouchpointLocal()?"true":"false");

		messageElement.addAttribute("defaultlanguage", contentObject.getDefaultContentObjectLanguageAsLocale().getLanguageCode());
		messageElement.addAttribute("defaultlocale", contentObject.getDefaultContentObjectLanguageAsLocale().getCode());

		for (ContentObjectData msgVersion : releventMsgVersions)
		{
			ContentObject message = msgVersion.getContentObject();
			message.setFocusOnDataType(msgVersion.getDataType());

			createVersionTag(msgVersion, includeTargeting, messageElement, tpSelection);
		}

		return messageElement;
	}

	private void createVersionTag(
			ContentObjectData contentObjectData,
	        boolean includeTargeting,
	        Element messageElement,
	        TouchpointSelection tpSelection)
	{
		ContentObject contentObject = contentObjectData.getModel();
		Element versionElement = messageElement.addElement("Version");

		versionElement.addAttribute("status", contentObjectData.getStatus().getLocaledString());

		if (userHasMessageViewPermission) 
		{
			String versionOrigin = contentObjectData.getCreationReason()!=null?contentObjectData.getCreationReason().getLocaledString():"";
			versionElement.addAttribute("origin", versionOrigin);
		}
		// Name
		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObject.getName());
		// Description
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObjectData.getDescription() != null)
		{
			descriptionElement.addText(contentObjectData.getDescription());
		}
		
		if (tpSelection != null && (!contentObject.isVariantType() || contentObject.getOwningTouchpointSelection().getId() != tpSelection.getId()))
		{
			// ContentSelector
			Element contentSelectorElement = versionElement.addElement("ContentSelector");
			if (contentObject.getOwningTouchpointSelection() != null)
				contentSelectorElement.addText(contentObject.getOwningTouchpointSelection().getName());
			else
				contentSelectorElement.addText("Master");
		}
		// Zones
		if ( contentObject.getZone() != null ) {
			Element zonesElement = versionElement.addElement("Zones");
				Element zoneElement = zonesElement.addElement("Zone");
				zoneElement.addText(contentObject.getZone().getFriendlyName());
		}

		if (userHasMessageViewPermission) 
		{
			if (contentObjectData.getDataType() == ContentObject.DATA_TYPE_WORKING)
			{
				Element checkoutElement = versionElement.addElement("Checkout");
				checkoutElement.addText(contentObjectData.getCreatedByName());
				checkoutElement.addAttribute("date", DateUtil.formatDateForXMLOutput(contentObjectData.getCreated()));
			}
	
			Element approvalsElement = versionElement.addElement("Approvals");
			List<ConfigurableWorkflowActionHistory> msgActionHisList = ConfigurableWorkflowActionHistory.findByContentObjectId(contentObjectData.getModel().getId());
			ConfigurableWorkflowActionHistory.sortByChronOrder(msgActionHisList, true);
			buildApprovalHistElm(approvalsElement, msgActionHisList);
		}

		String delivertType = contentObject.getDeliveryTypeText();

		if (contentObjectData.isDynamicVariantEnabled())
		{
			if (contentObject.getZone() != null)
			{
				Element deliveryElement = versionElement.addElement("Delivery");
				if (userHasMessageViewPermission )
				{
					deliveryElement.addAttribute("type", delivertType);	
				}
				deliveryElement.addAttribute("zonerefid", Long.valueOf(contentObject.getZone().getId()).toString());
			}
			if (userHasMessageViewPermission && includeContent) {
				createContentTagForSelectableMessage(contentObjectData, versionElement);
			}
		}
		else
		{
			if (contentObject.getZone() != null)
			{
				Element deliveryElement = versionElement.addElement("Delivery");
				if (userHasMessageViewPermission )
				{
					deliveryElement.addAttribute("type", delivertType);
				}
				deliveryElement.addAttribute("zonerefid", Long.valueOf(contentObject.getZone().getId()).toString());
				if (userHasMessageViewPermission && includeContent)
				{
					if (contentObject.isMultipartType())
					{
						Element contentsElement = versionElement.addElement("Contents");
						createContentTagForRegularMessage(contentObjectData, contentObject.getZone(), contentsElement);
					}
				}
			}
			if (userHasMessageViewPermission && includeContent)
			{
				if (!contentObject.isMultipartType())
				{
					Element contentsElement = versionElement.addElement("Contents");
					createContentTagForRegularMessage(contentObjectData, null, contentsElement);
				}
			}
		}

		if (userHasMessageViewPermission)
		{
			Element timingElement = versionElement.addElement("Timing");
			if (contentObjectData.getStartDate() != null)
			{
				timingElement.addAttribute("startdate", DateUtil.formatDateForXMLOutput(contentObjectData.getStartDate()));
			}
			if (contentObjectData.getEndDate() != null)
			{
				timingElement.addAttribute("enddate", DateUtil.formatDateForXMLOutput(contentObjectData.getEndDate()));
			}
			if ( contentObjectData.getStartDate() != null ||
			     contentObjectData.getEndDate() != null)
			{
				timingElement.addAttribute("repeatsannually", Boolean.valueOf(contentObjectData.isRepeatDatesAnnually()).toString());
			}
	
			if (includeTargeting)
			{
				Element targetCriteriaElement = versionElement.addElement("TargetCriteria");
				if ( contentObjectData.getExcludedTargetGroups() != null &&
                        !contentObjectData.getExcludedTargetGroups().isEmpty())
				{
					Element exclusionsElement = targetCriteriaElement.addElement("Exclusions");
					exclusionsElement.addAttribute("relationship", contentObjectData.getExcludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : contentObjectData.getExcludedTargetGroups())
					{
						Element targetGroupElement = exclusionsElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(contentObjectData));
					}
				}
				if ( contentObjectData.getIncludedTargetGroups() != null &&
                        !contentObjectData.getIncludedTargetGroups().isEmpty())
				{
					Element inclusionsGroupAElement = targetCriteriaElement.addElement("InclusionsGroupA");
					inclusionsGroupAElement.addAttribute("relationship", contentObjectData.getIncludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : contentObjectData.getIncludedTargetGroups())
					{
						Element targetGroupElement = inclusionsGroupAElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(contentObjectData));
					}
				}
				if ( contentObjectData.getExtendedTargetGroups() != null &&
                        !contentObjectData.getExtendedTargetGroups().isEmpty())
				{
					Element inclusionsGroupBElement = targetCriteriaElement.addElement("InclusionsGroupB");
					inclusionsGroupBElement.addAttribute("relationship", contentObjectData.getExtendedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : contentObjectData.getExtendedTargetGroups())
					{
						Element targetGroupElement = inclusionsGroupBElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(contentObjectData));
					}
				}
			}
		}
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			Element descriptElm = verHisElm.addElement("Descript");
			if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
				User user = User.findById(actionHis.getUserId());
				if (user != null)
					descriptElm.addAttribute("user", user.getName());
				else
					descriptElm.addAttribute("user", "Uknown");
			}else{	// Auto approve user
				descriptElm.addAttribute("user", "System");
			}
			if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation
				descriptElm.addAttribute("action", "Activated");
			}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
				descriptElm.addAttribute("action", "Release for Approval");
			}else{
				if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
					descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}else{	// Auto approve user
					descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}
			}
			if(actionHis.getAssignedTo() != null){
				User user = User.findById(actionHis.getAssignedTo());
				if (user != null)
					descriptElm.addAttribute("assignedTo", user.getName());
			}
			if(actionHis.getNotes() != null){
				descriptElm.addAttribute("notes", actionHis.getNotes());
			}
			descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
			switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
						if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
							descriptElm.setText("Pending " + (wfAction.getNextAction()!=null?wfAction.getNextAction().getConfigurableWorkflowStep().getState():wfAction.getConfigurableWorkflowStep().getState()));
						}else{	// All of
							if(!wfAction.isActionApproved()){
								descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
							}else{
								if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
									descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
								}else{							
									// If action is approved but the user is not the last to approve, stay in current state
									List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
									Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                        public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                            Date approvedDate1 = o1.getApprovedDate();
                                            Date approvedDate2 = o2.getApprovedDate();
                                            if (approvedDate1 == null && approvedDate2 == null) {
                                                return 0;
                                            } else if (approvedDate1 == null) {
                                                return 1;
                                            } else if (approvedDate2 == null) {
                                                return -1;
                                            } else {
                                                return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                            }
                                        }
                                    });
									if(!approvalDetailsSorted.isEmpty()){	// At least one approver
										for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
											ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
											if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
												continue;
											}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												break;
											}else{
												if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
													descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
												}else{
													descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												}
												break;
											}
										}
									}
								}
							}
						}	
					}
				}
			}
		}	
	}
	
	private void buildMessagePriorityElm(Element parentElm, List<ContentObject> releventMsgVersions, TouchpointSelection tpSelection){
		// Build the "MessagePriority" element
		Element msgPriElm = parentElm.addElement("MessagePriority");
		Map<Zone, List<ContentObject>> zonesMap = new HashMap<>();
		for(ContentObject contentObject : releventMsgVersions){
			Zone relevantZone = contentObject.getZone();
			if(relevantZone != null) {
				if (!zonesMap.containsKey(relevantZone)) {
					zonesMap.put(relevantZone, new ArrayList<>());
				}
				zonesMap.get(relevantZone).add(contentObject);
			}
		}

		for(Zone zone : zonesMap.keySet()){
			// Build the "Zone" element
			Element zoneElm = msgPriElm.addElement("Zone");
			zoneElm.addAttribute("name", zone.getFriendlyName());
			List<ContentObject> msgsSelected = zonesMap.get(zone);
			List<ContentObjectZonePriority> zoneAssociations = ContentObjectZonePriority.findAllMessagesByZone(zone, tpSelection, ContentObject.DELIVERY_TYPE_ALL);
			for(ContentObjectZonePriority zoneAssociation : zoneAssociations){
				// Build the "Message" element
				ContentObject msg = zoneAssociation.getContentObject();
				ContentObjectData msgInst = msg.getContentObjectData();
				Element msgElm = zoneElm.addElement("ContentObject");

				// It needs to be msg.getId() instead of msgInst.getId() from 20.2.0 version
				msgElm.addAttribute("id", Long.toString(msg.getId()));
				msgElm.addAttribute("name", msg.getName());
				msgElm.addAttribute("status", msgInst.isWorking()?"Working Copy":"Active");
				msgElm.addAttribute("delivery", msg.getDeliveryTypeText());

				int priority = msgInst.getPriorityNumberByZone(zone.getId());
				msgElm.addAttribute("priority", Integer.toString(priority));
				msgElm.addAttribute("suppressed", Boolean.toString(zoneAssociation.isSuppress()));
                msgElm.addAttribute("repeatwithnext", Boolean.toString(zoneAssociation.isRepeatWithNext()));
				msgElm.addAttribute("selected", Boolean.toString(msgsSelected.contains(msg)));
				msgElm.addAttribute("datatype", Long.toString(msgInst.getDataType()));
			}
		}
	}

	private void createContentTagForRegularMessage(
	        ContentObjectData contentObjectData,
	        Zone zone,
	        Element contentsElement)
	{
		ContentObject contentObject = contentObjectData.getModel();
		List<MessagepointLocale> languages = null;
		String defaultTouchpointLocaleCode = null;
		if (!documents.isEmpty())
		{
			for (Document doc : documents)
			{
				languages = doc.getTouchpointLanguagesAsLocales();
				defaultTouchpointLocaleCode = doc.getDefaultTouchpointLanguageLocaleCode();
			}
		}
		if (languages == null)
			languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
		if (defaultTouchpointLocaleCode == null)
			defaultTouchpointLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();
        
		if (contentObject.getContentType().getId() == ContentType.MULTIPART) 
		{
			Integer partNum = 1;
			for (ZonePart zonePart : zone.getPartsInSequenceOrder())
			{
				Element partElement = contentsElement.addElement("Part");
				partElement.addAttribute("num", (partNum++).toString());
				List<ContentObjectAssociation> contentAssociations = contentObjectData.getContentObjectAssociations();
				Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, contentAssociations);
				
				if ( partAssociations == null || partAssociations.isEmpty() )
				{
					continue;
				}			

				boolean zonePartEmpty = false;
    		    for (MessagepointLocale locale : languages)
				{
					String languageCode = locale.getLanguageCode();
					String localeCode = locale.getCode();

					boolean isActive = false;
					boolean missingFlag = true;					
					for( ContentObjectAssociation partCa : partAssociations )
					{
					    if ( partCa.getPGTreeNode() != null )
					        continue;
					    
						if ( partCa.getMessagepointLocale().getId() != locale.getId() )
						{
							continue;
						}

						if (partCa.getTypeId() == ContentAssociationType.ID_EMPTY)
						{
							zonePartEmpty = true;
							partElement.addAttribute("empty", Boolean.TRUE.toString());
							break;
						}

						String languageContent = "";
						Content langPartContent = partCa.getContent();
						String languageImageName = "";
						String langImageUpload = "";

						if ( langPartContent == null ){
							languageContent = null;
						}
						else if ( zonePart.getContentType().getId() == ContentType.TEXT || contentObject.getContentType().getId() == ContentType.SHARED_FREEFORM){
							languageContent = langPartContent.getContent();
						}else{
							languageContent = langPartContent.getImageName();
							languageImageName = langPartContent.getAppliedImageFilename();
							langImageUpload = DateUtil.formatDateForXMLOutput(langPartContent.getImageUploadedDate());
						}

						missingFlag = false;
						Element contentElement = partElement.addElement("Content");
						contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
						contentElement.addAttribute("coaid", String.valueOf(partCa.getId()));
						contentElement.addAttribute("versiondatatype", String.valueOf(partCa.getDataType()));

						if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
						if(partCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)){
							contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
						}else if (partCa.getReferencingImageLibrary() != null) {
							contentElement.addAttribute("imagelibraryrefid", Long.valueOf(partCa.getReferencingImageLibrary().getId()).toString());
							contentElement.addAttribute("imagelibraryrefname", partCa.getReferencingImageLibrary().getName());
						}else if (languageContent != null){
							contentElement.addCDATA(languageContent);

							if ( languageImageName != null && !languageImageName.isEmpty())
								contentElement.addAttribute("imagename", languageImageName);

							if (!langImageUpload.isEmpty())
								contentElement.addAttribute("uploaded", langImageUpload);
						}
					}
					if (missingFlag && !zonePartEmpty)
					{
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
				        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
	                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				        else
				        	contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
					}
				}
			}
		}

		else
		{	// Regular Text or Graphic Message (not multipart)
		    for (MessagepointLocale locale : languages)
			{
				String languageCode = locale.getLanguageCode();
				String localeCode = locale.getCode();
				List<ContentObjectAssociation> msgCAs = contentObjectData.getContentObjectAssociations();
				boolean missingFlag = true;
				for( ContentObjectAssociation ca : msgCAs )
				{
				    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
				    {
				        continue;
				    }
				    Content languageContent = ca.getContent();
				    
				    Element contentElement = null;

				    missingFlag = false;
			        contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
					contentElement.addAttribute("coaid", String.valueOf(ca.getId()));
					contentElement.addAttribute("versiondatatype", String.valueOf(ca.getDataType()));

					if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
			        {
                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					}else if (ca.getReferencingImageLibrary() != null) {
						contentElement.addAttribute("imagelibraryrefid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
						contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
			        }else if (languageContent != null)
			        {
				        if (contentObject.getContentType().getId() == ContentType.TEXT || contentObject.getContentType().getId() == ContentType.SHARED_FREEFORM)
				        {
				            contentElement.addCDATA(languageContent.getContent(ca.getDocument()));
				        }
				        else
				        {
			                contentElement.addCDATA(languageContent.getImageName());
                            if ( languageContent.getAppliedImageFilename() != null && !languageContent.getAppliedImageFilename().isEmpty())
                                contentElement.addAttribute("imagename", languageContent.getAppliedImageFilename());
                            if ( languageContent.getImageUploadedDate() != null )
                                contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(languageContent.getImageUploadedDate()) );
                            else
                                contentElement.addAttribute("uploaded", "unknown");
				        }			        	
			        }
				}
				if (missingFlag)
				{
					Element contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
			        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                        contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
			        else
			        	contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
				}
			}
		}
	}

	private void createContentTagForSelectableMessage(
	        ContentObjectData contentObjectData,
	        Element versionElement)
	{
		ContentObject contentObject = contentObjectData.getModel();
		Element selectableContentElement = versionElement.addElement("SelectableContent");
		createSelectorTag(contentObject.getParameterGroup(), selectableContentElement);
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");

		createContentTagForRegularMessage(contentObjectData, contentObject.getZone() != null ? contentObject.getZone() : null, defaultSelectionContentsElement);

		Set<Long> explored = new HashSet<>();
		List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObject, contentObject.getFocusOnDataType());
		for (ParameterGroupTreeNode pgtn : topLevelTreeNodes) {
		    if (pgtn == null || explored.contains(pgtn.getId())) {
				continue;
			}
		    explored.add(pgtn.getId());
			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), pgtn);
			if (cas != null && !cas.isEmpty()) {
				createSelectionTag(contentObject, cas, pgtn, defaultSelectionElement);
			}
		}
	}

	private void createSelectionTag(
	        ContentObject contentObject,
	        List<ContentObjectAssociation> msgContentAssociations,
			ParameterGroupTreeNode parameterGroupTreeNode,
	        Element element)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());

		if (touchpointSelection != null)
		{
			selectionElement.addAttribute("dna", touchpointSelection.getDna());
		}

		if(parameterGroupTreeNode != null) {
			selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
		}

		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}
		
		Element selectionNameElement = selectionElement.addElement("Name");

		if(parameterGroupTreeNode.getParameterGroupInstanceCollection() != null){
			List<ParameterGroupInstance> pgInstanceList = new ArrayList<>(parameterGroupTreeNode.getParameterGroupInstanceCollection().getParameterGroupInstances());
			pgInstanceList.sort(new IdentifiableMessagepointModelIdComparator());
			for(ParameterGroupInstance pgInstance : pgInstanceList){
				Element dataValueElm = selectionElement.addElement("DataValue");
				String value = getDataValue(pgInstance);
				dataValueElm.addAttribute("id", Long.toString(pgInstance.getId()));
				dataValueElm.addText(value);
			}
		}


		selectionNameElement.addText(parameterGroupTreeNode.getName());
		if (includeContent)
		{
			Element selectionContentsElement = selectionElement.addElement("Contents");
			createContentTagForSelection(contentObject, msgContentAssociations, parameterGroupTreeNode, selectionContentsElement);
		}
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes) {

				List<ContentObjectAssociation> childContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), childNode);
				createSelectionTag(contentObject, childContentAssociationList, childNode, selectionElement);

			}
		}
	}

	private Element createSelectionTagForTP(TouchpointSelection tpSelection, List<ContentObject> dbMessageInstanceVersionMapKeys, HashMap<ParameterGroupTreeNode, TouchpointSelection> tpSelections)
	{
		Element selectionElement = new DefaultElement("Selection");//element.addElement("Selection");
		ParameterGroupTreeNode parameterGroupTreeNode = tpSelection.getParameterGroupTreeNode();
		List<ContentObjectAssociation> values = ContentObjectAssociation.findAllByPgTreeNode(parameterGroupTreeNode);

		selectionElement.addAttribute("id", Long.toString(tpSelection.getId()));
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null) {
			selectionElement.addAttribute("datavaluerefid", Long.toString(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()));
		}

		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());
		Element selectionContentsElement = selectionElement.addElement("Contents");

		Document document = tpSelection.getDocument();

		if ( tpSelectableMessages.get(document) != null &&
		     !tpSelectableMessages.get(document).isEmpty()) 
		{
			for (ContentObject tpSelectableMessage : tpSelectableMessages.get(document))
			{
				if (includeAllMessages) {
					TouchpointSelection miSelection = tpSelectableMessage.getOwningTouchpointSelection();
					if (miSelection != null) {
						List<TouchpointSelection> miVariantAncestors = loadTpSelectionAncestors(miSelection, tpSelections);
						Set<TouchpointSelection> tpAncestors = new HashSet<>(loadTpSelectionAncestors(tpSelection, tpSelections));
						if (!tpAncestors.containsAll(miVariantAncestors)) {
						    continue;
						}
					}
				}

				// Skip adding the messages to the variant if it is not a structured message or the owing touchpoint selection of the message does not match the tpSelection
				if ( !tpSelectableMessage.isStructuredContentEnabled()) {
				    continue;
				}

				Element messageElement = selectionContentsElement.addElement("Message");
				messageElement.addAttribute("refid", Long.toString(tpSelectableMessage.getId()));

				List<ContentObjectAssociation> coaList;

				if (!pgTreeNodeCoaList.containsKey(parameterGroupTreeNode.getId())) {
					pgTreeNodeCoaList.put(parameterGroupTreeNode.getId(), values);
				}

				coaList = pgTreeNodeCoaList.get(parameterGroupTreeNode.getId());

				// Load variant structured messages at the master level
				if(tpSelectableMessage.getOwningTouchpointSelection()!=null) {
					coaList.addAll(ContentObjectAssociation.findAllByContentObjectAndParameters(tpSelectableMessage, tpSelectableMessage.getFocusOnDataType(), parameterGroupTreeNode));
				}

				long localeId;

				if (tpSelectableMessage.getFirstDocumentDelivery() != null && tpSelectableMessage.getFirstDocumentDelivery().getDefaultTouchpointLanguage() != null) {
					localeId = tpSelectableMessage.getFirstDocumentDelivery().getDefaultTouchpointLanguageLocale().getId();
				}
				else {
					localeId = systemDefaultLocaleId;
				}

				ContentObjectAssociation messageContentAssociation = coaList.stream()
						.filter(x ->
								x.getContentObject() != null &&
								x.getContentObject().getId() == tpSelectableMessage.getId() &&
								x.getDataType() == tpSelectableMessage.getFocusOnDataType() &&
								x.getMessagepointLocale().getId() == localeId)
						.findFirst()
						.orElse(null);

				if(messageContentAssociation == null){
					messageContentAssociation = new ContentObjectAssociation();
					messageContentAssociation.setTypeId(tpSelection.isMaster()?ContentAssociationType.ID_OWNS:ContentAssociationType.ID_REFERENCES);
				}

				if (includeContent) {
					if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_SUPPRESSES)
					{
						messageElement.addAttribute("suppress", Boolean.TRUE.toString());
					}
					else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_OWNS)
					{
						messageElement.addAttribute("custom", Boolean.TRUE.toString());
						List<ContentObjectAssociation> nodeContent = ContentObjectAssociation.findAllByContentObjectAndParameters(
								tpSelectableMessage, tpSelectableMessage.getFocusOnDataType(), parameterGroupTreeNode);
						createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement);
					}
					else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_REFERENCES)
					{
						messageElement.addAttribute("sameasparent", Boolean.TRUE.toString());
						messageElement.addAttribute("parentrefid", Long.toString(tpSelection.getParent().getId()));
					}
					else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_EMPTY)
					{
						messageElement.addAttribute("empty", Boolean.TRUE.toString());
					}
					else if (messageContentAssociation.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT)
					{
						messageElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					}
				}

				// Set content selection status

				//ContentSelectionStatusType contentSelectionStatus = new ContentSelectionStatusType(ContentObjectAssociation.findById(messageContentAssociation.getId()).getContentSelectionStatusId());//messageContentAssociation.getContentSelectionStatusId());
				ContentSelectionStatusType contentSelectionStatus = new ContentSelectionStatusType(messageContentAssociation.getContentSelectionStatusId());

				messageElement.addAttribute("status", contentSelectionStatus.getDisplayText());
			}
						
		}

		Element messagesElement = selectionElement.addElement("Messages");
		if ( tpSelectableMessages.get(document) != null &&
		     !tpSelectableMessages.get(document).isEmpty()) 
		{
			for (ContentObject tpSelectableMessage : tpSelectableMessages.get(document)) 
			{
				if ( tpSelectableMessage.isVariantType() && tpSelectableMessage.getOwningTouchpointSelection().getId() == tpSelection.getId()) {
					ContentObjectZonePriority contentObjectZonePriority = null; // TODO
//				    ContentObjectZonePriority.findByMessageInstanceAndZoneUnique(
//				            tpSelectableMessage, tpSelectableMessage.getZones().size() != 0 ? tpSelectableMessage.getZones().iterator().next() : null, tpSelection );
					Element messageElement = messagesElement.addElement("Message");
					messageElement.addAttribute("id", Long.toString(tpSelectableMessage.getId()));
					if (contentObjectZonePriority != null && contentObjectZonePriority.isSuppress()) {
						messageElement.addAttribute("suppressed", Boolean.TRUE.toString());
					}
                    if (contentObjectZonePriority != null && contentObjectZonePriority.isRepeatWithNext()) {
                        messageElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                    }
				}
			}
		}
		
		// Priority
		if (includeAllMessages)
			buildMessagePriorityElm(selectionElement, dbMessageInstanceVersionMapKeys, tpSelection);
		

		return selectionElement;
	}

	private HashMap<ParameterGroupTreeNode, TouchpointSelection> mapTpSelectionToPgTree(List<TouchpointSelection> selections) {

		HashMap<ParameterGroupTreeNode, TouchpointSelection> result = new HashMap<>();

		for (TouchpointSelection selection : selections) {
			if (!result.containsKey(selection.getParameterGroupTreeNode())) {
				result.put(selection.getParameterGroupTreeNode(), selection);
			} else {
				LogUtil.getLog(ExportMessageToXMLService.class).warn("PGTreeNode key exists: " + selection.getParameterGroupTreeNode().getId());
			}
		}

		return result;
	}

	private List<TouchpointSelection> loadTpSelectionAncestors(TouchpointSelection selection, HashMap<ParameterGroupTreeNode, TouchpointSelection> selections) {

		/*
		List<TouchpointSelection> ancesters = new ArrayList<TouchpointSelection>();

		ParameterGroupTreeNode treeMode = getParameterGroupTreeNode();
		ParameterGroupTreeNode parentTreeMode = null;
		if (treeMode != topParameterGroupTreeNode)
			parentTreeMode = treeMode.getParentNode();

		while (parentTreeMode != null) {
			TouchpointSelection parent = TouchpointSelection.findByPgTreeNodeId(parentTreeMode.getId());
			if (!parent.isDeleted())
				ancesters.add(parent);
			if (parentTreeMode != topParameterGroupTreeNode)
				parentTreeMode = parentTreeMode.getParentNode();
			else
				parentTreeMode = null;
		}

		Collections.reverse(ancesters);

		return ancesters;
		*/

		List<TouchpointSelection> result = new ArrayList<>();

		ParameterGroupTreeNode node = selection.getParameterGroupTreeNode();

		while (node != null) {
			if (selections.containsKey(node)) {
				result.add(selections.get(node));
			}

			node = node.getParentNode();
		}

		Collections.reverse(result);

		return result;
	}

	private Set<ContentObjectAssociation> getAssociationsForZonePart(
	        ZonePart zp,
	        Set<ContentObjectAssociation> contAssocs )
	{
        Set<ContentObjectAssociation> result = new HashSet<>();
        for( ContentObjectAssociation ca : contAssocs )
        {
            if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
            {
                result.add(ca);
            }
        }
        return result;
	}

	private Set<ContentObjectAssociation> getAssociationsForZonePart(
	        ZonePart zp,
	        List<ContentObjectAssociation> contAssocs)
	{
	    Set<ContentObjectAssociation> result = new HashSet<>();
	    for( ContentObjectAssociation ca : contAssocs )
	    {
	        if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
	        {
	            result.add(ca);
	        }
	    }
	    return result;
	}
	
	private void createContentTagForSelection(
	        ContentObject contentObject,
	        List<ContentObjectAssociation> messageContentAssociations,
	        ParameterGroupTreeNode selection,
	        Element contentsElement)
	{
		List<MessagepointLocale> languages = contentObject.getContentObjectLanguagesAsLocales();
		String defaultTouchpointLocaleCode = contentObject.getDefaultContentObjectLanguageAsLocale().getCode();

	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled()) 
	    {
	        selectableMessage = true;
	    }

	    if (contentObject.isMultipartType()) 
	    {
	        Integer partNum = 1;
	        for (ZonePart zonePart : contentObject.getZone().getPartsInSequenceOrder())
	        {
	            Element partElement = contentsElement.addElement("Part");
	            partElement.addAttribute("num", (partNum++).toString());
	            Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, messageContentAssociations);
	            
            	boolean isPartEmpty = false;
    		    for (MessagepointLocale locale : languages)
                {
                	if (isPartEmpty)
                		break;
	            
                    String languageCode = locale.getLanguageCode();
					String localeCode = locale.getCode();

					boolean missingFlag = true;					
		            for( ContentObjectAssociation ca : partAssociations )
		            {
		                if ( ca.getPGTreeNode() != null && !ca.getPGTreeNode().equals(selection) )
		                    continue;
		                
		                if ( ca.getTypeId() == ContentAssociationType.ID_EMPTY || ( ca.getTypeId() == ContentAssociationType.ID_OWNS && ca.getContent() == null && ca.getReferencingImageLibrary() == null) )
		                {
		                    partElement.addAttribute("empty", Boolean.TRUE.toString());
		                    isPartEmpty = true;
		                    break;
		                }
		                else {

							if (ca.getMessagepointLocale().getId() != locale.getId())
								continue;

							String languageContent = "";
							String langImageName = "";
							String langImageUploaded = "";


							boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
							Content content = ca.getContent();

							if (zonePart.getContentType().getId() == ContentType.TEXT || contentObject.getContentType().getId() == ContentType.SHARED_FREEFORM) {
								if (content != null) {
									if (content.getEncodedContent() != null) {
										languageContent = content.getContent();
									}
								} else {
									sameAsParent = true;
								}
							} else // graphic type
							{
								if (content != null) {
									if (content.getImageName() != null) {
										languageContent = content.getImageName();
										if (content.getAppliedImageFilename() != null)
											langImageName = content.getAppliedImageFilename();
										if (content.getImageUploadedDate() != null)
											langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
										else
											langImageUploaded = "unknown";
									}
								} else {
									sameAsParent = true;
								}
							}

							missingFlag = false;
							Element contentElement = partElement.addElement("Content");
							contentElement.addAttribute("language", languageCode);
							contentElement.addAttribute("locale", localeCode);
							contentElement.addAttribute("coaid", String.valueOf(ca.getId()));
							contentElement.addAttribute("versiondatatype", String.valueOf(ca.getDataType()));

							if (selectableMessage && sameAsParent) {
								contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
							} else {
								if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
									contentElement.addAttribute("isdefault", Boolean.TRUE.toString());

								if (ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)) {
									contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
								} else if (ca.getReferencingImageLibrary() != null) {
									contentElement.addAttribute("imagelibraryrefid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
									contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
								} else {
									if (!langImageName.isEmpty())
										contentElement.addAttribute("imagename", langImageName);

									if (!langImageUploaded.isEmpty())
										contentElement.addAttribute("uploaded", langImageUploaded);

									contentElement.addCDATA(languageContent);
								}
							}
						}
	                }
					if (missingFlag && !isPartEmpty)
					{
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
		                if (selectableMessage)
		                {
		                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
		                }
		                else
		                {
							if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
								contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
							else
								contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		                }
					}					
	            }
	        }
	    }
	    else 
	    {  // not multi-part (text or graphic) 
		    for (MessagepointLocale locale : languages)
	        {
	            String languageCode = locale.getLanguageCode();
				String localeCode = locale.getCode();
				boolean missingFlag = true;
	            for( ContentObjectAssociation ca : messageContentAssociations )
	            {
                    if ( ca.getPGTreeNode() != null && !ca.getPGTreeNode().equals(selection) )
                    {
                        continue;
                    }

	                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
	                {
	                    continue;
	                }

	                String languageContent = "";
	                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
	                Content content = ca.getContent();
                    String langImageName = "";
                    String langImageUploaded = "";
					ComplexValue imageLink = null;

	                if (contentObject.getContentType().getId() == ContentType.TEXT || contentObject.getContentType().getId() == ContentType.SHARED_FREEFORM)
	                {
	                    if ( content == null || content.getEncodedContent() == null )
	                    {
	                        sameAsParent = true;
	                    }
	                    else
	                    {
	                        languageContent = content.getContent(ca.getDocument());
	                    }
	                }
	                else
	                {
	                    if ( content != null) 
	                    {
							imageLink = content.getImageLink();
	                        languageContent = content.getImageName();
	                        if ( content.getAppliedImageFilename() != null )
	                            langImageName = content.getAppliedImageFilename();
	                        if ( content.getImageUploadedDate() != null )
	                            langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
	                        else
	                            langImageUploaded = "unknown";
	                    } 
	                    else if (ca.getReferencingImageLibrary() == null)
						{
                        	sameAsParent = true;
	                    }
	                }
				    
	                missingFlag = false;
	                Element contentElement = contentsElement.addElement("Content");
					contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
					contentElement.addAttribute("coaid", String.valueOf(ca.getId()));
					contentElement.addAttribute("versiondatatype", String.valueOf(ca.getDataType()));

	                if (sameAsParent && selectableMessage) {
	                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
					}else {
						if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());

						if (ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)) {
							contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
						} else if (ca.getReferencingImageLibrary() != null) {
							contentElement.addAttribute("imagelibraryrefid", Long.valueOf(ca.getReferencingImageLibrary().getId()).toString());
							contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
						} else {
							if (!langImageName.isEmpty())
								contentElement.addAttribute("imagename", langImageName);
							if (!langImageUploaded.isEmpty())
								contentElement.addAttribute("uploaded", langImageUploaded);

							contentElement.addCDATA(languageContent);

							if (imageLink != null)
							{
								Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
								imageLinkElement.addAttribute("language", languageCode);
								contentElement.addAttribute("locale", localeCode);
								imageLinkElement.addAttribute("guid", imageLink.getGuid());
								imageLinkElement.addCDATA(imageLink.getEncodedValue());
							}
						}
					}
	            }
				if (missingFlag)
				{
					Element contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);

	                if (selectableMessage)
	                {
	                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
	                }
	                else
	                {
						if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
							contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
						else
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
	                }
				}				    
	        }
	    }
	}


	/**
	 * Return the data value text
     */
	private String getDataValue(ParameterGroupInstance pgInstance){
		String dataValue = null;
		if(pgInstance.getPgItemValue1()!=null){
			dataValue = pgInstance.getPgItemValue1();
		}
		if(pgInstance.getPgItemValue2()!=null){
			dataValue += "|"+pgInstance.getPgItemValue2();
		}
		if(pgInstance.getPgItemValue3()!=null){
			dataValue += "|"+pgInstance.getPgItemValue3();
		}
		if(pgInstance.getPgItemValue4()!=null){
			dataValue += "|"+pgInstance.getPgItemValue4();
		}
		if(pgInstance.getPgItemValue5()!=null){
			dataValue += "|"+pgInstance.getPgItemValue5();
		}
		if(pgInstance.getPgItemValue6()!=null){
			dataValue += "|"+pgInstance.getPgItemValue6();
		}
		return dataValue;
	}

	private static String createValue(String returnValue, long pgiCollectionId, List<String> pgiPathValues, int level)throws Exception{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		StringBuilder valueString = new StringBuilder();

		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {

                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					childIthItemValues.add(createValue(returnValue, pgiCollectionId, pathValues, level + 1));
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if (valueString.length() > 0) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				//valueCell.setCellValue(valueString);

				//pgiPathValues.add(valueString);
			}
			else{
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
			}
		}
		returnValue = returnValue.concat(valueString.toString());
		return returnValue;
	}


	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(
			TouchpointSelection tpSelection,
			int selectionStatus,
			List<ContentObject> messagelist,
			boolean dateFilterEnabled,
			Date startDate,
			boolean includeTargeting,
			boolean includeContent,
			boolean includeAllMessages,
			User requestor,
			int auditReportTypeId,
			Document document)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportMessageToXMLServiceRequest request = new ExportMessageToXMLServiceRequest();

		request.setTpSelection(tpSelection);
		request.setSelectionStatus(selectionStatus);
		request.setMessageslist(messagelist);
		request.setDateFilterEnabled(dateFilterEnabled);
		request.setStartDate(startDate);
		request.setIncludeTargeting(includeTargeting);
		request.setIncludeContent(includeContent);
		request.setIncludeAllMessages(includeAllMessages);
		request.setRequestor(requestor);
		request.setAuditReportTypeId(auditReportTypeId);
		request.setDocument(document);

		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public boolean isIncludeAllMessages() {
		return includeAllMessages;
	}

	public void setIncludeAllMessages(boolean includeAllMessages) {
		this.includeAllMessages = includeAllMessages;
	}

	static ExecutorService getExecutorService(String threadNameSuffix) {

		ThreadFactory similarityThredBuilder = new ThreadFactoryBuilder().setNameFormat("export-message-xml-" + threadNameSuffix + "_" + System.currentTimeMillis() + "-%d").build();
		ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() - 2, similarityThredBuilder);

		return executorService;
	}

	private static class SelectionIdElement {
		long selectionId;
		Element element;
	}
}
