package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.audit.AuditReportStatus;
import com.prinova.messagepoint.model.externalevent.ExternalEvent;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;


public class CreateOrUpdateAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.CreateOrUpdateAuditReportService";
	private static final Log log = LogUtil.getLog(CreateOrUpdateAuditReportService.class);

	private static final int ACTION_CREATE 			= 1;
	private static final int ACTION_MARK_COMPLETE 	= 2;
	private static final int ACTION_ERROR 			= 3;
	private static final int ACTION_CREATE_EXTERNAL = 4;
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateAuditReportServiceRequest request = (CreateOrUpdateAuditReportServiceRequest) context.getRequest();

			AuditReport auditReport = null;
			
			switch (request.getAction()) {
			case ACTION_CREATE:
				List<AuditReport> currentActiveAuditReports = AuditReport.findAllInternalUnexpiredByUserId(request.getRequestorId(), request.getTypeId());
				for (AuditReport currentAuditReport : currentActiveAuditReports) {			
					currentAuditReport.setStatusId(AuditReportStatus.ID_EXPIRED);
					deleteAuditReportFiles(currentAuditReport);
					currentAuditReport.setXMLPath(null);
					currentAuditReport.setReportPath(null);
					currentAuditReport.save();
				}
				
				// Cleanup stale report: May occur from running multiple reports at the same time
				List<AuditReport> currentStaleReports = AuditReport.findAllInternalRequiringCleanupByUserId(request.getRequestorId(), request.getTypeId());
				for (AuditReport currentAuditReport : currentStaleReports) {
					deleteAuditReportFiles(currentAuditReport);
					currentAuditReport.setXMLPath(null);
					currentAuditReport.setReportPath(null);
					currentAuditReport.save();
				}
				
				auditReport = new AuditReport();
				
				auditReport.setTypeId(request.getTypeId());
				auditReport.setRequestDate(DateUtil.now());
				auditReport.setUserId(request.getRequestorId());
				auditReport.setStatusId(AuditReportStatus.ID_IN_PROCESS);
				
				auditReport.save();
				
				context.getResponse().setResultValueBean(auditReport.getId());

				break;
			case ACTION_CREATE_EXTERNAL:
				List<AuditReport> externalAuditReports = AuditReport.findAllExternalUnexpiredByUser(request.getRequestorId(), 
																									request.getTypeId(), 
																									request.getExternalEvent().getId());
				// External request, clean XML and expire previous report
				for (AuditReport currentExternalAuditReport : externalAuditReports) {
					deleteAuditReportFiles(currentExternalAuditReport);
					currentExternalAuditReport.setStatusId(AuditReportStatus.ID_EXPIRED);
					currentExternalAuditReport.save();
				}

				// Create new AuditReport
				auditReport = new AuditReport();
				
				auditReport.setTypeId(request.getTypeId());
				auditReport.setRequestDate(DateUtil.now());
				auditReport.setUserId(request.getRequestorId());
				auditReport.setXMLPath(request.getXmlPath());
				auditReport.setExternalEvent(request.getExternalEvent());
				auditReport.setStatusId(AuditReportStatus.ID_COMPLETE);

				auditReport.save();
				
				context.getResponse().setResultValueBean(auditReport.getId());
				
				break;
			case ACTION_MARK_COMPLETE:
		    	auditReport = AuditReport.findById(request.getReportId());

				// Update the new paths
				auditReport.setXMLPath(request.getXmlPath());
				auditReport.setReportPath(request.getReportPath());

				auditReport.setStatusId(AuditReportStatus.ID_COMPLETE);
		    	
				auditReport.save();
		    	
				break;
			case ACTION_ERROR:
		    	auditReport = AuditReport.findById(request.getReportId());

		    	auditReport.setStatusId(AuditReportStatus.ID_ERROR);
		    	
				break;
			default:
				break;
			}


		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateOrUpdateAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}
	
	private void deleteAuditReportFiles(AuditReport auditReport) {
		String xmlPath = auditReport.getXMLPath();
		if(xmlPath != null){
			File xmlFile = new File(xmlPath);
			xmlFile.delete();	
		}
		String reportPath = auditReport.getReportPath();
		if(reportPath != null){
			File reportFile = new File(reportPath);
			reportFile.delete();	
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContextForError(long reportId) {

		ServiceExecutionContext context = createContext(reportId,
														0,
														"", 
														"",
														null,
														ACTION_ERROR,
														null);

		return context;
	}

	public static ServiceExecutionContext createContextForMarkComplete(long reportId,
																	   int typeId,
		    													 	   String xmlPath,
		    													 	   String reportPath) {

		ServiceExecutionContext context = createContext(reportId,
														typeId,
														xmlPath, 
														reportPath,
														null,
														ACTION_MARK_COMPLETE,
														null);

		return context;
	}

	public static ServiceExecutionContext createContextForCreate(Long requestorId, int typeId) {

		ServiceExecutionContext context = createContext(-1L,
														typeId,
														"", 
														"",
														requestorId,
														ACTION_CREATE,
														null);

		return context;
	}
	
	public static ServiceExecutionContext createContextForExternalCreate(Long requestorId, 
																		 int typeId,
																		 String xmlPath,
																		 ExternalEvent externalEvent) {

		ServiceExecutionContext context = createContext(-1L,
														typeId,
														xmlPath, 
														"",
														requestorId,
														ACTION_CREATE_EXTERNAL,
														externalEvent);

		return context;
	}	
	

	public static ServiceExecutionContext createContext(long reportId,
														int typeId,
													    String xmlPath,
													    String reportPath,
													    Long requestorId,
													    int action, 
													    ExternalEvent externalEvent) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateAuditReportServiceRequest request = new CreateOrUpdateAuditReportServiceRequest();

		request.setReportId(reportId);
		request.setTypeId(typeId);
		request.setXmlPath(xmlPath);
		request.setReportPath(reportPath);
		request.setRequestorId(requestorId);
		request.setAction(action);
		request.setExternalEvent(externalEvent);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}