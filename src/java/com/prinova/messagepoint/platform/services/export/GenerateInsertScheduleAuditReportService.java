package com.prinova.messagepoint.platform.services.export;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class GenerateInsertScheduleAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateInsertScheduleAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateInsertScheduleAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateInsertScheduleAuditReportServiceRequest request = (GenerateInsertScheduleAuditReportServiceRequest) context.getRequest();

			InsertScheduleAuditReportRunner insertExportRunner = new InsertScheduleAuditReportRunner(request.getAuditReportId(),
                    request.getInsertScheduleList(),
                    request.getStartDate(),
                    request.getIncludeInsertTargeting(),
                    User.findById(request.getRequestorId()));
	   		MessagePointRunnableUtil.startThread(insertExportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateInsertScheduleAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class InsertScheduleAuditReportRunner extends MessagePointRunnable {

		private long auditReportId;
		private List<InsertSchedule> insertSchedulelist = new ArrayList<>();
		private Date startDate;
		private Boolean includeInsertTargeting;
		private User requestor;

		public InsertScheduleAuditReportRunner(long auditReportId, 
											   List<InsertSchedule> insertScheduleList, 
									   		   Date startDate, 
									   		   Boolean includeInsertTargeting,
									   		   User requestor) {
			
			this.auditReportId = auditReportId;
			this.insertSchedulelist = insertScheduleList;
			this.startDate = startDate;
			this.includeInsertTargeting = includeInsertTargeting;
			this.requestor = requestor;
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			try {
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
		    	ServiceExecutionContext exportServiceContext = ExportInsertScheduleToXMLService.createContext(getInsertSchedulelist(), 
		    																						   getStartDate(),
		    																						   getIncludeInsertTargeting(), 
		    																						   getRequestor()); 
	
		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportInsertScheduleToXMLService.SERVICE_NAME, ExportInsertScheduleToXMLService.class);
		    	exportService.execute(exportServiceContext);

		    	String xmlFilePath = ((ExportToXMLServiceResponse)exportServiceContext.getResponse()).getFilePath();
		    	
				// ******************************************
				// ********* Generate XSLT ******************
				// ******************************************

		    	String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, getRequestor(), AuditReportType.ID_INSERT_AUDIT_REPORT, 0);
		    	
				// *********************************************************
				// ********* Update the AuditReport object *****************
				// *********************************************************

				ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(getAuditReportId(),
																																	AuditReportType.ID_INSERT_AUDIT_REPORT,
																																	xmlFilePath, 
																																	xhtmlFilePath);
				Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				updateReportService.execute(updateReportServiceContext);
		    	
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Insert Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
			}
		}

		public long getAuditReportId() {
			return auditReportId;
		}

		public List<InsertSchedule> getInsertSchedulelist() {
			return insertSchedulelist;
		}

		public Date getStartDate() {
			return startDate;
		}

		public Boolean getIncludeInsertTargeting() {
			return includeInsertTargeting;
		}

		public User getRequestor() {
			return requestor;
		}

	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(long auditReportId, 
														List<InsertSchedule> insertScheduleList, 
														Date startDate,
														Boolean includeInsertTargeting, 
														Long requestorId) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateInsertScheduleAuditReportServiceRequest request = new GenerateInsertScheduleAuditReportServiceRequest();

		request.setAuditReportId(auditReportId);
		request.setInsertScheduleList(insertScheduleList);
		request.setStartDate(startDate);
		request.setIncludeInsertTargeting(includeInsertTargeting);
		request.setRequestorId(requestorId);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}