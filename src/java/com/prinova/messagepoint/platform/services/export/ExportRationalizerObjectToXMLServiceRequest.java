package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportRationalizerObjectToXMLServiceRequest extends SimpleServiceRequest {


	private static final long serialVersionUID = -4709165781423734958L;

	private String 		exportId;
    private long 		targeObjectId;
    private boolean		includeImagePathOnly;
    private User 		requestor;
    private ExportImportOptions exportOptions;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    public ExportRationalizerObjectToXMLServiceRequest(String exportId, long targeObjectId, boolean includeImagePathOnly, User user )
    {
    	this(exportId, targeObjectId, includeImagePathOnly, user, null);
    }
    public ExportRationalizerObjectToXMLServiceRequest(String exportId, long targeObjectId, boolean includeImagePathOnly, User user, StatusPollingBackgroundTask statusPollingBackgroundTask )
    {
        this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        this.includeImagePathOnly = includeImagePathOnly;
        this.requestor = user;
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }

    public ExportRationalizerObjectToXMLServiceRequest(String exportId, long targeObjectId, User user, ExportImportOptions exportOptions )
    {
    	this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        if(exportOptions!=null)
        	this.includeImagePathOnly = exportOptions.isIncludeImagePathOnly();
        this.requestor = user;
        this.exportOptions = exportOptions;
    }

	public boolean isIncludeImagePathOnly() {
		return includeImagePathOnly;
	}

	public String getExportId() {
		return exportId;
	}

	public long getTargeObjectId() {
        return targeObjectId;
    }
    
    public User getRequestor() {
        return requestor;
    }
    
    public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }
}
