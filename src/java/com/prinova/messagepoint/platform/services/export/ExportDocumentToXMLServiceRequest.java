package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportDocumentToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -1553522279771063788L;

	private String 		exportId;
    private long 		documentId;
    private ExportImportOptions exportOptions;
    private User 		requestor;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;
    
    public ExportDocumentToXMLServiceRequest(String exportId, long docId, ExportImportOptions exportOptions, User user, StatusPollingBackgroundTask statusPollingBackgroundTask )
    {
    	this.exportId = exportId;
        this.documentId = docId;
        this.setExportOptions(exportOptions);
        this.requestor = user;
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }

	public String getExportId() {
		return exportId;
	}

	public long getDocumentId() {
        return documentId;
    }
    
    public User getRequestor() {
        return requestor;
    }

	public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
		return statusPollingBackgroundTask;
	}

	public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
		this.statusPollingBackgroundTask = statusPollingBackgroundTask;
	}

	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}   
}
