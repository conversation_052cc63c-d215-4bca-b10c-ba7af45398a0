package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class GenerateTpDeliveryReportServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = 7603413376063341560L;
	
	private long reportId;
	private User requestor;
	private TpDeliveryReportScenario reportScenario;
	
	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}				
	
	public TpDeliveryReportScenario getReportScenario(){
		return reportScenario;
	}
	public void setReportScenario(TpDeliveryReportScenario reportScenario){
		this.reportScenario = reportScenario;
	}
}
