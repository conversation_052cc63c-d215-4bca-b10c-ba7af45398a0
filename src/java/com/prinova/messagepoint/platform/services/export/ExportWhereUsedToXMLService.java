package com.prinova.messagepoint.platform.services.export;

import java.io.File;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.wtu.WhereUsedVariableReport;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.wtu.WhereUsedReport;
import com.prinova.messagepoint.wtu.WhereUsedReportFactory;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class ExportWhereUsedToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportWhereUsedToXMLService";
	
	private static final Log log = LogUtil.getLog(ExportWhereUsedToXMLService.class);
	
	public static final String TRANSITION_FILE_EXT 	= ".inProcess";
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context))
				return;

			ExportWhereUsedToXMLServiceRequest request = (ExportWhereUsedToXMLServiceRequest) context.getRequest();
			
			// *****************************************
			// ********* Generate XML ******************
			// *****************************************
			WhereUsedReport whereUsedReport = WhereUsedReportFactory.getWhereUsedReportInstance(request.getTargetObjectId(), request.getTargetClass());
			whereUsedReport.setRequestor(request.getUser());
			whereUsedReport.setIncludeActiveOnly(request.isIncludeActiveObjOnly());
			whereUsedReport.setIndRefSelections(request.getIndirectReferences());
			whereUsedReport.generateWhereUsedXML();
			
			String inProcessReportFilePath = WhereUsedVariableReport.getReportXmlFilePath(request.getReportId(), request.getUser().getId()) + TRANSITION_FILE_EXT;
			String completedReportFilePath = WhereUsedVariableReport.getReportXmlFilePath(request.getReportId(), request.getUser().getId());
			whereUsedReport.writeToXMLFile( inProcessReportFilePath );
			File inProcessReportFile = new File( inProcessReportFilePath );
			inProcessReportFile.renameTo( new File(completedReportFilePath) );
		
			context.getResponse().setResultValueBean(request.getReportId());

		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportWhereUsedToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(long reportId, long targetObjectId, Class<?> targetClass, boolean includeActiveObjOnly, String[] indirectReferences, User user)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportWhereUsedToXMLServiceRequest request = new ExportWhereUsedToXMLServiceRequest();

		request.setReportId(reportId);
		request.setTargetObjectId(targetObjectId);
		request.setTargetClass(targetClass);
		request.setIncludeActiveObjOnly(includeActiveObjOnly);
		request.setIndirectReferences(indirectReferences);
		request.setUser(user);

		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}
