package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class GenerateProjectAuditReportServiceRequest extends SimpleServiceRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -437117050373100622L;
	
	private long auditReportId;
	private List<Long> projectList = new ArrayList<>();
	private boolean includeAllProjects;
	private User requestor;
	
	
	public long getAuditReportId() {
		return auditReportId;
	}
	public void setAuditReportId(long auditReportId) {
		this.auditReportId = auditReportId;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public List<Long> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<Long> projectList) {
		this.projectList = projectList;
	}
	public boolean isIncludeAllProjects() {
		return includeAllProjects;
	}
	public void setIncludeAllProjects(boolean includeAllProjects) {
		this.includeAllProjects = includeAllProjects;
	}
	
}