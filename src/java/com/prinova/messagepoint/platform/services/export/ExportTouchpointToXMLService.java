package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.common.ContentSelectionType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;

import java.io.File;
import java.util.*;


public class ExportTouchpointToXMLService extends AbstractService {
	public static final String SERVICE_NAME = "export.ExportTouchpointToXMLService";
	private static final Log log = LogUtil.getLog(ExportTouchpointToXMLService.class);

	public static final String EXTERNAL_EVENT_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm";
	public static final int VERSION_WORKINGCOPY = 0;
	public static final int VERSION_RELEASED 	= 1;
	public static final int VERSION_ACTIVE		= 2;
	
    private User requestor;
	private Date fromDate;
	private Date toDate;  
	private long reportTimestamp;
	private Document tpDocument;
    
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			ExportTouchpointToXMLServiceRequest request = (ExportTouchpointToXMLServiceRequest) context.getRequest();
			requestor = request.getRequestor();
			fromDate = request.getFromDate();
			toDate = request.getToDate();
			reportTimestamp = request.getReportTimestamp();	
			tpDocument = request.getDocument();
			if(reportTimestamp <= 0){
				reportTimestamp = System.currentTimeMillis();
			}
			
			// Generate the XML audit report
			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			generateXML(document);
			String xmlPath = ExportUtil.saveXMLToFile(document, requestor, AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT, reportTimestamp);
			File xmlFile = new File(xmlPath);
			((ExportToXMLServiceResponse) context.getResponse()).setFilePath(xmlFile.getAbsolutePath());
		}
		catch (Exception e){
			log.error(" unexpected exception when invoking ExportTouchpointToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}
	
	private org.dom4j.Document initializeWriter() throws Exception {
		return DocumentHelper.createDocument();
	}	
	
	private void generateXML(org.dom4j.Document document) throws Exception {
		// Build the "TouchpointAudit" element
		Element tpAuditElm = DocumentHelper.createElement("TouchpointAudit");
		tpAuditElm.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		if(fromDate != null || toDate != null){
			tpAuditElm.addAttribute("type", "change");
		}else{
			tpAuditElm.addAttribute("type", "audit");
		}
		document.setRootElement(tpAuditElm);
		// - Build the "Metadata" element
		buildMetaDataElm(tpAuditElm, requestor);
		// - Build the "Touchpoints" element
		Element tpsElm = DocumentHelper.createElement("Touchpoints");
		tpAuditElm.add(tpsElm);
		// Only for enabled TP
		if(tpDocument.isEnabled()){
			// Reload the touchpoint object
			Document doc = HibernateUtil.getManager().getObject(Document.class, tpDocument.getId());
			// If touchpoint does not have visible zones, don't include this touchpoint
			// in the audit report for the internal request (Does not apply for the external request)
			if(existsVisibleZone(doc)){	
				// -- Build the "Touchpoint" element
				Element tpElm = tpsElm.addElement("Touchpoint");
				tpElm.addAttribute("id", Long.toString(doc.getId()));
				// --- Build the "Name" element
				Element touchpointNameElement = tpElm.addElement("Name");
				touchpointNameElement.addText(doc.getName());	
				// --- Build the "Sections" element
				ExportUtil.createTPSectionsTag(doc, tpElm, requestor, true);
				if (tpDocument.isEnabledForVariation())
				{
					// --- Build the "Selections" element
					buildSelectionsElm(tpElm, requestor, doc);
				}
			}
		}			
	}
	
	/**
	 * Check whether the given touchpoint exists visible zone
     */
	private boolean existsVisibleZone(Document doc){
		long workgroupId = requestor.getWorkgroupId();
		if(workgroupId > 0){
			Workgroup wg = Workgroup.findById(workgroupId);
			if(wg!=null){
				for (Iterator<Zone> iterator = doc.getZones().iterator(); iterator.hasNext();) {
					Zone zone = (Zone) iterator.next();
					if(zone!=null && wg.getZones().contains(zone)){
						return true;
					}
				}//end of for
			}		
		}
		return false;
	}
	
	/**
	 * Build the "Metadata" element
     */
	private void buildMetaDataElm(Element parentElm, User requestor){
		// Build the "Metadata" element
		Element metadataElm = DocumentHelper.createElement("Metadata");
		parentElm.add(metadataElm);
		// - Build the "User" element
		metadataElm.add(DocumentHelper.createElement("User").addText(requestor.getName()));
		// - Build the "RequestDate" element
		metadataElm.add(DocumentHelper.createElement("RequestDate").addText(DateUtil.formatDateForXMLOutput(DateUtil.now())));
		// - Build the "FromDate" element
		if(fromDate != null){
			metadataElm.add(DocumentHelper.createElement("FromDate").addText(DateUtil.formatDateForXMLOutput(fromDate)));
		}
		// - Build the "ToDate" element
		if(toDate != null){
			metadataElm.add(DocumentHelper.createElement("ToDate").addText(DateUtil.formatDateForXMLOutput(toDate)));
		}
	}
	
	/**
	 * Build the "Selections" element
     */
	private void buildSelectionsElm(Element parentElm, User requestor, Document document){
		// Build the "Selections" element
		Element selectionsElm = parentElm.addElement("Selections");
		// - Build the "Selector" element
		buildSelectorElm(selectionsElm, document);
		// - Build the "Selection" element
		Set<TouchpointSelection> tpSelections = document.getVisibleTouchpointSelections();
		Iterator<TouchpointSelection> iter = tpSelections.iterator();
		while(iter.hasNext()){
			TouchpointSelection tpSelection = iter.next();
			// Find the master ("Default") and start building selection tree from there
			// Also perform for the visibility for the selection 
			if(tpSelection.isMaster() && tpSelection.isVisible(requestor)){
				buildSelectionElm(selectionsElm, tpSelection);
			}
		}
	}
	
	/**
	 * Build the "Selector" element
     */
	private void buildSelectorElm(Element parentElm, Document document){
		ParameterGroup selector = document.getSelectionParameterGroup();
		if(selector != null){
			// Build the "Selector" element
			Element selectorElm = parentElm.addElement("Selector");
			selectorElm.addAttribute("id", Long.toString(selector.getId()));
			// - Build the "Name" element
			selectorElm.add(DocumentHelper.createElement("Name").addText(selector.getName()));
			// - Build the "Parameters" element
			Element parasElm = selectorElm.addElement("Parameters");
			Set<ParameterGroupItem> paramItems = selector.getParameterGroupItems();
			Iterator<ParameterGroupItem> iter = paramItems.iterator();
			while(iter.hasNext()){
				Parameter param = iter.next().getParameter();
				// - Build the "Parameter" element
				Element paraElm = parasElm.addElement("Parameter");
				paraElm.addAttribute("id", Long.toString(param.getId()));
				paraElm.addText(param.getName());
			}
		}
	}
	
	/**
	 * Recursively build the "Selection" element
     */
	private void buildSelectionElm(Element parentElm, TouchpointSelection tpSelection){
		// Build the "Selection" element
		Element selectionElm = parentElm.addElement("Selection");
		selectionElm.addAttribute("id", Long.toString(tpSelection.getId()));
		// - Build the "Name" element
		selectionElm.add(DocumentHelper.createElement("Name").addText(tpSelection.getName()));
		// - Build the "DataValue" element
		buildDataValueElm(selectionElm, tpSelection);
		// - Build the "Versions" element
		Element versionsElm = selectionElm.addElement("Versions");
		buildVersionsElm(versionsElm, tpSelection);
		Element messagesElement = selectionElm.addElement("Messages");
		Map<String, String> orderByMap = new HashMap<>();
		orderByMap.put("name", "asc");
		List<ContentObject> list = buildItemsList(orderByMap, this.tpDocument.getId(), tpSelection.getId());

		if ( list != null && !list.isEmpty())
		{
			for ( ContentObject tpSelectableMessage: list)
			{
				if ( tpSelectableMessage == null) {
				    continue;
				}
				
				Element messageElement = messagesElement.addElement("Message");
				messageElement.addAttribute("id", Long.valueOf(tpSelectableMessage.getId()).toString());
				
				String status = "";
				int contentSelectionTypeId = 0;
				if(tpSelectableMessage.hasActiveData()){
					ContentObjectData activeCopy = tpSelectableMessage.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
					status = status.concat(activeCopy.getStatus().getLocaledString());
					contentSelectionTypeId = activeCopy.getContentSelectionTypeId();
				}
	
				if(tpSelectableMessage.hasWorkingData()){
					if(!status.isEmpty()) {
						status = status.concat(", ");
					}
					ContentObjectData workingCopy = tpSelectableMessage.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
					status = status.concat(workingCopy.getStatus().getLocaledString());
					contentSelectionTypeId = workingCopy.getContentSelectionTypeId();
				}
				messageElement.addAttribute("status", status);
				
				ContentSelectionType messageType = new ContentSelectionType(contentSelectionTypeId);
				messageElement.addAttribute("type", messageType.getDisplayText());
				String zoneName = "";
				if ( !tpSelectableMessage.getIsTouchpointLocal() || tpSelectableMessage.isDeliveredToPlaceholder() ) {
					zoneName = tpSelectableMessage.getZone().getFriendlyName();
				}
				messageElement.addAttribute("zone", zoneName);
				messageElement.addAttribute("contenttype", tpSelectableMessage.getContentType().getLocaledString());
				
				ContentObjectZonePriority contentObjectZonePriority =
                        ContentObjectZonePriority.findByContentObjectAndZoneUnique(tpSelectableMessage, tpSelectableMessage.getZone(), tpSelection );
				if (contentObjectZonePriority != null && contentObjectZonePriority.isSuppress()) 
				{
				    messageElement.addAttribute("suppressed", Boolean.TRUE.toString());
				}
                if (contentObjectZonePriority != null && contentObjectZonePriority.isRepeatWithNext())
                {
                    messageElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                }
				Element nameElement = messageElement.addElement("Name");
				nameElement.addText(tpSelectableMessage.getName());
			}
		}
		// - Build the "Selection" element
		List<TouchpointSelection> childSelections = tpSelection.getChildrenOrderByName();
		// Apply the visibility filter to internal request (Does not apply to external request)
		List<TouchpointSelection> visibleSelections = new ArrayList<>();
		for (TouchpointSelection currentSelection: childSelections)
			if (currentSelection.isVisible(requestor))
				visibleSelections.add(currentSelection);
		childSelections = visibleSelections;
		for(TouchpointSelection childSelection : childSelections){
			buildSelectionElm(selectionElm, childSelection);
		}
	}
	
	/**
	 * Build the "DataValue"
     */
	private void buildDataValueElm(Element parentElm, TouchpointSelection tpSelection){
		ParameterGroupInstanceCollection pgInsColl = tpSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection();
		if(pgInsColl != null){
			Set<ParameterGroupInstance> pgInstanceSet = pgInsColl.getParameterGroupInstances();
			Iterator<ParameterGroupInstance> iterator = pgInstanceSet.iterator();
			while(iterator.hasNext()){
				ParameterGroupInstance pgInstance = iterator.next();
				Element dataValueElm = parentElm.addElement("DataValue");
				String value = getDataValue(pgInstance);
				dataValueElm.addAttribute("id", Long.toString(pgInstance.getId()));
				dataValueElm.addText(value);
			}
		}
	}
	
	/**
	 * Return the data value text
     */
	private String getDataValue(ParameterGroupInstance pgInstance){
		String dataValue = null;
		if(pgInstance.getPgItemValue1()!=null){
			dataValue = pgInstance.getPgItemValue1();
		}
		if(pgInstance.getPgItemValue2()!=null){
			dataValue += "|"+pgInstance.getPgItemValue2();
		}
		if(pgInstance.getPgItemValue3()!=null){
			dataValue += "|"+pgInstance.getPgItemValue3();
		}
		if(pgInstance.getPgItemValue4()!=null){
			dataValue += "|"+pgInstance.getPgItemValue4();
		}
		if(pgInstance.getPgItemValue5()!=null){
			dataValue += "|"+pgInstance.getPgItemValue5();
		}
		if(pgInstance.getPgItemValue6()!=null){
			dataValue += "|"+pgInstance.getPgItemValue6();
		}		
		return dataValue;
	}
	
	/**
	 * Build the "Versions" element
     */
	private void buildVersionsElm(Element parentElm, TouchpointSelection tpSelection){
		List<ConfigurableWorkflowActionHistory> actionList = ConfigurableWorkflowActionHistory.findByTPSelectionId(tpSelection.getId());
		ConfigurableWorkflowActionHistory.sortByChronOrder(actionList, false);
		List<ConfigurableWorkflowActionHistory> wcActionList = new ArrayList<>();
		List<ConfigurableWorkflowActionHistory> rlActionList = new ArrayList<>();
		List<ConfigurableWorkflowActionHistory> acActionList = new ArrayList<>();
		boolean stopWcAdd = false;
		boolean stopRlAdd = false;
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			if(tpSelection.isWIP() && !stopWcAdd){	// Working copy
				if(actionHis.getStateCurrent() != ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL){
					wcActionList.add(actionHis);
					continue;					
				}else{
					stopRlAdd = true;
					stopWcAdd = true;					
				}
			}

			if(!tpSelection.isWIP() && !tpSelection.isActive() && !stopRlAdd){	// Released
				if(actionHis.getStateCurrent() != ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL){
					rlActionList.add(actionHis);
					continue;					
				}else{
					stopRlAdd = true;
					stopWcAdd = true;					
				}
			}
			acActionList.add(actionHis);
		}
		
		// Create for 'wc'
		buildVersionElm(parentElm, tpSelection, wcActionList, VERSION_WORKINGCOPY, false);
		
		// Create for 'released'
		buildVersionElm(parentElm, tpSelection, rlActionList, VERSION_RELEASED, false);
		
		// Create for 'active'
		if(!acActionList.isEmpty()){
			List<ConfigurableWorkflowActionHistory> acVersionActionList = new ArrayList<>();
			boolean isFirstActive = true;
			for(int i=0; i<acActionList.size(); i++){
				ConfigurableWorkflowActionHistory acActionHis = acActionList.get(i);
				if(acActionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL){	// Last step
					buildVersionElm(parentElm, tpSelection, acVersionActionList, VERSION_ACTIVE, isFirstActive);
					if(i!=0){
						isFirstActive = false;
					}
				}
				acVersionActionList.add(acActionHis);							
				if(i == (acActionList.size()-1)){
					buildVersionElm(parentElm, tpSelection, acVersionActionList, VERSION_ACTIVE, isFirstActive);	
				}
			}
		}
	}
	
	/**
	 * Build "Version" element, only add proof if it is the first active version
	 */
	private void buildVersionElm(Element parentElm, TouchpointSelection tpSelection, 
			List<ConfigurableWorkflowActionHistory> versionActionList, int versionType, boolean isFirstACVersion){
		if(!versionActionList.isEmpty()){
			// Apply the date filter, filter the active version which is not the first one
			if(!inDateRange(versionActionList.get(0).getActionDate()) && versionType==VERSION_ACTIVE && !isFirstACVersion){
				versionActionList.clear();
				return;
			}
			String status = "";
			String dateRef = "";
			Date actionDate = versionActionList.get(0).getActionDate();
			switch(versionType){
			case VERSION_WORKINGCOPY:
				status = "wc";
				dateRef = "created";
				actionDate = versionActionList.get(versionActionList.size()-1).getActionDate();
				break;
			case VERSION_RELEASED:
				status = "released";
				dateRef = "releasedAsOf";
				// Find the latest released date
				for(ConfigurableWorkflowActionHistory actionHis : versionActionList){
					if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
						actionDate = actionHis.getActionDate();
						break;
					}
				}
				break;
			case VERSION_ACTIVE:
				status = "active";
				dateRef = "activeAsOf";
				break;			
			}			
			Element versionElm = parentElm.addElement("Version");	
			versionElm.addAttribute("status", status);	
			versionElm.addAttribute(dateRef, DateUtil.formatDateForXMLOutput(actionDate));
			// Build "Proofs" element
			// Only add proof if it is wc/released or it is the first active version
			if(versionType!=VERSION_ACTIVE || isFirstACVersion){
				buildProofsElm(versionElm, tpSelection, versionType==VERSION_ACTIVE);
			}
			// Build "ApprovalHistory" element
			ConfigurableWorkflowActionHistory.sortByChronOrder(versionActionList, true);
			buildApprovalHistElm(versionElm, tpSelection, versionActionList);					
			versionActionList.clear();						
		}		
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, TouchpointSelection tpSelection, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			// Apply the date filter
			if(inDateRange(actionHis.getActionDate())){
				Element descriptElm = verHisElm.addElement("Descript");
				if(actionHis.getUserId() != null){
					{
						User user = User.findById(actionHis.getUserId());
						descriptElm.addAttribute("user", user.getName());
					}
				}else{	// Auto approve user
					descriptElm.addAttribute("user", "System");
				}
				if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation
					descriptElm.addAttribute("action", "Activated");
				}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
					descriptElm.addAttribute("action", "Release for Approval");
				}else{
					if(actionHis.getUserId() != null){
						descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
					}else{	// Auto approve user
						descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
					}
				}
				if(actionHis.getAssignedTo() != null){
					User user = User.findById(actionHis.getAssignedTo());
					descriptElm.addAttribute("assignedTo", user.getName());
				}
				if(actionHis.getNotes() != null){
					descriptElm.addAttribute("notes", actionHis.getNotes());
				}
				descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
				switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
					if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
						if(wfAction.getNextAction() != null) {
							descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
						}else{
							// Remove the element if this is an intermediate approval
							verHisElm.remove(descriptElm);
						}
					}else{	// All of
						if(!wfAction.isActionApproved()){
							descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
						}else{
							if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
								descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
							}else{
								// If action is approved but the user is not the last to approve, stay in current state
								List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
								Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                    public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                        Date approvedDate1 = o1.getApprovedDate();
                                        Date approvedDate2 = o2.getApprovedDate();
                                        if (approvedDate1 == null && approvedDate2 == null) {
                                            return 0;
                                        } else if (approvedDate1 == null) {
                                            return 1;
                                        } else if (approvedDate2 == null) {
                                            return -1;
                                        } else {
                                            return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                        }
                                    }
                                });
								if(!approvalDetailsSorted.isEmpty()){	// At least one approver
									for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
										ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
										if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
											continue;
										}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
											descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
											break;
										}else{
											if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
												descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
											}else{
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
											}
											break;
										}
									}
								}
							}
						}
					}					
				}
			}
			}
		}	
	}
	
	/**
	 * Build the "Proofs" element
     */
	private void buildProofsElm(Element parentElm, TouchpointSelection tpSelection, boolean isActiveProof){
		Element proofsElm = parentElm.addElement("Proofs");
		List<MessagepointLocale> langLocales = tpSelection.getDocument().getTouchpointLanguagesAsLocales();
		for (MessagepointLocale langLocale : langLocales) {
			List<Proof> langProofs = Proof.findAllNoneActiveProofsForLang(tpSelection, langLocale.getLanguageCode());
			List<Proof> twoLangProofs = new ArrayList<>();
			for (Proof proof : langProofs) {
				twoLangProofs.add(proof);
				if (twoLangProofs.size()==2) {
					break;
				}
			}
			Proof langActiveProof = tpSelection.getLatestActiveProofForLang(langLocale.getLanguageCode());
			
			if(isActiveProof && langActiveProof != null){
				if(langActiveProof.isComplete() && !langActiveProof.isError() && !langActiveProof.isHasDataError() 
						&& langActiveProof.getOutputPath() != null){
					buildProofElm(proofsElm, tpSelection, langActiveProof, langLocale);
				}
			}
			if(!isActiveProof && !twoLangProofs.isEmpty()){
				for(Proof langProof : twoLangProofs){
					if(langProof.isComplete() && !langProof.isError() && !langProof.isHasDataError() && langProof.getOutputPath() != null){
						buildProofElm(proofsElm, tpSelection, langProof, langLocale);
					}
				}
			}
		}		
	}
	
	/**
	 * Build the "Proof" element
     */
	private void buildProofElm(Element parentElm, TouchpointSelection tpSelection, Proof proof, MessagepointLocale langLocale){
		// Apply the date filter
		if(inDateRange(proof.getRequestDate())){
			Element proofElm = parentElm.addElement("Proof");
			proofElm.addAttribute("id", Long.toString(proof.getId()));
			proofElm.addAttribute("langCode", langLocale.getLanguageCode());
			proofElm.addAttribute("language", langLocale.getLanguageName());
			proofElm.addAttribute("locale", langLocale.getCode());
			if(proof.isStaled()){
				proofElm.addAttribute("stale", "true");
			}
			proofElm.addAttribute("completed", DateUtil.formatDateForXMLOutput(proof.getRequestDate()));
			if(proof.getOutputPath() != null){
				File proofFile = new File(proof.getOutputPath());
				proofElm.addCDATA(proofFile.getAbsolutePath());
			}
		}
	}
	
	/**
	 * Check whether the request date is the fromDate and toDate range
     */
	private boolean inDateRange(Date requestDate){
		if (fromDate != null) {
			if (requestDate.before(fromDate)) {
				return false;
			}
		}
		
		if (toDate != null) {
			if(requestDate.after(toDate)){
				return false;
			}
		}
		return true;		
	}
	
	private List<ContentObject> buildItemsList(Map<String, String> orderByMap, long documentId, long selectionId){
		
		Document document							= Document.findById(documentId);
		TouchpointSelection touchpointSelection 	= TouchpointSelection.findById(selectionId);
		List<ContentObject> returnList = new ArrayList<>();

		
		// Selection Context: Update to match current context
		if ( selectionId > 0 ) {
			TouchpointSelection selection = TouchpointSelection.findById(selectionId);
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(selection.getId()));
			if(UserUtil.getPrincipalUser() != null)
				UserUtil.updateUserContextAttributes(contextAttr);
		}
		
		// Alias init
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();

        // MessagepointCriterion init
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();

        // Join Type init
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
        
        // MessagepointOrder list init
        List<MessagepointOrder> orderList 						= new ArrayList<>();

		/**
		 * First Run: Apply the default filters (Document, Selection and Versions)
		 */
		boolean appliesAlternateTemplate = touchpointSelection != null && touchpointSelection.getAlternateLayout() != null;
		List<Long> zoneIdList = new ArrayList<>();
		zoneIdList = AsyncListTableController.getZoneList(documentId, 0, 0, false);
		// Alternate layout visibility filter
		if ( appliesAlternateTemplate ) {
			User principal = UserUtil.getPrincipalUser();
			List<Long> nonVisibleZones = new ArrayList<>();
			for ( Long currentZoneId : zoneIdList ) {
				Zone primaryZone = Zone.findById(currentZoneId);
				Zone targetZone = touchpointSelection.getAlternateLayout().findZoneByParent(primaryZone);
				if ( targetZone == null || !targetZone.getWorkgroups().contains(requestor.getWorkgroup()) || !targetZone.isEnabled() )
					nonVisibleZones.add(currentZoneId);
			}
			zoneIdList.removeAll(nonVisibleZones);
		}

		// Version filter
		List<Long> msgInstIds = ContentObject.findUniqueVisibleIdsOfMostRecentCopies(zoneIdList, ContentObject.OBJECT_TYPE_MESSAGE, documentId, null, requestor);
		if (msgInstIds.isEmpty()) {
			msgInstIds.add((long) 0);
		}

		if ( document.getIsOmniChannel() ) {
			firstLevelCriterionList.add( MessagepointRestrictions.or(
					MessagepointRestrictions.eq("channelContextId", UserUtil.getCurrentChannelContext().getId()),
					MessagepointRestrictions.isNull("channelContextId")
			) );
		}

		// Variant filter (The variant message can be shared from its ancestor selections)
		if ( document.isEnabledForVariation() ) {
			Set<Long> tpSelectionIds = new HashSet<>();
			TouchpointSelection parentSelection = touchpointSelection;
			while (parentSelection != null) {
				tpSelectionIds.add(parentSelection.getId());
				parentSelection = parentSelection.getParent();
			}

			if (tpSelectionIds.isEmpty())
				tpSelectionIds.add((long) 0);
			if(document.getMasterTouchpointSelection() == touchpointSelection ){
				firstLevelCriterionList.add(MessagepointRestrictions.isNull("owningTouchpointSelection"));
			}else{
				firstLevelCriterionList.add(MessagepointRestrictions.eq("owningTouchpointSelection.id", touchpointSelection.getId()));
			}
		}

		// SHARED CANVAS
		firstLevelCriterionList.add(MessagepointRestrictions.ne("contentType.id", ContentType.SHARED_FREEFORM));

		/**
		 * Second Run: Apply the left filters (Section and zone select, search and Status)
		 */

		// Section and zone select
		firstLevelJoinAlias.put("zone", "zone");
		firstLevelCriterionList.add(MessagepointRestrictions.in("zone.id", zoneIdList));

		this.addTableColumnsSort(orderByMap, orderList);

		// Sort
		PostQueryHandler postHandler = null;

		ServiceExecutionContext context = HibernatePaginationService.createContext(ContentObject.class, msgInstIds, firstLevelCriterionList, null, firstLevelJoinAlias, null, secondLevelJoinType, 1, 0, orderList, null, postHandler);
		Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
		paginationService.execute(context);
		PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
		List<?> list = serviceResponse.getPage().getList();


		for (Object o : list) {
			if(o instanceof ContentObject) {
				ContentObject contentObject = (ContentObject) o;
				if (contentObject.hasActiveData() || contentObject.hasWorkingData())
					returnList.add(contentObject);
			}
		}
        return returnList;
	}

	/**
	 * Add the sort criterion from the list table plug-in to the order list
     */
	private void addTableColumnsSort(Map<String, String> orderedByMap, List<MessagepointOrder> orderList){
		Set<String> keySet = orderedByMap.keySet();
		for(String key : keySet){
			String value = orderedByMap.get(key);
			String sortField = "";

			if(key.equals("name")){	// Sort by message name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name";
			}

			if(!sortField.isEmpty()){
				// Add to order list
				if(value.equals("asc")){
					orderList.add(MessagepointOrder.asc(sortField));
				}else{
					orderList.add(MessagepointOrder.desc(sortField));
				}
			}
		}
	}


	public void validate(ServiceExecutionContext context) {
	}
	
	/**
	 * Create the context for the internal triggered audit report generation
     */
	public static ServiceExecutionContext createContext(User requestor, Date fromDate, Date toDate, 
			long reportTimestamp, Document document){
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportTouchpointToXMLServiceRequest request = new ExportTouchpointToXMLServiceRequest();
		request.setRequestor(requestor);
		request.setFromDate(fromDate);
		request.setToDate(toDate);	
		request.setReportTimestamp(reportTimestamp);
		request.setDocument(document);
		
		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;		
	}
	
}
