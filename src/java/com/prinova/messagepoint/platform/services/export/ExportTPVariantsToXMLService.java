package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentFactory;
import org.dom4j.Element;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;


public class ExportTPVariantsToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportTPVariantsToXMLService";

	private static final Log log = LogUtil.getLog(ExportTPVariantsToXMLService.class);
    private User requestor;
	private boolean includeImagePathOnly = true;
	private final List<String> originalImageFiles = new ArrayList<>();
	private org.dom4j.Document			document;
	private Document touchpoint;
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportMessagepointObjectToXMLServiceRequest request = (ExportMessagepointObjectToXMLServiceRequest) context.getRequest();
	    	this.requestor = request.getRequestor();
	    	this.touchpoint = Document.findById(request.getTargeObjectId());
            if(touchpoint == null)
            	return;
	    	String exportName = touchpoint.getName();

	    	DocumentFactory docFactory = DocumentFactory.getInstance(); 
			this.document = docFactory.createDocument();
			this.document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);		
			Element rootElement = docFactory.createElement("MpBulkUploadDefinition");
			this.document.setRootElement(rootElement);		
			
			generateXML(rootElement);
			String filePath = ExportUtil.saveMessagepointObjectExportXMLToFile(document, requestor, ExportUtil.ExportType.TPVARIANTS, request.getExportId(), exportName);
			context.getResponse().setResultValueBean(request.getExportId());
			((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);

		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportTPVariantsToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}
	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}

	private void generateXML(Element rootElement) throws Exception 
	{
		
		MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        rootElement.addAttribute("version", version);
        rootElement.addAttribute("build", buildVersion);
        rootElement.addAttribute("requestedby", requestor.getName());
        rootElement.addAttribute("requestdate", DateUtil.formatDateForXMLOutput(DateUtil.now()));
        rootElement.addAttribute("name", this.touchpoint.getName());
        rootElement.addAttribute("guid", this.touchpoint.getGuid());
		
		Element topElm = rootElement.addElement("TouchpointVariants");
		List<ParameterGroupTreeNode> ChildNodes = touchpoint.getMasterTouchpointSelection().getParameterGroupTreeNode().getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
	        for (ParameterGroupTreeNode childNode : ChildNodes) {
				if(childNode == null)
	        		continue;
	        	buildNodeElm(topElm, childNode);
	        }
		}
	}
	
	/**
	 * Build the "Node" element
     */
	private void buildNodeElm(Element rootElement, ParameterGroupTreeNode node) throws Exception{
		Element variantElm = rootElement.addElement("Variant");
		variantElm.addAttribute("guid", node.getGuid());
		variantElm.addAttribute("dna", node.getDna());
		variantElm.addAttribute("id", String.valueOf(node.getId()));
		variantElm.addAttribute("refid", String.valueOf(node.getParentNode()!= null?node.getParentNode().getId():0L));
		Element nameElm = variantElm.addElement("Name");
		nameElm.addText(node.getName());
		
		Element selectionDataElm = variantElm.addElement("SelectionData");
		if(node.getParameterGroupInstanceCollection() != null)
			ExportTPVariantsToExcelService.createDataValueTag(node.getParameterGroupInstanceCollection(), selectionDataElm);
		
		if (node.getChildren() != null && !node.getChildren().isEmpty()) {
			for (ParameterGroupTreeNode childNode : node.getChildren()){
				buildNodeElm(rootElement, childNode);
			}
		}
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);

        context.setRequest(request);

        ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
	
	public boolean isIncludeImagePathOnly() {
		return includeImagePathOnly;
	}
	public void setIncludeImagePathOnly(boolean includeImagePathOnly) {
		this.includeImagePathOnly = includeImagePathOnly;
	}
	public List<String> getOriginalImageFiles() {
		return originalImageFiles;
	}
	public org.dom4j.Document getDocument() {
		return document;
	}
	public void setDocument(org.dom4j.Document document) {
		this.document = document;
	}
	public Document getTouchpoint() {
		return touchpoint;
	}
	public void setTouchpoint(Document touchpoint) {
		this.touchpoint = touchpoint;
	}
}
