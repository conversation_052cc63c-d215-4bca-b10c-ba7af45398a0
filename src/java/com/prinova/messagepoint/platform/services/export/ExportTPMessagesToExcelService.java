package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.export.utils.ExcelExportUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagepointException;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameIgnoreCaseComparator;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.common.ContentSelectionType;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;


public class ExportTPMessagesToExcelService extends AbstractService {

    public static final String SERVICE_NAME = "export.ExportTPMessagesToExcelService";

    private static final Log log = LogUtil.getLog(ExportTPMessagesToExcelService.class.getName());
    private User requestor = null;
    private boolean includeAllMessages = true;
    private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
    public void execute(ServiceExecutionContext context) {
        try
        {
            validate(context);
            if (hasValidationError(context))
            {
                return;
            }

            ExportMessagepointObjectToXMLServiceRequest request = (ExportMessagepointObjectToXMLServiceRequest) context.getRequest();

            Document document = Document.findById(request.getTargeObjectId());
            this.requestor = request.getRequestor();
            this.includeAllMessages = request.getExportOptions().isIncludeMessages();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            //outputDirectory.mkdirs();

            File file = new File(outputDirectory + File.separator + ExportUtil.EXCEL_EXPORT_TEMPLATE_TP_MESSAGES);
            FileInputStream fileStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(fileStream);

            generateWorkbook(document, workbook, requestor);

            String exportName = document.getName();
            if (exportName != null)
                exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");

            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook,
                    requestor,
                    ExportUtil.ExportType.TPMESSAGES,
                    request.getExportId(),
                    exportName);

            ((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
            if (ex.getCause() != null)
            {
                if (ex.getCause().getMessage() != null)
                {
                    if (ex.getCause().getMessage().equalsIgnoreCase("#RequiredValuesAreEmpty#"))
                    {
                        this.getResponse(context).addErrorMessage(
                                "error.message.content.library.imagefile.not.compatible",
                                "error.message.content.library.imagefile.not.compatible",
                                new String[] {ex.getCause().getMessage()}, "", null);
                        this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                        return;
                    }
                }
            }
            log.error(" unexpected exception when invoking ExportTPMessagesToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateWorkbook( Document document, XSSFWorkbook workbook, User requestor ) throws Exception
    {
        XSSFSheet sheet = workbook.getSheet("messages");

        List<MessagepointLocale> languages = document.getTouchpointLanguagesAsLocales();

        // need to generate Image ref sheet, Selection ref sheet and available zone list(excluding multipart zones)

        /*
         *  Possible ImageLibrary List Ref Sheet
         */
        XSSFSheet ref_sheet = workbook.getSheet("images");
        List<ContentObject> allImages = new ArrayList<>();
        int counter = 0;
        String[] imageNameList = null;
        long imageDropdownStrLength = 13; // total length of Empty Uploaded
        for(ContentObject contentObject: ContentObject.findGlobalSmartTexts(true)){
            if(!contentObject.isVariableContentEnabled()){
                if(!contentObject.getContentObjectAssociations().isEmpty()){
                    allImages.add(contentObject);
                    imageDropdownStrLength += contentObject.getName().length();
                }
            }else{
                if(!contentObject.getContentObjectAssociations().isEmpty()
                        && contentObject.getDocuments().contains(document) && !allImages.contains(contentObject)){
                    allImages.add(contentObject);
                    imageDropdownStrLength += contentObject.getName().length();
                }
            }
        }
        Collections.sort(allImages, new IdentifiableMessagepointModelNameIgnoreCaseComparator());

        int imageNameListCount = allImages.size()+2;
        if(imageDropdownStrLength > 255)
            imageNameListCount = 2;
        imageNameList = new String[imageNameListCount];

        imageNameList[0] = "Empty";
        imageNameList[1] = "Uploaded";
        Row row0 = ref_sheet.createRow(0);
        row0.createCell(0).setCellValue(imageNameList[0]);
        row0.createCell(1).setCellValue(0);
        Row row1 = ref_sheet.createRow(1);
        row1.createCell(0).setCellValue(imageNameList[1]);
        row1.createCell(1).setCellValue(0);

        counter = 2;
        for(ContentObject image: allImages){
            if(imageDropdownStrLength < 255){
                imageNameList[counter] = image.getName();
            }
            Row dataRow = ref_sheet.createRow(counter);
            dataRow.createCell(0).setCellValue(image.getName());
            dataRow.createCell(1).setCellValue(image.getId());
            counter++;
        }

        /*
         * ContentSelectionType
         */
        List<ContentSelectionType> contentSelectionTypes = ContentSelectionType.listAll();
        contentSelectionTypes.remove(new ContentSelectionType(ContentSelectionType.ID_EMBEDDED_SELECTABLE));
        contentSelectionTypes.remove(new ContentSelectionType(ContentSelectionType.ID_CONTENT_LIBRARY_SELECTABLE));
        contentSelectionTypes.remove(new ContentSelectionType(ContentSelectionType.ID_GLOBAL));
        String[] contentSelectorTypeNameList = new String[contentSelectionTypes.size()];
        int contentSelectorCounter = 0;
        for(ContentSelectionType type: contentSelectionTypes){
            contentSelectorTypeNameList[contentSelectorCounter] = type.getDisplayText();
            contentSelectorCounter++;
        }
        /*
         * ContentType
         */
        String[] contentTypeNameList = new String[2];
        contentTypeNameList[0] = ApplicationUtil.getMessage(ContentType.findById(ContentType.TEXT).getName());
        contentTypeNameList[1] = ApplicationUtil.getMessage(ContentType.findById(ContentType.GRAPHIC).getName());

        /*
         *  Possible Selector List Ref Sheet
         */
        List<ParameterGroup> selectableParameters = new ArrayList<>();
        for(ParameterGroup pgGroup:ParameterGroup.findAllIncludingParameters()){
            if(pgGroup.isVisibleToDocument(document)){
                selectableParameters.add(pgGroup);
            }
        }
        XSSFSheet selectors_sheet = null;
        selectors_sheet = workbook.getSheet("selectors");
        int selectors_counter = 0;
        String[] selectorNameList = new String[selectableParameters.size()];
//        long selectorStrLength = 0;
        for(ParameterGroup selector: selectableParameters){

            Row dataRow = selectors_sheet.createRow(selectors_counter);
            dataRow.createCell(0).setCellValue(selector.getName());
            dataRow.createCell(1).setCellValue(selector.getId());
            selectorNameList[selectors_counter] = Long.valueOf(selector.getId()).toString();
            selectors_counter++;
//            selectorStrLength += Long.valueOf(selector.getId()).toString().length();
        }

        /*
         * DeliveryType
         */
        String[] deliveryTypeNameList = new String[2];
        deliveryTypeNameList[0] = ApplicationUtil.getMessage("page.label.attachment.delivery.type.mandatory");
        deliveryTypeNameList[1] = ApplicationUtil.getMessage("page.label.attachment.delivery.type.optional");

        /*
         *  Possible Zone List zones Sheet
         */
        List<Zone> zoneList = new ArrayList<>();
        for(Zone zone:Zone.findByDocumentIdOrderByName(document.getId())){
            if(!zone.isMultipart() && zone.isEnabled()){
                zoneList.add(zone);
            }
        }
        if(zoneList.isEmpty()){
            // need to display error message
            throw new MessagepointException("Zones", new Throwable("#RequiredValuesAreEmpty#"));
        }
        XSSFSheet zones_sheet = null;
        zones_sheet = workbook.getSheet("zones");
        int zones_counter = 0;
        String[] zoneNameList = new String[zoneList.size()];
//        long zoneNameStrLength = 0;
        for(Zone zone: zoneList){

            Row dataRow = zones_sheet.createRow(zones_counter);
            dataRow.createCell(0).setCellValue(zone.getFriendlyName());
            dataRow.createCell(1).setCellValue(zone.getId());
            zoneNameList[zones_counter] = Long.valueOf(zone.getId()).toString();
            zones_counter++;
//            zoneNameStrLength += Long.valueOf(zone.getId()).toString().length();
        }

        /*
         *  Main message list sheet
         */
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();

        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));

        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(document.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(document.getGuid());


        int rowCounter = 8;
        int headerRowNum = 7;

        List<Long> msgInstIds = new ArrayList<>();
        // TODO msgInstIds = ContentObject.findAllVisibleIdsOfMostRecentCopiesWUser(getZoneIds(zoneList), null);

        CellRangeAddressList cellRangeMessageTypeList = new CellRangeAddressList();
        CellRangeAddressList cellRangeContentTypeList = new CellRangeAddressList();
        CellRangeAddressList cellRangeSelectorList = new CellRangeAddressList();
        CellRangeAddressList cellRangeDeliveryTypeList = new CellRangeAddressList();
        CellRangeAddressList cellRangeZoneList = new CellRangeAddressList();
        CellRangeAddressList cellRangeContentList = new CellRangeAddressList();
        if (msgInstIds != null && !msgInstIds.isEmpty() && this.includeAllMessages)
        {
            for (Long contentObjectId : msgInstIds)
            {
                ContentObject msg = ContentObject.findById(contentObjectId);
                if(msg.getOwningTouchpointSelection() != null)
                    continue;
                rowCounter = getRowsForMessage(sheet, rowCounter, document, msg, languages, cellRangeMessageTypeList, cellRangeContentTypeList, cellRangeSelectorList, cellRangeDeliveryTypeList, cellRangeZoneList, cellRangeContentList, counter);
            }
        }
        generateChildPgtnRow(sheet, rowCounter, document, null, languages, cellRangeMessageTypeList, cellRangeContentTypeList, cellRangeSelectorList, cellRangeDeliveryTypeList, cellRangeZoneList, cellRangeContentList, counter);

        DataValidationHelper validationHelper = new XSSFDataValidationHelper(sheet);

        DataValidationConstraint constraintType = validationHelper.createExplicitListConstraint(contentSelectorTypeNameList);
        DataValidation dataValidationType = validationHelper.createValidation(constraintType, cellRangeMessageTypeList);
        dataValidationType.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidationType);

        DataValidationConstraint constraintContentType = validationHelper.createExplicitListConstraint(contentTypeNameList);
        DataValidation dataValidationContentType = validationHelper.createValidation(constraintContentType, cellRangeContentTypeList);
        dataValidationContentType.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidationContentType);

        if (zoneNameList.length > 0 )
        {
            DataValidationConstraint constraintZone = validationHelper.createExplicitListConstraint(zoneNameList);
            DataValidation dataValidationZone = validationHelper.createValidation(constraintZone, cellRangeZoneList);
            dataValidationZone.setSuppressDropDownArrow(true);
            sheet.addValidationData(dataValidationZone);
        }

        if (selectorNameList.length > 0 )
        {
            DataValidationConstraint constraintSelector =validationHelper.createExplicitListConstraint(selectorNameList);
            DataValidation dataValidationSelector = validationHelper.createValidation(constraintSelector, cellRangeSelectorList);
            dataValidationSelector.setSuppressDropDownArrow(true);
            sheet.addValidationData(dataValidationSelector);
        }

        DataValidationConstraint constraintDeliveryType = validationHelper.createExplicitListConstraint(deliveryTypeNameList);
        DataValidation dataValidationDeliveryType = validationHelper.createValidation(constraintDeliveryType, cellRangeDeliveryTypeList);
        dataValidationDeliveryType.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidationDeliveryType);

        DataValidationConstraint constraintImage = validationHelper.createExplicitListConstraint(imageNameList);
        DataValidation dataValidationContent = validationHelper.createValidation(constraintImage, cellRangeContentList);
        dataValidationContent.setErrorStyle(DataValidation.ErrorStyle.STOP);
        dataValidationContent.setSuppressDropDownArrow(true);
        sheet.addValidationData(dataValidationContent);

        //for(int i = (11 + (languageCodes.size() * 3)); i < sheet.getRow(headerRowNum).getLastCellNum(); i++){
         //   sheet.setColumnHidden(i, true);
        //}

        int rowColumn = 11;

        ExcelExportUtils.setLanguageHeaderLabels(headerRowNum, rowColumn, sheet, languages, "Content");

        sheet.setColumnHidden(0, true);
        sheet.setColumnHidden(3, true);

    }

    private void generateChildPgtnRow(XSSFSheet sheet, int rowCounter, Document document, ContentObject msgInst,
                                      List<MessagepointLocale> languages,
                                      CellRangeAddressList cellRangeTypeList,
                                      CellRangeAddressList cellRangeContentTypeList,
                                      CellRangeAddressList cellRangeSelectorList,
                                      CellRangeAddressList cellRangeDeliveryTypeList,
                                      CellRangeAddressList cellRangeZoneList,
                                      CellRangeAddressList cellRangeContentList,
                                      int imageRowCounter) throws Exception {

        Row dataRow = sheet.createRow(rowCounter);

        if(msgInst != null){
            dataRow.createCell(0).setCellValue(msgInst.getGuid());
            dataRow.createCell(1).setCellValue(msgInst.getId());
            Cell typeCell = dataRow.createCell(2);
            typeCell.setCellValue(new ContentSelectionType(msgInst).getDisplayText());
            //cellRangeTypeList.addCellRangeAddress(typeCell.getRowIndex(), typeCell.getColumnIndex(), typeCell.getRowIndex(), typeCell.getColumnIndex());
            dataRow.createCell(3).setCellValue("");

            org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
            xmlDoc.setXMLEncoding("UTF-8");
            dataRow.createCell(4).setCellValue(ApplicationUtil.getMessage(msgInst.getContentTypeName()));

            if(!msgInst.isStructuredContentEnabled() && msgInst.getParameterGroup() != null){ // selectable
                dataRow.createCell(5).setCellValue(msgInst.getParameterGroup().getId());
            }else{
                dataRow.createCell(5).setCellValue("");
            }
            dataRow.createCell(6).setCellValue(msgInst.getDeliveryTypeText());
            dataRow.createCell(7).setCellValue(msgInst.getZone() != null ? msgInst.getZone().getId() : 0);
            dataRow.createCell(8).setCellValue(msgInst.getName());
            dataRow.createCell(9).setCellValue(msgInst.getMetatags());
            dataRow.createCell(10).setCellValue(msgInst.getDescription());
        }else{
            dataRow.createCell(0).setCellValue("");
            dataRow.createCell(1).setCellValue(1);

            // need to set contentSelectionType drop down menu
            Cell typeCell = dataRow.createCell(2);
            if (document != null && document.isEnabledForVariation()) {
                typeCell.setCellValue(new ContentSelectionType(ContentSelectionType.ID_TOUCHPOINT_CONTENT_SELECTABLE).getDisplayText());
            } else
                typeCell.setCellValue(new ContentSelectionType(ContentSelectionType.ID_REGULAR).getDisplayText());
            cellRangeTypeList.addCellRangeAddress(typeCell.getRowIndex(), typeCell.getColumnIndex(), typeCell.getRowIndex(), typeCell.getColumnIndex());

            dataRow.createCell(3).setCellValue("");
            // Content Type
            Cell contentContentTypeCell = dataRow.createCell(4);
            cellRangeContentTypeList.addCellRangeAddress(contentContentTypeCell.getRowIndex(), contentContentTypeCell.getColumnIndex(), contentContentTypeCell.getRowIndex(), contentContentTypeCell.getColumnIndex());
            contentContentTypeCell.setCellValue("Text");
            // selector
            Cell selectorCell = dataRow.createCell(5);
            selectorCell.setCellValue("");
            cellRangeSelectorList.addCellRangeAddress(selectorCell.getRowIndex(), selectorCell.getColumnIndex(), selectorCell.getRowIndex(), selectorCell.getColumnIndex());
            // delivery type
            Cell deliveryTypeCell = dataRow.createCell(6);
            cellRangeDeliveryTypeList.addCellRangeAddress(deliveryTypeCell.getRowIndex(), deliveryTypeCell.getColumnIndex(), deliveryTypeCell.getRowIndex(), deliveryTypeCell.getColumnIndex());
            deliveryTypeCell.setCellValue("Mandatory");
            // zone
            Cell zoneCell = dataRow.createCell(7);
            zoneCell.setCellValue("");
            cellRangeZoneList.addCellRangeAddress(zoneCell.getRowIndex(), zoneCell.getColumnIndex(), zoneCell.getRowIndex(), zoneCell.getColumnIndex());
            dataRow.createCell(8).setCellValue("Message Name");
            dataRow.createCell(9).setCellValue("");
            dataRow.createCell(10).setCellValue("");
        }
        sheet.setColumnWidth(1, 9000);
        int t = 0;
        char[] alphabet = "abcdefghijklmnopqrstuvwxyz".toUpperCase().toCharArray();
        for(MessagepointLocale locale:languages){
            int cellColumn = t+11;
            Cell cell = dataRow.createCell(cellColumn);
            int module = cellColumn % 3;

            if(module == 2)
                cellRangeContentList.addCellRangeAddress(cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());

            String cellValue = "Empty";
            TouchpointContentSelectionViewWrapper viewWrapper = null;
            if(msgInst != null){
                if(msgInst!=null && msgInst.getIsGraphic()){
                    ContentObjectAssociation ca = null;
                    if(!msgInst.isStructuredContentEnabled() && !msgInst.isDynamicVariantEnabled()){
                        ca = ContentObjectAssociation.findParentByContentObjectAndParameters(msgInst, msgInst.getFocusOnDataType(), null, locale, null);
                        if(ca != null) {
                            if(ca.getTypeId() == ContentAssociationType.ID_OWNS){
                                if(ca.getReferencingImageLibrary() != null) {
                                    cellValue = ca.getReferencingImageLibrary().getName();
                                }else if( ca.getContent() != null && ca.getContent().getImageName() != null){
                                    cellValue = "Uploaded";
                                }else{
                                    cellValue = "Empty";
                                }
                            }else if(ca.getTypeId() == ContentAssociationType.ID_REFERENCES){
                                cellValue = "Inheriting from parent";
                            }else{
                                cellValue = "Empty";
                            }
                        }
                    }else{
                        ca = null; // TODO ContentObjectAssociation.findMasterContentAssociationByMessageInstanceId(msgInst.getId());
                        if(ca != null) {
                            /** TODO
                             TouchpointContentSelection contentSelection = new TouchpointContentSelection(ca);
                             // TODO viewWrapper = new TouchpointContentSelectionViewWrapper(contentSelection, requestor);
                             ContentVO content = viewWrapper.getContentVO().getLangContentMap().get(languageCode.getLanguageCode());
                             if(ca.getTypeId() == ContentAssociationType.ID_OWNS){
                             if(content.getImageLibraryId() > 0){
                             cellValue = ContentLibrary.findById(content.getImageLibraryId()).getName();
                             }else if( content != null && content.getContentId() > 0 && content.getImageName() != null){
                             cellValue = "Uploaded";
                             }else{
                             cellValue = "Empty";
                             }
                             }else if(ca.getTypeId() == ContentAssociationType.ID_REFERENCES){
                             cellValue = "Inheriting from parent";
                             }else{
                             cellValue = "Empty";
                             }
                             **/
                        }
                    }
                }else if(msgInst.isTextContent()){
                    ContentObjectAssociation ca =  null;
                    if(msgInst.isStructuredContentEnabled())
                        ca = null; // TODO ContentObjectAssociation.findMasterContentAssociationByMessageInstanceId(msgInst.getId());
                    else
                        ca = null; // TODO ContentObjectAssociation.findParentContentAssociation4SelectionByLanguage(msgInst.getId(), languageCode, -1, null, ContentObject.class);
                    if(ca != null) {
                        if(msgInst.isStructuredContentEnabled()){
                            // TODO TouchpointContentSelection contentSelection = TouchpointContentSelection.findById(ca.getId());
                            // viewWrapper = new TouchpointContentSelectionViewWrapper(contentSelection, requestor);
                            cellValue = viewWrapper.getContentVO().getLangContentMap().get(locale.getLanguageCode()).getContent();
                        }else{
                            if(ca.getContent() != null)
                                cellValue = ca.getContent().getEncodedContent();
                        }
                    }
                }else{
                    cellValue = "Empty";
                }
            }
            cell.setCellValue(cellValue);
            t++;
            cellColumn++;
            Cell idCell = dataRow.createCell(cellColumn);
            idCell.setCellValue("0");
            char columnHeader = alphabet[cellColumn-1];
            // ***since row starts from 0 the formular rowCounter need to be increased 1
            String formular = "vlookup(" + columnHeader + (rowCounter + 1) + ", images!$A$1:$B$" + imageRowCounter + ", 2, FALSE)";
            idCell.setCellFormula(formular);

            sheet.setColumnHidden(idCell.getColumnIndex(), true);
            t++;
            cellColumn++;
            Cell lanuguageCell = dataRow.createCell(cellColumn);
            lanuguageCell.setCellValue(locale.getLanguageCode());
            t++;
        }
    }

    public List<String> getOriginalImageFiles()
    {
        return originalImageFiles;
    }

    @Override
    public void validate(ServiceExecutionContext context)
    {
    }

    public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor)
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    public static void createDataValueTag(
            ParameterGroupInstanceCollection pgiCollection,
            Element dataValuesElement)
            throws Exception
    {
        Element dataValueElement = dataValuesElement.addElement("DataValue");
        dataValueElement.addAttribute("id", Long.toString(pgiCollection.getId()));

        List<String> pgiPathValues = new ArrayList<>();
        createValueTag(pgiCollection.getId(), dataValueElement, pgiPathValues, 1, false);
        if(dataValueElement.getText().isEmpty()){
            StringBuilder valueString = new StringBuilder();
            for (String pathValue : pgiPathValues) {
                if (valueString.length() > 0) {
                    valueString.append(";");
                }
                valueString.append(pathValue);
            }
        }
    }

    private static boolean createValueTag(
            long pgiCollectionId,
            Element dataValueElement,
            List<String> pgiPathValues,
            int level,
            boolean secondValueTag)
            throws Exception
    {
        List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
        boolean createValueTag = false;
        List<String> valueTagValues = new ArrayList<>();
        if (IthItemValues != null && !IthItemValues.isEmpty()) {
            for (String IthItemValue : IthItemValues) {
                List<String> pathValues = new ArrayList<>(pgiPathValues);
                pathValues.add(IthItemValue);
                List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
                if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
                    secondValueTag = createValueTag(pgiCollectionId, dataValueElement, pathValues, level + 1, secondValueTag);
                } else {
                    createValueTag = true;
                    valueTagValues.add(IthItemValue);
                }
            }
            if (createValueTag) {
                Element valueElement = dataValueElement.addElement("Value");
                StringBuilder valueString = new StringBuilder();

                if (secondValueTag)
                    valueString = new StringBuilder("|");
                else
                    secondValueTag = true;

                if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
                    for (String pathValue : pgiPathValues) {
                        if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
                            valueString.append(";");
                        }
                        valueString.append(pathValue);
                    }
                }
                if (valueTagValues != null && !valueTagValues.isEmpty()) {
                    if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
                        valueString.append(";");
                    }
                    for (String valueTagValue : valueTagValues) {
                        valueString.append(valueTagValue).append(",");
                    }
                    if (valueString.toString().endsWith(",")) {
                        valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
                    }
                }
                valueElement.addText(valueString.toString());
            }
        }
        return secondValueTag;
    }

    public static String createValueCell(ParameterGroupInstanceCollection pgiCollection)
            throws Exception
    {
        List<String> pgiPathValues = new ArrayList<>();
        String returnValues = "";
        returnValues = createValue(returnValues, pgiCollection.getId(), pgiPathValues, 1);
        return returnValues;
    }

    private static String createValue(String returnValue, long pgiCollectionId, List<String> pgiPathValues, int level)throws Exception{
        List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
        boolean createValueTag = false;
        List<String> valueTagValues = new ArrayList<>();
        StringBuilder valueString = new StringBuilder();

        if (IthItemValues != null && !IthItemValues.isEmpty()) {
            for (String IthItemValue : IthItemValues) {

                List<String> pathValues = new ArrayList<>(pgiPathValues);
                pathValues.add(IthItemValue);
                List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
                if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
                    childIthItemValues.add(createValue(returnValue, pgiCollectionId, pathValues, level + 1));
                } else {
                    createValueTag = true;
                    valueTagValues.add(IthItemValue);
                }
            }
            if (createValueTag) {
                if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
                    for (String pathValue : pgiPathValues) {
                        if (valueString.length() > 0) {
                            valueString.append(";");
                        }
                        valueString.append(pathValue);
                    }
                }
                if (valueTagValues != null && !valueTagValues.isEmpty()) {
                    if (valueString.length() > 0) {
                        valueString.append(";");
                    }
                    for (String valueTagValue : valueTagValues) {
                        valueString.append(valueTagValue).append(",");
                    }
                    if (valueString.toString().endsWith(",")) {
                        valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
                    }
                }
                //valueCell.setCellValue(valueString);

                //pgiPathValues.add(valueString);
            }
            else{
                if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
                    for (String pathValue : pgiPathValues) {
                        if (valueString.length() > 0) {
                            valueString.append(";");
                        }
                        valueString.append(pathValue);
                    }
                }
            }
        }
        returnValue = returnValue.concat(valueString.toString());
        return returnValue;
    }

    public int getRowsForMessage(XSSFSheet sheet, int rowCounter,
                                 Document document, ContentObject msg,
                                 List<MessagepointLocale> languages,
                                 CellRangeAddressList cellRangeMessageTypeList,
                                 CellRangeAddressList cellRangeContentTypeList,
                                 CellRangeAddressList cellRangeSelectorList,
                                 CellRangeAddressList cellRangeDeliveryTypeList,
                                 CellRangeAddressList cellRangeZoneList,
                                 CellRangeAddressList cellRangeContentList,
                                 int counter) throws Exception {
        generateChildPgtnRow(sheet, rowCounter, document, msg, languages, cellRangeMessageTypeList, cellRangeContentTypeList, cellRangeSelectorList, cellRangeDeliveryTypeList, cellRangeZoneList, cellRangeContentList, counter);
        rowCounter++;



        return rowCounter;
    }

    private List<Long> getZoneIds(List<Zone> zoneList){
        List<Long> zoneIds = new ArrayList<>();

        for(Zone zone : zoneList){
            zoneIds.add(zone.getId());
        }
        return zoneIds;
    }

    public boolean isIncludeAllMessages() {
        return includeAllMessages;
    }

    public void setIncludeAllMessages(boolean includeAllMessages) {
        this.includeAllMessages = includeAllMessages;
    }
}
