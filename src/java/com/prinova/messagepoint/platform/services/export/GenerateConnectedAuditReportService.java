package com.prinova.messagepoint.platform.services.export;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;


public class GenerateConnectedAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateConnectedAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateConnectedAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateAuditReportServiceRequest request = (GenerateAuditReportServiceRequest) context.getRequest();

			ConnectedAuditReportRunner messageExportRunner = new ConnectedAuditReportRunner(
                    request.getAuditReportId(),
                    request.getList(),
                    request.getRequestor());
	   		MessagePointRunnableUtil.startThread(messageExportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateConnectedAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class ConnectedAuditReportRunner extends MessagePointRunnable {

		private long auditReportId;
		private List<Long> list = new ArrayList<>();
		private User requestor;
		
		public ConnectedAuditReportRunner(long auditReportId, 
				List<Long> list,
				User requestor) {

			this.auditReportId = auditReportId;
			this.requestor = requestor;
			this.list.addAll(list);
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			try {
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
				// String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor
		    	ServiceExecutionContext exportServiceContext = ExportConnectedToXMLService.createContext(this.auditReportId, this.list, getRequestor());

		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportConnectedToXMLService.SERVICE_NAME, ExportConnectedToXMLService.class);
		    	exportService.execute(exportServiceContext);

		    	String xmlFilePath = ((ExportToXMLServiceResponse)exportServiceContext.getResponse()).getFilePath();
		    	
				// ******************************************
				// ********* Generate XSLT ******************
				// ******************************************

		    	int auditReportType = AuditReportType.ID_CONNECTED_REPORT;
		    	String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, getRequestor(), auditReportType, 0);
		    	
				// *********************************************************
				// ********* Update the AuditReport object *****************
				// *********************************************************

				ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(getAuditReportId(), 
																																	auditReportType,
																																	xmlFilePath, 
																																	xhtmlFilePath);
				Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				updateReportService.execute(updateReportServiceContext);
		    	
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Message Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
			}
		}

		public long getAuditReportId() {
			return auditReportId;
		}

		public User getRequestor() {
			return requestor;
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(long auditReportId, 
														List<Long> list, 
														User requestor) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateAuditReportServiceRequest request = new GenerateAuditReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setRequestor(requestor);
		request.setList(list);
		context.setRequest(request);
		
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}