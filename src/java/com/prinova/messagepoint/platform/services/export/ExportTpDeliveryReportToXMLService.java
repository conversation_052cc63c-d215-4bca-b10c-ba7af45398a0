package com.prinova.messagepoint.platform.services.export;

import java.io.File;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.TpDeliveryReport;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class ExportTpDeliveryReportToXMLService extends AbstractService{
	public static final String SERVICE_NAME = "export.ExportTpDeliveryReportToXMLService";
	private static final Log log = LogUtil.getLog(ExportTpDeliveryReportToXMLService.class);
	
	public static final String TRANSITION_FILE_EXT 	= ".inProcess";
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			} 
			
			ExportTpDeliveryReportToXMLServiceRequest request = (ExportTpDeliveryReportToXMLServiceRequest) context.getRequest();		
			
			// *****************************************
			// ********* Generate XML ******************
			// *****************************************
			TpDeliveryReport tpDeliveryReport = new TpDeliveryReport();
			tpDeliveryReport.setRequestor(request.getRequestor());
			tpDeliveryReport.setReportScenario(request.getReportScenario());
			tpDeliveryReport.generateXML();
			
			TpDeliveryReportScenario reportScenario = tpDeliveryReport.getReportScenario();
			
			// Clean the old files for the given user and report scenario
			if(reportScenario.getXMLPath() != null){
				File xmlFileParentPath = new File(reportScenario.getXMLPath()).getParentFile();
				File[] files = xmlFileParentPath.listFiles();
				for(File file : files){
					file.delete();
				}
			}
			
			String inProcessReportFilePath = TpDeliveryReport.getReportXmlFilePath(request.getReportId(), request.getRequestor().getId(), reportScenario.getId()) + TRANSITION_FILE_EXT;
			String completedReportFilePath = TpDeliveryReport.getReportXmlFilePath(request.getReportId(), request.getRequestor().getId(), reportScenario.getId());
			
			tpDeliveryReport.writeToXMLFile( inProcessReportFilePath );
			File inProcessReportFile = new File( inProcessReportFilePath );
			inProcessReportFile.renameTo( new File(completedReportFilePath) );
			
			// Convert to HTML
			long reportId = request.getReportId();
			long userId = request.getRequestor().getId();
			String reportHtmlFilePath = TpDeliveryReport.getReportHtmlFilePath(reportId, userId, reportScenario.getId());
			TpDeliveryReport.transformXML(reportId, userId, reportScenario.getId());
			
			// Save path to TpDeliveryReportScenario object
			reportScenario.setXMLPath(completedReportFilePath);
			reportScenario.setReportPath(reportHtmlFilePath);
			reportScenario.save();
			
			context.getResponse().setResultValueBean(request.getReportId());			
		}catch (Exception e) {
			log.error(" unexpected exception when invoking ExportTpDeliveryReportToXMLService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(long reportId, User requestor, TpDeliveryReportScenario reportScenario){
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		ExportTpDeliveryReportToXMLServiceRequest request = new ExportTpDeliveryReportToXMLServiceRequest();
		request.setReportId(reportId);
		request.setRequestor(requestor);	
		request.setReportScenario(reportScenario);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}	
}
