package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class GenerateImageLibraryAuditReportServiceRequest extends SimpleServiceRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -437117050373100622L;
	
	private long auditReportId;
	private List<Long> list = new ArrayList<>();
	private User requestor;
	
	
	public long getAuditReportId() {
		return auditReportId;
	}
	public void setAuditReportId(long auditReportId) {
		this.auditReportId = auditReportId;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public List<Long> getList() {
		return list;
	}
	public void setList(List<Long> list) {
		this.list = list;
	}
}