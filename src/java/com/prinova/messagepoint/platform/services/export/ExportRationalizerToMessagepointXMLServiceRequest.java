package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;
import com.prinova.messagepoint.platform.services.rationalizer.dto.TouchpointExportUserPreferencesDto;

public class ExportRationalizerToMessagepointXMLServiceRequest extends SimpleServiceRequest {

    private long rationalizerApplicationId;

    private User requestor;

    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    private TouchpointExportUserPreferencesDto userPreferencesDto;

    public ExportRationalizerToMessagepointXMLServiceRequest(long rationalizerApplicationId, User user, StatusPollingBackgroundTask statusPollingBackgroundTask, TouchpointExportUserPreferencesDto userPreferencesDto) {
        this.rationalizerApplicationId = rationalizerApplicationId;
        this.requestor = user;
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
        this.userPreferencesDto = userPreferencesDto;
    }

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public long getRationalizerApplicationId() {
        return rationalizerApplicationId;
    }

    public void setRationalizerApplicationId(long rationalizerApplicationId) {
        this.rationalizerApplicationId = rationalizerApplicationId;
    }

    public User getRequestor() {
        return requestor;
    }

    public void setRequestor(User requestor) {
        this.requestor = requestor;
    }

    public TouchpointExportUserPreferencesDto getUserPreferencesDto() {
        return userPreferencesDto;
    }

    public void setUserPreferencesDto(TouchpointExportUserPreferencesDto userPreferencesDto) {
        this.userPreferencesDto = userPreferencesDto;
    }
}
