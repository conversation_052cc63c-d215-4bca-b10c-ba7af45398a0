package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;


public class GenerateTouchpointAuditReportService extends AbstractService {
	public static final String SERVICE_NAME = "export.GenerateTouchpointAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateTouchpointAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateTouchpointAuditReportServiceRequest request = (GenerateTouchpointAuditReportServiceRequest) context.getRequest();

			TouchpointAuditReportRunner messageExportRunner = new TouchpointAuditReportRunner(request.getAuditReportId(),
                    request.getRequestor(),
                    request.getFromDate(),
                    request.getToDate(),
                    request.getDocument());
	   		MessagePointRunnableUtil.startThread(messageExportRunner, Thread.MAX_PRIORITY);
		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateTouchpointAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class TouchpointAuditReportRunner extends MessagePointRunnable {

		private long auditReportId;
		private User requestor;
		private Date fromDate;
		private Date toDate;
		private Document document;
		
		public TouchpointAuditReportRunner(long auditReportId,
										   User requestor,
										   Date fromDate, 
										   Date toDate,
										   Document document){
			this.auditReportId = auditReportId;
			this.requestor = requestor;
			this.fromDate = fromDate;
			this.toDate = toDate;
			this.document = document;
		}
		
		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			try {
				long reportTimestamp = System.currentTimeMillis();
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
				ServiceExecutionContext exportServiceContext = ExportTouchpointToXMLService.createContext(requestor, fromDate, 
						toDate, reportTimestamp, document);
																											
				Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportTouchpointToXMLService.SERVICE_NAME, ExportTouchpointToXMLService.class);
		    	exportService.execute(exportServiceContext);

		    	String xmlFilePath = ((ExportToXMLServiceResponse)exportServiceContext.getResponse()).getFilePath();	
		    	
		    	// ******************************************
				// ********* Generate XSLT ******************
				// ******************************************

		    	String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, requestor, AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT, reportTimestamp);
		    	
				// *********************************************************
				// ********* Update the AuditReport object *****************
				// *********************************************************
		    	
		    	File xhtmlFile = new File(xhtmlFilePath);
				ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(auditReportId, 
																																	AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT,
																																	xmlFilePath, 
																																	xhtmlFile.getAbsolutePath());
				Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				updateReportService.execute(updateReportServiceContext);		    	
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Touchpoint Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
			}				
		}
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContext(long auditReportId, Document document, User requestor, Date fromDate, Date toDate){
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateTouchpointAuditReportServiceRequest request = new GenerateTouchpointAuditReportServiceRequest();
		request.setAuditReportId(auditReportId);
		request.setRequestor(requestor);
		request.setFromDate(fromDate);
		request.setToDate(toDate);
		request.setDocument(document);
		
		context.setRequest(request);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;		
	}

}
