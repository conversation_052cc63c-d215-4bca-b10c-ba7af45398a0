package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetingSummaryUtil;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ExportContentToExcelService extends AbstractService {
    public static final String SERVICE_NAME = "export.ExportContentToExcelService";

    private static final Log log = LogUtil.getLog(ExportContentToExcelService.class);
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    // ContentObject is the object being referenced, the first Integer is the object type being referenced in and the
    // second Integer is the number being referenced
    private Map<ContentObject, Map<Integer, Integer>> referencedNumMap = new HashMap<>();
    // This map is used to mark the reference being used
    private Map<Long, List<String>> referencedCheckedMap = new HashMap<>();
    private Map<Long, String> referencedInlineTargetingMap = new HashMap<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            ExportContentToExcelServiceRequest request = (ExportContentToExcelServiceRequest) context.getRequest();
            this.statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();
            TouchpointSelection variant = TouchpointSelection.findById(request.getTargeObjectId());
            boolean includeOnlyActiveContent = request.isIncludeOnlyActiveContent();
            if (variant == null) {
                return;
            }

            User requestor = request.getRequestor();
            File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            }

            XSSFWorkbook workbook = new XSSFWorkbook();
            generateWorkbook(variant, workbook, requestor, includeOnlyActiveContent);

            String exportName = variant.getName();
            if (exportName != null)
                exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");

            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook,
                    requestor,
                    ExportUtil.ExportType.VARIANTCONTENT,
                    request.getExportId(),
                    exportName);

            ((ExportToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        } catch (Exception ex) {
            log.error(" unexpected exception when invoking ExportContentToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateWorkbook(TouchpointSelection touchpointSelection, XSSFWorkbook workbook, User requestor, boolean includeOnlyActiveContent) {
        Document document = touchpointSelection.getDocument();
        MessagepointLocale defaultLocale = document.getDefaultTouchpointLanguageLocale();

        log.info("Start generating \"Messages\" worksheet");

        XSSFSheet msgsSheet = workbook.createSheet(ApplicationUtil.getMessage("page.label.messages"));

        List<MessagepointLocale> locales = touchpointSelection.getDocument().getTouchpointLanguagesAsLocales();

        XSSFFont boldFont = workbook.createFont();//Create boldFont
        boldFont.setBold(true);//Make boldFont bold
        boldFont.setFontName("Arial");
        XSSFFont font = workbook.createFont();//Create boldFont
        font.setFontName("Arial");

        XSSFCellStyle boldStyle = workbook.createCellStyle();
        boldStyle.setFont(boldFont);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);//set it to bold

        int rowIdx = 0;
        // Header
        Row headerRow = msgsSheet.createRow(rowIdx);

        int colIdx = 0;
        String nameStrP1 = ApplicationUtil.getMessage("page.label.message.name");
        String nameStrP2 = " (" + ApplicationUtil.getMessage("page.text.in.section.zone.priority.order") + ")";
        XSSFRichTextString richString = new XSSFRichTextString(nameStrP1 + nameStrP2);
        richString.applyFont(0, nameStrP1.length(), boldFont);
        richString.applyFont(nameStrP1.length(), nameStrP2.length(), font);
        headerRow.createCell(colIdx).setCellStyle(boldStyle);
        headerRow.getCell(colIdx).setCellValue(richString);

        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.message") + " " + ApplicationUtil.getMessage("page.label.part"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.dynamic.variation"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.variant.level"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.condition"));
        for (MessagepointLocale locale : locales) {
            createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.content") + " (" + locale.getName() + ")");
        }
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.section"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.section.order"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.zone"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.zone") + " Y");
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.zone") + " X");
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.priority"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.delivery.type"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.status"));

        for (MessagepointLocale locale : locales) {
            createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.number.embedded.content") + "s (" + locale.getName() + ")");
        }
        rowIdx++;
        // Data
        // Sort the list by zone part name, zone name, delivery type and priority
        List<ContentObject> selectedContentObjects = new ArrayList<>(getContentObjects(touchpointSelection));
        selectedContentObjects.sort((o1, o2) -> {
            if(o1.getZone().getSection()==null || o2.getZone().getSection()==null){
                return 0;
            }
            if(o1.getZone().getSection().getName().equals(o2.getZone().getSection().getName())){
                if(o1.getZone().getName().equals(o2.getZone().getName())){
                    ContentObjectData cod1 = o1.getContentObjectDataWorkingCentric();
                    ContentObjectData cod2 = o2.getContentObjectDataWorkingCentric();
                    if(cod1==null | cod2==null){
                        return 0;
                    }
                    Integer dt1 = cod1.getDeliveryType();
                    Integer dt2 = cod2.getDeliveryType();
                    if(dt1.equals(dt2)){
                        Integer p1 = cod1.getPriorityNumberByZone(o1.getZone().getId());
                        Integer p2 = cod2.getPriorityNumberByZone(o2.getZone().getId());
                        return p1.compareTo(p2);
                    }else{
                        return dt1.compareTo(dt2);
                    }
                }else{
                    return o1.getZone().getName().compareTo(o2.getZone().getName());
                }
            }else{
                return o1.getZone().getSection().getName().compareTo(o2.getZone().getSection().getName());
            }
        });

        this.statusPollingBackgroundTask.setProgressInPercentInThread(5);

        final double totalContentObjects = selectedContentObjects.size();
        double processedContentObjects = 0;
        for(ContentObject selectedContentObject : selectedContentObjects){
            ContentObjectData contentObjectData = includeOnlyActiveContent?selectedContentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE):selectedContentObject.getContentObjectDataWorkingCentric();
            // SKip the "Suppressed" objects
            if(selectedContentObject.isSuppressed() || contentObjectData==null){
                continue;
            }

            ParameterGroupTreeNode owningTpPgtn = selectedContentObject.getOwningTouchpointSelection()==null?null:selectedContentObject.getOwningTouchpointSelection().getParameterGroupTreeNode();

            Zone zone = selectedContentObject.getZone();
            DocumentSection section = zone.getSection();
            // !!! shouldn't disableStructuredContent be true, since dataType is ALL?
            Map<Long, List<ContentObjectAssociation>> coasMap = ContentObjectAssociation.findAllByContentObjectAndParameters(selectedContentObject, ContentObject.DATA_TYPE_ALL, null, null, null, false, true, true, false, false)
                    .stream().collect(Collectors.groupingBy(coa->coa.getTouchpointPGTreeNode()==null?(owningTpPgtn!=null?owningTpPgtn.getId():0):coa.getTouchpointPGTreeNode().getId()));
            // If the variant is referenced, CA might not be existed, so create dummy ones
            if(!coasMap.containsKey(touchpointSelection.getParameterGroupTreeNode().getId())
                    && !touchpointSelection.isMaster()
                    && touchpointSelection!=selectedContentObject.getOwningTouchpointSelection()
                    && !touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(owningTpPgtn)){
                List<ContentObjectAssociation> coas = new ArrayList<>();
                ParameterGroupTreeNode parentTpPgtn = touchpointSelection.getParameterGroupTreeNode();
                while(coas.isEmpty() && parentTpPgtn != null) {
                    coas.addAll(ContentObjectAssociation.findAllByContentObjectAndParameters(selectedContentObject, selectedContentObject.getFocusOnDataType(), parentTpPgtn));
                    parentTpPgtn = parentTpPgtn.getParentNode();
                }
                coasMap.put(touchpointSelection.getParameterGroupTreeNode().getId(), coas);
            }

            List<Long> tpPgtnIdList = new ArrayList(coasMap.keySet());
            Collections.sort(tpPgtnIdList);
            for(Long tpPgtnId : tpPgtnIdList) {
                ParameterGroupTreeNode keyTpPgtn = ParameterGroupTreeNode.findById(tpPgtnId);
                TouchpointSelection tpTs = document.getMasterTouchpointSelection();
                if(keyTpPgtn == null){
                    if(selectedContentObject.getOwningTouchpointSelection() != null){
                        tpTs = selectedContentObject.getOwningTouchpointSelection();
                    }
                }else{
                    tpTs = TouchpointSelection.findByPGTN(tpPgtnId);
                }

                Document alternateLayout = tpTs.getAlternateLayout();
                if(alternateLayout != null){
                    zone = alternateLayout.findZoneByParent(zone);
                    section = zone.getSection();
                }else{
                    zone = selectedContentObject.getZone();
                    section = zone.getSection();
                }

                if(!zone.isEnabled()){
                    continue;
                }

                Map<Long, List<ContentObjectAssociation>> variantCoasMap = coasMap.get(tpPgtnId).stream()
                        .collect(Collectors.groupingBy(coa->coa.getContentObjectPGTreeNode()==null?0:coa.getContentObjectPGTreeNode().getId()));

                List<Long> coPgtnIdList = new ArrayList(variantCoasMap.keySet());
                Collections.sort(coPgtnIdList);
                for(Long coPgtnId : coPgtnIdList) {
                    Map<Long, List<ContentObjectAssociation>> zpCoasMap = variantCoasMap.get(coPgtnId).stream()
                            .collect(Collectors.groupingBy(coa->coa.getZonePart()==null?0:coa.getZonePart().getId()));
                    for(Long zpId : zpCoasMap.keySet()) {
                        Map<MessagepointLocale, ContentObjectAssociation> coaLangMap = zpCoasMap.get(zpId).stream()
                                .collect(Collectors.toMap(ContentObjectAssociation::getMessagepointLocale,
                                        Function.identity(),
                                        (coa1, coa2) -> {
                                            if (coa1.getCreated().after(coa2.getCreated())) {
                                                return coa1;
                                            } else {
                                                return coa2;
                                            }
                                        }));

                        ContentObjectAssociation defaultLocaleCoa = coaLangMap.get(defaultLocale);

                        // Fill in empty default COA if it does not exist
                        if(defaultLocaleCoa == null){
                            MessagepointLocale nextAvailLocale = coaLangMap.keySet().iterator().next();
                            ContentObjectAssociation nextAvailLocaleCoa = coaLangMap.get(nextAvailLocale);
                            defaultLocaleCoa = new ContentObjectAssociation();
                            defaultLocaleCoa.setContentObject(selectedContentObject);
                            defaultLocaleCoa.setZonePart(nextAvailLocaleCoa.getZonePart());
                            defaultLocaleCoa.setDataType(nextAvailLocaleCoa.getDataType());
                            defaultLocaleCoa.setMessagepointLocale(defaultLocale);
                            defaultLocaleCoa.setTouchpointPGTreeNode(ParameterGroupTreeNode.findById(tpPgtnId));
                            defaultLocaleCoa.setContentObjectPGTreeNode(ParameterGroupTreeNode.findById(coPgtnId));
                            defaultLocaleCoa.setTypeId(ContentAssociationType.ID_EMPTY);
                            coaLangMap.put(defaultLocale, defaultLocaleCoa);
                        }

                        ParameterGroupTreeNode currentCoaTPGTreeNode = defaultLocaleCoa.getTouchpointPGTreeNode();
                        if(currentCoaTPGTreeNode==null){
                            currentCoaTPGTreeNode = owningTpPgtn;
                        }
                        if((keyTpPgtn == null && !touchpointSelection.isMaster() && selectedContentObject.getOwningTouchpointSelection()!=touchpointSelection) ||
                                (keyTpPgtn != null && tpPgtnId != touchpointSelection.getParameterGroupTreeNode().getId() && !touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(currentCoaTPGTreeNode))){
                            continue;
                        }
                        // Ignore the referenced content is it is not at the current of requesting
                        if (defaultLocaleCoa.getTypeId() == ContentAssociationType.ID_REFERENCES) {
                            if(touchpointSelection.getParameterGroupTreeNode().getId() != tpPgtnId
                                    && touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(keyTpPgtn)){
                                continue;
                            }
                        }

                        Row row = msgsSheet.createRow(rowIdx);
                        colIdx = 0;
                        // Name
                        createStyledCellAndSetValue(row, colIdx, style, selectedContentObject.getName());
                        // Message Part
                        createStyledCellAndSetValue(row, ++colIdx, style, defaultLocaleCoa.getZonePart() != null ? defaultLocaleCoa.getZonePart().getName() : "");
                        // Dynamic Variation
                        String dynVariantionName = selectedContentObject.isDynamicVariantEnabled(defaultLocaleCoa.getDataType()) ? "Master" : "";
                        if (defaultLocaleCoa.getContentObjectPGTreeNode() != null) {
                            dynVariantionName += ParameterGroupTreeNode.getFullPath(defaultLocaleCoa.getContentObjectPGTreeNode());
                        }
                        createStyledCellAndSetValue(row, ++colIdx, style, dynVariantionName);
                        // Variant Level
                        String fullPath = ParameterGroupTreeNode.getFullPath(tpTs.getParameterGroupTreeNode()).substring(1);
                        createStyledCellAndSetValue(row, ++colIdx, style, fullPath);
                        // Condition
                        // The maximum length of cell contents (text) is 32767 characters
                        String conditionStr = this.buildConditionValue(selectedContentObject, keyTpPgtn, defaultLocaleCoa.getContentObjectPGTreeNode());
                        if(conditionStr.length()>=32767){
                            log.info("Condition length exceed the maximum length of cell contents (text), co id=" + selectedContentObject.getId());
                        }
                        createStyledCellAndSetValue(row, ++colIdx, style, conditionStr);
                        // Contents
                        for (MessagepointLocale locale : locales) {
                            // Fill in "Same as default" COA if locale does not exist
                            if(!coaLangMap.containsKey(locale)){
                                ContentObjectAssociation sameAsDefaultCoa = new ContentObjectAssociation();
                                sameAsDefaultCoa.setContentObject(selectedContentObject);
                                sameAsDefaultCoa.setMessagepointLocale(locale);
                                sameAsDefaultCoa.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
                                coaLangMap.put(locale, sameAsDefaultCoa);
                            }

                            String contentStr = "";
                            ContentObjectAssociation coa = coaLangMap.get(locale);
                            if(coa != null) {
                                coa = resolveReferencedCoa(selectedContentObject, coa);
                                Content content = coa.getContent();

                                if (content != null) {
                                    if (content.getEncodedContent() != null && !content.getEncodedContent().isEmpty()) {
                                        contentStr = processContent(content, locale, document);
                                    } else if (content.getImageName() != null && !content.getImageName().isEmpty()) {
                                        contentStr = content.getImageName();
                                    }
                                } else if (coa.getReferencingImageLibrary() != null) {
                                    contentStr = processImageLibraryReference(selectedContentObject, coa.getReferencingImageLibrary());
                                }
                            }
                            createStyledCellAndSetValue(row, ++colIdx, style, contentStr);
                        }
                        // Section
                        createStyledCellAndSetValue(row, ++colIdx, style, section.getName());
                        createStyledCellAndSetValue(row, ++colIdx, style, section.getSectionOrder());
                        // Zone
                        createStyledCellAndSetValue(row, ++colIdx, style, zone.getFriendlyName());
                        createStyledCellAndSetValue(row, ++colIdx, style, new BigDecimal(zone.getTopYInInches()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        createStyledCellAndSetValue(row, ++colIdx, style, new BigDecimal(zone.getTopXInInches()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        // Priority
                        createStyledCellAndSetValue(row, ++colIdx, style, contentObjectData.getPriorityNumberByZone(zone.getId()));
                        createStyledCellAndSetValue(row, ++colIdx, style, (contentObjectData.getDeliveryType()==ContentObject.DELIVERY_TYPE_MANDATORY?ApplicationUtil.getMessage("page.label.mandatory"):ApplicationUtil.getMessage("page.label.optional")));
                        // Status
                        createStyledCellAndSetValue(row, ++colIdx, style, contentObjectData.getStatusDisplay());
                        // Number of smart texts
                        for (MessagepointLocale locale : locales) {
                            int numSmartTexts = 0;
                            ContentObjectAssociation coa = coaLangMap.get(locale);
                            if(coa != null) {
                                coa = resolveReferencedCoa(selectedContentObject, coa);
                                Content content = coa.getContent();
                                if (content != null) {
                                    numSmartTexts = content.getGlobalSmartTexts().size() + content.getLocalSmartTexts().size();
                                    addReferencedContent(selectedContentObject, coaLangMap.get(locale));
                                }
                            }
                            createStyledCellAndSetValue(row, ++colIdx, style, numSmartTexts);
                        }
                        rowIdx++;
                    }
                }
            }

            processedContentObjects++;
            double percentInDouble = (processedContentObjects / totalContentObjects) * 30;
            this.statusPollingBackgroundTask.setProgressInPercentInThread((int) percentInDouble + 5);
        }
        log.info("Finish generating \"Messages\" worksheet");

        this.statusPollingBackgroundTask.setProgressInPercentInThread(35);
        this.generateReferencedContentObjectSheet(touchpointSelection, workbook, true, true);
        this.statusPollingBackgroundTask.setProgressInPercentInThread(50);
        this.generateReferencedContentObjectSheet(touchpointSelection, workbook, false, true);
        this.statusPollingBackgroundTask.setProgressInPercentInThread(65);
        this.generateReferencedContentObjectSheet(touchpointSelection, workbook, true, false);
        this.statusPollingBackgroundTask.setProgressInPercentInThread(80);
        this.generateReferencedContentObjectSheet(touchpointSelection, workbook, false, false);
        this.statusPollingBackgroundTask.setProgressInPercentInThread(95);
        this.generateInlineTargetingSheet(workbook);
    }

    private void generateReferencedContentObjectSheet(TouchpointSelection touchpointSelection, XSSFWorkbook workbook, boolean isGlobalAsset, boolean isSmartText){
        Document document = touchpointSelection.getDocument();
        String sheetName = "", columnName = "";
        List<ContentObject> selectedCos = new ArrayList<>();
        int startPercentInDouble = 0;
        if(isGlobalAsset && isSmartText){
            sheetName = ApplicationUtil.getMessage("page.label.global.smart.texts");
            columnName = ApplicationUtil.getMessage("page.label.embedded.content") + ApplicationUtil.getMessage("page.label.name");
            selectedCos.addAll(this.referencedNumMap.keySet().stream().filter(co->co.getObjectType()==ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT).collect(Collectors.toList()));
            startPercentInDouble = 35;
        }else if(isGlobalAsset && !isSmartText){
            sheetName = ApplicationUtil.getMessage("page.label.global.image.library");
            columnName = ApplicationUtil.getMessage("page.label.content.library") + ApplicationUtil.getMessage("page.label.name");
            selectedCos.addAll(this.referencedNumMap.keySet().stream().filter(co->co.getObjectType()==ContentObject.OBJECT_TYPE_GLOBAL_IMAGE).collect(Collectors.toList()));
            startPercentInDouble = 50;
        }else if(!isGlobalAsset && isSmartText){
            sheetName = ApplicationUtil.getMessage("page.label.local.smart.texts");
            columnName = ApplicationUtil.getMessage("page.label.embedded.content") + ApplicationUtil.getMessage("page.label.name");
            selectedCos.addAll(this.referencedNumMap.keySet().stream().filter(co->co.getObjectType()==ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT).collect(Collectors.toList()));
            startPercentInDouble = 65;
        }else if(!isGlobalAsset && !isSmartText){
            sheetName = ApplicationUtil.getMessage("page.label.local.image.library");
            columnName = ApplicationUtil.getMessage("page.label.content.library") + ApplicationUtil.getMessage("page.label.name");
            selectedCos.addAll(this.referencedNumMap.keySet().stream().filter(co->co.getObjectType()==ContentObject.OBJECT_TYPE_LOCAL_IMAGE).collect(Collectors.toList()));
            startPercentInDouble = 80;
        }

        if(selectedCos.isEmpty()){
            return;
        }

        log.info("Start generating \"" + sheetName + "\" worksheet");

        XSSFSheet refCoSheet = workbook.createSheet(sheetName);

        List<MessagepointLocale> locales = new ArrayList<>();
        if(isGlobalAsset){
            locales = touchpointSelection.getDocument().getSystemDefaultAndTouchpointLanguagesAsLocales();
        }else{
            locales = touchpointSelection.getDocument().getTouchpointLanguagesAsLocales();
        }
        MessagepointLocale defaultLocale = isGlobalAsset?MessagepointLocale.getDefaultSystemLanguageLocale():touchpointSelection.getDocument().getDefaultTouchpointLanguageLocale();

        Font boldFont = workbook.createFont();//Create boldFont
        boldFont.setBold(true);//Make boldFont bold
        boldFont.setFontName("Arial");
        Font font = workbook.createFont();//Create boldFont
        font.setFontName("Arial");

        XSSFCellStyle boldStyle = workbook.createCellStyle();
        boldStyle.setFont(boldFont);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);//set it to bold

        int rowIdx = 0;
        // Header
        Row headerRow = refCoSheet.createRow(rowIdx);

        int colIdx = 0;
        createStyledCellAndSetValue(headerRow, colIdx, boldStyle, columnName);
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.dynamic.variation"));
        if(!isGlobalAsset) {
            createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.variant.level"));
        }
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.condition"));
        for (MessagepointLocale locale : locales) {
            createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.content") + " (" + locale.getName() + ")");
        }

        if(isSmartText) {
            for (MessagepointLocale locale : locales) {
                createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.number.embedded.content") + "s (" + locale.getName() + ")");
            }
        }
        // Number of Times Referenced in Messages
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.text.number.of.times.referenced.in.messages"));
        if(isSmartText) {
            // Number of Times Referenced in Smart Texts
            createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.text.number.of.times.referenced.in.smart.texts"));
        }

        rowIdx++;
        // Data
        // Sort the list by name
        selectedCos.sort(Comparator.comparing(ContentObject::getName));
        final double totalContentObjects = selectedCos.size();
        double processedContentObjects = 0;
        for(ContentObject selectedContentObject : selectedCos) {
            // SKip the "Suppressed" objects
            if(selectedContentObject.isSuppressed()){
                continue;
            }

            // !!! shouldn't disableStructuredContent be true, since dataType is ALL?
            Map<Long, List<ContentObjectAssociation>> coasMap = ContentObjectAssociation.findAllByContentObjectAndParameters(selectedContentObject, ContentObject.DATA_TYPE_ALL, null, null, null, false, true, true, false, false)
                    .stream().collect(Collectors.groupingBy(coa->coa.getTouchpointPGTreeNode()==null?0:coa.getTouchpointPGTreeNode().getId()));

            // Empty COAs, create empty one
            if(coasMap.isEmpty()){
                List<ContentObjectAssociation> coaList = new ArrayList<>();
                ContentObjectAssociation emptyCoa = new ContentObjectAssociation();
                emptyCoa.setMessagepointLocale(defaultLocale);
                emptyCoa.setTypeId(ContentAssociationType.ID_EMPTY);
                coaList.add(emptyCoa);
                coasMap.put(0L, coaList);
            }

            List<Long> tpPgtnIdList = new ArrayList(coasMap.keySet());
            Collections.sort(tpPgtnIdList);
            for(Long tpPgtnId : tpPgtnIdList) {
                ParameterGroupTreeNode keyTpPgtn = ParameterGroupTreeNode.findById(tpPgtnId);
                Map<Long, List<ContentObjectAssociation>> variantCoasMap = coasMap.get(tpPgtnId).stream()
                        .collect(Collectors.groupingBy(coa->coa.getContentObjectPGTreeNode()==null?0:coa.getContentObjectPGTreeNode().getId()));

                List<Long> coPgtnIdList = new ArrayList(variantCoasMap.keySet());
                Collections.sort(coPgtnIdList);
                for (Long coPgtnId : coPgtnIdList) {
                    Map<MessagepointLocale, ContentObjectAssociation> coaLangMap = variantCoasMap.get(coPgtnId).stream()
                            .collect(Collectors.toMap(ContentObjectAssociation::getMessagepointLocale,
                                    Function.identity(),
                                    (coa1, coa2) -> {
                                        if (coa1.getCreated().after(coa2.getCreated())) {
                                            return coa1;
                                        } else {
                                            return coa2;
                                        }
                                    }));

                    ContentObjectAssociation defaultLocaleCoa = coaLangMap.get(defaultLocale);

                    // Fill in empty default COA if it does not exist
                    if(defaultLocaleCoa == null){
                        MessagepointLocale nextAvailLocale = coaLangMap.keySet().iterator().next();
                        ContentObjectAssociation nextAvailLocaleCoa = coaLangMap.get(nextAvailLocale);
                        defaultLocaleCoa = new ContentObjectAssociation();
                        defaultLocaleCoa.setContentObject(selectedContentObject);
                        defaultLocaleCoa.setZonePart(nextAvailLocaleCoa.getZonePart());
                        defaultLocaleCoa.setDataType(nextAvailLocaleCoa.getDataType());
                        defaultLocaleCoa.setMessagepointLocale(defaultLocale);
                        defaultLocaleCoa.setTouchpointPGTreeNode(ParameterGroupTreeNode.findById(tpPgtnId));
                        defaultLocaleCoa.setContentObjectPGTreeNode(ParameterGroupTreeNode.findById(coPgtnId));
                        defaultLocaleCoa.setTypeId(ContentAssociationType.ID_EMPTY);
                        coaLangMap.put(defaultLocale, defaultLocaleCoa);
                    }

                    if(!isGlobalAsset) {
                        if ((keyTpPgtn == null && !touchpointSelection.isMaster() && selectedContentObject.getOwningTouchpointSelection() != touchpointSelection) ||
                                (keyTpPgtn != null && tpPgtnId != touchpointSelection.getParameterGroupTreeNode().getId() && !touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(defaultLocaleCoa.getTouchpointPGTreeNode()))) {
                            continue;
                        }

                        if (defaultLocaleCoa.getTypeId() == ContentAssociationType.ID_REFERENCES) {
                            if (touchpointSelection.getParameterGroupTreeNode().getId() != tpPgtnId
                                    && touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(keyTpPgtn)) {
                                continue;
                            }
                        }
                    }

                    String dynamicVariant = selectedContentObject.isDynamicVariantEnabled(defaultLocaleCoa.getDataType()) ? "Master" : "";
                    ParameterGroupTreeNode coPgtn = ParameterGroupTreeNode.findById(coPgtnId);
                    if (coPgtn != null) {
                        dynamicVariant += ParameterGroupTreeNode.getFullPath(coPgtn);
                    }
                    Row row = refCoSheet.createRow(rowIdx);
                    colIdx = 0;
                    // Name
                    createStyledCellAndSetValue(row, colIdx, style, selectedContentObject.getName());
                    // Dynamic Variation
                    createStyledCellAndSetValue(row, ++colIdx, style, dynamicVariant);
                    // Variant Level
                    if(!isGlobalAsset) {
                        ParameterGroupTreeNode tpPgtn = ParameterGroupTreeNode.findById(tpPgtnId);
                        String variantLevelName = "Master";
                        if (tpPgtn == null) {
                            if (selectedContentObject.getOwningTouchpointSelection() != null) {
                                variantLevelName = ParameterGroupTreeNode.getFullPath(selectedContentObject.getOwningTouchpointSelection().getParameterGroupTreeNode()).substring(1);
                            }
                        } else {
                            variantLevelName = ParameterGroupTreeNode.getFullPath(tpPgtn).substring(1);
                        }
                        createStyledCellAndSetValue(row, ++colIdx, style, variantLevelName);
                    }
                    // Condition
                    createStyledCellAndSetValue(row, ++colIdx, style, this.buildConditionValue(selectedContentObject, null, coPgtn));
                    // Contents
                    for (MessagepointLocale locale : locales) {
                        // Fill in "Same as default" COA if locale does not exist
                        if(!coaLangMap.containsKey(locale)){
                            ContentObjectAssociation sameAsDefaultCoa = new ContentObjectAssociation();
                            sameAsDefaultCoa.setContentObject(selectedContentObject);
                            sameAsDefaultCoa.setMessagepointLocale(locale);
                            sameAsDefaultCoa.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
                            coaLangMap.put(locale, sameAsDefaultCoa);
                        }

                        String contentStr = "";
                        ContentObjectAssociation coa = coaLangMap.get(locale);
                        if (coa != null) {
                            coa = resolveReferencedCoa(selectedContentObject, coa);
                            Content content = coa.getContent();

                            if (content != null) {
                                if (content.getEncodedContent() != null && !content.getEncodedContent().isEmpty()) {
                                    contentStr = processContent(content, locale, document);
                                } else if (content.getImageName() != null && !content.getImageName().isEmpty()) {
                                    contentStr = content.getImageName();
                                }
                            }
                        }
                        // The maximum length of cell contents (text) is 32767 characters
                        if(contentStr.length()>=32767){
                            log.info("Content length exceed the maximum length of cell contents (text), coa id=" + coa.getId());
                        }
                        createStyledCellAndSetValue(row, ++colIdx, style, contentStr);
                    }

                    if (isSmartText) {
                        // Number of smart texts
                        for (MessagepointLocale locale : locales) {
                            int numSmartTexts = 0;
                            ContentObjectAssociation coa = coaLangMap.get(locale);
                            if (coa != null) {
                                coa = resolveReferencedCoa(selectedContentObject, coa);
                                Content content = coa.getContent();
                                if (content != null) {
                                    numSmartTexts = content.getGlobalSmartTexts().size() + content.getLocalSmartTexts().size();
                                    addReferencedContent(selectedContentObject, coaLangMap.get(locale));
                                }
                            }
                            createStyledCellAndSetValue(row, ++colIdx, style, numSmartTexts);
                        }
                    }

                    ContentObjectAssociation defaultCa = coaLangMap.get(defaultLocale);
                    // Only display the following two fields if on the top level or variant structured LST at the matching variant level
                    if ((defaultCa.getContentObjectPGTreeNode() == null && defaultCa.getTouchpointPGTreeNode() == null) ||
                            (defaultCa.getTouchpointPGTreeNode() != null && defaultCa.getTouchpointPGTreeNode().getId() == touchpointSelection.getParameterGroupTreeNode().getId())) {
                        // Number of Times Referenced in Messages
                        Integer numRefInMs = this.referencedNumMap.get(selectedContentObject).get(ContentObject.OBJECT_TYPE_MESSAGE);
                        createStyledCellAndSetValue(row, ++colIdx, style, numRefInMs != null ? numRefInMs : 0);
                        if (isSmartText) {
                            // Number of Times Referenced in Smart Texts
                            Integer numRefInSTs = this.referencedNumMap.get(selectedContentObject).get(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);
                            createStyledCellAndSetValue(row, ++colIdx, style, numRefInSTs != null ? numRefInSTs : 0);
                        }
                    } else {
                        // Number of Times Referenced in Messages
                        createStyledCellAndSetValue(row, ++colIdx, style, "");
                        if (isSmartText) {
                            // Number of Times Referenced in Smart Texts
                            createStyledCellAndSetValue(row, ++colIdx, style, "");
                        }
                    }
                    rowIdx++;
                }
            }

            processedContentObjects++;
            double percentInDouble = (processedContentObjects / totalContentObjects) * 15;
            this.statusPollingBackgroundTask.setProgressInPercentInThread((int) percentInDouble + startPercentInDouble);
        }
        log.info("Finish generating \"" + sheetName + "\" worksheet");
    }

    private void generateInlineTargetingSheet(XSSFWorkbook workbook) {
        if (this.referencedInlineTargetingMap.isEmpty()) {
            return;
        }

        String sheetName = "Inline Targeted", columnName = "Inline Targeted ID";

        log.info("Start generating \"" + sheetName + "\" worksheet");

        XSSFSheet itSheet = workbook.createSheet(sheetName);

        Font boldFont = workbook.createFont();//Create boldFont
        boldFont.setBold(true);//Make boldFont bold
        boldFont.setFontName("Arial");
        Font font = workbook.createFont();//Create boldFont
        font.setFontName("Arial");

        XSSFCellStyle boldStyle = workbook.createCellStyle();
        boldStyle.setFont(boldFont);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);//set it to bold

        int rowIdx = 0;
        // Header
        Row headerRow = itSheet.createRow(rowIdx);

        int colIdx = 0;
        createStyledCellAndSetValue(headerRow, colIdx, boldStyle, columnName);
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.condition"));
        createStyledCellAndSetValue(headerRow, ++colIdx, boldStyle, ApplicationUtil.getMessage("page.label.content"));

        rowIdx++;
        List<Long> keyList = new ArrayList<>(this.referencedInlineTargetingMap.keySet());
        for(Long key : keyList){
            ContentTargeting contentTargeting = ContentTargeting.findById(key);
            if(contentTargeting == null){
                continue;
            }
            int idx = keyList.indexOf(key);
            String idxStr = String.format("%03d", ++idx);
            Row row = itSheet.createRow(rowIdx);
            // Name
            colIdx = 0;
            createStyledCellAndSetValue(row, colIdx, style, idxStr);
            // Condition
            String targetingStr = "";
            if(contentTargeting.getTargetGroupCount() != 0){
                targetingStr = Jsoup.parse(TargetingSummaryUtil.generateSummaryHtml(contentTargeting, null)).text();
            }
            createStyledCellAndSetValue(row, ++colIdx, style, targetingStr);
            // Content
            createStyledCellAndSetValue(row, ++colIdx, style, referencedInlineTargetingMap.get(key));
            rowIdx++;
        }

        log.info("Finish generating \"" + sheetName + "\" worksheet");
    }

    private List<ContentObject> getContentObjects(TouchpointSelection touchpointSelection){
        log.info("Collect messages information");

        long documentId = touchpointSelection.getDocument().getId();
        int localContext = 0;   // Messages
        boolean appliesAlternateTemplate = false;
        List<ContentObject> selectedContentObjects = new ArrayList<>();

        if (touchpointSelection != null && touchpointSelection.isMaster())
            appliesAlternateTemplate = touchpointSelection.getAlternateLayout() != null;
        List<Long> zoneIdList = new ArrayList<>();
        if ( !(localContext > 0) ) {
            localContext = 0;
            zoneIdList = AsyncListTableController.getZoneList(documentId, 0, 0, !appliesAlternateTemplate);
        }
        List<Long> selectedIds = ContentObject.findUniqueVisibleIdsOfMostRecentCopies(zoneIdList, (int)localContext, documentId, null); // Retrieve all messages
        for (Long selectedId : selectedIds) {
            ContentObject co = HibernateUtil.getManager().getObject(ContentObject.class, selectedId);
            if(co == null) {
                continue;
            }
            if(touchpointSelection.isMaster() || (co.isVariantType() && co.getOwningTouchpointSelection().getId() == touchpointSelection.getId()) ||
                    (co.isStructuredContentEnabled() && (!co.isVariantType() || co.getOwningTouchpointSelection().getParameterGroupTreeNode().getAllDescendentNodes().contains(touchpointSelection.getParameterGroupTreeNode()))) ||
                    (co.isVariantType() && touchpointSelection.getParameterGroupTreeNode().getAllDescendentNodes().contains(co.getOwningTouchpointSelection().getParameterGroupTreeNode()))){
                // 1. If the report is generated from the Master;
                // 2. If the message is a variant message created from this variant;
                // 3. If the message is a structured message created from the master or created in its ancestry variant;
                // 4. If the message is a variant message created in its descendant variant
                selectedContentObjects.add(co);
            }
        }
        return selectedContentObjects;
    }

    private String processImageLibraryReference(ContentObject coReferencedIn, ContentObject reference){
        if (!this.referencedNumMap.containsKey(reference)) {
            Map<Integer, Integer> referencedInMap = new HashMap<>();
            referencedInMap.put(coReferencedIn.getObjectType(),1);
            this.referencedNumMap.put(reference, referencedInMap);
        } else if(!this.referencedNumMap.get(reference).containsKey(coReferencedIn.getObjectType())) {
            this.referencedNumMap.get(reference).put(coReferencedIn.getObjectType(), 1);
        } else {
            int numReferences = this.referencedNumMap.get(reference).get(coReferencedIn.getObjectType());
            this.referencedNumMap.get(reference).put(coReferencedIn.getObjectType(), ++numReferences);
        }

        List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(reference, reference.getFocusOnDataType(),
                null, null, null, false, true, true, false, true);

        for (ContentObjectAssociation ca : cas) {

            if (!this.referencedCheckedMap.containsKey(reference.getId())) {
                this.referencedCheckedMap.put(reference.getId(), new ArrayList<>());
            }
            if (!this.referencedCheckedMap.get(reference.getId()).contains(String.valueOf(ca.getId()))) {
                if(ca.getReferencingImageLibrary() != null) {
                    processImageLibraryReference(reference, ca.getReferencingImageLibrary());
                }
                this.referencedCheckedMap.get(reference.getId()).add(String.valueOf(ca.getId()));
            }
        }
        if(reference.isGlobalImage()) {
            return "<IL " + reference.getName() + ">";
        }else{
            return "<LIL " + reference.getName() + ">";
        }
    }

    private void createStyledCellAndSetValue(Row row, int colIdx, CellStyle style, String value){
        // The maximum length of cell contents (text) is 32767 characters
        if(value.length()>=32767){
            log.info("Length exceed the maximum length of cell contents (text), value: " + value);
            value = value.substring(0,32760) + "...";
        }
        row.createCell(colIdx).setCellStyle(style);
        row.getCell(colIdx).setCellValue(value);
    }

    private void createStyledCellAndSetValue(Row row, int colIdx, CellStyle style, Integer value){
        row.createCell(colIdx).setCellStyle(style);
        row.getCell(colIdx).setCellValue(value);
    }

    private void createStyledCellAndSetValue(Row row, int colIdx, CellStyle style, Double value){
        row.createCell(colIdx).setCellStyle(style);
        row.getCell(colIdx).setCellValue(value);
    }

    private String processContent(Content content, MessagepointLocale locale, Document touchpoint){
        String encodedContent = ContentObjectContentUtil.translateContentForView((content.getEncodedContent() != null ? content.getEncodedContent() : ""), touchpoint, locale, null);

        // Process the inline targeting content
        Elements inss = Jsoup.parse(encodedContent).getElementsByAttribute("content_targeting_id");
        for (Element currentElement : inss) {
            if (currentElement.hasAttr("content_targeting_id")) {
                Long id = Long.valueOf(currentElement.attr("content_targeting_id"));
                if(!this.referencedInlineTargetingMap.containsKey(id)){
                    currentElement.getElementsByClass("renderedLabelContainer").remove();
                    this.referencedInlineTargetingMap.put(id, currentElement.text());
                }
            }
        }

        String separator = "__";
        String contentStr = ContentObjectContentUtil.getUnformattedTextContent(Jsoup.parse(encodedContent), separator);
        // Add a " " separator in case the "MPVAR" are appended together without space
        Pattern mpvarPattern = Pattern.compile(separator + "MPVAR[0-9]*#[0-9A-Za-z]*" + separator);
        Matcher sentenceMatcher = mpvarPattern.matcher(contentStr);
        while (sentenceMatcher.find()) {
            String groupText = sentenceMatcher.group();
            // MPVAR10#DDEDD
            String[] tokens = groupText.replace(separator, "").replace("MPVAR","").split("#");
            if(tokens.length <= 1){
                continue;
            }
            int type = Integer.parseInt(tokens[0]);
            String rpl = "";
            if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT)
                    || Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_COMPOUND_EMBEDDED_CONTENT)
                    || Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_LOCAL_CONTENT)
                    || Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_LOCAL_IMAGE_LIBRARY)
                    || Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_IMAGE_LIBRARY)){
                ContentObject contentObject = null;
                if(StringUtils.isNumeric(tokens[1])){
                    contentObject = ContentObject.findById(Long.parseLong(tokens[1]));
                }else{
                    if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT) ||
                        Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_COMPOUND_EMBEDDED_CONTENT) ||
                        Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_IMAGE_LIBRARY)
                    ) {
                        contentObject = ContentObject.findGlobalObjectByDna(tokens[1]);
                    } else {
                        contentObject = ContentObject.findByDnaAndDocument(tokens[1], touchpoint);
                    }
                }
                if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT)
                        || Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_COMPOUND_EMBEDDED_CONTENT)){
                    rpl = contentObject != null ? ("<ST " + contentObject.getName() + ">") : "";
                }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_LOCAL_CONTENT)){
                    rpl = contentObject != null ? ("<LST " + contentObject.getName() + ">") : "";
                }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_LOCAL_IMAGE_LIBRARY)){
                    rpl = contentObject != null ? ("<LIL " + contentObject.getName() + ">") : "";
                }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_IMAGE_LIBRARY)){
                    rpl = contentObject != null ? ("<IL " + contentObject.getName() + ">") : "";
                }
            }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_VARIABLE)){
                DataElementVariable variable = null;
                if(StringUtils.isNumeric(tokens[1])){
                    variable = DataElementVariable.findById(Long.parseLong(tokens[1]));
                }else{
                    variable = DataElementVariable.findByDna(tokens[1]);
                }
                rpl = variable!=null?("<VAR " + variable.getName() + ">"):"";
            }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_CONTENT_TARGETING)){
                if(StringUtils.isNumeric(tokens[1])){
                    List<Long> keyList = new ArrayList<>(this.referencedInlineTargetingMap.keySet());
                    int idx = keyList.indexOf(Long.parseLong(tokens[1]));
                    rpl = " <IT " + String.format("%03d", ++idx) + ">";
                }
            }else if(Integer.toString(type).equals(ContentObjectContentUtil.VAR_TYPE_DATABASE_FILE)){
                if(StringUtils.isNumeric(tokens[1])) {
                    String filename = DatabaseFile.findFilenameById(Long.parseLong(tokens[1]));
                    rpl = filename!=null?filename:"";
                }
            }

            contentStr = !rpl.isEmpty()?contentStr.replace(groupText, rpl):contentStr;
        }

        return contentStr;
    }

    private void addReferencedContent(ContentObject coReferencedIn, ContentObjectAssociation coa){
        ContentObjectAssociation resolvedCoa = resolveReferencedCoa(coReferencedIn, coa);
        Content content = resolvedCoa.getContent();
        if(content != null){
            Set<ContentObject> refCos = new HashSet<>();
            refCos.addAll(content.getGlobalSmartTexts());
            refCos.addAll(content.getLocalSmartTexts());
            refCos.addAll(content.getGlobalImageLibraryItems());
            refCos.addAll(content.getLocalImageLibraryItems());
            // Check the referenced marks to see whether the referenced content object has already being checked in the content
            Set<ContentObject> refCosToRemove = new HashSet<>();
            for(ContentObject refCo : refCos) {
                if (!this.referencedCheckedMap.containsKey(refCo.getId())) {
                    this.referencedCheckedMap.put(refCo.getId(), new ArrayList<>());
                }
                // Use composed key here as a content might be inherited or referenced
                String coaContentIdStr = String.valueOf(coa.getId()) + content.getId();
                if (this.referencedCheckedMap.get(refCo.getId()).contains(coaContentIdStr)) {
                    refCosToRemove.add(refCo);
                } else {
                    this.referencedCheckedMap.get(refCo.getId()).add(coaContentIdStr);
                }
            }
            refCos.removeAll(refCosToRemove);
            addReferencedNumRecursively(coReferencedIn, refCos);
        }
    }

    private void addReferencedNumRecursively(ContentObject coReferencedIn, Set<ContentObject> references){
        if(references == null || references.isEmpty()){
            return;
        }
        for(ContentObject reference : references) {
            // Add up to the number referenced map
            if (!this.referencedNumMap.containsKey(reference)) {
                Map<Integer, Integer> referencedInMap = new HashMap<>();
                referencedInMap.put(coReferencedIn.getObjectType(),1);
                this.referencedNumMap.put(reference, referencedInMap);
            } else if(!this.referencedNumMap.get(reference).containsKey(coReferencedIn.getObjectType())) {
                this.referencedNumMap.get(reference).put(coReferencedIn.getObjectType(), 1);
            } else {
                int numReferences = this.referencedNumMap.get(reference).get(coReferencedIn.getObjectType());
                this.referencedNumMap.get(reference).put(coReferencedIn.getObjectType(), ++numReferences);
            }

            List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(reference, reference.getFocusOnDataType(),
                    null, null, null, false, true, true, false, true);

            for (ContentObjectAssociation ca : cas) {
                addReferencedContent(reference, ca);
            }
        }
    }

    private String buildConditionValue(ContentObject contentObject, ParameterGroupTreeNode tppgtn, ParameterGroupTreeNode copgtn){
        StringBuilder conditionValSb = new StringBuilder();
        if(tppgtn == null && contentObject.getOwningTouchpointSelection() != null){
            tppgtn = contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode();
        }
        if(tppgtn != null){
            conditionValSb.append(tppgtn.translateSelectorValuesToView());
        }

        // Content Object targeting
        ContentObjectData cod = contentObject.getContentObjectDataWorkingCentric();
        if(cod!=null && cod.getDataType()!=contentObject.getFocusOnDataType()) {
            contentObject.setFocusOnDataTypeCheckAndAdjust(cod.getDataType());
        }
        if(contentObject.getTargetGroupCount() != 0){
            conditionValSb.append((conditionValSb.isEmpty() ?"":" AND ")).append(Jsoup.parse(TargetingSummaryUtil.generateSummaryHtml(cod, null)).text());
        }

        if(copgtn != null) {
            conditionValSb.append((conditionValSb.isEmpty() ?"":" AND ")).append(copgtn.translateSelectorValuesToView());
        }
        return conditionValSb.toString();
    }

    private ContentObjectAssociation resolveReferencedCoa(ContentObject co, ContentObjectAssociation coa){
        if (coa.getTypeId() == ContentAssociationType.ID_REFERENCES) {
            while(coa.getTypeId() == ContentAssociationType.ID_REFERENCES && coa.getReferencingTouchpointPGTreeNode() != null) {
                coa = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(co, coa.getReferencingTouchpointPGTreeNode(), coa.getMessagepointLocale(), coa.getZonePart());
            }
            return coa;
        } else if (coa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
            // If the content is the same as default, retrieve the default content
            MessagepointLocale defaultLocale = co.isGlobalContentObject()?MessagepointLocale.getDefaultSystemLanguageLocale():coa.getDocument().getDefaultTouchpointLanguageLocale();
            ContentObjectAssociation defaultCoa = ContentObjectAssociation.findByContentObjectAndParameters(co, coa.getDataType(), coa.getTouchpointPGTreeNode(), defaultLocale, coa.getZonePart());
            // Fill in tht empty default coa if it does not exist
            if(defaultCoa == null){
                defaultCoa = new ContentObjectAssociation();
                defaultCoa.setContentObject(coa.getContentObject());
                defaultCoa.setZonePart(coa.getZonePart());
                defaultCoa.setDataType(coa.getDataType());
                defaultCoa.setMessagepointLocale(defaultLocale);
                defaultCoa.setTouchpointPGTreeNode(coa.getTouchpointPGTreeNode());
                defaultCoa.setContentObjectPGTreeNode(coa.getContentObjectPGTreeNode());
                defaultCoa.setTypeId(ContentAssociationType.ID_EMPTY);
            }
            return defaultCoa;
        }else{
            return coa;
        }
    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeOnlyActiveContent, User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportContentToExcelServiceRequest request = new ExportContentToExcelServiceRequest(exportId, targetObjectId, null, includeOnlyActiveContent, requestor, statusPollingBackgroundTask);

        context.setRequest(request);

        ExportToExcelServiceResponse serviceResp = new ExportToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}

