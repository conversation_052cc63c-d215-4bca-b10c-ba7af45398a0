package com.prinova.messagepoint.platform.services.export;

import java.util.List;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportMessagepointObjectBackgroundTask;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class GenerateMessagepointObjectExportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateMessagepointObjectExportService";
	
	private static final Log log = LogUtil.getLog(GenerateMessagepointObjectExportService.class);
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateMessagepointObjectExportServiceRequest request = (GenerateMessagepointObjectExportServiceRequest) context.getRequest();
			
			ExportMessagepointObjectBackgroundTask task = new ExportMessagepointObjectBackgroundTask(
																request.getExportId(),
																request.getTargetObjectId(),
																request.getTargetDataType(),
																request.getSelectedIds(),
																request.getExportOptions(), 
																request.getTargetClass(),
																request.getRequestingUser());
			MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY, request.getRequestingUser());
			
			GenerateMessagepointObjectExportServiceResponse response = (GenerateMessagepointObjectExportServiceResponse) context.getResponse();
			response.setBackgroundTaskId(task.getBackgroundTaskId());
			response.setResultValueBean(request.getExportId());

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateMessagepointObjectExportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, ContentObject.DATA_TYPE_WORKING, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, ContentObject contentObject, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, contentObject.getId(), contentObject.getFocusOnDataType(), null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, ContentObject contentObject, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType, String targetLocaleCode) {
		return createContext(exportId, contentObject, null, includeImagePathOnly, false, targetClass,requestingUser, exportType, targetLocaleCode);
	}

    public static ServiceExecutionContext createContext(String exportId, ContentObject contentObject, List<Long> selectedIds, boolean includeImagePathOnly,boolean tryActiveDataIfNoWorkingData, Class<?> targetClass, User requestingUser, int exportType, String targetLocaleCode) {
        ExportImportOptions exportOptions = new ExportImportOptions();
        exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
        exportOptions.setExportExtType(exportType);
        exportOptions.setTargetLocaleCode(targetLocaleCode);
        exportOptions.setTryActiveCopyWhenWorkingNotExist(tryActiveDataIfNoWorkingData);
        return createContext(exportId, contentObject.getId(), contentObject.getFocusOnDataType(), selectedIds, exportOptions, targetClass, requestingUser);
    }

    public static ServiceExecutionContext createContext(String exportId, Document document, boolean includeImagePathOnly, boolean tryActiveDataIfNoWorkingData, Class<?> targetClass, User requestingUser, int exportType, String targetLocaleCode) {
        ExportImportOptions exportOptions = new ExportImportOptions();
        exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
        exportOptions.setExportExtType(exportType);
        exportOptions.setTargetLocaleCode(targetLocaleCode);
        exportOptions.setTryActiveCopyWhenWorkingNotExist(tryActiveDataIfNoWorkingData);
        return createContext(exportId, document.getId(), ContentObject.DATA_TYPE_WORKING, null, exportOptions, targetClass, requestingUser);
    }

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType, int historyType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		exportOptions.setExportHistoryType(historyType);
		return createContext(exportId, targetObjectId, ContentObject.DATA_TYPE_WORKING, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, List<Long> selectedIds, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, ContentObject.DATA_TYPE_WORKING, selectedIds, exportOptions, targetClass, requestingUser);
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {
		exportOptions.setExportExtType(0);
		return createContext(exportId, targetObjectId, ContentObject.DATA_TYPE_WORKING, null, exportOptions, targetClass, requestingUser);
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser, int exportType) {
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, ContentObject.DATA_TYPE_WORKING, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, int targetDataType, List<Long> selectedIds, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateMessagepointObjectExportServiceRequest request = new GenerateMessagepointObjectExportServiceRequest();

		request.setExportId(exportId);
		request.setTargetObjectId(targetObjectId);
		request.setTargetDataType(targetDataType);
		request.setExportOptions(exportOptions);
		request.setTargetClass(targetClass);
		request.setRequestingUser(requestingUser);
		request.setSelectedIds(selectedIds);

		context.setRequest(request);

		GenerateMessagepointObjectExportServiceResponse response = new GenerateMessagepointObjectExportServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
	
	public static ServiceExecutionContext createContext(String exportId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateMessagepointObjectExportServiceRequest request = new GenerateMessagepointObjectExportServiceRequest();

		request.setExportId(exportId);
		request.setExportOptions(exportOptions);
		request.setTargetClass(targetClass);
		request.setRequestingUser(requestingUser);

		context.setRequest(request);

		GenerateMessagepointObjectExportServiceResponse response = new GenerateMessagepointObjectExportServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}