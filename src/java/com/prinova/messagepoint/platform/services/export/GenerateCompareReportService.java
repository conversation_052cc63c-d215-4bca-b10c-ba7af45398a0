package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.CompareReport;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;


public class GenerateCompareReportService extends AbstractService {


	public static final String SERVICE_NAME = "export.GenerateCompareReportService";
	private static final Log log = LogUtil.getLog(GenerateCompareReportService.class);
	public static final String TRANSITION_FILE_EXT 	= ".inProcess";
	
	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateCompareReportServiceRequest request = (GenerateCompareReportServiceRequest) context.getRequest();

			CompareReportRunner compareReportRunner = new CompareReportRunner(request.getAuditReportId(),
                    request.getTpSelection(),
                    request.getRequestor(),
                    request.getDocument(),
                    request.getSyncSibling(),
                    request.isSyncFromOrigin(),
                    request.isSyncWithParent(),
                    request.getLocalContext(),
                    request.getContentObjectFilter(),
                    request.getOtherInstanceId(),
                    request.getInstanceId(),
                    request.isSyncMultiWay(),
                    request.getSiblingParentInstanceId(),
                    request.getSiblingParentId(),
                    request.getParentInstanceId(),
                    request.getParentId(),
                    request.getSelectedObjects());
	   		MessagePointRunnableUtil.startThread(compareReportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateMessageAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}
	
	private static class CompareReportRunner extends MessagePointRunnable {

		private long 				reportId;
		private Class<?>			type;
		private TouchpointSelection tpSelection;
		private User 				requestor;
		private long				localContext;
		private Document 			document;
		private boolean 			syncFromOrigin = true;
		private boolean 			syncWithParent = true;
		private long 				otherObjectId;
		private long 				otherInstanceId;
		private int 				contentObjectFilter;
		private long 				instanceId;
		private boolean 			syncMultiWay= false;
		private long 				siblingParentInstanceId;
		private long 				siblingParentId;
		private long 				parentInstanceId;
		private long 				parentId;
		private List<String> 		selectedObjects = new ArrayList<>();
		
		private final StatusPollingBackgroundTask statusPollingBackgroundTask;
		
		public CompareReportRunner(long auditReportId, 
										TouchpointSelection tpSelection,
										User requestor,
										Document document,
										long otherObjectId,
										Boolean syncFromOrigin,
										Boolean syncWithParent,
										long localContext,
										int contentObjectFilter,
										long otherInstanceId,
										long instanceId,
										boolean syncMultiWay,
										long siblingParentInstanceId,
										long siblingParentId,
										long parentInstanceId,
										long parentId,
										List<String> selectedObjects) {
			
			this.reportId = auditReportId;
			this.type = Document.class;
			this.tpSelection = tpSelection;
			this.document = document;
			this.otherObjectId = otherObjectId;
			this.requestor = requestor;
			this.setLocalUser(requestor);
			this.syncFromOrigin = syncFromOrigin;
			this.syncWithParent = syncWithParent;
			this.localContext = localContext;
			this.contentObjectFilter = contentObjectFilter;
			this.otherInstanceId = otherInstanceId;
			this.instanceId = instanceId;
			this.syncMultiWay = syncMultiWay;
			this.parentInstanceId = parentInstanceId;
			this.parentId = parentId;
			this.siblingParentInstanceId = siblingParentInstanceId;
			this.siblingParentId = siblingParentId;
			
			if(selectedObjects != null)
				this.selectedObjects = selectedObjects;
			
			this.statusPollingBackgroundTask = new StatusPollingBackgroundTask();
			this.statusPollingBackgroundTask.setCreatedBy(requestor.getId());
		}
		

		public void performMainProcessing() {
			long userId = this.requestor.getId();
			try {
				
				this.statusPollingBackgroundTask.setType(StatusPollingBackgroundTask.TYPE_SYNC_REPORT);
				this.statusPollingBackgroundTask.setName(StatusPollingBackgroundTask.SUB_TYPE_SYNC_REPORT);
				
				this.statusPollingBackgroundTask.setRequestDate(new Date(System.currentTimeMillis()));
				this.statusPollingBackgroundTask.setBackgroundThread(this.getOwningThread());
				this.statusPollingBackgroundTask.setBackgroundThreadId(this.getOwningThread().getId());
				this.statusPollingBackgroundTask.setProgressInPercentInThread(0);
				this.statusPollingBackgroundTask.setDescription(document.getName());
				this.statusPollingBackgroundTask.setActive(true);
				this.statusPollingBackgroundTask.save();
				
				// Delete File - remove any existing reports for user
				File srcDir = new File(CompareReport.getReportsRootPath());
				if (!srcDir.exists())
					srcDir.mkdir();
				String usersReportFileMarker = CompareReport.REPORT_NAME_PREFIX + "_" + userId +"_*";
				FileUtil.deleteAllByRegex(usersReportFileMarker, srcDir);
					
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
				int auditReportType = AuditReportType.ID_TOUCHPOINT_COMPARE_REPORT;
				
				CompareReport compareReport = new CompareReport(document.getId(), syncFromOrigin, otherObjectId, otherInstanceId, 
						syncMultiWay, siblingParentInstanceId, siblingParentId, parentInstanceId, parentId,
						selectedObjects, UserUtil.getPrincipalUser());
				compareReport.generateReportXML();
				
				String xmlFilePath = ExportUtil.saveXMLToFile(compareReport.getDocument(), requestor, auditReportType, 0);
				String reportHtmlFilePath = ExportUtil.transformXML(xmlFilePath, this.requestor, auditReportType, 0);
				String filePath = ApplicationUtil.getRootPath().concat(reportHtmlFilePath);
				
				this.statusPollingBackgroundTask.setOutputFilename(xmlFilePath);
				this.statusPollingBackgroundTask.setOutputPath(filePath);
				this.statusPollingBackgroundTask.setProgressInPercentInThread(100);
				this.statusPollingBackgroundTask.setCompletedDate(new Date(System.currentTimeMillis()));
				this.statusPollingBackgroundTask.setComplete(true);
				this.statusPollingBackgroundTask.setActive(false);
				this.statusPollingBackgroundTask.save();
				
				ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(reportId, 
						auditReportType,
						xmlFilePath, 
						reportHtmlFilePath);
				
				
				Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				updateReportService.execute(updateReportServiceContext);
				
				
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Sync Report Runner caught exception: " + sw);
				this.statusPollingBackgroundTask.setDescription(document.getName() + sw);
				this.statusPollingBackgroundTask.setError(true);
				this.statusPollingBackgroundTask.setActive(false);
				this.statusPollingBackgroundTask.save();
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.reportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
				
			}
		}
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	
	public static ServiceExecutionContext createContext(long auditReportId, TouchpointSelection touchpointSelection,
			User requestor, Document document, Long syncSibling, Long otherInstanceId, boolean syncFromOrigin, 
			boolean syncWithParent, int contentObjectFilter, long instanceId, boolean syncMultiWay,
			List<String> selectedObjects) {
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateCompareReportServiceRequest request = new GenerateCompareReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setTpSelection(touchpointSelection);
		request.setRequestor(requestor);
		request.setDocument(document);
		request.setSyncSibling((long)syncSibling);
		request.setSyncFromOrigin(syncFromOrigin);
		request.setSyncWithParent(syncWithParent);
		request.setContentObjectFilter(contentObjectFilter);
		request.setOtherInstanceId((long)otherInstanceId);
		request.setInstanceId(instanceId);
		request.setSyncMultiWay(syncMultiWay);
		request.setSelectedObjects(selectedObjects);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
	
	public static ServiceExecutionContext createContext(long auditReportId, TouchpointSelection touchpointSelection,
			User requestor, Document document, Long syncSibling, Long otherInstanceId, boolean syncFromOrigin, 
			boolean syncWithParent, int contentObjectFilter, long instanceId, boolean syncMultiWay,
			long siblingParentInstanceId, long siblingParentId, long parentInstanceId, long parentId,
			List<String> selectedObjects) {
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateCompareReportServiceRequest request = new GenerateCompareReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setTpSelection(touchpointSelection);
		request.setRequestor(requestor);
		request.setDocument(document);
		request.setSyncSibling((long)syncSibling);
		request.setSyncFromOrigin(syncFromOrigin);
		request.setSyncWithParent(syncWithParent);
		request.setContentObjectFilter(contentObjectFilter);
		request.setOtherInstanceId((long)otherInstanceId);
		request.setInstanceId(instanceId);
		request.setSyncMultiWay(syncMultiWay);
		request.setSiblingParentInstanceId(siblingParentInstanceId);
		request.setSiblingParentId(siblingParentId);
		request.setParentInstanceId(parentInstanceId);
		request.setParentId(parentId);
		request.setSelectedObjects(selectedObjects);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}