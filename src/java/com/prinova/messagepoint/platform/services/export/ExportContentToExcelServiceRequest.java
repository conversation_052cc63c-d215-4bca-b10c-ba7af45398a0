package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.List;

public class ExportContentToExcelServiceRequest extends SimpleServiceRequest {

    private static final long serialVersionUID = -3613858253259861590L;
    private String 		exportId;
    private long 		targeObjectId;
    private List<String> selectedIds;
    private boolean     includeOnlyActiveContent;
    private User 		requestor;
    private ExportImportOptions exportOptions;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;


    public ExportContentToExcelServiceRequest(String exportId, long targeObjectId, List<String> selectedIds, boolean includeActiveOnly, User user ) {
    	this(exportId, targeObjectId, selectedIds, includeActiveOnly, user, null);
    }

    public ExportContentToExcelServiceRequest(String exportId, long targeObjectId, List<String> selectedIds, boolean includeActiveOnly, User user, StatusPollingBackgroundTask statusPollingBackgroundTask ) {
        this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        this.selectedIds = selectedIds;
        this.includeOnlyActiveContent = includeActiveOnly;
        this.requestor = user;
        this.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
    }

	public String getExportId() {
		return exportId;
	}

	public long getTargeObjectId() {
        return targeObjectId;
    }

    public List<String> getSelectedIds() {
		return selectedIds;
	}

    public boolean isIncludeOnlyActiveContent() {
        return includeOnlyActiveContent;
    }

    public void setIncludeOnlyActiveContent(boolean includeOnlyActiveContent) {
        this.includeOnlyActiveContent = includeOnlyActiveContent;
    }

    public User getRequestor() {
        return requestor;
    }

	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }
}
