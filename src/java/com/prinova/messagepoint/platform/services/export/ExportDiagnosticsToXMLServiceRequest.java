package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportDiagnosticsToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 8206574215195072677L;

	private long 				reportId;
	private User				user;

	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}
	public User getUser() {
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}

}