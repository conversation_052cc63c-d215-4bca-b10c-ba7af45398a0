package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ContentObjectVariantContentExport {


    public static LinkedHashMap<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> loadContentObjectVariantContent(ContentObject contentObject) {

        if (contentObject == null) {
            return new LinkedHashMap<>();
        }

        List<MessagepointLocale> locales = contentObject.getContentObjectLanguagesAsLocales();

        int dataType = contentObject.getFocusOnDataType();

        List<ContentObjectAssociation> assoc = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(),
                null, null, null, false, true, true, false, false);

        Map<String, Map<MessagepointLocale, ContentObjectAssociation>> unorderedMap = loadContentObjectVariantContentUnordered(assoc);

        LinkedHashMap<ParameterGroupTreeNode, Map<MessagepointLocale, ContentObjectAssociation>> result = new LinkedHashMap<>();

        List<ParameterGroupTreeNode> insertOrderList = new ArrayList<>();

        List<ContentObjectAssociation> masterLevel = assoc.stream()
                .filter(x -> x.getPGTreeNode() == null)
                .toList();

        for (ContentObjectAssociation coa : masterLevel) {
            if (!result.containsKey(null)) {
                result.put(null, new HashMap<>());
            }

            result.get(null).put(coa.getMessagepointLocale(), coa);
        }

        List<ParameterGroupTreeNode> topLevel = assoc.stream()
                .map(ContentObjectAssociation::getPGTreeNode)
                .filter(Objects::nonNull)
                .filter(x -> x.getParentNode() == null)
                .distinct()
                .sorted((x, y) -> StringUtils.compare(x.getName(), y.getName()))
                .toList();

        List<ParameterGroupTreeNode> allNodesFromCOAs = assoc.stream()
                .map(ContentObjectAssociation::getPGTreeNode)
                .filter(Objects::nonNull)
                .distinct()
                .sorted((x, y) -> StringUtils.compare(x.getName(), y.getName()))
                .toList();

        List<ParameterGroupTreeNode> allNodes = new ArrayList<>();
        for (ParameterGroupTreeNode node : allNodesFromCOAs) {
            if (node.getDataType() != dataType) {
                node = ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(node, dataType);
            }
            allNodes.add(node);
        }

        for (ParameterGroupTreeNode node : topLevel) {
            if (node.getDataType() != dataType) {
                node = ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(node, dataType);
            }
            insertOrderList.add(node);
            pushChildTreeNodes(insertOrderList, node, allNodes);
        }

        for (ParameterGroupTreeNode node : insertOrderList) {
            result.put(node, unorderedMap.get(node.getDna()));
        }

        // Fill the missing locales and remove the ones that are not in the locales list
        for (ParameterGroupTreeNode node : result.keySet()) {
            Map<MessagepointLocale, ContentObjectAssociation> localeMap = result.get(node);
            for (MessagepointLocale locale : locales) {
                if (!localeMap.containsKey(locale)) {
                    ContentObjectAssociation coa = new ContentObjectAssociation();
                    coa.setContentObject(contentObject);
                    coa.setTypeId(node==null?ContentAssociationType.ID_SAME_AS_DEFAULT:ContentAssociationType.ID_REFERENCES);
                    localeMap.put(locale, coa);
                }
            }

            for(MessagepointLocale locale : new ArrayList<>(localeMap.keySet())){
                if(!locales.contains(locale)){
                    localeMap.remove(locale);
                }
            }
        }

        return result;
    }

    private static void pushChildTreeNodes(List<ParameterGroupTreeNode> insertOrderList, ParameterGroupTreeNode node, List<ParameterGroupTreeNode> allNodes) {

        Set<Long> childIds = node.getChildren().stream().map(IdentifiableMessagePointModel::getId).collect(Collectors.toSet());

        List<ParameterGroupTreeNode> children = allNodes.stream()
                .filter(x -> x != null && childIds.contains(x.getId()))
                .sorted((x, y) -> StringUtils.compare(x.getName(), y.getName()))
                .toList();

        for (ParameterGroupTreeNode child : children) {
            insertOrderList.add(child);
            pushChildTreeNodes(insertOrderList, child, allNodes);
        }

    }

    private static Map<String, Map<MessagepointLocale, ContentObjectAssociation>> loadContentObjectVariantContentUnordered(List<ContentObjectAssociation> assoc) {

        Comparator<ContentObjectAssociation> sortCompare = Comparator.comparing(x -> x.getPGTreeNode() != null ? x.getPGTreeNode().getId() : -1L);

        Map<String, Map<MessagepointLocale, ContentObjectAssociation>> pgTnLocaleCAs = new HashMap<>();

        for (ContentObjectAssociation association : assoc.stream().sorted(sortCompare).toList()) {

            if (association.getPGTreeNode()  != null) {

                if (!pgTnLocaleCAs.containsKey(association.getPGTreeNode().getDna())) {
                    pgTnLocaleCAs.put(association.getPGTreeNode().getDna(), new HashMap<>());
                }

                if (!pgTnLocaleCAs.get(association.getPGTreeNode().getDna()).containsKey(association.getMessagepointLocale())) {
                    pgTnLocaleCAs.get(association.getPGTreeNode().getDna()).put(association.getMessagepointLocale(), association);
                }

            }

        }

        return pgTnLocaleCAs;
    }
}
