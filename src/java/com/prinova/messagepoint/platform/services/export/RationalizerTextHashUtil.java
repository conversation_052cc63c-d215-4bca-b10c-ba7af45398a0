package com.prinova.messagepoint.platform.services.export;

import com.google.common.hash.Hashing;
import com.prinova.messagepoint.util.StringUtil;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class RationalizerTextHashUtil {
    private static final int ONE_HUNDRED_SEED = 100;
    private static final int TWO_HUNDREDS_SEED = 200;
    private static final String EMPTY_TEXT = "";

    private RationalizerTextHashUtil() {
    }

    public static String computeHashUsingOneHundredSeed(String text) {
        if (StringUtil.isEmptyOrNull(text)) {
            text = EMPTY_TEXT;
        }
        return Hashing.murmur3_128(ONE_HUNDRED_SEED)
                .hashString(text, StandardCharsets.UTF_8)
                .toString();
    }

    public static String computeHashUsingTwoHundredsSeed(String text) {
        if (StringUtil.isEmptyOrNull(text)) {
            text = EMPTY_TEXT;
        }
        return Hashing.murmur3_128(TWO_HUNDREDS_SEED)
                .hashString(text, StandardCharsets.UTF_8)
                .toString();
    }

    public static String generateUniqueId() {
        long val = -1;
        do {
            final UUID uid = UUID.randomUUID();
            final ByteBuffer buffer = ByteBuffer.wrap(new byte[16]);
            buffer.putLong(uid.getLeastSignificantBits());
            buffer.putLong(uid.getMostSignificantBits());
            final BigInteger bi = new BigInteger(buffer.array());
            val = bi.longValue();
        }
        // We also make sure that the ID is in positive space, if its not we simply repeat the process
        while (val < 0);

        return "" + val;
    }

    public static Long generateUniqueLongId() {
        long val = -1;
        do {
            final UUID uid = UUID.randomUUID();
            final ByteBuffer buffer = ByteBuffer.wrap(new byte[16]);
            buffer.putLong(uid.getLeastSignificantBits());
            buffer.putLong(uid.getMostSignificantBits());
            final BigInteger bi = new BigInteger(buffer.array());
            val = bi.longValue();
        }
        // We also make sure that the ID is in positive space, if its not we simply repeat the process
        while (val < 0);

        return val;
    }
}