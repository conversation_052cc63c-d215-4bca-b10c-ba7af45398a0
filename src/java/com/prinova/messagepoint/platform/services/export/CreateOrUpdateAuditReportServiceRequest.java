package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.externalevent.ExternalEvent;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class CreateOrUpdateAuditReportServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 2758490951613657508L;

	private long reportId;
	private int typeId;
	private String xmlPath;
	private String reportPath;
	private Long requestorId;
	private int action;
	private ExternalEvent externalEvent;
	
	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}
	public int getTypeId() {
		return typeId;
	}
	public void setTypeId(int typeId) {
		this.typeId = typeId;
	}
	public String getXmlPath() {
		return xmlPath;
	}
	public void setXmlPath(String xmlPath) {
		this.xmlPath = xmlPath;
	}
	public String getReportPath() {
		return reportPath;
	}
	public void setReportPath(String reportPath) {
		this.reportPath = reportPath;
	}
	public Long getRequestorId() {
		return requestorId;
	}
	public void setRequestorId(Long requestorId) {
		this.requestorId = requestorId;
	}
	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}
	public ExternalEvent getExternalEvent() {
		return externalEvent;
	}
	public void setExternalEvent(ExternalEvent externalEvent) {
		this.externalEvent = externalEvent;
	}	
}