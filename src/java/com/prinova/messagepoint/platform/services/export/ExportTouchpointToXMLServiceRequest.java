package com.prinova.messagepoint.platform.services.export;

import java.util.Date;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportTouchpointToXMLServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = -1756273477511353408L;
	
	private User requestor;
	private Date fromDate;
	private Date toDate;	
	private String fromDateStr;
	private String toDateStr;
	private long reportTimestamp;
	private String externalEventKey;
	private Document document;

	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}	
	public Date getFromDate() {
		return fromDate;
	}
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	public Date getToDate() {
		return toDate;
	}
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}	
	public long getReportTimestamp(){
		return this.reportTimestamp;
	}
	public void setReportTimestamp(long reportTimestamp){
		this.reportTimestamp = reportTimestamp;
	}
	public String getExternalEventKey(){
		return externalEventKey;
	}
	public void setExternalEventKey(String externalEventKey){
		this.externalEventKey = externalEventKey;
	}
	public String getFromDateStr(){
		return fromDateStr;
	}
	public void setFromDateStr(String fromDateStr){
		this.fromDateStr = fromDateStr;
	}
	public String getToDateStr(){
		return toDateStr;
	}
	public void setToDateStr(String toDateStr){
		this.toDateStr = toDateStr;
	}	
	public Document getDocument(){
		return document;
	}
	public void setDocument(Document document){
		this.document = document;
	}	
}
