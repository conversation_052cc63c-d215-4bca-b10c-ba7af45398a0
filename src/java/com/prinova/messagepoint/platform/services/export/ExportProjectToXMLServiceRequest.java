package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportProjectToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 132240516490390589L;

	private List<Long> projectList = new ArrayList<>();
	private boolean includeAllProjects;
	private User requestor;
	private long reportTimestamp;
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public List<Long> getProjectList() {
		return projectList;
	}
	public void setProjectList(List<Long> projectList) {
		this.projectList = projectList;
	}
	public boolean isIncludeAllProjects() {
		return includeAllProjects;
	}
	public void setIncludeAllProjects(boolean includeAllProjects) {
		this.includeAllProjects = includeAllProjects;
	}
	public long getReportTimestamp() {
		return reportTimestamp;
	}
	public void setReportTimestamp(long reportTimestamp) {
		this.reportTimestamp = reportTimestamp;
	}
	
}