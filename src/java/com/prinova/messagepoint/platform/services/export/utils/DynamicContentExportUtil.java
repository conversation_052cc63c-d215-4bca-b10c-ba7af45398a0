package com.prinova.messagepoint.platform.services.export.utils;

import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;

public class DynamicContentExportUtil {

    private static final String EMPTY_LABEL = "Empty";
    private static final String INHERIT_LABEL = "Inheriting from parent";
    private static final String SAME_AS_DEFAULT = "Same as default";

    public static String getVariantExportContent(ContentObjectAssociation ca) {
        String result = "";

        if (ca != null)
        {
            boolean isImage = ca.getContentObject().isGlobalImage() || ca.getContentObject().isLocalImage();

            if (ca.getTypeId() == ContentAssociationType.ID_OWNS) {
                if (isImage && ca.getContent() != null && ca.getContent().getImageName() != null) {
                    result = ca.getContent().getImageName();
                } else if (isImage && ca.getReferencingImageLibrary() != null) {
                    result = ca.getReferencingImageLibrary().getName();
                } else {
                    result = ca.getContent() != null ? ca.getContent().getEncodedContent() : null;
                }
            } else if(ca.getTypeId() == ContentAssociationType.ID_REFERENCES){
                result = INHERIT_LABEL;
            } else if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
                result = SAME_AS_DEFAULT;
            }else {
                // case for Copy Content
                if(ca.getContent() != null && ca.getContent().hasGlobalSmartTexts())
                    result = ca.getContent().getEncodedContent();
                else
                    result = EMPTY_LABEL;
            }
        }
        else
        {
            result = EMPTY_LABEL;
        }

        return result != null ? result : EMPTY_LABEL;
    }

}
