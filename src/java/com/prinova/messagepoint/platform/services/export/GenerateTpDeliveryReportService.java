package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class GenerateTpDeliveryReportService extends AbstractService {
	public static final String SERVICE_NAME = "export.GenerateTpDeliveryReportService";
	private static final Log log = LogUtil.getLog(GenerateTpDeliveryReportService.class);
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			} 
			GenerateTpDeliveryReportServiceRequest request = (GenerateTpDeliveryReportServiceRequest) context.getRequest();

			TpDeliveryReportRunner messageExportRunner = new TpDeliveryReportRunner(
                    request.getReportId(),
                    request.getRequestor(),
                    request.getReportScenario());
	   		MessagePointRunnableUtil.startThread(messageExportRunner, Thread.MAX_PRIORITY);			
		}catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateTpDeliveryReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private static class TpDeliveryReportRunner extends MessagePointRunnable {
		
		private long reportId;
		private User requestor;
		private TpDeliveryReportScenario reportScenario;
		
		public TpDeliveryReportRunner(long reportId, User requestor, TpDeliveryReportScenario reportScenario){
			this.reportId = reportId;
			this.requestor = requestor;		
			this.reportScenario = reportScenario;
		}
	
		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			// *****************************************
			// ********* Generate XML ******************
			// *****************************************
			ServiceExecutionContext exportServiceContext = ExportTpDeliveryReportToXMLService.createContext(
																		this.reportId, 
																		this.requestor, 
																		this.reportScenario);
			
			Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportTpDeliveryReportToXMLService.SERVICE_NAME, ExportTpDeliveryReportToXMLService.class);
	    	exportService.execute(exportServiceContext);		
		}
	}
	
	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(long reportId, User requestor, TpDeliveryReportScenario reportScenario){
		ServiceExecutionContext context = new SimpleExecutionContext();
		
		GenerateTpDeliveryReportServiceRequest request = new GenerateTpDeliveryReportServiceRequest();
		
		request.setReportId(reportId);
		request.setRequestor(requestor);
		request.setReportScenario(reportScenario);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
	
}
