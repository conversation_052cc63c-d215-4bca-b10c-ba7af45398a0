package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportWhereUsedToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 8206574215195072677L;

	private long 				reportId;
	private long				targetObjectId;
	private Class<?> 			targetClass;
	private boolean 			includeActiveObjOnly;
	private String[] 			indirectReferences;
	private User				user;

	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}

	public long getTargetObjectId() {
		return targetObjectId;
	}
	public void setTargetObjectId(long targetObjectId) {
		this.targetObjectId = targetObjectId;
	}

	public Class<?> getTargetClass() {
		return targetClass;
	}
	public void setTargetClass(Class<?> targetClass) {
		this.targetClass = targetClass;
	}

	public boolean isIncludeActiveObjOnly() {
		return includeActiveObjOnly;
	}
	public void setIncludeActiveObjOnly(boolean includeActiveObjOnly) {
		this.includeActiveObjOnly = includeActiveObjOnly;
	}

	public String[] getIndirectReferences() {
		return indirectReferences;
	}
	public void setIndirectReferences(String[] indirectReferences) {
		this.indirectReferences = indirectReferences;
	}

	public User getUser() {
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}

}