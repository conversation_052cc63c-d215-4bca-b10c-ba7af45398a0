package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import java.util.HashMap;
import java.util.Map;

public enum RationalizerDashboardExportType {
    DUPLICATES(100, "DUPLICATES"),
    SIMILARITY(101, "SIMILARITY"),
    SENTIMENT(102, "SENTIMENT"),
    READABILITY(103, "READABILITY"),
    BRAND(104, "BRAND");

    private final int id;
    private final String value;

    private static final Map<Integer, RationalizerDashboardExportType> lookup = new HashMap<>();

    static {
        for (RationalizerDashboardExportType s : RationalizerDashboardExportType.values()) {
            lookup.put(s.getId(), s);
        }
    }

    RationalizerDashboardExportType(int id, String value) {
        this.id = id;
        this.value = value;
    }

    public int getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    public static RationalizerDashboardExportType fromId(int id) {
        return lookup.get(id);
    }
}
