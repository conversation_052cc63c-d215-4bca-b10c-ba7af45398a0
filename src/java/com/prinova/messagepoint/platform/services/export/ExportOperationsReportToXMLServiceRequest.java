package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.scenario.OperationsReportScenario;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportOperationsReportToXMLServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = 724608631658415739L;
	
	private long reportId;
	private User requestor;		
	private OperationsReportScenario reportScenario;
	
	public long getReportId() {
		return reportId;
	}
	public void setReportId(long reportId) {
		this.reportId = reportId;
	}
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}		
	public OperationsReportScenario getReportScenario(){
		return reportScenario;
	}
	public void setReportScenario(OperationsReportScenario reportScenario){
		this.reportScenario = reportScenario;
	}
	
}
