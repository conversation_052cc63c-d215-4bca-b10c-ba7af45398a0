package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.metadata.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.*;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.model.testing.DataFilePreviewLanguage;
import com.prinova.messagepoint.model.util.JSONLayoutUtil;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.util.XmlLayoutUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.json.JSONArray;
import org.json.JSONObject;


public class ExportDocumentToXMLService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportDocumentToXMLService";
    
    private static final Log log = LogUtil.getLog(ExportDocumentToXMLService.class);
    private User requestor = null;
	private ExportImportOptions exportOptions;
	private final List<String> originalImageFiles = new ArrayList<>();
	
	private Set<MetadataFormDefinition> metadataFormDefinitions = new LinkedHashSet<>();
	private Element metadataTemplatesElement;
	
	private Set<DataFile> dataFiles = new LinkedHashSet<>();
	private Element dataFilesListElement;
    private Set<DataResource> dataResources = new LinkedHashSet<>();

    private Element dataResourcesListElement;
	
    Set<DatabaseFile> databaseFiles = new HashSet<>();
	private Element databaseFilesElement;

	@Override
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportDocumentToXMLServiceRequest request = (ExportDocumentToXMLServiceRequest) context.getRequest();
            
            Document doc = Document.findById(request.getDocumentId());
            requestor = request.getRequestor();
            exportOptions = request.getExportOptions();
            originalImageFiles.clear();
            
            org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
            xmlDoc.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
            generateXML(doc, xmlDoc, request.getStatusPollingBackgroundTask());
            
            String exportName = doc.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportXMLToFile(xmlDoc, 
            		requestor, 
            		ExportUtil.ExportType.DOCUMENT, 
            		request.getExportId(),
            		exportName);

            ((ExportDocumentToXMLServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
            log.error(" unexpected exception when invoking ExportDocumentToXMLService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateXML( Document doc, org.dom4j.Document xmlDoc, StatusPollingBackgroundTask statusPollingBackgroundTask ) 
    {
        Element exportNode = xmlDoc.addElement("MpTouchpointDefinition");
        ExportXMLUtils.createVersion(exportNode);
        updateProgressIndicator(statusPollingBackgroundTask, 5);
        
		createMetaDataTag(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 10);
        
		createDataCollection(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 15);
        
        createDatabaseFiles(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 20);
        
		createLookupTables(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 25);
        
		createReferenceData(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 30);
        
        createDataVariables(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 35);

        createDataFiles(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 40);
        
        createDataResources(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 40);
        
        createSelectors(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 45);
        
        createMetadataTemplates(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 50);

        Set<ContentObject> globalIndirectReferencedContentObjects = SyncTouchpointUtil.findAllGlobalContentObjectsReferencedByDocument(doc.getId());

        createTargetingObjects(doc, globalIndirectReferencedContentObjects, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 55);

        ExportXMLUtils.createTouchpoint(doc, exportNode, this);
		updateProgressIndicator(statusPollingBackgroundTask, 65);

        createEmbeddedContents(doc, globalIndirectReferencedContentObjects, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 70);
        
        createContentLibrary(doc, globalIndirectReferencedContentObjects, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 75);
        
        createMessages(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 80);
        
        createOriginalImageFilesList(doc, exportNode);
		updateProgressIndicator(statusPollingBackgroundTask, 85);
    }

	private void createOriginalImageFilesList( Document doc, Element export )
	{
		if (originalImageFiles.isEmpty())
			return;
		
		Element originalImageFilesElement = export.addElement("OriginalImageFileLocations");
		for (String imageFile : originalImageFiles)
		{
			Element imageFileElement = originalImageFilesElement.addElement("ImageFileLocation");
			imageFileElement.addCDATA(imageFile);
			
		}
	}
    
	private void createMetaDataTag( Document doc, Element export )
	{
		Element metadataElement = export.addElement("Metadata");
		Element userElement = metadataElement.addElement("User");
		userElement.addText(requestor.getFullName());
		Element requestDateElement = metadataElement.addElement("RequestDate");
		requestDateElement.addText(DateUtil.formatDateForXMLOutput(DateUtil.now()));
		Element systemDefaultLocaleElement = metadataElement.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());
		Element tpDefaultLocaleElement = metadataElement.addElement("TouchpointDefaultLocale");
		tpDefaultLocaleElement.addCDATA(doc.getDefaultTouchpointLanguageLocale().getName());
		tpDefaultLocaleElement.addAttribute("code", doc.getDefaultTouchpointLanguageLocale().getCode());
        Element exportOptionElement = metadataElement.addElement("ExportOptions");
        exportOptionElement.addAttribute("exportActiveCopyWhenBothActiveAndWorkingExist", exportOptions.isExportActiveCopyWhenBothActiveAndWorkingExist()?"true":"false");
	}
    
    private void createReferenceData( Document doc, Element export )
    {
        Element refdata = export.addElement("ReferenceData");

		boolean exportOnlyReferenced = exportOptions.isExportReferencedTextStylesOnly();

		List<ParagraphStyle> referenceParagraphStyles = exportOnlyReferenced ? ParagraphStyle.findAllReferencedByDocument(doc) : new ArrayList<>();
		List<ListStyle> referencedListStyles = exportOnlyReferenced ? ListStyle.findAllReferencedByDocument(doc) : new ArrayList<>();
        List<TextStyle> textStyles = exportOnlyReferenced ? TextStyle.findAllReferencedByDocument(doc) : TextStyle.findAll();
		if(exportOnlyReferenced) {
			Set<TextStyle> textStylesSet = new HashSet<>(textStyles);
			textStylesSet.addAll(referenceParagraphStyles.stream()
					.filter(ps->ps.getTextStyle() != null)
					.map(ps->ps.getTextStyle())
					.collect(Collectors.toList())
			);
			textStylesSet.addAll(referencedListStyles.stream()
					.filter(ls->ls.getTextStyle() != null)
					.map(ls->ls.getTextStyle())
					.collect(Collectors.toList())
			);
			textStyles = new ArrayList<>(textStylesSet);
		}

        Collections.sort(textStyles, new TextStyleIdComparator());

        Set<Long> handledTextStyleFontIDs = new HashSet<>();

        for( TextStyle style : textStyles )
        {
        	if(style.getTextStyleFont() != null) {
        		TextStyleFont textStyleFont = style.getTextStyleFont();

                if(handledTextStyleFontIDs.contains(textStyleFont.getId())) continue;

                ExportXMLUtils.createDatabaseFileTagForTextStyleFont(textStyleFont, databaseFilesElement, doc);

                handledTextStyleFontIDs.add(textStyleFont.getId());
        	}

            Map<Document, TextStyleCustomization> mapTSC = style.getTextStyleCustomizations();
            if(mapTSC != null) {
                TextStyleCustomization textStyleCustomization = mapTSC.get(doc);
                if(textStyleCustomization != null) {
                    TextStyleFont textStyleFont = textStyleCustomization.getTextStyleFont();

                    if(textStyleFont != null) {
                        if(handledTextStyleFontIDs.contains(textStyleFont.getId())) continue;

                        ExportXMLUtils.createDatabaseFileTagForTextStyleFont(textStyleFont, databaseFilesElement, doc);

                        handledTextStyleFontIDs.add(textStyleFont.getId());
                    }
                }
            }
        }

        Element fontsElement = refdata.addElement("Fonts");
        for( TextStyle style : textStyles )
        {
            ExportXMLUtils.createFont(style, fontsElement);
            Map<Document, TextStyleCustomization> mapTSC = style.getTextStyleCustomizations();
            if(mapTSC != null) {
                TextStyleCustomization textStyleCustomization = mapTSC.get(doc);
                if(textStyleCustomization != null) {
                    TextStyleFont textStyleFont = textStyleCustomization.getTextStyleFont();

                    if(textStyleFont != null) {
                        ExportXMLUtils.createFont(textStyleCustomization, fontsElement);
                    }
                }
            }
        }

        Element stylesElement = refdata.addElement("TextStyles");
        for( TextStyle style : textStyles )
        {
            ExportXMLUtils.createTextStyle(style, stylesElement);
        }
        
		List<ParagraphStyle> paraStyles = exportOnlyReferenced ? referenceParagraphStyles : ParagraphStyle.findAll();
        Collections.sort(paraStyles, new ParagraphStyleIdComparator());
        
        Element paraStylesElement = refdata.addElement("ParagraphStyles");
        for( ParagraphStyle style : paraStyles )
        {
            ExportXMLUtils.createParagraphStyle(style, paraStylesElement);
        }
        
        List<ListStyle> listStyles = exportOnlyReferenced ? referencedListStyles : ListStyle.findAll();
        Collections.sort(listStyles, new ListStyleIdComparator());

        for( ListStyle style : listStyles )
        {
            String bulletSymbolOverrides = style.getBulletSymbolOverrides();
            if(bulletSymbolOverrides != null && !bulletSymbolOverrides.isEmpty()) {
                JSONArray jsonArray = style.getBulletSymbolDataJSON();
                for(int index = 0; index < jsonArray.length(); ++ index) {
                    JSONObject jsonObject = jsonArray.getJSONObject(index);
                    if(jsonObject != null) {
                        String fontIDStr = jsonObject.optString("font");
                        String fontGuid = jsonObject.optString("font_guid");
                        TextStyleFont textStyleFont = null;
                        if(fontGuid != null && (!fontGuid.isEmpty()) && (!fontGuid.equalsIgnoreCase("null"))) {
                            textStyleFont = TextStyleFont.findByGuid(fontGuid);
                        }
                        if(fontIDStr != null && (!fontIDStr.isEmpty()) && (!fontIDStr.equalsIgnoreCase("null"))) {
                            long fontId = Long.parseLong(fontIDStr);
                            textStyleFont = TextStyleFont.findById(fontId);
                        }

                        if(textStyleFont != null) {
                            if(handledTextStyleFontIDs.contains(textStyleFont.getId())) continue;

                            ExportXMLUtils.createDatabaseFileTagForTextStyleFont(textStyleFont, databaseFilesElement, doc);

                            handledTextStyleFontIDs.add(textStyleFont.getId());

                            ExportXMLUtils.createFont(textStyleFont, fontsElement);

                            handledTextStyleFontIDs.add(textStyleFont.getId());
                        }
                    }
                }
            }
        }

        for( ListStyle listStyle : listStyles )
        {
            Map<Document, ListStyleCustomization> listStyleCustomizationsMap = listStyle.getListStyleCustomizations();
            if(listStyleCustomizationsMap != null) {
                ListStyleCustomization listStyleCustomization = listStyleCustomizationsMap.get(doc);
                if(listStyleCustomization != null) {
                    String bulletSymbolOverrides = listStyleCustomization.getBulletSymbolOverrides();
                    if(bulletSymbolOverrides != null && !bulletSymbolOverrides.isEmpty()) {
                        JSONArray jsonArray = listStyleCustomization.getBulletSymbolDataJSON();
                        for(int index = 0; index < jsonArray.length(); ++ index) {
                            JSONObject jsonObject = jsonArray.getJSONObject(index);
                            if (jsonObject != null) {
                                String fontIDStr = jsonObject.optString("font");
                                String fontGuid = jsonObject.optString("font_guid");
                                TextStyleFont textStyleFont = null;
                                if (fontGuid != null && (!fontGuid.isEmpty()) && (!fontGuid.equalsIgnoreCase("null"))) {
                                    textStyleFont = TextStyleFont.findByGuid(fontGuid);
                                }
                                if (fontIDStr != null && (!fontIDStr.isEmpty()) && (!fontIDStr.equalsIgnoreCase("null"))) {
                                    long fontId = Long.parseLong(fontIDStr);
                                    textStyleFont = TextStyleFont.findById(fontId);
                                }

                                if (textStyleFont != null) {
                                    if (handledTextStyleFontIDs.contains(textStyleFont.getId())) continue;

                                    ExportXMLUtils.createDatabaseFileTagForTextStyleFont(textStyleFont, databaseFilesElement, doc);

                                    handledTextStyleFontIDs.add(textStyleFont.getId());

                                    ExportXMLUtils.createFont(textStyleFont, fontsElement);

                                    handledTextStyleFontIDs.add(textStyleFont.getId());
                                }
                            }
                        }
                    }
                }
            }
        }

        Element listStylesElement = refdata.addElement("ListStyles");
        for( ListStyle style : listStyles )
        {
            ExportXMLUtils.createListStyle(style, listStylesElement);
        }
        
        Element dataGroupsElement = refdata.addElement("DataGroups");
        List<DataGroup> dataGroups = new ArrayList<>();
        if (doc.getDataSourceAssociation() != null)
        {
	        dataGroups.addAll(doc.getDataSourceAssociation().getPrimaryDataSource().getDataGroups());
	        Collections.sort(dataGroups, new DataGroupIdComparator());
        }
        for( DataGroup dg : dataGroups )
        {
            createDataGroup(dg, dataGroupsElement);
        }
    }

    
    private void createDataGroup( DataGroup dg, Element dataGroupsNode ) 
    {
        Element dgNode = dataGroupsNode.addElement("DataGroup");
        dgNode.addAttribute( "id", toString(dg.getId()) );
       	dgNode.addAttribute( "level", toString(dg.getLevel()) );
        if(dg.getParentDataGroupId() != null && dg.getParentDataGroupId() != 0) {
           dgNode.addAttribute("parent", toString(dg.getParentDataGroupId()));
        }

        dgNode.addElement("Name").addCDATA(dg.getName());
    }

    
    private void createDataValues( Document doc, Element export ) 
    {
        Element dataValuesElement = export.addElement("DataValues");

        List<ParameterGroupInstanceCollection> pgiCollections = ParameterGroupInstanceCollection.findAll();
        
        if ( pgiCollections != null )
        {
    		for (ParameterGroupInstanceCollection pgiCollection : pgiCollections) 
        	{
				if ( pgiCollection != null )
				{
	    			// Skip if is not visible to tp container
					if ( pgiCollection.getParameterGroup() != null && doc != null )
						if ( !pgiCollection.getParameterGroup().isVisibleToDocument(doc) )
							continue;
	    			try 
	    			{
	   					ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
	    			} 
	    			catch (Exception ex) 
	    			{
	    	            log.error(" unexpected exception when invoking ExportUtil.createDataValueTag(pgiCollection, dataValuesElement)", ex);
	    			}
				}
        	}
        }
    }

    
    private void createDataVariables( Document doc, Element export ) 
    {
        Element dataVariablesElement = export.addElement("DataVariables");

        // Create User Variables Element
        Element userVariablesElement = dataVariablesElement.addElement("UserVariables");
        
		// Get List of all DataElementVariables
		List<DataElementVariable> variables = HibernateUtil.getManager().getObjects(DataElementVariable.class);

		// Variables that are referenced in Touchpoint but not mapped to TP's Data Sources
		Set<String> referencedNotMappedVariables = doc.getReferencedVariablesNotBridged(true).stream().map(DataElementVariable::getGuid).collect(Collectors.toSet());

		Collections.sort(variables, new Comparator<>() {
            @Override
            public int compare(DataElementVariable arg0, DataElementVariable arg1) {
                if (arg0.isExpressionVariable() != arg1.isExpressionVariable()) {
                    if (arg0.isExpressionVariable())
                        return 1;
                    else
                        return -1;
                } else {
                    return Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
                }
            }
        });
        		
		for (DataElementVariable dataElementVariable : variables) 
		{
			// Skip if the variable is not visible
			if ( !dataElementVariable.isVisibleToDocument(doc) && !referencedNotMappedVariables.contains(dataElementVariable.getGuid()))
				continue;

			// Issue 19734 - check primary datasource for nulls to avoid
			// problems with Touchpoints without Data Collection
			VariableDataElementMap map = null;
			if(!dataElementVariable.isReferenceVariable() && doc.getPrimaryDataSource() != null){
				map = dataElementVariable.getDataElementMap().get(doc.getPrimaryDataSource().getId());
			}else if(dataElementVariable.isReferenceVariable() && doc.getReferenceDataSources() != null){
				for(DataSource referenceDataSource : doc.getReferenceDataSources()){
					map = dataElementVariable.getDataElementMap().get(referenceDataSource.getId());
					if(map != null){
						break;
					}
				}
			}

			// Add UserVariable Element with attributes id, referencevariable, guid, rulesenabled and contentenabled 
	        Element userVariableElement = userVariablesElement.addElement("UserVariable");
	        userVariableElement.addAttribute("id", toString(dataElementVariable.getId()));
	        userVariableElement.addAttribute("referencevariable", (dataElementVariable.isReferenceVariable())?"true":"false");
	        userVariableElement.addAttribute("guid", dataElementVariable.getGuid());
	        userVariableElement.addAttribute("dna", dataElementVariable.getDna());

	        userVariableElement.addAttribute("rulesenabled", dataElementVariable.isEnabledForRules()?"true":"false");
	        userVariableElement.addAttribute("contentenabled", dataElementVariable.isEnabledForContent()?"true":"false");
			userVariableElement.addAttribute("markupenabled", dataElementVariable.isEnabledForMarkupContent()?"true":"false");
            userVariableElement.addAttribute("connectedenabled", dataElementVariable.isEnabledForConnected()?"true":"false");
            userVariableElement.addAttribute("connectedinterviewenabled", dataElementVariable.isEnabledForConnectedInterview()?"true":"false");

	        userVariableElement.addAttribute("expressionvariable", dataElementVariable.isExpressionVariable()?"true":"false");
	        userVariableElement.addAttribute("scriptvariable", dataElementVariable.isScriptVariable()?"true":"false");
            userVariableElement.addAttribute("systemvariable", dataElementVariable.getIsSystemVariable()?"true":"false");
            userVariableElement.addAttribute("typeid", toString(dataElementVariable.getTypeId()));
            if(dataElementVariable.getIsSystemVariable()) {
                userVariableElement.addAttribute("systemvariabletypeid", toString(dataElementVariable.getSystemVariableTypeId()));
            }
	        // Add Name
	        userVariableElement.addElement("Name").addCDATA(dataElementVariable.getName());

	        // Add FriendlyName if it's different from Name
	        if ( !dataElementVariable.getName().equals(dataElementVariable.getFriendlyName()) )
	        	userVariableElement.addElement("FriendlyName").addCDATA(dataElementVariable.getFriendlyName());
	        
			// Add Default Value
			if ( dataElementVariable.getDefaultValue() != null  && !dataElementVariable.getDefaultValue().isEmpty())
	        	userVariableElement.addElement("DefaultValue").addCDATA(dataElementVariable.getDefaultValue());

            if ( dataElementVariable.getMetatags() != null && ! dataElementVariable.getMetatags().isEmpty() ) {
                userVariableElement.addElement("Metatags").addCDATA(dataElementVariable.getMetatags());
            }

	        // Add DataElement and its attributes
			Element dataElement = userVariableElement.addElement("DataElement");
	        
			if (dataElementVariable.isExpressionVariable())
			{
				ComplexValue expression = dataElementVariable.getExpression();
				dataElement.addAttribute("id", toString(expression.getId()));
				dataElement.addAttribute("type", "ComplexValue");

				Element contentElement = dataElement.addElement("Content");
				contentElement.addCDATA(expression.getEncodedValue());

				Element sqlElement = dataElement.addElement("SQL");
				sqlElement.addCDATA(dataElementVariable.getSqlExpression());
			}
			else if(dataElementVariable.isScriptVariable()) 
			{
				ComplexValue script = dataElementVariable.getScript();
				dataElement.addAttribute("id", toString(script.getId()));
				dataElement.addAttribute("type", "ComplexValue");

				Element contentElement = dataElement.addElement("Content");
				contentElement.addCDATA(script.getEncodedValue());
			}
			else if ( map != null && map.getDataElement() != null ) 
			{
				dataElement.addAttribute("id", toString(map.getDataElement().getId()));
				dataElement.addCDATA(map.getDataElement().getName().trim());
				if(dataElementVariable.isPrimary()){
					dataElement.addAttribute("datatype", map.getDataElement().getDataSubtypeName());
					dataElement.addAttribute("repeats", map.getDataElement().isRepeats()?"true":"false");
					if (map.getAggOperator() != null)
						dataElement.addAttribute("aggregoper", map.getAggOperator().toString());
					if (map.getLevel() != null)
						dataElement.addAttribute("aggreglevel", toString(map.getLevel().getValue()));
				}else{
					dataElement.addAttribute("referencedatasource", map.getDataElement().getDataSource().getName());
				}
			}

		} // end of for
                
    }

    private void createDataFiles( Document doc, Element export ) {
    	dataFiles = new LinkedHashSet<>();
        dataFilesListElement = export.addElement("DataFiles");
    	if(exportOptions.isIncludeDataFilesAndDataResources()) {
    		for(DataFile df : doc.getDataFiles()) {
    			addDataFile(df);
    		}
    	}
    }
    
    private void addDataFile(DataFile df) {
    	if(dataFiles.contains(df)) {
    		return;
    	}
    	dataFiles.add(df);
    	Element dataFileElement = dataFilesListElement.addElement("DataFile");
    	
    	dataFileElement.addAttribute("id",            toString(df.getId()));
    	
    	int sourceTypeId = df.getSourceType().getId();
    	dataFileElement.addAttribute("sourcetyperid",  toString(sourceTypeId));

    	dataFileElement.addElement("Name").addCDATA(df.getName());
    	dataFileElement.addElement("FileName").addCDATA(df.getFilename());
    	
    	dataFileElement.addElement("RemotePath").addCDATA(df.getRemoteDataFilePath());

    	File file = df.getFile();
    	if(file != null && file.exists() && file.length() > 0) {
    		Element contentElement = dataFileElement.addElement("Content");
    		try {
				contentElement.addCDATA(ExportXMLUtils.encodeFile(file.getCanonicalPath()));
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
    	}
    	Set<DataFilePreviewLanguage> previewLanguages = df.getDataFilePreviewLanguages();
    	if(previewLanguages != null ) {
        	Element previewLanguagesListElement = dataFileElement.addElement("PreviewLanguages");
        	for(DataFilePreviewLanguage pl : previewLanguages) {
        		Element previewLanguageElement = previewLanguagesListElement.addElement("PreviewLanguage");
        		previewLanguageElement.addAttribute("id", toString(pl.getId()));
        		previewLanguageElement.addElement("CustomerNumber").addCDATA(pl.getCustomerNumber());
        		previewLanguageElement.addElement("Language").addCDATA(pl.getLanguage());
        	}
    	}
    }
    
    private void createDataResources( Document doc, Element export ) {
    	dataResources = new LinkedHashSet<>();
    	dataResourcesListElement = export.addElement("DataResources");
    	if(exportOptions.isIncludeDataFilesAndDataResourcesForConnectedInterview()) {
    		if(doc.getCommunicationsDataResource() != null) {
    			addDataResource(doc.getCommunicationsDataResource());
    		}
    	}
    	if(exportOptions.isIncludeDataFilesAndDataResourcesForSegmentationAnalysis()) {
    		if(doc.getSegmentationAnalysisResource() != null) {
    			addDataResource(doc.getSegmentationAnalysisResource());
    		}
    	}
    	if(exportOptions.isIncludeDataFilesAndDataResources()) {
    		for(DataResource dr : doc.getDataResources()) {
    			addDataResource(dr);
    		}
    	}
    }
    
    private void addDataResource(DataResource dataResource) {
    	if(dataResources.contains(dataResource)) {
    		return;
    	}
    	
    	dataResources.add(dataResource);
    	
    	Element dataResourceElement = dataResourcesListElement.addElement("DataResource");
    	
    	dataResourceElement.addAttribute("id", toString(dataResource.getId()));
    	dataResourceElement.addAttribute("guid", (dataResource.getGuid()));
        dataResourceElement.addAttribute("dna", (dataResource.getDna()));
    	
    	dataResourceElement.addElement("Name").addCDATA(dataResource.getName());
    	
    	if(dataResource.getRemotePrimaryDataFile() != null) {
        	dataResourceElement.addElement("RemotePrimaryDataFilePath").addCDATA(dataResource.getRemotePrimaryDataFile());
    	}
    	
    	DataFile primaryDataFile = dataResource.getPrimaryDataFile();
    	if(primaryDataFile != null) {
    		addDataFile(primaryDataFile);
    		dataResourceElement.addElement("PrimaryDataFile").addAttribute("refid", toString(primaryDataFile.getId()));
    	}
    	
    	Set<ConnectionResource> connectionResources = dataResource.getConnectionResources();
    	if(connectionResources != null) {
    		Element connectionResourcesListElement = dataResourceElement.addElement("ConnectionResources");
    		for(ConnectionResource cr : connectionResources) {
    			Element connectionResourceElement = connectionResourcesListElement.addElement("ConnectionResource");
    			connectionResourceElement.addAttribute("id", toString(cr.getId()));
    			connectionResourceElement.addAttribute("guid", cr.getGuid());
    			String remoteReferenceDataFilePath = cr.getRemoteReferenceDataFilePath();
    			if(remoteReferenceDataFilePath != null && !remoteReferenceDataFilePath.isEmpty()) {
        			connectionResourceElement.addElement("RemoteReferenceDataFilePath").addCDATA( remoteReferenceDataFilePath );
    			}
    			DataFile df = cr.getReferenceDataFile();
    			if(df != null) {
        			addDataFile(df);
        			connectionResourceElement.addElement("ReferenceDataFile").addAttribute("refid", toString(df.getId()));
    			}
    			ReferenceConnection rc = cr.getReferenceConnection();
    			if(rc != null) {
        			connectionResourceElement.addElement("ReferenceConnection").addAttribute("refid", toString(rc.getId()));
    			}
    		}
    	}
    }
    
    private void createSelectors( Document doc, Element export ) 
    {
        Element selectorsElement = export.addElement("Selectors");

        createDataValues(doc, selectorsElement);
        
        Element selectionParametersElement = selectorsElement.addElement("SelectionParameters");
        
        List<ParameterGroup> selectableParameters = ParameterGroup.findAllIncludingParameters();

		// Variables that are referenced in Touchpoint but not mapped to TP's Data Sources
		Set<String> referencedNotMappedVariables = doc.getReferencedVariablesNotBridged(true).stream().map(DataElementVariable::getGuid).collect(Collectors.toSet());

		Function<ParameterGroup, Boolean> parameterGroupReferencesUnmappedVariable = (parameterGroup) -> {
			for (Parameter parameter : parameterGroup.getParameters()) {
				if (parameter != null && referencedNotMappedVariables.contains(parameter.getDataElementVariable().getGuid())) {
					return true;
				}
			}
			return false;
		};

		for (ParameterGroup pg : selectableParameters)
		{
			// Skip if the selector is not visible
			if ( !pg.isVisibleToDocument(doc) && !parameterGroupReferencesUnmappedVariable.apply(pg))
				continue;

        	Element selectorElement = selectorsElement.addElement("Selector");
        	selectorElement.addAttribute("id", toString(pg.getId()));	        
        	selectorElement.addAttribute("guid", pg.getGuid());
        	selectorElement.addAttribute("isgroup", pg.isParameter()?"false":"true");
            selectorElement.addAttribute("hash", pg.getHashSafe());

        	selectorElement.addElement("Name").addCDATA(pg.getName());
        	selectorElement.addElement("Description").addCDATA(pg.getDescription());

			Element selectorParametersElement = selectorElement.addElement("Parameters");
			List<Parameter> parameters = pg.getParameters();
			
			Set<Long> exportedParameterIDs = new HashSet<>();
			
	        for (Parameter parameter : parameters) 
			{
	        	if (parameter != null)
	        	{
		        	if ( /*pg.isParameter()*/ ! exportedParameterIDs.contains(parameter.getId()) ) 
		        	{
		        		exportedParameterIDs.add(parameter.getId());
		        		
		        		Element globalSelectionParameterElement = selectionParametersElement.addElement("SelectionParameter");
		        		globalSelectionParameterElement.addAttribute("id", toString(parameter.getId()));
		        		if (parameter.getDataElementVariable() != null)
		        		{
		        			globalSelectionParameterElement.addAttribute("uservarrefid", toString(parameter.getDataElementVariable().getId()));
		        		}
		        		else
		        		{
		        			globalSelectionParameterElement.addAttribute("uservarrefid", "0");	        			
		        		}
		        		globalSelectionParameterElement.addAttribute("guid", parameter.getGuid());
		        		globalSelectionParameterElement.addAttribute("restrictedvisability", "false");
		        		globalSelectionParameterElement.addElement("Name").addCDATA(parameter.getName());
		        		globalSelectionParameterElement.addElement("Description").addCDATA(parameter.getDescription());
		        	}
		        	
		        	Element selectionParameterElement = selectorParametersElement.addElement("SelectionParameter");
		        	selectionParameterElement.addAttribute("refid", toString(parameter.getId()));
	        	}
			}
		}		               
    }

    private void createMetadataTemplateItemElement(MetadataFormItemDefinition item, Element itemDefinitionsElement) {
    	Element element = itemDefinitionsElement.addElement("ItemDefinition");
    	element.addAttribute("id", toString(item.getId()));
    	element.addAttribute("guid", item.getGuid());
    	
    	String name = item.getName();
    	if(name != null) {
    		element.addElement("Name").addCDATA(name);
    	}

    	element.addAttribute("order", toString(item.getOrder()));
    	element.addAttribute("type", toString(item.getTypeId()));
    	
    	Boolean mandatory = item.getIsManadatory();
    	if(mandatory != null) {
    		element.addAttribute("mandatory", (mandatory.booleanValue()?"1":"0"));
    	}

		Integer repeatingDataTypeId= item.getRepeatingDataTypeId();
		if(repeatingDataTypeId != null) {
			element.addAttribute("repeatingdatatypeid",  toString(item.getRepeatingDataTypeId()));
		}

    	int refreshOnValueChangeTypeId = item.getWebServiceRefreshTypeId();
        element.addAttribute("refreshonvaluechange", String.valueOf(refreshOnValueChangeTypeId));
    	
    	AbstractDataElement dataElement = item.getDataElement();
    	if(dataElement != null) {
        	element.addAttribute("dataelementid", toString(dataElement.getId()));
    	}
    	String menuValueItems = item.getMenuValueItems();
    	if(menuValueItems != null) {
        	element.addElement("MenuValueItems").addCDATA(menuValueItems);
    	}
    	
    	String primaryConnector = item.getPrimaryConnector();
    	if(primaryConnector != null) {
    		element.addElement("PrimaryConnector").addCDATA(primaryConnector);
    	}
    	
    	Integer parentItemOrder = item.getParentItemOrder();
    	if(parentItemOrder != null) {
    		element.addAttribute("parentitemorder", toString(parentItemOrder));
    	}
    	
    	String displayTiggerValues = item.getDisplayTriggerValues();
    	if(displayTiggerValues != null) {
    		element.addElement("DisplayTriggerValues").addCDATA(displayTiggerValues);
    	}
		String regexValidation = item.getRegexValidation();
		if(regexValidation != null) {
			element.addElement("RegexValidation").addCDATA(regexValidation);
		}

		String criteriaOperator = item.getCriteriaOperator();
		if (criteriaOperator != null) {
			element.addElement("CriteriaOperator").addCDATA(criteriaOperator);
		}

		String criteriaTriggerValuesJson = item.getCriteriaTriggerValuesJson();
		if (criteriaTriggerValuesJson != null) {
			element.addElement("CriteriaTriggerValuesJson").addCDATA(criteriaTriggerValuesJson);
		}
    	
    	MetadataFormFieldSizeType fieldSizeType = item.getFieldSizeType();
    	if(fieldSizeType != null) {
    		element.addElement("FieldSizeTypeId").addText(toString(fieldSizeType.getId()));
    	}
    	
    	Integer fieldMaxLength = item.getFieldMaxLength();
    	if(fieldMaxLength != null) {
    		element.addElement("FieldMaxLength").addText(toString(fieldMaxLength));
    	}
    	
    	Integer inputValidationTypeId = item.getInputValidationTypeId();
    	if(inputValidationTypeId != null) {
    		element.addElement("InputValidationTypeId").addText(toString(inputValidationTypeId));
    	}
    	
    	String defaultInputValue = item.getDefaultInputValue();
    	if(defaultInputValue != null) {
    		element.addElement("DefaultInputValue").addCDATA(defaultInputValue);
    	}
    	
    	boolean defaultToToday = item.isDefaultDateValueToTodaysDate();
		element.addElement("DefaultToToday").addText(defaultToToday?"1":"0");
    	
		boolean uniqueValue = item.isUniqueValue();
		element.addElement("UniqueValue").addText(uniqueValue?"1":"0");

    }
    
    private void createMetadataTemplateElement(MetadataFormDefinition metadataFormDefinition, Element metadataTemplatesElement) {
    	Element element = metadataTemplatesElement.addElement("MetadataTemplate");
    	element.addAttribute("id",   toString(metadataFormDefinition.getId()));
    	element.addAttribute("guid", metadataFormDefinition.getGuid());
    	element.addAttribute("type", toString(metadataFormDefinition.getType()));
    	
    	element.addElement("Name").addCDATA(metadataFormDefinition.getName());
    	String metaTags = metadataFormDefinition.getMetatags();
    	if(metaTags != null) {
        	element.addElement("Metatags").addCDATA(metaTags);
    	}
    	
    	String description = metadataFormDefinition.getDescription();
    	if(description != null) {
        	element.addElement("Description").addCDATA(description);
    	}
    	
    	Element itemDefinitionsElement = element.addElement("ItemDefinitions");
    	for(MetadataFormItemDefinition item : metadataFormDefinition.getFormItemDefinitions()) {
    		createMetadataTemplateItemElement(item, itemDefinitionsElement);
    	}
    }
    
    private void addMetadataFormDefinition(MetadataFormDefinition formDef) {
    	if(! metadataFormDefinitions.contains(formDef)) {
    		createMetadataTemplateElement(formDef, metadataTemplatesElement);
    		metadataFormDefinitions.add(formDef);
    	}
    }
    
    private void createMetadataTemplates( Document doc, Element export ) {
        metadataTemplatesElement = export.addElement("MetadataTemplates");
        
    	MetadataFormDefinition touchpointMetadataFormDefinition = doc.getTouchpointMetadataFormDefinition();
    	if(touchpointMetadataFormDefinition != null) {
    		addMetadataFormDefinition(touchpointMetadataFormDefinition);
    	}
    	
    	MetadataFormDefinition variantMetadataFormDefinition = doc.getVariantMetadataFormDefinition();
    	if(variantMetadataFormDefinition != null) {
    		addMetadataFormDefinition(variantMetadataFormDefinition);
    	}
    }
        
    private void createTargetingObjects( Document doc, Set<ContentObject> globalIndirectReferencedContentObjects, Element export )
    {
        Element targetingObjectsElement = export.addElement("TargetingObjects");

        // Create Filters
        //
        Element filtersElement = targetingObjectsElement.addElement("Filters");
        
        List<FilterCondition> filters = FilterCondition.findAllSortedByIDs();
        for (FilterCondition filter : filters)
        {        	
        	// Check visibility of a variable used by the filter. Don't export this filter if the variable is not visible or is not defined
        	if ( filter.getDataElementVariable() == null )
        		continue;
        	if ( !filter.getDataElementVariable().isVisibleToDocument(doc) )
        		continue;
			
            Element filterElement = filtersElement.addElement("Filter");
            filterElement.addAttribute("id", toString(filter.getId()));
            filterElement.addAttribute("guid", filter.getGuid());
            
            filterElement.addAttribute("enabled", "true");
            
            String filterName = "Filter for "; 
            if (filter.getConditionSubelement() != null)
            {
            	filterName = filterName + filter.getConditionSubelement().getName();
            	if (filter.getConditionSubelement().getElement() != null)
            	{
                	filterName = filterName + " of " + filter.getConditionSubelement().getElement().getName();            		
            	}
            }
            
        	filterElement.addElement("Name").addCDATA(filterName);
            
            Element userVariableElement = filterElement.addElement("UserVariable");
            userVariableElement.addAttribute("refid", toString(filter.getDataElementVariable().getId()));
            userVariableElement.addCDATA(filter.getDataElementVariable().getName());

            Element comparisonTypeElement = filterElement.addElement("ComparisonType");
            // comparisonTypeElement.addAttribute("id", toString(filter.getDataElementComparisonId()));
            comparisonTypeElement.addText(toString(filter.getDataElementComparisonId()));

            Map<String, String> valueMap = filter.getConditionValueMap();
            Element mapElement = filterElement.addElement("Map");            
            for (String key : valueMap.keySet())
            {
                Element itemElement = mapElement.addElement("Item");
                itemElement.addElement("Key").addText(key);
                itemElement.addElement("Value").addCDATA(valueMap.get(key));            	
            }                        
        }
        
        // Create Rule Categories
        //
        targetingObjectsElement.addElement("RuleCategories");

        // Create Rules
        //
        Element rulesElement = targetingObjectsElement.addElement("Rules");

        List<ConditionElement> rules = ConditionElement.findAllSortedByIDs();
        for (ConditionElement rule : rules)
        {
        	// Don't export if this rule is not visible to the container
        	if ( !rule.isVisibleToDocument(doc) )
        		continue;

        	Element ruleElement = rulesElement.addElement("Rule");
        	ruleElement.addAttribute("id", toString(rule.getId()));
        	ruleElement.addAttribute("type", rule.getType());
        	ruleElement.addAttribute("guid", rule.getGuid());
            ruleElement.addAttribute("hash", rule.getHashSafe());

       		ruleElement.addAttribute("categoryrefid", "0");
        	
        	ruleElement.addElement("Name").addCDATA(rule.getName());

        	ruleElement.addElement("Metatags").addCDATA(rule.getMetatags());
        	
            Element defaultResultElement = ruleElement.addElement("DefaultResult");
            defaultResultElement.addAttribute("enabled", rule.isUseDefaultValue()?"true":"false");
            defaultResultElement.addText(rule.isDefaultValue()?"true":"false");

            List<ConditionSubelement> ruleConditions = rule.getSubElements();
            
            for (ConditionSubelement ruleCondition : ruleConditions)
            {
            	Element ruleConditionElement = ruleElement.addElement("Condition");
            	ruleConditionElement.addAttribute("id", toString(ruleCondition.getId()));
            	ruleConditionElement.addAttribute("guid", ruleCondition.getGuid());
            	if(ruleCondition.getConditionOperator() != null) {
            	    ruleConditionElement.addAttribute("operator", ruleCondition.getConditionOperator().getOperator());
            	}
            	ruleConditionElement.addAttribute("parameterized", ruleCondition.isParameterized()?"true":"false");
            	ruleConditionElement.addElement("Name").addCDATA(ruleCondition.getName());
            	
            	if ( ruleCondition.getDataFilePath() != null )
            	{
            		Element dfElem = ruleConditionElement.addElement("DataFile");
            		dfElem.addElement("DataFilePath").addCDATA(ruleCondition.getDataFilePath());
            		dfElem.addElement("DataFileContent").addCDATA( ExportXMLUtils.encodeFile(ruleCondition.getDataFilePath()) );
            	}
            	
                Element userVariableElement = ruleConditionElement.addElement("UserVariable");
                if (ruleCondition.getDataElementVariable() != null)
                {
                	userVariableElement.addAttribute("refid", toString(ruleCondition.getDataElementVariable().getId()));
                	userVariableElement.addCDATA(ruleCondition.getDataElementVariable().getName());
                }
                else
                {
                	userVariableElement.addAttribute("refid", "0");                	
                }

                Element comparisonTypeElement = ruleConditionElement.addElement("ComparisonType");
                comparisonTypeElement.addText(toString(ruleCondition.getDataElementComparisonId()));
                
                Map<String, String> valueMap = ruleCondition.getConditionAttributes();
                Element mapElement = ruleConditionElement.addElement("Map");            
                for (String key : valueMap.keySet())
                {
                    Element itemElement = mapElement.addElement("Item");
                    itemElement.addElement("Key").addText(key);
                    itemElement.addElement("Value").addCDATA(valueMap.get(key));            	
                } 
                if (ruleCondition.getFilterCondition() != null)
                {
                    Element filterElement = ruleConditionElement.addElement("Filter");
                    filterElement.addAttribute("refid", toString(ruleCondition.getFilterCondition().getId()));
                }
            }
        }
        
        // Create Target Groups
        //
        Element targetGroupsElement = targetingObjectsElement.addElement("TargetGroups");
        
        // Use Set to eliminate a duplicate
        Set<TargetGroup> targetGroupsSet = new LinkedHashSet<>();
        targetGroupsSet.addAll(TargetGroup.findAllByDocument(doc, null, false, 0, false));	// Associated Target Groups
        targetGroupsSet.addAll(TargetGroup.findAllUsedByDocument(doc));						// Used Target Group (although all used target group should be in associated target groups)
        targetGroupsSet.addAll(TargetGroup.findAllUsedByContentObjects(globalIndirectReferencedContentObjects)); // Used Target Group by indirect referenced smart texts

        // Convert to List to be able to sort
        List<TargetGroup> targetGroups = new ArrayList<>(targetGroupsSet);
		Collections.sort(targetGroups, new TargetGroupIdComparator());		        
        
        for (TargetGroup targetGroup : targetGroups)
        {

        	Element targetGroupElement = targetGroupsElement.addElement("TargetGroup");
        	targetGroupElement.addAttribute("id", toString(targetGroup.getId()));
        	targetGroupElement.addAttribute("guid", targetGroup.getGuid());
        	targetGroupElement.addAttribute("dna", targetGroup.getDna());
        	targetGroupElement.addAttribute("parameterized", targetGroup.isParameterized()?"true":"false");
        	targetGroupElement.addAttribute("relationship", toString(targetGroup.getConditionRelationship()));
            targetGroupElement.addAttribute("hash", targetGroup.getHashSafe());

        	targetGroupElement.addElement("Name").addCDATA(targetGroup.getName());

        	targetGroupElement.addElement("Metatags").addCDATA(targetGroup.getMetatags());
        	
            Element tgRulesElement = targetGroupElement.addElement("Rules");
            TargetGroupInstance tgInstance = targetGroup.getInstance();
            
            Map<ConditionItem, Boolean> tgConditionParamMap = tgInstance.getConditionParamMap();

            List<ConditionElement> tgRules = new ArrayList<>(tgInstance.getConditionElements());
			Collections.sort(tgRules, new ConditionElementIdComparator());		
            for (ConditionElement rule : tgRules)
            {
            	Element ruleElement = tgRulesElement.addElement("Rule");
            	ruleElement.addAttribute("refid", toString(rule.getId()));
            	
                ConditionItem conditionItem = tgInstance.getConditionItem(rule);

                boolean ruleParamaterized = false;
                if (tgConditionParamMap.containsKey(conditionItem))
                {
                	ruleParamaterized = tgConditionParamMap.get(conditionItem);
                }
            	ruleElement.addAttribute("parameterized", ruleParamaterized?"true":"false");                    	

                List<ConditionSubelement> ruleConditions = rule.getSubElements();
                try {
                    rule.getDirectReferences();
                }
                catch (Exception e) {
                    throw e;
                }
                for (ConditionSubelement ruleCondition : ruleConditions)
                {
                	if (!ruleCondition.isReferenced())
                		continue;
                	
                	boolean isSelected = false;
                	List<ConditionItemValue> ciValues = ruleCondition.getReferencingConditionItemValues();
                	for (ConditionItemValue ciValue : ciValues)
                	{
                		if ( ciValue.getConditionItem() != null && ciValue.getConditionItem().getInstance().getId() == targetGroup.getInstance().getId() )
                		{
                			isSelected = true;
                			break;
                		}
                	}               

                	if ( !isSelected )
                		continue;
                	
                	Element ruleConditionElement = ruleElement.addElement("Condition");
                	ruleConditionElement.addAttribute("refid", toString(ruleCondition.getId()));
                	                    
                    Set<ConditionItemValue> conditionItemValues = conditionItem.getConditionItemValues();
                    for (ConditionItemValue conditionItemValue : conditionItemValues)
                    {
                    	if ( conditionItemValue.getConditionSubelement() == null || conditionItemValue.getConditionSubelement().getId() != ruleCondition.getId() )
                    		continue;
                    	
                        Map<String, String> valueMap = conditionItemValue.getValueMap();
                        if (!valueMap.isEmpty() && !ruleParamaterized) 
                        {
	                        Element mapElement = ruleConditionElement.addElement("Parameter");            
	                        for (String key : valueMap.keySet())
	                        {
	                            Element itemElement = mapElement.addElement("Item");
	                            itemElement.addElement("Key").addText(key);
	                            itemElement.addElement("Value").addCDATA(valueMap.get(key));            	
	                        }
                        }
                    }
                }
            }
        }
        
        // Create Content Targeting
        //
        Element contentTargetingsElement = targetingObjectsElement.addElement("ContentTargetings");

        List<ContentTargeting> contentTargetings = new ArrayList<>(ContentTargeting.findAllByDocument(doc));
		Collections.sort(contentTargetings, new Comparator<>() {
            @Override
            public int compare(ContentTargeting arg0, ContentTargeting arg1) {
                return Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
            }
        });
		
        for (ContentTargeting contentTargeting : contentTargetings)
        {
			Element contentTargetingElement = contentTargetingsElement.addElement("ContentTargeting");
			contentTargetingElement.addAttribute("id", toString(contentTargeting.getId()));
			contentTargetingElement.addAttribute("guid", contentTargeting.getGuid());
			
			ExportXMLUtils.createTargetCriteriaTag(contentTargetingElement, contentTargeting);
        }
    }
    
    private void createMessages( Document doc, Element export ) 
    {   	
		List<ContentObject> messageInstanceList = ContentObject.findAllContentObjectsByDocument(doc, false);

		if ( !messageInstanceList.isEmpty() )
		{
			Collections.sort(messageInstanceList, new ContentObjectIdComparator());
		
            Element messagesElement = export.addElement("Messages");
			for (ContentObject contentObject : messageInstanceList) 
			{
				if(contentObject == null)
					continue;

				if((contentObject.isVariantType() || contentObject.isStructuredContentEnabled()) && contentObject.getOwningTouchpointSelection() !=null && contentObject.getOwningTouchpointSelection().getId()>0){
					TouchpointSelection miSelection = TouchpointSelection.findById(contentObject.getOwningTouchpointSelection().getId());
					if (miSelection == null) {
						continue;
					}
				}

                if(exportOptions.isExportActiveCopyWhenBothActiveAndWorkingExist())
                    contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
                else
                    contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

                if(contentObject.isRemoved()) continue;
                if(contentObject.getContentObjectDataTypeMap() == null || contentObject.getContentObjectDataTypeMap().isEmpty()) continue;
                if(contentObject.getContentObjectData() == null) {
                    continue;
                }

                if(exportOptions.isExportActiveCopyWhenBothActiveAndWorkingExist())
                    contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
                else
                    contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

				createMessageTag(contentObject, messagesElement, doc, messageInstanceList);
			}
		}
    }
    
//////////////////////////////////////////////////////////////////////////////////////////////////
//////
////// createMessages Subroutines
//////
//////////////////////////////////////////////////////////////////////////////////////////////////
    
	private void createMessageTag(ContentObject contentObject, Element messagesElement, Document doc, List<ContentObject> messageInstanceList)
	{
		Element messageElement = messagesElement.addElement("Message");
		messageElement.addAttribute("id", toString(contentObject.getId()));
		messageElement.addAttribute("guid", contentObject.getGuid());
        messageElement.addAttribute("dna", contentObject.getDna());

        messageElement.addAttribute("modelhash", contentObject.getHashSafe());
        {
            ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
            if (activeCopy != null) {
                messageElement.addAttribute("activecopyhash", activeCopy.getHashSafe());
            }
        }

        {
			ContentObjectData workingCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
            if (workingCopy != null) {
                messageElement.addAttribute("workingcopyhash", workingCopy.getHashSafe());
            }
        }

		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null) 
		{
	        Element externalIdElement = messageElement.addElement("ExternalId");
	        externalIdElement.addText(tmd.getExternalId().toString());
		}
		
		String messageType = "";
		if (contentObject.isStructuredContentEnabled()) 
		{
			messageType = "Selectable(Touchpoint Content)";
		}
		else if (contentObject.isDynamicVariantEnabled())
		{
			messageType = "Selectable(Message)";
		}
		else if (contentObject.isVariantType()) 
		{
			messageType = "Selectable(Touchpoint Message)";
		}
		else
		{
			messageType = "Regular";
		}

		messageElement.addAttribute("type", messageType);
		messageElement.addAttribute("contenttype", contentObject.getContentType().getLocaledString());

		messageElement.addAttribute("suppressed", contentObject.isSuppressed()?"true":"false");
		messageElement.addAttribute("onhold", contentObject.isOnHold()?"true":"false");
		messageElement.addAttribute("islocal", contentObject.getIsTouchpointLocal()?"true":"false");

		messageElement.addAttribute("defaultlanguage", doc.getDefaultTouchpointLanguageCode());
		messageElement.addAttribute("defaultlocale", doc.getDefaultTouchpointLanguageLocaleCode());

		createVersionTag(contentObject, messageElement, doc, messageInstanceList);
	}
	
	private void createVersionTag(ContentObject contentObject, Element messageElement, Document doc, List<ContentObject> messageInstanceList)
	{
		Element versionElement = messageElement.addElement("Version");

        if(contentObject.getContentObjectData() == null) {
            log.error("contentObject \"" + contentObject.getName() + "\" (id=" + contentObject.getId() + ") has no version data.");
            return;
        }

        versionElement.addAttribute("id", toString(contentObject.getId()));

		versionElement.addAttribute("guid", contentObject.getContentObjectData().getGuid());

		if (contentObject.isFocusOnActiveData())
			versionElement.addAttribute("status", "Active");
		else if (contentObject.isFocusOnWorkingData())
			versionElement.addAttribute("status", "Working Copy");
		else
			versionElement.addAttribute("status", "Archived");

		versionElement.addAttribute("flowtype", toString(contentObject.getFlowType()));

		int contentSpecializationType = 0;
		if (contentObject.getContentType().isMarkup())
			contentSpecializationType = 1;
		else if (contentObject.getContentType().isSharedFreeform())
			contentSpecializationType = 2;

        versionElement.addAttribute("contentspecializationtype", toString(contentSpecializationType));

        versionElement.addAttribute("keepcontenttogether", Boolean.valueOf(contentObject.isKeepContentTogether()).toString());
		if(contentObject.getSegmentationData() != null) {
			versionElement.addElement("SegmentationData").addCDATA(contentObject.getSegmentationData());
		}
		versionElement.addAttribute("canvasmaxwidth", toString(contentObject.getCanvasMaxWidth()));
		versionElement.addAttribute("canvasmaxheight", toString(contentObject.getCanvasMaxHeight()));
		versionElement.addAttribute("canvastrimwidth", toString(contentObject.getCanvasTrimWidth()));
		versionElement.addAttribute("canvastrimheight", toString(contentObject.getCanvasTrimHeight()));
		
		versionElement.addAttribute("supportstables", Boolean.valueOf(contentObject.isSupportsTables()).toString());
        versionElement.addAttribute("supportsforms", Boolean.valueOf(contentObject.isSupportsForms()).toString());
        versionElement.addAttribute("supportsbarcodes", Boolean.valueOf(contentObject.isSupportsBarcodes()).toString());
        versionElement.addAttribute("supportscontentmenus", Boolean.valueOf(contentObject.isSupportsContentMenus()).toString());
        versionElement.addAttribute("keepContentTogether", Boolean.valueOf(contentObject.isKeepContentTogether()).toString());
		versionElement.addAttribute("renderastaggedtext", Boolean.valueOf(contentObject.isRenderAsTaggedText()).toString());

        if(contentObject.getGraphicTypeId() != null) {
            versionElement.addAttribute("graphictype", toString(contentObject.getGraphicTypeId()));
        }
        
		if(contentObject.getIsTouchpointLocal()) {
			versionElement.addAttribute("insertasparagraph", Boolean.valueOf(contentObject.isInsertAsBlockContent()).toString());
		}

		if (contentObject.getContentObjectData().getCreationReason() != null)
			versionElement.addAttribute("origin", contentObject.getContentObjectData().getCreationReason().getLocaledString());
		else
			versionElement.addAttribute("origin", "New");

		if(contentObject.getChannelContextId() != null) {
	        versionElement.addAttribute("channel", contentObject.getChannelContextId().toString());
		}

        if(contentObject.getDataGroup() != null) {
            versionElement.addAttribute("dgrefid", toString(contentObject.getDataGroup().getId()));
        }

        if(contentObject.getDataGroupCompVarFormatType() != 0) {
            versionElement.addAttribute("datagroupcompvarformattype", toString(contentObject.getDataGroupCompVarFormatType()));
        }

		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObject.getName());

		Element metatagsElement = versionElement.addElement("Metatags");
		if (contentObject.getMetatags() != null && !contentObject.getMetatags().equalsIgnoreCase(""))
		{
			metatagsElement.addText(contentObject.getMetatags());
		}
		
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObject.getDescription() != null && !contentObject.getDescription().equalsIgnoreCase(""))
		{
			descriptionElement.addText(contentObject.getDescription());
		}

		Element commentsElement = versionElement.addElement("Comments");
        List<ContentObjectComment> comments = new ArrayList<>(contentObject.getComments());
		Collections.sort(comments, new CommentIdComparator());		
		for (ContentObjectComment comment : comments)
		{
			if (comment != null && comment.getComment() != null && !comment.getComment().equalsIgnoreCase(""))
			{
				Element commentElement = commentsElement.addElement("Comment");
				commentElement.addAttribute("id", toString(comment.getId()));				
				commentElement.addAttribute("created", DateUtil.formatDateForXMLOutput(comment.getCreated()));				
				commentElement.addText(comment.getComment());
			}
		}
		
		Element timingElement = versionElement.addElement("Timing");
		if (contentObject.getStartDate() != null) 
		{
			timingElement.addAttribute("startdate", DateUtil.formatDateYYYYsMMsDD(contentObject.getStartDate()));
		}
		if (contentObject.getEndDate() != null)
		{
			timingElement.addAttribute("enddate", DateUtil.formatDateYYYYsMMsDD(contentObject.getEndDate()));
		}
		if ( contentObject.getStartDate() != null || contentObject.getEndDate() != null ) 
		{
			timingElement.addAttribute("repeatsannually", Boolean.valueOf(contentObject.isRepeatDatesAnnually()).toString());
		}

		ExportXMLUtils.createTargetCriteriaTag(versionElement, contentObject);
		
		if(contentObject.getMetadataForm() != null) {
			addMetadataFormDefinition(contentObject.getMetadataForm().getFormDefinition());
            ExportXMLUtils.createMetadataForm( contentObject.getMetadataForm(), versionElement, "Metadata" );
		}

		if (contentObject.isDynamicVariantEnabled()) 
		{
			Zone zone = contentObject.getZone();
			if (zone != null)
			{
				Element deliveryElement = versionElement.addElement("Delivery");
				deliveryElement.addAttribute("type", contentObject.getDeliveryTypeText());
				deliveryElement.addAttribute("zonerefid", toString(zone != null ? zone.getId() : 0));

				// MessageListFilterType can be ID_ALL, ID_PRODUCTION_ONLY and ID_LATEST
				// Since this export does only LATEST messages, use ID_LATEST
				int priority = contentObject.getContentObjectData().getPriorityNumberByZone(zone.getId());
				if (priority > 0)
					deliveryElement.addAttribute("priority", toString(priority));
                if(doc.getMasterTouchpointSelection() != null) {
                    TouchpointSelection tpSelection = contentObject.getOwningTouchpointSelection();
                    if(tpSelection == null) {
                        tpSelection = doc.getMasterTouchpointSelection();
                    }
                    boolean repeatWithNext = contentObject.getContentObjectData().getRepeatWithNextByZone(zone, tpSelection);
                    if(repeatWithNext) {
                        deliveryElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                    }
                }
			}

			try
			{
				createContentTagForSelectableMessage(contentObject, versionElement, doc);
			} 
			catch (Exception e) 
			{
	            log.error(" unexpected exception when invoking createContentTagForSelectableMessage(contentObject, versionElement)", e);
			}
		}
		else
		{
			Zone zone = contentObject.getZone();
			Element deliveryElement = null;
			if (zone != null)
			{
				deliveryElement = versionElement.addElement("Delivery");
				deliveryElement.addAttribute("type", contentObject.getDeliveryTypeText());
				deliveryElement.addAttribute("zonerefid", toString(zone != null ? zone.getId() : 0));

				// MessageListFilterType can be ID_ALL, ID_PRODUCTION_ONLY and ID_LATEST
				// Since this export does only LATEST messages, use ID_LATEST
				int priority = contentObject.getContentObjectData().getPriorityNumberByZone(zone.getId());
				if (priority > 0)
					deliveryElement.addAttribute("priority", toString(priority));
                if(doc.getMasterTouchpointSelection() != null) {
                    TouchpointSelection tpSelection = contentObject.getOwningTouchpointSelection();
                    if(tpSelection == null) {
                        tpSelection = doc.getMasterTouchpointSelection();
                    }
                    boolean repeatWithNext = contentObject.getContentObjectData().getRepeatWithNextByZone(zone, tpSelection);
                    if(repeatWithNext) {
                        deliveryElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                    }
                }
			}

			if (!contentObject.isStructuredContentEnabled()) {
				if (zone != null && contentObject.isMultipartType()) {
					Element contentsElement = deliveryElement.addElement("Contents");
					createContentTagForRegularMessage(contentObject, zone, contentsElement, doc);

				} else {

					Element contentsElement = versionElement.addElement("Contents");
					createContentTagForRegularMessage(contentObject, null, contentsElement, doc);
				}
			}
		}
	}

	private void createContentTagForRegularMessage(ContentObject contentObject, Zone zone, Element contentsElement, Document doc) 
	{
        List<MessagepointLocale> languages = doc.getTouchpointLanguagesAsLocales();
        String defaultTouchpointLocaleCode = doc.getDefaultTouchpointLanguageLocaleCode();
        
		if (contentObject.isMultipartType())
		{
			Integer partNum = 1;
			for (ZonePart zonePart : zone.getPartsInSequenceOrder())
			{
				Element partElement = contentsElement.addElement("Part");
				partElement.addAttribute("num", (partNum++).toString());
				partElement.addAttribute("refid", toString(zonePart.getId()));
				
				if ( zonePart.getContentType().getId() == ContentType.TEXT )
					partElement.addAttribute("contenttype", "Text");
				else
					partElement.addAttribute("contenttype", "Graphic");

				List<ContentObjectAssociation> contentAssociations = contentObject.getContentObjectData().getContentObjectAssociations();
				Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, contentAssociations);
				
				if ( partAssociations == null || partAssociations.isEmpty() )
				{
					continue;
				}			

				boolean zonePartEmpty = false;
    	        for (MessagepointLocale locale : languages) 
				{
					String languageCode = locale.getLanguageCode();
					String localeCode = locale.getCode();

					boolean missingFlag = true;
					for( ContentObjectAssociation partCa : partAssociations )
					{
					    if ( partCa.getTouchpointPGTreeNode() != null || partCa.getContentObjectPGTreeNode() != null )
					        continue;
					    
					    if ( partCa.getMessagepointLocale().getId() != locale.getId() )
					    {
					        continue;
					    }
					    
					    if (partCa.getTypeId() == ContentAssociationType.ID_EMPTY)
					    {
					    	zonePartEmpty = true;
						    partElement.addAttribute("empty", Boolean.TRUE.toString());
						    break;
					    }
					    
						String languageContent = "";						
						Content langPartContent = partCa.getContent();
		                String languageImageName = "";
		                String langImageUpload = "";
		                String languageFileName = "";
					    ComplexValue imageLink = null;

		                if ( langPartContent == null ) 
		                {
		                	languageContent = null;
		                } 
		                else if ( zonePart.getContentType().getId() == ContentType.TEXT ) 
		                {
							languageContent = langPartContent.getEncodedContent();
						} 
		                else 
		                {
							imageLink = langPartContent.getImageLink();
		                	if ( langPartContent.getImageLocation() != null )
		                	{
		                		if (exportOptions.isIncludeImagePathOnly())
		                		{
		                			languageContent = langPartContent.getRelativeImageLocation();
		                		}
		                		else
		                		{
		                			languageContent = ExportXMLUtils.encodeFile(langPartContent.getImageLocation());
		                		}
		                	}
							languageFileName = langPartContent.getImageName();
							languageImageName = langPartContent.getAppliedImageFilename();
							langImageUpload = DateUtil.formatDateForXMLOutput(langPartContent.getImageUploadedDate());
						}
		                
		                addContentImageDatabaseFiles(doc, langPartContent);

						missingFlag = false;
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
						if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
				        if(partCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
				        {
				        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				        }
				        else if (partCa.getReferencingImageLibrary() != null)
						{
							if (partCa.getReferencingImageLibrary().isGlobalContentObject())
				        		contentElement.addAttribute("contentlibraryrefid", toString(partCa.getReferencingImageLibrary().getId()));
							else
								contentElement.addAttribute("localcontentlibraryrefid", toString(partCa.getReferencingImageLibrary().getId()));
						}
						else
						{
							if (languageContent != null)
							{
								contentElement.addCDATA(languageContent);
								if ( languageFileName != null && !languageFileName.isEmpty())
								{
								    contentElement.addAttribute("filename", languageFileName);
								    if (exportOptions.isIncludeImagePathOnly()) 
								    {
									    contentElement.addAttribute("pathonly", "true");
									    originalImageFiles.add(langPartContent.getImageLocation());
								    }
								}
								if ( languageImageName != null && !languageImageName.isEmpty())
								    contentElement.addAttribute("imagename", languageImageName);
								if (!langImageUpload.isEmpty())
								    contentElement.addAttribute("uploaded", langImageUpload);
							}
							
							if (imageLink != null)
							{
								Element imageLinkElement = partElement.addElement("ContentOfImageLink");
								imageLinkElement.addAttribute("language", languageCode);
								imageLinkElement.addAttribute("locale", localeCode);
								imageLinkElement.addAttribute("guid", imageLink.getGuid());
								imageLinkElement.addCDATA(imageLink.getEncodedValue());
							}
							
							if(langPartContent != null) {
							    addImageAltText(langPartContent.getImageAltText(), partElement, languageCode, localeCode);
								addImageExtLink(langPartContent.getImageExtLink(), partElement, languageCode, localeCode);
								addImageExtPath(langPartContent.getImageExtPath(), partElement, languageCode, localeCode);
							}
						}
					}
					if (missingFlag && !zonePartEmpty)
					{
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
						contentElement.addAttribute("locale", localeCode);
				        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
				        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
						else
							contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
					}
				}
			}
		}

		else
		{	// Regular Text or Graphic Message (not multipart)
	        for (MessagepointLocale locale : languages) 
			{
				String languageCode = locale.getLanguageCode();
				String localeCode = locale.getCode();
				List<ContentObjectAssociation> msgCAs = contentObject.getContentObjectData().getContentObjectAssociations();
				boolean missingFlag = true;
				for( ContentObjectAssociation ca : msgCAs )
				{
				    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
				    {
				        continue;
				    }
				    Content languageContent = ca.getContent();
					ComplexValue imageLink = null;

                    addContentImageDatabaseFiles(doc, languageContent);

                    Element contentElement = null;
				    missingFlag = false;
			        contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
					if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
			        {
			        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
			        }
					else if (ca.getReferencingImageLibrary() != null)
					{
						if (ca.getReferencingImageLibrary().isGlobalContentObject())
							contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
						else
							contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
					}
					else if (languageContent != null)
			        {
				        if (contentObject.getContentType().getId() == ContentType.TEXT) 
				        {
				            contentElement.addCDATA(languageContent.getEncodedContent());
				        }
				        else
				        {
							imageLink = languageContent.getImageLink();
				        	if ( languageContent.getImageLocation() != null )
				        	{
		                		if (exportOptions.isIncludeImagePathOnly())
		                		{
		                			contentElement.addCDATA(languageContent.getRelativeImageLocation());
		                		}
		                		else
		                		{
		                			contentElement.addCDATA(ExportXMLUtils.encodeFile(languageContent.getImageLocation()));
		                		}
				        	}
				            String fileImageName = languageContent.getImageName();
				            String langImageName = languageContent.getAppliedImageFilename();
				            Date imageUploaded = languageContent.getImageUploadedDate();
				            if ( fileImageName != null )
				            {
				                contentElement.addAttribute("filename", fileImageName);
							    if (exportOptions.isIncludeImagePathOnly())
							    {
								    contentElement.addAttribute("pathonly", "true");
								    originalImageFiles.add(languageContent.getImageLocation());
							    }
				            }
				            if ( langImageName != null )
				                contentElement.addAttribute("imagename", langImageName);
				            if ( imageUploaded != null )
				                contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(imageUploaded));
				            
							if (imageLink != null)
							{
								Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
								imageLinkElement.addAttribute("language", languageCode);
								imageLinkElement.addAttribute("locale", localeCode);
								imageLinkElement.addAttribute("guid", imageLink.getGuid());
								imageLinkElement.addCDATA(imageLink.getEncodedValue());
							}
                            
							if(languageContent != null) {
							    addImageAltText(languageContent.getImageAltText(), contentsElement, languageCode, localeCode);
								addImageExtLink(languageContent.getImageExtLink(), contentsElement, languageCode, localeCode);
								addImageExtPath(languageContent.getImageExtPath(), contentsElement, languageCode, localeCode);
							}
				        }
			        }
				}
				if (missingFlag)
				{
					Element contentElement = contentsElement.addElement("Content");
			        contentElement.addAttribute("language", languageCode);
					contentElement.addAttribute("locale", localeCode);
			        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
			        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
					else
						contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
				}
			}
		}
	}

    public void addImageAltText(ComplexValue imageAltText, Element contentParentElement, String language, String localeCode) {
        if (imageAltText != null) {
            Element imageAltElement = contentParentElement.addElement("AltText");
            imageAltElement.addAttribute("language", language);
			imageAltElement.addAttribute("locale", localeCode);
            imageAltElement.addCDATA(imageAltText.getEncodedValue());
        }
    }

	public void addImageExtLink(ComplexValue imageExtLink, Element contentParentElement, String language, String localeCode) {
		if (imageExtLink != null) {
			Element imageExtLinkElement = contentParentElement.addElement("ImageExternalLink");
			imageExtLinkElement.addAttribute("language", language);
			imageExtLinkElement.addAttribute("locale", localeCode);
			imageExtLinkElement.addCDATA(imageExtLink.getEncodedValue());
		}
	}

	public void addImageExtPath(ComplexValue imageExtPath, Element contentParentElement, String language, String localeCode) {
		if (imageExtPath != null) {
			Element imageExtLinkElement = contentParentElement.addElement("ImageExternalPath");
			imageExtLinkElement.addAttribute("language", language);
			imageExtLinkElement.addAttribute("locale", localeCode);
			imageExtLinkElement.addCDATA(imageExtPath.getEncodedValue());
		}
	}

	private Set<ContentObjectAssociation> getAssociationsForZonePart(ZonePart zp, List<ContentObjectAssociation> contAssocs)
	{
        Set<ContentObjectAssociation> result = new HashSet<>();
        for( ContentObjectAssociation ca : contAssocs )
        {
            if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
            {
                result.add(ca);
            }
        }
        return result;
	}

	// Selectable Messages	

	private void createContentTagForSelectableMessage(ContentObject contentObject, Element versionElement, Document doc)
	throws Exception 
	{
		Element selectableContentElement = versionElement.addElement("Selections");		
		if (contentObject.isDynamicVariantEnabled())
		{
			selectableContentElement.addAttribute("selectorrefid", toString(contentObject.getParameterGroup().getId()));
		}

		// Default Selection
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
		if (contentObject.isMultipartType())
		{
			defaultSelectionContentsElement.addAttribute("contenttype", "Multi-part");
		}
		else if (contentObject.getContentType().getId() == ContentType.TEXT)
		{
			defaultSelectionContentsElement.addAttribute("contenttype", "Text");			
		}
		else
		{
			defaultSelectionContentsElement.addAttribute("contenttype", "Graphic");			
		}

		createContentTagForRegularMessage(contentObject,  contentObject.getZone() != null ? contentObject.getZone() : null, defaultSelectionContentsElement, doc);

        List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObject, contentObject.getFocusOnDataType());

        Set<Long> explored = new HashSet<>();
        for (ParameterGroupTreeNode pgtn : topLevelTreeNodes)
        {
		    if ( explored.contains(pgtn.getId()) )
		        continue;
		    explored.add(pgtn.getId());

		    List<ContentObjectAssociation> messageContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, pgtn);
			createSelectionTag(contentObject, messageContentAssociationList, pgtn, defaultSelectionElement, doc);
		}
	}

	private void createSelectionTag(ContentObject contentObject, List<ContentObjectAssociation> msgContentAssociations, ParameterGroupTreeNode parameterGroupTreeNode, Element element, Document doc)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());
		
        if (touchpointSelection != null) 
        {
            selectionElement.addAttribute("dna", touchpointSelection.getDna());
        }
        
        if (parameterGroupTreeNode != null) {
            selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
        }
        
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}		
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		Element selectionContentsElement = selectionElement.addElement("Contents");		
		ExportXMLUtils.createContentTagForSelection(contentObject, msgContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this, doc);
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes) 
			{
				List<ContentObjectAssociation> childContentAssociationList = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, childNode);
				
				createSelectionTag(contentObject, childContentAssociationList, childNode, selectionElement, doc);
			}
		}
	}
	
//////
////// End of createMessages Subroutines  
//////   
/////////////////////////////////////////////////////////////////////////////////////////////////
	public boolean getIncludeImagePathOnly()
	{
		return exportOptions.isIncludeImagePathOnly();
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    private static String toString( long value )
    {
        String val = Long.valueOf(value).toString();
        return val;
    }
    
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long docId, ExportImportOptions exportOptions, User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportDocumentToXMLServiceRequest request = new ExportDocumentToXMLServiceRequest(exportId, docId, exportOptions, requestor, statusPollingBackgroundTask);

        context.setRequest(request);

        ExportDocumentToXMLServiceResponse serviceResp = new ExportDocumentToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
    
    public static class DataGroupIdComparator implements Comparator<DataGroup> {

    	@Override
		public int compare(DataGroup arg0, DataGroup arg1) {
    		int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
    		return result;
    	}
    }
    public static class TextStyleIdComparator implements Comparator<TextStyle> {

    	@Override
		public int compare(TextStyle arg0, TextStyle arg1) {
    		int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
    		return result;
    	}
    }
    public static class ParagraphStyleIdComparator implements Comparator<ParagraphStyle> {

    	@Override
		public int compare(ParagraphStyle arg0, ParagraphStyle arg1) {
    		int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
    		return result;
    	}
    }

    public static class ListStyleIdComparator implements Comparator<ListStyle> {

    	@Override
		public int compare(ListStyle arg0, ListStyle arg1) {
    		int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
    		return result;
    	}
    }
    
    private void addMetadataFormFromSelection(List<MetadataForm> metadataFormList, TouchpointSelection selection) {
    	if(selection == null) {
    		return;
    	}
		MetadataForm metadataForm = selection.getMetadataForm();
		if(metadataForm != null) {
			metadataFormList.add(metadataForm);
		}
		
		List<TouchpointSelection> children = selection.getChildren();
		if(children != null) {
			for(TouchpointSelection child : children) {
				addMetadataFormFromSelection(metadataFormList, child);
			}
		}
    }
    ////////////////////////////////////////////////////////////////////////////
    /// 
    /// Database Files Export
    ///
    ////////////////////////////////////////////////////////////////////////////
    
    private void createDatabaseFiles( Document doc, Element export ) 
    {   	
		List<ContentObject> messageInstanceList = ContentObject.findAllContentObjectsByDocument(doc, false);
		if ( !messageInstanceList.isEmpty() )
		{
			for (ContentObject mi : messageInstanceList)
			{
//				if (mi.isFreeformZone())
				{
					// It adds active and also working COA
					Set<ContentObjectAssociation> cas = mi.getContentObjectAssociations();
					for(ContentObjectAssociation ca : cas)
					{
						Content content = ca.getContent();
						if(content != null && content.getImages() != null && !content.getImages().isEmpty()){
							databaseFiles.addAll(content.getImages());
						}
					}
				}
			}
		}
	    	
        List<Document> documents = new ArrayList<>();
        documents.add(doc);
		List<LookupTableInstance> lookupTableInstances = LookupTable.findAllForDocumentFiltered(documents, "", -1);
		if ( !lookupTableInstances.isEmpty() )
		{
			for( LookupTableInstance lti : lookupTableInstances )
			{
				if (lti.getLookupFile() != null)
					databaseFiles.add(lti.getLookupFile());
			}
		}
		
		List<MetadataForm> metadataFormList = new ArrayList<>();
		
		MetadataForm metadataForm = doc.getMetadataForm();
		if(metadataForm != null) {
			metadataFormList.add(metadataForm);
		}
		
		TouchpointSelection masterSelection = doc.getMasterTouchpointSelection();
		addMetadataFormFromSelection(metadataFormList, masterSelection);
		
		for(MetadataForm form : metadataFormList)
		{
			Set<MetadataFormItem> items = form.getFormItems();
			if(items != null) {
				for(MetadataFormItem item : items ) {
					DatabaseFile uploadedFile = item.getUploadedFile();
					if(uploadedFile != null) {
						databaseFiles.add(uploadedFile);
					}
				}
			}
		}
			
		databaseFilesElement = export.addElement("DatabaseFiles");
		if ( !databaseFiles.isEmpty() )
		{
			List<DatabaseFile> databaseFileslist = new ArrayList<>(databaseFiles);
			Collections.sort(databaseFileslist, new Comparator<>() {
                @Override
                public int compare(DatabaseFile arg0, DatabaseFile arg1) {
                    return Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
                }
            });
		
			for (DatabaseFile databaseFile : databaseFileslist) 
			{
				ExportXMLUtils.createDatabaseFileTag(databaseFile, databaseFilesElement, doc);
			}
		}
    }
    
    public void addContentImageDatabaseFiles(Document doc, Content content) {
        if(content != null && content.getImages() != null && !content.getImages().isEmpty()) {
            for(DatabaseFile databaseFile : content.getImages()) {
                if(! databaseFiles.contains(databaseFile)) {
                    databaseFiles.add(databaseFile);
                    ExportXMLUtils.createDatabaseFileTag(databaseFile, databaseFilesElement, doc);
                }
            }
        }
    }
    
    ////////////////////////////////////////////////////////////////////////////
    /// 
    /// Embedded Content Export
    ///
    ////////////////////////////////////////////////////////////////////////////
    
    private void createEmbeddedContents( Document doc, Set<ContentObject> indirectGlobalObjects, Element export )
    {   	
    	Set<ContentObject> embeddedContentInstanceSet = new HashSet<>(
                indirectGlobalObjects
                        .stream()
                        .filter(co -> co.getObjectType() == ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT)
                        .collect(Collectors.toList())
        );

    	embeddedContentInstanceSet.addAll(ContentObject.findGlobalSmartTextsVisibleForDocument(doc, false));

		if ( !embeddedContentInstanceSet.isEmpty() && !embeddedContentInstanceSet.isEmpty())
		{
			Element embeddedContentsElement = export.addElement("EmbeddedContents");
	    	List<ContentObject> embeddedContentInstancelist = new ArrayList<>(embeddedContentInstanceSet);
			Collections.sort(embeddedContentInstancelist, new ContentObjectIdComparator());
		
			for (ContentObject contentObject : embeddedContentInstancelist) 
			{
                if(contentObject.isRemoved()) continue;

                contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);

                if(contentObject.getContentObjectDataTypeMap() == null || contentObject.getContentObjectDataTypeMap().isEmpty()) continue;
                if(contentObject.getContentObjectData() == null) {
                    continue;
                }

                contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
				createEmbeddedContentTag(contentObject, embeddedContentsElement, doc);
			}
		}
    }
    
	private void createEmbeddedContentTag(ContentObject contentObject, Element embeddedContentsElement, Document doc)
	{
		Element embeddedContentElement = embeddedContentsElement.addElement("EmbeddedContent");
		embeddedContentElement.addAttribute("id", toString(contentObject.getId()));
		embeddedContentElement.addAttribute("guid", contentObject.getGuid());
        embeddedContentElement.addAttribute("dna", contentObject.getDna());

        embeddedContentElement.addAttribute("modelhash", contentObject.getHashSafe());
		{
			ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
			if (activeCopy != null) {
				embeddedContentElement.addAttribute("activecopyhash", activeCopy.getHashSafe());
			}
		}

		{
			ContentObjectData workingCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
			if (workingCopy != null) {
				embeddedContentElement.addAttribute("workingcopyhash", workingCopy.getHashSafe());
			}
		}

		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null) 
		{
            Element externalIdElement = embeddedContentElement.addElement("ExternalId");
            externalIdElement.addText(tmd.getExternalId().toString());
		}
		
		String messageType = "";
		if (contentObject.isDynamicVariantEnabled()) 
		{
			messageType = "Selectable(EmbeddedContent)";
		}
		else if (!contentObject.isVariableContentEnabled())
		{
			messageType = "Global";
		}
		else
		{
			messageType = "Regular";
		}

		embeddedContentElement.addAttribute("type", messageType);

		String contentType = "Text";
		embeddedContentElement.addAttribute("contenttype", contentType);

		embeddedContentElement.addAttribute("defaultlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
		embeddedContentElement.addAttribute("defaultlocale", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		createECVersionTag(contentObject, embeddedContentElement, doc);
	}
	
	private void createECVersionTag(ContentObject contentObject, Element embeddedContentElement, Document doc)
	{
		Element versionElement = embeddedContentElement.addElement("Version");

        if(contentObject.getContentObjectData() == null) {
            log.error("contentObject \"" + contentObject.getName() + "\" (id=" + contentObject.getId() + ") has no version data.");
            return;
        }

        versionElement.addAttribute("id", toString(contentObject.getId()));

		versionElement.addAttribute("guid", contentObject.getContentObjectData().getGuid());

		if (contentObject.isFocusOnActiveData())
			versionElement.addAttribute("status", "Active");
		else if (contentObject.isFocusOnWorkingData())
				versionElement.addAttribute("status", "Working Copy");
		else
			versionElement.addAttribute("status", "Archived");

		if (contentObject.getContentObjectData().getCreationReason() != null)
			versionElement.addAttribute("origin", contentObject.getContentObjectData().getCreationReason().getLocaledString());
		else
			versionElement.addAttribute("origin", "New");

		versionElement.addAttribute("advanced", Boolean.valueOf(contentObject.isAdvanced()).toString());
		
		versionElement.addAttribute("deliverytypeid", toString(contentObject.getDeliveryType()));
				
        versionElement.addAttribute("usagetypeid", toString(contentObject.getUsageTypeId()));

		int contentSpecializationType = 0;
		if (contentObject.getContentType().isMarkup())
			contentSpecializationType = 1;
		else if (contentObject.getContentType().isSharedFreeform())
			contentSpecializationType = 2;

        versionElement.addAttribute("contenttypeid", toString(contentSpecializationType));
		
		versionElement.addAttribute("compvarformattype", toString(contentObject.getCompVarFormatType()));

		versionElement.addAttribute("insertasparagraph", Boolean.valueOf(contentObject.isInsertAsBlockContent()).toString());
		
		versionElement.addAttribute("contenttrimtype", toString(contentObject.getContentTrimType()));

		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObject.getName());
		
		versionElement.addAttribute("supportstables", Boolean.valueOf(contentObject.isSupportsTables()).toString());
        versionElement.addAttribute("supportsforms", Boolean.valueOf(contentObject.isSupportsForms()).toString());
        versionElement.addAttribute("supportsbarcodes", Boolean.valueOf(contentObject.isSupportsBarcodes()).toString());
        versionElement.addAttribute("supportscontentmenus", Boolean.valueOf(contentObject.isSupportsContentMenus()).toString());
        versionElement.addAttribute("keepContentTogether", Boolean.valueOf(contentObject.isKeepContentTogether()).toString());
		versionElement.addAttribute("renderastaggedtext", Boolean.valueOf(contentObject.isRenderAsTaggedText()).toString());

        if(contentObject.getDataGroupCompVarFormatType() != 0) {
            versionElement.addAttribute("datagroupcompvarformattype", toString(contentObject.getDataGroupCompVarFormatType()));
        }

		Element metatagsElement = versionElement.addElement("Metatags");
		if (contentObject.getMetatags() != null && !contentObject.getMetatags().equalsIgnoreCase(""))
		{
			metatagsElement.addText(contentObject.getMetatags());
		}
		
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObject.getDescription() != null && !contentObject.getDescription().equalsIgnoreCase(""))
		{
			descriptionElement.addText(contentObject.getDescription());
		}
		
		Element commentsElement = versionElement.addElement("Comments");
        List<ContentObjectComment> comments = new ArrayList<>(contentObject.getComments());
		Collections.sort(comments, new CommentIdComparator());		
		for (ContentObjectComment comment : comments)
		{
			if (comment != null && comment.getComment() != null && !comment.getComment().equalsIgnoreCase(""))
			{
				Element commentElement = commentsElement.addElement("Comment");
				commentElement.addAttribute("id", toString(comment.getId()));				
				commentElement.addAttribute("created", DateUtil.formatDateForXMLOutput(comment.getCreated()));				
				commentElement.addText(comment.getComment());
			}
		}
		
		Element timingElement = versionElement.addElement("Timing");
		if (contentObject.getStartDate() != null) 
		{
			timingElement.addAttribute("startdate", DateUtil.formatDateYYYYsMMsDD(contentObject.getStartDate()));
		}
		if (contentObject.getEndDate() != null)
		{
			timingElement.addAttribute("enddate", DateUtil.formatDateYYYYsMMsDD(contentObject.getEndDate()));
		}
		if ( contentObject.getStartDate() != null || contentObject.getEndDate() != null ) 
		{
			timingElement.addAttribute("repeatsannually", Boolean.valueOf(contentObject.isRepeatDatesAnnually()).toString());
		}

		if(contentObject.getMetadataForm() != null) {
			addMetadataFormDefinition(contentObject.getMetadataForm().getFormDefinition());
			ExportXMLUtils.createMetadataForm( contentObject.getMetadataForm(), versionElement, "Metadata" );
		}

		ExportXMLUtils.createTargetCriteriaTag(versionElement, contentObject);
		
		if (contentObject.isDynamicVariantEnabled())
		{
			try 
			{
				createContentTagForSelectableEmbeddedContent(contentObject, versionElement, doc);
			} 
			catch (Exception e) 
			{
	            log.error(" unexpected exception when invoking createContentTagForSelectableEmbeddedContent(contentObject, versionElement, doc)", e);
			}
		}
		else
		{
			Element contentsElement = versionElement.addElement("Contents");
			createContentTagForRegularEmbeddedContent(contentObject, contentsElement, doc);
		}
	}

	private void createContentTagForRegularEmbeddedContent(ContentObject contentObject, Element contentsElement, Document doc) 
	{
        String defaultSystemLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();

		List<MessagepointLocale> languages;
		if(exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects())
			languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
		else
			languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
		{
			String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			List<ContentObjectAssociation> msgCAs = contentObject.getContentObjectData().getContentObjectAssociations();
			boolean missingFlag = true;
			for( ContentObjectAssociation ca : msgCAs )
			{
			    if ( ca.getPGTreeNode() != null ||
			         ca.getMessagepointLocale().getId() != locale.getId() )
			    {
			        continue;
			    }
			    Content languageContent = ca.getContent();
			    
                addContentImageDatabaseFiles(doc, languageContent);
			    
			    Element contentElement = null;
			    missingFlag = false;
		        contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				if (localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        {
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }
		        else if (languageContent != null)
		        {
		            contentElement.addCDATA(languageContent.getEncodedContent());
		        }
			}
			if (missingFlag)
			{
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
		        if (!localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				else
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			}
		}
	}

	private void createContentTagForSelectableEmbeddedContent(ContentObject contentObject, Element versionElement, Document doc)
	throws Exception 
	{
		Element selectableContentElement = versionElement.addElement("Selections");		
		if (contentObject.getParameterGroup() != null)
		{
			selectableContentElement.addAttribute("selectorrefid", toString(contentObject.getParameterGroup().getId()));
		}

		// Default Selection
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
		defaultSelectionContentsElement.addAttribute("contenttype", "Text");			

		createContentTagForRegularEmbeddedContent(contentObject, defaultSelectionContentsElement, doc);
        //List<ContentObjectAssociation> topLevelAssociations = ContentObjectAssociation.findTopLevelAssociationsForContentObject(contentObject, contentObject.getFocusOnDataType());
        List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObject, contentObject.getFocusOnDataType());

		Set<Long> explored = new HashSet<>();
		for (ParameterGroupTreeNode pgtn : topLevelTreeNodes)
		{
		    if ( explored.contains(pgtn.getId()) )
		        continue;
		    explored.add(pgtn.getId());

			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), pgtn);
			if (cas != null && !cas.isEmpty())
			{
				createEmbeddedContentSelectionTag(contentObject, null, cas, pgtn, defaultSelectionElement, doc);
			}
		}
	}

	private void createEmbeddedContentSelectionTag(ContentObject contentObject, List<ContentObjectAssociation> ecProdContentAssociations, List<ContentObjectAssociation> ecContentAssociations, ParameterGroupTreeNode parameterGroupTreeNode, Element element, Document doc)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());
        if (touchpointSelection != null) 
        {
            selectionElement.addAttribute("dna", touchpointSelection.getDna());
        }
        
        if(parameterGroupTreeNode != null) {
            selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
        }
        
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}		
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		Element selectionContentsElement = selectionElement.addElement("Contents");		

		if (ecProdContentAssociations != null)
			ExportXMLUtils.createContentTagForEmbeddedContentProductionSelection(contentObject, ecProdContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this, doc, exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects());
		else
			ExportXMLUtils.createContentTagForEmbeddedContentSelection(contentObject, ecContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this, doc, exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects());
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes) 
			{
				List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), childNode);
				if (cas != null && !cas.isEmpty())
				{
					createEmbeddedContentSelectionTag(contentObject, null, cas, childNode, selectionElement, doc);
				}
			}
		}
	}
	
    ////////////////////////////////////////////////////////////////////////////
    /// 
    /// Content Library Export
    ///
    ////////////////////////////////////////////////////////////////////////////
    
    private void createContentLibrary( Document doc, Set<ContentObject> indirectGlobalObjects, Element export )
    {
        Set<ContentObject> globalImages = new HashSet<>(
                indirectGlobalObjects
                        .stream()
                        .filter(co -> co.getObjectType() == ContentObject.OBJECT_TYPE_GLOBAL_IMAGE)
                        .collect(Collectors.toList())

        );

		globalImages.addAll(ContentObject.findGlobalImagesVisibleForDocument(doc, false, false));

		if ( !globalImages.isEmpty() && !globalImages.isEmpty())
		{
			Element contentLibraryElement = export.addElement("ContentLibrary");
			List<ContentObject>contentLibraryInstancelist = new ArrayList<>(globalImages);
			Collections.sort(contentLibraryInstancelist, new ContentObjectIdComparator());
		
			for (ContentObject contentObject : contentLibraryInstancelist)
			{
                if(contentObject.isRemoved()) continue;

                contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);

                if(contentObject.getContentObjectDataTypeMap() == null || contentObject.getContentObjectDataTypeMap().isEmpty()) continue;
                if(contentObject.getLatestContentObjectDataActiveCentric() == null) continue;

                contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
				createContentLibraryTag(contentObject, contentLibraryElement, doc);
			}
		}
    }
    
	private void createContentLibraryTag(ContentObject contentObject, Element contentLibraryElement, Document doc)
	{
		Element imageLibraryElement = contentLibraryElement.addElement("ContentItem");
		imageLibraryElement.addAttribute("id", toString(contentObject.getId()));
		imageLibraryElement.addAttribute("guid", contentObject.getGuid());
        imageLibraryElement.addAttribute("dna", contentObject.getDna());

        imageLibraryElement.addAttribute("modelhash", contentObject.getHashSafe());
        {
            ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
            if (activeCopy != null) {
                imageLibraryElement.addAttribute("activecopyhash", activeCopy.getHashSafe());
            }
        }

        {
			ContentObjectData workingCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
            if (workingCopy != null) {
                imageLibraryElement.addAttribute("workingcopyhash", workingCopy.getHashSafe());
            }
        }

		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null) 
		{
            Element externalIdElement = imageLibraryElement.addElement("ExternalId");
            externalIdElement.addText(tmd.getExternalId().toString());
		}
		
		String messageType = "";
		if (contentObject.isDynamicVariantEnabled())
		{
			messageType = "Selectable(ContentLibrary)";
		}
		else if (!contentObject.isVariableContentEnabled())
		{
			messageType = "Global";
		}
		else
		{
			messageType = "Regular";
		}

		imageLibraryElement.addAttribute("type", messageType);

		String contentType = "Graphic";
		imageLibraryElement.addAttribute("contenttype", contentType);

		imageLibraryElement.addAttribute("defaultlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
		imageLibraryElement.addAttribute("defaultlocale", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		createILVersionTag(contentObject, imageLibraryElement, doc);
	}
	
	private void createILVersionTag(ContentObject contentObject, Element imageLibraryElement, Document doc)
	{
		Element versionElement = imageLibraryElement.addElement("Version");

		ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataActiveCentric();

		if (contentObjectData == null) {
            log.error("contentObject \"" + contentObject.getName() + "\" (id=" + contentObject.getId() + ") has no version data.");
			return;
		}

		versionElement.addAttribute("id", toString(contentObjectData.getId()));
		versionElement.addAttribute("guid", contentObject.getContentObjectData().getGuid());

		if (contentObject.isFocusOnActiveData())
			versionElement.addAttribute("status", "Active");
		else if (contentObject.isFocusOnWorkingData())
			versionElement.addAttribute("status", "Working Copy");
		else
			versionElement.addAttribute("status", "Archived");

		if (contentObject.getContentObjectData().getCreationReason() != null)
			versionElement.addAttribute("origin", contentObject.getContentObjectData().getCreationReason().getLocaledString());
		else
			versionElement.addAttribute("origin", "New");

		versionElement.addAttribute("deliverytypeid", toString(contentObject.getDeliveryType()));
				
		versionElement.addAttribute("usagetypeid", toString(contentObject.getUsageTypeId()));
		
		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObject.getName());

		Element metatagsElement = versionElement.addElement("Metatags");
		if (contentObject.getMetatags() != null && !contentObject.getMetatags().equalsIgnoreCase(""))
		{
			metatagsElement.addText(contentObject.getMetatags());
		}
		
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObject.getDescription() != null && !contentObject.getDescription().equalsIgnoreCase(""))
		{
			descriptionElement.addText(contentObject.getDescription());
		}
		
		Element commentsElement = versionElement.addElement("Comments");
        List<ContentObjectComment> comments = new ArrayList<>(contentObjectData.getComments());
		Collections.sort(comments, new CommentIdComparator());		
		for (ContentObjectComment comment : comments)
		{
			if (comment != null && comment.getComment() != null && !comment.getComment().equalsIgnoreCase(""))
			{
				Element commentElement = commentsElement.addElement("Comment");
				commentElement.addAttribute("id", toString(comment.getId()));				
				commentElement.addAttribute("created", DateUtil.formatDateForXMLOutput(comment.getCreated()));				
				commentElement.addText(comment.getComment());
			}
		}
		
		if(contentObject.getMetadataForm() != null) {
			addMetadataFormDefinition(contentObject.getMetadataForm().getFormDefinition());
			ExportXMLUtils.createMetadataForm( contentObject.getMetadataForm(), versionElement, "Metadata");
		}


		if (contentObject.isDynamicVariantEnabled()) 
		{
			try 
			{
				createContentTagForSelectableContentLibrary(contentObject, versionElement, doc);
			} 
			catch (Exception e) 
			{
	            log.error(" unexpected exception when invoking createContentTagForSelectableContentLibrary(contentLibraryInstance, versionElement, doc)", e);
			}
		}
		else
		{
			Element contentsElement = versionElement.addElement("Contents");
			createContentTagForRegularContentLibrary(contentObject, contentsElement, doc);
		}
	}

	private void createContentTagForRegularContentLibrary(ContentObject contentObject, Element contentsElement, Document doc) 
	{
		String defaultSystemLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();

		List<MessagepointLocale> languages;
		if(exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects())
			languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
		else
			languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

		ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataActiveCentric();
        
        for (MessagepointLocale locale : languages) 
		{
			String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			List<ContentObjectAssociation> msgCAs = contentObjectData.getContentObjectAssociations();
			boolean missingFlag = true;
			for( ContentObjectAssociation ca : msgCAs )
			{
			    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
			    {
			        continue;
			    }
			    Content languageContent = ca.getContent();
				ComplexValue imageLink = null;

                addContentImageDatabaseFiles(doc, languageContent);
                
			    Element contentElement = null;
			    missingFlag = false;
		        contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				if (localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        {
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }
		        else
		        {
		        	if (ca.getReferencingImageLibrary() != null)
		        	{
		        		if (ca.getReferencingImageLibrary().isGlobalContentObject())
			        		contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
		        		else
							contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
		        	}
		        	else if (languageContent != null)
		        	{
						imageLink = languageContent.getImageLink();
			        	if ( languageContent.getImageLocation() != null )
			        	{
	                		if (exportOptions.isIncludeImagePathOnly())
	                		{
	                			contentElement.addCDATA(languageContent.getRelativeImageLocation());
	                		}
	                		else
	                		{
	                			contentElement.addCDATA(ExportXMLUtils.encodeFile(languageContent.getImageLocation()));
	                		}
			        	}
			            String fileImageName = languageContent.getImageName();
			            String langImageName = languageContent.getAppliedImageFilename();
			            Date imageUploaded = languageContent.getImageUploadedDate();
			            if ( fileImageName != null )
			            {
			                contentElement.addAttribute("filename", fileImageName);
						    if (exportOptions.isIncludeImagePathOnly())
						    {
							    contentElement.addAttribute("pathonly", "true");
							    originalImageFiles.add(languageContent.getImageLocation());
						    }
			            }
			            if ( langImageName != null )
			                contentElement.addAttribute("imagename", langImageName);
			            if ( imageUploaded != null )
			                contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(imageUploaded));
			            
						if (imageLink != null)
						{
							Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
							imageLinkElement.addAttribute("language", languageCode);
							imageLinkElement.addAttribute("locale", localeCode);
							imageLinkElement.addAttribute("guid", imageLink.getGuid());
							imageLinkElement.addCDATA(imageLink.getEncodedValue());
						}
                        
						if(languageContent != null) {
						    addImageAltText(languageContent.getImageAltText(), contentsElement, languageCode, localeCode);
							addImageExtLink(languageContent.getImageExtLink(), contentsElement, languageCode, localeCode);
							addImageExtPath(languageContent.getImageExtPath(), contentsElement, languageCode, localeCode);
						}
		        	}
		        }
			}
			if (missingFlag)
			{
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
		        if (!localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				else
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			}
		}
	}
    
	private void createContentTagForSelectableContentLibrary(ContentObject contentObject, Element versionElement, Document doc)
	throws Exception 
	{
		ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataActiveCentric();

		Element selectableContentElement = versionElement.addElement("Selections");		
		if (contentObjectData.getParameterGroup() != null)
		{
			selectableContentElement.addAttribute("selectorrefid", toString(contentObjectData.getParameterGroup().getId()));
		}

		// Default Selection
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
		defaultSelectionContentsElement.addAttribute("contenttype", "Graphic");			

		createContentTagForRegularContentLibrary(contentObject, defaultSelectionContentsElement, doc);

//        List<ContentObjectAssociation> topLevelAssociations = ContentObjectAssociation.findTopLevelAssociationsForContentObject(contentObject, contentObject.getFocusOnDataType());

        List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObject, contentObject.getFocusOnDataType());

        Set<Long> explored = new HashSet<>();
        for (ParameterGroupTreeNode pgtn : topLevelTreeNodes)
        {
            if ( explored.contains(pgtn.getId()) )
                continue;

		    explored.add(pgtn.getId());

			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), pgtn);
			if (cas != null && !cas.isEmpty())
			{
				createContentLibrarySelectionTag(contentObject, null, cas, pgtn, defaultSelectionElement, doc);
			}
		}
	}

	private void createContentLibrarySelectionTag(ContentObject contentObject, List<ContentObjectAssociation> ecProdContentAssociations, List<ContentObjectAssociation> ecContentAssociations, ParameterGroupTreeNode parameterGroupTreeNode, Element element, Document doc)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());
		
        if (touchpointSelection != null) 
        {
            selectionElement.addAttribute("dna", touchpointSelection.getDna());
        }
        
        if(parameterGroupTreeNode != null) {
            selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
        }
        
		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}		
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		Element selectionContentsElement = selectionElement.addElement("Contents");	
		
		if (ecProdContentAssociations != null)
			ExportXMLUtils.createContentTagForProductionContentLibrarySelection(contentObject, ecProdContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this, doc, exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects());
		else 
			ExportXMLUtils.createContentTagForContentLibrarySelection(contentObject, ecContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this, doc, exportOptions.isIncludeAllLanguageLocalesForGlobalContentObjects());
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes) 
			{
				List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), childNode);
			    if (cas != null && !cas.isEmpty())
			    {
			    	createContentLibrarySelectionTag(contentObject, null, cas, childNode, selectionElement, doc);
			    }
			}
		}
	}
	
	public static class ContentObjectIdComparator implements Comparator<ContentObject> {

		@Override
		public int compare(ContentObject arg0, ContentObject arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}

	public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
	
	/// Export Data Model
	
	public static class DataRecordIdComparator implements Comparator<DataRecord> {

		@Override
		public int compare(DataRecord arg0, DataRecord arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
    private void createDataCollection( Document doc, Element parent )
    {
        DataSource primaryDs = null;
        DataSourceAssociation dsa = doc.getDataSourceAssociation();
        
        Element dataSpecNode = parent.addElement("DataCollection");
        
        if (dsa == null)
        	return;
        
        dataSpecNode.addAttribute( "guid", dsa.getGuid() );
        dataSpecNode.addAttribute( "dna",  dsa.getDna() );
        dataSpecNode.addAttribute( "primarydsrefid", toString(dsa.getPrimaryDataSource().getId()) );
        dataSpecNode.addAttribute( "customerdatarefid", toString(dsa.getCustomerDataElementId() != null? dsa.getCustomerDataElementId().getId(): 0) );
        dataSpecNode.addElement("Name").addCDATA(dsa.getName());
        
        Element dataSourcesNode = dataSpecNode.addElement("DataSources");
        for( DataSource ds : doc.getAllDataSources() )
        {
            if ( ds.isCustomerDriverFile() )
                primaryDs = ds;
            
            Element dsNode = dataSourcesNode.addElement("DataSource");
            dsNode.addAttribute( "id", toString(ds.getId()) );
            dsNode.addAttribute( "name", ds.getName() );
            
            int sourceTypeId = ds.getSourceType().getId();
            if (sourceTypeId == SourceType.TYPE_REFERENCE_CONNECTED)
            	dsNode.addAttribute( "type", "ReferenceConnected");
            else
            	dsNode.addAttribute( "type", sourceTypeId == SourceType.TYPE_PRIMARY ? "Driver" : "Reference");
            
            dsNode.addAttribute( "guid", ds.getGuid() );
            dsNode.addAttribute( "dna",  ds.getDna() );

            dsNode.addAttribute("datagroupsimplifiedmodel", "true");

            if(ds.isXML())
            {
            	XmlLayoutUtil.generateXmllayout(dsNode, ds);
            }
            else if(ds.isJSON())
            {
                JSONLayoutUtil.generateLayout(dsNode, ds);
            }
            else 
            {
	            dsNode.addElement("Encoding").addText(ds.getEncodingTypeName());
	            Element recLayNode = dsNode.addElement("RecordLayout");
	            recLayNode.addAttribute( "type", ds.getRecordTypeExportString() );
	            recLayNode.addAttribute( "end", "CRLF" );
	            if ( ds.isDelimited() )
	                recLayNode.addAttribute( "delimitedby", ds.getDelimeter() );
	            
	            createLayoutSpecificData( ds.getRecordType(), ds, recLayNode, dsNode );
	            
	            Element recListNode = recLayNode.addElement("RecordList");
                List<DataRecord> dataRecords = new LinkedList<>(ds.getDataRecords());
	            Collections.sort(dataRecords, new DataRecordIdComparator());
	            for( DataRecord rec : dataRecords )
	            {
	                createDataRecord( rec, ds.getRecordType(), recListNode, ds.isColumnar() );
	            }
            }
        }

        createDataGroups(dataSpecNode, doc, primaryDs);
        createReferenceDataSourceAssociation(doc, dataSpecNode);
    }

    private void createLayoutSpecificData( 
            RecordType recType,
            DataSource ds,
            Element recLayoutNode,
            Element dsNode )
    {
        if ( recType == null || recType.getId() == RecordType.TYPE_RECORD_TYPE || recType.getId() == RecordType.TYPE_CUSTOMER_ID )
        {
            Element indLayNode = recLayoutNode.addElement("IndicatorLayout");
            if ( ds.isColumnar() )
            {
                for( ColumnarLocation cl : ds.getColumnarIndicators() )
                {
                    Element segNode = indLayNode.addElement("IndicatorSegment");
                    segNode.addAttribute( "column", toString(cl.getIndicatorStart() + 1) );
                    segNode.addAttribute( "length", toString(cl.getIndicatorLength()) );
                }
            }
            else
            {
                for( Integer loc : ds.getDelimitedIndicators() )
                {
                    Element segNode = indLayNode.addElement("IndicatorSegment");
                    segNode.addAttribute( "position", loc.toString() );
                }
            }
        }
        else if ( recType.getId() == RecordType.TYPE_FIXED_RECORDS )
        {
            dsNode.addAttribute("recordpergroup", toString(ds.getDataRecords().size()) );
            dsNode.addAttribute("ignoreheaderlines", toString(ds.getHeaders()) );
        }
    }
    
    private void createDataRecord(
            DataRecord rec,
            RecordType recType,
            Element parent,
            boolean columnar )
    {
        Element recNode = parent.addElement("Record");
        recNode.addAttribute( "id", toString(rec.getId()) );
        recNode.addAttribute( "isrepeating", toString(rec.isRepeating()) );
        recNode.addAttribute( "isstartcust", toString(rec.isStartCustomer()) );

        if(rec.getParentDataRecord() != null) {
            recNode.addAttribute("parentid", toString(rec.getParentDataRecord().getId()));
        }

        if(rec.getDataGroup() != null) {
            recNode.addAttribute("datagroupid", toString(rec.getDataGroup().getId()));
        }

        recNode.addAttribute( "dna", rec.getDna() );

        if ( rec.getBreakIndicator() != null && !rec.getBreakIndicator().isEmpty())
            recNode.addAttribute( "breakindicator", rec.getBreakIndicator() );
        
        recNode.addElement("Indicator").addCDATA(rec.getRecordIndicator());                        // Add Indicator element for all record types
        
        if ( recType == null || recType.getId() == RecordType.TYPE_FIXED_RECORDS || (rec.getRecordIndicator() != null && rec.getRecordIndicator().equalsIgnoreCase("LookupRecord")))
        {
            recNode.addElement("PositionInRecordSet").addText(toString(rec.getRecordPosition()));  // this is the correct value of PositionInRecordSet
        }
        
        Element fieldListNode = recNode.addElement("FieldList");
        for( DataElement de : rec.getDataElements() )
        {
            createField( de, fieldListNode, columnar );
        }
    }
    
    private void createField(
            DataElement de,
            Element parent,
            boolean columnar )
    {
        Element field = parent.addElement("Field");
        field.addAttribute( "id", toString(de.getId()) );
        if ( columnar )
        {
            field.addAttribute( "itemcolumn", toString(de.getStartLocation()) );
            field.addAttribute( "itemlength", toString(de.getLength()) );
        }
        else
        {
            field.addAttribute( "itemposition", toString(de.getStartLocation()) );
        }
        field.addAttribute( "dna", de.getDna() );
        
        field.addAttribute( "catid", toString(de.getDataSubtypeId()) );
        field.addAttribute( "dec", toString(de.getDecimalPlaces()) );

        field.addAttribute( "anonymized", de.getAnonymized() ? "true" : "false" );
        if (de.getAnonymizationTypeId() != null)
        	field.addAttribute( "anonymizationtype", toString(de.getAnonymizationTypeId()) );

        field.addElement("FieldName").addText(de.getName().trim());
        if (de.getExternalFormatText() != null)
        	field.addElement("Format").addText(de.getExternalFormatText());
    }
    
    private void createDataGroups( 
            Element parent,
            Document doc,
            DataSource primaryDs )
    {
        Element dataGroupsNode = parent.addElement("DataGroups");
        if ( primaryDs != null )
            dataGroupsNode.addAttribute( "datasourcerefid", toString(primaryDs.getId()) );
        
        for( DataGroup dg : doc.getDataSourceAssociation().getPrimaryDataSource().getDataGroups() )
        {
            Element dgNode = dataGroupsNode.addElement("DataGroup");
            dgNode.addAttribute( "id", toString(dg.getId()) );
            dgNode.addAttribute( "level", toString(dg.getLevel()) );
            if ( dg.getParentDataGroupId() != null && dg.getParentDataGroupId() != 0) {
                dgNode.addAttribute("parentid", toString(dg.getParentDataGroupId()));
            }

            dgNode.addElement("Name").addText(dg.getName());
            if(primaryDs.isXML()) {
                XmlLayoutUtil.fillXmlDatagroupNodeContent(dgNode, dg);
            } else if(primaryDs.isJSON()) {
                JSONLayoutUtil.fillJsonDataGroups(dgNode, dg);
            } else {
            	dgNode.addAttribute( "startrecrefid", dg.getDataRecord() != null ? toString(dg.getDataRecord().getId()) : "0" );
				StringBuilder recordIds = new StringBuilder();
				recordIds.append(dg.getDataRecord().getId());
				dgNode.addElement("RecordIds").addText(recordIds.toString());
            }
        }
    }
    
    private void createReferenceDataSourceAssociation( Document doc, Element parent )
    {
        DataSourceAssociation dsa = doc.getDataSourceAssociation();
        Element dsaNode = parent.addElement("ReferenceDataSourceAssociation");
        
        for( ReferenceConnection refdc : dsa.getReferenceConnections() )
        {
            DataSource refds = refdc.getReferenceDataSource();
            Element refNode = dsaNode.addElement("Reference");
            refNode.addAttribute("id", toString(refdc.getId()) );
            refNode.addAttribute("guid", refdc.getGuid() );
            refNode.addAttribute("dsrefid", toString(refds.getId()) );
            if (refdc.getConnectorParameter() != null)
            	refNode.addAttribute("connectorparameter", refdc.getConnectorParameter());
            
            Element refVarNode = refNode.addElement("ReferenceVariableIds");
            StringBuilder variableIds = new StringBuilder();
            boolean first = true;
            if (refdc.getReferenceVariable() != null)
            {
            	variableIds.append(refdc.getReferenceVariable().getId());
            }
            else
            {
            	CompoundKey ck = refdc.getReferenceCompoundKey();
            	if (ck != null)
            	{
                    List<CompoundKeyItem> compoundKeyItems = new LinkedList<>(ck.getKeyItemsOrdered());
		            for( CompoundKeyItem cki : compoundKeyItems )
		            {
		                if ( !first )
		                	variableIds.append(",");
		                
		                variableIds.append(cki.getVariable().getId());
		                first = false;
		            }
            	}
            }
            refVarNode.addText(variableIds.toString());
            
            Element primaryVarNode = refNode.addElement("PrimaryVariableIds");
            variableIds = new StringBuilder();
            first = true;
            if (refdc.getPrimaryVariable() != null)
            {
            	variableIds.append(refdc.getPrimaryVariable().getId());
            }
            else
            {
            	CompoundKey ck = refdc.getPrimaryCompoundKey();
            	if (ck != null)
            	{
                    List<CompoundKeyItem> compoundKeyItems = new LinkedList<>(ck.getKeyItemsOrdered());
		            for( CompoundKeyItem cki : compoundKeyItems )
		            {
		                if ( !first )
		                	variableIds.append(",");
		                
		                variableIds.append(cki.getVariable().getId());
		                first = false;
		            }
            	}
            }
            primaryVarNode.addText(variableIds.toString());
        }
    }

    private void createLookupTables( Document doc, Element parent )
    {
        List<Document> documents = new ArrayList<>();
        documents.add(doc);
        List<LookupTableInstance> lookupTableInstances = LookupTable.findAllForDocumentFiltered(documents, "", -1);

        if (lookupTableInstances.isEmpty())
        	return;
        
		Collections.sort(lookupTableInstances, new Comparator<>() {
            @Override
            public int compare(LookupTableInstance arg0, LookupTableInstance arg1) {
                return Long.valueOf(arg0.getModel().getId()).compareTo(Long.valueOf(arg1.getModel().getId()));
            }
        });
        
        Element lookupTablesNode = parent.addElement("LookupTables");
        for( LookupTableInstance lti : lookupTableInstances )
        {
        	Element lookupTableNode = lookupTablesNode.addElement("LookupTable");
        	lookupTableNode.addAttribute("id", toString(lti.getModel().getId()));
        	lookupTableNode.addAttribute("guid", lti.getModel().getGuid());
        	lookupTableNode.addAttribute("dna",  lti.getModel().getDna());

    		Element versionElement = lookupTableNode.addElement("Version");

    		versionElement.addAttribute("id", toString(lti.getId()));
    		versionElement.addAttribute("guid", lti.getGuid());
    		versionElement.addAttribute("status", lti.getVersionInfo().getStatus().getLocaledString());
    		versionElement.addAttribute("filerefid", toString(lti.getLookupFile().getId()));
    		versionElement.addAttribute("delimiter", lti.getDelimiter());
    		versionElement.addAttribute("encoding", lti.getInputCharacterEncodingString());
    		versionElement.addAttribute("characterencoding", toString(lti.getInputCharacterEncoding()));
    		versionElement.addAttribute("fullyvisible", lti.isFullyVisible()?"true":"false");
        	
    		Element nameElement = versionElement.addElement("Name");
    		nameElement.addText(lti.getName());
    		
        	DataSource ds = lti.getDataSource();
        	if(ds != null) {
                Element dsNode = lookupTableNode.addElement("DataSource");
                dsNode.addAttribute( "id", toString(ds.getId()) );
                dsNode.addAttribute( "name", ds.getName() );
                
                int sourceTypeId = ds.getSourceType().getId();
                if (sourceTypeId == SourceType.TYPE_LOOKUP_TABLE)
                	dsNode.addAttribute( "type", "LookupTable");
                else if (sourceTypeId == SourceType.TYPE_REFERENCE_CONNECTED)
                	dsNode.addAttribute( "type", "ReferenceConnected");
                else
                	dsNode.addAttribute( "type", sourceTypeId == SourceType.TYPE_PRIMARY ? "Driver" : "Reference");
                
                dsNode.addAttribute( "guid", ds.getGuid() );
                dsNode.addAttribute( "dna",  ds.getDna() );
                
                if(ds.isXML())
                {
                	XmlLayoutUtil.generateXmllayout(dsNode, ds);
                } 
                else 
                {
    	            dsNode.addElement("Encoding").addText(ds.getEncodingTypeName());
    	            Element recLayNode = dsNode.addElement("RecordLayout");
    	            recLayNode.addAttribute( "type", ds.getRecordTypeExportString() );
    	            recLayNode.addAttribute( "end", "CRLF" );
    	            if ( ds.isDelimited() )
    	                recLayNode.addAttribute( "delimitedby", ds.getDelimeter() );
    	            
    	            createLayoutSpecificData( ds.getRecordType(), ds, recLayNode, dsNode );
    	            
    	            Element recListNode = recLayNode.addElement("RecordList");
                    List<DataRecord> dataRecords = new LinkedList<>(ds.getDataRecords());
    	            Collections.sort(dataRecords, new DataRecordIdComparator());
    	            for( DataRecord rec : dataRecords )
    	            {
    	                createDataRecord( rec, ds.getRecordType(), recListNode, ds.isColumnar() );
    	            }
                }
        	}
        }
    }
    
    private String toString (boolean value )
    {
        if ( value )
            return "true";
        return "false";                 
    }

	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}

	private void updateProgressIndicator(StatusPollingBackgroundTask statusPollingBackgroundTask, int progressInPercent) {
		if (exportOptions.isUpdateProgressIndicator()) {
			statusPollingBackgroundTask.setProgressInPercentInThread(progressInPercent);
		}
	}
}
