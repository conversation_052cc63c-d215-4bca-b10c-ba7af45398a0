package com.prinova.messagepoint.platform.services.export;

import java.util.Collections;
import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.JobPackerUtil;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

public class ExportJobPerformanceReportToXMLService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportJobPerformanceReportToXMLService";
    
    public static final String EXTERNAL_EVENT_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm";
    private static final Log log = LogUtil.getLog(ExportJobPerformanceReportToXMLService.class);
    private User requestor = null;
	private ExportImportOptions exportOptions;
		
	@Override
	public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            ExportJobPerformanceReportToXMLServiceRequest request = (ExportJobPerformanceReportToXMLServiceRequest) context.getRequest();
            
            requestor = request.getRequestor();
            exportOptions = request.getExportOptions();
            			
            org.dom4j.Document xmlDoc = DocumentHelper.createDocument();
            xmlDoc.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
            generateXML(xmlDoc, request.getStatusPollingBackgroundTask());
            
	    	Node instance = Node.getCurrentNode();
	    	String exportName = "";
            if (instance != null)
            	exportName = instance.getName();
            
            String filePath = ExportUtil.saveMessagepointObjectExportXMLToFile(xmlDoc, 
            		requestor, 
            		ExportUtil.ExportType.JOBPERFORMANCEREPORT, 
            		request.getExportId(),
            		exportName);

            ((ExportDocumentToXMLServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
            log.error(" unexpected exception when invoking ExportJobPerformanceReportToXMLService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateXML(org.dom4j.Document xmlDoc, StatusPollingBackgroundTask statusPollingBackgroundTask ) 
    {
        Element exportJobPerformanceReportElement= xmlDoc.addElement("ExportJobPerformanceReport");
        ExportXMLUtils.createVersion(exportJobPerformanceReportElement);
        statusPollingBackgroundTask.setProgressInPercentInThread(5);
        
		createMetaDataTag(exportJobPerformanceReportElement);
		Element jobsElement = exportJobPerformanceReportElement.addElement("Jobs");
		
		List<DeliveryEvent> deliveryEvents = exportOptions.getExportObjectId() > 0 ?
                Collections.singletonList(DeliveryEvent.findByJob(exportOptions.getExportObjectId())) :
				DeliveryEvent.findEvents(exportOptions.getFromDate(), exportOptions.getToDate(), exportOptions.getTypes(), exportOptions.getStatuses());
		int deliveryEventsNum = deliveryEvents.size();
		jobsElement.addAttribute("num", Long.toString(deliveryEventsNum));
        statusPollingBackgroundTask.setProgressInPercentInThread(10);  
        for (DeliveryEvent deliveryEvent : deliveryEvents) {
        	Job job = deliveryEvent.getJob();
        	if (job != null) {
	        	Element jobElement = jobsElement.addElement("Job");
	        	jobElement.add(DocumentHelper.createElement("Id").addText(Long.toString(job.getId())));
	        	jobElement.add(DocumentHelper.createElement("Created").addText(DateUtil.formatDateForXMLOutput(deliveryEvent.getCreated())));
	        	String jobTypeStr =  JobPackerUtil.getJobTypeString(deliveryEvent);
	        	if ( deliveryEvent.getItem() instanceof CommunicationProof ) {
	        		CommunicationProof cp = (CommunicationProof)deliveryEvent.getItem();
	        		if ( cp.getIsPreProof() ) {
						jobTypeStr += " (" +  ApplicationUtil.getMessage("page.label.pre.proof") + ")";
	        		}
				}
	        	jobElement.add(DocumentHelper.createElement("Type").addText(jobTypeStr));
	        	jobElement.add(DocumentHelper.createElement("Status").addText(deliveryEvent.getStatusDisplayString()));
	        	
	        	Element executionTimesElement = jobElement.addElement("ExecutionTimes");
	        	buildExecutionTimesElm(executionTimesElement, deliveryEvent);
        	}
        }
        statusPollingBackgroundTask.setProgressInPercentInThread(90);
    }
    
	private void createMetaDataTag(Element export) {
		Element reportRequestInfoElement = export.addElement("ReportRequestInfo");
		Element userElement = reportRequestInfoElement.addElement("User");
		userElement.addText(requestor.getFullName());
		Element requestDateElement = reportRequestInfoElement.addElement("RequestDate");
		requestDateElement.addText(DateUtil.formatDateForXMLOutput(DateUtil.now()));
		Element podElement = reportRequestInfoElement.addElement("Pod");
		podElement.addText(MessagepointMultiTenantConnectionProvider.getPodMasterCode());
		if(exportOptions.getExportObjectId() > 0){
			reportRequestInfoElement.add(DocumentHelper.createElement("JobId").addText(String.valueOf(exportOptions.getExportObjectId())));
		}
		// - Build the "FromDate" element
		if(exportOptions.getFromDate() != null){
			reportRequestInfoElement.add(DocumentHelper.createElement("FromDate").addText(DateUtil.formatDateForXMLOutput(exportOptions.getFromDate())));
		}
		if(exportOptions.getToDate() != null){
			reportRequestInfoElement.add(DocumentHelper.createElement("ToDate").addText(DateUtil.formatDateForXMLOutput(exportOptions.getToDate())));
		}
		// - Build the "ToDate" element

		// - Build the "Types" element
		reportRequestInfoElement.add(DocumentHelper.createElement("Types").addText(exportOptions.getTypesStr()));
		// - Build the "Statuses" element
		reportRequestInfoElement.add(DocumentHelper.createElement("Statuses").addText(exportOptions.getStatusesStr()));
	}
    
	private void buildExecutionTimesElm(Element executionTimesElement, DeliveryEvent deliveryEvent) {
		
		long waitingInQueueTime = (deliveryEvent.getWaitingInQueueTime() != null) ? deliveryEvent.getWaitingInQueueTime() : -1;
		long bundlingTime = (deliveryEvent.getBundlingTime() != null) ? deliveryEvent.getBundlingTime() : -1;
		long preprocessingTime = (deliveryEvent.getPreprocessingTime() != null) ? deliveryEvent.getPreprocessingTime() : -1; 
		long sendingTime = (deliveryEvent.getSendingTime() != null) ? deliveryEvent.getSendingTime() : -1; 

		long dewsWaitingInQueueTime = (deliveryEvent.getDewsWaitingInQueueTime() != null) ? deliveryEvent.getDewsWaitingInQueueTime() : -1;
		long dewsProcessingTime = (deliveryEvent.getDewsProcessingTime() != null) ? deliveryEvent.getDewsProcessingTime() : -1;
		long dewsQualificationEngineTime = (deliveryEvent.getDewsQualificationEngineTime() != null) ? deliveryEvent.getDewsQualificationEngineTime() : -1;
		long dewsConnectorTime = (deliveryEvent.getDewsConnectorTime() != null) ? deliveryEvent.getDewsConnectorTime() : -1;
		long dewsQepostTime = (deliveryEvent.getDewsQepostTime() != null) ? deliveryEvent.getDewsQepostTime() : -1;
		long dewsCompletedTime = (deliveryEvent.getDewsCompletedTime() != null) ? deliveryEvent.getDewsCompletedTime() : -1;
			
		long waitingForResultTime  = (deliveryEvent.getWaitingForResultTime() != null) ? deliveryEvent.getWaitingForResultTime() : -1; 
		long receivingResultTime  = (deliveryEvent.getReceivingResultTime() != null) ? deliveryEvent.getReceivingResultTime() : -1; 
		long processingResultTime = (deliveryEvent.getProcessingResultTime() != null) ? deliveryEvent.getProcessingResultTime() : -1; 
		long processingReportTime = (deliveryEvent.getProcessingReportTime() != null) ? deliveryEvent.getProcessingReportTime() : -1;
		long postProcessingTime = (deliveryEvent.getPostProcessingTime() != null) ? deliveryEvent.getPostProcessingTime() : -1;
		long completedTime = (deliveryEvent.getCompletedTime() != null) ? deliveryEvent.getCompletedTime() : -1;
		
		executionTimesElement.add(DocumentHelper.createElement("WaitingInQueueTime").addText(waitingInQueueTime != -1 ? Long.toString(waitingInQueueTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("BundlingTime").addText(bundlingTime != -1 ? Long.toString(bundlingTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("PreprocessingTime").addText(preprocessingTime != -1 ? Long.toString(preprocessingTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("SendingTime").addText(sendingTime != -1 ? Long.toString(sendingTime) : ""));
		
		executionTimesElement.add(DocumentHelper.createElement("DewsWaitingInQueueTime").addText(dewsWaitingInQueueTime != -1 ? Long.toString(dewsWaitingInQueueTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("DewsProcessingTime").addText(dewsProcessingTime != -1 ? Long.toString(dewsProcessingTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("DewsQualificationEngineTime").addText(dewsQualificationEngineTime != -1 ? Long.toString(dewsQualificationEngineTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("DewsConnectorTime").addText(dewsConnectorTime != -1 ? Long.toString(dewsConnectorTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("DewsQepostTime").addText(dewsQepostTime != -1 ? Long.toString(dewsQepostTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("DewsCompletedTime").addText(dewsCompletedTime != -1 ? Long.toString(dewsCompletedTime) : ""));		
		
		executionTimesElement.add(DocumentHelper.createElement("WaitingForResultTime").addText(waitingForResultTime != -1 ? Long.toString(waitingForResultTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("ReceivingResultTime").addText(receivingResultTime != -1 ? Long.toString(receivingResultTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("ProcessingResultTime").addText(processingResultTime != -1 ? Long.toString(processingResultTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("ProcessingReportTime").addText(processingReportTime != -1 ? Long.toString(processingReportTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("PostProcessingTime").addText(postProcessingTime != -1 ? Long.toString(postProcessingTime) : ""));
		executionTimesElement.add(DocumentHelper.createElement("CompletedTime").addText(completedTime != -1 ? Long.toString(completedTime) : ""));
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContext(String exportId, ExportImportOptions exportOptions, User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportJobPerformanceReportToXMLServiceRequest request = new ExportJobPerformanceReportToXMLServiceRequest(exportId, exportOptions, requestor, statusPollingBackgroundTask);

        context.setRequest(request);

        ExportDocumentToXMLServiceResponse serviceResp = new ExportDocumentToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

}
