package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dom4j.Element;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagepointException;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;

 
public class ExportMessagesOnApprovalToExcelService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportMessagesOnApprovalToExcelService";
    
    private static final Log log = LogUtil.getLog(ExportMessagesOnApprovalToExcelService.class);
    private User requestor = null;
	private boolean includeAllMessages = true;
	private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportMessagepointObjectToXMLServiceRequest request = (ExportMessagepointObjectToXMLServiceRequest) context.getRequest();
            
            Document document = Document.findById(request.getTargeObjectId());
            this.requestor = request.getRequestor();
            this.includeAllMessages = request.getExportOptions().isIncludeMessages();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            //outputDirectory.mkdirs();
    	    
            File file = new File(outputDirectory + File.separator + ExportUtil.EXCEL_EXPORT_TEMPLATE_MESSAGES_APPROVAL);
            FileInputStream fileStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(fileStream);
            
            generateWorkbook(document, workbook, requestor);
          
            String exportName = document.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook, 
            		requestor, 
            		ExportUtil.ExportType.MESSAGESWF, 
            		request.getExportId(),
            		exportName);

            ((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
        	if (ex.getCause() != null)
            {
            	if (ex.getCause().getMessage() != null)
            	{
            		if (ex.getCause().getMessage().equalsIgnoreCase("#RequiredValuesAreEmpty#"))
            		{
                        this.getResponse(context).addErrorMessage(
                        		"error.message.content.library.imagefile.not.compatible",
                        		"error.message.content.library.imagefile.not.compatible",
                        		new String[] {ex.getCause().getMessage()}, "", null);
                        this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                        return;
            		}
            	}
            }
            log.error(" unexpected exception when invoking ExportMessagesOnApprovalToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void generateWorkbook( Document document, XSSFWorkbook workbook, User requestor ) throws Exception 
    {
    	XSSFSheet sheet = workbook.getSheet("messages");
    	
    	List<MessagepointLocale> languages = document.getTouchpointLanguagesAsLocales();
        List<String> languageCodes = new ArrayList<>();
        
        for (MessagepointLocale locale : languages) 
		{
        	languageCodes.add(locale.getLanguageCode());
		}
        
        /*
         *  Possible Zone List zones Sheet 
         */
        List<Zone> zoneList = new ArrayList<>();
        for(Zone zone:Zone.findByDocumentIdOrderByName(document.getId())){
        	if(zone.isEnabled()){
        		zoneList.add(zone);
        	}
        }
        if(zoneList.isEmpty()){
        	// need to display error message
        	throw new MessagepointException("Zones", new Throwable("#RequiredValuesAreEmpty#"));
        }
        		
        /*
         *  Main message list sheet
         */
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        
        Row domainRow = sheet.getRow(4);
        domainRow.createCell(1).setCellValue(Node.getCurrentBranchName());
        Row instanceRow = sheet.getRow(5);
        instanceRow.createCell(1).setCellValue(Node.getCurrentNodeName());
        
        Row nameRow = sheet.getRow(6);
        nameRow.createCell(1).setCellValue(document.getName());
        Row guidRow = sheet.getRow(7);
        guidRow.createCell(1).setCellValue(document.getGuid());
        
        
        int rowCounter = 10;
        //int headerRowNum = 9;
        
        List<Long> msgInstIds = new ArrayList<>();
        msgInstIds = null; // TODO ContentObject.findAllVisibleIdsOfMostRecentCopiesWUser(getZoneIds(zoneList), null);

        if (msgInstIds != null && !msgInstIds.isEmpty())
		{
			for (Long contentObjectId : msgInstIds)
			{
				ContentObject msg = ContentObject.findById(contentObjectId);
//				if(msg.getOwningTouchpointSelection() != null )
//					continue;
				if(msg.getState() != null && msg.getState() == WorkflowState.findById(WorkflowState.STATE_WAITING_APPROVAL))
					rowCounter = getRowsForMessage(sheet, rowCounter, document, msg, languages);
		    }
		}
    }
    
	private void generateChildPgtnRow(XSSFSheet sheet, int rowCounter, Document document, ContentObject msgInst) throws Exception {
		
		Row dataRow = sheet.createRow(rowCounter);

		/** TODO
		if(msgInst != null){
			ConfigurableWorkflowAction wfa = msgInst.getWorkflowAction();
			dataRow.createCell(0).setCellValue(msgInst.getId());
	        dataRow.createCell(1).setCellValue(msgInst.getName());
			dataRow.createCell(2).setCellValue(msgInst.getAssignedToUserName());
			String zoneName = "";
			if ( !msgInst.getModel().getIsTouchpointLocal() || msgInst.isDeliveredToPlaceholder() ) {
				List<Zone> zones = new ArrayList<Zone>(msgInst.getZones());
				Collections.sort(zones, new ZoneComparator());
				Zone firstDelZone = zones.get(0);
				
				if(zones.size() > 1) {	// Multi-delivery
					zoneName = TxtFmtTag.maxTxtLengh(firstDelZone.getFriendlyName(), 20) + "(*)";
				} else {
					zoneName = firstDelZone.getFriendlyName();
				}
			}
			dataRow.createCell(3).setCellValue(zoneName);
			dataRow.createCell(4).setCellValue(DateUtil.formatDate(wfa.getReleaseForApprovalDate()));
			dataRow.createCell(5).setCellValue(DateUtil.daysBetween(wfa.getReleaseForApprovalDate(), DateUtil.now()) + " day(s)");
			dataRow.createCell(6).setCellValue(wfa.getConfigurableWorkflowStep() != null?wfa.getConfigurableWorkflowStep().getState():"");
		}
		 **/
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
    
    public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
	public static void createDataValueTag(
	        ParameterGroupInstanceCollection pgiCollection,
	        Element dataValuesElement)
	throws Exception 
	{
		Element dataValueElement = dataValuesElement.addElement("DataValue");
		dataValueElement.addAttribute("id", Long.valueOf(pgiCollection.getId()).toString());
		
		List<String> pgiPathValues = new ArrayList<>();
		createValueTag(pgiCollection.getId(), dataValueElement, pgiPathValues, 1, false);
		if(dataValueElement.getText().isEmpty()){
			StringBuilder valueString = new StringBuilder();
			for (String pathValue : pgiPathValues) {
				if (!valueString.isEmpty()) {
					valueString.append(";");
				}
				valueString.append(pathValue);
			}
		}
	}
	
	private static boolean createValueTag(
	        long pgiCollectionId,
	        Element dataValueElement,
	        List<String> pgiPathValues,
	        int level,
	        boolean secondValueTag)
	throws Exception
	{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {
                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					secondValueTag = createValueTag(pgiCollectionId, dataValueElement, pathValues, level + 1, secondValueTag);
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				Element valueElement = dataValueElement.addElement("Value");				
				StringBuilder valueString = new StringBuilder();
				
				if (secondValueTag)
					valueString = new StringBuilder("|");
				else
					secondValueTag = true;
				
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if ((valueString.length() > 0) && !valueString.toString().equals("|")) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				valueElement.addText(valueString.toString());
			}
		}
		return secondValueTag;
	}
	
	public static String createValueCell(ParameterGroupInstanceCollection pgiCollection)
	throws Exception 
	{
		List<String> pgiPathValues = new ArrayList<>();
		String returnValues = "";
		returnValues = createValue(returnValues, pgiCollection.getId(), pgiPathValues, 1);
		return returnValues;
	}
	
	private static String createValue(String returnValue, long pgiCollectionId, List<String> pgiPathValues, int level)throws Exception{
		List<String> IthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pgiPathValues, level);
		boolean createValueTag = false;
		List<String> valueTagValues = new ArrayList<>();
		StringBuilder valueString = new StringBuilder();
		
		if (IthItemValues != null && !IthItemValues.isEmpty()) {
			for (String IthItemValue : IthItemValues) {

                List<String> pathValues = new ArrayList<>(pgiPathValues);
				pathValues.add(IthItemValue);
				List<String> childIthItemValues = ParameterGroupInstanceCollection.findDistincIthItemValue(pgiCollectionId, pathValues, level + 1);
				if (childIthItemValues != null && !childIthItemValues.isEmpty() && childIthItemValues.get(0) != null) {
					childIthItemValues.add(createValue(returnValue, pgiCollectionId, pathValues, level + 1));
				} else {
					createValueTag = true;
					valueTagValues.add(IthItemValue);
				}
			}
			if (createValueTag) {
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
				if (valueTagValues != null && !valueTagValues.isEmpty()) {
					if (valueString.length() > 0) {
						valueString.append(";");
					}
					for (String valueTagValue : valueTagValues) {
						valueString.append(valueTagValue).append(",");
					}
					if (valueString.toString().endsWith(",")) {
						valueString = new StringBuilder(valueString.substring(0, valueString.length() - 1));
					}
				}
				//valueCell.setCellValue(valueString);
				
				//pgiPathValues.add(valueString);
			}
			else{
				if (pgiPathValues != null && !pgiPathValues.isEmpty()) {
					for (String pathValue : pgiPathValues) {
						if (valueString.length() > 0) {
							valueString.append(";");
						}
						valueString.append(pathValue);
					}
				}
			}
		}
		returnValue = returnValue.concat(valueString.toString());
		return returnValue;
	}
	
	public int getRowsForMessage(XSSFSheet sheet, int rowCounter,
			Document document, ContentObject msg,
			List<MessagepointLocale> languages) throws Exception {
		generateChildPgtnRow(sheet, rowCounter, document, msg);
		rowCounter++;
		
		
			
		return rowCounter;
	}
	
	private List<Long> getZoneIds(List<Zone> zoneList){		
		List<Long> zoneIds = new ArrayList<>();
		 
		for(Zone zone : zoneList){
			zoneIds.add(zone.getId());
		}
		return zoneIds;
	}

	public boolean isIncludeAllMessages() {
		return includeAllMessages;
	}

	public void setIncludeAllMessages(boolean includeAllMessages) {
		this.includeAllMessages = includeAllMessages;
	}
}
