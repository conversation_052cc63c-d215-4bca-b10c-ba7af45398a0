package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.insert.InsertScheduleBinAssignment;
import com.prinova.messagepoint.model.insert.RateSchedule;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.model.insert.RateScheduleDetail;
import com.prinova.messagepoint.model.insert.WeightUnit;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;

public class ExportInsertScheduleToXMLService extends AbstractService {
	
	public static final String SERVICE_NAME = "export.ExportInsertScheduleToXMLService";
	private static final Log log = LogUtil.getLog(ExportInsertScheduleToXMLService.class);

	private Set<Insert> inserts = new HashSet<>();
	private Set<InsertSchedule> insertSchedules = new HashSet<>();
	private Set<RateSchedule> rateSchedules = new HashSet<>();
	private Set<ParameterGroupInstanceCollection> pgiCollections = new HashSet<>();
	private WeightUnit defaultWeightUnit = null; 
	private boolean includeTargeting = false;
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			ExportInsertScheduleToXMLServiceRequest request = (ExportInsertScheduleToXMLServiceRequest)context.getRequest();
			
			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			generateXML(request, document);
			String filePath = ExportUtil.saveXMLToFile(document, request.getRequestor(), AuditReportType.ID_INSERT_AUDIT_REPORT, 0);
			
			((ExportToXMLServiceResponse)context.getResponse()).setFilePath(filePath);
			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ExportInsertScheduleToXMLService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	private org.dom4j.Document initializeWriter() throws Exception {
		return DocumentHelper.createDocument();
	}

	private void generateXML(ExportInsertScheduleToXMLServiceRequest request, org.dom4j.Document document) throws Exception {
		inserts.clear();
		insertSchedules.clear();
		rateSchedules.clear();
		pgiCollections.clear();
		includeTargeting = request.getIncludeInsertTargeting();
		
		int defaultWeightUnitId = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Insert.KEY_WeightUnits)).intValue();
		defaultWeightUnit = new WeightUnit(defaultWeightUnitId);

		// Loading the MessageInstances from database because this Service is run within a new Thread, so the hibernate session is disconnected.
		insertSchedules.addAll(request.getInsertScheduleList());
		List<InsertSchedule> dbInsertScheduleList = new ArrayList<>();
		for (InsertSchedule insertSchedule : insertSchedules) {
			InsertSchedule dbInsertSchedule = InsertSchedule.findById(insertSchedule.getId());
			dbInsertScheduleList.add(dbInsertSchedule);
		}
		
		insertSchedules.clear();
		insertSchedules.addAll(dbInsertScheduleList);
		
		Element insertScheduleExportElement = document.addElement("InsertScheduleExport");
		insertScheduleExportElement.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		insertScheduleExportElement.addAttribute("type", "audit");
		createMetaDataTag(request, insertScheduleExportElement);

		for (InsertSchedule insertSchedule : insertSchedules) {
			for (InsertScheduleBinAssignment binAssignment : insertSchedule.getBinAssignmentsSortedOnBinNo()) {
				Insert insert = binAssignment.getInsert();
				if (insert != null) {
					if (!inserts.contains(insert)) {
						inserts.add(insert);
					}
				}
			}
			for (Long threshold : insertSchedule.getInsertRateSchedules().keySet()) {
				RateScheduleCollection rateScheduleCollection = insertSchedule.getInsertRateSchedules().get(threshold);
				if (rateScheduleCollection != null) {
					RateSchedule rateSchedule = rateScheduleCollection.getRateScheduleForDate(request.getStartDate());
					if (rateSchedule != null) {
						if (!rateSchedules.contains(rateSchedule)) {
							rateSchedules.add(rateSchedule);
						}
					}
				}
			}
			if (insertSchedule.getParameterGroupInstanceCollection() != null) {
				pgiCollections.add(insertSchedule.getParameterGroupInstanceCollection());
			}
		}

		Element referenceDataElement = insertScheduleExportElement.addElement("ReferenceData");
		
		if (!pgiCollections.isEmpty()) {
			Element dataValuesElement = referenceDataElement.addElement("DataValues");
			for (ParameterGroupInstanceCollection pgiCollection : pgiCollections) {
				ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
			}
		}		

		if (!inserts.isEmpty()) {
			List<InsertSchedule> schedules = Insert.getInsertSchedWhereUsed(inserts);
			if (schedules != null) {
				schedules.removeAll(insertSchedules);
				if (!schedules.isEmpty()) {
					Element unexportedInsertSchedulesElement = referenceDataElement.addElement("UnexportedInsertSchedules");
					for (InsertSchedule insertSchedule : schedules) {
						Element unexportedInsertScheduleElement = unexportedInsertSchedulesElement.addElement("UnexportedInsertSchedule");
						unexportedInsertScheduleElement.addAttribute("id", Long.valueOf(insertSchedule.getId()).toString());
						unexportedInsertScheduleElement.addAttribute("name", insertSchedule.getName());
					}
				}
			}
			
			Element insertsElement = referenceDataElement.addElement("Inserts");
			for (Insert insert : inserts) {
				createInsertTag(insert, insertsElement);
			}
		}		

		if (!rateSchedules.isEmpty()) {
			Element rateSheetsElement = referenceDataElement.addElement("RateSheets");
			for (RateSchedule rateSchedule : rateSchedules) {
				createRateSheetTag(rateSchedule, rateSheetsElement);
			}
		}		

		if (!insertSchedules.isEmpty()) {
			Element insertSchedulesElement = insertScheduleExportElement.addElement("InsertSchedules");

            List<InsertSchedule> sortedInsertSchedule = new ArrayList<>(insertSchedules);
			// Sort the insert schedule list
			Collections.sort(sortedInsertSchedule, new Comparator<>() {
                public int compare(InsertSchedule o1, InsertSchedule o2) {
                    return o1.getName().compareTo(o2.getName());
                }
            });
			
			for (InsertSchedule insertSchedule : sortedInsertSchedule) {
				createInsertScheduleTag(insertSchedule, request.getStartDate(), insertSchedulesElement);
			}
		}		
		
	}

	private void createMetaDataTag(ExportInsertScheduleToXMLServiceRequest request, Element messageExportElement) throws Exception {
		Element metadataElement = messageExportElement.addElement("Metadata");
		Element userElement = metadataElement.addElement("User");
		userElement.addText(request.getRequestor().getFullName());
		Element requestDateElement = metadataElement.addElement("RequestDate");
		requestDateElement.addText(DateUtil.formatDateForXMLOutput(DateUtil.now()));
		Element settingsElement = metadataElement.addElement("Settings");
		settingsElement.addAttribute("targeting", Boolean.valueOf(includeTargeting).toString());
		if (request.getStartDate() != null) {
			settingsElement.addAttribute("from", DateUtil.formatDateForXMLOutput(request.getStartDate()));
		}
	}
	
	private void createInsertTag(Insert insert, Element insertsElement) throws Exception {
		Element insertElement = insertsElement.addElement("Insert");
		insertElement.addAttribute("id", Long.valueOf(insert.getId()).toString());

		Element insertStockElement = insertElement.addElement("ExternalId");
		insertStockElement.addText(insert.getStockId()==null?"":insert.getStockId());
		
		Element insertNameElement = insertElement.addElement("Name");
		insertNameElement.addText(insert.getName());
		
		Element insertDescriptionElement = insertElement.addElement("Description");
		insertDescriptionElement.addText(insert.getDescription()==null?"":insert.getDescription());
		
		Element insertDeliveryElement = insertElement.addElement("Delivery");
		insertDeliveryElement.addText(insert.getDeliveryType().getDisplayText());
		
		Element insertStatusElement = insertElement.addElement("Status");
		insertStatusElement.addText(insert.getStatusDisplay());
		
		Element insertWeightElement = insertElement.addElement("Weight");
		insertWeightElement.addAttribute("unit", defaultWeightUnit.getDisplayText());
		insertWeightElement.addText(insert.getDehydratedWeight());
		
		if (insert.getDocuments() != null && !insert.getDocuments().isEmpty()) {
			Element insertTouchpointsElement = insertElement.addElement("Touchpoints");
			for (Document document : insert.getDocuments()) {
				Element insertTouchpointElement = insertTouchpointsElement.addElement("Touchpoint");
				insertTouchpointElement.addAttribute("id", Long.valueOf(document.getId()).toString());
				Element insertTouchpointNameElement = insertTouchpointElement.addElement("Name");
				insertTouchpointNameElement.addText(document.getName());
			}
		}
		
		if (includeTargeting) {
			if (insert.getTargetGroupCount() > 0) {
				Element targetCriteriaElement = insertElement.addElement("TargetCriteria");
				if (insert.getExcludedTargetGroups() != null && !insert.getExcludedTargetGroups().isEmpty()) {
					Element exclusionsElement = targetCriteriaElement.addElement("Exclusions");
					exclusionsElement.addAttribute("relationship", insert.getExcludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : insert.getExcludedTargetGroups()) {
						Element targetGroupElement = exclusionsElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(insert));
					}
				}
				if (insert.getIncludedTargetGroups() != null && !insert.getIncludedTargetGroups().isEmpty()) {
					Element inclusionsGroupAElement = targetCriteriaElement.addElement("InclusionsGroupA");
					inclusionsGroupAElement.addAttribute("relationship", insert.getIncludedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : insert.getIncludedTargetGroups()) {
						Element targetGroupElement = inclusionsGroupAElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(insert));
					}
				}
				if (insert.getExtendedTargetGroups() != null && !insert.getExtendedTargetGroups().isEmpty()) {
					Element inclusionsGroupBElement = targetCriteriaElement.addElement("InclusionsGroupB");
					inclusionsGroupBElement.addAttribute("relationship", insert.getExtendedTargetGroupRelationship()==Targetable.RELATION_ALLOF?"AND":"OR");
					for (TargetGroup targetGroup : insert.getExtendedTargetGroups()) {
						Element targetGroupElement = inclusionsGroupBElement.addElement("TargetGroup");
						targetGroupElement.addCDATA(targetGroup.getDetails(insert));
					}
				}
			}
		}
		
		List<InsertSchedule> schedules = insert.getInsertSchedWhereUsed();
		Element insertSchedulesElement = insertElement.addElement("InsertSchedules");
		if (schedules != null && !schedules.isEmpty()) {
			for (InsertSchedule insertSchedule : schedules) {
				Element insertScheduleElement = insertSchedulesElement.addElement("InsertSchedule");
				insertScheduleElement.addAttribute("refid", Long.valueOf(insertSchedule.getId()).toString());
				boolean exported = Boolean.TRUE;
				if (!this.insertSchedules.contains(insertSchedule)) {
					exported = false;
				}
				insertScheduleElement.addAttribute("exported", Boolean.toString(exported));
				InsertScheduleBinAssignment binAssignment = InsertScheduleBinAssignment.findByInsertScheduleAndInsert(insertSchedule.getId(), insert.getId()); 
				if (binAssignment != null) {
					if (binAssignment.getStartDate() != null) {
						insertScheduleElement.addAttribute("from", binAssignment.getStartDate().toString());
					}
					if (binAssignment.getEndDate() != null) {
						insertScheduleElement.addAttribute("to", binAssignment.getEndDate().toString());
					}
				}
			}
		}
	}
	
	private void createRateSheetTag(RateSchedule rateSchedule, Element rateSheetsElement) throws Exception {
		Element rateSheetElement = rateSheetsElement.addElement("RateSheet");
		rateSheetElement.addAttribute("id", Long.valueOf(rateSchedule.getId()).toString());
		
		Element rateScheduleNameElement = rateSheetElement.addElement("Name");
		rateScheduleNameElement.addText(rateSchedule.getName());
		
		Element rateScheduleDescriptionElement = rateSheetElement.addElement("Description");
		rateScheduleDescriptionElement.addText(rateSchedule.getDescription()==null?"":rateSchedule.getDescription());

		if (rateSchedule.getRateScheduleCollection().getEnvelopeName() != null) {
			Element rateScheduleEnvelopeElement = rateSheetElement.addElement("Envelope");
			Element rateScheduleEnvelopeNameElement = rateScheduleEnvelopeElement.addElement("Name");
			rateScheduleEnvelopeNameElement.addText(rateSchedule.getRateScheduleCollection().getEnvelopeName());
			Element rateScheduleEnvelopeWeightElement = rateScheduleEnvelopeElement.addElement("Weight");
			rateScheduleEnvelopeWeightElement.addAttribute("unit", defaultWeightUnit.getDisplayText());
			rateScheduleEnvelopeWeightElement.addText(rateSchedule.getRateScheduleCollection().getDehydratedEnvelopWeight());
		}
		
		if (rateSchedule.getStartDate() != null) {
			Element rateScheduleStartDateElement = rateSheetElement.addElement("StartDate");
			rateScheduleStartDateElement.addText(rateSchedule.getStartDate().toString());
		}
		
		if (rateSchedule.getEndDate() != null) {
			Element rateScheduleEndDateElement = rateSheetElement.addElement("EndDate");
			rateScheduleEndDateElement.addText(rateSchedule.getEndDate().toString());
		}
		
		List<RateScheduleDetail> rateScheduleDetails = rateSchedule.getRateScheduleDetailsSorted();
		if (rateScheduleDetails != null && !rateScheduleDetails.isEmpty()) {
			Element thresholdsElement = rateSheetElement.addElement("Thresholds");
			for (RateScheduleDetail rateScheduleDetail : rateScheduleDetails) {
				Element thresholdElement = thresholdsElement.addElement("Threshold");
				thresholdElement.addAttribute("weight", rateScheduleDetail.getDehydratedWeight());
				thresholdElement.addAttribute("weightUnit", defaultWeightUnit.getDisplayText());
				thresholdElement.addAttribute("rate", rateScheduleDetail.getDehydratedRateWithoutDollarSign());
			}
		}
	}
	
	private void createInsertScheduleTag(InsertSchedule insertSchedule, Date startDate, Element insertSchedulesElement) throws Exception {
		Element insertScheduleElement = insertSchedulesElement.addElement("InsertSchedule");
		insertScheduleElement.addAttribute("id", Long.valueOf(insertSchedule.getId()).toString());
		
		Element insertScheduleExternalElement = insertScheduleElement.addElement("ExternalId");
		insertScheduleExternalElement.addText(insertSchedule.getScheduleId()==null?"":insertSchedule.getScheduleId());
		
		Element insertScheduleNameElement = insertScheduleElement.addElement("Name");
		insertScheduleNameElement.addText(insertSchedule.getName());
		
		Element insertScheduleDescriptionElement = insertScheduleElement.addElement("Description");
		insertScheduleDescriptionElement.addText(insertSchedule.getDescription()==null?"":insertSchedule.getDescription());
		
		Element insertScheduleStatusElement = insertScheduleElement.addElement("Status");
		insertScheduleStatusElement.addText(insertSchedule.getStatusDisplay());
		
		if (insertSchedule.getScheduleCollection().getDocument() != null) {
			Element insertScheduleTouchpointElement = insertScheduleElement.addElement("Touchpoint");
			insertScheduleTouchpointElement.addAttribute("id", Long.valueOf(insertSchedule.getScheduleCollection().getDocument().getId()).toString());
			Element insertScheduleTouchpointNameElement = insertScheduleTouchpointElement.addElement("Name");
			insertScheduleTouchpointNameElement.addText(insertSchedule.getScheduleCollection().getDocument().getName());
		}

		if (insertSchedule.getStartDate() != null) {
			Element rateScheduleStartDateElement = insertScheduleElement.addElement("StartDate");
			rateScheduleStartDateElement.addText(insertSchedule.getStartDate().toString());
		}
		
		if (insertSchedule.getEndDate() != null) {
			Element rateScheduleEndDateElement = insertScheduleElement.addElement("EndDate");
			rateScheduleEndDateElement.addText(insertSchedule.getEndDate().toString());
		}
		
		List<InsertScheduleBinAssignment> binAssignments = insertSchedule.getBinAssignmentsSortedOnBinNo();
		if (binAssignments != null && !binAssignments.isEmpty()) {
			Element binAssignmentsElement = insertScheduleElement.addElement("BinAssignments");
			for (InsertScheduleBinAssignment binAssignment : binAssignments) {
				Element binAssignmentElement = binAssignmentsElement.addElement("BinAssignment");
				binAssignmentElement.addAttribute("binnumber", Integer.valueOf(binAssignment.getBinNo()).toString());
				if (binAssignment.getInsert() != null) {
					binAssignmentElement.addAttribute("insertrefid", Long.valueOf(binAssignment.getInsert().getId()).toString());
					if (binAssignment.getStartDate() != null) {
						binAssignmentElement.addAttribute("from", binAssignment.getStartDate().toString());
					}
					if (binAssignment.getEndDate() != null) {
						binAssignmentElement.addAttribute("to", binAssignment.getEndDate().toString());
					}
				} else if (!binAssignment.isAvailable()) {
					binAssignmentElement.addAttribute("reserved", Boolean.TRUE.toString());
				}
			}
		}
		
		if (insertSchedule.getInsertRateSchedules() != null && !insertSchedule.getInsertRateSchedules().isEmpty()) {
			Element rateSheetsElement = insertScheduleElement.addElement("RateSheets");
			for (Long threshold : insertSchedule.getInsertRateSchedules().keySet()) {
				Element rateSheetElement = rateSheetsElement.addElement("RateSheet");
				rateSheetElement.addAttribute("numberofsheets", threshold.toString());
				RateSchedule rateSchedule = insertSchedule.getInsertRateSchedules().get(threshold).getRateScheduleForDate(startDate);
				if (rateSchedule != null) {
					rateSheetElement.addAttribute("ratesheetrefid", Long.valueOf(rateSchedule.getId()).toString());	
				}
			}
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContext(List<InsertSchedule> insertScheduleList,
														Date startDate,
														Boolean includeInsertTargeting,
														User requestor) {
		
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		ExportInsertScheduleToXMLServiceRequest request = new ExportInsertScheduleToXMLServiceRequest();
		
		request.setInsertScheduleList(insertScheduleList);
		request.setIncludeInsertTargeting(includeInsertTargeting);
		request.setStartDate(startDate);
		request.setRequestor(requestor);

		context.setRequest(request);	
		
		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}
}