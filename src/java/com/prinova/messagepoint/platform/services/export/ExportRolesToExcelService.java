package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.admin.RoleUtil;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameIgnoreCaseComparator;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;

 
public class ExportRolesToExcelService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportRolesToExcelService";
    
    private static final Log log = LogUtil.getLog(ExportRolesToExcelService.class);
    private User requestor = null;
	private boolean includeImagePathOnly = false;
	private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportMessagepointObjectToExcelServiceRequest request = (ExportMessagepointObjectToExcelServiceRequest) context.getRequest();
            
            Node node = Node.findById(request.getTargeObjectId());
            if(node == null)
            	return;
            requestor = request.getRequestor();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            //outputDirectory.mkdirs();
    	    
            File file = new File(outputDirectory + File.separator + (ExportUtil.EXCEL_EXPORT_TEMPLATE_ROLES));
            FileInputStream fileStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(fileStream);
            generateWorkbookUpdate(node, workbook, requestor);
            String exportName = node.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook, 
            		requestor, 
            		ExportUtil.ExportType.ROLE, 
            		request.getExportId(),
            		exportName,
            		request.getExportOptions());

            ((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
        	log.error(" unexpected exception when invoking ExportRolesToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }
    
    /*
     * Only existing users list generating
     */
	private void generateWorkbookUpdate( Node currentNode, XSSFWorkbook workbook, User requestor ) throws Exception 
    {
    	XSSFSheet sheet = workbook.getSheet("roles");
    	Branch branch = currentNode.getBranch();
    	/*
         *  Main branch users list sheet
         */
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        
        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        
        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(branch.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(branch.getGuid());
        
        
    	/*
         *  Possible Instance List and its role list with id for current domain
         */
    	
    	Row headerRow = sheet.getRow(8);
    	if(headerRow == null)
    		headerRow = sheet.createRow(8);
    	
    	Row topHeaderRow = sheet.getRow(7);
    	if(topHeaderRow == null)
    		topHeaderRow = sheet.createRow(7);
    	
    	int rowCounter = 9;
    	List<Node> nodeList = branch.getAllAccessibleNodes(true, requestor.canAccessTestingInstances());
    	Collections.sort(nodeList, new IdentifiableMessagepointModelNameIgnoreCaseComparator());
    	for(Node instance : nodeList){ // with DCS
    		if(instance.isDcsNode() && !requestor.isPodAdminUser())
    			continue;
        	Row instanceRow = sheet.createRow(rowCounter);
        	instanceRow.createCell(0).setCellValue(instance.getName());
        	List<Role> roleList = RoleUtil.findAllByNode(Node.getSchema(instance.getName(), branch.getName()));
        	if(roleList.isEmpty())
        		continue;
        	rowCounter++;
        	int roleRowCounter = 0;
        	int headerCellCounter = 5;
        	for(Role role:roleList){
        		Row roleRow = sheet.createRow(rowCounter++);
        			
        		roleRow.createCell(1).setCellValue(role.getName());
        		roleRow.createCell(2).setCellValue(role.getId());
        		roleRow.createCell(3).setCellValue(role.isActive()?ApplicationUtil.getMessage("page.label.active"):ApplicationUtil.getMessage("page.label.deactive"));
        		roleRow.createCell(4).setCellValue(role.getVisibility().equals("1")?ApplicationUtil.getMessage("page.label.role.visibility.shared"):ApplicationUtil.getMessage("page.label.role.visibility.private"));
        		
        		Map<String, Object> command = new HashMap<>();
        		RoleUtil.processPermissions(command, role);
        		boolean[][] selectedPermissions = (boolean[][]) command.get(RoleUtil.SELECTED_PERMISSIONS_KEY);
        		long[][] permissionValues = ((long[][]) command.get(RoleUtil.PERMISSION_VALUES_KEY));
        		String[] permissionTypes = ((String[]) command.get(RoleUtil.PERMISSION_TYPES_KEY));
        		String[] permissionCategoryNames = ((String[]) command.get(RoleUtil.PERMISSION_CATEGORY_NAMES_KEY));
        		int permColumnCounter = 5;
        		if(command != null){
        			for (int i = 0;  i < permissionCategoryNames.length;i++) {
        				for (int t = 0; t < permissionTypes.length; t++) {
        					if(roleRowCounter == 0){ // print header row for each permission
        						if(t == 0)
        							topHeaderRow.createCell(headerCellCounter).setCellValue(ApplicationUtil.getMessage(permissionCategoryNames[i]));
        						headerRow.createCell(headerCellCounter).setCellValue(ApplicationUtil.getMessage(permissionTypes[t]));
        						headerCellCounter++;
        					}
        					if(permissionValues[i][t] > 0){
        						if(selectedPermissions[i][t] == true){
        							roleRow.createCell(permColumnCounter).setCellValue("Y");
        						}else
        							roleRow.createCell(permColumnCounter).setCellValue("N");
        						
        					}else{
        						roleRow.createCell(permColumnCounter).setCellValue(" ");
        					}
        					permColumnCounter++;
	        			}
        			}
        		}
        		roleRowCounter++;
        	}
        }
    }
    
    public boolean getIncludeImagePathOnly()
	{
		return includeImagePathOnly;
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long imageId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToExcelServiceRequest request = new ExportMessagepointObjectToExcelServiceRequest(exportId, imageId, null, requestor, exportOptions);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}
