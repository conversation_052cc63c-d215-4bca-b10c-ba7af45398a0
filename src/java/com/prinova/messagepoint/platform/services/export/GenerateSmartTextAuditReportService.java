package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

public class GenerateSmartTextAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateSmartTextAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateSmartTextAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateAuditReportServiceRequest request = (GenerateAuditReportServiceRequest) context.getRequest();

			SmartTextAuditReportRunner projectExportRunner = new SmartTextAuditReportRunner(request.getAuditReportId(),
                    request.getList(),
                    request.getRequestor(),
                    request.getAuditReportTypeId());
	   		MessagePointRunnableUtil.startThread(projectExportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateSmartTextAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class SmartTextAuditReportRunner extends MessagePointRunnable {

		private long auditReportId;
		private List<Long> list = new ArrayList<>();
		private User requestor;
		private int auditReportTypeId;
		
		public SmartTextAuditReportRunner(long auditReportId, 
											List<Long> list,
											User requestor,
										  	int auditReportTypeId) {
			
			this.auditReportId = auditReportId;
			this.setList(list);
			this.requestor = requestor;
			this.auditReportTypeId = auditReportTypeId;
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			try {
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
		    	ServiceExecutionContext exportServiceContext = ExportSmartTextToXMLService.createContext(getList(), getRequestor(), getAuditReportTypeId());

		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportSmartTextToXMLService.SERVICE_NAME, ExportSmartTextToXMLService.class);
		    	exportService.execute(exportServiceContext);
		    	
		    	ServiceResponse serviceResponse = exportServiceContext.getResponse();
		    	String xmlFilePath = ((ExportToXMLServiceResponse)serviceResponse).getFilePath();
		    	if(xmlFilePath == null){
		    		// *********************************************************
					// ********* Update the AuditReport object *****************
					// *********************************************************

					ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForError(getAuditReportId());
					Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
					updateReportService.execute(updateReportServiceContext);
		    	}else{

				
					// ******************************************
					// ********* Generate XSLT ******************
					// ******************************************
	
			    	String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, getRequestor(), getAuditReportTypeId(), 0);
			    	
					// *********************************************************
					// ********* Update the AuditReport object *****************
					// *********************************************************
	
					ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(getAuditReportId(), 
																																		getAuditReportTypeId(),
																																		xmlFilePath, 
																																		xhtmlFilePath);
					Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
					updateReportService.execute(updateReportServiceContext);
		    	}
		    	
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("SmartText Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
			}
		}

		public long getAuditReportId() {
			return auditReportId;
		}

		@SuppressWarnings("unused")
		public void setAuditReportId(long auditReportId) {
			this.auditReportId = auditReportId;
		}

		public int getAuditReportTypeId() {
			return auditReportTypeId;
		}

		public void setAuditReportTypeId(int auditReportTypeId) {
			this.auditReportTypeId = auditReportTypeId;
		}

		public User getRequestor() {
			return requestor;
		}

		@SuppressWarnings("unused")
		public void setRequestor(User requestor) {
			this.requestor = requestor;
		}

		public List<Long> getList() {
			return list;
		}

		public void setList(List<Long> list) {
			this.list = list;
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(long auditReportId, List<Long> list,
			boolean includeAll, User requestor, int auditReportTypeId) {
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateAuditReportServiceRequest request = new GenerateAuditReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setRequestor(requestor);
		request.setList(list);
		request.setAuditReportTypeId(auditReportTypeId);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}