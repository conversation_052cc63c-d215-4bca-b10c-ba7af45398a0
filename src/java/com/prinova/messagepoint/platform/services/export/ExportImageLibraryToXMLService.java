package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.*;

public class ExportImageLibraryToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportImageLibraryToXMLService";
	private static final Log log = LogUtil.getLog(ExportImageLibraryToXMLService.class);

    private User requestor;
	private long reportTimestamp;
	private List<Long> list;
	private boolean includeImagePathOnly = true;
	private final List<String> originalImageFiles = new ArrayList<>();
	
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportImageLibraryToXMLServiceRequest request = (ExportImageLibraryToXMLServiceRequest) context.getRequest();
	    	requestor = request.getRequestor();
	    	reportTimestamp = request.getReportTimestamp();
	    	setList(request.getList());

			org.dom4j.Document document = initializeWriter();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			try{
				generateXML(request, document);
				String filePath = ExportUtil.saveXMLToFile(document, requestor, AuditReportType.ID_IMAGE_LIBRARY_AUDIT_REPORT, reportTimestamp);
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);
			}catch (Exception e){
				this.getResponse(context).addErrorMessage(
				        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
						context.getLocale() );
				
				((ExportToXMLServiceResponse) context.getResponse()).setFilePath(null);
			}
		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportImageLibraryToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
	}

	private org.dom4j.Document initializeWriter( )
	{
		return DocumentHelper.createDocument();
	}

	private void generateXML(ExportImageLibraryToXMLServiceRequest request,org.dom4j.Document document) throws Exception 
	{
		requestor = User.findById(requestor.getId());
		
		Element auditElm = document.addElement("ImageLibraryAudit");
		auditElm.addAttribute("ver", MessagePointStartUp.getBuildRevision());
		buildMetaDataElm(auditElm, requestor);
		
		if ( !getList().isEmpty() && !getList().isEmpty())
		{
			Long objectId = getList().get(0);
			ContentObject contentObject = ContentObject.findById(objectId);
			ContentObjectData contentObjectData = contentObject.getLatestContentObjectDataWorkingCentric();
			Element imageLibraryElement = auditElm.addElement("ImageLibrary");
			createImageLibraryTag(contentObject, imageLibraryElement);
			List<ConfigurableWorkflowActionHistory> actionHisList = ConfigurableWorkflowActionHistory.findByContentObjectId(contentObjectData.getModel().getId());
			if(!actionHisList.isEmpty()){
				Element approvalsElement = imageLibraryElement.addElement("Approvals");
				buildApprovalHistElm(approvalsElement, contentObject, actionHisList);
			}
			Element referenceDataElement = auditElm.addElement("ReferenceData");

			if (contentObject.getParameterGroupInstanceCollections() != null && !contentObject.getParameterGroupInstanceCollections().isEmpty())
			{
				Element dataValuesElement = referenceDataElement.addElement("DataValues");
				for (ParameterGroupInstanceCollection pgiCollection : contentObject.getParameterGroupInstanceCollections())
				{
					ExportUtil.createDataValueTag(pgiCollection, dataValuesElement);
				}
			}

			if (!contentObject.getDocuments().isEmpty())
			{
				Element touchpointsElement = referenceDataElement.addElement("Touchpoints");
				for (Document messageDocument : contentObject.getDocuments())
				{	
					if (messageDocument.isVisible(requestor)) 
					{
						Element touchpointElement = touchpointsElement.addElement("Touchpoint");
						touchpointElement.addAttribute("id", Long.valueOf(messageDocument.getId()).toString());
						Element touchpointNameElement = touchpointElement.addElement("Name");
						touchpointNameElement.addText(messageDocument.getName());
					}
				}
			}
		}
	}

	/**
	 * Build the "Metadata" element
     */
	private void buildMetaDataElm(Element parentElm, User requestor){
		// Build the "Metadata" element
		Element metadataElm = DocumentHelper.createElement("Metadata");
		parentElm.add(metadataElm);
		// - Build the "User" element
		metadataElm.add(DocumentHelper.createElement("User").addText(requestor.getName()));
		// - Build the "RequestDate" element
		metadataElm.add(DocumentHelper.createElement("RequestDate").addText(DateUtil.formatDateForXMLOutput(DateUtil.now())));

		Element intanceNameElement = metadataElm.addElement("InstanceName");
		intanceNameElement.addText(Node.getCurrentNodeName());

		Element systemDefaultLocaleElement = metadataElm.addElement("SystemDefaultLocale");
		systemDefaultLocaleElement.addCDATA(MessagepointLocale.getDefaultSystemLanguageLocale().getName());
		systemDefaultLocaleElement.addAttribute("code", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		metadataElm.add(DocumentHelper.createElement("ReportType").addText("Image Library Audit Report"));
	}
	
	/**
	 * Build the "ApprovalHistory" element
	 */
	private void buildApprovalHistElm(Element parentElm, ContentObject contentObject, List<ConfigurableWorkflowActionHistory> actionList){
		// Build the "ApprovalHistory" element
		Element verHisElm = parentElm.addElement("ApprovalHistory");				
		for(ConfigurableWorkflowActionHistory actionHis : actionList){
			// Build the "Descript" element
			Element descriptElm = verHisElm.addElement("Descript");
			if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
				User user = User.findById(actionHis.getUserId());
				if (user != null)
					descriptElm.addAttribute("user", user.getName());
				else
					descriptElm.addAttribute("user", "Uknown");
			}else{	// Auto approve user
				descriptElm.addAttribute("user", "System");
			}
			if(actionHis.getWorkflowAction() != null && actionHis.getWorkflowAction().getConfigurableWorkflowStep() == null){	// No step activation
				descriptElm.addAttribute("action", "Activated");
			}else if(actionHis.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL){
				descriptElm.addAttribute("action", "Release for Approval");
			}else{
				if(actionHis.getUserId() != null && actionHis.getUserId() > 0){
					descriptElm.addAttribute("action", ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}else{	// Auto approve user
					descriptElm.addAttribute("action", "Auto " + ConfigurableWorkflowActionType.getName(actionHis.getAction()));
				}
			}
			if(actionHis.getAssignedTo() != null){
				User user = User.findById(actionHis.getAssignedTo());
				if (user != null)
					descriptElm.addAttribute("assignedTo", user.getName());
			}
			if(actionHis.getNotes() != null){
				descriptElm.addAttribute("notes", actionHis.getNotes());
			}
			descriptElm.addAttribute("date", DateUtil.formatDateForXMLOutput(actionHis.getActionDate()));
			switch(actionHis.getStateCurrent()){
				case ConfigurableWorkflowActionStateType.ID_WORKING_COPY:{	// WIP
					descriptElm.setText("Working Copy");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL:{	// Active
					descriptElm.setText("Active");
					break;
				}
				case ConfigurableWorkflowActionStateType.ID_RELEASE_FOR_APPROVAL:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState());
					}
					break;
				}
				default:{
					ConfigurableWorkflowAction wfAction = actionHis.getWorkflowAction();
					if(wfAction != null && wfAction.getConfigurableWorkflowStep() != null){
						ConfigurableWorkflowStep wfStep = wfAction.getConfigurableWorkflowStep();
						if(wfStep.getApproveType() == ApprovalType.ID_APPROVAL_TYPE_ANY_OF){	// Any of
							descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
						}else{	// All of
							if(!wfAction.isActionApproved()){
								descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
							}else{
								if(actionHis.getActionType() == ConfigurableWorkflowActionHistory.ACTION_AUTO_APPROVED){	// Auto approve
									descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState());
								}else{							
									// If action is approved but the user is not the last to approve, stay in current state
									List<ConfigurableWorkflowApprovalDetail> approvalDetailsSorted = new ArrayList<>(wfAction.getApprovalDetails());
									Collections.sort(approvalDetailsSorted, new Comparator<>() {
                                        public int compare(ConfigurableWorkflowApprovalDetail o1, ConfigurableWorkflowApprovalDetail o2) {
                                            Date approvedDate1 = o1.getApprovedDate();
                                            Date approvedDate2 = o2.getApprovedDate();
                                            if (approvedDate1 == null && approvedDate2 == null) {
                                                return 0;
                                            } else if (approvedDate1 == null) {
                                                return 1;
                                            } else if (approvedDate2 == null) {
                                                return -1;
                                            } else {
                                                return o1.getApprovedDate().compareTo(o2.getApprovedDate());
                                            }
                                        }
                                    });
									if(!approvalDetailsSorted.isEmpty()){	// At least one approver
										for(int i=approvalDetailsSorted.size()-1; i>=0; i--){
											ConfigurableWorkflowApprovalDetail wfDetail = approvalDetailsSorted.get(i);
											if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.WAITING){
												continue;
											}else if(wfDetail.getApproved() == ConfigurableWorkflowApprovalDetail.AUTO_APPROVED){	// Last step is auto-approve
												descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												break;
											}else{
												if(Objects.equals(wfDetail.getUserId(), actionHis.getUserId())){
													descriptElm.setText("Pending " + wfAction.getNextAction().getConfigurableWorkflowStep().getState()); // Proceed to next state
												}else{
													descriptElm.setText("Pending " + wfAction.getConfigurableWorkflowStep().getState()); // Stay in current state
												}
												break;
											}
										}
									}
								}
							}
						}	
					}
				}
			}
		}	
	}
	
	
	public static ServiceExecutionContext createContext(
	        List<Long> list,
			User requestor)
	{
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ExportImageLibraryToXMLServiceRequest request = new ExportImageLibraryToXMLServiceRequest();
		request.setList(list);
		request.setRequestor(requestor);

		context.setRequest(request);

		ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	
	private void createImageLibraryTag(ContentObject contentObject, Element imageLibraryElement)
	{
		imageLibraryElement.addAttribute("id", Long.toString(contentObject.getId()));
		imageLibraryElement.addAttribute("guid", contentObject.getGuid());
		imageLibraryElement.addAttribute("dna", contentObject.getDna());
		TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
	
		if (tmd != null) 
		{
			imageLibraryElement.addAttribute("externalid", tmd.getExternalId().toString());
		}
		
		String type = "";
		if (contentObject.isDynamicVariantEnabled())
		{
			type = "Dynamic";
		}
		else if (!contentObject.isVariableContentEnabled())
		{
			type = "Global";
		}
		else
		{
			type = "Regular";
		}

		imageLibraryElement.addAttribute("type", type);

		String contentType = "Graphic";
		imageLibraryElement.addAttribute("contenttype", contentType);
		imageLibraryElement.addAttribute("nextaction", contentObject.getActionRequired());
		if(contentObject.getContentObjectDataWorkingCentric().isActive()){
			imageLibraryElement.addAttribute("assignedto", "N/A");
		}else{
			imageLibraryElement.addAttribute("assignedto", contentObject.getAssignedToUserName());
		}

		imageLibraryElement.addAttribute("defaultlanguage", MessagepointLocale.getDefaultSystemLanguageCode());
		imageLibraryElement.addAttribute("defaultlocale", MessagepointLocale.getDefaultSystemLanguageLocale().getCode());

		ContentObjectData wip = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
		ContentObjectData activeCopy = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
		if(wip != null)
			createILVersionTag(wip, imageLibraryElement);
		if(activeCopy != null)
			createILVersionTag(activeCopy, imageLibraryElement);
	}
	
	private void createILVersionTag(ContentObjectData contentObjectData, Element imageLibraryElement)
	{
		Element versionElement = imageLibraryElement.addElement("Version");

		versionElement.addAttribute("id", Long.toString(contentObjectData.getId()));
		versionElement.addAttribute("guid", contentObjectData.getGuid());
		versionElement.addAttribute("status", contentObjectData.getStatus().getLocaledString());

		String versionOrigin = contentObjectData.getCreationReason()!=null?contentObjectData.getCreationReason().getLocaledString():"";
		versionElement.addAttribute("origin", versionOrigin);
		versionElement.addAttribute("deliverytypeid", Long.toString(contentObjectData.getDeliveryType()));
		versionElement.addAttribute("usagetypeid", Long.toString(contentObjectData.getModel().getUsageTypeId()));
		
		Element nameElement = versionElement.addElement("Name");
		nameElement.addText(contentObjectData.getModel().getName());

		Element metatagsElement = versionElement.addElement("Metatags");
		if (contentObjectData.getMetatags() != null && !contentObjectData.getMetatags().equalsIgnoreCase(""))
		{
			metatagsElement.addText(contentObjectData.getMetatags());
		}
		
		Element descriptionElement = versionElement.addElement("Description");
		if (contentObjectData.getDescription() != null && !contentObjectData.getDescription().equalsIgnoreCase(""))
		{
			descriptionElement.addText(contentObjectData.getDescription());
		}
		
		Element commentsElement = versionElement.addElement("Comments");
        List<ContentObjectComment> comments = new ArrayList<>(contentObjectData.getComments());
		Collections.sort(comments, new CommentIdComparator());		
		for (ContentObjectComment comment : comments)
		{
			if (comment != null && comment.getComment() != null && !comment.getComment().equalsIgnoreCase(""))
			{
				Element commentElement = commentsElement.addElement("Comment");
				commentElement.addAttribute("id", Long.toString(comment.getId()));				
				commentElement.addAttribute("created", DateUtil.formatDateForXMLOutput(comment.getCreated()));				
				commentElement.addText(comment.getComment());
			}
		}
		
		contentObjectData.getOriginObject();
		if(contentObjectData.getStatus().getId() == VersionStatus.VERSION_WIP){
			Element checkoutElement = versionElement.addElement("Checkout");
			checkoutElement.addText(contentObjectData.getCreatedByName());
			checkoutElement.addAttribute("date", DateUtil.formatDateForXMLOutput(contentObjectData.getStartDate()));
		}
		Element approvalsElement = versionElement.addElement("Approvals");
		List<ConfigurableWorkflowActionHistory> msgActionHisList = ConfigurableWorkflowActionHistory.findAllByModel(ContentObjectData.findByGuid(contentObjectData.getGuid()).getModel());
		ConfigurableWorkflowActionHistory.sortByChronOrder(msgActionHisList, true);
		buildApprovalHistElm(approvalsElement, contentObjectData.getModel(), msgActionHisList);
		if (contentObjectData.getModel().isDynamicVariantEnabled())
		{
			try 
			{
				createContentTagForSelectableImageLibrary(contentObjectData, versionElement);
			} 
			catch (Exception e) 
			{
	            log.error(" unexpected exception when invoking createContentTagForSelectableContentLibrary(contentObjectData, versionElement)", e);
			}
		}
		else
		{
			Element contentsElement = versionElement.addElement("Contents");
			createContentTagForRegularImageLibrary(contentObjectData, contentsElement);
		}
	}

	private void createContentTagForRegularImageLibrary(ContentObjectData contentObjectData, Element contentsElement)
	{
		String defaultSystemLocaleCode = MessagepointLocale.getDefaultSystemLanguageLocale().getCode();

        List<MessagepointLocale> languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
		{
			String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			List<ContentObjectAssociation> msgCAs = contentObjectData.getContentObjectAssociations();
			boolean missingFlag = true;
			for( ContentObjectAssociation ca : msgCAs )
			{
			    if ( ca.getPGTreeNode() != null || ca.getMessagepointLocale().getId() != locale.getId() )
			    {
			        continue;
			    }
			    Content languageContent = ca.getContent();
				ComplexValue imageLink = null;
			    
			    Element contentElement = null;
			    missingFlag = false;
		        contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
				if (localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		        if(ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        {
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
		        }
		        else
		        {
                    if (ca.getReferencingImageLibrary() != null)
		        	{
		        		ContentObject referencedImageLibrary = ca.getReferencingImageLibrary();
						contentElement.addAttribute("imagelibraryrefid", Long.toString(ca.getReferencingImageLibrary().getId()));
						contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
		        	}
		        	else if (languageContent != null)
		        	{
						imageLink = languageContent.getImageLink();
			        	if ( languageContent.getImageLocation() != null )
			        	{
	                		if (includeImagePathOnly)
	                		{
	                			contentElement.addCDATA(languageContent.getImageName());
	                		}
	                		else
	                		{
	                			contentElement.addCDATA(ExportXMLUtils.encodeFile(languageContent.getImageLocation()));
	                		}
			        	}
			            String fileImageName = languageContent.getImageName();
			            String langImageName = languageContent.getAppliedImageFilename();
			            Date imageUploaded = languageContent.getImageUploadedDate();
			            if ( fileImageName != null )
			            {
			                contentElement.addAttribute("filename", fileImageName);
						    if (includeImagePathOnly)
						    {
							    contentElement.addAttribute("pathonly", "true");
							    originalImageFiles.add(languageContent.getImageLocation());
						    }
			            }
			            if ( langImageName != null )
			                contentElement.addAttribute("imagename", langImageName);
			            if ( imageUploaded != null )
			                contentElement.addAttribute("uploaded", DateUtil.formatDateForXMLOutput(imageUploaded));

						if (imageLink != null)
						{
							Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
							imageLinkElement.addAttribute("language", languageCode);
							imageLinkElement.addAttribute("locale", localeCode);
							imageLinkElement.addAttribute("guid", imageLink.getGuid());
							imageLinkElement.addCDATA(imageLink.getEncodedValue());
						}

						if(languageContent != null) {
							addImageAltText(languageContent.getImageAltText(), contentsElement, languageCode, localeCode);
							addImageExtLink(languageContent.getImageExtLink(), contentsElement, languageCode, localeCode);
							addImageExtPath(languageContent.getImageExtPath(), contentsElement, languageCode, localeCode);
						}
			        }
		        }
			}
			if (missingFlag)
			{
				Element contentElement = contentsElement.addElement("Content");
		        contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
		        if (!localeCode.equalsIgnoreCase(defaultSystemLocaleCode))
		        	contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
				else
					contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
			}
		}
	}

	public void addImageAltText(ComplexValue imageAltText, Element contentParentElement, String language, String localeCode) {
		if (imageAltText != null) {
			Element imageAltElement = contentParentElement.addElement("AltText");
			imageAltElement.addAttribute("language", language);
			imageAltElement.addAttribute("locale", localeCode);
			imageAltElement.addCDATA(imageAltText.getEncodedValue());
		}
	}

	public void addImageExtLink(ComplexValue imageExtLink, Element contentParentElement, String language, String localeCode) {
		if (imageExtLink != null) {
			Element imageExtLinkElement = contentParentElement.addElement("ImageExternalLink");
			imageExtLinkElement.addAttribute("language", language);
			imageExtLinkElement.addAttribute("locale", localeCode);
			imageExtLinkElement.addCDATA(imageExtLink.getEncodedValue());
		}
	}

	public void addImageExtPath(ComplexValue imageExtPath, Element contentParentElement, String language, String localeCode) {
		if (imageExtPath != null) {
			Element imageExtLinkElement = contentParentElement.addElement("ImageExternalPath");
			imageExtLinkElement.addAttribute("language", language);
			imageExtLinkElement.addAttribute("locale", localeCode);
			imageExtLinkElement.addCDATA(imageExtPath.getEncodedValue());
		}
	}

	private void createContentTagForSelectableImageLibrary(ContentObjectData contentObjectData, Element versionElement)
	throws Exception {
		Element selectableContentElement = versionElement.addElement("Selections");		
		if (contentObjectData.getParameterGroup() != null) {
			selectableContentElement.addAttribute("selectorrefid", Long.toString(contentObjectData.getParameterGroup().getId()));
		}

		// Default Selection
		Element defaultSelectionElement = selectableContentElement.addElement("Selection");
		Element defaultSelectionNameElement = defaultSelectionElement.addElement("Name");
		defaultSelectionNameElement.addText("Master");
		
		Element defaultSelectionContentsElement = defaultSelectionElement.addElement("Contents");
		defaultSelectionContentsElement.addAttribute("contenttype", "Graphic");			

		createContentTagForRegularImageLibrary(contentObjectData, defaultSelectionContentsElement);

		List<ParameterGroupTreeNode> topLevelTreeNodes = ParameterGroupTreeNode.findTopLevelTreeNodeForDynamicContentObject(contentObjectData.getModel(), contentObjectData.getDataType());
		Set<Long> explored = new HashSet<>();
		for (ParameterGroupTreeNode pgtn : topLevelTreeNodes) {
			if ( explored.contains(pgtn.getId()) )
				continue;
			explored.add(pgtn.getId());

			List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObjectData.getModel(), contentObjectData.getDataType(), pgtn);
			if (cas != null && !cas.isEmpty()) {
				createImageLibrarySelectionTag(contentObjectData, cas, pgtn, defaultSelectionElement);
			}
		}
	}

	private void createImageLibrarySelectionTag(ContentObjectData contentObjectData, List<ContentObjectAssociation> ecContentAssociations, ParameterGroupTreeNode parameterGroupTreeNode, Element element)
	{
		Element selectionElement = element.addElement("Selection");
		long id = parameterGroupTreeNode.getId();
		TouchpointSelection touchpointSelection = TouchpointSelection.findByPgTreeNodeId(id);

		if (touchpointSelection != null) 
		{
			id = touchpointSelection.getId();
		}
		
		selectionElement.addAttribute("id", Long.valueOf(id).toString());

		if (touchpointSelection != null)
		{
			selectionElement.addAttribute("dna", touchpointSelection.getDna());
		}

		if(parameterGroupTreeNode != null) {
			selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
		}

		if (parameterGroupTreeNode.getParameterGroupInstanceCollection() != null)
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
			selectionElement.addAttribute("shared", parameterGroupTreeNode.getParameterGroupInstanceCollection().isShared()?"true":"false");
			selectionElement.addAttribute("guid", parameterGroupTreeNode.getParameterGroupInstanceCollection().getGuid());
		}		
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addText(parameterGroupTreeNode.getName());

		Element selectionContentsElement = selectionElement.addElement("Contents");	
		
		createContentTagForImageLibrarySelection(contentObjectData, ecContentAssociations, parameterGroupTreeNode, selectionContentsElement, false, this);
		
		List<ParameterGroupTreeNode> ChildNodes = parameterGroupTreeNode.getChildren();
		if (ChildNodes != null && !ChildNodes.isEmpty())
		{
			for (ParameterGroupTreeNode childNode : ChildNodes)
			{
				List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObjectData.getModel(), contentObjectData.getDataType(), childNode);

				createImageLibrarySelectionTag(contentObjectData, cas, childNode, selectionElement);
			}
		}
	}
	
	public List<Long> getList() {
		return list;
	}

	public void setList(List<Long> list) {
		this.list = list;
	}

	public static class CommentIdComparator implements Comparator<ContentObjectComment> {

		@Override
		public int compare(ContentObjectComment arg0, ContentObjectComment arg1) {
			int result = Long.valueOf(arg0.getId()).compareTo(Long.valueOf(arg1.getId()));
			return result;
		}
	}
	
	public static void createContentTagForImageLibrarySelection(ContentObjectData contentObjectData,
			List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection,
			Element contentsElement, boolean emptyParts, ExportImageLibraryToXMLService edtXMLs)
	{
	    boolean selectableMessage = false;
	    if (contentObjectData.getModel().isDynamicVariantEnabled())
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
        {
            String languageCode = locale.getLanguageCode();
			String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langFileName = "";
                String langImageUploaded = "";
			    ComplexValue imageLink = null;

    	    	contentsElement.addAttribute("contenttype", "Graphic");
                if ( content != null) 
                {
					imageLink = content.getImageLink();
                	if ( content.getImageName() != null )
                		langFileName = content.getImageName();
                	if ( content.getImageLocation() != null )
                	{
                		if (edtXMLs.includeImagePathOnly)
                		{
                			languageContent = content.getRelativeImageLocation();
                		}
                		else
                		{
                			languageContent = ExportXMLUtils.encodeFile(content.getImageLocation());
                		}
                	}
                    if ( content.getAppliedImageFilename() != null )
                        langImageName = content.getAppliedImageFilename();
                    
                    if ( content.getImageUploadedDate() != null )
                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
                    else
                        langImageUploaded = "";
                } 
                else if(ca.getReferencingImageLibrary() == null)
                {
                    sameAsParent = true;
                }
                
				missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
		        	if (ca.getReferencingImageLibrary() != null)
		        	{
						contentElement.addAttribute("imagelibraryrefid", Long.toString(ca.getReferencingImageLibrary().getId()));
						contentElement.addAttribute("imagelibraryrefname", ca.getReferencingImageLibrary().getName());
		        	}
		        	else
		        	{
	                    if (!langFileName.isEmpty())
	                    {
	                        contentElement.addAttribute("filename", langFileName);
						    if (edtXMLs.includeImagePathOnly)
						    {
							    contentElement.addAttribute("pathonly", "true");
							    edtXMLs.originalImageFiles.add(content.getImageLocation());
						    }
	                    }
	
		                if (!langImageName.isEmpty())
		                    contentElement.addAttribute("imagename", langImageName);
		                
		                if (!langImageUploaded.isEmpty())
		                    contentElement.addAttribute("uploaded", langImageUploaded);
		                
	                    contentElement.addCDATA(languageContent);
	                    
	    				if (imageLink != null)
	    				{
	    					Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
	    					imageLinkElement.addAttribute("language", languageCode);
							contentElement.addAttribute("locale", localeCode);
	    					imageLinkElement.addAttribute("guid", imageLink.getGuid());
	    					imageLinkElement.addCDATA(imageLink.getEncodedValue());
	    				}
		        	}
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
				contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
	
	
}
