package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.export.ExportRationalizerObjectToExcelServiceResponse;
import com.prinova.messagepoint.util.FileUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class ExportDashboardContentsService extends AbstractService {

    private static final String SERVICE_NAME = "export.ExportDashboardContentsService";
    private static final Log log = LogUtil.getLog(ExportDashboardContentsService.class);

    @Override
    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            ExportRationalizerDashboardToExcelServiceRequest request = (ExportRationalizerDashboardToExcelServiceRequest) context.getRequest();
            long rationalizerAppId = request.getRationalizerAppId();
            RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerAppId);

            int appSyncStatus = rationalizerApplication.getAppSyncStatus();
            if (RationalizerApplicationSyncStatus.NEVER_SYNCHRONIZED.getStatusValue() == appSyncStatus || RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == appSyncStatus) {
                throw new IOException("Report can not be generated. Application is not synchronized with elastic or elastic server is down.");
            }

            log.info("Starting export rationalizer dashboard report for application = " + rationalizerApplication.getName());

            List<ReportContentDTO> reportContentsList = getContentsForReport(request, rationalizerApplication);
            if (CollectionUtils.isEmpty(reportContentsList)) {
                return;
            }

            StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();
            File outputDirectory = new File(statusPollingBackgroundTask.getOutputPath());
            statusPollingBackgroundTask.setProgressInPercentInThread(10);


            String exportFileName = FilenameUtils.removeExtension(statusPollingBackgroundTask.getOutputFilename());
            new GenerateRationalizerDashboardExcelReport(
                    rationalizerApplication,
                    reportContentsList,
                    exportFileName,
                    outputDirectory,
                    request.getDashboardExportType(),
                    request.getSourceSelectionBranches(),
                    request.getTargetSelectionBranches(),
                    statusPollingBackgroundTask)
                    .invoke();

            String zipFilePath = outputDirectory + File.separator + exportFileName + ".zip";
            zipFilePath = generateExportZip(outputDirectory, zipFilePath);

            log.info("End export rationalizer dashboard report for application = " + rationalizerApplication.getName());

            ((ExportRationalizerObjectToExcelServiceResponse) context.getResponse()).setFilePath(zipFilePath);
        } catch (Exception ex) {
            log.error(" unexpected exception when invoking ExportDashboardContentsService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        }
    }

    public abstract List<ReportContentDTO> getContentsForReport(ExportRationalizerDashboardToExcelServiceRequest request, RationalizerApplication application);

    @Override
    public void validate(ServiceExecutionContext context) {
    }

    public static ServiceExecutionContext createContext(long appId, List<String> selectedIds, String sourceSelectionBranches, String targetSelectionBranches,
                                                        User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask, RationalizerDashboardExportType dashboardExportType) {

        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportRationalizerDashboardToExcelServiceRequest request =
                new ExportRationalizerDashboardToExcelServiceRequest(
                        appId,
                        selectedIds,
                        sourceSelectionBranches,
                        targetSelectionBranches,
                        requestor,
                        statusPollingBackgroundTask,
                        dashboardExportType
                );

        context.setRequest(request);

        ExportRationalizerObjectToExcelServiceResponse serviceResp = new ExportRationalizerObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    private String generateExportZip(File filesDirectory, String zipFilePath) throws IOException {
        Map<String, InputStream> filesToZipMap = new HashMap<>();
        File[] listOfOriginalFiles = filesDirectory.listFiles();
        if (listOfOriginalFiles != null && listOfOriginalFiles.length > 0) {
            for (File file : listOfOriginalFiles) {
                filesToZipMap.put(file.getName(), new FileInputStream(file.getPath()));
            }
        }
        File zipFile = new File(zipFilePath);
        zipFile.createNewFile();
        FileUtil.createZip(filesToZipMap, zipFilePath);

        return zipFilePath;
    }
}
