package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

public class ExportDashboardContentsFactory {
    public ExportDashboardContentsFactory() {
    }

    public ExportDashboardContentsService createDashboardExportService(RationalizerDashboardExportType exportDataType) {
        switch (exportDataType) {
            case DUPLICATES:
                return new ExportDashboardDuplicateContentsService();
            case SIMILARITY:
                return new ExportDashboardSimilarityContentsService();
            default:
                return new ExportDashboardDuplicateContentsService();
        }
    }
}
