package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.List;

public class ExportRationalizerDashboardToExcelServiceRequest implements ServiceRequest {

    private static final long serialVersionUID = -1553522279771063789L;

    private long rationalizerAppId;
    private String sourceSelectionBranches;
    private String targetSelectionBranches;
    private RationalizerDashboardExportType dashboardExportType;

    private List<String>	selectedIds;
    private User 		requestor;
    private ExportImportOptions exportOptions;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    public ExportRationalizerDashboardToExcelServiceRequest(
            Long rationalizerAppId,
            List<String> selectedIds,
            String sourceSelectionBranches,
            String targetSelectionBranches,
            User user,
            StatusPollingBackgroundTask statusPollingBackgroundTask,
            RationalizerDashboardExportType dashboardExportType
    ) {
        this.rationalizerAppId = rationalizerAppId;
        this.selectedIds = selectedIds;
        this.requestor = user;
        this.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
        this.sourceSelectionBranches = sourceSelectionBranches;
        this.targetSelectionBranches= targetSelectionBranches;
        this.dashboardExportType = dashboardExportType;
    }

    public List<String> getSelectedIds() {
        return selectedIds;
    }

    public User getRequestor() {
        return requestor;
    }

    public ExportImportOptions getExportOptions() {
        return exportOptions;
    }

    public void setExportOptions(ExportImportOptions exportOptions) {
        this.exportOptions = exportOptions;
    }

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }

    public long getRationalizerAppId() {
        return rationalizerAppId;
    }

    public String getSourceSelectionBranches() {
        return sourceSelectionBranches;
    }

    public String getTargetSelectionBranches() {
        return targetSelectionBranches;
    }

    public RationalizerDashboardExportType getDashboardExportType() {
        return dashboardExportType;
    }

}
