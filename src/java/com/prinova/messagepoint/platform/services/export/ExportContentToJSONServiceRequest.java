package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;
import com.prinova.messagepoint.platform.services.imports.JsonContentObject;

import java.util.List;

public class ExportContentToJSONServiceRequest extends SimpleServiceRequest {

    private static final long serialVersionUID = -3613858253259861592L;
    private String 		    exportId;
    private ContentObject targeObjectId = null;
    private Document targetDocumentId = null;
    private List<Long> selectedIds;
    private User 		requestor;
    private String      requestGuid;
    private ExportImportOptions exportOptions;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    private JsonContentObject failedImportJsonContentObject;
    private String rejectionReason;
    private boolean isExportFromWorkflow = false;

    public ExportContentToJSONServiceRequest(String exportId, ContentObject targeObjectId, List<Long> selectedIds, ExportImportOptions exportImportOptions, User user ) {
    	this(exportId, targeObjectId, selectedIds, exportImportOptions, user, null);
    }

    public ExportContentToJSONServiceRequest(String exportId, ContentObject targeObjectId, List<Long> selectedIds, ExportImportOptions exportImportOptions, User user, StatusPollingBackgroundTask statusPollingBackgroundTask ) {
        this.exportId = exportId;
        this.targeObjectId = targeObjectId;
        this.selectedIds = selectedIds;
        this.exportOptions = exportImportOptions;
        this.requestor = user;
        this.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
    }

    public ExportContentToJSONServiceRequest(String exportId, Document targeObjectId, List<Long> selectedIds, ExportImportOptions exportImportOptions, User user, StatusPollingBackgroundTask statusPollingBackgroundTask ) {
        this.exportId = exportId;
        this.targetDocumentId = targeObjectId;
        this.selectedIds = selectedIds;
        this.exportOptions = exportImportOptions;
        this.requestor = user;
        this.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
    }

	public String getExportId() {
		return exportId;
	}

	public ContentObject getTargeObjectId() {
        return targeObjectId;
    }

    public Document getTargetDocumentId() {
        return targetDocumentId;
    }

    public void setTargetDocumentId(Document targetDocumentId) {
        this.targetDocumentId = targetDocumentId;
    }

    public List<Long> getSelectedIds() {
		return selectedIds;
	}

    public User getRequestor() {
        return requestor;
    }

	public ExportImportOptions getExportOptions() {
		return exportOptions;
	}

	public void setExportOptions(ExportImportOptions exportOptions) {
		this.exportOptions = exportOptions;
	}

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }

    public JsonContentObject getFailedImportJsonContentObject() {
        return failedImportJsonContentObject;
    }

    public void setFailedImportJsonContentObject(JsonContentObject failedImportJsonContentObject) {
        this.failedImportJsonContentObject = failedImportJsonContentObject;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public String getRequestGuid() {
        return requestGuid;
    }

    public void setRequestGuid(String requestGUID) {
        this.requestGuid = requestGUID;
    }

    public boolean isExportFromWorkflow() {
        return isExportFromWorkflow;
    }

    public void setExportFromWorkflow(boolean exportFromWorkflow) {
        isExportFromWorkflow = exportFromWorkflow;
    }
}
