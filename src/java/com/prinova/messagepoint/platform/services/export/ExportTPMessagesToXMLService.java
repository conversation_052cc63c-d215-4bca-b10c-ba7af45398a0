package com.prinova.messagepoint.platform.services.export;

import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentFactory;
import org.dom4j.Element;

import com.prinova.messagepoint.MessagepointException;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.common.ContentSelectionType;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;


public class ExportTPMessagesToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportTPMessagesToXMLService";

	private static final Log log = LogUtil.getLog(ExportTPMessagesToXMLService.class);
    private User requestor;
	private boolean includeAllMessages = true;
	private final List<String> originalImageFiles = new ArrayList<>();
	private org.dom4j.Document			document;
	private Document touchpoint;
	
	public void execute(ServiceExecutionContext context) {
		try 
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			ExportMessagepointObjectToXMLServiceRequest request = (ExportMessagepointObjectToXMLServiceRequest) context.getRequest();
	    	this.requestor = request.getRequestor();
	    	this.includeAllMessages = request.getExportOptions().isIncludeMessages();
	    	this.touchpoint = Document.findById(request.getTargeObjectId());
            if(touchpoint == null)
            	return;
	    	String exportName = touchpoint.getName();

	    	DocumentFactory docFactory = DocumentFactory.getInstance(); 
			this.document = docFactory.createDocument();
			this.document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);		
			Element rootElement = docFactory.createElement("MpBulkUploadDefinition");
			this.document.setRootElement(rootElement);		
			
			generateXML(rootElement);
			String filePath = ExportUtil.saveMessagepointObjectExportXMLToFile(document, requestor, ExportUtil.ExportType.TPMESSAGES, request.getExportId(), exportName);
			context.getResponse().setResultValueBean(request.getExportId());
			((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);

		}
		catch (Exception ex)
		{
			if (ex.getCause() != null)
            {
            	if (ex.getCause().getMessage() != null)
            	{
            		if (ex.getCause().getMessage().equalsIgnoreCase("#RequiredValuesAreEmpty#"))
            		{
                        this.getResponse(context).addErrorMessage(
                        		"error.message.content.library.imagefile.not.compatible",
                        		"error.message.content.library.imagefile.not.compatible",
                        		new String[] {ex.getCause().getMessage()}, "", null);
                        this.getResponse(context).setReturnCode(SimpleServiceResponse.ERROR);
                        return;
            		}
            	}
            }
			log.error(" unexpected exception when invoking ExportTPMessagesToXMLService execute method", ex);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + ex,
					context.getLocale() );
			throw new RuntimeException(ex.getMessage());
		}
	}
	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}

	private void generateXML(Element rootElement) throws Exception 
	{
		
		MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        rootElement.addAttribute("version", version);
        rootElement.addAttribute("build", buildVersion);
        rootElement.addAttribute("requestedby", requestor.getName());
        rootElement.addAttribute("requestdate", DateUtil.formatDateForXMLOutput(DateUtil.now()));
        rootElement.addAttribute("name", this.touchpoint.getName());
        rootElement.addAttribute("guid", this.touchpoint.getGuid());
		
		Element topElm = rootElement.addElement("TouchpointMessages");
		
		List<Long> msgInstIds = new ArrayList<>();
		
		List<Zone> zoneList = new ArrayList<>();
        for(Zone zone:Zone.findByDocumentIdOrderByName(this.touchpoint.getId())){
        	if(!zone.isMultipart() && zone.isEnabled()){
        		zoneList.add(zone);
        	}
        }
        if(zoneList.isEmpty()){
        	// need to display error message
        	throw new MessagepointException("Zones", new Throwable("#RequiredValuesAreEmpty#"));
        }
        List<Long> zoneIds = new ArrayList<>();
		for(Zone zone : zoneList){
			zoneIds.add(zone.getId());
		}
        msgInstIds = null; // TODO ContentObject.findAllVisibleIdsOfMostRecentCopiesWUser(zoneIds, null);
        if (msgInstIds != null && !msgInstIds.isEmpty() && this.includeAllMessages){
        	for (Long contentObjectId : msgInstIds){
				ContentObject msg = ContentObject.findById(contentObjectId);
				if(msg.getOwningTouchpointSelection() != null)
					continue;
				buildNodeElm(topElm, msg);
			}
		}else{
			buildNodeElm(topElm, null);
		}
	}
	
	/**
	 * Build the "Node" element
     */
	private void buildNodeElm(Element rootElement, ContentObject msg) throws Exception{
		Element variantElm = rootElement.addElement("Variant");
		if(msg == null){
			variantElm.addAttribute("guid", "");
			variantElm.addAttribute("dna", "");
			variantElm.addAttribute("id", "1");
			if (touchpoint != null && touchpoint.isEnabledForVariation()) {
				variantElm.addAttribute("type", new ContentSelectionType(ContentSelectionType.ID_TOUCHPOINT_CONTENT_SELECTABLE).getDisplayText());
			} else
				variantElm.addAttribute("type", new ContentSelectionType(ContentSelectionType.ID_REGULAR).getDisplayText());
			
			variantElm.addAttribute("timing", "");
			variantElm.addAttribute("contenttype", "Text");
			variantElm.addAttribute("selectorid", "");
	        
			variantElm.addAttribute("deliverytype", "Mandatory");
			variantElm.addAttribute("zonerefid", "");
			Element nameElm = variantElm.addElement("Name");
			nameElm.addText("Message Name");
			Element detatagsElm = variantElm.addElement("Metatags");
			detatagsElm.addText("");
			Element descriptionElm = variantElm.addElement("Description");
			descriptionElm.addText("");
			List<MessagepointLocale> languages = this.touchpoint.getTouchpointLanguagesAsLocales();
			
			for(MessagepointLocale locale:languages){
				
	        	String cellValue = "Empty";
	        	Element contentElm = variantElm.addElement("Content");
				contentElm.addAttribute("id", "0");
				contentElm.addAttribute("language", locale.getLanguageCode());
				contentElm.addAttribute("locale", locale.getCode());
				contentElm.addText(cellValue!=null?cellValue:"");
			}
			return;
		}
		variantElm.addAttribute("guid", msg.getGuid());
		variantElm.addAttribute("dna", msg.getDna());
		variantElm.addAttribute("id", String.valueOf(msg.getId()));
		variantElm.addAttribute("type", new ContentSelectionType(msg).getDisplayText());
		variantElm.addAttribute("timing", "");
		variantElm.addAttribute("contenttype", ApplicationUtil.getMessage(msg.getContentTypeName()));
		if(!msg.isStructuredContentEnabled() && msg.getParameterGroup() != null){ // selectable
			variantElm.addAttribute("selectorid", String.valueOf(msg.getParameterGroup().getId()));
        }else{
        	variantElm.addAttribute("selectorid", "");
        }
		
		variantElm.addAttribute("deliverytype", msg.getDeliveryTypeText());
		variantElm.addAttribute("zonerefid", String.valueOf(msg.getZone() != null ? msg.getZone().getId() : 0L));
		Element nameElm = variantElm.addElement("Name");
		nameElm.addText(msg.getName());
		Element detatagsElm = variantElm.addElement("Metatags");
		detatagsElm.addText(msg.getMetatags()!=null?msg.getMetatags():"");
		Element descriptionElm = variantElm.addElement("Description");
		descriptionElm.addText(msg.getDescription()!=null?msg.getDescription():"");
		List<MessagepointLocale> languages = this.touchpoint.getTouchpointLanguagesAsLocales();
		
		for(MessagepointLocale locale:languages){
			
        	String cellValue = "Empty";
        	TouchpointContentSelectionViewWrapper viewWrapper = null;
        	if(msg != null){
	        	if(msg!=null && msg.getIsGraphic()){
	        		ContentObjectAssociation ca = null;
	        		if(!msg.isStructuredContentEnabled() && !msg.isDynamicVariantEnabled()){
	        			ca = null; // TODO ContentObjectAssociation.findParentContentAssociation4SelectionByLanguage(msg.getId(), languageCode, -1, null, ContentObject.class);
	        			if(ca != null) {
							if(ca.getTypeId() == ContentAssociationType.ID_OWNS){
			        			if(ca.getReferencingImageLibrary() != null){
			        				cellValue = ca.getReferencingImageLibrary().getName();
			        			}else if( ca.getContent() != null && ca.getContent().getImageName() != null){
			        				cellValue = "Uploaded";
			            		}else{
			        				cellValue = "Empty";
			        			}
			        		}else if(ca.getTypeId() == ContentAssociationType.ID_REFERENCES){
			        			cellValue = "Inheriting from parent";
			        		}else{
			    				cellValue = "Empty";
			    			}
		    			}
	        		}else{
	        			ca = null; // TODO ContentObjectAssociation.findMasterContentAssociationByMessageInstanceId(msg.getId());
	        			if(ca != null) {
							// TODO TouchpointContentSelection contentSelection = new TouchpointContentSelection(ca);
							// viewWrapper = new TouchpointContentSelectionViewWrapper(contentSelection, requestor);
			    			ContentVO content = viewWrapper.getContentVO().getLangContentMap().get(locale.getLanguageCode());
							if(ca.getTypeId() == ContentAssociationType.ID_OWNS){
			        			if(content.getImageLibraryId() > 0){
			    					cellValue = ContentObject.findById(content.getImageLibraryId()).getName();
			        			}else if( content != null && content.getContentId() > 0 && content.getImageName() != null){
			        				cellValue = "Uploaded";
			            		}else{
			        				cellValue = "Empty";
			        			}
			        		}else if(ca.getTypeId() == ContentAssociationType.ID_REFERENCES){
			        			cellValue = "Inheriting from parent";
			        		}else{
			    				cellValue = "Empty";
			    			}
		    			}
	        		}
	        	}else if(msg.isTextContent()){
	        		ContentObjectAssociation ca =  null;
	        		if(msg.isStructuredContentEnabled())
        				ca = null; // TODO ContentObjectAssociation.findMasterContentAssociationByMessageInstanceId(msg.getId());
        			else
        				ca = null; // TODO ContentObjectAssociation.findParentContentAssociation4SelectionByLanguage(msg.getId(), languageCode, -1, null, ContentObject.class);
	        		if(ca != null) {
	    				if(msg.isStructuredContentEnabled()){
	    					// TODO TouchpointContentSelection contentSelection = TouchpointContentSelection.findById(ca.getId());
		    				// viewWrapper = new TouchpointContentSelectionViewWrapper(contentSelection, requestor);
		    				cellValue = viewWrapper.getContentVO().getLangContentMap().get(locale.getLanguageCode()).getContent();
	    				}else{
	    					if(ca.getContent() != null)
	    						cellValue = ca.getContent().getEncodedContent();
	    				}
	    			} 
	        	}else{
	        		cellValue = "Empty";
	        	}
        	}
        	Element contentElm = variantElm.addElement("Content");
			contentElm.addAttribute("id", "0");
			contentElm.addAttribute("language", locale.getLanguageCode());
			contentElm.addAttribute("locale", locale.getCode());
			contentElm.addText(cellValue!=null?cellValue:"");
		}
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
	
	public List<String> getOriginalImageFiles() {
		return originalImageFiles;
	}
	public org.dom4j.Document getDocument() {
		return document;
	}
	public void setDocument(org.dom4j.Document document) {
		this.document = document;
	}
	public Document getTouchpoint() {
		return touchpoint;
	}
	public void setTouchpoint(Document touchpoint) {
		this.touchpoint = touchpoint;
	}
}
