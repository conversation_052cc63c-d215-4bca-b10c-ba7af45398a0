package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ExportDynamicContentObjectServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -1553522279771063788L;

	// legacy fields
	private String 		exportId;
    private long 		imageId;
    private boolean		includeImagePathOnly;
    private User 		requestor;



    private ExportUtil.ExportFormat exportFormat;
    private ContentObject contentObject;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;

    private ExportDynamicContentObjectServiceRequest() {

    }

    public boolean isIncludeImagePathOnly() {
		return includeImagePathOnly;
	}

	public String getExportId() {
		return exportId;
	}

	public long getImageId() {
        return imageId;
    }
    
    public User getRequestor() {
        return requestor;
    }

    public ContentObject getContentObject() {
        return contentObject;
    }

    public ExportUtil.ExportFormat getExportFormat() {
        return exportFormat;
    }

    public static ExportDynamicContentObjectServiceRequest createExcelExportRequest(String exportId, long imageId, boolean includeImagePathOnly, User user )
    {
        ExportDynamicContentObjectServiceRequest instance = new ExportDynamicContentObjectServiceRequest();
        instance.exportId = exportId;
        instance.imageId = imageId;
        instance.includeImagePathOnly = includeImagePathOnly;
        instance.requestor = user;
        instance.exportFormat = ExportUtil.ExportFormat.EXCEL;

        return instance;
    }

    public static ExportDynamicContentObjectServiceRequest createNewRequest(ContentObject contentObject, String exportId, ExportUtil.ExportFormat format, boolean includeImagePathOnly, User user) {
        ExportDynamicContentObjectServiceRequest instance = new ExportDynamicContentObjectServiceRequest();

        instance.contentObject = contentObject;
        instance.exportId = exportId;
        instance.exportFormat = format;
        instance.includeImagePathOnly = includeImagePathOnly;
        instance.requestor = user;

        return instance;
    }

    public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
        return statusPollingBackgroundTask;
    }

    public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
    }



}
