package com.prinova.messagepoint.platform.services.export;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.admin.RoleUtil;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameIgnoreCaseComparator;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.imports.BulkUploadUsersUpdateService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.UserUtil;

 
public class ExportUsersToExcelService extends AbstractService {
	
    public static final String SERVICE_NAME = "export.ExportUsersToExcelService";
    
    private static final Log log = LogUtil.getLog(ExportUsersToExcelService.class);
    private User requestor = null;
	private boolean includeImagePathOnly = false;
	private final List<String> originalImageFiles = new ArrayList<>();

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.NEVER, readOnly = false)
	public void execute(ServiceExecutionContext context) {
        try 
        {
            validate(context);
            if (hasValidationError(context)) 
            {
                return;
            }

            ExportMessagepointObjectToExcelServiceRequest request = (ExportMessagepointObjectToExcelServiceRequest) context.getRequest();
            
            Node node = Node.findById(request.getTargeObjectId());
            if(node == null)
            	return;
            requestor = request.getRequestor();
            originalImageFiles.clear();
            File outputDirectory = new File(ExportUtil.EXPORT_EXCEL_TEMPLATES_DIR + File.separator);
            //outputDirectory.mkdirs();
    	    
            File file = new File(outputDirectory + File.separator + (ExportUtil.EXCEL_EXPORT_TEMPLATE_USERS_UPDATE));
            FileInputStream fileStream = new FileInputStream(file);
            XSSFWorkbook workbook = new XSSFWorkbook(fileStream);
            generateWorkbookUpdate(node, workbook, requestor);
            String exportName = node.getName();
            if (exportName != null)
            	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
            
            String filePath = ExportUtil.saveMessagepointObjectExportExcelToFile(workbook, 
            		requestor, 
            		ExportUtil.ExportType.USER, 
            		request.getExportId(),
            		exportName,
            		request.getExportOptions());

            ((ExportMessagepointObjectToExcelServiceResponse) context.getResponse()).setFilePath(filePath);
        }
        catch( Exception ex )
        {
        	log.error(" unexpected exception when invoking ExportUsersToExcelService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale() );
            throw new RuntimeException(ex.getMessage());
        }
    }
    
    /*
     * Only existing users list generating
     */
    private void generateWorkbookUpdate( Node currentNode, XSSFWorkbook workbook, User requestor ) throws Exception 
    {
    	XSSFSheet sheet = workbook.getSheet("users");
    	Branch branch = currentNode.getBranch();
    	/*
         *  Main branch users list sheet
         */
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        
        // create xml definition row
        Row versionRow = sheet.getRow(0);
        versionRow.createCell(1).setCellValue(version);
        Row buildVersionRow = sheet.getRow(1);
        buildVersionRow.createCell(1).setCellValue(buildVersion);
        Row userRow = sheet.getRow(2);
        userRow.createCell(1).setCellValue(requestor.getName());
        Row dateRow = sheet.getRow(3);
        dateRow.createCell(1).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        
        Row nameRow = sheet.getRow(4);
        nameRow.createCell(1).setCellValue(branch.getName());
        Row guidRow = sheet.getRow(5);
        guidRow.createCell(1).setCellValue(branch.getGuid());
        
        
    	/*
         *  Possible Instance List and its role list with id for current domain
         */
    	
    	Map<Node, List<Role>> instanceRoleMap = new HashMap<>();
    	Map<Node, CellRangeAddressList> cellRangeListMap = new HashMap<>();
    	int maxInstanceNumber = 0;
    	for(Node node : branch.getAllAccessibleNodes(true, requestor.canAccessTestingInstances())) // with DCS
		{
			if(!instanceRoleMap.containsKey(node)){
				List<Role> roleList = RoleUtil.findAllByNode(Node.getSchema(node.getName(), branch.getName()));
				Collections.sort(roleList, new IdentifiableMessagepointModelNameIgnoreCaseComparator());
				instanceRoleMap.put(node, roleList);
				if(maxInstanceNumber < roleList.size())
					maxInstanceNumber = roleList.size();
			}
			if(!cellRangeListMap.containsKey(node)){
				CellRangeAddressList newList = new CellRangeAddressList();
				cellRangeListMap.put(node, newList);
			}
		}
    	
    	XSSFSheet ref_sheet = workbook.getSheet("reference");
    	// each instnace will have two columns for Role Name and Role ID
    	Row ref_headerRow = ref_sheet.createRow(0);
        int ref_headerRow_Column = 0;
        for (Node instance : instanceRoleMap.keySet()){
        	ref_headerRow.createCell(ref_headerRow_Column).setCellValue(instance.getName());
        	
        	Row descRow = ref_sheet.getRow(1);
        	if(descRow == null)
        		descRow = ref_sheet.createRow(1);
        	descRow.createCell(ref_headerRow_Column).setCellValue(ApplicationUtil.getMessage("page.label.rolename"));
        	descRow.createCell(ref_headerRow_Column+1).setCellValue(ApplicationUtil.getMessage("page.label.role.id"));
        	
        	Row roleNoneRow = ref_sheet.getRow(2);
        	if(roleNoneRow == null)
        		roleNoneRow = ref_sheet.createRow(2);
        	roleNoneRow.createCell(ref_headerRow_Column).setCellValue(ApplicationUtil.getMessage("page.label.no.access"));
        	roleNoneRow.createCell(ref_headerRow_Column+1).setCellValue(0);
        	
        	if(instanceRoleMap.get(instance).isEmpty())
        		continue;
        	
        	int roleRowCounter = 3;
        	for(Role role:instanceRoleMap.get(instance)){
        		
        		Row roleRow = ref_sheet.getRow(roleRowCounter);
        		if(roleRow == null)
        			roleRow = ref_sheet.createRow(roleRowCounter);
        		roleRow.createCell(ref_headerRow_Column).setCellValue(role.getName());
        		roleRow.createCell(ref_headerRow_Column+1).setCellValue(role.getId());
        		roleRowCounter++;
        	}
        	ref_headerRow_Column += 2;
        }
        
    	//generateChildPgtnRow(sheet, currentNode, instanceRoleMap);
    	int rowCounter = 8;
		
		String[] yesNoList = new String[2];
		yesNoList[0] = ApplicationUtil.getMessage("page.label.yes");
		yesNoList[1] = ApplicationUtil.getMessage("page.label.no");
		
		int idpTypeCounter = 1;
		if(branch.isPingSSOEnabled()){
			idpTypeCounter= 2;
        }
        String[] IDProviderTypeList = new String[idpTypeCounter];
        IDProviderTypeList[0] = ApplicationUtil.getMessage("page.label.local.domain");
        if(branch.isPingSSOEnabled() && branch.getSsoIdPId() != null){
        	IDProviderTypeList[1] = ApplicationUtil.getMessage("page.label.local.sso");
        }
        Map<String, CellRangeAddressList> cellRangeActionListMap = new HashMap<>();
        Map<String, String[]> actionListMap = new HashMap<>();
        getActionList(cellRangeActionListMap, actionListMap);
        
		CellRangeAddressList cellRangeYesNoList = new CellRangeAddressList();
        CellRangeAddressList cellRangeIDPTypeList = new CellRangeAddressList();
        int repeatingColumnStartCell = 12;
        
        // Get users from branch DCS instance
        List<User> branchUserList = UserUtil.getUsersForExportByRequestor(requestor, branch);
        
        XSSFCellStyle boldFontCellStyle = workbook.createCellStyle();
    	Font font = workbook.createFont();//Create font
        font.setBold(true);//Make font bold
        boldFontCellStyle.setFont(font);//set it to bold
        
        for(User user:branchUserList){
        	if(user == null || (!user.isHomeDcsUser() && !user.isLocalSSOUser()))
        		continue;
        	User localDCSuser = user.getLocalDcsUser();
			Row dataRow = sheet.createRow(rowCounter);
			dataRow.createCell(0).setCellValue(user.getGuid()); //GUID
			dataRow.createCell(1).setCellValue(user.getId()); //ID
			dataRow.createCell(2).setCellValue(user.getFirstName()); // FirstName
			dataRow.createCell(3).setCellValue(user.getLastName()); // LastName
			Cell userNameCell = dataRow.createCell(4);
			userNameCell.setCellValue(user.getUsername()); // UserName
			if(user.isDomainAdminUser())
				userNameCell.setCellStyle(boldFontCellStyle);
			dataRow.createCell(5).setCellValue(user.getEmail()); // Email
			
			// need to set ID Provider Type drop down menu
			Cell typeCell = dataRow.createCell(6);
			typeCell.setCellValue(user.getIdPTypeDisplayName());
			cellRangeIDPTypeList.addCellRangeAddress(typeCell.getRowIndex(), typeCell.getColumnIndex(), typeCell.getRowIndex(), typeCell.getColumnIndex());
	        
	        //  Email Notification Yes or No default No
	        Cell contentContentTypeCell = dataRow.createCell(7);
	        cellRangeYesNoList.addCellRangeAddress(contentContentTypeCell.getRowIndex(), contentContentTypeCell.getColumnIndex(), contentContentTypeCell.getRowIndex(), contentContentTypeCell.getColumnIndex());
	        contentContentTypeCell.setCellValue(user.isEmailNotifyRealTime()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
	        
	        // Current User Status
	        String status = localDCSuser.getUserPendingStatus(false);
	        dataRow.createCell(8).setCellValue(status); 
	        
	        // Action
	        Cell actionTypeCell = dataRow.createCell(9);
	        cellRangeActionListMap.get(status).addCellRangeAddress(actionTypeCell.getRowIndex(), actionTypeCell.getColumnIndex(), actionTypeCell.getRowIndex(), actionTypeCell.getColumnIndex());
	        actionTypeCell.setCellValue(ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE));
	        
	        // Add Domain name and guid cells
			dataRow.createCell(10).setCellValue(branch.getName()); // Domain Name
			dataRow.createCell(11).setCellValue(branch.getGuid()); // Domain GUID
			
			int t = 0;
			int refColumnHead = 0;
	        User homeDCSUser = user.getHomeDcsUser();
	        for(Node instance:instanceRoleMap.keySet()){
	        	int cellColumn = t+repeatingColumnStartCell;
	        	Cell instanceCell = dataRow.createCell(cellColumn);
	        	instanceCell.setCellValue(instance.getName());
	        	
	        	t++;
	        	cellColumn++;
	        	User nodeUser = null;
	        	if(homeDCSUser != null)
					nodeUser = User.findByGuid(homeDCSUser.getGuid(), instance);
	        	Set<Role> roles = null;
	        	if(nodeUser != null){
	        		roles = UserUtil.getNodeUserRolesByUserID(nodeUser.getId(), instance.getSchemaName());
				}
	        	Cell idCell = dataRow.createCell(cellColumn);
	        	idCell.setCellValue(roles != null ? roles.stream().map(x -> String.valueOf(x.getId())).collect(Collectors.joining(", ")) : "0");
	        	
	        	String header = getColumbHeader(cellColumn+1);
	        	// ***since row starts from 0 the formular rowCounter need to be increased 1
	        	int roleCounter = instanceRoleMap.get(instance).size() +3;
	        	String formular = "vlookup(" + header + (rowCounter + 1) + ", reference!$" + getColumbHeader(refColumnHead) + "$3:$" + getColumbHeader(refColumnHead+1) + "$" + roleCounter + ", 2, FALSE)";
	        	idCell.setCellFormula(formular);
	        	
	        	sheet.setColumnHidden(idCell.getColumnIndex(), true);
	        	
	        	t++;
	        	cellColumn++;
	        	Cell roleCell = dataRow.createCell(cellColumn);
	        	int module = cellColumn % 3;
	        	if(module == 2)
	        		cellRangeListMap.get(instance).addCellRangeAddress(roleCell.getRowIndex(), roleCell.getColumnIndex(), roleCell.getRowIndex(), roleCell.getColumnIndex());
	        	roleCell.setCellValue(roles == null ? ApplicationUtil.getMessage("page.label.no.access") : roles.stream().map(IdentifiableMessagePointModel::getName).collect(Collectors.joining(", ")));
	        	t++;
	        	refColumnHead += 2;
	        }
	        rowCounter++;
        }
        
        /*
         * Add empty line at the end for new user creation
         */
        for(int i =0; i < 2; i++){
	        Row dataRow = sheet.createRow(rowCounter);
			dataRow.createCell(0).setCellValue(""); //GUID
			dataRow.createCell(1).setCellValue(i+1); //ID
			dataRow.createCell(2).setCellValue(""); // FirstName
			dataRow.createCell(3).setCellValue(""); // LastName
			dataRow.createCell(4).setCellValue(""); // UserName
			dataRow.createCell(5).setCellValue(""); // Email
			
			// need to set ID Provider Type drop down menu
			Cell typeCell = dataRow.createCell(6);
			cellRangeIDPTypeList.addCellRangeAddress(typeCell.getRowIndex(), typeCell.getColumnIndex(), typeCell.getRowIndex(), typeCell.getColumnIndex());
			typeCell.setCellValue(ApplicationUtil.getMessage("page.label.local.domain"));
			
	        //  Email Notification Yes or No default No
	        Cell EmailNotificationCell = dataRow.createCell(7);
	        cellRangeYesNoList.addCellRangeAddress(EmailNotificationCell.getRowIndex(), EmailNotificationCell.getColumnIndex(), EmailNotificationCell.getRowIndex(), EmailNotificationCell.getColumnIndex());
	        EmailNotificationCell.setCellValue(ApplicationUtil.getMessage("page.label.no"));
	        
	        // Current User Status
	        dataRow.createCell(8).setCellValue(""); 
	        
	        // Action
	        Cell actionTypeCell = dataRow.createCell(9);
	        actionTypeCell.setCellValue(ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_CREATE));
	        
	        // Add Domain name and guid cells
			dataRow.createCell(10).setCellValue(branch.getName()); // Domain Name
			dataRow.createCell(11).setCellValue(branch.getGuid()); // Domain GUID
	        
			int t = 0;
			int refColumnHead = 0;
	        for(Node instance:instanceRoleMap.keySet()){
	        	int cellColumn = t+repeatingColumnStartCell;
	        	Cell instanceCell = dataRow.createCell(cellColumn);
	        	instanceCell.setCellValue(instance.getName());
	        	
	        	t++;
	        	cellColumn++;
	        	Cell idCell = dataRow.createCell(cellColumn);
	        	idCell.setCellValue(instance.isDcsNode()?"0":"96");
	        	
	        	String header = getColumbHeader(cellColumn+1);
	        	// ***since row starts from 0 the formular rowCounter need to be increased 1
	        	int roleCounter = instanceRoleMap.get(instance).size() +3;
	        	String formular = "vlookup(" + header + (rowCounter + 1) + ", reference!$" + getColumbHeader(refColumnHead) + "$3:$" + getColumbHeader(refColumnHead+1) + "$" + roleCounter + ", 2, FALSE)";
	        	idCell.setCellFormula(formular);
	        	
	        	sheet.setColumnHidden(idCell.getColumnIndex(), true);
	        	
	        	t++;
	        	cellColumn++;
	        	Cell roleCell = dataRow.createCell(cellColumn);
	        	int module = cellColumn % 3;
	        	if(module == 2)
	        		cellRangeListMap.get(instance).addCellRangeAddress(roleCell.getRowIndex(), roleCell.getColumnIndex(), roleCell.getRowIndex(), roleCell.getColumnIndex());
	        	roleCell.setCellValue(instance.isDcsNode()?ApplicationUtil.getMessage("page.label.no.access"):"Connected Access");
	        	t++;
	        	refColumnHead += 2;
	        }
	        rowCounter++;
        }
        
        /*
         * Setting validators for the sheet
         * 
         */
        DataValidationHelper validationHelper = new XSSFDataValidationHelper(sheet);
        
        DataValidationConstraint constraintYesNoType = validationHelper.createExplicitListConstraint(yesNoList);
        DataValidation dataValidationYesNoType = validationHelper.createValidation(constraintYesNoType, cellRangeYesNoList);
        dataValidationYesNoType.setSuppressDropDownArrow(true); 
        sheet.addValidationData(dataValidationYesNoType);

        DataValidationConstraint idpProviderType = validationHelper.createExplicitListConstraint(IDProviderTypeList);
        DataValidation dataValidationIdpProviderType = validationHelper.createValidation(idpProviderType, cellRangeIDPTypeList);
        dataValidationIdpProviderType.setSuppressDropDownArrow(true);
       	sheet.addValidationData(dataValidationIdpProviderType);
       	
       	for(String actionString:cellRangeActionListMap.keySet()){
       		if(cellRangeActionListMap.get(actionString).getSize() == 0)
       			continue;
       		CellRangeAddressList rangeList = cellRangeActionListMap.get(actionString);
       		if(rangeList.countRanges() == 0)
       			continue;
       		String[] list = actionListMap.get(actionString);
       		DataValidationConstraint actionType = validationHelper.createExplicitListConstraint(list);
       		
            DataValidation dataValidationActionType = validationHelper.createValidation(actionType, rangeList);
            dataValidationActionType.setSuppressDropDownArrow(true);
            sheet.addValidationData(dataValidationActionType);
       	}
       	
       	for (Node instance : cellRangeListMap.keySet()){
       		if(instanceRoleMap.get(instance).isEmpty())
       			continue;
       		String[] roleList = new String[instanceRoleMap.get(instance).size()+1];
       		roleList[0] = ApplicationUtil.getMessage("page.label.no.access");
       		for(int i=1; i <= instanceRoleMap.get(instance).size(); i++){
       			roleList[i] = instanceRoleMap.get(instance).get(i-1).getName();
       		}
       		DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(roleList);
            DataValidation validation = validationHelper.createValidation(constraint, cellRangeListMap.get(instance));
            validation.setSuppressDropDownArrow(true);
           	sheet.addValidationData(validation);
       	}

       	for(int i = (repeatingColumnStartCell + (instanceRoleMap.keySet().size() * 3)); i < sheet.getRow(7).getLastCellNum(); i++){
        	sheet.setColumnHidden(i, true);
        }
       	sheet.setColumnWidth(0, 9000);
		sheet.setColumnWidth(1, 9000);
       	sheet.setColumnHidden(10, true);
       	sheet.setColumnHidden(11, true);
    }
    
    private String getColumbHeader(int columnNum){
    	String header = "A";
    	String alphabet = "abcdefghijklmnopqrstuvwxyz".toUpperCase();
    	if(columnNum < 26){
    		return alphabet.substring(columnNum, columnNum+1);
    	}else{
    		String later = "A";
    		int module = columnNum % 26;
    		if(module > 0){
    			later = alphabet.substring(module, module+1);
    		}
    		int headerPos = columnNum / 26;
    		header = alphabet.substring(headerPos-1, headerPos) + later;
    	}
    	return header;
    }
	private void getActionList(Map<String, CellRangeAddressList> cellRangeActionListMap, Map<String, String[]> actionListMap) {
		
		String[] activateList = new String[3];
		activateList[0] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE);
		activateList[1] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_UPDATE);
		activateList[2] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_ACTIVATE);
		
		actionListMap.put(ApplicationUtil.getMessage("page.label.disabled"), activateList);
		actionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.1")), activateList);
		actionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.2")), activateList);
		actionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.3")), activateList);
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.disabled"), new CellRangeAddressList());
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.1")), new CellRangeAddressList());
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.2")), new CellRangeAddressList());
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.inactive").concat(" - " + ApplicationUtil.getMessage("user.deactivate.reason.3")), new CellRangeAddressList());
		
		String[] deactivateList = new String[5];
		deactivateList[0] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE);
		deactivateList[1] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_UPDATE);
		deactivateList[2] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_DEACTIVATE);
		deactivateList[3] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_RESET_PASSWORD);
		deactivateList[4] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_SOFT_DEACTIVATE);
		actionListMap.put(ApplicationUtil.getMessage("page.label.active"), deactivateList);
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.active"), new CellRangeAddressList());
		
		String[] pendingList = new String[5];
		pendingList[0] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE);
		pendingList[1] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_UPDATE);
		pendingList[2] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_DEACTIVATE);
		pendingList[3] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_RESEND_ACTIVATION);
		pendingList[4] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_SOFT_DEACTIVATE);
		actionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.activation.pending")), pendingList);
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.activation.pending")), new CellRangeAddressList());
		
		String[] softdeactivateList = new String[4];
		softdeactivateList[0] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE);
		softdeactivateList[1] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_UPDATE);
		softdeactivateList[2] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_DEACTIVATE);
		softdeactivateList[3] = ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_RESET_PASSWORD);
		actionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.soft.deactivated")), softdeactivateList);
		actionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.reset.password.pending")), softdeactivateList);
		actionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.forget.password.pending")), deactivateList);
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.soft.deactivated")), new CellRangeAddressList());
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.reset.password.pending")), new CellRangeAddressList());
		cellRangeActionListMap.put(ApplicationUtil.getMessage("page.label.active").concat(" - " + ApplicationUtil.getMessage("page.label.forget.password.pending")), new CellRangeAddressList());
	}

	public boolean getIncludeImagePathOnly()
	{
		return includeImagePathOnly;
	}
	
	public List<String> getOriginalImageFiles()
	{
		return originalImageFiles;
	}
	
    @Override
	public void validate(ServiceExecutionContext context) 
    {
    }
    
    public static ServiceExecutionContext createContext(String exportId, long imageId, ExportImportOptions exportOptions, User requestor) 
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToExcelServiceRequest request = new ExportMessagepointObjectToExcelServiceRequest(exportId, imageId, null, requestor, exportOptions);

        context.setRequest(request);

        ExportMessagepointObjectToExcelServiceResponse serviceResp = new ExportMessagepointObjectToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}
