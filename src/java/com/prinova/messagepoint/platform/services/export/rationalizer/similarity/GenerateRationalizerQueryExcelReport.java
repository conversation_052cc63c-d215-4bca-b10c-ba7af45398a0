package com.prinova.messagepoint.platform.services.export.rationalizer.similarity;

import ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.LOWERCASE_METADATA_CONNECTORS_NOT_SENT_TO_ELASTICSEARCH;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.DOT_PATH_SEPARATOR_REGEX;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.extractRawContentSourceList;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.getFirstFieldValueOrDefault;
import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

public class GenerateRationalizerQueryExcelReport {
    private final int MAX_ROWS = 500_000;
    public static final int MAX_EXCEL_CELL_LENGTH = 32766;
    private RationalizerQuery query;
    private List<RationalizerContent> reportContentsList;
    private String exportName;
    private File outputDirectory;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;
    private String schemaName;
    private XSSFWorkbook workbook;
    private XSSFSheet sheet;
    private Row finishedTimeRow;
    private  List<String> parsedContentMetadataDefinitions = new LinkedList<>();
    int lastHeadColumnNo;

    GenerateRationalizerQueryExcelReport(RationalizerQuery query,
                                         List<RationalizerContent> reportContentsList,
                                         String exportName,
                                         File outputDirectory,
                                         StatusPollingBackgroundTask statusPollingBackgroundTask,
                                         String schemaName) {
        this.query = query;
        this.reportContentsList = reportContentsList;
        this.exportName = exportName;
        this.outputDirectory = outputDirectory;
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
        this.schemaName = schemaName;
    }

    public void invoke() throws IOException {
        int fileNo = 1;
        createSessionToDatabase();
        generateWorkbookSheetWithHeaders(fileNo);
        int rowCounter = 5;
        int contentNo = 0;
        for (RationalizerContent content : reportContentsList) {
            contentNo++;
            if (content == null) {
                continue;
            }
            rowCounter = createAndStyleSearchedContentRow(workbook, sheet, rowCounter, content);
            rowCounter = addSimilarityContentRowsForSearchedContent(sheet, rowCounter, query, content);
            rowCounter++;

            updateStatusPollingProgress(contentNo);

            if (rowCounter > MAX_ROWS) {
                finishedTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()).concat("  - Total rows " + rowCounter));
                writeToExcelFile(exportName + "_" + fileNo, outputDirectory, workbook);
                fileNo++;
                generateWorkbookSheetWithHeaders(fileNo);
                rowCounter = 5;
            }
        }
        finishedTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()).concat("  - Total rows " + rowCounter));
        writeToExcelFile(exportName + "_" + fileNo, outputDirectory, workbook);
    }

    private void updateStatusPollingProgress(int contentNo) {
        statusPollingBackgroundTask.setProgressInPercentInThread(10 + contentNo * 90 / reportContentsList.size());
        statusPollingBackgroundTask.save();
    }

    private void generateWorkbookSheetWithHeaders(int fileNo) {
        workbook = new XSSFWorkbook();
        sheet = workbook.createSheet(query.getName().replaceAll("[^a-zA-Z0-9.-]", "_") + "_" + fileNo);
        finishedTimeRow = generateSimilarityReportHeaders(query.getName(), query.getRationalizerApplication().getId(), workbook, sheet);
    }

    private void createSessionToDatabase() {
        SessionFactory sessionFactory = HibernateUtil.getManager().getSessionFactory();
        MessagepointCurrentTenantIdentifierResolver.setTenantIdentifier(schemaName);
        Session session = HibernateUtil.getManager().openSessionInView();
        if (!TransactionSynchronizationManager.hasResource(sessionFactory)) {
            TransactionSynchronizationManager.bindResource(sessionFactory, new SessionHolder(session));
        }
    }

    private void writeToExcelFile(String exportName, File outputDirectory, XSSFWorkbook workbook) throws IOException {
        String outputFilePath = outputDirectory.getPath() + File.separator + exportName + ".xlsx";
        File file = new File(outputFilePath);
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
    }

    private int addSimilarityContentRowsForSearchedContent(XSSFSheet sheet, int rowCounter, RationalizerQuery query, RationalizerContent content) throws IOException {
        rowCounter++;
        RationalizerApplication rationalizerApplication = query.getRationalizerApplication();
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication);

        int appSyncStatus = rationalizerApplication.getAppSyncStatus();
        if (RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == appSyncStatus) {
            throw new IOException("Report can not be generated. Application is not synchronized with elastic or elastic server is down.");
        }
        JsonArray jsonArray;
        if (query.getExactMatchesOnly()) {
            jsonArray = rationalizerElasticSearchHandler.searchForDuplicates(
                    content.getHashCode(), null
            );
            jsonArray = extractRawContentSourceList(jsonArray);
        } else {
            String contentText = StringUtils.isEmpty(content.getTextContent()) ? StringUtils.EMPTY : content.getTextContent();
            jsonArray = rationalizerElasticSearchHandler.searchByText(contentText,
                    (float) query.getComparisonThreshold() / 100, (float) query.getComparisonThresholdMax() / 100);

        }
        if (jsonArray == null || jsonArray.isEmpty()) {
            return rowCounter;
        }

        Map<String, Float> sortedMap = ElasticSearchQueryUtil.constructElasticSearchGuidToScoreMapForSimilarities(jsonArray).entrySet().stream()
                .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));

        if (MapUtils.isEmpty(sortedMap)) {
            return rowCounter;
        }

        final int listBatchSize = 25;
        List<String> guidList = new ArrayList<>(sortedMap.keySet());
        Map<String, JsonObject> contentsMap = new HashMap<>();

        for (int i = 0; i < guidList.size(); i = i + listBatchSize) {
            int rightMargin = i + listBatchSize > guidList.size() ? guidList.size() : i + listBatchSize;
            List<String> crtGuidBatch = guidList.subList(i, rightMargin);
            JsonArray responseJsonArray = rationalizerElasticSearchHandler.getContentsByTerms("id",
                    crtGuidBatch, Arrays.asList("id", "text", "htmltext", "metadata", "document.attribute.name"));
            if (responseJsonArray != null && !responseJsonArray.isEmpty()) {
                responseJsonArray.forEach(crt -> {
                    JsonObject crtJsonObject = crt.getAsJsonObject().getAsJsonObject("_source");
                    String id = ApplicationUtils.getValueForJsonPath(crtJsonObject, "id", String.class);
                    contentsMap.put(id, crtJsonObject);
                });
            }
        }
        for (String rowContentGuid : guidList) {
            rowCounter = addRow(sheet, rowCounter, contentsMap.get(rowContentGuid), sortedMap.get(rowContentGuid), content.getHashCode());
        }

        return rowCounter;
    }

    private int addRow(XSSFSheet sheet, int rowCounter, JsonObject rowContentJson, Float rowContentScore, String initialContentHash) {
        if (rowContentJson == null) {
            return rowCounter;
        }
        int firstRowNo = rowCounter;
        String documentName = getFirstFieldValueOrDefault(rowContentJson, "document.attribute.name", DOT_PATH_SEPARATOR_REGEX, "");
        String contentText = ApplicationUtils.getValueForJsonPath(rowContentJson, "text", String.class);
        contentText = contentText.replace("&nbsp;", " ");
        String htmlText = ApplicationUtils.getValueForJsonPath(rowContentJson, "htmltext", String.class);
        htmlText = htmlText.replace("&nbsp;", " ");
        String currentHash = ElasticsearchContentUtils.calculateContentHash(contentText);

        final String text = contentText;
        int limitText = (text.length() % MAX_EXCEL_CELL_LENGTH) == 0 ? text.length() / MAX_EXCEL_CELL_LENGTH : (text.length() / MAX_EXCEL_CELL_LENGTH) + 1;
        List<String> cellTexts = IntStream.iterate(0, i -> i + MAX_EXCEL_CELL_LENGTH).limit(limitText)
                .mapToObj(i -> text.substring(i, Math.min(text.length() , i + MAX_EXCEL_CELL_LENGTH)))
                .collect(Collectors.toList());
        final String html = htmlText;
        int limitHtml = (htmlText.length() % MAX_EXCEL_CELL_LENGTH) == 0 ? htmlText.length() / MAX_EXCEL_CELL_LENGTH : (htmlText.length() / MAX_EXCEL_CELL_LENGTH) + 1;
        List<String> cellsHtml = IntStream.iterate(0, i -> i + MAX_EXCEL_CELL_LENGTH).limit(limitHtml)
                .mapToObj(i -> html.substring(i, Math.min(html.length() , i + MAX_EXCEL_CELL_LENGTH)))
                .collect(Collectors.toList());
        for(int i = 0; i < cellsHtml.size(); i++) {
            Row contentRow = sheet.createRow(rowCounter);
            if(i==0) {
                contentRow.createCell(2).setCellValue(documentName);
            }
            Cell textCell = contentRow.createCell(3);
            if(i >= cellTexts.size()) {
                textCell.setCellValue("");
            } else {
                textCell.setCellValue(cellTexts.get(i));
            }
            Cell htmlCell = contentRow.createCell(4);
            htmlCell.setCellValue(StringUtils.isNotEmpty(cellsHtml.get(i)) ? cellsHtml.get(i) : "");

            float score = rowContentScore * 100;
            if(i==0) {
                if(query.getExactMatchesOnly()) {
                    contentRow.createCell(5).setCellValue(String.format("%.1f", score) + "%");
                } else if(score == 100.0) {
                    if(currentHash.equals(initialContentHash)) {
                        contentRow.createCell(5).setCellValue(String.format("%.1f", score) + "%");
                    } else {
                        contentRow.createCell(5).setCellValue(String.format("~%.1f", score) + "%");
                    }
                } else {
                    contentRow.createCell(5).setCellValue(String.format("%.1f", score) + "%");
                }
                int cellId = 6;

                for(String definition : parsedContentMetadataDefinitions) {
                    String currentValue = StringUtils.EMPTY;
                    JsonElement jsonElement = ApplicationUtils.getJsonElementForJsonPath(rowContentJson, "metadata." + definition);
                    if(jsonElement != null && jsonElement != JsonNull.INSTANCE) {
                        currentValue = getValue(jsonElement);
                    }
                    contentRow.createCell(cellId).setCellValue(StringUtils.isNotEmpty(currentValue) ? currentValue : StringUtils.EMPTY);
                    cellId++;
                }
            }
            rowCounter++;
        }
        if(cellsHtml.size() > 1) {
            for(int j = 1; j <= lastHeadColumnNo; j++) {
                if(j == 3) {
                    continue;
                }
                if(j == 4) {
                    continue;
                }

                sheet.addMergedRegion(new CellRangeAddress(firstRowNo, rowCounter - 1, j, j));
            }
        }

        return rowCounter;
    }

    private String getValue(JsonElement jsonElement) {
        String currentValue;
        if (jsonElement instanceof JsonArray) {
            Set<String> elemValues = new HashSet<>();
            for (JsonElement element : jsonElement.getAsJsonArray()) {
                elemValues.add(element.getAsString());
            }
            currentValue = String.join("; ", elemValues);
        } else {
            currentValue = jsonElement.getAsString();
        }
        return currentValue;
    }

    private int createAndStyleSearchedContentRow(XSSFWorkbook workbook, XSSFSheet sheet, int rowCounter, RationalizerContent content) {
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
        contentStyle.setFillBackgroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
        contentStyle.setFillPattern(SOLID_FOREGROUND);
        String contentText = content.getTextContent();
        contentText = contentText.replace("&nbsp;", " ");
        final String text = contentText;
        int limit = (text.length() % MAX_EXCEL_CELL_LENGTH) == 0 ? text.length() / MAX_EXCEL_CELL_LENGTH : (text.length() / MAX_EXCEL_CELL_LENGTH) + 1;
        List<String> cellTexts = IntStream.iterate(0, i -> i + MAX_EXCEL_CELL_LENGTH).limit(limit)
                .mapToObj(i -> text.substring(i, Math.min(text.length() , i + MAX_EXCEL_CELL_LENGTH)))
                .collect(Collectors.toList());
        for(int i = 0; i < cellTexts.size(); i++) {
            Row row = sheet.createRow(rowCounter);
            row.setRowStyle(contentStyle);
            Cell textCell = row.createCell(1);
            textCell.setCellStyle(contentStyle);
            textCell.setCellValue(StringUtils.isNotEmpty(cellTexts.get(i)) ? cellTexts.get(i) : "");
            rowCounter++;
       }
        return rowCounter;
    }

    private Row generateSimilarityReportHeaders(String queryName, long appId, XSSFWorkbook workbook, XSSFSheet sheet) {
        Row queryRow = sheet.createRow(0);
        queryRow.createCell(1).setCellValue("Query ");
        queryRow.createCell(2).setCellValue(queryName);
        Row requestTimeRow = sheet.createRow(1);
        requestTimeRow.createCell(1).setCellValue("Report requested");
        requestTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        Row finishedTimeRow = sheet.createRow(2);
        finishedTimeRow.createCell(1).setCellValue("Report finished");
        Row headerRow = sheet.createRow(3);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFillPattern(SOLID_FOREGROUND);
        style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
        style.setFillBackgroundColor(IndexedColors.SKY_BLUE.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        headerRow.setRowStyle(style);
        headerRow.createCell(1).setCellValue("Searched content");
        headerRow.getCell(1).setCellStyle(style);
        headerRow.createCell(2).setCellValue("Letter");
        headerRow.getCell(2).setCellStyle(style);
        headerRow.createCell(3).setCellValue("Text");
        headerRow.getCell(3).setCellStyle(style);
        headerRow.createCell(4).setCellValue("Markup");
        headerRow.getCell(4).setCellStyle(style);
        headerRow.createCell(5).setCellValue("Similarity");
        headerRow.getCell(5).setCellStyle(style);
        RationalizerApplication app = RationalizerApplication.findById(appId);
        int headerColumnCount = 6;
        if (app != null) {
            if (app.getParsedContentFormDefinition() != null) {
                List<MetadataFormItemDefinition> formItemDefinitionsInAlphabeticalOrder = app.getParsedContentFormDefinition().getAllMetadataFormItemDefinitionsInAlphabeticalOrder();
                formItemDefinitionsInAlphabeticalOrder.forEach(crt -> {
                    final String primaryConnector = crt.getPrimaryConnector();
                    if (StringUtils.isEmpty(primaryConnector)) {
                        return;
                    }

                    if ( LOWERCASE_METADATA_CONNECTORS_NOT_SENT_TO_ELASTICSEARCH.contains(primaryConnector.toLowerCase()) ) {
                        return;
                    }

                    parsedContentMetadataDefinitions.add(primaryConnector);
                });
            }
            for (String definition : parsedContentMetadataDefinitions) {
                headerRow.createCell(headerColumnCount).setCellValue(definition);
                headerRow.getCell(headerColumnCount).setCellStyle(style);
                headerColumnCount++;
            }
        }
        sheet.setColumnHidden(0, true);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 26000);
        sheet.setColumnWidth(3, 26000);
        sheet.setColumnWidth(4, 26000);
        lastHeadColumnNo = headerColumnCount - 1;
        return finishedTimeRow;
    }
}