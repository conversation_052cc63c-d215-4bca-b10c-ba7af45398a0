package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportRationalizerObjectBackgroundTask;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class GenerateRationalizerObjectExportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateRationalizerObjectExportService";
	
	private static final Log log = LogUtil.getLog(GenerateRationalizerObjectExportService.class);
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateRationalizerObjectExportServiceRequest request = (GenerateRationalizerObjectExportServiceRequest) context.getRequest();
			
			ExportRationalizerObjectBackgroundTask task = new ExportRationalizerObjectBackgroundTask(
																request.getExportId(),
																request.getTargetObjectId(), 
																request.getSelectedIds(),
																request.getExportOptions(), 
																request.getTargetClass(),
																request.getRequestingUser());
			MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY, request.getRequestingUser());

			GenerateRationalizerObjectExportServiceResponse response = (GenerateRationalizerObjectExportServiceResponse) context.getResponse();
			response.setBackgroundTaskId(task.getBackgroundTaskId());
			response.setResultValueBean(request.getExportId());

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateRationalizerObjectExportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(0);
		return createContext(exportId, targetObjectId, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType, int historyType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		exportOptions.setExportHistoryType(historyType);
		return createContext(exportId, targetObjectId, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, List<String> selectedIds, boolean includeImagePathOnly, Class<?> targetClass, User requestingUser, int exportType) {
		ExportImportOptions exportOptions = new ExportImportOptions();
		exportOptions.setIncludeImagePathOnly(includeImagePathOnly);
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, selectedIds, exportOptions, targetClass, requestingUser);
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {
		exportOptions.setExportExtType(0);
		return createContext(exportId, targetObjectId, null, exportOptions, targetClass, requestingUser);
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser, int exportType) {
		exportOptions.setExportExtType(exportType);
		return createContext(exportId, targetObjectId, null, exportOptions, targetClass, requestingUser);
	}

	public static ServiceExecutionContext createContext(String exportId, long targetObjectId, List<String> selectedIds, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateRationalizerObjectExportServiceRequest request = new GenerateRationalizerObjectExportServiceRequest();

		request.setExportId(exportId);
		request.setTargetObjectId(targetObjectId);
		request.setExportOptions(exportOptions);
		request.setTargetClass(targetClass);
		request.setRequestingUser(requestingUser);
		request.setSelectedIds(selectedIds);

		context.setRequest(request);

		GenerateRationalizerObjectExportServiceResponse response = new GenerateRationalizerObjectExportServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
	
	public static ServiceExecutionContext createContext(String exportId, ExportImportOptions exportOptions, Class<?> targetClass, User requestingUser) {

		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		GenerateRationalizerObjectExportServiceRequest request = new GenerateRationalizerObjectExportServiceRequest();

		request.setExportId(exportId);
		request.setExportOptions(exportOptions);
		request.setTargetClass(targetClass);
		request.setRequestingUser(requestingUser);

		context.setRequest(request);

		GenerateRationalizerObjectExportServiceResponse response = new GenerateRationalizerObjectExportServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}
}