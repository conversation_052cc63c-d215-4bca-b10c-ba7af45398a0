package com.prinova.messagepoint.platform.services.export;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.utils.SessionHelper;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.dom4j.DocumentFactory;
import org.dom4j.Element;
import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.imports.BulkUploadUsersUpdateService;


public class ExportUsersToXMLService extends AbstractService 
{
	public static final String SERVICE_NAME = "export.ExportUsersToXMLService";

	private static final Log log = LogUtil.getLog(ExportUsersToXMLService.class);
    private User requestor;

	public void execute(ServiceExecutionContext context) {
		try
		{
			validate(context);
			if (hasValidationError(context)) 
			{
				return;
			}

			poolPodMasterSession();
			ExportMessagepointObjectToXMLServiceRequest request = (ExportMessagepointObjectToXMLServiceRequest) context.getRequest();
	    	this.requestor = request.getRequestor();
	    	Node instance = Node.findById(request.getTargeObjectId());
            if(instance == null)
            	return;
	    	String exportName = instance.getName();
			org.dom4j.Document	document;
	    	DocumentFactory docFactory = DocumentFactory.getInstance(); 
			document = docFactory.createDocument();
			document.setXMLEncoding(ApplicationLanguageUtils.XML_ENCODING);
			Element rootElement = docFactory.createElement("MpBulkUploadDefinition");
			document.setRootElement(rootElement);

			HashMap<User, Element> userElements = new HashMap<>();
			HashMap<User, HashMap<Branch, Element>> userInstancesElements = new HashMap<>();
			HashMap<User, HashMap<Branch, Element>> userSubdomainsElements = new HashMap<>();
			
			generateXML(rootElement,
					userElements,
					userInstancesElements,
					userSubdomainsElements,
					instance,
					request.getObjectGuids());
			String filePath = ExportUtil.saveMessagepointObjectExportXMLToFile(document, requestor, ExportUtil.ExportType.USER, request.getExportId(), exportName);
			context.getResponse().setResultValueBean(request.getExportId());
			((ExportToXMLServiceResponse) context.getResponse()).setFilePath(filePath);

		}
		catch (Exception e)
		{
			log.error(" unexpected exception when invoking ExportUsersToXMLService execute method", e);
			this.getResponse(context).addErrorMessage(
			        "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
			        ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
					context.getLocale() );
			throw new RuntimeException(e.getMessage());
		}
		finally {
			SessionHelper.removeSession(null);
		}
	}

	private void poolPodMasterSession(){
		SessionHolder sessionHolder = null;
		try{
			sessionHolder = HibernateUtil.getManager().openTemporarySession(null);
			Session podMasterSession = HibernateUtil.getManager().getSession();
			SessionHelper.addSession(null, podMasterSession);
		}
		finally {
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}
	}

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}

	private void generateXML(Element rootElement,
							 HashMap<User, Element> userElements,
							 HashMap<User, HashMap<Branch, Element>> userInstancesElements,
							 HashMap<User, HashMap<Branch, Element>> userSubdomainsElements,
							 Node instance,
							 List<String> userGuids)
	{
		
		Branch branch = instance.getBranch();
		MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        rootElement.addAttribute("version", version);
        rootElement.addAttribute("build", buildVersion);
        rootElement.addAttribute("requestedby", requestor.getName());
        rootElement.addAttribute("requestdate", DateUtil.formatDateForXMLOutput(DateUtil.now()));
        rootElement.addAttribute("name", branch.getName());
        rootElement.addAttribute("guid", branch.getGuid());
		
		Element usersElm = rootElement.addElement("Users");

		List<User> users = UserUtil.getUsersForExportByRequestor(this.requestor, branch);;
		users = filterUsers(users, userGuids);

		for (User user : users) {
			if(user == null || (!user.isHomeDcsUser() && !user.isLocalSSOUser()))
        		continue;
        	buildUserElm(usersElm, user, branch, userElements, userInstancesElements, userSubdomainsElements);
        }

		// Create the root level branch elements
		for (User currentUser : userElements.keySet()) {
			createUserBranchElements(userElements.get(currentUser), currentUser, branch, userInstancesElements, userSubdomainsElements);
		}

		// Populate the root level branch info for all users
		fillBranchNodeElementsForUsers(branch, userElements, userInstancesElements);

		// Build the elements for each subdomain for all users
		buildAndPopulateSubdomains(branch, userElements, userInstancesElements, userSubdomainsElements);
	}

	private List<User> filterUsers(List<User> users, List<String> userGuids){
		if(users != null && !users.isEmpty() && userGuids != null && !userGuids.isEmpty()){
			return users.stream().filter(u -> userGuids.contains(u.getGuid())).collect(Collectors.toList());
		}

		return users;
	}

	private void buildUserElm(Element rootElement, User user, Branch branch, HashMap<User, Element> userElements,
							  HashMap<User, HashMap<Branch, Element>> userInstancesElements,
							  HashMap<User, HashMap<Branch, Element>> userSubdomainsElements){
		Element userElm = rootElement.addElement("User");
		
		Element guidElm = userElm.addElement("GUID");
		guidElm.addText(user.getGuid());
		
		Element idElm = userElm.addElement("ID");
		idElm.addText(String.valueOf(user.getId()));
		
		Element fisrtNameElm = userElm.addElement("FirstName");
		fisrtNameElm.addText(user.getFirstName());
		
		Element lastNameElm = userElm.addElement("LastName");
		lastNameElm.addText(user.getLastName());

		Element usernameElm = userElm.addElement("UserName");
		usernameElm.addText(user.getUsername());
		
		Element emailElm = userElm.addElement("Email");
		emailElm.addText(user.getEmail());
		
		Element idpTypeElm = userElm.addElement("IDProvider");
		idpTypeElm.addText(user.getIdPTypeDisplayName());
		
		Element statusElm = userElm.addElement("CurrentStatus");
		statusElm.addText(user.getUserPendingStatus(false));

		Element isSsoUserElm = userElm.addElement("IsSsoUser");
		isSsoUserElm.addText(user.isSSOUser()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));

		Element actionElm = userElm.addElement("Action");
		actionElm.addText(ApplicationUtil.getMessage(BulkUploadUsersUpdateService.MESSAGE_CODE_ACTION_NONE));
		
		//buildDomainElem(userElm, user, branch);
		userElements.put(user, userElm);
		userInstancesElements.put(user, new HashMap<>());
		userSubdomainsElements.put(user, new HashMap<>());

	}

	private void buildAndPopulateSubdomains(Branch branch, HashMap<User, Element> userElements,
											HashMap<User, HashMap<Branch, Element>> userInstancesElements,
											HashMap<User, HashMap<Branch, Element>> userSubdomainsElements) {
		// Populate the child level branch elements
		for (Branch child : branch.getAllChildren().stream().sorted(Comparator.comparing(Branch::getName)).collect(Collectors.toList())) {
			for (User user : userElements.keySet()) {
				createUserBranchElements(userSubdomainsElements.get(user).get(branch), user, child, userInstancesElements, userSubdomainsElements);
			}

			fillBranchNodeElementsForUsers(child, userElements, userInstancesElements);
			buildAndPopulateSubdomains(child, userElements, userInstancesElements, userSubdomainsElements);
		}
	}

	private void fillBranchNodeElementsForUsers(Branch branch, HashMap<User, Element> userElements,
												HashMap<User, HashMap<Branch, Element>> userInstancesElements) {

		LogUtil.getLog(ExportUsersToXMLService.class).trace(MessageFormat.format("fillBranchNodeElementsForUsers[branch.schemaName={0}]", branch.getDcsSchemaName()));


		populateDcsUserElements(branch, userElements, userInstancesElements);
		for(Node instance : branch.getAllAccessibleNodes(true, requestor.canAccessTestingInstances()).stream().sorted(Comparator.comparing(Node::getName)).collect(Collectors.toList())) // with DCS
		{
			SessionHolder mainSession = null;

			try {
				mainSession = HibernateUtil.getManager().openTemporarySession(instance.getSchemaName());


				for (User currentUser : userElements.keySet()) {
					populateUserDomainInstanceElem(currentUser, instance, userInstancesElements.get(currentUser).get(branch));
				}

			} finally {
				HibernateUtil.getManager().restoreSession(mainSession);
			}
		}
	}

	private void populateDcsUserElements(Branch branch, HashMap<User, Element> userElements,
										 HashMap<User, HashMap<Branch, Element>> userInstancesElements){
		SessionHolder mainSession = null;

		try {
			Node dcsNode = branch.getDcsNode();
			mainSession = HibernateUtil.getManager().openTemporarySession(dcsNode.getSchemaName());

			for (User currentUser : userElements.keySet()) {
				Element domainElem = userInstancesElements.get(currentUser).get(branch).getParent();
				User dcsUser = User.findByGuid(currentUser.getGuid());

				Element emailNotificationReceivingElm = domainElem.element("EmailNotificationReceiving");
				if(emailNotificationReceivingElm != null){
					emailNotificationReceivingElm.setText(dcsUser != null && dcsUser.isEmailNotifyRealTime()?ApplicationUtil.getMessage("page.label.yes"):ApplicationUtil.getMessage("page.label.no"));
				}
			}

		} finally {
			HibernateUtil.getManager().restoreSession(mainSession);
		}
	}

	private void populateUserDomainInstanceElem(User user, Node instance, Element domainElm) {

		LogUtil.getLog(ExportUsersToXMLService.class).trace(MessageFormat.format("populateUserDomainInstanceElem[user={0}, node={1}]", user.getUsername(), instance.getSchemaName()));

		User nodeUser = null;
		User homeDCSUser = user.getHomeDcsUser();

		if(homeDCSUser != null)
			nodeUser = User.findByGuid(homeDCSUser.getGuid(), instance);
		Set<Role> roles = null;
		if(nodeUser != null){
			roles = UserUtil.getNodeUserRolesByUserID(nodeUser.getId(), instance.getSchemaName());
		}
		Element instanceElm = domainElm.addElement("Instance");
		if(nodeUser != null){
			instanceElm.addAttribute("enabled", Boolean.valueOf(nodeUser.isAccountEnabled()).toString());
		}

		if (roles != null && !roles.isEmpty()) {
			for (Role role : roles.stream().sorted(Comparator.comparingLong(Role::getId)).collect(Collectors.toList())) {
				Element roleElement = instanceElm.addElement("Role");
				roleElement.addAttribute("id", String.valueOf(role.getId()));
				roleElement.addAttribute("name", role.getName());
			}
		} else {
			Element roleElement = instanceElm.addElement("Role");
			roleElement.addAttribute("id", String.valueOf(0L));
			roleElement.addAttribute("name", ApplicationUtil.getMessage("page.label.no.access"));
		}

		instanceElm.addAttribute("name", instance.getName());

		if(nodeUser != null){
			Element workgroupElm = instanceElm.addElement("Workgroup");
			workgroupElm.addAttribute("id", String.valueOf(nodeUser.getWorkgroupId()));
			if(nodeUser.getWorkgroupId() > 0){
				workgroupElm.addAttribute("name", Workgroup.findByIdAndNode(nodeUser.getWorkgroupId(), instance.getSchemaName()).getName());
			}
		}
	}

	private void createUserBranchElements(Element rootElement, User user, Branch branch,
										  HashMap<User, HashMap<Branch, Element>> userInstancesElements,
										  HashMap<User, HashMap<Branch, Element>> userSubdomainsElements) {
		LogUtil.getLog(ExportUsersToXMLService.class).trace(MessageFormat.format("createUserBranchElements[user={0}, branch.schemaName={1}]", user.getUsername(), branch.getDcsSchemaName()));

		Element domainElm = rootElement.addElement("Domain");

		Element nameElm = domainElm.addElement("Name");
		nameElm.addText(branch.getName());
		Element guidElm = domainElm.addElement("GUID");
		guidElm.addText(branch.getGuid());

		domainElm.addElement("EmailNotificationReceiving");

		Element instancesElem = domainElm.addElement("Instances");
		userInstancesElements.get(user).put(branch, instancesElem);

		Element subdomainsElem = domainElm.addElement("Subdomains");
		userSubdomainsElements.get(user).put(branch, subdomainsElem);
	}
	
	public static ServiceExecutionContext createContext(String exportId, long targeObjectId, ExportImportOptions exportOptions, User requestor, List<String> objectGuids)
    {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportMessagepointObjectToXMLServiceRequest request = new ExportMessagepointObjectToXMLServiceRequest(exportId, targeObjectId, requestor, exportOptions);
		if(objectGuids != null && !objectGuids.isEmpty()){
			request.setObjectGuids(objectGuids);
		}
        context.setRequest(request);

        ExportToXMLServiceResponse serviceResp = new ExportToXMLServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}
