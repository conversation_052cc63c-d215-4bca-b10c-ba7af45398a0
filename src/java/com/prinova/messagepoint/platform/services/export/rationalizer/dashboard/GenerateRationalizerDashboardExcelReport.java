package com.prinova.messagepoint.platform.services.export.rationalizer.dashboard;

import ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.DOT_PATH_SEPARATOR_REGEX;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.getFirstFieldValueOrDefault;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MAX_SIMILARITY;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MIN_SIMILARITY_FOR_DASHBOARD;
import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

public class GenerateRationalizerDashboardExcelReport {
    public static final int MAX_EXCEL_CELL_LENGTH = 32766;
    private static final float NINETY_NINE_POINT_NINE = 99.9f;
    public static final int SIZE = 1000;
    private final int MAX_ROWS = 500000;
    private RationalizerApplication rationalizerApplication;
    private List<ReportContentDTO> reportContentsList;
    private String exportName;
    private File outputDirectory;
    private RationalizerDashboardExportType dashboardExportType;
    private StatusPollingBackgroundTask statusPollingBackgroundTask;
    private Row finishedTimeRow;
    private List<MetadataFormItemDefinition> appMetaAlphabeticalOrdered = new ArrayList<>();
    int lastHeadColumnNo;
    private List<Map<String, String>> targetMetadataFilterMap;
    private List<DashboardFilter> dashboardFilters;
    private static final Log log = LogUtil.getLog(GenerateRationalizerDashboardExcelReport.class);

    GenerateRationalizerDashboardExcelReport(RationalizerApplication rationalizerApplication,
                                             List<ReportContentDTO> reportContentsList,
                                             String exportName,
                                             File outputDirectory,
                                             RationalizerDashboardExportType dashboardExportType,
                                             String sourceSelectionBranches,
                                             String targetSelectionBranches,
                                             StatusPollingBackgroundTask statusPollingBackgroundTask) {
        this.rationalizerApplication = rationalizerApplication;
        this.reportContentsList = reportContentsList;
        this.exportName = exportName;
        this.outputDirectory = outputDirectory;
        this.dashboardExportType = dashboardExportType;
        this.statusPollingBackgroundTask = statusPollingBackgroundTask;
        appMetaAlphabeticalOrdered = getParsedContentMetadataDefinitionsAlphabetically(rationalizerApplication);
        targetMetadataFilterMap = RationalizerUtil.processDashboardTreeSelections(rationalizerApplication.getId(), targetSelectionBranches);
        dashboardFilters = rationalizerApplication.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
    }
    private List<MetadataFormItemDefinition> getParsedContentMetadataDefinitionsAlphabetically(RationalizerApplication app) {
        if (app.getParsedContentFormDefinition() != null) {
            return app.getParsedContentFormDefinition().getAllMetadataFormItemDefinitionsInAlphabeticalOrder();
        }
        return  new ArrayList<>();
    }

    public void invoke() throws IOException {
        AtomicInteger batchNo = new AtomicInteger(0);
        List<List<ReportContentDTO>> batchList = ListUtils.partition(reportContentsList, SIZE);
        log.debug("GenerateRationalizerDashboardExcelReport no of Excel files " + batchList.size());
        batchList.stream()
                .forEach(batch -> {
                    try {
                        processBatch(batchNo.getAndIncrement(), batch, batchList.size());
                    } catch(IOException | InvalidFormatException e) {
                        log.error("Error when generating Excel report for bartch no " + (batchNo.get() -1) + ". Error: "  + e.getMessage());
                    }
                });
    }

    private void processBatch(int batchNo, List<ReportContentDTO> batchReportContentsList, int totalNoOfBatches) throws IOException, InvalidFormatException {
        log.debug("Start Generate Rationalizer Dashboard Excel Report batch no  " + batchNo);
        int fileNo = 1;
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = generateWoorkbookSheetWithHeaders(workbook, batchNo, fileNo);
        int rowCounter = 5;
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication);
        for (ReportContentDTO content : batchReportContentsList) {
            if (content == null && StringUtils.isEmpty(content.getText())) {
                continue;
            }
            rowCounter = createAndStyleSearchedContentRow(workbook, sheet, rowCounter, content);
            rowCounter = addContentRowsForSearchedContent(rationalizerElasticSearchHandler, sheet, rowCounter, content);
            rowCounter++;
            if (rowCounter > MAX_ROWS) {
                finishedTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()).concat("  - Total rows " + rowCounter));
                writeToExcelFile(exportName + "_" + batchNo + "_" + fileNo, outputDirectory, workbook);
                fileNo++;
                sheet = generateWoorkbookSheetWithHeaders(workbook, batchNo, fileNo);
                rowCounter = 5;
            }
        }
        int progressInPercent = statusPollingBackgroundTask.getProgressInPercent();

        statusPollingBackgroundTask.setProgressInPercentInThread(progressInPercent +  90 /totalNoOfBatches);
        statusPollingBackgroundTask.save();
        finishedTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()).concat("  - Total rows " + rowCounter));
        writeToExcelFile(exportName + "_" + batchNo + "_" + fileNo, outputDirectory, workbook);
    }

    private void writeToExcelFile(String exportName, File outputDirectory, XSSFWorkbook workbook) throws IOException {
        log.info("Write report to Excel file " +  outputDirectory.getPath() + File.separator + exportName + ".xlsx");
        String outputFilePath = outputDirectory.getPath() + File.separator + exportName + ".xlsx";
        File file = new File(outputFilePath);
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
    }

    private XSSFSheet generateWoorkbookSheetWithHeaders(XSSFWorkbook workbook, int batchNo, int fileNo) {
        int pos = StringUtils.ordinalIndexOf(exportName, "_", 3);
        XSSFSheet sheet = workbook.createSheet(exportName.substring(0, pos) + "_" + batchNo + "_" + fileNo);
        finishedTimeRow = generateReportHeaders(workbook, sheet);
        return sheet;
    }
    private int addContentRowsForSearchedContent(RationalizerElasticSearchHandler rationalizerElasticSearchHandler, XSSFSheet sheet, int rowCounter, ReportContentDTO content) throws IOException {
        rowCounter++;

        int appSyncStatus = rationalizerApplication.getAppSyncStatus();
        if (RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == appSyncStatus) {
            throw new IOException("Report can not be generated. Application is not synchronized with Elastic or Elastic server is down.");
        }

        Map<String, Float> sortedMap = new HashMap<>();
        if (RationalizerDashboardExportType.DUPLICATES.equals(dashboardExportType)) {
            Collection<String> elasticSearchResultList = rationalizerElasticSearchHandler.searchDuplicatesIdsByHashWithCompareContext(
                    targetMetadataFilterMap,
                    content.getHash(),
                    dashboardFilters,
                    null
            );

            if (!CollectionUtils.isEmpty(elasticSearchResultList)) {
                sortedMap = elasticSearchResultList.stream().collect(Collectors.toMap(s -> s, s -> 1.0f));
            }

        } else {
            Map<String, Float> similaritiesMap = rationalizerElasticSearchHandler.getFromMatchesSimilarityFieldByContentIdAndDocumentFileNameWithCompareContext(
                    content.getElasticSearchGuid(), MIN_SIMILARITY_FOR_DASHBOARD, MAX_SIMILARITY, null, targetMetadataFilterMap, dashboardFilters);

            if (MapUtils.isNotEmpty(similaritiesMap)) {
                sortedMap = similaritiesMap.entrySet().stream()
                        .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
            }
        }
        if (MapUtils.isEmpty(sortedMap)) {
            return rowCounter;
        }

        final int listBatchSize = 25;
        List<String> guidList = new ArrayList<>(sortedMap.keySet());
        Map<String, JsonObject> contentsMap = new HashMap<>();

        for (int i = 0; i < guidList.size(); i = i + listBatchSize) {
            int rightMargin = i + listBatchSize > guidList.size() ? guidList.size() : i + listBatchSize;
            List<String> crtGuidBatch = guidList.subList(i, rightMargin);
            JsonArray responseJsonArray = rationalizerElasticSearchHandler.getContentsByTerms( "id",
                    crtGuidBatch, Arrays.asList("id", "text", "htmltext", "metadata", "document.attribute.name"));
            if (responseJsonArray != null && !responseJsonArray.isEmpty()) {
                responseJsonArray.forEach(crt -> {
                    JsonObject crtJsonObject = crt.getAsJsonObject().getAsJsonObject("_source");
                    String id = ApplicationUtils.getValueForJsonPath(crtJsonObject, "id", String.class);
                    contentsMap.put(id, crtJsonObject);
                });
            }
        }
        for (String rowContentGuid : guidList) {
            rowCounter = addRow(sheet, rowCounter, contentsMap.get(rowContentGuid), sortedMap.get(rowContentGuid));
        }

        return rowCounter;
    }

    private int addRow(XSSFSheet sheet, int rowCounter, JsonObject rowContentJson, Float rowContentScore) {
        if (rowContentJson == null) {
            return rowCounter;
        }
        int firstRowNo = rowCounter;
        String documentName = getFirstFieldValueOrDefault(rowContentJson, "document.attribute.name", DOT_PATH_SEPARATOR_REGEX, "");
        String contentText = ApplicationUtils.getValueForJsonPath(rowContentJson, "text", String.class);
        contentText = contentText.replace("&nbsp;", " ");
        String htmlText = ApplicationUtils.getValueForJsonPath(rowContentJson, "htmltext", String.class);
        htmlText = htmlText.replace("&nbsp;", " ");

        final String text = contentText;
        List<String> cellTexts = constructListOfTextForSplitCells(text, text);
        final String html = htmlText;
        List<String> cellsHtml = constructListOfTextForSplitCells(htmlText, html);
        for(int i = 0; i < cellsHtml.size(); i++) {
            Row contentRow = sheet.createRow(rowCounter);
            if(i==0) {
                contentRow.createCell(2).setCellValue(documentName);
            }
            Cell textCell = contentRow.createCell(3);
            if(i >= cellTexts.size()) {
                textCell.setCellValue("");
            } else {
                textCell.setCellValue(cellTexts.get(i));
            }
            Cell htmlCell = contentRow.createCell(4);
            htmlCell.setCellValue(StringUtils.isNotEmpty(cellsHtml.get(i)) ? cellsHtml.get(i) : "");

            if(i==0) {
                float score = rowContentScore * 100;
                int cellId = 5;
                if (RationalizerDashboardExportType.DUPLICATES.equals(dashboardExportType)) {
                    contentRow.createCell(5).setCellValue(String.format("%.1f", score) + "%");
                    cellId++;
                } else if (RationalizerDashboardExportType.SIMILARITY.equals(dashboardExportType)) {
                    if (score == 100.0) {
                        contentRow.createCell(5).setCellValue(String.format("%.1f",  NINETY_NINE_POINT_NINE) + "%");
                    } else {
                        contentRow.createCell(5).setCellValue(String.format("%.1f", score) + "%");
                    }
                    cellId++;
                }
                for (MetadataFormItemDefinition definition : appMetaAlphabeticalOrdered) {
                    String currentValue = StringUtils.EMPTY;
                    JsonElement jsonElement = ApplicationUtils.getJsonElementForJsonPath(rowContentJson, "metadata." + definition.getPrimaryConnector());
                    if (jsonElement != null && jsonElement != JsonNull.INSTANCE) {
                        currentValue = getValue(jsonElement);
                    }
                    contentRow.createCell(cellId).setCellValue(StringUtils.isNotEmpty(currentValue) ? currentValue : StringUtils.EMPTY);
                    cellId++;
                }
            }
            rowCounter++;
        }
        if(cellsHtml.size() > 1) {
            for(int j = 1; j <= lastHeadColumnNo; j++) {
                if(j == 3) {
                    continue;
                }
                if(j == 4) {
                    continue;
                }
                sheet.addMergedRegion(new CellRangeAddress(firstRowNo, rowCounter - 1, j, j));
            }
        }

        return rowCounter;
    }

    private List<String> constructListOfTextForSplitCells(String htmlText, String html) {
        int limitHtml = (htmlText.length() % MAX_EXCEL_CELL_LENGTH) == 0 ? htmlText.length() / MAX_EXCEL_CELL_LENGTH : (htmlText.length() / MAX_EXCEL_CELL_LENGTH) + 1;
        List<String> cellsHtml = IntStream.iterate(0, i -> i + MAX_EXCEL_CELL_LENGTH).limit(limitHtml)
                .mapToObj(i -> html.substring(i, Math.min(html.length() , i + MAX_EXCEL_CELL_LENGTH)))
                .collect(Collectors.toList());
        return cellsHtml;
    }

    private String getValue(JsonElement jsonElement) {
        String currentValue;
        if (jsonElement instanceof JsonArray) {
            Set<String> elemValues = new HashSet<>();
            for (JsonElement element : jsonElement.getAsJsonArray()) {
                elemValues.add(element.getAsString());
            }
            currentValue = String.join("; ", elemValues);
        } else {
            currentValue = jsonElement.getAsString();
        }
        return currentValue;
    }

    private int  createAndStyleSearchedContentRow(XSSFWorkbook workbook, XSSFSheet sheet, int rowCounter, ReportContentDTO content) {
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
        contentStyle.setFillBackgroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
        contentStyle.setFillPattern(SOLID_FOREGROUND);
        String contentText = content.getText();
        contentText = contentText.replace("&nbsp;", " ");
        final String text = contentText;
        List<String> cellTexts = constructListOfTextForSplitCells(text, text);
        for(int i = 0; i < cellTexts.size(); i++) {
            Row row = sheet.createRow(rowCounter);
            row.setRowStyle(contentStyle);
            Cell textCell = row.createCell(1);
            textCell.setCellStyle(contentStyle);
            textCell.setCellValue(StringUtils.isNotEmpty(cellTexts.get(i)) ? cellTexts.get(i) : "");
            rowCounter++;
        }

        return rowCounter;
    }

    private Row generateReportHeaders(XSSFWorkbook workbook, XSSFSheet sheet) {
        Row queryRow = sheet.createRow(0);
        queryRow.createCell(1).setCellValue("Dashboard report");
        queryRow.createCell(2).setCellValue(exportName);
        Row requestTimeRow = sheet.createRow(1);
        requestTimeRow.createCell(1).setCellValue("Report requested");
        requestTimeRow.createCell(2).setCellValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));
        Row finishedTimeRow = sheet.createRow(2);
        finishedTimeRow.createCell(1).setCellValue("Report finished");
        Row headerRow = sheet.createRow(3);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFillPattern(SOLID_FOREGROUND);
        style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
        style.setFillBackgroundColor(IndexedColors.SKY_BLUE.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        headerRow.setRowStyle(style);
        headerRow.createCell(1).setCellValue("Searched content");
        headerRow.getCell(1).setCellStyle(style);
        headerRow.createCell(2).setCellValue("Letter");
        headerRow.getCell(2).setCellStyle(style);
        headerRow.createCell(3).setCellValue("Text");
        headerRow.getCell(3).setCellStyle(style);
        headerRow.createCell(4).setCellValue("Markup");
        headerRow.getCell(4).setCellStyle(style);

        int headerColumnCount = 5;
        if (RationalizerDashboardExportType.DUPLICATES.equals(dashboardExportType) || RationalizerDashboardExportType.SIMILARITY.equals(dashboardExportType)) {
            headerRow.createCell(5).setCellValue("Similarity percent");
            headerRow.getCell(5).setCellStyle(style);
            headerColumnCount = 6;
        }

        for (MetadataFormItemDefinition definition : appMetaAlphabeticalOrdered) {
            headerRow.createCell(headerColumnCount).setCellValue(definition.getName());
            headerRow.getCell(headerColumnCount).setCellStyle(style);
            headerColumnCount++;
        }
        sheet.setColumnHidden(0, true);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 26000);
        sheet.setColumnWidth(3, 26000);
        sheet.setColumnWidth(4, 26000);
        lastHeadColumnNo = headerColumnCount - 1;

        return finishedTimeRow;
    }
}
