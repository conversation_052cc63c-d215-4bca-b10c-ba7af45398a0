package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExportMessageToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 132240516490390589L;

	private TouchpointSelection tpSelection;
	private int selectionStatus = -1;
	private List<ContentObject> messageslist = new ArrayList<>();
	private boolean dateFilterEnabled;
	private Date startDate;
	private boolean includeTargeting;
	private boolean includeContent;
	private boolean includeAllMessages;
	private User requestor;
	private int auditReportTypeId;
	private Document document;

	public TouchpointSelection getTpSelection() {
		return tpSelection;
	}
	public void setTpSelection(TouchpointSelection tpSelection) {
		this.tpSelection = tpSelection;
	}
	public int getSelectionStatus() {
		return selectionStatus;
	}
	public void setSelectionStatus(int selectionStatus) {
		this.selectionStatus = selectionStatus;
	}
	public List<ContentObject> getMessageslist() {
		return messageslist;
	}
	public void setMessageslist(List<ContentObject> messageslist) {
		this.messageslist = messageslist;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public boolean isIncludeTargeting() {
		return includeTargeting;
	}
	public void setIncludeTargeting(boolean includeTargeting) {
		this.includeTargeting = includeTargeting;
	}
	public boolean isIncludeContent() {
		return includeContent;
	}
	public void setIncludeContent(boolean includeContent) {
		this.includeContent = includeContent;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public boolean isDateFilterEnabled() {
		return dateFilterEnabled;
	}
	public void setDateFilterEnabled(boolean dateFilterEnabled) {
		this.dateFilterEnabled = dateFilterEnabled;
	}
	public int getAuditReportTypeId() {
		return auditReportTypeId;
	}
	public void setAuditReportTypeId(int auditReportTypeId) {
		this.auditReportTypeId = auditReportTypeId;
	}
	public boolean isIncludeAllMessages() {
		return includeAllMessages;
	}
	public void setIncludeAllMessages(boolean includeAllMessages) {
		this.includeAllMessages = includeAllMessages;
	}

    public void setDocument(Document document) {
        this.document = document;
    }
    public Document getDocument() {
        return document;
    }
}