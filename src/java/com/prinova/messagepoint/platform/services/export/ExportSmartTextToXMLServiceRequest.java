package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.ArrayList;
import java.util.List;

public class ExportSmartTextToXMLServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 132240516490390589L;

	private List<Long> list = new ArrayList<>();
	private User requestor;
	private long reportTimestamp;
	private int auditReportTypeId;
	
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public long getReportTimestamp() {
		return reportTimestamp;
	}
	public void setReportTimestamp(long reportTimestamp) {
		this.reportTimestamp = reportTimestamp;
	}
	public List<Long> getList() {
		return list;
	}
	public void setList(List<Long> list) {
		this.list = list;
	}
	public int getAuditReportTypeId() {
		return auditReportTypeId;
	}
	public void setAuditReportTypeId(int auditReportTypeId) {
		this.auditReportTypeId = auditReportTypeId;
	}
}