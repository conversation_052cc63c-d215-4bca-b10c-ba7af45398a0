package com.prinova.messagepoint.platform.services.export;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;

public class GenerateProjectAuditReportService extends AbstractService {

	public static final String SERVICE_NAME = "export.GenerateProjectAuditReportService";
	private static final Log log = LogUtil.getLog(GenerateProjectAuditReportService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			GenerateProjectAuditReportServiceRequest request = (GenerateProjectAuditReportServiceRequest) context.getRequest();

			ProjectAuditReportRunner projectExportRunner = new ProjectAuditReportRunner(request.getAuditReportId(),
                    request.getProjectList(),
                    request.isIncludeAllProjects(),
                    request.getRequestor());
	   		MessagePointRunnableUtil.startThread(projectExportRunner, Thread.MAX_PRIORITY);

		} catch (Exception e) {
			log.error(" unexpected exception when invoking GenerateProjectAuditReportService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	private static class ProjectAuditReportRunner extends MessagePointRunnable {

		private long auditReportId;
		private List<Long> projectList = new ArrayList<>();
		private boolean includeAllProjects;
		private User requestor;
		private long reportTimestamp = System.currentTimeMillis();
		
		public ProjectAuditReportRunner(long auditReportId, 
										List<Long> projectList,
										boolean includeAllProjects,
										User requestor) {
			
			this.auditReportId = auditReportId;
			this.setProjectList(projectList);
			this.setIncludeAllProjects(includeAllProjects);
			this.requestor = requestor;
		}

		@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
		public void performMainProcessing() {
			
			try {
				// *****************************************
				// ********* Generate XML ******************
				// *****************************************
		    	ServiceExecutionContext exportServiceContext = ExportProjectToXMLService.createContext(getProjectList(), 
		    																						   isIncludeAllProjects(),
		    																						   getRequestor(),
		    																						   reportTimestamp);

		    	Service exportService = MessagepointServiceFactory.getInstance().lookupService(ExportProjectToXMLService.SERVICE_NAME, ExportProjectToXMLService.class);
		    	exportService.execute(exportServiceContext);
		    	ServiceResponse serviceResponse = exportServiceContext.getResponse();
		    	String xmlFilePath = ((ExportToXMLServiceResponse)serviceResponse).getFilePath();
		    	if(xmlFilePath == null){
		    		// *********************************************************
					// ********* Update the AuditReport object *****************
					// *********************************************************

					ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForError(getAuditReportId());
					Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
					updateReportService.execute(updateReportServiceContext);
		    	}else{
		    		
			    	
					// ******************************************
					// ********* Generate XSLT ******************
					// ******************************************

			    	int auditReportType = AuditReportType.ID_PROJECT_AUDIT_REPORT;
			    	String xhtmlFilePath = ExportUtil.transformXML(xmlFilePath, getRequestor(), auditReportType, reportTimestamp);
			    	
					// *********************************************************
					// ********* Update the AuditReport object *****************
					// *********************************************************

					ServiceExecutionContext updateReportServiceContext = CreateOrUpdateAuditReportService.createContextForMarkComplete(getAuditReportId(), 
																																		auditReportType,
																																		xmlFilePath, 
																																		xhtmlFilePath);
					Service updateReportService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
					updateReportService.execute(updateReportServiceContext);
		    	}
		    	
		    	
			} catch (Throwable throwable) {
		    	// Throw ERROR.
				Log log = LogUtil.getLog(this.getClass());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				throwable.printStackTrace(pw);
				log.error("Project Audit Report Runner caught exception: " + sw);
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForError(this.auditReportId);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
			}
		}

		public long getAuditReportId() {
			return auditReportId;
		}

		@SuppressWarnings("unused")
		public void setAuditReportId(long auditReportId) {
			this.auditReportId = auditReportId;
		}

		
		public User getRequestor() {
			return requestor;
		}

		@SuppressWarnings("unused")
		public void setRequestor(User requestor) {
			this.requestor = requestor;
		}

		public List<Long> getProjectList() {
			return projectList;
		}

		public void setProjectList(List<Long> projectList) {
			this.projectList = projectList;
		}

		public boolean isIncludeAllProjects() {
			return includeAllProjects;
		}

		public void setIncludeAllProjects(boolean includeAllProjects) {
			this.includeAllProjects = includeAllProjects;
		}

	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(long auditReportId, List<Long> projectList,
			boolean includeAllProjects, User requestor) {
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		GenerateProjectAuditReportServiceRequest request = new GenerateProjectAuditReportServiceRequest();
		
		request.setAuditReportId(auditReportId);
		request.setIncludeAllProjects(includeAllProjects);
		request.setRequestor(requestor);
		request.setProjectList(projectList);
		context.setRequest(request);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}