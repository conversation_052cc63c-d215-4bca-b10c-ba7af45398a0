package com.prinova.messagepoint.platform.services.export;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.clickatell.ClickatellConfiguration;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.eMessaging.EMessagingConfiguration;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.ftp.FtpConfiguration;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormFieldSizeType;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.model.sendmail.SendmailConfiguration;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.model.targeting.*;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.util.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.dom4j.Element;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

// This class is a common class for generating the XML data for touchpoint 
// and touchpoint container export services.
public class ExportXMLUtils
{
    private static final Log log = LogUtil.getLog(ExportXMLUtils.class);
    private static final String ACCESSIBILITY = "accessibility";
    
	// class for converting percentage dimensions to absolute.
    static private class Block
    {
        int topx = 0;
        int topy = 0;
        int height = 0;
        int width = 0;

        int dbtopx = 0;
        int dbtopy = 0;
        int dbheight = 0;
        int dbwidth = 0;
        
        Block(Zone z, Document d)
        {
            if ( z.isEmailSubjectLine() )
                return;
            
            dbtopx = z.getTopX();
            dbtopy = z.getTopY();
            dbwidth = z.getWidth();
            dbheight = z.getHeight();
            
            int xPer = z.getTopX() / 1000000;
            int yPer = z.getTopY() / 1000000;
            int widthPer = z.getWidth() / 1000000;
            int heightPer = z.getHeight() / 1000000;
            
            topx = (int)Math.round((8500 * xPer) / 100.0);
            topy = (int)Math.round((11000 * yPer) / 100.0);
            width = (int)Math.round((8500 * widthPer) / 100.0);
            height = (int)Math.round((11000 * heightPer) / 100.0);
        }
        
        Block(ZonePart zp, Zone z, Document d)
        {
            Block base = new Block(z,d);
            
            dbtopx = zp.getTopX();
            dbtopy = zp.getTopY();
            dbwidth = zp.getWidth();
            dbheight = zp.getHeight();
            
            int xPer = zp.getTopX() / 1000000;
            int yPer = zp.getTopY() / 1000000;
            int widthPer = zp.getWidth() / 1000000;
            int heightPer = zp.getHeight() / 1000000;
            
            topx = (int)Math.round((base.width * xPer) / 100.0);
            topy = (int)Math.round((base.height * yPer) / 100.0);
            width = (int)Math.round((base.width * widthPer) / 100.0);
            height = (int)Math.round((base.height * heightPer) / 100.0);
        }
    }
    
    public static String encodeFile(String fileLocation)
    {
    	PathUtil path = new PathUtil(fileLocation, false);
        File sourceFile = path.file(false);

		try 
		{
			byte[] buf = FileUtils.readFileToByteArray(sourceFile);
			String encodedContent = Base64.encodeBase64String(buf);
			encodedContent = encodedContent.replace("\n", "");
			return encodedContent;
		} 
		catch (IOException e) 
		{
            log.error(" unexpected exception when invoking ExportXMLUtils.encodeFile(String fileLocation)", e);
		}

        return "";
    }
    
    public static String encodeContent(byte[] content)
    {
		String encodedContent = Base64.encodeBase64String(content);
		encodedContent = encodedContent.replace("\n", "");
		return encodedContent;
    }
    
    public static void createVersion( Element parent )
    {
        MessagepointLicenceManager mlt = MessagepointLicenceManager.getInstance();
        String buildVersion = mlt.getBuildRevision();
        String version = mlt.getVersion();
        
        Element ver = parent.addElement("Version");
        ver.addAttribute("version", version);
        ver.addAttribute("build", buildVersion);   
    }
    
    public static void createParagraphStyle( ParagraphStyle ps, Element paraStylesNode )
    {
        Element styleNode = paraStylesNode.addElement("Style");

        styleNode.addAttribute( "id", toString(ps.getId()) );
        styleNode.addAttribute( "name", ps.getName() );
        styleNode.addAttribute( "connectorname", ps.getConnectorName() );
        styleNode.addAttribute( "identifier", ps.getIdentifier() );
        styleNode.addAttribute( "alignment", ps.getAlignment().getCSSValue() );
        styleNode.addAttribute( "spacebefore", ps.getParagraphSpacingBeforeAsStringInInches() );
        styleNode.addAttribute( "spaceafter", ps.getParagraphSpacingAfterAsStringInInches() );
        try {
			styleNode.addAttribute( "precisionlinespace", DecimalValueUtil.dehydrate(ps.getLineSpacing()) );
		} catch (ParseException e) {
			log.error("Error: ", e);
		}
        styleNode.addAttribute( "linespacingtype", toString(ps.getLineSpacingType()) );
        styleNode.addAttribute( "indent", ps.getIndentAsString() );
        styleNode.addAttribute( "hanging", toString(ps.isHangingIndent()) );
        styleNode.addAttribute( "leftmargin", ps.getLeftMarginAsString() );
        styleNode.addAttribute( "rightmargin", ps.getRightMarginAsString() );
        styleNode.addAttribute( "bordertypeid", toString(ps.getBorderTypeId()) ); 
        styleNode.addAttribute( "borderwidth", ps.getBorderWidthAsString() );
        if (ps.getTextStyle() != null)
            styleNode.addAttribute( "textstylerefid", toString(ps.getTextStyle().getId()) );
        
        styleNode.addAttribute( "bulletedlistapplicable", toString(ps.isBulletedListApplicable()) );        	

        String taggingOverride = ps.getTaggingOverride(); 
        if (taggingOverride != null && !taggingOverride.isEmpty())
        {
        	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
        }

        styleNode.addAttribute("bulletSymbolId",     toString(ps.getBulletSymbolId()));
        styleNode.addAttribute("numberedListTypeId", toString(ps.getNumberedListTypeId()));

        styleNode.addAttribute("toggleAlignment",    toString(ps.isToggleAlignment()));
        styleNode.addAttribute("toggleLineSpacing",  toString(ps.isToggleLineSpacing()));
        if(ps.getToggleLineSpacingValues() != null && !ps.getToggleLineSpacingValues().isEmpty()) {
            styleNode.addAttribute("toggleLineSpacingValues",
                    ps.getToggleLineSpacingValues().stream().collect(Collectors.joining(",")));
        }
        styleNode.addAttribute("toggleLeftMargin",   toString(ps.isToggleLeftMargin()));
    }
    
    public static void createListStyle( ListStyle listStyle, Element listStylesNode )
    {
        Element styleNode = listStylesNode.addElement("Style");

        styleNode.addAttribute( "id", toString(listStyle.getId()) );
        styleNode.addAttribute( "name", listStyle.getName() );
        styleNode.addAttribute( "connectorname", listStyle.getConnectorName() );
        styleNode.addAttribute( "identifier", listStyle.getIdentifier() );
        
        styleNode.addAttribute( "alignment", new ParagraphAlignment(listStyle.getAlignmentId()).getCSSValue() );
        
        styleNode.addAttribute( "spacebefore", listStyle.getListSpacingBeforeAsString() );
        styleNode.addAttribute( "spaceafter", listStyle.getListSpacingAfterAsString() );
        styleNode.addAttribute( "spaceright", listStyle.getListSpacingRightAsString() );
        
        styleNode.addAttribute( "bordertypeid", toString(listStyle.getBorderTypeId()) ); 
        styleNode.addAttribute( "borderwidth",  listStyle.getBorderWidthAsString() );

        
        try {
			styleNode.addAttribute( "precisionlinespace", DecimalValueUtil.dehydrate(listStyle.getLineSpacing()) );
		} catch (ParseException e) {
			log.error("Error: ", e);
		}
        
        styleNode.addAttribute( "linespacingtype", toString(listStyle.getLineSpacingType()) );

        styleNode.addAttribute( "indent", listStyle.getIndentAsString() );
        styleNode.addAttribute( "hanging", toString(listStyle.isHangingIndent()) );
        
        styleNode.addAttribute( "bulletLeftMargin", listStyle.getBulletLeftMarginAsString() );
        styleNode.addAttribute( "bulletRightMargin", listStyle.getBulletRightMarginAsString() );
        styleNode.addAttribute( "bulletTopMargin", listStyle.getBulletTopMarginAsString() );
        styleNode.addAttribute( "bulletBottomMargin", listStyle.getBulletBottomMarginAsString() );
        
        if (listStyle.getTextStyle() != null)
            styleNode.addAttribute( "textstylerefid", toString(listStyle.getTextStyle().getId()) );

        String bulletSymbolOverrides = listStyle.getBulletSymbolOverrides();
    	if(bulletSymbolOverrides != null && !bulletSymbolOverrides.isEmpty()) 
    	{
        	styleNode.addElement("BulletSymbolOverrides").addCDATA(bulletSymbolOverrides);
    	}

        String bulletSpacingData = listStyle.getBulletSpacingData();
        if(bulletSpacingData != null && !bulletSpacingData.isEmpty())
        {
            styleNode.addElement("BulletSpacingData").addCDATA(bulletSpacingData);
        }

        String taggingOverride = listStyle.getTaggingOverride(); 
        if (taggingOverride != null && !taggingOverride.isEmpty())
        {
        	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
        }
    }
    
    public static void createFont( AbstractTextStyle style, Element fontsNode )
    {
        Element fontNode = fontsNode.addElement("Font");
        fontNode.addAttribute( "id", toString(style.getId()) );
        fontNode.addAttribute( "name", style.getFontName() );
        fontNode.addAttribute( "htmldescription", style.getWebFontName() );
        
        Element propNode = fontNode.addElement("Property");
        propNode.addAttribute( "id", toString(style.getId()) );
        try {
			propNode.addAttribute( "pointsize", DecimalValueUtil.dehydrate(style.getPointSize()) );
		} catch (ParseException e) {
			log.error("Error: ", e);
		}
        propNode.addAttribute( "bold", toString(style.isBold()) );
        propNode.addAttribute( "italics", toString(style.isItalic()) );
        propNode.addAttribute( "underline", toString(style.isUnderline()) );

        TextStyleFont textStyleFont = style.getTextStyleFont();
        if (textStyleFont != null)
        {
        	Element fontFileNode = fontNode.addElement("FontFile");
            fontFileNode.addAttribute("id", toString(textStyleFont.getId()));
            fontFileNode.addAttribute("guid", textStyleFont.getGuid());
        	fontFileNode.addElement("Name").addCDATA(textStyleFont.getName());
        	if(textStyleFont.getTtfFile() != null) {
        		fontFileNode.addAttribute("ttfrefid", toString(textStyleFont.getTtfFile().getId()));
        	}
        	if(textStyleFont.getEotFile() != null) {
        		fontFileNode.addAttribute("eotrefid", toString(textStyleFont.getEotFile().getId()));
        	}
            if(textStyleFont.getTtfBoldFile() != null) {
                fontFileNode.addAttribute("ttfbrefid", toString(textStyleFont.getTtfBoldFile().getId()));
            }
            if(textStyleFont.getTtfItalicFile() != null) {
                fontFileNode.addAttribute("ttfirefid", toString(textStyleFont.getTtfItalicFile().getId()));
            }
            if(textStyleFont.getTtfBoldItalicFile() != null) {
                fontFileNode.addAttribute("ttfbirefid", toString(textStyleFont.getTtfBoldItalicFile().getId()));
            }
        }
    }

    public static void createFont(TextStyleFont textStyleFont, Element fontsNode )
    {
        Element fontNode = fontsNode.addElement("Font");
        fontNode.addAttribute( "id", toString(textStyleFont.getId()) );
        fontNode.addAttribute( "name", textStyleFont.getName() );

        Element fontFileNode = fontNode.addElement("FontFile");
        fontFileNode.addAttribute("id", toString(textStyleFont.getId()));
        fontFileNode.addAttribute("guid", textStyleFont.getGuid());
        fontFileNode.addElement("Name").addCDATA(textStyleFont.getName());
        if(textStyleFont.getTtfFile() != null) {
            fontFileNode.addAttribute("ttfrefid", toString(textStyleFont.getTtfFile().getId()));
        }
        if(textStyleFont.getEotFile() != null) {
            fontFileNode.addAttribute("eotrefid", toString(textStyleFont.getEotFile().getId()));
        }
        if(textStyleFont.getTtfBoldFile() != null) {
            fontFileNode.addAttribute("ttfbrefid", toString(textStyleFont.getTtfBoldFile().getId()));
        }
        if(textStyleFont.getTtfItalicFile() != null) {
            fontFileNode.addAttribute("ttfirefid", toString(textStyleFont.getTtfItalicFile().getId()));
        }
        if(textStyleFont.getTtfBoldItalicFile() != null) {
            fontFileNode.addAttribute("ttfbirefid", toString(textStyleFont.getTtfBoldItalicFile().getId()));
        }
    }

    public static void createTextStyle( TextStyle style, Element stylesNode )
    {
        Element styleNode = stylesNode.addElement("Style");
        styleNode.addAttribute( "id", toString(style.getId()) );
        styleNode.addAttribute( "name", style.getName() );
        styleNode.addAttribute( "webname", style.getConnectorName() );
        styleNode.addAttribute( "color", style.getColor() );
        styleNode.addAttribute( "identifier", style.getIdentifier() );

        styleNode.addAttribute( "istogglebold", toString(style.isToggleBold()) );
        styleNode.addAttribute( "istoggleitalic", toString(style.isToggleItalic()) );
        styleNode.addAttribute( "istoggleunderline", toString(style.isToggleUnderline()) );
        styleNode.addAttribute( "istogglecolor", toString(style.isToggleColor()) );
        styleNode.addAttribute( "istogglepointsize", toString(style.isTogglePointSize()) );
        styleNode.addAttribute( "isapplytextstylefont", toString(style.isApplyTextStyleFont()) );

        if(StringUtils.isNotBlank(style.getStyleSetDefaults())) {
            styleNode.addElement("StyleSetDefaults").addCDATA(style.getStyleSetDefaults());
        }

        Set<String> toggleValues = style.getToggleColorValuesCMYK();
        if (toggleValues != null && !toggleValues.isEmpty())
        {
            Element toggleValuesNode = styleNode.addElement("ToggleColorValues");
            for (String value : toggleValues)
            {
            	toggleValuesNode.addElement("ToggleColorValue").addCDATA(value);
            }
        }
        
        toggleValues = style.getTogglePointSizeValues();
        if (toggleValues != null && !toggleValues.isEmpty())
        {
            Element toggleValuesNode = styleNode.addElement("TogglePointSizeValues");
            for (String value : toggleValues)
            {
            	toggleValuesNode.addElement("TogglePointSizeValue").addCDATA(value);
            }
        }
        
        String taggingOverride = style.getTaggingOverride(); 
        if (taggingOverride != null && !taggingOverride.isEmpty())
        {
        	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
        }
        
        TextStyleFont textStyleFont = style.getTextStyleFont();
        if (textStyleFont != null)
        {
        	Element textStyleFontNode = styleNode.addElement("TextStyleFont");
        	textStyleFontNode.addElement("Name").addCDATA(textStyleFont.getName());
        }
        
        Element fontNode = styleNode.addElement("Font");
        fontNode.addAttribute( "refid", toString(style.getId()) );
        fontNode.addAttribute( "propertyrefid", toString(style.getId()) );
    }

    public static void createDatabaseFileTagForTextStyleFont(TextStyleFont textStyleFont, Element databaseFilesElement, Document doc){
        if(textStyleFont.getTtfFile() != null) {
            createDatabaseFileTag(textStyleFont.getTtfFile(), databaseFilesElement, doc);
        }
        if(textStyleFont.getEotFile() != null) {
            createDatabaseFileTag(textStyleFont.getEotFile(), databaseFilesElement, doc);
        }
        if(textStyleFont.getTtfBoldFile() != null) {
            createDatabaseFileTag(textStyleFont.getTtfBoldFile(), databaseFilesElement, doc);
        }
        if(textStyleFont.getTtfItalicFile() != null) {
            createDatabaseFileTag(textStyleFont.getTtfItalicFile(), databaseFilesElement, doc);
        }
        if(textStyleFont.getTtfBoldItalicFile() != null) {
            createDatabaseFileTag(textStyleFont.getTtfBoldItalicFile(), databaseFilesElement, doc);
        }
    }

    public static void createDatabaseFileTag(DatabaseFile databaseFile, Element databaseFilesElement, Document doc )
    {
        Element databaseFileElement = databaseFilesElement.addElement("File");
        databaseFileElement.addAttribute("id", toString(databaseFile.getId()));

        databaseFileElement.addAttribute("type", toString(databaseFile.getType()));

        databaseFileElement.addAttribute("contenttype", databaseFile.getContentType());

        Element nameElement = databaseFileElement.addElement("Filename");
        nameElement.addText(databaseFile.getFileName());

        Element contentElement = databaseFileElement.addElement("Content");
        contentElement.addCDATA(ExportXMLUtils.encodeContent(databaseFile.getFileContent()));
    }

    public static void createTargetCriteriaTag(Element parentElement, Targetable item)
    {
		Element targetCriteriaElement = parentElement.addElement("TargetCriteria");
		if ( item.getExcludedTargetGroups() != null && !item.getExcludedTargetGroups().isEmpty())
		{
			Element exclusionsElement = targetCriteriaElement.addElement("Exclusions");
			exclusionsElement.addAttribute("relationship", toString(item.getExcludedTargetGroupRelationship()));	
            List<TargetGroup> tgGroups = item.getExcludedTargetGroups();
			Collections.sort(tgGroups, new TargetGroupIdComparator());		
			for (TargetGroup targetGroup : tgGroups)
			{
				createTargetGroupTag(item, targetGroup, exclusionsElement);
			}
		}
		if ( item.getIncludedTargetGroups() != null && !item.getIncludedTargetGroups().isEmpty())
		{
			Element inclusionsGroupAElement = targetCriteriaElement.addElement("InclusionsGroupA");
			inclusionsGroupAElement.addAttribute("relationship", toString(item.getIncludedTargetGroupRelationship()));			
            List<TargetGroup> tgGroups = item.getIncludedTargetGroups();
			Collections.sort(tgGroups, new TargetGroupIdComparator());		
			for (TargetGroup targetGroup : tgGroups)
			{
				createTargetGroupTag(item, targetGroup, inclusionsGroupAElement);
			}
		}
		if ( item.getExtendedTargetGroups() != null && !item.getExtendedTargetGroups().isEmpty())
		{
			Element inclusionsGroupBElement = targetCriteriaElement.addElement("InclusionsGroupB");
			inclusionsGroupBElement.addAttribute("relationship", toString(item.getExtendedTargetGroupRelationship()));			
            List<TargetGroup> tgGroups = item.getExtendedTargetGroups();
			Collections.sort(tgGroups, new TargetGroupIdComparator());		
			for (TargetGroup targetGroup : tgGroups)
			{
				createTargetGroupTag(item, targetGroup, inclusionsGroupBElement);
			}
		}    	
    }
    
	private static void createTargetGroupTag(Targetable item, TargetGroup targetGroup, Element parentElement)
	{
        Element targetGroupElement = parentElement.addElement("TargetGroup");
		targetGroupElement.addAttribute("refid", toString(targetGroup.getId()));
		targetGroupElement.addAttribute("parameterized", targetGroup.isParameterized()?"true":"false");

		if (!targetGroup.isParameterized())
			return;

        Element tgRulesElement = targetGroupElement.addElement("Rules");
        
        TargetGroupInstance tgInstance = null;
        if ( item instanceof ContentObject )
        	tgInstance = targetGroup.getParameterizedMap().get(((ContentObject) item).getContentObjectData().getContentObjectDataMapKey());
        else if ( item instanceof ContentObjectData )
            tgInstance = targetGroup.getParameterizedMap().get(((ContentObjectData) item).getContentObjectDataMapKey());
        else if ( item instanceof Insert )
        	tgInstance = targetGroup.getInsertTargetGroupsMap().get(item.getId());
        else if ( item instanceof Tag )
        	tgInstance = targetGroup.getTagTargetGroupsMap().get(item.getId());
        else if ( item instanceof Attachment )
        	tgInstance = targetGroup.getAttachmentTargetGroupsMap().get(item.getId());
        else if ( item instanceof TouchpointTargeting )
        	tgInstance = targetGroup.getTouchpointTargetGroupsMap().get(item.getId());
        else if ( item instanceof ContentTargeting )
        	tgInstance = targetGroup.getContentTargetGroupsMap().get(item.getId());

        if (tgInstance != null) 
        {
            Map<ConditionItem, Boolean> tgConditionParamMap = tgInstance.getConditionParamMap();

            List<ConditionElement> tgRules = new ArrayList<>(tgInstance.getConditionElements());
			Collections.sort(tgRules, new ConditionElementIdComparator());		
	        for (ConditionElement rule : tgRules)
	        {
	        	Element ruleElement = tgRulesElement.addElement("Rule");
	        	ruleElement.addAttribute("refid", toString(rule.getId()));

	        	ConditionItem conditionItem = tgInstance.getConditionItem(rule);

                boolean ruleParamaterized = false;
                if (tgConditionParamMap.containsKey(conditionItem))
                {
                	ruleParamaterized = tgConditionParamMap.get(conditionItem);
                }
            	ruleElement.addAttribute("parameterized", ruleParamaterized?"true":"false");                    	
	
	            List<ConditionSubelement> ruleConditions = rule.getSubElements();  
	            //rule.getDirectReferences();
	            for (ConditionSubelement ruleCondition : ruleConditions)
	            {
	            	if (!ruleCondition.isReferenced())
	            		continue;
	            	
	            	boolean isSelected = false;
	            	List<ConditionItemValue> ciValues = ruleCondition.getReferencingConditionItemValues();
	            	for (ConditionItemValue ciValue : ciValues)
	            	{
	            		if ( ciValue.getConditionItem() != null
                          && ciValue.getConditionItem().getInstance() != null
                          && ciValue.getConditionItem().getInstance().getId() == tgInstance.getId() )
	            		{
	            			isSelected = true;
	            			break;
	            		}
	            	}               
	
	            	if ( !isSelected )
	            		continue;
	            	
	            	Element ruleConditionElement = ruleElement.addElement("Condition");
	            	ruleConditionElement.addAttribute("refid", toString(ruleCondition.getId()));
	            	                    
	                Set<ConditionItemValue> conditionItemValues = conditionItem.getConditionItemValues();
	                for (ConditionItemValue conditionItemValue : conditionItemValues)
	                {
	                	if ( conditionItemValue.getConditionSubelement().getId() != ruleCondition.getId() )
	                		continue;
	                	
	                    Map<String, String> valueMap = conditionItemValue.getValueMap();
	                    if (!valueMap.isEmpty()) 
	                    {
	                        Element mapElement = ruleConditionElement.addElement("Parameter");            
	                        for (String key : valueMap.keySet())
	                        {
	                            Element itemElement = mapElement.addElement("Item");
	                            itemElement.addElement("Key").addText(key);
	                            itemElement.addElement("Value").addCDATA(valueMap.get(key));            	
	                        }
	                    }
	                }
	            }
	        }
        }
	}

    public static void createLanguages( Document doc, Element parent )
    {
        Element langsNode = parent.addElement("Languages");
        for( TouchpointLanguage language : doc.getTouchpointLanguagesSortedByLanguageName() )
        {
        	Element langNode = langsNode.addElement("Language");
        	langNode.addAttribute("code", language.getLanguageCode());
        	langNode.addAttribute("default", toString(language.isDefaultLanguage()));
        	langNode.addElement("Name").addCDATA(language.getLanguageName());
        	
        	Element localeNode = langNode.addElement("Locale");
        	localeNode.addAttribute("code", language.getLocaleCode());
        	localeNode.addElement("Name").addCDATA(language.getLocaleName());
        }
    }
    
    public static void createWebServiceTag( Element parent, String upperLevelElementName, WebServiceConfiguration webServiceConfig ) {
		if(upperLevelElementName != null) {
	    	Element webServiceParentElement = parent.addElement(upperLevelElementName);
			parent = webServiceParentElement;
		}
		String xmlExportImportSecretKey1 = ApplicationUtil.getProperty(SystemPropertyKeys.XMLExportImport.KEY_TPExportImportSecretKey_1);
		Element webServiceElement = parent.addElement("WebService");
		AesUtil aes = new AesUtil(256);
		webServiceElement.addAttribute("id", toString(webServiceConfig.getId()));
		webServiceElement.addAttribute("guid", webServiceConfig.getGuid());
		String url = webServiceConfig.getUrl();
		if(url != null) {
			try {
				webServiceElement.addElement("EncryptedURL1").addCDATA(aes.encrypt(url, xmlExportImportSecretKey1));
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		String username = webServiceConfig.getUsername();
		if(username != null) {
    		try {
				webServiceElement.addElement("EncryptedUsername1").addCDATA(aes.encrypt(username, xmlExportImportSecretKey1));
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		String password = webServiceConfig.getPassword();
		if(password != null) {
    		try {
//				webServiceElement.addElement("EncryptedPassword1").addCDATA(aes.encrypt(password, xmlExportImportSecretKey1));
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
    }
    
    public static void createTouchpoint( Document doc, Element export, ExportDocumentToXMLService edtXMLs )
    {
        List<DocumentSection> sections = doc.getDocumentSectionsByOrder();
        Element tpNode = export.addElement("Touchpoint");
        tpNode.addAttribute( "id", toString(doc.getId()) );
        tpNode.addAttribute( "name", doc.getName() );
        tpNode.addAttribute( "dna", doc.getDna() );
        tpNode.addAttribute( "enabled", toString(doc.isEnabled()) );
        tpNode.addAttribute( "sections", toString(sections.size()) );
        
        tpNode.addAttribute( "connectedenabled", toString(doc.isConnectedEnabled()));
        tpNode.addAttribute( "stripoenabled", toString(doc.isStripoEnabled()));

        if(doc.getTouchpointMetadataFormDefinition() != null) {
        	tpNode.addAttribute( "tpmdrefid", toString(doc.getTouchpointMetadataFormDefinition().getId()));
        }
        if(doc.getVariantMetadataFormDefinition() != null) {
        	tpNode.addAttribute( "vrmdrefid", toString(doc.getVariantMetadataFormDefinition().getId()));
        }
        if ( doc.getDefaultListStyle() != null )
            tpNode.addAttribute( "defaultliststylerefid", toString(doc.getDefaultListStyle().getId()) );
        if ( doc.getDefaultParagraphStyle() != null )
            tpNode.addAttribute( "defaultparagraphstylerefid", toString(doc.getDefaultParagraphStyle().getId()) );
        if ( doc.getDefaultTextStyle() != null )
            tpNode.addAttribute( "defaulttextstylerefid", toString(doc.getDefaultTextStyle().getId()) );

        tpNode.addAttribute( "fiscalyearstart", toString(doc.getFiscalYearStartMonth()) );
        if (doc.getCustomerReportingVariableA() != null)
        	tpNode.addAttribute( "crvarefid", toString(doc.getCustomerReportingVariableA().getId()) );
        if (doc.getCustomerReportingVariableB() != null)
        	tpNode.addAttribute( "crvbrefid", toString(doc.getCustomerReportingVariableB().getId()) );
        
        if (doc.getDataGroup() != null)
        	tpNode.addAttribute( "datagrouprefid", toString(doc.getDataGroup().getId()) );
        
        tpNode.addAttribute("bundlecombinedcontent", doc.isProcessUsingCombinedContent()?"1":"0");

        if ( doc.getDictionaryIdentifier() != null && !doc.getDictionaryIdentifier().isEmpty() )
            tpNode.addAttribute( "dictionaryidentifier", doc.getDictionaryIdentifier() );

        tpNode.addElement("GUID").addCDATA(doc.getGuid());
        tpNode.addElement("Metatags").addCDATA(doc.getMetatags());
        
        createConnectorData( doc, tpNode );
        
        createLanguages( doc, tpNode );
        
		if (doc.isEmailTouchpoint() || doc.isWebTouchpoint()) {
            if(doc.isStripoEnabled()){
                EmailTemplateUtils.packageTemplatesFolder(doc.getId());
            }
			boolean templatePackageExists = EmailTemplateUtils.getTemplatePackageExists(doc.getId()); 
			if (templatePackageExists) {
				String templatePackagePath = EmailTemplateUtils.getTemplatePackagePath(doc.getId());
				Element templateFile = tpNode.addElement("TemplateFile");
				templateFile.addCDATA(encodeFile(templatePackagePath));
			}
		}
		
        {
        	boolean isAlternate = false;
        	createSections( doc, tpNode, sections, edtXMLs, isAlternate );
        	createZones( doc, tpNode, isAlternate );
        	createChannelLayouts( doc, tpNode, edtXMLs);
        }
        
        Element alternateLayoutsElement = tpNode.addElement("AlternateLayouts");
    	List<Document> alternateLayouts = doc.getAlternateLayouts();
    	if(alternateLayouts != null && !alternateLayouts.isEmpty()) {
            for(Document layout : alternateLayouts) {
            	Element layoutElement =  createLayoutElement(alternateLayoutsElement, layout);
            	boolean isAlternate = true;
                createSections( layout, layoutElement, layout.getDocumentSectionsByOrder(), edtXMLs, isAlternate );
                createZones( layout, layoutElement, isAlternate );
            	createChannelLayouts( layout, layoutElement, edtXMLs);
        		createTemplateAttributes(layoutElement, layout);
            }
    	}
    	    	
        if(doc.getMetadataForm() != null) {
            createMetadataForm( doc.getMetadataForm(), tpNode, "Metadata" );
        }
        
		TouchpointSelection tpMasterSelection = doc.getMasterTouchpointSelection();

		Element selectionsElement = tpNode.addElement("Selections");
		if (doc.isEnabledForVariation() && tpMasterSelection != null) 
		{
			selectionsElement.addAttribute("selectorrefid", toString(doc.getSelectionParameterGroup().getId()));
			if (doc.isEnabledForVariantWorkflow())
				selectionsElement.addAttribute("selectiontype", "Variant");
			else
				selectionsElement.addAttribute("selectiontype", "Message");
			
			createSelectionsForTP(tpMasterSelection, selectionsElement, edtXMLs, doc);
		}
		
		createTemplateAttributes(tpNode, doc);
        
		LanguageSelection tpMasterLangSelection = doc.getMasterLanguageSelection();

		Element langManagementElement = tpNode.addElement("LanguageManagement");
		if ( doc.isEnabledForLanguageSelection() && tpMasterLangSelection != null ) 
		{
			langManagementElement.addAttribute("selectorrefid", toString(doc.getLanguageParameterGroup().getId()));
			createLanguageManagementForTP(tpMasterLangSelection, langManagementElement);		
		}
        
		if(doc.isConnectedEnabled()) {
			Element connectedManagementElement = tpNode.addElement("ConnectedManagement");

            if(doc.isConnectedEnabled() && doc.getDataSourceAssociation() != null) {
                connectedManagementElement.addAttribute("updateReferenceConnection", toString(true));
                long primaryDataVariableId = 0L;
                long referenceDataVariableId = 0L;
                for(ReferenceConnection referenceConnection : doc.getDataSourceAssociation().getReferenceConnections()) {
                    if(referenceConnection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                        if(referenceConnection.getPrimaryVariable() != null) {
                            primaryDataVariableId = referenceConnection.getPrimaryVariable().getId();
                            connectedManagementElement.addAttribute("primaryDataVariableRefId", toString(primaryDataVariableId));
                        }
                        if(referenceConnection.getReferenceVariable() != null) {
                            referenceDataVariableId = referenceConnection.getReferenceVariable().getId();
                            connectedManagementElement.addAttribute("referenceDataVariableRefId", toString(referenceDataVariableId));
                        }
                        break;
                    }
                }
            }
            if(doc.isEnabledForVariation() && doc.isCommunicationAppliesTouchpointSelection()) {
                connectedManagementElement.addElement("VariantSelection").addText(doc.isCommunicationAppliesTouchpointSelection()?"1":"0");
            }

			if(edtXMLs.getExportOptions().isIncludeDataFilesAndDataResourcesForConnectedInterview()) {
				if(doc.getCommunicationsDataResource() != null) {
					connectedManagementElement.addElement("DataResource").addAttribute("refid", toString(doc.getCommunicationsDataResource().getId()));
				}
			}
			
			if(doc.getCommunicationZoneMarkerStyle() != null) {
				connectedManagementElement.addElement("ZoneMarkerStyle").addAttribute("refid", toString(doc.getCommunicationZoneMarkerStyle().getId()));
			}
			
			connectedManagementElement.addElement("OrderEntryEnabled").addText(doc.isCommunicationOrderEntryEnabled()?"1":"0");
			
			Element webServicesElement = connectedManagementElement.addElement("WebServices");
			if(doc.getCommunicationCompositionResultsWebService() != null) {
				WebServiceConfiguration webServiceConfig = doc.getCommunicationCompositionResultsWebService();
				createWebServiceTag(webServicesElement, "CompositionResults", webServiceConfig);
			}
			
			if(doc.getCommunicationProductionStatusWebService() != null) {
				WebServiceConfiguration webServiceConfig = doc.getCommunicationProductionStatusWebService();
				createWebServiceTag(webServicesElement, "ProductionStatus", webServiceConfig);
			}

            if(doc.getCommunicationNotificationWebService() != null) {
                WebServiceConfiguration webServiceConfig = doc.getCommunicationNotificationWebService();
                createWebServiceTag(webServicesElement, "Notification", webServiceConfig);
            }
			
			if(doc.getCommunicationDataFeedWebService() != null) {
				WebServiceConfiguration webServiceConfig = doc.getCommunicationDataFeedWebService();
				createWebServiceTag(webServicesElement, "DataFeed", webServiceConfig);
			}
			
			connectedManagementElement.addElement("ProductionType").addAttribute("refid", toString(doc.getCommunicationProductionTypeId()));
			connectedManagementElement.addElement("ExternalValidationEnabled").addText(doc.isCommunicationExternalValidationEnabled()?"1":"0");
			connectedManagementElement.addElement("AppliesTagCloud").addText(doc.isCommunicationAppliesTagCloud()?"1":"0");
			connectedManagementElement.addElement("AppliesCopiesInput").addText(doc.isCommunicationAppliesCopiesInput()?"1":"0");
			connectedManagementElement.addElement("DisplayTouchpointThumbnail").addText(doc.isCommunicationDisplayTouchpointThumbnail()?"1":"0");
			connectedManagementElement.addElement("ResolveVariableValues").addText(doc.isCommunicationResolveVariableValues()?"1":"0");
			connectedManagementElement.addElement("SuppressNonEditableZones").addText(doc.isCommunicationSuppressNonEditableZones()?"1":"0");
			
			if(doc.getCommunicationMultiRecipientIdentifier() != null) {
				DataElementVariable multipleRecipientIdentifier = doc.getCommunicationMultiRecipientIdentifier();
				Element dataVariableElement = connectedManagementElement.addElement("MultipleRecipient");
				dataVariableElement.addAttribute("refid", toString(multipleRecipientIdentifier.getId()));
			}
			
			Set<CommunicationOrderEntryItemDefinition>	orderDefEntries = doc.getCommunicationOrderEntryItemDefinitions();
			if(orderDefEntries != null && !orderDefEntries.isEmpty()) {
				Element orderEntriesListElement = connectedManagementElement.addElement("OrderEntries");
				for(CommunicationOrderEntryItemDefinition item : orderDefEntries) {
					Element element = orderEntriesListElement.addElement("OrderEntry");
					element.addAttribute("id", toString(item.getId()));
					element.addAttribute("guid", item.getGuid());
					element.addAttribute("isprimary", item.getIsPrimaryDriverEntry()?"1":"0");
					element.addAttribute("isindicator", item.getIsIndicatorEntry()?"1":"0");
					element.addAttribute("dataprivacytypeid", toString(item.getDataPrivacyTypeId()));
					
			    	String name = item.getName();
			    	if(name != null) {
			    		element.addElement("Name").addCDATA(name);
			    	}

			    	String description = item.getDescription();
			    	if(description != null) {
			    		element.addElement("Description").addCDATA(description);
			    	}
			    	
			    	element.addAttribute("order", toString(item.getOrder()));
			    	element.addAttribute("type", toString(item.getTypeId()));
			    	
			    	Boolean mandatory = item.getIsManadatory();
			    	if(mandatory != null) {
			    		element.addAttribute("mandatory", (mandatory.booleanValue()?"1":"0"));
			    	}
			    	int refreshOnValueChangeTypeId = item.getWebServiceRefreshTypeId();
			        element.addAttribute("refreshonvaluechange", String.valueOf(refreshOnValueChangeTypeId));
			    	
			    	DataElementVariable dataElementVariable = item.getDataElementVariable();
			    	if(dataElementVariable != null) {
			        	element.addAttribute("dataElementVariableRid", toString(dataElementVariable.getId()));
			    	}
			    	String menuValueItems = item.getMenuValueItems();
			    	if(menuValueItems != null) {
			        	element.addElement("MenuValueItems").addCDATA(menuValueItems);
			    	}

                    String regexValidation = item.getRegexValidation();
                    if(regexValidation != null) {
                        element.addElement("RegexValidation").addCDATA(regexValidation);
                    }

                    String criteriaOperator = item.getCriteriaOperator();
                    if (criteriaOperator != null) {
                        element.addElement("CriteriaOperator").addCDATA(criteriaOperator);
                    }

                    String criteriaTriggerValuesJson = item.getCriteriaTriggerValuesJson() ;
                    if (criteriaTriggerValuesJson != null) {
                        element.addElement("CriteriaTriggerValuesJson").addCDATA(criteriaTriggerValuesJson);
                    }
			    	
			    	String primaryConnector = item.getPrimaryConnector();
			    	if(primaryConnector != null) {
			    		element.addElement("PrimaryConnector").addCDATA(primaryConnector);
			    	}
			    	
			    	Integer parentItemOrder = item.getParentItemOrder();
			    	if(parentItemOrder != null) {
			    		element.addAttribute("parentitemorder", toString(parentItemOrder));
			    	}
			    	
			    	String displayTriggerValues = item.getDisplayTriggerValues();
			    	if(displayTriggerValues != null) {
			    		element.addElement("DisplayTriggerValues").addCDATA(displayTriggerValues);
			    	}
			    	
			    	MetadataFormFieldSizeType fieldSizeType = item.getFieldSizeType();
			    	if(fieldSizeType != null) {
			    		element.addAttribute("fieldsizetype", toString(fieldSizeType.getId()));
			    	}
			    	
			    	Integer fieldMaxLength = item.getFieldMaxLength();
			    	if(fieldMaxLength != null) {
			    		element.addAttribute("fieldmaxlength", toString(fieldMaxLength));
			    	}
			    	
			    	Integer inputValidationTypeId = item.getInputValidationTypeId();
			    	if(inputValidationTypeId != null) {
			    		element.addAttribute("inputvalidationtype", toString(inputValidationTypeId));
			    	}
			    	
			    	String defaultInputValue = item.getDefaultInputValue();
			    	if(defaultInputValue != null) {
			    		element.addElement("DefaultInputValue").addCDATA(defaultInputValue);
			    	}
			    	
			    	boolean defaultToToday = item.isDefaultDateValueToTodaysDate();
					element.addAttribute("defaulttotoday", defaultToToday?"1":"0");


                    Integer repeatingDataTypeId = item.getRepeatingDataTypeId();
                    element.addAttribute("repeatingdatatypeid", toString(repeatingDataTypeId != null ? repeatingDataTypeId : RepeatingDataType.ID_NONE));
			    	
					boolean uniqueValue = item.isUniqueValue();
					element.addAttribute("uniquevalue", uniqueValue?"1":"0");
					
					Map<String,DataElementVariable> connectorElementMap = item.getConnectorVariableMap();
					if(connectorElementMap != null && (! connectorElementMap.isEmpty())) {
						Element connectorElementMapElement = element.addElement("ConnectorVariableMap");
						for(Map.Entry<String,DataElementVariable> entry : connectorElementMap.entrySet()) {
							Element itemElement = connectorElementMapElement.addElement("Item");
							itemElement.addElement("Key").addCDATA(entry.getKey());
							itemElement.addElement("DataElementVariable").addAttribute("refid", toString(entry.getValue().getId()));
						}
					}

                    if(doc.isEnabledForVariation() && doc.isCommunicationAppliesTouchpointSelection()) {
                        Set<TouchpointSelection> applicableSelections = item.getApplicableSelections();
                        if(applicableSelections != null && ! applicableSelections.isEmpty()) {
                            Element applicableSelectionsElement = element.addElement("ApplicableSelections");
                            for(TouchpointSelection touchpointSelection : applicableSelections) {
                                applicableSelectionsElement.addElement("ApplicableSelection").addAttribute("refid", toString(touchpointSelection.getId()));
                            }
                        }
                    }
                }
			}
		}
		
        tpNode.addAttribute( "segmentationanalysisenabled", toString(doc.isSegmentationAnalysisEnabled()));
		if (doc.isSegmentationAnalysisEnabled()) {
			if(edtXMLs.getExportOptions().isIncludeDataFilesAndDataResourcesForSegmentationAnalysis()) {
				if(doc.getSegmentationAnalysisResource() != null) {
					tpNode.addAttribute("segmentationanalysisdrid", toString(doc.getSegmentationAnalysisResource().getId()));
				}
			}
		}
		
		if (doc.isEnabledForAttachments() && doc.getAttachments() != null)
		{
			Element attachmentManagementElement = tpNode.addElement("AttachmentManagement");
	        for ( Attachment attach : doc.getAttachments() )
	        {
	        	Element attachmentElement = attachmentManagementElement.addElement("Attachment");
	        	attachmentElement.addAttribute("id", toString(attach.getId()));
	        	attachmentElement.addAttribute("deliverytype", attach.getDeliveryType().getName());
                attachmentElement.addAttribute("dna", attach.getDna());
	        	attachmentElement.addElement("Name").addCDATA(attach.getName());
	        	attachmentElement.addElement("FileLocation").addCDATA(attach.getRecipientAttachmentLocation().getEncodedValue());
	        	attachmentElement.addElement("AttachName").addCDATA(attach.getRecipientAttachmentName().getEncodedValue());
	        	createTargetCriteriaTag(attachmentElement, attach);
	        }
		}

		TouchpointTargeting tptarget = TouchpointTargeting.findByDocument(doc);
		if ( tptarget != null )
		{
			Element touchpointTargetingElement = tpNode.addElement("TouchpointTargeting");
			createTargetCriteriaTag(touchpointTargetingElement, tptarget);
		}
		
		if(doc.getExtReportingDataVariables() != null && !doc.getExtReportingDataVariables().isEmpty())
		{
			Element reportVarsElement = tpNode.addElement("ReportingVariables");
			for( DataElementVariable var : doc.getExtReportingDataVariables() )
			{
				Element varElement = reportVarsElement.addElement("UserVariable");
				varElement.addAttribute("refid", toString(var.getId()));
				varElement.addCDATA(var.getName());
			}
		}

        List<TextStyle> textStyles = TextStyle.findAllOrderById();
        Element stylesCustomElement = tpNode.addElement("TextStyleCustomizations");
        for( TextStyle style : textStyles )
        {
        	Map<Document, TextStyleCustomization> mapTSC = style.getTextStyleCustomizations();
        	if (mapTSC.containsKey(doc))
        	{
        		TextStyleCustomization tsc = mapTSC.get(doc);
                Element styleNode = stylesCustomElement.addElement("Style");
                styleNode.addAttribute( "id", toString(tsc.getId()) );
                styleNode.addAttribute( "refId", toString(tsc.getMasterTextStyle().getId()) );

                styleNode.addAttribute( "connectorname", tsc.getConnectorName() );
                styleNode.addAttribute( "color", tsc.getColor() );
                styleNode.addAttribute( "fontname", tsc.getFontName() );
                styleNode.addAttribute( "fontwebname", tsc.getWebFontName() );

                try {
                	styleNode.addAttribute( "pointsize", DecimalValueUtil.dehydrate(tsc.getPointSize()) );
        		} catch (ParseException e) {
        			log.error("Error: ", e);
        		}
                styleNode.addAttribute( "bold", toString(tsc.isBold()) );
                styleNode.addAttribute( "italics", toString(tsc.isItalic()) );
                styleNode.addAttribute( "underline", toString(tsc.isUnderline()) );
                
                styleNode.addAttribute( "istogglebold", toString(tsc.isToggleBold()) );
                styleNode.addAttribute( "istoggleitalic", toString(tsc.isToggleItalic()) );
                styleNode.addAttribute( "istoggleunderline", toString(tsc.isToggleUnderline()) );
                styleNode.addAttribute( "istogglecolor", toString(tsc.isToggleColor()) );
                styleNode.addAttribute( "istogglepointsize", toString(tsc.isTogglePointSize()) );
                styleNode.addAttribute( "isapplytextstylefont", toString(tsc.isApplyTextStyleFont()) );

                if(StringUtils.isNotBlank(style.getStyleSetDefaults())) {
                    styleNode.addElement("StyleSetDefaults").addCDATA(style.getStyleSetDefaults());
                }

                Set<String> toggleValues = tsc.getToggleColorValuesCMYK();
                if (toggleValues != null && !toggleValues.isEmpty())
                {
                    Element toggleValuesNode = styleNode.addElement("ToggleColorValues");
                    for (String value : toggleValues)
                    {
                    	toggleValuesNode.addElement("ToggleColorValue").addCDATA(value);
                    }
                }
                
                toggleValues = tsc.getTogglePointSizeValues();
                if (toggleValues != null && !toggleValues.isEmpty())
                {
                    Element toggleValuesNode = styleNode.addElement("TogglePointSizeValues");
                    for (String value : toggleValues)
                    {
                    	toggleValuesNode.addElement("TogglePointSizeValue").addCDATA(value);
                    }
                }
                
                String taggingOverride = tsc.getTaggingOverride(); 
                if (taggingOverride != null && !taggingOverride.isEmpty())
                {
                	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
                }
                
                TextStyleFont textStyleFont = tsc.getTextStyleFont();
                if (textStyleFont != null)
                {
                	Element textStyleFontNode = styleNode.addElement("TextStyleFont");
                	textStyleFontNode.addElement("Name").addCDATA(textStyleFont.getName());
                }
        	}
        }
        
		List<ParagraphStyle> pStyles = ParagraphStyle.findAllOrderById(); 
        Element paraStylesCustomElement = tpNode.addElement("ParagraphStyleCustomizations");
        for( ParagraphStyle style : pStyles )
        {
        	Map<Document, ParagraphStyleCustomization> mapPSC = style.getParagraphStyleCustomizations();
        	if (mapPSC.containsKey(doc))
        	{
        		ParagraphStyleCustomization psc = mapPSC.get(doc);
                Element styleNode = paraStylesCustomElement.addElement("Style");
                styleNode.addAttribute( "id", toString(psc.getId()) );
                styleNode.addAttribute( "refId", toString(psc.getMasterParagraphStyle().getId()) );

                styleNode.addAttribute( "name", psc.getName() );
                styleNode.addAttribute( "connectorname", psc.getConnectorName() );
                styleNode.addAttribute( "alignment", psc.getAlignment().getCSSValue() );
                styleNode.addAttribute( "spacebefore", psc.getParagraphSpacingBeforeAsStringInInches() );
                styleNode.addAttribute( "spaceafter", psc.getParagraphSpacingAfterAsStringInInches() );
                try {
					styleNode.addAttribute( "precisionlinespace", DecimalValueUtil.dehydrate(psc.getLineSpacing()) );
				} catch (ParseException e) {
					log.error("Error: ", e);
				}
                styleNode.addAttribute( "linespacingtype", toString(psc.getLineSpacingType()) );
                styleNode.addAttribute( "indent", psc.getIndentAsString() );
                styleNode.addAttribute( "hanging", toString(psc.isHangingIndent()) );
                styleNode.addAttribute( "leftmargin", psc.getLeftMarginAsString() );
                styleNode.addAttribute( "rightmargin", psc.getRightMarginAsString() );
                styleNode.addAttribute( "bordertypeid", toString(psc.getBorderTypeId()) );
                styleNode.addAttribute( "borderwidth", psc.getBorderWidthAsString() );
                if (psc.getTextStyle() != null)
                	styleNode.addAttribute( "textstylerefid", toString(psc.getTextStyle().getId()) );
                                         
                styleNode.addAttribute( "bulletedlistapplicable", toString(psc.isBulletedListApplicable()) );        	

                String taggingOverride = psc.getTaggingOverride(); 
                if (taggingOverride != null && !taggingOverride.isEmpty())
                {
                	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
                }

                styleNode.addAttribute("bulletSymbolId",     toString(psc.getBulletSymbolId()));
                styleNode.addAttribute("numberedListTypeId", toString(psc.getNumberedListTypeId()));

                styleNode.addAttribute("toggleAlignment",    toString(psc.isToggleAlignment()));
                styleNode.addAttribute("toggleLineSpacing",  toString(psc.isToggleLineSpacing()));
                styleNode.addAttribute("toggleLeftMargin",   toString(psc.isToggleLeftMargin()));
        	}
        }        
        
		List<ListStyle> listStyles = ListStyle.findAllOrderById(); 
        Element listStylesCustomElement = tpNode.addElement("ListStyleCustomizations");
        for( ListStyle style : listStyles )
        {
        	Map<Document, ListStyleCustomization> mapListStyleCust = style.getListStyleCustomizations();
        	if (mapListStyleCust.containsKey(doc))
        	{
        		ListStyleCustomization listStyleCust = mapListStyleCust.get(doc);
                Element styleNode = listStylesCustomElement.addElement("Style");
                styleNode.addAttribute( "id", toString(listStyleCust.getId()) );
                styleNode.addAttribute( "refId", toString(listStyleCust.getMasterListStyle().getId()) );

                styleNode.addAttribute( "name", listStyleCust.getName() );
                styleNode.addAttribute( "connectorname", listStyleCust.getConnectorName() );
                styleNode.addAttribute( "alignment", new ParagraphAlignment(listStyleCust.getAlignmentId()).getCSSValue() );
                
                styleNode.addAttribute( "spacebefore", listStyleCust.getListSpacingBeforeAsString() );
                styleNode.addAttribute( "spaceafter", listStyleCust.getListSpacingAfterAsString() );
                styleNode.addAttribute( "spaceright", listStyleCust.getListSpacingRightAsString() );
                
                styleNode.addAttribute( "bordertypeid", toString(listStyleCust.getBorderTypeId()) ); 
                styleNode.addAttribute( "borderwidth",  listStyleCust.getBorderWidthAsString() );
                
                try {
					styleNode.addAttribute( "precisionlinespace", DecimalValueUtil.dehydrate(listStyleCust.getLineSpacing()) );
				} catch (ParseException e) {
					log.error("Error: ", e);
				}
                styleNode.addAttribute( "linespacingtype", toString(listStyleCust.getLineSpacingType()) );
                styleNode.addAttribute( "indent", listStyleCust.getIndentAsString() );
                styleNode.addAttribute( "hanging", toString(listStyleCust.isHangingIndent()) );

                
                styleNode.addAttribute( "bulletLeftMargin", listStyleCust.getBulletLeftMarginAsString() );
                styleNode.addAttribute( "bulletRightMargin", listStyleCust.getBulletRightMarginAsString() );
                styleNode.addAttribute( "bulletTopMargin", listStyleCust.getBulletTopMarginAsString() );
                styleNode.addAttribute( "bulletBottomMargin", listStyleCust.getBulletBottomMarginAsString() );

                if (listStyleCust.getTextStyle() != null)
                	styleNode.addAttribute( "textstylerefid", toString(listStyleCust.getTextStyle().getId()) );
                                         
                String bulletSymbolOverrides = listStyleCust.getBulletSymbolOverrides();
            	if(bulletSymbolOverrides != null && !bulletSymbolOverrides.isEmpty()) 
            	{
                	styleNode.addElement("BulletSymbolOverrides").addCDATA(bulletSymbolOverrides);
            	}

                String bulletSpacingData = listStyleCust.getBulletSpacingData();
                if(bulletSpacingData != null && !bulletSpacingData.isEmpty())
                {
                    styleNode.addElement("BulletSpacingData").addCDATA(bulletSpacingData);
                }

                String taggingOverride = listStyleCust.getTaggingOverride(); 
                if (taggingOverride != null && !taggingOverride.isEmpty())
                {
                	styleNode.addElement("TaggingOverride").addCDATA(taggingOverride);
                }
        	}
        }        
        
    }
    
    static void createSections(Document doc, Element tpNode, List<DocumentSection> sections, ExportDocumentToXMLService edtXMLs, boolean alternate) {
        Element sectionsNode = tpNode.addElement("Sections");
        
        for( DocumentSection section : sections )
        {
            Element sectionNode = sectionsNode.addElement("Section");
            sectionNode.addAttribute( "id", toString(section.getId()) );
            sectionNode.addAttribute( "dna", section.getDna() );
            sectionNode.addAttribute( "number", toString(section.getSectionOrder()) );
            sectionNode.addAttribute( "width", toString(section.getWidth()) );
            sectionNode.addAttribute( "height", toString(section.getHeight()) );
            sectionNode.addAttribute( "layouttype", toString(section.getLayoutType()) );
            sectionNode.addAttribute( "sectiontype", toString(section.getSectionType()) );
            sectionNode.addAttribute( "pagenumberingtype", toString(section.getPageNumberingType()) );

            sectionNode.addAttribute( "margintop", toString(section.getMarginTop()) );
            sectionNode.addAttribute( "marginright", toString(section.getMarginRight()) );
            sectionNode.addAttribute( "marginbottom", toString(section.getMarginBottom()) );
            sectionNode.addAttribute( "marginleft", toString(section.getMarginLeft()) );

            sectionNode.addAttribute( "headerheight", toString(section.getHeaderHeight()) );
            sectionNode.addAttribute( "footerheight", toString(section.getFooterHeight()) );
            sectionNode.addAttribute( "regionleftwidth", toString(section.getRegionLeftWidth()) );
            sectionNode.addAttribute( "regionrightwidth", toString(section.getRegionRightWidth()) );
            sectionNode.addAttribute( "startsfrontfacing", toString(section.isStartsFrontFacing()) );
            sectionNode.addAttribute( "countblankbackers", toString(section.isCountBlankBackers()) );
            sectionNode.addAttribute( "addheaderandfooter", toString(section.isAddHeaderAndFooter()) );

            if(section.getAfpCopyGroupName() != null && !section.getAfpCopyGroupName().trim().isEmpty()) {
                sectionNode.addAttribute( "afpcopygroupname", section.getAfpCopyGroupName() );
            }

/*            
        	private boolean override Name			= false;
        	private boolean override SectionOrder	= false;
        	private boolean override Image			= false;
        	private boolean override Dimensions		= false;
        	private boolean override Margin			= false;
        	private boolean override Header			= false;
        	private boolean override Footer			= false;
        	private boolean override RegionLeft		= false;
        	private boolean override RegionRight	= false;
        	private boolean override Guides			= false;
*/        	
        	if(alternate) {
        		if(section.getParent() != null) {
        			sectionNode.addAttribute("parentrefid", 			toString(section.getParent().getId()));
        		}
                sectionNode.addAttribute( "overridename", 			section.isOverrideName()?"1":"0" );
                sectionNode.addAttribute( "overridesectionorder", 	section.isOverrideSectionOrder()?"1":"0" );
                sectionNode.addAttribute( "overrideimage", 			section.isOverrideImage()?"1":"0" );
                sectionNode.addAttribute( "overridedimensions",		section.isOverrideDimensions()?"1":"0" );
                sectionNode.addAttribute( "overridemargin",			section.isOverrideMargin()?"1":"0" );
                sectionNode.addAttribute( "overrideheader",			section.isOverrideHeader()?"1":"0" );
                sectionNode.addAttribute( "overridefooter",			section.isOverrideFooter()?"1":"0" );
                sectionNode.addAttribute( "overrideregionleft",		section.isOverrideRegionLeft()?"1":"0" );
                sectionNode.addAttribute( "overrideregionright",	section.isOverrideRegionRight()?"1":"0" );
                sectionNode.addAttribute( "overrideguides",			section.isOverrideGuides()?"1":"0" );
        	}

        	if((! alternate) || (section.getParent() == null) || (section.isOverrideName()) ){
            	Element sectionNameNode = sectionNode.addElement("Name");
            	sectionNameNode.addCDATA(section.getName());
                sectionNode.addCDATA(section.getName());
            }
            
            if((! alternate) || (section.getParent() == null) || (section.isOverrideImage()) ) {
            	Element sectionImageNode = sectionNode.addElement("Image");
            	if ( section.getImageLocation() != null )
            	{
            		sectionImageNode.addAttribute( "filename", section.getImageName() );
            		if (edtXMLs.getIncludeImagePathOnly())
            		{
            			File file = new File(section.getImageLocation());
            			edtXMLs.getOriginalImageFiles().add(file.getAbsolutePath());
            			sectionImageNode.addAttribute("pathonly", "true");
            			sectionImageNode.addCDATA(section.getRelativeImageLocation());
            		}
            		else
            		{
            			sectionImageNode.addCDATA(encodeFile(section.getImageLocation()));
            		}
            	}
            }
        }
    }
    
    static void createZones(Document doc, Element tpNode, boolean alternate) {
        for( Zone z : doc.getZones() )
        {
            Element zoneNode = tpNode.addElement("Zone");
            zoneNode.addAttribute( "id", toString(z.getId()) );
            zoneNode.addAttribute( "friendlyname", z.getFriendlyName() );
            zoneNode.addAttribute( "name", z.getName() );
            zoneNode.addAttribute( "dna", z.getDna() );
            long dgId = 0;
            if (z.getDataGroup() != null)
            	dgId = z.getDataGroup().getId();
            zoneNode.addAttribute( "dgrefid", toString(dgId) );
            zoneNode.addAttribute("mixeddatagroups", toString(z.getMixedDataGroups()));
            zoneNode.addAttribute( "section", toString(z.getSection() != null ? z.getSection().getSectionOrder() : 0) );
            zoneNode.addAttribute( "zonetype", toString(z.getZoneTypeId()) );
            zoneNode.addAttribute( "enabled", toString(z.isEnabled()) );
            zoneNode.addAttribute( "grows", toString(z.isCanGrow()) );
            zoneNode.addAttribute( "flows", toString(z.isCanFlow()) );
            zoneNode.addAttribute( "repeats", toString(z.isRepeats()) );
            zoneNode.addAttribute( "minimumsize", toString(z.isMinimumSize()) );
            zoneNode.addAttribute( "rotationangle", toString(z.getRotationAngle()) );
            zoneNode.addAttribute( "backgroundcolor", z.getBackgroundColor() );
            zoneNode.addAttribute( "dxfoutput", z.isDxfOutput()?"true":"false" );
            zoneNode.addAttribute( "htmloutput", z.isHtmlOutput()?"true":"false" );
            zoneNode.addAttribute( "supportstables", z.isSupportsTables()?"true":"false" );
            zoneNode.addAttribute( "supportsforms", z.isSupportsForms()?"true":"false" );
            zoneNode.addAttribute( "supportsbarcodes", z.isSupportsBarcodes()?"true":"false" );
            zoneNode.addAttribute( "supportscustomparagraphs", z.isSupportsCustomParagraphs()?"true":"false" );
            zoneNode.addAttribute( "freeform", z.isFreeform()?"true":"false" );
            zoneNode.addAttribute( "exporttosinglemessage", z.isExportToSingleMessage()?"true":"false" );
            zoneNode.addAttribute( "alttext", z.isApplyAltText()?"true":"false" );
            zoneNode.addAttribute( "enforceminheight", z.isEnforceMinimumHeight()?"true":"false");
            zoneNode.addAttribute( "splittables", z.isSplitTables()?"true":"false");
            zoneNode.addAttribute("flowzone", z.isFlowZone() ? "true":"false");
            zoneNode.addAttribute("backer", z.isBacker() ? "true":"false");

            if(z.getFlowIntoZone() != null) {
                zoneNode.addAttribute("flowintozonerefid", "" + z.getFlowIntoZone().getId());
            }

            if(z.getVerticalAlignment() != null) {
                zoneNode.addAttribute("verticalalignment", "" + z.getVerticalAlignment());
            }

            if(z.getDefaultCommunicationTemplateImage() != null) {
                zoneNode.addAttribute( "defconntemplclrefid", toString(z.getDefaultCommunicationTemplateImage().getId()) );
            }
            if(z.getDefaultCommunicationTemplateSmartText() != null) {
                zoneNode.addAttribute( "defconntemplecrefid", toString(z.getDefaultCommunicationTemplateSmartText().getId()) );
            }
            zoneNode.addAttribute( "communicationcontentediting" , z.isCommunicationContentEditing() ? "true":"false" );
            
            if(z.getDefaultCanvasWidth() != null) {
                zoneNode.addAttribute( "defaultcanvaswidth", toString(z.getDefaultCanvasWidth()) );
            }
            if(z.getVerticallyRelativeTo() != null) {
                zoneNode.addAttribute( "verticallyrelativeto", toString(z.getVerticallyRelativeTo()) );
            }
            if(z.getRelativeDistance() != null) {
                zoneNode.addAttribute( "relativedistance", toString(z.getRelativeDistance()) );
            }
            if(z.getMinimumKeepTogether() != null) {
                zoneNode.addAttribute( "minimumkeeptogether", toString(z.getMinimumKeepTogether()) );
            }
/*
            private boolean overrideName					= false;
            private boolean overrideFriendlyName			= false;
            private boolean overrideEnabled					= false;
            private boolean overridePosition				= false;
            private boolean overrideDimensions				= false;
            private boolean overrideDocumentSection			= false;
            private boolean overrideDefaultTextStyle		= false;
            private boolean overrideTextStyles				= false;
            private boolean overrideDefaultParagraphStyle	= false;
            private boolean overrideParagraphStyles			= false;
            private boolean overrideBackgroundColor			= false;
            private boolean overrideWorkgroups				= false;
            private boolean overrideSharedAssets			= false;
            private boolean overrideCommunicationTemplate	= false;
*/
            if(alternate) {
            	Zone parentZoneObject = (Zone) z.getParentObject();
            	if(parentZoneObject != null) {
            		zoneNode.addAttribute( "parentrefid",                    toString(parentZoneObject.getId()));
            	}
            	zoneNode.addAttribute( "overridename",                   z.isOverrideName()?"1":"0" );
            	zoneNode.addAttribute( "overridefriendlyName",           z.isOverrideFriendlyName()?"1":"0" );
            	zoneNode.addAttribute( "overrideenabled",                z.isOverrideEnabled()?"1":"0" );
            	zoneNode.addAttribute( "overrideposition",               z.isOverridePosition()?"1":"0" );
            	zoneNode.addAttribute( "overridedimensions",             z.isOverrideDimensions()?"1":"0" );
            	zoneNode.addAttribute( "overridedocumentsection",        z.isOverrideDocumentSection()?"1":"0" );
            	zoneNode.addAttribute( "overridedefaulttextstyle",       z.isOverrideDefaultTextStyle()?"1":"0" );
            	zoneNode.addAttribute( "overridetextstyles",             z.isOverrideTextStyles()?"1":"0" );
            	zoneNode.addAttribute( "overridedefaultparagraphstyle",  z.isOverrideDefaultParagraphStyle()?"1":"0" );
            	zoneNode.addAttribute( "overrideparagraphstyles",        z.isOverrideParagraphStyles()?"1":"0" );
            	zoneNode.addAttribute( "overridebackgroundcolor",        z.isOverrideBackgroundColor()?"1":"0" );
            	zoneNode.addAttribute( "overrideworkgroups",             z.isOverrideWorkgroups()?"1":"0" );
            	zoneNode.addAttribute( "overridesharedassets",           z.isOverrideSharedAssets()?"1":"0" );
            	zoneNode.addAttribute( "overridecommunicationtemplate",  z.isOverrideCommunicationTemplate()?"1":"0" );
            	zoneNode.addAttribute( "overridedefaultliststyle",       z.isOverrideDefaultListStyle()?"1":"0" );
            	zoneNode.addAttribute( "overrideliststyles",             z.isOverrideListStyles()?"1":"0" );
            }
            
            if ( z.getZoneAttributes().containsKey("qualifiedEqualsDelivered") )
                zoneNode.addAttribute("qualifiedisdelivered", z.getZoneAttributes().get("qualifiedEqualsDelivered") );
            if ( z.getContentType() != null )
                zoneNode.addAttribute( "contenttype", toString(z.getContentTypeId()) );
            if ( z.getSubContentType() != null )
                zoneNode.addAttribute( "subcontenttype", toString(z.getSubContentTypeId()) );
            if ( z.getDefaultParagraphStyle() != null )
                zoneNode.addAttribute( "defaultparagraphstylerefid", toString(z.getDefaultParagraphStyle().getId()) );
            if ( z.getDefaultListStyle() != null )
                zoneNode.addAttribute( "defaultliststylerefid", toString(z.getDefaultListStyle().getId()) );
            if ( z.getDefaultTextStyle() != null )
                zoneNode.addAttribute( "defaulttextstylerefid", toString(z.getDefaultTextStyle().getId()) );
            if ( z.getStarterTextStyle() != null ) {
                zoneNode.addAttribute( "startertextstylerefid", toString(z.getStarterTextStyle().getId()) );
            }

            if ( z.isMultiColumnFlowEnabled()) {
                zoneNode.addAttribute( "multicolumnflowenabled",  toString(z.isMultiColumnFlowEnabled()) );
            }
            if ( z.getNumberOfColumns() != null ) {
                zoneNode.addAttribute("numberofcolumns",          toString(z.getNumberOfColumns()));
            }
            if ( z.getGutterWidth() != null ) {
                zoneNode.addAttribute( "gutterwidth",             toString(z.getGutterWidth()) );
            }

            Block zBlock = new Block(z, doc);
            createLocation( zoneNode, zBlock);
            
            for( ZonePart zp : z.getPartsInSequenceOrder() )
            {
                Element zpNode = zoneNode.addElement("ZonePart");
                zpNode.addAttribute( "id", toString(zp.getId()) );
                zpNode.addAttribute( "name", zp.getName() );
                zpNode.addAttribute( "dna", zp.getDna() );
                zpNode.addAttribute( "sequence", toString(zp.getSequence()) );
                zpNode.addAttribute( "contenttype", toString(zp.getContentType().getId()) );
                if ( zp.getSubContentType() != null )
                    zpNode.addAttribute( "subcontenttype", toString(zp.getSubContentType().getId()) );

                if(alternate) {
/*                	
                	private boolean 		overrideName		= false;
                	private boolean 		overridePosition	= false;
                	private boolean 		overrideDimensions	= false;
*/
                    zpNode.addAttribute( "overridename", 		zp.isOverrideName()?"1":"0" );
                    zpNode.addAttribute( "overrideposition", 	zp.isOverridePosition()?"1":"0" );
                    zpNode.addAttribute( "overridedimensions", 	zp.isOverrideDimensions()?"1":"0" );
                }
                Block zpBlock = new Block(zp, z, doc);
                createLocation( zpNode, zpBlock );
            }
            
            Element tsNode = zoneNode.addElement("TextStyles");
            for( TextStyle s : z.getStyles() )
            {
                Element sNode = tsNode.addElement("Style");
                sNode.addAttribute( "refid", toString(s.getId()) );
            }
            {
            Element psNode = zoneNode.addElement("ParagraphStyles");
            for( ParagraphStyle ps : z.getParagraphStyles() )
            {
                Element sNode = psNode.addElement("Style");
                sNode.addAttribute( "refid", toString(ps.getId()) );
            }
            }
            
            {
            Element lsNode = zoneNode.addElement("ListStyles");
            for( ListStyle ls : z.getListStyles() )
            {
                Element sNode = lsNode.addElement("Style");
                sNode.addAttribute( "refid", toString(ls.getId()) );
            }
            }
        }
    }

    static void createChannelLayouts( Document doc, Element parentElement, ExportDocumentToXMLService edtXMLs) {
        List<Document> channelLayouts = doc.getChannelAlternateDocuments();
    	if(channelLayouts != null && !channelLayouts.isEmpty()) {
        	Element channelsElement = parentElement.addElement("ChannelLayouts");
            for(Document channelLayout : channelLayouts) {
            	Element channelLayoutElement =  createLayoutElement(channelsElement, channelLayout);
            	
                List<DocumentSection> layoutSections = channelLayout.getDocumentSectionsByOrder();
            	
                createSections( channelLayout, channelLayoutElement, layoutSections, edtXMLs, true );
                createZones( channelLayout, channelLayoutElement, true );
        		createTemplateAttributes(channelLayoutElement, channelLayout);
            }
    	}
    }
    
    static Element createLayoutElement(Element parent, Document layout) {
        Element layoutElement = parent.addElement("Layout");
    	
        layoutElement.addAttribute("id", toString(layout.getId()));
        layoutElement.addAttribute("guid", layout.getGuid());
        layoutElement.addAttribute("name", layout.getName() );
        layoutElement.addAttribute( "dna", layout.getDna() );
        layoutElement.addAttribute("sections", toString(layout.getDocumentSections().size()) );
        layoutElement.addAttribute("channel", toString(layout.getConnectorConfiguration().getChannel().getId()) );
        
        if(layout.getParent() != null) {
        	layoutElement.addAttribute( "parentrefid", toString(layout.getParent().getId()) );
        }
        
        if(layout.getChannelParent() != null) {
        	layoutElement.addAttribute( "channelparentrefid", toString(layout.getChannelParent().getId()) );
        }
        
		if (layout.isEmailTouchpoint() || layout.isWebTouchpoint()) {
			if(layout.isStripoEnabled()){
                EmailTemplateUtils.packageTemplatesFolder(layout.getId());
            }

            boolean templatePackageExists = EmailTemplateUtils.getTemplatePackageExists(layout.getId());
            if (templatePackageExists) {
				String templatePackagePath = EmailTemplateUtils.getTemplatePackagePath(layout.getId());
				Element templateFile = layoutElement.addElement("TemplateFile");
				templateFile.addCDATA(encodeFile(templatePackagePath));
			}
		}
		
        createConnectorData( layout, layoutElement );
        
        return layoutElement;
    }

    static void createMetadataForm( MetadataForm metadataForm, Element parentElement, String elementTagName ) {
   		Element metadataFormElement = parentElement.addElement(elementTagName);
   		metadataFormElement.addAttribute("id", toString(metadataForm.getId()));
   		metadataFormElement.addAttribute("guid", metadataForm.getGuid());
   		metadataFormElement.addAttribute("definitionrefid", toString(metadataForm.getFormDefinition().getId()));
   		List<MetadataFormItem> items = metadataForm.getFormItemsInOrder();
   		Element itemsElement = metadataFormElement.addElement("MetadataFormItems");
   		for(MetadataFormItem item : items) {
   	   		Element itemElement = itemsElement.addElement("MetadataFormItem");
   	   		itemElement.addAttribute("id", toString(item.getId()));
   	   		itemElement.addAttribute("guid", item.getGuid());
   	   		itemElement.addAttribute("definitionrefid", toString(item.getItemDefinition().getId()));
   	   		String value = item.getValue();
   	   		if(value != null) {
   	   			itemElement.addElement("Value").addCDATA(item.getValue());
   	   		}
   	   		DatabaseFile uploadedFile = item.getUploadedFile();
   	   		if(uploadedFile != null) {
   	   			Element uploadedFileElement = itemElement.addElement("File");
   	   			uploadedFileElement.addAttribute("id", toString(uploadedFile.getId()));
   	   		}
   		}
    }

    private static void createTemplateAttributes(Element parentElement, Document doc)
    {
   		Element emailAttributesElement = parentElement.addElement("TemplateModifiers");
   		List<TemplateModifier> globalEmailAttributes = TemplateModifier.findAllActiveByTouchpointOrderById(doc);
        if (globalEmailAttributes != null && !globalEmailAttributes.isEmpty())
        {
    		for (TemplateModifier attribute : globalEmailAttributes)
    		{
	        	Element atributeElement = emailAttributesElement.addElement("TemplateModifier");
	        	atributeElement.addAttribute("id", toString(attribute.getId()));
	        	atributeElement.addAttribute("active", toString(attribute.getIsActive()));
	        	atributeElement.addAttribute("templatemanaged", toString(attribute.isTemplateManaged()));
	        	atributeElement.addAttribute("valuetype", toString(attribute.getValueType()));
	        	
	        	if (attribute.getTouchpointSelection() != null)
	        		atributeElement.addAttribute("selectionrefid", toString(attribute.getTouchpointSelection().getId()));
	        	
	        	if (attribute.getReferencingTouchpointSelection() != null)
	        		atributeElement.addAttribute("sameasselectionrefid", toString(attribute.getReferencingTouchpointSelection().getId()));
	        	
	        	atributeElement.addElement("Name").addCDATA(attribute.getName());
	        	atributeElement.addElement("ConnectorName").addCDATA(attribute.getConnectorName());
	        	
	        	if (attribute.getComplexValue() != null)
	        		atributeElement.addElement("Value").addCDATA(attribute.getComplexValue().getEncodedValue());    			
    		}
        }    	
    }
    
	private static void createLanguageManagementForTP(LanguageSelection tpSelection, Element element) 
	{
		Element selectionElement = element.addElement("LanguageSelection");
		ParameterGroupTreeNode parameterGroupTreeNode = tpSelection.getParameterGroupTreeNode();
		selectionElement.addAttribute("id", Long.valueOf(tpSelection.getId()).toString());
        selectionElement.addAttribute("dna", tpSelection.getDna());
		selectionElement.addAttribute("langcode", tpSelection.getMessagepointLocale().getLanguageCode());
		if(parameterGroupTreeNode != null) {
		    selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
		}
		
		if ( parameterGroupTreeNode.getParameterGroupInstanceCollection() != null ) 
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
		}
		
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addCDATA(parameterGroupTreeNode.getName());
		
		List<LanguageSelection> childSelections = tpSelection.getChildrenOrderByName();
		if ( childSelections != null && !childSelections.isEmpty())
		{
			for (LanguageSelection childSelection : childSelections) 
			{
				createLanguageManagementForTP(childSelection, selectionElement);
			}
		}		
	}

	private static void createSelectionsForTP(TouchpointSelection tpSelection, Element element, ExportDocumentToXMLService edtXMLs, Document doc) 
	{
		Element selectionElement = element.addElement("Selection");
		ParameterGroupTreeNode parameterGroupTreeNode = tpSelection.getParameterGroupTreeNode();
		selectionElement.addAttribute("id", Long.valueOf(tpSelection.getId()).toString());
        selectionElement.addAttribute("dna", tpSelection.getDna());
		if(parameterGroupTreeNode != null) {
	        selectionElement.addAttribute("pgtndna", parameterGroupTreeNode.getDna());
            selectionElement.addAttribute("pgtnguid", parameterGroupTreeNode.getGuid());
		}
		if ( parameterGroupTreeNode.getParameterGroupInstanceCollection() != null ) 
		{
			selectionElement.addAttribute("datavaluerefid", Long.valueOf(parameterGroupTreeNode.getParameterGroupInstanceCollection().getId()).toString());
		}
		if(tpSelection.getAlternateLayout() != null) {
			selectionElement.addAttribute("layoutrefid", toString(tpSelection.getAlternateLayout().getId()));
		}
		Element selectionNameElement = selectionElement.addElement("Name");
		selectionNameElement.addCDATA(parameterGroupTreeNode.getName());

        Set<ContentObject> messageInstancesSet = new HashSet<>(ContentObject.findAllStructuredContentObjectsByTPVariantIncludingParents(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, tpSelection));
		
		Element selectionContentsElement = selectionElement.addElement("Contents");

		Set<Long> alreadyExportedMessageInstanceIDs = new HashSet<>();
        List<ContentObject> messageInstancesList = new ArrayList<>();
		
		if ( !messageInstancesSet.isEmpty() )
		{
            messageInstancesList.addAll(messageInstancesSet);
			Collections.sort(messageInstancesList, new ContentObjectIdComparator());
			
			for (ContentObject tpSelectableMessage : messageInstancesList)
			{
				if ( !tpSelectableMessage.isStructuredContentEnabled() )
				{
				    continue;
				}

				if(alreadyExportedMessageInstanceIDs.contains(tpSelectableMessage.getId())) {
					continue;
				}
				
				alreadyExportedMessageInstanceIDs.add(tpSelectableMessage.getId());

                if(edtXMLs.getExportOptions().isExportActiveCopyWhenBothActiveAndWorkingExist())
                    tpSelectableMessage.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
                else
                    tpSelectableMessage.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

                List<ContentObjectAssociation> nodeContent = ContentObjectAssociation.findAllByContentObjectAndParameters(tpSelectableMessage, tpSelectableMessage.getFocusOnDataType(), parameterGroupTreeNode);

                Element messageElement = selectionContentsElement.addElement("Message");
                messageElement.addAttribute("refid", Long.valueOf(tpSelectableMessage.getId()).toString());

                TouchpointSelection owningTouchpointSelection = tpSelectableMessage.getOwningTouchpointSelection();
                if(owningTouchpointSelection == null) {
                    owningTouchpointSelection = doc.getMasterTouchpointSelection();
                }
                if (tpSelectableMessage.isVariantType())
                {
                    ContentObjectZonePriority mesageZA = null;
                    int priority = 0;
                    if (tpSelectableMessage.getZone() != null)
                    {
                        mesageZA = ContentObjectZonePriority.findByContentObjectAndZoneUnique(tpSelectableMessage, tpSelectableMessage.getZone(), tpSelection);

                        if (mesageZA != null && tpSelection.getId() != owningTouchpointSelection.getId())
                        {
                            int mainPriority = tpSelectableMessage.getContentObjectData().getPriorityNumberByZone(tpSelectableMessage.getZone().getId());
                            int currentPriority = tpSelectableMessage.getContentObjectData().getPriorityNumberByZone(tpSelectableMessage.getZone().getId(), tpSelection);
                            if (mainPriority != currentPriority)
                                priority = currentPriority;
                        }
                    }

                    if ( priority > 0 )
                        messageElement.addAttribute("priority", toString(priority));
                    if (mesageZA != null && mesageZA.isRepeatWithNext()) {
                        messageElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                    }
                }

                boolean allSuppresed = nodeContent.stream().allMatch(coa->coa.getTypeId() == ContentAssociationType.ID_SUPPRESSES);
                boolean allEmpty = nodeContent.stream().allMatch(coa->coa.getTypeId() == ContentAssociationType.ID_EMPTY);
                boolean allReferences = nodeContent.stream().allMatch(coa->coa.getTypeId() == ContentAssociationType.ID_REFERENCES);

                Set<Long> referencedNodeIds =
                        nodeContent.stream()
                        .filter(coa->coa.getTypeId() == ContentAssociationType.ID_REFERENCES)
                        .map(coa->{
                            if(coa.getReferencingTouchpointPGTreeNode() != null) return coa.getReferencingTouchpointPGTreeNode().getId();
                            if(coa.getReferencingContentObjectPGTreeNode() != null) return coa.getReferencingContentObjectPGTreeNode().getId();
                            return 0L;
                        })
                        .collect(Collectors.toSet())
                        ;

                allReferences = allReferences && (referencedNodeIds.size() == 1);

                boolean custom = (! allSuppresed) && (! allEmpty) && (! allReferences) && (! nodeContent.isEmpty());

                if (!nodeContent.isEmpty())
                {
                    ContentObjectAssociation messageContentAssociation = nodeContent.get(0);
				    if (allSuppresed)
				    {
				        messageElement.addAttribute("suppress", Boolean.TRUE.toString());
	
				        // For Multi-part Message create empty Parts
					    if (tpSelectableMessage.isMultipartType()) 
					    	createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, true, edtXMLs, doc);
				    }
				    else if (custom)
				    {
				        messageElement.addAttribute("custom", Boolean.TRUE.toString());
				        createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, false, edtXMLs, doc);
				    }
				    else if (allReferences)
				    {
				        ParameterGroupTreeNode referencingTouchpointPGTreeNode = messageContentAssociation.getReferencingTouchpointPGTreeNode();
				        if(referencingTouchpointPGTreeNode != null) {
                            TouchpointSelection parentSelection = TouchpointSelection.findByPgTreeNodeId(referencingTouchpointPGTreeNode.getId());
                            messageElement.addAttribute("sameasselectionrefid", Long.valueOf(parentSelection.getId()).toString());
                        }

				        // For Multi-part Message create empty Parts
					    if (tpSelectableMessage.isMultipartType()) 
					    	createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, true, edtXMLs, doc);
					}
				    else if (allEmpty)
				    {
					    messageElement.addAttribute("empty", Boolean.TRUE.toString());
					    
				        // For Multi-part Message create empty Parts
					    if (tpSelectableMessage.isMultipartType()) 
					    	createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, true, edtXMLs, doc);
				    }
				    
					messageElement.addAttribute("islocal", tpSelectableMessage.getIsTouchpointLocal()?"true":"false");
				}
                else {

                    if (parameterGroupTreeNode != null && parameterGroupTreeNode.getParentNode() != null)
                    {
                        TouchpointSelection parentSelection = TouchpointSelection.findByPgTreeNodeId(parameterGroupTreeNode.getParentNode().getId());
                        messageElement.addAttribute("sameasselectionrefid", Long.valueOf(parentSelection.getId()).toString());

                        // For Multi-part Message create empty Parts
                        if (tpSelectableMessage.isMultipartType())
                            createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, true, edtXMLs, doc);
                    }
                    else {
                        messageElement.addAttribute("custom", Boolean.TRUE.toString());
                        createContentTagForSelection(tpSelectableMessage, nodeContent, parameterGroupTreeNode, messageElement, false, edtXMLs, doc);
                    }

                    messageElement.addAttribute("islocal", tpSelectableMessage.getIsTouchpointLocal()?"true":"false");
                }
			}
		}
		
		Element selectionMessagessElement = selectionElement.addElement("Messages");
		alreadyExportedMessageInstanceIDs = new HashSet<>();
        messageInstancesSet.clear();
        messageInstancesSet.addAll(ContentObject.findAllNotStructuredContentObjectsByTPVariantIncludingParents(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, tpSelection));

        if ( !messageInstancesSet.isEmpty() )
        {
            messageInstancesList.clear();
            messageInstancesList.addAll(messageInstancesSet);
            Collections.sort(messageInstancesList, new ContentObjectIdComparator());

			for (ContentObject tpSelectableMessage : messageInstancesList)
			{
				if ( !tpSelectableMessage.isVariantType() || tpSelectableMessage.isStructuredContentEnabled() )
				{
				    continue;
				}
				
				if(alreadyExportedMessageInstanceIDs.contains(tpSelectableMessage.getId())) {
					continue;
				}
				
				alreadyExportedMessageInstanceIDs.add(tpSelectableMessage.getId());

                if(edtXMLs.getExportOptions().isExportActiveCopyWhenBothActiveAndWorkingExist())
                    tpSelectableMessage.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
                else
                    tpSelectableMessage.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

                ContentObjectZonePriority mesageZA = null;
				int priority = 0;
				if (tpSelectableMessage.getZone() != null)
				{
					mesageZA = ContentObjectZonePriority.findByContentObjectAndZoneUnique(tpSelectableMessage, tpSelectableMessage.getZone(), tpSelection);
					
					if (mesageZA != null && tpSelection.getId() != tpSelectableMessage.getOwningTouchpointSelection().getId())
					{
						int mainPriority = tpSelectableMessage.getContentObjectData().getPriorityNumberByZone(tpSelectableMessage.getZone().getId());
						int currentPriority = tpSelectableMessage.getContentObjectData().getPriorityNumberByZone(tpSelectableMessage.getZone().getId(), tpSelection);
						if (mainPriority != currentPriority)
							priority = currentPriority;
					}
				}
				
				if (tpSelection.getId() == tpSelectableMessage.getOwningTouchpointSelection().getId() || (mesageZA != null && mesageZA.isSuppress()) || priority > 0)
				{					
					Element messageElement = selectionMessagessElement.addElement("Message");
					messageElement.addAttribute("refid", Long.valueOf(tpSelectableMessage.getId()).toString());
					
					if ((mesageZA != null && mesageZA.isSuppress())) 
					{
					    messageElement.addAttribute("suppress", Boolean.TRUE.toString());
					}

                    if (mesageZA != null && mesageZA.isRepeatWithNext()) {
                        messageElement.addAttribute("repeatwithnext", Boolean.TRUE.toString());
                    }

					if ( priority > 0 )
						messageElement.addAttribute("priority", toString(priority));
					
					messageElement.addAttribute("islocal", tpSelectableMessage.getIsTouchpointLocal()?"true":"false");
				}
			}
		}		
		
		MetadataForm metadataForm = tpSelection.getMetadataForm();
		if(metadataForm != null) {
			selectionElement.addAttribute("inheritmetadata", tpSelection.isInheritMetadata()?"1":"0");
			createMetadataForm(metadataForm, selectionElement, "Metadata");
		}
		
		List<TouchpointSelection> childSelections = tpSelection.getChildrenOrderByName();
		if ( childSelections != null && !childSelections.isEmpty())
		{
			for (TouchpointSelection childSelection : childSelections) 
			{
				createSelectionsForTP(childSelection, selectionElement, edtXMLs, doc);
			}
		}
	}
	
	public static void createContentTagForSelection(ContentObject contentObject, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportDocumentToXMLService edtXMLs, Document doc)
	{
	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled())
	    {
	        selectableMessage = true;
	    }

	    boolean masterSelection = selection == null || (!selectableMessage && ((selection.getParentNode() == null) || (contentObject.getOwningTouchpointSelection() != null && contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode() == selection)));

	    List<MessagepointLocale> languages = doc.getTouchpointLanguagesAsLocales();
        String defaultTouchpointLocaleCode = doc.getDefaultTouchpointLanguageLocaleCode();

	    if (contentObject.isMultipartType())
	    {
	    	contentsElement.addAttribute("contenttype", "Multi-part");

	    	Integer partNum = 1;
	        for (ZonePart zonePart : contentObject.getZone().getPartsInOrder())
	        {
	            Element partElement = contentsElement.addElement("Part");
	            partElement.addAttribute("num", (partNum++).toString());
				partElement.addAttribute("refid", toString(zonePart.getId()));
				partElement.addAttribute("zonerefid", toString(zonePart.getZone().getId()));
				
				if (emptyParts) continue;
				
	            Set<ContentObjectAssociation> partAssociations = getAssociationsForZonePart(zonePart, messageContentAssociations);
	            
            	boolean isPartEmpty = false;
    	        for (MessagepointLocale locale : languages) 
                {
                	if (isPartEmpty)
                		break;
	            
                    String languageCode = locale.getLanguageCode();
                    String localeCode = locale.getCode();

					boolean missingFlag = true;
		            for( ContentObjectAssociation ca : partAssociations )
		            {
		                if ( masterSelection && ca.getPGTreeNode() != null )
		                    continue;

                        if ( !masterSelection && ca.getPGTreeNode() != selection )
                            continue;

		                if ( ca.getTypeId() == ContentAssociationType.ID_EMPTY || ( ca.getTypeId() == ContentAssociationType.ID_OWNS && ca.getContent() == null && ca.getReferencingImageLibrary() == null)  )
		                {
		                    partElement.addAttribute("empty", Boolean.TRUE.toString());
		                    isPartEmpty = true;
		                    break;
		                }
		                else
		                {

	                        if ( ca.getMessagepointLocale().getId() != locale.getId() )
	                            continue;

	                        String languageContent = "";
	                        String langImageName = "";
	                        String langFileName = "";
	                        String langImageUploaded = "";
						    ComplexValue imageLink = null;
	                        
	                        boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
	                        Content content = ca.getContent();

	                        if (zonePart.getContentType().getId() == ContentType.TEXT)
	                        {
	    	        	    	partElement.addAttribute("contenttype", "Text");
	                            if ( content != null )
	                            {
	                                if ( content.getEncodedContent() != null )
	                                {
	                                    languageContent = content.getEncodedContent();
	                                }
	                            }
                                else if (ca.getReferencingImageLibrary() == null)
	                            {
	                                sameAsParent = true;
	                            }
	                        }
	                        else // graphic type
	                        {
	    	        	    	partElement.addAttribute("contenttype", "Graphic");
	                            if ( content != null )
	                            {
	    							imageLink = content.getImageLink();
	                                if ( content.getImageName() != null )
	                                {
	                                	langFileName = content.getImageName();	
	                                    if ( content.getImageLocation() != null )
	                                    {
	    			                		if (edtXMLs.getIncludeImagePathOnly())
	    			                		{
	    			                			languageContent = content.getRelativeImageLocation();
	    			                		}
	    			                		else
	    			                		{
	    			                			languageContent = encodeFile(content.getImageLocation());
	    			                		}
	                                    }
	                                    if ( content.getAppliedImageFilename() != null )
	                                        langImageName = content.getAppliedImageFilename();
	                                    
	                                    if ( content.getImageUploadedDate() != null )
	                                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
	                                    else
	                                        langImageUploaded = "";
	                                }
	                            }
                                else if (ca.getReferencingImageLibrary() == null)
	                            {
	                                sameAsParent = true;
	                            }
	                        }

	                        edtXMLs.addContentImageDatabaseFiles(doc, content);
	                        
							missingFlag = false;
	                        Element contentElement = partElement.addElement("Content");
	                        contentElement.addAttribute("language", languageCode);
                            contentElement.addAttribute("locale", localeCode);

	                        if (selectableMessage && sameAsParent) 
	                        {
	                            contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
	                        }
	                        else {
                                if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                                    contentElement.addAttribute("isdefault", Boolean.TRUE.toString());

                                if (ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode)) {
                                    contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                                } else if (ca.getReferencingImageLibrary() != null) {
                                    if (ca.getReferencingImageLibrary().isGlobalContentObject())
                                        contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
                                    else
                                        contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
                                } else {
                                    if (!langFileName.isEmpty()) {
                                        contentElement.addAttribute("filename", langFileName);
                                        if (edtXMLs.getIncludeImagePathOnly()) {
                                            contentElement.addAttribute("pathonly", "true");
                                            edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
                                        }
                                    }

                                    if (!langImageName.isEmpty())
                                        contentElement.addAttribute("imagename", langImageName);

                                    if (!langImageUploaded.isEmpty())
                                        contentElement.addAttribute("uploaded", langImageUploaded);

                                    contentElement.addCDATA(languageContent);

                                    if (imageLink != null) {
                                        Element imageLinkElement = partElement.addElement("ContentOfImageLink");
                                        imageLinkElement.addAttribute("language", languageCode);
                                        imageLinkElement.addAttribute("locale", localeCode);
                                        imageLinkElement.addAttribute("guid", imageLink.getGuid());
                                        imageLinkElement.addCDATA(imageLink.getEncodedValue());
                                    }

                                    if (content != null) {
                                        edtXMLs.addImageAltText(content.getImageAltText(), partElement, languageCode, localeCode);
                                        edtXMLs.addImageExtLink(content.getImageExtLink(), partElement, languageCode, localeCode);
                                        edtXMLs.addImageExtPath(content.getImageExtPath(), partElement, languageCode, localeCode);
                                    }
                                }
                            }
	                    }
	                }
					if (missingFlag && !isPartEmpty)
					{
						Element contentElement = partElement.addElement("Content");
				        contentElement.addAttribute("language", languageCode);
                        contentElement.addAttribute("locale", localeCode);
		                if (selectableMessage)
		                {
		                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
		                }
		                else
		                {
                            if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                                contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                            else
                                contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
		                }
					}							            
	            }
	        }
	    }
	    else 
	    {   // Selectable Regular Text or Graphic Message (not multipart)
	        for (MessagepointLocale locale : languages) 
	        {
	            String languageCode = locale.getLanguageCode();
                String localeCode = locale.getCode();
				boolean missingFlag = true;
	            for( ContentObjectAssociation ca : messageContentAssociations )
	            {
	                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
	                {
	                    continue;
	                }

	                String languageContent = "";
                    boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
	                Content content = ca.getContent();
                    String langImageName = "";
                    String langFileName = "";
                    String langImageUploaded = "";
				    ComplexValue imageLink = null;

	                if (contentObject.getContentType().getId() == ContentType.TEXT)
	                {
	        	    	contentsElement.addAttribute("contenttype", "Text");
	                    if ( content == null/*|| content.getEncodedContent() == null*/ )
	                    {
                            sameAsParent = true;
	                    }
	                    else
	                    {
	                        if(content.getEncodedContent() != null) {
                                languageContent = content.getEncodedContent();
                            }
	                    }
	                }
	                else
	                {
	        	    	contentsElement.addAttribute("contenttype", "Graphic");
	                    if ( content != null)
	                    {
							imageLink = content.getImageLink();
                        	if ( content.getImageName() != null )
                        		langFileName = content.getImageName();
                        	if ( content.getImageLocation() != null )
                        	{
		                		if (edtXMLs.getIncludeImagePathOnly())
		                		{
		                			languageContent = content.getRelativeImageLocation();
		                		}
		                		else
		                		{
		                			languageContent = encodeFile(content.getImageLocation());
		                		}
                        	}
	                        if ( content.getAppliedImageFilename() != null )
	                            langImageName = content.getAppliedImageFilename();

	                        if ( content.getImageUploadedDate() != null )
	                            langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
	                        else
	                            langImageUploaded = "";
	                    }
	                    else if(ca.getReferencingImageLibrary() == null)
	                    {
	                        sameAsParent = true;
	                    }
	                }

	                edtXMLs.addContentImageDatabaseFiles(doc, content);

					missingFlag = false;
	                Element contentElement = contentsElement.addElement("Content");
	                contentElement.addAttribute("language", languageCode);
                    contentElement.addAttribute("locale", localeCode);

	                if (sameAsParent && selectableMessage)
	                {
	                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
	                }
                    else {
                        if (localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                            contentElement.addAttribute("isdefault", Boolean.TRUE.toString());

                        if (ca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT && !localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                        {
                            contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                        }
                        else if (ca.getReferencingImageLibrary() != null)
                        {
                            if (ca.getReferencingImageLibrary().isGlobalContentObject())
                                contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
                            else
                                contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
                        }
                        else
                        {
                            if (!langFileName.isEmpty())
                            {
                                contentElement.addAttribute("filename", langFileName);
                                if (edtXMLs.getIncludeImagePathOnly())
                                {
                                    contentElement.addAttribute("pathonly", "true");
                                    edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
                                }
                            }

                            if (!langImageName.isEmpty())
                                contentElement.addAttribute("imagename", langImageName);

                            if (!langImageUploaded.isEmpty())
                                contentElement.addAttribute("uploaded", langImageUploaded);

                            contentElement.addCDATA(languageContent);

                            if (imageLink != null)
                            {
                                Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
                                imageLinkElement.addAttribute("language", languageCode);
                                imageLinkElement.addAttribute("locale", localeCode);
                                imageLinkElement.addAttribute("guid", imageLink.getGuid());
                                imageLinkElement.addCDATA(imageLink.getEncodedValue());
                            }

                            if(content != null) {
                                edtXMLs.addImageAltText(content.getImageAltText(), contentsElement, languageCode, localeCode);
                                edtXMLs.addImageExtLink(content.getImageExtLink(), contentsElement, languageCode, localeCode);
                                edtXMLs.addImageExtPath(content.getImageExtPath(), contentsElement, languageCode, localeCode);
                            }
                        }
                    }
	            }
				if (missingFlag)
				{
	                Element contentElement = contentsElement.addElement("Content");
	                contentElement.addAttribute("language", languageCode);
                    contentElement.addAttribute("locale", localeCode);
	                if (selectableMessage)
	                {
	                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
	                }
	                else
	                {
                        if (!localeCode.equalsIgnoreCase(defaultTouchpointLocaleCode))
                            contentElement.addAttribute("sameasdefaultlanguage", Boolean.TRUE.toString());
                        else
                            contentElement.addAttribute("isdefault", Boolean.TRUE.toString());
	                }
				}						            
	        }
	    }
	}

	private static Set<ContentObjectAssociation> getAssociationsForZonePart(ZonePart zp, List<ContentObjectAssociation> contAssocs)
	{
	    Set<ContentObjectAssociation> result = new HashSet<>();
	    for( ContentObjectAssociation ca : contAssocs )
	    {
	        if ( ca.getZonePart() != null && ca.getZonePart().getId() == zp.getId() )
	        {
	            result.add(ca);
	        }
	    }
	    return result;
	}
		

	////////////////////////////////////////////

	public static void createContentTagForEmbeddedContentSelection(ContentObject contentObject, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportDocumentToXMLService edtXMLs, Document doc, boolean includeAllLanguageLocalesForGlobalContentObjects)
	{
	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled()) 
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages;
        if (includeAllLanguageLocalesForGlobalContentObjects)
            languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        else
            languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
        {
            String languageCode = locale.getLanguageCode();
            String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langFileName = "";
                String langImageUploaded = "";

    	    	contentsElement.addAttribute("contenttype", "Text");
                if ( content == null/* || content.getEncodedContent() == null */)
                {
                    sameAsParent = true;
                }
                else
                {
                    if(content.getEncodedContent() != null) {
                        languageContent = content.getEncodedContent();
                    }
                }
                
                edtXMLs.addContentImageDatabaseFiles(doc, content);

                missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
                    if (!langFileName.isEmpty())
                    {
                        contentElement.addAttribute("filename", langFileName);
					    if (edtXMLs.getIncludeImagePathOnly())
					    {
						    contentElement.addAttribute("pathonly", "true");
						    edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
					    }
                    }

	                if (!langImageName.isEmpty())
	                    contentElement.addAttribute("imagename", langImageName);
	                
	                if (!langImageUploaded.isEmpty())
	                    contentElement.addAttribute("uploaded", langImageUploaded);
	                
                    contentElement.addCDATA(languageContent);
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
	
	public static void createContentTagForEmbeddedContentProductionSelection(ContentObject contentObject, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportDocumentToXMLService edtXMLs, Document doc, boolean includeAllLanguageLocalesForGlobalContentObjects)
	{
	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled())
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages;
        if (includeAllLanguageLocalesForGlobalContentObjects)
            languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        else
            languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
        {
            String languageCode = locale.getLanguageCode();
            String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langFileName = "";
                String langImageUploaded = "";

    	    	contentsElement.addAttribute("contenttype", "Text");
                if ( content == null/* || content.getEncodedContent() == null */)
                {
                    sameAsParent = true;
                }
                else
                {
                    if(content.getEncodedContent() != null) {
                        languageContent = content.getEncodedContent();
                    }
                }
                
                edtXMLs.addContentImageDatabaseFiles(doc, content);

                missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
                    if (!langFileName.isEmpty())
                    {
                        contentElement.addAttribute("filename", langFileName);
					    if (edtXMLs.getIncludeImagePathOnly())
					    {
						    contentElement.addAttribute("pathonly", "true");
						    edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
					    }
                    }

	                if (!langImageName.isEmpty())
	                    contentElement.addAttribute("imagename", langImageName);
	                
	                if (!langImageUploaded.isEmpty())
	                    contentElement.addAttribute("uploaded", langImageUploaded);
	                
                    contentElement.addCDATA(languageContent);
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
	
	////////////////////////////////////////////

	public static void createContentTagForContentLibrarySelection(ContentObject contentObject, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportDocumentToXMLService edtXMLs, Document doc, boolean includeAllLanguageLocalesForGlobalContentObjects)
	{
	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled())
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages;
        if (includeAllLanguageLocalesForGlobalContentObjects)
            languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        else
            languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages) 
        {
            String languageCode = locale.getLanguageCode();
            String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langFileName = "";
                String langImageUploaded = "";
			    ComplexValue imageLink = null;

    	    	contentsElement.addAttribute("contenttype", "Graphic");
                if ( content != null) 
                {
					imageLink = content.getImageLink();
                	if ( content.getImageName() != null )
                		langFileName = content.getImageName();
                	if ( content.getImageLocation() != null )
                	{
                		if (edtXMLs.getIncludeImagePathOnly())
                		{
                			languageContent = content.getRelativeImageLocation();
                		}
                		else
                		{
                			languageContent = encodeFile(content.getImageLocation());
                		}
                	}
                    if ( content.getAppliedImageFilename() != null )
                        langImageName = content.getAppliedImageFilename();
                    
                    if ( content.getImageUploadedDate() != null )
                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
                    else
                        langImageUploaded = "";
                } 
                else if (ca.getReferencingImageLibrary() == null)
                {
                    sameAsParent = true;
                }
                
                edtXMLs.addContentImageDatabaseFiles(doc, content);
                
				missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
		        	if (ca.getReferencingImageLibrary() != null)
		        	{
		        	    if (ca.getReferencingImageLibrary().isGlobalContentObject())
			        	    contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
		        	    else
                            contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
		        	}
		        	else
		        	{
	                    if (!langFileName.isEmpty())
	                    {
	                        contentElement.addAttribute("filename", langFileName);
						    if (edtXMLs.getIncludeImagePathOnly())
						    {
							    contentElement.addAttribute("pathonly", "true");
							    edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
						    }
	                    }
	
		                if (!langImageName.isEmpty())
		                    contentElement.addAttribute("imagename", langImageName);
		                
		                if (!langImageUploaded.isEmpty())
		                    contentElement.addAttribute("uploaded", langImageUploaded);
		                
	                    contentElement.addCDATA(languageContent);
	                    
	    				if (imageLink != null)
	    				{
	    					Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
	    					imageLinkElement.addAttribute("language", languageCode);
                            imageLinkElement.addAttribute("locale", localeCode);
	    					imageLinkElement.addAttribute("guid", imageLink.getGuid());
	    					imageLinkElement.addCDATA(imageLink.getEncodedValue());
	    				}
	    				
	    				if(content != null) {
	    				    edtXMLs.addImageAltText(content.getImageAltText(), contentsElement, languageCode, localeCode);
                            edtXMLs.addImageExtLink(content.getImageExtLink(), contentsElement, languageCode, localeCode);
                            edtXMLs.addImageExtPath(content.getImageExtPath(), contentsElement, languageCode, localeCode);
	    				}
		        	}
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
	
	public static void createContentTagForProductionContentLibrarySelection(ContentObject contentObject, List<ContentObjectAssociation> messageContentAssociations, ParameterGroupTreeNode selection, Element contentsElement, boolean emptyParts, ExportDocumentToXMLService edtXMLs, Document doc, boolean includeAllLanguageLocalesForGlobalContentObjects)
	{
	    boolean selectableMessage = false;
	    if (contentObject.isDynamicVariantEnabled()) 
	    {
	        selectableMessage = true;
	    }

        List<MessagepointLocale> languages;
        if (includeAllLanguageLocalesForGlobalContentObjects)
            languages = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();
        else
            languages = doc.getSystemDefaultAndTouchpointLanguagesAsLocales();

        for (MessagepointLocale locale : languages)
        {
            String languageCode = locale.getLanguageCode();
            String localeCode = locale.getCode();
			boolean missingFlag = true;
            for( ContentObjectAssociation ca : messageContentAssociations )
            {
                if ( ca.getMessagepointLocale() == null || ca.getMessagepointLocale().getId() != locale.getId() )
                {
                    continue;
                }

                String languageContent = "";
                boolean sameAsParent = ca.getTypeId() == ContentAssociationType.ID_REFERENCES;
                Content content = ca.getContent();
                String langImageName = "";
                String langFileName = "";
                String langImageUploaded = "";
			    ComplexValue imageLink = null;

    	    	contentsElement.addAttribute("contenttype", "Graphic");
                if ( content != null) 
                {
					imageLink = content.getImageLink();
                	if ( content.getImageName() != null )
                		langFileName = content.getImageName();
                	if ( content.getImageLocation() != null )
                	{
                		if (edtXMLs.getIncludeImagePathOnly())
                		{
                			languageContent = content.getRelativeImageLocation();
                		}
                		else
                		{
                			languageContent = encodeFile(content.getImageLocation());
                		}
                	}
                    if ( content.getAppliedImageFilename() != null )
                        langImageName = content.getAppliedImageFilename();
                    
                    if ( content.getImageUploadedDate() != null )
                        langImageUploaded = DateUtil.formatDateForXMLOutput(content.getImageUploadedDate());
                    else
                        langImageUploaded = "";
                } 
                else if(ca.getReferencingImageLibrary() == null)
                {
                    sameAsParent = true;
                }
                
                edtXMLs.addContentImageDatabaseFiles(doc, content);
                
				missingFlag = false;
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);

                if (sameAsParent && selectableMessage) 
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                } 
                else 
                {
		        	if (ca.getReferencingImageLibrary() != null)
		        	{
                        if (ca.getReferencingImageLibrary().isGlobalContentObject())
			        	    contentElement.addAttribute("contentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
                        else
                            contentElement.addAttribute("localcontentlibraryrefid", toString(ca.getReferencingImageLibrary().getId()));
		        	}
		        	else
		        	{
	                    if (!langFileName.isEmpty())
	                    {
	                        contentElement.addAttribute("filename", langFileName);
						    if (edtXMLs.getIncludeImagePathOnly())
						    {
							    contentElement.addAttribute("pathonly", "true");
							    edtXMLs.getOriginalImageFiles().add(content.getImageLocation());
						    }
	                    }
	
		                if (!langImageName.isEmpty())
		                    contentElement.addAttribute("imagename", langImageName);
		                
		                if (!langImageUploaded.isEmpty())
		                    contentElement.addAttribute("uploaded", langImageUploaded);
		                
	                    contentElement.addCDATA(languageContent);
	                    
	    				if (imageLink != null)
	    				{
	    					Element imageLinkElement = contentsElement.addElement("ContentOfImageLink");
	    					imageLinkElement.addAttribute("language", languageCode);
                            imageLinkElement.addAttribute("locale", localeCode);
	    					imageLinkElement.addAttribute("guid", imageLink.getGuid());
	    					imageLinkElement.addCDATA(imageLink.getEncodedValue());
	    				}
	    				
	    				if(content != null) {
	    				    edtXMLs.addImageAltText(content.getImageAltText(), contentsElement, languageCode, localeCode);
                            edtXMLs.addImageExtLink(content.getImageExtLink(), contentsElement, languageCode, localeCode);
                            edtXMLs.addImageExtPath(content.getImageExtPath(), contentsElement, languageCode, localeCode);
	    				}
		        	}
                }
            }
			if (missingFlag)
			{
                Element contentElement = contentsElement.addElement("Content");
                contentElement.addAttribute("language", languageCode);
                contentElement.addAttribute("locale", localeCode);
                if (selectableMessage)
                {
                    contentElement.addAttribute("sameasparent", Boolean.TRUE.toString());
                }
                else
                {
                	contentElement.addCDATA("");
                }
			}						            
        }
	}
	
	////////////////////////////////////////////
	
    private static void createLocation( 
            Element parent,
            ExportXMLUtils.Block block )
    {
        Element blockNode = parent.addElement("Block");
        Element tl = blockNode.addElement("TopLeft");
        tl.addAttribute( "x", toString(block.topx) );
        tl.addAttribute( "y", toString(block.topy) );
        tl.addAttribute( "dbx", toString(block.dbtopx) );
        tl.addAttribute( "dby", toString(block.dbtopy) );
        
        Element dim = blockNode.addElement("Dimensions");
        dim.addAttribute( "height", toString(block.height) );
        dim.addAttribute( "width", toString(block.width) );
        dim.addAttribute( "dbheight", toString(block.dbheight) );
        dim.addAttribute( "dbwidth", toString(block.dbwidth) );
    }

    private static void createConnectorData( Document doc, Element tpNode )
    {
        tpNode.addAttribute( "channel", toString(doc.getConnectorConfiguration().getChannel().getId()) );
        String connectorType = toString(doc.getConnectorConfiguration().getConnector().getId());
        
        ConnectorConfiguration cc = doc.getConnectorConfiguration();
        tpNode.addAttribute( "connectortype", connectorType );
        tpNode.addAttribute( "qualificationoutput", cc.getQualificationOutput() != null ? toString(cc.getQualificationOutput().getId()) : "" );
        tpNode.addAttribute( "inputcharacterencoding", toString(cc.getInputCharacterEncoding()));
        tpNode.addAttribute( "outputcharacterencoding", toString(cc.getOutputCharacterEncoding()));
        tpNode.addAttribute( "playmessageonemptyvar", cc.getPlayMessageOnEmptyVar()?"true":"false");
        tpNode.addAttribute( "usingimagenameinbundle", cc.getApplyFilenamesToBundledImages()?"true":"false");
        tpNode.addAttribute("separatorforbundledimages", cc.getFilenameSeparatorForBundledImages() != null ? cc.getFilenameSeparatorForBundledImages() : "");

        
		String xmlExportImportSecretKey1 = ApplicationUtil.getProperty(SystemPropertyKeys.XMLExportImport.KEY_TPExportImportSecretKey_1);
		AesUtil aes = new AesUtil(256);
		
        if(doc.getConnectorName() != null && ! doc.getConnectorName().isEmpty()) {
            tpNode.addAttribute( "connectorname", doc.getConnectorName() );
        }
        
        tpNode.addElement("CustomerDataFile").addCDATA(cc.getCustomerDriverInputFileName());
        
        tpNode.addAttribute( "executeInCloudTest",       cc.isExecuteInCloudTest()?       "true":"false");
        tpNode.addAttribute( "executeInCloudPreview",    cc.isExecuteInCloudPreview()?    "true":"false");
        tpNode.addAttribute( "executeInCloudProof",      cc.isExecuteInCloudProof()?      "true":"false");
        tpNode.addAttribute( "executeInCloudSimulation", cc.isExecuteInCloudSimulation()? "true":"false");
        
        tpNode.addElement("PreQualEngineScript")  .addCDATA(cc.getPreQualEngineScript());
        tpNode.addElement("PostQualEngineScript") .addCDATA(cc.getPostQualEngineScript());
        tpNode.addElement("PostConnectorScript")  .addCDATA(cc.getPostConnectorScript());

        if(cc.isOverrideRemote()) {
            tpNode.addAttribute( "overrideRemote", "true");
            Element remoteElement = tpNode.addElement("Remote");
            remoteElement.addElement("RemoteServerIP").addCDATA(cc.getRemoteServerIP());
            remoteElement.addElement("RemoteServerPort").addCDATA(cc.getRemoteServerPort());
            remoteElement.addElement("RemoteServerUser").addCDATA(cc.getRemoteServerUser());
        	try {
        		String encryptedPassword = aes.encrypt(cc.getRemoteServerPassword(), xmlExportImportSecretKey1);
//        		remoteElement.addElement("RemoteServerEncryptedPassword1").addCDATA(encryptedPassword);
        	} catch (Exception ex) {
        		
        	}
        }
        
        if(cc.getValidateProductionBundle() != null) {
        	tpNode.addAttribute("validateProductionBundle", cc.getValidateProductionBundle() ? "true" : "false");
        }

        if(cc.getEscapeTagsInDriverData() != null) {
            tpNode.addAttribute("escapeTagsInDriverData", cc.getEscapeTagsInDriverData() ? "true" : "false");
        }

        if(cc.getConvertTableBorderPxToPts() != null) {
            tpNode.addAttribute("convertTableBorderPxToPts", cc.getConvertTableBorderPxToPts() ? "true" : "false");
        }

        if(cc.getEvalNotEqualOnMissingTag() != null) {
            tpNode.addAttribute("evalNotEqualOnMissingTag", cc.getEvalNotEqualOnMissingTag() ? "true" : "false");
        }

        if(cc.getPlayEmptyAggFirstLastVar() != null) {
            tpNode.addAttribute("playEmptyAggFirstLastVar", cc.getPlayEmptyAggFirstLastVar() ? "true" : "false");
        }

        if(cc.getRemoveZeroFromStyleConnector() != null) {
            tpNode.addAttribute("removeZeroFromStyleConnector", cc.getRemoveZeroFromStyleConnector() ? "true" : "false");
        }

        if(cc.getCompTimeParentTagging() != null) {
            tpNode.addAttribute("compTimeParentTagging", cc.getCompTimeParentTagging() ? "true" : "false");
        }

        if(cc.getDataGroupExpressionVarProc() != null) {
            tpNode.addAttribute("dataGroupExpressionVarProc", cc.getDataGroupExpressionVarProc() ? "true" : "false");
        }

        if(cc.getScriptVarAppliesUndefined() != null) {
            tpNode.addAttribute("scriptVarAppliesUndefined", cc.getScriptVarAppliesUndefined() ? "true" : "false");
        }

        if(cc.getCorrectParagraphTextStyles() != null) {
            tpNode.addAttribute("correctParagraphTextStyles", cc.getCorrectParagraphTextStyles() ? "true" : "false");
        }

        if(cc.getFixInlineTargetingStyles() != null) {
            tpNode.addAttribute("fixInlineTargetingStyles", cc.getFixInlineTargetingStyles() ? "true" : "false");
        }

        if(cc.getPreserveDataWhitespace() != null) {
            tpNode.addAttribute("preserveDataWhitespace", cc.getPreserveDataWhitespace() ? "true" : "false");
        }

        if(cc.getNbspComposedAsSpace() != null) {
            tpNode.addAttribute("nbspComposedAsSpace", cc.getNbspComposedAsSpace() ? "true" : "false");
        }

        if(cc.getNormalizeImageLibrary() != null) {
            tpNode.addAttribute("normalizeImageLibrary", cc.getNormalizeImageLibrary() ? "true" : "false");
        }
        if(cc.getNormalizeEmbeddedContent() != null) {
            tpNode.addAttribute("normalizeEmbeddedContent", cc.getNormalizeEmbeddedContent() ? "true" : "false");
        }
        if(cc.getGmcSpanToTTag() != null) {
            tpNode.addAttribute("gmcSpanToTTag", cc.getGmcSpanToTTag() ? "true" : "false");
        }
        if(cc.getBlueUnderlineLinks() != null) {
            tpNode.addAttribute("blueUnderlineLinks", cc.getBlueUnderlineLinks() ? "true" : "false");
        }
        if(cc.getUnalteredZonePDFPassthrough() != null) {
            tpNode.addAttribute("unalteredZonePDFPassthrough", cc.getUnalteredZonePDFPassthrough() ? "true" : "false");
        }

        if(cc.getListStyleControlType() > 0) {
            tpNode.addAttribute("liststylecontroltype", toString(cc.getListStyleControlType()));
        }

        if(cc.getColorOutputFormatType() > 0) {
            tpNode.addAttribute("coloroutputformattype", toString(cc.getColorOutputFormatType()));
        }

        tpNode.addAttribute("usedefaultimage", toString(cc.getUseDefaultImage()));

        if ( cc instanceof DialogueConfiguration )
        {
            DialogueConfiguration dc = (DialogueConfiguration)(cc);
            tpNode.addAttribute( "supportsstyles", toString(dc.isSupportsStyles()) );
            tpNode.addAttribute("legacydxfmode", toString(dc.isLegacyDxfMode()) );
            tpNode.addAttribute("mixeddxftaggedtext", toString(dc.isMixedDxfTaggedText()) );
            tpNode.addAttribute("runtimedxf", toString(dc.isRunTimeDxf()) );
            
            tpNode.addElement("PublicationFile").addCDATA(dc.getPubFile());
            
        }
        else if ( cc instanceof GMCConfiguration )
        {
            
            GMCConfiguration gc = (GMCConfiguration)(cc);
            tpNode.addAttribute( "restrictedquotes", toString(gc.getRestrictedQuotes()) );
            tpNode.addAttribute( "supportsstyles", toString(gc.getControlStyles()) );

            tpNode.addElement("WorkflowFile").addCDATA(gc.getWorkflowFile());
        }
        else if ( cc instanceof EMessagingConfiguration )
        {
            EMessagingConfiguration ec = (EMessagingConfiguration)(cc);
           
            if (ec.getCustomerEmailAddressVariable() != null)
            	tpNode.addElement("CustomerEmailAddress").addAttribute("variablerefid", toString(ec.getCustomerEmailAddressVariable().getId()));
            if (ec.getCustomerPhoneNumberVariable() != null)
            	tpNode.addElement("CustomerPhoneNumber").addAttribute("variablerefid", toString(ec.getCustomerPhoneNumberVariable().getId()));
        }
        else if ( cc instanceof SendmailConfiguration )
        {
             
            SendmailConfiguration smc = (SendmailConfiguration)(cc);
            
            if (smc.getCustomerEmailAddressVariable() != null)
            	tpNode.addElement("CustomerEmailAddress").addAttribute("variablerefid", toString(smc.getCustomerEmailAddressVariable().getId()));
            
            if(smc.isOverrideSMTP()) {
            	tpNode.addAttribute("overridesmtp", "true");
            	Element smtpElement = tpNode.addElement("SMTP");
            	smtpElement.addElement("SMTPHost").addCDATA(smc.getSmtpHost());
            	smtpElement.addElement("SMTPPort").addCDATA(smc.getSmtpPort());
            	smtpElement.addElement("SMTPSecurity").addCDATA(smc.getSmtpSecurity());
            	smtpElement.addElement("SMTPAccount").addCDATA(smc.getSmtpAccount());
            	try {
            		String encryptedPassword = aes.encrypt(smc.getSmtpPassword(), xmlExportImportSecretKey1);
//            		smtpElement.addElement("SMTPEncryptedPassword1").addCDATA(encryptedPassword);
            	} catch (Exception ex) {
            		
            	}
            	smtpElement.addElement("SMTPCustomHeader").addCDATA(smc.getSmtpCustomHeader());
            }
            
        }
        else if ( cc instanceof ExactTargetConfiguration )
        {
            
            ExactTargetConfiguration smc = (ExactTargetConfiguration)(cc);
            
            if (smc.getCustomerEmailAddressVariable() != null)
            	tpNode.addElement("CustomerEmailAddress").addAttribute("variablerefid", toString(smc.getCustomerEmailAddressVariable().getId()));
            if (smc.getCustomerKeyVariable() != null)
            	tpNode.addElement("CustomerKey").addAttribute("variablerefid", toString(smc.getCustomerKeyVariable().getId()));
        }
        else if ( cc instanceof ClickatellConfiguration )
        {
            ClickatellConfiguration cac = (ClickatellConfiguration)(cc);
            
            if (cac.getCustomerPhoneNumberVariable() != null)
            	tpNode.addElement("CustomerPhoneNumber").addAttribute("variablerefid", toString(cac.getCustomerPhoneNumberVariable().getId()));
        }   
        else if ( cc instanceof FtpConfiguration )
        {
            FtpConfiguration ftpC = (FtpConfiguration)(cc);
            Element htmlFile = tpNode.addElement("HtmlFileNameOutput").addAttribute("landingpage", String.valueOf(!ftpC.getIsEmbedded()));
            if(ftpC.getRecipientFileComplexValue()!=null) {
            	htmlFile.addCDATA(ftpC.getRecipientFileComplexValue().getEncodedValue());
            }
            tpNode.addElement("WebURL").addCDATA(ftpC.getWebURL());
            
			long serverId = ftpC.getServerId();
			SystemPropertyManager spManager = SystemPropertyManager.getInstance();
			
			String host = spManager.getSystemProperty("ftp" + serverId + ".host");
			if (host == null)
				host = "";

			String user = spManager.getSystemProperty("ftp" + serverId + ".user");
			if (user == null)
				user = "";

			String password = spManager.getSystemProperty("ftp" + serverId + ".password");
			if (password == null)
				password = "";
			
			Element ftpServe = tpNode.addElement("FTPServer");
			ftpServe.addAttribute("serverid", toString(serverId));
			ftpServe.addAttribute("host", host);
			ftpServe.addAttribute("port", "21");
			ftpServe.addElement("User").addCDATA(user);
//			ftpServe.addElement("Password").addCDATA(password);
        	try {
        		String encryptedPassword = aes.encrypt(password, xmlExportImportSecretKey1);
//    			ftpServe.addElement("EncryptedPassword1").addCDATA(encryptedPassword);
        	} catch (Exception ex) {
        		
        	}
			ftpServe.addElement("UploadLocation").addCDATA(ftpC.getRecipientFileLocation());		
        }
        else if ( cc instanceof SefasConfiguration ) {
            SefasConfiguration sc = (SefasConfiguration) cc;
            if(sc.getCompositionVersion() != null) {
                tpNode.addAttribute("compositionversion", sc.getCompositionVersion());
            }
            if(sc.getOutputFileType() != null) {
                tpNode.addAttribute("outputfiletype", toString(sc.getOutputFileType()));
            }
            if(sc.getStartsOnOddPage() != null) {
                tpNode.addAttribute("startsonoddpage", toString(sc.getStartsOnOddPage()));
            }
            if(sc.getDuplexOutput() != null) {
                tpNode.addAttribute( "duplexoutput", toString(sc.getDuplexOutput()) );
            }
            if(sc.getTemplateControl() != null) {
                tpNode.addAttribute("templatecontrol", toString(sc.getTemplateControl()));
            }
            if(sc.getAccessibility() != null){
                tpNode.addAttribute(ACCESSIBILITY, toString(sc.getAccessibility()));
            }
            if(sc.getLinespacePosition() != null) {
                tpNode.addAttribute("linespaceposition",toString(sc.getLinespacePosition()));
            }
            if(sc.getTablePaddingInPts() != null) {
                tpNode.addAttribute("tablepaddinginpts",toString(sc.getTablePaddingInPts()));
            }
        } else if ( cc instanceof MPHCSConfiguration ) {
            MPHCSConfiguration mc = (MPHCSConfiguration) cc;
            if(mc.getCompositionVersion() != null) {
                tpNode.addAttribute("compositionversion", mc.getCompositionVersion());
            }
            if(mc.getOutputFileType() != null) {
                tpNode.addAttribute("outputfiletype", toString(mc.getOutputFileType()));
            }
            if(mc.getStartsOnOddPage() != null) {
                tpNode.addAttribute("startsonoddpage", toString(mc.getStartsOnOddPage()));
            }
            if(mc.getDuplexOutput() != null) {
                tpNode.addAttribute( "duplexoutput", toString(mc.getDuplexOutput()) );
            }
            if(mc.getTemplateControl() != null) {
                tpNode.addAttribute("templatecontrol", toString(mc.getTemplateControl()));
            }
            if(mc.getAccessibility() != null){
                tpNode.addAttribute(ACCESSIBILITY, toString(mc.getAccessibility()));
            }
            if(mc.getLinespacePosition() != null) {
                tpNode.addAttribute("linespaceposition",toString(mc.getLinespacePosition()));
            }
            if(mc.getTablePaddingInPts() != null) {
                tpNode.addAttribute("tablepaddinginpts",toString(mc.getTablePaddingInPts()));
            }
        }
    }
        
    private static String toString (boolean value )
    {
        if ( value )
            return "true";
        return "false";                 
    }
    
    private static String toString( long value )
    {
        String val = Long.valueOf(value).toString();
        return val;
    }
}
