package com.prinova.messagepoint.platform.services.export;

import com.google.gson.*;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskType;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.imports.ImportJSONtoContentService;
import com.prinova.messagepoint.platform.services.imports.JsonContentObject;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ExportContentToJSONService extends AbstractService {
    public static final String SERVICE_NAME = "export.ExportContentToJSONService";

    private static final Log log = LogUtil.getLog(ExportContentToJSONService.class);
    private StatusPollingBackgroundTask statusPollingBackgroundTask;
    private String podID;
    private String requestGUID = RandomGUID.getGUID();
    private String domainGUID;
    private String instanceGUID;
    private String sourceLocaleCode;
    private String targetLocaleCode;
    private String jsonCreated;
    private String touchpointGUID = "N/A - GlobalContentObject";
    private String touchpointDNA = "N/A - GlobalContentObject";
    private String touchpointName = "N/A - GlobalContentObject";
    private boolean exportAllContentsToOneJSONFile = false;
    private JsonContentObject failedImportJsonContentObject;

    private String rejectionReason;

    @Override
    // This service should not use a transaction, but any services under it will need one.
    @Transactional(propagation = Propagation.SUPPORTS, readOnly = false)
    public void execute(ServiceExecutionContext context) {
        try {
            validate(context);
            if (hasValidationError(context)) {
                return;
            }

            ExportContentToJSONServiceRequest request = (ExportContentToJSONServiceRequest) context.getRequest();
            this.statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();
            ContentObject targetContentObject = request.getTargeObjectId();
            Document targetDocument = request.getTargetDocumentId();
            List<Long> selectedIds = request.getSelectedIds();

            if (targetContentObject == null && targetDocument == null) {
                return;
            }

            if(request.getRequestGuid()!=null && !request.getRequestGuid().isEmpty()) {
                // Re-use the requestGuid if provided
                this.requestGUID = request.getRequestGuid();
            }
            this.podID = MessagepointMultiTenantConnectionProvider.getPodMasterCode();
            this.domainGUID = Node.getCurrentBranch().getDcsNode().getGuid();
            this.instanceGUID = Node.getCurrentNode().getGuid();
            this.sourceLocaleCode = (targetDocument == null && targetContentObject != null) ?
                  targetContentObject.getDefaultContentObjectLanguageAsLocale().getCode()
                : targetDocument.getDefaultTouchpointLanguageLocaleCode();
            this.targetLocaleCode = request.getExportOptions().getTargetLocaleCode();
            this.jsonCreated =  new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").format(new Date());
            this.exportAllContentsToOneJSONFile = request.getExportOptions().isIncludeImagePathOnly();    // Temporary use of this "isIncludeImagePathOnly" flag to determine if we should export all content to one JSON file or not
            this.failedImportJsonContentObject = request.getFailedImportJsonContentObject();
            this.rejectionReason = request.getRejectionReason();

            boolean exportOnlyWorkingContent = !request.getExportOptions().isTryActiveCopyWhenWorkingNotExist(); // Note that it is ! because the flag is "tryActiveData"

            User requestor = request.getRequestor();
            File outputDirectory = new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            }
            PathUtil zipFile = new PathUtil(outputDirectory.getAbsolutePath() + File.separator);

            String zipFileName = "GlobalContentObject";
            if(targetDocument == null) {
                if (targetContentObject.getDocument() != null) {
                    this.touchpointGUID = targetContentObject.getDocument().getGuid();
                    this.touchpointDNA = targetContentObject.getDocument().getDna();
                    this.touchpointName = targetContentObject.getDocument().getName();
                    zipFileName = this.touchpointName.replaceAll(" ", "");
                }
            } else {
                this.touchpointGUID = targetDocument.getGuid();
                this.touchpointDNA = targetDocument.getDna();
                this.touchpointName = targetDocument.getName();
                zipFileName = this.touchpointName.replaceAll(" ", "");
            }

            if(targetDocument == null && targetContentObject != null && selectedIds == null) {
                zipFileName = zipFileName + "_" + targetContentObject.getName().replaceAll(" ", "");
            }

            zipFileName = zipFileName + "_" + this.sourceLocaleCode + "_" + this.targetLocaleCode + "_" + this.requestGUID;

            if(StringUtils.isNotBlank(rejectionReason) || failedImportJsonContentObject != null){
                zipFileName = zipFileName + "_reject";
            }
            else{
                zipFileName = zipFileName + "_translate";
            }

            zipFile.append(zipFileName + ".zip");


            Set<ContentObject> contentObjects = new HashSet<>();
            if(targetContentObject == null && targetDocument != null) {
                contentObjects.addAll(ContentObject.findAllContentObjectsByDocument(targetDocument));
            } else {
                contentObjects.add(targetContentObject);
                if(selectedIds != null && !selectedIds.isEmpty()) {
                    for(Long coid : selectedIds) {
                        ContentObject co = ContentObject.findById(coid);
                        if(co != null) {
                            contentObjects.add(co);
                        }
                    }
                }
            }

            File returnFile = zipFile.file(false);
            ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(returnFile));
            Writer writer = new OutputStreamWriter(zos, StandardCharsets.UTF_8);

            for(ContentObject contentObject : contentObjects)
            {
                contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);
//                List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(co, ContentObject.DATA_TYPE_WORKING, null, co.getDefaultContentObjectLanguageAsLocale(), null, false, true, false, !tryActiveCopyWhenWorkingNotExist);

                // !!! shouldn't disableStructuredContent be false? Why this is related to exportOnlyWorkingContent?
                List<ContentObjectAssociation> coas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject,
                    (exportOnlyWorkingContent || contentObject.hasWorkingData() || (contentObject.isStructuredContentEnabled() && contentObject.getDocument() != null && contentObject.getDocument().isEnabledForVariantWorkflow()))? ContentObject.DATA_TYPE_WORKING : ContentObject.DATA_TYPE_ACTIVE,
                    null,
                    contentObject.getDefaultContentObjectLanguageAsLocale(),
                    null, false, true, false,
                    exportOnlyWorkingContent, false);

                Map<String, ContentObjectAssociation> coaIndexToSourceContent = new HashMap<>();
                for(ContentObjectAssociation coa : coas) {
                    String coaIndex = generateCoaIndex(coa);
                    coaIndexToSourceContent.put(coaIndex, coa);
                }

                coas.removeIf(coa -> !coa.getMessagepointLocale().getCode().equals(this.sourceLocaleCode) || coa.getContent() == null);

                Collections.sort(coas, (c1, c2)->{
                    {
                        int dataType1 = c1.getDataType();
                        int dataType2 = c2.getDataType();

                        if(dataType1 != dataType2){
                            return dataType1 - dataType2;
                        }
                    }

                    {
                        ParameterGroupTreeNode p1 = c1.getTouchpointPGTreeNode();
                        ParameterGroupTreeNode p2 = c2.getTouchpointPGTreeNode();

                        if (p1 == null && p2 != null) return -1;
                        if (p1 != null && p2 == null) return 1;

                        if (p1 != null && p2 != null && p1.getId() != p2.getId()) {
                            return ParameterGroupTreeNode.getFullPath(p1).compareTo(ParameterGroupTreeNode.getFullPath(p2));
                        }
                    }

                    {
                        ParameterGroupTreeNode p1 = c1.getContentObjectPGTreeNode();
                        ParameterGroupTreeNode p2 = c2.getContentObjectPGTreeNode();

                        if (p1 == null && p2 != null) return -1;
                        if (p1 != null && p2 == null) return 1;

                        if (p1 != null && p2 != null && p1.getId() != p2.getId()) {
                            return ParameterGroupTreeNode.getFullPath(p1).compareTo(ParameterGroupTreeNode.getFullPath(p2));
                        }
                    }

                    {
                        ZonePart zp1 = c1.getZonePart();
                        ZonePart zp2 = c2.getZonePart();

                        if(zp1 == null && zp2 != null) return -1;
                        if(zp1 != null && zp1 == null) return 1;

                        if(zp1 != null && zp2 != null && zp1.getId() != zp2.getId()) {
                            return Long.compare(zp1.getId(), zp2.getId());
                        }
                    }

                    return 0;
                });

                if (coas.isEmpty()) continue;

                MessagepointLocale targetLocale = MessagepointLocale.findByLocaleCode(targetLocaleCode);
                // !!! shouldn't disableStructuredContent be false? Why this is related to exportOnlyWorkingContent?
                List<ContentObjectAssociation> targetLocaleCoas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject,
                    (exportOnlyWorkingContent || contentObject.hasWorkingData() || (contentObject.isStructuredContentEnabled() && contentObject.getDocument() != null && contentObject.getDocument().isEnabledForVariantWorkflow()))? ContentObject.DATA_TYPE_WORKING : ContentObject.DATA_TYPE_ACTIVE,
                    null,
                    targetLocale,
                    null, false, true, false,
                    exportOnlyWorkingContent, false);

                targetLocaleCoas.removeIf(coa -> !coa.getMessagepointLocale().getCode().equals(this.targetLocaleCode) || coa.getContent() == null);

                // For export during workflow filter out variants that do not have tasks associated for the requested locale
                Map<String, String> mapPGTreeNodeDnaToTasksDescription = mapContentObjectVariantsToTasks(contentObject, coas, targetLocale);
                mapPGTreeNodeDnaToTasksDescription.putAll(mapContentObjectVariantsToTasks(contentObject, targetLocaleCoas, targetLocale));
                if(request.isExportFromWorkflow() && !contentObject.hasDirtyContentForDefaultLanguage()){
                    filterOutContentObjectAssociationsByTasks(contentObject, coas, mapPGTreeNodeDnaToTasksDescription);
                    filterOutContentObjectAssociationsByTasks(contentObject, targetLocaleCoas, mapPGTreeNodeDnaToTasksDescription);
                }

                Map<String,     ContentObjectAssociation> targetLocaleContents = new HashMap<>();
                for(ContentObjectAssociation coa : targetLocaleCoas) {
                    String coaIndex = generateCoaIndex(coa);
                    targetLocaleContents.put(coaIndex, coa);
                }

                if (this.exportAllContentsToOneJSONFile) {
                    String jsonFileName = contentObject.getId() + "_" + contentObject.getName().replaceAll(" ", "") + "_" + this.requestGUID + ".json";

                    // Create a new zip entry for the JSON content
                    ZipEntry zipEntry = new ZipEntry(jsonFileName);
                    zos.putNextEntry(zipEntry);

                    String jsonString = generateJSONExport(contentObject, coas, coaIndexToSourceContent, targetLocaleContents, mapPGTreeNodeDnaToTasksDescription);

                    // Write JSON string directly to the zip entry
                    writer.write(jsonString);

                    // It's important to call these to ensure everything is properly written and finalized
                    writer.flush();
                    zos.closeEntry();
                } else {
                    for (ContentObjectAssociation coa : coas) {
                        String jsonFileName = contentObject.getId() + "_" + contentObject.getName() + "_" + coa.getContent().getId() + "_" + coa.getMessagepointLocale().getCode() + "_" + this.targetLocaleCode + "_" + this.requestGUID + ".json";

                        // Create a new zip entry for the JSON content
                        ZipEntry zipEntry = new ZipEntry(jsonFileName);
                        zos.putNextEntry(zipEntry);

                        String jsonString = generateJSONExport(contentObject, new ArrayList<>() {{
                            add(coa);
                        }}, coaIndexToSourceContent, targetLocaleContents, mapPGTreeNodeDnaToTasksDescription);

                        // Write JSON string directly to the zip entry
                        writer.write(jsonString);

                        // It's important to call these to ensure everything is properly written and finalized
                        writer.flush();
                        zos.closeEntry();
                    }
                }
            }

            // It's important to close the writer and the zip output stream
            zos.close();

            ExportToExcelServiceResponse response = (ExportToExcelServiceResponse) context.getResponse();
            response.setFilePath(returnFile.getAbsolutePath());
            response.setRequestGUID(this.requestGUID);

        } catch (Exception ex) {
            log.error(" unexpected exception when invoking ExportContentToJSONService execute method", ex);
            this.getResponse(context).addErrorMessage(
                    "UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
                    ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
                    SERVICE_NAME + ex,
                    context.getLocale());
            throw new RuntimeException(ex.getMessage());
        }
    }

    /**
     * Filter out content object associations that do not have tasks associated for the requested locale
     */
    private void filterOutContentObjectAssociationsByTasks(ContentObject contentObject, List<ContentObjectAssociation> coas, Map<String, String> mapPGTreeNodeDnaToTasksDescription) {
        if(!mapPGTreeNodeDnaToTasksDescription.isEmpty() && !mapPGTreeNodeDnaToTasksDescription.containsKey("0")){
            coas.removeIf(coa -> {
                ParameterGroupTreeNode pgTreeNode = contentObject.isDynamicVariantEnabled() ? coa.getContentObjectPGTreeNode() : coa.getTouchpointPGTreeNode();
                if (pgTreeNode == null) {
                    return true;
                }
                return !mapPGTreeNodeDnaToTasksDescription.containsKey(pgTreeNode.getDna());
            });
        }
    }

    /**
     * Map content object variants to tasks description
     */
    private Map<String, String> mapContentObjectVariantsToTasks(ContentObject contentObject, List<ContentObjectAssociation> coas, MessagepointLocale targetLocale) {
        Map<String, String> mapPGTreeNodeDnaToTasksDescription = new HashMap<>();
        List<String> pgTreeNodeDNAsWithContent = new ArrayList<>();

        coas.forEach(coa -> {
            ParameterGroupTreeNode parameterGroupTreeNode = contentObject.isDynamicVariantEnabled() ? coa.getContentObjectPGTreeNode() : coa.getTouchpointPGTreeNode();
            if(parameterGroupTreeNode != null){
                pgTreeNodeDNAsWithContent.add(parameterGroupTreeNode.getDna());
            }
        });
        Set<Task> unfinishedTranslationTasks = getUnfinishedTranslationTasks(contentObject, targetLocale);
        if (!unfinishedTranslationTasks.isEmpty()) {
            unfinishedTranslationTasks.forEach(task -> {
                String pgTreeNodeDna = StringUtils.isNotBlank(task.getParameterGroupTreeNodeDNA()) ? task.getParameterGroupTreeNodeDNA() : "0";
                if("0".equalsIgnoreCase(pgTreeNodeDna) || pgTreeNodeDNAsWithContent.contains(pgTreeNodeDna)){
                    mapPGTreeNodeDnaToTasksDescription.merge(pgTreeNodeDna, ObjectUtils.firstNonNull(task.getRequirementStr(), ""), (oldValue, newValue) -> oldValue + ", " + newValue);
                }
            });
        }

        return mapPGTreeNodeDnaToTasksDescription;
    }


    private Set<Task> getUnfinishedTranslationTasks(ContentObject contentObject, MessagepointLocale targetLocale) {
        Set<Task> tasks = new HashSet<>(contentObject.getTasks());
        tasks.removeIf(task -> task.isComplete() || task.getTaskType() != TaskType.ID_TRANSLATE || (task.getMessagepointLocale() != null && task.getMessagepointLocale().getId() != targetLocale.getId()));

        return tasks;
    }

    private String generateJSONExport(ContentObject contentObject, List<ContentObjectAssociation> contentObjectAssociations,
                                      Map<String, ContentObjectAssociation> sourceLocaleContents,
                                      Map<String, ContentObjectAssociation> targetLocaleContents,
                                        Map<String, String> mapPGTreeNodeGuidToTasksDescription
    ) throws Exception {

        // Create an object to add 'request'
        JsonObject request = new JsonObject();
        request.addProperty("pod_id", this.podID);
        request.addProperty("request_guid", this.requestGUID);
        request.addProperty("domain_guid", this.domainGUID);
        request.addProperty("instance_guid", this.instanceGUID);
        request.addProperty("source_locale", this.sourceLocaleCode);
        request.addProperty("target_locale", this.targetLocaleCode);
        request.addProperty("json_created", this.jsonCreated);
        request.addProperty("touchpoint_guid", this.touchpointGUID);
        request.addProperty("touchpoint_dna", this.touchpointDNA);
        request.addProperty("touchpoint_name", this.touchpointName);
        addRequestErrors(request);


        // Create the 'sources' JsonArray
        JsonArray sources = new JsonArray();

        Set<String> coaIndexHandled = new HashSet<>();

        for (ContentObjectAssociation coa : contentObjectAssociations) {
            String coaIndex = generateCoaIndex(coa);
            ContentObjectAssociation targetLocaleCoa = targetLocaleContents.get(coaIndex);

            coaIndexHandled.add(coaIndex);

            ParameterGroupTreeNode pgTreeNode = coa.getPGTreeNode();
            String reasonForExport = mapPGTreeNodeGuidToTasksDescription != null && pgTreeNode != null ? mapPGTreeNodeGuidToTasksDescription.get(pgTreeNode.getGuid()) : null;
            Content content = coa.getContent();
            Content targetLocaleContent = targetLocaleCoa == null ? null : targetLocaleCoa.getContent();

            addContentItem(contentObject, coa, pgTreeNode, content, targetLocaleContent, sources, reasonForExport);
        }

        if(contentObject.isDynamicVariantEnabled()) {
            for (String coaIndex : targetLocaleContents.keySet()) {
                if (coaIndexHandled.contains(coaIndex)) {
                    continue;
                }
                coaIndexHandled.add(coaIndex);
                ContentObjectAssociation targetLocaleCoa = targetLocaleContents.get(coaIndex);
                ContentObjectAssociation coa = findSourceLocaleContentForDynamicContentObject(sourceLocaleContents, coaIndex);

                if (coa == null) {
                    continue;
                }
                Content content = coa.getContent();
                if (content == null) {
                    continue;
                }

                ParameterGroupTreeNode pgTreeNode = targetLocaleCoa.getPGTreeNode();
                String reasonForExport = mapPGTreeNodeGuidToTasksDescription != null && pgTreeNode != null ? mapPGTreeNodeGuidToTasksDescription.get(pgTreeNode.getGuid()) : null;
                Content targetLocaleContent = targetLocaleCoa == null ? null : targetLocaleCoa.getContent();
                addContentItem(contentObject, coa, pgTreeNode, content, targetLocaleContent, sources, reasonForExport);
            }
        }

        // Create the top-level object to hold everything
        JsonObject gsonTopLevelObject = new JsonObject();
        gsonTopLevelObject.add("request", request);
        gsonTopLevelObject.add("sources", sources);

        // Configure Gson for pretty printing and also disable HTML escaping
        Gson gson = new GsonBuilder().setPrettyPrinting().disableHtmlEscaping().create();

        // Serialize the JsonObject with pretty printing
        String prettyJson = gson.toJson(gsonTopLevelObject);

        return prettyJson;
    }

    private void addContentItem(ContentObject contentObject, ContentObjectAssociation coa, ParameterGroupTreeNode pgTreeNode, Content content, Content targetLocaleContent, JsonArray sources, String reasonForExport) throws IOException {
        // Create an object to add in 'sources' array
        JsonObject sourceItem = new JsonObject();
        sourceItem.addProperty("content_object_name", contentObject.getName());
        sourceItem.addProperty("content_object_guid", contentObject.getGuid());
        sourceItem.addProperty("content_object_dna", contentObject.getDna());

        if(contentObject.getOwningTouchpointSelection() != null) {
            sourceItem.addProperty("created_at_variant", contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode().getName());
        }
        else if(contentObject.isMessageOrTouchpointLocal()) {
            if(contentObject.getDocument().isEnabledForVariation()
                && contentObject.getDocument().getMasterTouchpointSelection() != null
                && contentObject.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode() != null)
            {
                sourceItem.addProperty("created_at_variant", contentObject.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode().getName());
            } else {
                sourceItem.addProperty("created_at_variant", "Master");
            }
        }

        String contentObjectVariantType = "Static";
        if(contentObject.isMessageOrTouchpointLocal() && contentObject.isStructuredContentEnabled()) {
            contentObjectVariantType = "Structured";
        }
        else if(contentObject.isDynamicVariantEnabled()) {
            contentObjectVariantType = "Dynamic";
        }
        else {
            contentObjectVariantType = "Static";
        }

        String contentObjectType = "Unknown";

        if(contentObject.isMessage()) {
            contentObjectType = contentObjectVariantType + " " + "Message";
        }
        else if(contentObject.isLocalSmartText()) {
            contentObjectType = contentObjectVariantType + " " + "Local Smart Text";
        }
        else if(contentObject.isLocalImage()) {
            contentObjectType = contentObjectVariantType + " " + "Local Image";
        }
        else if(contentObject.isGlobalSmartText()) {
            contentObjectType = "Global " + contentObjectVariantType + " " + "Smart Text";
        }
        else if(contentObject.isGlobalImage()) {
            contentObjectType = "Global " + contentObjectVariantType + " " + "Image";
        }

        sourceItem.addProperty("content_object_type", contentObjectType);

        if (pgTreeNode != null) {
            sourceItem.addProperty("variant_name", pgTreeNode.getName());
            if (coa.getTouchpointPGTreeNode() != null) {
                sourceItem.addProperty("tp_pg_tn_guid", pgTreeNode.getGuid());
                sourceItem.addProperty("tp_pg_tn_dna", pgTreeNode.getDna());
            } else {
                sourceItem.addProperty("co_pg_tn_guid", pgTreeNode.getGuid());
                sourceItem.addProperty("co_pg_tn_dna", pgTreeNode.getDna());
            }
        }
        else {
//                if (contentObject.isStructuredContentEnabled() && contentObject.getDocument() != null) {
//                    if(contentObject.getOwningTouchpointSelection() == null) {
//                        sourceItem.addProperty("variant_name", "Master");
////                        sourceItem.addProperty("tp_pg_tn_guid", contentObject.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode().getGuid());
////                        sourceItem.addProperty("tp_pg_tn_dna", contentObject.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode().getDna());
//                    } else {
//                        pgTreeNode = contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode();
//                        sourceItem.addProperty("variant_name", pgTreeNode.getName());
////                        sourceItem.addProperty("tp_pg_tn_guid", pgTreeNode.getGuid());
////                        sourceItem.addProperty("tp_pg_tn_dna", pgTreeNode.getDna());
//                    }
//                } else if(contentObject.isDynamicVariantEnabled(coa.getDataType())) {
//                    sourceItem.addProperty("variant_name", "Master");
//                }
        }

        if (coa.getZonePart() != null) {
            sourceItem.addProperty("zone_part_id", coa.getZonePart().getId());
            sourceItem.addProperty("zone_part_dna", coa.getZonePart().getDna());
        }

        if (content.getEncodedContent() != null) {
            sourceItem.addProperty("source_text", content.getEncodedContent());
            if(targetLocaleContent != null && targetLocaleContent.getEncodedContent() != null) {
                sourceItem.addProperty("target_currenttext", targetLocaleContent.getEncodedContent());
            }
            sourceItem.addProperty("source_type", "text_content");
        }
        else {
            if (content.getImageAltText() != null) {
                sourceItem.addProperty("source_text", content.getImageAltText().getEncodedValue());
                if(targetLocaleContent != null && targetLocaleContent.getImageAltText() != null) {
                    sourceItem.addProperty("target_currenttext", targetLocaleContent.getImageAltText().getEncodedValue());
                }
                sourceItem.addProperty("source_type", "img_alt_text");
            }
            // Content is empty and alternate text is empty too
            else{
                String sourceType = contentObject.isLocalImage() || contentObject.isGlobalImage() ? "img_alt_text" : "text_content";
                sourceItem.addProperty("source_text", "");
                if(targetLocaleContent != null && targetLocaleContent.getEncodedContent() != null) {
                    sourceItem.addProperty("target_currenttext", targetLocaleContent.getEncodedContent());
                }
                else if(targetLocaleContent != null && targetLocaleContent.getImageAltText() != null) {
                    sourceItem.addProperty("target_currenttext", targetLocaleContent.getImageAltText().getEncodedValue());
                }

                sourceItem.addProperty("source_type", sourceType);
            }
        }

        sourceItem.addProperty("source_content_id", content.getId());
        sourceItem.addProperty("source_content_hash", content.getSha256Hash());
        sourceItem.addProperty("source_content_last_updated", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").format(content.getUpdated()));
        sourceItem.addProperty("source_content_updated_by", content.getUpdatedBy());
        addSourceErrors(sourceItem);
        if(StringUtils.isNotBlank(rejectionReason)){
            sourceItem.addProperty("reason", reasonForExport);
        }

        // Add the sourceItem object to the sources array
        sources.add(sourceItem);
    }

    private ContentObjectAssociation findSourceLocaleContentForDynamicContentObject(Map<String, ContentObjectAssociation> sourceLocaleContents, String coaIndex) {
        while(true) {
            ContentObjectAssociation sourceLocaleContent = sourceLocaleContents.get(coaIndex);
            if (sourceLocaleContent == null) {
                return null;
            }

            if (sourceLocaleContent.getTypeId() == ContentAssociationType.ID_OWNS) {
                return sourceLocaleContent;
            }

            if (sourceLocaleContent.getTypeId() == ContentAssociationType.ID_REFERENCES) {
                ParameterGroupTreeNode refCoPgtn = sourceLocaleContent.getReferencingContentObjectPGTreeNode();
                coaIndex = generateCoaIndex(sourceLocaleContent.getContentObject(), sourceLocaleContent.getTouchpointPGTreeNode(), refCoPgtn, sourceLocaleContent.getZonePart());
            }
            else {
                return null;
            }
        }
    }

    private String generateCoaIndex(ContentObjectAssociation coa) {
        return generateCoaIndex(coa.getContentObject(), coa.getTouchpointPGTreeNode(), coa.getContentObjectPGTreeNode(), coa.getZonePart());
    }

    private String generateCoaIndex(ContentObject contentObject, ParameterGroupTreeNode tppgtn, ParameterGroupTreeNode copgtn, ZonePart zonePart) {
        long contentObjectId = contentObject.getId();
        // We are using dna instead of pgtn's id, because now in the contentObjectAssociations of a contentObjectData, the pgtn may come from other data type.
        // So if a contentObjectAssociation is referencing its ancestor, the referenced contentObjectAssociation's pgtn may not be in the same data type.
        // And the id of the pgtn of the referenced contentObjectAssociation may not be the id of the ancestor of the pgtn of the contentObjectAssociation.
        String tppgtnDna = tppgtn == null ? "Master" : tppgtn.getDna();
        String copgtnDna = copgtn == null ? "Master" : copgtn.getDna();
        long zonePartId = zonePart == null ? 0 : zonePart.getId();
        return Long.toString(contentObjectId) + "-" + tppgtnDna + "-" + copgtnDna + "-" + zonePartId;
    }

    private void addSourceErrors(JsonObject exportSource) throws IOException {
        if(this.failedImportJsonContentObject != null){
            boolean isExportSourceTpVariant = exportSource.has("tp_pg_tn_guid") || exportSource.has("tp_pg_tn_dna"),
                    isExportSourceDynamicVariant = exportSource.has("co_pg_tn_guid") || exportSource.has("co_pg_tn_dna");

            for(JsonContentObject.JsonContentSource importSource: this.failedImportJsonContentObject.getSources()){
                if(isExportSourceTpVariant){
                    // Import source is not a touchpoint variant - skip to next
                    if(!importSource.isTouchpointVariant())
                        continue;
                    // Import source is a touchpoint variant for a different variant than export one - skip to next
                    if(!importSource.getTpPgTnGuid().equalsIgnoreCase(exportSource.get("tp_pg_tn_guid").getAsString()) &&
                            !importSource.getTpPgTnDna().equalsIgnoreCase(exportSource.get("tp_pg_tn_dna").getAsString()))
                        continue;
                }
                else if(isExportSourceDynamicVariant){
                    // Import source is not a dynamic variant - skip to next
                    if(!importSource.isDynamicVariant())
                        continue;
                    // Import source is a dynamic variant for different variant than export one - skip to next
                    if(!importSource.getCoPgTnGuid().equalsIgnoreCase(exportSource.get("co_pg_tn_guid").getAsString()) &&
                            !importSource.getCoPgTnDna().equalsIgnoreCase(exportSource.get("co_pg_tn_dna").getAsString()))
                        continue;
                }
                else{
                    // Export source is for Master
                    // If import source is dynamic or touchpoint variant - skip to next one
                    if(importSource.isDynamicVariant() || importSource.isTouchpointVariant())
                        continue;
                }

                if(!importSource.isValid() || !this.failedImportJsonContentObject.isValid()){
                    exportSource.addProperty(ImportJSONtoContentService.IMPORT_FAILED_TARGET_TEXT, Objects.requireNonNullElse(importSource.getTargetText(), ""));
                    exportSource.addProperty(ImportJSONtoContentService.IMPORT_FAILURE_REASON, importSource.isValid() ? this.failedImportJsonContentObject.getMessagesAsString() : importSource.getMessagesAsString());
                }
                else{
                    exportSource.addProperty("target_text", Objects.requireNonNullElse(importSource.getTargetText(), ""));
                }
            }
        }
    }

    private void addRequestErrors(JsonObject request) throws IOException {
        if(StringUtils.isNotBlank(this.rejectionReason)){
            request.addProperty("rejection_reason", this.rejectionReason);
        }
    }


    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(String exportId, ContentObject targetObjectId, List<Long> selectedIds, ExportImportOptions exportImportOptions, User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportContentToJSONServiceRequest request = new ExportContentToJSONServiceRequest(exportId, targetObjectId, selectedIds, exportImportOptions, requestor, statusPollingBackgroundTask);

        context.setRequest(request);

        ExportToExcelServiceResponse serviceResp = new ExportToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    public static ServiceExecutionContext createContext(String exportId, ContentObject targetObjectId, ExportImportOptions exportImportOptions, User requestor, JsonContentObject failedImportJsonContentObject, String rejectionReason, String requestGUID) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportContentToJSONServiceRequest request = new ExportContentToJSONServiceRequest(exportId, targetObjectId, null, exportImportOptions, requestor, null);
        request.setFailedImportJsonContentObject(failedImportJsonContentObject);
        request.setRejectionReason(rejectionReason);
        request.setRequestGuid(requestGUID);

        context.setRequest(request);

        ExportToExcelServiceResponse serviceResp = new ExportToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }

    public static ServiceExecutionContext createContextForWorkflow(String exportId, ContentObject targetObjectId, ExportImportOptions exportImportOptions, User requestor, JsonContentObject failedImportJsonContentObject, String rejectionReason, String requestGUID) {
        ServiceExecutionContext context = createContext(exportId, targetObjectId, exportImportOptions, requestor, failedImportJsonContentObject, rejectionReason, requestGUID);
        ExportContentToJSONServiceRequest request = (ExportContentToJSONServiceRequest) context.getRequest();
        request.setExportFromWorkflow(true);

        return context;
    }

    public static ServiceExecutionContext createContext(String exportId, Document targetObjectId, List<Long> selectedIds, ExportImportOptions exportImportOptions, User requestor, StatusPollingBackgroundTask statusPollingBackgroundTask) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ExportContentToJSONServiceRequest request = new ExportContentToJSONServiceRequest(exportId, targetObjectId, selectedIds, exportImportOptions, requestor, statusPollingBackgroundTask);

        context.setRequest(request);

        ExportToExcelServiceResponse serviceResp = new ExportToExcelServiceResponse();
        serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(serviceResp);

        return context;
    }
}

