package com.prinova.messagepoint.platform.services.imports.document;

import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.imports.*;
import com.prinova.messagepoint.util.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.jsoup.Jsoup;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/// Abstract class for which anything that contains messagepoint content
/// can derive from.  Provides methods for parsing and handling the content
/// that is shared by all subclasses.
public abstract class ContentImportHandler extends MessagepointImportHandler
{
	private static final Log log = LogUtil.getLog(ContentImportHandler.class);
    private String currentLanguage;
	private String currentLocale;
    private String currentLanguageOfImageLink;
	private String currentLocaleOfImageLink;
    private String currentLanguageOfAltText;
	private String currentLocaleOfAltText;
	private String currentLanguageOfImageExtLink;
	private String currentLocaleOfImageExtLink;
	private String currentLanguageOfImageExtPath;
	private String currentLocaleOfImageExtPath;

    private final Map<String, String> localeContent = new ConcurrentHashMap<>();
    private final Map<String, String> appliedFilenames = new ConcurrentHashMap<>();
    private final Map<String, String> filenames = new ConcurrentHashMap<>();
    private final Map<String, String> newFileLocations = new ConcurrentHashMap<>();
    private final Map<String, Boolean> pathOnlys = new ConcurrentHashMap<>();
    private final Map<String, Long> contentLibraryReference = new ConcurrentHashMap<>();
	private final Map<String, Long> localContentLibraryReference = new ConcurrentHashMap<>();

    private final Map<String, String> guidOfImageLink = new ConcurrentHashMap<>();
    private final Map<String, String> languageContentOfImageLink = new ConcurrentHashMap<>();
    private final Map<String, String> altTexts = new ConcurrentHashMap<>();
	private final Map<String, String> imageExtLinks = new ConcurrentHashMap<>();
	private final Map<String, String> imageExtPaths = new ConcurrentHashMap<>();
    
	public static Pattern newLinePattern = Pattern.compile("\n");
	public static Pattern lineBreakPattern = Pattern.compile("\r");

	private ExportImportHandler eihParent;

    protected boolean createdDummyObject = false;

    protected ContentImportHandler( Class<?extends MessagepointImportHandler> childClazz )
    {
        super(childClazz);
        setAttrMethod("Content", "language", "setCurrentLanguage", String.class);
		setAttrMethod("Content", "locale", "setCurrentLocale", String.class);
        setAttrMethod("Content", "filename", "setFilename", String.class);
        setAttrMethod("Content", "imagename", "setAppliedFilename", String.class);
        setAttrMethod("Content", "sameasdefaultlanguage", "setSameAsEnglish", Boolean.class);
        setAttrMethod("Content", "sameasenglish", "setSameAsEnglish", Boolean.class);
        setAttrMethod("Content", "sameasparent", "setSameAsParent", Boolean.class);
        setAttrMethod("Content", "pathonly", "setPathOnly", Boolean.class);
        setAttrMethod("Content", "contentlibraryrefid", "setContentLibraryReference", Long.class);
		setAttrMethod("Content", "localcontentlibraryrefid", "setLocalContentLibraryReference", Long.class);
        setAttrMethod("Content", "", "setLocaleContent", String.class);
        setAttrMethod("ContentOfImageLink", "language", "setCurrentLanguageOfImageLink", String.class);
		setAttrMethod("ContentOfImageLink", "locale", "setCurrentLocaleOfImageLink", String.class);
        setAttrMethod("ContentOfImageLink", "guid", "setGuidOfImageLink", String.class);
        setAttrMethod("ContentOfImageLink", "", "setLanguageContentOfImageLink", String.class);
        setAttrMethod("AltText", "language", "setLanguageContentOfAltText", String.class);
		setAttrMethod("AltText", "locale", "setLocaleContentOfAltText", String.class);
        setAttrMethod("AltText", "", "setAltText", String.class);
		setAttrMethod("ImageExternalLink", "language", "setLanguageContentOfImageExtLink", String.class);
		setAttrMethod("ImageExternalLink", "locale", "setLocaleContentOfImageExtLink", String.class);
		setAttrMethod("ImageExternalLink", "", "setImageExtLink", String.class);
		setAttrMethod("ImageExternalPath", "language", "setLanguageContentOfImageExtPath", String.class);
		setAttrMethod("ImageExternalPath", "locale", "setLocaleContentOfImageExtPath", String.class);
		setAttrMethod("ImageExternalPath", "", "setImageExtPath", String.class);
    }

	public ExportImportHandler getEihParent()
	{
		return eihParent;
	}
	public void setEihParent( ExportImportHandler p )
	{
		eihParent = p;
	}

    private final String getLocaleContent(String locale, boolean globalContentObject)
    {
        if ( localeContent.containsKey(locale) )
        {
            String content = localeContent.get(locale);

			if ( globalContentObject && content.equalsIgnoreCase("#SameAsEnglish#") )
			{
				if (!eihParent.validateSystemDefaultLocale() && localeContent.containsKey(eihParent.getSystemDefaultLocaleCode()))
				{
					content = localeContent.get(eihParent.getSystemDefaultLocaleCode());
				}
			}

			if ( content.equalsIgnoreCase("#SameAsEnglish#") || content.equalsIgnoreCase("#SameAsParent#") || content.equalsIgnoreCase("#ContentLibraryReference#") )
			{
				content = "";
			}
            return content;
        }
        return "";
    }

	public final List<ContentObjectAssociation> createTextContentsForContentObject(ZonePartImportHandler zpih, boolean resolveContent, boolean globalContentObject )
	{
		List<ContentObjectAssociation> caList = new ArrayList<>();

		for ( String localeCode : localeContent.keySet() )
		{
			String content = localeContent.get(localeCode);
			ContentObjectAssociation ca = new ContentObjectAssociation();
			MessagepointLocale messagepointLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(localeCode);
			ca.setMessagepointLocale(messagepointLocale);
			if ( zpih != null )
				ca.setZonePart( zpih.getZonePart() );

			if ( globalContentObject && content.equalsIgnoreCase("#SameAsEnglish#") )
			{
				if (!eihParent.validateSystemDefaultLocale() && localeContent.containsKey(eihParent.getSystemDefaultLocaleCode()))
				{
					content = localeContent.get(eihParent.getSystemDefaultLocaleCode());
				}
			}

			if ( content.equalsIgnoreCase("#SameAsEnglish#") || content.equalsIgnoreCase("#SameAsParent#") )
			{
				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
			}
			else
			{
				Content c = new Content();
				if (resolveContent)
					content = resolveAndCorrectContent(content, eihParent, c);
				else
					c.setResolveRelations(false);
				c.setEncodedContent(content);
				c.save();
				ca.setContent(c);
				ca.setTypeId(ContentAssociationType.ID_OWNS);
			}
			ca.save();
			caList.add(ca);
		}

		MessagepointLocale defaultSystemLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
		if (globalContentObject && !localeContent.containsKey(defaultSystemLocale.getCode()))
		{
			ContentObjectAssociation ca = new ContentObjectAssociation();
			ca.setMessagepointLocale(defaultSystemLocale);
			if ( zpih != null )
				ca.setZonePart( zpih.getZonePart() );

			Content c = new Content();
			c.save();
			ca.setContent(c);
			ca.setTypeId(ContentAssociationType.ID_OWNS);
			ca.save();
			caList.add(ca);
		}

		return caList;
	}

    private ComplexValue createAndCorrectImportingComplexValue(ExportImportHandler eih, String importingValue) {
        Content contentValue = new Content();
        String langContent = resolveAndCorrectContent( importingValue, eih, contentValue);
        ComplexValue complexValue = new ComplexValue();
        complexValue.setEncodedValue(langContent);
        complexValue.setVariables(contentValue.getVariables());
        complexValue.setGlobalSmartTexts(contentValue.getGlobalSmartTexts());
        complexValue.save();
        return complexValue;
    }

    private void createAltText(String localeCode, Content c, ExportImportHandler eih) {
        if(altTexts.containsKey(localeCode)) {
            String importingValue = altTexts.get(localeCode);
            ComplexValue complexValue = createAndCorrectImportingComplexValue(eih, importingValue);
            c.setImageAltText(complexValue);
        }
    }

    private void createImageLink(String localeCode, Content c, ExportImportHandler eih) {
        if ( languageContentOfImageLink.containsKey(localeCode) )
        {
            String importingValue = languageContentOfImageLink.get(localeCode);
        	ComplexValue complexValue = createAndCorrectImportingComplexValue(eih, importingValue);
        	c.setImageLink(complexValue);
        }
    }

	private void createImageExtLink(String localeCode, Content c, ExportImportHandler eih) {
		if ( imageExtLinks.containsKey(localeCode) )
		{
			String importingValue = imageExtLinks.get(localeCode);
			ComplexValue complexValue = createAndCorrectImportingComplexValue(eih, importingValue);
			c.setImageExtLink(complexValue);
		}
	}

	private void createImageExtPath(String localeCode, Content c, ExportImportHandler eih) {
		if ( imageExtPaths.containsKey(localeCode) )
		{
			String importingValue = imageExtPaths.get(localeCode);
			ComplexValue complexValue = createAndCorrectImportingComplexValue(eih, importingValue);
			c.setImageExtPath(complexValue);
		}
	}

	public final List<ContentObjectAssociation> createGraphicContentsForContentObject(ZonePartImportHandler zpih, boolean globalContentObject)
			throws Exception
	{
		// Copy the temporary image file into the fileroot.
		List<ContentObjectAssociation> caList = new ArrayList<>();
		Date now = new Date(System.currentTimeMillis());

		for( String localeCodeFromSet : localeContent.keySet() )
		{
			ContentObjectAssociation ca = new ContentObjectAssociation();
			MessagepointLocale messagepointLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(localeCodeFromSet);
			ca.setMessagepointLocale(messagepointLocale);
			if ( zpih != null )
				ca.setZonePart( zpih.getZonePart() );

			String content = localeContent.get(localeCodeFromSet);
			String localeCode = localeCodeFromSet;

			if ( globalContentObject && content.equalsIgnoreCase("#SameAsEnglish#") )
			{
				if (!eihParent.validateSystemDefaultLocale() && localeContent.containsKey(eihParent.getSystemDefaultLocaleCode()))
				{
					localeCode = eihParent.getSystemDefaultLocaleCode();
					content = localeContent.get(localeCode);
				}
			}

            if (contentLibraryReference.containsKey(localeCode))
			{
				long refId = contentLibraryReference.get(localeCode);

				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_OWNS);

				Set<ContentLibraryImportHandler> contentLibrary = eihParent.getContentLibrary();
				for (ContentLibraryImportHandler clih : contentLibrary)
				{
					if (clih.getId() == refId)
					{
						ContentObject cli = clih.getContentObject();
						if (cli != null)
						{
							ca.setReferencingImageLibrary(cli);
						}
						break;
					}
				}
			}
			else if (localContentLibraryReference.containsKey(localeCode))
			{
				long refId = localContentLibraryReference.get(localeCode);

				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_OWNS);

				Set<MessageImportHandler> contentLibrary = eihParent.getMessages();
				for (MessageImportHandler clih : contentLibrary)
				{
					if (clih.getId() == refId)
					{
						ContentObject cli = clih.getContentObject();
						if (cli != null)
						{
							ca.setReferencingImageLibrary(cli);
						}
						break;
					}
				}
			}
			else if (newFileLocations.containsKey(localeCode))
			{
				String filename = filenames.get(localeCode);
				String appliedName = appliedFilenames.containsKey(localeCode) ? appliedFilenames.get(localeCode) : "";

				Content c = new Content();

				File newImageLoc = copyImage(localeCode, localeCodeFromSet.substring(0, 2), filename);

				c.setImageLocation( newImageLoc.getAbsolutePath() );
				c.setImageName(filename);
				c.setAppliedImageFilename(appliedName);
				c.setImageUploadedDate( now );

				createImageLink(localeCode, c, eihParent);
				createAltText(localeCode, c, eihParent);
				createImageExtLink(localeCode, c, eihParent);
				createImageExtPath(localeCode, c, eihParent);

				c.save();
				ca.setContent(c);
				ca.setTypeId(ContentAssociationType.ID_OWNS);
			}
			else
			{
				if ( content.equalsIgnoreCase("#SameAsEnglish#") || content.equalsIgnoreCase("#SameAsParent#") )
				{
					ca.setContent(null);
					ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
				}
				else
				{
					Content c = new Content();

					createImageLink(localeCode, c, eihParent);
					createAltText(localeCode, c, eihParent);
					createImageExtLink(localeCode, c, eihParent);
					createImageExtPath(localeCode, c, eihParent);

					c.save();
					ca.setContent(c);
					ca.setTypeId(ContentAssociationType.ID_OWNS);
				}
			}
			ca.save();
			caList.add(ca);
		}

		MessagepointLocale defaultSystemLocale = MessagepointLocale.getDefaultSystemLanguageLocale();
		if (globalContentObject && !localeContent.containsKey(defaultSystemLocale.getCode()))
		{
			ContentObjectAssociation ca = new ContentObjectAssociation();
			ca.setMessagepointLocale(defaultSystemLocale);
			if ( zpih != null )
				ca.setZonePart( zpih.getZonePart() );

			Content c = new Content();
			c.save();
			ca.setContent(c);
			ca.setTypeId(ContentAssociationType.ID_OWNS);
			ca.save();
			caList.add(ca);
		}

		return caList;
	}

    public final void createSelectableMessageContents(ZonePartImportHandler zpih, ExportImportHandler eih, ContentObject contentObject, ParameterGroupTreeNode PGTreeNode, boolean isLocal, boolean globalContentObject)
    throws Exception
    {
        Date now = new Date(System.currentTimeMillis());

		long contentTypeId = contentObject.getContentType().getId();
		ZonePart zonePart = null;
		if (zpih != null)
		{
			contentTypeId = zpih.getContentTypeId();
			zonePart = zpih.getZonePart();
		}

		for( String localeCodeFromSet : localeContent.keySet() )
        {
			MessagepointLocale locale = MessagepointLocale.getLanguageLocaleByLocaleCode(localeCodeFromSet);
			String contentString = localeContent.get(localeCodeFromSet);
			String localeCode = localeCodeFromSet;

			if ( globalContentObject && contentString.equalsIgnoreCase("#SameAsEnglish#") )
			{
				if (!eihParent.validateSystemDefaultLocale() && localeContent.containsKey(eihParent.getSystemDefaultLocaleCode()))
				{
					localeCode = eihParent.getSystemDefaultLocaleCode();
					contentString = localeContent.get(localeCode);
				}
			}

			ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, PGTreeNode, locale, zonePart, false, true);
			if (ca == null)
			{
				ca = new ContentObjectAssociation();
				ca.setContentObjectPGTreeNode(PGTreeNode);
				ca.setContentObject(contentObject);
				ca.setDataType(contentObject.getFocusOnDataType());
				ca.setTypeId(ContentAssociationType.ID_EMPTY);
				ca.setMessagepointLocale(locale);
				ca.setReferencingContentObjectPGTreeNode(PGTreeNode.getParentNode());
				if (zpih != null)
					ca.setZonePart(zpih.getZonePart());
				ca.save();
			}

			if ( contentString.equalsIgnoreCase("#SameAsEnglish#") || contentString.equalsIgnoreCase("#SameAsParent#") )
			{
				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_REFERENCES);
				ca.save();
			}
			else if (contentLibraryReference.containsKey(localeCode))
			{
				long refId = contentLibraryReference.get(localeCode);

				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_OWNS);

				Set<ContentLibraryImportHandler> contentLibrary = eih.getContentLibrary();
				for (ContentLibraryImportHandler clih : contentLibrary)
				{
					if (clih.getId() == refId)
					{
						ContentObject cli = clih.getContentObject();
						if (cli != null)
						{
							ca.setReferencingImageLibrary(cli);
						}
						break;
					}
				}

				ca.save();
			}
			else if (localContentLibraryReference.containsKey(localeCode))
			{
				long refId = localContentLibraryReference.get(localeCode);

				ca.setContent(null);
				ca.setTypeId(ContentAssociationType.ID_OWNS);

				Set<MessageImportHandler> contentLibrary = eih.getMessages();
				for (MessageImportHandler clih : contentLibrary)
				{
					if (clih.getId() == refId)
					{
						ContentObject cli = clih.getContentObject();
						if (cli != null)
						{
							ca.setReferencingImageLibrary(cli);
						}
						break;
					}
				}

				ca.save();
			}
			else
			{
				Content content = ca.getContent();
				if (ca.getTypeId() != ContentAssociationType.ID_OWNS)
				{
					content = new Content();
					ca.setContent(content);
				}

				content.clearTextAndGraphicContent();

				if (ContentType.GRAPHIC == contentTypeId)
				{
					if ( newFileLocations.containsKey(localeCode) )
					{
						String filename = filenames.get(localeCode);
						String appliedName = appliedFilenames.containsKey(localeCode) ? appliedFilenames.get(localeCode) : "";

						File newImageLoc = copyImage(localeCode, localeCodeFromSet.substring(0, 2), filename);

						content.setImageLocation( newImageLoc.getAbsolutePath() );
						content.setImageName(filename);
						content.setAppliedImageFilename(appliedName);
						content.setImageUploadedDate( now );
					}

					createImageLink(localeCode, content, eih);
					createAltText(localeCode, content, eih);
					createImageExtLink(localeCode, content, eih);
				}
				else
				{
					if (isLocal)
					{
						content.setEncodedContent(contentString);
						content.setResolveRelations(false);
					}
					else
					{
						content.setEncodedContent(resolveAndCorrectContent(contentString, eih, content));
					}
				}

				content.save();
				ca.setTypeId(ContentAssociationType.ID_OWNS);
				ca.save();
			}
        }
    }

    public final void setCurrentLanguage( String val )
    {
        currentLanguage = val;
    	if (currentLanguage.equalsIgnoreCase("al"))
    	{
    		currentLanguage = "sq";
    	}

		currentLocale = null;
    }

	public final void setCurrentLocale( String val )
	{
		currentLocale = val;
		if (!localeContent.containsKey(currentLocale))
		{
			localeContent.put(currentLocale, "");
		}
	}

	public final void resolvedCurrentLocale()
	{
		if (currentLocale == null)
		{
			currentLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(eihParent.getTouchpoint().getLanguageLocaleCode(currentLanguage)).getCode();
		}
	}

	public final void setLocaleContent( String val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

        localeContent.remove(currentLocale);

    	if (val != null)
			localeContent.put(currentLocale, val);
    	else
			localeContent.put(currentLocale, "");
    }

    public final void setCurrentLanguageOfImageLink( String val )
    {
        currentLanguageOfImageLink = val;
    	if (currentLanguageOfImageLink.equalsIgnoreCase("al"))
    	{
    		currentLanguageOfImageLink = "sq";
    	}
		currentLocaleOfImageLink = null;
    }

	public final void resolvedCurrentLocaleOfImageLink()
	{
		if (currentLocaleOfImageLink == null)
		{
			currentLocaleOfImageLink = MessagepointLocale.getLanguageLocaleByLocaleCode(eihParent.getTouchpoint().getLanguageLocaleCode(currentLanguageOfImageLink)).getCode();
		}
	}

	public final void setCurrentLocaleOfImageLink( String val )
	{
		currentLocaleOfImageLink = val;
		if (!guidOfImageLink.containsKey(currentLocaleOfImageLink))
		{
			guidOfImageLink.put(currentLocaleOfImageLink, "");
		}
		if (!languageContentOfImageLink.containsKey(currentLocaleOfImageLink))
		{
			languageContentOfImageLink.put(currentLocaleOfImageLink, "");
		}
	}

	public final void setGuidOfImageLink( String val )
    {
		if (currentLocaleOfImageLink == null)
			resolvedCurrentLocaleOfImageLink();

        guidOfImageLink.remove(currentLocaleOfImageLink);

    	if (val != null)
    		guidOfImageLink.put(currentLocaleOfImageLink, val);
    	else
    		guidOfImageLink.put(currentLocaleOfImageLink, "");
    }
    
    public final void setLanguageContentOfImageLink( String val )
    {
		if (currentLocaleOfImageLink == null)
			resolvedCurrentLocaleOfImageLink();

        languageContentOfImageLink.remove(currentLocaleOfImageLink);

    	if (val != null)
    		languageContentOfImageLink.put(currentLocaleOfImageLink, val);
    	else
    		languageContentOfImageLink.put(currentLocaleOfImageLink, "");
    }

    public final void setLanguageContentOfAltText( String val ) 
    {
        currentLanguageOfAltText = val;
        if (currentLanguageOfAltText.equalsIgnoreCase("al"))
        {
            currentLanguageOfAltText = "sq";
        }
		currentLocaleOfAltText = null;
    }

	public final void resolvedCurrentLocaleContentOfAltText()
	{
		if (currentLocaleOfAltText == null)
		{
			currentLocaleOfAltText = MessagepointLocale.getLanguageLocaleByLocaleCode(eihParent.getTouchpoint().getLanguageLocaleCode(currentLanguageOfAltText)).getCode();
		}
	}

	public final void setLocaleContentOfAltText( String val )
	{
		currentLocaleOfAltText = val;
	}

	public final void setAltText( String val )
    {
		if (currentLocaleOfAltText == null) resolvedCurrentLocaleContentOfAltText();
        if(val != null) {
            altTexts.put(currentLocaleOfAltText, val);
        } else altTexts.remove(currentLocaleOfAltText);
    }

	public final void setLanguageContentOfImageExtLink( String val )
	{
		currentLanguageOfImageExtLink = val;
		if (currentLanguageOfImageExtLink.equalsIgnoreCase("al"))
		{
			currentLanguageOfImageExtLink = "sq";
		}
		currentLocaleOfImageExtLink = null;
	}

	public final void resolveLocaleContentOfImageExtLink()
	{
		if (currentLocaleOfImageExtLink == null)
		{
			currentLocaleOfImageExtLink = MessagepointLocale.getLanguageLocaleByLocaleCode(eihParent.getTouchpoint().getLanguageLocaleCode(currentLanguageOfImageExtLink)).getCode();
		}
	}

	public final void setLocaleContentOfImageExtLink( String val )
	{
		currentLocaleOfImageExtLink = val;
	}

	public final void setImageExtLink( String val )
	{
		if (currentLocaleOfImageExtLink == null) resolveLocaleContentOfImageExtLink();
		if(val != null) {
			imageExtLinks.put(currentLocaleOfImageExtLink, val);
		} else imageExtLinks.remove(currentLocaleOfImageExtLink);
	}

	public final void setLanguageContentOfImageExtPath( String val )
	{
		currentLanguageOfImageExtPath = val;
		if (currentLanguageOfImageExtPath.equalsIgnoreCase("al"))
		{
			currentLanguageOfImageExtPath = "sq";
		}
		currentLocaleOfImageExtPath = null;
	}

	public final void resolveLocaleContentOfImageExtPath()
	{
		if (currentLocaleOfImageExtPath == null)
		{
			currentLocaleOfImageExtPath = MessagepointLocale.getLanguageLocaleByLocaleCode(eihParent.getTouchpoint().getLanguageLocaleCode(currentLanguageOfImageExtPath)).getCode();
		}
	}

	public final void setLocaleContentOfImageExtPath( String val )
	{
		currentLocaleOfImageExtPath = val;
	}

	public final void setImageExtPath( String val )
	{
		if (currentLocaleOfImageExtPath == null) resolveLocaleContentOfImageExtPath();
		if(val != null) {
			imageExtPaths.put(currentLocaleOfImageExtPath, val);
		} else imageExtPaths.remove(currentLocaleOfImageExtPath);
	}
    
    public final void setContentLibraryReference( Long val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

   		contentLibraryReference.put(currentLocale, val);
        localeContent.put(currentLocale, "#ContentLibraryReference#");
    }

	public final void setLocalContentLibraryReference( Long val )
	{
		if (currentLocale == null)
			resolvedCurrentLocale();

		localContentLibraryReference.put(currentLocale, val);
		localeContent.put(currentLocale, "#ContentLibraryReference#");
	}

    public final void setSameAsEnglish( Boolean val )
	{
		if (currentLocale == null)
			resolvedCurrentLocale();

		localeContent.put(currentLocale, "#SameAsEnglish#");
    }
    public final void setSameAsParent( Boolean val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

		localeContent.put(currentLocale, "#SameAsParent#");
    }
    public final void setFilename( String val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

    	if (val != null)
    		filenames.put( currentLocale, val );
    }
    public final void setAppliedFilename( String val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

    	if (val != null)
    		appliedFilenames.put( currentLocale, val );
    }
    public final void setPathOnly( Boolean val )
    {
		if (currentLocale == null)
			resolvedCurrentLocale();

    	if (val != null)
    		pathOnlys.put( currentLocale, val );
    }

    protected final void createGraphicFiles(boolean globalContentObject)
    {
        for( String localeCode : filenames.keySet() )
        {
            try
            {
            	Boolean pathOnly = pathOnlys.get(localeCode);
            	if (pathOnly != null)
            	{
            		if (pathOnly)
            		{
                        newFileLocations.put(localeCode, "!" + getLocaleContent(localeCode, globalContentObject));
                        continue;
            		}
            	}
            	
    			String tmpFilerootPath = ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir );
    			FileUtil.createDirectoryIfNeeded(tmpFilerootPath);
    			if(getTpImportTask() != null) {
    				tmpFilerootPath = getTpImportTask().getTempFolder();
    				if(! tmpFilerootPath.endsWith("/")) {
    					tmpFilerootPath += "/";
    				}
    			}
    			
                String filename = tmpFilerootPath + RandomGUID.getGUID();
                newFileLocations.put(localeCode, "@" + filename);
                File newFile = new File(filename);

                // perform base 64 conversion
                FileOutputStream fos = new FileOutputStream(newFile);
                byte[] filecontent = Base64.decodeBase64(getLocaleContent(localeCode, globalContentObject));

                fos.write(filecontent);
                fos.close();

                // languageContent.remove(lang);  // no need to hold this anymore  // Actually is used by TP image import
            }
            catch( Exception ex )
            {
                log.error("Caught exception: ", ex);
            }
        }   
    }
    
    // Should be able to take any variable, constant, or style from the text and
    // be able to convert it to the current object in the system that represents
    // the same thing.  Note that the text content is not set, but the 
    // "this references" data will be.
    public static String resolveAndCorrectContent( 
            String content,
            ExportImportHandler eih,
            Content contentObj )
    {
    	if (contentObj == null)
    		return content;
    	
    	contentObj.setResolveRelations(false);
    	
    	if (content == null || content.isEmpty())
    	{
    		contentObj.setUnformattedText(content);
    		return content;
    	}
    	
        org.jsoup.nodes.Document docJsoup = null;
        
        try
        {
            docJsoup = org.jsoup.Jsoup.parse(content);
        }
        catch( Exception ex ) 
        {
            log.error("Cannot parse text content of message: " + content);
            return content;
        }        

        if(docJsoup == null) {
        	return content;
        }

        for( org.jsoup.nodes.Element childNode : docJsoup.children() )
        {
        	resolveAndCorrectContent(childNode, eih, contentObj );
        }

        if (contentObj.getEncodedContent() != null)
        	contentObj.setUnformattedText(ContentObjectContentUtil.getUnformattedTextContent(Jsoup.parse(contentObj.getEncodedContent())));
        else
			contentObj.setUnformattedText(null);
        
        // turn the DOM back into HTML, without the <TEXT> tag
        try
        {
        	docJsoup.outputSettings().syntax( org.jsoup.nodes.Document.OutputSettings.Syntax.xml );
        	docJsoup.outputSettings().prettyPrint(false);
            org.jsoup.select.Elements bodyElement = docJsoup.getElementsByTag("body");
            String result = ContentObjectContentUtil.processForSpecialChars(bodyElement.first().children().toString());
            result = newLinePattern.matcher(result).replaceAll("");
            result = lineBreakPattern.matcher(result).replaceAll("");
            return result;
        }
        catch( Exception ex )
        {
            log.error("Caught exception: ", ex);
            return content;
        }
    }
    
    private File copyImage(
            String localeCode,
			String languageCode,
            String fileName )
    throws Exception
    {
        String imageDirBase = ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_ImagesDir );
        FileUtil.createDirectoryIfNeeded(imageDirBase);
        
        File imageFile = null;
        String tempFileLoc = newFileLocations.get(localeCode);
        File tempLocation = new File(tempFileLoc.substring(1));
        if (tempFileLoc.charAt(0) == '!')
        {
        	// Path Only option. The file has to be copied manually from its original location to imageDirBase folder
            imageFile = new File( imageDirBase + File.separator + tempLocation );        	
        }
        else
        {
    		String guid = RandomGUID.getGUID();
            File imageDir = new File( imageDirBase + File.separator + guid + File.separator + languageCode);
            imageFile = new File(imageDir + File.separator + fileName );
            
            FileUtils.forceMkdir(imageDir);
            FileUtils.copyFile(tempLocation, imageFile);        	
        }

        newFileLocations.remove(localeCode);
        newFileLocations.put(localeCode, " " + imageFile.getPath());
        if (tempFileLoc.charAt(0) == '@')
        {
        	// delete original temporary image file
        	tempLocation.delete();
        }
        
        return imageFile;
    }
    
    private static void correctImageReference(org.jsoup.nodes.Element  e, String idAttrName, ExportImportHandler eih, Content content)
    {
    	String idAttr = e.attr(idAttrName);
    	if (idAttr != null && (! idAttr.isEmpty()))
    	{
    		long id = Long.parseLong( idAttr );

    		DatabaseFileImportHandler baseHandler = null;
    		Set<DatabaseFileImportHandler> databaseFiles = eih.getDatabaseFiles();
    		for (DatabaseFileImportHandler dfih : databaseFiles)
    		{
    			if ( dfih.getId() == id )
    			{
    				baseHandler = dfih;
    				break;
    			}        		
    		}

    		if (baseHandler == null)
    		{
    			log.error("Image File NOT resolved: ID = " + id + " cannot find in import file");
    			e.attr(idAttrName, "0");
    		}
    		else
    		{
    			DatabaseFile databaseFile = baseHandler.getDatabaseFile();
    			if ( databaseFile != null )
    			{
    				e.attr(idAttrName, "" + databaseFile.getId());

    				String srcAttr = e.attr("src");
    				if (srcAttr != null && ! srcAttr.isEmpty()) {
                        if (srcAttr.contains("file=")) {
                            e.attr("src", srcAttr.replace("file=" + id, "file=" + databaseFile.getId()));
                        } else if (srcAttr.contains("resource=")) {
                            e.attr("src", getNewSrcResourceAttr(srcAttr, databaseFile.getId()));
                        }
                    }

    				if (content != null)
    					content.getImages().add(databaseFile); 
    			}
    			else
    			{
    				log.error("Image File NOT resolved: ID = " + id + " wasn't imported.");
    				e.attr(idAttrName, "0");
    			}
    		}
    	}
    }

    private static void correctSandboxImageReference(org.jsoup.nodes.Element  e, String idAttrName, ExportImportHandler eih, Content content)
    {
    	String idAttr = e.attr(idAttrName);
    	if (idAttr != null && (! idAttr.isEmpty()))
    	{
    		long id = Long.parseLong( idAttr );

    		DatabaseFileImportHandler baseHandler = null;
    		Set<DatabaseFileImportHandler> databaseFiles = eih.getDatabaseFiles();
    		for (DatabaseFileImportHandler dfih : databaseFiles)
    		{
    			if ( dfih.getId() == id )
    			{
    				baseHandler = dfih;
    				break;
    			}        		
    		}

    		if (baseHandler == null)
    		{
    			log.error("Image File NOT resolved: ID = " + id + " cannot find in import file");
    			e.attr(idAttrName, "0");
    		}
    		else
    		{
    			DatabaseFile databaseFile = baseHandler.getDatabaseFile();
    			if ( databaseFile != null )
    			{
    				e.attr(idAttrName, "" + databaseFile.getId());

    				String srcAttr = e.attr("src");
    				if (srcAttr != null && ! srcAttr.isEmpty()) {
                        if (srcAttr.contains("file=")) {
                            e.attr("src", srcAttr.replace("file=" + id, "file=" + databaseFile.getId()));
                        } else if (srcAttr.contains("resource=")) {
                            e.attr("src", getNewSrcResourceAttr(srcAttr, databaseFile.getId()));
                        }
                    }
    				if (content != null)
    					content.getImages().add(databaseFile); 
    			}
    			else
    			{
    				log.error("Image File NOT resolved: ID = " + id + " wasn't imported.");
    				e.attr(idAttrName, "0");
    			}
    		}
    	}
    }

    private static String getNewSrcResourceAttr(String srcAttr, Long id) {
        String resourcePrefix = "resource=";
        String endOfResourcePrefix = "&";
        int startIndex = srcAttr.indexOf(resourcePrefix) + resourcePrefix.length();
        int endIndex = srcAttr.indexOf(endOfResourcePrefix, startIndex);
        String oldId = srcAttr.substring(startIndex, endIndex);
        String newId = HttpRequestUtil.getFileResourceToken(id);
        return srcAttr.replace(oldId, newId);
    }

    private static void correctImageLibraryReference(org.jsoup.nodes.Element  e, String idAttrName, ExportImportHandler eih, Content content)
    {
    	String idAttr = e.attr(idAttrName);
    	if (idAttr != null && (! idAttr.isEmpty()))
    	{
    		long id = Long.parseLong( idAttr );
    		
			ContentLibraryImportHandler contentLibraryImportHandler = null;
    		Set<ContentLibraryImportHandler> contentLibraries = eih.getContentLibrary();
    		for (ContentLibraryImportHandler clih : contentLibraries)
    		{
    			if ( clih.getId() == id )
    			{
    				contentLibraryImportHandler = clih;
    				break;
    			}        		
    		}

    		if (contentLibraryImportHandler == null || contentLibraryImportHandler.getContentObject() == null)
    		{
    			log.error("Image File NOT resolved: ID = " + id + " cannot find in import file");
    		}
    		else
    		{
				String contentLibraryItemDna = contentLibraryImportHandler.getContentObject().getDna();
  				e.attr("dna", contentLibraryItemDna);
				e.removeAttr("id");
  				if(content != null) {
                    content.getContentObjectsTypeMap().put(contentLibraryImportHandler.getContentObject(), contentLibraryImportHandler.getContentObject().getObjectType());
  				}
    		}
    	}
    }

    private static void correctLocalImageLibraryReference(org.jsoup.nodes.Element  e, String idAttrName, ExportImportHandler eih, Content content)
    {
    	String idAttr = e.attr(idAttrName);
    	if (idAttr != null && (! idAttr.isEmpty()))
    	{
    		long id = Long.parseLong( idAttr );
    		
    		MessageImportHandler messageHandler = null;
    		Set<MessageImportHandler> messages = eih.getMessages();
    		for (MessageImportHandler mih : messages)
    		{
    			if ( mih.getId() == id )
    			{
    				messageHandler = mih;
    				break;
    			}        		
    		}

    		if (messageHandler == null || messageHandler.getContentObject() == null)
    		{
    			log.error("Image File NOT resolved: ID = " + id + " cannot find in import file");
    		}
    		else
    		{
				String contentLibraryItemDna = messageHandler.getContentObject().getDna();
				e.attr("dna", contentLibraryItemDna);
				e.removeAttr("id");
  				if(content != null) {
  				    // TODO !! content.getLocalContentLibrary().add(messageHandler.getContentObject());
  				}
    		}
    	}
    }

    private static void resolveAndCorrectContent( 
    		org.jsoup.nodes.Element node,
            ExportImportHandler eih,
            Content content )
    {
        if (node.hasAttr("data_group_id")) {
            org.jsoup.nodes.Element e = (org.jsoup.nodes.Element) node;
            String idAttr = e.attr("data_group_id");
            if (idAttr != null && (! idAttr.isEmpty())) {
                Long id = Long.parseLong(idAttr);
                DataGroupListImportHandler dataGroupListImportHandler = eih.getDataGroups();
                DataGroup dataGroup = dataGroupListImportHandler == null ? null : dataGroupListImportHandler.getDataGroupById(id);
                if (dataGroup == null) {
                    log.error("Data Group NOT resolved: ID = " + id + " cannot find in import file");
                    e.removeAttr("data_group_id");
                }
                else {
                    e.attr("data_group_id", "" + dataGroup.getId());
                }
            }

        }

        if ( "tr".equalsIgnoreCase(node.tagName()) || node.hasAttr("content_targeting_id"))
        {
        	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element) node;
            String idAttr = e.attr("content_targeting_id");
            if (idAttr != null && (!idAttr.isEmpty()))
            {
            	long id = 0L;
            	if(!idAttr.isEmpty()) {
            		id = Long.parseLong( idAttr );
            	} else {
            		id = 0L;
            	}
                
            	ContentTargetingImportHandler baseHandler = eih.getTargetData().findContentTargetingByRefId(id);
            	
                if (baseHandler == null)
                {
                	log.error("Content Targeting NOT resolved: ID = " + id + " cannot find in import file");
               		e.attr("content_targeting_id", "0");
                }
                else
                {
	                ContentTargeting ct = baseHandler.getContentTargeting();
	                if ( ct != null )
	                {
	                	e.attr("content_targeting_id", "" + ct.getId());
	                	if (content != null)
	                		content.getContentTargeting().add(ct);
	                }
	                else
	                {
	                	log.error("Content Targeting NOT resolved: ID = " + id + " wasn't imported.");
                		e.attr("content_targeting_id", "0");
	                }
                }
            }
        }

        // Handle column_targeting_ids attribute (table column targeting)
        if ( "table".equalsIgnoreCase(node.tagName()) || node.hasAttr("column_targeting_ids"))
        {
        	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element) node;
            String columnTargetingIdsAttr = e.attr("column_targeting_ids");
            if (columnTargetingIdsAttr != null && (!columnTargetingIdsAttr.isEmpty()))
            {
                // Parse colon-separated targeting IDs (e.g., "12159:0:12160")
                String[] targetingIds = columnTargetingIdsAttr.split(":");
                StringBuilder resolvedIds = new StringBuilder();

                for (int i = 0; i < targetingIds.length; i++)
                {
                    String targetingIdStr = targetingIds[i].trim();
                    if (i > 0) {
                        resolvedIds.append(":");
                    }

                    if (targetingIdStr.equals("0") || targetingIdStr.isEmpty())
                    {
                        // Preserve 0 values (no targeting for this column)
                        resolvedIds.append("0");
                    }
                    else
                    {
                        try
                        {
                            long targetingId = Long.parseLong(targetingIdStr);
                            ContentTargetingImportHandler baseHandler = eih.getTargetData().findContentTargetingByRefId(targetingId);

                            if (baseHandler == null)
                            {
                                log.error("Column Content Targeting NOT resolved: ID = " + targetingId + " cannot find in import file");
                                resolvedIds.append("0");
                            }
                            else
                            {
                                ContentTargeting ct = baseHandler.getContentTargeting();
                                if (ct != null)
                                {
                                    resolvedIds.append(ct.getId());
                                    if (content != null)
                                        content.getContentTargeting().add(ct);
                                }
                                else
                                {
                                    log.error("Column Content Targeting NOT resolved: ID = " + targetingId + " wasn't imported.");
                                    resolvedIds.append("0");
                                }
                            }
                        }
                        catch (NumberFormatException ex)
                        {
                            log.error("Invalid column targeting ID format: " + targetingIdStr);
                            resolvedIds.append("0");
                        }
                    }
                }

                // Update the attribute with resolved IDs
                e.attr("column_targeting_ids", resolvedIds.toString());
            }
        }
        else if ( "img".equalsIgnoreCase(node.tagName()) )
        {
        	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element) node;
        	correctImageReference(e, "image_file_id", eih, content);
        	correctSandboxImageReference(e, "sandbox_file_id", eih, content);
        	correctImageLibraryReference(e, "image_library_id", eih, content);
        	correctLocalImageLibraryReference(e, "local_image_library_id", eih, content);
        }
        else
        {
	    	long type = 0L;
	        if ( "span".equalsIgnoreCase(node.tagName()) )
	        {
	        	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element)node;
	            String idAttr = e.attr("id");
	            String typeAttr = e.attr("type");
	            
	            if (idAttr != null && (!idAttr.isEmpty()) && typeAttr != null && (!typeAttr.isEmpty()))
	            {
	                type = Long.parseLong( typeAttr );
	            }
	        }
	
	        if (type == 4 || type == 5 || (type >= 8 && type <= 10) || type == 17)
	        {
	        	updateVarNode(node, eih, content);
	        }
	        else 
	        {
		        if ( "var".equalsIgnoreCase(node.tagName()) )
		        {
		            updateVarNode(node, eih, content);
		        }
		        else if (!node.tagName().isEmpty())
		        {
		            resolveStyles(node, eih, content);
		        }
	        }
        }

        if (node.children() != null && !node.children().isEmpty())
        {        
	        // recurse through the children
	        for( org.jsoup.nodes.Element n : node.children() )
	        {
	            resolveAndCorrectContent(n, eih, content);
	        }
        }
    }
    
    private static void resolveStyles( org.jsoup.nodes.Element node, ExportImportHandler eih, Content content )
    {
    	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element) node;
        String textClassAttr = e.attr("class");
        String psClassAttr = e.attr("paragraphclass");
        TextStyleListImportHandler textStyleList = eih.getTextStyles(); 
        if ( textClassAttr != null && textClassAttr.length() >= 33 && textClassAttr.startsWith("S"))
        {
            String identifier = textClassAttr.substring(0,  33);
            String remains = textClassAttr.substring(33);
            
            TextStyleImportHandler sih = textStyleList.findByIdentifier(identifier);
            if ( sih != null )
            {
                TextStyle style = sih.getTextStyle();
                e.attr("class", style.getIdentifier() + remains);
                if(remains.isEmpty()) {
                    if (content != null) {
                        content.getTextStyles().add(style);
                    } else {
                        e.removeAttr("class");
                    }
                }
            }
            else {
                e.removeAttr("class");
            }
        }
        if ( psClassAttr != null )
        {
            String identifier = psClassAttr;
            if ( identifier.startsWith(ParagraphStyle.IDENTIFIER_PREFIX) ) {
            	
	            String compoundAttrs = null;
	            String baseIdentifier = identifier;
	            if ( identifier.indexOf(ContentObjectContentUtil.STYLE_INDICATOR_PRIMARY_JOINER) != -1 ) {
	            	baseIdentifier = identifier.split(ContentObjectContentUtil.STYLE_INDICATOR_PRIMARY_JOINER)[0];
	            	compoundAttrs = identifier.split(ContentObjectContentUtil.STYLE_INDICATOR_PRIMARY_JOINER)[1];
	            }
            	
	            ParagraphStyle s = ParagraphStyle.findByIdentifier(baseIdentifier);
	            if ( s != null )
	            {
	            	if (content != null)
	            		content.getParagraphStyles().add(s);
	            	else
	                    e.removeAttr("paragraphclass");
	            }
	            else
	                e.removeAttr("paragraphclass");
            }
        }
    }
    
    private static void updateVarNode( 
    		org.jsoup.nodes.Element node,
            ExportImportHandler eih,
            Content content )
    {
        // Var nodes are always of the form <var id="x" type="1|2|3|4">VAR NAME</var>
    	org.jsoup.nodes.Element e = (org.jsoup.nodes.Element)node;
        String idAttr = e.attr("id");
        String dna = e.attr("dna");
        long id = 0L;
        if (idAttr != null && !idAttr.trim().equals("null") && (! idAttr.isEmpty()))
        	id = Long.parseLong( idAttr );
        long type = Long.parseLong( e.attr("type") );
        Integer sysTypeId = e.hasAttr("sys_type_id")?Integer.parseInt(e.attr("sys_type_id")):null;
        MessagepointImportHandler baseHandler = null;
        if (type == 4 || type == 5)
        {
        	Set<EmbeddedContentImportHandler> embeddedContents = eih.getEmbeddedContents();
        	for (EmbeddedContentImportHandler ecih : embeddedContents)
        	{
                if ( id != 0 && ecih.getId() == id )
                {
                	baseHandler = ecih;
                	break;
                }
                if ( id == 0 && dna != null && ecih.getDna() != null && ecih.getDna().equals(dna) ) {
                	baseHandler = ecih;
                	break;
                }
        	}
        }
        else if (type == 8)
        {
        	Set<LookupTableImportHandler> lookupTables = eih.getLookupTables();
        	for (LookupTableImportHandler ltih : lookupTables)
        	{
                if ( id != 0 && ltih.getId() == id )
                {
                	baseHandler = ltih;
                	break;
                }
                
                if ( id == 0 && dna != null && ltih.getDna() != null && ltih.getDna().equals(dna) ) {
                	baseHandler = ltih;
                	break;
                }
        	}
        }
        else if (type == 9)
        {
        	Set<LookupTableImportHandler> lookupTables = eih.getLookupTables();
        	for (LookupTableImportHandler ltih : lookupTables)
        	{
                if ( id != 0 && ltih.findDataElementByRefId(id) != null )
                {
                	baseHandler = ltih;
                	break;
                }
                
                if ( id == 0 && dna != null && ltih.findDataElementByRefDna(dna) != null ) {
                	baseHandler = ltih;
                	break;
                }
        	}
        }
        else if (type == 10)
        {
        	Set<MessageImportHandler> localContents = eih.getMessages();
        	for (MessageImportHandler mih : localContents)
        	{
                if ( id != 0 && mih.getId() == id )
                {
                	baseHandler = mih;
                	break;
                }
                
                if ( id == 0 && dna != null && mih.getDna() != null && mih.getDna().equals(dna) ) {
                	baseHandler = mih;
                	break;
                }
        	}
        }
        else if(type == 15) 
        {
        	return;
        }
		else if(type == 19)
		{
			return;
		}
        else if(type == 17) 
        {
            Set<ZoneImportHandler> zones = eih.getTouchpoint().getZones();
            for(ZoneImportHandler zih : zones) {
                if( id != 0 && zih.getId() == id ) {
                    baseHandler = zih;
                    break;
                }
                
                if ( id == 0 && dna != null && zih.getDna() != null && zih.getDna().equals(dna) ) {
                	baseHandler = zih;
                	break;
                }
            }
        }
        else
        {
        	if ( id != 0 ) {
        	    baseHandler = eih.getDataVariables().FindByRefIdAndType( id, type );
        	} else if ( id == 0 && type == 1 && sysTypeId != null ) {
                baseHandler = eih.getDataVariables().findVariableBySysTypeId( sysTypeId);
            } else if ( id == 0 && dna != null ) {
        	    baseHandler = eih.getDataVariables().FindByRefDnaAndType( dna, type );
        	}
        }
        
        if (baseHandler == null)
        {
        	log.error("Var node NOT resolved: Name = " + e.text() + " ID = " + id + " DNA = " + dna + " type = " + type);
        	if (idAttr != null && (! idAttr.isEmpty()))
        		e.attr("id", "0");
        	e.text(e.text() + " - not imported!");
        	return;
        }
        
        if( baseHandler instanceof LookupTableImportHandler )
        {
        	LookupTableImportHandler ltih = (LookupTableImportHandler) baseHandler;
        	if (type == 8)
        	{
	            LookupTable lt = ltih.getLookupTable();
	            if ( lt != null )
	            {
	            	e.attr("id", "" + lt.getId());
	            	if (content != null && content.getImportTempString() != null)
	            		content.setImportTempString(content.getImportTempString().replace("T_" + id, "T_" + lt.getId()));
	            }
        	}
        	else
        	{
        		DataElement de = id != 0 ? ltih.findDataElementByRefId(id) : dna != null ? ltih.findDataElementByRefDna(dna) : null;
	            if ( de != null )
	            {
	            	e.attr("id", "" + de.getId());
	            	if (content != null && content.getImportTempString() != null)
	            		content.setImportTempString(content.getImportTempString().replace("E_" + id, "E_" + de.getId()));
	            }
        	}
        }
        else if( baseHandler instanceof MessageImportHandler )
        {
        	MessageImportHandler mih = (MessageImportHandler) baseHandler;
            ContentObject mi = mih.getContentObject();
            if ( mi != null )
            {
				ContentObject m2 = ContentObject.findById(mi.getId());
				e.removeAttr("id");
				e.attr("dna", m2.getDna());
            	if (content != null) {
            		// TODO
					// content.getLocalContent().add(m2);
				}
            }
        }
        else if( baseHandler instanceof EmbeddedContentImportHandler )
        {
        	EmbeddedContentImportHandler ecih = (EmbeddedContentImportHandler) baseHandler;
            ContentObject eci = ecih.getContentObject();
            if ( eci != null )
            {
				ContentObject eci2 = ContentObject.findById(eci.getId());
				ContentObject ec2 = eci2;
				e.removeAttr("id");
				e.attr("dna", ec2.getDna());
            	if (content != null)
            		content.getContentObjectsTypeMap().put(ec2, ec2.getObjectType());
            }
        }
        else if( baseHandler instanceof UserVariableImportHandler )
        {
            UserVariableImportHandler uvih = (UserVariableImportHandler) baseHandler;
            DataElementVariable dev = uvih.getDataElementVariable();
            if ( dev != null )
            {
				e.removeAttr("id");
                e.attr("dna", dev.getDna());
            	if (content != null)
            	{
            		content.getVariables().add(dev);
            		if (content.getImportTempString() != null)
            			content.setImportTempString(content.getImportTempString().replace("@" + id, "@" + dev.getId()));     
            	}
            }
            else
            {
            	log.error("Variable NOT resolved: Name = " + e.text() + " ID = " + id + " DNA = " + dna + " type = " + type);
            	if (idAttr != null)
            		e.attr("id", "0");
            	e.text(e.text() + " - not imported!");
            }
        }
        else if ( baseHandler instanceof ZoneImportHandler )
        {
            ZoneImportHandler zih = (ZoneImportHandler) baseHandler;
            Zone zone = zih.getZone();
            if(zone != null) {
				e.removeAttr("id");
				e.attr("dna", zone.getDna());
                if (content != null)
                    content.getPlaceholders().add(zone);
            }
        }
    }

}
