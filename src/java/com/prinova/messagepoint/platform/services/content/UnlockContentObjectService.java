package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionHistory;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionStateType;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionType;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class UnlockContentObjectService extends AbstractService{

	/**
	 * this service is only avaliable after v2.6
	 * 
	 * the owner of the model or anyone with particular permission, 
	 * assigns the model from the current updater to some one else(must have proper permission)
	 * a task is created for the assignee with a note  
	 * 
	 *  all inputs are passed in through UnlockModelServiceServcieRequest
	 */
	public static final String SERVICE_NAME = "content.UnlockContentObjectService";
	private static final Log log = LogUtil.getLog(UnlockContentObjectService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			UnlockContentObjectServiceRequest request =(UnlockContentObjectServiceRequest) context.getRequest();
			
			for(ContentObject contentObject : request.getContentObjectList()){

				boolean assignToSelf = false;
				String reassignFromUserName = null;
				if ( contentObject.getLockedFor() != null ) {
					reassignFromUserName = contentObject.getLockedForName();
					assignToSelf = (contentObject.getLockedFor().equals(request.getAssignToUserId()));
				}

				contentObject.setLockedFor(request.getAssignToUserId());
				contentObject.update();
				
				if (request.isWorkComplete()) {
					contentObject.setReadyForApproval(true);
					contentObject.setState(WorkflowState.findById(WorkflowState.STATE_WAITING_APPROVAL));
					contentObject.save();
				}
				List<User> assignedToUsers = request.getAssignedUsers();
				if(request.getAssignToUserId() != null)
				{
					User user = User.findById(request.getAssignToUserId());
					if (user != null && !assignedToUsers.contains(user))
					{
						assignedToUsers.add(user);
					}
				}
				/**
				ServiceExecutionContext taskContext = SystemTaskEditService.createContext();
				SystemTaskEditServiceRequest taskRequest = (SystemTaskEditServiceRequest) taskContext.getRequest();
				taskRequest.setAssignedTo(assignedToUsers);
				taskRequest.setNotes(request.getNote());
				taskRequest.setType(SystemTaskType.findById(SystemTaskType.TYPE_SYSTEM));
	
				String taskName = ApplicationUtil.getMessage("page.label.ASSIGNED") + " - " + contentObject.getName();
				if (taskName.length() > 96) {
					taskName = taskName.substring(0, 95);
				}
				taskRequest.setName(taskName);
				taskRequest.setActionRequired(SystemTaskType.TYPE_FOLLOW_UP);
	
				SystemTask task = contentObject.getTask();
				if (task == null) {
					task = new SystemTask();
				}
				task.setItem(contentObject);
				taskRequest.setActionItem(task);
				// set task type
				Service service = MessagepointServiceFactory.getInstance().lookupService(SystemTaskEditService.SERVICE_NAME, SystemTaskEditService.class);
				service.execute(taskContext);
				if (!taskContext.getResponse().isSuccessful()) {
					context.getResponse().mergeResultMessages(taskContext.getResponse());
					throw new Exception("TaskEditService Error");
				}
				context.getResponse().setResultValueBean(task);
				*/
				//create the configurable workflow action history for reassign
				if(request.isReassign() && !assignToSelf){
					ConfigurableWorkflowActionHistory.generateAction(null, contentObject, ConfigurableWorkflowActionType.ID_REASSIGNED, ConfigurableWorkflowActionStateType.ID_WORKING_COPY, ConfigurableWorkflowActionStateType.ID_WORKING_COPY, ApplicationUtil.getMessage(ConfigurableWorkflowActionStateType.STATE_WORKING_COPY_CODE), UserUtil.getPrincipalUser(), assignedToUsers, DateUtil.now(), request.getNote(), ConfigurableWorkflowActionHistory.ACTION_REGULAR);

					// Workflow Changes (Reassigned)
					AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_REASSIGNED, AuditMetadataBuilder.forReassign(reassignFromUserName, contentObject.getLockedForName()));

					// Task Reassign if linked to a task
					Task linkedTask = contentObject.getTask(null);
					if(linkedTask != null){
						TaskUtil.reassignTask(linkedTask, assignedToUsers, User.findById(request.getRequestorId()));
					}
				}
			}
		} catch (Exception e) {
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			log.error("Error:", e);
			throw new RuntimeException(e);
		}
	}
	
	public void validate(ServiceExecutionContext context) {
		UnlockContentObjectServiceRequest request =(UnlockContentObjectServiceRequest) context.getRequest();
		if(request == null ){
			this.getResponse(context).addErrorMessage(ApplicationErrorMessages.UL_REQUEST_CAN_NOT_BE_NULL,
					ApplicationErrorMessages.UL_REQUEST_CAN_NOT_BE_NULL,
					null,
					context.getLocale());
			return;
		}
		
		if(request.getContentObjectList() == null || request.getContentObjectList().isEmpty()){
			this.getResponse(context).addErrorMessage(ApplicationErrorMessages.UL_INSTANCE_CAN_NOT_BE_NULL,
					ApplicationErrorMessages.UL_INSTANCE_CAN_NOT_BE_NULL,
					null,
					context.getLocale());
			return;
		}
		
		for(ContentObject contentObject : request.getContentObjectList()){
			if(!hasReassignPermission(contentObject, request.getRequestorId())){
				this.getResponse(context).addErrorMessage(ApplicationErrorMessages.UL_USER_NO_PERMISSION,
						ApplicationErrorMessages.UL_USER_NO_PERMISSION,
						null,
						context.getLocale());
				return;
			}
		}
	}
	
	public boolean hasReassignPermission(Object modelInstance, Long userId){
		boolean result = false;
		if(userId != null){
			if ((Object) modelInstance instanceof ContentObject) {
				//check user permission
				if (((ContentObject)modelInstance).getLockedFor() != null && ((ContentObject)modelInstance).getLockedFor().longValue() == userId) {
					return true;
				} else {
					return (UserUtil.isAllowed(userId, Permission.ID_ROLE_MESSAGE_REASSIGNMENT));
				}
			}
		}
		return result;
	}
	

	public static ServiceExecutionContext createReassignContext(List<ContentObject> contentObjectList, Long requestorId, Long assignToUserId, String note, boolean isWorkComplete) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		UnlockContentObjectServiceRequest request = new UnlockContentObjectServiceRequest();
		request.setContentObjectList(contentObjectList);
		request.setRequestorId(requestorId);
		
		if (assignToUserId != null)
			request.setAssignToUserId(assignToUserId);
		else
			request.setAssignToUserId(requestorId);
		
		request.setNote(note);
		request.setWorkComplete(isWorkComplete);
		request.setReassign(true);
		context.setRequest(request);	

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}

	public static ServiceExecutionContext createReassignContext(ContentObject contentObject, Long requestorId, Long assignToUserId, String note, boolean isWorkComplete) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		UnlockContentObjectServiceRequest request = new UnlockContentObjectServiceRequest();
		request.setContentObject(contentObject);
		request.setRequestorId(requestorId);

		if (assignToUserId != null)
			request.setAssignToUserId(assignToUserId);
		else
			request.setAssignToUserId(requestorId);

		request.setNote(note);
		request.setWorkComplete(isWorkComplete);
		request.setReassign(true);
		context.setRequest(request);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public static ServiceExecutionContext createContext(ContentObject contentObject, Long requestorId, List<User> assignUsers, String note, boolean isWorkComplete) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		UnlockContentObjectServiceRequest request = new UnlockContentObjectServiceRequest();
		request.setContentObject(contentObject);
		request.setRequestorId(requestorId);
		request.setAssignedUsers(assignUsers);
		request.setNote(note);
		request.setWorkComplete(isWorkComplete);
		context.setRequest(request);	

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}

}
