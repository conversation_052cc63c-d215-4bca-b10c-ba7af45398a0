package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.version.CheckoutFromProductionService;
import com.prinova.messagepoint.platform.services.version.CheckoutFromProductionServiceResponse;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;
import java.util.Map;

public class BulkCheckoutContentObjectService extends AbstractService {
	public static final String SERVICE_NAME = "content.BulkCheckoutContentObjectService";
	private static final Log log = LogUtil.getLog(BulkCheckoutContentObjectService.class);

	public void execute(ServiceExecutionContext context) {
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			String userNote = request.getUserNote();
			Map<Long, Long> taskLinkedMap = request.getTaskLinkedMap();
			Map<Long, String> taskDescriptionMap = request.getTaskDescriptionMap();
			for (ContentObject contentObject : contentObjectList) {
				Task linkedToTask = null;
				if(taskLinkedMap != null && !taskLinkedMap.isEmpty()) {
					Long linkedTaskId = taskLinkedMap.get(contentObject.getId());
					linkedToTask = linkedTaskId!=null?Task.findById(linkedTaskId):null;
					userNote = taskDescriptionMap.get(contentObject.getId());
				}
				Integer taskType = null;
				if (linkedToTask != null)
					taskType = linkedToTask.getTaskType();
				else if(request.getTaskTypeMap() != null)
					taskType = request.getTaskTypeMap().getOrDefault(contentObject.getId(), null);

				Long taskMessagepointLocaleId = null;
				if (linkedToTask != null && linkedToTask.getMessagepointLocale() != null)
					taskMessagepointLocaleId = linkedToTask.getMessagepointLocale().getId();
				else if ( request.getTaskLocaleMap() != null)
					taskMessagepointLocaleId = request.getTaskLocaleMap().getOrDefault(contentObject.getId(), null);

				Long taskParameterGroupTreeNodeId = null;
				if (linkedToTask != null && linkedToTask.getParameterGroupTreeNode() != null)
					taskParameterGroupTreeNodeId = linkedToTask.getParameterGroupTreeNode().getId();
				else if ( request.getTaskVariantMap() != null)
					taskParameterGroupTreeNodeId = request.getTaskVariantMap().getOrDefault(contentObject.getId(), null);

				ServiceExecutionContext context2 = CheckoutFromProductionService.createContextForTaskCreation(contentObject,
						requestor,
						userNote,
						linkedToTask,
						taskType,
						taskMessagepointLocaleId,
						taskParameterGroupTreeNodeId);
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(CheckoutFromProductionService.SERVICE_NAME, CheckoutFromProductionService.class);
				service.execute(context2);
				if (!context2.getResponse().isSuccessful()) {
					context.getResponse().mergeResultMessages(context2.getResponse());
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkCheckoutContentObjectService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		/**
		 * Use the CheckoutService to validate each check out request
		 */
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		User requestor = UserUtil.getPrincipalUser();
		String userNote = request.getUserNote();
		for (ContentObject contentObject : contentObjectList) {
			ServiceExecutionContext context2 = CheckoutFromProductionService.createContext(contentObject, requestor, userNote);
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(CheckoutFromProductionService.SERVICE_NAME, CheckoutFromProductionService.class);
			service.validate(context2);
			if (!context2.getResponse().isSuccessful()) {
				context.getResponse().mergeResultMessages(context2.getResponse());
			}
		}
	}

	public static ServiceExecutionContext createContext(List<ContentObject> contentObjects, User requestor,
			String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkContentObjectActionServiceRequest request = new BulkContentObjectActionServiceRequest();
		context.setRequest(request);
		request.setContentObjects(contentObjects);
		request.setRequestor(requestor);
		request.setUserNote(userNote);

		CheckoutFromProductionServiceResponse serviceResp = new CheckoutFromProductionServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public static ServiceExecutionContext createContextForTaskCreation(List<ContentObject> contentObjects, User requestor,
																	   Map<Long, String> taskDescriptionMap,
																	   Map<Long, Long> taskLinkedMap,
																	   Map<Long, Integer> taskTypeMap,
																	   Map<Long, Long> taskLocaleMap,
																	   Map<Long, Long> taskVariantMap) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkContentObjectActionServiceRequest request = new BulkContentObjectActionServiceRequest();
		context.setRequest(request);
		request.setContentObjects(contentObjects);
		request.setRequestor(requestor);
		//request.setUserNote(userNote);
		request.setTaskDescriptionMap(taskDescriptionMap);
		request.setTaskLinkedMap(taskLinkedMap);
		request.setTaskTypeMap(taskTypeMap);
		request.setTaskLocaleMap(taskLocaleMap);
		request.setTaskVariantMap(taskVariantMap);

		CheckoutFromProductionServiceResponse serviceResp = new CheckoutFromProductionServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
