package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.tasks.CreateOrUpdateTaskService;
import com.prinova.messagepoint.platform.services.version.CloneServiceRequest;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;

public class ContentObjectCloneService extends AbstractService {
	public static final String SERVICE_NAME = "content.ContentObjectCloneService";
	
	private static final Log log = LogUtil.getLog(ContentObjectCloneService.class);

	private ContentObject clonedModel;
	private ContentObjectData clonedInstance;

	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CloneServiceRequest request = (CloneServiceRequest) context.getRequest();

			duplicateMessage(request);
			context.getResponse().setResultValueBean(this.clonedModel);
		} catch (Exception e) {
			log.error(" unexpected exception when invoking ContentObjectCloneService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	public void duplicateMessage(CloneServiceRequest request) {
		ContentObject originContentObject = ContentObject.findByIdWorkingDataFocusCentric(request.getCloneFromInstanceId());

		if (originContentObject != null){

			int clonedDataType = ContentObject.DATA_TYPE_ARCHIVED;
			if (originContentObject.hasWorkingData())
				clonedDataType = ContentObject.DATA_TYPE_WORKING;
			else if (originContentObject.hasActiveData())
				clonedDataType = ContentObject.DATA_TYPE_ACTIVE;

			this.clonedModel = originContentObject.clone(clonedDataType);
			this.clonedModel.setName(request.getCloneName());
			this.clonedModel.setOriginObject(null);

			if (request.getCloneToTouchpointSelectionId() != null) {
				TouchpointSelection tpSelection = TouchpointSelection.findById(request.getCloneToTouchpointSelectionId());
				if(tpSelection != null && !tpSelection.isMaster())
					this.clonedModel.setOwningTouchpointSelection(tpSelection);
			}

			this.clonedModel.save();

			this.clonedModel.setLockedFor(clonedModel.getCreatedBy());


			this.clonedInstance = this.clonedModel.getContentObjectData(clonedDataType);
			this.clonedInstance.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));
			this.clonedInstance.setWorkflowAction(null);
			this.clonedInstance.setReadyForApproval(false);
			this.clonedInstance.setState(WorkflowState.findById(WorkflowState.STATE_CONTENT));
			this.clonedInstance.save();

			// Post processing
			HibernateUtil.getManager().getSession().flush();

			if (clonedDataType != ContentObject.DATA_TYPE_WORKING) {
				// Switch data type of the cloned ContentObjectData to working type
				ContentObjectData atributes = this.clonedModel.getContentObjectDataTypeMap().remove(clonedDataType);

				StringBuilder updateQuery = new StringBuilder();
				updateQuery.append("UPDATE CONTENT_OBJECT_DATA SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(", STATUS_ID = "); updateQuery.append(VersionStatus.VERSION_WIP);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXCLUDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_EXTENDED_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_TG_INSTANCE_MAP SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_COMMENT SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE PG_TREE_NODE SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				updateQuery.append("UPDATE CONTENT_OBJECT_ZONE_PRIORITY SET DATA_TYPE = "); updateQuery.append(ContentObject.DATA_TYPE_WORKING);
				updateQuery.append(" WHERE CONTENT_OBJECT_ID = "); updateQuery.append(this.clonedModel.getId());
				updateQuery.append(" AND DATA_TYPE = "); updateQuery.append(clonedDataType);
				updateQuery.append("; ");

				NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(updateQuery.toString());
				sqlQuery.executeUpdate();

				/** Don't change the status of the historical hca, which record the status of the CA when it was on
				 * Not sure about the comment above. This is cloning, so we should change the status of the historical hca
				sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("UPDATE HIST_CONTENT_OBJ_ASSOCIATION SET DATA_TYPE = " + ContentObject.DATA_TYPE_WORKING + " WHERE CONTENT_OBJECT_ID = " + this.clonedModel.getId() + " AND DATA_TYPE = " + clonedDataType);
				sqlQuery.executeUpdate();
				 */

				// We need to disconnect previous content object data, which the composite id was modified above
				HibernateUtil.getManager().getSession().evict(atributes);

				HibernateUtil.getManager().getSession().refresh(this.clonedModel);
			}

			this.clonedModel.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
			this.clonedModel.save();

			// Push content change to elastic
			if(ElasticsearchContentUtils.isContentCompareEnabled()) {
				ElasticsearchContentUtils.updateContentForModel(
						false,
						clonedModel,
						this.clonedInstance
				);
			}
		}

		if(clonedModel != null) {
            SyncTouchpointUtil.updateContentObjectHash(clonedModel.getId(), false);

			// Task creation
			ServiceExecutionContext taskCreationContext = CreateOrUpdateTaskService.createContextForWorkingCopyCreation(clonedModel, request.getUser(), null, null);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTaskService.SERVICE_NAME, CreateOrUpdateTaskService.class);
			service.execute(taskCreationContext);
        }
    }

	@Override
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(Long documentId, Long cloneFromInstanceId, Long cloneToTouchpointSelectionId, User requestor, String cloneName) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);
		
		CloneServiceRequest request = new CloneServiceRequest();
		context.setRequest(request);
		
		request.setDocumentId(documentId);
		request.setCloneFromInstanceId(cloneFromInstanceId);
		request.setCloneToTouchpointSelectionId(cloneToTouchpointSelectionId);
		request.setCloneName(cloneName);
		request.setUser(requestor);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}
}
