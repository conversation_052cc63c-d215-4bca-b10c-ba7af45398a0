package com.prinova.messagepoint.platform.services.content;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class BulkUpdateTextStylesService extends AbstractService{

	private static final Log log = LogUtil.getLog(BulkUpdateTextStylesService.class);
	public static final String SERVICE_NAME = "content.BulkUpdateTextStylesService";

	public void execute(ServiceExecutionContext context) {
		BulkUpdateTextStylesServiceRequest request = (BulkUpdateTextStylesServiceRequest) context.getRequest();
		List<TextStyle> textStyles = filterActiveTextStyles(request.getTextStyles());
		if(textStyles == null || textStyles.isEmpty()){
			return;
		}		
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			for(TextStyle textStyle: textStyles){
				ServiceExecutionContext context2 = CreateOrUpdateTextStyleService.createContext(textStyle.getId(),
																								textStyle.getName(),
																								textStyle.getConnectorName(),
																								textStyle.getColor(),
																								textStyle.getFontName(),
																								textStyle.getWebFontName(),
																								textStyle.getPointSize(),
																								textStyle.isBold(),
																								textStyle.isUnderline(),
																								textStyle.isItalic(),
																								textStyle.isApplyTextStyleFont(),
																								textStyle.getTextStyleFont(),
																								textStyle.getTaggingOverride(),
																								textStyle.isToggleBold(),
																								textStyle.isToggleUnderline(),
																								textStyle.isToggleItalic(),
																								textStyle.isTogglePointSize(),
																								textStyle.isToggleColor(),
																								textStyle.getToggleColorValuesCMYK(),
																								textStyle.getTogglePointSizeValues(),
																								textStyle.getStyleSetDefaults());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateTextStyleService.SERVICE_NAME, CreateOrUpdateTextStyleService.class);
				service.execute(context2);
				if(!context2.getResponse().isSuccessful()){
					context2.getResponse().mergeResultMessages(context2.getResponse());
				}
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in BulkUpdateTextStylesService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	private List<TextStyle> filterActiveTextStyles(List<TextStyle> textStyles) {
		List<TextStyle> filteredTextStyles = new ArrayList<>();
		
		for (TextStyle addStyle : textStyles) {
			if (addStyle.getId() != 0L) {
				filteredTextStyles.add(addStyle);
			}
		}
		
		return filteredTextStyles;
	}

	public static ServiceExecutionContext createContext(List<TextStyle> textStyles) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkUpdateTextStylesServiceRequest request = new BulkUpdateTextStylesServiceRequest();
		context.setRequest(request);

		request.setTextStyles(textStyles);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}

}