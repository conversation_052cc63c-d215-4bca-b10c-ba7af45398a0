package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class CreateNewContentObjectServiceResponse extends SimpleServiceResponse{

	private static final long serialVersionUID = -1288957872479699295L;

	private ContentObject contentObject;
	public ContentObject getContentObject() {
		return contentObject;
	}
	public void setContentObject(ContentObject contentObject) {
		this.contentObject = contentObject;
	}
}
