package com.prinova.messagepoint.platform.services.content;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.controller.content.ContentObjectTouchpointAssignmentUtil;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.UserUtil;

public class UpdateContentObjectTouchpointAssignmentService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectTouchpointAssignmentService";
	
	private static final Log log = LogUtil.getLog(UpdateContentObjectTouchpointAssignmentService.class);
	
	public void execute(ServiceExecutionContext context) {
		UpdateContentObjectTouchpointAssignmentServiceRequest request = (UpdateContentObjectTouchpointAssignmentServiceRequest) context.getRequest();
		try {

			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			List<ContentObject> contentObjects = new ArrayList<>();
			for( Long currentId : request.getSelectedContentObjectIds() ) {
				ContentObject contentObject = ContentObject.findById(currentId);
				if (contentObject != null) {
					contentObjects.add(contentObject);
				}
			}

			Document trashTp = Document.findTrashTp();
			
			if(contentObjects.size()==1){	// Single action: allow add/remove
				ContentObject contentObject = contentObjects.get(0);
				
				// Need to maintain the touchpoints which are invisible to the user
				Set<Document> touchpoints = new HashSet<>();
				Set<Document> invisibleDocuments = new HashSet<>();
				for(Document oldDocument : contentObject.getDocuments()){
					if(!oldDocument.isVisible(UserUtil.getPrincipalUser())){
						invisibleDocuments.add(oldDocument);
					}
				}
				touchpoints.addAll(request.getTouchpointAssignments());
				// Add the invisible touchpoints to the selection
				touchpoints.addAll(invisibleDocuments);
				
				if(trashTp != null && touchpoints.contains(trashTp)){
					touchpoints.remove(trashTp);
				}

				contentObject.setVisibleDocuments(touchpoints);
				
				// Need to maintain the collections which are invisible to the user
				Set<TouchpointCollection> collections = new HashSet<>();
				Set<TouchpointCollection> invisibleCollections = new HashSet<>();
				for(TouchpointCollection oldCollection : contentObject.getTouchpointCollections()){
					if(!oldCollection.isVisible()){
						invisibleCollections.add(oldCollection);
					}
				}
				collections.addAll(request.getTpCollectionAssignments());
				// Add the invisible collections to the selection
				collections.addAll(invisibleCollections);
				contentObject.setTouchpointCollections(collections);

				contentObject.save();

				// Add newly added touchpoints to the referenced target groups
				ContentObjectTouchpointAssignmentUtil.associateNewTpsInReferencedTargetGroups(contentObject);
			}else{	// Bulk action: only add new touchpoints/collections
				for (ContentObject contentObject : contentObjects) {
					// touchpoints
					Set<Document> instanceDocs = contentObject.getDocuments();
					for (Document currentNewDocAssignment: request.getTouchpointAssignments())
						if (!instanceDocs.contains(currentNewDocAssignment))
							instanceDocs.add(currentNewDocAssignment);
					
					if(trashTp != null && instanceDocs.contains(trashTp)){
						instanceDocs.remove(trashTp);
					}

					contentObject.getVisibleDocuments().addAll(instanceDocs);
					// collections
					Set<TouchpointCollection> instanceCollections = contentObject.getTouchpointCollections();
					for (TouchpointCollection currentNewCollAssignment: request.getTpCollectionAssignments())
						if (!instanceCollections.contains(currentNewCollAssignment))
							instanceCollections.add(currentNewCollAssignment);
					contentObject.getTouchpointCollections().addAll(instanceCollections);

					contentObject.save();

					// Associate newly added touchpoints to the referenced target groups
					ContentObjectTouchpointAssignmentUtil.associateNewTpsInReferencedTargetGroups(contentObject);
				}
			}
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectTouchpointAssignmentService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e);
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
		
	}
	
	public static ServiceExecutionContext createContext(Set<Long> contentObjectIds, List<Document> documents, List<TouchpointCollection> tpCollections) {
		
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		UpdateContentObjectTouchpointAssignmentServiceRequest request = new UpdateContentObjectTouchpointAssignmentServiceRequest();
		context.setRequest(request);
		
		request.setSelectedContentObjectIds(contentObjectIds);
		request.setTouchpointAssignments(documents);
		request.setTpCollectionAssignments(tpCollections);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);
		
		return context;
	}
}