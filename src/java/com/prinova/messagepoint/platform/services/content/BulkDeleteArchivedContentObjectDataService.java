package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.DocumentPreview;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class BulkDeleteArchivedContentObjectDataService extends AbstractService {
	public static final String SERVICE_NAME = "content.BulkDeleteArchivedContentObjectDataService";
	private static final Log log = LogUtil.getLog(BulkDeleteArchivedContentObjectDataService.class);

	public void execute(ServiceExecutionContext context) {
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			String userNote = request.getUserNote();
			for (ContentObject contentObject : contentObjectList) {

				contentObject.deleteArchiveData();

				if (!contentObject.hasWorkingData() && !contentObject.hasActiveData()) {

					// The similar code is in BulkDeleteWorkingContentObjectDataService.java

					contentObject.setRemoved(true);
					contentObject.setZone(null);

					// remove previews
					List<DocumentPreview> previews = DocumentPreview.findByMessage(contentObject.getId());
					for (DocumentPreview preview : previews) {
						HibernateUtil.getManager().deleteObject(DocumentPreview.class, preview.getId());
					}

					// TODO Remove PG_TREE_NODES for Dynamic Type
//							if(messageInstance.isDynamicMessage())
//								discardSelectionWorkingCopy(messageInstance);

					// Clear the global assets reference
					if (contentObject.isGlobalContentObject()) {
						List<ContentObject> localSmartTexts = ContentObject.findByGlobalParentObject(contentObject, null);
						if(localSmartTexts != null && !localSmartTexts.isEmpty()){
							for(ContentObject localSmartText : localSmartTexts){
								clearGlobalReference(localSmartText);
							}
						}
					}
					else
					{
						clearGlobalReference(contentObject);
					}
				}

				contentObject.save();

				// Audit (Audit Delete archive)
				AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_ARCHIVE_DELETED, null);
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkDeleteArchivedContentObjectDataService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
	}

	private void clearGlobalReference(ContentObject contentObject){
		contentObject.setGlobalParentObject(null);
		contentObject.setLastGlobalParentObjectSyncDate(null);
		contentObject.save();
	}

	public static ServiceExecutionContext createContext(List<ContentObject> contentObjects, User requestor,
			String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkContentObjectActionServiceRequest request = new BulkContentObjectActionServiceRequest();
		context.setRequest(request);
		request.setContentObjects(contentObjects);
		request.setRequestor(requestor);
		request.setUserNote(userNote);
		
		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

}