package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.List;

public class CreateOrUpdateListStyleServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -6729028405998880493L;

	private boolean				isCust;
	private long 				id;

	private int					action;
	
	private ListStyle			listStyle;
	
	private boolean 			toggleAlignment = false;
	private int 				alignmentId;
	private String 				listSpacingBefore;
	private String 				listSpacingAfter;
	private String 				listSpacingRight;
	
	private int 				lineSpacing;
	private int					lineSpacingType;
	
	private TextStyle			textStyle;
	private String				taggingOverride;

	private List<String> 		bulletLeftMargin;
	private List<String> 		bulletRightMargin;
	private List<String> 		bulletTopMargin;
	private List<String> 		bulletBottomMargin;
	
	private String				bulletSymbolOverrides;

	private String 				indent;
	private boolean 			hangingIndent = false;
	
	private long 				documentId;

	public boolean isCust() {
		return isCust;
	}
	public void setCust(boolean isCust) {
		this.isCust = isCust;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public int getAction() { return action; }
	public void setAction(int action) { this.action = action; }
	public ListStyle getListStyle() {
		return listStyle;
	}
	public void setListStyle(ListStyle listStyle) {
		this.listStyle = listStyle;
	}

	public boolean getToggleAlignment() {
		return this.toggleAlignment;
	}
	public void setToggleAlignment(boolean toggleAlignment) {
		this.toggleAlignment = toggleAlignment;
	}
	public int getAlignmentId() {
		return this.alignmentId;
	}
	public void setAlignmentId(int alignmentId) {
		this.alignmentId = alignmentId;
	}
	public String getListSpacingBefore() {
		return listSpacingBefore;
	}
	public void setListSpacingBefore(String listSpacingBefore) {
		this.listSpacingBefore = listSpacingBefore;
	}
	public String getListSpacingAfter() {
		return listSpacingAfter;
	}
	public void setListSpacingAfter(String listSpacingAfter) {
		this.listSpacingAfter = listSpacingAfter;
	}
	public String getListSpacingRight() {
		return listSpacingRight;
	}
	public void setListSpacingRight(String listSpacingRight) {
		this.listSpacingRight = listSpacingRight;
	}
	public int getLineSpacing() {
		return lineSpacing;
	}
	public void setLineSpacing(int lineSpacing) {
		this.lineSpacing = lineSpacing;
	}
	public int getLineSpacingType() {
		return lineSpacingType;
	}
	public void setLineSpacingType(int lineSpacingType) {
		this.lineSpacingType = lineSpacingType;
	}
	public TextStyle getTextStyle() {
		return textStyle;
	}
	public void setTextStyle(TextStyle textStyle) {
		this.textStyle = textStyle;
	}
	public String getTaggingOverride() {
		return taggingOverride;
	}
	public void setTaggingOverride(String taggingOverride) {
		this.taggingOverride = taggingOverride;
	}
	public List<String> getBulletLeftMargin() { return bulletLeftMargin; }
	public void setBulletLeftMargin(List<String> bulletLeftMargin) { this.bulletLeftMargin = bulletLeftMargin; }
	public List<String> getBulletRightMargin() { return bulletRightMargin; }
	public void setBulletRightMargin(List<String> bulletRightMargin) { this.bulletRightMargin = bulletRightMargin; }
	public List<String> getBulletTopMargin() { return bulletTopMargin; }
	public void setBulletTopMargin(List<String> bulletTopMargin) { this.bulletTopMargin = bulletTopMargin; }
	public List<String> getBulletBottomMargin() { return bulletBottomMargin; }
	public void setBulletBottomMargin(List<String> bulletBottomMargin) { this.bulletBottomMargin = bulletBottomMargin; }

	public String getBulletSymbolOverrides() {
		return bulletSymbolOverrides;
	}
	public void setBulletSymbolOverrides(String bulletSymbolOverrides) {
		this.bulletSymbolOverrides = bulletSymbolOverrides;
	}
	public String getIndent() {
		return indent;
	}
	public void setIndent(String indent) {
		this.indent = indent;
	}
	public boolean isHangingIndent() {
		return hangingIndent;
	}
	public void setHangingIndent(boolean hangingIndent) {
		this.hangingIndent = hangingIndent;
	}
	public long getDocumentId() {
		return documentId;
	}
	public void setDocumentId(long documentId) {
		this.documentId = documentId;
	}

}