package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentObjectTouchpointAssignmentUtil;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.ParameterGroupInstance;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.communication.LibraryItemUsageType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;

import java.util.*;

public class UpdateContentObjectOverviewService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectOverviewService";
	private static final Log log = LogUtil.getLog(UpdateContentObjectOverviewService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateContentObjectOverviewServiceRequest request = (UpdateContentObjectOverviewServiceRequest) context.getRequest();
			ContentObjectOverviewWrapper wrapper = request.getContentObjectOverviewWrapper();
			ContentObject contentObject = null;
			int newDeliveryType =0;
			newDeliveryType= wrapper.getDeliveryType();	
			boolean deliveryTypeChanged = false;
			if (wrapper.getContentObject().getId() != 0) {
				if (wrapper.getContentObject().getDeliveryType()!=newDeliveryType) {
					deliveryTypeChanged=true;
				}
				wrapper.getContentObject().setDeliveryType(newDeliveryType);

				contentObject = (ContentObject) HibernateUtil.getManager().getSession().merge(wrapper.getContentObject());
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
				contentObject.checkAndAdjustFocus();
				contentObject.setNewComment(wrapper.getContentObject().getNewComment());
//				messageInstance.fixAssociations();
			} else {
				wrapper.getContentObject().setDeliveryType(newDeliveryType);
				contentObject = wrapper.getContentObject();
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
				contentObject.checkAndAdjustFocus();
			}
			
			if ( wrapper.getFormWrapper() != null ) {
				MetadataForm metadataForm = wrapper.getFormWrapper().getMetadataForm();
				metadataForm.save();
				contentObject.setMetadataForm(metadataForm);
			}

			ContentObjectComment newComment = contentObject.getNewComment();
			if ((newComment != null) && (newComment.getComment() != null) && (!newComment.getComment().trim().isEmpty())) {
				newComment.setCreated(new Date(System.currentTimeMillis()));
				newComment.setContentObject(contentObject);
				newComment.setDataType(contentObject.getFocusOnDataType());
				contentObject.getComments().add(newComment);
				HibernateUtil.getManager().saveObject(newComment, true);
			}
			/**
			 * Update the external id which resides on tenant_meta_data table
			 */
			String persistedExternalId = contentObject.getExternalIdOrNull();
			String externalId = wrapper.getExternalId();
			TenantMetaData tmd = TenantMetaData.findByInternalId(TenantMetaData.MODEL_SIGN_MESSAGE, contentObject.getId());
			User user = UserUtil.getPrincipalUser();
			if (externalId != null && !externalId.trim().isEmpty()) {

				if(tmd==null){
					//insert new one
					TenantMetaData.insert(
					TenantMetaData.MODEL_SIGN_MESSAGE,
					contentObject.getId(),
					externalId,
					user);
				}else if(!tmd.getExternalId().trim().equals(externalId.trim())){
					//update the old one
					tmd.setExternalId(externalId);
					tmd.save();
				}
			} else {
				// delete
				if (tmd != null && (externalId == null || externalId.trim().isEmpty())) {
					HibernateUtil.getManager().deleteObject(tmd);
				}
			}

			 if (wrapper.getParameterGroup() != null) {	// Become dynamic asset

				if (wrapper.getContentObject().getId() != 0 && contentObject.getParameterGroup() != null) {
					if(wrapper.getParameterGroup().getId() != contentObject.getParameterGroup().getId()){
						
						// Update the new parameter group
						// PGTN
						List<ParameterGroupTreeNode> pgtns = ParameterGroupTreeNode.findAllNodesForDynamicAsset(contentObject, ContentObject.DATA_TYPE_WORKING);
						List<ParameterGroupInstanceCollection> pgics = new ArrayList<>();
						for(ParameterGroupTreeNode pgtn : pgtns){
							pgtn.setParameterGroup(wrapper.getParameterGroup());
							pgtn.save();
							
							if(!pgics.contains(pgtn.getParameterGroupInstanceCollection())){
								pgics.add(pgtn.getParameterGroupInstanceCollection());
							}
						}
						// PGI
						for(ParameterGroupInstanceCollection pgic : pgics){
							for(ParameterGroupInstance pgi : pgic.getParameterGroupInstances()){
								pgi.setParameterGroup(wrapper.getParameterGroup());
								pgi.save();
							}
						}

						contentObject.setParameterGroup(wrapper.getParameterGroup());
					}
				}else{	// New
					contentObject.setParameterGroup(wrapper.getParameterGroup());
				}
			 } else {
				 if (contentObject.isStructuredContentEnabled())
				 {
					 // Structured content object has to be in all versions, thus dynamic variants have to be disabled in all data types
					 contentObject.setParameterGroup(null, ContentObject.DATA_TYPE_ALL);
				 }
				 else {
					 // Static working content object can have active or archived versions enabled for dynamic variants
					 contentObject.setParameterGroup(null, ContentObject.DATA_TYPE_WORKING);
				}
			 }

			 // Handle switching between structure, dynamic, global and static types
			List<ContentObjectAssociation> variantForContentDeletion = new ArrayList<>();
			if(contentObject.isStructuredContentEnabled()){
				// New type is structured enabled, which means it cannot have any dynamic variant in ALL versions
				variantForContentDeletion.addAll(ContentObjectAssociation.findAllDynamicVariants(contentObject, ContentObject.DATA_TYPE_ALL));
			}else if(contentObject.isDynamicVariantEnabled()){
				// New type is dynamic, which means it cannot have any TP variants in ALL versions
				variantForContentDeletion.addAll(ContentObjectAssociation.findAllStructuredVariants(contentObject));
			}else{
				// New type is static, which means it cannot have dynamic variant in WORKING version and cannot have TP variants in ALL versions
				variantForContentDeletion.addAll(ContentObjectAssociation.findAllDynamicVariants(contentObject, ContentObject.DATA_TYPE_WORKING));
				variantForContentDeletion.addAll(ContentObjectAssociation.findAllStructuredVariants(contentObject));
			}

			if(!variantForContentDeletion.isEmpty()){
				// Delete variant contents
				for(ContentObjectAssociation coa : variantForContentDeletion) {
					if (coa.getTypeId() == ContentAssociationType.ID_OWNS) {
						if (coa.getContent() != null && coa.getReferencingImageLibrary() == null) {
							coa.getContent().deleteContentSafe(false);
						}
					}
				}

				NativeQuery sqlQuery;
				List<ParameterGroupTreeNode> pgtnForDeletion = new ArrayList<>();
				for(ContentObjectAssociation coa : variantForContentDeletion) {
					if (coa.getContentObjectPGTreeNode() != null) {
						HistoricalContentObjectAssociation.deleteHistoricalContentObjectAssociationByVariant(coa.getContentObjectPGTreeNode().getId());

						pgtnForDeletion.add(coa.getContentObjectPGTreeNode());
					}

					// Delete content object association of variants
					contentObject.getContentObjectAssociations().remove(coa);
				}
				if (!pgtnForDeletion.isEmpty()) {
					contentObject.save();
					for (ParameterGroupTreeNode pgtn : pgtnForDeletion) {
						ParameterGroupInstanceCollection pgic = pgtn.getParameterGroupInstanceCollection();
						pgtn.delete();
						if (pgic != null) {
							sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM parameter_group_instance WHERE pg_instance_collection_id = " + pgic.getId());
							sqlQuery.executeUpdate();
							pgic.delete();
						}
					}
				}
			}

			Zone selectedZone = wrapper.getSelectedZone();

			if (selectedZone != null)
			{
				contentObject.setDeliveryType(wrapper.getDeliveryType());
				Zone fromZone = contentObject.getZone();
				ContentObjectZonePriority.fixAssociationDueToContentObjectZoneDeliveryChanges(contentObject, fromZone, selectedZone, deliveryTypeChanged, request.isHasPlaceholderChange());

				if (fromZone.getId() != selectedZone.getId() && fromZone.getContentObjects().contains(contentObject)) {
					fromZone.removeContentObject(contentObject);
				}

				if (!selectedZone.getContentObjects().contains(contentObject)) {
					selectedZone.addContentObject(contentObject);
				}

				contentObject.setZone(selectedZone);
			}

			contentObject.setSupportsBarcodes(wrapper.isSupportsBarcodes());
			if(contentObject.getUsageTypeId() != LibraryItemUsageType.ID_COMMUNICATION){
				contentObject.setSupportsContentMenus(wrapper.isSupportsContentMenus());
			}else {
				contentObject.setSupportsContentMenus(false);
			}
			contentObject.setSupportsForms(wrapper.isSupportsForms());
			contentObject.setSupportsTables(wrapper.isSupportsTables());

			contentObject.setMetatags(wrapper.getMetatags());

			// Record the details change before the persisting
			Map<String, Object> persistedCustomProperties = new HashMap<>();
			persistedCustomProperties.put("externalId", persistedExternalId);
			Map<String, Object> updatedCustomProperties = new HashMap<>();
			updatedCustomProperties.put("externalId", externalId);

			AuditEventUtil.auditContentObjectDetailsChanged(contentObject, null, persistedCustomProperties, updatedCustomProperties);

			contentObject.save();

			// Update tag cloud
			int cloudTypeId = TagCloudType.ID_MESSAGE;
			if(wrapper.getContentObject().isLocalSmartText() || wrapper.getContentObject().isGlobalSmartText()){
				cloudTypeId = TagCloudType.ID_EMBEDDED_TEXT;
			}else if(wrapper.getContentObject().isLocalImage() || wrapper.getContentObject().isGlobalImage()){
				cloudTypeId = TagCloudType.ID_IMAGE_LIBRARY;
			}

			Document document = null;
			if(!wrapper.getContentObject().getIsGlobalContentObject()){
				document = contentObject.getFirstDocumentDelivery();
			}

			// Associate newly added touchpoints to the referenced target groups
			ContentObjectTouchpointAssignmentUtil.associateNewTpsInReferencedTargetGroups(contentObject);

			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
			ServiceExecutionContext executionContext = UpdateTagCloudService.createContext(cloudTypeId, contentObject.getMetatags(), document, false);
			if ( service != null && executionContext != null )
				service.execute(executionContext);

			UpdateContentObjectOverviewServiceResponse response = (UpdateContentObjectOverviewServiceResponse) context.getResponse();
			response.setMessageId(contentObject.getId());

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectOverviewService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub

	}

	public static ServiceExecutionContext createContext(ContentObjectOverviewWrapper contentObjectOverviewWrapper,
														boolean hasPlaceholderChange) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateContentObjectOverviewServiceRequest request = new UpdateContentObjectOverviewServiceRequest();
		context.setRequest(request);

		request.setContentObjectOverviewWrapper(contentObjectOverviewWrapper);
		request.setHasPlaceholderChange(hasPlaceholderChange);

		UpdateContentObjectOverviewServiceResponse serviceResp = new UpdateContentObjectOverviewServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
