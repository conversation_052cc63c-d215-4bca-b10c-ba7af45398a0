package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import org.springframework.beans.factory.annotation.Autowired;

public class CreateOrUpdateTextStyleFontService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateOrUpdateTextStyleFontService.class);
	private enum FONT_TYPE {
		TTF_DEFAULT,
		TTF_BOLD,
		TTF_ITALIC,
		TTF_BOLD_ITALIC,
	}
	public static final String SERVICE_NAME = "content.CreateOrUpdateTextStyleFontService";

	@Autowired
	private CacheDataRepository cacheDataRepository;

	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			CreateOrUpdateTextStyleFontServiceRequest request = (CreateOrUpdateTextStyleFontServiceRequest) context.getRequest();
			TextStyleFont textStyleFont = request.getTextStyleFont();
			Long ttfSandboxId = request.getTtfSandboxId();
			Long ttfBoldSandboxId = request.getTtfBoldSandboxId();
			Long ttfItalicSandboxId = request.getTtfItalicSandboxId();
			Long ttfBoldItalicSandboxId = request.getTtfBoldItalicSandboxId();

			if(ttfSandboxId != null){
				extractSandboxFile(textStyleFont, ttfSandboxId, FONT_TYPE.TTF_DEFAULT);
			}
			if(ttfBoldSandboxId != null){
				extractSandboxFile(textStyleFont, ttfBoldSandboxId, FONT_TYPE.TTF_BOLD);
			}
			if(ttfItalicSandboxId != null){
				extractSandboxFile(textStyleFont, ttfItalicSandboxId, FONT_TYPE.TTF_ITALIC);
			}
			if(ttfBoldItalicSandboxId != null){
				extractSandboxFile(textStyleFont, ttfBoldItalicSandboxId, FONT_TYPE.TTF_BOLD_ITALIC);
			}

			textStyleFont.save();

			if (cacheDataRepository != null) {
				cacheDataRepository.updateStylesData();
			} else {
				log.error("CacheDataRepository is null. Unable to update styles data.");
			}
			getResponse(context).setResultValueBean(textStyleFont);

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CreateOrUpdateTextStyleFontService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void extractSandboxFile(TextStyleFont textStyleFont, Long sandboxId, FONT_TYPE fontType) {
		SandboxFile sb = SandboxFile.findById(sandboxId);
		DatabaseFile databaseFile = new DatabaseFile(sb.getFileContent(), sb.getFileName(), sb.getContentType(), sb.getSandboxType());
		databaseFile.makeHash(false);
		if (fontType == FONT_TYPE.TTF_DEFAULT) {
			textStyleFont.setTtfFile(databaseFile);
		}  else if (fontType == FONT_TYPE.TTF_BOLD) {
			textStyleFont.setTtfBoldFile(databaseFile);
		} else if (fontType == FONT_TYPE.TTF_ITALIC) {
			textStyleFont.setTtfItalicFile(databaseFile);
		}  else if (fontType == FONT_TYPE.TTF_BOLD_ITALIC) {
			textStyleFont.setTtfBoldItalicFile(databaseFile);
		}
		sb.delete();
	}

	public static ServiceExecutionContext createContext(TextStyleFont textStyleFont, Long ttfSandboxId, Long ttfBoldSandboxId, Long ttfItalicSandboxId, Long ttfBoldItalicSandboxId){
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		CreateOrUpdateTextStyleFontServiceRequest request = new CreateOrUpdateTextStyleFontServiceRequest();
		request.setTextStyleFont(textStyleFont);
		request.setTtfSandboxId(ttfSandboxId);
		request.setTtfBoldSandboxId(ttfBoldSandboxId);
		request.setTtfItalicSandboxId(ttfItalicSandboxId);
		request.setTtfBoldItalicSandboxId(ttfBoldItalicSandboxId);
		context.setRequest(request);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;		
	}

	@Override
	public void validate(ServiceExecutionContext context) {
	}

}
