package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO.MultipartContentZoneVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO.ZonePartVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SubContentType;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Prinova Inc. 1998-2020
 * All rights reserved.
 * 
 * UpdateContentObjectContentService
 *
 * @contentRefactor
 *
 * @since 20.1
 * <AUTHOR> Team
 */
public class UpdateContentObjectContentService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectContentService";
	private static final Log log = LogUtil.getLog(UpdateContentObjectContentService.class);

	private static final int ACTION_UPDATE_REGULAR_SINGLE_PART = 1;
	private static final int ACTION_UPDATE_REGULAR_MULTI_PART = 2;

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateContentObjectContentServiceRequest request = (UpdateContentObjectContentServiceRequest) context.getRequest();

			ContentObject contentObject = ContentObject.findById(request.getContentObjectId());
			contentObject.setFocusOnDataTypeCheckAndAdjust(request.getDataType());
			int dataType = contentObject.getFocusOnDataType();
			List<MessagepointLocale> locales = contentObject.getContentObjectLanguagesAsLocales();
			MessagepointLocale defaultLocale = contentObject.getDefaultContentObjectLanguageAsLocale();

			contentObject.setState(WorkflowState.findById(WorkflowState.STATE_CONTENT));

			switch (request.getAction()) {
				case (ACTION_UPDATE_REGULAR_SINGLE_PART): {
					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation contentObjectAssociation = ContentObjectAssociation.findParentByContentObjectAndParameters(contentObject, dataType, null, locale, null);
						ContentVO contentVO = request.getContents().get(locale.getId());
						updateContent(contentObjectAssociation, request.getContentAssociationType(), contentVO, contentObject, dataType, locale, defaultLocale, null);
						
						// GRAPHIC: Set graphicContentType if unset
						if ( (contentObject.getContentType() == null || contentObject.getContentType().getId() == ContentType.GRAPHIC) &&
							 (contentObject.getGraphicTypeId() == null || contentObject.getGraphicTypeId() == 0) &&
							 contentVO.getFile() != null && 
							 contentVO.getFile().getOriginalFilename() != null ) {
							contentObject.setContentType( ContentType.findById(ContentType.GRAPHIC) );
							SubContentType subContentType = ContentObjectContentUtil.getSubContentTypeFromFilename(contentVO.getFile().getOriginalFilename());
							if ( subContentType != null )
								contentObject.setGraphicTypeId(Integer.valueOf(String.valueOf(subContentType.getId())));
						}

					}
					
					if ( (contentObject.getIsSharedFreeform() || ((contentObject.getIsTouchpointLocal() || contentObject.getIsGlobalSmartText()) && contentObject.isSupportsTables(dataType)))  && request.getCanvasMaxWidth() != null && request.getCanvasTrimWidth() != null ) {

						contentObject.setCanvasMaxWidth( DecimalValueUtil.hydrate(request.getCanvasMaxWidth()), dataType );
						contentObject.setCanvasMaxHeight( DecimalValueUtil.hydrate(request.getCanvasMaxHeight()), dataType );
						contentObject.setCanvasTrimWidth( DecimalValueUtil.hydrate(request.getCanvasTrimWidth()), dataType );
						contentObject.setCanvasTrimHeight( DecimalValueUtil.hydrate(request.getCanvasTrimHeight()), dataType );
						
					}
					contentObject.save();
					break;
				}
				case (ACTION_UPDATE_REGULAR_MULTI_PART): {
					MultipartContentZoneVO multipartContentZoneVO = request.getMultipartContentVO().getZoneVO();
					for (ZonePartVO zonePartVO : multipartContentZoneVO.getParts()) {
						Map<Long, ContentVO> multipartContents = zonePartVO.getLanguageContentVOs();
						processLocalContent(locales, defaultLocale, contentObject, dataType, request.getContentAssociationType(), multipartContents, zonePartVO);

						// GRAPHIC: Set graphicContentType if unset
						if ( (contentObject.getContentType() == null || contentObject.getContentType().getId() == ContentType.GRAPHIC) &&
							 (contentObject.getGraphicTypeId() == null || contentObject.getGraphicTypeId() == 0) ) {
							for ( Long localeId : zonePartVO.getLanguageContentVOs().keySet() ) {
								ContentVO contentVO = zonePartVO.getLanguageContentVOs().get(localeId);
								if ( contentVO.getFile() != null && contentVO.getFile().getOriginalFilename() != null ) {
									SubContentType subContentType = ContentObjectContentUtil.getSubContentTypeFromFilename(contentVO.getFile().getOriginalFilename());
									if ( subContentType != null )
										contentObject.setGraphicTypeId(Integer.valueOf(String.valueOf(subContentType.getId())));
								}
							}
						}

					}
                    contentObject.save();
					break;
				}
			}			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectContentService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void processLocalContent(List<MessagepointLocale> locales, MessagepointLocale defaultLocale, ContentObject contentObject, int dataType, ContentAssociationType contentAssociationType, Map<Long, ContentVO> multipartContents, ZonePartVO zonePartVO) throws Exception {

		contentObject.setFocusOnDataTypeCheckAndAdjust(dataType);

		for (MessagepointLocale locale : locales) {
			ContentObjectAssociation contentObjectAssociation = ContentObjectAssociation.findByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, null, locale,  zonePartVO.getZonePart());
			ContentVO contentVO = multipartContents.get(locale.getId());
			updateContent(contentObjectAssociation, contentAssociationType, contentVO, contentObject, dataType, locale, defaultLocale, zonePartVO);
		}
	}

	private void updateContent(ContentObjectAssociation contentObjectAssociation,
											 ContentAssociationType contentAssociationType,
											 ContentVO contentVO,
											 ContentObject contentObject,
							   				 int dataType,
							    			 MessagepointLocale locale, MessagepointLocale defaultLocale, ZonePartVO zonePartVO) throws Exception {
		boolean isNewCA = false;
		if (contentObjectAssociation == null) {
			contentObjectAssociation = new ContentObjectAssociation();
			contentObjectAssociation.setDataType(dataType);
			contentObjectAssociation.setContentObject(contentObject);
			contentObjectAssociation.setContent(null);
			isNewCA = true;
		}
		
		contentObjectAssociation.setMessagepointLocale(locale);
		
		Content content = contentObjectAssociation.getContent();

		if (contentAssociationType != null && contentAssociationType.getId() == ContentAssociationType.ID_SUPPRESSES)
		{
			// Suppressed content
			if (content != null) {
				if (contentObjectAssociation.getTypeId() == ContentAssociationType.ID_OWNS && contentObjectAssociation.getReferencingImageLibrary() == null) {
					// This should be always true if contentObjectAssociation.getContent() != null and data are correct in the database
					// However we had an issue with a code and there is still possibility that content was set even when it was referencing something else
					// We cannot delete this referenced content, thus the if statement here, so we delete the content only if data are correct in the database
					content.deleteContentSafe(true);
				}
			}
			contentObjectAssociation.setContent(null);
			contentObjectAssociation.setReferencingImageLibrary(null);
			contentObjectAssociation.setTypeId(ContentAssociationType.ID_SUPPRESSES);

			contentVO.setContentSupplier(() -> null);
			contentVO.setAppliedImageFilename(null);
			contentVO.setImageLocation(null);
			contentVO.setImageName(null);
			contentVO.setImageUploadedDate(null);
			contentVO.setUseImageLibrary(false);
			contentVO.setImageLink(null);
			contentVO.setImageAltText(null);
			contentVO.setImageExtLink(null);
			contentVO.setImageExtPath(null);
			contentVO.setAssetId(null);
			contentVO.setAssetSite(null);
			contentVO.setAssetURL(null);
			contentVO.setAssetLastUpdate(null);
			contentVO.setAssetLastSync(null);

		} else {

			if (zonePartVO != null && zonePartVO.isEmptyZonePart()) {    // Leave empty
				if (content != null) {
					if (contentObjectAssociation.getTypeId() == ContentAssociationType.ID_OWNS && contentObjectAssociation.getReferencingImageLibrary() == null) {
						// This should be always true if contentObjectAssociation.getContent() != null and data are correct in the database
						// However we had an issue with a code and there is still possibility that content was set even when it was referencing something else
						// We cannot delete this referenced content, thus the if statement here, so we delete the content only if data are correct in the database
						content.deleteContentSafe(true);
					}
				}
				contentObjectAssociation.setContent(null);
				contentObjectAssociation.setReferencingImageLibrary(null);
				contentObjectAssociation.setTypeId(ContentAssociationType.ID_EMPTY);

			} else {

				// Content is referencing Image Library
				if (!contentVO.isSameAsDefault() && contentVO.isUseImageLibrary()) {
					if (content != null) {
						if (contentObjectAssociation.getTypeId() == ContentAssociationType.ID_OWNS && contentObjectAssociation.getReferencingImageLibrary() == null) {
							// This should be always true if contentObjectAssociation.getContent() != null and data are correct in the database
							// However we had an issue with a code and there is still possibility that content was set even when it was referencing something else
							// We cannot delete this referenced content, thus the if statement here, so we delete the content only if data are correct in the database
							content.deleteContentSafe(true);
						}
					}
					contentObjectAssociation.setContent(null);
					contentObjectAssociation.setReferencingImageLibrary(ContentObject.findByIdActiveDataFocusCentric(contentVO.getImageLibraryId()));
					contentObjectAssociation.setTypeId(ContentAssociationType.ID_OWNS);

				} else {

					boolean isNew = false;
					if (contentVO.isSameAsDefault() && locale.getId() != defaultLocale.getId()) { // Same as default
						if (content != null) {
							if (contentObjectAssociation.getTypeId() == ContentAssociationType.ID_OWNS && contentObjectAssociation.getReferencingImageLibrary() == null) {
								// This should be always true if contentObjectAssociation.getContent() != null and data are correct in the database
								// However we had an issue with a code and there is still possibility that content was set even when it was referencing something else
								// We cannot delete this referenced content, thus the if statement here, so we delete the content only if data are correct in the database
								content.deleteContentSafe(true);
								content = null;
							}
						}
						contentObjectAssociation.setContent(null);
						contentObjectAssociation.setReferencingImageLibrary(null);
						contentObjectAssociation.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);

						contentVO.setContentSupplier(() -> null);
						contentVO.setAppliedImageFilename(null);
						contentVO.setImageLocation(null);
						contentVO.setImageName(null);
						contentVO.setImageUploadedDate(null);
						contentVO.setUseImageLibrary(false);
						contentVO.setImageLink(null);
						contentVO.setImageAltText(null);
						contentVO.setImageExtLink(null);
						contentVO.setImageExtPath(null);
						contentVO.setAssetId(null);
						contentVO.setAssetSite(null);
						contentVO.setAssetURL(null);
						contentVO.setAssetLastUpdate(null);
						contentVO.setAssetLastSync(null);

					} else {

						// Custom content
						if (content == null) {
							content = new Content();
							isNew = true;
						}
						content.setContent(contentVO.getContent());
						content.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						if (isNew) {
							content.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
							content.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
							content.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
							content.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
						} else {
							content.setImageLink(contentVO.getImageLink());
							content.setImageAltText(contentVO.getImageAltText());
							content.setImageExtLink(contentVO.getImageExtLink());
							content.setImageExtPath(contentVO.getImageExtPath());
						}
						content.setAssetId(contentVO.getAssetId());
						content.setAssetSite(contentVO.getAssetSite());
						content.setAssetURL(contentVO.getAssetURL());
						content.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						content.setAssetLastSync(contentVO.getAssetLastSync());
						saveAssociatedFiles(content, contentObject, dataType, contentVO, isNew, locale);

						contentObjectAssociation.setContent(content);
						contentObjectAssociation.setReferencingImageLibrary(null);
						contentObjectAssociation.setTypeId(ContentAssociationType.ID_OWNS);
					}

					if (content != null) {
						// Set the Target touchpoint to resolve the local smart text and local image references in content save
						if (CloneHelper.getTargetDocument() == null) {
							Document targetTouchpoint = contentObject.getDocument();
							if (!contentObject.getIsTouchpointLocal()) {
								targetTouchpoint = contentObject.getFirstDocumentDelivery();
							}
							CloneHelper.setTargetDocument(targetTouchpoint);
						}

						content.save(isNew);
					}

				}

				if (isNewCA) {
					contentObjectAssociation.save();
					contentObject.getContentObjectAssociations().add(contentObjectAssociation);
				}
			}
		}

		if(zonePartVO != null){
			contentObjectAssociation.setZonePart(zonePartVO.getZonePart());
		}
		
		contentObjectAssociation.save();

	}

	
	public void saveAssociatedFiles(Content obj, ContentObject contentObject, int dataType, ContentVO contentVO, boolean isNew, MessagepointLocale locale) throws Exception {
		if (contentObject.isMultipartType()) {
			if ((contentObject.getContentType().getId() != ContentType.TEXT)) {
				if ((contentVO.getFile() != null)) {
					MultipartFile theFile = contentVO.getFile();
					if (isNew) {
						if (theFile.getSize() > 0) {
							String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + locale.getLanguageCode() + "/" + contentVO.getFile().getOriginalFilename();
							File image = new File(imageLocation);
							if(!image.getParentFile().exists())
								image.getParentFile().mkdirs();
							contentVO.getFile().transferTo(image);
							obj.clearTextContent();
							obj.setImageLocation(imageLocation);
							obj.setImageName(contentVO.getFile().getOriginalFilename());
							obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
							obj.setImageUploadedDate(DateUtil.now());
							obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
							obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
							obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
							obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
							obj.setAssetId(contentVO.getAssetId());
							obj.setAssetSite(contentVO.getAssetSite());
							obj.setAssetURL(contentVO.getAssetURL());
							obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
							obj.setAssetLastSync(contentVO.getAssetLastSync());
						} else {
							String pathToUseToSaveFileOnServer = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + locale.getLanguageCode() + "/";
							if (!contentVO.isSameAsDefault() && contentVO.getImageLocation() != null) {
								File directory = new File(pathToUseToSaveFileOnServer);
								directory.mkdirs();
								File file = new File(contentVO.getImageLocation());
								if (file.exists()){
									FileUtil.copy(file, new File(pathToUseToSaveFileOnServer + file.getName()));
									obj.clearTextContent();
									obj.setImageLocation(pathToUseToSaveFileOnServer + file.getName());
									obj.setImageName(file.getName());
									obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
									obj.setImageUploadedDate(DateUtil.now());
									obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
									obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
									obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
									obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
									obj.setAssetId(contentVO.getAssetId());
									obj.setAssetSite(contentVO.getAssetSite());
									obj.setAssetURL(contentVO.getAssetURL());
									obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
									obj.setAssetLastSync(contentVO.getAssetLastSync());
								}else{
									obj.clearTextAndGraphicContent();
								}
							}
						}
					} else {
						if (theFile.getSize() > 0) {
							String defaultImageLocation = obj.getImageLocation();
							File defaultFile = null;
							if (defaultImageLocation != null) {
								defaultFile = new File(defaultImageLocation);
							}
							boolean hasChanged = (defaultFile == null) || !StringUtils.equals(obj.getImageName(), contentVO.getFile().getOriginalFilename());
							hasChanged = hasChanged || (defaultFile.length() != contentVO.getFile().getSize());
							if (hasChanged) {
								if (defaultFile == null || defaultFile.delete()) {
									String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + locale.getLanguageCode() + "/" + contentVO.getFile().getOriginalFilename();

									File image = new File(imageLocation);
									if(!image.getParentFile().exists())
										image.getParentFile().mkdirs();

									File[] filesInDirectory = image.listFiles();
									if (filesInDirectory != null) {
										for (File fileToDelete : filesInDirectory) {
											fileToDelete.delete();
										}
									}

									contentVO.getFile().transferTo(image);
									obj.clearTextContent();
									obj.setImageLocation(imageLocation);
									obj.setImageName(contentVO.getFile().getOriginalFilename());
									obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
									obj.setImageUploadedDate(DateUtil.now());
									obj.setImageLink(contentVO.getImageLink());
									obj.setImageAltText(contentVO.getImageAltText());
									obj.setImageExtLink(contentVO.getImageExtLink());
									obj.setImageExtPath(contentVO.getImageExtPath());
									obj.setAssetId(contentVO.getAssetId());
									obj.setAssetSite(contentVO.getAssetSite());
									obj.setAssetURL(contentVO.getAssetURL());
									obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
									obj.setAssetLastSync(contentVO.getAssetLastSync());
								}
							}
						} else {
							if (contentVO.isSameAsDefault() && contentVO.getImageLocation() != null) {
								File file = new File(contentVO.getImageLocation());
								if (file.exists()) {
									String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + locale.getLanguageCode() + "/";
									File path = new File(imageLocation);
									if (!path.exists())
										path.mkdirs();

									File[] filesInDirectory = path.listFiles();
									if (filesInDirectory != null) {
										for (File fileToDelete : filesInDirectory) {
											fileToDelete.delete();
										}
									}

									File image = new File(imageLocation + file.getName());
									FileUtil.copy(file, image);
									obj.clearTextContent();
									obj.setImageLocation(imageLocation + file.getName());
									obj.setImageName(image.getName());
									obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
									obj.setImageUploadedDate(contentVO.getImageUploadedDate());
									obj.setImageLink(contentVO.getImageLink());
									obj.setImageAltText(contentVO.getImageAltText());
									obj.setImageExtLink(contentVO.getImageExtLink());
									obj.setImageExtPath(contentVO.getImageExtPath());
									obj.setAssetId(contentVO.getAssetId());
									obj.setAssetSite(contentVO.getAssetSite());
									obj.setAssetURL(contentVO.getAssetURL());
									obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
									obj.setAssetLastSync(contentVO.getAssetLastSync());
								}
							}
						}
					}
				} else if (contentVO.isSameAsDefault() && contentVO.getImageLocation() != null) {
					File file = new File(contentVO.getImageLocation());
					if (file.exists()) {
						String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + locale.getLanguageCode() + "/";
						File path = new File(imageLocation);
						if (!path.exists())
							path.mkdirs();

						File[] filesInDirectory = path.listFiles();
						if (filesInDirectory != null) {
							for (File fileToDelete : filesInDirectory) {
								fileToDelete.delete();
							}
						}

						File image = new File(imageLocation + file.getName());
						FileUtil.copy(file, image);
						obj.clearTextContent();
						obj.setImageLocation(imageLocation + file.getName());
						obj.setImageName(image.getName());
						obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						obj.setImageLink(contentVO.getImageLink());
						obj.setImageAltText(contentVO.getImageAltText());
						obj.setImageExtLink(contentVO.getImageExtLink());
						obj.setImageExtPath(contentVO.getImageExtPath());
						obj.setAssetId(contentVO.getAssetId());
						obj.setAssetSite(contentVO.getAssetSite());
						obj.setAssetURL(contentVO.getAssetURL());
						obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						obj.setAssetLastSync(contentVO.getAssetLastSync());
					}
				}
			}
		} else {
			if (isNew) {
				if ((contentObject.getContentType().getId() != ContentType.TEXT && contentObject.getContentType().getId() != ContentType.VIDEO) && (contentVO.getFile() != null)) {
					String imageFileLocation = saveFile(contentVO, obj.getGuid(), locale.getLanguageCode());
					if (contentVO.getFile().getOriginalFilename() != null && !contentVO.getFile().getOriginalFilename().isEmpty()) {
						obj.clearTextContent();
						obj.setImageLocation(imageFileLocation);
						obj.setImageName(contentVO.getFile().getOriginalFilename());
						obj.setImageUploadedDate(DateUtil.now());
						obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
						obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
						obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
						obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
						obj.setAssetId(contentVO.getAssetId());
						obj.setAssetSite(contentVO.getAssetSite());
						obj.setAssetURL(contentVO.getAssetURL());
						obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						obj.setAssetLastSync(contentVO.getAssetLastSync());
					} else {
						obj.clearTextContent();
						obj.setImageLocation(contentVO.getImageLocation());
						obj.setImageUploadedDate(contentVO.getImageUploadedDate());
						obj.setImageName(contentVO.getImageName());
						obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
						obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
						obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
						obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
						obj.setAssetId(contentVO.getAssetId());
						obj.setAssetSite(contentVO.getAssetSite());
						obj.setAssetURL(contentVO.getAssetURL());
						obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						obj.setAssetLastSync(contentVO.getAssetLastSync());
					}
				}
			} else {
				// Otherwise the message exists and we are updating it.
				if ((contentObject.getContentType().getId() != ContentType.TEXT && contentObject.getContentType().getId() != ContentType.VIDEO) && (contentVO.getFile() != null)) {
					if (contentVO.getFile().getOriginalFilename() != null && !contentVO.getFile().getOriginalFilename().isEmpty()) {
						String imageFileLocation = saveFile(contentVO, obj.getGuid(), locale.getLanguageCode());
						obj.clearTextContent();
						obj.setImageLocation(imageFileLocation);
						obj.setImageName(contentVO.getFile().getOriginalFilename());
						obj.setImageUploadedDate(DateUtil.now());
						obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						obj.setImageLink(contentVO.getImageLink());
						obj.setImageAltText(contentVO.getImageAltText());
						obj.setImageExtLink(contentVO.getImageExtLink());
						obj.setImageExtPath(contentVO.getImageExtPath());
						obj.setAssetId(contentVO.getAssetId());
						obj.setAssetSite(contentVO.getAssetSite());
						obj.setAssetURL(contentVO.getAssetURL());
						obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						obj.setAssetLastSync(contentVO.getAssetLastSync());
					} else {
						obj.clearTextContent();
						obj.setImageLocation(contentVO.getImageLocation());
						if (contentVO.getImageLocation() != null){
							File defaultImage = new File(contentVO.getImageLocation());
							if (defaultImage.exists()) {
								obj.setImageName(defaultImage.getName());
							}
						}else{
							obj.setImageName(null);
						}
						obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
						obj.setImageUploadedDate(contentVO.getImageUploadedDate());
						obj.setImageLink(contentVO.getImageLink());
						obj.setImageAltText(contentVO.getImageAltText());
						obj.setImageExtLink(contentVO.getImageExtLink());
						obj.setImageExtPath(contentVO.getImageExtPath());
						obj.setAssetId(contentVO.getAssetId());
						obj.setAssetSite(contentVO.getAssetSite());
						obj.setAssetURL(contentVO.getAssetURL());
						obj.setAssetLastUpdate(contentVO.getAssetLastUpdate());
						obj.setAssetLastSync(contentVO.getAssetLastSync());
					}
				}
			}
		}
		
		// Clean up the Sandbox file
		contentVO.cleanupSandboxFile();
	}

	private String saveFile(ContentVO obj, String guid, String languageCode) {
		MultipartFile theFile = obj.getFile();
		String imageFileLocation = null;
		try {
			String pathToUseToSaveFileOnServer = obj.getComputedGraphicFilePath(guid, languageCode);
			// Make any directories that aren't yet in existence.
			File directory = new File(pathToUseToSaveFileOnServer);
			directory.mkdirs();

			// Delete any existing files in directory since there should only
			// ever be one in each folder.
			File[] filesInDirectory = directory.listFiles();
			if (filesInDirectory != null) {
				for (File fileToDelete : filesInDirectory) {
					fileToDelete.delete();
				}
			}
			imageFileLocation = pathToUseToSaveFileOnServer + theFile.getOriginalFilename();
			if(theFile.getSize() > 0){
				theFile.transferTo(new File(imageFileLocation));
			}else {
				if(!obj.isSameAsDefault() && obj.getImageLocation() != null){
					File file = new File(obj.getImageLocation());
					if (file.exists()){
						FileUtil.copy(file, new File(pathToUseToSaveFileOnServer + file.getName()));
						obj.setContentSupplier(() -> null);
						obj.setImageLocation(pathToUseToSaveFileOnServer + file.getName());
						obj.setImageName(file.getName());
						imageFileLocation = pathToUseToSaveFileOnServer + file.getName();
					}else{
						obj.setContentSupplier(() -> null);
						obj.setImageLocation(null);
						obj.setImageName(null);
						obj.setAppliedImageFilename(null);
						obj.setImageUploadedDate(null);
						obj.setImageLink(null);
						obj.setImageAltText(null);
						obj.setImageExtLink(null);
						obj.setImageExtPath(null);
						obj.setAssetId(null);
						obj.setAssetSite(null);
						obj.setAssetURL(null);
						obj.setAssetLastUpdate(null);
						obj.setAssetLastSync(null);
						imageFileLocation = null;						
					}
				}
			}
		} catch (IOException e) {
			log.error("Error: ", e);
		}
		return imageFileLocation;
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContextForRegularSinglepart(long contentObjectId,
																			ContentAssociationType contentAssociationType,
																			Map<Long, ContentVO> contents,
																			String maxCanvasWidth,
																			String maxCanvasHeight,
																			String trimCanvasWidth,
																			String trimCanvasHeight) {
		return createContext(ACTION_UPDATE_REGULAR_SINGLE_PART, contentObjectId, contentAssociationType, contents, null, maxCanvasWidth, maxCanvasHeight, trimCanvasWidth, trimCanvasHeight);
	}
	
	public static ServiceExecutionContext createContextForRegularMultipart(long contentObjectId,
																		    ContentAssociationType contentAssociationType,
																			ContentObjectMultipartContentVO messageMultipartContent) {
		return createContext(ACTION_UPDATE_REGULAR_MULTI_PART, contentObjectId, contentAssociationType, null, messageMultipartContent, null, null, null, null);
	}

	private static ServiceExecutionContext createContext(int action,
														 long contentObjectId,
														 ContentAssociationType contentAssociationType,
														 Map<Long, ContentVO> contents,
														 ContentObjectMultipartContentVO multipartContentVO,
														 String maxCanvasWidth,
														 String maxCanvasHeight,
														 String trimCanvasWidth,
														 String trimCanvasHeight) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateContentObjectContentServiceRequest request = new UpdateContentObjectContentServiceRequest();
		context.setRequest(request);

		request.setAction(action);
		request.setContentObjectId(contentObjectId);

		request.setContentAssociationType(contentAssociationType);

		request.setContents(contents);
		request.setMultipartContentVO(multipartContentVO);

		request.setCanvasMaxWidth(maxCanvasWidth);
		request.setCanvasMaxHeight(maxCanvasHeight);
		request.setCanvasTrimWidth(trimCanvasWidth);
		request.setCanvasTrimHeight(trimCanvasHeight);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}