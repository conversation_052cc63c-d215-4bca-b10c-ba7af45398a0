package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class TouchpointContentSelectionUpdateServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -2467204671369072121L;
	
	private TouchpointContentObjectContentSelection contentSelection;
	private ContentObjectContentSelectionVO			contentVO;
	private ContentObjectMultipartContentVO 		mpContentVO;
	private ContentAssociationType 					contentType;
	private ParameterGroupTreeNode 					refereningTreeNode;
	private boolean 								updateVersionControl;
	// Used in discarding structured working copy action by the AbortWIPService
	private boolean 								isAbortWIPAction = false;
	
	private String									canvasMaxWidth;
	private String									canvasMaxHeight;
	private String									canvasTrimWidth;
	private String									canvasTrimHeight;
	
	private long userid;
	private User user;
	public User getUser() {
		if (user==null) {
			user = User.findById(userid);
		}
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}
	public ContentObjectContentSelectionVO getContentVO() {
		return contentVO;
	}
	public void setContentVO(ContentObjectContentSelectionVO contentVO) {
		this.contentVO = contentVO;
	}
	public ContentObjectMultipartContentVO getMpContentVO() {
		return mpContentVO;
	}
	public void setMpContentVO(ContentObjectMultipartContentVO mpContentVO) {
		this.mpContentVO = mpContentVO;
	}
	public ContentAssociationType getContentType() {
		return contentType;
	}
	public void setContentType(ContentAssociationType contentType) {
		this.contentType = contentType;
	}
	public ParameterGroupTreeNode getRefereningTreeNode() {
		return refereningTreeNode;
	}
	public void setRefereningTreeNode(ParameterGroupTreeNode refereningTreeNode) {
		this.refereningTreeNode = refereningTreeNode;
	}
	public TouchpointContentObjectContentSelection getContentSelection() {
		return contentSelection;
	}
	public void setContentSelection(TouchpointContentObjectContentSelection contentSelection) {
		this.contentSelection = contentSelection;
	}	
	public boolean getUpdateVersionControl() {
		return updateVersionControl;
	}
	public void setUpdateVersionControl(boolean updateVersionControl) {
		this.updateVersionControl = updateVersionControl;
	}
	public boolean isAbortWIPAction() {
		return isAbortWIPAction;
	}
	public void setAbortWIPAction(boolean isAbortWIPAction) {
		this.isAbortWIPAction = isAbortWIPAction;
	}
	public String getCanvasMaxWidth() {
		return canvasMaxWidth;
	}
	public void setCanvasMaxWidth(String canvasMaxWidth) {
		this.canvasMaxWidth = canvasMaxWidth;
	}
	public String getCanvasMaxHeight() {
		return canvasMaxHeight;
	}
	public void setCanvasMaxHeight(String canvasMaxHeight) {
		this.canvasMaxHeight = canvasMaxHeight;
	}
	public String getCanvasTrimWidth() {
		return canvasTrimWidth;
	}
	public void setCanvasTrimWidth(String canvasTrimWidth) {
		this.canvasTrimWidth = canvasTrimWidth;
	}
	public String getCanvasTrimHeight() {
		return canvasTrimHeight;
	}
	public void setCanvasTrimHeight(String canvasTrimHeight) {
		this.canvasTrimHeight = canvasTrimHeight;
	}

}
