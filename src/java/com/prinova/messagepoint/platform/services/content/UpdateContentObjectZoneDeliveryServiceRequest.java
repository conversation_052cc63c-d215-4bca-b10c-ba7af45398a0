package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditWrapper;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateContentObjectZoneDeliveryServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 6080145991938396988L;
	
	private ContentObjectZonePriorityEditWrapper ContentObjectZonePriorityEditWrapper;

	public ContentObjectZonePriorityEditWrapper getContentObjectZonePriorityEditWrapper() {
		return ContentObjectZonePriorityEditWrapper;
	}
	public void setContentObjectZonePriorityEditWrapper(
			ContentObjectZonePriorityEditWrapper ContentObjectZonePriorityEditWrapper) {
		this.ContentObjectZonePriorityEditWrapper = ContentObjectZonePriorityEditWrapper;
	}

}
