package com.prinova.messagepoint.platform.services.content;

import java.util.Set;

import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CreateOrUpdateTextStyleServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 6767711098298617778L;

	private long 			id;
	private String 			name;
	private String 			connectorName;
	private String 			color;
	private String 			fontName;
	private String 			webFontName;
	private int 			pointSize;
	private boolean 		bold;
	private boolean 		underline;
	private boolean 		italic;
	private boolean 		applyTextStyleFont;
	private TextStyleFont	textStyleFont;
	private String			taggingOverride;
	
	private boolean 		toggleBold;
	private boolean 		toggleUnderline;
	private boolean 		toggleItalic;
	private boolean 		togglePointSize;
	private boolean 		toggleColor;
	private Set<String> 	toggleColorValues;
	private Set<String> 	togglePointSizeValues;
	private String 			styleSetDefaults;

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getConnectorName() {
		return connectorName;
	}
	public void setConnectorName(String connectorName) {
		this.connectorName = connectorName;
	}
	public String getColor() {
		return color;
	}
	public void setColor(String color) {
		this.color = color;
	}
	public String getFontName() {
		return fontName;
	}
	public void setFontName(String fontName) {
		this.fontName = fontName;
	}
	public String getWebFontName() {
		return webFontName;
	}
	public void setWebFontName(String webFontName) {
		this.webFontName = webFontName;
	}
	public int getPointSize() {
		return pointSize;
	}
	public void setPointSize(int pointSize) {
		this.pointSize = pointSize;
	}
	public boolean isBold() {
		return bold;
	}
	public void setBold(boolean bold) {
		this.bold = bold;
	}
	public boolean isUnderline() {
		return underline;
	}
	public void setUnderline(boolean underline) {
		this.underline = underline;
	}
	public boolean isItalic() {
		return italic;
	}
	public void setItalic(boolean italic) {
		this.italic = italic;
	}
	public boolean isApplyTextStyleFont() {
		return applyTextStyleFont;
	}
	public void setApplyTextStyleFont(boolean applyTextStyleFont) {
		this.applyTextStyleFont = applyTextStyleFont;
	}
	public TextStyleFont getTextStyleFont() {
		return textStyleFont;
	}
	public void setTextStyleFont(TextStyleFont textStyleFont) {
		this.textStyleFont = textStyleFont;
	}
	public String getTaggingOverride() {
		return taggingOverride;
	}
	public void setTaggingOverride(String taggingOverride) {
		this.taggingOverride = taggingOverride;
	}
	public boolean isToggleBold() {
		return toggleBold;
	}
	public void setToggleBold(boolean toggleBold) {
		this.toggleBold = toggleBold;
	}
	public boolean isToggleUnderline() {
		return toggleUnderline;
	}
	public void setToggleUnderline(boolean toggleUnderline) {
		this.toggleUnderline = toggleUnderline;
	}
	public boolean isToggleItalic() {
		return toggleItalic;
	}
	public void setToggleItalic(boolean toggleItalic) {
		this.toggleItalic = toggleItalic;
	}
	public boolean isTogglePointSize() {
		return togglePointSize;
	}
	public void setTogglePointSize(boolean togglePointSize) {
		this.togglePointSize = togglePointSize;
	}
	public boolean isToggleColor() {
		return toggleColor;
	}
	public void setToggleColor(boolean toggleColor) {
		this.toggleColor = toggleColor;
	}
	public Set<String> getToggleColorValues() {
		return toggleColorValues;
	}
	public void setToggleColorValues(Set<String> toggleColorValues) {
		this.toggleColorValues = toggleColorValues;
	}
	public Set<String> getTogglePointSizeValues() {
		return togglePointSizeValues;
	}
	public void setTogglePointSizeValues(Set<String> togglePointSizeValues) {
		this.togglePointSizeValues = togglePointSizeValues;
	}
	public String getStyleSetDefaults() {
		return styleSetDefaults;
	}
	public void setStyleSetDefaults(String styleSetDefaults) {
		this.styleSetDefaults = styleSetDefaults;
	}
}
