package com.prinova.messagepoint.platform.services.content;

import java.util.Date;
import java.util.Set;

import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * MessageContentSelectionCheckInProductionService
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class MessageContentSelectionCheckInProductionService extends AbstractService {
	
	private static final Log log = LogUtil.getLog(MessageContentSelectionCheckInProductionService.class);
	public static final String SERVICE_NAME = "content.MessageContentSelectionCheckInProductionService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			MessageContentSelectionCheckInProductionServiceRequest request = (MessageContentSelectionCheckInProductionServiceRequest) context.getRequest();

			// SelectionContent sc = checkinSelectionContent(request);
			// context.getResponse().setResultValueBean(sc);

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in MessageContentSelectionCheckInProductionService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context), ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
	
	private boolean requiresPublish(Set<ContentObjectAssociation> assocs, Date latestProductionDate) {
		if(latestProductionDate == null)
			return true;
		boolean publish = false;
		for (ContentObjectAssociation ca : assocs) {
			if (ca.getUpdated().after(latestProductionDate)) {
				return true;
			} else if(ca.getContent() != null && ca.getContent().getUpdated().after(latestProductionDate)) {
				return true;
			}
		}
		return publish;
	}

	/** TODO use PGTreeNode instead of SelectionContent
	private SelectionContent checkinSelectionContent(MessageContentSelectionCheckInProductionServiceRequest request) {
		SelectionContent sc = request.getSelectionContent();
		Set<ContentAssociation> assocs = sc.getContentAssociations();
		Date productionDate = DateUtil.now();
		
		Date latestProductionDate = ProductionContentAssociation.getSelectionLastestProductionDate(sc.getId());

		if(this.requiresPublish(assocs, latestProductionDate) /*&& sc.getStatus() != null && sc.getStatus().getId() == VersionStatus.VERSION_WAITING_APPROVAL*/ /**) {
		for (ContentAssociation ca : assocs) {
//			if (ca.getUpdated().after(latestProductionDate)) {
				ProductionContentAssociation pca = new ProductionContentAssociation();
//				pca.setContentAssociation(ca);
				pca.setMessagepointLocale(ca.getMessagepointLocale());
				pca.setTypeId(ca.getTypeId());
				pca.setProductionDate(productionDate);
				pca.setParameterGroupTreeNode(ca.getParameterGroupTreeNode());
				pca.setReferencingTreeNode(ca.getReferencingTreeNode());
				pca.setSha256Hash(ca.getSha256Hash());
				if (ca.getContent() != null) {
					Content pc = new Content();
					pc.setEncodedContent(ca.getContent().getEncodedContent());
					if (ca.getContent().isGraphic() && ca.getContent().getImageLocation() != null) {
						File srcFile = new File(ca.getContent().getImageLocation());
						String mcCloneImageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + File.separator + pc.getGuid() + File.separator + ca.getMessagepointLocale().getLanguageCode() + File.separator + ca.getContent().getImageName();
						File destFile = new File(mcCloneImageLocation);

						if (!destFile.getParentFile().exists()) {
							destFile.getParentFile().mkdirs();
						}

						FileUtil.copy(srcFile, destFile);
						pc.setImageLocation(mcCloneImageLocation);
						pc.setImageName(destFile.getName());
						pc.setImageUploadedDate(ca.getContent().getImageUploadedDate());
						pc.setImageAltText(Content.copyImageAltTextFrom(ca.getContent().getImageAltText()));
					}
					pc.setAppliedImageFilename(ca.getContent().getAppliedImageFilename());
					pc.setImageLink(Content.copyImageLinkFrom(ca.getContent().getImageLink()));
					pc.setImageAltText(Content.copyImageAltTextFrom(ca.getContent().getImageAltText()));
					pc.setAssetId(ca.getContent().getAssetId());
					pc.setAssetSite(ca.getContent().getAssetSite());
					pc.setAssetURL(ca.getContent().getAssetURL());
					pc.setAssetLastUpdate(ca.getContent().getAssetLastUpdate());
					pc.setAssetLastSync(ca.getContent().getAssetLastSync());

						// Set the Target touchpoint to resolve the local smart text and local image references in content save
						if(CloneHelper.getTargetDocument() == null) {
							CloneHelper.setTargetDocument(ca.getDocument());
						}

					pc.save();
					pca.setContent(pc);
				}
				pca.setMessage(ca.getMessage());
				pca.setMessageInstance(ca.getMessageInstance());
				pca.setEmbeddedContent(ca.getEmbeddedContent());
				pca.setEmbeddedContentInstance(ca.getEmbeddedContentInstance());
				pca.setContentLibrary(ca.getContentLibrary());
				pca.setContentLibraryInstance(ca.getContentLibraryInstance());
				pca.setReferencingContentLibrary(ca.getReferencingContentLibrary());
				pca.setReferencingLocalContentLibrary(ca.getReferencingLocalContentLibrary());
				pca.setZonePart(ca.getZonePart());
				pca.save();
			}
		}

		return sc;
	}
    **/

	public void validate(ServiceExecutionContext context) {
		
	}

	public static ServiceExecutionContext createContext(long contentInstanceId, Long userId) {
		SimpleExecutionContext context = new SimpleExecutionContext();

		context.setServiceName(SERVICE_NAME);

		MessageContentSelectionCheckInProductionServiceRequest request = new MessageContentSelectionCheckInProductionServiceRequest();
		context.setRequest(request);

		request.setContentInstanceId(contentInstanceId);
		request.setUserId(userId);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}