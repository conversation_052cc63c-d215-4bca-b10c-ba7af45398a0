package com.prinova.messagepoint.platform.services.content;

import java.util.HashSet;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class CloneParagraphStyleService extends AbstractService{

	private static final Log log = LogUtil.getLog(CloneParagraphStyleService.class);
	public static final String SERVICE_NAME = "content.CloneParagraphStyleService";

	public void execute(ServiceExecutionContext context) {		
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			CloneParagraphStyleServiceRequest request = (CloneParagraphStyleServiceRequest)context.getRequest();
			ParagraphStyle oriParaStyle = request.getParagraphStyle();
			ParagraphStyle newParaStyle = new ParagraphStyle();
			newParaStyle.setName(request.getCloneName());
			newParaStyle.setConnectorName(oriParaStyle.getConnectorName());
			newParaStyle.setAlignmentId(oriParaStyle.getAlignmentId());
			newParaStyle.setParagraphSpacingBefore(oriParaStyle.getParagraphSpacingBefore());
			newParaStyle.setParagraphSpacingAfter(oriParaStyle.getParagraphSpacingAfter());
			newParaStyle.setLeftMargin(oriParaStyle.getLeftMargin());
			newParaStyle.setRightMargin(oriParaStyle.getRightMargin());
			newParaStyle.setLineSpacing(oriParaStyle.getLineSpacing());
			newParaStyle.setLineSpacingType(oriParaStyle.getLineSpacingType());
			newParaStyle.setIndent(oriParaStyle.getIndent());
			newParaStyle.setNumberedListTypeId(oriParaStyle.getNumberedListTypeId());
			newParaStyle.setBorderTypeId(oriParaStyle.getBorderTypeId());
			newParaStyle.setBorderWidth(oriParaStyle.getBorderWidth());
			newParaStyle.setBulletSymbolId(oriParaStyle.getBulletSymbolId());
			newParaStyle.setTextStyle(oriParaStyle.getTextStyle());
            Set<Zone> zones = new HashSet<>(oriParaStyle.getZones());
			newParaStyle.setZones(zones);
			newParaStyle.save(true);
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CloneParagraphStyleService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public static ServiceExecutionContext createContext(ParagraphStyle paragraphStyle, String cloneName) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CloneParagraphStyleServiceRequest request = new CloneParagraphStyleServiceRequest();
		context.setRequest(request);

		request.setParagraphStyle(paragraphStyle);
		request.setCloneName(cloneName);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}

}