package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class UpdateContentObjectOverviewServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -6813018634783189716L;

    private ContentObjectOverviewWrapper contentObjectOverviewWrapper ;
    private boolean	hasPlaceholderChange;

	public ContentObjectOverviewWrapper getContentObjectOverviewWrapper() {
		return contentObjectOverviewWrapper;
	}
	public void setContentObjectOverviewWrapper(ContentObjectOverviewWrapper contentObjectOverviewWrapper) {
		this.contentObjectOverviewWrapper = contentObjectOverviewWrapper;
	}

	public boolean isHasPlaceholderChange() {
		return hasPlaceholderChange;
	}
	public void setHasPlaceholderChange(boolean hasPlaceholderChange) {
		this.hasPlaceholderChange = hasPlaceholderChange;
	}

}
