package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CreateOrUpdateContentTargetingServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 4717852257573223093L;

	private int 	action;
	private Long 	contentObjectId;

	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}

	public Long getContentObjectId() {
		return contentObjectId;
	}
	public void setContentObjectId(Long contentObjectId) {
		this.contentObjectId = contentObjectId;
	}

}
