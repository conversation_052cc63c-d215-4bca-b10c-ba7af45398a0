package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.HistoricalContentObjectAssociation;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class DeleteContentObjectDynamicVariantTreeNodeService extends AbstractService {
	private static final Log log = LogUtil.getLog(DeleteContentObjectDynamicVariantTreeNodeService.class);
	public static final String SERVICE_NAME = "content.DeleteContentObjectDynamicVariantTreeNodeService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				getResponse(context).logMessages(log);
				return;
			}
			
			DeleteContentObjectDynamicVariantTreeNodeServiceRequest request = (DeleteContentObjectDynamicVariantTreeNodeServiceRequest) context.getRequest();

			ContentObject contentObject = ContentObject.findById(request.getObjectInstanceId());
			if (contentObject != null)
			{
				/*
				 * Delete the content and content association
				 */

				List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_ALL, ParameterGroupTreeNode.findById(request.getPgTreeNodeId()));
				for (ContentObjectAssociation ca : cas) {
					if(ca.getContent() != null && ca.getTypeId() == ContentAssociationType.ID_OWNS && ca.getReferencingImageLibrary() == null) {
						ca.getContent().deleteContentSafe(false);
					}
					contentObject.getContentObjectAssociations().remove(ca);
					ca.delete();
				}
				cas.clear();

				/*
				 * Delete all historical content object associations for the given variant tree nodeDelete all historical content object associations for the given variant tree node
				 */

				HistoricalContentObjectAssociation.deleteHistoricalContentObjectAssociationByVariant(request.getPgTreeNodeId());

				/*
				 * Delete the parameter group tree node
				 */

				ParameterGroupTreeNode.delete(request.getPgTreeNodeId());

				contentObject.save();
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in DeleteContentObjectDynamicVariantTreeNodeService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					context.getServiceName() + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		DeleteContentObjectDynamicVariantTreeNodeServiceRequest request;
		String errorMsgKey = null;
		try {
			request = (DeleteContentObjectDynamicVariantTreeNodeServiceRequest) context.getRequest();
			errorMsgKey = "Parameter Group Tree Node Id = " + request.getPgTreeNodeId() + " object instance id ="
					+ request.getObjectInstanceId();

			if (request.getPgTreeNode().hasChildren()) {
				getResponse(context).addErrorMessage(errorMsgKey,
						ApplicationErrorMessages.SC_CANNOT_REMOVE_HAVE_CHILDREN,
						context.getServiceName(),
						context.getLocale());
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in DeleteContentObjectDynamicVariantTreeNodeService.validate(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(errorMsgKey,
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_VALIDATION,
					context.getServiceName() + e,
					context.getLocale());
		}
	}

	public static ServiceExecutionContext createContext(long objectInstanceId, long pgTreeNodeId, boolean forceVariantDelete, Long requestorId) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		DeleteContentObjectDynamicVariantTreeNodeServiceRequest request = new DeleteContentObjectDynamicVariantTreeNodeServiceRequest();
		request.setPgTreeNodeId(pgTreeNodeId);
		request.setForceVariantDelete(forceVariantDelete);
		request.setObjectInstanceId(objectInstanceId);
		request.setRequestorId(requestorId);
		context.setRequest(request);
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
