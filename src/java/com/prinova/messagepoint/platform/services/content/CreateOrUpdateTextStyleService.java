package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.Set;

public class CreateOrUpdateTextStyleService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateOrUpdateTextStyleService.class);
	public static final String SERVICE_NAME = "content.CreateOrUpdateTextStyleService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateTextStyleServiceRequest request = (CreateOrUpdateTextStyleServiceRequest) context.getRequest();
			
			boolean isNew = (request.getId() == Long.MAX_VALUE);
			
			TextStyle style = null;
			if (isNew) {
				style = new TextStyle();
			} else {
				style = TextStyle.findById(request.getId());
			}
			
			if(request.getName().trim().equals(String.valueOf(Long.MIN_VALUE))){	// Removed text style
				style.delete();
			}else{
				style.setName(request.getName());
				style.setConnectorName(request.getConnectorName());
				style.setColor(request.getColor());
				style.setFontName(request.getFontName());
				style.setPointSize(request.getPointSize());
				style.setBold(request.isBold());
				style.setUnderline(request.isUnderline());
				style.setItalic(request.isItalic());
				style.setApplyTextStyleFont(request.isApplyTextStyleFont());
				if(request.isApplyTextStyleFont()){
					style.setTextStyleFont(request.getTextStyleFont());
				}else{
					style.setWebFontName(request.getWebFontName());
				}
				style.setTaggingOverride(request.getTaggingOverride());
				
				style.setToggleBold(request.isToggleBold());
				style.setToggleColor(request.isToggleColor());
				style.setToggleItalic(request.isToggleItalic());
				style.setTogglePointSize(request.isTogglePointSize());
				style.setToggleUnderline(request.isToggleUnderline());
				style.setToggleColorValues(request.getToggleColorValues());
				style.setTogglePointSizeValues(request.getTogglePointSizeValues());
				style.setStyleSetDefaults(request.getStyleSetDefaults());

				style.save();
			}

			getResponse(context).setResultValueBean(style.getId());

			if(isNew){
				// Audit (Audit Text Style creation)
				AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_TEXT_STYLE, request.getName(), style.getId(), AuditActionType.ID_CHANGE_CREATED, null);
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CreateOrUpdateStyleService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	public static ServiceExecutionContext createContext(long id,
														String name,
														String connectorName,
														String color,
														String fontName,
														String webFontName,
														int pointSize,
														boolean bold,
														boolean underline,
														boolean italic,
														boolean applyTextStyleFont,
														TextStyleFont textStyleFont,
														String taggingOverride,
														boolean 	toggleBold,
														boolean 	toggleUnderline,
														boolean 	toggleItalic,
														boolean 	togglePointSize,
														boolean 	toggleColor,
														Set<String> toggleColorValues,
														Set<String> togglePointSizeValues,
														String styleSetDefaults
			) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateTextStyleServiceRequest request = new CreateOrUpdateTextStyleServiceRequest();
		context.setRequest(request);

		request.setId(id);
		request.setName(name);
		request.setConnectorName(connectorName);
		request.setColor(color);
		request.setFontName(fontName);
		request.setWebFontName(webFontName);
		request.setPointSize(pointSize);
		request.setBold(bold);
		request.setUnderline(underline);
		request.setItalic(italic);
		request.setApplyTextStyleFont(applyTextStyleFont);
		request.setTextStyleFont(textStyleFont);
		request.setTaggingOverride(taggingOverride);
		
		request.setToggleBold(toggleBold);
		request.setToggleColor(toggleColor);
		request.setToggleItalic(toggleItalic);
		request.setTogglePointSize(togglePointSize);
		request.setToggleUnderline(toggleUnderline);
		request.setToggleColorValues(toggleColorValues);
		request.setTogglePointSizeValues(togglePointSizeValues);
		request.setStyleSetDefaults(styleSetDefaults);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}

}