package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;

import java.util.List;

public class ReassignContentObjectForTranslationService extends AbstractService{

	/**
	 * this service is only avaliable after v2.6
	 * 
	 * the owner of the model or anyone with particular permission, 
	 * assigns the model from the current updater to some one else(must have proper permission)
	 * a task is created for the assignee with a note  
	 * 
	 *  all inputs are passed in through ReassignContentObjectForTranslationService
	 */
	public static final String SERVICE_NAME = "content.ReassignContentObjectForTranslationService";
	private static final Log log = LogUtil.getLog(ReassignContentObjectForTranslationService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			UnlockContentObjectServiceRequest request =(UnlockContentObjectServiceRequest) context.getRequest();
			ConfigurableWorkflow actionToWorkflow = request.getWorkflow();

			for(ContentObject contentObject : request.getContentObjectList()){

				boolean assignToSelf = false;
				String reassignFromUserName = "";
				if ( contentObject.getCurrentTranslators() != null ) {
					// Concatenate the names of the current translators to reassignFromUserName using stream
					reassignFromUserName = contentObject.getCurrentTranslators().stream().map(User::getFullName).reduce((a, b) -> a + ", " + b).orElse("");
					// Check if the current user is the one being reassigned using stream
					assignToSelf = contentObject.getCurrentTranslators().stream().anyMatch(user -> user.getId() == request.getAssignToUserId());
				}

				List<User> currentTranslators	= contentObject.getCurrentTranslators();
				boolean isCurrentTranslator		= currentTranslators.stream().anyMatch(ct->ct.getId()==UserUtil.getPrincipalUserId());

				List<User> assignedToUsers = request.getAssignedUsers();
				if(request.getAssignToUserId() != null) {
					User user = User.findById(request.getAssignToUserId());
					if (user != null && !assignedToUsers.contains(user))
					{
						assignedToUsers.add(user);
					}
				}

				ConfigurableWorkflowAction workflowAction = contentObject.getWorkflowAction();
				if(actionToWorkflow != null) {
					workflowAction = contentObject.getActionByWorkflow(actionToWorkflow);
				}

				if(workflowAction != null) {
					List<ConfigurableWorkflowApprovalDetail> wfDetails = workflowAction.getApprovalDetailsSorted();
					for (ConfigurableWorkflowApprovalDetail wfDetail : wfDetails) {
						if (wfDetail.getUser().getId() == request.getAssignToUserId()) {
							wfDetail.setApproved(ConfigurableWorkflowApprovalDetail.REASSIGNED);
						} else {
							wfDetail.setApproved(ConfigurableWorkflowApprovalDetail.WAITING);
						}
						wfDetail.save();
					}

					//create the configurable workflow action history for reassign
					if (!assignToSelf) {
						ConfigurableWorkflowActionHistory.generateAction(workflowAction, contentObject, ConfigurableWorkflowActionType.ID_REASSIGNED, ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL, ConfigurableWorkflowActionStateType.ID_INTERMEDIATE_APPROVAL, ApplicationUtil.getMessage("page.label.translation.reassign"), UserUtil.getPrincipalUser(), assignedToUsers, DateUtil.now(), request.getNote(), ConfigurableWorkflowActionHistory.ACTION_REGULAR);

						// Workflow Changes (Reassigned)
						AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_TRANSLATION_REASSIGNED, AuditMetadataBuilder.forReassign(reassignFromUserName, assignedToUsers.iterator().next().getFullName()));

						// Task Reassign if linked to a task
						Task linkedTask = contentObject.getTask(actionToWorkflow);
						if (linkedTask != null) {
							TaskUtil.reassignTask(linkedTask, assignedToUsers, User.findById(request.getRequestorId()));
						}
					}
				}
			}
		} catch (Exception e) {
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			log.error("Error:", e);
			throw new RuntimeException(e);
		}
	}
	
	public void validate(ServiceExecutionContext context) {
	}
	
	public boolean hasReassignPermission(Object modelInstance, Long userId){
		boolean result = false;
		if(userId != null){
			if ((Object) modelInstance instanceof ContentObject) {
				//check user permission
				if (((ContentObject)modelInstance).getLockedFor() != null && ((ContentObject)modelInstance).getLockedFor().longValue() == userId) {
					return true;
				} else {
					return (UserUtil.isAllowed(userId, Permission.ID_ROLE_MESSAGE_REASSIGNMENT));
				}
			}
		}
		return result;
	}

	public static ServiceExecutionContext createReassignContext(List<ContentObject> contentObjectList, Long requestorId, Long assignToUserId, String note) {
		return createReassignContext(contentObjectList, requestorId, assignToUserId, null, note);
	}

	public static ServiceExecutionContext createReassignContext(List<ContentObject> contentObjectList, Long requestorId, Long assignToUserId, ConfigurableWorkflow actionToWorkflow, String note) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		UnlockContentObjectServiceRequest request = new UnlockContentObjectServiceRequest();
		request.setContentObjectList(contentObjectList);
		request.setRequestorId(requestorId);
		request.setWorkflow(actionToWorkflow);
		
		if (assignToUserId != null)
			request.setAssignToUserId(assignToUserId);
		else
			request.setAssignToUserId(requestorId);
		
		request.setNote(note);
		context.setRequest(request);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}
}
