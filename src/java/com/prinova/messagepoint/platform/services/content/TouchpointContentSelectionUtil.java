package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.security.User;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * TouchpointContentSelectionUtil
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class TouchpointContentSelectionUtil {
	
	public static ContentObjectMultipartContentVO fillMPContentVO(TouchpointContentObjectContentSelection contentSelection, User requestor) {
		ContentObjectMultipartContentVO mpContentVO = null;
		if (contentSelection.getContentObject().isMultipartType()) {
			mpContentVO = new ContentObjectMultipartContentVO(contentSelection.getContentObject(), contentSelection, contentSelection.getTouchpointSelection().getParameterGroupTreeNode());
		}
		return mpContentVO;
	}

	public static ContentObjectContentSelectionVO fillContentVO(TouchpointContentObjectContentSelection contentSelection, User requestor) {
		ContentObjectContentSelectionVO contentVO = null;
		if (!contentSelection.getContentObject().isMultipartType()) {
			contentVO = ContentObjectContentSelectionVO.mapFromParameterGroupTreeNode(contentSelection.getContentObject(), null, contentSelection.getContentObjectAssociation().getTouchpointPGTreeNode(), requestor.getId());
		}
		return contentVO;
	}
}
