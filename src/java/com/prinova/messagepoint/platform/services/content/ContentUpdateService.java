package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

import java.util.List;

public class ContentUpdateService extends AbstractService {

    public static final String SERVICE_NAME = "content.ContentUpdateService";

    @Override
    public void execute(ServiceExecutionContext context) {
        validate(context);
        if (hasValidationError(context)) {
            return;
        }

        ContentUpdateServiceRequest request = (ContentUpdateServiceRequest) context.getRequest();

        Content content = Content.findById( request.getContentId() );

        String translatedContent = ContentObjectContentUtil.translateContentForPersistance(
               request.getContent(),
                ContentStyleUtils.getStyleNameToIdentifierMap(),
                ContentObjectContentUtil.getObjectIdToDNAMap() );
        content.setEncodedContent( translatedContent );
        content.save();

        List<ContentObjectAssociation> coa = ContentObjectAssociation.findByContent(content.getId());
        for ( int i=0; i < coa.size(); i++ ) {
            if ( coa.get(i).getContentObject().getId() == request.getContentObjectId() )
                coa.get(i).save();
        }

    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(Long contentId,
                                                        String content,
                                                        Long contentObjectId) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ContentUpdateServiceRequest request = new ContentUpdateServiceRequest();
        request.setContentId(contentId);
        request.setContent(content);
        request.setContentObjectId(contentObjectId);
        context.setRequest(request);

        SimpleServiceResponse response = new SimpleServiceResponse();
        response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(response);

        return context;
    }
}
