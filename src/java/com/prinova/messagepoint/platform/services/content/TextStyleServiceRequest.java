package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.font.TextStyleJsonVO;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.List;
import java.util.Set;

public class TextStyleServiceRequest implements ServiceRequest {

    private static final long serialVersionUID = 1L;

    private List<TextStyleJsonVO> updateTextStyles;

    private Set<Long> deleteIds;


    public List<TextStyleJsonVO> getUpdateTextStyles() {
        return updateTextStyles;
    }

    public void setUpdateTextStyles(List<TextStyleJsonVO> updateTextStyles) {
        this.updateTextStyles = updateTextStyles;
    }

    public Set<Long> getDeleteIds() {
        return deleteIds;
    }

    public void setDeleteIds(Set<Long> deleteIds) {
        this.deleteIds = deleteIds;
    }
}
