package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ContentObjectAssociationPostSaveServiceRequest implements ServiceRequest{

    private static final long serialVersionUID = 5310488485985560788L;

    private ContentObjectAssociation coa;
    private boolean needCreateHistory;
    private boolean isNewContentObjectAssociation;

    public ContentObjectAssociation getCoa() {
        return coa;
    }
    public void setCoa(ContentObjectAssociation coa) {
        this.coa = coa;
    }

    public boolean isNeedCreateHistory() {
        return needCreateHistory;
    }
    public void setNeedCreateHistory(boolean needCreateHistory) {
        this.needCreateHistory = needCreateHistory;
    }

    public boolean isNew() {
        return this.isNewContentObjectAssociation;
    }
    public void setNew(boolean isNewContentObjectAssociation) {
        this.isNewContentObjectAssociation = isNewContentObjectAssociation;
    }
}
