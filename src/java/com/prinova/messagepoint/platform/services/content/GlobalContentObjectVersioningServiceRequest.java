package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.Map;
import java.util.Set;

public class GlobalContentObjectVersioningServiceRequest implements ServiceRequest{
	/**
     * 
     */
    private static final long serialVersionUID = -5053524665419221926L;
    private int     action;
	private Long    originDocumentId;
	private Long    clonedDocumentId;
	private User    requestor;
	private String  sourceSchema;
	
	private boolean syncUpdateDocument = true;
	private boolean forceSync = false;
	private boolean logDifferences = false;
	private boolean hideUntilNextChange = false;
    private Set<Long> languagesForSync;
	private Map<Long, Long> globalContentObjectMap;
	private Set<Long> needAssociates;

	private StatusPollingBackgroundTask statusPollingBackgroundTask;

	public int getAction() {
		return action;
	}
	
	public void setAction(int action) {
		this.action = action;
	}

	public Long getOriginDocumentId() {
		return originDocumentId;
	}
	
	public void setOriginDocumentId(Long originDocumentId) {
		this.originDocumentId = originDocumentId;
	}

	public Long getClonedDocumentId() {
		return clonedDocumentId;
	}
	
	public void setClonedDocumentId(Long clonedDocumentId) {
		this.clonedDocumentId = clonedDocumentId;
	}

	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	
	public String getSourceSchema() {
		return sourceSchema;
	}
	public void setSourceSchema(String sourceSchema) {
		this.sourceSchema = sourceSchema;
	}
	
	public boolean isSyncUpdateDocument() {
		return syncUpdateDocument;
	}

	public void setSyncUpdateDocument(boolean syncUpdateDocument) {
		this.syncUpdateDocument = syncUpdateDocument;
	}
	
	public StatusPollingBackgroundTask getStatusPollingBackgroundTask() {
		return statusPollingBackgroundTask;
	}
	
	public void setStatusPollingBackgroundTask(StatusPollingBackgroundTask statusPollingBackgroundTask) {
		this.statusPollingBackgroundTask = statusPollingBackgroundTask;
	}

    public Map<Long, Long> getGlobalContentObjectMap() {
        return globalContentObjectMap;
    }

    public void setGlobalContentObjectMap(Map<Long, Long> globalContentObjectMap) {
        this.globalContentObjectMap = globalContentObjectMap;
    }

    public Set<Long> getNeedAssociates() {
	    return this.needAssociates;
    }

    public void setNeedAssociates(Set<Long> needAssociates) {
	    this.needAssociates = needAssociates;
    }

	public boolean isForceSync() {
		return forceSync;
	}

	public void setForceSync(boolean forceSync) {
		this.forceSync = forceSync;
	}

	public boolean isLogDifferences() {
		return logDifferences;
	}

	public void setLogDifferences(boolean logDifferences) {
		this.logDifferences = logDifferences;
	}

	public boolean isHideUntilNextChange() {
	    return hideUntilNextChange;
    }

    public void setHideUntilNextChange(boolean hideUntilNextChange) {
	    this.hideUntilNextChange = hideUntilNextChange;
    }

    public Set<Long> getLanguagesForSync() {
	    return languagesForSync;
    }

    public void setLanguagesForSync(Set<Long> languagesForSync) {
	    this.languagesForSync = languagesForSync;
    }
}
