package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ContentUpdateServiceRequest implements ServiceRequest{

    private Long contentId;
    private String content;
    private Long contentObjectId;

    public Long getContentId() {
        return contentId;
    }
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public String getContent() {
        return content;
    }
    public void setContent(String content) {
        this.content = content;
    }

    public Long getContentObjectId() {
        return contentObjectId;
    }
    public void setContentObjectId(Long contentObjectId) {
        this.contentObjectId = contentObjectId;
    }
}
