package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.io.Serial;

public class ContentObjectVariantCloneCOWEditServiceRequest extends SimpleServiceRequest {

	@Serial
	private static final long serialVersionUID = -7680476715585320708L;

	private ContentObject contentObject;
	private ParameterGroupTreeNode parameterGroupTreeNode;

	public ParameterGroupTreeNode getParameterGroupTreeNode() {
		return parameterGroupTreeNode;
	}
	public void setParameterGroupTreeNode(ParameterGroupTreeNode parameterGroupTreeNode) {
		this.parameterGroupTreeNode = parameterGroupTreeNode;
	}
	public ContentObject getContentObject() {
		return contentObject;
	}
	public void setContentObject(ContentObject contentObject) {
		this.contentObject = contentObject;
	}
}
