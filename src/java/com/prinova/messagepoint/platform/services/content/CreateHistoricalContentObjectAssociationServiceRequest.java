package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class CreateHistoricalContentObjectAssociationServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -8040030864732734995L;
	
	private ContentObjectAssociation contentObjectAssociation;
	private int contentActionTypeId;

	public ContentObjectAssociation getContentObjectAssociation() {
		return contentObjectAssociation;
	}

	public void setContentObjectAssociation(ContentObjectAssociation contentObjectAssociation) {
		this.contentObjectAssociation = contentObjectAssociation;
	}

	public int getContentActionTypeId() {
		return contentActionTypeId;
	}

	public void setContentActionTypeId(int contentActionTypeId) {
		this.contentActionTypeId = contentActionTypeId;
	}

}
