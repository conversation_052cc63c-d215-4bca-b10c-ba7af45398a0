package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ContentObjectBulkImageActionServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 1937771378179610623L;
	private List<ContentObject> contentObjects;
	private User requestor;
	private User assignToUser;
	private String userNote;
	private List<TouchpointSelection> variants;

	public List<ContentObject> getContentObjects() {
		return contentObjects;
	}

	public void setContentObjects(List<ContentObject> contentObjects) {
		this.contentObjects = contentObjects;
	}

	public User getRequestor() {
		return requestor;
	}

	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}

	public User getAssignToUser() {
		return assignToUser;
	}

	public void setAssignToUser(User assignToUser) {
		this.assignToUser = assignToUser;
	}

	public String getUserNote() {
		return userNote;
	}

	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public List<TouchpointSelection> getVariants() {
		return variants;
	}

	public void setVariants(List<TouchpointSelection> variants) {
		this.variants = variants;
	}

}
