package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class DeleteTextStyleFontService extends AbstractService {

	public static final String SERVICE_NAME = "content.DeleteTextStyleFontService";
	
	private static final Log log = LogUtil.getLog(DeleteTextStyleFontService.class);
	
	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			DeleteTextStyleFontServiceRequest request = (DeleteTextStyleFontServiceRequest)context.getRequest();
			if ( request != null) {
				TextStyleFont font = TextStyleFont.findById(request.getTextStyleFontId());
				if ( font.getTtfFile() != null )
					font.getTtfFile().delete();
				if ( font.getEotFile() != null )
					font.getEotFile().delete();
				if ( font.getTtfBoldFile() != null )
					font.getTtfBoldFile().delete();
				if ( font.getTtfItalicFile() != null )
					font.getTtfItalicFile().delete();
				if ( font.getTtfBoldItalicFile() != null )
					font.getTtfBoldItalicFile().delete();
				
				font.delete();
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking DeleteTextStyleFontService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}		
	}

	@Override
	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(long textStyleFontId) {
		ServiceExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		DeleteTextStyleFontServiceRequest request = new DeleteTextStyleFontServiceRequest();
		
		request.setTextStyleFontId(textStyleFontId);
		
		context.setRequest(request);	
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);
		
		return context;
	}	
}
