package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CloneParagraphStyleServiceRequest implements ServiceRequest{
	private static final long serialVersionUID = 3651778518824240054L;
	
	private ParagraphStyle 	paragraphStyle;
	private String			cloneName;

	public ParagraphStyle getParagraphStyle() {
		return paragraphStyle;
	}

	public void setParagraphStyle(ParagraphStyle paragraphStyle) {
		this.paragraphStyle = paragraphStyle;
	}

	public String getCloneName() {
		return cloneName;
	}

	public void setCloneName(String cloneName) {
		this.cloneName = cloneName;
	}
}
