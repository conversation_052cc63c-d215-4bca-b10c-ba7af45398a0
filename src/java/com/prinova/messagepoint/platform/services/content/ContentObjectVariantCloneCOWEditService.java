package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.ContentObjectAssociationIdComparator;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.List;

/**
 *
 * Messagepoint Inc. 1998-2025
 * All rights reserved.
 * ContentObjectVariantCloneCOWEditService
 *
 * @since 3.5
 * <AUTHOR> Team
 */
public class ContentObjectVariantCloneCOWEditService extends AbstractService {

	private static final Log log = LogUtil.getLog(ContentObjectVariantCloneCOWEditService.class);
	public static final String SERVICE_NAME = "content.ContentObjectVariantCloneCOWEditService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			ContentObjectVariantCloneCOWEditServiceRequest request = (ContentObjectVariantCloneCOWEditServiceRequest) context.getRequest();
			if (request != null) {
				ParameterGroupTreeNode parameterGroupTreeNode = request.getParameterGroupTreeNode();
				ContentObject contentObject = request.getContentObject();
				if (parameterGroupTreeNode != null && contentObject != null) {
					if (contentObject.isDynamicVariantEnabled())
						cloneDynamicVariantContentObjectAssociations(contentObject, parameterGroupTreeNode);
					else if (contentObject.isStructuredContentEnabled())
						cloneTouchpointContentObjectAssociations(contentObject, parameterGroupTreeNode);
				}
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occurred in ContentObjectVariantCloneCOWEditService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					context.getServiceName() + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void cloneDynamicVariantContentObjectAssociations(ContentObject contentObject, ParameterGroupTreeNode variantPgtn) {

		// The same/similar code is also in ImportJSONtoContentService
		// If any modification here, please check also there

		ParameterGroupTreeNode pgtnActiveVersion = ParameterGroupTreeNode.findActiveDynamicVariant(variantPgtn);
		List<ContentObjectAssociation> contentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_ACTIVE, pgtnActiveVersion);
		for (ContentObjectAssociation sourceContentObjectAssociation : contentObjectAssociations)
		{
			// This is clone of dynamic variant only (there is no need to clone master)
			ParameterGroupTreeNode sourceContentObjectPgtn = sourceContentObjectAssociation.getContentObjectPGTreeNode();
			ParameterGroupTreeNode sourceRefContentObjectPgtn = sourceContentObjectAssociation.getReferencingContentObjectPGTreeNode();

			CloneHelper.setSourceDocument(contentObject.getDocument());
			CloneHelper.setTargetDocument(contentObject.getDocument());
			ContentObjectAssociation clonedContentObjectAssociation = CloneHelper.clone(sourceContentObjectAssociation, o -> o.clone(contentObject));
			clonedContentObjectAssociation.setContentObject(contentObject);
			clonedContentObjectAssociation.setDataType(ContentObject.DATA_TYPE_WORKING);

			ParameterGroupTreeNode clonedCoPgTn = sourceContentObjectPgtn == null ? null : ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(sourceContentObjectPgtn, ContentObject.DATA_TYPE_WORKING);
			ParameterGroupTreeNode clonedRefCoPgTn = sourceRefContentObjectPgtn == null ? null : ParameterGroupTreeNode.findSpecificVersionOfDynamicVariant(sourceRefContentObjectPgtn, ContentObject.DATA_TYPE_WORKING);

			clonedContentObjectAssociation.setContentObjectPGTreeNode(clonedCoPgTn);
			clonedContentObjectAssociation.setReferencingContentObjectPGTreeNode(clonedRefCoPgTn);
			boolean needCreateHistory = clonedContentObjectAssociation.getNeedCreateHistory();
			clonedContentObjectAssociation.setNeedCreateHistory(false);
			clonedContentObjectAssociation.save();
			clonedContentObjectAssociation.setNeedCreateHistory(needCreateHistory);
			contentObject.getContentObjectAssociations().add(clonedContentObjectAssociation);
		}
	}

	private void cloneTouchpointContentObjectAssociations(ContentObject contentObject, ParameterGroupTreeNode variantPgtn) {

		// The same/similar code is also in ImportJSONtoContentService
		// If any modification here, please check also there

		List<ContentObjectAssociation> coasActive = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_ACTIVE, variantPgtn, null, null, false, false, true, true, false);
		if (coasActive.isEmpty() && contentObject.getDocument() != null && !contentObject.getDocument().isEnabledForVariantWorkflow())
		{	// If no active content object association found, try to find archived one. This is needed for the case when the touchpoint is not enabled for variant workflow
			coasActive = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_ARCHIVED, variantPgtn, null, null, false, false, true, true, false);
		}
		if (!coasActive.isEmpty()) {
			coasActive.sort(new ContentObjectAssociationIdComparator());

			for (ContentObjectAssociation sourceContentObjectAssociation : coasActive) {
				// This is clone of structured TP variant
				CloneHelper.setSourceDocument(contentObject.getDocument());
				CloneHelper.setTargetDocument(contentObject.getDocument());
				ContentObjectAssociation clonedContentObjectAssociation = CloneHelper.clone(sourceContentObjectAssociation, o -> o.clone(contentObject));
				clonedContentObjectAssociation.setContentObject(contentObject);
				clonedContentObjectAssociation.setDataType(ContentObject.DATA_TYPE_WORKING);

				CloneHelper.execInSaveSession(() -> {
					clonedContentObjectAssociation.save();
					contentObject.getContentObjectAssociations().add(clonedContentObjectAssociation);
				});
			}
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(ContentObject contentObject, ParameterGroupTreeNode parameterGroupTreeNode) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ContentObjectVariantCloneCOWEditServiceRequest request = new ContentObjectVariantCloneCOWEditServiceRequest();
		context.setRequest(request);

		request.setContentObject(contentObject);
		request.setParameterGroupTreeNode(parameterGroupTreeNode);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}