package com.prinova.messagepoint.platform.services.content;

import java.util.HashSet;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class CloneListStyleService extends AbstractService{

	private static final Log log = LogUtil.getLog(CloneListStyleService.class);
	public static final String SERVICE_NAME = "content.CloneListStyleService";

	public void execute(ServiceExecutionContext context) {		
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			CloneListStyleServiceRequest request = (CloneListStyleServiceRequest)context.getRequest();
			ListStyle oriListStyle = request.getListStyle();
			ListStyle newListStyle = new ListStyle();
			newListStyle.setName(request.getCloneName());
			newListStyle.setConnectorName(oriListStyle.getConnectorName());

			newListStyle.setListSpacingBefore(oriListStyle.getListSpacingBefore());
			newListStyle.setListSpacingAfter(oriListStyle.getListSpacingAfter());
			newListStyle.setListSpacingRight(oriListStyle.getListSpacingRight());
			newListStyle.setBulletLeftMargin(oriListStyle.getBulletLeftMargin());
			newListStyle.setBulletRightMargin(oriListStyle.getBulletRightMargin());
			newListStyle.setBulletTopMargin(oriListStyle.getBulletTopMargin());
			newListStyle.setBulletBottomMargin(oriListStyle.getBulletBottomMargin());
			newListStyle.setBulletSpacingData(oriListStyle.getBulletSpacingData());
			newListStyle.setBulletSymbolOverrides(oriListStyle.getBulletSymbolOverrides());
			newListStyle.setTextStyle(oriListStyle.getTextStyle());
			newListStyle.setIndent(oriListStyle.getIndent());
			newListStyle.setLineSpacingType(oriListStyle.getLineSpacingType());
			newListStyle.setLineSpacing(oriListStyle.getLineSpacing());
			newListStyle.setTaggingOverride(oriListStyle.getTaggingOverride());

            Set<Zone> zones = new HashSet<>(oriListStyle.getZones());
			newListStyle.setZones(zones);
			newListStyle.save(true);
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CloneListStyleService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public static ServiceExecutionContext createContext(ListStyle listStyleStyle, String cloneName) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CloneListStyleServiceRequest request = new CloneListStyleServiceRequest();
		context.setRequest(request);

		request.setListStyle(listStyleStyle);
		request.setCloneName(cloneName);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}

}