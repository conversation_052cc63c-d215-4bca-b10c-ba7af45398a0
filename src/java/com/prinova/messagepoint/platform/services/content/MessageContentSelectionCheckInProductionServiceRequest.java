package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * MessageContentSelectionCheckInProductionServiceRequest
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class MessageContentSelectionCheckInProductionServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 886760391035873874L;
	private long contentVariantId;  // id of the contentInstance or the messagepart that will be checked in production
	// TODO use PGTreeNode instead of SelectionContent private SelectionContent selectionContent;
	private Long userId;
	
	public long getContentInstanceId() {
		return contentVariantId;
	}

	public void setContentInstanceId(long contentInstanceId) {
		this.contentVariantId = contentInstanceId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

//	public void setSelectionContent(SelectionContent selectionContent) {
//		this.selectionContent = selectionContent;
//	}
//
//	public SelectionContent getSelectionContent() {
//		if(selectionContent == null)
//			return HibernateUtil.getManager().getObject(SelectionContent.class, contentVariantId);
//		return selectionContent;
//	}
}
