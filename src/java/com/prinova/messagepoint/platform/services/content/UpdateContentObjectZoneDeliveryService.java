package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditWrapper;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.ArrayList;
import java.util.List;

public class UpdateContentObjectZoneDeliveryService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectZoneDeliveryService";
	private static final Log log = LogUtil.getLog(UpdateContentObjectZoneDeliveryService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateContentObjectZoneDeliveryServiceRequest request = (UpdateContentObjectZoneDeliveryServiceRequest) context.getRequest();
			ContentObjectZonePriorityEditWrapper wrapper = request.getContentObjectZonePriorityEditWrapper();
			
			List<String> targetContentObjectDataGuids = new ArrayList<>();
			
			wrapper.getMandatoryMessages().remove("0");
			wrapper.getOptionalMessages().remove("0");
			wrapper.getSuppressedMessages().remove("0");
			
			targetContentObjectDataGuids.addAll(wrapper.getMandatoryMessages());
			targetContentObjectDataGuids.addAll(wrapper.getOptionalMessages());

			// Audit
			AuditEventUtil.auditMessagePriorityChanged(wrapper.getMandatoryMessages(), wrapper.getOptionalMessages(), wrapper.getSuppressedMessages(), wrapper.getRepeatWithNextMessages(), wrapper.getZone(), wrapper.getTpSelection());
			
			for (String currentContentObjectDataGuid : targetContentObjectDataGuids ) {

				ContentObjectData contentObjectData = ContentObjectData.findByGuid(currentContentObjectDataGuid);
				
				if ( contentObjectData.getDeliveryType() == ContentObject.DELIVERY_TYPE_MANDATORY && wrapper.getOptionalMessages().contains(contentObjectData.getGuid()) ||
						contentObjectData.getDeliveryType() == ContentObject.DELIVERY_TYPE_OPTIONAL && wrapper.getMandatoryMessages().contains(contentObjectData.getGuid()) ) {

					ContentObject contentObject = contentObjectData.getContentObject();
					if (contentObject.getZone() != null) {

						if (contentObject.isMessage())
							contentObjectData.setDeliveryType(wrapper.getMandatoryMessages().contains(contentObjectData.getGuid()) ? ContentObject.DELIVERY_TYPE_MANDATORY : ContentObject.DELIVERY_TYPE_OPTIONAL);
						else
							contentObjectData.setDeliveryType(ContentObject.DELIVERY_TYPE_MANDATORY);

						contentObjectData.save();

						ContentObjectZonePriority.fixAssociationDueToContentObjectZoneDeliveryChanges(contentObject, contentObject.getZone(), contentObject.getZone(), true, false);
						//contentObject.save();

					}
				}
			}		

		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectZoneDeliveryService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(ContentObjectZonePriorityEditWrapper ContentObjectZonePriorityEditWrapper) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateContentObjectZoneDeliveryServiceRequest request = new UpdateContentObjectZoneDeliveryServiceRequest();
		context.setRequest(request);

		request.setContentObjectZonePriorityEditWrapper(ContentObjectZonePriorityEditWrapper);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
