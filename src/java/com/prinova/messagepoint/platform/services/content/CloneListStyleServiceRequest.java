package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CloneListStyleServiceRequest implements ServiceRequest{
	private static final long serialVersionUID = 3651778518824240054L;
	
	private ListStyle 	paragraphStyle;
	private String			cloneName;

	public ListStyle getListStyle() {
		return paragraphStyle;
	}

	public void setListStyle(ListStyle paragraphStyle) {
		this.paragraphStyle = paragraphStyle;
	}

	public String getCloneName() {
		return cloneName;
	}

	public void setCloneName(String cloneName) {
		this.cloneName = cloneName;
	}
}
