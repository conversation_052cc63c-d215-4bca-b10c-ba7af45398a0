package com.prinova.messagepoint.platform.services.content;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class UpdateContentObjectTouchpointAssignmentServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 8875263533491339453L;

	private Set<Long>				selectedContentObjectIds = new HashSet<>();
	private List<Document>			touchpointAssignments = new ArrayList<>();
	private List<TouchpointCollection>	tpCollectionAssignments = new ArrayList<>();
	
	public Set<Long> getSelectedContentObjectIds() {
		return selectedContentObjectIds;
	}
	public void setSelectedContentObjectIds(Set<Long> selectedContentObjectIds) {
		this.selectedContentObjectIds = selectedContentObjectIds;
	}
	
	public List<Document> getTouchpointAssignments() {
		return touchpointAssignments;
	}
	public void setTouchpointAssignments(List<Document> touchpointAssignments) {
		this.touchpointAssignments = touchpointAssignments;
	}		

	public List<TouchpointCollection> getTpCollectionAssignments() {
		return tpCollectionAssignments;
	}
	public void setTpCollectionAssignments(
			List<TouchpointCollection> tpCollectionAssignments) {
		this.tpCollectionAssignments = tpCollectionAssignments;
	}	
}
