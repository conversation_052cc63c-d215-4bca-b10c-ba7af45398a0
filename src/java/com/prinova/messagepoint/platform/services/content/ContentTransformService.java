package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentTransformWrapper;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ContentTransformBackgroundTask;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;

public class ContentTransformService extends AbstractService {

    public static final String SERVICE_NAME = "content.ContentTransformService";

    @Override
    public void execute(ServiceExecutionContext context) {
        validate(context);
        if (hasValidationError(context)) {
            return;
        }

        ContentTransformServiceRequest request = (ContentTransformServiceRequest) context.getRequest();

        ContentTransformBackgroundTask task = new ContentTransformBackgroundTask(request.getWrapper(), UserUtil.getPrincipalUser());
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(ContentTransformWrapper wrapper) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ContentTransformServiceRequest request = new ContentTransformServiceRequest();
        request.setWrapper(wrapper);
        context.setRequest(request);

        SimpleServiceResponse response = new SimpleServiceResponse();
        response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(response);

        return context;
    }
}
