package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ListStyleCustomization;
import com.prinova.messagepoint.model.font.ParagraphAlignment;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class CreateOrUpdateListStyleService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateOrUpdateListStyleService.class);
	public static final String SERVICE_NAME = "content.CreateOrUpdateListStyleService";

	private static final int ACTION_CREATE_OR_UPDATE	= 1;
	private static final int ACTION_REFRESH_STYLE_DATA 	= 2;

	@Autowired
	private CacheDataRepository cacheDataRepository;

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateListStyleServiceRequest request = (CreateOrUpdateListStyleServiceRequest) context.getRequest();

			if ( request.getAction() == ACTION_REFRESH_STYLE_DATA ) {

				if (cacheDataRepository != null) {
					cacheDataRepository.updateStylesData();
				} else {
					log.error("cacheDataRepository is null");
				}
				return;

			} else if(request.getAction() == ACTION_CREATE_OR_UPDATE) {

				boolean isCust = request.isCust();
				if (isCust) {
					ListStyle listStyle = ListStyle.findById(request.getId());
					Document document = Document.findById(request.getDocumentId());
					ListStyleCustomization listStyleCust = listStyle.getListStyleCustomizations().get(document);
					boolean isNew = false;
					if (listStyleCust == null) {    // New
						listStyleCust = new ListStyleCustomization();
						isNew = true;
					}
					listStyleCust.setConnectorName(request.getListStyle().getConnectorName());

					listStyleCust.setToggleAlignment(request.getToggleAlignment());
					listStyleCust.setAlignmentId(request.getAlignmentId());
					listStyleCust.setListSpacingBefore(DecimalValueUtil.hydrate(request.getListSpacingBefore()));
					listStyleCust.setListSpacingAfter(DecimalValueUtil.hydrate(request.getListSpacingAfter()));
					listStyleCust.setListSpacingRight(DecimalValueUtil.hydrate(request.getListSpacingRight()));

					listStyleCust.setLineSpacing(request.getLineSpacing());
					listStyleCust.setLineSpacingType(request.getLineSpacingType());

					listStyleCust.setTextStyle(request.getTextStyle());
					listStyleCust.setTaggingOverride(request.getTaggingOverride());

					JSONArray spacingArray = new JSONArray();
					for (int i = 0; i < request.getBulletTopMargin().size(); i++) {
						JSONObject spacingObj = new JSONObject();
						spacingObj.put("t", Float.valueOf(request.getBulletTopMargin().get(i)));
						spacingObj.put("r", Float.valueOf(request.getBulletRightMargin().get(i)));
						spacingObj.put("b", Float.valueOf(request.getBulletBottomMargin().get(i)));
						spacingObj.put("l", Float.valueOf(request.getBulletLeftMargin().get(i)));
						spacingArray.put(spacingObj);
					}
					listStyleCust.setBulletSpacingData(spacingArray.toString());

					listStyleCust.setIndent(DecimalValueUtil.hydrate(request.getIndent()));
					if (request.isHangingIndent()) {
						listStyleCust.setIndent(listStyleCust.getIndent() * -1);
					}

					listStyleCust.setBulletSymbolOverrides(request.getBulletSymbolOverrides());

					if (isNew) {
						listStyleCust.setMasterListStyle(listStyle);
						listStyleCust.save(true);
						listStyle.getListStyleCustomizations().put(document, listStyleCust);
						listStyle.save();
					} else {
						listStyleCust.save();
					}

					getResponse(context).setResultValueBean(listStyle.getId());

				} else {
					boolean isNew = (request.getId() <= 0);

					ListStyle listStyle = null;
					if (isNew) {
						listStyle = new ListStyle();
					} else {
						listStyle = ListStyle.findById(request.getId());
					}

					listStyle.setName(request.getListStyle().getName());
					listStyle.setConnectorName(request.getListStyle().getConnectorName());

					listStyle.setToggleAlignment(request.getToggleAlignment());
					listStyle.setAlignmentId(request.getAlignmentId());
					listStyle.setListSpacingBefore(DecimalValueUtil.hydrate(request.getListSpacingBefore()));
					listStyle.setListSpacingAfter(DecimalValueUtil.hydrate(request.getListSpacingAfter()));
					listStyle.setListSpacingRight(DecimalValueUtil.hydrate(request.getListSpacingRight()));

					listStyle.setLineSpacing(request.getLineSpacing());
					listStyle.setLineSpacingType(request.getLineSpacingType());

					listStyle.setTextStyle(request.getTextStyle());
					listStyle.setTaggingOverride(request.getTaggingOverride());

					JSONArray spacingArray = new JSONArray();
					for (int i = 0; i < request.getBulletTopMargin().size(); i++) {
						JSONObject spacingObj = new JSONObject();
						spacingObj.put("t", Float.valueOf(request.getBulletTopMargin().get(i)));
						spacingObj.put("r", Float.valueOf(request.getBulletRightMargin().get(i)));
						spacingObj.put("b", Float.valueOf(request.getBulletBottomMargin().get(i)));
						spacingObj.put("l", Float.valueOf(request.getBulletLeftMargin().get(i)));
						spacingArray.put(spacingObj);
					}
					listStyle.setBulletSpacingData(spacingArray.toString());

					listStyle.setIndent(DecimalValueUtil.hydrate(request.getIndent()));
					if (request.isHangingIndent()) {
						listStyle.setIndent(listStyle.getIndent() * -1);
					}

					listStyle.setBulletSymbolOverrides(request.getBulletSymbolOverrides());

					listStyle.save();

					if (cacheDataRepository != null) {
						cacheDataRepository.updateStylesData();
					} else {
						log.error("cacheDataRepository is null");
					}

					getResponse(context).setResultValueBean(listStyle.getId());

					if (isNew) {
						// Audit (Audit List Style creation)
						AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_LIST_STYLE, request.getListStyle().getName(), listStyle.getId(), AuditActionType.ID_CHANGE_CREATED, null);
					}
				}
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CreateOrUpdateListStyleService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	public static ServiceExecutionContext createContextForStyleDataUpdate() {
		return createContext(ACTION_REFRESH_STYLE_DATA, false, 0, null, false, ParagraphAlignment.ID_LEFT,  null, null, null, 0, 0, null, null, null, null, null, null, null, null, false, 0);
	}

	public static ServiceExecutionContext createContextForCreateOrUpdate(boolean isCust,
																		 long id,
																		 ListStyle listStyle,
																		 boolean toggleAlignment,
																		 int alignmentId,
																		 String listSpacingBefore,
																		 String listSpacingAfter,
																		 String listSpacingRight,
																		 int lineSpacing,
																		 int lineSpacingType,
																		 TextStyle textStyle,
																		 String taggingOverride,
																		 List<String> bulletLeftMargin,
																		 List<String> bulletRightMargin,
																		 List<String> bulletTopMargin,
																		 List<String> bulletBottomMargin,
																		 String bulletSymbolOverrides,
																		 String indent,
																		 boolean hangingIndent,
																		 long documentId) {
		return createContext(ACTION_CREATE_OR_UPDATE, isCust, id, listStyle, toggleAlignment, alignmentId, listSpacingBefore, listSpacingAfter, listSpacingRight, lineSpacing, lineSpacingType, textStyle, taggingOverride, bulletLeftMargin, bulletRightMargin, bulletTopMargin, bulletBottomMargin, bulletSymbolOverrides, indent, hangingIndent, documentId);
	}

	public static ServiceExecutionContext createContext(int action,
														boolean isCust,
														long id,
														ListStyle listStyle,
														boolean toggleAlignment,
														int alignmentId,
														String listSpacingBefore,
														String listSpacingAfter,
														String listSpacingRight,
														int lineSpacing,
														int lineSpacingType,
														TextStyle textStyle,
														String taggingOverride,
														List<String> bulletLeftMargin,
														List<String> bulletRightMargin,
														List<String> bulletTopMargin,
														List<String> bulletBottomMargin,
														String bulletSymbolOverrides,
														String indent,
														boolean hangingIndent,
														long documentId) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateListStyleServiceRequest request = new CreateOrUpdateListStyleServiceRequest();
		context.setRequest(request);

		request.setCust(isCust);
		request.setId(id);
		request.setAction(action);
		
		request.setListStyle(listStyle);
		request.setToggleAlignment(toggleAlignment);
		request.setAlignmentId(alignmentId);
		request.setListSpacingBefore(listSpacingBefore);
		request.setListSpacingAfter(listSpacingAfter);
		request.setListSpacingRight(listSpacingRight);
		
		request.setLineSpacing(lineSpacing);
		request.setLineSpacingType(lineSpacingType);
		
		request.setTextStyle(textStyle);
		request.setTaggingOverride(taggingOverride);
		
		request.setBulletLeftMargin(bulletLeftMargin);
		request.setBulletRightMargin(bulletRightMargin);
		request.setBulletTopMargin(bulletTopMargin);
		request.setBulletBottomMargin(bulletBottomMargin);
		request.setBulletSymbolOverrides(bulletSymbolOverrides);

		request.setIndent(indent);
		request.setHangingIndent(hangingIndent);

		request.setDocumentId(documentId);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}
}