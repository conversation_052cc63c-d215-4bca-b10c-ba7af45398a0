package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataElementVariableType;
import com.prinova.messagepoint.model.admin.SystemVariableType;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;

public class UpdateSystemVariablesService extends AbstractService {

	private static final Log log = LogUtil.getLog(UpdateSystemVariablesService.class);
	public static final String SERVICE_NAME = "content.UpdateSystemVariablesService";
	
	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			List<DataElementVariable> systemVariables = DataElementVariable.findAllSystemVariables();
			
			boolean bundleDateVarExists 			= false;
			boolean pageNumberExists 				= false;
			boolean pageCountExists 				= false;
			boolean processingDateVarExists 		= false;
			boolean recipientIndexVarExists 		= false;
			boolean recipientPageCountVarExists 	= false;
			boolean totalPageCountVarExists 		= false;
			boolean recipientSheetCountVarExists 	= false;
			boolean totalSheetCountVarExists 		= false;
			boolean recipientCountVarExists 		= false;
			boolean index0VarExists					= false;
			boolean index1VarExists					= false;
			boolean touchpointSelectiolnVarExists	= false;
			
			for ( DataElementVariable currentVariable: systemVariables ) {
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_BUNDLE_DATE )
					bundleDateVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_PAGE_NUMBER )
					pageNumberExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_PAGE_COUNT )
					pageCountExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_PROCESSING_DATE )
					processingDateVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_RECIPIENT_INDEX )
					recipientIndexVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_RECIPIENT_PAGE_COUNT )
					recipientPageCountVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_TOTAL_PAGE_COUNT )
					totalPageCountVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_RECIPIENT_SHEET_COUNT )
					recipientSheetCountVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_TOTAL_SHEET_COUNT )
					totalSheetCountVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_RECIPIENT_COUNT )
					recipientCountVarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_INDEX_0_BASED )
					index0VarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_INDEX_1_BASED )
					index1VarExists = true;
				if ( currentVariable.getSystemVariableTypeId() == SystemVariableType.ID_TOUCHPOINT_SELECTION_PATH)
					touchpointSelectiolnVarExists = true;
			}
			
			if ( !bundleDateVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_BUNDLE_DATE) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_BUNDLE_DATE) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_BUNDLE_DATE );
				
				variable.save();
			}
			if ( !pageNumberExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PAGE_NUMBER) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PAGE_NUMBER) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_PAGE_NUMBER );
				
				variable.save();
			}
			if ( !pageCountExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PAGE_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PAGE_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_PAGE_COUNT );
				
				variable.save();
			}
			if ( !processingDateVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PROCESSING_DATE) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_PROCESSING_DATE) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_PROCESSING_DATE );
				
				variable.save();
			}
			if ( !recipientIndexVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_INDEX) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_INDEX) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_RECIPIENT_INDEX );
				
				variable.save();
			}
			if ( !recipientPageCountVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_PAGE_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_PAGE_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_RECIPIENT_PAGE_COUNT );
				
				variable.save();
			}
			if ( !totalPageCountVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOTAL_PAGE_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOTAL_PAGE_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_TOTAL_PAGE_COUNT );
				
				variable.save();
			}
			if ( !recipientSheetCountVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_SHEET_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_SHEET_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_RECIPIENT_SHEET_COUNT );
				
				variable.save();
			}
			if ( !totalSheetCountVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOTAL_SHEET_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOTAL_SHEET_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_TOTAL_SHEET_COUNT );
				
				variable.save();
			}
			if ( !recipientCountVarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_COUNT) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_RECIPIENT_COUNT) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_RECIPIENT_COUNT );
				
				variable.save();
			}
			if ( !index0VarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_INDEX_0_BASED) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_INDEX_0_BASED) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_INDEX_0_BASED );
				
				variable.save();
			}
			if ( !index1VarExists ) {
				DataElementVariable variable = new DataElementVariable();
				
				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_INDEX_1_BASED) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_INDEX_1_BASED) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_INDEX_1_BASED );
				
				variable.save();
			}
			if ( !touchpointSelectiolnVarExists ) {
				DataElementVariable variable = new DataElementVariable();

				variable.setName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOUCPOINT_SELECTION_PATH) );
				variable.setFriendlyName( ApplicationUtil.getMessage(SystemVariableType.MESSAGE_CODE_TOUCPOINT_SELECTION_PATH) );
				variable.setTypeId( DataElementVariableType.ID_SYSTEM );
				variable.setSystemVariableTypeId( SystemVariableType.ID_TOUCHPOINT_SELECTION_PATH);

				variable.save();
			}
			 
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in UpdateSystemVariablesService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}				

	@Override
	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext() {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	
}
