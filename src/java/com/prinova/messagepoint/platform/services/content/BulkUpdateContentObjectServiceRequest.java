package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class BulkUpdateContentObjectServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -1153556767808127636L;

	private int							action;
	
	private List<ContentObject> 		contentObjects;
	
	private boolean						isSuppressedOnHold;
	private boolean						updateVariantVersioning;
	private List<TouchpointSelection> 	variants;

    private User                        requestor = null;
    private Document                    currentDocument = null;

	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}

	public List<ContentObject> getContentObjects() {
		return contentObjects;
	}
	public void setContentObjects(List<ContentObject> contentObjects) {
		this.contentObjects = contentObjects;
	}
	
	public boolean isSuppressedOnHold() {
		return isSuppressedOnHold;
	}
	public void setSuppressedOnHold(boolean isSuppressedOnHold) {
		this.isSuppressedOnHold = isSuppressedOnHold;
	}
	
	public boolean isUpdateVariantVersioning() {
		return updateVariantVersioning;
	}
	public void setUpdateVariantVersioning(boolean updateVariantVersioning) {
		this.updateVariantVersioning = updateVariantVersioning;
	}

	public List<TouchpointSelection> getVariants() {
		return variants;
	}

	public void setVariants(List<TouchpointSelection> variants) {
		this.variants = variants;
	}

    public void setRequestor(User requestor) {
        this.requestor = requestor;
    }

    public User getRequestor() {
        return this.requestor;
    }

    public void setCurrentDocument(Document currentDocument) {
        this.currentDocument = currentDocument;
    }

    public Document getCurrentDocument() {
        return this.currentDocument;
    }
}
