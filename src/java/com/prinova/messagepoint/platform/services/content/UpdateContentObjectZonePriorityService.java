package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentObjectZonePriorityEditWrapper;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;

public class UpdateContentObjectZonePriorityService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectZonePriorityService";
	private static final Log log = LogUtil.getLog(UpdateContentObjectZonePriorityService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateContentObjectZonePriorityServiceRequest request = (UpdateContentObjectZonePriorityServiceRequest) context.getRequest();
			ContentObjectZonePriorityEditWrapper wrapper = request.getContentObjectZonePriorityEditWrapper();
			
			wrapper.getMandatoryMessages().remove("0");
			wrapper.getOptionalMessages().remove("0");
			wrapper.getSuppressedMessages().remove("0");
			wrapper.getRepeatWithNextMessages().remove("0");
			
			ContentObjectZonePriority.resetMessagePrioritiesInZoneAndTPSelection(wrapper.getZone(), wrapper.getTpSelection(), wrapper.getMandatoryMessages());
			ContentObjectZonePriority.resetMessagePrioritiesInZoneAndTPSelection(wrapper.getZone(), wrapper.getTpSelection(), wrapper.getOptionalMessages());
			ContentObjectZonePriority.setSuppressedMessagePrioritiesInZoneAndTPSelection(wrapper.getZone(), wrapper.getTpSelection(), wrapper.getSuppressedMessages());
			ContentObjectZonePriority.setRepeatWithNextMessagePrioritiesInZoneAndTPSelection(wrapper.getZone(), wrapper.getTpSelection(), wrapper.getRepeatWithNextMessages());
			
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectZonePriorityService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(ContentObjectZonePriorityEditWrapper ContentObjectZonePriorityEditWrapper) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateContentObjectZonePriorityServiceRequest request = new UpdateContentObjectZonePriorityServiceRequest();
		context.setRequest(request);

		request.setContentObjectZonePriorityEditWrapper(ContentObjectZonePriorityEditWrapper);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
