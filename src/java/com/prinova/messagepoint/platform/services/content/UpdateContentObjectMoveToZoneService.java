package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.HibernateUtil;

public class UpdateContentObjectMoveToZoneService extends AbstractService {

	public static final String SERVICE_NAME = "content.UpdateContentObjectMoveToZoneService";
	private static final Log log = LogUtil.getLog(UpdateContentObjectMoveToZoneService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			UpdateContentObjectMoveToZoneServiceRequest request = (UpdateContentObjectMoveToZoneServiceRequest) context.getRequest();

			ContentObject contentObject = request.getContentObject();
			
			// LOCAL CONTENT: Not applicable for delivery/priority
			if ( !contentObject.isMessage() && !contentObject.isDeliveredToPlaceholder() )
				return;
			
			Zone fromZone = request.getFromZone();
			Zone toZone = request.getToZone();

			if(fromZone == null){	// Only one delivery zone
				fromZone = contentObject.getZone();
			}
			
			if (fromZone.getId() != toZone.getId() && fromZone.getContentObjects().contains(contentObject.getId())) {
				fromZone.removeContentObject(contentObject);
			}

			if (!toZone.getContentObjects().contains(contentObject)) {
				toZone.addContentObject(contentObject);
			}

			contentObject.setZone(toZone);

			ContentObjectZonePriority.fixAssociationDueToContentObjectZoneDeliveryChanges(contentObject, fromZone, toZone, false, false);
			
			HibernateUtil.getManager().saveObject( contentObject );
		} catch (Exception e) {
			log.error(" unexpected exception when invoking UpdateContentObjectMoveToZoneService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	
	}

	public static ServiceExecutionContext createContext(ContentObject contentObject, Zone fromZone, Zone toZone) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		UpdateContentObjectMoveToZoneServiceRequest request = new UpdateContentObjectMoveToZoneServiceRequest();
		context.setRequest(request);

		request.setContentObject(contentObject);
		request.setFromZone(fromZone);
		request.setToZone(toZone);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
