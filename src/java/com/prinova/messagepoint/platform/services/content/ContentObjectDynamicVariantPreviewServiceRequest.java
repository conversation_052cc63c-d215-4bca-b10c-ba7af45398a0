package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ContentObjectDynamicVariantPreviewServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = -7665913346833854731L;
	private long contentObjectId;
	private long parameterGroupTreeNodeId;
	private long localeId;
	private long requestorUserid;

	// lazy objects which are set by user or fetched from database from id
	private ContentObject contentobject;
	private User requestor;

	public long getContentObjectId() {
		return contentObjectId;
	}

	public void setContentObjectId(long contentObjectId) {
		this.contentObjectId = contentObjectId;
	}

	public long getLocaleId() {
		return localeId;
	}

	public void setLocaleId(long localeId) {
		this.localeId = localeId;
	}

	public long getRequestorUserid() {
		return requestorUserid;
	}

	public void setRequestorUserid(long requestorUserid) {
		this.requestorUserid = requestorUserid;
	}

	public void setContentobject(ContentObject contentobject) {
		this.contentobject = contentobject;
	}

	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}

	// Lazy getters
	public ContentObject getContentobject() {
		if (contentobject == null) {
			contentobject = ContentObject.findById(getContentObjectId());
		}
		return contentobject;
	}

	public User getRequestor() {
		if (requestor == null) {
			requestor = User.findById(getRequestorUserid());
		}
		return requestor;
	}

	public long getParameterGroupTreeNodeId() {
		return parameterGroupTreeNodeId;
	}

	public void setParameterGroupTreeNodeId(long parameterGroupTreeNodeId) {
		this.parameterGroupTreeNodeId = parameterGroupTreeNodeId;
	}

}
