package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentPowerEditWrapper;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class ContentPowerEditServiceRequest implements ServiceRequest{

    private ContentPowerEditWrapper wrapper;

    public ContentPowerEditWrapper getWrapper() {
        return wrapper;
    }

    public void setWrapper(ContentPowerEditWrapper wrapper) {
        this.wrapper = wrapper;
    }
}
