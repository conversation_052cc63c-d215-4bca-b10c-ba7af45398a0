package com.prinova.messagepoint.platform.services.content;

import java.util.*;

import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.*;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

/**
 * 
 * Messagepoint Inc. 1998-2025
 * All rights reserved.
 * CreateContentObjectDynamicVariantTreeNodeService
 * This service will create the parameter instance / parameter group instance
 * and an empty message content association.
 *
 * @since 3.5
 * <AUTHOR> Team
 */
public class CreateContentObjectDynamicVariantTreeNodeService extends AbstractService {
	private static final Log log = LogUtil.getLog(CreateContentObjectDynamicVariantTreeNodeService.class);
	public static final String SERVICE_NAME = "content.CreateContentObjectDynamicVariantTreeNodeService";

	private static final int NEW_CREATE = 1;
	private static final int SHARE_COLLECTION = 2;
	
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			CreateContentObjectDynamicVariantTreeNodeServiceRequest request = (CreateContentObjectDynamicVariantTreeNodeServiceRequest) context.getRequest();
			if(request != null){
				ContentObject contentObject = ContentObject.findById(request.getContentObjectId());
				contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);

				ParameterGroup paramGroup = contentObject.getParameterGroup();

				ParameterGroupInstanceCollection pgInstanceCollection;
				ParameterGroupTreeNode pgTreeNode = new ParameterGroupTreeNode();
				long parentNodeId = request.getParentTreeNodeId();
				if(parentNodeId>0)
				{
					ParameterGroupTreeNode parentNode = ParameterGroupTreeNode.findById(parentNodeId);
					if(parentNode!=null)
					{
						pgTreeNode.setParentNode(parentNode);
					}
				}
				if(request.getPgtnDna() != null) {
					pgTreeNode.setDna(request.getPgtnDna());
				}
				pgTreeNode.setParameterGroup(paramGroup);

				if(request.getPgCollectionId()>0){
					//use shared collection
					pgInstanceCollection = ParameterGroupInstanceCollection.findById(request.getPgCollectionId());
					if (pgInstanceCollection != null) {
						pgTreeNode.setName(pgInstanceCollection.getName());
					}
				}else{
					//create new collection
					pgTreeNode.setName(request.getNodeName());
					List<String> itemValues =new ArrayList<>(Arrays.asList(request.getStartingValues()));

					pgInstanceCollection = new ParameterGroupInstanceCollection();
					pgInstanceCollection.setName(request.getNodeName());
					pgInstanceCollection.setShared(request.isShared());
					if (request.getPgCollectionGuid() != null)
						pgInstanceCollection.setGuid(request.getPgCollectionGuid());

					Set<ParameterGroupInstance> parameterGroupInstances = new HashSet<>();

					ParameterGroupInstance.removeEmptyValuesFromEnd(itemValues);

					List<List<String>> flatValues = ParameterGroupInstance.flattenValue(itemValues);

					for (List<String> flatValue : flatValues) {
						if (!flatValue.isEmpty())
						{
							ParameterGroupInstance parameterGroupInstance = ParameterGroupInstance.createParameterGroupInstance(paramGroup.getId(), flatValue);
							parameterGroupInstance.setParameterGroupInstanceCollection(pgInstanceCollection);
							parameterGroupInstances.add(parameterGroupInstance);
						}
					}

					pgInstanceCollection.replaceParameterGroupInstances(parameterGroupInstances);

					pgInstanceCollection.save();
				}

				if(pgInstanceCollection!=null){
					pgTreeNode.setParameterGroupInstanceCollection(pgInstanceCollection);
					if(request.getNodeName()==null || request.getNodeName().trim().isEmpty()){
						pgTreeNode.setName(pgInstanceCollection.getName());
					}
				}

				pgTreeNode.save(true);

				pgTreeNode.setContentObject(contentObject);
				pgTreeNode.setDataType(ContentObject.DATA_TYPE_WORKING);
				pgTreeNode.save();

				List<MessagepointLocale> languages = contentObject.getContentObjectLanguagesAsLocales();

				//create new content_association for available languages
				if(contentObject.isMultipartType()) {
					Zone zone = contentObject.getZone();
					List<ZonePart> parts = zone.getPartsInOrder();
					for (ZonePart zonePart : parts) {
						createAssociationsForDynamicVariant(contentObject, pgTreeNode, zonePart, request.getUserId(), languages);
					}
				} else {
					createAssociationsForDynamicVariant(contentObject, pgTreeNode, null, request.getUserId(), languages);
				}
                contentObject.save();
				context.getResponse().setResultValueBean(pgTreeNode);
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occurred in CreateContentObjectDynamicVariantTreeNodeService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					context.getServiceName() + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void createAssociationsForDynamicVariant(ContentObject contentObject, ParameterGroupTreeNode treeNode, ZonePart zonePart, Long userId, List<MessagepointLocale> languages) {

		for (MessagepointLocale locale : languages) {
			ContentObjectAssociation assoc = new ContentObjectAssociation();
			assoc.setContentObject(contentObject);
			assoc.setDataType(contentObject.getFocusOnDataType());
			assoc.setContentObjectPGTreeNode(treeNode);
			assoc.setReferencingContentObjectPGTreeNode(treeNode.getParentNode());
			assoc.setCreated(DateUtil.now());
			assoc.setCreatedBy(userId);
			assoc.setTypeId(ContentAssociationType.ID_REFERENCES);
			assoc.setMessagepointLocale(locale);

			// If it's referencing, these attributes have to be null
//			//find the default content
//			ContentObjectAssociation ca = ContentObjectAssociation.findParentByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), treeNode, locale, zonePart);
//			if(ca != null){
//				if (ca.getReferencingImageLibrary() != null)
//					assoc.setContent(null);
//				else
//					assoc.setContent(ca.getContent());
//				assoc.setReferencingImageLibrary(ca.getReferencingImageLibrary());
//			}

			assoc.setZonePart(zonePart);
			contentObject.getContentObjectAssociations().add(assoc);
			assoc.save(true);
		}
	}

	public void validate(ServiceExecutionContext context) {
		
	}
	
	public static ServiceExecutionContext createContextForNewCreateWithGuid( long contentObjectId,String nodeName,
			long parentTreeNodeId, Long userId, String[] startingValues, boolean shareFlag, String guid){
		return createContext(NEW_CREATE, contentObjectId,nodeName, null, null,
				parentTreeNodeId, userId, startingValues, shareFlag,0, guid);
	}
	
    public static ServiceExecutionContext createContextForNewCreateWithGuid( long contentObjectId,String nodeName, String dna, String pgtnDna,
            long parentTreeNodeId, Long userId, String[] startingValues, boolean shareFlag, String guid){
        return createContext(NEW_CREATE, contentObjectId,nodeName, dna, pgtnDna,
                parentTreeNodeId, userId, startingValues, shareFlag,0, guid);
    }
    
	public static ServiceExecutionContext createContextForNewCreate( long contentObjectId,String nodeName,
			long parentTreeNodeId, Long userId, String[] startingValues, boolean shareFlag){
		return createContext(NEW_CREATE, contentObjectId,nodeName, null, null,
				parentTreeNodeId, userId,startingValues, shareFlag,0, null);
	}
	
    public static ServiceExecutionContext createContextForNewCreate( long contentObjectId,String nodeName, String dna, String pgtnDna,
            long parentTreeNodeId, Long userId, String[] startingValues, boolean shareFlag){
        return createContext(NEW_CREATE, contentObjectId,nodeName, dna, pgtnDna,
                parentTreeNodeId, userId,startingValues, shareFlag,0, null);
    }
    
	public static ServiceExecutionContext createContextForShareCollection( long contentObjectId,String nodeName,
			long parentTreeNodeId, Long userId, long pgCollectionId){
		return createContextForShareCollection(contentObjectId, nodeName, null, null, parentTreeNodeId, userId, pgCollectionId);
	}
	
    public static ServiceExecutionContext createContextForShareCollection( long contentObjectId,String nodeName, String dna, String pgtnDna,
            long parentTreeNodeId, Long userId, long pgCollectionId){
        return createContext(SHARE_COLLECTION, contentObjectId,nodeName, dna, pgtnDna,
                parentTreeNodeId, userId, null, false,pgCollectionId, null);
    }
    
	private static  ServiceExecutionContext createContext(int createMode, long contentObjectId,String nodeName, String dna, String pgtnDna,
			long parentTreeNodeId, Long userId, String[] startingValues, boolean shareFlag, long pgCollectionId, String guid) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateContentObjectDynamicVariantTreeNodeServiceRequest request = new CreateContentObjectDynamicVariantTreeNodeServiceRequest();
		context.setRequest(request);
		request.setContentObjectId(contentObjectId);
		request.setNodeName(nodeName);
		request.setParentTreeNodeId(parentTreeNodeId);
		request.setUserId(userId);

		if(createMode == NEW_CREATE) {
			request.setStartingValues(startingValues);
			request.setShared(shareFlag);
			request.setPgCollectionGuid(guid);
			request.setDna(dna);
			request.setPgtnDna(pgtnDna);
		} else if(createMode == SHARE_COLLECTION) {
			request.setPgCollectionId(pgCollectionId);
		}
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
