package com.prinova.messagepoint.platform.services.content;

import java.util.List;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient.SftpRepoImage;

public class ContentObjectBulkImageCreateServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = -4353826201842151064L;

	private User 				requestor;
	private List<MultipartFile> contentLibraryImages;
	private List<String> 		contentLibraryNames;
	private Set<Long> 			documentIds;
	private String 				metatags;
	private Boolean				variableContentEnabled;
	private Boolean				isActivate;
	private List<SftpRepoImage>	sftpRepoImageList;
	
	public List<MultipartFile> getContentLibraryImages() {
		return contentLibraryImages;
	}
	public void setContentLibraryImages(List<MultipartFile> contentLibraryImages) {
		this.contentLibraryImages = contentLibraryImages;
	}
	public List<String> getContentLibraryNames() {
		return contentLibraryNames;
	}
	public void setContentLibraryNames(List<String> contentLibraryNames) {
		this.contentLibraryNames = contentLibraryNames;
	}
	public Set<Long> getDocumentIds() {
		return documentIds;
	}
	public void setDocumentIds(Set<Long> documentIds) {
		this.documentIds = documentIds;
	}
	public String getMetatags() {
		return metatags;
	}
	public void setMetatags(String metatags) {
		this.metatags = metatags;
	}
	public Boolean getVariableContentEnabled() {
		return variableContentEnabled;
	}
	public void setVariableContentEnabled(Boolean variableContentEnabled) {
		this.variableContentEnabled = variableContentEnabled;
	}
	public List<SftpRepoImage> getSftpRepoImageList() {
		return sftpRepoImageList;
	}
	public void setSftpRepoImageList(List<SftpRepoImage> sftpRepoImageList) {
		this.sftpRepoImageList = sftpRepoImageList;
	}
	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	public Boolean getIsActivate() {
		return isActivate;
	}
	public void setIsActivate(Boolean isActivate) {
		this.isActivate = isActivate;
	}
}
