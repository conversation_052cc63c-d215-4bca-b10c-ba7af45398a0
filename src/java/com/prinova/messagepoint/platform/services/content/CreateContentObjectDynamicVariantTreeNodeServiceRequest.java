package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class CreateContentObjectDynamicVariantTreeNodeServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = 717728902503638386L;
	private long contentObjectId;
	private String nodeName;
	private String dna;
	private String pgtnDna;
	private long parentTreeNodeId;
	private Long userId;
	// the following attributes are for used for creating a new collection
	private String[] startingValues;
	private boolean shared;
	// the following attribute is need for picking a pre-existing collection
	private long pgCollectionId;
	private String pgInstanceCollectionGuid;

	private ContentObject contentObject;
	public ContentObject getContentObject() {
		if (contentObject == null) {
			contentObject = ContentObject.findById(contentObjectId);
		}
		return contentObject;
	}


	public long getContentObjectId() {
		return contentObjectId;
	}


	public void setContentObjectId(long contentObjectId) {
		this.contentObjectId = contentObjectId;
	}

    public String getDna()
    {
        return dna;
    }

    public void setDna(String dna)
    {
        this.dna = dna;
    }
    public String getPgtnDna()
    {
        return pgtnDna;
    }

    public void setPgtnDna(String pgtnDna)
    {
        this.pgtnDna = pgtnDna;
    }

	public String getNodeName() {
		return nodeName;
	}


	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
	}


	public long getParentTreeNodeId() {
		return parentTreeNodeId;
	}


	public void setParentTreeNodeId(long parentTreeNodeId) {
		this.parentTreeNodeId = parentTreeNodeId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public boolean isShared() {
		return shared;
	}

	public void setShared(boolean shared) {
		this.shared = shared;
	}

	public long getPgCollectionId() {
		return pgCollectionId;
	}

	public void setPgCollectionId(long pgCollectionId) {
		this.pgCollectionId = pgCollectionId;
	}

	public String[] getStartingValues() {
		return startingValues;
	}


	public void setStartingValues(String[] startingValues) {
		this.startingValues = startingValues;
	}

    public String getPgCollectionGuid()
    {
        return this.pgInstanceCollectionGuid;
    }
    
    public void setPgCollectionGuid(String guid)
    {
        this.pgInstanceCollectionGuid = guid;
    }
    
}
