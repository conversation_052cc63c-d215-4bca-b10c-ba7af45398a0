package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;


public class CreateOrUpdateClipboardContentServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 6434761114760621409L;

	private int 						action;
	private long 						clipboardContentId;
	private String 						metatags;
	private String						content;
	private User 						requestor;
	private Boolean						isShared;
	
	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}

	public User getRequestor() {
		return requestor;
	}
	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}
	
	public long getClipboardContentId() {
		return clipboardContentId;
	}
	public void setClipboardContentId(long clipboardContentId) {
		this.clipboardContentId = clipboardContentId;
	}
	
	public String getMetatags() {
		return metatags;
	}
	public void setMetatags(String metatags) {
		this.metatags = metatags;
	}
	
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	
	public Boolean getIsShared() {
		return isShared;
	}
	public void setIsShared(Boolean isShared) {
		this.isShared = isShared;
	}

}