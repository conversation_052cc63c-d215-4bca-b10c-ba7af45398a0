package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.ArrayList;
import java.util.List;

public class UnlockContentObjectServiceRequest implements ServiceRequest {

    private static final long serialVersionUID = 9015608700563852355L;

    private List<ContentObject> contentObjectList;
    private Long assignToUserId;
    private Long requestorId;
    private String note;
    private boolean workComplete;
    private List<User> assignedUsers = new ArrayList<>();
    private boolean isReassign;
    private ConfigurableWorkflow workflow;

    public String getNote() {
        return note;
    }
    public void setNote(String note) {
        this.note = note;
    }
    public Long getAssignToUserId() {
        return assignToUserId;
    }
    public void setAssignToUserId(Long assignToUserId) {
        this.assignToUserId = assignToUserId;
    }
    public List<ContentObject> getContentObjectList() {
        return contentObjectList;
    }
    public void setContentObjectList(List<ContentObject> contentObjectList) {
        this.contentObjectList = contentObjectList;
    }
    public Object getContentObject(){
        if(this.contentObjectList == null){
            return null;
        }else{
            return this.contentObjectList.get(0);
        }
    }
    public void setContentObject(ContentObject contentObject){
        if(this.contentObjectList == null){
            this.contentObjectList = new ArrayList<>();
        }
        this.contentObjectList.add(contentObject);
    }
    public Long getRequestorId() {
        return requestorId;
    }
    public void setRequestorId(Long requestorId) {
        this.requestorId = requestorId;
    }
    public boolean isWorkComplete() {
        return workComplete;
    }
    public void setWorkComplete(boolean workComplete) {
        this.workComplete = workComplete;
    }
    public List<User> getAssignedUsers() {
        return assignedUsers;
    }
    public void setAssignedUsers(List<User> assignedUsers) {
        this.assignedUsers = assignedUsers;
    }
    public boolean isReassign() {
        return isReassign;
    }
    public void setReassign(boolean isReassign) {
        this.isReassign = isReassign;
    }
    public ConfigurableWorkflow getWorkflow() {
        return workflow;
    }
    public void setWorkflow(ConfigurableWorkflow workflow) {
        this.workflow = workflow;
    }

}