package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

public class CreateOrUpdateParagraphStyleService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateOrUpdateParagraphStyleService.class);
	public static final String SERVICE_NAME = "content.CreateOrUpdateParagraphStyleService";

	@Autowired
	private CacheDataRepository cacheDataRepository;

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateParagraphStyleServiceRequest request = (CreateOrUpdateParagraphStyleServiceRequest) context.getRequest();
			boolean isCust = request.isCust();
			if(isCust){
				ParagraphStyle paraStyle = ParagraphStyle.findById(request.getId());
				Document document = Document.findById(request.getDocumentId());
				ParagraphStyleCustomization paraStyleCust = paraStyle.getParagraphStyleCustomizations().get(document);
				boolean isNew = false;
				if(paraStyleCust == null){	// New
					paraStyleCust = new ParagraphStyleCustomization();
					isNew = true;
				}
				paraStyleCust.setConnectorName(request.getConnectorName());
				paraStyleCust.setAlignmentId(request.getAlignment().getId());
				paraStyleCust.setParagraphSpacingBefore(DecimalValueUtil.hydrate(request.getParagraphSpacingBefor()));
				paraStyleCust.setParagraphSpacingAfter(DecimalValueUtil.hydrate(request.getParagraphSpacingAfter()));
				paraStyleCust.setLineSpacing(request.getLineSpacing());
				paraStyleCust.setLineSpacingType(request.getLineSpacingType());
				paraStyleCust.setLeftMargin(DecimalValueUtil.hydrate(request.getLeftMargin()));
				paraStyleCust.setRightMargin(DecimalValueUtil.hydrate(request.getRightMargin()));
				paraStyleCust.setIndent(DecimalValueUtil.hydrate(request.getIndent()));
				if (request.isHangingIndent()) {
					paraStyleCust.setIndent(paraStyleCust.getIndent() * -1);
				}
				if (request.getBorder() != null)
					paraStyleCust.setBorderTypeId(request.getBorder().getId());
				else
					paraStyleCust.setBorderTypeId(BorderType.ID_BORDER_NONE);
				paraStyleCust.setBorderWidth(DecimalValueUtil.hydrate(request.getBorderWidth() != null && !request.getBorderWidth().trim().isEmpty() ?
																	request.getBorderWidth() : "0"));
				paraStyleCust.setTextStyle(request.getTextStyle());
				paraStyleCust.setBulletedListApplicable(request.isBulletedListApplicable());
				paraStyleCust.setTaggingOverride(request.getTaggingOverride());
				if(isNew){
					paraStyleCust.setMasterParagraphStyle(paraStyle);
					paraStyleCust.save(true);
					paraStyle.getParagraphStyleCustomizations().put(document, paraStyleCust);
					paraStyle.save();
				}else{
					paraStyleCust.save();
				}
				paraStyleCust.setToggleAlignment(request.isToggleAlignment());
				paraStyleCust.setToggleLeftMargin(request.isToggleLeftMargin());
				paraStyleCust.setToggleLineSpacing(request.isToggleLeftMargin());
				paraStyleCust.setToggleLineSpacingValues(request.getToggleLineSpacingValues());
			}else{
				boolean isNew = (request.getId() <= 0);
				
				ParagraphStyle paragraphStyle = null;
				if (isNew) {
					paragraphStyle = new ParagraphStyle();
				} else {
					paragraphStyle = ParagraphStyle.findById(request.getId());
				}
				
				paragraphStyle.setName(request.getName());
				paragraphStyle.setConnectorName(request.getConnectorName());
				paragraphStyle.setAlignmentId(request.getAlignment().getId());
				paragraphStyle.setParagraphSpacingBefore(DecimalValueUtil.hydrate(request.getParagraphSpacingBefor()));
				paragraphStyle.setParagraphSpacingAfter(DecimalValueUtil.hydrate(request.getParagraphSpacingAfter()));
				paragraphStyle.setLineSpacing(request.getLineSpacing());
				paragraphStyle.setLineSpacingType(request.getLineSpacingType());
				paragraphStyle.setLeftMargin(DecimalValueUtil.hydrate(request.getLeftMargin()));
				paragraphStyle.setRightMargin(DecimalValueUtil.hydrate(request.getRightMargin()));
				paragraphStyle.setIndent(DecimalValueUtil.hydrate(request.getIndent()));
				paragraphStyle.setTaggingOverride(request.getTaggingOverride());
				if (request.isHangingIndent()) {
					paragraphStyle.setIndent(paragraphStyle.getIndent() * -1);
				}
				if (request.getBorder() != null)
					paragraphStyle.setBorderTypeId(request.getBorder().getId());
				else
					paragraphStyle.setBorderTypeId(BorderType.ID_BORDER_NONE);
				paragraphStyle.setBorderWidth(DecimalValueUtil.hydrate(request.getBorderWidth()));
				paragraphStyle.setTextStyle(request.getTextStyle());
				paragraphStyle.setBulletedListApplicable(request.isBulletedListApplicable());
				
				paragraphStyle.setToggleAlignment(request.isToggleAlignment());
				paragraphStyle.setToggleLeftMargin(request.isToggleLeftMargin());
				paragraphStyle.setToggleLineSpacing(request.isToggleLineSpacing());
				paragraphStyle.setToggleLineSpacingValues(request.getToggleLineSpacingValues());

				paragraphStyle.save();

				if (cacheDataRepository != null) {
					cacheDataRepository.updateStylesData();
				} else {
					log.error("CacheDataRepository is null. Unable to update styles data.");
				}

				getResponse(context).setResultValueBean(paragraphStyle.getId());

				if(isNew){
					// Audit (Audit Paragraph Style creation)
					AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_PARA_STYLE, request.getName(), paragraphStyle.getId(), AuditActionType.ID_CHANGE_CREATED, null);
				}
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CreateOrUpdateParagraphStyleService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	public static ServiceExecutionContext createContext(boolean isCust,
														long id,
														String name,
														String connectorName,
														ParagraphAlignment alignment,
														String paragraphSpacingBefor,
														String paragraphSpacingAfter,
														int lineSpacing,
														int lineSpacingType,
														String leftMargin,
														String rightMargin,
														String indent,
														boolean hangingIndent,
														BorderType border,
														String borderWidth,
														TextStyle textStyle,
														boolean isBulletedListApplicable,
														long documentId,
														String taggingOverride,
														boolean	toggleAlignment,
														boolean toggleLineSpacing,
														boolean	toggleLeftMargin,
														Set<String> toggleLineSpacingValues) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateParagraphStyleServiceRequest request = new CreateOrUpdateParagraphStyleServiceRequest();
		context.setRequest(request);

		request.setCust(isCust);
		request.setId(id);
		request.setName(name);
		request.setConnectorName(connectorName);
		request.setAlignment(alignment);
		request.setParagraphSpacingBefor(paragraphSpacingBefor);
		request.setParagraphSpacingAfter(paragraphSpacingAfter);
		request.setLineSpacing(lineSpacing);
		request.setLineSpacingType(lineSpacingType);
		request.setLeftMargin(leftMargin);
		request.setRightMargin(rightMargin);
		request.setIndent(indent);
		request.setHangingIndent(hangingIndent);
		request.setBorder(border);
		request.setBorderWidth(borderWidth);
		request.setTextStyle(textStyle);
		request.setBulletedListApplicable(isBulletedListApplicable);
		request.setDocumentId(documentId);
		request.setTaggingOverride(taggingOverride);
		
		request.setToggleAlignment(toggleAlignment);
		request.setToggleLineSpacing(toggleLineSpacing);
		request.setToggleLeftMargin(toggleLeftMargin);
		request.setToggleLineSpacingValues(toggleLineSpacingValues);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}
}