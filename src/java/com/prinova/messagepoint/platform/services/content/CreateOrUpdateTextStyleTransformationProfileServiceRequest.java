package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.font.ActionEntry;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.HashSet;
import java.util.Set;

public class CreateOrUpdateTextStyleTransformationProfileServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 7333437527545726028L;

	private long						id;

	private int							action;
	
	private String						name;

	private Set<MessagepointLocale> languages = new HashSet<>();

	private Set<ActionEntry> actionEntries = new HashSet<>();

	private Boolean transformDefaultStyles = false;

	private int type = TextStyleTransformationProfile.TYPE_TRANSLATION;

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public int getAction() { return action; }
	public void setAction(int action) { this.action = action; }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Set<MessagepointLocale> getLanguages() {
		return languages;
	}

	public void setLanguages(Set<MessagepointLocale> languages) {
		this.languages = languages;
	}

	public Set<ActionEntry> getActionEntries() {
		return actionEntries;
	}

	public void setActionEntries(Set<ActionEntry> actionEntries) {
		this.actionEntries = actionEntries;
	}


	public Boolean getTransformDefaultStyles() {
		return transformDefaultStyles;
	}

	public void setTransformDefaultStyles(Boolean transformDefaultStyles) {
		this.transformDefaultStyles = transformDefaultStyles;
	}

	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

}