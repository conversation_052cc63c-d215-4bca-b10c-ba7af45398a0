package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public class BulkArchiveContentObjectService extends AbstractService {
	public static final String SERVICE_NAME = "content.BulkArchiveContentObjectService";
	private static final Log log = LogUtil.getLog(BulkArchiveContentObjectService.class);
	
	@Transactional(propagation = Propagation.SUPPORTS)	
	public void execute(ServiceExecutionContext context) {
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			User requestor = UserUtil.getPrincipalUser();
			String userNote = request.getUserNote();
			for (ContentObject contentObject : contentObjectList) {
				try {

					if (contentObject.archiveActiveData(false) == ContentObject.REQUEST_SUCCESSFUL)
					{
						if(userNote!=null && !userNote.isEmpty()) {
							// Persist the user comment
							ContentObjectComment newComment = new ContentObjectComment();
							newComment.setContentObject(contentObject);
							newComment.setDataType(ContentObject.DATA_TYPE_ARCHIVED);
							newComment.setComment(userNote);
							newComment.setUserId(requestor.getId());
							HibernateUtil.getManager().saveObjectWithPropagationRequired(newComment);
							contentObject.getComments().add(newComment);
						}

					    contentObject.save();
                        HibernateUtil.getManager().getSession().flush();
						// Push content change to elastic
						/** TODO
						if(ElasticsearchContentUtils.isContentCompareEnabled()) {
							ElasticsearchContentUtils.removeContentForModel(ContentObject.class, true, model, modelInstance);
						}
						 **/
					}

				} catch (Exception e1) {
					log.error(" unexpected exception when invoking BulkArchiveContentObjectService execute method", e1);
				}

				// Audit (Audit Archived)
				AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_ARCHIVED, null);
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkArchiveContentObjectService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION", ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		/**
		 * Use the ArchiveWIPService to validate each check out request
		 */
		BulkContentObjectActionServiceRequest request = (BulkContentObjectActionServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		User principal = UserUtil.getPrincipalUser();
		User requestor = null;
		if (principal != null) {
			requestor = User.findById(principal.getId());
		}
		String userNote = request.getUserNote();
		for (ContentObject contentObject : contentObjectList) {

			if (!contentObject.hasActiveData()) {
				this.getResponse(context).addErrorMessage("model id : " + contentObject.getId(),
						ApplicationErrorMessages.ARC_INSTANCE_NOT_IP,
						null,
						context.getLocale());
				// return;
			}
		}
	}

	public static ServiceExecutionContext createContext(List<ContentObject> contentObjects, User requestor, String userNote) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkContentObjectActionServiceRequest request = new BulkContentObjectActionServiceRequest();
		context.setRequest(request);
		request.setContentObjects(contentObjects);
		request.setRequestor(requestor);
		request.setUserNote(userNote);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

}