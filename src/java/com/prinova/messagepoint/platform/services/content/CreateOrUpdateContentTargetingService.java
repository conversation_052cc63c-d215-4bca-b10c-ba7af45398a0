package com.prinova.messagepoint.platform.services.content;

import java.util.Date;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.ContentTargeting;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.UserUtil;

public class CreateOrUpdateContentTargetingService extends AbstractService {

	public static final String SERVICE_NAME = "content.CreateOrUpdateContentTargetingService";
	
	private static final Log log = LogUtil.getLog(CreateOrUpdateContentTargetingService.class);
	
	public static final int ACTION_CREATE_CONTENT_TARGETING 	= 1;
	public static final int ACTION_CONVERT_TO_CONTENT_TARGETING = 2;

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateContentTargetingServiceRequest request = (CreateOrUpdateContentTargetingServiceRequest) context.getRequest();

			User requestor = UserUtil.getPrincipalUser();

			if ( request.getAction() == ACTION_CREATE_CONTENT_TARGETING ) {

				ContentTargeting contentTargeting = new ContentTargeting();
	
				contentTargeting.setUpdated(new Date());
				contentTargeting.setUpdatedBy(requestor.getId());
				contentTargeting.setCreated(new Date());
				contentTargeting.setCreatedBy(requestor.getId());
				contentTargeting.save(true);
			
				context.getResponse().setResultValueBean( contentTargeting.getId() );

			} else if ( request.getAction() == ACTION_CONVERT_TO_CONTENT_TARGETING) {

				ContentObject contentObject = ContentObject.findById( request.getContentObjectId() != null ? request.getContentObjectId() : -1L );

				if ( contentObject != null ) {

					ContentTargeting contentTargeting = new ContentTargeting();

					ContentObjectData cloneFrom = contentObject.getLatestContentObjectDataActiveCentric();

					contentTargeting.setIncludedTargetGroupRelationship( cloneFrom.getIncludedTargetGroupRelationship() );
					contentTargeting.setExtendedTargetGroupRelationship( cloneFrom.getExtendedTargetGroupRelationship() );
					contentTargeting.setExcludedTargetGroupRelationship( cloneFrom.getExcludedTargetGroupRelationship() );

					if (cloneFrom.getIncludedTargetGroups() != null) {
						for (TargetGroup sourceTargetGroup : cloneFrom.getIncludedTargetGroups()) {
							TargetGroup clonedTargetGroup = CloneHelper.assign(sourceTargetGroup, o->o.clone(cloneFrom, contentTargeting, null));

							if(sourceTargetGroup.isParameterized()) {
								TargetGroup.copyParamerizedData(sourceTargetGroup, clonedTargetGroup, cloneFrom, contentTargeting);
							}

							clonedTargetGroup.save();
							contentTargeting.getIncludedTargetGroups().add( clonedTargetGroup );
						}
					}

					if (cloneFrom.getExcludedTargetGroups() != null) {
						for (TargetGroup sourceTargetGroup : cloneFrom.getExcludedTargetGroups()) {
							TargetGroup clonedTargetGroup = CloneHelper.assign(sourceTargetGroup, o->o.clone(cloneFrom, contentTargeting, null));

                            if(sourceTargetGroup.isParameterized()) {
								TargetGroup.copyParamerizedData(sourceTargetGroup, clonedTargetGroup, cloneFrom, contentTargeting);
							}

							clonedTargetGroup.save();
							contentTargeting.getExcludedTargetGroups().add(clonedTargetGroup);
						}
					}

					if (cloneFrom.getExtendedTargetGroups() != null) {
						for (TargetGroup sourceTargetGroup : cloneFrom.getExtendedTargetGroups()) {
							TargetGroup clonedTargetGroup = CloneHelper.assign(sourceTargetGroup, o->o.clone(cloneFrom, contentTargeting, null));

                            if(sourceTargetGroup.isParameterized()) {
								TargetGroup.copyParamerizedData(sourceTargetGroup, clonedTargetGroup, cloneFrom, contentTargeting);
							}

							clonedTargetGroup.save();
							contentTargeting.getExtendedTargetGroups().add(clonedTargetGroup);
						}
					}

					contentTargeting.setUpdated(new Date());
					contentTargeting.setUpdatedBy(requestor.getId());
					contentTargeting.setCreated(new Date());
					contentTargeting.setCreatedBy(requestor.getId());
					contentTargeting.save(true);

					context.getResponse().setResultValueBean( contentTargeting.getId() );

				}

			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking CreateOrUpdateContentTargetingService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}
		
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}
	
	public static ServiceExecutionContext createContextForCreateContentTargeting() {
		return createContext(ACTION_CREATE_CONTENT_TARGETING, null);
	}

	public static ServiceExecutionContext createContextForConvertToContentTargeting(Long contentObjectId) {
		return createContext(ACTION_CONVERT_TO_CONTENT_TARGETING, contentObjectId);
	}

	private static ServiceExecutionContext createContext(int actionId, Long contentObjectId) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateContentTargetingServiceRequest request = new CreateOrUpdateContentTargetingServiceRequest();
		context.setRequest(request);

		request.setAction(actionId);
		request.setContentObjectId(contentObjectId);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
