package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class CreateOrUpdateTextStyleFontServiceRequest extends SimpleServiceRequest {
	private static final long serialVersionUID = -5011620421342850242L;

	private TextStyleFont		textStyleFont;
	private Long 				ttfSandboxId;
	private Long 				ttfBoldSandboxId;
	private Long 				ttfItalicSandboxId;
	private Long 				ttfBoldItalicSandboxId;

	public TextStyleFont getTextStyleFont() {
		return textStyleFont;
	}
	public void setTextStyleFont(TextStyleFont textStyleFont) {
		this.textStyleFont = textStyleFont;
	}
	public Long getTtfSandboxId() {
		return ttfSandboxId;
	}
	public void setTtfSandboxId(Long ttfSandboxId) {
		this.ttfSandboxId = ttfSandboxId;
	}

	public Long getTtfBoldSandboxId() {
		return ttfBoldSandboxId;
	}
	public void setTtfBoldSandboxId(Long ttfBoldSandboxId) {
		this.ttfBoldSandboxId = ttfBoldSandboxId;
	}
	public Long getTtfItalicSandboxId() {
		return ttfItalicSandboxId;
	}
	public void setTtfItalicSandboxId(Long ttfItalicSandboxId) {
		this.ttfItalicSandboxId = ttfItalicSandboxId;
	}
	public Long getTtfBoldItalicSandboxId() {
		return ttfBoldItalicSandboxId;
	}
	public void setTtfBoldItalicSandboxId(Long ttfBoldItalicSandboxId) {
		this.ttfBoldItalicSandboxId = ttfBoldItalicSandboxId;
	}
}
