package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class GlobalContentObjectVersioningService extends AbstractService {

	public static final String SERVICE_NAME = "contentObject.GlobalContentObjectVersioningService";

    private static final Log log = LogUtil.getLog(GlobalContentObjectVersioningService.class);

	public static final int ACTION_DUPLICATE_CONTENT_OBJECT 	= 2;
	public static final int ACTION_SYNC_CONTENT_OBJECT 		    = 3;

    static final private boolean usingMergeInsteadOfCopy = false;

    private Map<Long, Long> clonedOrSyncedModelMap = new HashMap<>();

	public void execute(ServiceExecutionContext context) {
        SessionHolder podMasterSessionHolder = null;
        SessionHolder mainSessionHolder = null;
        SessionHolder saveSessionHolder = null;
        boolean sessionSwitched = false;

        clonedOrSyncedModelMap.clear();

        try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

            log.info("Running GlobalContentObjectVersioningService");

            GlobalContentObjectVersioningServiceRequest request = (GlobalContentObjectVersioningServiceRequest) context.getRequest();

            boolean syncFromOther = request.isSyncUpdateDocument();

            String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
            String requestSchema = request.getSourceSchema();

            String sourceSchema = syncFromOther ? requestSchema : currentSchema;
            String targetSchema = syncFromOther ? currentSchema : requestSchema;

            User requestor = request.getRequestor();

            if(syncFromOther) {
                if (sourceSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(sourceSchema)) {
                    Session saveSession = HibernateUtil.getManager().getSession();
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;
                }
            } else {
                if (targetSchema != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(targetSchema)) {
                    Node targetNode = Node.findBySchema(targetSchema);
                    podMasterSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
                    Session podMasterSession = HibernateUtil.getManager().getSession();
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(targetSchema);
                    Session saveSession = HibernateUtil.getManager().getSession();
                    saveSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema);
                    Session sourceSession = HibernateUtil.getManager().getSession();
                    CloneHelper.startCrossInstanceClone();
                    CloneHelper.addSession(null, podMasterSession);
                    CloneHelper.addSession(targetSchema, saveSession);
                    CloneHelper.addSession(sourceSchema, sourceSession);
                    sessionSwitched = true;

                    User targetUser = requestor.getNodeUser(targetNode);
                    if(targetUser != null) {
                        requestor = targetUser;
                    }
                }
            }

            CloneHelper.setRequestor(requestor);

            if ( request.getAction() == ACTION_DUPLICATE_CONTENT_OBJECT ) {
                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
                    syncGlobalContentObjects(request, context.getResponse(), true, 40, 5);
                    checkZoneGlobalObjects(request, context.getResponse(), 45, 5);
                    swapContentIfNotSwappedYet(request, context.getResponse(), 50, 8);
                    swapComplexValueIfNotSwappedYet(request, context.getResponse(), 58, 2);
                    CloneHelper.swapContentObjectIDs();
    	            updateGlobalContentObjectsSyncHistory(request, SyncHistory.SYNC_TYPE_CLONING);
                }
                updateZoneReference(request);
			} else if ( request.getAction() == ACTION_SYNC_CONTENT_OBJECT ) {
                // We need swap the global reference IDs if we are cloning crossing schemas.
                if (MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() != null) {
                    syncGlobalContentObjects(request, context.getResponse(), false, 40, 5);
                    checkZoneGlobalObjects(request, context.getResponse(), 45, 5);
                    swapContentIfNotSwappedYet(request, context.getResponse(), 50, 8);
                    swapComplexValueIfNotSwappedYet(request, context.getResponse(), 58, 2);
                    CloneHelper.swapContentObjectIDs();
                    updateGlobalContentObjectsSyncHistory(request, SyncHistory.SYNC_TYPE_SYNCHRONIZING);
                } else {
//                    syncEmbeddedContents(request, context.getResponse(), 40, 20);
                }
            }

            doHibernateUtilFlush();
            doHibernateUtilClear();

            log.info("Finished GlobalContentObjectVersioningService");
		} catch (Exception e) {
			log.error(" unexpected exception when invoking GlobalContentObjectVersioningService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
		} finally {
            clonedOrSyncedModelMap.clear();

            if (sessionSwitched) {
                CloneHelper.stopCrossInstanceClone();

                if(saveSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(saveSessionHolder);
                if(mainSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);
                if(podMasterSessionHolder != null)
                    HibernateUtil.getManager().restoreSession(podMasterSessionHolder);
            }
		}
	}

	private void doHibernateUtilFlush() {
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            HibernateUtil.getManager().getSession().flush();
        } else {
            CloneHelper.getCloneSession().flush();
        }
	}

    private void doHibernateUtilClear() {
        if(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier() == null) {
            HibernateUtil.getManager().getSession().clear();
        } else {
            CloneHelper.getCloneSession().clear();
        }
    }

    private void updateGlobalContentObjectsSyncHistory(GlobalContentObjectVersioningServiceRequest request, Integer syncOperationType) {
        User requestor = request.getRequestor();

        Document projectDocument = CloneHelper.queryInSaveSession(()->Document.findById(request.getClonedDocumentId()));
        Document otherDocument =   Document.findById(request.getOriginDocumentId());

        Document sourceDocument = otherDocument;
        Document targetDocument = projectDocument;

        String sourceNodeGuid = Node.getCurrentNode().getGuid();
        String targetNodeGuid = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getGuid());
        String targetSchemaName = CloneHelper.queryInSaveSession(() -> Node.getCurrentNode().getSchemaName());
        boolean hideUntilNextChange = request.isHideUntilNextChange();

        Set<Long> languagesForSync = request.getLanguagesForSync();

        for(Map.Entry<Long, Long> entry :  clonedOrSyncedModelMap.entrySet()) {
            Long targetModelId = entry.getKey();
            Long sourceModelId = entry.getValue();
            Map<Long, Long> modelIdMap = new HashMap<>();
            modelIdMap.put(targetModelId, sourceModelId);
            SyncTouchpointUtil.updateContentObjectSyncHistory(syncOperationType,
                    modelIdMap,
                    sourceDocument.getId(), targetDocument.getId(),
                    requestor.getId(),
                    sourceNodeGuid, targetNodeGuid,
                    targetSchemaName,
                    false,
                    hideUntilNextChange, languagesForSync);
            doHibernateUtilFlush();
            doHibernateUtilClear();
        }

/*
        for(Map.Entry<Long, Long> clonedIdEntry : clonedOrSyncedAllEmbeddedContentInstances.entrySet()) {
            EmbeddedContentInstance targetInstance = CloneHelper.queryInSaveSession((()->EmbeddedContentInstance.findById(clonedIdEntry.getKey())));
            EmbeddedContentInstance sourceInstance = EmbeddedContentInstance.findById(clonedIdEntry.getValue());
            Map<EmbeddedContentInstance, EmbeddedContentInstance> clonedMap = new HashMap<>();
            clonedMap.put(targetInstance, sourceInstance);
            SyncTouchpointUtil.updateVersionedModelSyncHistory(syncOperationType, SyncObjectType.ID_SMART_TEXT, clonedMap, sourceDocument.getId(), targetDocument.getId(), requestor.getId(), sourceNodeGuid, hideUntilNextChange);
        }
 */
	}

    private void updateZoneReference(GlobalContentObjectVersioningServiceRequest request) {
        Document projectDocument = CloneHelper.queryInSaveSession(()->Document.findById(request.getClonedDocumentId()));
        Document otherDocument =   Document.findById(request.getOriginDocumentId());

        Document sourceDocument = otherDocument;
        Document targetDocument = projectDocument;

        Set<Zone> sourceZones = sourceDocument.getZones();
        Map<String, Zone> dnaToZoneMap = CloneHelper.queryInSaveSession(() -> targetDocument.getZones().stream()
                .collect(Collectors.toMap(Zone::getDna, Function.identity()))
        );

        for(Zone sourceZone : sourceZones) {
            String zoneDna = sourceZone.getDna();
            Zone targetZone = dnaToZoneMap.get(zoneDna);
            if(targetZone != null) {
                ContentObject sourceZoneDefaultCommunicationTemplateSmartText = sourceZone.getDefaultCommunicationTemplateSmartText();
                Set<ContentObject> sourceZoneSmartTextAssets = sourceZone.getSmartTextAssets();

                ContentObject targetZoneDefaultCommunicationTemplateSmartText = CloneHelper.assignAlreadyClonedObject(sourceZoneDefaultCommunicationTemplateSmartText);
                Set<ContentObject> targetZoneSmartTexts = CloneHelper.assignAlreadyClonedObject(sourceZoneSmartTextAssets);

                CloneHelper.execInSaveSession(()->{
                    targetZone.setDefaultCommunicationTemplateSmartText(targetZoneDefaultCommunicationTemplateSmartText);
                    targetZone.getSmartTextAssets().addAll(targetZoneSmartTexts);
                    targetZone.save();
                });
            }
        }

        CloneHelper.execInSaveSession(()->{
            HibernateUtil.getManager().getSession().flush();
            HibernateUtil.getManager().getSession().clear();
        });
    }

	private void syncGlobalContentObjects(GlobalContentObjectVersioningServiceRequest request, ServiceResponse response, boolean isClone, int startPercentage, int totalPercentage) throws Exception {
        User requestor = request.getRequestor();

        Document projectDocument = CloneHelper.queryInSaveSession(()->Document.findById(request.getClonedDocumentId()));
        Document otherDocument =   Document.findById(request.getOriginDocumentId());

        Document sourceDocument = otherDocument;
        Document targetDocument = projectDocument;

        Map<Long, Long> globalContentObjectMap = request.getGlobalContentObjectMap();

        Set<Long> needAssociates = request.getNeedAssociates();

        StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();

        if (globalContentObjectMap != null)
        {
            Map<ContentObject, ContentObject> processedGlobalContentObjectMap = new HashMap<>();

            int total = globalContentObjectMap.size();
            int count = 0;
            // Message
            //
            ArrayList<Long>globalContentObjectIDsList = new ArrayList<>(globalContentObjectMap.keySet());
            Collections.reverse(globalContentObjectIDsList);
            for (Long objectIdx10 : globalContentObjectIDsList)
            {
                syncGlobalContentObject(request, response, requestor, globalContentObjectMap, projectDocument, otherDocument,
                        sourceDocument, targetDocument, processedGlobalContentObjectMap, objectIdx10, isClone);

                count ++;
                int progress = startPercentage + (count * totalPercentage) / total;

                statusPollingBackgroundTask.setProgressInPercentInThread((int) progress);
                statusPollingBackgroundTask.save();

                doHibernateUtilFlush();

            }

        }

        {
            ContentObject.findGlobalSmartTextsVisibleForDocument(sourceDocument, false).forEach(ec -> {
                String dna = ec.getDna();
                CloneHelper.execInSaveSession(() -> {
                    ContentObject targetObject = ContentObject.findGlobalObjectByDna(dna);
                    if (targetObject != null) {
                        associateDocuments(targetObject, new ArrayList<>(), targetDocument);
                    }
                });
            });

            ContentObject.findGlobalImagesVisibleForDocument(sourceDocument, false, false).forEach(ec -> {
                String dna = ec.getDna();
                CloneHelper.execInSaveSession(() -> {
                    ContentObject targetObject = ContentObject.findGlobalObjectByDna(dna);
                    if (targetObject != null) {
                        associateDocuments(targetObject, new ArrayList<>(), targetDocument);
                    }
                });
            });

            if(needAssociates != null && ! needAssociates.isEmpty()) {
                for(Long contentObjectId : needAssociates) {
                    CloneHelper.execInSaveSession(() -> {
                        ContentObject targetObject = ContentObject.findById(contentObjectId);
                        if (targetObject != null) {
                            associateDocuments(targetObject, new ArrayList<>(), targetDocument);
                        }
                    });
                }
            }
        }

        doHibernateUtilFlush();
        doHibernateUtilClear();

        response.setResultValueBean( Boolean.valueOf(true) );
//        doHibernateUtilFlush();
    } // End of syncMessages

    private void syncGlobalContentObject(GlobalContentObjectVersioningServiceRequest request, ServiceResponse response, User requestor,
            Map<Long, Long> globalContentObjectMap, Document projectDocument, Document originDocument,
            Document sourceDocument, Document targetDocument,
            Map<ContentObject, ContentObject> processedMap, Long objectIdx10, boolean isClone) throws Exception {
    	boolean forceSync = request.isForceSync();
    	boolean logDifferences = request.isLogDifferences();
        boolean hideUntilNextChange = request.isHideUntilNextChange();

        Set<Long> languageIDsForSync = request.getLanguagesForSync();

        Map<Long, String> languagesForSync = languageIDsForSync == null ? null : languageIDsForSync
                .stream()
                .map(MessagepointLocale::findById)
                .collect(Collectors.toMap(MessagepointLocale::getId, MessagepointLocale::getCode));

        long objectId = objectIdx10 / 0x10;
        long objectStatus = globalContentObjectMap.get(objectIdx10);
        boolean objectIsReferenced = (objectStatus & 0x100000) > 0;
        boolean objectIsFromOther =  (objectStatus & 0x10000) > 0;
        objectStatus = objectStatus & 0xFFFF;

        long projectObjectStatus = (objectStatus & 0xFF00) / 0x100;
        long originObjectStatus = (objectStatus & 0xFF);

        long sourceActiveCopyStatus = 0;
        long sourceWorkingCopyStatus = 0;

        long targetActiveCopyStatus = 0;
        long targetWorkingCopyStatus = 0;

        sourceActiveCopyStatus = (originObjectStatus & 0xF0) / 0x10;
        sourceWorkingCopyStatus = originObjectStatus & 0xF;

        targetActiveCopyStatus = (projectObjectStatus & 0xF0) / 0x10;;
        targetWorkingCopyStatus = projectObjectStatus & 0xF;

        String objectSchema = objectIsFromOther ? originDocument.getObjectSchemaName() : projectDocument.getObjectSchemaName();

        ContentObject objectModel = CloneHelper.queryInSchema(objectSchema, ()->ContentObject.findById(objectId));
        if (objectModel != null && !processedMap.containsKey(objectModel))
        {
            String dna = CloneHelper.queryInSchema(objectSchema, ()->objectModel.getDna());
            String msgName = CloneHelper.queryInSchema(objectSchema, ()->objectModel.getName());
            boolean isLocalObject = CloneHelper.queryInSchema(objectSchema, ()->objectModel.isMessage() || objectModel.isLocalContentObject());

            ContentObject sourceModel = null;
            ContentObject targetModel = null;

            if (objectIsFromOther)
            {
                sourceModel = objectModel;
                targetModel = CloneHelper.queryInSaveSession(()->{
                    ContentObject embeddedContent = isLocalObject ?
                            ContentObject.findByDnaAndDocument(dna, targetDocument) : ContentObject.findGlobalObjectByDna(dna);
                	if(embeddedContent == null) {
                		embeddedContent = ContentObject.findGlobalObjectByDna(dna);
                	}
                	return embeddedContent;
                });
            }
            else
            {
                targetModel = objectModel;
                sourceModel = isLocalObject ?
                        ContentObject.findByDnaAndDocument(dna, sourceDocument) :
                        ContentObject.findGlobalObjectByDna(dna);
            }

            if(sourceModel == null) {
                return;
            }

            if(! forceSync) {
                if(targetModel != null) {
                    ContentObject targetModelTempFinal = targetModel;
                    if(CloneHelper.queryInSaveSession(()->targetModelTempFinal.hasWorkingData()
                            && targetModelTempFinal.getLockedForId() != 0
                            && targetModelTempFinal.getLockedForId() != requestor.getId()
                    )) {
                        return;
                    }
                }
            }

            String syncNote;
            if(hideUntilNextChange && sourceModel != null && targetModel != null) {
                syncNote = "Hide embedded content source id = (" + sourceModel.getId() + "), name = \"" + msgName + "\"";
                log.info(syncNote);
                clonedOrSyncedModelMap.put(targetModel.getId(), sourceModel.getId());
                return;
            } else if(isClone) {
                syncNote = "Clone embedded content source id = (" + sourceModel.getId() + "), name = \"" + msgName + "\"";
                log.info(syncNote);
            } else {
                syncNote = "Sync " + (objectIsReferenced ? "referenced" : "selected") + " embedded content source id = (" + sourceModel.getId() + "), name = \"" + msgName + "\"";
                log.info(syncNote);

                if(logDifferences) {
                    SyncTouchpointUtil.logSyncReason(log, sourceModel, targetModel);
                }
            }

            if(sourceModel == null || sourceModel.isRemoved())
            {
                if(targetModel != null) {
                    ContentObject targetModelFinal = targetModel;
                    CloneHelper.execInSaveSession(() -> {
                        targetModelFinal.setRemoved(true);
                    });

                    if(sourceModel == null || ! sourceModel.hasWorkingData()) {
                        CloneHelper.execInSaveSession(() -> {
                            if (targetModelFinal.hasWorkingData()) {
                                targetModelFinal.discardWorkingData();
                            }
                        });
                    }

                    if(sourceModel == null || ! sourceModel.hasActiveData()) {
                        CloneHelper.execInSaveSession(() -> {
                            if (targetModelFinal.hasActiveData()) {
                                targetModelFinal.archiveActiveData(false);
                            }
                        });
                    }

                    if(sourceModel == null || ! sourceModel.hasArchivedData()) {
                        CloneHelper.execInSaveSession(() -> {
                            if (targetModelFinal.hasArchivedData()) {
                                targetModelFinal.deleteArchiveData();
                            }
                        });
                    }
                }
            }
            else if(targetModel == null)
            {
                Long sourceLockedFor = sourceModel.getLockedFor();
                Long targetLockedFor = requestor.getId();
                targetModel = CloneHelper.clone(sourceModel, o->{
                    ContentObject clonedContentObject = o.clone(targetDocument, true, languagesForSync);
                    if(sourceLockedFor != null) {
                        clonedContentObject.setLockedFor(targetLockedFor);
                    }
                    return clonedContentObject;
                });
            }
            else
            {
                if (hideUntilNextChange && targetModel != null && sourceModel != null) {
                    clonedOrSyncedModelMap.put(targetModel.getId(), sourceModel.getId());
                    return;
                }

                Map<Long, String> pgtnNoLongerUsed = new HashMap<>();

                SyncTouchpointUtil.syncContentObjectVersionStatus(sourceDocument, targetDocument, sourceModel, targetModel, requestor);

                sourceModel = ContentObject.findById(sourceModel.getId());
                long targetModelId = targetModel.getId();
                targetModel = CloneHelper.queryInSaveSession(()->ContentObject.findById(targetModelId));

                SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ARCHIVED);
                SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE);
                SyncTouchpointUtil.mapDynamicTreeNodes(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_WORKING);

                SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ARCHIVED, languagesForSync, pgtnNoLongerUsed);
                SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_ACTIVE, languagesForSync, pgtnNoLongerUsed);
                SyncTouchpointUtil.syncContentObjectDataType(sourceDocument, targetDocument, sourceModel, targetModel, ContentObject.DATA_TYPE_WORKING, languagesForSync, pgtnNoLongerUsed);

                SyncTouchpointUtil.cleanUnusedDynamicVariantPgtn(targetModel, pgtnNoLongerUsed);
            }

            if(sourceModel != null && targetModel != null) {
                long targetDocumentID = targetDocument.getId();
                ContentObject targetModelFinal = targetModel;
                CloneHelper.execInSaveSession(()->{
                    if(targetModelFinal.isGlobalContentObject()) {
                        Document targetDocumentFinal = Document.findById(targetDocumentID);
                        targetModelFinal.getVisibleDocuments().add(targetDocumentFinal);
                        targetModelFinal.save();
                    }
                });

                clonedOrSyncedModelMap.put(targetModel.getId(), sourceModel.getId());
            }

            doHibernateUtilFlush();
            doHibernateUtilClear();
        }
    }

    private void associateDocuments(ContentObject targetContentObject, List<Long> documentIDs, Document targetDocument) {
    	CloneHelper.execInSaveSession(()->{
    		Set<Long> targetContentObjectDocumentIDs = targetContentObject.getDocuments().stream().map(d->d.getId()).collect(Collectors.toSet());
    		for(Long documentId : documentIDs) {
    			if(! targetContentObjectDocumentIDs.contains(documentId)) {
    				Document document = Document.findById(documentId);
    				if(document != null) {
    					targetContentObject.getVisibleDocuments().add(document);
    				}
                    targetContentObjectDocumentIDs.add(documentId);
    			}
    		}

    		if(! targetContentObjectDocumentIDs.contains(targetDocument.getId())) {
				Document document = Document.findById(targetDocument.getId());
				if(document != null) {
                    targetContentObject.getVisibleDocuments().add(document);
				}
                targetContentObjectDocumentIDs.add(targetDocument.getId());
    		}

            Document.findAllRemovedOrTrash().forEach(
                deletedDocument -> targetContentObject.getVisibleDocuments().remove(deletedDocument)
            );

            targetContentObject.save();
    	});
    }

    // End of syncEmbeddedContents functions

    private void checkZoneGlobalObjects(GlobalContentObjectVersioningServiceRequest request, ServiceResponse response, int startPercentage, int totalPercentage) throws Exception {
        User requestor = request.getRequestor();
        Document projectDocument = CloneHelper.queryInSaveSession(()->{
            Document document = Document.findById(request.getClonedDocumentId());
            return document;
        });
        Document otherDocument = Document.findById(request.getOriginDocumentId());

        boolean syncFromOther = request.isSyncUpdateDocument();

        Document sourceDocument = syncFromOther ? otherDocument : projectDocument;
        Document targetDocument = syncFromOther ? projectDocument : otherDocument;

        StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();

        Set<Long> languagesForSync = request.getLanguagesForSync();

        Set<Zone> targetZones = CloneHelper.queryInSaveSession(()->targetDocument.getZones());
        int total = targetZones.size();
        int count = 0;
        for(Zone targetZone : targetZones) {
            String zoneDna = CloneHelper.queryInSaveSession(()->targetZone.getDna());
            Zone sourceZone = Zone.findByDnaAndDocument(zoneDna, sourceDocument);
            if(sourceZone != null) {
                {
                    ContentObject sourceDefaultCommunicationTemplateSmartText = sourceZone.getDefaultCommunicationTemplateSmartText();
                    if(sourceDefaultCommunicationTemplateSmartText != null) {
                        String smartTextDna = sourceDefaultCommunicationTemplateSmartText.getDna();
                        ContentObject targetDefaultCommunicationTemplateSmartText = CloneHelper.queryInSaveSession(() -> ContentObject.findGlobalObjectByDna(smartTextDna));
                        CloneHelper.execInSaveSession(() -> targetZone.setDefaultCommunicationTemplateSmartText(targetDefaultCommunicationTemplateSmartText));
                    }
                }

                {
                    Set<ContentObject> sourceEmbeddedContentAssets = sourceZone.getSmartTextAssets();
                    for (ContentObject sourceModel : sourceEmbeddedContentAssets) {
                        String smartTextDna = sourceModel.getDna();
                        ContentObject targetModel = CloneHelper.queryInSaveSession(() -> ContentObject.findGlobalObjectByDna(smartTextDna));
                        if(targetModel != null) {
                            CloneHelper.execInSaveSession(() -> {
                                if(! targetZone.getSmartTextAssets().stream().anyMatch(co->co.getId() == targetModel.getId())) {
                                    targetZone.getSmartTextAssets().add(targetModel);
                                }
                            });
                        }
                    }
                }

                {
                    ContentObject sourceDefaultCommunicationTemplateImage = sourceZone.getDefaultCommunicationTemplateImage();
                    if(sourceDefaultCommunicationTemplateImage != null) {
                        String imageDna = sourceDefaultCommunicationTemplateImage.getDna();
                        ContentObject targetDefaultCommunicationTemplateImage = CloneHelper.queryInSaveSession(()->ContentObject.findGlobalObjectByDna(imageDna));
                        CloneHelper.execInSaveSession(()->targetZone.setDefaultCommunicationTemplateImage(targetDefaultCommunicationTemplateImage));
                    }
                }

                {
                    Set<ContentObject> sourceImageAssets = sourceZone.getImageAssets();
                    for(ContentObject sourceModel : sourceImageAssets) {
                        String imageDna = sourceModel.getDna();
                        ContentObject targetModel = CloneHelper.queryInSaveSession(()->ContentObject.findGlobalObjectByDna(imageDna));
                        if(targetModel != null) {
                            CloneHelper.execInSaveSession(() -> {
                                if(! targetZone.getImageAssets().stream().anyMatch(co->co.getId() == targetModel.getId())) {
                                    targetZone.getImageAssets().add(targetModel);
                                }
                            });
                        }
                    }
                }
            }
            count ++;
            int progress = startPercentage + (count * totalPercentage) / total;
            statusPollingBackgroundTask.setProgressInPercentInThread(progress);
            statusPollingBackgroundTask.save();
        }

        doHibernateUtilFlush();
    }

    private void swapContentIfNotSwappedYet(GlobalContentObjectVersioningServiceRequest request, ServiceResponse response, int startPercentage, int totalPercentage) throws Exception {
        User requestor = request.getRequestor();

        Document projectDocument = CloneHelper.queryInSaveSession(()->{
            Document document = Document.findById(request.getClonedDocumentId());
            return document;
        });
        Document otherDocument =   Document.findById(request.getOriginDocumentId());

        boolean syncFromOther = request.isSyncUpdateDocument();

        Document sourceDocument = syncFromOther ? otherDocument : projectDocument;
        Document targetDocument = syncFromOther ? projectDocument : otherDocument;

        StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();

        Map<Long, Long> objectIDsMap = CloneHelper.getClonedIDsMap();

        CloneHelper.execInSaveSession(()->{
            Set<Long> contentIDs = CloneHelper.getContentIDsForSwap();
            int total = contentIDs.size();
            if(total == 0) return;
            int count = 0;
            for(Long contentId : contentIDs) {
                Content content = Content.findById(contentId);
                if(content != null) {
                    content.swapCrossSchemaObjectsRef(objectIDsMap);
                    content.setNeedRecalculateHash(false);
                    content.save();
                }
                count ++;
                int progress = startPercentage + (count * totalPercentage) / total;
                statusPollingBackgroundTask.setProgressInPercentInThread(progress);
                statusPollingBackgroundTask.save();
            }
        });

        doHibernateUtilFlush();
        doHibernateUtilClear();
    }

    private void swapComplexValueIfNotSwappedYet(GlobalContentObjectVersioningServiceRequest request, ServiceResponse response, int startPercentage, int totalPercentage) throws Exception {
        User requestor = request.getRequestor();
        Document projectDocument = CloneHelper.queryInSaveSession(()->{
            Document document = Document.findById(request.getClonedDocumentId());
            return document;
        });
        Document otherDocument =   Document.findById(request.getOriginDocumentId());

        boolean syncFromOther = request.isSyncUpdateDocument();

        Document sourceDocument = syncFromOther ? otherDocument : projectDocument;
        Document targetDocument = syncFromOther ? projectDocument : otherDocument;

        StatusPollingBackgroundTask statusPollingBackgroundTask = request.getStatusPollingBackgroundTask();

        Map<Long, Long> objectIDsMap = CloneHelper.getClonedIDsMap();

        CloneHelper.execInSaveSession(()->{
            Set<Long> complexValueIDs = CloneHelper.getComplexValueIDsForSwap();
            int total = complexValueIDs.size();
            if(total > 0) {
	            int count = 0;
	            for(Long complexValueId : complexValueIDs) {
                    ComplexValue complexValue = ComplexValue.findById(complexValueId);
                    if(complexValue != null) {
                        complexValue.swapCrossSchemaObjectsRef(objectIDsMap);
                        complexValue.save();
                    }
	                count ++;
	                int progress = startPercentage + (count * totalPercentage) / total;
	                statusPollingBackgroundTask.setProgressInPercentInThread(progress);
	            }
            }
            List<DataElementVariable> allDocVisibleDevs = DataElementVariable.findAllForDocument(targetDocument);
            for(DataElementVariable dev : allDocVisibleDevs) {
            	if(dev.isExpressionVariable()) {
            		ComplexValue cv = dev.getExpression();
            		if(cv != null) {
	            		String sqlExpression = DataElementVariable.calculateSQLExpression(dev);
	            		dev.setSqlExpression(sqlExpression);
	            		dev.save();
            		}
            	}
            }
        });

        doHibernateUtilFlush();
        doHibernateUtilClear();
    }

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContextForCloneContentObjects(Long originDocumentId, Long clonedDocumentId, boolean syncUpdateDocument, boolean forceSync, boolean logDifferences, Set<Long> languaguesForSync, Map<Long, Long> objectMap, Set<Long> needAssociates, StatusPollingBackgroundTask statusPollingBackgroundTask, User requestor, String sourceSchema) {
		return createContext(ACTION_DUPLICATE_CONTENT_OBJECT, originDocumentId, clonedDocumentId, syncUpdateDocument, forceSync, logDifferences, false, languaguesForSync, objectMap, needAssociates, statusPollingBackgroundTask, requestor, sourceSchema);
	}

	public static ServiceExecutionContext createContextForSyncContentObjects(Long originDocumentId, Long clonedDocumentId, boolean syncUpdateDocument, boolean forceSync, boolean logDifferences, boolean hideUntilNextChange, Set<Long> languaguesForSync, Map<Long, Long> objectMap, Set<Long> needAssociates, StatusPollingBackgroundTask statusPollingBackgroundTask, User requestor, String sourceSchema) {
		return createContext(ACTION_SYNC_CONTENT_OBJECT, originDocumentId, clonedDocumentId, syncUpdateDocument, forceSync, logDifferences, hideUntilNextChange, languaguesForSync, objectMap, needAssociates, statusPollingBackgroundTask, requestor, sourceSchema);
	}

	public static ServiceExecutionContext createContext(int actionId,
														Long originDocumentId,
														Long clonedDocumentId,
														boolean syncUpdateDocument,
														boolean forceSync,
														boolean logDifferences,
														boolean hideUntilNextChange,
														Set<Long> languagesForSync,
														Map<Long, Long> objectMap,
														Set<Long> needAssociates,
														StatusPollingBackgroundTask statusPollingBackgroundTask,
														User requestor, String sourceSchema) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

        GlobalContentObjectVersioningServiceRequest request = new GlobalContentObjectVersioningServiceRequest();
		context.setRequest(request);

		request.setAction(actionId);
		request.setOriginDocumentId(originDocumentId);
		request.setClonedDocumentId(clonedDocumentId);
		request.setSyncUpdateDocument(syncUpdateDocument);
		request.setForceSync(forceSync);
		request.setLogDifferences(logDifferences);
		request.setHideUntilNextChange(hideUntilNextChange);
		request.setLanguagesForSync(languagesForSync);
		request.setGlobalContentObjectMap(objectMap);
		request.setNeedAssociates(needAssociates);
		request.setStatusPollingBackgroundTask(statusPollingBackgroundTask);
		request.setRequestor(requestor);
		request.setSourceSchema(sourceSchema);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
