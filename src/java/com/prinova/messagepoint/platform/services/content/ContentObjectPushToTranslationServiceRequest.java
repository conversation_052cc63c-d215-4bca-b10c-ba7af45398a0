package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.MessagepointObjectImportReport;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.Set;

public class ContentObjectPushToTranslationServiceRequest implements ServiceRequest {
    private ContentObject contentObject;
    private Set<ContentObjectPushToTranslationService.TranslationInaccuracy> translationInaccuracies;
    private User requestor;
    private MessagepointObjectImportReport report;

    public ContentObject getContentObject() {
        return contentObject;
    }

    public void setContentObject(ContentObject contentObject) {
        this.contentObject = contentObject;
    }

    public User getRequestor() {
        return requestor;
    }

    public void setRequestor(User requestor) {
        this.requestor = requestor;
    }

    public MessagepointObjectImportReport getReport() {
        return report;
    }
    public void setReport(MessagepointObjectImportReport report) {
        this.report = report;
    }

    public Set<ContentObjectPushToTranslationService.TranslationInaccuracy> getTranslationInaccuracies() {
        return translationInaccuracies;
    }

    public void setTranslationInaccuracies(Set<ContentObjectPushToTranslationService.TranslationInaccuracy> translationInaccuracies) {
        this.translationInaccuracies = translationInaccuracies;
    }
}
