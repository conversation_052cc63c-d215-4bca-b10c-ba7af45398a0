package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.ContentTargeting;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.migrate.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.util.HashSet;
import java.util.Set;

public class CreateHistoricalContentObjectAssociationService extends AbstractService {

	private static final Log log = LogUtil.getLog(CreateHistoricalContentObjectAssociationService.class);
	public static final String SERVICE_NAME = "content.CreateHistoricalContentObjectAssociationService";
	
	@Override
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateHistoricalContentObjectAssociationServiceRequest request = (CreateHistoricalContentObjectAssociationServiceRequest)context.getRequest();
			
			ContentObjectAssociation ca = ContentObjectAssociation.findById(request.getContentObjectAssociation().getId());
			int contentActionTypeId = request.getContentActionTypeId();
			if(ca != null && ca.getContentObject() != null){
                ContentObjectData cod = ca.getContentObject().getLatestContentObjectDataWorkingCentric();
                if(cod == null) {
                    cod = ca.getContentObject().getContentObjectData(ContentObject.DATA_TYPE_ARCHIVED);
                }
                if(cod != null) {
                    HistoricalContentObjectAssociation hca = new HistoricalContentObjectAssociation();
                    hca.setTypeId(ca.getTypeId());
                    hca.setContentObject(ca.getContentObject());
                    hca.setDataType(ca.getDataType());
                    if(cod != null) {
                        hca.setContentObjectDataGuid(cod.getGuid());
                    }
                    hca.setContentObjectPGTreeNode(ca.getContentObjectPGTreeNode());
                    hca.setTouchpointPGTreeNode(ca.getTouchpointPGTreeNode());
                    hca.setReferencingContentObjectPGTreeNode(ca.getReferencingContentObjectPGTreeNode());
                    hca.setReferencingTouchpointPGTreeNode(ca.getReferencingTouchpointPGTreeNode());
                    hca.setReferencingImageLibrary(ca.getReferencingImageLibrary());
                    hca.setMessagepointLocale(ca.getMessagepointLocale());
                    hca.setCreatedBy(ca.getCreatedBy());
                    hca.setUpdatedBy(ca.getUpdatedBy());
                    hca.setZonePart(ca.getZonePart());
                    hca.setActionTypeId(contentActionTypeId);

                    Content c = ca.getContent();
                    if(c != null) {
                        HistoricalContent hc = new HistoricalContent();
                        hc.save(true);
                        hc.setSha256Hash(c.getHashSafe());
                        hc.setUnformattedText(c.getUnformattedText());
                        hc.setEncodedContent(c.getEncodedContent());
                        hc.setActionTypeId(contentActionTypeId);

                        // Variables
                        if(c.getVariables() != null){
                            Set<DataElementVariable> vs = new HashSet<>(c.getVariables());
                            hc.setVariables(vs);
                        }
                        // Text styles
                        if(c.getTextStyles() != null){
                            Set<TextStyle> ts = new HashSet<>(c.getTextStyles());
                            hc.setTextStyles(ts);
                        }
                        // Paragraph styles
                        if(c.getParagraphStyles() != null){
                            Set<ParagraphStyle> ps = new HashSet<>(c.getParagraphStyles());
                            hc.setParagraphStyles(ps);
                        }
                        // Images
                        if(c.getImages() != null){
                            Set<DatabaseFile> ds = new HashSet<>(c.getImages());
                            hc.setImages(ds);
                        }

                        hc.setImageLink(Content.copyImageLinkFrom(c.getImageLink()));
                        hc.setImageAltText(Content.copyImageAltTextFrom(c.getImageAltText()));
                        hc.setImageExtLink(Content.copyImageExtLinkFrom(c.getImageExtLink()));
                        hc.setImageExtPath(Content.copyImageExtPathFrom(c.getImageExtPath()));

                        if(c.getImageLocation() != null && !c.getImageLocation().isEmpty()){
                            String hisImageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/history/" + hc.getGuid() + "/" + hca.getMessagepointLocale().getLanguageCode() + "/" + c.getImageName();

                            File hisImage = new File(hisImageLocation);
                            if(!hisImage.getParentFile().exists())
                                hisImage.getParentFile().mkdirs();
                            // Copy the file
                            File image = new File(c.getImageLocation());
                            if(!image.isDirectory())
                                FileUtil.copy(image, hisImage);
                            hc.setImageLocation(hisImageLocation);
                        }

                        if(c.getContentTargeting() != null){
                            org.jsoup.nodes.Document historicalDocument = Jsoup.parse(hc.getEncodedContent() != null ? hc.getEncodedContent() : "");
                            org.jsoup.nodes.Document originalDocument = Jsoup.parse(c.getEncodedContent() != null ? c.getEncodedContent() : "");
                            Set<ContentTargeting> clonedTargetingsForHistory = new HashSet<>();
                            Set<ContentTargeting> clonedTargetings = new HashSet<>();
                            for (ContentTargeting currentItem : c.getContentTargeting()) {
                                if(currentItem.isContentTargetingFromHistory()){
                                    // Check if the content targeting is from history (Revert the content from the history), if so, clone it again
                                    ContentTargeting clonedTargeting = (ContentTargeting) currentItem.clone();
                                    clonedTargeting.save();
                                    clonedTargetings.add(clonedTargeting);
                                    this.swapContentTargetingIds(originalDocument, currentItem.getId(), clonedTargeting.getId());
                                }
                                ContentTargeting clonedTargetingForHistory = (ContentTargeting) currentItem.clone();
                                clonedTargetingForHistory.save();
                                clonedTargetingsForHistory.add(clonedTargetingForHistory);
                                this.swapContentTargetingIds(historicalDocument, currentItem.getId(), clonedTargetingForHistory.getId());
                            }
                            if(!clonedTargetings.isEmpty()){
                                c.setContentTargeting(clonedTargetings);
                                c.setEncodedContent(ContentObjectContentUtil.formatBody(originalDocument));
                                c.save();
                            }
                            hc.setContentTargeting(clonedTargetingsForHistory);
                            hc.setEncodedContent(ContentObjectContentUtil.formatBody(historicalDocument));
                        }
                        if(c.getPlaceholders() != null){
                            Set<Zone> pl = new HashSet<>(c.getPlaceholders());
                            hc.setPlaceholders(pl);
                        }

                        // Content Objects
                        if(!c.getContentObjectsTypeMap().isEmpty()) {
                            hc.getContentObjectsTypeMap().clear();
                            hc.getContentObjectsTypeMap().putAll(c.getContentObjectsTypeMap());
                        }

                        hc.setImageName(c.getImageName());
                        hc.setImageUploadedDate(c.getImageUploadedDate());
                        hc.setCreatedBy(c.getCreatedBy());
                        hc.setUpdatedBy(c.getUpdatedBy());
                        hc.save(false);
                        hca.setContent(hc);
                    }

                    hca.save(true);
                    getResponse(context).setResultValueBean(hca.getId());
                }
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in CreateHistoricalContentObjectAssociationService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

    private void swapContentTargetingIds(org.jsoup.nodes.Document document, long fromContentTargetingId, long toContentTargetingId) {
        Elements targetSwapTags = document.select("[content_targeting_id=\"" + fromContentTargetingId + "\"],[selected_targeting_id=\"" + fromContentTargetingId + "\"]");
        for (Element currentElement : targetSwapTags) {
            if ( currentElement.hasAttr("content_targeting_id") )
                currentElement.attr("content_targeting_id", String.valueOf(toContentTargetingId));
            else if ( currentElement.hasAttr("selected_targeting_id") )
                currentElement.attr("selected_targeting_id", String.valueOf(toContentTargetingId));
        }
    }

	@Override
	public void validate(ServiceExecutionContext context) {
	}

	public static ServiceExecutionContext createContext(ContentObjectAssociation contentObjectAssociation, int contentActionTypeId) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateHistoricalContentObjectAssociationServiceRequest request = new CreateHistoricalContentObjectAssociationServiceRequest();
		context.setRequest(request);
		
		request.setContentObjectAssociation(contentObjectAssociation);
		request.setContentActionTypeId(contentActionTypeId);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}	

}
