package com.prinova.messagepoint.platform.services.content;

import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class BulkContentObjectActionServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = 1937771378179610623L;
	private List<ContentObject> contentObjects;
	private User requestor;
	private User assignToUser;
	private String userNote;
	private Map<Long, Long> taskLinkedMap;
	private Map<Long, String> taskDescriptionMap;
	private Map<Long, Long> taskLocaleMap;
	private Map<Long, Integer> taskTypeMap;
	private Map<Long, Long> taskVariantMap;

	public List<ContentObject> getContentObjects() {
		return contentObjects;
	}

	public void setContentObjects(List<ContentObject> contentObjects) {
		this.contentObjects = contentObjects;
	}

	public User getRequestor() {
		return requestor;
	}

	public void setRequestor(User requestor) {
		this.requestor = requestor;
	}

	public User getAssignToUser() {
		return assignToUser;
	}

	public void setAssignToUser(User assignToUser) {
		this.assignToUser = assignToUser;
	}

	public String getUserNote() {
		return userNote;
	}

	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public Map<Long, Long> getTaskLinkedMap() {
		return taskLinkedMap;
	}

	public void setTaskLinkedMap(Map<Long, Long> taskLinkedMap) {
		this.taskLinkedMap = taskLinkedMap;
	}

	public Map<Long, String> getTaskDescriptionMap() {
		return taskDescriptionMap;
	}

	public void setTaskDescriptionMap(Map<Long, String> taskDescriptionMap) {
		this.taskDescriptionMap = taskDescriptionMap;
	}
	public Map<Long, Long> getTaskLocaleMap() {
		return taskLocaleMap;
	}

	public void setTaskLocaleMap(Map<Long, Long> taskLocaleMap) {
		this.taskLocaleMap = taskLocaleMap;
	}

	public Map<Long, Integer> getTaskTypeMap() {
		return taskTypeMap;
	}

	public void setTaskTypeMap(Map<Long, Integer> taskTypeMap) {
		this.taskTypeMap = taskTypeMap;
	}

	public Map<Long, Long> getTaskVariantMap() {
		return taskVariantMap;
	}

	public void setTaskVariantMap(Map<Long, Long> taskVariantMap) {
		this.taskVariantMap = taskVariantMap;
	}
}
