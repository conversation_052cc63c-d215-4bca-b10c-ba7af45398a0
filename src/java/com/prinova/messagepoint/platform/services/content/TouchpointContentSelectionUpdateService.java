package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionEditWrapper;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO.MultipartContentZoneVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO.ZonePartVO;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.admin.SubContentType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.tpselection.TouchpointVariantStatusManager;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * TouchpointContentSelectionUpdateService
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class TouchpointContentSelectionUpdateService extends AbstractService {

	public static final String SERVICE_NAME = "content.TouchpointContentSelectionUpdateService";
	private static final Log log = LogUtil.getLog(TouchpointContentSelectionUpdateService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			TouchpointContentSelectionUpdateServiceRequest request = (TouchpointContentSelectionUpdateServiceRequest) context.getRequest();
			TouchpointContentObjectContentSelection contentSelection = request.getContentSelection();

			ParameterGroupTreeNode referencingTreeNode = request.getRefereningTreeNode();
			if (referencingTreeNode != null && referencingTreeNode.getParentNode() == null)
				referencingTreeNode = null;

			ContentObject msgInst = contentSelection.getContentObject();

			if (msgInst.getOwningTouchpointSelection() != null && msgInst.getOwningTouchpointSelection().getParameterGroupTreeNode() == referencingTreeNode)
				referencingTreeNode = null;

			boolean updateContentObject = update(contentSelection, request.getContentVO(), request.getMpContentVO(), request.getContentType(), request.getRefereningTreeNode(), request.getUser(), request.getUpdateVersionControl(), request.isAbortWIPAction());

			HibernateUtil.getManager().getSession().refresh(msgInst);
			// SHARED CANVAS
			if ( (msgInst.getIsSharedFreeform() || (msgInst.getIsTouchpointLocal() && msgInst.isSupportsTables()))  && request.getCanvasMaxWidth() != null && request.getCanvasTrimWidth() != null ) {
				msgInst.setCanvasMaxWidth( DecimalValueUtil.hydrate(request.getCanvasMaxWidth()) );
				msgInst.setCanvasMaxHeight( DecimalValueUtil.hydrate(request.getCanvasMaxHeight()) );
				msgInst.setCanvasTrimWidth( DecimalValueUtil.hydrate(request.getCanvasTrimWidth()) );
				msgInst.setCanvasTrimHeight( DecimalValueUtil.hydrate(request.getCanvasTrimHeight()) );
			}
			
			if (updateContentObject && msgInst != null) {
				msgInst.save();
			}
			
			// GRAPHIC: Set graphicContentType if unset
			if ( request.getContentType().getId() == ContentAssociationType.ID_OWNS && 
				 msgInst.getContentType().getId() == ContentType.GRAPHIC &&
				 (msgInst.getGraphicTypeId() == null || msgInst.getGraphicTypeId() == 0) &&
				 !request.getContentVO().getLangContentMap().isEmpty() ) {

				Map<Long, ContentVO> contentMap = request.getContentVO().getLangContentMap();
				for ( Long lang : contentMap.keySet() ) {
					ContentVO vo = contentMap.get(lang);
					if ( vo.getFile() != null && vo.getFile().getOriginalFilename() != null ) {
						SubContentType subContentType = ContentObjectContentUtil.getSubContentTypeFromFilename(vo.getFile().getOriginalFilename());
						if ( subContentType != null && (msgInst.getGraphicTypeId() == null || msgInst.getGraphicTypeId() == 0) ) {
							msgInst.setGraphicTypeId(Integer.valueOf(String.valueOf(subContentType.getId())));
							msgInst.save();
							break;
						}
					}
				}
			}

            contentSelection.getTouchpointSelection().save();
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in " + SERVICE_NAME + ".execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context), ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private boolean update(TouchpointContentObjectContentSelection contentSelection, ContentObjectContentSelectionVO contentVO, ContentObjectMultipartContentVO mpContentVO, ContentAssociationType newContentType, ParameterGroupTreeNode referencingTreeNode, User user, boolean updateVersionControl, boolean isAbortWIP) throws Exception {
		ContentObjectAssociation contentAssoc = contentSelection.getContentObjectAssociation();

		if (contentAssoc.getDataType() != 1)
		{
			contentAssoc = ContentObjectAssociation.findByContentObjectAndParameters(contentAssoc.getContentObject(), ContentObject.DATA_TYPE_WORKING, contentAssoc.getTouchpointPGTreeNode(), contentAssoc.getMessagepointLocale(), contentAssoc.getZonePart());
		}

		ContentAssociationType oldContAssocType = new ContentAssociationType(contentAssoc.getTypeId());
		ContentType contentType = contentSelection.getContentObject().getContentType();
		boolean ownedContentDataChanged = false;
		boolean updateContentObject = false;
		
		List<MessagepointLocale> locales = contentSelection.getTouchpointSelection().getDocument().getTouchpointLanguagesAsLocales();

		/**
		 * STEP #1: Update content data and content associations. Keep track a
		 * list of affected tree nodes. Keep track of if owned content data has
		 * been changed.
		 */
		//
		// 1-Default:
		// Content type change: Empty ==> Suppress
		// - Self association change: Type = suppress; Content = null;
		// /
		if (oldContAssocType.getId() == ContentAssociationType.ID_EMPTY && newContentType.getId() == ContentAssociationType.ID_SUPPRESSES) {
			updateContentObject = true;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			List<ContentObjectAssociation> assocs = ContentObjectAssociation.findAllByContentObjectAndParameters(contentAssoc.getContentObject(), ContentObject.DATA_TYPE_WORKING, contentAssoc.getTouchpointPGTreeNode(), null, null, false, false, true, true, false);
			for (ContentObjectAssociation ca : assocs) {
				Content content = ca.getContent();
				if (content != null && ca.getId() > 0) {
					content.deleteContentSafe(true);
				}
				ca.setContent(null);
				ca.setTypeId(newContentType.getId());
				ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
				ca.save();
			}
		}

		// 2-Default:
		// Content type change: Empty ==> Own
		// - Create new master content.
		// - Change self association to Own this new content
		//
		if (oldContAssocType.getId() == ContentAssociationType.ID_EMPTY && newContentType.getId() == ContentAssociationType.ID_OWNS) {
			ownedContentDataChanged = true;
			updateContentObject = true;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			if (contentType.getId() == ContentType.MULTIPART) {
				MultipartContentZoneVO mvo = mpContentVO.getZoneVO();
				ArrayList<ZonePartVO> parts = mvo.getParts();
				for (ZonePartVO zonePartVO : parts) {
					Map<Long, ContentVO> multipartContents = zonePartVO.getLanguageContentVOs();
					ZonePart zonePart = zonePartVO.getZonePart();

					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation existingCa = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePart);
						existingCa.setTypeId(newContentType.getId());
						existingCa.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
						if(zonePartVO.isEmptyZonePart()) {
                            existingCa.setTypeId(ContentAssociationType.ID_EMPTY);
                            existingCa.setReferencingImageLibrary(null);
                            existingCa.setContent(null);
                            existingCa.setReferencingTouchpointPGTreeNode(null);
                        } else {
							ContentVO vo = multipartContents.get(locale.getId());
							if (vo != null) {
								if (vo.isSameAsDefault()) {
									existingCa.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
									existingCa.setReferencingImageLibrary(null);
									existingCa.setContent(null);
									existingCa.setReferencingTouchpointPGTreeNode(null);
								} else {
									Content content = new Content();
									content.setContent(vo.getContent());
									content.setImageName(vo.getImageName());
									content.setImageLocation(vo.getImageLocation());
									if (zonePartVO.getZonePart().getContentType().getId() == ContentType.GRAPHIC) {
										content.setImageUploadedDate(vo.getImageUploadedDate());
										content.setAppliedImageFilename(vo.getAppliedImageFilename());
										content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
										content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
										content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
										content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
										content.setAssetId(vo.getAssetId());
										content.setAssetSite(vo.getAssetSite());
										content.setAssetURL(vo.getAssetURL());
										content.setAssetLastUpdate(vo.getAssetLastUpdate());
										content.setAssetLastSync(vo.getAssetLastSync());
										saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
									}

									// Content Library Reference
									if (vo.isUseImageLibrary()) {
										ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
										ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
										if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
											clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
										}
										if (imageLibrary != null)
											existingCa.setContent(null);
										else
											existingCa.setContent(clCa != null ? clCa.getContent() : null);
										existingCa.setReferencingImageLibrary(imageLibrary);
										existingCa.setTypeId(ContentAssociationType.ID_OWNS);
									} else {
										content.save();
										existingCa.setContent(content);
										existingCa.setReferencingImageLibrary(null);
									}
								}
							}
						}
						existingCa.save();
					}
				}
			} else {
				if (!contentVO.getLangContentMap().isEmpty()) {
					Map<Long, ContentVO> contentMap = contentVO.getLangContentMap();
					Map<Long, ContentObjectAssociation> associationHolder = new HashMap<>();

					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, null);
						ContentVO vo = contentMap.get(locale.getId());

						if (vo.isSameAsDefault())
						{
							ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
							ca.setReferencingImageLibrary(null);
							ca.setContent(null);
							ca.setReferencingTouchpointPGTreeNode(null);
						}
						else {
							Content content = new Content();
							content.setContent(vo.getContent());
							content.setImageName(vo.getImageName());
							content.setImageLocation(vo.getImageLocation());
							if (contentType.getId() == ContentType.GRAPHIC) {
								content.setImageUploadedDate(vo.getImageUploadedDate());
								content.setAppliedImageFilename(vo.getAppliedImageFilename());
								content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
								content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
								content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
								content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
								content.setAssetId(vo.getAssetId());
								content.setAssetSite(vo.getAssetSite());
								content.setAssetURL(vo.getAssetURL());
								content.setAssetLastUpdate(vo.getAssetLastUpdate());
								content.setAssetLastSync(vo.getAssetLastSync());
								saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
							}

							// Content Library Reference
							if (vo.isUseImageLibrary()) {
								ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
								ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
								if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
									clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
								}
								if (imageLibrary != null)
									ca.setContent(null);
								else
									ca.setContent(clCa != null ? clCa.getContent() : null);
								ca.setReferencingImageLibrary(imageLibrary);
								ca.setTypeId(ContentAssociationType.ID_OWNS);
							} else {
								content.save();
								ca.setContent(content);
								ca.setTypeId(newContentType.getId());
								ca.setReferencingImageLibrary(null);
							}
							ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
						}

						ca.save();

						associationHolder.put(locale.getId(), ca);
					}
				}
			}
		}

		//
		// 3-Content type change: Same as ==> Suppress
		// - Self association change: Type = suppress; Content = null;
		//
		if (oldContAssocType.getId() == ContentAssociationType.ID_REFERENCES && newContentType.getId() == ContentAssociationType.ID_SUPPRESSES) {
			updateContentObject = true;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			Set<Long> processedLocales = new HashSet<>();
            List<ContentObjectAssociation> assocs = ContentObjectAssociation.findAllByContentObjectAndParameters(contentAssoc.getContentObject(), ContentObject.DATA_TYPE_WORKING, contentAssoc.getTouchpointPGTreeNode(), null, null, false, false, true, true, false);
			for (ContentObjectAssociation ca : assocs) {
				ca.setContent(null);
				ca.setTypeId(newContentType.getId());
				ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
				ca.setReferencingImageLibrary(null);
				ca.save();

				if (ca.getMessagepointLocale() != null)
					processedLocales.add(ca.getMessagepointLocale().getId());
			}

			for (MessagepointLocale locale : locales) {
				if (!processedLocales.contains(locale.getId()))
				{

                    if (contentType.getId() == ContentType.MULTIPART) {
                        if (mpContentVO.getZoneVO() != null) {
                            MultipartContentZoneVO multipartContentZoneVO = mpContentVO.getZoneVO();
                            for (ZonePartVO zonePartVO : multipartContentZoneVO.getParts())
                            {
                                ContentObjectAssociation ca = new ContentObjectAssociation();
                                ca.setTypeId(newContentType.getId());
                                ca.setContentObject(contentAssoc.getContentObject());
                                ca.setDataType(contentAssoc.getDataType());
                                ca.setTouchpointPGTreeNode(contentAssoc.getTouchpointPGTreeNode());
                                ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
                                ca.setMessagepointLocale(locale);
                                ca.setReferencingImageLibrary(null);
                                ca.setZonePart(zonePartVO.getZonePart());
                                ca.save();

                                if (ca.getMessagepointLocale() != null)
                                    processedLocales.add(ca.getMessagepointLocale().getId());
                            }
                        }
                    } else {
                        ContentObjectAssociation ca = new ContentObjectAssociation();
                        ca.setTypeId(newContentType.getId());
                        ca.setContentObject(contentAssoc.getContentObject());
                        ca.setDataType(contentAssoc.getDataType());
                        ca.setTouchpointPGTreeNode(contentAssoc.getTouchpointPGTreeNode());
                        ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
                        ca.setMessagepointLocale(locale);
                        ca.setReferencingImageLibrary(null);
                        ca.save();

                        if (ca.getMessagepointLocale() != null)
                            processedLocales.add(ca.getMessagepointLocale().getId());
                    }
				}
			}
		}

		//
		// 4-Content type change: Same as ==> Own
		// - Create new content.
		// - Change self association to Own this new content
		//
		if (oldContAssocType.getId() == ContentAssociationType.ID_REFERENCES && newContentType.getId() == ContentAssociationType.ID_OWNS) {
			updateContentObject = true;

			// Save the previous referencing content for history auditing
			HistoricalContentObjectAssociation hca = HistoricalContentObjectAssociation.getMatchingHCA(contentAssoc);
			if(hca == null || HistoricalContentObjectAssociation.isContentChanged(hca, contentAssoc)){
				ContentObjectAssociation.createHistory(contentAssoc, HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT);
			}

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			Map<Long, ContentObjectAssociation> associationHolder = new HashMap<>();

			if (contentType.getId() == ContentType.MULTIPART) {
				if (mpContentVO.getZoneVO() != null) {
					MultipartContentZoneVO multipartContentZoneVO = mpContentVO.getZoneVO();
					for (ZonePartVO zonePartVO : multipartContentZoneVO.getParts()) {
						Map<Long, ContentVO> multipartContents = zonePartVO.getLanguageContentVOs();

						for (MessagepointLocale locale : locales) {
							ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePartVO.getZonePart());

							if(zonePartVO.isEmptyZonePart()) {
								ca.setTypeId(ContentAssociationType.ID_EMPTY);
								ca.setReferencingImageLibrary(null);
								ca.setContent(null);
								ca.setReferencingTouchpointPGTreeNode(null);
							} else {
								ContentVO vo = multipartContents.get(locale.getId());
								if (vo != null) {
									if (vo.isSameAsDefault()) {
										ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
										ca.setReferencingImageLibrary(null);
										ca.setContent(null);
										ca.setReferencingTouchpointPGTreeNode(null);
									} else {
										Content content = new Content();
										content.setContent(vo.getContent());
										content.setImageName(vo.getImageName());
										content.setImageLocation(vo.getImageLocation());
										if (zonePartVO.getZonePart().getContentType().getId() == ContentType.GRAPHIC) {
											content.setImageUploadedDate(vo.getImageUploadedDate());
											content.setAppliedImageFilename(vo.getAppliedImageFilename());
											content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
											content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
											content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
											content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
											content.setAssetId(vo.getAssetId());
											content.setAssetSite(vo.getAssetSite());
											content.setAssetURL(vo.getAssetURL());
											content.setAssetLastUpdate(vo.getAssetLastUpdate());
											content.setAssetLastSync(vo.getAssetLastSync());
											saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
										}

										// Content Library Reference
										if (vo.isUseImageLibrary()) {
											ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
											ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
											if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
												clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
											}
											if (imageLibrary != null)
												ca.setContent(null);
											else
												ca.setContent(clCa != null ? clCa.getContent() : null);
											ca.setReferencingImageLibrary(imageLibrary);
											ca.setTypeId(ContentAssociationType.ID_OWNS);
										} else {
											content.save();
											ca.setContent(content);
											ca.setReferencingImageLibrary(null);
										}
										ca.setTypeId(newContentType.getId());
										ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
									}
								}
							}
							ca.save();
						}
					}
				}
			} else {
				if (!contentVO.getLangContentMap().isEmpty()) {
					Map<Long, ContentVO> contentMap = contentVO.getLangContentMap();
					Map<Long, ContentObjectAssociation> assocs = contentAssoc.getTouchpointVariantContentObjectAssociations(locales);
					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation ca = assocs.get(locale.getId());
						ContentVO vo = contentMap.get(locale.getId());

						if (vo.isSameAsDefault())
						{
							ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
							ca.setReferencingImageLibrary(null);
							ca.setContent(null);
							ca.setReferencingTouchpointPGTreeNode(null);
						}
						else {
							Content content = new Content();
							content.setContent(vo.getContent());
							content.setImageName(vo.getImageName());
							content.setImageLocation(vo.getImageLocation());
							if (contentType.getId() == ContentType.GRAPHIC) {
								content.setImageUploadedDate(vo.getImageUploadedDate());
								content.setAppliedImageFilename(vo.getAppliedImageFilename());
								content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
								content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
								content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
								content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
								content.setAssetId(vo.getAssetId());
								content.setAssetSite(vo.getAssetSite());
								content.setAssetURL(vo.getAssetURL());
								content.setAssetLastUpdate(vo.getAssetLastUpdate());
								content.setAssetLastSync(vo.getAssetLastSync());
	//							if(vo.getFile() == null || vo.getFile().getSize() == 0) {
	//								content.setImageName(null);
	//								content.setImageLocation(null);
	//							}
								saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
							}

							// Content Library Reference
							if (vo.isUseImageLibrary()) {
								ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
								ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
								if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
									clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
								}
								if (imageLibrary != null)
									ca.setContent(null);
								else
									ca.setContent(clCa != null ? clCa.getContent() : null);
								ca.setReferencingImageLibrary(imageLibrary);
								ca.setTypeId(ContentAssociationType.ID_OWNS);
							} else {
								// Set the Target touchpoint to resolve the local smart text and local image references in content save
								if (CloneHelper.getTargetDocument() == null) {
									CloneHelper.setTargetDocument(ca.getDocument());
								}
								content.save();
								ca.setContent(content);
								ca.setReferencingImageLibrary(null);
								ca.setTypeId(newContentType.getId());
							}
							ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
						}

						ca.save();

						associationHolder.put(locale.getId(), ca);
					}
				}
			}
		}

		//
		// 6-Content type change: Suppress ==> Own
		// - Create new content.
		// - Change self association to Own this new content
		if (oldContAssocType.getId() == ContentAssociationType.ID_SUPPRESSES && newContentType.getId() == ContentAssociationType.ID_OWNS) {
			updateContentObject = true;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			if (contentType.getId() == ContentType.MULTIPART) {
				MultipartContentZoneVO multipartContentZoneVO = mpContentVO.getZoneVO();
				for (ZonePartVO zonePartVO : multipartContentZoneVO.getParts()) {
					Map<Long, ContentVO> multipartContents = zonePartVO.getLanguageContentVOs();

					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePartVO.getZonePart());

						if(zonePartVO.isEmptyZonePart()) {
                            ca.setTypeId(ContentAssociationType.ID_EMPTY);
                            ca.setReferencingImageLibrary(null);
                            ca.setContent(null);
                            ca.setReferencingTouchpointPGTreeNode(null);
                        } else {
							ContentVO vo = multipartContents.get(locale.getId());
							if (vo != null) {
								if (vo.isSameAsDefault()) {
									ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
									ca.setReferencingImageLibrary(null);
									ca.setContent(null);
									ca.setReferencingTouchpointPGTreeNode(null);
								} else {
									Content content = new Content();
									content.setContent(vo.getContent());
									content.setImageName(vo.getImageName());
									content.setImageLocation(vo.getImageLocation());
									if (zonePartVO.getZonePart().getContentType().getId() == ContentType.GRAPHIC) {
										content.setImageUploadedDate(vo.getImageUploadedDate());
										content.setAppliedImageFilename(vo.getAppliedImageFilename());
										content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
										content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
										content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
										content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
										content.setAssetId(vo.getAssetId());
										content.setAssetSite(vo.getAssetSite());
										content.setAssetURL(vo.getAssetURL());
										content.setAssetLastUpdate(vo.getAssetLastUpdate());
										content.setAssetLastSync(vo.getAssetLastSync());
										saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
									}

									// Content Library Reference
									if (vo.isUseImageLibrary()) {
										ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
										ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
										if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
											clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
										}
										ca.setContent(clCa != null ? clCa.getContent() : null);
										ca.setReferencingImageLibrary(imageLibrary);
										ca.setTypeId(ContentAssociationType.ID_OWNS);
									} else {
										// Set the Target touchpoint to resolve the local smart text and local image references in content save
										if (CloneHelper.getTargetDocument() == null) {
											CloneHelper.setTargetDocument(ca.getDocument());
										}
										content.save();
										ca.setContent(content);
										ca.setReferencingImageLibrary(null);
									}
									ca.setTypeId(newContentType.getId());
									ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
								}
							}
						}
						ca.setZonePart(zonePartVO.getZonePart());
						ca.save();
					}
				}

			} else {
				if (!contentVO.getLangContentMap().isEmpty()) {
					Map<Long, ContentVO> contentMap = contentVO.getLangContentMap();
					Map<Long, ContentObjectAssociation> associationHolder = new HashMap<>();

					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, null);
						ContentVO vo = contentMap.get(locale.getId());

						if (vo.isSameAsDefault())
						{
							ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
							ca.setReferencingImageLibrary(null);
							ca.setContent(null);
							ca.setReferencingTouchpointPGTreeNode(null);
						}
						else {
							Content content = new Content();
							content.setContent(vo.getContent());
							content.setImageName(vo.getImageName());
							content.setImageLocation(vo.getImageLocation());
							if (contentType.getId() == ContentType.GRAPHIC) {
								content.setImageUploadedDate(vo.getImageUploadedDate());
								content.setAppliedImageFilename(vo.getAppliedImageFilename());
								content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
								content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
								content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
								content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
								content.setAssetId(vo.getAssetId());
								content.setAssetSite(vo.getAssetSite());
								content.setAssetURL(vo.getAssetURL());
								content.setAssetLastUpdate(vo.getAssetLastUpdate());
								content.setAssetLastSync(vo.getAssetLastSync());
								saveAssociatedFiles(content, contentType, vo, true, locale.getLanguageCode(), isAbortWIP);
							}

							// Content Library Reference
							if (vo.isUseImageLibrary()) {
								ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
								ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
								if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
									clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
								}
								ca.setContent(clCa != null ? clCa.getContent() : null);
								ca.setReferencingImageLibrary(imageLibrary);
								ca.setTypeId(ContentAssociationType.ID_OWNS);
							} else {
								// Set the Target touchpoint to resolve the local smart text and local image references in content save
								if (CloneHelper.getTargetDocument() == null) {
									CloneHelper.setTargetDocument(ca.getDocument());
								}
								content.save();
								ca.setContent(content);
								ca.setReferencingImageLibrary(null);
							}
							ca.setTypeId(newContentType.getId());
							ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
						}

						ca.save();

						associationHolder.put(locale.getId(), ca);
					}
				}
			}
		}

		//
		// 7-Content type change: Any ==> "Same as" however not "Same as" to the same "Same as"
		// - Delete content
		// - Change self association to Same as referencing content.
		if (newContentType.getId() == ContentAssociationType.ID_REFERENCES /* && (oldContAssocType.getId() != ContentAssociationType.ID_REFERENCES || !referencingTreeNode.equals(contentAssoc.getReferencingTouchpointPGTreeNode())) */ ) {
			updateContentObject = true;

			// !! boolean referencingParent = false;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(referencingTreeNode);

			// !! if (contentAssoc.getTouchpointPGTreeNode() != null)
			// !!	referencingParent = contentAssoc.getTouchpointPGTreeNode().isChildOf(referencingTreeNode);

			if (contentType.getId() == ContentType.MULTIPART) {
				Zone zone = contentAssoc.getContentObject().getZone();
				for (ZonePart zonePart : zone.getPartsInOrder()) {
					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation existingCa = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePart);
						if (existingCa != null) {
							Content content = existingCa.getContent();
							if (existingCa.getReferencingImageLibrary() == null && content != null && existingCa.getId() > 0) {
								content.deleteContentSafe(true);
							}
							existingCa.setContent(null);
							existingCa.setReferencingImageLibrary(null);
							existingCa.setTypeId(newContentType.getId());
							existingCa.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
							existingCa.save();
						}
						else {
							ContentObjectAssociation ca = new ContentObjectAssociation();
							ca.setContentObject(contentAssoc.getContentObject());
							ca.setDataType(contentAssoc.getDataType());
							ca.setTouchpointPGTreeNode(contentAssoc.getTouchpointPGTreeNode());
							ca.setMessagepointLocale(locale);
							ca.setZonePart(zonePart);
							ca.setTypeId(newContentType.getId());
							ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
							ca.setReferencingImageLibrary(null);
							ca.setZonePart(zonePart);
							ca.save();
						}
					}
				}
			} else {
				Map<Long, ContentObjectAssociation> assocs = contentAssoc.getTouchpointVariantContentObjectAssociations(locales);
				for (MessagepointLocale locale : locales) {
					ContentObjectAssociation ca = assocs.get(locale.getId());
					if (ca != null) {
						Content content = ca.getContent();
						if (ca.getReferencingImageLibrary() == null && content != null) {
							if (ca.getId() > 0)
								content.deleteContentSafe(true);
						}
					}
					else {
						ca = new ContentObjectAssociation();
						ca.setContentObject(contentAssoc.getContentObject());
						ca.setDataType(contentAssoc.getDataType());
						ca.setTouchpointPGTreeNode(contentAssoc.getTouchpointPGTreeNode());
						ca.setMessagepointLocale(locale);
					}

					ca.setContent(null);
					ca.setReferencingImageLibrary(null);
					ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
					ca.setTypeId(newContentType.getId());
					ca.save();
				}
			}

		}

		//
		// 8-Content type change: Own ==> Suppress
		// - Delete content
		// - Self association change: Type = suppress; Content = null;
		//
		if (oldContAssocType.getId() == ContentAssociationType.ID_OWNS && newContentType.getId() == ContentAssociationType.ID_SUPPRESSES) {
			updateContentObject = true;

			contentAssoc.setTypeId(newContentType.getId());
			contentAssoc.setReferencingTouchpointPGTreeNode(null);

			if (contentType.getId() == ContentType.MULTIPART) {
				Zone zone = contentAssoc.getContentObject().getZone();
				for (ZonePart zonePart : zone.getPartsInOrder()) {
					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation existingCa = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePart);

						Content content = existingCa.getContent();
						if (existingCa.getReferencingImageLibrary() == null && content != null && existingCa.getId() > 0) {
							content.deleteContentSafe(true);
						}
						existingCa.setContent(null);
						existingCa.setTypeId(newContentType.getId());
						existingCa.setReferencingTouchpointPGTreeNode(null);
						existingCa.setReferencingImageLibrary(null);
						existingCa.save();
					}
				}
			} else {
				Map<Long, ContentObjectAssociation> assocs = contentAssoc.getTouchpointVariantContentObjectAssociations(locales);
				Map<Long, ContentObjectAssociation> associationHolder = new HashMap<>();
				for (MessagepointLocale locale : locales) {
					ContentObjectAssociation ca = assocs.get(locale.getId());
					Content content = ca.getContent();
					if (ca.getReferencingImageLibrary() == null && content != null) {
						ca.setContent(null);
						if (ca.getId() > 0)
							content.deleteContentSafe(true);
					}
					ca.setTypeId(newContentType.getId());
					ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
					ca.setReferencingImageLibrary(null);
					ca.setContent(null);
					ca.save();
					associationHolder.put(locale.getId(), ca);
				}
			}
		}

		//
		// 10-Type stays as "OWNS", if reached this code, then the only thing
		// that is changing is the true content data.
		// FUTURE: need a way to keep the total content hash in the database.
		// Compare this hash here to determine if the
		// true content data has changed.
		// - update its content data
		//
		if (oldContAssocType.getId() == ContentAssociationType.ID_OWNS && newContentType.getId() == ContentAssociationType.ID_OWNS) {
			ownedContentDataChanged = true;
			updateContentObject = true;

			if (contentType.getId() == ContentType.MULTIPART) {
				if (mpContentVO.getZoneVO() != null) {
					MultipartContentZoneVO multipartContentZoneVO = mpContentVO.getZoneVO();

					for (ZonePartVO zonePartVO : multipartContentZoneVO.getParts()) {
						Map<Long, ContentVO> multipartContents = zonePartVO.getLanguageContentVOs();

						for (MessagepointLocale locale : locales) {
							ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentAssoc.getContentObjectWithDataTypeFocus(), contentAssoc.getTouchpointPGTreeNode(), locale, zonePartVO.getZonePart());
							Content content = ca.getContent();
							if(zonePartVO.isEmptyZonePart()) {
								if(ca.getReferencingImageLibrary() == null && content != null){
									if (ca.getId() > 0)
										content.deleteContentSafe(true);
								}
                                ca.setTypeId(ContentAssociationType.ID_EMPTY);
                                ca.setReferencingImageLibrary(null);
                                ca.setContent(null);
                                ca.setReferencingTouchpointPGTreeNode(null);
                                ca.save();
							} else {
								boolean isNew = false;
								if(content == null || ca.getReferencingImageLibrary() != null) {
									content = new Content();
									isNew = true;
								}
								ContentVO vo = multipartContents.get(locale.getId());
								if (vo != null) {
									if (vo.isSameAsDefault()) {
										ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
										if (!isNew) {
											if (content != null && ca.getReferencingImageLibrary() == null) {
												content.deleteContentSafe(true);
											}
										}
										ca.setReferencingImageLibrary(null);
										ca.setContent(null);
										ca.setReferencingTouchpointPGTreeNode(null);
									} else {
										content.setContent(vo.getContent());
										content.setImageName(vo.getImageName());
										content.setImageLocation(vo.getImageLocation());
										if (zonePartVO.getZonePart().getContentType().getId() == ContentType.GRAPHIC) {
											content.setImageUploadedDate(vo.getImageUploadedDate());
											content.setAppliedImageFilename(vo.getAppliedImageFilename());
											if (isNew) {
												content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
												content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
												content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
												content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
											} else {
												content.setImageLink(vo.getImageLink());
												content.setImageAltText(vo.getImageAltText());
												content.setImageExtLink(vo.getImageExtLink());
												content.setImageExtPath(vo.getImageExtPath());
											}
											content.setAssetId(vo.getAssetId());
											content.setAssetSite(vo.getAssetSite());
											content.setAssetURL(vo.getAssetURL());
											content.setAssetLastUpdate(vo.getAssetLastUpdate());
											content.setAssetLastSync(vo.getAssetLastSync());
											saveAssociatedFiles(content, contentType, vo, isNew, locale.getLanguageCode(), isAbortWIP);
										}

										// Content Library Reference
										if (vo.isUseImageLibrary()) {
											ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
											ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
											if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
												clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
											}

											if (imageLibrary != null)
												ca.setContent(null);
											else
												ca.setContent(clCa != null ? clCa.getContent() : null);
											ca.setReferencingImageLibrary(imageLibrary);
										} else {
											content.save();
											ca.setContent(content);
											ca.setReferencingImageLibrary(null);
										}
										ca.setTypeId(newContentType.getId());
										ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
									}
								}
								else
								{
									ca.setTypeId(newContentType.getId());
									ca.setReferencingTouchpointPGTreeNode(contentAssoc.getReferencingTouchpointPGTreeNode());
								}

								ca.save();
							}
						}
					}
				}
			} else {
				if (!contentVO.getLangContentMap().isEmpty()) {
					Map<Long, ContentVO> contentMap = contentVO.getLangContentMap();
					Map<Long, ContentObjectAssociation> assocs = contentAssoc.getTouchpointVariantContentObjectAssociations(locales);
					Map<Long, ContentObjectAssociation> associationHolder = new HashMap<>();
					
					for (MessagepointLocale locale : locales) {
						ContentObjectAssociation ca = assocs.get(locale.getId());
						Content content = ca.getContent();
						boolean isNew = false;
						if (content == null || ca.getReferencingImageLibrary() != null){
							content = new Content();
							isNew = true;
						}
						ContentVO vo = contentMap.get(locale.getId());

						if (vo.isSameAsDefault())
						{
							ca.setTypeId(ContentAssociationType.ID_SAME_AS_DEFAULT);
							if (!isNew)
							{
								if (content != null && ca.getReferencingImageLibrary() == null){
									content.deleteContentSafe(true);
								}
							}
							ca.setReferencingImageLibrary(null);
							ca.setContent(null);
							ca.setReferencingTouchpointPGTreeNode(null);
						}
						else {
							ca.setTypeId(newContentType.getId());
							content.setContent(vo.getContent());
							content.setImageName(vo.getImageName());
							content.setImageLocation(vo.getImageLocation());
							if (contentType.getId() == ContentType.GRAPHIC) {
								content.setImageUploadedDate(vo.getImageUploadedDate());
								content.setAppliedImageFilename(vo.getAppliedImageFilename());
								if (isNew) {
									content.setImageLink(Content.copyImageLinkFrom(vo.getImageLink()));
									content.setImageAltText(Content.copyImageAltTextFrom(vo.getImageAltText()));
									content.setImageExtLink(Content.copyImageExtLinkFrom(vo.getImageExtLink()));
									content.setImageExtPath(Content.copyImageExtPathFrom(vo.getImageExtPath()));
								} else {
									content.setImageLink(vo.getImageLink());
									content.setImageAltText(vo.getImageAltText());
									content.setImageExtLink(vo.getImageExtLink());
									content.setImageExtPath(vo.getImageExtPath());
								}
								content.setAssetId(vo.getAssetId());
								content.setAssetSite(vo.getAssetSite());
								content.setAssetURL(vo.getAssetURL());
								content.setAssetLastUpdate(vo.getAssetLastUpdate());
								content.setAssetLastSync(vo.getAssetLastSync());
								saveAssociatedFiles(content, contentType, vo, isNew, locale.getLanguageCode(), isAbortWIP);
							}

							// Content Library Reference
							if (vo.isUseImageLibrary()) {
								ContentObject imageLibrary = ContentObject.findByIdActiveDataFocusCentric(vo.getImageLibraryId());
								ContentObjectAssociation clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, locale, null);
								if (clCa != null && clCa.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
									clCa = ContentObjectAssociation.findByContentObjectAndParameters(imageLibrary, ContentObject.DATA_TYPE_FOCUS_ON, null, MessagepointLocale.getDefaultSystemLanguageLocale(), null);
								}
								if (imageLibrary != null)
									ca.setContent(null);
								else
									ca.setContent(clCa != null ? clCa.getContent() : null);
								ca.setReferencingImageLibrary(imageLibrary);
							} else {
								ca.setReferencingImageLibrary(null);
								content.save();
								if (isNew) {
									ca.setContent(content);
									ca.setTypeId(ContentAssociationType.ID_OWNS);
									ca.save();
								}
							}
						}

						ca.save();
						associationHolder.put(locale.getId(), ca);
					}
				}
			}
		}

		/**
		 * STEP #2: Version Control. If owned content data has changed, change
		 * the content status. For all affectedTreeNode, change its
		 * TouchpointSelection status.
		 */
//		if (updateVersionControl)
//		{
//			if (ownedContentDataChanged) {
//				versionControlContentData(contentAssoc, user);
//				affectedTreeNodes.remove(contentAssoc.getTouchpointPGTreeNode());
//			}
//			versionControlTouchpointSelections(affectedTreeNodes, user);
//		}

		HibernateUtil.getManager().getSession().flush();

		return updateContentObject;
	}

	public void saveAssociatedFiles(Content obj, ContentType contentType, ContentVO contentVO, boolean isNew, String langCode, boolean isAbortWIPAction) throws Exception {

		MultipartFile theFile = contentVO.getFile();
		
		if (theFile != null && theFile.getSize() > 0) {
			String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + langCode + "/" + contentVO.getFile().getOriginalFilename();

			if (!isNew && obj.getImageLocation() != null) {
				File defaultFile = new File(obj.getImageLocation());
				boolean hasChanged = (defaultFile == null) || !StringUtils.equals(obj.getImageName(), contentVO.getFile().getOriginalFilename());
				hasChanged = hasChanged || (defaultFile.length() != contentVO.getFile().getSize()); 
				
				if(hasChanged) {
					File oldImage = new File(obj.getImageLocation());
					if (oldImage.exists()) {
						oldImage.delete();
					}
					File image = new File(imageLocation);
					if (!image.getParentFile().exists())
						image.getParentFile().mkdirs();

					contentVO.getFile().transferTo(image);
					obj.clearTextAndGraphicContent();
					obj.setImageLocation(imageLocation);
					obj.setImageName(contentVO.getFile().getOriginalFilename());
					
					obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
					obj.setImageUploadedDate(DateUtil.now());

					obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
					obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
					obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
					obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
				}
			} else {
				File image = new File(imageLocation);
				if (!image.getParentFile().exists())
					image.getParentFile().mkdirs();
	
				contentVO.getFile().transferTo(image);
				obj.clearTextAndGraphicContent();
				obj.setImageLocation(imageLocation);
				obj.setImageName(contentVO.getFile().getOriginalFilename());
				
				obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
				obj.setImageUploadedDate(DateUtil.now());

				obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
				obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
				obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
				obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));
			}
		} else if(isNew && obj.getImageLocation() != null) {
			File defaultFile = new File(obj.getImageLocation());
			if(defaultFile.exists()) {
				String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + langCode + "/" + defaultFile.getName();
				File image = new File(imageLocation);
				if (!image.getParentFile().exists())
					image.getParentFile().mkdirs();
				obj.clearTextAndGraphicContent();
				obj.setImageLocation(imageLocation);
				obj.setImageName(defaultFile.getName());

				obj.setAppliedImageFilename(contentVO.getAppliedImageFilename());
				obj.setImageUploadedDate(DateUtil.now());

				obj.setImageLink(Content.copyImageLinkFrom(contentVO.getImageLink()));
				obj.setImageAltText(Content.copyImageAltTextFrom(contentVO.getImageAltText()));
				obj.setImageExtLink(Content.copyImageExtLinkFrom(contentVO.getImageExtLink()));
				obj.setImageExtPath(Content.copyImageExtPathFrom(contentVO.getImageExtPath()));

				if (theFile != null)
					obj.setImageUploadedDate(DateUtil.now());
				FileUtil.copy(defaultFile, image);
			}
		} else if(theFile == null && !isNew && obj.getImageLocation() != null && isAbortWIPAction){
		    // this situation indicates that discard structured working copy is in process
			// create a new copy of the image file
			File defaultFile = new File(obj.getImageLocation());
			if(defaultFile.exists()) {
				String imageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + "/" + obj.getGuid() + "/" + langCode + "/" + defaultFile.getName();
				File image = new File(imageLocation);
				if (!image.getParentFile().exists())
					image.getParentFile().mkdirs();
				obj.clearTextAndGraphicContent();
				obj.setImageLocation(imageLocation);
				obj.setImageName(defaultFile.getName());
				FileUtil.copy(defaultFile, image);
			}
		}
		
		// Clean up the Sandbox file
		contentVO.cleanupSandboxFile();
	}

	private void versionControlContentData(ContentObjectAssociation contentAssoc, User requestor) {
		ParameterGroupTreeNode sc = contentAssoc.getTouchpointPGTreeNode();
		if (sc != null) {
			// TODO WorkflowUtilities.invalidateStateApproval(sc, sc.getState());
			sc.setStatus(VersionStatus.findById(VersionStatus.VERSION_WIP));
			sc.setLockedFor(requestor.getId());
			sc.save();
		}
		// TODO TouchpointVariantStatusManager.executeOwnedContentDataChangedTrigger(contentAssoc, requestor, true);
	}

	private void versionControlTouchpointSelections(Set<ParameterGroupTreeNode> affectedTreeNode, User requestor) {
		TouchpointVariantStatusManager.executeContentChangeDownstreamEffect(affectedTreeNode, requestor, true);
	}

	public void validate(ServiceExecutionContext context) {
		/*
		 * Content type Empty & no content sent in for update - Error asking
		 * user to enter content
		 */
	}

	public static ServiceExecutionContext createContext(TouchpointContentSelectionEditWrapper wrapper, 
														User requestor, 
														boolean updateVersionControl,
														String maxCanvasWidth,
														String maxCanvasHeight,
														String trimCanvasWidth,
														String trimCanvasHeight) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		TouchpointContentSelectionUpdateServiceRequest request = new TouchpointContentSelectionUpdateServiceRequest();
		context.setRequest(request);

		request.setContentSelection(wrapper.getContentSelection());
		request.setUser(requestor);
		request.setContentVO(wrapper.getContentVO());
		request.setMpContentVO(wrapper.getMpContentVO());
		request.setContentType(wrapper.getContentType());
		request.setRefereningTreeNode(wrapper.getReferencingTreeNode());
		request.setUpdateVersionControl(updateVersionControl);
		
		request.setCanvasMaxWidth(maxCanvasWidth);
		request.setCanvasMaxHeight(maxCanvasHeight);
		request.setCanvasTrimWidth(trimCanvasWidth);
		request.setCanvasTrimHeight(trimCanvasHeight);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public static ServiceExecutionContext createContextForAbortWIP(TouchpointContentSelectionEditWrapper wrapper, User requestor, boolean updateVersionControl) {
		SimpleExecutionContext context = (SimpleExecutionContext)createContext(wrapper, requestor, updateVersionControl,null,null,null,null);
		TouchpointContentSelectionUpdateServiceRequest request = (TouchpointContentSelectionUpdateServiceRequest)context.getRequest();
		request.setAbortWIPAction(true);
		return context;
	}
}
