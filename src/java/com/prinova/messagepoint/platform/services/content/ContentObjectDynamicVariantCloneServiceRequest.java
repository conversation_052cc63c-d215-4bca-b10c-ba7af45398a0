package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

public class ContentObjectDynamicVariantCloneServiceRequest extends SimpleServiceRequest {

	private static final long serialVersionUID = -7680476715585320703L;

	
	private ContentObject contentObject;
	private long fromTreeNodeId;
	private long toTreeNodeId;
	private Long userId;
	
	public long getFromTreeNodeId() {
		return fromTreeNodeId;
	}
	public void setFromTreeNodeId(long fromTreeNodeId) {
		this.fromTreeNodeId = fromTreeNodeId;
	}
	public long getToTreeNodeId() {
		return toTreeNodeId;
	}
	public void setToTreeNodeId(long toTreeNodeId) {
		this.toTreeNodeId = toTreeNodeId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public ContentObject getContentObject() {
		return contentObject;
	}
	public void setContentObject(ContentObject contentObject) {
		this.contentObject = contentObject;
	}
}
