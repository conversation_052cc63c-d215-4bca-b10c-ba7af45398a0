package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class BulkUpdateTextStylesServiceRequest implements ServiceRequest{
	private static final long serialVersionUID = 3651778518824240054L;
	
	private List<TextStyle> textStyles;

	public List<TextStyle> getTextStyles() {
		return textStyles;
	}

	public void setTextStyles(List<TextStyle> textStyles) {
		this.textStyles = textStyles;
	}
}
