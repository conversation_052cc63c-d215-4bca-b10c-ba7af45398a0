package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.platform.services.ServiceRequest;

public class BulkDeleteStylesServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -8546328285690271154L;

	private List<Long> 	targetIds;
	private int			targetType;
	
	public List<Long> getTargetIds() {
		return targetIds;
	}
	public void setTargetIds(List<Long> targetIds) {
		this.targetIds = targetIds;
	}
	
	public int getTargetType() {
		return targetType;
	}
	public void setTargetType(int targetType) {
		this.targetType = targetType;
	}

}
