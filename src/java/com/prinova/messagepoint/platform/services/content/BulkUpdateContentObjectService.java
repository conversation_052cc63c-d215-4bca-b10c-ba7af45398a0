package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.communication.LibraryItemUsageType;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskType;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;

import java.util.List;

public class BulkUpdateContentObjectService extends AbstractService {
	
	public static final String SERVICE_NAME = "content.BulkUpdateContentObjectService";
	
	public static final int ACTION_HOLD 				=  1;
	public static final int ACTION_UNHOLD 				=  2;
	public static final int ACTION_SUPPRESS				=  3;
	public static final int ACTION_RESTORE				=  4;
	public static final int ACTION_MAKE_GLOBAL			=  5;
	public static final int ACTION_MOVE_TO_GLOBAL		=  6;
	public static final int ACTION_PUSH_TO_GLOBAL		=  7;
	public static final int ACTION_PULL_FROM_GLOBAL		=  8;
	public static final int ACTION_CREATE_LOCAL			=  9;
	public static final int ACTION_MOVE_TO_LOCAL		= 10;
    public static final int ACTION_COPY_TO_LOCAL		= 11;

	private static final Log log = LogUtil.getLog(BulkUpdateContentObjectService.class);

	public void execute(ServiceExecutionContext context) {
		
		BulkUpdateContentObjectServiceRequest request = (BulkUpdateContentObjectServiceRequest) context.getRequest();
		List<ContentObject> contentObjectList = request.getContentObjects();
		
		if (contentObjectList == null || contentObjectList.isEmpty()) {
			return;
		}
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			
			User requestor = request.getRequestor();
            if(requestor == null) requestor = UserUtil.getPrincipalUser();

			Document currentDocument = request.getCurrentDocument();
            if(currentDocument == null) currentDocument = UserUtil.getCurrentTouchpointContext();

			List<TouchpointSelection> variants = request.getVariants();

			switch ( request.getAction() ) {
				case (ACTION_HOLD): {
					for (ContentObject contentObject : contentObjectList) {
                        contentObject.setOnHold(true);
						if (request.isSuppressedOnHold())
                            contentObject.setSuppressed(true);
                        contentObject.save();
					}
					break;
				}
				case (ACTION_UNHOLD): {
					for (ContentObject contentObject : contentObjectList) {
                        contentObject.setOnHold(false);
                        contentObject.save();
						
						// Structured Message: Content not suppressed: Variant versioning must be updated
						boolean forcedVariantVersionUpdate = false;
						/** TODO
						if (contentObject.isStructuredContentEnabled() &&
							ContentAssociation.findMasterContentAssociationByContentObjectId(contentObject.getId()).getTypeId() != ContentAssociationType.ID_SUPPRESSES )
							forcedVariantVersionUpdate = true;
						**/
						// VERSIONING: Variants: Create working copies of all referencing selections if requested by user

						//if ( request.isUpdateVariantVersioning() || forcedVariantVersionUpdate ) {
						//	ContentAssociation mca = ContentAssociation.findMasterContentAssociationByContentObjectId(contentObject.getId());
							/** TODO
							List<ContentAssociation> cas = TouchpointContentSelectionUpdateService.findAllDependentContentObjectAssociations(mca);
							for (ContentAssociation currentContent: cas) {
								TouchpointSelection currentSelection = TouchpointSelection.findByPgTreeNodeId(currentContent.getParameterGroupTreeNode().getId());
								TouchpointVariantStatusManager.executeChangingStatusToWIP(currentSelection, null, requestor);
							}
							 **/
						//}
					}
					break;
				}
				case (ACTION_SUPPRESS): {
					for (ContentObject contentObject : contentObjectList) {
                        contentObject.setSuppressed(true);
                        contentObject.save();
					}
					break;
				}
				case (ACTION_RESTORE): {
					for (ContentObject contentObject : contentObjectList) {
                        contentObject.setSuppressed(false);
                        contentObject.save();
					}
					break;
				}
				case (ACTION_MAKE_GLOBAL): {
					for (ContentObject contentObject : contentObjectList) {

						if (contentObject.getGlobalParentObject() != null) {
							log.info("Making local smart text global...");
							this.pushToExistingGlobalObjects(contentObject);

							ContentObject globalContentObject = contentObject.getGlobalParentObject();

							// Assigned the current touchpoint to the global content object
							if (globalContentObject.isGlobalContentObject() && contentObject.getDocument() != null)
							{
								globalContentObject.getVisibleDocuments().add(contentObject.getDocument());
								globalContentObject.setDocument(null);
								globalContentObject.save();
							}

							// Switch references in all contents where the local content object is used to the global
							if(globalContentObject.isGlobalSmartText()){
								List<Content> contents = Content.findAllWithLocalSTRef(contentObject.getId());
								for (Content content : contents) {
									content.swapBetweenLocalAndGlobalSTs(contentObject.getId(), globalContentObject.getId(), true);
									content.save();
								}
							}
							else if (globalContentObject.isGlobalImage())
							{
								List<ContentObjectAssociation> contentObjectAssociations = ContentObjectAssociation.findByReferencingImageLibrary(contentObject.getId());
								for (ContentObjectAssociation coa : contentObjectAssociations) {
									coa.setContent(null);
									coa.setReferencingImageLibrary(globalContentObject);
									coa.save();
								}
							}

							HibernateUtil.getManager().getSession().flush();
							HibernateUtil.getManager().getSession().refresh(contentObject);
							HibernateUtil.getManager().getSession().refresh(globalContentObject);

							if (globalContentObject.hasWorkingData())
							{
								// Activate the global content object since the push created working data
								log.info("Activating the new working copy of global smart text...");
								globalContentObject.activateWorkingData();
							}

							// Delete local content object
							log.info("Deleting data of local smart text...");
							if (contentObject.deleteAllData() == ContentObject.REQUEST_SUCCESSFUL)
							{
								log.info("Deleted data of local smart text...");

								HibernateUtil.getManager().getSession().flush();
								HibernateUtil.getManager().getSession().refresh(contentObject);
								
								contentObject.getTasks().clear();

								if(!HistoricalContent.isReferencingContentObject(contentObject.getId())) {
									log.info("Deleting local smart text...");
									contentObject.delete();
									log.info("Deleted local smart text...");
								}
							}
						}
					}
					break;
				}
				case (ACTION_MOVE_TO_GLOBAL): {
					for (ContentObject contentObject : contentObjectList) {
						try {
							// Switch local type to global type
							if (contentObject.isLocalSmartText()) {
								contentObject.setObjectType(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);

								if(contentObject.getUsageTypeId() == 0){
									contentObject.setUsageTypeId(LibraryItemUsageType.ID_MESSAGE);
								}
							}else if (contentObject.isLocalImage()) {
								contentObject.setObjectType(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE);
							}

							// Assigned the current touchpoint
							if (contentObject.isGlobalContentObject())
							{
								if (contentObject.getDocument() != null) {
									contentObject.getVisibleDocuments().add(contentObject.getDocument());
									contentObject.setDocument(null);
								}

								contentObject.setOwningTouchpointSelection(null);
							}

							contentObject.save();

							boolean isReferenced = false;

							// Switch type in all contents where it is used
							if(contentObject.isGlobalSmartText()){
								List<Content> contents = Content.findAllWithLocalSTRef(contentObject.getId());
								for(Content content : contents){
									content.swapBetweenLocalAndGlobalSTs(contentObject.getId(), contentObject.getId(), true);
									content.save();
								}

								isReferenced = !contents.isEmpty();
							}
							else if (contentObject.isGlobalImage())
							{
								isReferenced = ContentObjectAssociation.isUsedInReferencingImageLibrary(contentObject.getId());
							}

							if (isReferenced && !contentObject.hasActiveData() && contentObject.hasWorkingData())
							{
								// Activate the content object since is used and as global it has to have active data
								contentObject.activateWorkingData();
							}

						} catch (Exception e1) {
							log.error(" unexpected exception when invoking MoveLocalAssetsToGlobalService execute method", e1);
						}
					}
					break;
				}
				case (ACTION_PUSH_TO_GLOBAL): {
					for (ContentObject contentObject : contentObjectList) {
						if(contentObject.isLocalContentObject())
						{
							if (contentObject.getGlobalParentObject() != null)
								this.pushToExistingGlobalObjects(contentObject);
							else
								this.pushToNewGlobalObjects(contentObject);
						}
					}
					break;
				}
				case (ACTION_PULL_FROM_GLOBAL): {
					for (ContentObject contentObject : contentObjectList) {
						if(contentObject.isLocalContentObject() && contentObject.getGlobalParentObject() != null)
						{
							this.pullFromExistingGlobalObject(contentObject);
						}
					}
					break;
				}
				case (ACTION_CREATE_LOCAL): {
					for (ContentObject contentObject : contentObjectList) {
						try {
							if(currentDocument.getHasTouchpointSelections() && variants != null && !variants.isEmpty()) {
								for (TouchpointSelection variant : variants) {
									cloneToLocal(contentObject, variant, requestor, currentDocument, true);
								}
							}
							else
							{
								cloneToLocal(contentObject, null, requestor, currentDocument, true);
							}
						} catch (Exception e1) {
							log.error(" unexpected exception when invoking MakeLocalAssetsGlobalService execute method", e1);
						}
					}
					break;
				}
                case (ACTION_COPY_TO_LOCAL): {
                    for (ContentObject contentObject : contentObjectList) {
                        try {
                            if(currentDocument.getHasTouchpointSelections() && variants != null && !variants.isEmpty()) {
                                for (TouchpointSelection variant : variants) {
                                    cloneToLocal(contentObject, variant, requestor, currentDocument, false);
                                }
                            }
                            else
                            {
                                cloneToLocal(contentObject, null, requestor, currentDocument, false);
                            }
                        } catch (Exception e1) {
                            log.error(" unexpected exception when invoking MakeLocalAssetsGlobalService execute method", e1);
                        }
                    }
                    break;
                }
				case (ACTION_MOVE_TO_LOCAL): {
					for (ContentObject contentObject : contentObjectList) {
						try {
							// Switch global type to local type
							if (contentObject.isGlobalSmartText())
								contentObject.setObjectType(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT);
							else if (contentObject.isGlobalImage())
								contentObject.setObjectType(ContentObject.OBJECT_TYPE_LOCAL_IMAGE);

							// Assign the current touchpoint and enable variables in content
							if (contentObject.isLocalContentObject())
							{
								contentObject.setDocument(currentDocument);
								contentObject.getVisibleDocuments().clear();
								contentObject.setVariableContentEnabled(true);
								if(currentDocument.getHasTouchpointSelections() && variants != null && !variants.isEmpty()) {
									TouchpointSelection variant = variants.get(0);
									if (!variant.isMaster())
									{
										contentObject.setOwningTouchpointSelection(variant);
									}
								}
							}

							contentObject.save();

							// Switch type in all contents where it is used
							if(contentObject.isLocalSmartText()){
								List<Content> contents = Content.findAllWithGlobalSTRefAndDocument(contentObject.getId(), currentDocument.getId(), true);
								for(Content content : contents){
									content.swapBetweenLocalAndGlobalSTs(contentObject.getId(), contentObject.getId(), false);
									content.save();
								}
							}

						} catch (Exception e1) {
							log.error(" unexpected exception when invoking MoveGlobalAssetsToLocalService execute method", e1);
						}
					}
					break;
				}
				default:
					break;
			}

		} catch (Exception e) {
			log.error(" unexpected exception when invoking BulkUpdateContentObjectService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}

	}

	public void validate(ServiceExecutionContext context) {
		return;
	}

	private void cloneToLocal(ContentObject contentObject, TouchpointSelection variant, User requestor, Document document, boolean updateWhereUsed) {

		CloneHelper.setIsCloning(CloneHelper.ID_CLONING_TYPE_LOCAL);
		ServiceExecutionContext cloneContext = ContentObjectCloneService.createContext(document.getId(), contentObject.getId(), variant != null ? variant.getId() : null, requestor, contentObject.getName());
		Service service = MessagepointServiceFactory.getInstance().lookupService(ContentObjectCloneService.SERVICE_NAME, ContentObjectCloneService.class);
		service.execute(cloneContext);
		ServiceResponse serviceResponse = cloneContext.getResponse();
		CloneHelper.setIsCloning(CloneHelper.ID_CLONING_TYPE_INACTIVE);

		if (!serviceResponse.isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(ContentObjectCloneService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" id=").append(contentObject.getId());
			sb.append(" Global Smart Text is not cloned ");
			log.error(sb.toString());
		} else {
			ContentObject clonedContentObject = (ContentObject) serviceResponse.getResultValueBean();

			// Switch global type to local type
			if (clonedContentObject.isGlobalSmartText())
				clonedContentObject.setObjectType(ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT);
			else if (clonedContentObject.isGlobalImage())
				clonedContentObject.setObjectType(ContentObject.OBJECT_TYPE_LOCAL_IMAGE);

			// Assigned the current touchpoint
			if (clonedContentObject.isLocalContentObject())
			{
				clonedContentObject.setDocument(document);
				clonedContentObject.getVisibleDocuments().clear();
			}

			clonedContentObject.setGlobalParentObject(contentObject);

			clonedContentObject.save();

			clonedContentObject.activateWorkingData();
			if(ElasticsearchContentUtils.isContentCompareEnabled()) {
				if(clonedContentObject.hasWorkingData()) {
					ElasticsearchContentUtils.updateContentForModel(false, clonedContentObject, clonedContentObject.getContentObjectData());
				}
				if(clonedContentObject.hasActiveData()) {
					ElasticsearchContentUtils.updateContentForModel(true, clonedContentObject, clonedContentObject.getContentObjectData());
				}
			}
			// Switch type in all contents where it is used
            if(updateWhereUsed) {
                if (clonedContentObject.isLocalSmartText()) {
                    List<Content> contents = Content.findAllWithGlobalSTRefAndDocument(contentObject.getId(), document.getId(), true);
                    for (Content content : contents) {
                        content.swapBetweenLocalAndGlobalSTs(clonedContentObject.getId(), contentObject.getId(), false);
                        content.save();
                    }
                }
            }
		}
	}

	private void pullFromExistingGlobalObject(ContentObject contentObject){
		ContentObject originContentObject = contentObject.getGlobalParentObject();
		ContentObjectData originContentObjectData = originContentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
		if(originContentObjectData != null){
			contentObject.createWorkingDataFrom(originContentObjectData);

			contentObject.setLastGlobalParentObjectSyncDate(DateUtil.now());
			contentObject.save();
		}
	}

	private void pushToExistingGlobalObjects(ContentObject contentObject){
		ContentObject originContentObject = contentObject;
		ContentObjectData originContentObjectData = originContentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);

		if (originContentObjectData != null){
			ContentObject targetContentObject = originContentObject.getGlobalParentObject();

			targetContentObject.createWorkingDataFrom(originContentObjectData);
			if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					ElasticsearchContentUtils.updateContentForModel(false, targetContentObject, targetContentObject.getContentObjectData());
			}
			originContentObject.setLastGlobalParentObjectSyncDate(DateUtil.now());
			originContentObject.save();

			// Post processing
			HibernateUtil.getManager().getSession().flush();

			// Update ParameterGroupTreeNode records to establish proper version relationships
			// This SQL updates working copies (DATA_TYPE = 1) to point to their corresponding active copies (DATA_TYPE = 2)
			// and synchronizes the DNA values between versions
			StringBuilder query = new StringBuilder("UPDATE PG_TREE_NODE SET ORIGIN_OBJECT_ID = (");

			// First subquery: Find the ID of the corresponding active copy
			// Matches by content_object_id and name to identify the same logical tree node
			query.append("  SELECT active_pgtn.ID FROM PG_TREE_NODE active_pgtn ");
			query.append("  WHERE active_pgtn.CONTENT_OBJECT_ID = "); query.append(targetContentObject.getId());
			query.append("  AND active_pgtn.DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
			query.append("  AND active_pgtn.NAME = PG_TREE_NODE.NAME");
			query.append("  LIMIT 1");  // Ensure single result for origin_object_id

			// Second field update: Synchronize DNA from active copy to working copy
			query.append("), DNA = (");

			// Second subquery: Get the DNA value from the corresponding active copy
			// This ensures working copies inherit the correct DNA from their active counterparts
			query.append("  SELECT active_pgtn.DNA FROM PG_TREE_NODE active_pgtn ");
			query.append("  WHERE active_pgtn.CONTENT_OBJECT_ID = "); query.append(targetContentObject.getId());
			query.append("  AND active_pgtn.DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
			query.append("  AND active_pgtn.NAME = PG_TREE_NODE.NAME");
			query.append("  LIMIT 1");  // Ensure single result for DNA value
			query.append(") ");

			// Target condition: Only update working copies for this content object
			query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(targetContentObject.getId());
			query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
			query.append("; ");

			NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());
			sqlQuery.executeUpdate();

			// HibernateUtil.getManager().getSession().flush();
			// HibernateUtil.getManager().getSession().refresh(newInstance);

			// Update the reference content for dynamic variants
			// LocalGlobalSyncUtils.updateReferenceContent(targetModel, newInstance, ContentObject.class, newInstance.getId());
		}
	}

	private void pushToNewGlobalObjects(ContentObject contentObject) {
		ContentObject originContentObject = contentObject;

		ContentObject clonedContentObject = originContentObject.clone(ContentObject.DATA_TYPE_ACTIVE);

		clonedContentObject.setWorkflowAction(null);
		clonedContentObject.setReadyForApproval(false);
		clonedContentObject.setState(WorkflowState.findById(WorkflowState.STATE_CONTENT));
		clonedContentObject.save();

		clonedContentObject.setLockedFor(clonedContentObject.getCreatedBy());

		// Post processing
		HibernateUtil.getManager().getSession().flush();

		// Switch data type of the cloned ContentObjectData to working type
		StringBuilder query = new StringBuilder("UPDATE CONTENT_OBJECT_ASSOCIATION SET DATA_TYPE = " + ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = ").append(clonedContentObject.getId()).append(" AND DATA_TYPE = ").append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_DATA SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(", STATUS_ID = "); query.append(VersionStatus.VERSION_WIP);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_TG_INCLUDED_MAP SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_TG_EXCLUDED_MAP SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_TG_EXTENDED_MAP SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_TG_INSTANCE_MAP SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		query.append("UPDATE CONTENT_OBJECT_COMMENT SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		// Update ParameterGroupTreeNode records to establish proper version relationships
		query.append("UPDATE PG_TREE_NODE SET DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_WORKING);
		query.append(", ORIGIN_OBJECT_ID = NULL ");
		query.append(" WHERE CONTENT_OBJECT_ID = "); query.append(clonedContentObject.getId());
		query.append(" AND DATA_TYPE = "); query.append(ContentObject.DATA_TYPE_ACTIVE);
		query.append("; ");

		if (ElasticsearchContentUtils.isContentCompareEnabled()) {
			ElasticsearchContentUtils.updateContentForModel(false, clonedContentObject, clonedContentObject.getContentObjectData());
		}

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query.toString());
		sqlQuery.executeUpdate();

		clonedContentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);

		if (clonedContentObject.isLocalSmartText()) {
			clonedContentObject.setObjectType(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);

			if(contentObject.getUsageTypeId() == 0){
				clonedContentObject.setUsageTypeId(LibraryItemUsageType.ID_MESSAGE);
			}
		}else if (clonedContentObject.isLocalImage()) {
			clonedContentObject.setObjectType(ContentObject.OBJECT_TYPE_GLOBAL_IMAGE);
		}

		if (clonedContentObject.getDocument() != null)
		{
			clonedContentObject.getVisibleDocuments().add(clonedContentObject.getDocument());
			clonedContentObject.setDocument(null);
		}

		clonedContentObject.setOwningTouchpointSelection(null);

		clonedContentObject.save();

		// Save smart text as global reference
		originContentObject.setGlobalParentObject(clonedContentObject);
		originContentObject.setLastGlobalParentObjectSyncDate(DateUtil.now());
		originContentObject.save();

		// HibernateUtil.getManager().getSession().flush();
		// HibernateUtil.getManager().getSession().refresh(newInstance);

		// Update the reference content
		// LocalGlobalSyncUtils.updateReferenceContent(newModel, newInstance, ContentObject.class, newInstance.getId());
	}


	public static ServiceExecutionContext createContextForHold(	List<ContentObject> contentObjects,
																boolean isSuppressedOnHold) {
		return createContext (	ACTION_HOLD,
								contentObjects,
								isSuppressedOnHold,
								false,
								null);
	}
	
	public static ServiceExecutionContext createContextForUnhold(	List<ContentObject> contentObjects,
																	boolean updateVariantVersioning) {
		return createContext (	ACTION_UNHOLD,
								contentObjects,
								false,
								updateVariantVersioning,
								null);
	}
	
	public static ServiceExecutionContext createContextForSuppress(	List<ContentObject> contentObjects ) {
		return createContext (	ACTION_SUPPRESS,
								contentObjects,
								false,
								false,
								null);
	}
	
	public static ServiceExecutionContext createContextForRestore(List<ContentObject> contentObjects ) {
		return createContext (	ACTION_RESTORE,
								contentObjects,
								false,
								false,
								null);
	}

    public static ServiceExecutionContext createContext(int action,
                                                        List<ContentObject> contentObjects,
                                                        boolean isSuppressedOnHold,
                                                        boolean updateVariantVersioning,
                                                        List<TouchpointSelection> variants) {
        return createContext(action, contentObjects, isSuppressedOnHold, updateVariantVersioning, variants, null, null);
    }


    public static ServiceExecutionContext createContext(int action,
														List<ContentObject> contentObjects, 
														boolean isSuppressedOnHold,
														boolean updateVariantVersioning,
														List<TouchpointSelection> variants,
                                                        User requestor,
                                                        Document currentDocument) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkUpdateContentObjectServiceRequest request = new BulkUpdateContentObjectServiceRequest();
		context.setRequest(request);

		request.setAction(action);
		request.setContentObjects(contentObjects);
		request.setSuppressedOnHold(isSuppressedOnHold);
		request.setUpdateVariantVersioning(updateVariantVersioning);
		request.setVariants(variants);
		request.setRequestor(requestor);
        request.setCurrentDocument(currentDocument);

		SimpleServiceResponse response = new SimpleServiceResponse();
		response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(response);

		return context;
	}

}