package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.version.VersionStatus;

import java.io.Serializable;
import java.util.*;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * ContentObjectContentSelectionVO
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class ContentObjectContentSelectionVO implements Serializable {

	private static final long serialVersionUID = -6219645732275668109L;
	private long id;
	private String guid;
	private Map<Long, ContentVO> langContentMap = new HashMap<>();
	private Map<Long, Boolean> langContentSameAsMap = new HashMap<>();
	private boolean canUpdate;
	private boolean canApprove;
	private boolean canMarkComplete;
	private boolean isMaster;
	private Date updated;
	private String updatedBy;
	private VersionStatus status;
	private Long lastEditor;
	private Long lockedFor;

	public ContentObjectContentSelectionVO() {
		
	}

	public static ContentObjectContentSelectionVO mapFromMasterContent(ContentObject contentObject, Map<Long, ContentVO> langContentMap) {
		ContentObjectContentSelectionVO v = new ContentObjectContentSelectionVO();
		v.setLangContentMap(langContentMap);
		v.setCanUpdate(false);
		v.setMaster(true);
		v.setCanApprove(false);
		v.setCanMarkComplete(false);
		v.setUpdated(contentObject.getUpdated());
		v.setUpdatedBy(contentObject.getUpdatedByName());
		v.setLastEditor(null);
		return v;
	}

	public static ContentObjectContentSelectionVO mapRegularSelectionContent(ContentObject contentObject, ParameterGroupTreeNode contentObjectPGTN, long updatorUserId, Map<Long, ContentVO> masterContentMap) {
		ContentObjectContentSelectionVO v = new ContentObjectContentSelectionVO();
		v.setLangContentMap(mapFromContentInstance(contentObjectPGTN, contentObject, masterContentMap));
		
		if (contentObjectPGTN != null) {
			v.setId(contentObjectPGTN.getId());
			v.setGuid(contentObjectPGTN.getGuid());
			v.setStatus(contentObjectPGTN.getStatus());
			
			List<ContentObjectAssociation> cass = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, contentObjectPGTN, null);
			
			if(!cass.isEmpty()) {
				boolean isEmptyContent = true;
				for (ContentObjectAssociation ca : cass) {
					if(ca.getContent() != null) {
						isEmptyContent = false;
						break;
					}
				}
				if(!isEmptyContent) {
					v.setUpdated(contentObjectPGTN.getUpdated());
					v.setUpdatedBy(contentObjectPGTN.getUpdatedByName());
				}
			}
			v.setLastEditor(contentObjectPGTN.getLastEditor());
			v.setLockedFor(contentObjectPGTN.getLockedFor());
		}
		
		v.setCanUpdate(isVarianceUpdatable(contentObject, contentObjectPGTN, updatorUserId));
		v.setCanApprove(isApprovalRequired(contentObjectPGTN) && (contentObjectPGTN.getLockedFor()!=null && contentObjectPGTN.getLockedFor().longValue() == updatorUserId) );
		v.setCanMarkComplete(isMarkCompleteAllowed(contentObjectPGTN, updatorUserId));
		v.setMaster(false);
		
		return v;
	}
	
	public static ContentObjectContentSelectionVO mapFromParameterGroupTreeNode(ContentObject contentObject, ParameterGroupTreeNode contentObjectPGTN, ParameterGroupTreeNode pgTreeNode,
                                                                                long updatorUserId) {
		ContentObjectContentSelectionVO v = new ContentObjectContentSelectionVO();

        Map<Long, ContentVO> cMap = new HashMap<>(ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, -1L, true, pgTreeNode));

		List<MessagepointLocale> languages = contentObject.getContentObjectLanguagesAsLocales();
		for (MessagepointLocale locale : languages) {
			if (!cMap.containsKey(locale.getId())) {
				// if the content vaiance does not exist for the lang, set the same as parent checkbox and create an
				// empty
				// contentVO to allow binding to the screen
				ContentVO vo = new ContentVO();
				vo.setContentTypeId(contentObject.getContentType().getId());
				vo.setLocaleId(locale.getId());
				vo.setEmpty(true);
				v.getLangContentMap().put(locale.getId(), vo);
				v.getLangContentSameAsMap().put(locale.getId(), Boolean.TRUE);
			} else {
				v.getLangContentSameAsMap().put(locale.getId(), Boolean.FALSE);
				v.getLangContentMap().put(locale.getId(), cMap.get(locale.getId()));
			}
		}
		
		if (contentObjectPGTN != null) {
			v.setId(contentObjectPGTN.getId());
			v.setGuid(contentObjectPGTN.getGuid());
			v.setStatus(contentObjectPGTN.getStatus());
			v.setUpdated(contentObjectPGTN.getUpdated());
			v.setUpdatedBy(contentObjectPGTN.getUpdatedByName());
			v.setLastEditor(contentObjectPGTN.getLastEditor());
			v.setLockedFor(contentObjectPGTN.getLockedFor());
		} 
		v.setCanUpdate(isVarianceUpdatable(contentObject, contentObjectPGTN, updatorUserId));
		v.setCanApprove(isApprovalRequired(contentObjectPGTN) && (contentObjectPGTN.getLockedFor()!=null && contentObjectPGTN.getLockedFor().longValue() == updatorUserId) );
		v.setCanMarkComplete(isMarkCompleteAllowed(contentObjectPGTN, updatorUserId));
		v.setMaster(false);
		return v;
	}

	private static boolean isMarkCompleteAllowed(ParameterGroupTreeNode pgtn, long updatorUserId) {
		if (pgtn==null) {
			return false;
		}
		if (pgtn.getLockedFor()!= null && pgtn.getLockedFor().longValue() == updatorUserId && pgtn.getStatus().getId() == VersionStatus.VERSION_WIP) {
			return true;
		}
		return false;
	}

	private static boolean isApprovalRequired(ParameterGroupTreeNode pgtn) {
		if (pgtn == null) {
			return false;
		}
		
		if(pgtn.getStatus() == null)
			return false;
		
		if (pgtn.getStatus().getId() == VersionStatus.VERSION_WAITING_APPROVAL) {
			return true;
		}
		return false;
	}

	private static boolean isVarianceUpdatable(ContentObject message, ParameterGroupTreeNode pgtn, long updatorUserId) {
		// no overwrites
		if (pgtn == null) {
			return true;
		}

		// the content instance is being worked on by another person
		if (pgtn.getLockedFor() != null && pgtn.getLockedFor() != updatorUserId) {
			return false;
		}

		return true;
	}

	public Map<Long, ContentVO> getLangContentMap() {
		return langContentMap;
	}

	public void setLangContentMap(Map<Long, ContentVO> langContentMap) {
		this.langContentMap = langContentMap;
	}

	public boolean isCanUpdate() {
		return canUpdate;
	}

	public void setCanUpdate(boolean canUpdate) {
		this.canUpdate = canUpdate;
	}

	public Map<Long, Boolean> getLangContentSameAsMap() {
		return langContentSameAsMap;
	}

	public void setLangContentSameAsMap(Map<Long, Boolean> langContentSameAsMap) {
		this.langContentSameAsMap = langContentSameAsMap;
	}

	public boolean isMaster() {
		return isMaster;
	}

	public void setMaster(boolean isMaster) {
		this.isMaster = isMaster;
	}

	/*
	 * Determine if the content variance contains any overwrites or is it an empty tree node
	 */
	public boolean isSameAsParent() {
		for (Long localeId : getLangContentMap().keySet()) {
			if(!getLangContentMap().get(localeId).isReference()) {
				return false;
			}
		}
		return true;
	}

	public boolean isCanApprove() {
		return canApprove;
	}

	public void setCanApprove(boolean canApprove) {
		this.canApprove = canApprove;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	public boolean isCanMarkComplete() {
		return canMarkComplete;
	}

	public void setCanMarkComplete(boolean canMarkComplete) {
		this.canMarkComplete = canMarkComplete;
	}

	public VersionStatus getStatus() {
		return status;
	}

	public void setStatus(VersionStatus status) {
		this.status = status;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Long getLastEditor() {
		return lastEditor;
	}

	public void setLastEditor(Long lastEditor) {
		this.lastEditor = lastEditor;
	}

	public Long getLockedFor() {
		return lockedFor;
	}

	public void setLockedFor(Long lockedFor) {
		this.lockedFor = lockedFor;
	}

	public static Map<Long, ContentVO> mapFromContentInstance(ParameterGroupTreeNode contentObjectPGTN, ContentObject contentObject, Map<Long, ContentVO> masterContentMap) {
		Map<Long, ContentVO> voMap = new HashMap<>();
		List<MessagepointLocale> languages = contentObject.getContentObjectLanguagesAsLocales();

		if (contentObjectPGTN == null) {
			for (MessagepointLocale locale : languages) {
				ContentVO contentVO = new ContentVO();
				contentVO.setLocaleId(locale.getId());
				contentVO.setReference(true);
				// Retrieve the "dirty" flag from the master VOs
				if(masterContentMap.containsKey(locale.getId())) {
					ContentVO masterContentVO = masterContentMap.get(locale.getId());
					contentVO.setAssociationType(masterContentVO.getAssociationType());
					contentVO.setDirty(masterContentVO.isDirty());
				}
				voMap.put(locale.getId(), contentVO);
			}
			return voMap;
		}
		
		Map<Long, ContentVO> map = ContentObjectAssociation.getDynamicVariantContents(contentObject, contentObjectPGTN, null, languages);
		if(!map.isEmpty()) {
			for (Long key : map.keySet()) {
				ContentVO cvo = map.get(key);
				if (cvo.isReference() && cvo.getContentId() <= 0 )
				{
					if (masterContentMap != null && masterContentMap.containsKey(key))
					{
						ContentVO mcvo = masterContentMap.get(key);
						cvo.setContentSupplier(() -> mcvo.getContent());
						cvo.setContentId(mcvo.getContentId());
						cvo.setSha256Hash(mcvo.getSha256Hash());
						cvo.setImageLocation(mcvo.getImageLocation());
						cvo.setImageName(mcvo.getImageName());
						cvo.setImageUploadedDate(mcvo.getImageUploadedDate());
						cvo.setAppliedImageFilename(mcvo.getAppliedImageFilename());
						cvo.setUseImageLibrary(mcvo.isUseImageLibrary());
						cvo.setImageLibraryId(mcvo.getImageLibraryId());
						cvo.setImageLibraryObjectType(mcvo.getImageLibraryObjectType());
						cvo.setImageLibraryName(mcvo.getImageLibraryName());
						cvo.setImageLink(mcvo.getImageLink());
						cvo.setImageAltText(mcvo.getImageAltText());
						cvo.setImageExtLink(mcvo.getImageExtLink());
						cvo.setImageExtPath(mcvo.getImageExtPath());
						cvo.setAssetId(mcvo.getAssetId());
						cvo.setAssetSite(mcvo.getAssetSite());
						cvo.setAssetURL(mcvo.getAssetURL());
						cvo.setAssetLastUpdate(mcvo.getAssetLastUpdate());
						cvo.setAssetLastSync(mcvo.getAssetLastSync());
						cvo.setUnformattedText(mcvo.getUnformattedText());
						cvo.setContainsVariables(mcvo.isContainsVariables());
					}
				}
				voMap.put(map.get(key).getLocaleId(), cvo);
			}
		}
		
		return voMap;
	}

}