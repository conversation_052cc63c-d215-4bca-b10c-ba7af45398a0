package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.Set;

public class CreateOrUpdateTextStyleTransformationProfileService extends AbstractService{

	private static final Log log = LogUtil.getLog(CreateOrUpdateTextStyleTransformationProfileService.class);
	public static final String SERVICE_NAME = "content.CreateOrUpdateTextStyleTransformationProfileService";

	private static final int ACTION_CREATE_OR_UPDATE = 1;

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateOrUpdateTextStyleTransformationProfileServiceRequest request = (CreateOrUpdateTextStyleTransformationProfileServiceRequest) context.getRequest();

			if(request.getAction() == ACTION_CREATE_OR_UPDATE) {

				boolean isNew = (request.getId() <= 0);

				TextStyleTransformationProfile textStyleTransformationProfile;
				if (isNew) {
					textStyleTransformationProfile = new TextStyleTransformationProfile();
				} else {
					textStyleTransformationProfile = TextStyleTransformationProfile.findById(request.getId());
				}

				textStyleTransformationProfile.setName(request.getName());
				textStyleTransformationProfile.setLanguages(request.getLanguages());
				textStyleTransformationProfile.setActionEntries(request.getActionEntries());
				textStyleTransformationProfile.setTransformDefaultStyles(request.getTransformDefaultStyles());
				textStyleTransformationProfile.setType(request.getType());

				textStyleTransformationProfile.save();

				getResponse(context).setResultValueBean(textStyleTransformationProfile.getId());

				if (isNew) {
					// Audit (Audit Text Style Transformation Profile creation)
					AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_TEXT_STYLE_TRANSFORMATION_PROFILE, request.getName(), textStyleTransformationProfile.getId(), AuditActionType.ID_CHANGE_CREATED, null);
				}
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occurred in CreateOrUpdateTextStyleTransformationProfileService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}

	public static ServiceExecutionContext createContextForCreateOrUpdate(long id,
																		 String name,
																		 Set<MessagepointLocale> languages,
																		 Set<ActionEntry> actionEntries,
																		 Boolean transformDefaultStyles,
																		 int type) {
		return createContext(ACTION_CREATE_OR_UPDATE, id, name, languages, actionEntries, transformDefaultStyles, type);
	}

	public static ServiceExecutionContext createContext(int action,
														long id,
														String name,
														Set<MessagepointLocale> languages,
														Set<ActionEntry> actionEntries,
														Boolean transformDefaultStyles,
														int type) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateOrUpdateTextStyleTransformationProfileServiceRequest request = new CreateOrUpdateTextStyleTransformationProfileServiceRequest();
		context.setRequest(request);

		request.setId(id);
		request.setAction(action);

		request.setName(name);
		request.setLanguages(languages);
		request.setActionEntries(actionEntries);
		request.setTransformDefaultStyles(transformDefaultStyles);
		request.setType(type);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}
}