package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * ContentObjectDynamicVariantCloneService
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class ContentObjectDynamicVariantCloneService extends AbstractService {

	private static final Log log = LogUtil.getLog(ContentObjectDynamicVariantCloneService.class);
	public static final String SERVICE_NAME = "content.ContentObjectDynamicVariantCloneService";

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}
			ContentObjectDynamicVariantCloneServiceRequest request = (ContentObjectDynamicVariantCloneServiceRequest) context.getRequest();
			if (request != null) {
				cloneDynamicVariant(request);
			}
		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in ContentObjectDynamicVariantCloneService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					context.getServiceName() + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void cloneDynamicVariant(ContentObjectDynamicVariantCloneServiceRequest request) {
		ParameterGroupTreeNode fromPgtn = ParameterGroupTreeNode.findById(request.getFromTreeNodeId());
		ParameterGroupTreeNode toPgtn = ParameterGroupTreeNode.findById(request.getToTreeNodeId());
		List<ContentObjectAssociation> toCas = ContentObjectAssociation.findAllByContentObjectAndParameters(request.getContentObject(), ContentObject.DATA_TYPE_WORKING, toPgtn);
		for (ContentObjectAssociation toCa : toCas) {
			ContentObjectAssociation fromCa = ContentObjectAssociation.findByContentObjectAndParameters(request.getContentObject(), ContentObject.DATA_TYPE_WORKING, fromPgtn, toCa.getMessagepointLocale(), toCa.getZonePart());
			cloneSelectionContent(fromCa, toCa, request.getContentObject());
		}
	}
	
	/**
	 * copy the fromCa's contents to toCa's contents
	 *
     */
	private void cloneSelectionContent(ContentObjectAssociation fromCa, ContentObjectAssociation toCa, ContentObject contentObject) {
		boolean isAffectedDependents = true;
		Content toContent = null;
		if(toCa.getContent() != null && toCa.getTypeId() == ContentAssociationType.ID_OWNS && toCa.getReferencingImageLibrary() == null) {
			if(toCa.getContent().getImageLocation() != null) {
				File image = new File(toCa.getContent().getImageLocation());
				if(image.exists())
					image.delete();
			}
			//keep the current content, just update the text or image value for this existing content
			//only delete the image file
			toContent = toCa.getContent();
			isAffectedDependents = false;
		}
		
		if(fromCa != null && fromCa.getContent() != null) {
			Content content = null;
			if(toContent == null)
				content = new Content();
			else 
				content = toContent;
			
			content.setCreated(fromCa.getCreated());
			content.setCreatedBy(fromCa.getCreatedBy());
			content.setEncodedContent(fromCa.getContent().getEncodedContent());
			if(fromCa.getContent().isGraphicPresent()) {
				File srcFile = new File(fromCa.getContent().getImageLocation());
				String mcCloneImageLocation = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir) + File.separator + content.getGuid() + File.separator + toCa.getMessagepointLocale().getLanguageCode() + File.separator + fromCa.getContent().getImageName();
				File destFile = new File(mcCloneImageLocation);

				if (!destFile.getParentFile().exists()) {
					destFile.getParentFile().mkdirs();
				}

				FileUtil.copy(srcFile, destFile);
				content.setImageLocation(mcCloneImageLocation);
				content.setImageName(destFile.getName());
				content.setAppliedImageFilename(fromCa.getContent().getAppliedImageFilename());
				content.setImageUploadedDate(fromCa.getContent().getImageUploadedDate());
				content.setImageLink(Content.copyImageLinkFrom(fromCa.getContent().getImageLink()));
				content.setImageAltText(Content.copyImageAltTextFrom(fromCa.getContent().getImageAltText()));
				content.setImageExtLink(Content.copyImageExtLinkFrom(fromCa.getContent().getImageExtLink()));
				content.setImageExtPath(Content.copyImageExtPathFrom(fromCa.getContent().getImageExtPath()));
				content.setAssetId(fromCa.getContent().getAssetId());
				content.setAssetSite(fromCa.getContent().getAssetSite());
				content.setAssetURL(fromCa.getContent().getAssetURL());
				content.setAssetLastUpdate(fromCa.getContent().getAssetLastUpdate());
				content.setAssetLastSync(fromCa.getContent().getAssetLastSync());
				
				if ( fromCa.getReferencingImageLibrary() != null ) {
					toCa.setReferencingImageLibrary(fromCa.getReferencingImageLibrary());
					toCa.setContent(null);
				}
				else
				{
					content.save();
					toCa.setContent(content);
				}
			}
			else {
				content.clearGraphicContent();
				content.save();
				toCa.setContent(content);
			}
			toCa.setTypeId(fromCa.getTypeId());
		} else {
			if (fromCa != null)
				toCa.setTypeId(fromCa.getTypeId());
			else
				toCa.setTypeId(ContentAssociationType.ID_EMPTY);
			toCa.setContent(null);
		}
		toCa.save();
		
		if(isAffectedDependents)
			updateDependents(contentObject, toCa);
	}

	private void updateDependents(ContentObject contentObject, ContentObjectAssociation contentObjectAssociation) {
		List<ContentObjectAssociation> depends = new ArrayList<>();
		ContentObjectAssociation.getDependentsByLanguage(contentObject.getId(),contentObjectAssociation.getPGTreeNode().getId(), contentObjectAssociation.getZonePart()==null?0:contentObjectAssociation.getZonePart().getId(), contentObjectAssociation.getMessagepointLocale(), depends);
		for (ContentObjectAssociation ca : depends) {
			if(ca.getContent() != null && !ca.getContent().equals(contentObjectAssociation.getContent())) {
				ca.setContent(contentObjectAssociation.getContent());
				ca.save();
			} else if((ca.getContent() == null && contentObjectAssociation.getContent() != null) || (ca.getContent() != null && contentObjectAssociation.getContent() == null)) {
				ca.setContent(contentObjectAssociation.getContent());
				ca.save();
			}
		}
	}
	
	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	public static ServiceExecutionContext createContext(ContentObject contentObject, long fromTreeNodeId, long toTreeNodeId, Long userId) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		ContentObjectDynamicVariantCloneServiceRequest request = new ContentObjectDynamicVariantCloneServiceRequest();
		context.setRequest(request);

		request.setContentObject(contentObject);
		request.setUserId(userId);
		request.setFromTreeNodeId(fromTreeNodeId);
		request.setToTreeNodeId(toTreeNodeId);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}