package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.message.content.GraphicContentProxy;
import com.prinova.messagepoint.model.message.content.TextContentProxy;

public interface GenericContentProxy  {
	
	public TextContentProxy getTextContent(MessagepointLocale code);
	
	public GraphicContentProxy getGraphicContent(MessagepointLocale code);
	
	public long getId();
	
	public ContentType getContentType();
	

}
