package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class DeleteContentObjectDynamicVariantTreeNodeServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = 4876661544199440180L;
	private long objectInstanceId;
	private long pgTreeNodeId;
	private Long requestorId;
	private ParameterGroupTreeNode pgTreeNode;
	private boolean forceVariantDelete;
	
	public long getObjectInstanceId() {
		return objectInstanceId;
	}
	
	public void setObjectInstanceId(long objectInstanceId) {
		this.objectInstanceId = objectInstanceId;
	}
	
	public long getPgTreeNodeId() {
		return pgTreeNodeId;
	}
	
	public void setPgTreeNodeId(long pgTreeNodeId) {
		this.pgTreeNodeId = pgTreeNodeId;
	}
	
	public Long getRequestorId() {
		return requestorId;
	}
	
	public void setRequestorId(Long requestorId) {
		this.requestorId = requestorId;
	}
	
	public ParameterGroupTreeNode getPgTreeNode() {
		if (pgTreeNode == null) {
			pgTreeNode = ParameterGroupTreeNode.findById(getPgTreeNodeId());
		}
		return pgTreeNode;
	}

	public boolean isForceVariantDelete() {
		return forceVariantDelete;
	}

	public void setForceVariantDelete(boolean forceVariantDelete) {
		this.forceVariantDelete = forceVariantDelete;
	}
	
}
