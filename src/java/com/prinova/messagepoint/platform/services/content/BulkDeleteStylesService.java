package com.prinova.messagepoint.platform.services.content;

import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;

public class BulkDeleteStylesService extends AbstractService{

	private static final Log log = LogUtil.getLog(BulkDeleteStylesService.class);
	public static final String SERVICE_NAME = "content.BulkDeleteStylesService";
	
	public static final int TYPE_TEXT_STYLES				= 1;
	public static final int TYPE_PARAGRAPH_STYLES			= 2;

	public void execute(ServiceExecutionContext context) {
		
		BulkDeleteStylesServiceRequest request = (BulkDeleteStylesServiceRequest) context.getRequest();

		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			for ( Long currentId: request.getTargetIds() ) {
				
				ServiceExecutionContext deleteContext = null;
				
				if ( request.getTargetType() == TYPE_TEXT_STYLES )
					deleteContext = DeleteModelService.createContext(currentId, TextStyle.class.getName());
				else if ( request.getTargetType() == TYPE_PARAGRAPH_STYLES )
					deleteContext = DeleteModelService.createContext(currentId, ParagraphStyle.class.getName());
				
				Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(DeleteModelService.SERVICE_NAME, DeleteModelService.class);
				deleteModelService.execute(deleteContext);

				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(DeleteModelService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" paragraph style is not deleted. ");
					log.error(sb.toString());
				}
			}

		} catch (Exception e) {
			String logInfo = "Unexpected exception has occured in BulkDeleteStylesService.execute(ServiceExecutionContext)...";
			log.error(logInfo, e);
			getResponse(context).addErrorMessage(getErrorMessageKey(context),
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
				context.getServiceName() + e, context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
		
	}
	
	public static ServiceExecutionContext createContextForTextStylesDelete(List<Long> targetIds) {
		return createContext(targetIds, TYPE_TEXT_STYLES);
	}
	public static ServiceExecutionContext createContextForParagraphStylesDelete(List<Long> targetIds) {
		return createContext(targetIds, TYPE_PARAGRAPH_STYLES);
	}

	private static ServiceExecutionContext createContext(List<Long> targetIds, int targetType) {

		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		BulkDeleteStylesServiceRequest request = new BulkDeleteStylesServiceRequest();
		context.setRequest(request);

		request.setTargetIds(targetIds);
		request.setTargetType(targetType);
		
		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

	public void validate(ServiceExecutionContext context) {
		//Validation checkings are applied here.
	}

}