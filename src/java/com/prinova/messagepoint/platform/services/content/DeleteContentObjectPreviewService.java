package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.DocumentPreview;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ApplicationErrorMessages;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleResourceAwareServiceExeContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.util.HibernateUtil;

public class DeleteContentObjectPreviewService extends AbstractService {

	public static final String SERVICE_NAME = "content.DeleteContentObjectPreviewService";
	private static final Log log = LogUtil.getLog(DeleteContentObjectPreviewService.class);

	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			DeleteContentObjectPreviewServiceRequest request = (DeleteContentObjectPreviewServiceRequest) context.getRequest();

			Long documentPreviewId = request.getDocumentPreviewId();
			DocumentPreview documentPreview = HibernateUtil.getManager().getObject( DocumentPreview.class, documentPreviewId );
			HibernateUtil.getManager().sessionSafeDelete( documentPreview );

		} catch (Exception e) {
			log.error(" unexpected exception when invoking DeleteContentObjectPreviewService execute method", e);
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
				ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION, SERVICE_NAME + e,
				context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	
	}

	public static ServiceExecutionContext createContext(Long documentPreviewId) {

		SimpleResourceAwareServiceExeContext context = new SimpleResourceAwareServiceExeContext();
		context.setServiceName(SERVICE_NAME);

		DeleteContentObjectPreviewServiceRequest request = new DeleteContentObjectPreviewServiceRequest();
		context.setRequest(request);

		request.setDocumentPreviewId(documentPreviewId);

		SimpleServiceResponse serviceResp = new SimpleServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}
}
