package com.prinova.messagepoint.platform.services.content;

import java.util.HashSet;
import java.util.Set;

import com.prinova.messagepoint.model.font.BorderType;
import com.prinova.messagepoint.model.font.ParagraphAlignment;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.platform.services.ServiceRequest;

public class CreateOrUpdateParagraphStyleServiceRequest implements ServiceRequest{

	private static final long serialVersionUID = -6729028405998880493L;

	private boolean				isCust;
	private long 				id;
	private String 				name;
	private String 				connectorName;
	private ParagraphAlignment 	alignment;
	private String 				paragraphSpacingBefor;
	private String 				paragraphSpacingAfter;
	private int 				lineSpacing;
	private int					lineSpacingType;
	private String 				leftMargin;
	private String 				rightMargin;
	private String 				indent;
	private boolean 			hangingIndent = false;
	private BorderType			border;
	private String				borderWidth;
	private TextStyle			textStyle;
	private boolean 			isBulletedListApplicable = false;
	private long 				documentId;
	private String				taggingOverride;
	
	private boolean				toggleAlignment			= false;
	private boolean				toggleLineSpacing		= false;
	private boolean				toggleLeftMargin		= false;
	private Set<String>			toggleLineSpacingValues	= new HashSet<>();

	public boolean isCust() {
		return isCust;
	}
	public void setCust(boolean isCust) {
		this.isCust = isCust;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getConnectorName() {
		return connectorName;
	}
	public void setConnectorName(String connectorName) {
		this.connectorName = connectorName;
	}
	public ParagraphAlignment getAlignment() {
		return alignment;
	}
	public void setAlignment(ParagraphAlignment alignment) {
		this.alignment = alignment;
	}
	public String getParagraphSpacingBefor() {
		return paragraphSpacingBefor;
	}
	public void setParagraphSpacingBefor(String paragraphSpacingBefor) {
		this.paragraphSpacingBefor = paragraphSpacingBefor;
	}
	public String getParagraphSpacingAfter() {
		return paragraphSpacingAfter;
	}
	public void setParagraphSpacingAfter(String paragraphSpacingAfter) {
		this.paragraphSpacingAfter = paragraphSpacingAfter;
	}
	public int getLineSpacing() {
		return lineSpacing;
	}
	public void setLineSpacing(int lineSpacing) {
		this.lineSpacing = lineSpacing;
	}
	public int getLineSpacingType() {
		return lineSpacingType;
	}
	public void setLineSpacingType(int lineSpacingType) {
		this.lineSpacingType = lineSpacingType;
	}
	public String getLeftMargin() {
		return leftMargin;
	}
	public void setLeftMargin(String leftMargin) {
		this.leftMargin = leftMargin;
	}
	public String getRightMargin() {
		return rightMargin;
	}
	public void setRightMargin(String rightMargin) {
		this.rightMargin = rightMargin;
	}
	public String getIndent() {
		return indent;
	}
	public void setIndent(String indent) {
		this.indent = indent;
	}
	public boolean isHangingIndent() {
		return hangingIndent;
	}
	public void setHangingIndent(boolean hangingIndent) {
		this.hangingIndent = hangingIndent;
	}
	public BorderType getBorder() {
		return border;
	}
	public void setBorder(BorderType border) {
		this.border = border;
	}
	public String getBorderWidth() {
		return borderWidth;
	}
	public void setBorderWidth(String borderWidth) {
		this.borderWidth = borderWidth;
	}
	public TextStyle getTextStyle() {
		return textStyle;
	}
	public void setTextStyle(TextStyle textStyle) {
		this.textStyle = textStyle;
	}
	public boolean isBulletedListApplicable() {
		return isBulletedListApplicable;
	}
	public void setBulletedListApplicable(boolean isBulletedListApplicable) {
		this.isBulletedListApplicable = isBulletedListApplicable;
	}
	public long getDocumentId() {
		return documentId;
	}
	public void setDocumentId(long documentId) {
		this.documentId = documentId;
	}
	public String getTaggingOverride() {
		return taggingOverride;
	}
	public void setTaggingOverride(String taggingOverride) {
		this.taggingOverride = taggingOverride;
	}
	public boolean isToggleAlignment() {
		return toggleAlignment;
	}
	public void setToggleAlignment(boolean toggleAlignment) {
		this.toggleAlignment = toggleAlignment;
	}
	public boolean isToggleLineSpacing() {
		return toggleLineSpacing;
	}
	public void setToggleLineSpacing(boolean toggleLineSpacing) {
		this.toggleLineSpacing = toggleLineSpacing;
	}
	public boolean isToggleLeftMargin() {
		return toggleLeftMargin;
	}
	public void setToggleLeftMargin(boolean toggleLeftMargin) {
		this.toggleLeftMargin = toggleLeftMargin;
	}
	public Set<String> getToggleLineSpacingValues() {
		return toggleLineSpacingValues;
	}
	public void setToggleLineSpacingValues(Set<String> toggleLineSpacingValues) {
		this.toggleLineSpacingValues = toggleLineSpacingValues;
	}
	
}