package com.prinova.messagepoint.platform.services.content;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class NewMultipartContentProxy implements Serializable {

	private static final long serialVersionUID = 4756649843735482551L;
	
	private String name;
	private String description;
	private String categoryName;
	private Long contentType;
	private Map<String, String> textContent = new HashMap<>();
	private Map<String, Object[]> binaryContent = new HashMap<>();
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Long getContentType() {
		return contentType;
	}

	public void setContentType(Long contentType) {
		this.contentType = contentType;
	}

	public Map<String, String> getTextContent() {
		return textContent;
	}

	public void setTextContent(Map<String, String> textContent) {
		this.textContent = textContent;
	}

	public Map<String, Object[]> getBinaryContent() {
		return binaryContent;
	}

	public void setBinaryContent(Map<String, Object[]> binaryContent) {
		this.binaryContent = binaryContent;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}


}
