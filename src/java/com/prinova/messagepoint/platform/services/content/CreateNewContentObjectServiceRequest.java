package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectComment;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper;
import com.prinova.messagepoint.platform.services.SimpleServiceRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

public class CreateNewContentObjectServiceRequest extends SimpleServiceRequest{

	private static final long serialVersionUID = 8935170318200750560L;
	
	private String 						 name;
	private String                       dna;
	private String 						 metatags;
	private String 						 description;
	private ContentObjectComment comment;
	private int 				 		 objectType = ContentObject.OBJECT_TYPE_MESSAGE;
    private ContentType 				 contentType;
    private String 						 externalId;
    private long 						 userid;
    private User 						 user;
    private Date 						 startDate;
    private Date 						 endDate;
    private boolean 					 repeatsAnnually;
    private Date 						 created;
    private long 						 parameterGroupId;
    private ParameterGroup 				 parameterGroup;
    private int 						 deliveryType;
    private int 						 flowType;
    private ContentObjectOverviewWrapper contentObjectOverviewWrapper;
    private TouchpointSelection 		 owningTouchpointSelection;
    private boolean 					 updateVariantVersioning = false;
    private boolean						 isTouchpointLocal 		 = false;
    private Document					 document;
    private Set<Document>				 visibleDocuments;
    private int							 contentSpecializationTypeId;
    private int                          graphicTypeId;
    private boolean						 supportsTables			 = false;
    private boolean						 supportsForms			 = false;
    private boolean						 supportsBarcodes		 = false;
    private boolean						 supportsContentMenus	 = false;
    private boolean						 keepContentTogether     = false;
    private boolean						 renderAsTaggedText		 = false;
	private MetadataFormEditWrapper 	 formWrapper;
	private Long						 channelContextId;
	private boolean						 importProcess			 = false;
	private DataGroup 					 dataGroup;
	private Long                         updateContentObjectId   = null;

	private List<ContentObjectAssociation> 	contentList = new ArrayList<>();
	private boolean						 structuredContentEnabled = true;
	private boolean						 variableContentEnabled   = true;
	private boolean						 startsFrontFacing        = false;
	private boolean						 startsFrontFacingEvery        = false;

	private boolean 					 advanced;
	private int 						 usageTypeId;
	private String 						 modelGuid = null;

	public int getDeliveryType() {
		return deliveryType;
	}
	public void setDeliveryType(int deliveryType) {
		this.deliveryType = deliveryType;
	}

	public int getFlowType() {
		return flowType;
	}
	public void setFlowType(int flowType) {
		this.flowType = flowType;
	}

	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getMetatags() {
		return metatags;
	}
	public void setMetatags(String metatags) {
		this.metatags = metatags;
	}
	
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public int getObjectType() {
		return objectType;
	}
	public void setObjectType(int objectType) {
		this.objectType = objectType;
	}

	public ContentType getContentType() {
		return contentType;
	}
	public void setContentType(ContentType contentType) {
		this.contentType = contentType;
	}

	public User getUser() {
		if (user==null)
			user = User.findById(getUserid());
		return user;
	}
	public void setUser(User user) {
		this.user = user;
	}

	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getCreated() {
		return created;
	}
	public void setCreated(Date created) {
		this.created = created;
	}

	public List<ContentObjectAssociation> getContentList() {
		return contentList;
	}
	public void setContentList(List<ContentObjectAssociation> contentList) {
		this.contentList = contentList;
	}

	public ContentObjectComment getComment() {
		return comment;
	}
	public void setComment(ContentObjectComment comment) {
		this.comment = comment;
	}

	public String getExternalId() {
		return externalId;
	}
	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	public long getUserid() {
		return userid;
	}
	public void setUserid(long userid) {
		this.userid = userid;
	}

	public long getParameterGroupId() {
		return parameterGroupId;
	}
	public void setParameterGroupId(long parameterGroupId) {
		this.parameterGroupId = parameterGroupId;
	}
	
	public ParameterGroup getParameterGroup() {
		if (parameterGroup == null)
			if (getParameterGroupId() > 0)
				parameterGroup = ParameterGroup.findById(getParameterGroupId());
		return parameterGroup;
	}

	public ContentObjectOverviewWrapper getContentObjectOverviewWrapper() {
		return contentObjectOverviewWrapper;
	}
	public void setContentObjectOverviewWrapper(
			ContentObjectOverviewWrapper contentObjectOverviewWrapper) {
		this.contentObjectOverviewWrapper = contentObjectOverviewWrapper;
	}

	public TouchpointSelection getOwningTouchpointSelection() {
		return owningTouchpointSelection;
	}
	public void setOwningTouchpointSelection(TouchpointSelection owningTouchpointSelection) {
		this.owningTouchpointSelection = owningTouchpointSelection;
	}
	
	public void setRepeatsAnnually(boolean val ) {
	    repeatsAnnually = val;
	}
	public boolean isRepeatsAnnually() {
	    return repeatsAnnually;
	}

	public boolean isUpdateVariantVersioning() {
		return updateVariantVersioning;
	}
	public void setUpdateVariantVersioning(boolean updateVariantVersioning) {
		this.updateVariantVersioning = updateVariantVersioning;
	}
	
	public boolean isTouchpointLocal() {
		return isTouchpointLocal;
	}
	public void setTouchpointLocal(boolean isTouchpointLocal) {
		this.isTouchpointLocal = isTouchpointLocal;
	}
	
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}

	public Set<Document> getVisibleDocuments() {
		return visibleDocuments;
	}
	public void setVisibleDocuments(Set<Document> visibleDocuments) {
		this.visibleDocuments = visibleDocuments;
	}

	public int getContentSpecializationTypeId() {
		return contentSpecializationTypeId;
	}
	public void setContentSpecializationTypeId(int contentSpecializationTypeId) {
		this.contentSpecializationTypeId = contentSpecializationTypeId;
	}
	
	public boolean isSupportsTables() {
		return supportsTables;
	}
	public void setSupportsTables(boolean supportsTables) {
		this.supportsTables = supportsTables;
	}
	
	public boolean isSupportsForms() {
		return supportsForms;
	}
	public void setSupportsForms(boolean supportsForms) {
		this.supportsForms = supportsForms;
	}

	public boolean isSupportsBarcodes() {
		return supportsBarcodes;
	}
	public void setSupportsBarcodes(boolean supportsBarcodes) {
		this.supportsBarcodes = supportsBarcodes;
	}

	public boolean isSupportsContentMenus() {
		return supportsContentMenus;
	}
	public void setSupportsContentMenus(boolean supportsContentMenus) {
		this.supportsContentMenus = supportsContentMenus;
	}

	public boolean isRenderAsTaggedText() {
		return renderAsTaggedText;
	}
	public void setRenderAsTaggedText(boolean renderAsTaggedText) {
		this.renderAsTaggedText = renderAsTaggedText;
	}

	public boolean isKeepContentTogether() { return keepContentTogether; }
	public void setKeepContentTogether(boolean keepContentTogether) { this.keepContentTogether = keepContentTogether; }

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}
	
	public Long getChannelContextId() {
		return channelContextId;
	}
	public void setChannelContextId(Long channelContextId) {
		this.channelContextId = channelContextId;
	}
	
    public int getGraphicTypeId() {
	    return graphicTypeId;
	}
    
    public void setGraphicTypeId(int graphicTypeId) {
        this.graphicTypeId = graphicTypeId;
    }
    public String getDna() {
        return dna;
    }
    public void setDna(String dna) {
        this.dna = dna;
    }

	public boolean isImportProcess() {
		return importProcess;
	}
	public void setImportProcess(boolean importProcess) {
		this.importProcess = importProcess;
	}

	public DataGroup getDataGroup() {
		return dataGroup;
	}

	public void setDataGroup(DataGroup dataGroup) {
		this.dataGroup = dataGroup;
	}

	public boolean isStructuredContentEnabled() {
		return structuredContentEnabled;
	}

	public void setStructuredContentEnabled(boolean structuredContentEnabled) {
		this.structuredContentEnabled = structuredContentEnabled;
	}

	public boolean getStartsFrontFacing() {
		return startsFrontFacing;
	}

	public void setStartsFrontFacing(boolean startsFrontFacing) {
		this.startsFrontFacing = startsFrontFacing;
	}

	public boolean getStartsFrontFacingEvery() {
		return startsFrontFacingEvery;
	}
	public void setStartsFrontFacingEvery(boolean startsFrontFacingEvery) {
		this.startsFrontFacingEvery = startsFrontFacingEvery;
	}

	public boolean isVariableContentEnabled() {
		return variableContentEnabled;
	}

	public void setVariableContentEnabled(boolean variableContentEnabled) {
		this.variableContentEnabled = variableContentEnabled;
	}

	public boolean isAdvanced() {
		return advanced;
	}
	public void setAdvanced(boolean advanced) {
		this.advanced = advanced;
	}

	public int getUsageTypeId() {
		return usageTypeId;
	}
	public void setUsageTypeId(int usageTypeId) {
		this.usageTypeId = usageTypeId;
	}

	public void setModelGuid(String val ) {
		modelGuid = val;
	}
	public String getModelGuid() {
		return modelGuid;
	}

	public Long getUpdateContentObjectID() {
	    return updateContentObjectId;
    }
    public void setUpdateContentObjectID(long id) {
	    this.updateContentObjectId = id;
    }
}