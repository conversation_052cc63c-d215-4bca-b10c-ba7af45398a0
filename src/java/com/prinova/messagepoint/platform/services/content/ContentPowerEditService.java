package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentPowerEditWrapper;
import com.prinova.messagepoint.platform.services.AbstractService;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ContentPowerEditBackgroundTask;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;

public class ContentPowerEditService extends AbstractService {

    public static final String SERVICE_NAME = "content.ContentPowerEditService";

    @Override
    public void execute(ServiceExecutionContext context) {
        validate(context);
        if (hasValidationError(context)) {
            return;
        }

        ContentPowerEditServiceRequest request = (ContentPowerEditServiceRequest) context.getRequest();

        ContentPowerEditBackgroundTask task = new ContentPowerEditBackgroundTask(request.getWrapper(), UserUtil.getPrincipalUser());
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
    }

    @Override
    public void validate(ServiceExecutionContext context) {

    }

    public static ServiceExecutionContext createContext(ContentPowerEditWrapper wrapper) {
        ServiceExecutionContext context = new SimpleExecutionContext();
        context.setServiceName(SERVICE_NAME);

        ContentPowerEditServiceRequest request = new ContentPowerEditServiceRequest();
        request.setWrapper(wrapper);
        context.setRequest(request);

        SimpleServiceResponse response = new SimpleServiceResponse();
        response.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
        context.setResponse(response);

        return context;
    }
}
