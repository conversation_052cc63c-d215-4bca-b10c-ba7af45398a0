package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.notification.NotificationActionType;
import com.prinova.messagepoint.model.notification.NotificationEventType;
import com.prinova.messagepoint.model.notification.NotificationEventUtil;
import com.prinova.messagepoint.model.notification.NotificationObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.tenant.TenantMetaData;
import com.prinova.messagepoint.model.util.FreeformContentUtil;
import com.prinova.messagepoint.model.util.TaskUtil;
import com.prinova.messagepoint.model.workflow.WorkflowState;
import com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 
 * Messagepoint Inc. 1998-2020
 *
 * All rights reserved.
 * 
 * CreateNewContentObjectService
 *
 * @contentRefactor
 * 
 * @since 20.1
 * <AUTHOR> Team
 */
public class CreateNewContentObjectService extends AbstractService {

	/**
	 * create Content Object and working instance data, all inputs are passed in through
	 * CreateNewContentObjectServiceRequest
	 */
	public static final String SERVICE_NAME = "content.CreateNewContentObjectService";
//	private static final Log log = LogUtil.getLog(CreateNewContentObjectService.class);

	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
	public void execute(ServiceExecutionContext context) {
		try {
			validate(context);
			if (hasValidationError(context)) {
				return;
			}

			CreateNewContentObjectServiceRequest request = (CreateNewContentObjectServiceRequest) context.getRequest();
			ContentObjectOverviewWrapper wrapper = request.getContentObjectOverviewWrapper();

			ContentObject contentObject = createContentObject(request);

			CreateNewContentObjectServiceResponse response = (CreateNewContentObjectServiceResponse) context.getResponse();
			response.setContentObject(contentObject);

			if ( request.getOwningTouchpointSelection() != null ) {
				contentObject.setOwningTouchpointSelection(request.getOwningTouchpointSelection());
			}
			
			if (wrapper != null) {

				if (wrapper.getContentObject() != null)
					wrapper.getContentObject().setId(contentObject.getId());
				else
					wrapper.setContentObject(contentObject);

				if ( !wrapper.getContentObject().getIsTouchpointLocal() || wrapper.getPlaceholder() != null ) {
					if ( wrapper.getPlaceholder() != null ) {
						wrapper.setDeliveryType(ContentObject.DELIVERY_TYPE_MANDATORY);
						contentObject.setZone(wrapper.getPlaceholder());
					}
					else {
						wrapper.setDeliveryType(contentObject.getDeliveryType(ContentObject.DATA_TYPE_WORKING));
						if (wrapper.getSelectedZone() != null)
							contentObject.setZone(wrapper.getSelectedZone());
					}

					contentObject.setDeliveryType(wrapper.getDeliveryType());

					if (contentObject.getZone() != null) {
						ContentObjectZonePriority.fixAssociationDueToContentObjectZoneDeliveryChanges(contentObject, contentObject.getZone(), contentObject.getZone(), false, true);
					}

					contentObject.save();
				}

			}

			if ( contentObject.isStructuredContentEnabled() ) {
				Document document = wrapper.getContentObject().getDocument();
				updateTouchpointSelections(request, document, contentObject, request.getUser());
			}

			long docId = -1L;
			Document doc = request.getDocument();
			if(doc != null) {
				docId = doc.getId();
			}
			NotificationEventUtil.notificationEventHappen(NotificationEventType.ID_MESSAGES, 
					request.isTouchpointLocal() ? NotificationObjectType.ID_LOCAL_SMART_TEXT : NotificationObjectType.ID_MESSAGE, 
					"", contentObject.getId(), docId,
					NotificationActionType.ID_MESSAGE_WIP_CREATED, "message created through bulk upload");

			// Workflow Changes (Create new)
			AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_CREATED, null);

			// Update tag cloud
			int cloudTypeId = TagCloudType.ID_MESSAGE;
			if(contentObject.isLocalSmartText() || contentObject.isGlobalSmartText()){
				cloudTypeId = TagCloudType.ID_EMBEDDED_TEXT;
			}else if(contentObject.isLocalImage() || contentObject.isGlobalImage()){
				cloudTypeId = TagCloudType.ID_IMAGE_LIBRARY;
			}

			Document document = null;
			if(!contentObject.getIsGlobalContentObject()){
				document = contentObject.getFirstDocumentDelivery();
			}

			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
			ServiceExecutionContext executionContext = UpdateTagCloudService.createContext(cloudTypeId, request.getMetatags(), document, false);
			if ( service != null && executionContext != null )
				service.execute(executionContext);


			// Create a task
			TaskUtil.createTaskForWorkingCopy(contentObject, request.getUser());

			//create the configurable workflow action
			/** TO DO
			ConfigurableWorkflowActionHistory.generateAction(null, msgInstance, ConfigurableWorkflowActionType.ID_CREATED, ConfigurableWorkflowActionStateType.ID_NONE, 
															ConfigurableWorkflowActionStateType.ID_WORKING_COPY, ApplicationUtil.getMessage(ConfigurableWorkflowActionStateType.STATE_WORKING_COPY_CODE), 
															UserUtil.getPrincipalUser(), null, DateUtil.now(), null, ConfigurableWorkflowActionHistory.ACTION_REGULAR);
			 **/
		} catch (Exception e) {
		    LogUtil.getLog(this.getClass()).error("Exception: ", e);
		            
			this.getResponse(context).addErrorMessage("UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION",
					ApplicationErrorMessages.UNEXPECTED_EXCEPTION_IN_SERVICE_EXECUTION,
					SERVICE_NAME + e,
					context.getLocale());
			throw new RuntimeException(e.getMessage());
		}
	}

	private void createAssociationsForSelection(
	        ContentObject contentObject,
	        ParameterGroupTreeNode treeNode,
	        ParameterGroupTreeNode referencingTreeNode,
	        ZonePart zonePart,
	        User user,
	        int contentAssociationType,
	        Document document) 
	{
		List<MessagepointLocale> languages = document.getTouchpointLanguagesAsLocales();
		for (MessagepointLocale locale : languages) {
			ContentObjectAssociation assoc = new ContentObjectAssociation();
			assoc.setTouchpointPGTreeNode(treeNode);
			assoc.setContentObject(contentObject);
			assoc.setDataType(contentObject.getFocusOnDataType());
			assoc.setCreated(DateUtil.now());
			assoc.setCreatedBy(user.getId());
			assoc.setTypeId(contentAssociationType);
			assoc.setMessagepointLocale(locale);
			assoc.setTouchpointPGTreeNode(referencingTreeNode);
			
			//currently no content for each language
			assoc.setContent(null);
			assoc.setZonePart(zonePart);
			contentObject.getContentObjectAssociations().add(assoc);
			assoc.save(true);
		}
	}
	
	protected void updateTouchpointSelections(
	        CreateNewContentObjectServiceRequest request,
	        Document document,
	        ContentObject contentObject,
	        User user ) 
	{
		if (request.getContentList() == null || request.getContentList().isEmpty())
			return;

		List<ContentObjectAssociation> assos = request.getContentList();
		for(ContentObjectAssociation ca: assos) {
			if (ca != null) {

				Content content = ca.getContent();
				content.save();
				ca.setContentObject(contentObject);
				ca.setDataType(contentObject.getFocusOnDataType());
				ca.setContent(content);
				ca.save();
			}
		}

		/**

		Set<ParameterGroupTreeNode> affectedTreeNodes = new HashSet<ParameterGroupTreeNode>();
		
		ParameterGroupTreeNode startTreeNode;
		if (contentObject.isVariantType())
		{
			startTreeNode = contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode();
		}
		else
		{
			if (request.isImportProcess())
				startTreeNode = document.getMasterTouchpointSelectionDuringImport().getParameterGroupTreeNode();
			else
				startTreeNode = document.getMasterTouchpointSelection().getParameterGroupTreeNode();
		}
		
		ContentObjectAssociation startContentAssoc = new ContentObjectAssociation();
		startContentAssoc.setContentObject(contentObject);
		startContentAssoc.setDataType(contentObject.getFocusOnDataType());
		startContentAssoc.setTypeId(ContentAssociationType.ID_SUPPRESSES);
		startContentAssoc.setTouchpointPGTreeNode(startTreeNode);
		startContentAssoc.save();

		if (contentObject.isMultipartType()) 
		{
			Set<Zone> zones = contentObject.getZones();
			for (Zone zone : zones) 
			{
				List<ZonePart> parts = zone.getPartsInOrder();
				for (ZonePart zonePart : parts) 
				{
					// TODO createAssociationsForSelection( contentObject, message, startTreeNode, null, startSc, zonePart, request.getUser(), ContentObjectAssociationType.ID_SUPPRESSES, document);
				}
			}
		} else 
		{
			// TODO createAssociationsForSelection(contentObject, message, startTreeNode, null, startSc, null, request.getUser(), ContentObjectAssociationType.ID_SUPPRESSES, document);
		}
		
		// Add default content 
		if (request.getContentList() != null && request.getContentList().size() > 0) {
			List<ContentObjectAssociation> assos = request.getContentList();
			startContentAssoc.setTypeId(ContentAssociationType.ID_OWNS);
			startContentAssoc.save();
			Map<String, ContentObjectAssociation> cas = new HashMap<String, ContentObjectAssociation>();
			
			for (ContentObjectAssociation ca : contentObject.getContentObjectAssociations()) {
				cas.put(ca.getMessagepointLocale().getLanguageCode(), ca);
			}
			
			for(ContentObjectAssociation ca: assos) {
				ContentObjectAssociation caa = cas.get(ca.getMessagepointLocale().getLanguageCode());
				if (caa != null) {
					Content content = ca.getContent();
					content.save();
					caa.setContent(content);
					caa.setTypeId(ca.getTypeId());
					caa.save();
				}
			}
		}
		
		// For each touchpoint selection, create content association to reference the master 
		for ( ParameterGroupTreeNode tpPGTreeNode : startTreeNode.getAllDescendentNodes() ) 
		{
			ContentObjectAssociation contentAssoc = new ContentObjectAssociation();
			contentAssoc.setContentObject(contentObject);
			contentAssoc.setDataType(contentObject.getFocusOnDataType());
			contentAssoc.setTouchpointPGTreeNode(tpPGTreeNode);
			contentAssoc.setReferencingTouchpointPGTreeNode(tpPGTreeNode.getParentNode());
			contentAssoc.setTypeId(ContentAssociationType.ID_REFERENCES);
			contentAssoc.save();

			if (request.isImportProcess())
			{
				SelectionContent sc = SelectionContent.findByPgTreeNodeId(tpPGTreeNode.getId());
				if (contentObject.isMultipartType()) 
				{
					affectedTreeNodes.add(tpPGTreeNode);
					Set<Zone> zones = contentObject.getZones();
					for (Zone zone : zones) 
					{
						List<ZonePart> parts = zone.getPartsInOrder();
						for (ZonePart zonePart : parts) 
						{
							// TODO createAssociationsForSelection(contentObject, message, tpPGTreeNode, tpPGTreeNode.getParentNode(), sc, zonePart, request.getUser(), ContentObjectAssociationType.ID_REFERENCES, document);
						}
					}
				} else {
					// TODO createAssociationsForSelection(contentObject, message, tpPGTreeNode, tpPGTreeNode.getParentNode(), sc, null, request.getUser(), ContentObjectAssociationType.ID_REFERENCES, document);
				}
			}
			
			affectedTreeNodes.add(tpPGTreeNode);
		}
		
		// Version control
		//TODO change the parameter 1
		// TODO TouchpointVariantStatusManager.executeOwnedContentDataChangedTrigger(startContentAssoc, user, request.isUpdateVariantVersioning());
		if (request.isUpdateVariantVersioning())
			TouchpointVariantStatusManager.executeContentChangeDownstreamEffect(affectedTreeNodes, user, request.isUpdateVariantVersioning());

		 **/
	}

	public void validate(ServiceExecutionContext context) {
		// TODO Auto-generated method stub
	}

	protected ContentObject createContentObject(CreateNewContentObjectServiceRequest request) {
	    Long updateContentObjectId = request.getUpdateContentObjectID();
	    ContentObject contentObject = (updateContentObjectId == null || updateContentObjectId.longValue() == 0) ? null : ContentObject.findById(updateContentObjectId);
        User user = request.getUser();
	    if(contentObject == null) {
            contentObject = new ContentObject(request.getName(), user);
            if(request.getDna() != null) {
                contentObject.setDna(request.getDna());
            }
            if(request.getModelGuid() != null)
                contentObject.setGuid(request.getModelGuid());
	    } else {
            contentObject.createWorkingDataEmpty();

            contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
            contentObject.setLockedFor(user.getId());
            contentObject.setWorkflowAction(null);
            contentObject.setReadyForApproval(false);
            contentObject.setState(WorkflowState.findById(WorkflowState.STATE_CONTENT));
/*
	        if(! contentObject.hasWorkingData()) {
	            if(contentObject.hasActiveData()) {
                    contentObject.createWorkingDataFromActive();
                } else if(contentObject.hasArchivedData()) {
	                contentObject.createWorkingDataFromArchive();
                } else {
	                contentObject.createWorkingDataEmpty();
                }
            }
 */
        }

		contentObject.setObjectType(request.getObjectType());

		if (request.isTouchpointLocal() && request.getObjectType() == ContentObject.OBJECT_TYPE_MESSAGE)
			contentObject.setObjectType((int)request.getContentType().getId());

		contentObject.setAdvanced(request.isAdvanced());
		contentObject.setUsageTypeId(request.getUsageTypeId());

		if ((request.getObjectType() & ContentObject.OBJECT_TYPE_ANY_GLOBAL) > 0)
		{
			// If it's global object, visibleDocuments should be set
			if (request.getVisibleDocuments() != null && !request.getVisibleDocuments().isEmpty()) {
				contentObject.getVisibleDocuments().addAll(request.getVisibleDocuments());
			}
		}
		else {

			// If it's local object (including the message type) the document has to be set

			if (request.getDocument() != null) {
				contentObject.setDocument(request.getDocument());
			} else {
				Zone selectedZone = request.getContentObjectOverviewWrapper().getSelectedZone();
				if (selectedZone != null) {
					contentObject.setDocument(selectedZone.getDocument());
				}
			}
		}

		contentObject.setLockedFor(request.getUser().getId());

		contentObject.setContentType(request.getContentType());

		contentObject.setStructuredContentEnabled(request.isStructuredContentEnabled());
		contentObject.setVariableContentEnabled(request.isVariableContentEnabled());
		contentObject.setStartsFrontFacing(request.getStartsFrontFacing());
		contentObject.setStartsFrontFacingEvery(request.getStartsFrontFacingEvery());

		String externalId = request.getExternalId();
		// if the external id is filled in, then insert it to the tenant meta-data table
		if (externalId != null && !externalId.trim().isEmpty()) {
			TenantMetaData tmd = new TenantMetaData();
			tmd.setInternalId(contentObject.getId());
			tmd.setExternalId(externalId);
			tmd.setModelSignature(TenantMetaData.MODEL_SIGN_MESSAGE);
			tmd.setCreatedBy(request.getUser().getId());
			tmd.setUpdatedBy(request.getUser().getId());
			tmd.save();
		}

		// Set ContentObjectInstaceData for new working instance

		contentObject.setMetatags(request.getMetatags(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setDescription(request.getDescription());
		contentObject.setStartDate(request.getStartDate(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setEndDate(request.getEndDate(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setRepeatDatesAnnually(request.isRepeatsAnnually(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setDeliveryType(request.getDeliveryType(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setFlowType(request.getFlowType(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setGraphicTypeId(request.getGraphicTypeId());
		contentObject.setChannelContextId(request.getChannelContextId());
		contentObject.setKeepContentTogether(request.isKeepContentTogether(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setDataGroup(request.getDataGroup(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setSupportsTables(request.isSupportsTables(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setSupportsForms(request.isSupportsForms(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setSupportsBarcodes(request.isSupportsBarcodes(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setSupportsContentMenus(request.isSupportsContentMenus(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setStartsFrontFacing(request.getStartsFrontFacing(), ContentObject.DATA_TYPE_WORKING);
		contentObject.setStartsFrontFacingEvery(request.getStartsFrontFacingEvery(), ContentObject.DATA_TYPE_WORKING);

		if ( request.isTouchpointLocal() ) {
			if (request.getContentObjectOverviewWrapper().getPlaceholder() == null) {
				contentObject.setRenderAsTaggedText(request.isRenderAsTaggedText(), ContentObject.DATA_TYPE_WORKING);
			} else {
				contentObject.setRenderAsTaggedText(false, ContentObject.DATA_TYPE_WORKING);
			}
		}

		// contentSpecializationTypeId
		// public static final int ID_CONTENT_TYPE_SHARED_FREEFORM  = 2;
		if ( request.getContentSpecializationTypeId() == 2 || ((contentObject.getIsTouchpointLocal() || contentObject.getIsGlobalSmartText()) && contentObject.isSupportsTables(ContentObject.DATA_TYPE_WORKING)) ) {
			try {
				if ( request.getContentObjectOverviewWrapper().getPlaceholder() != null )
					contentObject.setCanvasMaxWidth( request.getContentObjectOverviewWrapper().getPlaceholder().getDefaultCanvasWidth(), ContentObject.DATA_TYPE_WORKING );
				else
					contentObject.setCanvasMaxWidth(DecimalValueUtil.hydrate( String.valueOf(FreeformContentUtil.DYNAMIC_CANVAS_DEFAULT_WIDTH) ), ContentObject.DATA_TYPE_WORKING);
				contentObject.setCanvasMaxHeight(DecimalValueUtil.hydrate( String.valueOf(FreeformContentUtil.DYNAMIC_CANVAS_DEFAULT_HEIGHT) ), ContentObject.DATA_TYPE_WORKING);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}


		if (request.getParameterGroupId() > 0) {
			contentObject.setParameterGroup(request.getParameterGroup(), ContentObject.DATA_TYPE_WORKING);
		} else {
		    contentObject.setParameterGroup(null);
        }

		contentObject.setState(WorkflowState.findById(WorkflowState.STATE_OVERVIEW));

		if (request.getComment() != null && request.getComment().getComment()!=null && !request.getComment().getComment().trim().isEmpty()) {
			ContentObjectComment comment = request.getComment();
			comment.setCreated(new Date(System.currentTimeMillis()));
			comment.setContentObject(contentObject);
			comment.setDataType(ContentObject.DATA_TYPE_WORKING);
			comment.save(true);
			contentObject.getComments().add(request.getComment());
		}

		// TODO This was only for static message
		//
		if ( !contentObject.isStructuredContentEnabled() && !contentObject.isDynamicVariantEnabled() && request.getContentList() != null && !request.getContentList().isEmpty())
		{
			List<ContentObjectAssociation> contentAssocs = request.getContentList();
			for (ContentObjectAssociation ca : contentAssocs) {
				ca.setContentObject(contentObject);
				ca.save();
				removeExistingContentObjectAssociation(contentObject.getContentObjectAssociations(), ca);
				contentObject.getContentObjectAssociations().add(ca);
			}
		}

		if ( request.getFormWrapper() != null ) {
			MetadataForm metadataForm = request.getFormWrapper().getMetadataForm();
			metadataForm.save(true);
			contentObject.setMetadataForm( metadataForm, ContentObject.DATA_TYPE_WORKING );
		} else {
            contentObject.setMetadataForm( null, ContentObject.DATA_TYPE_WORKING );
        }

		contentObject.save();

		return contentObject;
	}

	private void removeExistingContentObjectAssociation(Set<ContentObjectAssociation> contentObjectAssociations, ContentObjectAssociation ca) {
        Set<ContentObjectAssociation> existingContentObjectAssociations = contentObjectAssociations
                .stream()
                .filter(eca->
                        {
                            if(eca.getDataType() != ca.getDataType()) return false;

                            if(eca.getContentObjectPGTreeNode() == null && ca.getContentObjectPGTreeNode() != null) return false;
                            if(eca.getContentObjectPGTreeNode() != null && ca.getContentObjectPGTreeNode() == null) return false;
                            if(eca.getContentObjectPGTreeNode() != null && ca.getContentObjectPGTreeNode() != null)
                            {
                                if(! eca.getContentObjectPGTreeNode().getDna().equals(ca.getContentObjectPGTreeNode().getDna())) {
                                    return false;
                                }
                            }

                            if(eca.getTouchpointPGTreeNode() == null && ca.getTouchpointPGTreeNode() != null) return false;
                            if(eca.getTouchpointPGTreeNode() != null && ca.getTouchpointPGTreeNode() == null) return false;
                            if(eca.getTouchpointPGTreeNode() != null && ca.getTouchpointPGTreeNode() != null)
                            {
                                if(! eca.getTouchpointPGTreeNode().getDna().equals(ca.getTouchpointPGTreeNode().getDna())) {
                                    return false;
                                }
                            }

                            if(eca.getMessagepointLocale() == null && ca.getMessagepointLocale() != null) return false;
                            if(eca.getMessagepointLocale() != null && ca.getMessagepointLocale() == null) return false;
                            if(eca.getMessagepointLocale() != null && ca.getMessagepointLocale() != null)
                            {
                                if(eca.getMessagepointLocale().getId() != (ca.getMessagepointLocale().getId())) {
                                    return false;
                                }
                            }

                            if(eca.getZonePart() == null && ca.getZonePart() != null) return false;
                            if(eca.getZonePart() != null && ca.getZonePart() == null) return false;
                            if(eca.getZonePart() != null && ca.getZonePart() != null)
                            {
                                if(! eca.getZonePart().getDna().equals(ca.getZonePart().getDna())) {
                                    return false;
                                }
                            }

                            return true;
                        }
                )
                .collect(Collectors.toSet());
;
        if(existingContentObjectAssociations != null && ! existingContentObjectAssociations.isEmpty()) {
            contentObjectAssociations.removeAll(existingContentObjectAssociations);
        }
    }

	public static ServiceExecutionContext createContext(long requestorUserId) {
		SimpleExecutionContext context = new SimpleExecutionContext();
		context.setServiceName(SERVICE_NAME);

		CreateNewContentObjectServiceRequest request = new CreateNewContentObjectServiceRequest();
		request.setUserid(requestorUserId);
		context.setRequest(request);

		CreateNewContentObjectServiceResponse serviceResp = new CreateNewContentObjectServiceResponse();
		serviceResp.getResultStatus().setReturnCode(SimpleServiceResponse.SUCCESSFUL);
		context.setResponse(serviceResp);

		return context;
	}

}
