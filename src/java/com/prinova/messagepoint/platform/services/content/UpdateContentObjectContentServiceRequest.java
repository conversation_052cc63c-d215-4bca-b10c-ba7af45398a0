package com.prinova.messagepoint.platform.services.content;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.ServiceRequest;

import java.util.HashMap;
import java.util.Map;

public class UpdateContentObjectContentServiceRequest implements ServiceRequest {

	private static final long serialVersionUID = 7422435349943863729L;

	private int									action;

	private long 								contentObjectId;
	private int									dataType = ContentObject.DATA_TYPE_WORKING;
	private ContentAssociationType 				contentAssociationType = new ContentAssociationType(ContentAssociationType.ID_OWNS);

	private Map<Long, ContentVO> 				contents = new HashMap<>();
	private Map<Long, Map<Long, ContentVO>> 	multipartContents = new HashMap<>();
	private ContentObjectMultipartContentVO		multipartContentVO;

	private String								canvasMaxWidth;
	private String								canvasMaxHeight;
	private String								canvasTrimWidth;
	private String								canvasTrimHeight;
	
	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}
	public long getContentObjectId() {
		return contentObjectId;
	}
	public void setContentObjectId(long contentObjectId) {
		this.contentObjectId = contentObjectId;
	}

	public int getDataType() {
		return dataType;
	}

	public void setDataType(int dataType) {
		this.dataType = dataType;
	}

	public Map<Long, ContentVO> getContents() {
		return contents;
	}
	public void setContents(Map<Long, ContentVO> contents) {
		this.contents = contents;
	}
	public Map<Long, Map<Long, ContentVO>> getMultipartContents() {
		return multipartContents;
	}
	public void setMultipartContents(Map<Long, Map<Long, ContentVO>> multipartContents) {
		this.multipartContents = multipartContents;
	}
	public void setMultipartContentVO(ContentObjectMultipartContentVO multipartContentVO) {
		this.multipartContentVO = multipartContentVO;
	}
	public ContentObjectMultipartContentVO getMultipartContentVO() {
		return multipartContentVO;
	}
	public String getCanvasMaxWidth() {
		return canvasMaxWidth;
	}
	public void setCanvasMaxWidth(String canvasMaxWidth) {
		this.canvasMaxWidth = canvasMaxWidth;
	}
	public String getCanvasMaxHeight() {
		return canvasMaxHeight;
	}
	public void setCanvasMaxHeight(String canvasMaxHeight) {
		this.canvasMaxHeight = canvasMaxHeight;
	}
	public String getCanvasTrimWidth() {
		return canvasTrimWidth;
	}
	public void setCanvasTrimWidth(String canvasTrimWidth) {
		this.canvasTrimWidth = canvasTrimWidth;
	}
	public String getCanvasTrimHeight() {
		return canvasTrimHeight;
	}
	public void setCanvasTrimHeight(String canvasTrimHeight) {
		this.canvasTrimHeight = canvasTrimHeight;
	}

	public ContentAssociationType getContentAssociationType() {
		return contentAssociationType;
	}

	public void setContentAssociationType(ContentAssociationType contentAssociationType) {
		this.contentAssociationType = contentAssociationType;
	}

}