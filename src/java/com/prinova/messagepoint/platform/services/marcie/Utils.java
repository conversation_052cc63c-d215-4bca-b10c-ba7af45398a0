package com.prinova.messagepoint.platform.services.marcie;

import ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils;
import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;

public class Utils{
    public static JsonObject handleResponse(final HttpResponse response) {
        StatusLine statusLine = response.getStatusLine();
        if(statusLine.getStatusCode() >= 300) {
            return buildFailure(statusLine.getStatusCode(), statusLine.getReasonPhrase());
        }

        HttpEntity entity = response.getEntity();

        if(entity == null) {
            return buildFailure("Response contains no content");
        }

        String payload = null;
        try {
            payload = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            if(Strings.isBlank(payload)) {
                return buildFailure("Response contains no content");
            }
            JsonObject responseDetailsJsonObject = ApplicationUtils.readJsonObject(payload);
            JsonObject responseJsonObject = new JsonObjectBuilder()
                    .add("status", new JsonObjectBuilder()
                            .addProperty("success", Boolean.TRUE)
                            .addProperty("code", 200)
                            .build()).build();
            responseJsonObject.add("details", responseDetailsJsonObject);
            return responseJsonObject;

        } catch(IOException | JsonSyntaxException e) {
            return buildFailure("Error parsing Response: " + e.getMessage());
        }

    }


    public static JsonObject buildFailure(String message) {
        return buildFailure(400, message);
    }

    public static JsonObject buildFailure(Integer code, String message) {
        return new JsonObjectBuilder()
                .add("status", new JsonObjectBuilder()
                        .addProperty("success", Boolean.FALSE)
                        .addProperty("code", code)
                        .build())
                .add("error", new JsonObjectBuilder()
                        .addProperty("message", StringUtils.isEmpty(message) ? getMessage("error.semnatex.request") : message)
                        .build())
                .build();
    }

    public static boolean hasError(JsonObject jsonResponse) {
        if(jsonResponse == null) return true;

        Boolean isSuccessful = ApplicationUtils.getValueForJsonPath(jsonResponse, "status.success", Boolean.class);
        if(isSuccessful != null && !isSuccessful) {
            return true;
        }
        Boolean hasError = ApplicationUtils.getValueForJsonPath(jsonResponse, "status.error", Boolean.class);
        return hasError != null && hasError;
    }
}
