package com.prinova.messagepoint.platform.services.marcie.translate;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

public class HttpClientTranslateHandler implements MessagepointTranslateHandler{
    private static final Log log = LogUtil.getLog(HttpClientTranslateHandler.class);


    public JsonObject translate(Metadata metadata, String filePath, TranslationCallBackConfig config) {
        JsonObject response;
        Path path = Paths.get(filePath);
        String fileName = path.getFileName().toString();
        String extension = FileUtil.getFileExtension(fileName);
        String zipFilePath = filePath;
        if(!"zip".equalsIgnoreCase(extension) ) {
            File outputDirectory =
                    new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + File.separator
                            + "toTranslate" + File.separator);

            if(!outputDirectory.mkdirs()){
                log.warn("Error creating directories for " + outputDirectory);
            }
            String fileNoExt = FilenameUtils.removeExtension(fileName);

            zipFilePath = outputDirectory + File.separator + fileNoExt + ".zip";
            String[] fileArray = new String[] {filePath};
            FileUtil.createZip(fileArray, zipFilePath);
            log.info("Translated file was compressed to " + zipFilePath);
        }
        File file = new File(zipFilePath);
        long sizeInBytes = file.length();
        long sizeInMb = sizeInBytes / (1024 * 1024);

        if(sizeInMb < 4) {
            log.info("The zip file for translation will be uploaded with a multipart request " + zipFilePath);
            response = TranslateMessagepointClient.translateSmallFile(metadata, zipFilePath, config);
        } else{
            log.info("The large zip file for translation will be uploaded first in S3 bucket and then sent to translation " + zipFilePath);
            response = TranslateMessagepointClient.translateLargeFile(metadata, zipFilePath, config);
        }
        return response;
    }
}
