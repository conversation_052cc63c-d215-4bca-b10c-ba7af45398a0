package com.prinova.messagepoint.platform.services.marcie.translate;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.marcie.MarcieUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.FileEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;

import static com.prinova.messagepoint.platform.services.marcie.Utils.buildFailure;
import static com.prinova.messagepoint.platform.services.marcie.Utils.handleResponse;
import static com.prinova.messagepoint.platform.services.marcie.Utils.hasError;

public class TranslateMessagepointClient{
    private static final Log log = LogUtil.getLog(TranslateMessagepointClient.class);

    public static JsonObject translateSmallFile(Metadata metadata, String filePath, TranslationCallBackConfig callBackConfig) {
        String translateUploadUrl = MarcieUtils.getTranslateSendForTranslationUrl(metadata.getTranslationProvider());
        return sendMultipartPost(metadata, translateUploadUrl, filePath, callBackConfig);
    }

    public static JsonObject translateLargeFile(Metadata metadata, String filePath, TranslationCallBackConfig callBackConfig) {
        String presignRequestUrl = MarcieUtils.getTranslateRequestResignUrl();
        JsonObject presignURLResponseJson = sendPresignURLRequest(metadata.getTranslationProvider(), presignRequestUrl, filePath);
        System.out.println("presignURLResponseJson " + presignURLResponseJson);

        if(hasError(presignURLResponseJson)) {
            return presignURLResponseJson;
        }
        String putURL = presignURLResponseJson.get("PUT_URL") == null ? presignURLResponseJson.get("details").getAsJsonObject().get("PUT_URL").getAsString() :  presignURLResponseJson.get("PUT_URL").getAsString();

        JsonObject putResponse = sendPutRequestToSaveInS3(putURL, filePath);
        if(hasError(putResponse)) {
            return putResponse;
        }

        String translateUploadUrl = MarcieUtils.getTranslateSendForTranslationUrl(metadata.getTranslationProvider());
        JsonObject uploadFinalResponse = sendPostWithS3Location(translateUploadUrl, filePath, callBackConfig);
        return uploadFinalResponse;
    }

    /**
     * Sends translate requests to marcie for different translate services (e.g. CQF, Trados, GenAI).
     * In case of GenAI, in case genaiModel is missing from TranslationCallBackConfig, the value from the lambda configuration parameter will be used.
     * @param metadata
     * @param url
     * @param filePath
     * @param callBackConfig
     * @return
     */
    private static JsonObject sendMultipartPost(Metadata metadata, String url, String filePath, TranslationCallBackConfig callBackConfig) {
        if( StringUtils.isEmpty(url)){
            return buildFailure(500, "No url provided for the translation");
        }
        log.info("Calling translate service provider on URL " + url);
        HttpResponse httpResponse;
        try(CloseableHttpClient httpclient = HttpClients.custom().build()) {

            FileBody bin = new FileBody(new File(filePath));
            ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
            String callBackConfigJson = ow.writeValueAsString(callBackConfig);
            StringBody conf = new StringBody(callBackConfigJson, ContentType.APPLICATION_JSON);

            HttpEntity entity = MultipartEntityBuilder.create()
                    .addPart("file", bin)
                    .addPart("callBackConfig", conf)
                    .build();

            HttpPost httppost = new HttpPost(url);
            httppost.setEntity(entity);

            httpResponse = httpclient.execute(httppost);

            return handleResponse(httpResponse);

        } catch(IOException e) {
            log.error("sendMultipartPost failed with error " + e.getMessage());
            return buildFailure(500, e.getMessage());
        }
    }

    private static JsonObject sendPresignURLRequest(String translationProvider, String url, String filePath) {
        try(CloseableHttpClient httpclient = HttpClients.custom().build()) {
            Path path = Paths.get(filePath);
            String fileName = path.getFileName().toString();
            JsonObject payload = new JsonObject();
            payload.addProperty("client", translationProvider);
            payload.addProperty("fileName", fileName);

            final StringEntity entity = new StringEntity(payload.toString(), StandardCharsets.UTF_8);

            final HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-Type", "application/json");

            HttpResponse httpResponse = httpclient.execute(httpPost);
            return handleResponse(httpResponse);

        } catch(IOException e) {
            log.error("sendPresignURLRequest failed with error " + e.getMessage());
            return buildFailure(500, e.getMessage());
        }
    }

    private static JsonObject sendPutRequestToSaveInS3(String url, String filePath) {
        try(CloseableHttpClient httpclient = HttpClients.custom().build()) {
            final FileEntity entity = new FileEntity(new File(filePath));
            final HttpPut httpPut = new HttpPut(url);
            httpPut.setEntity(entity);
            httpPut.setHeader("Accept", "*/*");
            httpPut.setHeader("Content-Type", "application/zip");
            HttpResponse httpResponse = httpclient.execute(httpPut);
            StatusLine statusLine = httpResponse.getStatusLine();
            if(statusLine.getStatusCode() >= 300) {
                return buildFailure(statusLine.getStatusCode(), statusLine.getReasonPhrase());
            }
            return new JsonObjectBuilder()
                    .add("status", new JsonObjectBuilder()
                            .addProperty("success", Boolean.TRUE)
                            .addProperty("code", 200)
                            .build()).build();



        } catch(IOException e) {
            log.error("sendPutRequestToSaveInS3 failed with error " + e.getMessage());
            return buildFailure(500, e.getMessage());
        }
    }

    private static JsonObject sendPostWithS3Location(String url, String filePath, TranslationCallBackConfig callBackConfig) {
        if(StringUtils.isEmpty(url)) {
            return buildFailure(500, "No URL provided for the translation");
        }
        try(CloseableHttpClient httpclient = HttpClients.custom().build()) {
            Path path = Paths.get(filePath);
            String fileName = path.getFileName().toString();
            JsonObject payload = new JsonObject();
            payload.addProperty("fileName", fileName);

            JsonObject callBackConf = new JsonObject();
            callBackConf.addProperty("callbackURL", callBackConfig.getCallbackURL());
            callBackConf.addProperty("podId", callBackConfig.getBranchId());
            callBackConf.addProperty("instanceGuid", callBackConfig.getNodeId());
            callBackConf.addProperty("originalFileName", callBackConfig.getOriginalFileName());
            payload.add("callBackConfig", callBackConf);

            final StringEntity entity = new StringEntity(payload.toString(), StandardCharsets.UTF_8);

            final HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "*/*");
            httpPost.setHeader("Content-Type", "application/json");

            HttpResponse httpResponse = httpclient.execute(httpPost);
            return handleResponse(httpResponse);

        } catch(IOException e) {
            log.error("sendPutRequestToSaveInS3 failed with error " + e.getMessage());
            return buildFailure(500, e.getMessage());
        }
    }


}