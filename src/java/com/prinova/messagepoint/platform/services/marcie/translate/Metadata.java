package com.prinova.messagepoint.platform.services.marcie.translate;

public class Metadata {
    private String translationProvider;
    private String projectId;

    //like "en-US"
    private String sourceLanguage;

    //like "es-MX"
    private String targetLanguage;

    public Metadata translationProvider(String translationProvider) {
        this.translationProvider = translationProvider;
        return this;
    }

    public String getTranslationProvider() {
        return translationProvider;
    }

    public void setTranslationProvider(String translationProvider) {
        this.translationProvider = translationProvider;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }
}
