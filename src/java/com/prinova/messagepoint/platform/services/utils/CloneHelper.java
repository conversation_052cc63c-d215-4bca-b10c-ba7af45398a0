package com.prinova.messagepoint.platform.services.utils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.*;

import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.dataadmin.LookupTableVersionMapping;

import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;

import com.prinova.messagepoint.model.testing.DataFile;
import com.prinova.messagepoint.util.*;
import org.hibernate.Hibernate;
import org.hibernate.Session;
import org.hibernate.collection.spi.PersistentCollection;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.insert.RateScheduleCollection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionItem;
import com.prinova.messagepoint.model.targeting.ConditionItemValue;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.ModelVersionMappingId;

public class CloneHelper {
    public static final String DataKey_ConnectedInterviewUpdated = "ConnectedInterviewUpdated";
    public static final String DataKey_ConnectedInterviewPrimaryVariableDna = "ConnectedInterviewPrimaryVariableDna";
    public static final String DataKey_ConnectedInterviewReferenceVariableDna = "ConnectedInterviewReferenceVariableDna";
    public static final String DataKey_ConnectedInterviewOrder2DataElementMap = "ConnectedInterviewOrder2DataElementMap";

    public static Object hibernateInitialize(Object object) {
        if (object != null) {
            if (object instanceof HibernateProxy) {
                boolean isObjectInitialized = Hibernate.isInitialized(object);
                Hibernate.initialize(object);
                if (object instanceof HibernateProxy) {
                    object = ((HibernateProxy) object)
                            .getHibernateLazyInitializer()
                            .getImplementation();
                }
            }

            if (object instanceof Collection) {
                object = hibernateInitializeCollection(object);
            }
        }
        return object;
    }

    private static Object hibernateInitializeCollection(Object object) {
        if (object instanceof Collection && (object instanceof Set || object instanceof List)) {
            Collection sourceObjectsCollection = (Collection) object;
            if (!sourceObjectsCollection.isEmpty()) {
                Collection newCollection = (object instanceof List) ? new ArrayList() : new HashSet<>();

                for (Object childObject : sourceObjectsCollection) {
                    if (childObject instanceof HibernateProxy) {
                        childObject = hibernateInitialize(childObject);
                    }

                    if (childObject instanceof Collection) {
                        childObject = hibernateInitializeCollection(childObject);
                    }

                    newCollection.add(childObject);
                }

                object = newCollection;
            }
        }

        return object;
    }

    private static class ClonedObjectInfo {
        Class<? extends UpdatableMessagePointModel> clazz;
        Serializable sourceObjectID;
        Serializable targetObjectID;
        UpdatableMessagePointModel sourceObject;
        UpdatableMessagePointModel clonedObject;

        public ClonedObjectInfo(UpdatableMessagePointModel sourceObject, UpdatableMessagePointModel clonedObject, boolean isNew) {
            clazz = getObjectClass(sourceObject);
            sourceObject = (UpdatableMessagePointModel) hibernateInitialize(sourceObject);
            sourceObjectID = getObjectId(sourceObject);
            targetObjectID = isNew ? null : getObjectId(clonedObject);
            ;
            this.sourceObject = sourceObject;
            this.clonedObject = clonedObject;
        }

        public Serializable getSourceID() {
            return sourceObjectID;
        }

        public Serializable getTargetID() {
            return targetObjectID;
        }

        public void setTargetID(Serializable targetID) {
            this.targetObjectID = targetID;
        }

        public UpdatableMessagePointModel getClonedObject() {
            if (clonedObject != null && targetObjectID != null) {
                execInSaveSession(() -> {
                    try {
                        UpdatableMessagePointModel oldClonedObject = clonedObject;
                        Session session = HibernateUtil.getManager().getSession();
                        if (!session.contains(clonedObject)) {
                            try {
                                if(clazz == ContentObjectData.class) {
                                    String ids[] = targetObjectID.toString().split("-");
                                    long contentObjectId = Long.parseLong(ids[0]); ((ContentObjectData) clonedObject).getContentObject().getId();
                                    int dataType = Integer.parseInt(ids[1]);
                                    clonedObject = ContentObjectData.findAllByContentObjectIdAndDataType(contentObjectId, dataType);
                                }
                                else {
                                    clonedObject = session.get(clazz, targetObjectID);
                                }
                                if(clonedObject != null) {
                                    session.refresh(clonedObject);
                                }
                            } catch (Exception ex) {
                                clonedObject = null;
//                                ex.printStackTrace();
//                                throw ex;
                            } catch (StackOverflowError se) {
                                se.printStackTrace();
                                throw se;
                            }

                            if (clonedObject == null || !session.contains(clonedObject)) {                  // Not attached by refresh
                                clonedObject = session.get(clazz, targetObjectID);  // (UpdatableMessagePointModel) session.merge(clonedObject);
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        throw ex;
                    }
                });
            }
            return clonedObject;
        }
    }

    public static final int ID_CLONING_TYPE_INACTIVE = 0;
    public static final int ID_CLONING_TYPE_GLOBAL = 1;
    public static final int ID_CLONING_TYPE_LOCAL = 2;

    private static ThreadLocal<Boolean> crossInstanceClone = ThreadLocal.withInitial(() -> false);
    private static ThreadLocal<Map<String, ClonedObjectInfo>> source2InfoMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Collection<UpdatableMessagePointModel>> savedObjects = ThreadLocal.withInitial(() -> new ArrayList<>());
    private static ThreadLocal<User> requestor = new ThreadLocal<>();
    private static ThreadLocal<Map<String, Session>> sessionsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Document> sourceDocument = new ThreadLocal<>();
    private static ThreadLocal<Document> targetDocument = new ThreadLocal<>();
    private static ThreadLocal<Boolean> isImporting = ThreadLocal.withInitial(() -> false);
    private static ThreadLocal<Integer> isCloning = ThreadLocal.withInitial(() -> ID_CLONING_TYPE_INACTIVE);
    private static ThreadLocal<Boolean> isSyncing = ThreadLocal.withInitial(() -> false);
    private static ThreadLocal<Boolean> isFromArchive = ThreadLocal.withInitial(() -> false);
    private static ThreadLocal<String> sourceSchema = new ThreadLocal<>();
    private static ThreadLocal<Set<Long>> clonedContentIDs = ThreadLocal.withInitial(() -> new HashSet<>());
    private static ThreadLocal<Set<Long>> clonedComplexValueIDs = ThreadLocal.withInitial(() -> new HashSet<>());
    private static ThreadLocal<Map<Long, Long>> clonedDataCollectionIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Map<Long, Long>> clonedDataSourceIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Map<Long, Long>> clonedLookupTableIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Map<Long, Long>> clonedDataElementVariableIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Map<Long, Long>> clonedTargetGroupIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Map<Long, Long>> clonedTargetRuleIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());
    private static ThreadLocal<Set<Long>> clonedSyncHistoryIDsSet = ThreadLocal.withInitial(() -> new HashSet<>());
    private static ThreadLocal<Map<Long, Long>> clonedMetadataFormDefinitionIDsMap = ThreadLocal.withInitial(() -> new HashMap<>());

    private static ThreadLocal<Map<String, Object>> syncData = ThreadLocal.withInitial(()->new HashMap<>());

    public static void clearResources() { // Should be invoked after task completed.
        source2InfoMap.get().clear();
        savedObjects.get().clear();
        sourceDocument.remove();
        targetDocument.remove();
        sessionsMap.get().clear();
        sourceSchema.remove();
        usedNames.set(new HashMap<>());
        usedFriendlyNames.set(new HashMap<>());
        usedDocumentContentObjectNames.set(new HashMap<>());
        clonedSyncHistoryIDsSet.set(new HashSet<>());
        clonedMetadataFormDefinitionIDsMap.set(new HashMap<>());
        syncData.set(new HashMap<>());
    }

    public static void startCrossInstanceClone() {
        crossInstanceClone.set(true);
        sessionsMap.get().clear();
        usedNames.set(new HashMap<>());
        usedFriendlyNames.set(new HashMap<>());
        usedDocumentContentObjectNames.set(new HashMap<>());
    }

    public static void stopCrossInstanceClone() {
        sessionsMap.get().clear();
        crossInstanceClone.set(false);
        usedNames.set(new HashMap<>());
        usedFriendlyNames.set(new HashMap<>());
        usedDocumentContentObjectNames.set(new HashMap<>());
    }

    public static void addSession(String sessionIdentifier, Session session) {
        if(sessionIdentifier == null || sessionIdentifier.isEmpty()) {
            sessionIdentifier = "NULL=POD_MASTER";
        }
        sessionsMap.get().put(sessionIdentifier, session);
    }

    public static Session getSession(String sessionIdentifier) {
        if(sessionIdentifier == null || sessionIdentifier.isEmpty()) {
            sessionIdentifier = "NULL=POD_MASTER";
        }
        return sessionsMap.get().get(sessionIdentifier);
    }

    public static Session getCloneSession() {
        if (!crossInstanceClone.get()) {
            return HibernateUtil.getManager().getSession();
        }

        return getSession(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier());
    }

    public static void setSourceDocument(Document source) {
        sourceDocument.set(source);
    }

    public static Document getSourceDocument() {
        Document sourceDocument = CloneHelper.sourceDocument.get();

        if(sourceDocument != null) {
            long documentId = sourceDocument.getId();
            sourceDocument = queryInSchema(sourceDocument.getObjectSchemaName(), () -> Document.findById(documentId));
            CloneHelper.sourceDocument.set(sourceDocument);
        }

        return sourceDocument;
    }

    public static void setTargetDocument(Document target) {
        targetDocument.set(target);
    }

    public static Document getTargetDocument() {
        Document targetDocument = CloneHelper.targetDocument.get();

        if(targetDocument != null) {
            long documentId = targetDocument.getId();
            targetDocument = queryInSaveSession(() -> Document.findById(documentId));
            CloneHelper.targetDocument.set(targetDocument);
        }

        return targetDocument;
    }

    public static void setIsImporting(boolean isImportingDocument) {
        if( isImportingDocument && ! isImporting.get())
        {
            usedNames.set(new HashMap<>());
            usedFriendlyNames.set(new HashMap<>());
            usedDocumentContentObjectNames.set(new HashMap<>());
        }

        isImporting.set(isImportingDocument);
    }

    public static void setIsCloning(int cloningType) {
        isCloning.set(cloningType);
    }

    public static void setIsSynchronizing(boolean isSynchronizingDocument) {
        isSyncing.set(isSynchronizingDocument);
    }

    public static boolean getIsImporting() {
        return isImporting.get();
    }

    public static int getIsCloning() {
        return isCloning.get();
    }

    public static boolean getIsCloningActive() {
        return isCloning.get() > ID_CLONING_TYPE_INACTIVE;
    }

    public static boolean getIsCloningGlobal() {
        return isCloning.get() == ID_CLONING_TYPE_GLOBAL;
    }

    public static boolean getIsCloningLocal() {
        return isCloning.get() == ID_CLONING_TYPE_LOCAL;
    }

    public static boolean getIsSynchronizing() {
        return isSyncing.get();
    }

    public static void setIsFromArchive(boolean isWorkingCopyCrreatedFromArchive) {
        isFromArchive.set(isWorkingCopyCrreatedFromArchive);
    }

    public static boolean getIsFromArchive() {
        return isFromArchive.get();
    }

    public static boolean getIsCrossInstanceClone() {
        return crossInstanceClone != null && crossInstanceClone.get();
    }

    public static void setSourceSchema(String source) {
        sourceSchema.set(source);
    }

    public static String getSourceSchema() {
        return sourceSchema.get();
    }

    public static void setData(String key, Object value) {
        syncData.get().put(key, value);
    }

    public static Object getData(String key) {
        return syncData.get().get(key);
    }

    private static Serializable getObjectId(UpdatableMessagePointModel sourceObject) {
        Serializable id = null;

        if (sourceObject == null) {
            System.out.println("Null source object !");
        } else if (sourceObject instanceof ContentObjectData) {
            id = ((ContentObjectData) sourceObject).getContentObject().getId() + "-" + ((ContentObjectData)sourceObject).getDataType();
        } else if (sourceObject instanceof IdentifiableMessagePointModel) {
            Long objectId = ((IdentifiableMessagePointModel) sourceObject).getId();
            if (objectId != null && objectId.longValue() != 0) {
                id = objectId;
            }
        } else if (sourceObject instanceof IdentifiableMessagePointModelInt) {
            Integer objectId = ((IdentifiableMessagePointModelInt) sourceObject).getId();
            if (objectId != null && objectId.intValue() != 0) {
                id = objectId;
            }
        } else if (sourceObject instanceof ConditionItemValue) {
            Long objectId = ((ConditionItemValue) sourceObject).getId();
            if (objectId != null && objectId.longValue() != 0) {
                id = objectId;
            }
        } else if (sourceObject instanceof ModelVersionMapping) {
            ModelVersionMappingId mvmId = ((ModelVersionMapping) sourceObject).getId();
//            id = mvmId.getModelId() + "#" + mvmId.getModelInstanceId();
            id = mvmId;
        } else {
            System.out.println("Bad id !");
        }

        return id;
    }

    private static <T extends UpdatableMessagePointModel> Class<T> getObjectClass(T sourceObject) {
        sourceObject = (T) hibernateInitialize(sourceObject);
        return (Class<T>) sourceObject.getClass();
    }

    private static <T extends UpdatableMessagePointModel> String getSourceObjectKey(T sourceObject) {
        Class<T> clazz = getObjectClass(sourceObject);
        Serializable objectId = getObjectId(sourceObject);
        String objectSchemaName = sourceObject.getObjectSchemaName();
        if (objectId instanceof Number) {
            return objectSchemaName + "#" + clazz.getName() + "#" + objectId;
        } else if (objectId instanceof ModelVersionMappingId) {
            ModelVersionMappingId mvmId = (ModelVersionMappingId) objectId;
            long modelId = mvmId.getModelId();
            long instId = mvmId.getModelInstanceId();
            return objectSchemaName + "#" + clazz.getName() + "#" + modelId + "#" + instId;
        }

        return objectSchemaName + "#" + clazz.getName() + "#" + objectId.toString();
    }

    public static <T extends UpdatableMessagePointModel> T getAlreadyClonedObject(T sourceObject) {
        if (sourceObject == null) return null;

        Map<String, ClonedObjectInfo> clonedObjectMapInThread = source2InfoMap.get();
        if (clonedObjectMapInThread == null) {
            return null;
        }

        try {
            sourceObject = (T) hibernateInitialize(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        String sourceObjectKey = getSourceObjectKey(sourceObject);

        ClonedObjectInfo clonedObjectInfo = clonedObjectMapInThread.get(sourceObjectKey);
        if (clonedObjectInfo != null) {
            return (T) clonedObjectInfo.getClonedObject();
        }

        return null;
    }

    private static <T extends UpdatableMessagePointModel> T getSimiliarInDBObject(T sourceObject) throws Exception {
        T clonedObject = null;

        try {
            if (sourceObject instanceof DocumentObjectModel) {
                DocumentObjectModel sourceDocumentObjectModel = (DocumentObjectModel) sourceObject;
                Document sourceDocumentFromObject = sourceDocumentObjectModel.getDocument();
                if (sourceDocumentFromObject == null) {
                    sourceDocumentFromObject = sourceDocument.get();
                }
                if (sourceDocumentFromObject != null) {
                    Document clonedDocument = getAlreadyClonedObject(sourceDocumentFromObject);
                    if (clonedDocument != null) {
                        String dna = sourceDocumentObjectModel.getDna();
                        if (sourceObject instanceof Zone) {
                            clonedObject = queryInSaveSession(() -> (T) Zone.findByDnaAndDocument(dna, clonedDocument));
                        } else if (sourceObject instanceof DocumentSection) {
                            clonedObject = queryInSaveSession(() -> (T) DocumentSection.findByDnaAndDocument(dna, clonedDocument));
                        } else if (sourceObject instanceof TouchpointSelection) {
                            clonedObject = queryInSaveSession(() -> (T) TouchpointSelection.findByDnaAndDocument(dna, clonedDocument));
                        } else if (sourceObject instanceof LanguageSelection) {
                            clonedObject = queryInSaveSession(() -> (T) LanguageSelection.findByDnaAndDocument(dna, clonedDocument));
                        } else if (sourceObject instanceof TouchpointLanguage) {
                            TouchpointLanguage sourceLanguage = (TouchpointLanguage) sourceObject;
                            Set<TouchpointLanguage> languages = queryInSaveSession(() -> clonedDocument.getTouchpointLanguages());
                            String languageName = sourceLanguage.getName();
                            String languageCode = sourceLanguage.getLanguageCode();
                            clonedObject = queryInSaveSession(() -> (T) languages.stream().filter(l -> l.getName().equals(languageName) && l.getMessagepointLocale().getId() == sourceLanguage.getMessagepointLocale().getId() && l.getLanguageCode().equals(languageCode)).findFirst().orElse(null));
                        } else if (sourceObject instanceof ParameterGroupTreeNode) {
                            throw new Exception("Invalid invoking");
                        }
                    }
                }
            } else if (sourceObject instanceof ParameterGroupTreeNode) {
                ParameterGroupTreeNode sourceParameterGroupTreeNode = (ParameterGroupTreeNode) sourceObject;
                ParameterGroupTreeNode clonedParameterGroupTreeNode = null;

                if (sourceParameterGroupTreeNode.getContentObject() != null) {
                    ContentObject sourceContentObject = sourceParameterGroupTreeNode.getContentObject();
                    ContentObject targetContentObject = assignAlreadyClonedObject(sourceContentObject);
                    if (targetContentObject != null) {
                        clonedParameterGroupTreeNode = CloneHelper.queryInSaveSession(() -> ParameterGroupTreeNode.findByDNAandDataType(sourceParameterGroupTreeNode.getDna(), sourceParameterGroupTreeNode.getDataType(), targetContentObject));
                    }
                }

                if(clonedParameterGroupTreeNode == null) {
                    TouchpointSelection sourceTouchpointSelection = TouchpointSelection.findByPgTreeNodeId(sourceParameterGroupTreeNode.getId());
                    if (sourceTouchpointSelection != null) {
                        TouchpointSelection targetTouchpointSelection = assignAlreadyClonedObject(sourceTouchpointSelection);
                        if (targetTouchpointSelection != null) {
                            clonedParameterGroupTreeNode = CloneHelper.queryInSaveSession(() -> targetTouchpointSelection.getParameterGroupTreeNode());
                        }
                    }
                }

                if(clonedParameterGroupTreeNode == null) {
                    LanguageSelection sourceLanguageSelection = LanguageSelection.findByPgTreeNodeId(sourceParameterGroupTreeNode.getId());
                    if(sourceLanguageSelection != null) {
                        LanguageSelection targetLanguageSelection = assignAlreadyClonedObject(sourceLanguageSelection);
                        if(targetLanguageSelection != null) {
                            clonedParameterGroupTreeNode = CloneHelper.queryInSaveSession(()->targetLanguageSelection.getParameterGroupTreeNode());
                        }
                    }
                }

                if(clonedParameterGroupTreeNode == null) {
                    ContentObjectData sourceContentObjectData = null;
                    Document syncFromDocument = sourceDocument.get();
                    Document sourceDocumentFromObject = null;

                    List<ContentObjectData> cods = ContentObjectAssociation.findAllByPgTreeNode(sourceParameterGroupTreeNode)
                        .stream()
                        .filter(coa ->
                            coa.getContentObject().hasDataType(coa.getDataType())
                                && (
                                (coa.getContentObject().isGlobalContentObject() && coa.getContentObject().getDocument() == null)
                                    || (coa.getContentObject().isMessageOrTouchpointLocal()
                                    && coa.getContentObject().getDocument() != null
                                    && syncFromDocument != null
                                    && coa.getContentObject().getDocument().getId() == syncFromDocument.getId()
                                )
                            )
                        )
                        .map(coa -> coa.getContentObject().getContentObjectData(coa.getDataType()))
                        .collect(Collectors.toList());

                    sourceContentObjectData = cods.stream().filter(cod -> cod.getContentObject().isMessageOrTouchpointLocal()).findFirst().orElse(null);
                    if (sourceContentObjectData == null) {
                        sourceContentObjectData = cods.stream().filter(cod -> cod.getContentObject().isGlobalContentObject() && cod.getContentObject().getDocument() == null).findFirst().orElse(null);
                    }
                    sourceDocumentFromObject = sourceContentObjectData == null ? null : sourceContentObjectData.getContentObject().getDocument();

                    if (sourceDocumentFromObject == null) {
                        sourceDocumentFromObject = syncFromDocument;
                    }

                    String contentObjectDna = sourceContentObjectData == null ? null : sourceContentObjectData.getContentObject().getDna();
                    Integer contentObjectDataType = sourceContentObjectData == null ? null : sourceContentObjectData.getDataType();
                    boolean isGlobalContentObject = sourceContentObjectData == null ? false : sourceContentObjectData.getContentObject().isGlobalContentObject();
                    boolean isMessageOrTouchpointLocal = sourceContentObjectData == null ? false : sourceContentObjectData.getContentObject().isMessageOrTouchpointLocal();
                    String parameterGroupTreeNodeDna = sourceParameterGroupTreeNode.getDna();
                    Document clonedDocument = getAlreadyClonedObject(sourceDocumentFromObject);
                    clonedParameterGroupTreeNode = queryInSaveSession(() -> {
                        ParameterGroupTreeNode targetParameterGroupTreeNode = null;
                        if (contentObjectDna != null && contentObjectDataType != null) {
                            ContentObject targetContentObject = null;
                            ContentObjectData targetContentObjectData = null;
                            if (isGlobalContentObject) {
                                targetContentObject = ContentObject.findGlobalObjectByDna(contentObjectDna);
                            } else if (isMessageOrTouchpointLocal) {
                                targetContentObject = ContentObject.findByDnaAndDocument(contentObjectDna, clonedDocument);
                            }
                            if (targetContentObject != null) {
                                targetContentObjectData = targetContentObject.getContentObjectData(contentObjectDataType.intValue());
                                if(targetContentObjectData != null) {
                                    targetParameterGroupTreeNode = ContentObjectAssociation.findAllByContentObjectAndParameters(
                                            targetContentObjectData.getContentObject(),
                                            targetContentObjectData.getDataType(),
                                            null,
                                            null,
                                            null,
                                            false,
                                            true,
                                            true,
                                            false,
                                            false
                                        ).stream()
                                        .filter(coa ->
                                            coa.getDataType() == contentObjectDataType
                                                && coa.getContentObjectPGTreeNode() != null
                                                && coa.getContentObjectPGTreeNode().getDna().equals(parameterGroupTreeNodeDna))
                                        .map(coa -> coa.getContentObjectPGTreeNode())
                                        .findFirst()
                                        .orElse(null);
                                }

                            }
                        }
                        return targetParameterGroupTreeNode;
                    });
                }

                if(clonedParameterGroupTreeNode != null) {
                    clonedObject = (T) clonedParameterGroupTreeNode;
                }
            } else if (sourceObject instanceof ZonePart) {
                ZonePart sourceZonePart = (ZonePart) sourceObject;
                String zonePartDna = sourceZonePart.getDna();
                Zone sourceZone = sourceZonePart.getZone();
                String zoneDna = sourceZone.getDna();
                Document sourceDocument = sourceZone.getDocument();
                if (sourceDocument != null) {
                    Document clonedDocument = getAlreadyClonedObject(sourceDocument);
                    if (clonedDocument != null) {
                        Zone clonedZone = queryInSaveSession(() -> Zone.findByDnaAndDocument(zoneDna, clonedDocument));
                        if (clonedZone != null) {
                            ZonePart clonedZonePart = queryInSaveSession(() -> ZonePart.findByDnaAndZone(zonePartDna, clonedZone));
                            if (clonedZonePart != null) {
                                clonedObject = (T) clonedZonePart;
                            }
                        }
                    }
                }
            } else if (sourceObject instanceof TextStyle) {
                TextStyle sourceTextStyle = (TextStyle) sourceObject;
                String identifier = sourceTextStyle.getIdentifier();
                TextStyle targetTextStyle = queryInSaveSession(() -> TextStyle.findByIdentifier(identifier));
                if (targetTextStyle != null) {
                    clonedObject = (T) targetTextStyle;
                }
            } else if (sourceObject instanceof TextStyleFont) {
                TextStyleFont sourceTextStyleFont = (TextStyleFont) sourceObject;
                String fontGuid = sourceTextStyleFont.getGuid();
                TextStyleFont targetTextStyleFont = queryInSaveSession(() -> TextStyleFont.findByGuid(fontGuid));

                if (targetTextStyleFont != null) {
                    clonedObject = (T) targetTextStyleFont;
                }
            } else if (sourceObject instanceof DatabaseFile) {
                DatabaseFile sourceDatabaseFile = (DatabaseFile) sourceObject;
                DatabaseFile targetDatabaseFile = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(DatabaseFile.class,
                        MessagepointRestrictions.and(
                            MessagepointRestrictions.eqOrIsNull("fileName", sourceDatabaseFile.getFileName()),
                            MessagepointRestrictions.eqOrIsNull("type", sourceDatabaseFile.getType()),
                            MessagepointRestrictions.eqOrIsNull("contentType", sourceDatabaseFile.getContentType()),
                            MessagepointRestrictions.eqOrIsNull("metadata", sourceDatabaseFile.getMetadata()),
                            MessagepointRestrictions.eqOrIsNull("sha256Hash", sourceDatabaseFile.getSha256Hash())
                        )
                ).stream().findFirst().orElse(null));
                if (targetDatabaseFile != null) {
                    clonedObject = (T) targetDatabaseFile;
                }
            } else if (sourceObject instanceof ListStyle) {
                ListStyle sourceListStyle = (ListStyle) sourceObject;
                String identifier = sourceListStyle.getIdentifier();
                ListStyle targetListStyle = queryInSaveSession(() -> ListStyle.findByIdentifier(identifier));
                if (targetListStyle != null) {
                    clonedObject = (T) targetListStyle;
                }
            } else if (sourceObject instanceof ParagraphStyle) {
                ParagraphStyle sourceParagraphStyle = (ParagraphStyle) sourceObject;
                String identifier = sourceParagraphStyle.getIdentifier();
                ParagraphStyle targetParagraphStyle = queryInSaveSession(() -> ParagraphStyle.findByIdentifier(identifier));
                if (targetParagraphStyle != null) {
                    clonedObject = (T) targetParagraphStyle;
                }
            }
            else if (sourceObject instanceof DataResource) {
                DataResource sourceDataResource = (DataResource) sourceObject;
                String dna = sourceDataResource.getDna();
                Document sourceDataResourceDocument = sourceDataResource.getDocument();
                if(sourceDataResourceDocument == null) sourceDataResourceDocument = sourceDocument.get();
                Document clonedDocument = getAlreadyClonedObject(sourceDataResourceDocument);
                if(clonedDocument == null) clonedDocument = targetDocument.get();
                Document targetDataResourceDocument = clonedDocument;
                DataResource targetDataResource = queryInSaveSession(() -> DataResource.findByDnaAndDocument(dna, targetDataResourceDocument));
                if(targetDataResource != null) {
                    clonedObject = (T) targetDataResource;
                }
            }
            else if (sourceObject instanceof ContentObject) {
                ContentObject sourceContentObject = (ContentObject) sourceObject;
                String dna = sourceContentObject.getDna();
                boolean isLocalObject = sourceContentObject.isLocalContentObject();
                Document targetContentObjectDocument = null;

                if(isLocalObject) {
                    Document sourceDocument = sourceContentObject.getDocument();
                    targetContentObjectDocument = getAlreadyClonedObject(sourceDocument);
                }

                Document targetContentObjectDocumentFinal = targetContentObjectDocument;
                ContentObject targetContentObject =
                        CloneHelper.queryInSaveSession(()-> isLocalObject ?
                                ContentObject.findByDnaAndDocument(dna, targetContentObjectDocumentFinal) :
                                ContentObject.findGlobalObjectByDna(dna));

                if(targetContentObject != null) {
                    for(Integer dataType : sourceContentObject.getContentObjectDataTypeMap().keySet()) {
                        ContentObjectData sourceContentObjectData = sourceContentObject.getContentObjectDataTypeMap().get(dataType);
                        ContentObjectData targetContentObjectData = queryInSaveSession(()->
                            targetContentObject.getContentObjectDataTypeMap().get(dataType)
                        );
                        if(targetContentObjectData != null) {
                            mapClonedObject(sourceContentObjectData, targetContentObjectData);
                        }
                    }

                    if(sourceContentObject.isGlobalContentObject()) {
                        execInSaveSession(()->{
                            Document targetDocumentLocal = targetDocument.get();
                            long targetDocumentId = targetDocumentLocal.getId();
                            if(! targetContentObject.getDocuments().stream().anyMatch(d->d.getId() == targetDocumentId)) {
                                targetContentObject.getDocuments().add(targetDocumentLocal);
                            }
                        });
                    }

                    clonedObject = (T) targetContentObject;
                }
            } else if (sourceObject instanceof DataSourceAssociation) {
                DataSourceAssociation sourceDataSourceAssociation = (DataSourceAssociation) sourceObject;
                String dataSourceAssociationName = sourceDataSourceAssociation.getName();
                String pureName = ApplicationUtil.getPureName(dataSourceAssociationName);
                String dna = sourceDataSourceAssociation.getDna();
                DataSourceAssociation targetDataSourceAssociation = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(DataSourceAssociation.class, MessagepointRestrictions.eqOrIsNull("dna", dna))
                        .stream().findFirst().orElse(null));
                if (targetDataSourceAssociation != null) {
                    DataSource sourceDataSource = sourceDataSourceAssociation.getPrimaryDataSource();
                    DataSource targetDataSource = targetDataSourceAssociation.getPrimaryDataSource();
                    if (sourceDataSource != null && targetDataSource != null) {
                        CloneHelper.mapDataSource(sourceDataSource, targetDataSource);
                    }
                    {
                        Set<ReferenceConnection> sourceReferenceConnections = sourceDataSourceAssociation.getReferenceConnections();
                        Set<ReferenceConnection> targetReferenceConnections = queryInSaveSession(() -> targetDataSourceAssociation.getReferenceConnections());
                        Map<String, ReferenceConnection> dsDnaToTargetReferenceConnectionMap = queryInSaveSession(() -> targetReferenceConnections.stream().collect(Collectors.toMap(rc->rc.getReferenceDataSource().getDna(), Function.identity())));
                        for(ReferenceConnection sourceReferenceConnection : sourceReferenceConnections) {
                            DataSource sourceRefDataSource = sourceReferenceConnection.getReferenceDataSource();
                            String dataSourceDNA = sourceRefDataSource.getDna();
                            ReferenceConnection targetReferenceConnection = dsDnaToTargetReferenceConnectionMap.get(dataSourceDNA); // CloneHelper.assignAlreadyClonedObject(sourceReferenceConnection.getReferenceDataSource());
                            if(sourceRefDataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                                targetReferenceConnection = targetReferenceConnections.stream().filter(rc->rc.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED).findFirst().orElse(null);
                            }

                            if(targetReferenceConnection != null) {
                                mapClonedObject(sourceReferenceConnection, targetReferenceConnection);
                                ReferenceConnection targetReferenceConnectionFinal = targetReferenceConnection;
                                DataSource targetRefDataSource = queryInSaveSession(() -> targetReferenceConnectionFinal.getReferenceDataSource());
                                if(sourceRefDataSource != null && targetRefDataSource != null) {
                                    mapDataSource(sourceRefDataSource, targetRefDataSource);
                                }
                            }
                        }
                    }
                    clonedObject = (T) targetDataSourceAssociation;
                }
            } else if (sourceObject instanceof DataSource) {
                DataSource sourceDataSource = (DataSource) sourceObject;
                String dataSourceName = sourceDataSource.getName();
                String pureName = ApplicationUtil.getPureName(dataSourceName);
                String dna = sourceDataSource.getDna();
                DataSourceAssociation targetDataSourceAssociation = queryInSaveSession(() -> targetDocument.get().getDataSourceAssociation());
                DataSource targetDataSource = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(DataSource.class, MessagepointRestrictions.eqOrIsNull("dna", dna))
                        .stream()
                        .findFirst()
                        .orElse(null));
                if (targetDataSource != null) {
                    mapDataSource(sourceDataSource, targetDataSource);
                    clonedObject = (T) targetDataSource;
                }
            } else if (sourceObject instanceof LookupTable) {
                LookupTable sourceLookupTable = (LookupTable) sourceObject;
                String lookupTableName = sourceLookupTable.getName();
                String pureName = ApplicationUtil.getPureName(lookupTableName);
                String dna = sourceLookupTable.getDna();
                DataSourceAssociation targetDataSourceAssociation = queryInSaveSession(() -> targetDocument.get().getDataSourceAssociation());
                LookupTable targetLookupTable = queryInSaveSession(() -> LookupTable.findByDna(dna));
                if (targetLookupTable != null) {
                    mapClonedObject(sourceLookupTable, targetLookupTable);

                    Date nowDate = DateUtil.now();

                    {
                        LookupTableVersionMapping sourceArchivedVersionMapping = LookupTableVersionMapping.findLatestArchived(sourceLookupTable);
                        LookupTableVersionMapping targetArchivedVersionMapping = queryInSaveSession(() -> LookupTableVersionMapping.findLatestArchived(targetLookupTable));
                        if (sourceArchivedVersionMapping != null && targetArchivedVersionMapping != null) {
                            LookupTableInstance sourceArchivedInstance = sourceArchivedVersionMapping.getModelInstance();
                            LookupTableInstance targetArchivedInstance = queryInSaveSession(() -> targetArchivedVersionMapping.getModelInstance());
                            if (sourceArchivedInstance != null && targetArchivedInstance != null) {
                                mapClonedObject(sourceArchivedInstance, targetArchivedInstance);

                                DataSource sourceDataSource = sourceArchivedInstance.getDataSource();
                                DataSource targetDataSource = queryInSaveSession(() -> targetArchivedInstance.getDataSource());
                                mapDataSource(sourceDataSource, targetDataSource);
                            }
                        }

                        if(targetArchivedVersionMapping != null) {
                            execInSaveSession(() -> {
                                LookupTableInstance targetArchivedInstance = targetArchivedVersionMapping.getModelInstance();
                                if(targetArchivedInstance.isFullyVisible()) return;
                                Document doc = Document.findById(targetDocument.get().getId());
                                if (!targetArchivedInstance.getDocuments().stream().anyMatch(d -> d.getId() == doc.getId())) {
                                    targetArchivedInstance.getDocuments().add(doc);
                                }
                            });
                        }
                    }

                    {
                        LookupTableVersionMapping sourceActiveVersionMapping = LookupTableVersionMapping.findProductionForModel(sourceLookupTable, nowDate);
                        LookupTableVersionMapping targetActiveVersionMapping = queryInSaveSession(() -> LookupTableVersionMapping.findProductionForModel(targetLookupTable, nowDate));
                        if (sourceActiveVersionMapping != null && targetActiveVersionMapping != null) {
                            LookupTableInstance sourceActiveInstance = sourceActiveVersionMapping.getModelInstance();
                            LookupTableInstance targetActiveInstance = queryInSaveSession(() -> targetActiveVersionMapping.getModelInstance());
                            if (sourceActiveInstance != null && targetActiveInstance != null) {
                                mapClonedObject(sourceActiveInstance, targetActiveInstance);

                                DataSource sourceDataSource = sourceActiveInstance.getDataSource();
                                DataSource targetDataSource = queryInSaveSession(() -> targetActiveInstance.getDataSource());
                                mapDataSource(sourceDataSource, targetDataSource);
                            }
                        }

                        if(targetActiveVersionMapping != null) {
                            execInSaveSession(() -> {
                                LookupTableInstance targetActiveInstance = targetActiveVersionMapping.getModelInstance();
                                if(targetActiveInstance.isFullyVisible()) return;
                                Document doc = Document.findById(targetDocument.get().getId());
                                if (!targetActiveInstance.getDocuments().stream().anyMatch(d -> d.getId() == doc.getId())) {
                                    targetActiveInstance.getDocuments().add(doc);
                                }
                            });

                        }
                    }

                    {
                        LookupTableVersionMapping sourceWorkingVersionMapping = LookupTableVersionMapping.findWIPForModel(sourceLookupTable);
                        LookupTableVersionMapping targetWorkingVersionMapping = queryInSaveSession(() -> LookupTableVersionMapping.findWIPForModel(targetLookupTable));
                        if (sourceWorkingVersionMapping != null && targetWorkingVersionMapping != null) {
                            LookupTableInstance sourceWorkingInstance = sourceWorkingVersionMapping.getModelInstance();
                            LookupTableInstance targetWorkingInstance = queryInSaveSession(() -> targetWorkingVersionMapping.getModelInstance());
                            if (sourceWorkingInstance != null && targetWorkingInstance != null) {
                                mapClonedObject(sourceWorkingInstance, targetWorkingInstance);

                                DataSource sourceDataSource = sourceWorkingInstance.getDataSource();
                                DataSource targetDataSource = queryInSaveSession(() -> targetWorkingInstance.getDataSource());
                                mapDataSource(sourceDataSource, targetDataSource);
                            }
                        }

                        if(targetWorkingVersionMapping != null) {
                            execInSaveSession(() -> {
                                LookupTableInstance targetWorkingInstance = targetWorkingVersionMapping.getModelInstance();
                                if(targetWorkingInstance.isFullyVisible()) return;
                                Document doc = Document.findById(targetDocument.get().getId());
                                if (!targetWorkingInstance.getDocuments().stream().anyMatch(d -> d.getId() == doc.getId())) {
                                    targetWorkingInstance.getDocuments().add(doc);
                                }
                            });
                        }
                    }

                    clonedObject = (T) targetLookupTable;
                }
            } else if (sourceObject instanceof DataElementVariable) {
                DataElementVariable sourceDataElementVariable = (DataElementVariable) sourceObject;
                int typeId = sourceDataElementVariable.getTypeId();
                int systemVariableTypeId = typeId == DataElementVariableType.ID_SYSTEM ? sourceDataElementVariable.getSystemVariableTypeId() : 0;
                String dna = sourceDataElementVariable.getDna();
                DataElementVariable targetDataElementVariable = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(DataElementVariable.class,
                        MessagepointRestrictions.or(
                                MessagepointRestrictions.and(
                                        MessagepointRestrictions.eq("typeId", DataElementVariableType.ID_SYSTEM),
                                        MessagepointRestrictions.eq("systemVariableTypeId", systemVariableTypeId)
                                ),
                                MessagepointRestrictions.and(
                                        MessagepointRestrictions.ne("typeId", DataElementVariableType.ID_SYSTEM),
                                        MessagepointRestrictions.eq("dna", dna)
                                )
                        )
                ).stream().findFirst().orElse(null));
                if (targetDataElementVariable != null) {
                    List<DataSource> sourceDataSourcesList = sourceDataElementVariable.getDataSources();
                    Map<Long, VariableDataElementMap> sourceDataSourceVariableDataElementMap = queryInSchema(sourceDataElementVariable.getObjectSchemaName(), () -> sourceDataElementVariable.getDataElementMap());
                    for (DataSource sourceDataSource : sourceDataSourcesList) {
                        DataSource targetDataSource = getAlreadyClonedObject(sourceDataSource);
                        if (targetDataSource != null) {
                            VariableDataElementMap sourceVariableDataElementMap = sourceDataSourceVariableDataElementMap.get(sourceDataSource.getId());
                            VariableDataElementMap targetVariableDataElementMap = queryInSaveSession(() -> targetDataElementVariable.getDataElementMap().get(targetDataSource.getId()));

                            if (targetVariableDataElementMap == null) {
                                if (sourceVariableDataElementMap != null) {
                                    VariableDataElementMap cloneVariableDataElementMap = CloneHelper.clone(sourceVariableDataElementMap);
                                    CloneHelper.execInSaveSession(()->{
                                        cloneVariableDataElementMap.save();
                                        targetDataElementVariable.getDataElementMap().put(targetDataSource.getId(), cloneVariableDataElementMap);
                                        targetDataElementVariable.save();
                                    });
                                    targetVariableDataElementMap = cloneVariableDataElementMap;
                                }
                            }

                            if (sourceVariableDataElementMap != null && targetVariableDataElementMap != null) {
                                mapClonedObject(sourceVariableDataElementMap, targetVariableDataElementMap);
                                AbstractDataElement sourceDataElement = sourceVariableDataElementMap.getDataElement();
                                VariableDataElementMap targetVariableDataElementMapFinal = targetVariableDataElementMap;
                                AbstractDataElement targetDataElement = queryInSaveSession(() -> targetVariableDataElementMapFinal.getDataElement());
                                if (sourceDataElement != null && targetDataElement != null && sourceDataElement.getDna().equals(targetDataElement.getDna())) {
                                    mapClonedObject(sourceDataElement, targetDataElement);
                                }
                            }
                        }
                    }
                    if (sourceDocument.get() != null && targetDocument.get() != null) {
                        Set<Document> sourceDocuments = sourceDataElementVariable.getDocuments();
                        if (sourceDocuments.stream().anyMatch(d -> d.getId() == sourceDocument.get().getId())) {
                            execInSaveSession(() -> {
                                if (!targetDataElementVariable.getDocuments().stream().anyMatch(d -> d.getId() == targetDocument.get().getId())) {
                                    Document targetDocumentForVariable = Document.findById(targetDocument.get().getId());
                                    targetDataElementVariable.getDocuments().add(targetDocumentForVariable);
                                    targetDataElementVariable.save();
                                }
                            });
                        }
                    }
                }
                if (targetDataElementVariable != null) {
                    clonedObject = (T) targetDataElementVariable;
                }
//            } else if (sourceObject instanceof Message) {
//                if (isCloning.get() == ID_CLONING_TYPE_INACTIVE) {
//                    Message sourceMessage = (Message) sourceObject;
//                    Message targetMessage = queryInSaveSession(() -> Message.findByDnaAndDocument(sourceMessage, targetDocument.get()));
//                    if (targetMessage != null) {
//                        clonedObject = (T) targetMessage;
//                    }
//                }
            } else if (sourceObject instanceof TargetGroup) {
                TargetGroup sourceTargetGroup = (TargetGroup) sourceObject;
                String targetGroupDna = sourceTargetGroup.getDna();
                TargetGroup targetTargetGroup = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(TargetGroup.class, MessagepointRestrictions.and(MessagepointRestrictions.eqOrIsNull("dna", targetGroupDna))).stream().findFirst().orElse(null));

                if (targetTargetGroup == null) {
                    String targetGroupGuid = sourceTargetGroup.getGuid();
                    targetTargetGroup = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(TargetGroup.class, MessagepointRestrictions.and(MessagepointRestrictions.eqOrIsNull("guid", targetGroupGuid))).stream().findFirst().orElse(null));
                }

                if (targetTargetGroup != null) {
                    mapTargetGroup(sourceTargetGroup, targetTargetGroup);
                    clonedObject = (T) targetTargetGroup;
                }
            } else if (sourceObject instanceof ConditionElement) {
                ConditionElement sourceConditionElement = (ConditionElement) sourceObject;
                String conditionElementGuid = sourceConditionElement.getGuid();
                ConditionElement targetConditionElement = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(ConditionElement.class, MessagepointRestrictions.and(MessagepointRestrictions.eqOrIsNull("guid", conditionElementGuid))).stream().findFirst().orElse(null));
                if(targetConditionElement == null) {
                    for(ConditionSubelement sourceConditionSubelement : sourceConditionElement.getSubElements()) {
                        String conditionSubelementGuid = sourceConditionSubelement.getGuid();
                        targetConditionElement = queryInSaveSession(()->{
                            ConditionSubelement targetConditionSubelement = ConditionSubelement.findByGuid(conditionSubelementGuid);
                            return targetConditionSubelement == null ? null : targetConditionSubelement.getElement();
                        });
                        if(targetConditionElement != null) break;
                    }
                }
                if (targetConditionElement != null) {
                    clonedObject = (T) targetConditionElement;
                }
            } else if (sourceObject instanceof ConditionSubelement) {
                ConditionSubelement sourceConditionSubelement = (ConditionSubelement) sourceObject;
                String sourceConditionSubelementDataValueString = getConditionSubelementDataValue(sourceConditionSubelement);
                ConditionSubelement targetConditionSubelement = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(ConditionSubelement.class, MessagepointRestrictions.and(MessagepointRestrictions.eqOrIsNull("guid", sourceConditionSubelement.getGuid()))).stream().findFirst().orElse(null));
                if (targetConditionSubelement == null && sourceConditionSubelement.getElement() != null) {
                    ConditionElement targetConditionElement = queryInSaveSession(() -> HibernateUtil.getManager().getObjectsAdvanced(ConditionElement.class, MessagepointRestrictions.and(MessagepointRestrictions.eqOrIsNull("guid", sourceConditionSubelement.getElement().getGuid()))).stream().findFirst().orElse(null));
                    if (targetConditionElement != null && targetConditionElement.getSubElements() != null) {
                        for (ConditionSubelement conditionSubelement : targetConditionElement.getSubElements()) {
                            String conditionSubelementDataValueString = queryInSaveSession(() -> getConditionSubelementDataValue(conditionSubelement));

                            if (sourceConditionSubelementDataValueString.equals(conditionSubelementDataValueString)) {
                                targetConditionSubelement = conditionSubelement;
                                break;
                            }
                        }
                    }
                }

                if (targetConditionSubelement != null) {
                    clonedObject = (T) targetConditionSubelement;
                }
            } else if (sourceObject instanceof ParameterGroup) {
                ParameterGroup sourceParameterGroup = (ParameterGroup) sourceObject;
                String parameterGroupGuid = sourceParameterGroup.getGuid();
                ParameterGroup clonedParameterGroup = queryInSaveSession(() -> ParameterGroup.findByGuid(parameterGroupGuid));
                if(clonedParameterGroup == null) {
                    if(sourceParameterGroup.isParameter() && ! sourceParameterGroup.getParameters().isEmpty()) {
                        Parameter sourceParameter = sourceParameterGroup.getParameters().get(0);
                        String parameterGuid = sourceParameter.getGuid();
                        Parameter targetParameter = queryInSaveSession(() -> Parameter.findByGuid(parameterGuid));
                        if(targetParameter == null) {
                            DataElementVariable sourceDataElementVariable = sourceParameter.getDataElementVariable();
                            if(sourceDataElementVariable != null) {
                                String dataElementVariableDna = sourceDataElementVariable.getDna();
                                DataElementVariable targetDataElementVariable = queryInSaveSession(()->DataElementVariable.findByDna(dataElementVariableDna));
                                if(targetDataElementVariable != null) {
                                    mapClonedObject(sourceDataElementVariable, targetDataElementVariable);
                                    List<Parameter> targetParameters = queryInSaveSession(()->Parameter.findByVariable(targetDataElementVariable.getId()));
                                    ParameterGroup targetParameterGroup = queryInSaveSession(() -> targetParameters.stream()
                                            .map(tp->tp.getCorrespondingParameterGroup())
                                            .filter(pg->pg != null && pg.isParameter())
                                            .findFirst()
                                            .orElse(null)
                                    );
                                    if(targetParameterGroup != null) {
                                        Parameter clonedParameter = queryInSaveSession(()->targetParameterGroup.getParameters().get(0));
                                        mapClonedObject(sourceParameter, clonedParameter);
                                    }
                                    clonedParameterGroup = targetParameterGroup;
                                }
                            }
                        } else {
                            clonedParameterGroup = queryInSaveSession(() -> targetParameter.getCorrespondingParameterGroup());
                        }
                    }
                }
/*            	
            	if(clonedParameterGroup == null) {
	            	List<ParameterGroup> allTargetParameterGroups = queryInSaveSession(()->ParameterGroup.findAllIncludingParameters());
	            	Document targetDocument = CloneHelper.getTargetDocument();
	            	String sourceParameterDnas = sourceParameterGroup.getParameters().stream().sequential().map(p->p.getDataElementVariable().getDna()).collect(Collectors.joining(","));
	            	clonedParameterGroup = queryInSaveSession(()->allTargetParameterGroups.stream()
	            			.filter(
	            				pg->pg.isVisibleToDocument(targetDocument) && 
	            				    pg.getParameters().stream().sequential()
	            				    .map(p->p.getDataElementVariable().getDna())
	            				    .collect(Collectors.joining(",")).equals(sourceParameterDnas)
	            			).findFirst().orElse(null));
            	}
*/
                if (clonedParameterGroup != null) {
                    clonedObject = (T) clonedParameterGroup;
                }
            } else if (sourceObject instanceof Parameter) {
                Parameter sourceParameter = (Parameter) sourceObject;
                String parameterGuid = sourceParameter.getGuid();
                Parameter clonedParameter = queryInSaveSession(() -> Parameter.findByGuid(parameterGuid));
                if (clonedParameter != null) {
                    clonedObject = (T) clonedParameter;
                }
            } else if(sourceObject instanceof RateScheduleCollection) {
            	RateScheduleCollection sourceRateScheduleCollection = (RateScheduleCollection) sourceObject;
            	String rateScheduleCollectionGuid = sourceRateScheduleCollection.getGuid();
            	RateScheduleCollection targetRateScheduleCollection = queryInSaveSession(()->RateScheduleCollection.findByGuid(rateScheduleCollectionGuid));
            	if(targetRateScheduleCollection != null) {
            		clonedObject = (T) targetRateScheduleCollection;
            	}
            } else if(sourceObject instanceof MetadataFormDefinition) {
                MetadataFormDefinition sourceMetadataFormDefinition = (MetadataFormDefinition) sourceObject;
                String metadataFormDefinitionGuid = sourceMetadataFormDefinition.getGuid();
                MetadataFormDefinition targetMetadataFormDefinition = queryInSaveSession(()->MetadataFormDefinition.findByGuid(metadataFormDefinitionGuid));
                if(targetMetadataFormDefinition != null) {
                    mapMetadataFormDefinition(sourceMetadataFormDefinition, targetMetadataFormDefinition);
                    clonedObject = (T) targetMetadataFormDefinition;
                }
            } else if(sourceObject instanceof MetadataFormItemDefinition) {
                MetadataFormItemDefinition sourceMetadataFormItemDefinition = (MetadataFormItemDefinition) sourceObject;
                String metadataFormItemDefinitionGuid = sourceMetadataFormItemDefinition.getGuid();
                MetadataFormItemDefinition targetMetadataFormItemDefinition = queryInSaveSession(()->MetadataFormItemDefinition.findByGuid(metadataFormItemDefinitionGuid));
                if(targetMetadataFormItemDefinition != null) {
                    mapClonedObject(sourceMetadataFormItemDefinition, targetMetadataFormItemDefinition);
                    clonedObject = (T) targetMetadataFormItemDefinition;
                }
            } else if(sourceObject instanceof DataFile) {
                DataFile sourceDataFile = (DataFile) sourceObject;
                String dataFileDna = sourceDataFile.getDna();
                DataFile targetDataFile = queryInSaveSession(()->DataFile.findByDna(dataFileDna));
                if(targetDataFile != null) {
                    mapClonedObject(sourceDataFile, targetDataFile);
                    clonedObject = (T) targetDataFile;
                }
            } else if(sourceObject instanceof CompositionFileSet) {
                CompositionFileSet sourceCompositionFileSet = (CompositionFileSet) sourceObject;
                Document targetDocument = getAlreadyClonedObject(sourceCompositionFileSet.getDocument());
                String compositionFileSetDna = sourceCompositionFileSet.getDna();
                if(targetDocument != null){
                    CompositionFileSet targetCompositionFileSet = queryInSaveSession(()->CompositionFileSet.findByDocumentAndDna(targetDocument.getId(), compositionFileSetDna));
                    if(targetCompositionFileSet != null) {
                        mapClonedObject(sourceCompositionFileSet, targetCompositionFileSet);
                        clonedObject = (T) targetCompositionFileSet;
                    }
                }
            }
            else if(sourceObject instanceof TextStyleTransformationProfile) {
                TextStyleTransformationProfile sourceTextStyleTransformationProfile = (TextStyleTransformationProfile) sourceObject;
                String textStyleTransformationProfileDna = sourceTextStyleTransformationProfile.getDna();
                TextStyleTransformationProfile targetTextStyleTransformationProfile = queryInSaveSession(()->TextStyleTransformationProfile.findByDna(textStyleTransformationProfileDna));
                if(targetTextStyleTransformationProfile != null) {
                    mapClonedObject(sourceTextStyleTransformationProfile, targetTextStyleTransformationProfile);
                    clonedObject = (T) targetTextStyleTransformationProfile;
                }
            }

            if (clonedObject != null) {
                if (clonedObject instanceof HibernateProxy || clonedObject instanceof PersistentCollection) {
                    clonedObject = (T) queryInSaveSession((IdentifiableMessagePointModel) clonedObject, o -> {
                        try {
                            IdentifiableMessagePointModel tNewObj = (IdentifiableMessagePointModel) hibernateInitialize(o);
                            return tNewObj;
                        } catch (Exception e) {
                            e.printStackTrace();
                            return null;
                        }
                    });
                }
            }
        } finally {

        }

        if (clonedObject != null) {
            if (sourceObject instanceof ParameterGroupTreeNode && clonedObject instanceof ParameterGroupTreeNode) {
                mapParameterTreeNode((ParameterGroupTreeNode) sourceObject, (ParameterGroupTreeNode) clonedObject);
            } else if (sourceObject instanceof DataSource && clonedObject instanceof DataSource) {
                mapDataSource((DataSource) sourceObject, (DataSource) clonedObject);
            } else {
                mapClonedObject(sourceObject, clonedObject);
            }
        }

        return clonedObject;
    }

    private static void mapParameterTreeNode(ParameterGroupTreeNode sourceObject, ParameterGroupTreeNode clonedObject) {
        mapClonedObject(sourceObject, clonedObject);
        ParameterGroup sourceParameterGroup = sourceObject.getParameterGroup();
        ParameterGroup targetParameterGroup = queryInSaveSession(() -> clonedObject.getParameterGroup());
        if (sourceParameterGroup != null && targetParameterGroup != null) {
            mapParameterGroup(sourceParameterGroup, targetParameterGroup);
        }
        if (sourceObject.getParameterGroupInstanceCollection() != null && queryInSaveSession(() -> clonedObject.getParameterGroup()) != null) {
            mapParameterGroupInstanceCollection(sourceObject.getParameterGroupInstanceCollection(), queryInSaveSession(() -> clonedObject.getParameterGroupInstanceCollection()));
        }
    }

    public static void mapParameterGroup(ParameterGroup sourceParameterGroup, ParameterGroup targetParameterGroup) {
        String sourceParameterGroupGuid = sourceParameterGroup.getGuid();
        String targetParameterGroupGuid = queryInSaveSession(() -> targetParameterGroup.getGuid());
        if (sourceParameterGroupGuid.equals(targetParameterGroupGuid)) {
            mapClonedObject(sourceParameterGroup, targetParameterGroup);
/*    		
    	} else {
	    	String sourceParameterDnas = sourceParameterGroup.getParameters().stream().sequential().map(p->p.getDataElementVariable().getDna()).collect(Collectors.joining(","));
	    	String targetParameterDnas = queryInSaveSession(()->targetParameterGroup.getParameters().stream().sequential().map(p->p.getDataElementVariable().getDna()).collect(Collectors.joining(",")));
	    	if(sourceParameterDnas.equals(targetParameterDnas)) {
	    		mapClonedObject(sourceParameterGroup, targetParameterGroup);
	    	}
*/
        }
    }

    public static void mapMetadataFormDefinition(MetadataFormDefinition sourceMetadataFormDefinition, MetadataFormDefinition targetMetadataFormDefinition) {
        mapClonedObject(sourceMetadataFormDefinition, targetMetadataFormDefinition);

        Map<String, MetadataFormItemDefinition> guidToTargetItems =
                queryInSaveSession(()->targetMetadataFormDefinition.getFormItemDefinitions()
                        .stream()
                        .collect(Collectors.toMap(MetadataFormItemDefinition::getGuid, Function.identity(), (p1, p2)->{
                            if(p1.getId() < p2.getId()) {
                                return p1;
                            }
                            if(p1.getId() > p2.getId()) {
                                return p2;
                            }
                            return p1;
                        })));

        for(MetadataFormItemDefinition sourceItem : sourceMetadataFormDefinition.getFormItemDefinitions()) {
            String itemGuid = sourceItem.getGuid();
            MetadataFormItemDefinition targetItem = guidToTargetItems.get(itemGuid);
            if(targetItem != null) {
                mapClonedObject(sourceItem, targetItem);
            }
        }
    }

    private static void mapParameterGroupInstanceCollection(ParameterGroupInstanceCollection sourceObject, ParameterGroupInstanceCollection clonedObject) {
        mapClonedObject(sourceObject, clonedObject);
    }

    public static <T extends UpdatableMessagePointModel> T assignAlreadyClonedObject(T sourceObject) {
        if (sourceObject == null) return null;

        try {
            sourceObject = (T) hibernateInitialize(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        T targetObject = getAlreadyClonedObject(sourceObject);
        if (targetObject != null) {
            return targetObject;
        }

        try {
            targetObject = getSimiliarInDBObject(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        if (targetObject != null) {
            return targetObject;
        }

        return null;
    }

    public static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS assignAlreadyClonedObject(TS sourceObjectsCollection) {
        if (sourceObjectsCollection == null) {
            return null;
        }

        TS targetObjectsCollection = getTargetCollection(sourceObjectsCollection);

        if (!sourceObjectsCollection.isEmpty()) {
            for (T sourceObject : sourceObjectsCollection) {
                T targetObject = assignAlreadyClonedObject(sourceObject);
                if (targetObject != null) {
                    targetObjectsCollection.add(targetObject);
                }
            }
        }

        return targetObjectsCollection;
    }

    private static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS getTargetCollection(TS sourceObjectsCollection) {
        if (sourceObjectsCollection == null) return null;

        TS targetObjectsCollection = null;

        if (sourceObjectsCollection instanceof Set) {
            targetObjectsCollection = (TS) new LinkedHashSet<T>();
        } else if (sourceObjectsCollection instanceof List) {
            targetObjectsCollection = (TS) new ArrayList<T>();
        } else {
            System.out.println("Unsupported collection !");
        }

        return targetObjectsCollection;
    }

    public static <T extends UpdatableMessagePointModel> T clone(T sourceObject) {
        return clone(sourceObject, so -> (T) so.clone());
    }

    public static <T extends UpdatableMessagePointModel> T clone(T sourceObject, Function<T, T> cloneFunc) {
        if (sourceObject == null) return null;

        try {
            sourceObject = (T) hibernateInitialize(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        T targetObject = (T) getAlreadyClonedObject(sourceObject);
        if (targetObject != null) {
            return targetObject;
        }

        targetObject = cloneFunc.apply(sourceObject);

        return targetObject;
    }

    public static <T extends UpdatableMessagePointModel> void mapNewClonedObject(T sourceObject, T clonedObject) {
        if (sourceObject instanceof ContentObjectAssociation) return;
        if (sourceObject instanceof Content) return;

        Map<String, ClonedObjectInfo> source2InfoMapInThread = source2InfoMap.get();
        String sourceObjectKey = getSourceObjectKey(sourceObject);
        if (!source2InfoMapInThread.containsKey(sourceObjectKey)) {
            ClonedObjectInfo clonedObjectInfo = new ClonedObjectInfo(sourceObject, clonedObject, true);
            source2InfoMapInThread.put(sourceObjectKey, clonedObjectInfo);
        } else {
            ClonedObjectInfo clonedObjectInfo = new ClonedObjectInfo(sourceObject, clonedObject, true);
            source2InfoMapInThread.put(sourceObjectKey, clonedObjectInfo);
        }
        if ((clonedObject instanceof Document) && (sourceDocument.get() != null) && (sourceObject == sourceDocument.get()) && (targetDocument.get() == null)) {
            targetDocument.set((Document) clonedObject);
        }
    }

    public static <T extends UpdatableMessagePointModel> void mapClonedObject(T sourceObject, T clonedObject) {
        Map<String, ClonedObjectInfo> source2InfoMapInThread = source2InfoMap.get();
        String sourceObjectKey = getSourceObjectKey(sourceObject);
        if (!source2InfoMapInThread.containsKey(sourceObjectKey)) {
            ClonedObjectInfo clonedObjectInfo = new ClonedObjectInfo(sourceObject, clonedObject, false);
            source2InfoMapInThread.put(sourceObjectKey, clonedObjectInfo);
        } else {
            ClonedObjectInfo clonedObjectInfo = new ClonedObjectInfo(sourceObject, clonedObject, false);
            source2InfoMapInThread.put(sourceObjectKey, clonedObjectInfo);
        }
        if ((clonedObject instanceof Document) && (sourceDocument.get() != null) && (sourceObject == sourceDocument.get()) && (targetDocument.get() == null)) {
            targetDocument.set((Document) clonedObject);
        }
    }

    public static <T extends UpdatableMessagePointModel> void unmapObject(T sourceObject) {
        Map<String, ClonedObjectInfo> source2InfoMapInThread = source2InfoMap.get();
        if(source2InfoMapInThread != null) {
            String sourceObjectKey = getSourceObjectKey(sourceObject);
            if (source2InfoMapInThread.containsKey(sourceObjectKey)) {
                source2InfoMapInThread.remove(sourceObjectKey);
            }
        }
    }

    public static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS clone(TS sourceObjectsCollection) {
        return clone(sourceObjectsCollection, so -> (T) so.clone());
    }

    public static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS clone(TS sourceObjectsCollection, Function<T, T> cloneFunc) {
        if (sourceObjectsCollection == null) {
            return null;
        }

        TS targetObjectsCollection = getTargetCollection(sourceObjectsCollection);

        if (!sourceObjectsCollection.isEmpty()) {
            for (T sourceObject : sourceObjectsCollection) {
                T targetObject = clone(sourceObject, cloneFunc);
                targetObjectsCollection.add(targetObject);
            }
        }

        return targetObjectsCollection;
    }

    public static <T extends UpdatableMessagePointModel> T assign(T sourceObject) {
        if (sourceObject == null) {
            return null;
        }

        try {
            sourceObject = (T) hibernateInitialize(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        T targetObject = getAlreadyClonedObject(sourceObject);
        if (targetObject != null) {
            return targetObject;
        }

        if (!crossInstanceClone.get()) {
            return sourceObject;
        }

        if (sourceObject.getObjectSchemaName().equals(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier())) {
            return sourceObject;
        }

        try {
            targetObject = getSimiliarInDBObject(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        if (targetObject != null) {
            return targetObject;
        }

        targetObject = clone(sourceObject);
        if (sourceObject instanceof IdentifiableMessagePointModel) {
            IdentifiableMessagePointModel identifiableMessagePointModel = (IdentifiableMessagePointModel) targetObject;
            identifiableMessagePointModel.save();
        } else {
            targetObject.save(true);
        }

        return targetObject;
    }

    public static <T extends UpdatableMessagePointModel> T assign(T sourceObject, Function<T, T> cloneFunc) {
        return assign(sourceObject, cloneFunc, true);
    }

    public static <T extends UpdatableMessagePointModel> T assign(T sourceObject, Function<T, T> cloneFunc, boolean lookForSameInDB) {
        if (sourceObject == null) {
            return null;
        }

        try {
            sourceObject = (T) hibernateInitialize(sourceObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        T targetObject = getAlreadyClonedObject(sourceObject);
        if (targetObject != null) {
            return targetObject;
        }

        if (!crossInstanceClone.get()) {
            return sourceObject;
        }

        if (sourceObject.getObjectSchemaName().equals(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier())) {
            return sourceObject;
        }

        if (lookForSameInDB) {
            try {
                targetObject = getSimiliarInDBObject(sourceObject);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }

            if (targetObject != null) {
                return targetObject;
            }
        }

        targetObject = clone(sourceObject, cloneFunc);
        if (sourceObject instanceof IdentifiableMessagePointModel) {
            IdentifiableMessagePointModel identifiableMessagePointModel = (IdentifiableMessagePointModel) targetObject;
            identifiableMessagePointModel.save();
        } else {
            targetObject.save(true);
        }

        return targetObject;
    }

    public static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS assign(TS sourceObjectsCollection) {
        if (sourceObjectsCollection == null) {
            return null;
        }

        TS targetObjectsCollection = getTargetCollection(sourceObjectsCollection);

        if (!sourceObjectsCollection.isEmpty()) {
            for (T sourceObject : sourceObjectsCollection) {
                T targetObject = assign(sourceObject);
                targetObjectsCollection.add(targetObject);
            }
        }

        return targetObjectsCollection;
    }

    public static <T extends UpdatableMessagePointModel, TS extends Collection<T>> TS assign(TS sourceObjectsCollection, Function<T, T> cloneFunc) {
        if (sourceObjectsCollection == null) {
            return null;
        }

        TS targetObjectsCollection = getTargetCollection(sourceObjectsCollection);

        if (!sourceObjectsCollection.isEmpty()) {
            for (T sourceObject : sourceObjectsCollection) {
                T targetObject = assign(sourceObject, cloneFunc);
                targetObjectsCollection.add(targetObject);
            }
        }

        return targetObjectsCollection;
    }

    public static <T extends IdentifiableMessagePointModelInt> T assignConstObject(T sourceObject) {
        if (sourceObject == null) {
            return null;
        }

        if (!crossInstanceClone.get()) {
            return sourceObject;
        }

        if (sourceObject.getObjectSchemaName().equals(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier())) {
            return sourceObject;
        }

        T targetObject = getAlreadyClonedObject(sourceObject);

        if (targetObject == null) {
            Class<T> clazz = getObjectClass(sourceObject);

            targetObject = queryInSaveSession(()->(T) HibernateUtil.getManager().getObject(clazz, sourceObject.getId()));

            if (targetObject == null) {
                targetObject = (T) sourceObject.clone();
                targetObject.setId(sourceObject.getId());
                targetObject.save(true);
            }
        }

        return targetObject;
    }

    public static <T extends IdentifiableMessagePointModel> T assignConstObject(T sourceObject) {
        if (sourceObject == null) {
            return null;
        }
        if (!crossInstanceClone.get()) {
            return sourceObject;
        }

        if (sourceObject.getObjectSchemaName().equals(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier())) {
            return sourceObject;
        }

        T targetObject = getAlreadyClonedObject(sourceObject);

        if (targetObject == null) {
            Class<T> clazz = getObjectClass(sourceObject);

            targetObject = queryInSaveSession(()->(T) HibernateUtil.getManager().getObject(clazz, sourceObject.getId()));

            if (targetObject == null) {
                targetObject = (T) sourceObject.clone();
                targetObject.setId(sourceObject.getId());
                targetObject.save(true);
            }
        }

        return targetObject;
    }

    public static User getRequestor() {
        return requestor.get();
    }

    public static void setRequestor(User user) {
        requestor.set(user);
    }

    public static <T extends IdentifiableMessagePointModel> T queryInSaveSession(T sourceObject, Function<T, T> func) {
        SessionHolder mainSessionHolder = null;
        boolean sessionSwitched = false;
        if (crossInstanceClone.get()) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier());
            sessionSwitched = true;
        }
        T targetObject = null;
        try {
            targetObject = func.apply(sourceObject);
            targetObject = (T) hibernateInitialize(targetObject);
        } finally {
            if (crossInstanceClone.get()) {
                if (sessionSwitched) {
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);
                }
            }
        }
        return targetObject;
    }

    public static <T extends IdentifiableMessagePointModelInt> T queryInSaveSession(T sourceObject, Function<T, T> func) {
        SessionHolder mainSessionHolder = null;
        if (crossInstanceClone.get()) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier());
        }
        T targetObject = null;
        try {
            targetObject = func.apply(sourceObject);
            targetObject = (T) hibernateInitialize(targetObject);
        } finally {
            if (crossInstanceClone.get()) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
        return targetObject;
    }

    public static <T extends Object> T queryInSaveSession(Supplier<T> func) {
        SessionHolder mainSessionHolder = null;
        if (crossInstanceClone.get()) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier());
        }
        T targetObject = null;
        try {
            targetObject = func.get();
            targetObject = (T) hibernateInitialize(targetObject);
        } finally {
            if (crossInstanceClone.get()) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
        return targetObject;
    }

    /**
     * public static <T extends Object> T queryInSourceSession(Supplier<T> func) {
     * boolean switched = false;
     * SessionHolder mainSessionHolder = null;
     * if (!sourceSchema.get().equals(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier()))
     * {
     * mainSessionHolder = HibernateUtil.getManager().openTemporarySession(sourceSchema.get());
     * switched = true;
     * }
     * T targetObject = null;
     * try {
     * targetObject = func.get();
     * if (targetObject != null) {
     * if (targetObject instanceof HibernateProxy) {
     * Hibernate.initialize(targetObject);
     * targetObject = (T) ((HibernateProxy) targetObject).getHibernateLazyInitializer().getImplementation();
     * }
     * }
     * } finally {
     * if(switched) {
     * HibernateUtil.getManager().restoreSession(mainSessionHolder);
     * }
     * }
     * return targetObject;
     * }
     **/

    public static void queryInSourceSession(Runnable func) {
        func.run();
    }

    public static void execInSaveSession(Runnable func) {
        SessionHolder mainSessionHolder = null;
        if (crossInstanceClone.get()) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier());
        }
        try {
            func.run();
        } finally {
            if (crossInstanceClone.get()) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    public static void saveAllClonedObjects() {
        if (true || crossInstanceClone.get()) {
            Map<String, ClonedObjectInfo> clonedObjectMapInThread = source2InfoMap.get();
            for (ClonedObjectInfo clonedObjectInfo : clonedObjectMapInThread.values()) {
                UpdatableMessagePointModel clonedObject = clonedObjectInfo.clonedObject;
                if (!savedObjects.get().contains(clonedObject)) {
//                    System.out.println("saveAllClonedObjects: Object " + clonedObject + " Not saved");
                }
//                clonedObject.save(! savedObjects.get().contains(clonedObject));
            }
        }
    }

    public static void markSaved(UpdatableMessagePointModel updatableMessagePointModel) {
        return;
/*
        if(updatableMessagePointModel instanceof ContentObjectAssociation) return;
        if(updatableMessagePointModel instanceof ProductionContentAssociation) return;
        if(updatableMessagePointModel instanceof Content) return;

        if(crossInstanceClone.get()) {
            Collection<UpdatableMessagePointModel> savedObjectsInThread = savedObjects.get();
            if(! savedObjectsInThread.contains(updatableMessagePointModel)) {
                savedObjectsInThread.add(updatableMessagePointModel);
            }
        }

 */
    }

    public static void updateClonedObjectId(UpdatableMessagePointModel clonedObject, UpdatableMessagePointModel sourceObject) {
        if (true || crossInstanceClone.get()) {
            Map<String, ClonedObjectInfo> clonedObjectMapInThread = source2InfoMap.get();

            String sourceObjectKey = getSourceObjectKey(sourceObject);

            ClonedObjectInfo clonedObjectInfo = clonedObjectMapInThread.get(sourceObjectKey);

            if (clonedObjectInfo != null) {
                Serializable clonedObjectId = getObjectId(clonedObject);
                clonedObjectInfo.setTargetID(clonedObjectId);
            }
        }
    }

    public static Map<Long, Long> getClonedIDsMap() {
        Map<Long, Long> clonedIDsMap = new HashMap<>();
        if (true || crossInstanceClone.get()) {
            Map<String, ClonedObjectInfo> clonedObjectMapInThread = source2InfoMap.get();
            for (ClonedObjectInfo clonedObjectInfo : clonedObjectMapInThread.values()) {
                Serializable sourceID = clonedObjectInfo.getSourceID();
                Serializable clonedID = clonedObjectInfo.getTargetID();
                if (sourceID != null && clonedID != null) {
                    if (sourceID instanceof Number && clonedID instanceof Number) {
                        clonedIDsMap.put(((Number) sourceID).longValue(), ((Number) clonedID).longValue());
                    }
                }
            }
        }

        return clonedIDsMap;
    }

    public static <T extends Object> T queryInSchema(String schemaName, Supplier<T> func) {
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if (schemaName != null && !schemaName.equals(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifierWithPodMasterTranslation())) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
            switchedSession = true;
        }

        T targetObject = null;

        try {
            targetObject = func.get();
            targetObject = (T) hibernateInitialize(targetObject);
        } finally {
            if (switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }

        return targetObject;
    }

    public static void execInSchema(String schemaName, Runnable func) {
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if (schemaName != null && !schemaName.equals(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifierWithPodMasterTranslation())) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
            switchedSession = true;
        }
        try {
            func.run();
        } finally {
            if (switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
    }

    public static <T extends Object> T execInSchema(String schemaName, Supplier<T> func) {
        boolean switchedSession = false;
        SessionHolder mainSessionHolder = null;
        if (schemaName != null && !MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier().equals(schemaName)) {
            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
            switchedSession = true;
        }

        T returnValue = null;

        try {
            returnValue = func.get();
        } finally {
            if (switchedSession) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }

        return returnValue;
    }

    public static void saveInCurrentSchema(Runnable func) {
        String saveSchema = MessagepointCurrentTenantIdentifierResolver.getSaveIdentifier();
        String currentSchema = HibernateUtil.getManager().getSession().getTenantIdentifier();
        MessagepointCurrentTenantIdentifierResolver.setSaveIdentifier(currentSchema);
        try {
            func.run();
        } finally {
            MessagepointCurrentTenantIdentifierResolver.setSaveIdentifier(saveSchema);
        }
    }

    public static void updateContentAssociationReferences(ContentObjectAssociation clonedContentAssociation, Map<Long, Map<Long, Map<Long, ContentObjectAssociation>>> contentByLanguagePgtnMap, Long defaultMessagepointLocaleId, boolean isTreeModel) {

        MessagepointLocale messagepointLocale = clonedContentAssociation.getMessagepointLocale();
        if (messagepointLocale != null) {
            Long messagepointLocaleId = messagepointLocale.getId();
            long parameterGroupTreeNodeId = 0L;
            ParameterGroupTreeNode parameterGroupTreeNode = clonedContentAssociation.getPGTreeNode();
            if (parameterGroupTreeNode != null) {
                parameterGroupTreeNodeId = parameterGroupTreeNode.getId();
            }

            Long zonePartId = 0L;
            if (clonedContentAssociation.getZonePart() != null) {
                zonePartId = clonedContentAssociation.getZonePart().getId();
            }

            Map<Long, Map<Long, ContentObjectAssociation>> pgtnContentMap = contentByLanguagePgtnMap.get(messagepointLocaleId);
            if (pgtnContentMap == null) {
                pgtnContentMap = new HashMap<>();
                contentByLanguagePgtnMap.put(messagepointLocaleId, pgtnContentMap);
            }
            Map<Long, ContentObjectAssociation> zonePartContentMap = pgtnContentMap.get(parameterGroupTreeNodeId);
            if (zonePartContentMap == null) {
                zonePartContentMap = new HashMap<>();
                pgtnContentMap.put(parameterGroupTreeNodeId, zonePartContentMap);
            }

            if (clonedContentAssociation.getTypeId() != ContentAssociationType.ID_SAME_AS_DEFAULT && clonedContentAssociation.getTypeId() != ContentAssociationType.ID_REFERENCES) {
                CloneHelper.execInSaveSession(() -> {
                    if (clonedContentAssociation.getTypeId() == ContentAssociationType.ID_OWNS) {
/*
                        if (clonedContentAssociation.getReferencingContentLibrary() != null) {
                            ContentLibrary targetReferencingContentLibrary = clonedContentAssociation.getReferencingContentLibrary();
                            if (targetReferencingContentLibrary != null) {
                                ContentLibraryInstance targetReferencingContentLibraryInstance = (ContentLibraryInstance) targetReferencingContentLibrary.getActiveCopy();
                                if (targetReferencingContentLibraryInstance != null) {
                                    if (clonedContentAssociation.getZonePart() == null || clonedContentAssociation.getZonePart().getContentType().isGraphic()) {
                                        String languageCode = clonedContentAssociation.getMessagepointLocale().getLanguageCode();
                                        ContentObjectAssociation contentLibraryInstanceContentAssociation
                                                = ContentObjectAssociation.findLanguageContentAssociation(targetReferencingContentLibraryInstance.getId(), 0, languageCode, ContentLibraryInstance.class);
                                        if (contentLibraryInstanceContentAssociation != null) {
                                            Content targetContentLibraryInstanceContent = contentLibraryInstanceContentAssociation.getContent();
                                            if (targetContentLibraryInstanceContent != null) {
                                                clonedContentAssociation.setContent(targetContentLibraryInstanceContent);
                                            }
                                        }
                                    }
                                }
                            }
                        }
 */
                    }
                });

                zonePartContentMap.put(zonePartId, clonedContentAssociation);
            } else {
                if (clonedContentAssociation.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT) {
                    clonedContentAssociation.setReferencingImageLibrary(null);
                    clonedContentAssociation.setContent(null);
                    if (isTreeModel) {
                        zonePartContentMap.put(zonePartId, clonedContentAssociation);
                    }
                } else if (clonedContentAssociation.getTypeId() == ContentAssociationType.ID_REFERENCES) {
                    if (isTreeModel) {
                        ParameterGroupTreeNode refParameterGroupTreeNode = clonedContentAssociation.getReferencingPGTreeNode();
                        if (refParameterGroupTreeNode != null) {
                            Long refParameterGroupTreeNodeId = refParameterGroupTreeNode.getId();
                            Map<Long, ContentObjectAssociation> refZonePartContentMap = pgtnContentMap.get(refParameterGroupTreeNodeId);
                            if (refZonePartContentMap != null) {
                                ContentObjectAssociation contentObjectAssociation = refZonePartContentMap.get(zonePartId);

                                if (contentObjectAssociation != null) {
                                    clonedContentAssociation.setReferencingImageLibrary(contentObjectAssociation.getReferencingImageLibrary());
                                    if (contentObjectAssociation.getReferencingImageLibrary() != null)
                                        clonedContentAssociation.setContent(null);
                                    else
                                        clonedContentAssociation.setContent(contentObjectAssociation.getContent());
                                }
                            }
                        }
                        zonePartContentMap.put(zonePartId, clonedContentAssociation);
                    } else {
                        CloneHelper.execInSaveSession(() -> {
                            if (clonedContentAssociation.getReferencingImageLibrary() != null) {
/*
                                ContentLibrary targetReferencingContentLibrary = clonedContentAssociation.getReferencingContentLibrary();
                                if (targetReferencingContentLibrary != null) {
                                    ContentLibraryInstance targetReferencingContentLibraryInstance = (ContentLibraryInstance) targetReferencingContentLibrary.getActiveCopy();
                                    if (targetReferencingContentLibraryInstance != null) {
                                        if (clonedContentAssociation.getZonePart() == null || clonedContentAssociation.getZonePart().getContentType().isGraphic()) {
                                            String languageCode = clonedContentAssociation.getMessagepointLocale().getLanguageCode();
                                            ContentObjectAssociation contentLibraryInstanceContentAssociation
                                                    = ContentObjectAssociation.findLanguageContentAssociation(targetReferencingContentLibraryInstance.getId(), 0, languageCode, ContentLibraryInstance.class);
                                            if (contentLibraryInstanceContentAssociation != null) {
                                                Content targetContentLibraryInstanceContent = contentLibraryInstanceContentAssociation.getContent();
                                                if (targetContentLibraryInstanceContent != null) {
                                                    clonedContentAssociation.setContent(targetContentLibraryInstanceContent);
                                                }
                                            }
                                        }
                                    }
                                }
 */
                            }
                        });
                    }
                }
            }
        }
    }

    public static void mapDataSource(DataSource sourceDataSource, DataSource targetDataSource) {
        if (sourceDataSource != null && targetDataSource != null) {
            if (getAlreadyClonedObject(sourceDataSource) != null) return;
            CloneHelper.mapClonedObject(sourceDataSource, targetDataSource);

            String sourceDna = sourceDataSource.getDna();
            String targetDna = targetDataSource.getDna();
            boolean mapByDna = sourceDna.equalsIgnoreCase(targetDna);

            Set<DataRecord> sourceDataRecordsSet = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceDataSource.getDataRecords());
            Set<DataRecord> targetDataRecordsSet = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataSource.getDataRecords());
            Set<DataElement> sourceDataElementsSet = sourceDataRecordsSet == null ? new HashSet<>() :
                    CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(),
                            () -> sourceDataRecordsSet.stream().flatMap(dr-> dr.getDataElements().stream()).collect(Collectors.toSet()));
            Set<DataElement> targetDataElementsSet = targetDataRecordsSet == null ? new HashSet<>() :
                    CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(),
                            () -> targetDataRecordsSet.stream().flatMap(dr-> dr.getDataElements().stream()).collect(Collectors.toSet()));

            Map<String, DataElement> dnaOrPathToTargetDataElementMap = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataElementsSet.stream().collect(Collectors.toMap(de->mapByDna?de.getDna():de.getPath(), Function.identity())));

            for (DataElement sourceDataElement : sourceDataElementsSet) {
                if (getAlreadyClonedObject(sourceDataElement) != null) continue;
                String dataElementDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceDataElement.getDna():sourceDataElement.getPath());
                DataElement targetDataElement = dnaOrPathToTargetDataElementMap.get(dataElementDnaOrPath);
                if(targetDataElement != null) {
                    DataElement targetDataElementFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> (DataElement) DataElement.findById(targetDataElement.getId()));
                    CloneHelper.mapClonedObject(sourceDataElement, targetDataElementFinal);
                    DataRecord sourceDataRecord = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceDataElement.getDataRecord());
                    if (getAlreadyClonedObject(sourceDataRecord) != null) continue;
                    DataRecord targetDataRecord = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataElement.getDataRecord());
                    if(targetDataRecord != null) {
                        DataRecord targetDataRecordFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> DataRecord.findById(targetDataRecord.getId()));
                        CloneHelper.mapClonedObject(sourceDataRecord, targetDataRecordFinal);
                    }
                }
            }

            for (DataRecord sourceDataRecord : sourceDataRecordsSet) {
                if (getAlreadyClonedObject(sourceDataRecord) != null) continue;

                String sourceDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceDataRecord.getDna():sourceDataRecord.getPath());

                for (DataRecord targetDataRecord : targetDataRecordsSet) {
                    String targetDnaOrPath = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> mapByDna?targetDataRecord.getDna():targetDataRecord.getPath());

                    if (
                            sourceDnaOrPath.equals(targetDnaOrPath) ||
                            (sourceDataRecord.getName().equals(targetDataRecord.getName()) &&
                                ((sourceDataRecord.getBreakIndicator() == null && targetDataRecord.getBreakIndicator() == null) || (sourceDataRecord.getBreakIndicator() != null && targetDataRecord.getBreakIndicator() != null && sourceDataRecord.getBreakIndicator().equals(targetDataRecord.getBreakIndicator()))) &&
                                ((sourceDataRecord.getRecordIndicator() == null && targetDataRecord.getRecordIndicator() == null) || (sourceDataRecord.getRecordIndicator() != null && targetDataRecord.getRecordIndicator() != null && sourceDataRecord.getRecordIndicator().equals(targetDataRecord.getRecordIndicator()))) &&
                                (sourceDataRecord.getRecordPosition() == targetDataRecord.getRecordPosition()) &&
                                (sourceDataRecord.isStartCustomer() == targetDataRecord.isStartCustomer()) &&
                                (sourceDataRecord.isRepeating() == targetDataRecord.isRepeating())
                            )
                    ) {
                        DataRecord targetDataRecordFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> DataRecord.findById(targetDataRecord.getId()));
                        CloneHelper.mapClonedObject(sourceDataRecord, targetDataRecordFinal);
                        break;
                    }
                }
            }
            Set<XmlDataTagDefinition> sourceXmlDataTagDefinitionsSet = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceDataSource.getXmlDataTagDefinitions());
            Set<XmlDataTagDefinition> targetXmlDataTagDefinitionsSet = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataSource.getXmlDataTagDefinitions());
            for (XmlDataTagDefinition sourceXmlDataTagDefinition : sourceXmlDataTagDefinitionsSet) {
                if (getAlreadyClonedObject(sourceXmlDataTagDefinition) != null) continue;

                String sourceTagDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceXmlDataTagDefinition.getDna():sourceXmlDataTagDefinition.getPath());
                for (XmlDataTagDefinition targetXmlDataTagDefinition : targetXmlDataTagDefinitionsSet) {
                    String targetTagDnaOrPath = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> mapByDna?targetXmlDataTagDefinition.getDna():targetXmlDataTagDefinition.getPath());
                    if (sourceTagDnaOrPath.equalsIgnoreCase(targetTagDnaOrPath)) {
                        XmlDataTagDefinition targetXmlDataTagDefinitionFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> XmlDataTagDefinition.findById(targetXmlDataTagDefinition.getId()));
                        CloneHelper.mapClonedObject(sourceXmlDataTagDefinition, targetXmlDataTagDefinitionFinal);
                        Set<XmlDataElement> sourceXmlDataElementsSet = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceXmlDataTagDefinition.getDataElements());
                        Set<XmlDataElement> targetXmlDataElementsSet = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetXmlDataTagDefinition.getDataElements());
                        for (XmlDataElement sourceDataElement : sourceXmlDataElementsSet) {
                            if (getAlreadyClonedObject(sourceDataElement) != null) continue;
                            String sourceElementDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceDataElement.getDna():sourceDataElement.getPath());
                            for (XmlDataElement targetDataElement : targetXmlDataElementsSet) {
                                String targetElementDnaOrPath = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> mapByDna?targetDataElement.getDna():targetDataElement.getPath());
                                if (sourceElementDnaOrPath.equalsIgnoreCase(targetElementDnaOrPath)) {
                                    XmlDataElement targetDataElementFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> (XmlDataElement) XmlDataElement.findById(targetDataElement.getId()));
                                    CloneHelper.mapClonedObject(sourceDataElement, targetDataElementFinal);
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
            }

            Set<XmlDataElement> sourceXmlDataElementsSet = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(),
                    () -> sourceDataSource
                            .getXmlDataTagDefinitions().stream().flatMap(xdtd->xdtd.getDataElements().stream()).collect(Collectors.toSet()));
            Set<XmlDataElement> targetXmlDataElementsSet = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(),
                    () -> targetDataSource
                            .getXmlDataTagDefinitions().stream().flatMap(xdtd->xdtd.getDataElements().stream()).collect(Collectors.toSet()));
            Map<String, XmlDataElement> dnaOrPathToTargetXmlDataElementMap = targetXmlDataElementsSet == null ? new HashMap<>() :
                    CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(),
                            () -> targetXmlDataElementsSet.stream().collect(Collectors.toMap(xde -> mapByDna?xde.getDna():xde.getPath(), Function.identity())));
            for(XmlDataElement sourceXmlDataElement : sourceXmlDataElementsSet) {
                if (getAlreadyClonedObject(sourceXmlDataElement) != null) continue;
                String xmlDataElementDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceXmlDataElement.getDna():sourceXmlDataElement.getPath());
                XmlDataElement targetXmlDataElement = dnaOrPathToTargetXmlDataElementMap.get(xmlDataElementDnaOrPath);
                if(targetXmlDataElement != null) {
                    XmlDataElement targetDataElementFinal = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> (XmlDataElement) XmlDataElement.findById(targetXmlDataElement.getId()));
                    CloneHelper.mapClonedObject(sourceXmlDataElement, targetDataElementFinal);
                }
            }

            Set<JSONDataDefinition> sourceJSONDataDefinitionsSet = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceDataSource.getJsonDataDefinitions());
            Set<JSONDataDefinition> targetJSONDataDefinitionsSet = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataSource.getJsonDataDefinitions());
            Map<String, JSONDataDefinition> dnaToTargetJSONDataDefinitionMap = targetJSONDataDefinitionsSet == null ? new HashMap<>() :
                CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetJSONDataDefinitionsSet.stream().collect(Collectors.toMap(jdd->mapByDna?jdd.getDna():jdd.getPath(), Function.identity())));
            for(JSONDataDefinition sourceJSONDataDefinition : sourceJSONDataDefinitionsSet) {
                String jsonDataDefinitiondna = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceJSONDataDefinition.getDna():sourceJSONDataDefinition.getPath());
                JSONDataDefinition targetJSONDataDefinition = dnaToTargetJSONDataDefinitionMap.get(jsonDataDefinitiondna);
                if(targetJSONDataDefinition != null) {
                    mapClonedObject(sourceJSONDataDefinition, targetJSONDataDefinition);
                    JSONDataElement sourceJSONDataElement = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceJSONDataDefinition.getJsonDataElement());
                    JSONDataElement targetJSONDataElement = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetJSONDataDefinition.getJsonDataElement());
                    if(sourceJSONDataElement != null && targetJSONDataElement != null) {
                        String sourceJSONDataElementDnaOrPath = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> mapByDna?sourceJSONDataElement.getDna():sourceJSONDataElement.getPath());
                        String targetJSONDataElementDnaOrPath = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> mapByDna?targetJSONDataElement.getDna():targetJSONDataElement.getPath());
                        if(sourceJSONDataElementDnaOrPath.equals(targetJSONDataElementDnaOrPath)) {
                            mapClonedObject(sourceJSONDataElement, targetJSONDataElement);
                        }
                    }
                }
            }

            Set<DataGroup> sourceDataGroups = CloneHelper.queryInSchema(sourceDataSource.getObjectSchemaName(), () -> sourceDataSource.getDataGroups());
            Set<DataGroup> targetDataGroups = CloneHelper.queryInSchema(targetDataSource.getObjectSchemaName(), () -> targetDataSource.getDataGroups());
            mapDataGroups(sourceDataGroups, targetDataGroups);
        }
    }

    private static void mapDataGroups(Set<DataGroup> sourceDataGroups, Set<DataGroup> targetDataGroups) {
        Set<DataGroup> unMappedSourceDataGroups = new HashSet<>(sourceDataGroups);
        Set<DataGroup> unMappedTargetDataGroups = new HashSet<>(targetDataGroups);

        for (DataGroup sourceDataGroup : sourceDataGroups) {
            Set<XmlDataTagDefinition> sourceXmlDataTagDefinitions = CloneHelper.queryInSchema(sourceDataGroup.getObjectSchemaName(), () -> sourceDataGroup.getXmlDataTagDefinitions());
            Set<JSONDataDefinition> sourceJSONDataDefinitions = CloneHelper.queryInSchema(sourceDataGroup.getObjectSchemaName(), () -> sourceDataGroup.getJsonDataDefinitions());

            DataRecord sourceDataRecord = CloneHelper.queryInSchema(sourceDataGroup.getObjectSchemaName(), () -> sourceDataGroup.getDataRecord());
            String mappedDataRecordIDs = "";

            if (sourceDataRecord != null) {
                if (getAlreadyClonedObject(sourceDataRecord) != null) {
                    Long clonedObjectId = (Long) getAlreadyClonedObject(sourceDataRecord).getId();
                    mappedDataRecordIDs = Long.toString(clonedObjectId);
                }
            }

            String mappedXmlDataTagDefinitionIDs = sourceXmlDataTagDefinitions.stream().filter(sdr -> getAlreadyClonedObject(sdr) != null).map(sdr -> {
                return ((Long) (getAlreadyClonedObject(sdr).getId())).toString();
            }).sorted().collect(Collectors.joining(","));
            String mappedJsonDataDefinitionIDs = sourceJSONDataDefinitions.stream().filter(sdr ->  getAlreadyClonedObject(sdr) != null).map(sdr -> {
                return ((Long) (getAlreadyClonedObject(sdr).getId())).toString();
            }).sorted().collect(Collectors.joining(","));

            for (DataGroup targetDataGroup : targetDataGroups) {
                if (sourceDataGroup.getLevel() == targetDataGroup.getLevel() &&
                        sourceDataGroup.getName().equals(targetDataGroup.getName())
                ) {
                    Set<XmlDataTagDefinition> targetXmlDataTagDefinitions = CloneHelper.queryInSchema(targetDataGroup.getObjectSchemaName(), () -> targetDataGroup.getXmlDataTagDefinitions());
                    DataRecord targetDataRecord = CloneHelper.queryInSchema(targetDataGroup.getObjectSchemaName(), () -> targetDataGroup.getDataRecord());
                    String targetDataRecordIDs = "";
                    if (targetDataRecord != null) {
                        targetDataRecordIDs = Long.toString(targetDataRecord.getId());
                    }

                    String targetXmlDataTagDefinitionIDs = targetXmlDataTagDefinitions.stream().map(tdr -> {
                        return ((Long) (tdr.getId())).toString();
                    }).sorted().collect(Collectors.joining(","));
                    String targetJsonDataDefinitionIDs = targetXmlDataTagDefinitions.stream().map(tdr -> {
                        return ((Long) (tdr.getId())).toString();
                    }).sorted().collect(Collectors.joining(","));

                    if (mappedDataRecordIDs.equals(targetDataRecordIDs)
                        && mappedXmlDataTagDefinitionIDs.equals(targetXmlDataTagDefinitionIDs)
                        && mappedJsonDataDefinitionIDs.equals(targetJsonDataDefinitionIDs)
                    ) {
                        CloneHelper.mapClonedObject(sourceDataGroup, targetDataGroup);
                        unMappedSourceDataGroups.remove(sourceDataGroup);
                        unMappedTargetDataGroups.remove(targetDataGroup);
                        break;
                    }
                }
            }
        }

        for(DataGroup sourceDataGroup : unMappedSourceDataGroups) {
            String sourceDataGroupFullPath = CloneHelper.queryInSchema(sourceDataGroup.getObjectSchemaName(), () -> sourceDataGroup.getFullPath());
            for(DataGroup targetDataGroup : unMappedTargetDataGroups) {
                String targetDataGroupFullPath = CloneHelper.queryInSchema(targetDataGroup.getObjectSchemaName(), () -> targetDataGroup.getFullPath());
                if(sourceDataGroupFullPath.equals(targetDataGroupFullPath)) {
                    CloneHelper.mapClonedObject(sourceDataGroup, targetDataGroup);
                    break;
                }
            }
        }
    }

    /*
        public static void mapDataGroup(DataGroup sourceDataGroup, DataGroup targetDataGroup) {
            CloneHelper.mapClonedObject(sourceDataGroup, targetDataGroup);
            Set<DataGroup> sourceDataGroups = CloneHelper.queryInSchema(sourceDataGroup.getObjectSchemaName(), ()->sourceDataGroup.getChildren2());
            Set<DataGroup> targetDataGroups = CloneHelper.queryInSchema(targetDataGroup.getObjectSchemaName(), ()->targetDataGroup.getChildren2());
            mapDataGroups(sourceDataGroups, targetDataGroups);
        }
    */
    public static void mapTargetGroup(TargetGroup sourceTargetGroup, TargetGroup targetTargetGroup) {
        CloneHelper.mapClonedObject(sourceTargetGroup, targetTargetGroup);
        TargetGroupInstance sourceTargetGroupInstance = CloneHelper.queryInSchema(sourceTargetGroup.getObjectSchemaName(), () -> sourceTargetGroup.getInstance());
        TargetGroupInstance targetTargetGroupInstance = CloneHelper.queryInSchema(targetTargetGroup.getObjectSchemaName(), () -> targetTargetGroup.getInstance());
        if (sourceTargetGroupInstance != null && targetTargetGroupInstance != null) {
            CloneHelper.mapClonedObject(sourceTargetGroupInstance, targetTargetGroupInstance);
            Set<ConditionItem> sourceConditionItems = CloneHelper.queryInSchema(sourceTargetGroup.getObjectSchemaName(),
                    () -> sourceTargetGroupInstance.getConditionItems());
            Map<String, ConditionItem> targetConditionItemsMap = CloneHelper.queryInSchema(targetTargetGroup.getObjectSchemaName(),
                    () -> targetTargetGroupInstance.getConditionItems().stream().collect(Collectors.toMap(ci -> ci.getConditionElement().getGuid(), Function.identity())));
            for (ConditionItem sourceConditionItem : sourceConditionItems) {
                ConditionElement sourceConditionElement = sourceConditionItem.getConditionElement();
                String elementGuid = sourceConditionElement.getGuid();
                ConditionItem targetConditionItem = targetConditionItemsMap.get(elementGuid);
                if (sourceConditionItem != null && targetConditionItem != null) {
                    CloneHelper.mapClonedObject(sourceConditionItem, targetConditionItem);
                    ConditionElement targetConditionElement = targetConditionItem.getConditionElement();
                    if (sourceConditionElement != null && targetConditionElement != null) {
                        CloneHelper.mapClonedObject(sourceConditionElement, targetConditionElement);
                    }
                    Set<ConditionItemValue> sourceConditionItemValues = CloneHelper.queryInSchema(sourceTargetGroup.getObjectSchemaName(), () -> sourceConditionItem.getConditionItemValues());
                    Map<String, ConditionItemValue> targetConditionItemValuesMap = CloneHelper.queryInSchema(targetTargetGroup.getObjectSchemaName(),
                            () -> targetConditionItem.getConditionItemValues().stream().collect(Collectors.toMap(tiv -> getConditionSubelementDataValue(tiv.getConditionSubelement()), Function.identity(), (p1, p2)->p1)));
                    for (ConditionItemValue sourceConditionItemValue : sourceConditionItemValues) {
                        ConditionSubelement sourceConditionSubElement = sourceConditionItemValue.getConditionSubelement();
                        String sourceConditionSubelementDataValue = getConditionSubelementDataValue(sourceConditionSubElement);
                        ConditionItemValue targetConditionItemValue = targetConditionItemValuesMap.get(sourceConditionSubelementDataValue);
                        if (sourceConditionItemValue != null && targetConditionItemValue != null) {
                            CloneHelper.mapClonedObject(sourceConditionItemValue, targetConditionItemValue);
                            ConditionSubelement targetConditionSubElement = targetConditionItemValue.getConditionSubelement();
                            if (sourceConditionSubElement != null && targetConditionSubElement != null) {
                                CloneHelper.mapClonedObject(sourceConditionSubElement, targetConditionSubElement);
                            }
                        }
                    }
                }
            }

        }

    }

    public static String getConditionSubelementDataValue(ConditionSubelement cs) {
        String dataValue = (cs == null) ? "" : (cs.getGuid() != null ? cs.getGuid() : (cs.getName() + "[" + (cs.getDataElementVariable() == null ? "" : cs.getDataElementVariable().getDna()) + "]" + "[" + ConditionElementUtil.getDataElementValueString(cs.getDataElementComparisonId(), cs.getDataValueSubtype(), cs.getConditionAttributes(), cs.getDataFilePath()) + "]"));
        return dataValue;
    }

    public static void addContentForSwapRef(Content content) {
        if (isSyncing.get() || isCloning.get() > ID_CLONING_TYPE_INACTIVE) {
            clonedContentIDs.get().add(content.getId());
        }
    }

    public static void removeFromSwapRef(Content content) {
        clonedContentIDs.get().remove(content.getId());
    }

    public static Set<Long> getContentIDsForSwap() {
        return new HashSet<>(clonedContentIDs.get());
    }

    public static void addComplexValueForSwapRef(ComplexValue complexValue) {
        if (isSyncing.get() || isCloning.get() > ID_CLONING_TYPE_INACTIVE) {
            clonedComplexValueIDs.get().add(complexValue.getId());
        }
    }

    public static void removeFromSwapRef(ComplexValue complexValue) {
        clonedComplexValueIDs.get().remove(complexValue.getId());
    }

    public static Set<Long> getComplexValueIDsForSwap() {
        return new HashSet<>(clonedComplexValueIDs.get());
    }


    public static void addNewClonedDataSourceAssociation(DataSourceAssociation source, DataSourceAssociation target) {
        clonedDataCollectionIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedDataSourceAssociationMap() {
        return new HashMap<>(clonedDataCollectionIDsMap.get());
    }

    public static void addNewClonedDataSource(DataSource source, DataSource target) {
        clonedDataSourceIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedDataSourceMap() {
        return new HashMap<>(clonedDataSourceIDsMap.get());
    }

    public static void addNewClonedLookupTable(LookupTable source, LookupTable target) {
        clonedLookupTableIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedLookupTableMap() {
        return new HashMap<>(clonedLookupTableIDsMap.get());
    }

    public static void addNewClonedDataElementVariable(DataElementVariable source, DataElementVariable target) {
        clonedDataElementVariableIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedDataElementVariableMap() {
        return new HashMap<>(clonedDataElementVariableIDsMap.get());
    }

    public static void addNewClonedTargetGroup(TargetGroup source, TargetGroup target) {
        clonedTargetGroupIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedTargetGroupMap() {
        return new HashMap<>(clonedTargetGroupIDsMap.get());
    }

    public static void addNewClonedTargetRule(ConditionElement source, ConditionElement target) {
        clonedTargetRuleIDsMap.get().put(source.getId(), target.getId());
    }

    public static Map<Long, Long> getNewClonedTargetRuleMap() {
        return new HashMap<>(clonedTargetRuleIDsMap.get());
    }

    public static void addNewClonedSyncHistory(SyncHistory source) {
        clonedSyncHistoryIDsSet.get().add(source.getId());
    }

    public static Map<Long, Long> getNewClonedMetadataFormDefinitionMap() {
        return new HashMap<>(clonedMetadataFormDefinitionIDsMap.get());
    }

    public static void addNewClonedMetadataFormDefinition(MetadataFormDefinition source, MetadataFormDefinition target) {
        clonedMetadataFormDefinitionIDsMap.get().put(source.getId(), target.getId());
    }

    public static boolean isSyncHistoryAlreadyCloned(Long sourceId) {
        return clonedSyncHistoryIDsSet.get().contains(sourceId);
    }

    private static ThreadLocal<Map<String, Set<String>>> usedNames = ThreadLocal.withInitial(() -> new HashMap<>());

    public static <T extends IdentifiableMessagePointModel> void cloneName(T sourceModel, T targetModel, boolean isCreatingNew) {
        boolean useCache = getIsCloningActive() || isImporting.get() || isSyncing.get();
        String sourceName = sourceModel.getName();
        String targetName = CloneHelper.queryInSaveSession(()->{
            if(sourceName == null) return null;
            if(sourceName.isEmpty()) return sourceName;

            if(useCache) {
                Set<String> usedNamesForClass = usedNames.get().get(targetModel.getClass().getName());
                if(usedNamesForClass == null) {
                    usedNamesForClass = ApplicationUtil.getAllNames(targetModel.getClass(), null);
                    usedNames.get().put(targetModel.getClass().getName(), usedNamesForClass);
                }
                Set<String> usedNamesForSearch = new HashSet<>(usedNamesForClass);
                if(! isCreatingNew) {
                    usedNamesForSearch.remove(targetModel.getName());
                }
                String newName = ApplicationUtil.getUnusedName(sourceName, usedNamesForSearch);
                usedNamesForClass.add(newName);
                return newName;
            } else {
                usedNames.set(new HashMap<>());
                return ApplicationUtil.getUnusedName(sourceName, targetModel.getClass(), List.of(targetModel.getId()));
            }
        });
        targetModel.setName(targetName);
    }

    private static ThreadLocal<Map<String, Set<String>>> usedFriendlyNames = ThreadLocal.withInitial(() -> new HashMap<>());

    public static <T extends Variable> void cloneFriendlyName(T sourceModel, T targetModel, boolean isCreatingNew) {
        boolean useCache = getIsCloningActive() || isImporting.get() || isSyncing.get();
        String sourceFriendlyName = sourceModel.getFriendlyName();
        String targetFriendlyName = CloneHelper.queryInSaveSession(()->{
            if(sourceFriendlyName == null) return null;
            if(sourceFriendlyName.isEmpty()) return sourceFriendlyName;

            if(useCache) {
                Set<String> usedFriendlyNamesForClass = usedFriendlyNames.get().get(targetModel.getClass().getName());
                if(usedFriendlyNamesForClass == null) {
                    usedFriendlyNamesForClass = ApplicationUtil.getAllFriendlyNames(targetModel.getClass(), null);
                    usedFriendlyNames.get().put(targetModel.getClass().getName(), usedFriendlyNamesForClass);
                }
                Set<String> usedFriendlyNamesForSearch = new HashSet<>(usedFriendlyNamesForClass);
                if(! isCreatingNew) {
                    usedFriendlyNamesForSearch.remove(targetModel.getFriendlyName());
                }
                String newFriendlyName = ApplicationUtil.getUnusedName(sourceFriendlyName, usedFriendlyNamesForSearch);
                usedFriendlyNamesForClass.add(newFriendlyName);
                return newFriendlyName;
            } else {
                usedFriendlyNames.set(new HashMap<>());
                return ApplicationUtil.getUnusedFriendlyName(sourceFriendlyName, targetModel.getClass(), List.of(targetModel.getId()));
            }
        });

        targetModel.setFriendlyName(targetFriendlyName);
    }
//                                 Object Type  VarID     ObjID Name
    private static ThreadLocal<
        Map<Integer,          // Object Type
            Map<String,       // Variant tree node DNA, "Master" for global objects
                Set<String>   // Name already used
            >
        >
    > usedDocumentContentObjectNames = ThreadLocal.withInitial(() -> new HashMap<>());
    public static String getUnusedContentObjectName(ContentObject sourceModel, ContentObject targetModel, Document targetDocument, boolean isCreatingNew) {
        boolean useCache = getIsCloningActive() || isImporting.get() || isSyncing.get();
        String contentObjectName = sourceModel.getName();
        int objectType = sourceModel.getObjectType();
        boolean isMessageOrTouchpointLocal = sourceModel.isMessageOrTouchpointLocal();
        String tpVariantNodeDna = sourceModel.getOwningTouchpointSelection() == null ? "MASTER" :
            sourceModel.getOwningTouchpointSelection().getParameterGroupTreeNode().getDna();

        String newContentObjectName = CloneHelper.queryInSaveSession(() -> {
            if(contentObjectName == null) return null;

            if(useCache) {
                Map<String, Set<String>> namesUsedByType = usedDocumentContentObjectNames.get().get(objectType);
                if(namesUsedByType == null) {
                    namesUsedByType = new HashMap<>();
                    usedDocumentContentObjectNames.get().put(objectType, namesUsedByType);
                    {
                        Collection<ContentObject> contentObjects = null;
                        switch (objectType) {
                            case ContentObject.OBJECT_TYPE_GLOBAL_IMAGE:
                                contentObjects = ContentObject.findAllGlobalImageLibraryItems();
                                break;
                            case ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT:
                                contentObjects = ContentObject.findAllWithObjectType(ContentObject.OBJECT_TYPE_GLOBAL_SMART_TEXT);
                                break;
                            case ContentObject.OBJECT_TYPE_MESSAGE:
                                contentObjects = ContentObject.findMessagesByDocument(targetDocument, false);
                                break;
                            case ContentObject.OBJECT_TYPE_LOCAL_SMART_TEXT:
                                contentObjects = ContentObject.findLocalSmartTextByDocument(targetDocument.getId(), false);
                                break;
                            case ContentObject.OBJECT_TYPE_LOCAL_IMAGE:
                                contentObjects = ContentObject.findLocalImagesByDocument(targetDocument.getId(), false);
                                break;
                        }

                        if (contentObjects != null && !contentObjects.isEmpty()) {
                            for (ContentObject contentObject : contentObjects) {
                                if(contentObject.isRemoved()) continue;
                                String owningVariantNodeDna = contentObject.getOwningTouchpointSelection() == null ? "MASTER" :
                                    contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode().getDna();
                                Set<String> namesUsedInOwningVariant = namesUsedByType.get(owningVariantNodeDna);
                                if (namesUsedInOwningVariant == null) {
                                    namesUsedInOwningVariant = new HashSet<>();
                                    namesUsedByType.put(owningVariantNodeDna, namesUsedInOwningVariant);
                                }
                                namesUsedInOwningVariant.add(contentObject.getName());
                            }
                        }
                    }
                }

                Set<String> namesUsedInVariant = namesUsedByType.get(tpVariantNodeDna);
                if(namesUsedInVariant == null) {
                    namesUsedInVariant = new HashSet<>();
                    namesUsedByType.put(tpVariantNodeDna, namesUsedInVariant);
                }

                Set<String> namedUsedInSearch = new HashSet<>(namesUsedInVariant);
                if(! isCreatingNew) {
                    namedUsedInSearch.remove(targetModel.getName());
                }
                String newName = ApplicationUtil.getUnusedName(contentObjectName, namedUsedInSearch);
                namesUsedInVariant.add(newName);
                return newName;
            } else {
                if (ContentObject.findByName(contentObjectName, objectType, false) == null) {
                    return contentObjectName;
                }
                if(isMessageOrTouchpointLocal) {
                    return contentObjectName;
                }
                String newContentObjectNamePrefix = ApplicationUtil.getPureName(contentObjectName);
                int suffixNumber = 0;
                String newName;
                while (true) {
                    newName = newContentObjectNamePrefix + "_" + (++suffixNumber);
                    ContentObject existingContentObject = ContentObject.findByName(newName, objectType, false);
                    if (existingContentObject == null) break;
                    if (targetModel != null && targetModel.getId() == existingContentObject.getId()) break;
                }
                return newName;
            }
        });

        return newContentObjectName;
    }

    static ThreadLocal <Map<Long, Map<String, Map<Long, Set<String>>>>> objectsForIdSwapMap = ThreadLocal.withInitial(()->new HashMap<>());

    public static void addObjectForContentObjectIdSwap(Long sourceObjectId, IdentifiableMessagePointModel targetModel, String field) {
        Map<String, Map<Long, Set<String>>> targetObjectMap = objectsForIdSwapMap.get().get(sourceObjectId);
        if(targetObjectMap == null) {
            targetObjectMap = new HashMap<>();
            objectsForIdSwapMap.get().put(sourceObjectId, targetObjectMap);
        }
        Map<Long, Set<String>> targetObjectClassMap = targetObjectMap.get(targetModel.getClass().getName());
        if(targetObjectClassMap == null) {
            targetObjectClassMap = new HashMap<>();
            targetObjectMap.put(targetModel.getClass().getName(), targetObjectClassMap);
        }
        Set<String> fields = targetObjectClassMap.get(targetModel.getId());
        if(fields == null) {
            fields = new HashSet<>();
            targetObjectClassMap.put(targetModel.getId(), fields);
        }
        fields.add(field);
    }

    public static void swapContentObjectIDs() {
        Map<Long, Long> clonedIdMap = getClonedIDsMap();
        for(Long sourceObjectId : objectsForIdSwapMap.get().keySet()) {
            Long clonedObjectId = clonedIdMap.get(sourceObjectId);
            if(clonedObjectId == null) continue;
            Map<String, Map<Long, Set<String>>> targetObjectClassMap = objectsForIdSwapMap.get().get(sourceObjectId);
            execInSaveSession(()->{
                ContentObject clonedContentObject = ContentObject.findById(clonedObjectId);
                if(clonedContentObject == null) return;

                for(String targetClassName : targetObjectClassMap.keySet()) {
                    Map<Long, Set<String>> targetObjectMap = targetObjectClassMap.get(targetClassName);
                    try {
                        Class targetClass = Class.forName(targetClassName);
                        for(Long targetObjectId : targetObjectMap.keySet()) {
                            IdentifiableMessagePointModel targetModel = (IdentifiableMessagePointModel) HibernateUtil.getManager().getObject(targetClass, targetObjectId);
                            Set<String> fields = targetObjectMap.get(targetObjectId);
                            for(String fieldName : fields) {
                                Field field = targetClass.getDeclaredField(fieldName);
                                if(field != null) {
                                    field.setAccessible(true);
                                    field.set(targetModel, clonedContentObject);
                                }
                            }
                            HibernateUtil.getManager().saveObject(targetModel);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            });
        }
    }
}
