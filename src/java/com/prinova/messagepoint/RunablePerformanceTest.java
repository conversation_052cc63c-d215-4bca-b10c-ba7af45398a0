package com.prinova.messagepoint;

import org.apache.commons.logging.Log;
import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.util.LogUtil;

public class RunablePerformanceTest extends MessagePointRunnable {
	private static final Log log = LogUtil.getLog(RunablePerformanceTest.class);
	private int seq;
	
	public RunablePerformanceTest() {}
	public RunablePerformanceTest(int i) { seq = i; }
	   
	public void performMainProcessing() {

		  StringBuilder str = new StringBuilder();
	      long begTest = new java.util.Date().getTime();
	      log.error("RunablePerformanceTest start task #" + seq);
	      //try {
	         // sleep for 1 second to simulate a remote call,
	         // just waiting for the call to return
	         // Thread.sleep(1000);
	         // loop that just concatenate a str to simulate
	         // work on the result form remote call
	         for(int i = 0; i < 100000; i++)
	            str.append('t');
	      //} catch (InterruptedException e) {}
	      Double secs = Double.valueOf((new java.util.Date().getTime() - begTest)*0.001);
	      log.error("RunablePerformanceTest task #" + seq + " is done. Run time " + secs + " secs");
	}

}
