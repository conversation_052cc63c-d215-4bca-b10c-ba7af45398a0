package com.prinova.messagepoint.integrator;

import java.io.Serializable;

import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.SequenceGenerator;

public class MessagepointSequenceGenerator extends SequenceGenerator {
    
    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) {
        Serializable id = session.getEntityPersister(null, object).getClassMetadata().getIdentifier(object, session);
        return (id != null && (Long) id > 0) ? id : super.generate(session, object);
    }
}
