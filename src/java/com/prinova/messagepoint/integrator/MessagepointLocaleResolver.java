package com.prinova.messagepoint.integrator;

import java.util.Locale;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.LocaleResolver;

import com.prinova.messagepoint.model.admin.ApplicationLocale;

public class MessagepointLocaleResolver implements LocaleResolver {
	public static final String PARM_LANG 					= "lang";
	
	@Override
	public Locale resolveLocale(HttpServletRequest request) {
		// Get the locale from the session
		String appLocaleCode = (String) request.getSession().getAttribute(ApplicationLocale.SESSION_ATTR_NAME);
		if(appLocaleCode != null && !appLocaleCode.isEmpty()){
			return new Locale(appLocaleCode);
		}else{
			// Get the locale from the URL parameter
			appLocaleCode = ServletRequestUtils.getStringParameter(request, PARM_LANG, null);
			if(appLocaleCode != null && !appLocaleCode.isEmpty()){
				return new Locale(appLocaleCode);
			}else{
				// Get the locale from the cookies
				Cookie[] cookies = request.getCookies();
				if (cookies != null) 
				{
					for(Cookie cookie : cookies){
						if(cookie.getName().equals(PARM_LANG)){
							appLocaleCode = cookie.getValue();
						}
					}
				}
				if(appLocaleCode != null && !appLocaleCode.isEmpty()){
					return new Locale(appLocaleCode);
				}else{
					return new Locale(ApplicationLocale.resolveMissingUserApplicationLocale().getLanguageCode());
				}
			}
		}
	}

	@Override
	public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
	}

}
