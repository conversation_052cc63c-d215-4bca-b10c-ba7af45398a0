package com.prinova.messagepoint.integrator;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.quartz.*;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.web.context.support.XmlWebApplicationContext;

import java.util.Date;
import java.util.List;

public class MessagepointQuartzUtils {
    private static final Log log = LogUtil.getLog(MessagepointQuartzUtils.class);

    public static void scheduleJob(Class<? extends MessagePointRunnable> clazz, FactoryBean triggerBean){
        try {
            Object job = clazz.newInstance();
            // Create Job
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                JobDetailFactoryBean jobDetailFactoryBean = new JobDetailFactoryBean();
                jobDetailFactoryBean.setJobClass(MessagepointQuartzJobBean.class);
                jobDetailFactoryBean.setName(MessagepointQuartzUtils.getJobKeyStr(clazz));
                jobDetailFactoryBean.setDurability(true);
                JobDataMap jobdataMap = new JobDataMap();
                jobdataMap.put("batchProcessorName", MessagepointQuartzUtils.getJobKeyStr(clazz));
                jobDetailFactoryBean.setJobDataMap(jobdataMap);
                jobDetailFactoryBean.afterPropertiesSet();

                Trigger trigger = MessagepointQuartzUtils.getTriggerFromBean(triggerBean, jobDetailFactoryBean.getObject());

                if (trigger != null) {
                    if (!scheduler.isStarted()) {
                        scheduler.start();
                    }

                    if (jobDetailFactoryBean.getObject() != null && !scheduler.checkExists(jobDetailFactoryBean.getObject().getKey())) {
                        scheduler.scheduleJob(jobDetailFactoryBean.getObject(), trigger);
                    }

                }
            }
        }catch (Exception e){
            MessagepointQuartzUtils.log.error("Error:", e);
        }
    }

    public static void rescheduleJob(Class<? extends MessagePointRunnable> clazz, FactoryBean triggerBean){
        try {
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                boolean isJobScheduled = false;
                if (scheduler.checkExists(MessagepointQuartzUtils.getJobKey(clazz))) {
                    if (!scheduler.isStarted()) {
                        scheduler.start();
                    } else {
                        JobDetail jobDetail = scheduler.getJobDetail(MessagepointQuartzUtils.getJobKey(clazz));
                        Trigger trigger = MessagepointQuartzUtils.getTriggerFromBean(triggerBean, jobDetail);
                        List<? extends Trigger> triggerList = scheduler.getTriggersOfJob(MessagepointQuartzUtils.getJobKey(clazz));
                        TriggerKey triggerKey = trigger.getKey();
                        if (!triggerList.isEmpty()) {
                            isJobScheduled = true;
                            triggerKey = triggerList.get(0).getKey();
                        } else {
                            triggerKey = trigger.getKey();
                        }
                        Date updatedTriggerDate = scheduler.rescheduleJob(triggerKey, trigger);
                    }
                }

                if (!isJobScheduled) {
                    MessagepointQuartzUtils.scheduleJob(clazz, triggerBean);
                }
            }
        }catch (SchedulerException e){
            MessagepointQuartzUtils.log.error("Error:", e);
        }
    }

    public static void pauseJob(Class<? extends MessagePointRunnable> clazz){
        try {
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                scheduler.pauseJob(MessagepointQuartzUtils.getJobKey(clazz));
            }
        }catch (SchedulerException e){
            log.error("Error:", e);
        }
    }

    public static void resumeJob(Class<? extends MessagePointRunnable> clazz){
        try {
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                scheduler.resumeJob(MessagepointQuartzUtils.getJobKey(clazz));
            }
        }catch (SchedulerException e){
            MessagepointQuartzUtils.log.error("Error:", e);
        }
    }

    public static void deleteJob(Class<? extends MessagePointRunnable> clazz){
        try {
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                if (scheduler.checkExists(MessagepointQuartzUtils.getJobKey(clazz))) {
                    scheduler.deleteJob(MessagepointQuartzUtils.getJobKey(clazz));
                }
            }
        }catch (SchedulerException e){
            MessagepointQuartzUtils.log.error("Error:", e);
        }
    }

    public static CronTrigger retrieveCronTrigger(Class<? extends MessagePointRunnable> clazz){
        try {
            Scheduler scheduler = MessagepointQuartzUtils.getScheduler();

            if(scheduler != null) {
                List<? extends Trigger> triggerList = scheduler.getTriggersOfJob(MessagepointQuartzUtils.getJobKey(clazz));
                if (triggerList != null && !triggerList.isEmpty()) {
                    Trigger trigger = triggerList.get(0);
                    if (trigger instanceof CronTrigger) {
                        return (CronTrigger) trigger;
                    }
                }
            }
        }catch (SchedulerException e){
            MessagepointQuartzUtils.log.error("Error:", e);
        }
        return null;
    }

    public static void debugApplicationContext(){
        XmlWebApplicationContext applicationContext = (XmlWebApplicationContext) ApplicationUtil.getStaticApplicationContext();

        String[] configLocations = applicationContext.getConfigLocations();
        for(String configLocation : configLocations){
            MessagepointQuartzUtils.log.info("Config Location: " + configLocation);
        }
        String[] beanDefs = applicationContext.getBeanDefinitionNames();
        for(String beanDef : beanDefs){
            MessagepointQuartzUtils.log.info("Bean: " + beanDef);
        }
    }

    private static Trigger getTriggerFromBean(FactoryBean triggerBean, JobDetail jobDetail){
        Trigger trigger = null;
        if(triggerBean instanceof MessagepointSimpleTriggerFactoryBean){
            MessagepointSimpleTriggerFactoryBean simpleTriggerFactoryBean = (MessagepointSimpleTriggerFactoryBean)triggerBean;
            simpleTriggerFactoryBean.setJobDetail(jobDetail);
            simpleTriggerFactoryBean.afterPropertiesSet();
            trigger = simpleTriggerFactoryBean.getObject();
        }else if(triggerBean instanceof MessagepointCronTriggerFactoryBean){
            MessagepointCronTriggerFactoryBean cronTriggerFactoryBean = (MessagepointCronTriggerFactoryBean)triggerBean;
            cronTriggerFactoryBean.setJobDetail(jobDetail);
            cronTriggerFactoryBean.afterPropertiesSet();
            trigger = cronTriggerFactoryBean.getObject();
        }
        return trigger;
    }

    private static String getJobKeyStr(Class<? extends MessagePointRunnable> clazz){
        return clazz.getSimpleName() + "JobBean";
    }

    private static JobKey getJobKey(Class<? extends MessagePointRunnable> clazz){
        return new JobKey(MessagepointQuartzUtils.getJobKeyStr(clazz), Scheduler.DEFAULT_GROUP);
    }

    private static Scheduler getScheduler(){
        Scheduler scheduler = null;
        try{
            scheduler = ApplicationUtil.getBean("scheduler", Scheduler.class);
        }catch (NoSuchBeanDefinitionException nsbe){
            MessagepointQuartzUtils.log.error(nsbe);
        }
        return scheduler;
    }

}
