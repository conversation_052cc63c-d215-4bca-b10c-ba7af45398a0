package com.prinova.messagepoint.integrator;

import java.beans.PropertyVetoException;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Properties;

import javax.sql.DataSource;

import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.PropertyUtils;
import org.apache.commons.logging.Log;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.hibernate.service.spi.ServiceRegistryAwareService;
import org.hibernate.service.spi.ServiceRegistryImplementor;

import com.mchange.v2.c3p0.ComboPooledDataSource;

/**
 *
 * Multi-Tenant Provider
 *
 */
public class MessagepointMultiTenantConnectionProvider implements MultiTenantConnectionProvider, ServiceRegistryAwareService {
	private static final long serialVersionUID = 1449047058249424959L;
	private static final Log log = LogUtil.getLog(MessagepointMultiTenantConnectionProvider.class);

	private static ComboPooledDataSource dataSource;
	private static String defaultDriver;
	private static String defaultJdbcURL;
	private static String defaultUserName;
	private static String defaultPassword;
	private static int maxPoolSize;

	/**
     *
     * Constructor. Initializes the ComboPooledDataSource based on the config.properties.
     *
     */
    public MessagepointMultiTenantConnectionProvider() throws PropertyVetoException {
        log.debug("Initializing MessagepointMultiTenantConnectionProvider");

        Properties properties;
        try {

			HashMap<String, Properties> runtimeProperties = PropertyUtils.getRuntimeProperties();

			if (runtimeProperties.containsKey("applicationContext-common.properties")) {
				properties = runtimeProperties.get("applicationContext-common.properties");
			} else {
				properties = new Properties();
				properties.load(Thread.currentThread().getContextClassLoader().getResourceAsStream("applicationContext-common.properties"));
			}


        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        
        /**
         * db.messagepoint.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
         * db.messagepoint.driver=oracle.jdbc.driver.OracleDriver
         * db.messagepoint.url=******************************************
         * db.messagepoint.user=MASTER
         * db.messagepoint.password=password
         * db.messagepoint.xa.datasource=oracle.jdbc.xa.client.OracleXADataSource
         * db.messagepoint.datasourcename=java:/messagepoint_TX_DS
         */
        
        defaultDriver = properties.getProperty("db.messagepoint.driver");
        defaultJdbcURL = properties.getProperty("db.messagepoint.url");
        defaultUserName = properties.getProperty("db.messagepoint.user");
        defaultPassword = properties.getProperty("db.messagepoint.password");
        
        log.debug("MessagepointMultiTenantConnectionProvider initialised");
    }
	
    @Override
    public void injectServices(ServiceRegistryImplementor serviceRegistry) {
/**    	
    	// Get POD MASTER user (schema) from JBOSS data source
    	//
        try {
            DataSource jbossDataSource = (DataSource) serviceRegistry.getService( ConfigurationService.class ).getSettings().get( Environment.DATASOURCE );
            dataSource = jbossDataSource;
            Connection connection = jbossDataSource.getConnection();
            if (connection != null)
            {
            	DatabaseMetaData dmd = connection.getMetaData();
            	if (dmd != null)
            	{
            		defaultUserName = dmd.getUserName();
            		defaultJdbcURL = dmd.getURL();
            	}
            }
        } 
        catch (SQLException e) {
        	log.error("MessagepointMultiTenantConnectionProvider injectServices SQLException: " + e.getMessage());
		} 
**/        
        // Create C3P0 Pooled Data Source
        //
        try {
            ComboPooledDataSource cpds = new ComboPooledDataSource();
            cpds.setDriverClass(defaultDriver);
            cpds.setJdbcUrl(defaultJdbcURL);
            cpds.setUser(defaultUserName);
            cpds.setPassword(defaultPassword);
            dataSource = cpds;
			maxPoolSize = dataSource.getMaxPoolSize();
        }
        catch (PropertyVetoException e) {
        	log.error("MessagepointMultiTenantConnectionProvider injectServices PropertyVetoException: " + e.getMessage());
		}
		
    }
    
	/**
	 * Allows access to the database metadata of the underlying database(s) in situations where we do not have a
	 * tenant id (like startup processing, for example).
	 *
	 * @return The database metadata.
	 *
	 * @throws SQLException Indicates a problem opening a connection
	 */
	@Override
	public Connection getAnyConnection() throws SQLException {
		/** for ComboPooledDataSource type
        log.error("Get Any Connection:::Number of connections (max: busy - idle): {" + dataSource.getMaxPoolSize() + "} : {" + dataSource.getNumBusyConnectionsAllUsers() + " } - {" + dataSource.getNumIdleConnectionsAllUsers() + " } ");
        if (dataSource.getNumConnectionsAllUsers() == dataSource.getMaxPoolSize()){
            log.error("Maximum number of connections opened");
        }
        if (dataSource.getNumConnectionsAllUsers() == dataSource.getMaxPoolSize() && dataSource.getNumIdleConnectionsAllUsers()==0){
            log.error("Connection pool empty!");
        }
        **/
        return dataSource.getConnection();
	}

	/**
	 * Obtains a connection for Hibernate use according to the underlying strategy of this provider.
	 *
	 * @param tenantIdentifier The identifier of the tenant for which to get a connection
	 *
	 * @return The obtained JDBC connection
	 *
	 * @throws SQLException Indicates a problem opening a connection
	 * @throws org.hibernate.HibernateException Indicates a problem otherwise obtaining a connection.
	 */
	@Override
	public Connection getConnection(String tenantIdentifier) throws SQLException {
		if (tenantIdentifier != null && !tenantIdentifier.isEmpty()) {
			if (isPostgres()) {
				tenantIdentifier = tenantIdentifier.toLowerCase();
			}
			try
			{
				if (dataSource.getNumBusyConnections(tenantIdentifier, defaultPassword) >= maxPoolSize)
				{
					log.error("getConnection ----- there is no available connection for schema " + tenantIdentifier);
					logCurrentStatus(tenantIdentifier);
					return null;
				}
			} catch (SQLException e) {
				if (!e.getMessage().startsWith("No pool has been initialized"))
					log.error("getConnection " + tenantIdentifier + ": " + e.getMessage());
			}

			return dataSource.getConnection(tenantIdentifier, defaultPassword);
		}
		else {
			try
			{
				if (dataSource.getNumBusyConnectionsDefaultUser() >= maxPoolSize)
				{
					log.error("getConnection ----- there is no available connection for schema NULL - POD MASTER");
					logCurrentStatus(tenantIdentifier);
					return null;
				}
			} catch (SQLException e) {
				log.error("getConnection POD MASTER: " + e.getMessage());
			}

			return dataSource.getConnection();
		}
	}

	/**
	 * Release a connection obtained from {@link #getAnyConnection}
	 *
	 * @param connection The JDBC connection to release
	 *
	 * @throws SQLException Indicates a problem closing the connection
	 */
	@Override
	public void releaseAnyConnection(Connection connection) throws SQLException {
		connection.close();
	}

	/**
	 * Release a connection from Hibernate use.
	 *
	 * @param connection The JDBC connection to release
	 * @param tenantIdentifier The identifier of the tenant.
	 *
	 * @throws SQLException Indicates a problem closing the connection
	 * @throws org.hibernate.HibernateException Indicates a problem otherwise releasing a connection.
	 */
	@Override
	public void releaseConnection(String tenantIdentifier, Connection connection) throws SQLException {
        try {
            this.releaseAnyConnection(connection);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }	
	}

	/**
	 * Does this connection provider support aggressive release of JDBC
	 * connections and re-acquisition of those connections (if need be) later?
	 * <p/>
	 * This is used in conjunction with {@link org.hibernate.cfg.Environment#RELEASE_CONNECTIONS}
	 * to aggressively release JDBC connections.  However, the configured ConnectionProvider
	 * must support re-acquisition of the same underlying connection for that semantic to work.
	 * <p/>
	 * Typically, this is only true in managed environments where a container
	 * tracks connections by transaction or thread.
	 *
	 * Note that JTA semantic depends on the fact that the underlying connection provider does
	 * support aggressive release.
	 *
	 * @return {@code true} if aggressive releasing is supported; {@code false} otherwise.
	 */
	@Override
	public boolean supportsAggressiveRelease() {
		return true;
	}

	@Override
	public boolean isUnwrappableAs(Class arg0) {
		return false;
	}

	@Override
	public <T> T unwrap(Class<T> arg0) {
		return null;
	}
	
	public void logCurrentStatus(String tenantIdentifier)
	{
        try 
        {
        	boolean notDefaultUser = tenantIdentifier != null && !tenantIdentifier.isEmpty();

			dataSource.getLoginTimeout();

			int num_busy_connections = 0;
			if (notDefaultUser)
				num_busy_connections = dataSource.getNumBusyConnections(tenantIdentifier, defaultPassword);
			else
				num_busy_connections = dataSource.getNumBusyConnectionsDefaultUser();
			
			if (num_busy_connections > 0)
			{
				if (notDefaultUser)
					log.info("num_busy_connections " + tenantIdentifier + ": " + num_busy_connections);
				else
					log.info("num_busy_connections NULL - POD MASTER: " + num_busy_connections);

				log.info("num_busy_connections default schema: " + dataSource.getNumBusyConnectionsDefaultUser());
				log.info("num_busy_connections all schemas: " + dataSource.getNumBusyConnectionsAllUsers());

				if (notDefaultUser)
					log.info("num_idle_connections " + tenantIdentifier + ": " + dataSource.getNumIdleConnections(tenantIdentifier, defaultPassword));
				else
					log.info("num_idle_connections NULL - POD MASTER: " + dataSource.getNumIdleConnectionsDefaultUser());

				log.info("num_idle_connections default schema: " + dataSource.getNumIdleConnectionsDefaultUser());
				log.info("num_idle_connections all schemas: " + dataSource.getNumIdleConnectionsAllUsers());

				if (notDefaultUser)
					log.info("num_connections " + tenantIdentifier + ": " + dataSource.getNumConnections(tenantIdentifier, defaultPassword));
				else
					log.info("num_connections NULL - POD MASTER: " + dataSource.getNumConnectionsDefaultUser());

				log.info("num_connections default schema: " + dataSource.getNumConnectionsDefaultUser());
				log.info("num_connections all schemas: " + dataSource.getNumConnectionsAllUsers());

				log.info("max_num_connections per schema: " + dataSource.getMaxPoolSize());
			}

        } catch (SQLException e) {
            log.error("logCurrentStatus: ", e);
        }
	}

	public static void logCurrentStaticStatus(String tenantIdentifier)
	{
		try
		{
			boolean notDefaultUser = tenantIdentifier != null && !tenantIdentifier.isEmpty();

			int num_busy_connections = 0;

			dataSource.getLoginTimeout();

			if (notDefaultUser)
				num_busy_connections = dataSource.getNumBusyConnections(tenantIdentifier, defaultPassword);
			else
				num_busy_connections = dataSource.getNumBusyConnectionsDefaultUser();

			if (num_busy_connections >= 0)
			{
				if (notDefaultUser)
					log.info("num_busy_connections " + tenantIdentifier + ": " + num_busy_connections);
				else
					log.info("num_busy_connections NULL - POD MASTER: " + num_busy_connections);

				log.info("num_busy_connections default schema: " + dataSource.getNumBusyConnectionsDefaultUser());
				log.info("num_busy_connections all schemas: " + dataSource.getNumBusyConnectionsAllUsers());

				if (notDefaultUser)
					log.info("num_idle_connections " + tenantIdentifier + ": " + dataSource.getNumIdleConnections(tenantIdentifier, defaultPassword));
				else
					log.info("num_idle_connections NULL - POD MASTER: " + dataSource.getNumIdleConnectionsDefaultUser());

				log.info("num_idle_connections default schema: " + dataSource.getNumIdleConnectionsDefaultUser());
				log.info("num_idle_connections all schemas: " + dataSource.getNumIdleConnectionsAllUsers());

				if (notDefaultUser)
					log.info("num_connections " + tenantIdentifier + ": " + dataSource.getNumConnections(tenantIdentifier, defaultPassword));
				else
					log.info("num_connections NULL - POD MASTER: " + dataSource.getNumConnectionsDefaultUser());

				log.info("num_connections default schema: " + dataSource.getNumConnectionsDefaultUser());
				log.info("num_connections all schemas: " + dataSource.getNumConnectionsAllUsers());

				log.info("max_num_connections per schema: " + dataSource.getMaxPoolSize());
			}

		} catch (SQLException e) {
			log.error("logCurrentStatus: ", e);
		}
	}

	public static DataSource getDataSource() { return dataSource; }
	
	public static String getDefaultJdbcURL()
	{
		return defaultJdbcURL;
	}
	
	public static String getPodMasterSchemaName()
	{
		return defaultUserName;
	}
	
	public static String getPodMasterPassword()
	{
		return defaultPassword;
	}
	
	public static String getPodMasterCode()
	{
		String result = "";
		if (defaultUserName != null)
		{
			if (defaultUserName.contains("_")){
				String temp = defaultUserName.substring(0, defaultUserName.indexOf("_"));
				result = result.concat(temp.substring(0, temp.length() >= 8 ? 8 : temp.length()));
			}		
			else{
				result = defaultUserName.substring(0, defaultUserName.length() >= 8 ? 8 : defaultUserName.length());
			}
		}

		if (isPostgres()) {
			return result.toLowerCase();
		}

		return result.toUpperCase();
	}

	public static boolean isPostgres() {
		return (defaultDriver != null) && defaultDriver.toLowerCase().contains("postgres");
	}
}
