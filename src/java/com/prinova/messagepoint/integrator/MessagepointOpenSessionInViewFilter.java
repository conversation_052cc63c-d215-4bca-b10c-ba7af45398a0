package com.prinova.messagepoint.integrator;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.prinova.messagepoint.model.admin.proxylogin.*;
import com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.orm.hibernate5.support.OpenSessionInViewFilter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyKeys.ApplicationServer;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;


public class MessagepointOpenSessionInViewFilter extends OpenSessionInViewFilter {

public static final String 	BRANCH_COOKIE_NAME = "branch";
public static final int 	BRANCH_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
public static final String 	NODE_COOKIE_NAME = "node";
public static final int 	NODE_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
public static final String 	USER_COOKIE_NAME = "username";
public static final int 	USER_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)

	@Override
	protected void doFilterInternal(
			HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {

		// Set multi-tenant DB schema
		// schema = null will use top root schema defined in JBoss configuration
		//
		String schema = null;
		
		String branchName = request.getParameter("company");
		String nodeName = request.getParameter("instance");
		String redirectionTypeStr = request.getParameter("prerelease");
		String signin_username = "";
		String signin_pwd = "";
		String targetUrl = null;
		boolean isOIDCAuth = request.getSession().getAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH) != null;
		boolean isIDPSignIn = false;
		String sourcePage = request.getParameter("sourcePage");
		if (isOIDCAuth && sourcePage != null && sourcePage.equals("idpsignin") && request.getRequestURI().contains("j_spring_security_check"))
			isIDPSignIn = true;

		if (branchName != null && !request.getRequestURI().contains("/api/"))
		{
			if (branchName.contains("-via-"))
			{
				branchName = branchName.replace("-via-", "#");
			}
			targetUrl = request.getRequestURI();
			if (request.getQueryString() != null)
				targetUrl = targetUrl + "?" + request.getQueryString();
			targetUrl = targetUrl.replace(request.getContextPath(), "");
			targetUrl = targetUrl.replaceAll("(&company=[^&]*|company=[^&]*?(&|$))", "");
			targetUrl = targetUrl.replaceAll("(&instance=[^&]*|instance=[^&]*?(&|$))", "");
			targetUrl = targetUrl.replaceAll("(&prerelease=[^&]*|prerelease=[^&]*?(&|$))", "");
			request.getSession().setAttribute("MESSAGEPOINT_SAVED_URL_REQUEST", targetUrl);
		}
		else
		{
			branchName = request.getParameter("j_branch");
			nodeName = request.getParameter("j_node");
			signin_username = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
			signin_pwd = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_PASSWORD_KEY);
		}
		
		//request.get
		Authentication auth = null;
		User user = null;
		
		String targetBranchName = null;

		if (branchName != null)
		{
			branchName = branchName.trim().toLowerCase();

			if (signin_username != null)
				signin_username = signin_username.trim().toLowerCase();
			else
				signin_username = "";

			String cookie_branch = branchName;
			String cookie_instance = "";
			if (nodeName != null)
				cookie_instance = nodeName;

			String cookie_username = signin_username;

			int index = branchName.indexOf('#');
			if (index != -1)
			{
				targetBranchName = branchName.substring(0, index);
				branchName = branchName.substring(index + 1);

				index = signin_username.indexOf('#');
				if (index != -1)
				{
					// domain#via_domain / user#home_domain => domain / user#home_domain
					//
					branchName = targetBranchName;
					targetBranchName = null;
				}
				else
				{
					if (!signin_username.isEmpty())
					{
						// domain#via_domain / user => domain / user#via_domain
						//
						signin_username = signin_username + "#" + branchName;
						branchName = targetBranchName;
						targetBranchName = null;
						request.setAttribute("mp_username", signin_username);
					}
				}
			}
			else
			{
				index = signin_username.indexOf('#');
				if ( index != -1)
				{
					String branchViaName = signin_username.substring(index + 1);
					if (branchViaName != null && branchViaName.equalsIgnoreCase(branchName))
					{
						signin_username = signin_username.substring(0, index);
						request.setAttribute("mp_username", signin_username);
					}
				}
			}

			// It's coming from signin.jsp page
			//
			// Let's find the default schema of the entered branch name. The user authentication will be processed with this default schema.
			//
			SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
			if (!branchName.isEmpty())
			{
				if (isIDPSignIn)
				{
					if (signin_username.isEmpty())
					{
						// IDP sign-in without username
						Domain domain = Domain.findByName(branchName);
						if (domain != null)
						{
							RedirectionInfo redirectionInfo = RedirectionInfo.findByDomain(domain);
							if (redirectionInfo != null && redirectionInfo.getProduction() != null)
							{
								DomainPod domainPod = DomainPod.findByDomainAndPod(domain, redirectionInfo.getProduction());
								if (domainPod != null && domainPod.isPingSSOEnabled() && domainPod.getSsoIdPId() != null)
								{
									String location = MessagepointOpenSessionInViewFilter.getSSOIdPLocation(domainPod.isPingOneSSOEnabled(), domainPod.getSsoIdPId(), "-idp", branchName, "prinova", SystemPropertyManager.getInstance());
									response.sendRedirect(location);
									HibernateUtil.getManager().restoreSession(mainSessionHolder);
									return;
								}
								else {
									String fromUrl = request.getRequestURI().replace("j_spring_security_check", "idpsignin.jsp");
									String toUrl = fromUrl + "?domain=" + branchName;
									response.sendRedirect(response.encodeRedirectURL(toUrl));
									HibernateUtil.getManager().restoreSession(mainSessionHolder);
									return;
								}
							}
							if (branchName.equals("prinova"))
							{
								Branch branch = Branch.findByName(branchName);
								if (branch != null && branch.isPingSSOEnabled() && branch.getSsoIdPId() != null)
								{
									String location = MessagepointOpenSessionInViewFilter.getSSOIdPLocation(branch.isPingOneSSOEnabled(), branch.getSsoIdPId(), "-idp", branchName, "prinova", SystemPropertyManager.getInstance());
									response.sendRedirect(location);
									HibernateUtil.getManager().restoreSession(mainSessionHolder);
									return;
								}
							}
							// redirect back with error about company not in production
							String fromUrl = request.getRequestURI().replace("j_spring_security_check", "idpsignin.jsp");
							String toUrl = fromUrl + "?msgkey=code.text.authenticationfailed.redirectproduction";
							response.sendRedirect(response.encodeRedirectURL(toUrl));
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}
						// redirect back with error about company not found
						String fromUrl = request.getRequestURI().replace("j_spring_security_check", "idpsignin.jsp");
						String toUrl = fromUrl + "?msgkey=code.text.authenticationfailed.companyidpconnect";
						response.sendRedirect(response.encodeRedirectURL(toUrl));
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
						return;
						// request.getSession().removeAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH);
					}
					else {
						user = UserUtil.signIn(signin_username + "#idp#" + branchName, signin_pwd, null, request, response);
						if (user == null) {
							String fromUrl = request.getRequestURI().replace("j_spring_security_check", "idpsignin.jsp");
							String toUrl = fromUrl + "?msgkey=code.text.authenticationfailed";
							response.sendRedirect(response.encodeRedirectURL(toUrl));
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}
						schema = user.getSchemaOfThisUser();
						String redirectURL = PINCOpenIdConnectUtil.completeOpenIdAuthFlow(request);
						if (redirectURL != null) {
							response.sendRedirect(redirectURL);
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}
						request.getSession().removeAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH);
					}
				}
				else {
					// In case of Master, redirect to Slave
					request.getSession().removeAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH);
					SystemPropertyManager spm = SystemPropertyManager.getInstance();
					Integer proxySSOPodRole = spm.getIntegerSystemProperty(SystemPropertyKeys.ProxySSO.KEY_ProxySSOPodRole, PodRole.POD_ROLE_SLAVE);
					if (proxySSOPodRole == PodRole.POD_ROLE_MASTER) {
						if (redirectionTypeStr == null)
							redirectionTypeStr = request.getParameter("j_redirectionType");
						int redirectionType = ProxyLoginManager.REDIRECTION_TYPE_PRODUCTION;
						if (redirectionTypeStr != null && redirectionTypeStr.equals("true")) {
							redirectionType = ProxyLoginManager.REDIRECTION_TYPE_TRANSITION;
						}
						ProxyLoginManager plm = ProxyLoginManager.getInstance();
						String error = plm.redirectSignin(request, response, branchName, targetBranchName, nodeName, signin_username, signin_pwd, redirectionType, false);
						if (error != null) {
							if (!error.isEmpty()) {
								String fromUrl = request.getRequestURI().replace("j_spring_security_check", "signin.jsp");
								String toUrl = fromUrl + "?msgkey=" + error;
								response.sendRedirect(response.encodeRedirectURL(toUrl));
							}
							HibernateUtil.getManager().restoreSession(mainSessionHolder);

							Cookie branchCookie = new Cookie(BRANCH_COOKIE_NAME, URLEncoder.encode(cookie_branch, "UTF-8"));
							Cookie nodeCookie = new Cookie(NODE_COOKIE_NAME, URLEncoder.encode(cookie_instance, "UTF-8"));
							Cookie userCookie = new Cookie(USER_COOKIE_NAME, URLEncoder.encode(cookie_username, "UTF-8"));

							String remember = request.getParameter("remembermeCheckbox");
							if (remember != null && remember.equals("true")) {
								branchCookie.setMaxAge(BRANCH_COOKIE_AGE);
								nodeCookie.setMaxAge(NODE_COOKIE_AGE);
								userCookie.setMaxAge(USER_COOKIE_AGE);
							} else {
								branchCookie.setMaxAge(0);
								nodeCookie.setMaxAge(0);
								userCookie.setMaxAge(0);
							}

							response.addCookie(branchCookie);
							response.addCookie(nodeCookie);
							response.addCookie(userCookie);
							return;
						}
					}

					if (nodeName != null && !nodeName.isEmpty()) {
						if (targetBranchName != null) {
							// check if the requested instance in the target domain exists
							// if not display invalid instance error, otherwise set the schema name to "via" domain

							schema = Node.getSchema(nodeName, targetBranchName);
							if (schema != null) {
								if (Node.isSchemaAccessible(schema))
									schema = Node.getDefaultSchema(branchName);
								else
									schema = null;
							}
						} else
							schema = Node.getSchema(nodeName, branchName);

						if (schema != null && !Node.isSchemaAccessible(schema)) {
							schema = null;
						}

						if (schema == null) {
							String fromUrl = request.getRequestURI().replace("j_spring_security_check", "signin.jsp");
							String toUrl = fromUrl + "?msgkey=code.text.company.node.invalid";
							response.sendRedirect(response.encodeRedirectURL(toUrl));
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}
					} else {
						schema = Node.getDefaultSchema(branchName);

						if (schema != null && !Node.isSchemaAccessible(schema)) {
							schema = null;
						}

						if (schema == null) {
							String fromUrl = request.getRequestURI().replace("j_spring_security_check", "signin.jsp");
							String toUrl = fromUrl + "?msgkey=code.text.authenticationfailed";
							response.sendRedirect(response.encodeRedirectURL(toUrl));
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}

						// if instance wasn't defined, try to find a default instance of the user
						if (!signin_username.isEmpty()) {
							SessionHolder localSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
							User localUser = User.findByUsername(signin_username, false);
							if (localUser != null && localUser.getDefaultNodeId() > 0) {
								Node defaultNode = Node.findById(localUser.getDefaultNodeId());
								if (defaultNode != null && defaultNode.isOnline())
									schema = defaultNode.getSchemaName();
							}
							HibernateUtil.getManager().restoreSession(localSessionHolder);
						}
					}

					Branch branch = Node.getBranch(schema);
					if (branch != null && branch.isPingSSOEnabled() && branch.getSsoIdPId() != null) {
						User localUser = null;
						if (!signin_username.isEmpty()) {
							SessionHolder localSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);
							localUser = User.findByUsername(signin_username, false);
							HibernateUtil.getManager().restoreSession(localSessionHolder);
						}

						if (localUser != null) {
							UserUtil.signIn(localUser, schema, request, response);
						} else {
							index = signin_username.indexOf('#');
							if (index != -1) {
								String fromUrl = request.getRequestURI().replace("j_spring_security_check", "signin.jsp");
								String toUrl = fromUrl + "?msgkey=code.text.authenticationfailed";
								response.sendRedirect(response.encodeRedirectURL(toUrl));
								HibernateUtil.getManager().restoreSession(mainSessionHolder);
								return;
							}

							String location = MessagepointOpenSessionInViewFilter.getSSOIdPLocation(branch.isPingOneSSOEnabled(), branch.getSsoIdPId(), nodeName, targetBranchName, branchName, spm);


							Cookie branchCookie = new Cookie(BRANCH_COOKIE_NAME, URLEncoder.encode(cookie_branch, "UTF-8"));
							Cookie nodeCookie = new Cookie(NODE_COOKIE_NAME, URLEncoder.encode(cookie_instance, "UTF-8"));
							Cookie userCookie = new Cookie(USER_COOKIE_NAME, URLEncoder.encode(cookie_username, "UTF-8"));

							String remember = request.getParameter("remembermeCheckbox");
							if (remember != null && remember.equals("true")) {
								branchCookie.setMaxAge(BRANCH_COOKIE_AGE);
								nodeCookie.setMaxAge(NODE_COOKIE_AGE);
								userCookie.setMaxAge(USER_COOKIE_AGE);
							} else {
								branchCookie.setMaxAge(0);
								nodeCookie.setMaxAge(0);
								userCookie.setMaxAge(0);
							}

							response.addCookie(branchCookie);
							response.addCookie(nodeCookie);
							response.addCookie(userCookie);

							response.sendRedirect(location);
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							return;
						}
					}
				}
			}
			else
			{
				String fromUrl = request.getRequestURI().replace("j_spring_security_check", isIDPSignIn?"idpsignin.jsp":"signin.jsp");
				String toUrl = fromUrl + (isIDPSignIn?"?msgkey=code.text.authenticationfailed.companyidpconnect":"?msgkey=code.text.authenticationfailed");
				response.sendRedirect(response.encodeRedirectURL(toUrl));
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
				return;
			}

			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
		else
		{
			HttpSession httpSession = request.getSession();
			SecurityContext context = (SecurityContext) httpSession.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
			if(context != null){
				auth = context.getAuthentication();
				if (auth != null) {
					Object authorizedUser = auth.getPrincipal();
					if (authorizedUser instanceof User) {
						user = (User) authorizedUser;
						schema = user.getSchemaOfThisUser();
					}
				}
			}
		}
		
		if (logger.isDebugEnabled())
			logger.debug("Start OpenSessionInViewFilter with schema = " + schema + " for url = " + request.getRequestURL());
		
		MessagepointCurrentTenantIdentifierResolver.setTenantIdentifier(schema);
		
		super.doFilterInternal(request, response, filterChain);
		
		if (logger.isDebugEnabled())
			logger.debug("End OpenSessionInViewFilter with schema = " + schema + " for url = " + request.getRequestURL());
	}

	public static String getSSOIdPLocation(boolean isPingOneSSOEnabled, String ssoIdPId, String nodeName, String targetBranchName, String branchName, SystemPropertyManager spm) throws UnsupportedEncodingException {

		String location = null;

		if (isPingOneSSOEnabled)
		{
			String saasId = URLEncoder.encode(spm.getSystemProperty(SystemPropertyKeys.SSO.KEY_SSOPingSaasId), "UTF-8");
			String idpId = URLEncoder.encode(ssoIdPId, "UTF-8");
			location = String.format(spm.getSystemProperty(SystemPropertyKeys.SSO.KEY_SSOPingInitSsoUrl), saasId, idpId);
		}
		else if (StringUtils.isNotEmpty(ssoIdPId))
		{
			String adapterId = spm.getSystemProperty(SystemPropertyKeys.SSO.KEY_SSOPingAdapterId);
			String startURL = spm.getSystemProperty(SystemPropertyKeys.SSO.KEY_SSOPingStartSsoUrl) + "?PartnerIdpId=%s";
			String targetResource = "https://sso-sp";
			if (nodeName != null && !nodeName.isEmpty())
				targetResource += nodeName;
			targetResource += ".";
			if (targetBranchName != null && !targetBranchName.isEmpty())
				targetResource += targetBranchName + "-via-";
			targetResource += branchName + "." + spm.getPodMasterSystemProperty(ApplicationServer.KEY_ApplicationServerNameSpace);
			if (adapterId != null && !adapterId.isEmpty())
			{
				startURL += "&SpSessionAuthnAdapterId=%s&TargetResource=%s";
				location = String.format(startURL, URLEncoder.encode(ssoIdPId, "UTF-8"), URLEncoder.encode(adapterId, "UTF-8"), URLEncoder.encode(targetResource, "UTF-8"));
			}
			else
			{
				startURL += "&TargetResource=%s";
				location = String.format(startURL, URLEncoder.encode(ssoIdPId, "UTF-8"), URLEncoder.encode(targetResource, "UTF-8"));
			}
		}

		return location;
	}

}
