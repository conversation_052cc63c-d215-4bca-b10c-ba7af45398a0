package com.prinova.messagepoint.integrator;

import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

public class MessagepointSessionHolder extends SessionHolder {
    String previousTenantIdentifier;
	public MessagepointSessionHolder(Session session, String previousTenantIdentifier) {
		super(session);
        this.previousTenantIdentifier = previousTenantIdentifier;
	}
    public String getPreviousTenantIdentifier() {
        return previousTenantIdentifier;
    }
}
