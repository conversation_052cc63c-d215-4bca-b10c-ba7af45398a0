package com.prinova.messagepoint.integrator;

import org.hibernate.context.spi.CurrentTenantIdentifierResolver;

import com.prinova.messagepoint.model.security.User;

/**
 * 
 * Multi-Tenant Identifier (DB Schema Name)
 * 
 * A callback registered with the {@link org.hibernate.SessionFactory} that is responsible for resolving the
 * current tenant identifier for use with CurrentSessionContext and
 * {@link org.hibernate.SessionFactory#getCurrentSession()}
 * 
 */
public class MessagepointCurrentTenantIdentifierResolver implements CurrentTenantIdentifierResolver {
	
	private static ThreadLocal<String> threadLocalSchemaName = new ThreadLocal<>();
	private static ThreadLocal<String> threadLocalSchemaSave = new ThreadLocal<>();
	private static ThreadLocal<User>   threadLocalUser = new ThreadLocal<>();
	
	public static void setUser(User user) {
		threadLocalUser.set(user);
	}
	
	public static User getUser() {
		return threadLocalUser.get();
	}
	
	public static void setTenantIdentifier(String tenantIdentifier) {
		if (tenantIdentifier == null || tenantIdentifier.isEmpty())
		{
			threadLocalSchemaName.set(null);
		}
		else
		{
			threadLocalSchemaName.set(tenantIdentifier);
		}
	}
	
	public static String getTenantIdentifier() {
		return threadLocalSchemaName.get();
	}

	public static String getTenantIdentifierWithPodMasterTranslation() {
		String tenantIdentifier = threadLocalSchemaName.get();

		// return pod master schema if tenantIdentifier is not set
		if (tenantIdentifier == null)
			return MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName();

		return tenantIdentifier;
	}

	public static void setSaveIdentifier(String saveIdentifier) {
		if (saveIdentifier == null || saveIdentifier.isEmpty())
		{
			threadLocalSchemaSave.set(null);
		}
		else
		{
			threadLocalSchemaSave.set(saveIdentifier);
		}
	}
	
	public static String getSaveIdentifier() {
		return threadLocalSchemaSave.get();
	}
	
	public static boolean isPodMasterNode() {
		return (threadLocalSchemaName.get() == null);
	}
	
	/**
	 * Resolve the current tenant identifier.
	 * 
	 * @return The current tenant identifier
	 */
	@Override
	public String resolveCurrentTenantIdentifier() {
		String tenantIdentifier = threadLocalSchemaName.get();
		
		// return "" to get default schema if tenantIdentifier is not set
		if (tenantIdentifier == null)
			return "";
		
		return tenantIdentifier;
	}

	/**
	 * Should we validate that the tenant identifier on "current sessions" that already exist when
	 * CurrentSessionContext#currentSession() is called matches the value returned here from
	 * {@link #resolveCurrentTenantIdentifier()}?
	 * 
	 * @return {@code true} indicates that the extra validation will be performed; {@code false} indicates it will not.
	 *
	 * @see org.hibernate.context.TenantIdentifierMismatchException
	 */
	@Override
	public boolean validateExistingCurrentSessions() {
		String tenantIdentifier = threadLocalSchemaName.get();
		
		if (tenantIdentifier != null) {
			return true;
		} else {
			return false;
		}		
	}

}
