package com.prinova.messagepoint.integrator;

import java.sql.Connection;
import java.sql.SQLException;

import org.quartz.utils.ConnectionProvider;

/**
 * 
 * Database for Quartz
 * 
 */

public class MessagepointQuartzConnectionProvider implements ConnectionProvider {

    public Connection getConnection() throws SQLException {
        return MessagepointMultiTenantConnectionProvider.getDataSource().getConnection();
    }
    
    public void shutdown() throws SQLException {
    	// do nothing
    }    

    public void initialize() throws SQLException {
        // do nothing, already initialized in MessagepointMultiTenantConnectionProvider
    }

}
