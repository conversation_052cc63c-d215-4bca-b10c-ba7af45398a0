package com.prinova.messagepoint.integrator;

import com.prinova.messagepoint.util.LogUtil;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.quartz.SchedulerContext;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.text.MessageFormat;

/**
 * Needed to set Quartz useProperties=true when using Spring classes,
 * because Spring sets an object reference on JobDataMap that is not a String
 * 
 * @see http://site.trimplement.com/using-spring-and-quartz-with-jobstore-properties/
 * @see http://forum.springsource.org/showthread.php?130984-Quartz-error-IOException
 * @see https://stackoverflow.com/questions/28590129/upgrading-to-spring-4-1-breaks-quartz-persistence-due-to-removed-jobdetailawaret
 */

@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class MessagepointQuartzJobBean extends QuartzJobBean
{
    public static final String JOB_DETAIL_KEY = "jobDetail";
 
    private String batchProcessorName;
 
    public String getBatchProcessorName() {
        return batchProcessorName;
    }
 
    public void setBatchProcessorName(String name) {
        this.batchProcessorName = name;
    }
 
    protected void executeInternal(JobExecutionContext jobCtx) throws JobExecutionException
    {
        try {
            SchedulerContext schedCtx = jobCtx.getScheduler().getContext();
 
            ApplicationContext appCtx =
                (ApplicationContext) schedCtx.get("applicationContext");
            java.lang.Runnable proc = (java.lang.Runnable) appCtx.getBean(batchProcessorName);
            proc.run();
        }
        catch (Exception ex) {
            LogUtil.getLog(MessagepointQuartzJobBean.class).error(MessageFormat.format("MessagepointQuartzJobBean[{0}] Exception:", batchProcessorName), ex);
            throw new JobExecutionException("Unable to execute batch job: " + batchProcessorName, ex);
        }
        catch (Error er) {
            LogUtil.getLog(MessagepointQuartzJobBean.class).error(MessageFormat.format("MessagepointQuartzJobBean[{0}] Error:", batchProcessorName), er);
            throw er;
        }
    }
}

