package com.prinova.messagepoint.integrator;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.boot.Metadata;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.integrator.spi.Integrator;
import org.hibernate.service.spi.SessionFactoryServiceRegistry;

import com.prinova.messagepoint.model.interceptor.delete.DataFileDeleteListener;
import com.prinova.messagepoint.model.interceptor.delete.ProductionEventDeleteListener;

/**
 * 
 * Registering additional event listeners for Spring 3.2.5 to Hibernate 4.2.7
 * 
 */
public class MessagepointSpringHibernateIntegrator implements Integrator {
	private static final Log log = LogUtil.getLog(MessagepointSpringHibernateIntegrator.class);


	@Override
	public void integrate(Metadata metadata, SessionFactoryImplementor sessionFactory, SessionFactoryServiceRegistry serviceRegistry) {

		//String messagepointDataSource = configuration.getProperty("messagepoint.data.source");
		String messagepointDataSource = (String) sessionFactory.getProperties().get("messagepoint.data.source"); //(String) sessionFactory.getCurrentSession().getProperties().get("messagepoint.data.source");

		// register listeners only for messagepoint data source (not for report)
		//
		if (messagepointDataSource != null && messagepointDataSource.equalsIgnoreCase("messagepoint"))
		{
			log.info("Start listeners registration for " + messagepointDataSource);

			EventListenerRegistry listenerRegistry = serviceRegistry.getService(EventListenerRegistry.class);

			listenerRegistry.appendListeners(EventType.DELETE, new DataFileDeleteListener());
			listenerRegistry.appendListeners(EventType.DELETE, new ProductionEventDeleteListener());

			log.info("End listeners registration.");
		}
	}

	/**
     * Tongue-in-cheek name for a shutdown callback.
     *
     * @param sessionFactory The session factory being closed.
     * @param serviceRegistry That session factory's service registry
     */
	@Override
	public void disintegrate(SessionFactoryImplementor sessionFactory, SessionFactoryServiceRegistry serviceRegistry) 
	{		
	}

}
