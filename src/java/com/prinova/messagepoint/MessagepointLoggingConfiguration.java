package com.prinova.messagepoint;

import com.prinova.messagepoint.util.PropertyUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.ConfigurationFactory;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilder;
import org.apache.logging.log4j.core.config.builder.impl.BuiltConfiguration;
import org.apache.logging.log4j.core.config.properties.PropertiesConfigurationBuilder;
import org.apache.logging.log4j.core.config.xml.XmlConfiguration;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.text.MessageFormat;

public class MessagepointLoggingConfiguration extends ConfigurationFactory {



    @Override
    protected String[] getSupportedTypes() {
        return new String[] {".xml", "*"};
    }

    @Override
    public Configuration getConfiguration(LoggerContext loggerContext, String name, URI configLocation, ClassLoader loader) {
        return this.getCustomXMLConfig(loggerContext);
    }

    @Override
    public Configuration getConfiguration(LoggerContext loggerContext, ConfigurationSource source) {
        return this.getCustomXMLConfig(loggerContext);
    }

    @Override
    public Configuration getConfiguration(LoggerContext loggerContext, String name, URI configLocation) {
        return this.getCustomXMLConfig(loggerContext);
    }

    private XmlConfiguration getCustomXMLConfig(LoggerContext loggerContext) {

        InputStream stream = null;
        String configFileSource = "";
        ConfigurationSource configSource = null;

        try {

            if (PropertyUtils.getRuntimeProperties().containsKey("log4j.xml")) {
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                PropertyUtils.getRuntimeProperties().get("log4j.properties").store(output, StringUtils.EMPTY);
                stream = new ByteArrayInputStream(output.toByteArray());
                configFileSource = "ExternalRuntimeProperties";
            } else {
                URL internalPropertiesConfig = getClass().getClassLoader().getResource("log4j.xml");
                if (internalPropertiesConfig != null) {
                    stream = internalPropertiesConfig.openStream();
                }
                configFileSource = "DefaultInternalProperties";
            }
            if (stream != null) {
                configSource = new ConfigurationSource(stream);
            }
        } catch (Exception e) {
            System.out.print(e);
        }

        XmlConfiguration config = new XmlConfiguration(loggerContext, configSource);
        config.setName(configFileSource);

        return config;
    }

    private Configuration getCustomConfig() {
        try {

            InputStream stream = null;
            String configFileSource;

            if (PropertyUtils.getRuntimeProperties().containsKey("log4j.xml")) {
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                PropertyUtils.getRuntimeProperties().get("log4j.properties").store(output, StringUtils.EMPTY);
                stream = new ByteArrayInputStream(output.toByteArray());
                configFileSource = "ExternalRuntimeProperties";
            } else {
                URL internalPropertiesConfig = getClass().getClassLoader().getResource("log4j.xml");
                if (internalPropertiesConfig != null) {
                    stream = internalPropertiesConfig.openStream();
                }
                configFileSource = "DefaultInternalProperties";
            }
            if (stream != null) {
                ConfigurationBuilder<BuiltConfiguration> builder = PropertiesConfigurationBuilder.newConfigurationBuilder();
                ConfigurationSource configSource = new ConfigurationSource(stream);
                builder.setConfigurationSource(configSource);

                BuiltConfiguration config = builder.build();
                config.setName(MessageFormat.format("{0}-via-{1}", MessagepointLoggingConfiguration.class.getSimpleName(), configFileSource));

                return config;

            }
        } catch (Exception e) {
            System.out.print(e);
        }

        return null;
    }
}

