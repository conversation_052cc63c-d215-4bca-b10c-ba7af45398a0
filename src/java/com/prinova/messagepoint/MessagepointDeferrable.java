package com.prinova.messagepoint;

import com.prinova.messagepoint.util.LogUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;

/**
 * MessagepointDeferable provides support for deferring an action until a HTTPServletRequest has been fully serviced. This
 * class is integrated with MessagepointStartFilter and AnalyticsEventEndFilter to set a request context and run deferred actions
 * once a request is completed. In cases where an action is deferred on a non HttpServletRequest thread, such as in a background service,
 * the action will be run immediately.
 */
public class MessagepointDeferrable {

    private static final ThreadLocal<String> requestIdentifier = ThreadLocal.withInitial(() -> null);
    private static final ThreadLocal<List<Runnable>> deferrableActions = ThreadLocal.withInitial(() -> null);

    public static boolean getHasRequestIdentifier() {
        return MessagepointDeferrable.requestIdentifier.get() != null;
    }

    public static void setRequestIdentifier(String requestIdentifier) {
        MessagepointDeferrable.requestIdentifier.set(requestIdentifier);
    }

    public static void add(MessagePointRunnable runnable) {
        add(() -> {
            try {
                runnable.performMainProcessing();
            } catch (Exception e) {
                LogUtil.getLog(MessagepointDeferrable.class).error("Error", e);
            }
        });
    }

    public static void add(Runnable action) {

        if (!getHasRequestIdentifier()) {
            action.run();
        }

        if (deferrableActions.get() == null) {
            deferrableActions.set(new ArrayList<>());
        }

        deferrableActions.get().add(action);
    }

    public static void runAll() {
        if (deferrableActions.get() == null) {
            return;
        }

        Stack<Runnable> actions = new Stack<>();
        actions.addAll(deferrableActions.get());

        deferrableActions.set(null);

        while (!actions.isEmpty()) {
            Runnable next = actions.pop();
            next.run();
        }
    }
}
