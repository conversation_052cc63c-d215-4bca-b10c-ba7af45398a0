package com.prinova.messagepoint;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.hibernate.Session;


public class ExceptionEvent {

	private Throwable exception;

	private boolean throwException   = true;
	
	private boolean overrideHandlers = false;
	
	private Set<ExceptionHandler> handlers = new HashSet<>();
	private Map<String, Object>   params   = new HashMap<>();
	
	public Map<String, Object> getParams() {
		return params;
	}

	public void setParams(Map<String, Object> params) {
		this.params = params;
	}

	public boolean isThrowException() {
		return throwException;
	}

	public void setThrowException(boolean throwException) {
		this.throwException = throwException;
	}

	public Throwable getException() {
		return exception;
	}

	public void setException(Throwable exception) {
		this.exception = exception;
	}

	public boolean isOverrideHandlers() {
		return overrideHandlers;
	}

	public void setOverrideHandlers(boolean overrideHandlers) {
		this.overrideHandlers = overrideHandlers;
	}

	public Set<ExceptionHandler> getHandlers() {
		return handlers;
	}

	public void setHandlers(Set<ExceptionHandler> handlers) {
		this.handlers = handlers;
	}

	public static ExceptionEvent createDefault(Throwable t, Object...parameters){
		ExceptionEvent e = new ExceptionEvent();
		
		e.setException(t);
		e.setThrowException(true);
		
		if(parameters != null){
			e.setParams(createParameters(parameters));
		}
		
		return e;
	}
	
	@SuppressWarnings("unchecked")
	public static HashMap<String, Object> createParameters(Object...objects){
		HashMap<String, Object> prop = new HashMap<>();
		
		
		//handle a user map passed.
		if(objects != null && objects.length==1 && Map.class.isInstance(objects[0])){
			Map<String, Object> paramMap = (Map<String, Object>)objects[0];
			prop.putAll(paramMap);
			return prop;
		}
		
		//For all other cases we will try to infer the key based on the type.
		for (Object object : objects) {
			prop.put(getKey(object), object);
		}
		
		return prop;
	}
	
	
	private static String getKey(Object obj){
		if(Session.class.isInstance(obj)){
			return KEY_HIBERNATE_SESSION;
			
		}else if(Throwable.class.isInstance(obj)){
			return KEY_EXCEPTION;
			
		}else if(Log.class.isInstance(obj)){
			return KEY_LOG;
			
		}else if(String.class.isInstance(obj) ){
			return KEY_USER_ERROR_MESSAGE;
			
		}else if(HttpServletRequest.class.isInstance(obj)){
			return KEY_HTTP_REQUEST;
			
		}else if(HttpServletResponse.class.isInstance(obj)){
			return KEY_HTTP_RESPONSE;
			
		}else if(HttpSession.class.isInstance(obj)){
			return KEY_HTTP_SESSION;
		}
		
		return (obj != null)? obj.getClass().getName() : "null";
	}

	public static final String KEY_HIBERNATE_SESSION = "hibernate.session";
	public static final String KEY_EXCEPTION = "exception";
	public static final String KEY_LOG = "log";
	public static final String KEY_USER_ERROR_MESSAGE = "user.error.message";
	public static final String KEY_HTTP_REQUEST = "http.request";
	public static final String KEY_HTTP_RESPONSE = "http.response";
	public static final String KEY_HTTP_SESSION = "http.session";
	
	public static final int PRIORITY_HIGHEST = 0;
	public static final int PRIORITY_LOWEST  = 10;

}
