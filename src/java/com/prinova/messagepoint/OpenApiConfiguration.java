package com.prinova.messagepoint;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springdoc.core.customizers.OpenApiCustomiser;


@Configuration
public class OpenApiConfiguration {
    @Autowired
    private SwaggerUiConfigParameters swaggerUiConfigParameters;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Messagepoint REST API Documentation")
                        .version("1.0")
                        .description("Messagepoint REST API allows you to search and manage Messagepoint business models like Touchpoints, Data Variables and Data Elements, Task and so on.")
                        .contact(new Contact()
                                .name("Messagepoint")
                                .url("https://messagepoint.com")
                                .email("<EMAIL>")));
    }

    @Bean
    public OpenApiCustomiser sortTagsAndPathsCustomizer() {
        return openApi -> {
            openApi.setTags(openApi.getTags().stream()
                    .sorted((t1, t2) -> t1.getName().compareToIgnoreCase(t2.getName()))
                    .toList());
        };
    }

    @Bean
    public SwaggerUiConfigParameters springDocConfigProperties() {
        swaggerUiConfigParameters.setConfigUrl("/v3/api-docs");
        return swaggerUiConfigParameters;
    }
}
