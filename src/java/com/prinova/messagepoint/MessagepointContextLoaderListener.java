package com.prinova.messagepoint;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.ConfigUtils;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.RedisUtil;
import org.springframework.web.context.ContextLoaderListener;

import javax.servlet.ServletContextEvent;

public class MessagepointContextLoaderListener extends ContextLoaderListener {

    @Override
    public void contextInitialized(ServletContextEvent event) {

        try {
            ConfigUtils.loadLog4JConfig();
        } catch (Exception e) {
            LogUtil.getLog(MessagepointContextLoaderListener.class).error("Error", e);
        }

        super.contextInitialized(event);

        // have to wait until applicationContext is available
        try {
            RedisUtil.getRedisContext().subscribe();
        } catch (Exception e) {
            LogUtil.getLog(MessagepointContextLoaderListener.class).error("Error", e);
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent event) {

        try {
            for (Node node : Node.getAllOnlineAndOfflineNodes()) {
                MessagePointStartUp.shutdownSchema(node);
            }
        } catch (Exception e) {
            LogUtil.getLog(MessagepointContextLoaderListener.class).error(e);
        }

        super.contextDestroyed(event);

    }
}
