package com.prinova.messagepoint;

import java.io.FileInputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.dom4j.Document;
import org.dom4j.Node;

import com.prinova.licence.common.Constants;
import com.prinova.licence.common.XmlUtils;

public class MessagepointLicenceToken {
	
	private String 		id;
	private String 		licenceType;
	private Date 		licenceExpiryDate;
	private Date 		licenceStartDate;
	private Date 		licenceCreationDate;
	
	private String 		applicationName;
	private String 		version;
	
	private String 		CustomerName;
	private String 		serverName;
	private String[] 	serverIps;
	private boolean 	simulationFlag;
	
	private boolean 	printChannelsAuthorized = false;
	private boolean 	emailChannelsAuthorized = false;
	private boolean 	webChannelsAuthorized = false;
	private boolean 	smsChannelsAuthorized = false;

	private String 		environmentType;
	
	public MessagepointLicenceToken() {
		try {
			FileInputStream in = new FileInputStream( System.getProperty(Constants.KEY_SYS_PROP_LICENCE_FILE) );
			Document d = XmlUtils.readDocument(in);
			in.close();
			
			setId( getStringValue(d, Constants.XP_ROOT_ID));
			setLicenceType( getStringValue(d, Constants.XP_Type));
			SimpleDateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT);
			setLicenceCreationDate( getDate(d, Constants.XP_CreationDate, df) );
			setLicenceStartDate   ( getDate(d, Constants.XP_StartDate   , df) );
			setLicenceExpiryDate  ( getDate(d, Constants.XP_EndDate     , df) );
			
			setApplicationName( getStringValue(d, Constants.XP_Application_Name) );
			setVersion(
					getStringValue(d, Constants.XP_Application_VersionMajor) + 
					"." + 
					getStringValue(d, Constants.XP_Application_VersionMinor));
			
			setCustomerName   ( getStringValue(d, Constants.XP_Customer_Name) );
			setServerName     ( getStringValue(d, Constants.XP_Customer_Servers_ServerInfo_Name) );
			setEnvironmentType( getStringValue(d, Constants.XP_Type));
			
			List<Node>  ipElements = d.selectNodes(Constants.XP_Customer_Servers_ServerInfo_IPs);
			if(ipElements != null && !ipElements.isEmpty()){
				//for now only read the first one.
				setServerIps(ipElements.get(0).getText().split("\\s"));
			}else{
				setServerIps( new String[0]);
			}
			
			String printChannelsAuthorized = getStringValue(d, Constants.XP_AuthorizedForPrintChannels);
			if (printChannelsAuthorized != null && !printChannelsAuthorized.trim().isEmpty()) {
				setPrintChannelsAuthorized(Boolean.valueOf(printChannelsAuthorized));	
			}
			
			String emailChannelsAuthorized = getStringValue(d, Constants.XP_AuthorizedForEmailChannels);
			if (emailChannelsAuthorized != null && !emailChannelsAuthorized.trim().isEmpty()) {
				setEmailChannelsAuthorized(Boolean.valueOf(emailChannelsAuthorized));	
			}

			String smsChannelsAuthorized = getStringValue(d, Constants.XP_AuthorizedForSMSChannels);
			if (smsChannelsAuthorized != null && !smsChannelsAuthorized.trim().isEmpty()) {
				setSmsChannelsAuthorized(Boolean.valueOf(smsChannelsAuthorized));	
			}
			
			String webChannelsAuthorized = getStringValue(d, Constants.XP_AuthorizedForWebChannels);
			if (webChannelsAuthorized != null && !webChannelsAuthorized.trim().isEmpty()) {
				setWebChannelsAuthorized(Boolean.valueOf(webChannelsAuthorized));	
			}
			
		} catch (Exception e) {
			throw new RuntimeException("Could not create the application token from the licence file.", e);
		}
	
	}

	public final String getId() {
		return id;
	}
	private final void setId(String id) {
		this.id = id;
	}

	public final String getLicenceType() {
		return licenceType;
	}
	private final void setLicenceType(String licenceType) {
		this.licenceType = licenceType;
	}

	public final Date getLicenceExpiryDate() {
		return licenceExpiryDate;
	}
	private final void setLicenceExpiryDate(Date licenceExpiryDate) {
		this.licenceExpiryDate = licenceExpiryDate;
	}

	public final Date getLicenceStartDate() {
		return licenceStartDate;
	}
	private final void setLicenceStartDate(Date licenceStartDate) {
		this.licenceStartDate = licenceStartDate;
	}

	public final Date getLicenceCreationDate() {
		return licenceCreationDate;
	}
	private final void setLicenceCreationDate(Date licenceCreationDate) {
		this.licenceCreationDate = licenceCreationDate;
	}

	public final String getApplicationName() {
		return applicationName;
	}
	private final void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public final String getCustomerName() {
		return CustomerName;
	}
	private final void setCustomerName(String customerName) {
		CustomerName = customerName;
	}

	public final String getServerName() {
		return serverName;
	}
	private final void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public final String[] getServerIps() {
		return serverIps;
	}
	private final void setServerIps(String[] serverIps) {
		this.serverIps = serverIps;
	}
	
	private final String getStringValue(Document d, String xpath){
		Node n = d.selectSingleNode(xpath);
		return (n == null)?null: n.getText();
	}
	
	private  final Date getDate(Document d, String xpath, SimpleDateFormat df) throws ParseException{
		String value = getStringValue(d, xpath);
		if(value == null){ return null; }
		return df.parse(value);
	}

	public final String getVersion() {
		return version;
	}
	private final void setVersion(String version) {
		this.version = version;
	}	
	
	public String getBuildDate() {
		return MessagePointStartUp.getBuildDate();
	}

	public String getEnvironmentType() {
		return environmentType;
	}
	private void setEnvironmentType(String environmentType) {
		this.environmentType = environmentType;
	}
	
	public String getBuildRevision(){
		return MessagePointStartUp.getBuildRevision();
	}

	public boolean isSimulationFlag() {
		return simulationFlag;
	}
	public void setSimulationFlag(boolean simulationFlag) {
		this.simulationFlag = simulationFlag;
	}

	public boolean isEmailChannelsAuthorized() {
		return emailChannelsAuthorized;
	}
	public void setEmailChannelsAuthorized(boolean emailChannelsAuthorized) {
		this.emailChannelsAuthorized = emailChannelsAuthorized;
	}

	public boolean isWebChannelsAuthorized() {
		return webChannelsAuthorized;
	}
	public void setWebChannelsAuthorized(boolean webChannelsAuthorized) {
		this.webChannelsAuthorized = webChannelsAuthorized;
	}

	public boolean isSmsChannelsAuthorized() {
		return smsChannelsAuthorized;
	}
	public void setSmsChannelsAuthorized(boolean smsChannelsAuthorized) {
		this.smsChannelsAuthorized = smsChannelsAuthorized;
	}

	public boolean isPrintChannelsAuthorized() {
		return printChannelsAuthorized;
	}
	public void setPrintChannelsAuthorized(boolean printChannelsAuthorized) {
		this.printChannelsAuthorized = printChannelsAuthorized;
	}

}