package com.prinova.messagepoint.exceptionhandler;

import java.lang.reflect.Proxy;

import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.Transaction;

import com.prinova.messagepoint.ExceptionEvent;
import com.prinova.messagepoint.ExceptionHandler;

/**
 * Rolls back a hibernate transaction.
 */
public class RollbackHibernateExceptionHandler implements ExceptionHandler {

	
	/**
	 * Rollsback the transaction if active.  If there is an exception processing
	 * the hibenrate rollback, the excption is returned.
	 */
	public Object handle(ExceptionEvent event) {
		rollback( (Session) event.getParams().get(ExceptionEvent.KEY_HIBERNATE_SESSION) );
		return true;
	}

	public boolean canHandle(ExceptionEvent event) {
		
		if(HibernateException.class.isInstance(event.getException())){
			return true;
		}
		
		return false;
	}
	
	
	public int getPriority() {
		return ExceptionEvent.PRIORITY_LOWEST;
	}
	
	
	/**
	 * Rolls back the transaction.
	 * @param s A {@link Session} object
	 */
	private void rollback(Session s){
		if(s == null){ return; }
		Transaction t = s.getTransaction();
		
		if(Proxy.isProxyClass(t.getClass())){
			return;
		}
		
		if(t != null && t.isActive()){
			t.rollback();
		}
	}
	
}
