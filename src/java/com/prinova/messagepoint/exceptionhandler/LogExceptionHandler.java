package com.prinova.messagepoint.exceptionhandler;

import java.util.Map;

import org.apache.commons.logging.Log;
import org.hibernate.HibernateException;

import com.prinova.messagepoint.ExceptionEvent;
import com.prinova.messagepoint.ExceptionHandler;
import com.prinova.messagepoint.util.LogUtil;

/**
 * A log exception handler will attempt to log
 * the exception being processed.  
 * 
 * The appropriate {@link Log} object can be passed in to 
 * the exception handler.  In the absence of the {@link Log}
 * object, the exception handler will use its own {@link Log}
 * object.
 * 
 * This exception handler is always on.
 *
 */
public class LogExceptionHandler implements ExceptionHandler {

	
	public boolean canHandle(ExceptionEvent event) {
		return true; //always on. 
	}

	public Object handle(ExceptionEvent event) {
		Log log = getLog(event.getParams());
		String message = (String)  event.getParams().get(ExceptionEvent.KEY_USER_ERROR_MESSAGE);

		if(log.isErrorEnabled()){
			log.error(message, getRealException(event.getException()));
		}

		return true;
	}

	private Log getLog(Map<String, Object> params){
		Log log = (Log) params.get(ExceptionEvent.KEY_LOG);
		return (log != null)? log : LogUtil.getLog(LogExceptionHandler.class);
	}
	
	
	private Throwable getRealException(Throwable t){
		if(t == null){ return null; }

		if(HibernateException.class.isInstance(t)){
			HibernateException he = (HibernateException)t;
			
			//Sometimes the hibernate exceptions have the wrong cause populated.
			Throwable cause = (he.getCause() == null)?t: he.getCause();
			return cause;
		}
		
		return t;
	}

	public int getPriority() {
		return ExceptionEvent.PRIORITY_HIGHEST;
	}
}
