package com.prinova.messagepoint.exceptionhandler;

import org.springframework.web.servlet.ModelAndView;

import com.prinova.messagepoint.ExceptionEvent;
import com.prinova.messagepoint.ExceptionHandler;

/**
 * Exception handler for the presentation layer.
 */
public class SpringMvcExceptionHandler implements ExceptionHandler{

	public boolean canHandle(ExceptionEvent event) {
		return event.getParams().containsKey(ExceptionEvent.KEY_HTTP_REQUEST);
	}

	public int getPriority() {
		return ExceptionEvent.PRIORITY_HIGHEST/2;
	}

	public Object handle(ExceptionEvent event) {
		return getGlobalErroView((event!=null)? event.getException(): null);
	}

	
	public static ModelAndView getGlobalErroView(Throwable t){
		String errorPageName = "error";
		ModelAndView modelAndView = new ModelAndView(errorPageName,"exception", t);
		return modelAndView;
	}
}
