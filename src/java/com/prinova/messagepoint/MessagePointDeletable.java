package com.prinova.messagepoint;

import java.util.List;

import org.hibernate.Session;

public interface MessagePointDeletable {
	
	/**
	 * Will be executed before {@link Session#delete(Object)} call.
	 */
	public void beforeDelete();
	
	
	/**
	 * Will be executed after {@link Session#delete(Object)} call.
	 * 
	 * There are no guarantees that the Transaction will be committed before this call.   
	 */
	public void afterDelete();
	
	
	/**
	 * @return true if an entity can be deleted, false otherwise.
	 */
	public DeleteResponse canDelete();
	
	
	
	/**
	 * A delete response
	 */
	public static interface DeleteResponse{
		
		public List<String> getMessages();
		
		public boolean canDelete();
		
	}
	
}
