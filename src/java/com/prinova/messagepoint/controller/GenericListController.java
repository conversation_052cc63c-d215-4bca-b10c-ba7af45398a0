package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.query.IPage;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;

import java.util.ArrayList;
import java.util.List;

import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;

public class GenericListController {

    protected IPage getPage(final int pageIndex, final int pageSize, final String sSearch, final String sortKey, String sortDirection, Class<?> entityClass) {
        ServiceExecutionContext context = HibernatePaginationService.createContext(entityClass, emptyList(), emptyList(), emptyMap(), emptyMap(), emptyMap(), pageIndex, pageSize, getOrderByList(sortKey, sortDirection));
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();

        IPage page = serviceResponse.getPage();

        // If filter is applied and new result set has less pages than the current index then go to the last page
        if (!StringUtils.isEmpty(sSearch) && page.getList().isEmpty() && page.getRowCount() > 0 && pageIndex > 0) {
            // There is no Math.ceilDiv, so negate the dividend of floorDiv and then negate the result to get to equivalent of what Math.ceilDiv would do
            int nextIndex = -Math.floorDiv(-page.getRowCount(), page.getPageSize() != 0 ? page.getPageSize() : 1);
            page = getPage(nextIndex, pageSize, sSearch, sortKey, sortDirection, entityClass);
        }

        return page;
    }

    protected List<MessagepointOrder> getOrderByList(String sortKey, String sortDirection) {
        if (sortKey == null || sortKey.isEmpty()) {
            return new ArrayList<>();
        }

        ArrayList<MessagepointOrder> result = new ArrayList<>();
        result.add(sortDirection.equals("asc")
                ? MessagepointOrder.asc(HibernateObjectManager.MODEL_ALIAS_PREFIX + "." + sortKey)
                : MessagepointOrder.desc(HibernateObjectManager.MODEL_ALIAS_PREFIX + "." + sortKey));

        return result;
    }
}
