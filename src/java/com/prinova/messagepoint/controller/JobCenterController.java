package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.wrapper.JobCenterWrapper;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class JobCenterController extends MessagepointController {
    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
        long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);
        Boolean globalContext = ServletRequestUtils.getBooleanParameter(request, AsyncListTableController.PARAM_GLOBAL_CONTEXT);

        if ( globalContext != null )
            if ( UserUtil.getCurrentGlobalContext() != globalContext ) {
                // Persist URL global context: Method of toggling
                HashMap<String,String> contextAttr = new HashMap<>();
                contextAttr.put(UserUtil.CONTEXT_KEY_GLOBAL_CONTEXT, globalContext.toString().toLowerCase());
                UserUtil.updateUserContextAttributes(contextAttr);

                return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
            }

        if (documentId == -2) {
            return super.showForm(request, response, errors);
        } else if (documentId != -1) { // Secondary access: Navigating post entry

            // Context validation
            ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), false);
            if ( contextMismatchView != null )
                return contextMismatchView;

        } else if (collectionId != -1) { // Secondary access: Navigating post entry
            boolean contextMismatch = false;
            TouchpointCollection contextCollection = UserUtil.getCurrentCollectionContext();
            if ( contextCollection != null && contextCollection.getId() != collectionId )
                contextMismatch = true;

            HashMap<String,String> contextAttr = new HashMap<>();
            contextAttr.put(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT, String.valueOf(collectionId));
            // Update user context properties
            UserUtil.updateUserContextAttributes(contextAttr);

            if (contextMismatch)
                return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
        } else { // First access: Direct traffic
            return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), true);
        }

        return super.showForm(request, response, errors);
    }

    private Map<String, Object> getSuccessViewParms(HttpServletRequest request) throws Exception {
        Map<String, Object> parms = new HashMap<>();

        long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
        long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);
        Boolean globalContext = ServletRequestUtils.getBooleanParameter(request, AsyncListTableController.PARAM_GLOBAL_CONTEXT);


        if ( globalContext != null )
            parms.put(AsyncListTableController.PARAM_GLOBAL_CONTEXT, globalContext);

        if (documentId != -1L) {
            parms.put(AsyncListTableController.PARAM_DOCUMENT_ID, documentId);
        }

        if (collectionId != -1L) {
            parms.put(AsyncListTableController.PARAM_COLLECTION_ID, collectionId);
        } else {
            List<Document> visibleDocuments = Document.getVisibleDocuments();
            parms.put(AsyncListTableController.PARAM_DOCUMENT_ID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
        }
        return parms;
    }


    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return new JobCenterWrapper();
    }
}