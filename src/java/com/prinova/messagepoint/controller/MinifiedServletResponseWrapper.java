package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.io.output.ByteArrayOutputStream;

import com.google.javascript.jscomp.CompilationLevel;
import com.google.javascript.jscomp.Compiler;
import com.google.javascript.jscomp.CompilerOptions;
import com.google.javascript.jscomp.SourceFile;
import com.googlecode.htmlcompressor.compressor.HtmlCompressor;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringUtil;

public class MinifiedServletResponseWrapper extends HttpServletResponseWrapper {

    private final HttpServletRequest servletRequest;
    private final boolean isPassThrough;

    private boolean isDeveloperMode;
    private byte[] responseBytes;
    private ByteArrayPrintWriter output;
    private HashMap<String, Long> dateHeaders = new HashMap<>();
    private HashMap<String, Object> headers = new HashMap<>();
    private HttpServletResponse servletResponse;
    private int contentLength;
    private int status = HttpStatus.SC_OK;
    private String characterEncoding;
    private String contentType;

    private ThreadLocal<HtmlCompressor> localHtmlCompressor = ThreadLocal.withInitial(this::initNewHtmlCompressor);

    MinifiedServletResponseWrapper(ServletRequest request, ServletResponse response)
    {
        super((HttpServletResponse) response);
        output = new ByteArrayPrintWriter();

        servletResponse = (HttpServletResponse) response;
        servletRequest = (HttpServletRequest) request;

        isPassThrough = isPassThroughRequest(servletRequest);

        isDeveloperMode = isDeveloperMode(servletRequest);
    }

    private boolean isPassThroughRequest(HttpServletRequest servletRequest) {
        if (servletRequest != null && servletRequest.getRequestURI() != null) {
            String requestUri = servletRequest.getRequestURI().toLowerCase();

            return requestUri.endsWith("/download/pdf.form") ||
                    requestUri.endsWith("/download/data.form") ||
                    requestUri.endsWith("/download/font.form") ||
                    requestUri.endsWith("/download/xml.form") ||
                    requestUri.endsWith("/download/excel.form") ||
                    requestUri.endsWith("/download/image.form") ||
                    requestUri.endsWith("/download/log.form") ||
                    requestUri.endsWith("/download/zip.form");
        }

        return false;
    }

    void commitResponse() throws IOException {

        if (isPassThrough) {
            return;
        }

        String contentType = this.getContentType();

        if (!isDeveloperMode && contentType != null && contentType.toLowerCase().contains("text/html")) {
            String out = new String(getByteArray());
            String htmlAssetResponse = minifyInlineJavascript(out);
            HtmlCompressor minifiedHtml = getLocalHtmlCompressor().get();
            String minifiedHtmlOutput = minifiedHtml.compress(htmlAssetResponse);

            String headerAcceptEncoding = servletRequest.getHeader("accept-encoding");

            if (headerAcceptEncoding != null && headerAcceptEncoding.contains("gzip")) {
                java.io.ByteArrayOutputStream byteArrayOutputStream = new java.io.ByteArrayOutputStream();
                GZIPOutputStream zipStream = new GZIPOutputStream(byteArrayOutputStream);
                zipStream.write(minifiedHtmlOutput.getBytes());
                zipStream.finish();
                zipStream.flush();
                servletResponse.setHeader("Content-Encoding", "gzip");
                byte[] gzipBytes = byteArrayOutputStream.toByteArray();
                setContentLength(gzipBytes.length);
                setResponseBytes(gzipBytes);
            } else {
                byte[] responseBytes = minifiedHtmlOutput.getBytes();
                setContentLength(responseBytes.length);
                setResponseBytes(responseBytes);
            }
        }
        else {
            if (!servletResponse.isCommitted()) {
                byte[] output = getByteArray();
                setResponseBytes(output);
                setContentLength(output.length);
            }
        }

        servletResponse.setStatus(this.status);
        servletResponse.setContentLength(this.contentLength);
        servletResponse.setContentType(this.contentType);

        if (StringUtil.isEmptyOrNull(this.characterEncoding)) {
            servletResponse.setCharacterEncoding(this.characterEncoding);
        }

        for (String key : this.headers.keySet()) {
            if (this.headers.get(key) instanceof Integer) {
                this.servletResponse.setIntHeader(key, (Integer) this.headers.get(key));
            } else {
                this.servletResponse.setHeader(key, String.valueOf(this.headers.get(key)));
            }
        }

        for (String key : this.dateHeaders.keySet()) {
            this.servletResponse.setDateHeader(key, this.dateHeaders.get(key));
        }

        if (this.responseBytes != null && !servletResponse.isCommitted()) {
            try {
                servletResponse.getOutputStream().write(this.responseBytes);
            } catch (IOException ioe) {
                if (ioe.toString().contains("Broken pipe")) {
                    String message = MessageFormat.format("Request for {0} canceled by client before response fully sent", servletRequest.getRequestURI());
                    LogUtil.getLog(MinifiedServletResponseWrapper.class).info(message);
                }
            }
        }

        servletResponse.flushBuffer();


    }

    public ThreadLocal<HtmlCompressor> getLocalHtmlCompressor() {
        return localHtmlCompressor;
    }

    private HtmlCompressor initNewHtmlCompressor() {
        HtmlCompressor minifiedHtml = new HtmlCompressor();
        minifiedHtml.setCompressCss(true);
        minifiedHtml.setCompressJavaScript(false);
        minifiedHtml.setRemoveIntertagSpaces(true);
        List<Pattern> patterns = new ArrayList<>();
        patterns.add(Pattern.compile("(imgPath|imgName)=\"(.)*?\"", Pattern.DOTALL | Pattern.CASE_INSENSITIVE));
        minifiedHtml.setPreservePatterns(patterns);
        minifiedHtml.setRemoveComments(true);
        return minifiedHtml;
    }

    @Override
    @SuppressWarnings("deprecation")
    public void setStatus(int sc, String sm) {
        if (isPassThrough) {
            super.setStatus(sc, sm);
        } else {
            this.status = sc;
        }
    }

    @Override
    public void setStatus(int sc) {
        if (isPassThrough) {
            super.setStatus(sc);
        } else {
            this.status = sc;
        }
    }

    private boolean isDeveloperMode(HttpServletRequest request) {

        if (request.getCookies() != null) {
            Optional<Cookie> developerMode = Arrays.stream(request.getCookies()).filter(x -> x.getName().startsWith("developer-mode")).findFirst();
            return developerMode.isPresent();
        }

        return false;
    }

    public boolean isDeveloperMode() {
        return isDeveloperMode;
    }

    private byte[] getByteArray() throws IOException {
        return output.toByteArray();
    }

    private String minifyInlineJavascript(String htmlResponse) {

        String assetResponse = htmlResponse;

        Pattern inlineScriptTagRegex = Pattern.compile("<script((?:(?!src=).)*?)>(?<inlinejs>.*?)</script>", Pattern.DOTALL);
        Matcher inlineScriptTagMatcher = inlineScriptTagRegex.matcher(htmlResponse);

        while (inlineScriptTagMatcher.find()) {

            String scriptTagValue = inlineScriptTagMatcher.group();
            String inlineScript = inlineScriptTagMatcher.group("inlinejs");

            if (inlineScript != null && !inlineScript.isEmpty() && scriptTagValue != null) {

                if (scriptTagValue.contains("x-handlebars-template") || scriptTagValue.contains("x-tmpl")) {
                    continue;
                }

                if (scriptTagValue.contains("import * as")) {
                    continue;
                }

                try {
                    Compiler compiler = new Compiler();
                    CompilerOptions options = new CompilerOptions();
                    CompilationLevel.WHITESPACE_ONLY.setOptionsForCompilationLevel(options);
                    options.setLanguageIn(CompilerOptions.LanguageMode.ECMASCRIPT5);

                    SourceFile extern = SourceFile.fromCode("externs.js", "function alert(x) {}");
                    SourceFile input = SourceFile.fromCode("input.js", inlineScript);

                    compiler.compile(extern, input, options);
                    String minifiedInlineSrc = compiler.toSource();

                    if (!compiler.hasErrors()) {
                        assetResponse = assetResponse.replace(inlineScript, minifiedInlineSrc);
                    }
                } catch (Exception ex) {
                    LogUtil.getLog(MinifiedServletResponseWrapper.class).error("Error:", ex);
                }
            }
        }

        return assetResponse;
    }

    private void setResponseBytes(byte[] bytes) {
        this.responseBytes = bytes;
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException
    {
        if (isPassThrough) {
            return super.getOutputStream();
        } else {
            return output.getStream();
        }
    }

    @Override
    public PrintWriter getWriter() throws IOException
    {
        if (isPassThrough) {
            return super.getWriter();
        } else {
            return output.getWriter();
        }
    }

    @Override
    public boolean isCommitted() {
        return super.isCommitted();
    }

    @Override
    public boolean containsHeader(String name) {
        if (isPassThrough) {
            return super.containsHeader(name);
        } else {
            return this.headers.containsKey(name) || this.dateHeaders.containsKey(name);
        }
    }

    @Override
    public String getHeader(String name) {
        if (isPassThrough) {
            return super.getHeader(name);
        } else {
            String result = null;

            if (this.headers.containsKey(name)) {
                result = String.valueOf(this.headers.get(name));
            } else if (this.dateHeaders.containsKey(name)) {
                result = String.valueOf(this.dateHeaders.get(name));
            }

            return result;
        }
    }

    @Override
    public Collection<String> getHeaderNames() {
        if (isPassThrough) {
            return super.getHeaderNames();
        } else {
            List<String> headers = new ArrayList<>();

            headers.addAll(this.headers.keySet());
            headers.addAll(this.dateHeaders.keySet());


            return headers;
        }
    }

    @Override
    public Collection<String> getHeaders(String name) {
        if (isPassThrough) {
            return super.getHeaders(name);
        } else {
            List<String> result = new ArrayList<>();

            if (this.headers.containsKey(name)) {
                result.add(String.valueOf(this.headers.get(name)));
            } else if (this.dateHeaders.containsKey(name)) {
                result.add(String.valueOf(this.headers.get(name)));
            }

            return result;
        }
    }

    @Override
    public void setDateHeader(String name, long date) {
        if (isPassThrough) {
            super.setDateHeader(name, date);
        } else {
            this.dateHeaders.put(name, date);
            this.headers.remove(name);
        }
    }

    @Override
    public void setHeader(String name, String value) {
        if (isPassThrough) {
            super.setHeader(name, value);
        } else {
            this.headers.put(name, value);
            this.dateHeaders.remove(name);
        }
    }

    @Override
    public void setIntHeader(String name, int value) {
        if (isPassThrough) {
            super.setIntHeader(name, value);
        } else {
            this.headers.put(name, value);
            this.dateHeaders.remove(name);
        }
    }

    @Override
    public void addHeader(String name, String value) {
        if (isPassThrough) {
            super.addHeader(name, value);
        } else {
            this.headers.put(name, value);
        }
    }

    @Override
    public void addIntHeader(String name, int value) {
        if (isPassThrough) {
            super.addIntHeader(name, value);
        } else {
            this.headers.put(name, value);
        }
    }

    @Override
    public void setContentLength(int len) {
        if (isPassThrough) {
            super.setContentLength(len);
        } else {
            this.contentLength = len;
        }
    }

    @Override
    public void setCharacterEncoding(String charset) {
        if (isPassThrough) {
            super.setCharacterEncoding(charset);
        } else {
            this.characterEncoding = charset;
        }
    }

    @Override
    public String getCharacterEncoding() {
        if (isPassThrough) {
            return super.getCharacterEncoding();
        } else {
            return this.characterEncoding;
        }
    }

    @Override
    public void setContentType(String type) {
        if (isPassThrough) {
            super.setContentType(type);
        } else {
            this.contentType = type;
        }
    }

    @Override
    public String getContentType() {
        if (isPassThrough) {
            return super.getContentType();
        } else {
            return this.contentType;
        }
    }

    @Override
    public void flushBuffer() {
        if (isPassThrough) {
            try {
                super.flushBuffer();
            } catch (IOException ignored) {
            }
        }
    }

    private static class ByteArrayServletStream extends ServletOutputStream
    {
        ByteArrayOutputStream baos;

        ByteArrayServletStream(ByteArrayOutputStream baos)
        {
            this.baos = baos;
        }

        public void write(int param) {
            baos.write(param);
        }
    }

    private static class ByteArrayPrintWriter
    {

        private ByteArrayOutputStream baos = new ByteArrayOutputStream();

        private PrintWriter pw = new PrintWriter(baos);

        private ServletOutputStream sos = new ByteArrayServletStream(baos);

        public PrintWriter getWriter()
        {
            return pw;
        }

        public ServletOutputStream getStream()
        {
            return sos;
        }

        byte[] toByteArray() throws IOException {
            pw.flush();
            sos.flush();
            baos.flush();
            return baos.toByteArray();
        }
    }
}

