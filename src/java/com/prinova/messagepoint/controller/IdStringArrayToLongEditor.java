package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;

public class IdStringArrayToLongEditor extends PropertyEditorSupport {

    public void setAsText(String text) throws IllegalArgumentException {
        if ( (text == null) || (text.isEmpty())) {
            setValue(0);
        }else{
            String[] splittedArr = text.split(",");
            for(String splitted : splittedArr){
                long longValue = Long.parseLong(splitted);
                if(longValue>0){
                    setValue(longValue);
                }
            }

        }
    }

    public String getAsText() {
        if ( (getValue() == null) || (getValue().equals("")) ) {
            return "0,0";
        } else {
            return getValue().toString();
        }
    }
}
