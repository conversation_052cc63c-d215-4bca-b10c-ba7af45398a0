package com.prinova.messagepoint.controller;

import java.text.MessageFormat;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.HttpRequestUtil;
import org.apache.commons.lang.StringEscapeUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.controller.insert.InsertListController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.WeightUnit;
import com.prinova.messagepoint.tag.view.ShadowFrameTag;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.CreateThumbnail;

public class AsyncInsertImageThumbnailController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncInsertImageThumbnailController.class);
	
	private static final String REQ_PARAM_TYPE = 	"type";
	private static final String TYPE_IMAGE_ONLY = 	"imageOnly";
	private static final String TYPE_VERBOSE = 		"verbose";

	private static final String HTML_DIV_OPEN =		"<div id=\"{0}\" style=\"{1}\"> \n";
	private static final String HTML_DIV_CLOSE =	"</div> \n";
	
	private static final int WIDTH_PORTRAIT = 100;
	private static final int WIDTH_LANDSCAPE = 125;
	private static final int WIDTH_PORTRAIT_VERBOSE = 200;
	private static final int WIDTH_LANDSCAPE_VERBOSE = 250;
	
	HttpServletRequest request;
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		setRequest(request);

		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseXML(request,response).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
        	log.error("Exception: Unable to complete insert image thumbnail request."+e.getMessage(),e);
			out.flush();
		}
		return null;
	}
	
	private String getResponseXML (HttpServletRequest request, HttpServletResponse response) throws Exception {
		String insertThumbnailHTML = "";

		if ( getInsertIdParam(request) != -9 ) {
			Insert insert = Insert.findById(getInsertIdParam(request));
			
			// Data
			if ( getType(getRequest()).equalsIgnoreCase(TYPE_VERBOSE))
				insertThumbnailHTML += getInsertDataHTML(insert);
			
			// Image(s)
			if (insert.getFrontContentPath() != null) {
				insertThumbnailHTML += getInsertThumbnailHTML (insert.getFrontContentPath(), "insertThumbnailDiv_front", getImageLabelHTML("front",insert.getBackContentPath() != null));
			} else {
				insertThumbnailHTML += getInsertThumbnailHTML (null, "insertThumbnailDiv_front", getImageLabelHTML("front",false));
			}
			if (insert.getBackContentPath() != null) {
				insertThumbnailHTML += getInsertThumbnailHTML (insert.getBackContentPath(), "insertThumbnailDiv_back", getImageLabelHTML("back",true));
			}

		} else {
			insertThumbnailHTML = "UNKNOWN";
		}
		
		return "<?xml version='1.0'?><insertImageHTML>"+StringEscapeUtils.escapeXml(insertThumbnailHTML)+"</insertImageHTML>";	
	}
	
	private String getImageLabelHTML (String type, Boolean hasImageForBack) {
		String label;
		if (hasImageForBack)
			if (type.equals("front"))
				label = "<div class=\"fullLineLabel\">" + ApplicationUtil.getMessage("page.label.FRONT") + " <span style=\"color: #888; padding-left: 10px;\">" + ApplicationUtil.getMessage("page.label.BACK") + "</span></div>";
			else
				label = "<div class=\"fullLineLabel\"><span style=\"color: #888; padding-right: 10px;\">" + ApplicationUtil.getMessage("page.label.FRONT") + "</span>" + ApplicationUtil.getMessage("page.label.BACK") + "</div>";
		else
			label = "<div class=\"fullLineLabel\">" + ApplicationUtil.getMessage("page.label.FRONT") + "</div>";
		return label;
	}
	
	private String getInsertDataHTML(Insert insert) {
		String insertDataHTML = "";

		int defaultWeightUnitId = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Insert.KEY_WeightUnits)).intValue();
		WeightUnit defaultWeightUnit = new WeightUnit(defaultWeightUnitId);
		
		insertDataHTML += "<table style=\"font-size: 12px;\">";
		insertDataHTML += 		"<tr>" +
									"<td>" + ApplicationUtil.getMessage("page.label.name") + ":</td>" +
									"<td>"+insert.getName()+"</td>" +
									"</tr>";
		insertDataHTML += 		"<tr>" +
									"<td>" + ApplicationUtil.getMessage("page.label.stock.id") + ":</td>" +
									"<td>"+( !(insert.getStockId() == null || insert.getStockId().trim().isEmpty()) ? insert.getStockId() : ApplicationUtil.getMessage("page.label.none")  )+"</td>" +
									"</tr>";
		insertDataHTML += 		"<tr>" +
									"<td>" + ApplicationUtil.getMessage("page.label.delivery") + ":&nbsp;&nbsp;</td>" +
									"<td>"+insert.getDeliveryType().getDisplayText()+"</td>" +
									"</tr>";
		insertDataHTML += 		"<tr>" +
									"<td>" + ApplicationUtil.getMessage("page.label.weight") + ":</td>" +
									"<td>"+insert.getDehydratedWeight()+" "+defaultWeightUnit.getDisplayText()+"</td>" +
									"</tr>";
		insertDataHTML += "</table>";
		
		return insertDataHTML;
	}
	
	private String getInsertThumbnailHTML (String filePath, String divId, String label) {
		String insertThumbnailHTML = "";
		String imagePath = "";
		
		if (filePath != null)
			imagePath = ApplicationUtil.getWebRoot()+"download/image.form?resource="+ HttpRequestUtil.getFileResourceToken(filePath);
		else
			imagePath = "../includes/themes/commonimages/inserts/insert_watermark_front.gif";

		insertThumbnailHTML += MessageFormat.format(HTML_DIV_OPEN, new Object[]{divId, "display: none;"} );
		insertThumbnailHTML += 	ShadowFrameTag.getFrameOpen();
		insertThumbnailHTML += 		"<img src=\""+imagePath+"\" width=\""+getImageWidth(filePath)+"px\" style=\" border: 1px #ccc solid\" />";
		insertThumbnailHTML += 	ShadowFrameTag.getFrameClose();
		insertThumbnailHTML += label;
		insertThumbnailHTML += HTML_DIV_CLOSE;
		
		
		return insertThumbnailHTML;	
	}
	
	private int getImageWidth( String imageFile ) {
		if ( getType(getRequest()).equalsIgnoreCase(TYPE_VERBOSE))
			if ( isLandscapeImage(imageFile) )
				return WIDTH_LANDSCAPE_VERBOSE;
			else
				if (imageFile == null)
					return WIDTH_PORTRAIT;
				else
					return WIDTH_PORTRAIT_VERBOSE;
		else
			if ( isLandscapeImage(imageFile) )
				return WIDTH_LANDSCAPE;
			else
				return WIDTH_PORTRAIT;
	}
	
	private static boolean isLandscapeImage(String imageFilePath) {
		if (imageFilePath == null)
			return false;
        CreateThumbnail image = new CreateThumbnail(imageFilePath);
        return (image.getImageWidth() > image.getImageHeight());
	}
	
	private Long getInsertIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, InsertListController.REQ_PARAM_INSERT_ID, -9);
	}
	
	private String getType( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, TYPE_IMAGE_ONLY);
	}

	public HttpServletRequest getRequest() {
		return request;
	}
	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

}