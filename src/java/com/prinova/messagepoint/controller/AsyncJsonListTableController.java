package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.font.TextStyleJsonVO;
import com.prinova.messagepoint.util.LogUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public class AsyncJsonListTableController implements Controller {

    @Override
    public ModelAndView handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONArray result = new JSONArray();

        List<TextStyle> textStyles = TextStyle.findAll();

        textStyles.forEach(textStyle -> {
            TextStyleJsonVO textStyleJsonVO = new TextStyleJsonVO(textStyle);
            JSONObject value = new JSONObject(textStyleJsonVO);
            result.put(value);
        });

        sendResponse(httpServletResponse, result);

        return null;
    }

    private void sendResponse(HttpServletResponse response, Object result) {
        try {
            response.setContentType("application/json");
            ServletOutputStream out = response.getOutputStream();
            out.print(result.toString());
        } catch (IOException ex) {
            LogUtil.getLog(AsyncJsonListTableController.class).error("Error processing async request: " + ex.getMessage());
        }
    }
}
