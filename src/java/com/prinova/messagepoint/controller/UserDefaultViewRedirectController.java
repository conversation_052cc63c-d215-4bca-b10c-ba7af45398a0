package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.navigation.NavigationTab;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.util.LogUtil;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

import static com.prinova.messagepoint.util.UserUtil.convertAuthorityToPermission;

public class UserDefaultViewRedirectController  extends AbstractController{

	private String defaultRedirect = "user/workspace_reset.form?msgkey=error.message.cannotviewyourdefaultpage";
	private static final Log log = LogUtil.getLog(UserDefaultViewRedirectController.class);
	
	@Override
	protected ModelAndView handleRequestInternal(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		
		String redirectURL = this.defaultRedirect;
		
		User user = UserUtil.getPrincipalUser();
		if ( (user != null) && (user.getDefaultTabId() > 0 && user.getDefaultTabId() < NavigationTab.TAB_ID_RATIONALIZER_INT)) {
			logger.info("********user default tab id is " + user.getDefaultTabId() );
			defaultRedirect += "&defaulttabid=" + user.getDefaultTabId();
			switch ( UserUtil.isMessagepointInteractiveRestrictedUser() ? NavigationTab.TAB_ID_TOUCHPOINTS_INT : user.getDefaultTabId() ) {
			case (NavigationTab.TAB_ID_TASKS_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TASKS))?"tasks/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_TASKS : null ;
				break;
			case (NavigationTab.TAB_ID_INSERTS_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_INSERTS))?"inserts/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_INSERTS : null ;
				break;
			case (NavigationTab.TAB_ID_TARGET_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TARGET))?"target/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_TARGET : null ;
				break;
			case (NavigationTab.TAB_ID_REPORTS_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_REPORTS))?"reports/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_REPORTS : null ;
				break;
			case (NavigationTab.TAB_ID_TESTING_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TESTING))?"testing/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_TESTING : null ;
				break;
			case (NavigationTab.TAB_ID_TOUCHPOINTS_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TOUCHPOINTS))?"tpadmin/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_TOUCHPOINTS : null ;
				break;
			case (NavigationTab.TAB_ID_DATA_ADMIN_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_DATA_ADMIN))?"dataadmin/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_DATA_ADMIN : null ;
				break;
			case (NavigationTab.TAB_ID_ADMIN_INT):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_ADMIN))?"admin/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_ADMIN : null ;
				break;
			case ((int) NavigationTab.TAB_ID_PINC):
				redirectURL = (isUserAllowed(user.getId(), NavigationTab.TAB_ID_PINC)) ? "pinc/redirect.form?CURRENT_TAB_ID=" + NavigationTab.TAB_ID_PINC : null;
				break;
			default:
		 		redirectURL = null;
				break;
			}
		}else{			
			if(user == null){
				logger.info("********user  is null" );
			}else{
				logger.info("********user  defaultTabId is " +user.getDefaultTabId() );
				redirectURL=null;
				/*
				 * This part is to deal with cases which is not related any tabs like rationalizer
				 */
				switch(user.getDefaultTabId()){
					case (NavigationTab.TAB_ID_RATIONALIZER_INT):
						if ( UserUtil.isAllowed(user.getId(), Permission.ROLE_ECATALOG_VIEW) 
								|| UserUtil.isAllowed(user.getId(), Permission.ROLE_ECATALOG_EDIT)
								|| UserUtil.isAllowed(user.getId(), Permission.ROLE_ECATALOG_ADMIN)
								|| UserUtil.isAllowed(user.getId(), Permission.ROLE_ECATALOG_APPROVE)) {
							redirectURL="rationalizer/rationalizer_documents_list.form";
						}
					break;
				}
			}
		}

		if (redirectURL == null)
		{
			boolean reset = false;
			boolean onlyTask = false;
			if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_CONTENTS))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TARGET))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_INSERTS))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_REPORTS))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TESTING))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_SIMULATION))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TOUCHPOINTS))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_DATA_ADMIN))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_ADMIN))
				reset = true;
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_PINC))
				reset = true;
			
			/* 
			*	*** Please don't move this condition. Task tab will be the last one to check for permission
			*/
			else if (isUserAllowed(user.getId(), NavigationTab.TAB_ID_TASKS)){ 
				reset = true;
				onlyTask = true;
			}
			if (reset) {
				redirectURL = this.defaultRedirect;
				if(onlyTask && UserUtil.isRationalizerOnlyUser(user)) {
					redirectURL="rationalizer/rationalizer_documents_list.form";
				}
			}
			else {
				redirectURL = "dashboards/my_dashboard.form";
			}
		}
		
		response.sendRedirect(redirectURL);
		return null;
	}
	
	private boolean isUserAllowed(long userId, long navigationTabId){
		User user = User.findById(userId);
		boolean isUserAllowed = false;
		NavigationTab tab = HibernateUtil.getManager().getObject(NavigationTab.class, navigationTabId);
		if(tab != null) {
			Set<Permission> permissions = tab.getPermissions();
			List<Permission> usersAggregatePermissions = convertAuthorityToPermission(user.getAuthorities());
			for( Permission permission : permissions ) {
				if (usersAggregatePermissions.contains(permission)) {
					isUserAllowed = true;
					break;
				}
			}
			return isUserAllowed;
		}
		return false;
	}

}
