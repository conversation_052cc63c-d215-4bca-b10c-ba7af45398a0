package com.prinova.messagepoint.controller;

import java.util.Collection;
import java.util.Iterator;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.navigation.Tree;
import com.prinova.messagepoint.tag.view.tree.TreeConstants;
import com.prinova.messagepoint.tag.view.tree.TreeConverter;
import com.prinova.messagepoint.tag.view.tree.TreeNode;
import com.prinova.messagepoint.tag.view.tree.TreeQueryHelper;
import com.prinova.messagepoint.tag.view.tree.TreeUtils;
import com.prinova.messagepoint.tag.view.tree.TreeWriter;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.HibernateUtil;

public class NavigationTreeController implements Controller {

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		response.setCharacterEncoding(ApplicationLanguageUtils.NAVIGATION_TREE_ENCODING);
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();
		
		String type = request.getParameter("root");
		if (type != null && type.equals("source")) {
			Long treeId = Long.parseLong(  request.getParameter("treeId") );
			Tree tree = HibernateUtil.getManager().getObject( Tree.class, treeId );
			if( tree != null ) {
				TreeWriter treeWriter = tree.getWriter( request );
				out.print( treeWriter.treeJSON(null, request).toString() );
			}
		} else if (type != null) {
			long treeId = Long.valueOf( type.substring(0, type.indexOf("_")) ).longValue();
			String idStr = type.substring(type.indexOf("_")+1);
			TreeNode parentNode = buildTreeNode( treeId, idStr, request );
			JSONArray treeJSON = new JSONArray();

			for( int i = 0 ; i < parentNode.getTree().getNodes().size(); i++ ) {
				
				Tree childTree = parentNode.getTree().getNodes().get(i);

				Collection<?> collection = TreeQueryHelper.executeQuery( childTree.getQuery(), parentNode );

				if ( !(collection == null || collection.isEmpty()) ) {
					
					int j = 0;
					Iterator<?> iterator = collection.iterator();
					
					for( ; iterator.hasNext() && j < TreeConstants.MAX_CHILDREN_PER_NODE ; j++ ) {
						Object object = iterator.next();
						TreeNode node = new TreeNode( parentNode, childTree, object, request);
						treeJSON.put(TreeUtils.getLeafNodeJSON(childTree, node, request));
					}
				}
			}
			out.print( treeJSON.toString() );
		}
		
		return null;
	}

	private TreeNode buildTreeNode( long treeId, String idStr, HttpServletRequest request ) {
		Tree tree = HibernateUtil.getManager().getObject( Tree.class, treeId );
		Object expandedNodeId = TreeConverter.convert(tree, idStr);
		TreeNode parentNode =  new TreeNode( null, tree, expandedNodeId, request );

		TreeNode current = parentNode;
		while( current.getTree().getParent() != null ) {
			TreeNode parent = new TreeNode( null, current.getTree().getParent(), null, request );
			current.setParent( parent );
			parent.getChildren().add( current );
			current = current.getParent();
		}
		
		// Got the TreeNode structure...
		
		// Execute the query for each... going down... determining which id to 
		// store for each node based on which collection set eventually contains 
		// the expanded id.
		
		
		// Until the children of the tree contains "tree" then keep building the whole tree 
		// structure...

		fillIn( current, tree, idStr );
		
		return parentNode;
	}
	
	private boolean fillIn( TreeNode rootTreeNode, Tree tree, String idStr ) {
		Collection<?> collection = TreeQueryHelper.executeQuery( rootTreeNode.getTree().getQuery(), rootTreeNode.getParent() );
		for( Object collectionItem : collection ) {

			if( rootTreeNode.getTree().equals( tree ) ) {
				if( TreeConverter.isEqual( tree, idStr, collectionItem ) ) {
					rootTreeNode.setNodeModel( collectionItem );
					return true;
				}
			} else {
				for( TreeNode childNode : rootTreeNode.getChildren() ) {
					rootTreeNode.setNodeModel( collectionItem );
					if( fillIn( childNode, tree, idStr ) ) {
						// Done!
						return true;
					}
				}
			}
		}
		return false;
	}
}