package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.DataElement;
import com.prinova.messagepoint.model.admin.DataRecord;

import com.prinova.messagepoint.model.admin.VariableDataElementMap;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by dave.mckeown on 2016-09-13.
 */
public class AsyncDataElementController implements Controller {
    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        long drid = ServletRequestUtils.getLongParameter(request, "deid", -1);
        String reqtype = ServletRequestUtils.getStringParameter(request, "reqtype", "");

        JSONObject result;

        if (reqtype.equals("detail")) {
            result = getDataRecordDetails(drid);
        } else {
            result = getDataRecord(drid);
        }

        ServletOutputStream out = response.getOutputStream();
        out.print(result.toString());

        return null;
    }

    private JSONObject getDataRecordDetails(Long id) throws JSONException {
        JSONObject result = new JSONObject();
        DataRecord record = DataRecord.findById(id);

        result.put("name", record.getName());
        result.put("id", record.getId());

        DataElement[] dataElements = record.getDataElementArray();

        JSONArray variables = new JSONArray();

        if (dataElements != null && dataElements.length > 0) {
            for (DataElement element : record.getDataElementArray()) {
                List<VariableDataElementMap> map = VariableDataElementMap.findAllByDataElement(element);

                if (map != null) {
                    for (VariableDataElementMap entry : map) {
                        JSONObject variable = new JSONObject();
                        variable.put("id", entry.getDataElement().getId());
                        variable.put("name", entry.getDataElement().getName());
                        variables.put(variable);
                    }
                }
            }
        }

        result.put("showVariables", !variables.isEmpty());
        result.put("variables", variables);

        result.put("breakIndicator", record.getBreakIndicator());
        result.put("startsCustomer", record.isStartCustomer());

        if (record.getDataGroup() != null && record.getDataGroup().getStartDataRecord() != null) {
            result.put("startDataGroup", record.getDataGroup().getStartDataRecord().getId() == record.getId());
        } else {
            result.put("startDataGroup", false);
        }

        result.put("isEnabled", record.isEnabled());
        result.put("isRepeating", record.isRepeating());

        //review
        if (record.getDataGroup()==null) {
            result.put("dataGroups", "none");
        } else {
            JSONArray dataGroups = new JSONArray();
            JSONObject dataGroup = new JSONObject();
            dataGroup.put("name", record.getDataGroup().getName());
            dataGroups.put(dataGroup);
            result.put("dataGroups", dataGroups);
        }

        result.put("attributeCount", record.getDataElementArray().length);
        return result;
    }

    private JSONObject getDataRecord(long id) throws JSONException {

        JSONObject result = new JSONObject();
        DataRecord record = DataRecord.findById(id);

        result.put("name", record.getName());
        result.put("id", record.getId());

        JSONObject extras = new JSONObject();

        extras.put("attributeCount", record.getDataElementArray().length);
        extras.put("isRepeating", record.isRepeating());
        result.put("extras", extras);

        return result;
    }
}
