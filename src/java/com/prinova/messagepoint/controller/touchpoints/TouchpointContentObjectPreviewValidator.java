package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

public class TouchpointContentObjectPreviewValidator extends MessagepointInputValidator{

    public void validateNotGenericInputs(Object command, Errors errors) {
    	TouchpointContentObjectPreviewWrapper tpMsgPreviewWrapper = (TouchpointContentObjectPreviewWrapper)command;

    	if (tpMsgPreviewWrapper.getDocumentPreview().getMode() != null && tpMsgPreviewWrapper.getDocumentPreview().getMode().equals("preview")) {
	    	DataResource dataResource = tpMsgPreviewWrapper.getDocumentPreview().getDataResource();
	    	if (dataResource == null) {
	    		errors.rejectValue("documentPreview.dataResource", "error.message.dataresourcerequired");
	    	} 
    	}
    }
}
