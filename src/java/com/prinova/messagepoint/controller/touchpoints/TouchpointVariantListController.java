package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.*;
import com.prinova.messagepoint.controller.tpadmin.ExportImportOptions;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.common.TouchpointSelectionListFilterType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.wrapper.AsyncSelectionListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportContentBackgroundTask;
import com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateMessagepointObjectExportService;
import com.prinova.messagepoint.platform.services.export.GenerateTouchpointAuditReportService;
import com.prinova.messagepoint.platform.services.metadata.ClearMetadataService;
import com.prinova.messagepoint.platform.services.tpselection.CreateOrUpdateTouchpointVariantService;
import com.prinova.messagepoint.platform.services.tpselection.CreateTouchpointVariantProofService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TouchpointVariantListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointVariantListController.class);

	public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID 	= "touchpointSelectionId";
	public static final String REQ_PARAM_TARGET_SELECTION_STATUS_ID	= "targetSelectionStatusId";
	public static final String REQ_PARAM_DOCUMENTID 				= "documentId";
	public static final String REQ_PARAM_VIEWID 					= "viewId";
	public static final String REQ_PARAM_ACTION 					= "action";
	public static final String REQ_PARAM_LOCALE_ID					= "localeId";
	public static final String REQ_PARM_EXPORT_EXT 					= "ext";


	public static final int ACTION_CREATE_NEW_NODE 		= 1; //action = add selection
	public static final int ACTION_RENAME_NODE 			= 2;
	public static final int ACTION_DELETE_NODE 			= 3;
	public static final int ACTION_APPROVE 				= 4;
	public static final int ACTION_REJECT 				= 5;	
	public static final int ACTION_RELEASE_FOR_APPROVAL = 7;
	public static final int ACTION_REASSIGN 			= 9;
	public static final int ACTION_DISCARD_WORKING 		= 10;
	public static final int ACTION_CREATE_WORKING_COPY 	= 11;
	public static final int ACTION_REMOVE 				= 12;		
	public static final int ACTION_CLEAR_METADATA		= 14;
	public static final int ACTION_REQUEST_TP_REPORT	= 15;
	public static final int ACTION_REQUEST_TP_PROOF		= 16;
	public static final int ACTION_EXPORT_VARIANTS 		= 34;
	public static final int ACTION_EXPORT_VARIANT_METADATA 	= 35;
	public static final int ACTION_EXPORT_CONTENT		= 41;

	private String editView;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		User principalUser = UserUtil.getPrincipalUser();
		
		// Filters
		List<AssignmentFilterType> primaryFilterTypes = AssignmentFilterType.listAllStandard();
		referenceData.put("primaryFilterTypes", primaryFilterTypes);
		
		List<SelectionStatusFilterType> selectionStatusFilterTypes = SelectionStatusFilterType.listWorkingActive();
		referenceData.put("selectionStatusFilterTypes", selectionStatusFilterTypes);
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID, -1L);
		int currentSelectionStatusId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);
		Document document = Document.findById(documentId);
		if (document != null) {
			List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
			referenceData.put("locales", langLocales);

			referenceData.put("document", document);
			
			TouchpointSelection touchpointSelection = TouchpointSelection.findById(selectionId);
			// Layout
			Document currentLayout = document;
			if ( touchpointSelection != null && touchpointSelection.getAlternateLayout() != null )
				currentLayout = touchpointSelection.getAlternateLayout();
			currentLayout = UserUtil.getCurrentChannelDocumentContext( currentLayout );

			referenceData.put("currentLayout", currentLayout);
			referenceData.put("layoutSections", currentLayout.getDocumentSectionsByOrder());
			referenceData.put("layoutZones", currentLayout.getSortedVisibleZonesOfUser(false, false));
			
			if(selectionId == -1){
				selectionId = document.getMasterTouchpointSelection().getId();
			}
			
			referenceData.put("currentSelection", touchpointSelection);

			referenceData.put("tpViewRedirect", "touchpoint_content_object_list.form");
			
			referenceData.put("isEnabledForVariantWorkflow", document.isEnabledForVariantWorkflow());
			
			referenceData.put("isMasterSelection", touchpointSelection.isMaster());
			referenceData.put("selectionContext", AsyncSelectionListWrapper.getSelectionListVO(touchpointSelection));
			referenceData.put("hasDefaultTouchpointLanguage", document.hasDefaultTouchpointLanguage());
			
			// Proofs: For actions context menu
			Map<Long, List<Proof>> proofs = new HashMap<>();
			referenceData.put("proofs", proofs);
			Map<Long, Proof> activeProofs = new HashMap<>();
			referenceData.put("activeProofs", activeProofs);
			boolean hasActiveProofs = false;
			for (MessagepointLocale locale : langLocales) {
				List<Proof> langProofs = new ArrayList<>();
				if(!document.isEnabledForVariantWorkflow() || (document.isEnabledForVariantWorkflow() && touchpointSelection.isMaster())) {
					langProofs.addAll(Proof.findAllProofsForLang(touchpointSelection, locale.getLanguageCode(), document.getIsOmniChannel(), -1));
				}else{
					langProofs.addAll(Proof.findAllNoneActiveProofsForLang(touchpointSelection, locale.getLanguageCode(), document.getIsOmniChannel()));
				}
				List<Proof> twoLangProofs = new ArrayList<>();
				for (Proof proof : langProofs) {
					if(proof.getType()==Proof.PROOF_TYPE_VARIANT){
						if (twoLangProofs.size()<2) {
							twoLangProofs.add(proof);
						}else{
							break;
						}
					}
				}
				proofs.put(locale.getId(), twoLangProofs);
				
				// Active proofs: Doesn't apply to Master Variant
				if (!touchpointSelection.isMaster()) {
					List<Proof> activeLangProofs = Proof.findActiveProofsForLang(touchpointSelection, locale.getLanguageCode(), document.getIsOmniChannel());
					Proof oneActiveLangProof = null;
					for (Proof proof : activeLangProofs) {
						if(proof.getType()==Proof.PROOF_TYPE_VARIANT){
							oneActiveLangProof = proof;
							hasActiveProofs = true;
							break;
						}
					}

					if(oneActiveLangProof!=null) {
						activeProofs.put(locale.getId(), oneActiveLangProof);
					}
				}
			}
			referenceData.put("hasActiveProofs", hasActiveProofs);
			referenceData.put("canProofForLanguage", touchpointSelection.getCanProofForLanguage(langLocales));
			// End Proofs
			
			int contextStatusFilterId = -1;
			if ( ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1L) != -1 )
				contextStatusFilterId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);
			else
				if(document.isEnabledForVariantWorkflow())
					contextStatusFilterId = UserUtil.getCurrentSelectionStatusContext().getId();
				else
					contextStatusFilterId = SelectionStatusFilterType.ID_WORKING_COPIES;
			referenceData.put("isStatusViewActive", contextStatusFilterId == SelectionStatusFilterType.ID_ACTIVE);
			
			boolean isSingleSelectorVariant = false;
			if ( document.getSelectionParameterGroup().getParameterGroupItems().size() == 1 && !touchpointSelection.isMaster() )
				isSingleSelectorVariant = true;
			referenceData.put("isSingleSelectorVariant", isSingleSelectorVariant);
			
			boolean isSingleSelectorMaster = false;
			if ( document.getSelectionParameterGroup().getParameterGroupItems().size() == 1 && touchpointSelection.isMaster() )
				isSingleSelectorMaster = true;
			referenceData.put("isSingleSelectorMaster", isSingleSelectorMaster);
			
			referenceData.put("isAccessRestricted", !touchpointSelection.isVisible(principalUser));
			
			boolean displaySegmentationAnalysis = document.isSegmentationAnalysisEnabled() && UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW);
			referenceData.put("displaySegmentationAnalysis", displaySegmentationAnalysis);	
			
			Boolean applyConnectedVisibility = document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection();
			referenceData.put("applyConnectedVisibility", applyConnectedVisibility);

			referenceData.put("isEmailTouchpoint", document.getIsEmailTouchpoint() || UserUtil.getCurrentChannelDocumentContext(document).getIsEmailTouchpoint());
		}

		String requestParameters = HttpRequestUtil.getParamsFromMap( getSuccessViewParams(request, documentId, selectionId, currentSelectionStatusId) );
		if (requestParameters != null && !requestParameters.trim().isEmpty()) {
			referenceData.put("requestParameters", requestParameters);
		}
		
		AuditReport latestAuditReport = AuditReport.findLatestInternalByUserId(principalUser.getId(), AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT);
		if (latestAuditReport != null) {
			referenceData.put("auditReport", latestAuditReport);
		}

		String[] hours = {"00:00","01:00","02:00","03:00","04:00","05:00","06:00","07:00","08:00","09:00","10:00",
				"11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"};
		referenceData.put("hours", hours);

		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );

		// Task metadata form definition
		referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
		referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(TouchpointSelectionListFilterType.class,
                new StaticTypeIdCustomEditor<>(TouchpointSelectionListFilterType.class));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		log.debug("*** Begin formBackingObject***");	
    	HttpRequestUtil.saveBackToListURL(request, HttpRequestUtil.getAllParamenterFromRequest(request), HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_TP_SELECTION);		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		if (documentId != -1) {
			Document document = getDocument(request);
			
			if (document != null && document.isEnabledForVariation()) {
				TouchpointVariantListWrapper wrapper = new TouchpointVariantListWrapper(documentId);
				String	exportName = document.getName();
				if (exportName != null)
		        	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
		        wrapper.setExportName(exportName);
				return wrapper;
			}
		}
		log.debug("*** Begin list service ***");		
		return new TouchpointVariantListWrapper(documentId);
		
    }

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
		long selectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);
		int currentSelectionStatusId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);
		int targetSelectionStatusId = ServletRequestUtils.getIntParameter(request, REQ_PARAM_TARGET_SELECTION_STATUS_ID, -1);
		
		// No licence for variant management, redirect to message list page.
		if(!MessagepointLicenceManager.getInstance().isLicencedForVariantManagement()){
			return new ModelAndView(new RedirectView(request.getContextPath() +  "/touchpoints/touchpoint_content_object_list.form"));
		}
		
		if (documentId != -1 && documentId != -2 && selectionId != -1) {
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParams(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;
			
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS, "touchpoint_variant_list.form");
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(documentId));

			// Verify selection's accessibility
			TouchpointSelection selection = TouchpointSelection.findById(selectionId);
			if( selection == null || selection.isDeleted() || selection.getDocument() == null ){
				Map<String, Object> params = getSuccessViewParams(request);
				// Clear selection ID
				params.remove(REQ_PARAM_TOUCHPOINT_SELECTION_ID);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}			
			// Ensure Touchpoint and Selection are in sync (multi-window usage can cause miss match)
			if (documentId == selection.getDocument().getId()){
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(selectionId));
			}else{
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(Document.findById(documentId).getMasterTouchpointSelection() != null ?
																										Document.findById(documentId).getMasterTouchpointSelection().getId() : -1));
			}
			
			// Ensure selection status is in sync (Drill down may require context status change)
			if (targetSelectionStatusId != -1)
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(targetSelectionStatusId));
			else
			{
				Document document = Document.findById(documentId);
				if (document != null && !document.isEnabledForVariantWorkflow())
				{
					contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(TouchpointContentObjectListController.VIEW_WORKING_COPY));
				}
			}
			
			UserUtil.updateUserContextAttributes(contextAttr);
		}
		// ******* End User context persist/recall **************
		
		boolean idUpdated = false;
		
		Document doc = Document.findById(documentId);
		if( doc == null ) {
			doc = getDefaultTouchpoint();
			if (doc != null) {
				documentId = doc.getId();
				idUpdated = true;
			} else {
				Map<String, Object> params = new HashMap<>();
				params.put(REQ_PARAM_DOCUMENTID, -2);
				return new ModelAndView(new RedirectView(request.getContextPath() +  "/touchpoints/touchpoint_content_object_list.form"), params);
			}			
		}
		
		TouchpointSelection tpSelection = TouchpointSelection.findById(selectionId);
		if(tpSelection == null){
			// Prefer selection context (visibility managed)
			TouchpointSelection selectionContext = UserUtil.getCurrentSelectionContext();
			if ( selectionContext != null && selectionContext.getDocument() != null && doc.getId() == selectionContext.getDocument().getId() )
				tpSelection = selectionContext;
			else
				tpSelection = doc.getMasterTouchpointSelection();
			if(tpSelection != null){
				selectionId = tpSelection.getId();
				idUpdated = true;
			} else {
				Map<String, Object> params = new HashMap<>();
				params.put(REQ_PARAM_DOCUMENTID, -2);
				return new ModelAndView(new RedirectView(request.getContextPath() +  "/touchpoints/touchpoint_content_object_list.form"), params);
			}
		}
		
		if(idUpdated){
			return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, selectionId, currentSelectionStatusId));
		}else{
			// Audit (Page access: Variants list)
			AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_NONE, null, null, AuditActionType.ID_VARIANTS_LIST, null);			
			return super.handleRequestInternal(request, response);
		}
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.SelectionsList);

		try {
			int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
			int currentSelectionStatusId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);

			TouchpointVariantListWrapper command = (TouchpointVariantListWrapper) commandObj;

			User principalUser = UserUtil.getPrincipalUser();
			User requestor = User.findById(principalUser.getId());

			switch (action) {
				case (ACTION_CREATE_NEW_NODE): {
					analyticsEvent.setAction(Actions.Create);
					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					TouchpointSelection touchpointSelection = touchpointSelections.get(0);
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForNew(touchpointSelection.getId(),
																												   command.getSelectionName(),
																												   principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" new tree node was not created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						
						return super.showForm(request, response, errors);
					} else {
						if((touchpointSelection.getDocument().isEnabledForVariation() && !touchpointSelection.getDocument().isEnabledForVariantWorkflow())&& !touchpointSelection.getDocument().isTpContentChanged()){
							touchpointSelection.getDocument().setTpContentChanged(true);
							touchpointSelection.getDocument().save();
						}
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_RENAME_NODE): {
					analyticsEvent.setAction(Actions.Rename);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					TouchpointSelection touchpointSelection = touchpointSelections.get(0);
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForRenameNode(touchpointSelection.getId(),
																															 command.getSelectionName(),
																															 principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" tree node was not renamed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_DISCARD_WORKING): {
					analyticsEvent.setAction(Actions.DiscardWorkingCopy);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForDiscardWorkingCopy(touchpointSelections, principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME,
									CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" working copy was not discarded. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(
								context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						if (isContextMenuAction(request)) {
							TouchpointSelection currentSelection = touchpointSelections
									.get(0);
							if (!currentSelection.isMaster()) {
								currentSelection = currentSelection.getParent();
								return new ModelAndView(new RedirectView(
										getSuccessView()), getSuccessViewParams(
										request, documentId, currentSelection.getId(),
										currentSelectionStatusId));
							}
						}
						
						return new ModelAndView(new RedirectView(getSuccessView()),
								getSuccessViewParams(request));
					}
				} case (ACTION_DELETE_NODE): {
					analyticsEvent.setAction(Actions.Delete);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForDeleteNode(touchpointSelections, principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" tree node was not deleted. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						TouchpointSelection currentSelection = touchpointSelections.get(0);
						if((currentSelection.getDocument().isEnabledForVariation() && !currentSelection.getDocument().isEnabledForVariantWorkflow())&& !currentSelection.getDocument().isTpContentChanged()){
							currentSelection.getDocument().setTpContentChanged(true);
							currentSelection.getDocument().save();
						}
						if(isContextMenuAction(request)){
							//TouchpointSelection currentSelection = touchpointSelections.get(0);
							if(!currentSelection.isMaster()){
								currentSelection = currentSelection.getParent();
								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, currentSelection.getId(), currentSelectionStatusId));
							}
						}
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_REASSIGN): {
					analyticsEvent.setAction(Actions.Reassign);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForReassign(touchpointSelections, command.getUserNote(), command.getAssignedToUser(), principalUser);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" reassignment was not completed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_RELEASE_FOR_APPROVAL): {
					analyticsEvent.setAction(Actions.ReleaseForApproval);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), touchpointSelections.toArray(new TouchpointSelection[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" release for approval was not completed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						// This recalculation of the hash is NOT necessary here because it is (it should be) recalculated in WorkflowReleaseForApprovalService
						// Since it was running twice, it was adding 20 seconds to variant approval for Conduit client
                        // TouchpointSelectionUtil.updateContentObjectHash(new HashSet<>(touchpointSelections));

						if(isContextMenuAction(request)){
							TouchpointSelection currentSelection = touchpointSelections.get(0);
							if(currentSelection.isActive()){
								HashMap<String,String> contextAttr = new HashMap<>();
								contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(SelectionStatusFilterType.ID_ACTIVE));
								UserUtil.updateUserContextAttributes(contextAttr);
								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, currentSelection.getId(), SelectionStatusFilterType.ID_ACTIVE));
							}
						}
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_APPROVE): {
					analyticsEvent.setAction(Actions.Approve);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, command.getUserNote(), false,  touchpointSelections.toArray(new TouchpointSelection[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						// This recalculation of the hash is NOT necessary here because it is (it should be) recalculated in WorkflowApproveService
						// Since it was running twice, it was adding 20 seconds to variant approval for Conduit client
						// TouchpointSelectionUtil.updateContentObjectHash(new HashSet<>(touchpointSelections));

						if(isContextMenuAction(request)){
							TouchpointSelection currentSelection = touchpointSelections.get(0);
							if(currentSelection.isActive()){
								HashMap<String,String> contextAttr = new HashMap<>();
								contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(SelectionStatusFilterType.ID_ACTIVE));
								UserUtil.updateUserContextAttributes(contextAttr);
								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, currentSelection.getId(), SelectionStatusFilterType.ID_ACTIVE));
							}
						}
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_REJECT): {
					analyticsEvent.setAction(Actions.Reject);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, command.getAssignedToUser(), command.getUserNote(), touchpointSelections.toArray(new TouchpointSelection[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_CREATE_WORKING_COPY): {
					analyticsEvent.setAction(Actions.CreateWorkingCopy);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForCreateWorkingCopy(touchpointSelections, requestor);

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" working copies not created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						if(isContextMenuAction(request)){
							TouchpointSelection currentSelection = touchpointSelections.get(0);
							if(currentSelection.isWIP()){
								HashMap<String,String> contextAttr = new HashMap<>();
								contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(SelectionStatusFilterType.ID_WORKING_COPIES));
								UserUtil.updateUserContextAttributes(contextAttr);
								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, currentSelection.getId(), SelectionStatusFilterType.ID_WORKING_COPIES));
							}
						}
						
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_REMOVE): {
					analyticsEvent.setAction(Actions.Remove);

					TouchpointSelection touchpointSelection = command.getSelectedList().iterator().next();
					ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForRemoveNode(new ArrayList<>(Collections.singletonList(touchpointSelection)), requestor, command.getUserNote());
					String name = touchpointSelection.getName();

					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						if(!touchpointSelection.getDocument().isTpContentChanged()){
							touchpointSelection.getDocument().setTpContentChanged(true);
							touchpointSelection.getDocument().save();
						}
						if(isContextMenuAction(request)){
							TouchpointSelection currentSelection = touchpointSelection;
							if(!currentSelection.isMaster()){
								currentSelection = currentSelection.getParent();
								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, documentId, currentSelection.getId(), currentSelectionStatusId));
							}
						}
						
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				} case (ACTION_REQUEST_TP_REPORT) : {
					analyticsEvent.setAction(Actions.RequestProofTouchpointReport);

					AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Touchpoint);
					auditEvent.setAction(Actions.GenerateReport);
					try {
						ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForCreate(requestor.getId(), AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT);
						Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
						service.execute(context);
						ServiceResponse serviceResponse = context.getResponse();
						if (!serviceResponse.isSuccessful()) {
							StringBuilder sb = new StringBuilder();
							sb.append(CreateOrUpdateAuditReportService.SERVICE_NAME);
							sb.append(" service call is not successful ");
							sb.append(" in ").append(this.getClass().getName());
							sb.append(" requestor = ").append(requestor.getUsername());
							sb.append(" AuditReport object could not be created. ");
							log.error(sb.toString());
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return super.showForm(request, response, errors);
						} else {
							long auditReportId = (Long) serviceResponse.getResultValueBean();
							AuditReportType type = new AuditReportType(AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT);
							Date fromDate = command.getFromDate();
							Date toDate = command.getToDate();
							boolean changeReportEnabled = command.getChangeReportEnabled();
							if (!changeReportEnabled) {
								fromDate = null;
								toDate = null;
							}
							Document document = HibernateUtil.getManager().getObject(Document.class, documentId);
							auditEvent.add(AuditReportEvents.Properties.ContextName, () -> document.getName());
							context = GenerateTouchpointAuditReportService.createContext(auditReportId, document, requestor, fromDate, toDate);
							service = MessagepointServiceFactory.getInstance().lookupService(GenerateTouchpointAuditReportService.SERVICE_NAME, GenerateTouchpointAuditReportService.class);
							service.execute(context);
							serviceResponse = context.getResponse();

							if (!serviceResponse.isSuccessful()) {
								context = CreateOrUpdateAuditReportService.createContextForError(auditReportId);
								service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
								service.execute(context);

								// Audit (Audit Report Failed)
								AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_FAIL, null);

								StringBuilder sb = new StringBuilder();
								sb.append(GenerateTouchpointAuditReportService.SERVICE_NAME);
								sb.append(" service call is not successful ");
								sb.append(" in ").append(this.getClass().getName());
								sb.append(" requestor = ").append(requestor.getUsername());
								sb.append(" AuditReport object could not be generated. ");
								log.error(sb.toString());
								ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
								return super.showForm(request, response, errors);
							} else {
								// Audit (Audit Report Success)
								AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_SUCCEED, null);

								return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
							}
						}
					}finally {
						auditEvent.send();
					}
				}
				case (ACTION_REQUEST_TP_PROOF): {
					analyticsEvent.setAction(Actions.RequestTouchpointProof);

					List<TouchpointSelection> touchpointSelections = command.getSelectedList();
					TouchpointSelection touchpointSelection = touchpointSelections.get(0);
					long languageCode = ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID, UserUtil.getCurrentLanguageLocaleContext().getId());
					int selectionStatus = UserUtil.getCurrentSelectionStatusContext().getId();
					if(touchpointSelection.isMaster()){
						//since if isMaster somehow it's always set to active but we need to request working copy proof for master.
						selectionStatus = SelectionStatusFilterType.ID_WORKING_COPIES;
					}
					Long channelContextId = null;
					if ( touchpointSelection.getDocument().getIsOmniChannel() )
						channelContextId = UserUtil.getCurrentChannelContext().getId();
					ServiceExecutionContext context = CreateTouchpointVariantProofService.createContext(touchpointSelection.getId(), languageCode, UserUtil.getPrincipalUser(), selectionStatus,
							channelContextId, null, null, null);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateTouchpointVariantProofService.SERVICE_NAME, CreateTouchpointVariantProofService.class);
					service.execute(context);
					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking CreateTouchpointVariantProofService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						// Audit (Audit proof requested)
						AuditEventUtil.push(requestor, AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_VARIANT, 
								touchpointSelection.getName(), touchpointSelection.getId(), AuditActionType.ID_CHANGES, ApplicationUtil.getMessage("page.label.Proof"));
						
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_EXPORT_VARIANTS): {
					/**
					 * ACTION_EXPORT_VARIANTS
					 */

					analyticsEvent.setAction(Actions.ExportVariants);


					ExportImportOptions exportOptions = new ExportImportOptions();
					exportOptions.setIncludeImagePathOnly(true);
					exportOptions.setExportExtType(command.getExtType());
					exportOptions.setIncludeExistingUsers(true);
					ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(
							command.getExportId(),
							documentId,
							ContentObject.DATA_TYPE_WORKING,
							null,
							exportOptions,
							TouchpointSelection.class,
							UserUtil.getPrincipalUser()
							);
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(GenerateMessagepointObjectExportService.SERVICE_NAME,
									GenerateMessagepointObjectExportService.class);
					service.execute(context);


					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking GenerateMessagepointObjectExportService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_EXPORT_VARIANT_METADATA): {
					/**
					 * ACTION_EXPORT_VARIANT_METADATA
					 */

					analyticsEvent.setAction(Actions.ExportVariantMetadata);

					ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(command.getExportId(),
							documentId,
							true,
							MetadataFormDefinition.class,
							UserUtil.getPrincipalUser(),
							1); // 0 = xml 1 = excel
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(GenerateMessagepointObjectExportService.SERVICE_NAME,
									GenerateMessagepointObjectExportService.class);
					service.execute(context);

					if (!context.getResponse().isSuccessful()) {
						log.error(" unexpected exception when invoking GenerateMessagepointObjectExportService execute method");
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_CLEAR_METADATA): {
					TouchpointSelection touchpointSelection = command.getSelectedList().iterator().next();
					ServiceExecutionContext context = ClearMetadataService.createContext(requestor, touchpointSelection.getId(), ClearMetadataService.OBJECT_TYPE_VARIANT);
					Service extendDeliveryService = MessagepointServiceFactory.getInstance().lookupService(ClearMetadataService.SERVICE_NAME, ClearMetadataService.class);
					extendDeliveryService.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						return showForm(request, response, errors);
					} else {
						// Audit (Audit clear metadata)
						AuditEventUtil.push(requestor, AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_VARIANT, 
								touchpointSelection.getName(), touchpointSelection.getId(), AuditActionType.ID_CHANGES, ApplicationUtil.getMessage("page.label.clear.metadata"));
						
						return new ModelAndView( new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_EXPORT_CONTENT):{
					TouchpointSelection touchpointSelection = command.getSelectedList().iterator().next();

					ExportContentBackgroundTask task = new ExportContentBackgroundTask(
							command.getExportId(),
							touchpointSelection.getId(),
							command.isIncludeOnlyActiveContent(),
							UserUtil.getPrincipalUser());
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY, UserUtil.getPrincipalUser());

					return new ModelAndView( new RedirectView(getSuccessView()), getSuccessViewParams(request));
				}
				default:
					break;
				}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request){
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID, -1L);
		int currentSelectionStatusId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);		
		return getSuccessViewParams(request, documentId, selectionId, currentSelectionStatusId);
	}
	
	private Map<String, Object> getSuccessViewParams(HttpServletRequest request, long documentId, long selectionId, int currentSelectionStatusId) {
		Map<String, Object> params = new HashMap<>();
		long viewid = ServletRequestUtils.getLongParameter(request, REQ_PARAM_VIEWID, -1L);
		
		if(viewid != -1L){
			params.put(REQ_PARAM_VIEWID, viewid);
		}
		if(documentId != -1L){
			params.put(REQ_PARAM_DOCUMENTID, documentId);
		}
		if(selectionId != -1L){
			params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, selectionId);
		}
		if(currentSelectionStatusId != -1L){
			params.put(AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, currentSelectionStatusId);
		}
		
		if(isContextMenuAction(request)){
			params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		}
		
		return params;
	}

	private Document getDefaultTouchpoint() {
		if (!(UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW) || UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW)))
			return null;
		List<Document> visibleDocuments = Document.findAllDocumentsAndProjectsVisible();
		for (Document document: visibleDocuments)
			if (document.isEnabledForVariation())
				return document;
		return null;
	}

	protected static Document getDocument(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1);
		Document document = HibernateUtil.getManager().getObject(Document.class, id);
		return document;
	}

	public String getEditView() {
		return editView;
	}
	public void setEditView(String editView) {
		this.editView = editView;
	}	

	private boolean isContextMenuAction(HttpServletRequest request){
		String url = request.getRequestURI();
		if(url.contains("selection_actns_context_menu.form")){
			return true;
		}else{
			return false;
		}
	}
	
	public ModelAndView processFormSubmission(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{		
		ModelAndView mv = super.processFormSubmission(request, response, command, errors);
		if(errors.hasErrors() && isContextMenuAction(request)){
			mv.getModelMap().put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, "error");
		}			
		return mv;
	}	
}