package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.CloneEvents;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.content.GlobalContentListController;
import com.prinova.messagepoint.controller.tpadmin.TouchpointContentSelectionViewController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskType;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUtilities;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportMessagepointObjectBackgroundTask;
import com.prinova.messagepoint.platform.services.content.*;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateMessageAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateMessagepointObjectExportService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.platform.services.whereused.CreateWhereUsedReportService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

public class TouchpointContentObjectListController extends MessagepointController {

	private String messageEditRedirect;
	
	private static final Log log = LogUtil.getLog(TouchpointContentObjectListController.class);
	
	public static final String REQ_PARM_ZONEID 					= "zoneId";
	public static final String REQ_PARM_SECTIONID 				= "sectionId";
	public static final String REQ_PARM_DOCUMENTID 				= "documentId";
	public static final String REQ_PARM_SELECTION_ID 			= "touchpointSelectionId";
	public static final String REQ_PARM_TYPE 					= "type";
	public static final String REQ_PARM_ACTION 					= "action";
	public static final String REQ_PARM_VIEWID 					= "viewId";
	public static final String REQ_PARM_PARAM_INST_ID			= "paramInstId";
	public static final String REQ_PARM_STATUS_VIEW_ID			= "statusViewId";
	public static final String REQ_PARM_CONTENT_SELECTION_ID 	= "contentSelectionId";
	public static final String REQ_PARM_STATUS_FILTER_ID 		= "statusFilterId";
	public static final String REQ_PARM_RESET					= "reset";
	public static final String REQ_LOCAL_CONTEXT				= "localContext";
	public static final String REQ_IS_SHARED_CANVAS				= "isFreeform";
	public static final String REQ_PARM_EXPORT_EXT 				= "ext";
	public static final String SESSION_PARAM_REDIRECT_DOC_ID = "redirectDocId";

	public static final int ACTION_UPDATE 						= 1;
	public static final int ACTION_CLONE 						= 2;
	public static final int ACTION_REASSIGN 					= 3;
	public static final int ACTION_CREATE_WORKING_COPY 			= 4;
	public static final int ACTION_DISCARD_WORKING 				= 5;
	public static final int ACTION_ARCHIVE 						= 6;
	public static final int ACTION_DELETE_ARCHIVE 				= 7;
	public static final int ACTION_UPDATE_FILTER 				= 8;
	public static final int ACTION_RELEASE_FOR_APPROVAL 		= 9;
	public static final int ACTION_ACTIVATE						= 10;
	public static final int ACTION_CREATE_MESSAGE 				= 15;
	public static final int ACTION_EXPORT 						= 16;
	public static final int ACTION_APPROVE						= 17;
	public static final int ACTION_REJECT						= 18;
	public static final int ACTION_HOLD 						= 24;
	public static final int ACTION_UNHOLD 						= 25;
	public static final int ACTION_SUPPRESS						= 26;
	public static final int ACTION_RESTORE						= 27;
	public static final int ACTION_WHERE_USED 					= 28;
	public static final int ACTION_MAKE_GLOBAL					= 29;
	public static final int ACTION_MOVE_TO_GLOBAL				= 30;
	public static final int ACTION_PUSH_TO_GLOBAL				= 31;
	public static final int ACTION_PULL_FROM_GLOBAL				= 32;
	public static final int ACTION_ABORT						= 40;
	public static final int ACTION_TRANSLATION_REASSIGN 		= 41;
	public static final int ACTION_RELEASE_FROM_TRANSLATION 	= 42;
	public static final int ACTION_CREATE_JSON_FOR_TRANSLATION 	= 43;
	public static final int ACTION_RETRY_TRANSLATION 			= 45;
	public static final long REF_NO_SELECTION_PARM_ID 		= 0;

	public static final int VIEW_WORKING_COPY 				= 1;
	public static final int VIEW_ACTIVE 					= 2;
	
	public String getMessageEditRedirect() {
		return messageEditRedirect;
	}
	public void setMessageEditRedirect(String messageEditRedirect) {
		this.messageEditRedirect = messageEditRedirect;
	}

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		User principalUser = UserUtil.getPrincipalUser();

		// Retrieve the available document sections
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		long localContext = ServletRequestUtils.getLongParameter(request, REQ_LOCAL_CONTEXT, -1L);
		boolean isSharedCanvas = ServletRequestUtils.getBooleanParameter(request, REQ_IS_SHARED_CANVAS, false);
		
		boolean isLocalContext = localContext > 0;
				
		Document document = Document.findById(documentId);
		if (document != null) {
			
			referenceData.put("sharedCanvasNotApplicable", isSharedCanvas && !document.isNativeCompositionTouchpoint());
			
			referenceData.put("isVariantTouchpoint", document.isEnabledForVariation()); 
			referenceData.put("isEnabledForVariantWorkflow", document.isEnabledForVariantWorkflow());
			referenceData.put("hasDefaultTouchpointLanguage", document.hasDefaultTouchpointLanguage());

			boolean isMasterSelection = document.isEnabledForVariation();
			TouchpointSelection touchpointSelection = UserUtil.getCurrentSelectionContext();
			long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PARM_SELECTION_ID, -1L);
			if (selectionId != -1) {
				touchpointSelection = TouchpointSelection.findById(selectionId);
			}
			if (touchpointSelection != null)
				isMasterSelection = touchpointSelection.isMaster();
			referenceData.put("isMasterSelection", isMasterSelection);
			
			referenceData.put("currentSelection", touchpointSelection);
			
			referenceData.put("document", document);
			
			// Layout
			if ( !isLocalContext ) {
				Document currentLayout = document;
				if ( touchpointSelection != null && touchpointSelection.getAlternateLayout() != null )
					currentLayout = touchpointSelection.getAlternateLayout();
				currentLayout = UserUtil.getCurrentChannelDocumentContext( currentLayout );
				
				referenceData.put("currentLayout", currentLayout);
				referenceData.put("layoutSections", currentLayout.getDocumentSectionsByOrder());
				referenceData.put("layoutZones", currentLayout.getSortedVisibleZonesOfUser(false, false));
			}
			
			// Filters
			List<AssignmentFilterType> primaryFilterTypes = AssignmentFilterType.listAllIncludingVariant();
			if( !document.isEnabledForVariation() )
				primaryFilterTypes = AssignmentFilterType.listAllStandard();
			referenceData.put("primaryFilterTypes", primaryFilterTypes);
			
			List<VersionStatusFilterType> messageStatusFilterTypes = VersionStatusFilterType.listAll();
			referenceData.put("messageStatusFilterTypes", messageStatusFilterTypes);

			List<ContentTypeFilterType> contentTypeFilterTypes = ContentTypeFilterType.listAll();
			referenceData.put("contentTypeFilterTypes", contentTypeFilterTypes);
			
			List<SelectionStatusFilterType> selectionStatusFilterTypes = SelectionStatusFilterType.listWorkingActive();
			referenceData.put("selectionStatusFilterTypes", selectionStatusFilterTypes);
			
			referenceData.put("userHasTouchpointSelectionContentViewPermission", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW));
	
			String requestParameters = getRequestParameters(request);
			if (requestParameters != null && !requestParameters.trim().isEmpty()) {
				referenceData.put("requestParameters", requestParameters);
			}

			int auditTypeId = getAuditReportTypeId(request);
			AuditReport latestAuditReport = AuditReport.findLatestByUserId(principalUser.getId(), auditTypeId);
			if (latestAuditReport != null) {
				referenceData.put("auditReport", latestAuditReport);
			}
			
			referenceData.put("touchpointContext", document);
			
			int contextStatusFilterId = -1;
			if ( ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1L) != -1 )
				contextStatusFilterId = ServletRequestUtils.getIntParameter(request, AsyncListTableController.PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);
			else
				contextStatusFilterId = UserUtil.getCurrentSelectionStatusContext().getId();
			referenceData.put("isStatusViewActive", contextStatusFilterId == SelectionStatusFilterType.ID_ACTIVE);

			boolean isContextActiveStatusValid = true;
			if ( document.isEnabledForVariantWorkflow() && !isMasterSelection && contextStatusFilterId == SelectionStatusFilterType.ID_ACTIVE && touchpointSelection != null && !touchpointSelection.getHasActiveCopy() )
				isContextActiveStatusValid = false;
			referenceData.put("isContextActiveStatusValid", isContextActiveStatusValid);
			
			boolean isContextWorkingCopyStatusValid = true;
			if ( document.isEnabledForVariantWorkflow() && !isMasterSelection && contextStatusFilterId == SelectionStatusFilterType.ID_WORKING_COPIES && touchpointSelection != null && !(touchpointSelection.isWIP() || touchpointSelection.isAwaitingApproval()) )
				isContextWorkingCopyStatusValid = false;
			referenceData.put("isContextWorkingCopyStatusValid", isContextWorkingCopyStatusValid);
			
			if (!isMasterSelection && touchpointSelection != null && document.isEnabledForVariantWorkflow())
			{
				if (contextStatusFilterId == SelectionStatusFilterType.ID_ACTIVE || !touchpointSelection.hasWorkingCopy())
					referenceData.put("canUpdateVariant", false);
				else
					referenceData.put("canUpdateVariant", touchpointSelection.canUpdate(principalUser));
			}
			else
				referenceData.put("canUpdateVariant", true);
			
			boolean displaySegmentationAnalysis = document.isSegmentationAnalysisEnabled() && UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW);
			referenceData.put("displaySegmentationAnalysis", displaySegmentationAnalysis);
		} else {
            referenceData.put("noTouchpoints", true);
            referenceData.put("noVisibleTouchpoints", true);
		}
		
		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );
		
		boolean hasMessageEditPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_EDIT);
		referenceData.put("hasMessageEditPermission", hasMessageEditPermission );
		
		referenceData.put("hasMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_MESSAGE).isEmpty());
		referenceData.put("metadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_MESSAGE));

		// Task metadata form definition
		referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
		referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

		List<Zone> placeholders = new ArrayList<>();
		if ( isLocalContext )
			placeholders = Zone.findPlaceholdersByDocumentId(document);
		referenceData.put("placeholders", placeholders);

		referenceData.put("default_css", ContentStyleUtils.generateDefaultCSS(ContentStyleUtils.CONTENT_TYPE_VIEW));

		referenceData.put("showContentJSONExport", FeatureFlag.isEnabled(FeatureFlag.Features.ContentJsonExport, request));
		List<MessagepointLocale> locales;
		if (document != null) {
			locales = document.getTouchpointLanguagesAsLocales();
			locales.remove(0); // The first locale is always the default locale
		}
		else {
			locales = MessagepointLocale.getFavouriteLanguages();
		}
		referenceData.put("exportTargetLocales", locales);

		boolean requiresDescriptionOnTasks = Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.GeneralSettings.KEY_RequireDescriptionOnTasks));
		referenceData.put("requiresDescriptionOnTasks", requiresDescriptionOnTasks);
		referenceData.put("taskTypes", TaskType.listAll().stream().sorted(Comparator.comparing(TaskType::getName)).collect(Collectors.toList()));
		referenceData.put("messagepointLocales", document != null ? document.getTouchpointLanguagesAsLocales().stream().sorted(Comparator.comparing(MessagepointLocale::getName)).collect(Collectors.toList()) : new ArrayList<>());

		return referenceData;
	}

	private String getRequestParameters(HttpServletRequest request) {
		String parameters = "";

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		if (documentId != -1) {
			parameters += "," + REQ_PARM_DOCUMENTID;
		}

		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		if (action != -1) {
			parameters += "," + REQ_PARM_ACTION;
		}

		if (parameters != null && parameters.trim().startsWith(",")) {
			parameters = parameters.trim().substring(1);
		}
		
		return parameters;
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		
		Document document = Document.findById(documentId);
		TouchpointContentObjectListWrapper wrapper = new TouchpointContentObjectListWrapper();
		String	exportName = "";
		if(document != null){
			exportName = document.getName();
		}
		 
		if (exportName == null || !exportName.isEmpty())
        	exportName = exportName.replaceAll(StringUtil.injectI18Nchars("[^a-zA-Z0-9]"), "");
        wrapper.setExportName(exportName);
		return wrapper;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(ParameterGroup.class, new IdCustomEditor<>(ParameterGroup.class));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(ContentObject.class, new IdCustomEditor<>(ContentObject.class));
		binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
		binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(ConfigurableWorkflow.class, new IdCustomEditor<>(ConfigurableWorkflow.class));
		binder.registerCustomEditor(Task.class, new IdCustomEditor<>(Task.class));
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
			BindException errors) throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PARM_SELECTION_ID, -1L);
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		long localContext = ServletRequestUtils.getLongParameter(request, REQ_LOCAL_CONTEXT, -1L);
		String isSharedCanvas = ServletRequestUtils.getStringParameter(request, REQ_IS_SHARED_CANVAS, "");
		
		TouchpointContentObjectListWrapper c = (TouchpointContentObjectListWrapper) command;
		List<ContentObject> selectedContentObjects = c.getSelectedList();
		ContentObject selectedContentObject = null;
		if (selectedContentObjects != null && !selectedContentObjects.isEmpty())
			selectedContentObject = selectedContentObjects.iterator().next();

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.MessageList);
		analyticsEvent.add(TouchpointEvents.Properties.TouchpointContext, () -> UserUtil.getCurrentTouchpointContext().getName());

		try {
			long principalUserId = UserUtil.getPrincipalUserId();
			User requestor = User.findById(principalUserId);

			switch (action) {
			case (ACTION_UPDATE): {
				/**
				 * ACTION_UPDATE: - pick the first selected message and redirect to the edit page of that message instance
				 */
				analyticsEvent.add(TouchpointEvents.Properties.MessageInstance, selectedContentObject::getName);
				analyticsEvent.setAction(Actions.Update);

				Map<String, Object> parms = new HashMap<>();
				parms.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, Long.valueOf(selectedContentObject.getId()));
				parms.put("statusViewId", 1);

				String cfSource = request.getParameter(REQUEST_PARAM_CUSTOM_FORM_SOURCE);

				if(cfSource != null)
					parms.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, cfSource);

				return new ModelAndView(new RedirectView(getMessageEditRedirect()), parms);
			}
			case (ACTION_CLONE): {
				/**
				 * ACTION_CLONE: - clone the first selected content object and redirect to the edit page of the new cloned
				 * content object
				 */

				AnalyticsEvent<CloneEvents> cloneEvent = AnalyticsUtil.requestFor(CloneEvents.Message);
				cloneEvent.setAction(Actions.Clone);
				cloneEvent.add(CloneEvents.Properties.MessageCount, () -> 1);

				try {
					CloneHelper.setIsCloning(CloneHelper.ID_CLONING_TYPE_LOCAL);
					ServiceExecutionContext context = ContentObjectCloneService.createContext(UserUtil.getCurrentTouchpointContext().getId(), selectedContentObject.getId(), c.getCloneToSelectionId(), requestor, c.getCloneName());
					Service service = MessagepointServiceFactory.getInstance().lookupService(ContentObjectCloneService.SERVICE_NAME, ContentObjectCloneService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					CloneHelper.setIsCloning(CloneHelper.ID_CLONING_TYPE_INACTIVE);

					analyticsEvent.setAction(Actions.Clone);

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(ContentObjectCloneService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" id=").append(selectedContentObject.getId());
						sb.append("  Content Object is not cloned ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						cloneEvent.add(CloneEvents.Properties.Successful, () -> false);
						return showForm(request, response, errors);
					} else {
						cloneEvent.add(CloneEvents.Properties.Successful, () -> true);
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
					}
				} finally {
					cloneEvent.send();
				}

			}
			case (ACTION_REASSIGN): {
				/**
				 * ACTION_REASSIGN: - reassign working copy messages to another user
				 *
				 */
				analyticsEvent.setAction(Actions.Reassign);

				ServiceExecutionContext context = UnlockContentObjectService.createReassignContext(selectedContentObjects,
						requestor.getId(),
						c.getAssignedToUser().getId(),
						c.getUserNote(),false);
				Service service = MessagepointServiceFactory.getInstance().lookupService(UnlockContentObjectService.SERVICE_NAME,
						UnlockContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(UnlockContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  Content Object is not reassigned. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_TRANSLATION_REASSIGN): {
				/**
				 * ACTION_TRANSLATION_REASSIGN: - reassign messages to another translator
				 *
				 */
				analyticsEvent.setAction(Actions.Reassign);
				ConfigurableWorkflow actionToWorkflow = c.getActionToWorkflow();
				ServiceExecutionContext context = ReassignContentObjectForTranslationService.createReassignContext(selectedContentObjects,
						requestor.getId(),
						c.getAssignedToUser().getId(),
						actionToWorkflow,
						c.getUserNote());
				Service service = MessagepointServiceFactory.getInstance().lookupService(ReassignContentObjectForTranslationService.SERVICE_NAME,
						ReassignContentObjectForTranslationService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(ReassignContentObjectForTranslationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  Content Object is not reassigned for translation. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_CREATE_WORKING_COPY): {
				/**
				 * ACTION_CREATE_WORKING_COPY: - for each of the selected instance, create working copy for each
				 *
				 */
				analyticsEvent.setAction(Actions.CreateWorkingCopy);

				ServiceExecutionContext context = BulkCheckoutContentObjectService.createContextForTaskCreation(selectedContentObjects, requestor, c.getTaskDescriptionMap(), c.getTaskLinkedMap(), c.getTaskTypeMap(), c.getTaskLocaleMap(), c.getTaskVariantMap());
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkCheckoutContentObjectService.SERVICE_NAME,
						BulkCheckoutContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.updateContentForModel(false, sco, sco.getContentObjectData());
					}
				}
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkCheckoutContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  working copies not created. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_DISCARD_WORKING): {
				/**
				 * ACTION_DISCARD_WORKING: - for each of the selected instance, abort the working copy
				 *
				 */
				analyticsEvent.setAction(Actions.DiscardWorkingCopy);
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.removeContentForModel(false, sco, sco.getContentObjectData());
					}
				}
				String userNote = c.getUserNote();
				ServiceExecutionContext context = BulkDeleteWorkingContentObjectDataService.createContext(selectedContentObjects, requestor, userNote);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteWorkingContentObjectDataService.SERVICE_NAME,
						BulkDeleteWorkingContentObjectDataService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteWorkingContentObjectDataService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  working copies not aborted. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_ARCHIVE): {
				/**
				 * ACTION_ARCHIVE: - for each of the selected instance, archive the active copy
				 *
				 */
				analyticsEvent.setAction(Actions.Archive);
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.removeContentForModel(true, sco, sco.getContentObjectData());
					}
				}
				String userNote = c.getUserNote();
				ServiceExecutionContext context = BulkArchiveContentObjectService.createContext(selectedContentObjects, requestor, userNote);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkArchiveContentObjectService.SERVICE_NAME,
						BulkArchiveContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkArchiveContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  active copies are not archived. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					for(ContentObject contentObject: selectedContentObjects){
						for(Document doc:contentObject.getDocuments()){
							if(doc != null && !doc.isTpContentChanged()){
								doc.setTpContentChanged(true);
								doc.save();
							}
						}
					}

					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_DELETE_ARCHIVE): {
				/**
				 * ACTION_DELETE_ARCHIVE: - for each of the selected instance, perform delete archive action
				 *
				 */
				analyticsEvent.setAction(Actions.DeleteArchive);

				String userNote = c.getUserNote();
				ServiceExecutionContext context = BulkDeleteArchivedContentObjectDataService.createContext(selectedContentObjects, requestor, userNote);
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(BulkDeleteArchivedContentObjectDataService.SERVICE_NAME, BulkDeleteArchivedContentObjectDataService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkArchiveContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  messages are not removed. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					for(ContentObject contentObject: selectedContentObjects){
						for(Document doc:contentObject.getDocuments()){
							if(doc != null && !doc.isTpContentChanged()){
								doc.setTpContentChanged(true);
								doc.save();
							}
						}
					}

					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_RELEASE_FOR_APPROVAL): {
				/**
				 * ACTION_RELEASE_FOR_APPROVAL: - mark Release for Approval
				 *
				 */
				analyticsEvent.setAction(Actions.ReleaseForApproval);

				List<ContentObject> list = c.getSelectedList();
				String userNote = c.getUserNote();
				ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContextWithAction(requestor, userNote, false, list.toArray(new ContentObject[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : list) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  messages are not released for approval. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_ACTIVATE): {
				/**
				 * ACTION_ACTIVATE: - mark Activate
				 *
				 */
				List<ContentObject> list = c.getSelectedList();
				String userNote = c.getUserNote();
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.removeContentForModel(false, sco, sco.getContentObjectData());
					}
				}
				ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContextWithAction(requestor, userNote, true, list.toArray(new ContentObject[list.size()]));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.updateContentForModel(true, sco, sco.getContentObjectData());
					}
				}
				analyticsEvent.setAction(Actions.Activate);


				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : list) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not activated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {

					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}
			case (ACTION_EXPORT): {
				boolean includeAllMessages = false;
				if ((selectedContentObjects == null || selectedContentObjects.isEmpty()) || (!selectedContentObjects.isEmpty() && c.isIncludeAllMessagesAudit() && !(localContext > 0) && !isSharedCanvas.equalsIgnoreCase("true"))) {
					includeAllMessages = true;
				}
				TouchpointSelection touchpointSelection = TouchpointSelection.findById(selectionId);
				Document currentDocument = Document.findById(documentId);
				if (includeAllMessages && currentDocument.isEnabledForVariation()) {
					touchpointSelection = currentDocument.getMasterTouchpointSelection();
				}
				if (includeAllMessages) {
					boolean appliesAlternateTemplate = false;
					if (touchpointSelection != null && touchpointSelection.isMaster())
						appliesAlternateTemplate = touchpointSelection.getAlternateLayout() != null;
					List<Long> zoneIdList = new ArrayList<>();
					if ( !(localContext > 0) ) {
						localContext = 0;
						zoneIdList = AsyncListTableController.getZoneList(documentId, 0, 0, !appliesAlternateTemplate);
					}
					List<Long> selectedIds = ContentObject.findUniqueVisibleIdsOfMostRecentCopies(zoneIdList, (int)localContext, documentId, null); // Retrieve all messages
					if (selectedIds != null) {
						for (Long selectedId : selectedIds) {
							ContentObject msg = HibernateUtil.getManager().getObject(ContentObject.class, selectedId);
							//if(msg.getContentSpecializationTypeId() != ContentType.SHARED_FREEFORM)
							if(msg == null)
								continue;
							if((msg.isVariantType() || msg.isStructuredContentEnabled()) && msg.getOwningTouchpointSelection() !=null && msg.getOwningTouchpointSelection().getId()>0){
								TouchpointSelection miSelection = TouchpointSelection.findById(msg.getOwningTouchpointSelection().getId());
								if (miSelection == null) {
									continue;
								}
							}
							selectedContentObjects.add(msg);
						}
					}
				}
				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForCreate(principalUserId, getAuditReportTypeId(request));
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				analyticsEvent.setAction(Actions.Export);
				AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Message);
				auditEvent.setAction(Actions.GenerateReport);
				try {


					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateAuditReportService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" AuditReport object could not be created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						long auditReportId = (Long) serviceResponse.getResultValueBean();
						AuditReportType type = new AuditReportType(getAuditReportTypeId(request));
						boolean includeTargeting = false;
						if (UserUtil.isAllowed(principalUserId, Permission.ID_ROLE_MESSAGE_VIEW_ALL)) {
							includeTargeting = c.isIncludeTargeting();
						}

						int statusId = UserUtil.getCurrentSelectionStatusContext().getId();
						context = GenerateMessageAuditReportService.createContext(auditReportId, touchpointSelection, statusId, selectedContentObjects, c.isDateFilterEnabled(), c.getStartDate(), includeTargeting, c.isIncludeContent(), includeAllMessages, requestor, getAuditReportTypeId(request), Document.findById(documentId));

						service = MessagepointServiceFactory.getInstance().lookupService(GenerateMessageAuditReportService.SERVICE_NAME, GenerateMessageAuditReportService.class);
						service.execute(context);
						serviceResponse = context.getResponse();

						if (!serviceResponse.isSuccessful()) {

							context = CreateOrUpdateAuditReportService.createContextForError(auditReportId);
							service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
							service.execute(context);
							// Audit (Audit Report Failed)
							AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_FAIL, null);


							StringBuilder sb = new StringBuilder();
							sb.append(GenerateMessageAuditReportService.SERVICE_NAME);
							sb.append(" service call is not successful ");
							sb.append(" in ").append(this.getClass().getName());
							sb.append(" requestor = ").append(requestor.getUsername());
							sb.append(" AuditReport object could not be generated. ");
							log.error(sb.toString());
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return showForm(request, response, errors);
						} else {
							// Audit (Audit Report Success)
							AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, type.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_SUCCEED, null);

							return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
						}
					}
				}finally {
					auditEvent.send();
				}
			}

			case (ACTION_RELEASE_FROM_TRANSLATION),(ACTION_APPROVE): {
				analyticsEvent.setAction(Actions.Approve);

				List<ContentObject> list = c.getSelectedList();
				String userNote = c.getUserNote();
				ConfigurableWorkflow actionToWorkflow = c.getActionToWorkflow();
				ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, userNote, false, actionToWorkflow, list.toArray(new ContentObject[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();


				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowApproveService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : list) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  messages are not approved. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_REJECT): {
				analyticsEvent.setAction(Actions.Reject);

				List<ContentObject> list = c.getSelectedList();
				String userNote = c.getUserNote();
				ConfigurableWorkflow actionToWorkflow = c.getActionToWorkflow();
				ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, c.getAssignedToUser(), userNote, actionToWorkflow, list.toArray(new ContentObject[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();


				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowRejectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : list) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not rejected. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_ABORT): {
				analyticsEvent.setAction(Actions.Abort);

				List<ContentObject> list = c.getSelectedList();
				String userNote = c.getUserNote();
				ServiceExecutionContext context = WorkflowRejectService.createContextForAbort(requestor, userNote, list.toArray(new ContentObject[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();


				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowRejectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : list) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not aborted. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_RETRY_TRANSLATION): {
				analyticsEvent.setAction(Actions.Retry);

				List<ContentObject> list = c.getSelectedList();
				for(ContentObject contentObject: list){
					WorkflowUtilities.sendContentObjectToTranslationService(contentObject, c.getActionToWorkflow(), true, c.getUserNote());
				}
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
			}

			case (ACTION_HOLD): {
				analyticsEvent.setAction(Actions.Hold);

				ServiceExecutionContext context = BulkUpdateContentObjectService.createContextForHold(selectedContentObjects, c.isSuppressedOnHold());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not place on hold. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}

			}

			case (ACTION_UNHOLD): {
				analyticsEvent.setAction(Actions.Unhold);

				ServiceExecutionContext context = BulkUpdateContentObjectService.createContextForUnhold(selectedContentObjects, c.isUpdateVariantVersioning());
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  hold not removed from content objects. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}

			}

			case (ACTION_SUPPRESS): {
				analyticsEvent.setAction(Actions.Suppress);
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.removeContentForModel(true, sco, sco.getContentObjectData());
					}
				}
				ServiceExecutionContext context = BulkUpdateContentObjectService.createContextForSuppress(selectedContentObjects);
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not suppressed. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					// Set TPContentChanged to true
					for(ContentObject contentObject: selectedContentObjects){
						if(contentObject != null) {
							for(Document doc:contentObject.getDocuments()){
								if(doc != null && !doc.isTpContentChanged()){
									doc.setTpContentChanged(true);
									doc.save();
								}
							}
						}

						// Audit (Audit suppress)
						AuditEventUtil.auditWorkflowChanged(contentObject, AuditActionType.ID_CHANGE_SUPPRESSED, null);
					}

					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}

			}

			case (ACTION_RESTORE): {
				analyticsEvent.setAction(Actions.Restore);
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.updateContentForModel(true, sco, sco.getContentObjectData());
					}
				}
				ServiceExecutionContext context = BulkUpdateContentObjectService.createContextForRestore(selectedContentObjects);
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  content objects are not restored. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					for(ContentObject instance: c.getSelectedList()){
						for(Document doc:instance.getDocuments()){
							if(doc != null && !doc.isTpContentChanged()){
								doc.setTpContentChanged(true);
								doc.save();
							}
						}
					}
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}

			}

			case (ACTION_WHERE_USED):{
				analyticsEvent.setAction(Actions.WhereUsed);

				long reportId 						= Long.valueOf(c.getWhereUsedReportId());

				ServiceExecutionContext context = CreateWhereUsedReportService.createContext(reportId, selectedContentObject.getId(), ContentObject.class, false, new String[]{}, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateWhereUsedReportService.SERVICE_NAME, CreateWhereUsedReportService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateWhereUsedReportService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" Where used report could not be generated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_MAKE_GLOBAL): {
				analyticsEvent.setAction(Actions.MakeGlobal);
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.removeContentForModel(false, sco, sco.getContentObjectData());
						ElasticsearchContentUtils.removeContentForModel(true, sco, sco.getContentObjectData());
					}
				}
				ServiceExecutionContext context = BulkUpdateContentObjectService.createContext(BulkUpdateContentObjectService.ACTION_MAKE_GLOBAL, selectedContentObjects, false, false, null);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.updateContentForModel(true, sco, sco.getContentObjectData());
					}
				}

				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  local assets are not global. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_MOVE_TO_GLOBAL): {
					analyticsEvent.setAction(Actions.MoveToGlobal);
					if (ElasticsearchContentUtils.isContentCompareEnabled()) {
						for(ContentObject sco: selectedContentObjects) {
							ElasticsearchContentUtils.removeContentForModel(false, sco, sco.getContentObjectData());
							ElasticsearchContentUtils.removeContentForModel(true, sco, sco.getContentObjectData());
						}
					}
/**
					Set<ContentObject> references = new HashSet<ContentObject>();
					for (ContentObject contentObject : selectedContentObjects)
					{
						if (contentObject.isLocalImage())
							references.addAll(contentObject.findAllLocalImageReference());
						else if (contentObject.isLocalSmartText())
							references.addAll(contentObject.findAllLocalSmartTextReference());
					}

					if (!references.isEmpty() && !selectedContentObjects.containsAll(references))
					{
						// error
					}
**/

					ServiceExecutionContext context = BulkUpdateContentObjectService.createContext(BulkUpdateContentObjectService.ACTION_MOVE_TO_GLOBAL, selectedContentObjects, false, false, null);
					Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					if (ElasticsearchContentUtils.isContentCompareEnabled()) {
						for(ContentObject sco: selectedContentObjects) {
							if(sco.hasWorkingData()) {
								ElasticsearchContentUtils.updateContentForModel(false, sco, sco.getContentObjectData());
							}
							if(sco.hasActiveData()) {
								ElasticsearchContentUtils.updateContentForModel(true, sco, sco.getContentObjectData());
							}
						}
					}
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (ContentObject contentObject : selectedContentObjects) {
							sb.append(" id ").append(contentObject.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  local assets are not global. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);

					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
					}
				}

			case (ACTION_PUSH_TO_GLOBAL): {
				analyticsEvent.setAction(Actions.PushToGlobal);
					if (ElasticsearchContentUtils.isContentCompareEnabled()) {
						for(ContentObject sco: selectedContentObjects) {
							ElasticsearchContentUtils.removeContentForModel(false, sco, sco.getContentObjectData());
							ElasticsearchContentUtils.removeContentForModel(true, sco, sco.getContentObjectData());
						}
					}
				ServiceExecutionContext context = BulkUpdateContentObjectService.createContext(BulkUpdateContentObjectService.ACTION_PUSH_TO_GLOBAL, selectedContentObjects, false, false, null);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();


				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  local assets are not global. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_PULL_FROM_GLOBAL): {
				analyticsEvent.setAction(Actions.PullFromGlobal);

				ServiceExecutionContext context = BulkUpdateContentObjectService.createContext(BulkUpdateContentObjectService.ACTION_PULL_FROM_GLOBAL, selectedContentObjects, false, false, null);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateContentObjectService.SERVICE_NAME, BulkUpdateContentObjectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();

				if (ElasticsearchContentUtils.isContentCompareEnabled()) {
					for(ContentObject sco: selectedContentObjects) {
						ElasticsearchContentUtils.updateContentForModel(false, sco, sco.getContentObjectData());
					}
				}
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateContentObjectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (ContentObject contentObject : selectedContentObjects) {
						sb.append(" id ").append(contentObject.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  local assets are not global. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			}

			case (ACTION_CREATE_JSON_FOR_TRANSLATION): {

				String targetLocaleCode = "es_us";
				if (c.getExportTargetLocale() != null)
					targetLocaleCode = c.getExportTargetLocale().getCode();

				if(! selectedContentObjects.isEmpty()) {
					ContentObject contentObject = selectedContentObjects.get(0);
					selectedContentObjects.remove(0);
					if (selectedContentObjects.isEmpty()) {
						selectedContentObjects = null;
					}

					List<Long> selectedIds = selectedContentObjects == null ? null : selectedContentObjects.stream().map(ContentObject::getId).collect(Collectors.toList());
					ServiceExecutionContext context = GenerateMessagepointObjectExportService.createContext(c.getExportId(),
							contentObject,
							selectedIds,
							true,
							true,
							ContentObject.class,
							UserUtil.getPrincipalUser(), ExportMessagepointObjectBackgroundTask.EXPORT_JSON_FOR_TRANSLATION_ZIP, targetLocaleCode);
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(GenerateMessagepointObjectExportService.SERVICE_NAME,
									GenerateMessagepointObjectExportService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();

					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(GenerateMessagepointObjectExportService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" id ").append(contentObject.getId());
						sb.append(" requestor=").append(requestor.getUsername());
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					}
				}

				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
			}

			default:
				break;
			}

			return null;
		} finally {
			analyticsEvent.send();
		}

	}

	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> parms = new HashMap<>();

		long viewid 			= ServletRequestUtils.getLongParameter(request, REQ_PARM_VIEWID, -1L);
		long documentId 		= ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		long contentObjectId	= ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1L);
		int dataType			= ServletRequestUtils.getIntParameter(request, ContentObject.REQ_PARM_DATA_TYPE, -1);
	    long paramInstId 		= ServletRequestUtils.getLongParameter(request, REQ_PARM_PARAM_INST_ID, -1L);
	    long statusViewId 		= ServletRequestUtils.getLongParameter(request, REQ_PARM_STATUS_VIEW_ID, -1L);
	    boolean reset			= ServletRequestUtils.getBooleanParameter(request, REQ_PARM_RESET, false);
	    long localContext		= ServletRequestUtils.getLongParameter(request, REQ_LOCAL_CONTEXT, -1L);
	    boolean isFreeform 		= ServletRequestUtils.getBooleanParameter(request, GlobalContentListController.REQ_PARM_IS_FREEFORM, false);
	    long contentSelectionId	= ServletRequestUtils.getLongParameter(request, TouchpointContentSelectionViewController.REQ_PARAM_CONTENT_SELECTION_ID_PARAM, -1L);

		if (viewid != -1L)
			parms.put(REQ_PARM_VIEWID, viewid);
		if (localContext != -1L)
			parms.put(REQ_LOCAL_CONTEXT, localContext);
		if (isFreeform)
			parms.put(GlobalContentListController.REQ_PARM_IS_FREEFORM, isFreeform);
		if (statusViewId != -1)
			parms.put(REQ_PARM_STATUS_VIEW_ID, statusViewId);
		if (documentId != -1L) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		if (reset)
			parms.put(REQ_PARM_RESET, true);
		} else {
			Document firstTreedTp = getFirstVisiblePermissionedTouchpoint();
			parms.put(REQ_PARM_DOCUMENTID, firstTreedTp != null ? firstTreedTp.getId() : -2);
		}

		// Cross link params
		if (contentObjectId != -1)
			parms.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);
		if (contentObjectId != -1)
			parms.put(ContentObject.REQ_PARM_DATA_TYPE, dataType);
		if (paramInstId != -1)
			parms.put(REQ_PARM_PARAM_INST_ID, paramInstId);
		if ( contentSelectionId != -1 )
			parms.put(TouchpointContentSelectionViewController.REQ_PARAM_CONTENT_SELECTION_ID_PARAM, contentSelectionId);

		Document currentDocument = Document.findById(documentId);
		if (currentDocument != null && currentDocument.isEnabledForVariation()) {
			long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PARM_SELECTION_ID, -1L);
			if (selectionId != -1) {
				parms.put(REQ_PARM_SELECTION_ID, selectionId);
			} else {
				if ( UserUtil.getCurrentSelectionContext() != null )
					parms.put(REQ_PARM_SELECTION_ID, UserUtil.getCurrentSelectionContext().getId());
				else
					parms.put(REQ_PARM_SELECTION_ID, selectionId);	
			}
		}

		return parms;
	}

	private Document getFirstVisiblePermissionedTouchpoint() {
		List<Document> docList = Document.getVisibleDocuments();
		if (docList == null || docList.isEmpty())
			return null;
		for (Document currentDocument: docList) {
		    if(currentDocument.getParent() != null) continue;
            if(currentDocument.getChannelParent() != null) continue;
			if ( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) )
				return currentDocument;
			else if ( currentDocument.isEnabledForVariation() &&
					  (UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW) || UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW)) )
				return currentDocument;
		}
		
		return null;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

		setOriginForm(request, new String[]{ContentObject.REQ_PARM_CONTENT_OBJECT_ID, ContentObject.REQ_PARM_DATA_TYPE, REQ_PARM_PARAM_INST_ID, REQ_PARM_CONTENT_SELECTION_ID, REQ_PARM_STATUS_VIEW_ID});

		// ******* Start User context persist/recall ************
		long documentId 	= ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
		long localContext	= ServletRequestUtils.getLongParameter(request, REQ_LOCAL_CONTEXT, -1L);
		boolean isFreeform 	= ServletRequestUtils.getBooleanParameter(request, GlobalContentListController.REQ_PARM_IS_FREEFORM, false);
		
		User principal = UserUtil.getPrincipalUser();
		User user = User.findById(principal.getId());

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else {
			Document currentDocument = null;
			if (documentId != -1)
			{
				currentDocument = Document.findById(documentId);
				if (currentDocument == null)
					documentId = -1;
			}
			if (documentId != -1) { // Secondary access: Navigating post entry

			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;

			HashMap<String,String> contextAttr = new HashMap<>();

			long selectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);

			TouchpointSelection selection 	= TouchpointSelection.findById(selectionId);
			TouchpointSelection currentDocumentMasterSelection = currentDocument.getMasterTouchpointSelection();

			if (selectionId != -1L && selection == null && currentDocumentMasterSelection == null) {
				Map<String, Object> params = getSuccessViewParms(request);
				params.put(AsyncListTableController.PARAM_SELECTION_ID, -1L);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}

			if ((selection == null || selection.getDocument() == null) && currentDocumentMasterSelection != null) {
				Map<String, Object> params = getSuccessViewParms(request);
				params.put(AsyncListTableController.PARAM_SELECTION_ID, currentDocumentMasterSelection.getId());
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}

			if (selection != null && selection.getDocument() == null && currentDocumentMasterSelection == null) {
				Map<String, Object> params = getSuccessViewParms(request);
				params.put(AsyncListTableController.PARAM_SELECTION_ID, -1L);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}

			// Validate selection context is in sync with document
			if (selection != null && (selection.getDocument().getId() != currentDocument.getId() || selection.isDeleted())) {
				Map<String, Object> params = getSuccessViewParms(request);
				if (currentDocumentMasterSelection != null)
					params.put(AsyncListTableController.PARAM_SELECTION_ID, currentDocumentMasterSelection.getId());

				// Validate if request params are the same as new redirection to avoid TOO_MANY_REDIRECTIONS error
				if (Long.valueOf(selectionId).equals(params.get(AsyncListTableController.PARAM_SELECTION_ID))
						&& documentId == ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L)) {
					request.getSession().setAttribute(SESSION_PARAM_REDIRECT_DOC_ID, documentId);
					params.put(AsyncListTableController.PARAM_DOCUMENT_ID, selection.getDocument().getId());
				}

				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}
			
			if ( !( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) )	)
				return new ModelAndView(new RedirectView("touchpoint_variant_list.form"), getSuccessViewParms(request));
			else if ( currentDocument.isEnabledForVariation() && currentDocumentMasterSelection != null ) {
				// Prefer selection context (visibility managed)
				TouchpointSelection selectionContext = UserUtil.getCurrentSelectionContext();
				if ( selection == null && selectionContext != null && selectionContext.getDocument().getId() == currentDocumentMasterSelection.getDocument().getId() )
					return new ModelAndView(new RedirectView(request.getRequestURI().indexOf("touchpoint_content_object_list") != -1 ? "touchpoint_content_object_list.form" : "local_content_list.form"), getSuccessViewParms(request));
				else if ( (selection != null && selection.isMaster()) && !currentDocumentMasterSelection.isVisible(user) )
					return new ModelAndView(new RedirectView("touchpoint_variant_list.form"), getSuccessViewParms(request));
			}

			// List context
			if ( request.getRequestURI().indexOf("touchpoint_content_object_list") != -1 ) {
				contextAttr.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS, "touchpoint_content_object_list.form");
			} else if ( request.getRequestURI().indexOf("local_content_list") != -1 ) {
				if ( isFreeform )
					contextAttr.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS, "local_content_list.form?" + REQ_LOCAL_CONTEXT + "=1" + "&" + GlobalContentListController.REQ_PARM_IS_FREEFORM + "=true");
				else
					contextAttr.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS, "local_content_list.form?" + REQ_LOCAL_CONTEXT + "=" + (localContext <= 0 ? 1 : localContext) );
			}
			
			contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(documentId));

			if (currentDocument != null && !currentDocument.isEnabledForVariantWorkflow())
			{
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_STATUS_CONTEXT, String.valueOf(VIEW_WORKING_COPY));
			}
			
			if ( selection != null) {	// Touchpoint selection ID exists
				// Verify selection's visibility
				User requestor = UserUtil.getPrincipalUser();
				if(selection.isDeleted() || !selection.isVisible(requestor)){
					Map<String, Object> params = getSuccessViewParms(request);
					// Clear selection ID
					params.remove(REQ_PARM_SELECTION_ID);
					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}
				if(currentDocument.isEnabledForVariation()){
					// Ensure Touchpoint and Selection are in sync (multi-window usage can cause miss match)
					if (documentId == selection.getDocument().getId() && !selection.isDeleted()){
						contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(selectionId));
					}else{
						contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(currentDocumentMasterSelection != null ? currentDocumentMasterSelection.getId() : -1));
					}
				} else {	// // Selection ID does not apply to message list: Remove and reload
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
				}
			} else {	// Touchpoint selection ID not exists
				if ( currentDocument.isEnabledForVariation() && currentDocumentMasterSelection != null) {	// try to find default selection and reload
					Map<String, Object> params = getSuccessViewParms(request);
					params.put(AsyncListTableController.PARAM_SELECTION_ID, currentDocumentMasterSelection.getId());
					return new ModelAndView(new RedirectView(getSuccessView()), params);
				} 
			}
			
			// Verify language context: Switch to Touchpoint default if incompatible
			// it'll keep trying to switch to a compatible MessagepointLocale
			// until (compatibleLanguage == true && compatibleLocaleId == 0);
			// which means that algorithm found a compatible Language/Locale,
			// and it's already selected (currentLanguageLocaleContext)
			boolean compatibleLanguage = false;
			long compatibleLocaleId = 0;

			MessagepointLocale currentLanguageLocaleContext = UserUtil.getCurrentLanguageLocaleContext();
			TouchpointLanguage defaultTouchpointLanguage = currentDocument.getDefaultTouchpointLanguage();
			MessagepointLocale defaultTPMessagepointLocale = (defaultTouchpointLanguage == null) ? null : (defaultTouchpointLanguage.getMessagepointLocale() == null ? null : defaultTouchpointLanguage.getMessagepointLocale());

			if (currentLanguageLocaleContext != null) {
				// leave as is if current context language is the same as default TP language/locale
				if (defaultTPMessagepointLocale != null &&
						!defaultTPMessagepointLocale.getCode().equalsIgnoreCase(
								currentLanguageLocaleContext.getCode())) {
					compatibleLanguage = true;
				} else {
					Set<TouchpointLanguage> documentLanguages = currentDocument.getTouchpointLanguages();
					if (documentLanguages != null && !documentLanguages.isEmpty()) {
						for (TouchpointLanguage currentLanguage : currentDocument.getTouchpointLanguages()) {
							MessagepointLocale currentLanguageMessagepointLocale = currentLanguage.getMessagepointLocale();

							// test if current context language is compatible with any TP languages, without the locale part
							if (currentLanguageMessagepointLocale.getLanguageCode().equalsIgnoreCase(currentLanguageLocaleContext.getLanguageCode())) {

								// leave as is if current selected language/locale is present in TP Language/Locales
								if (currentLanguageMessagepointLocale.getCode().equalsIgnoreCase(currentLanguageLocaleContext.getCode())) {
									compatibleLanguage = true;
									compatibleLocaleId = 0;
									break;
								}

								// compatible language/locale has to be different from default TP language/locale
								if (compatibleLocaleId == 0 && (defaultTPMessagepointLocale == null ||
										!currentLanguageMessagepointLocale.getCode().equalsIgnoreCase(
												defaultTPMessagepointLocale.getCode()))) {
									compatibleLanguage = true;
									// if currently selected locale is already a compatible locale, don't need to select another
									if (!currentLanguageMessagepointLocale.getCode().equalsIgnoreCase(currentLanguageLocaleContext.getCode())) {
										compatibleLocaleId = currentLanguageMessagepointLocale.getId();
									}
								}
							}
						}

						if (!compatibleLanguage && defaultTouchpointLanguage == null) {
							// prevent redirection loop if the current touchpoint doesn't have defined default languages
							compatibleLanguage = true;
						}
					} else {
						// prevent redirection loop if the current touchpoint doesn't have defined any languages
						compatibleLanguage = true;
					}
				}
			}
			if (!compatibleLanguage)
			{
				if (defaultTouchpointLanguage != null)
					contextAttr.put(UserUtil.CONTEXT_KEY_LANGUAGE_CONTEXT, String.valueOf(defaultTouchpointLanguage.getMessagepointLocale().getId()));
				else
					contextAttr.put(UserUtil.CONTEXT_KEY_LANGUAGE_CONTEXT, String.valueOf(MessagepointLocale.getDefaultSystemLanguageLocale().getId()));
			}
			else if (compatibleLocaleId > 0)
				contextAttr.put(UserUtil.CONTEXT_KEY_LANGUAGE_CONTEXT, String.valueOf(compatibleLocaleId) );

			// Set return from setup page context
			if ( request.getRequestURI().indexOf("touchpoint_content_object_list") != -1 ) {
				contextAttr.put(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT, "/touchpoints/touchpoint_content_object_list.form");
			} else if ( request.getRequestURI().indexOf("local_content_list") != -1 ) {
				if ( isFreeform )
					contextAttr.put(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT, "/touchpoints/local_content_list.form?" + GlobalContentListController.REQ_PARM_IS_FREEFORM + "=true");
				else
					contextAttr.put(UserUtil.CONTEXT_KEY_RETURN_FROM_SETUP_PAGE_CONTEXT, "/touchpoints/local_content_list.form?" + REQ_LOCAL_CONTEXT + "=" + localContext);
			}
				
			// Update user context properties
			UserUtil.updateUserContextAttributes(contextAttr);
			
			// If no compatible language: Set URL parameter and reload
			if (!compatibleLanguage || compatibleLocaleId > 0) {
				Map<String, Object> params = getSuccessViewParms(request);
				if (compatibleLocaleId > 0)
					params.put(ContextBarTag.REQ_PARAM_LANGUAGE_ID_PARAM, compatibleLocaleId);
				else
				{
					if (defaultTouchpointLanguage != null)
						params.put(ContextBarTag.REQ_PARAM_LANGUAGE_ID_PARAM, defaultTouchpointLanguage.getMessagepointLocale().getId());
					else
						params.put(ContextBarTag.REQ_PARAM_LANGUAGE_ID_PARAM, MessagepointLocale.getDefaultSystemLanguageLocale().getId());
				}
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			}
			
		} else { // First access: Direct traffic

			if (user.getContextAttributes() != null) {
				JSONObject attrJSON = new JSONObject(user.getContextAttributes());
				if (attrJSON.has(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS) && attrJSON.has(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT)) {
					Map<String, Object> params = new HashMap<>();
					
					// User has lost visibility to persisted Touchpoint:  Redirect (first visible touchpoint will be activated)
					Document targetDocument = Document.findById(Long.valueOf(attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT).toString()));
					if (targetDocument == null || !targetDocument.isVisible(UserUtil.getPrincipalUser()) || !targetDocument.isEnabled())
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
					
					params.put(AsyncListTableController.PARAM_DOCUMENT_ID, attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT));
					TouchpointSelection targetSelection = UserUtil.getCurrentSelectionContext();
					if(targetSelection != null && !targetSelection.isDeleted())
					{
						params.put(AsyncListTableController.PARAM_SELECTION_ID, attrJSON.get(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT));
					}
					
					// Check permissions and set target page as necessary
					String listContent = attrJSON.get(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS).toString();
					if ( listContent.indexOf("touchpoint_selections_content_list.form") != -1 )
						attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_variant_list.form");
					
					if ( listContent.indexOf("touchpoint_content_object_list") != -1 &&
						 !( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) )	)
						attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_variant_list.form");
					else if ( listContent.indexOf("local_content_list") != -1 &&
							 !UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_CONTENT_VIEW)	)
							attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_variant_list.form?" + REQ_LOCAL_CONTEXT + "=" + localContext);
					else if ( (listContent.indexOf("touchpoint_content_object_list") != -1 || listContent.indexOf("local_content_list") != -1)  &&
							(targetDocument.isEnabledForVariation()) &&
							( (targetSelection != null && targetSelection.isMaster()) || targetSelection == null) && targetDocument.getMasterTouchpointSelection() != null && 
							(targetDocument.getMasterTouchpointSelection() != null && !targetDocument.getMasterTouchpointSelection().isVisible(user)) )
							attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_variant_list.form");
					else if ( listContent.indexOf("touchpoint_variant_list") != -1 &&
							  !( UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW) || UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW) ) )
						attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_content_object_list.form");
					
					if ( listContent.indexOf("touchpoint_variant_list") != -1 && !( targetDocument.isEnabledForVariation() ) )
						attrJSON.put(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS,"touchpoint_content_object_list.form");
					
					return new ModelAndView(new RedirectView((String)attrJSON.get(UserUtil.CONTEXT_KEY_LIST_CONTEXT_TOUCHPOINTS)), params); 
				}
			}

			if(documentId == -1)
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
		}
	}

		Object redirectDocId = request.getSession().getAttribute(SESSION_PARAM_REDIRECT_DOC_ID);
		long redirectedFromDocumentId = redirectDocId == null ? -1L : (Long)redirectDocId;
		if (redirectedFromDocumentId != -1) {
			Document redirectedFromDocument = Document.findById(redirectedFromDocumentId);
			String documentIdentification = redirectedFromDocument == null ? "#ID " + redirectedFromDocumentId : "GUID " + redirectedFromDocument.getGuid();
			errors.reject("error.message.touchpoint.deleted.or.not.accessible", new Object[] {documentIdentification}, "Touchpoint " + documentIdentification + " was deleted or is not accessible. Showing default Touchpoint for Selection instead.");
			request.getSession().removeAttribute(SESSION_PARAM_REDIRECT_DOC_ID);
		}

		// ******* End User context persist/recall **************

		// Audit (Page access: Message list)
		int actionType = AuditActionType.ID_MESSAGE_LIST;
		if(localContext == ContentType.TEXT){
			actionType = AuditActionType.ID_LOCAL_SMART_TEXT_LIST;
		}else if(localContext == ContentType.GRAPHIC){
			actionType = AuditActionType.ID_LOCAL_IMAGE_LIST;
		}
		AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_NONE, null, null, actionType, null);
		return super.showForm(request, response, errors);
	}

	private int getAuditReportTypeId(HttpServletRequest request){
		long localContext = ServletRequestUtils.getLongParameter(request, REQ_LOCAL_CONTEXT, -1L);
		boolean isSharedCanvas = ServletRequestUtils.getBooleanParameter(request, REQ_IS_SHARED_CANVAS, false);

		int auditReportTypeId = AuditReportType.ID_MESSAGE_AUDIT_REPORT;
		if(localContext == 1){
			if(isSharedCanvas){
				auditReportTypeId = AuditReportType.ID_LOCAL_SMART_CANVAS_AUDIT_REPORT;
			}else{
				auditReportTypeId = AuditReportType.ID_LOCAL_SMART_TEXT_AUDIT_REPORT;
			}
		}else if(localContext == 2){
			auditReportTypeId = AuditReportType.ID_LOCAL_IMAGE_AUDIT_REPORT;
		}

		return auditReportTypeId;
	}
}
