package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentSection;
import com.prinova.messagepoint.model.DocumentSectionLayoutType;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.admin.Connector;
import com.prinova.messagepoint.model.admin.DataSourceAssociation;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.dialogue.DialogueConfiguration;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.gmc.GMCConfiguration;
import com.prinova.messagepoint.model.nativecomposition.NativeCompositionConfiguration;
import com.prinova.messagepoint.model.sefas.MPHCSConfiguration;
import com.prinova.messagepoint.model.sefas.SefasConfiguration;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.logging.Log;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

public class TouchpointCollectionEditValidator extends MessagepointInputValidator {

	private static final Log log = LogUtil.getLog(TouchpointCollectionEditValidator.class);
	public static final String 	MP_COMPOSER_CONNECTOR_NAME = "MP_Composer";

	public boolean supports(Class clazz) {
		return clazz.equals(TouchpointCollectionEditWrapper.class);
	}
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		TouchpointCollectionEditWrapper wrapper = (TouchpointCollectionEditWrapper) commandObj;
		List<Document> documents = wrapper.getDocuments();
		
		if(documents == null || documents.isEmpty()){
			errors.reject("error.message.no.touchpoints.selected");
			return;
		}
		
		if(wrapper.getTouchpointCollection().isExecutable()){
			// Touchpoint cannot be in more than one executable collection which is not a subset of Touchpoints 
			// Subset checking
			boolean isSubset = false, isSuperset = true;
			for(TouchpointCollection tpCollection : TouchpointCollection.findAll(true)){
				if(tpCollection.getId() != wrapper.getTouchpointCollection().getId()){
					if(new HashSet<>(tpCollection.getDocuments()).containsAll(documents)){
						isSubset = true;
					}
					if(!new HashSet<>(wrapper.getTouchpointCollection().getDocuments()).containsAll(tpCollection.getDocuments())){
						isSuperset = false;
					}					
				}
			}
			if(!isSubset && !isSuperset){
				for(Document document : documents){
					List<TouchpointCollection> tpCollections = document.getExecutableCollections();
					tpCollections.remove(wrapper.getTouchpointCollection());
					if(!tpCollections.isEmpty()){
						errors.reject("error.message.packaged.touchpoint.must.belong.to.same.family", new String[]{document.getName()}, null);
						return;	
					}
				}				
			}
			List<String> documentNames = new ArrayList<>();
			// Validate the channel/connector
			Connector connector = null;
			for(Document document : documents){
				if(document.getConnectorConfiguration() == null){
					continue;
				}
				// SEFAS_TODO
				if(	!(document.getConnectorConfiguration() instanceof DialogueConfiguration ||
						document.getConnectorConfiguration() instanceof GMCConfiguration ||
						document.getConnectorConfiguration() instanceof NativeCompositionConfiguration ||
						document.getConnectorConfiguration() instanceof SefasConfiguration ||
						document.getConnectorConfiguration() instanceof MPHCSConfiguration ) ) {

					if (!documentNames.contains(document.getName())) {
						documentNames.add(document.getName());
					}
				}
				if(document.getConnectorName() != null && document.getConnectorName().equalsIgnoreCase(MP_COMPOSER_CONNECTOR_NAME) && !documentNames.contains(document.getName())){
					documentNames.add(document.getName());
				}
				if(connector == null){
					connector = document.getConnectorConfiguration().getConnector();
				}else{
					if(connector.getId() != document.getConnectorConfiguration().getConnector().getId()){
						errors.reject("error.message.touchpoints.connector.are.not.the.same");
						return;
					}
				}
			}
			if(!documentNames.isEmpty()) {
				StringBuilder sb = new StringBuilder();
				for (String name : documentNames) {
					sb.append(name).append(", ");
				}
				errors.reject("error.message.touchpoint.collection.connector.not.mp.composer", new String[]{sb.toString()}, null);
				return;
			}
			
			// Validate the data source association
			DataSourceAssociation dsa = null;
			for(Document document : documents){
				if(document.getDataSourceAssociation() == null){
					continue;
				}
				if(dsa == null){
					dsa = document.getDataSourceAssociation();
				}else{
					if(dsa.getId() != document.getDataSourceAssociation().getId()){
						errors.reject("error.message.touchpoints.dsa.are.not.the.same");
						return;					
					}
				}
			}
			
			// Validate the composition package
//			CompositionFileSet compPack = null;
//			for(Document document : documents){
//				if(document.getCompositionFileSet() == null){
//					continue;
//				}
//				if(compPack == null){
//					compPack = document.getCompositionFileSet();
//				}else{
//					if(!compPack.getTemplateFileName().equals(document.getCompositionFileSet().getTemplateFileName()) || 
//							!compPack.getCompositionConfigurationFileName().equals(document.getCompositionFileSet().getCompositionConfigurationFileName())){
//						errors.reject("error.message.touchpoints.composition.package.are.not.the.same");
//						return;					
//					}
//				}
//			}

			validateSectionSizeConflict(wrapper, errors);
			validateTemplateControlledTouchpoints(wrapper, errors);
		}

		for(Document document : documents){
			for(Document document2 : documents){
				if(document.getId() != document2.getId()){
					List<MessagepointLocale> documentLocales = document.getTouchpointLanguages().stream().map(TouchpointLanguage::getMessagepointLocale).collect(Collectors.toList());
					List<MessagepointLocale> document2Locales = document2.getTouchpointLanguages().stream().map(TouchpointLanguage::getMessagepointLocale).collect(Collectors.toList());
					// Compare the documentLocales to document2Locales, if they have the same language_id but different code, reject the request
					for(MessagepointLocale documentLocale : documentLocales){
						for(MessagepointLocale document2Locale : document2Locales){
							if(documentLocale.getLanguageId() == document2Locale.getLanguageId() && !documentLocale.getCode().equals(document2Locale.getCode())){
								errors.reject("error.message.touchpoints.languages");
								return;
							}
						}
					}
				}
			}
		}
	}

	//Touchpoints must be all template controlled or all non-template controlled
	private void validateTemplateControlledTouchpoints(TouchpointCollectionEditWrapper wrapper, Errors errors) {
		List<Document> documents = wrapper.getDocuments();
		if (documents == null || documents.isEmpty())
			return;

        if (isAllTemplateControlled(documents) || areAllNotTemplateControlled(documents))
			return;
		else
            errors.reject("error.message.touchpoint.collections.all.template.or.non.template.controlled");

        return;
    }

	public boolean isAllTemplateControlled(List<Document> documents) {
		for (Document document : documents) {
			if (!document.isTemplateControlled())
				return false;
		}
		return true;
	}

	public boolean areAllNotTemplateControlled(List<Document> documents) {
		for (Document document : documents) {
			if (document.isTemplateControlled())
				return false;
		}
		return true;
	}


	//Touchpoint sections must be all the same size for sefas and mphcs
	private void validateSectionSizeConflict(TouchpointCollectionEditWrapper tpLayoutManagerWrapper, Errors errors) {
		try {
			Float compareWidth = null;
			Float compareHeight = null;

			List<Document> documents = tpLayoutManagerWrapper.getDocuments();
			if (documents == null || documents.isEmpty())
				return;

			//Should only be list of only 1 connector, MPHCS or Sefas.  Check first item in list is enough.
			boolean isSefasOrMPHCS = documents.get(0).isSefasCompositionTouchpoint() || documents.get(0).isMPHCSCompositionTouchpoint();
			if (!isSefasOrMPHCS)
				return;
			
			for (Document document : documents) {
				List<DocumentSection> documentSections = document.getDocumentSections().stream().toList();
				for (int i = 0; i < documentSections.size(); i++) {
					DocumentSection currentSection = documentSections.get(i);
					float currentWidth = -1;
					float currentHeight = -1;
					if ( currentSection.getLayoutType() == DocumentSectionLayoutType.ID_CUSTOM) {
						currentWidth = Float.parseFloat(currentSection.getWidth() > 0 ? DecimalValueUtil.dehydrate(currentSection.getWidth()) : "");
						currentHeight = Float.parseFloat(currentSection.getHeight() > 0 ? DecimalValueUtil.dehydrate(currentSection.getHeight()) : "");
					} else {
						DocumentSectionLayoutType documentLayoutType = new DocumentSectionLayoutType(currentSection.getLayoutType());
						currentWidth = Float.valueOf(documentLayoutType.getWidth());
						currentHeight = Float.valueOf(documentLayoutType.getHeight());
					}

					if (compareWidth == null && compareHeight == null) {
						compareWidth = currentWidth;
						compareHeight = currentHeight;
					} else {
						if ( (compareWidth != currentWidth || compareHeight != currentHeight) &&
								(compareWidth != currentHeight || compareHeight != currentWidth) ) {
							errors.reject("error.section.size.different");
							break;
						}
					}
				}
			}
		} catch (Exception ex) {
			log.error(ex.getMessage());
		}
	}
}
