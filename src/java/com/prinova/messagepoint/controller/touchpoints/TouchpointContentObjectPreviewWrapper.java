package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.DocumentPreview;
import com.prinova.messagepoint.model.content.ContentObject;

import java.io.Serializable;

public class TouchpointContentObjectPreviewWrapper implements Serializable{

	private static final long serialVersionUID = -2526588383459857342L;

	private ContentObject 	contentObject;
	private DocumentPreview	documentPreview;

	public TouchpointContentObjectPreviewWrapper(){
		super();
	}

	public TouchpointContentObjectPreviewWrapper(DocumentPreview documentPreview, ContentObject contentObject){
		super();
		this.documentPreview = documentPreview;
		this.contentObject = contentObject;
	}

	public ContentObject getContentObject() {
		return contentObject;
	}

	public void setContentObject(ContentObject contentObject) {
		this.contentObject = contentObject;
	}

	public DocumentPreview getDocumentPreview() {
		return documentPreview;
	}

	public void setDocumentPreview(DocumentPreview documentPreview) {
		this.documentPreview = documentPreview;
	}	
}
