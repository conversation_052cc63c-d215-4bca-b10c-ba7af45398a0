package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.ApplicationUtil;

public class VersionStatusFilterType extends StaticType {
	private static final long serialVersionUID = 546293711499914404L;

	public static final String MESSAGE_CODE_WORKING_AND_ACTIVE	= "page.label.list.filter.type.in.any.state.not.archive";
	public static final String MESSAGE_CODE_WORKING_COPY 		= "page.label.list.filter.type.working.copy";
	public static final String MESSAGE_CODE_ACTIVE 				= "page.label.list.filter.type.active";
	public static final String MESSAGE_CODE_ARCHIVED 			= "page.label.list.filter.type.archived";

	public VersionStatusFilterType(Integer id) {
		super();
		switch (id) {
			case ContentObject.DATA_TYPE_WORKING_AND_ACTIVE:
				this.setId(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE);
				this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_AND_ACTIVE));
				this.setDisplayMessageCode(MESSAGE_CODE_WORKING_AND_ACTIVE);
				break;
		case ContentObject.DATA_TYPE_WORKING:
			this.setId(ContentObject.DATA_TYPE_WORKING);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPY));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPY);
			break;
		case ContentObject.DATA_TYPE_ACTIVE:
			this.setId(ContentObject.DATA_TYPE_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
			break;
			case ContentObject.DATA_TYPE_ARCHIVED:
			this.setId(ContentObject.DATA_TYPE_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
			break;
		default:
			break;
		}
	}

	public VersionStatusFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPY))) {
			this.setId(ContentObject.DATA_TYPE_WORKING);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPY));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE))) { 
			this.setId(ContentObject.DATA_TYPE_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED))) { 
			this.setId(ContentObject.DATA_TYPE_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
		}
	}
	
	public static List<VersionStatusFilterType> listAll() {
		List<VersionStatusFilterType> allListFilterTypes = new ArrayList<>();

		VersionStatusFilterType listFilterType = null;

		listFilterType = new VersionStatusFilterType(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE);
		allListFilterTypes.add(listFilterType);

		listFilterType = new VersionStatusFilterType(ContentObject.DATA_TYPE_WORKING);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new VersionStatusFilterType(ContentObject.DATA_TYPE_ACTIVE);
		allListFilterTypes.add(listFilterType);

		listFilterType = new VersionStatusFilterType(ContentObject.DATA_TYPE_ARCHIVED);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}
