package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.testing.TestingUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.email.TemplateVariant;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointVariantTemplateService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class TouchpointVariantTemplateEditController extends MessagepointController {
	
	public static final String REQ_PARAM_DOCUMENTID 	= "documentId";
	public static final String REQ_PARAM_SELECTION_ID = "touchpointSelectionId";
	public static final String REQ_PARAM_SELECTED_IDS = "selectedIds";

	
	@Override
	protected Map<String,Object> referenceData(HttpServletRequest request, Object commandObj, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		// Retrieve the available document sections
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS, "");
		Set<TouchpointSelection> tpSelections = new HashSet<>();
		
		if (!selectedIdsS.isEmpty()) {
			for(Long tpId : getSelectedIds(selectedIdsS)){
				tpSelections.add(TouchpointSelection.findById(tpId));
			}
			
		}
		referenceData.put("tpSelections", tpSelections);
		referenceData.put("templateVariants", TemplateVariant.findAllByTouchpoint(document));
		referenceData.put("document", document);
		
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( TouchpointSelection.class, new IdCustomEditor<>(TouchpointSelection.class) );
		binder.registerCustomEditor( TemplateVariant.class, new IdCustomEditor<>(TemplateVariant.class) );
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		Command command = null;
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS, "");
		
		if(!selectedIdsS.isEmpty()){
			command = new Command(document, getSelectedIds(selectedIdsS));
		}
		
		return command;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors)
			throws Exception {

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.SelectionsTemplateEdit)
				.setAction(Actions.Update);

		try {
			Command command = (Command) commandObj;
			ServiceExecutionContext templateEditContext = UpdateTouchpointVariantTemplateService.createContextForTouchpointSelection(command.getDocument(), command.getSelectedTemplateVariant(), command.getTouchpointSelectionIds(), command.isUseDefaultOpt());
			Service updateTouchpointVariantTemplateService = MessagepointServiceFactory.getInstance().lookupService(UpdateTouchpointVariantTemplateService.SERVICE_NAME, UpdateTouchpointVariantTemplateService.class);
			updateTouchpointVariantTemplateService.execute(templateEditContext);
			ServiceResponse templateEditServiceResponse = templateEditContext.getResponse();

			if(!templateEditServiceResponse.isSuccessful()){
				ServiceResponseConverter.convertToSpringErrors(templateEditServiceResponse, errors);
				return super.showForm(request, response, errors);
			}
			Map<String, Object> parms = TestingUtils.getContextMapParms(request);
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
		} finally {
			analyticsEvent.send();
		}
	}
	
	/**
	 * Convert selected id string to set
     */
	private Set<Long> getSelectedIds(String selectedIdsS){		
		Set<Long> selectionIds = new HashSet<>();
		if(selectedIdsS != null && !selectedIdsS.isEmpty()){
			for(String idS : selectedIdsS.split("_")){
				selectionIds.add(Long.valueOf(idS));
			}
		}
		return selectionIds;
	}
	
	public static class Command {
		private Document document;
		private Set<Long> touchpointSelectionIds	= new HashSet<>();
		private long selectedTemplateVariant;
		private boolean useDefaultOpt = true;

		public Command(Document document, Set<Long> selectedIds) {
			this.document = document;
			this.touchpointSelectionIds = selectedIds;
			if ( selectedIds.size() == 1 ) {
				TouchpointSelection tpSelection = TouchpointSelection.findById(selectedIds.iterator().next());
				if ( tpSelection == null )
					tpSelection = document.getMasterTouchpointSelection();
				
				if ( tpSelection != null ) {
					if ( tpSelection.getTemplateVariant() == null || tpSelection.getTemplateVariant().getId() == 0 ) {
						this.useDefaultOpt = true;
						this.selectedTemplateVariant = 0;
					} else {
						this.useDefaultOpt = false;
						this.selectedTemplateVariant = tpSelection.getTemplateVariant().getId();
					}
				}
			}
		}

		public Document getDocument() {
			return document;
		}

		public void setDocument(Document document) {
			this.document = document;
		}

		public Set<Long> getTouchpointSelectionIds() {
			return touchpointSelectionIds;
		}

		public void setTouchpointSelectionIds(Set<Long> touchpointSelectionIds) {
			this.touchpointSelectionIds = touchpointSelectionIds;
		}

		public long getSelectedTemplateVariant() {
			return selectedTemplateVariant;
		}

		public void setSelectedTemplateVariant(long selectedTemplateVariant) {
			this.selectedTemplateVariant = selectedTemplateVariant;
		}

		public boolean isUseDefaultOpt() {
			return useDefaultOpt;
		}

		public void setUseDefaultOpt(boolean useDefaultOpt) {
			this.useDefaultOpt = useDefaultOpt;
		}

	}
}
