package com.prinova.messagepoint.controller.touchpoints;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class TouchpointVariantListWrapper implements Serializable {

	private static final long serialVersionUID = 9159796454490740602L;

	private List<Long>		selectedIds;
	private String 			selectionName;
	private String 			userNote;
	private User 			assignedToUser;
	private String 			action;
	private int 			extType 			= 0;
	private boolean			includeOnlyActiveContent = false;

	// Export specific properties
	private Date									fromDate;
	private String									fromHour;
	
	private Date									toDate;
	private String									toHour;
	
	private boolean 								changeReportEnabled;
	private String									exportId 	= DateUtil.exportDateTimeStamp();
	private String									exportName	= "";
	
	public TouchpointVariantListWrapper(long documentId) {
		super();

		this.fromDate = DateUtil.now();
		this.toDate = DateUtil.now();
		// Default value set to last item on the hours selection list
		this.toHour = "23:00";
		this.changeReportEnabled = true; 
		this.selectedIds = new ArrayList<>();
	}

	public String getSelectionName() {
		return selectionName;
	}
	public void setSelectionName(String selectionName) {
		this.selectionName = selectionName;
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}

	public Date getFromDate() {
		return fromDate;
	}
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	
	public String getFromHour(){
		return fromHour;
	}
	public void setFromHour(String fromHour){
		this.fromHour = fromHour;
	}		

	public Date getToDate() {
		return toDate;
	}
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}	
	
	public String getToHour(){
		return toHour;
	}
	public void setToHour(String toHour){
		this.toHour = toHour;
	}		
	
	public boolean getChangeReportEnabled() {
		return changeReportEnabled;
	}
	public void setChangeReportEnabled(boolean changeReportEnabled) {
		this.changeReportEnabled = changeReportEnabled;
	}
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public List<TouchpointSelection> getSelectedList(){
		List<TouchpointSelection> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(TouchpointSelection.class, selectedId));
		}
		return selectedList;
	}

	public String getExportId() {
		return exportId;
	}

	public void setExportId(String exportId) {
		this.exportId = exportId;
	}

	public String getExportName() {
		return exportName;
	}

	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public int getExtType() {
		return extType;
	}

	public void setExtType(int extType) {
		this.extType = extType;
	}

	public boolean isIncludeOnlyActiveContent() {
		return includeOnlyActiveContent;
	}

	public void setIncludeOnlyActiveContent(boolean includeOnlyActiveContent) {
		this.includeOnlyActiveContent = includeOnlyActiveContent;
	}
}