package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

public class TouchpointContentObjectMoveToZoneValidator extends MessagepointInputValidator{

    public void validateNotGenericInputs(Object command, Errors errors) {
    	TouchpointContentObjectMoveToZoneWrapper wrapper = (TouchpointContentObjectMoveToZoneWrapper) command;
    	
    	if ( wrapper.getToZone() == null )
    		errors.reject("error.message.target.zone.must.be.selected.for.message.move");

    }
}
