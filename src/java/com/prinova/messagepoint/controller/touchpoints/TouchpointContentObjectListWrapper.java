package com.prinova.messagepoint.controller.touchpoints;

import java.util.*;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.util.DateUtil;

public class TouchpointContentObjectListWrapper {

	private List<Long>						selectedIds;
	private String 							userNote;
	private User 							assignedToUser;
	private String							actionValue;
	private String							cloneName;

	//  Export specific properties:
	private boolean                         dateFilterEnabled   	= false;
	private Date 							startDate;
	private boolean 						includeTargeting 		= true;
	private boolean 						includeContent			= true;
	private boolean 						includeAllMessages		= true;
	private boolean 						includeAllMessagesAudit	= true;
	private boolean 						exportToOneJSONFile		= true;

	private Long                    		cloneToSelectionId; 		
	
	private boolean							isSuppressedOnHold 		= false;
	private boolean							updateVariantVersioning	= true;
	private String							exportId 	= DateUtil.exportDateTimeStamp();
	private String							exportName	= "";
	private MessagepointLocale 				exportTargetLocale = null;
	private int 							extType 			= 0;
	private int								statusFilterId		= -1;
	private ConfigurableWorkflow 			actionToWorkflow;
	
	private String							whereUsedReportId 	= DateUtil.timeStamp();
	private Task				            currentLinkedTaskId;
	private Map<Long, Long> 				taskLinkedMap	= new HashMap<>();
	private Map<Long, String>				taskDescriptionMap = new HashMap<>();
	private Map<Long, Integer> 				taskTypeMap = new HashMap<>();
	private Map<Long, Long> 				taskLocaleMap = new HashMap<>();
	private Map<Long, Long> 				taskVariantMap = new HashMap<>();
	private Integer							createTaskType;
	private Long 							messagepointLocaleId;
	private String 							taskVariantId;

	public TouchpointContentObjectListWrapper() {
		super();
		this.startDate = DateUtil.now();
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public boolean isIncludeTargeting() {
		return includeTargeting;
	}
	public void setIncludeTargeting(boolean includeTargeting) {
		this.includeTargeting = includeTargeting;
	}

	public boolean isIncludeContent() {
		return includeContent;
	}
	public void setIncludeContent(boolean includeContent) {
		this.includeContent = includeContent;
	}

	public Long getCloneToSelectionId() {
		return cloneToSelectionId;
	}
	public void setCloneToSelectionId(Long cloneToSelectionId) {
		this.cloneToSelectionId = cloneToSelectionId;
	}

	public boolean isDateFilterEnabled() {
		return dateFilterEnabled;
	}
	public void setDateFilterEnabled(boolean dateFilterEnabled) {
		this.dateFilterEnabled = dateFilterEnabled;
	}	
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getCloneName() {
		return cloneName;
	}
	public void setCloneName(String cloneName) {
		this.cloneName = cloneName;
	}

	public boolean isSuppressedOnHold() {
		return isSuppressedOnHold;
	}
	public void setSuppressedOnHold(boolean isSuppressedOnHold) {
		this.isSuppressedOnHold = isSuppressedOnHold;
	}

	public boolean isUpdateVariantVersioning() {
		return updateVariantVersioning;
	}
	public void setUpdateVariantVersioning(boolean updateVariantVersioning) {
		this.updateVariantVersioning = updateVariantVersioning;
	}

	public String getTaskVariantId() {
		return taskVariantId;
	}

	public void setTaskVariantId(String taskVariantId) {
		this.taskVariantId = taskVariantId;
	}

	public List<ContentObject> getSelectedList(){
		List<ContentObject> selectedList = new ArrayList<>();
		if(this.selectedIds != null){
			for(Long selectedId : this.selectedIds){
				ContentObject contentObject = null;
				if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
					contentObject = ContentObject.findByIdActiveDataFocusCentric(selectedId);
				else if (statusFilterId == ContentObject.DATA_TYPE_WORKING)
					contentObject = ContentObject.findByIdWorkingDataFocusCentric(selectedId);
				else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
					contentObject = ContentObject.findByIdArchivedDataFocusCentric(selectedId);
				else
					contentObject = ContentObject.findByIdWorkingDataFocusCentric(selectedId);

				if (contentObject != null)
					selectedList.add(contentObject);
			}
		}
		return selectedList;
	}

	public List<ContentObjectData> getSelectedInstanceDataList(){
		List<ContentObjectData> selectedList = new ArrayList<>();
		if(this.selectedIds != null){
			for(Long selectedId : this.selectedIds){
				ContentObject contentObject = null;
				if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
					contentObject = ContentObject.findByIdActiveDataFocusCentric(selectedId);
				else if (statusFilterId == ContentObject.DATA_TYPE_WORKING)
					contentObject = ContentObject.findByIdWorkingDataFocusCentric(selectedId);
				else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
					contentObject = ContentObject.findByIdArchivedDataFocusCentric(selectedId);
				else
					contentObject = ContentObject.findByIdWorkingDataFocusCentric(selectedId);

				if (contentObject != null)
					selectedList.add(contentObject.getContentObjectData());
			}
		}
		return selectedList;
	}

	public String getExportId() {
		return exportId;
	}

	public void setExportId(String exportId) {
		this.exportId = exportId;
	}

	public String getExportName() {
		return exportName;
	}

	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public MessagepointLocale getExportTargetLocale() {
		return exportTargetLocale;
	}

	public void setExportTargetLocale(MessagepointLocale exportTargetLocale) {
		this.exportTargetLocale = exportTargetLocale;
	}

	public boolean isIncludeAllMessages() {
		return includeAllMessages;
	}

	public void setIncludeAllMessages(boolean includeAllMessages) {
		this.includeAllMessages = includeAllMessages;
	}

	public boolean isIncludeAllMessagesAudit() {
		return includeAllMessagesAudit;
	}

	public void setIncludeAllMessagesAudit(boolean includeAllMessagesAudit) {
		this.includeAllMessagesAudit = includeAllMessagesAudit;
	}

	public boolean isExportToOneJSONFile() {
		return exportToOneJSONFile;
	}

	public void setExportToOneJSONFile(boolean exportToOneJSONFile) {
		this.exportToOneJSONFile = exportToOneJSONFile;
	}

	public String getWhereUsedReportId() {
		return whereUsedReportId;
	}

	public void setWhereUsedReportId(String whereUsedReportId) {
		this.whereUsedReportId = whereUsedReportId;
	}

	public int getExtType() {
		return extType;
	}

	public void setExtType(int extType) {
		this.extType = extType;
	}

	public int getStatusFilterId() {
		return statusFilterId;
	}

	public void setStatusFilterId(int statusFilterId) {
		this.statusFilterId = statusFilterId;
	}

	public ConfigurableWorkflow getActionToWorkflow() {
		return actionToWorkflow;
	}

	public void setActionToWorkflow(ConfigurableWorkflow actionToWorkflow) {
		this.actionToWorkflow = actionToWorkflow;
	}

	public Map<Long, Long> getTaskLinkedMap() {
		return taskLinkedMap;
	}

	public void setTaskLinkedMap(Map<Long, Long> taskLinkedMap) {
		this.taskLinkedMap = taskLinkedMap;
	}

	public Task getCurrentLinkedTaskId() {
		return currentLinkedTaskId;
	}

	public void setCurrentLinkedTaskId(Task currentLinkedTaskId) {
		this.currentLinkedTaskId = currentLinkedTaskId;
	}

	public Map<Long, String> getTaskDescriptionMap() {
		return taskDescriptionMap;
	}

	public void setTaskDescriptionMap(Map<Long, String> taskDescriptionMap) {
		this.taskDescriptionMap = taskDescriptionMap;
	}

	public Map<Long, Integer> getTaskTypeMap() {
		return taskTypeMap;
	}

	public void setTaskTypeMap(Map<Long, Integer> taskTypeMap) {
		this.taskTypeMap = taskTypeMap;
	}

	public Map<Long, Long> getTaskLocaleMap() {
		return taskLocaleMap;
	}

	public void setTaskLocaleMap(Map<Long, Long> taskLocaleMap) {
		this.taskLocaleMap = taskLocaleMap;
	}

	public Map<Long, Long> getTaskVariantMap() {
		return taskVariantMap;
	}

	public void setTaskVariantMap(Map<Long, Long> taskVariantMap) {
		this.taskVariantMap = taskVariantMap;
	}

	public Integer getCreateTaskType() {
		return createTaskType;
	}

	public void setCreateTaskType(Integer createTaskType) {
		this.createTaskType = createTaskType;
	}

	public Long getMessagepointLocaleId() {
		return messagepointLocaleId;
	}

	public void setMessagepointLocaleId(Long messagepointLocaleId) {
		this.messagepointLocaleId = messagepointLocaleId;
	}
}
