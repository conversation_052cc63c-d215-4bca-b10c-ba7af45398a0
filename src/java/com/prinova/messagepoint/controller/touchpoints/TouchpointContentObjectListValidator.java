package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.wrapper.AsyncContentObjectListVO;
import com.prinova.messagepoint.model.wrapper.AsyncContentObjectListWrapper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import com.prinova.messagepoint.wtu.ReferencableObject;
import org.springframework.validation.Errors;

import java.util.*;
import java.util.function.Predicate;

public class TouchpointContentObjectListValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointContentObjectListWrapper wrapper = (TouchpointContentObjectListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}

		switch (action) {
			case TouchpointContentObjectListController.ACTION_CLONE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanClone);
				break;
			case TouchpointContentObjectListController.ACTION_REASSIGN:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanReassign);
				break;
			case TouchpointContentObjectListController.ACTION_CREATE_WORKING_COPY:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanCreateWorkingCopy);
				break;
			case TouchpointContentObjectListController.ACTION_DISCARD_WORKING:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanDiscard);
				break;
			case TouchpointContentObjectListController.ACTION_ARCHIVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanArchive);
				break;
			case TouchpointContentObjectListController.ACTION_DELETE_ARCHIVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanDeleteArchive);
				break;
			case TouchpointContentObjectListController.ACTION_RELEASE_FOR_APPROVAL:
			case TouchpointContentObjectListController.ACTION_ACTIVATE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanReleaseForApproval);
				break;
			case TouchpointContentObjectListController.ACTION_PUSH_TO_GLOBAL:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanPushToGlobal);
				break;
			case TouchpointContentObjectListController.ACTION_PULL_FROM_GLOBAL:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanPullFromGlobal);
				break;
			case TouchpointContentObjectListController.ACTION_MOVE_TO_GLOBAL:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanMoveToGlobal);
				break;
			case TouchpointContentObjectListController.ACTION_MAKE_GLOBAL:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanMakeGlobal);
				break;
			case TouchpointContentObjectListController.ACTION_APPROVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanApprove);
				break;
			case TouchpointContentObjectListController.ACTION_REJECT:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanReject);
				break;
			case TouchpointContentObjectListController.ACTION_HOLD:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanHold);
				break;
			case TouchpointContentObjectListController.ACTION_UNHOLD:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanUnhold);
				break;
			case TouchpointContentObjectListController.ACTION_SUPPRESS:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanSuppress);
				break;
			case TouchpointContentObjectListController.ACTION_RESTORE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanRestore);
				break;
			case TouchpointContentObjectListController.ACTION_RELEASE_FROM_TRANSLATION:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanReleaseFromTranslation);
				break;
			case TouchpointContentObjectListController.ACTION_RETRY_TRANSLATION:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanRetry);
				break;
			case TouchpointContentObjectListController.ACTION_TRANSLATION_REASSIGN:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncContentObjectListVO.ContentObjectListVOFlags::isCanTranslationReassign);
				break;

		}

		if (action==TouchpointContentObjectListController.ACTION_REASSIGN) {
			
			List<String> nameCurrentlyInEdit = new ArrayList<>();
			for (ContentObject instance : wrapper.getSelectedList() ) {
				if ( !instance.canReassign() )
					nameCurrentlyInEdit.add( instance.getName() );
			}
			if (!nameCurrentlyInEdit.isEmpty())
				errors.reject("error.message.cannot.reassign.asset.currently.being.edited.parameterized", new String[] {StringUtil.join(nameCurrentlyInEdit, ", ")}, "");
			
		} else if (action==TouchpointContentObjectListController.ACTION_EXPORT) {
		   if (wrapper.isDateFilterEnabled()) {
			   if (wrapper.getStartDate()==null) {
				errors.reject("error.export.message.filter.date.required");			   
			   } else {
				   Date today = DateUtil.getDateWithMaxTime((DateUtil.now()));
				   if (wrapper.getStartDate().after(today)) {
					   errors.reject("error.export.message.filter.date.cannot.be.in.future");
				   }
			   }
		   }
		} else if (action == TouchpointContentObjectListController.ACTION_CREATE_WORKING_COPY){
			List<ContentObject> selectedMsgList = wrapper.getSelectedList();
			for(ContentObject contentObject : selectedMsgList) {
				// Find all the content objects that are being used by this content object
				List<ContentObject> contentObjects = contentObject.findAllAssetsUsedIn();
				if (!contentObjects.isEmpty()) {
					for (ContentObject co : contentObjects) {
						if (!co.hasActiveData() && !co.hasWorkingData()) {
							errors.reject("error.content.object.is.being.referenced.by.archived.on.creating.copy",
									new String[]{contentObject.getObjectTypeForDisplay().toLowerCase() + " \"" + contentObject.getName() + "\""},
									null);
							return;
						}
					}
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_DISCARD_WORKING){
			// Discard working copy
			List<ContentObject> selectedMsgList = wrapper.getSelectedList();
			for(ContentObject instance : selectedMsgList){
				if ( instance.getIsTouchpointLocal() && !instance.hasActiveData() ) {
					if ( instance.isReferenced() ) {
						errors.reject("error.local.content.is.being.referenced.on.discard.working.copy");
						break;
					}
				}
			}
		} else if ( action == TouchpointContentObjectListController.ACTION_DELETE_ARCHIVE ) {

		} else if ( action == TouchpointContentObjectListController.ACTION_ARCHIVE ) {
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				List<ReferencableObject> directReferences = contentObject.getDirectReferences();
				boolean referenced = directReferences != null && !directReferences.isEmpty();
				if (referenced) {
					for(ReferencableObject referencableObject : directReferences) {
						Object targetObjct = referencableObject.getTargetObject();
						if (targetObjct instanceof ContentObjectData) {
							ContentObjectData targetMessageInstance = (ContentObjectData) targetObjct;
							ContentObject targetMessage = targetMessageInstance.getContentObject();
							if(!targetMessage.hasWorkingData()&&!targetMessage.hasActiveData()&&targetMessageInstance.isArchived()){
								continue;
							}
							errors.reject("error.content.object.is.being.referenced.on.archive",
									new String[]{contentObject.getObjectTypeForDisplay().toLowerCase()},
									null);
							break;
						}
					}
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_RELEASE_FOR_APPROVAL){
			// Release for approval
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				// Check whether this object references only working copy local asset
				List<ContentObject> referencedNoActiveLocalAssets = contentObject.findAllReferencedNonActiveLocalAssets();
				StringBuilder referencedAssetNames = new StringBuilder();
				for (ContentObject referencedNoActiveLocalAsset: referencedNoActiveLocalAssets){
					referencedAssetNames.append((referencedAssetNames.length() > 0) ? ", " : "").append(referencedNoActiveLocalAsset.getName());
				}
				if (!referencedNoActiveLocalAssets.isEmpty()) {
					errors.reject(	"error.message.activate.with.referenced.non.active.local.asset",
							new String[] { contentObject.getName(), referencedAssetNames.toString()},
							null);
					break;
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_CLONE){
			String label = ApplicationUtil.getMessage("page.label.name");
			MessagepointInputValidationUtil.validateStringValue(label, wrapper.getCloneName().trim(), true, 3, 96, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
		} else if(action == TouchpointContentObjectListController.ACTION_PUSH_TO_GLOBAL){
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				if(contentObject.isLocalSmartText()){
					// Check local active copy exists
					if(!contentObject.hasActiveData()){
						errors.reject("error.assets.push.no.local.active.copy");
						break;
					}
					// Check global working copy exists
					if(contentObject.getGlobalParentObject() != null){
						if(contentObject.getGlobalParentObject().hasWorkingData()){
							errors.reject("error.assets.push.global.working.copy.exists");
							break;
						}
					}
					// Check the LST references another LST
					for(ContentObjectAssociation ca : contentObject.getContentObjectAssociations()){
						Content content = ca.getContent();
						if(content != null && content.hasLocalSmartTexts()){
							errors.reject("error.assets.local.global.reference.other.local.assets");
							break;
						}
					}
				}
				if(contentObject.isLocalImage()){
					// Check local active copy exists
					if(!contentObject.hasActiveData()){
						errors.reject("error.assets.push.no.local.active.copy");
						break;
					}
					// Check global working copy exists
					if(contentObject.getGlobalParentObject() != null){
						if(contentObject.getGlobalParentObject().hasWorkingData()){
							errors.reject("error.assets.push.global.working.copy.exists");
							break;
						}
					}
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_PULL_FROM_GLOBAL){
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				if(contentObject.isLocalSmartText()){
					// Check global active copy exists
					if(contentObject.getGlobalParentObject() != null){
						if(!contentObject.getGlobalParentObject().hasActiveData()){
							errors.reject("error.assets.pull.no.global.active.copy");
							break;
						}
					}else{
						errors.reject("error.assets.pull.no.global.active.copy");
						break;
					}
					// Check local working copy exists
					if(contentObject.hasWorkingData()){
						errors.reject("error.assets.pull.local.working.copy.exists");
						break;
					}
				}
				if(contentObject.isLocalImage()){
					// Check local working copy exists
					if(contentObject.hasWorkingData()){
						errors.reject("error.assets.pull.local.working.copy.exists");
						break;
					}
					// Check global active copy exists
					if(contentObject.getGlobalParentObject() != null){
						if(!contentObject.getGlobalParentObject().hasActiveData()){
							errors.reject("error.assets.pull.no.global.active.copy");
							break;
						}
						//!!! NOTE: Image library can't reference another image library as local image DOES NOT support image referencing currently
						for(ContentObjectAssociation ca : contentObject.getGlobalParentObject().getContentObjectAssociations()){
							if(ca.getReferencingImageLibrary() != null){
								errors.reject("error.assets.pull.image.library.references.another.image");
								break;
							}
						}
					}
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_MOVE_TO_GLOBAL){
			List<ContentObject> selectedList = wrapper.getSelectedList();
			for (ContentObject selectedAsset : selectedList ) {
				if (selectedAsset.isLocalSmartText()) {
					// Check the local smart text references another local smart text which is not in the selected list
					List<ContentObject> referencedList = selectedAsset.getReferencedLocalSmartTexts();
					referencedList.removeAll(selectedList);
					if(!referencedList.isEmpty()){
						errors.reject("error.assets.local.global.reference.other.local.assets");
						break;
					}
				}else if (selectedAsset.isLocalImage()) {
					// Check the local image references another local image which is not in the selected list
					List<ContentObject> referencedList = selectedAsset.getReferencedLocalImages();
					referencedList.removeAll(selectedList);
					if(!referencedList.isEmpty()){
						errors.reject("error.assets.local.global.reference.other.local.assets");
						break;
					}
				}
			}
		} else if(action == TouchpointContentObjectListController.ACTION_MAKE_GLOBAL){
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				if(contentObject.isLocalSmartText()){
					// Check local active copy exists
					if(!contentObject.hasActiveData()){
						errors.reject("error.assets.make.global.no.local.active.copy");
						break;
					}
					// Check local working copy exists
					if(contentObject.hasWorkingData()){
						errors.reject("error.assets.make.global.local.working.copy.exists");
						break;
					}
					// Check global working copy exists
					if(contentObject.getGlobalParentObject() != null){
						if(contentObject.getGlobalParentObject().hasWorkingData()){
							errors.reject("error.assets.make.global.global.working.copy.exists");
							break;
						}
					}
					// Check the LST references another LST
					if(!contentObject.getReferencedLocalSmartTexts().isEmpty()){
						errors.reject("error.assets.local.global.reference.other.local.assets");
						break;
					}
					if(contentObject.isDynamicVariantEnabled()){
						for(ContentObjectAssociation coa : ContentObjectAssociation.findAllLatestForDynamicAsset(contentObject)){
							Content content = coa.getContent();
							if(content != null && content.hasLocalSmartTexts()){
								errors.reject("error.assets.local.global.reference.other.local.assets");
								break;
							}
						}
					}
				}
			}
		}else if(action == TouchpointContentObjectListController.ACTION_APPROVE ||
				action == TouchpointContentObjectListController.ACTION_ACTIVATE){
			for (ContentObject contentObject : wrapper.getSelectedList() ) {
				Document messageDocument = contentObject.getFirstDocumentDelivery();

				TouchpointSelection ts = null;
				if (contentObject.isVariantType()) {
					ts = contentObject.getOwningTouchpointSelection();
				} else {
					ts = messageDocument.getMasterTouchpointSelection();
				}
				
				if(ts != null){
					Set<TouchpointSelection> notFinalApprovedParents = new HashSet<>();
					StringBuilder notFinalApprovedParentNames = new StringBuilder("[");
					validateFinalApproval(ts, notFinalApprovedParents);
					
					if (!notFinalApprovedParents.isEmpty()) {
						for (TouchpointSelection notApprovedTpSelection : notFinalApprovedParents) {
							notFinalApprovedParentNames.append(notApprovedTpSelection.getName()).append(",");
						}
						if (notFinalApprovedParentNames.charAt(notFinalApprovedParentNames
								.length() - 1) == ',') {
							notFinalApprovedParentNames = new StringBuilder(notFinalApprovedParentNames
                                    .substring(0, notFinalApprovedParentNames.length() - 1));
						}
						notFinalApprovedParentNames.append("]");
						if (notFinalApprovedParentNames.length() > 0) {
							errors.reject("error.message.selection.final.approval.out.of.sequence", new String[] {notFinalApprovedParentNames.toString()}, "");
						}
					}
				}


				if(action == TouchpointContentObjectListController.ACTION_ACTIVATE) {
					// Check whether this object references only working copy local asset
					List<ContentObject> referencedNoActiveLocalAssets = contentObject.findAllReferencedNonActiveLocalAssets();
					StringBuilder referencedAssetNames = new StringBuilder();
					for (ContentObject referencedNoActiveLocalAsset: referencedNoActiveLocalAssets){
						referencedAssetNames.append((referencedAssetNames.length() > 0) ? ", " : "").append(referencedNoActiveLocalAsset.getName());
					}
					if (!referencedNoActiveLocalAssets.isEmpty()) {
						errors.reject(	"error.message.activate.with.referenced.non.active.local.asset",
								new String[] { contentObject.getName(), referencedAssetNames.toString()},
								null);
						break;
					}
				}
			}
		}
	}

	private void validateActionPermission(List<ContentObject> contentObjects, Errors errors, String errorMessage, Predicate<AsyncContentObjectListVO.ContentObjectListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( contentObjects.isEmpty() )
			errors.reject(errorMessage);

		for (ContentObject contentObject : contentObjects) {
			AsyncContentObjectListVO vo = new AsyncContentObjectListVO();
			vo.setContentObject(contentObject);

			AsyncContentObjectListVO.ContentObjectListVOFlags flags = new AsyncContentObjectListVO.ContentObjectListVOFlags();
			AsyncContentObjectListWrapper.setActionFlags(contentObject, flags, request);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}

	private boolean validateFinalApproval(TouchpointSelection tpSelection, Set<TouchpointSelection> notFinalApprovedParents) {
		if (tpSelection.isAwaitingFinalApproval()) {
			Set<TouchpointSelection> dependingAncesters = tpSelection.getDependingAncesters();
			boolean parentNotAllApproved = false;
			StringBuilder parentNotApprovedNamesStr = new StringBuilder(" ");
			Set<String> parentNotApprovedNames = new HashSet<>();
			for (TouchpointSelection ancester : dependingAncesters) {
				if (!ancester.isActive() && !ancester.isMaster()) {
					parentNotAllApproved = true;
					notFinalApprovedParents.add(ancester);
					if (!parentNotApprovedNames.contains(ancester.getName())) {
						parentNotApprovedNamesStr.append(ancester.getName()).append(", ");
					}
					parentNotApprovedNames.add(ancester.getName());
				}
			}
			if (parentNotAllApproved) {
				return false;
			}
		}
		return true;
	}
}
