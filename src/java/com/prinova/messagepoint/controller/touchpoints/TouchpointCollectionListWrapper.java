package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.util.HibernateUtil;

public class TouchpointCollectionListWrapper {
	private List<Long>				selectedIds;
	private String 					actionValue;
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}
	
	public List<TouchpointCollection> getSelectedList(){
		List<TouchpointCollection> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(TouchpointCollection.class, selectedId));
		}
		return selectedList;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}	
}
