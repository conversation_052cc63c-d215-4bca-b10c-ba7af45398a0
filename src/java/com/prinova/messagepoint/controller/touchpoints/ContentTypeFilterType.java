package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class ContentTypeFilterType extends StaticType {
	private static final long serialVersionUID = 546293711499914404L;

	public static final int ID_ANY	 			= 1;
	public static final int ID_CUSTOM 			= 2;
	public static final int ID_SUPPRESS			= 3;
	public static final int ID_REFERENCED		= 4;
	public static final int ID_GLOBAL 			= 5;

	public static final String MESSAGE_CODE_ANY 		= "page.label.list.filter.type.any";
	public static final String MESSAGE_CODE_CUSTOM 		= "page.label.list.filter.type.custom";
	public static final String MESSAGE_CODE_REFERENCED	= "page.label.list.filter.type.sap";
	public static final String MESSAGE_CODE_SUPPRESS	= "page.label.list.filter.type.suppress";
	public static final String MESSAGE_CODE_GLOBAL 		= "page.label.list.filter.type.global";

	public ContentTypeFilterType(Integer id) {
		super();
		switch (id) {
		case ID_ANY:
			this.setId(ID_ANY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ANY));
			this.setDisplayMessageCode(MESSAGE_CODE_ANY);
			break;		
		case ID_CUSTOM:
			this.setId(ID_CUSTOM);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_CUSTOM));
			this.setDisplayMessageCode(MESSAGE_CODE_CUSTOM);
			break;
		case ID_REFERENCED:
			this.setId(ID_REFERENCED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED));
			this.setDisplayMessageCode(MESSAGE_CODE_REFERENCED);
			break;
		case ID_SUPPRESS:
			this.setId(ID_SUPPRESS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SUPPRESS));
			this.setDisplayMessageCode(MESSAGE_CODE_SUPPRESS);
			break;
		case ID_GLOBAL:
			this.setId(ID_GLOBAL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_GLOBAL));
			this.setDisplayMessageCode(MESSAGE_CODE_GLOBAL);
			break;			
		default:
			break;
		}
	}

	public ContentTypeFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_CUSTOM))) { 
			this.setId(ID_CUSTOM);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_CUSTOM));
			this.setDisplayMessageCode(MESSAGE_CODE_CUSTOM);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED))) { 
			this.setId(ID_REFERENCED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED));
			this.setDisplayMessageCode(MESSAGE_CODE_REFERENCED);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_SUPPRESS))) { 
			this.setId(ID_SUPPRESS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_SUPPRESS));
			this.setDisplayMessageCode(MESSAGE_CODE_SUPPRESS);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ANY))) { 
			this.setId(ID_REFERENCED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ANY));
			this.setDisplayMessageCode(MESSAGE_CODE_ANY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_REFERENCED))) { 
			this.setId(ID_GLOBAL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_GLOBAL));
			this.setDisplayMessageCode(MESSAGE_CODE_GLOBAL);
		} 
	}
	
	public static List<ContentTypeFilterType> listAll() {
		List<ContentTypeFilterType> allListFilterTypes = new ArrayList<>();

		ContentTypeFilterType listFilterType = null;

		listFilterType = new ContentTypeFilterType(ID_ANY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ContentTypeFilterType(ID_CUSTOM);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ContentTypeFilterType(ID_SUPPRESS);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ContentTypeFilterType(ID_REFERENCED);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ContentTypeFilterType(ID_GLOBAL);
		allListFilterTypes.add(listFilterType);
		
		return allListFilterTypes;
	}
}
