package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.ComplexValue;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.TpCollectionTouchpointAssignment;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;

public class TouchpointCollectionEditWrapper {
	private TouchpointCollection 		touchpointCollection;
	private List<Document> 				documents 				= new ArrayList<>();
	private MetadataFormEditWrapper 	formWrapper;
	private String						outputFilename;
	private String						outputDocumentTitle;

	public TouchpointCollectionEditWrapper() {
		new TouchpointCollectionEditWrapper((MetadataFormDefinition)null);
	}
	
	public TouchpointCollectionEditWrapper( MetadataFormDefinition metadataFormDefinition ) {
		super();
		this.touchpointCollection 	= new TouchpointCollection();
		if ( metadataFormDefinition != null )
			this.formWrapper = new MetadataFormEditWrapper( metadataFormDefinition );
	}
	
	public TouchpointCollectionEditWrapper(TouchpointCollection touchpointCollection){
		this.touchpointCollection = touchpointCollection;
		if(touchpointCollection.getId() > 0){
			for(TpCollectionTouchpointAssignment tpAssignment : touchpointCollection.getTpCollectionTouchpointsSortedOnOrder()){
				this.documents.add(tpAssignment.getDocument());
			}
		}
		if ( touchpointCollection.getMetadataForm() != null )
			this.formWrapper				= new MetadataFormEditWrapper( touchpointCollection.getMetadataForm() );
		
		ComplexValue outputFilename = touchpointCollection.getOutputFilename();
		if ( outputFilename != null )
			setOutputFilename( outputFilename.getViewContent() );
		ComplexValue outputDocumentTitle = touchpointCollection.getOutputDocumentTitle();
		if ( outputDocumentTitle != null )
			setOutputDocumentTitle( outputDocumentTitle.getViewContent() );
	}
	
	public TouchpointCollection getTouchpointCollection() {
		return touchpointCollection;
	}

	public void setTouchpointCollection(TouchpointCollection touchpointCollection) {
		this.touchpointCollection = touchpointCollection;
	}

	public List<Document> getDocuments() {
		return documents;
	}

	public void setDocuments(List<Document> documents) {
		this.documents = documents;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}

	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	public String getOutputFilename() {
		return outputFilename;
	}

	public void setOutputFilename(String outputFilename) {
		this.outputFilename = outputFilename;
	}

	public String getOutputDocumentTitle() {
		return outputDocumentTitle;
	}

	public void setOutputDocumentTitle(String outputDocumentTitle) {
		this.outputDocumentTitle = outputDocumentTitle;
	}
	
}
