package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AssignmentFilterType extends StaticType {
	private static final long serialVersionUID = 546293711499914404L;

	public static final int ID_ALL 					= 1;
	public static final int ID_MY			 		= 2;
	public static final int ID_MASTER				= 3;
	public static final int ID_VARIANT				= 4;
	public static final int ID_MESSAGE				= 5;
	public static final int ID_COMMUNICATION		= 6;
	public static final int ID_UNCHANGEABLE			= 7;
	public static final int ID_EDITABLE				= 8;
	public static final int ID_OTHERS				= 9;
	public static final int ID_LATEST				= 10;
	public static final int ID_ASSIGNED				= 11;
	public static final int ID_CREATED				= 12;

	public static final String MESSAGE_CODE_ALL 			= "page.label.list.filter.type.all";	
	public static final String MESSAGE_CODE_MY			 	= "page.label.list.filter.type.my";
	public static final String MESSAGE_CODE_MASTER			= "page.label.list.filter.type.master";
	public static final String MESSAGE_CODE_VARIANT			= "page.label.list.filter.type.variant";
	public static final String MESSAGE_CODE_MESSAGE			= "page.label.list.filter.type.message";
	public static final String MESSAGE_CODE_COMMUNICATION	= "page.label.list.filter.type.interactive";
	public static final String MESSAGE_CODE_UNCHANGEABLE	= "page.label.list.filter.type.unchangeable";
	public static final String MESSAGE_CODE_EDITABLE		= "page.label.list.filter.type.editable";
	public static final String MESSAGE_CODE_OTHERS			= "page.label.list.filter.type.others";
	public static final String MESSAGE_CODE_LATEST			= "page.label.list.filter.type.latest";
	public static final String MESSAGE_CODE_ASSIGNED		= "page.label.list.filter.type.assigned";
	public static final String MESSAGE_CODE_CREATED			= "page.label.list.filter.type.created";

	public AssignmentFilterType(Integer id) {
		super();
		switch (id) {
		case ID_ALL:
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
			break;
		case ID_MY:
			this.setId(ID_MY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MY));
			this.setDisplayMessageCode(MESSAGE_CODE_MY);
			break;
		case ID_MASTER:
			this.setId(ID_MASTER);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MASTER));
			this.setDisplayMessageCode(MESSAGE_CODE_MASTER);
			break;
		case ID_VARIANT:
			this.setId(ID_VARIANT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_VARIANT));
			this.setDisplayMessageCode(MESSAGE_CODE_VARIANT);
			break;	
		case ID_MESSAGE:
			this.setId(ID_MESSAGE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MESSAGE));
			this.setDisplayMessageCode(MESSAGE_CODE_MESSAGE);
			break;	
		case ID_COMMUNICATION:
			this.setId(ID_COMMUNICATION);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_COMMUNICATION));
			this.setDisplayMessageCode(MESSAGE_CODE_COMMUNICATION);
			break;	
		case ID_UNCHANGEABLE:
			this.setId(ID_UNCHANGEABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_UNCHANGEABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_UNCHANGEABLE);
			break;	
		case ID_EDITABLE:
			this.setId(ID_EDITABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_EDITABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_EDITABLE);
			break;	
		case ID_OTHERS:
			this.setId(ID_OTHERS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_OTHERS));
			this.setDisplayMessageCode(MESSAGE_CODE_OTHERS);
			break;
		case ID_LATEST:
			this.setId(ID_LATEST);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_LATEST));
			this.setDisplayMessageCode(MESSAGE_CODE_LATEST);
			break;
		case ID_ASSIGNED:
			this.setId(ID_ASSIGNED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ASSIGNED));
			this.setDisplayMessageCode(MESSAGE_CODE_ASSIGNED);
			break;
		case ID_CREATED:
			this.setId(ID_CREATED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_CREATED));
			this.setDisplayMessageCode(MESSAGE_CODE_CREATED);
			break;
		default:
			break;
		}
	}

	public AssignmentFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ALL))) { 
			this.setId(ID_ALL);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ALL));
			this.setDisplayMessageCode(MESSAGE_CODE_ALL);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MY))) { 
			this.setId(ID_MY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MY));
			this.setDisplayMessageCode(MESSAGE_CODE_MY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MASTER))) { 
			this.setId(ID_MASTER);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MASTER));
			this.setDisplayMessageCode(MESSAGE_CODE_MASTER);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_VARIANT))) { 
			this.setId(ID_VARIANT);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_VARIANT));
			this.setDisplayMessageCode(MESSAGE_CODE_VARIANT);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MESSAGE))) { 
			this.setId(ID_MESSAGE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MESSAGE));
			this.setDisplayMessageCode(MESSAGE_CODE_MESSAGE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_COMMUNICATION))) { 
			this.setId(ID_COMMUNICATION);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_COMMUNICATION));
			this.setDisplayMessageCode(MESSAGE_CODE_COMMUNICATION);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_UNCHANGEABLE))) { 
			this.setId(ID_UNCHANGEABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_UNCHANGEABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_UNCHANGEABLE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_EDITABLE))) { 
			this.setId(ID_EDITABLE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_EDITABLE));
			this.setDisplayMessageCode(MESSAGE_CODE_EDITABLE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_OTHERS))) { 
			this.setId(ID_OTHERS);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_OTHERS));
			this.setDisplayMessageCode(MESSAGE_CODE_OTHERS);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_LATEST))) { 
			this.setId(ID_LATEST);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_LATEST));
			this.setDisplayMessageCode(MESSAGE_CODE_LATEST);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ASSIGNED))) {
			this.setId(ID_ASSIGNED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ASSIGNED));
			this.setDisplayMessageCode(MESSAGE_CODE_ASSIGNED);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_CREATED))) {
			this.setId(ID_CREATED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_CREATED));
			this.setDisplayMessageCode(MESSAGE_CODE_CREATED);
		}
	}
	
	public static List<AssignmentFilterType> listAllStandard() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;
		
		listFilterType = new AssignmentFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MY);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}

	public static List<AssignmentFilterType> listAllForOrderList() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;

		listFilterType = new AssignmentFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);

		listFilterType = new AssignmentFilterType(ID_MY);
		allListFilterTypes.add(listFilterType);

		listFilterType = new AssignmentFilterType(ID_CREATED);
		allListFilterTypes.add(listFilterType);

		listFilterType = new AssignmentFilterType(ID_ASSIGNED);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
	
	public static List<AssignmentFilterType> listAllIncludingVariant() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;
		
		listFilterType = new AssignmentFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MASTER);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_VARIANT);
		allListFilterTypes.add(listFilterType);		

		listFilterType = new AssignmentFilterType(ID_EDITABLE);
		allListFilterTypes.add(listFilterType);		

		return allListFilterTypes;
	}
	
	public static List<AssignmentFilterType> listAllIncludingInteractive() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;
		
		listFilterType = new AssignmentFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MESSAGE);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_COMMUNICATION);
		allListFilterTypes.add(listFilterType);		

		return allListFilterTypes;
	}

	public static List<AssignmentFilterType> listSyncFilters() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;
		
		listFilterType = new AssignmentFilterType(ID_MESSAGE);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_OTHERS);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
	
	public static List<AssignmentFilterType> listInsertScheduleFilters() {
		List<AssignmentFilterType> allListFilterTypes = new ArrayList<>();

		AssignmentFilterType listFilterType = null;
		
		listFilterType = new AssignmentFilterType(ID_ALL);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_MY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new AssignmentFilterType(ID_LATEST);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}