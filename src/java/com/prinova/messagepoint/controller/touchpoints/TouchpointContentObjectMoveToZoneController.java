package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.UpdateContentObjectMoveToZoneService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TouchpointContentObjectMoveToZoneController extends MessagepointController {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(TouchpointContentObjectMoveToZoneController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 		= "documentId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		
		// From zone
		Zone fromZone = contentObject.getZone();

		// To zone list
		List<Zone> toZoneList = new ArrayList<>();
		Set<Zone> visibleZones = document.getVisibleZonesOfUser();
		for(Zone zone : visibleZones){
			if(		fromZone.getDataGroup() != null && zone.getDataGroup() != null &&
					fromZone.getDataGroup().getId() == zone.getDataGroup().getId() &&
					fromZone.getContentTypeId() == zone.getContentTypeId() &&
					fromZone.getSubContentTypeId() == zone.getSubContentTypeId() &&
					fromZone.getId() != zone.getId()){
				toZoneList.add(zone);
			}
		}
		Collections.sort(toZoneList, new Comparator<>() {
            public int compare(Zone o1, Zone o2) {
                return o1.getFriendlyName().compareTo(o2.getFriendlyName());
            }
        });
		
		referenceData.put("fromZone", fromZone);
		referenceData.put("toZoneList", toZoneList);
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Zone.class, new IdCustomEditor<>(Zone.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		
		return new TouchpointContentObjectMoveToZoneWrapper(contentObject);
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.MessageMove)
				.setAction(Actions.Zone.Move);
		try {
			TouchpointContentObjectMoveToZoneWrapper command = (TouchpointContentObjectMoveToZoneWrapper)commandObj;

			ServiceExecutionContext context = UpdateContentObjectMoveToZoneService.createContext(command.getContentObject(), command.getFromZone(), command.getToZone());
			Service updateContentObjectMoveToZoneService = MessagepointServiceFactory.getInstance().lookupService(UpdateContentObjectMoveToZoneService.SERVICE_NAME, UpdateContentObjectMoveToZoneService.class);
			updateContentObjectMoveToZoneService.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				return showForm(request, response, errors);
			} else {
				return new ModelAndView( new RedirectView(getSuccessView()), getSuccessViewParms(request));
			}
		} finally {
			analyticsEvent.send();
		}
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		long contentObjectId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
		int dataType = ServletRequestUtils.getIntParameter(request, ContentObject.REQ_PARM_DATA_TYPE, ContentObject.DATA_TYPE_WORKING);
		HashMap<String,Object> params = new HashMap<>();
		params.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
		params.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);
		params.put(ContentObject.REQ_PARM_DATA_TYPE, dataType);
		params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		return params;
	}
}