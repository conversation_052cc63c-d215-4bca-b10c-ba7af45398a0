package com.prinova.messagepoint.controller.touchpoints;

import java.util.List;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointCollectionListDetailValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object command, Errors errors) {
		TouchpointCollectionListDetailWrapper wrapper = (TouchpointCollectionListDetailWrapper)command;
		int action = Integer.valueOf(wrapper.getActionValue());
	
		if(action == TouchpointCollectionListDetailController.ACTION_DELETE_COMP_PACKS){
			List<CompositionFileSet> compositionFileSetList = wrapper.getSelectedCompositionFiles();
			for(CompositionFileSet compFileSet : compositionFileSetList){
				if(compFileSet.isReferenced()){
					errors.reject("error.composition.package.is.being.referenced.on.delete");
					break;
				}
			}
		}
	}
}
