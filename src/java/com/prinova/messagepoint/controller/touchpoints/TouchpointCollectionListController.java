package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.admin.EventType;
import com.prinova.messagepoint.model.admin.SystemState;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.BulkDeleteTouchpointCollectionsService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class TouchpointCollectionListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointCollectionListController.class);

	public static final String REQ_PARM_ACTION 				= "action";
	public static final String PARAMETER_TP_COLLECTION_ID 	= "tpCollectionId";
	
	public static final int ACTION_UPDATE 					= 1;
	public static final int ACTION_DELETE 					= 2;
	
	private String collectionEditRedirect;

	public String getCollectionEditRedirect() {
		return collectionEditRedirect;
	}

	public void setCollectionEditRedirect(String collectionEditRedirect) {
		this.collectionEditRedirect = collectionEditRedirect;
	}
	
	@SuppressWarnings("unchecked")
	public Map<String, Object> referenceData(HttpServletRequest request) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		
		referenceData.put("tpCollections", TouchpointCollection.findAll());
		
		
		// START TEMP: COLLECTION PROOF LINK
		List<Long> testIds = new ArrayList<>();
		List<TestScenario> errorTestList = HibernateUtil.getManager().getObjectsAdvanced(TestScenario.class, MessagepointRestrictions.and(MessagepointRestrictions.eq("complete", true), MessagepointRestrictions.eq("error", false)));
		for ( TestScenario errorTest : errorTestList )
			testIds.add(errorTest.getId());
		
		List<Long> testIds2 = new ArrayList<>();
		String hql = "select de FROM DeliveryEvent de " +
					"	left join de.deliveryEventStateTransitions st on st.deliveryEventStateId in (:errorStateIds) " +
					" WHERE st.id is null and de.eventTypeId = " + EventType.TYPE_TEST;
		Map<String, Object> params = new HashMap<>();
		params.put("errorStateIds", SystemState.getErrorStates());
		List<DeliveryEvent> errorDeliveryList = (List<DeliveryEvent>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
		for ( DeliveryEvent errorDelivery : errorDeliveryList )
			testIds2.add(errorDelivery.getItem().getId());

		// Retain the common ones
		testIds.retainAll(testIds2);
		
		Collections.sort(testIds);
		
		TestScenario mostRecentTest = !testIds.isEmpty() ? TestScenario.findById(testIds.get(testIds.size()-1)) : null;
		referenceData.put("testCollectionLink", mostRecentTest);
		// END TEMP: COLLECTION PROOF LINK
		
		referenceData.put("hasMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_COLLECTION).isEmpty());
		referenceData.put("metadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_COLLECTION));

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	public Object formBackingObject(HttpServletRequest request) throws Exception{
		return new TouchpointCollectionListWrapper();
	}
	
	public ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception{

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.CollectionList);

		try {
			int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
			TouchpointCollectionListWrapper command = (TouchpointCollectionListWrapper) commandObj;

			switch(action){
				case ACTION_DELETE:{
					analyticsEvent.setAction(Actions.Delete);
					List<TouchpointCollection> list = command.getSelectedList();
					ServiceExecutionContext context = BulkDeleteTouchpointCollectionsService.createContext(list);
					Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteTouchpointCollectionsService.SERVICE_NAME,
							BulkDeleteTouchpointCollectionsService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(BulkDeleteTouchpointCollectionsService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for(TouchpointCollection tpCollection: list){
							sb.append(" id ").append(tpCollection.getId());
						}
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}
			}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}
}
