package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.AsyncSectionImageThumbnailController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointWidgetController extends MessagepointController {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(TouchpointWidgetController.class);

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long documentId 		= ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
		long channelContextId	= ServletRequestUtils.getLongParameter(request, AsyncSectionImageThumbnailController.REQ_PARM_CHANNEL_CONTEXT_ID, -1L);

        Document contextDoc 	= Document.findById(documentId);
        if ( channelContextId > 0 && channelContextId != contextDoc.getConnectorConfiguration().getChannel().getId() )
        	documentId = contextDoc.getChannelAlternateByType( channelContextId ).get(0).getId();
		
        Document touchpoint = Document.findById(documentId);
        
		referenceData.put("touchpoint", touchpoint);
		referenceData.put("sections", touchpoint.getDocumentSectionsByOrder());
		
		referenceData.put("isVariantEnabled", touchpoint.isEnabledForVariation());
		
		if (!touchpoint.isEnabledForVariation())
			HttpRequestUtil.saveViewingTpSelectionId(request, -1L);
		
		boolean hasTemplateModifiers 	= false;
		boolean appliesTemplateVariant 	= false;
		long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);
		if ( touchpoint.isEmailTouchpoint() || touchpoint.isWebTouchpoint() ) {
			if ( touchpointSelectionId > 0 && touchpoint.isEnabledForVariation() ) {
				TouchpointSelection selection = TouchpointSelection.findById(touchpointSelectionId);
				appliesTemplateVariant = (selection.getTemplateVariant() != null);
			}
				
			Set<TemplateModifier> templateModifiers = touchpoint.getMasterTemplateModifiers();
			for ( TemplateModifier currentModifier: templateModifiers ) {
				if ( currentModifier.isTemplateManaged() && currentModifier.getIsActive() ) {
					hasTemplateModifiers = true;
					break;
				}
			}

		}
		
		referenceData.put("appliesTemplateVariant"		, appliesTemplateVariant);
		referenceData.put("hasTemplateModifiers"		, hasTemplateModifiers);
		referenceData.put("langCode", UserUtil.getCurrentLanguageLocaleContext().getLanguageCode());

		referenceData.put("channelId"					, touchpoint.getConnectorConfiguration().getChannel().getId());
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	return new ModelAndView(new RedirectView(getSuccessView()));
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}

}