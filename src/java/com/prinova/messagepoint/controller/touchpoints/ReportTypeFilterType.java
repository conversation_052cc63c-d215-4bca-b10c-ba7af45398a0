package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class ReportTypeFilterType extends StaticType {
	private static final long serialVersionUID = -303336792024066539L;
	
	public static final int ID_MSG_DELIVERY 				= 5;
	public static final int ID_TP_DELIVERY			 		= 6;
	public static final int ID_BATCH						= 7;

	public static final String MESSAGE_CODE_MSG_DELIVERY 	= "page.label.list.filter.type.message.delivery";	
	public static final String MESSAGE_CODE_TP_DELIVERY		= "page.label.list.filter.type.touchpoint.delivery";
	public static final String MESSAGE_CODE_BATCH			= "page.label.list.filter.type.batch";

	public ReportTypeFilterType(Integer id) {
		super();
		switch (id) {
		case ID_MSG_DELIVERY:
			this.setId(ID_MSG_DELIVERY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MSG_DELIVERY));
			this.setDisplayMessageCode(MESSAGE_CODE_MSG_DELIVERY);
			break;
		case ID_TP_DELIVERY:
			this.setId(ID_TP_DELIVERY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_TP_DELIVERY));
			this.setDisplayMessageCode(MESSAGE_CODE_TP_DELIVERY);
			break;
		case ID_BATCH:
			this.setId(ID_BATCH);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_BATCH));
			this.setDisplayMessageCode(MESSAGE_CODE_BATCH);
			break;	
		default:
			break;
		}
	}

	public ReportTypeFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_MSG_DELIVERY))) { 
			this.setId(ID_MSG_DELIVERY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_MSG_DELIVERY));
			this.setDisplayMessageCode(MESSAGE_CODE_MSG_DELIVERY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_TP_DELIVERY))) { 
			this.setId(ID_TP_DELIVERY);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_TP_DELIVERY));
			this.setDisplayMessageCode(MESSAGE_CODE_TP_DELIVERY);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_BATCH))) { 
			this.setId(ID_BATCH);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_BATCH));
			this.setDisplayMessageCode(MESSAGE_CODE_BATCH);
		}
	}
	
	public static List<ReportTypeFilterType> listAll() {
		List<ReportTypeFilterType> allListFilterTypes = new ArrayList<>();

		ReportTypeFilterType listFilterType = null;
		
		listFilterType = new ReportTypeFilterType(ID_MSG_DELIVERY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ReportTypeFilterType(ID_TP_DELIVERY);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new ReportTypeFilterType(ID_BATCH);
		allListFilterTypes.add(listFilterType);		

		return allListFilterTypes;
	}
}