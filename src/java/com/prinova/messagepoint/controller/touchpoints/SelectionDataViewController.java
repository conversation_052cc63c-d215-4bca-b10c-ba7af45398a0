package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

public class SelectionDataViewController implements Controller {

	public static String REQ_PRAM_COLECTION_ID = "collectionId";
	
	private String successView;
	private String formView;

	public String getSuccessView() {
		return successView;
	}

	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}

	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long collectionId = ServletRequestUtils.getLongParameter(request, REQ_PRAM_COLECTION_ID, -1L);
		referenceData.put("collectionId", collectionId);

		return new ModelAndView(getFormView(), referenceData);
	}
}
