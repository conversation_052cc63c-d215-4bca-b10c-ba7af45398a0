package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.ParameterGroupInstance;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.wrapper.AsyncSelectionListVO;
import com.prinova.messagepoint.model.wrapper.AsyncSelectionListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tpselection.CreateOrUpdateTouchpointVariantService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.*;
import java.util.function.Predicate;

public class TouchpointVariantListValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointVariantListWrapper command = (TouchpointVariantListWrapper) commandObj;
		User requestor = UserUtil.getPrincipalUser();

		int action = -1;
		if (command.getAction() != null && !command.getAction().trim().isEmpty()) {
			action = Integer.valueOf(command.getAction()).intValue();
		}

		switch (action) {
			case TouchpointVariantListController.ACTION_CREATE_WORKING_COPY:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanCreateWorkingCopy);
				break;
			case TouchpointVariantListController.ACTION_DISCARD_WORKING:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanDiscardWorkingCopy);
				break;
			case TouchpointVariantListController.ACTION_RELEASE_FOR_APPROVAL:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanReleaseForApproval);
				break;
			case TouchpointVariantListController.ACTION_APPROVE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanApprove);
				break;
			case TouchpointVariantListController.ACTION_REJECT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanReject);
				break;
			case TouchpointVariantListController.ACTION_REASSIGN:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanReassign);
				break;
			case TouchpointVariantListController.ACTION_DELETE_NODE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanDelete);
				break;
			case TouchpointVariantListController.ACTION_REMOVE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSelectionListVO.SelectionListVOFlags::isCanRemove);
				break;
		}

		if (action == TouchpointVariantListController.ACTION_CREATE_NEW_NODE) {

			int selectionLevel = command.getSelectedList().get(0).getLevel();

			if (selectionLevel == ParameterGroupInstance.MAX_NUMBER_OF_PARAMETERS_IN_GROUP) {
				errors.reject("error.touchpointselection.selection.hierarchy.may.not.exceed.x", new String[] {Integer.toString(ParameterGroupInstance.MAX_NUMBER_OF_PARAMETERS_IN_GROUP)}, "");
			}
			
			SelectionsValidationUtil.checkNodeNameValue(command.getSelectionName(), errors);
			SelectionsValidationUtil.checkTouchpointSelectionNameUniqueness(0L, command.getSelectedList().get(0), command.getSelectionName(), errors);

		} else if (action == TouchpointVariantListController.ACTION_RENAME_NODE) {

			SelectionsValidationUtil.checkNodeNameValue(command.getSelectionName(), errors);
			SelectionsValidationUtil.checkTouchpointSelectionNameUniqueness(command.getSelectedList().get(0).getId(), null, command.getSelectionName(), errors);

		} else if (action == TouchpointVariantListController.ACTION_DELETE_NODE) {

			for (TouchpointSelection touchpointSelection : command.getSelectedList()) {
				if (touchpointSelection.isMaster()) {
					errors.reject("error.touchpointselection.cannot.delete.is.master");
				} else if (touchpointSelection.hasChildren()) {
					if (!new HashSet<>(command.getSelectedList()).containsAll(touchpointSelection.getChildrenOrderByName())) {
						errors.reject("error.touchpointselection.cannot.delete.has.selection");
					}
				} else {
					boolean hasMessages = TouchpointSelection.ownsNotRemovedContentObjects(touchpointSelection);
					if (hasMessages) {
						errors.reject("error.touchpointselection.cannot.delete.has.nonremoved.message");
						return;
					}
					
					if (touchpointSelection.hasEverBeenActiveOrItIsMaster()) {
						errors.reject("error.touchpointselection.cannot.delete.has.been.previously.active");
						return;
					}
				}
			}
		} else if (action == TouchpointVariantListController.ACTION_REMOVE) {
			for (TouchpointSelection touchpointSelection : command.getSelectedList()) {
				if(!touchpointSelection.isMaster() && !touchpointSelection.hasChildren()){
					boolean hasMessages = TouchpointSelection.ownsNotRemovedContentObjects(touchpointSelection);
					if (hasMessages) {
						errors.reject("error.touchpointselection.cannot.delete.has.nonremoved.message");
						return;
					}
				}
			}
			ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForRemoveNode(command.getSelectedList(), requestor, command.getUserNote());
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
			service.validate(context);
			if (!context.getResponse().isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			}
		} else if (action == TouchpointVariantListController.ACTION_CREATE_WORKING_COPY) {
			ServiceExecutionContext context = CreateOrUpdateTouchpointVariantService.createContextForCreateWorkingCopy(command.getSelectedList(), requestor);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
			service.validate(context);
			if (!context.getResponse().isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			}
		} else if (action == TouchpointVariantListController.ACTION_REQUEST_TP_REPORT){
			boolean changeReportEnabled = command.getChangeReportEnabled();
			Date fromDate = command.getFromDate();
			Date toDate = command.getToDate();			

			if (changeReportEnabled) {
				// Check From Date				
				if (fromDate==null) {
					errors.reject("error.export.touchpointselection.filter.date.required", new String[] {ApplicationUtil.getMessage("page.label.from.date")}, "");			   
				} else {
					// Add up the hours select
					String fromHour = command.getFromHour();
					String[] fromHourSpl = fromHour.split(":");
					long fromDateMillSec = fromDate.getTime() + Integer.valueOf(fromHourSpl[0])*3600000;
					fromDate.setTime(fromDateMillSec);						
					if (fromDate.after(DateUtil.now())) {
						errors.reject("error.export.touchpointselection.filter.date.cannot.be.in.future", new String[] {ApplicationUtil.getMessage("page.label.from.date")}, "");
					}
				}		
				// Check To Date
				if (toDate==null) {
					errors.reject("error.export.touchpointselection.filter.date.required", new String[] {ApplicationUtil.getMessage("page.label.to.date")}, "");			   
				} else{
					// Add up the hours select
					String toHour = command.getToHour();
					String[] toHourSpl = toHour.split(":");
					long toDateMillSec = toDate.getTime() + Integer.valueOf(toHourSpl[0])*3600000;
					toDate.setTime(toDateMillSec);						
				}
				// From date cannot be later than the to date
				if (fromDate!=null && toDate!=null && fromDate.after(toDate)) {
					errors.reject("error.export.touchpointselection.fromdate.cannot.be.after.todate");
				}
			}
		} else if (action == TouchpointVariantListController.ACTION_RELEASE_FOR_APPROVAL ||
					action == TouchpointVariantListController.ACTION_APPROVE) {

			Set<TouchpointSelection> notFinalApprovedParents = new HashSet<>();
			List<ContentObject> masterMessagesNotApproved = new ArrayList<>();

			for (TouchpointSelection tpSelection : command.getSelectedList()) {
				if (tpSelection.isAwaitingFinalApproval() || tpSelection.appliesNoStepWorkflow()) {
					if(tpSelection.isActivatingStillRunning()){
						errors.reject("page.text.activate.still.running");
						return;
					}

					if(tpSelection.isActive()){
						errors.reject("error.variant.not.awaiting.approval", new String[]{tpSelection.getName()}, "");
						return;
					}

					Set<TouchpointSelection> dependingAncesters = tpSelection.getDependingAncesters();
					Set<String> parentNotApprovedNames = new HashSet<>();
					for (TouchpointSelection ancester : dependingAncesters) {
						if (!ancester.isActive() && !ancester.isMaster()) {
							notFinalApprovedParents.add(ancester);
							parentNotApprovedNames.add(ancester.getName());
						}
					}

					ParameterGroupTreeNode masterTreeNode = tpSelection.getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode();
					List<ContentObject> structuredContentObjects = ContentObject.findAllStructuredContentObjectsByTPVariantIncludingParents(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, tpSelection);

					for (ContentObject contentObject : structuredContentObjects) {
						if (contentObject.isOnHold())
							continue;	// skip on hold content objects - they are not part of the approval process

						if (!contentObject.hasActiveData()) {
							// if the content object has no active data and is in an active Zone,
							// we cannot activate any sub variants since we need 1st activate the message envelope
							if (contentObject.getZone() == null || contentObject.getZone().isThisEnabled())
								masterMessagesNotApproved.add(contentObject);
						} else {
							ContentObjectAssociation defaultContentAssociation = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contentObject, tpSelection.getParameterGroupTreeNode(), null, null);

							long masterTreeNodeId;
							if (contentObject.isVariantType()) {
								masterTreeNodeId = contentObject.getOwningTouchpointSelection().getParameterGroupTreeNode().getId();
								if (contentObject.getOwningTouchpointSelection().getId() == tpSelection.getId()) {
									if (contentObject.hasWorkingData() && !masterMessagesNotApproved.contains(contentObject) && (contentObject.getIsTouchpointLocal() || contentObject.isInEnabledZone())) {
										masterMessagesNotApproved.add(contentObject);
									}
								}
							} else {
								masterTreeNodeId = masterTreeNode.getId();
							}
							if (defaultContentAssociation.getReferencingTouchpointPGTreeNode() != null) {
								if (defaultContentAssociation.getReferencingTouchpointPGTreeNode().getId() == masterTreeNodeId) {
									if (contentObject.hasWorkingData() && !masterMessagesNotApproved.contains(contentObject) && (contentObject.getIsTouchpointLocal() || contentObject.isInEnabledZone()))
										masterMessagesNotApproved.add(contentObject);
								}
								else {
									TouchpointSelection referencedTouchpointSelection = TouchpointSelection.findByPgTreeNodeId(defaultContentAssociation.getReferencingTouchpointPGTreeNode().getId());
									if (referencedTouchpointSelection != null && !referencedTouchpointSelection.isActive() && !referencedTouchpointSelection.isMaster()) {
										if (!notFinalApprovedParents.contains(referencedTouchpointSelection)) {
											notFinalApprovedParents.add(referencedTouchpointSelection);
											parentNotApprovedNames.add(referencedTouchpointSelection.getName());
										}
									}
								}
							}
						}
					}

					List<ContentObject> nonStructuredContentObjects = ContentObject.findAllNotStructuredContentObjectsByTPVariantIncludingParents(ContentObject.DATA_TYPE_WORKING_AND_ACTIVE, tpSelection);
					for (ContentObject contentObject : nonStructuredContentObjects) {
						if (contentObject.isOnHold())
							continue;

						if (contentObject.isVariantType() && contentObject.getOwningTouchpointSelection().getId() == tpSelection.getId()) {
							if (contentObject.hasWorkingData() && !masterMessagesNotApproved.contains(contentObject) && (contentObject.getIsTouchpointLocal() || contentObject.isInEnabledZone())) {
								masterMessagesNotApproved.add(contentObject);
							}
						}
					}
				}
			}

			if (!notFinalApprovedParents.isEmpty()) {
				StringBuilder notFinalApprovedParentNames = new StringBuilder("[");
				for (TouchpointSelection notApprovedTpSelection : notFinalApprovedParents) {
					notFinalApprovedParentNames.append(notApprovedTpSelection.getName()).append(",");
				}
				if (notFinalApprovedParentNames.charAt(notFinalApprovedParentNames.length() - 1) == ',')
					notFinalApprovedParentNames = new StringBuilder(notFinalApprovedParentNames.substring(0, notFinalApprovedParentNames.length() - 1));

				notFinalApprovedParentNames.append("]");

				errors.reject("error.message.selection.final.approval.out.of.sequence", new String[] {notFinalApprovedParentNames.toString()}, "");
			}
			
			if (!masterMessagesNotApproved.isEmpty()) {
				StringBuilder masterMessagesNotApprovedNames = new StringBuilder();
				int messageCount = 1;
				int messageCountCap = 10;
				for (ContentObject currentMessage : masterMessagesNotApproved) {
					if (messageCount++ == messageCountCap && masterMessagesNotApproved.size() > messageCountCap) {
						masterMessagesNotApprovedNames.append(" +").append(masterMessagesNotApproved.size() - messageCountCap).append(" ").append(ApplicationUtil.getMessage("page.text.other.messages"));
						break;
					}
					masterMessagesNotApprovedNames.append(currentMessage.getName()).append(",");
				}
				if (masterMessagesNotApprovedNames.charAt(masterMessagesNotApprovedNames.length() - 1) == ',')
					masterMessagesNotApprovedNames = new StringBuilder(masterMessagesNotApprovedNames.substring(0, masterMessagesNotApprovedNames.length() - 1));

				errors.reject("error.message.master.message.require.approval", new String[] {masterMessagesNotApprovedNames.toString()}, "");
			}
			
		}

	}

	private void validateActionPermission(List<TouchpointSelection> touchpointSelections, Errors errors, String errorMessage, Predicate<AsyncSelectionListVO.SelectionListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( touchpointSelections.isEmpty() )
			errors.reject(errorMessage);

		for (TouchpointSelection touchpointSelection : touchpointSelections) {
			AsyncSelectionListVO vo = new AsyncSelectionListVO();
			vo.setTouchpointSelection(touchpointSelection);

			AsyncSelectionListVO.SelectionListVOFlags flags = new AsyncSelectionListVO.SelectionListVOFlags();
			AsyncSelectionListWrapper.setActionFlags(touchpointSelection, flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}
}
