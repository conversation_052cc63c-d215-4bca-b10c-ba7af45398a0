package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointSelectionsContentListDetailController implements Controller {
	
	private String successView;
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long touchpointSelectionId 	= ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);

		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		TouchpointSelection touchpointSelection = TouchpointSelection.findById(touchpointSelectionId);

		if (contentObject != null && touchpointSelection != null) {

			referenceData.put("contentObject", contentObject);
			
			if ((UserUtil.getCurrentSelectionStatusContext().getId() == SelectionStatusFilterType.ID_ACTIVE) && contentObject.hasActiveData()) {
				contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
			}

			List<ContentObjectAssociation> contentObjectAssociations = ContentObjectAssociation.findAllByContentObjectAndParameters(contentObject, contentObject.getFocusOnDataType(), touchpointSelection.getParameterGroupTreeNode(), null);
			if (!contentObjectAssociations.isEmpty())
				referenceData.put("content", contentObjectAssociations.get(0));

		}
		return new ModelAndView(getFormView(), referenceData);
	}
	
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
