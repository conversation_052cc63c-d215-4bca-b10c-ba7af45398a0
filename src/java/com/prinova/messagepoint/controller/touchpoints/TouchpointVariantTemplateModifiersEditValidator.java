package com.prinova.messagepoint.controller.touchpoints;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointVariantTemplateModifiersEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointVariantTemplateModifiersEditWrapper command = (TouchpointVariantTemplateModifiersEditWrapper) commandObj;
		
    	if( command.getDocument() == null ) {
    		errors.reject("error.message.mustselecttouchpoint");
			return;
    	} 
    }
}
