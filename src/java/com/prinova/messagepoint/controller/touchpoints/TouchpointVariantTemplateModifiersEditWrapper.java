package com.prinova.messagepoint.controller.touchpoints;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.util.TemplateModifierUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointVariantTemplateModifiersEditWrapper implements Serializable{

	private static final long serialVersionUID = -2526588383459857342L;

	private Document 				document;
	private boolean					isSelectorDefined       = false;
	private List<TemplateModifier> 	templateModifiers 		= new ArrayList<>();
	private Set<Long> 				touchpointSelectionIds	= new HashSet<>();
	private TouchpointSelection		touchpointSelection;
	
	public TouchpointVariantTemplateModifiersEditWrapper(Document touchpoint, TouchpointSelection touchpointSelection){
		super();
		
		Document channelContextTouchpoint = UserUtil.getCurrentChannelDocumentContext(touchpoint);
		
		this.setDocument(channelContextTouchpoint);
		
		if (touchpoint != null)
			this.setIsSelectorDefined(touchpoint.isEnabledForVariation());
		
		Set<TemplateModifier> touchpointTemplateModifiers 	= new HashSet<>(TemplateModifier.findAllMasterActiveModifiersByTouchpoint(channelContextTouchpoint));
		if(!touchpoint.isEnabledForVariation()){
			this.templateModifiers = new ArrayList<>(touchpointTemplateModifiers);
		}

		if (touchpointSelection != null && touchpoint.isEnabledForVariation()) {
			if(touchpointSelection.getTemplateModifiers().isEmpty()){
				if(touchpointSelection.isMaster()){	// if master, set value empty
					// There's a chance master selction's set have not set yet. 
					// Touchpoint was enabled for variation recently without Template package uploaded.
					// check if template updated and master selection's set is empty. if so, get touchpoint master set like standard touchpoint.
					if(channelContextTouchpoint.isEmailTemplateUploaded() && touchpointTemplateModifiers.isEmpty()) {
						if (!TemplateModifier.findAllMasterModifiersByTouchpointNoSelectionSet(channelContextTouchpoint).isEmpty())
							this.templateModifiers.addAll(TemplateModifier.findAllMasterModifiersByTouchpointNoSelectionSet(channelContextTouchpoint));
					}
					else {
						this.templateModifiers.addAll(touchpointTemplateModifiers);
					}
				}else{
					this.setTemplateModifiers(TemplateModifierUtil.generateCombinedListWithMasterTemplateModifers(touchpointSelection, channelContextTouchpoint, touchpointTemplateModifiers, touchpointSelection.getTemplateModifiers()));
				}
				
			}else{	
				/*
				* if touchpointSelection modifiers exist
				* get matching modifiers from masterTemplateModifiers
				* get needToCreateList from masterTemplateModifiers, create them then set to final list.
				*/
				this.setTemplateModifiers(TemplateModifierUtil.generateCombinedListWithMasterTemplateModifers(touchpointSelection, channelContextTouchpoint, touchpointTemplateModifiers, touchpointSelection.getTemplateModifiers()));
			}
		}
		
		// MODIFIER CONTENT: Translate VAR tags for view
		for ( TemplateModifier currentModifier: this.getTemplateModifiers() )
			if ( currentModifier.getComplexValue() != null )
				currentModifier.getComplexValue().setEncodedValue( ContentObjectContentUtil.translateContentForView(currentModifier.getComplexValue().getEncodedValue(), touchpoint, MessagepointLocale.getDefaultSystemLanguageLocale(), null) );
	}
	
	
	public TouchpointVariantTemplateModifiersEditWrapper(Document touchpoint, Set<Long> touchpointSelectionIds){
		super();
		
		List<TemplateModifier> finalList = new ArrayList<>();
		Set<TouchpointSelection> selectionList = new HashSet<>();
		String value = "";
		
		Document channelContextTouchpoint = UserUtil.getCurrentChannelDocumentContext(touchpoint);
		
		this.setDocument(channelContextTouchpoint);
		
		if (touchpoint != null)
			this.setIsSelectorDefined(touchpoint.isEnabledForVariation());
		Set<TemplateModifier> touchpointTemplateModifiers 	= new HashSet<>(TemplateModifier.findAllMasterActiveModifiersByTouchpoint(channelContextTouchpoint));
		for(long tpsId : touchpointSelectionIds){
			TouchpointSelection tps = TouchpointSelection.findById(tpsId);
			if(tps != null){
				selectionList.add(tps);
				tps.setTemplateModifiers(null);
			}
		}
		if(!selectionList.isEmpty()){
			if(selectionList.size() == 1){ // For single selection
				TouchpointSelection tps = selectionList.iterator().next();
				TouchpointSelection masterSelection = tps.getAncesters().get(0);
				for(TemplateModifier tm : touchpointTemplateModifiers){
					if(tm.getIsActive()){
						TemplateModifier parentModifier = TemplateModifier.findByTPSelectionAndMatchingConnectorName(masterSelection, channelContextTouchpoint, tm);
						value = null;
						if(parentModifier != null){
							if(parentModifier.getComplexValue() != null)
								value = parentModifier.getComplexValue().getEncodedValue();
						}
						if(tps.getTemplateModifiers() ==  null)
							tps.setTemplateModifiers(new HashSet<>());
						
						TemplateModifier newTM = TemplateModifierUtil.createEmptyModifierWithGivenValue(channelContextTouchpoint, tm, masterSelection, value);
						if(newTM != null){
							newTM.setValueType(TemplateModifier.TEMPLATE_MODIFIER_VALUE_TYPE_INHERITED);
							newTM.setReferencingTouchpointSelection(masterSelection);
							newTM.setTemplateManaged(tm.isTemplateManaged());
							tps.getTemplateModifiers().add(newTM);
						}
					}
				}
				finalList.addAll(tps.getTemplateModifiers());
			}
			finalList.addAll(TemplateModifierUtil.createNewModifierListFromMasterTemplateModifiers(touchpointTemplateModifiers, selectionList.iterator().next()));
		}
		
		this.setTemplateModifiers(finalList);
		
		// MODIFIER CONTENT: Translate VAR tags for view
		for ( TemplateModifier currentModifier: this.getTemplateModifiers() )
			if ( currentModifier.getComplexValue() != null )
				currentModifier.getComplexValue().setEncodedValue( ContentObjectContentUtil.translateContentForView(currentModifier.getComplexValue().getEncodedValue(), touchpoint, MessagepointLocale.getDefaultSystemLanguageLocale(), null) );
		
	}
	
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	
	public boolean getIsSelectorDefined() {
		return isSelectorDefined;
	}
	public void setIsSelectorDefined(boolean isSelectorDefined) {
		this.isSelectorDefined = isSelectorDefined;
	}
	
	public List<TemplateModifier> getTemplateModifiers() {
		return templateModifiers;
	}
	public void setTemplateModifiers(List<TemplateModifier> templateModifiers) {
		this.templateModifiers = templateModifiers;
	}
	public Set<Long> getTouchpointSelectionIds() {
		return touchpointSelectionIds;
	}
	public void setTouchpointSelectionIds(Set<Long> touchpointSelectionIds) {
		this.touchpointSelectionIds = touchpointSelectionIds;
	}
	public TouchpointSelection getTouchpointSelection() {
		return touchpointSelection;
	}

	public void setTouchpointSelection(TouchpointSelection touchpointSelection) {
		this.touchpointSelection = touchpointSelection;
	}
	
}
