package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointContentObjectListDetailController implements Controller {

	private String successView;
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> referenceData = new HashMap<>();

		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		referenceData.put("contentObject", contentObject);

		// Latest preview
		MessagepointLocale locale = UserUtil.getCurrentLanguageLocaleContext();
		referenceData.put("latestPreview", contentObject.getLatestPreview(locale));

		// Global copy
		if(contentObject.getIsTouchpointLocal()){
			String globalCopyName = null;
			if(contentObject.getGlobalParentObject() != null){
				globalCopyName = contentObject.getGlobalParentObject().getName();
			}
			referenceData.put("globalCopy", globalCopyName);
			referenceData.put("lastSyncDate", contentObject.getLastGlobalParentObjectSyncDate());
			referenceData.put("dateTimeFormat", DateUtil.DATE_TIME_FORMAT);
		}
		return new ModelAndView(getFormView(), referenceData);
	}
	
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
