package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTouchpointCollectionService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointCollectionEditController extends MessagepointController {
	public static final String 	REQ_PARM_TP_COLLECTIONID			= "tpCollectionId";
	public static final String REQ_PARAM_METADATA_DEFINITION_ID		= "metadataDefinitionId";

	public Map<String, Object> referenceData(HttpServletRequest request) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		
		List<Document> availableTouchpoints = Document.findAllDocumentsAndProjectsVisible(true);
		referenceData.put("availableTouchpoints", availableTouchpoints);
		
		List<DataElementVariable> systemVariables = DataElementVariable.findAllSystemVariablesEnabledForFilenames();
		referenceData.put("systemVariablesData", DataElementVariable.getListAsJSON(systemVariables));
		
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}	
	
	public Object formBackingObject(HttpServletRequest request) throws Exception{
		long tpCollectionId = ServletRequestUtils.getLongParameter(request, REQ_PARM_TP_COLLECTIONID, -1);
		long metadataDefinitionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_METADATA_DEFINITION_ID, -1);
		
		TouchpointCollectionEditWrapper wrapper;
		if (tpCollectionId == -1) {
			MetadataFormDefinition metadataFormDefinition = MetadataFormDefinition.findById(metadataDefinitionId);
			wrapper = new TouchpointCollectionEditWrapper(metadataFormDefinition);
		} else {
			TouchpointCollection tpCollection = HibernateUtil.getManager().getObject(TouchpointCollection.class,
					Long.parseLong(request.getParameter(REQ_PARM_TP_COLLECTIONID)));
			wrapper = new TouchpointCollectionEditWrapper(tpCollection);
		}		
		return wrapper;
	}
	
	public ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors)
			 throws Exception {

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.CollectionEdit);
		analyticsEvent.setAction(Actions.Edit);

		try {
			TouchpointCollectionEditWrapper command = (TouchpointCollectionEditWrapper) commandObj;

			// execute the service
			ServiceExecutionContext ctx = UpdateTouchpointCollectionService.createContext(command.getTouchpointCollection(), command.getDocuments(), command.getFormWrapper(), command.getOutputFilename(), command.getOutputDocumentTitle());
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(UpdateTouchpointCollectionService.SERVICE_NAME, UpdateTouchpointCollectionService.class);
			service.execute(ctx);

			ServiceResponse serviceResponse = ctx.getResponse();
			if (!serviceResponse.isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}
