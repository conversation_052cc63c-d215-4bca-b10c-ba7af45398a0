package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.content.ContentObject;

import java.io.Serializable;

public class TouchpointContentObjectMoveToZoneWrapper implements Serializable{
	private static final long serialVersionUID = -5653849331108824753L;

	private ContentObject 	contentObject;
	private Zone			fromZone;
	private Zone			toZone;

	public TouchpointContentObjectMoveToZoneWrapper(){
		super();
	}

	public TouchpointContentObjectMoveToZoneWrapper(ContentObject contentObject){
		super();
		this.contentObject = contentObject;
	}

	public ContentObject getContentObject() {
		return contentObject;
	}

	public void setContentObject(ContentObject contentObject) {
		this.contentObject = contentObject;
	}

	public Zone getFromZone() {
		return fromZone;
	}

	public void setFromZone(Zone fromZone) {
		this.fromZone = fromZone;
	}

	public Zone getToZone() {
		return toZone;
	}

	public void setToZone(Zone toZone) {
		this.toZone = toZone;
	}
}
