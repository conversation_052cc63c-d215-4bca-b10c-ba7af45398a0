package com.prinova.messagepoint.controller.touchpoints;

import java.util.List;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointCollectionListValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointCollectionListWrapper command = (TouchpointCollectionListWrapper)commandObj;
		List<TouchpointCollection> collections = command.getSelectedList();
		
		int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(command.getActionValue()).intValue();
		}

		if(action == TouchpointCollectionListController.ACTION_DELETE){		
			for(TouchpointCollection collection : collections){
				if(collection.isReferenced()){
					errors.reject("error.touchpoint.collection.is.being.referenced.on.delete");
					break;					
				}
			}
		}
	}
}
