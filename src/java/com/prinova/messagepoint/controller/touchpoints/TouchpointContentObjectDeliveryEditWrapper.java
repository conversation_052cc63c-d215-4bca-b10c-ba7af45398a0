package com.prinova.messagepoint.controller.touchpoints;

import java.io.Serializable;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.content.ContentObject;

public class TouchpointContentObjectDeliveryEditWrapper implements Serializable{

	private static final long serialVersionUID = 7493316296096627792L;
	
	private String					actionValue;
	
	private ContentObject 			selectedContentObject;
	private Zone					selectedZone;
	
	public TouchpointContentObjectDeliveryEditWrapper(){
		super();
	}

	public TouchpointContentObjectDeliveryEditWrapper(Zone selectedZone){
		super();
		this.selectedZone = selectedZone;
	}
	
	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public ContentObject getSelectedContentObject() {
		return selectedContentObject;
	}

	public void setSelectedContentObject(ContentObject selectedContentObject) {
		this.selectedContentObject = selectedContentObject;
	}

	public Zone getSelectedZone() {
		return selectedZone;
	}

	public void setSelectedZone(Zone selectedZone) {
		this.selectedZone = selectedZone;
	}	
}
