package com.prinova.messagepoint.controller.touchpoints;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.User;

public class TouchpointVariantVisibilityWrapper implements Serializable{

	private static final long serialVersionUID = -2526588383459857342L;

	private List<Long>				selectedVisibleUserIds = new ArrayList<>();
	private TouchpointSelection		touchpointSelection;
	private boolean 				isFullyVisible = true;
	
	private List<Long>				selectedConnectedVisibleUserIds = new ArrayList<>();
	private boolean 				isConnectedFullyVisible = true;

	public TouchpointVariantVisibilityWrapper(){
		super();
	}
	
	public TouchpointVariantVisibilityWrapper(TouchpointSelection touchpointSelection){
		super();
		
		this.touchpointSelection = touchpointSelection;
		if (touchpointSelection != null) {
			if(!this.touchpointSelection.isFullyVisible()){
				for (User user: this.touchpointSelection.getVisibleUsersSorted()){
					selectedVisibleUserIds.add(user.getId());
				}
				this.setFullyVisible(false);
			}
			if(!this.touchpointSelection.isConnectedFullyVisible()){
				for (User user: this.touchpointSelection.getVisibleConnectedUsersSorted()){
					selectedConnectedVisibleUserIds.add(user.getId());
				}
				this.setConnectedFullyVisible(false);
			}
		}
	}

	public void setSelectedVisibleUserIds(List<Long> selectedVisibleUserIds) {
		this.selectedVisibleUserIds = selectedVisibleUserIds;
	}
	
	public List<Long> getSelectedVisibleUserIds() {
		return selectedVisibleUserIds;
	}
	
	public List<Long> getAllAvailUserIds(){
		List<Long> allAvailUserIds = new ArrayList<>();
		for (User user: User.findAllActiveUsersSorted()){
			allAvailUserIds.add(user.getId());
		}
		return allAvailUserIds;
	}

	public TouchpointSelection getTouchpointSelection() {
		return touchpointSelection;
	}

	public void setTouchpointSelection(TouchpointSelection touchpointSelection) {
		this.touchpointSelection = touchpointSelection;
	}

	public boolean isFullyVisible() {
		return isFullyVisible;
	}

	public void setFullyVisible(boolean isFullyVisible) {
		this.isFullyVisible = isFullyVisible;
	}

	public List<Long> getSelectedConnectedVisibleUserIds() {
		return selectedConnectedVisibleUserIds;
	}

	public void setSelectedConnectedVisibleUserIds(List<Long> selectedConnectedVisibleUserIds) {
		this.selectedConnectedVisibleUserIds = selectedConnectedVisibleUserIds;
	}

	public boolean isConnectedFullyVisible() {
		return isConnectedFullyVisible;
	}

	public void setConnectedFullyVisible(boolean isConnectedFullyVisible) {
		this.isConnectedFullyVisible = isConnectedFullyVisible;
	}

}
