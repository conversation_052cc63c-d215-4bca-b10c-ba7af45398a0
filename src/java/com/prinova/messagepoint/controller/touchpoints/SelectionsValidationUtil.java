package com.prinova.messagepoint.controller.touchpoints;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.LanguageSelection;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;

public class SelectionsValidationUtil {

	public static void checkNodeNameValue(String nodeName, Errors errors) {
		String label = ApplicationUtil.getMessage("page.label.name");
		MessagepointInputValidationUtil.validateStringValue(label, nodeName, true, 2, 80, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
	}
	
	public static void checkTouchpointSelectionNameUniqueness(long selectionId, TouchpointSelection parentSelection, String name, Errors errors) {
		if (TouchpointSelection.nameExistsInSiblings(selectionId, parentSelection, name)) {
			errors.reject("error.touchpointselection.selection.name.already.exists", "");
		}
	}
	
	public static void checkLanguageSelectionNameUniqueness(long selectionId, LanguageSelection parentSelection, String name, Errors errors) {
		if (LanguageSelection.nameExistsInSibelings(selectionId, parentSelection, name)) {
			errors.reject("error.touchpointselection.selection.name.already.exists", "");
		}
	}
		
}
