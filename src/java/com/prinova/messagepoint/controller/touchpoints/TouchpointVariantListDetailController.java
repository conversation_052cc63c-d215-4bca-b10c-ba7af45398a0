package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointVariantListDetailController implements Controller {

	public static String REQ_PRAM_SELECTIONID = "touchpointSelectionId";
	
	private String successView;
	private String formView;

	public String getSuccessView() {
		return successView;
	}

	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}

	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long selectionId = ServletRequestUtils.getLongParameter(request, REQ_PRAM_SELECTIONID, -1L);
		TouchpointSelection tpSelection = HibernateUtil.getManager().getObject(TouchpointSelection.class, selectionId);
		referenceData.put("touchpointSelection", tpSelection);
		
		Boolean selectionCanHaveProofs = !tpSelection.isMaster();
		referenceData.put("selectionCanHaveProofs", selectionCanHaveProofs);
		if (selectionCanHaveProofs) {
			MessagepointLocale currentLocale = UserUtil.getCurrentLanguageLocaleContext();
			
			Proof latestProof;
			if (UserUtil.getCurrentSelectionStatusContext().getId() == SelectionStatusFilterType.ID_ACTIVE)
				latestProof = tpSelection.getLatestActiveProofForLang(currentLocale.getLanguageCode(),tpSelection.getDocument().getIsOmniChannel());
			else
				latestProof = tpSelection.getLatestCompletedProofForLang(currentLocale.getLanguageCode(),tpSelection.getDocument().getIsOmniChannel());
			referenceData.put("latestProof", latestProof);
			
			List<MessagepointLocale> lang = new ArrayList<>();
			lang.add(currentLocale);
			referenceData.put("canProofForLanguage", tpSelection.getCanProofForLanguage(lang).get(currentLocale.getLanguageCode()));
		}
		
		referenceData.put("isEnabledForVariantWorkflow", tpSelection.getDocument().isEnabledForVariantWorkflow());
		
		return new ModelAndView(getFormView(), referenceData);
	}
}
