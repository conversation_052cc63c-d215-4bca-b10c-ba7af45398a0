package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.CleanupCompositionPacksService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class TouchpointCollectionListDetailController extends MessagepointController {
	public static final String REQUEST_PARM_TPCOLLECTIONID 			= "tpCollectionId";
	public static final String REQUEST_PARM_ACTION 					= "action";
	
	public static final int ACTION_DELETE_COMP_PACKS				= 21;

	protected Map<String, Object> referenceData(HttpServletRequest request) {
		Map<String, Object> referenceData = new HashMap<>();
		
		long tpCollectionId = ServletRequestUtils.getLongParameter(request, TouchpointCollectionListController.PARAMETER_TP_COLLECTION_ID, -1L);
		
		TouchpointCollection collection = HibernateUtil.getManager().getObject(TouchpointCollection.class, tpCollectionId);
		referenceData.put("collection", collection);
		
		referenceData.put("defaultCompositionPackageId", collection != null && collection.getDefaultCompositionFileSet() != null ? collection.getDefaultCompositionFileSet().getId() : -1);

		Document firstDocument = collection.getFirstDocument();
		referenceData.put("collectionConnectorTypeId", firstDocument != null ? collection.getFirstDocument().getConnectorConfiguration().getConnector().getId() : -1);

		boolean isCollectionFilePackage = false;
		if (collection != null) {
			isCollectionFilePackage = collection.getIsTemplateControlled() && (collection.getIsSefasCollection() || collection.getIsMPHCSCollection());
		}
		referenceData.put("isCollectionFilePackage", isCollectionFilePackage);

		referenceData.put("collectionTemplateControlled", collection.getIsTemplateControlled() );

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long tpCollectionId = ServletRequestUtils.getLongParameter(request, REQUEST_PARM_TPCOLLECTIONID, -1);
		TouchpointCollection tpCollection = TouchpointCollection.findById(tpCollectionId);
		TouchpointCollectionListDetailWrapper command = new TouchpointCollectionListDetailWrapper(tpCollection);
		return command;
	}
	
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.CollectionListDetail);

		try {
			TouchpointCollectionListDetailWrapper command = (TouchpointCollectionListDetailWrapper)commandObj;

			int actionId = ServletRequestUtils.getIntParameter(request, REQUEST_PARM_ACTION, -1);
			long tpCollectionId = ServletRequestUtils.getLongParameter(request, REQUEST_PARM_TPCOLLECTIONID, -1);

			switch (actionId) {
				case (ACTION_DELETE_COMP_PACKS) : {
					analyticsEvent.setAction(Actions.CompositionPackage.Delete);
					ServiceExecutionContext context = CleanupCompositionPacksService.createContext(command.getSelectedCompositionFiles());
					Service cleanupCompPacksService = MessagepointServiceFactory.getInstance().lookupService(CleanupCompositionPacksService.SERVICE_NAME, CleanupCompositionPacksService.class);

					cleanupCompPacksService.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return super.showForm(request, response, errors);
					} else {
						Map<String, Object> parms = new HashMap<>();
						parms.put(REQUEST_PARM_TPCOLLECTIONID, tpCollectionId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
			}
			return new ModelAndView(getFormView());
		} finally {
			analyticsEvent.send();
		}
	}
}
