package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentPreview;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.testing.DataFilePreviewLanguage;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.DeleteContentObjectPreviewService;
import com.prinova.messagepoint.platform.services.content.UpdateContentObjectPreviewService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TouchpointContentObjectPreviewController extends MessagepointController {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(TouchpointContentObjectPreviewController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 		= "documentId";
	public static final String REQ_PARAM_SELECTION_ID_PARAM 	= "touchpointSelectionId";
	public static final String REQ_PARAM_SELECTED_IDS_PARAM 	= "selectedIds";
	public static final String REQ_PARAM_LOCALE_ID_PARAM 		= "localeId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		long localeId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID_PARAM, UserUtil.getCurrentLanguageLocaleContext().getId());
		
		Document document = Document.findById(documentId);
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		MessagepointLocale langLocale = MessagepointLocale.getLanguageLocaleByLocaleId(localeId);
		
		List<Zone> zoneList = new ArrayList<>();
		Set<Zone> visibleZones = document.getVisibleZonesOfUser();
		if (contentObject.getZone() != null && visibleZones.contains(contentObject.getZone()))
		{
			zoneList.add(contentObject.getZone());
		}

		List<DataResource> dataResList = new ArrayList<>();
		for(DataResource dr : document.getDataResources()){
			DataFilePreviewLanguage[] currentFilePreviewLanguages = null;
			if (dr != null && dr.isPrimaryLocal() ) {
				currentFilePreviewLanguages = dr.getPrimaryDataFile().getDataFilePreviewLanguagesArray();
				for (int j=0;j<currentFilePreviewLanguages.length;j++) {
					if (langLocale.getLanguageCode().trim().equals(currentFilePreviewLanguages[j].getLanguage().trim())) {
						dataResList.add(dr);
					}
				}
			}			
		}
		
		referenceData.put("zoneList", zoneList);
		referenceData.put("dataResourceList", dataResList);
		
		referenceData.put("nodeGUID", Node.getCurrentNode().getGuid());
		referenceData.put("webDAVToken", UserUtil.getPrincipalUser().getWebDAVToken());
		referenceData.put("canEditDataFiles", UserUtil.isPermissionGranted(Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT));
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor( DataResource.class, new IdCustomEditor<>(DataResource.class) );
    	binder.registerCustomEditor( Zone.class, new IdCustomEditor<>(Zone.class) );
    	binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		long localeId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID_PARAM, UserUtil.getCurrentLanguageLocaleContext().getId());
		
    	long userId = UserUtil.getPrincipalUserId();
    	Document document = Document.findById(documentId);
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		MessagepointLocale langLocale = MessagepointLocale.getLanguageLocaleByLocaleId(localeId);
		
		DocumentPreview documentPreview = new DocumentPreview();
		Date now = new Date(System.currentTimeMillis());
		documentPreview.setDocument(document);
		documentPreview.setMessagepointLocale(langLocale);
		documentPreview.setContentObject(contentObject);
		documentPreview.setRequestDate(now);
		documentPreview.setUserId(userId);
		documentPreview.setUpdatedBy(userId);
		documentPreview.setMode("preview");
		
		TouchpointContentObjectPreviewWrapper command = new TouchpointContentObjectPreviewWrapper(documentPreview, contentObject);
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.MessagePreview)
				.setAction(Actions.Update);
		try {
			TouchpointContentObjectPreviewWrapper messagePreviewWrapper = (TouchpointContentObjectPreviewWrapper)commandObj;
			// Only keep the latest preview for the current language, delete the rest
			MessagepointLocale locale = messagePreviewWrapper.getDocumentPreview().getMessagepointLocale();
			List<DocumentPreview> allLangPreviews = messagePreviewWrapper.getContentObject().getAllPreview(locale);
			if(allLangPreviews.size() > 1){
				DocumentPreview latestLangPreview = messagePreviewWrapper.getContentObject().getLatestPreview(locale);
				for(DocumentPreview docPreview : allLangPreviews){
					if(docPreview.getId() != latestLangPreview.getId()){	// Not the latest preview, delete
						ServiceExecutionContext context = DeleteContentObjectPreviewService.createContext(docPreview.getId());
						Service deleteMessagePreviewService = MessagepointServiceFactory.getInstance().lookupService(DeleteContentObjectPreviewService.SERVICE_NAME, DeleteContentObjectPreviewService.class);
						deleteMessagePreviewService.execute(context);
					}
				}
			}

			ServiceExecutionContext context = UpdateContentObjectPreviewService.createContext(messagePreviewWrapper.getDocumentPreview());
			Service updateContentObjectPreviewService = MessagepointServiceFactory.getInstance().lookupService(UpdateContentObjectPreviewService.SERVICE_NAME, UpdateContentObjectPreviewService.class);
			updateContentObjectPreviewService.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				return showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}

}