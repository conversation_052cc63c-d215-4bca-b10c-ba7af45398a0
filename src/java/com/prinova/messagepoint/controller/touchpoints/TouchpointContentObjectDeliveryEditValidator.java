package com.prinova.messagepoint.controller.touchpoints;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointContentObjectDeliveryEditValidator extends MessagepointInputValidator{

    public void validateNotGenericInputs(Object command, Errors errors) {
    	TouchpointContentObjectDeliveryEditWrapper tpMsgPreviewWrapper = (TouchpointContentObjectDeliveryEditWrapper)command;
    	if (tpMsgPreviewWrapper.getSelectedContentObject() == null) {
    		errors.rejectValue("selectedMsgIns", "error.message.mustselectmessage");
    	}
    }
}
