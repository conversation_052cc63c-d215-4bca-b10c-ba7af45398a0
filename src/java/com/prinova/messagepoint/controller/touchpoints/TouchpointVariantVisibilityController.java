package com.prinova.messagepoint.controller.touchpoints;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointVariantVisibiltyService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class TouchpointVariantVisibilityController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointVariantVisibilityController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 	= "documentId";
	public static final String REQ_PARAM_SELECTION_ID_PARAM = "touchpointSelectionId";
	public static final String REQ_PARAM_SELECTED_IDS_PARAM = "selectedIds";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);
		
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
		if (!selectedIdsS.isEmpty()) {
			List<User> availableUsers = User.findAllActiveUsersSorted();
			referenceData.put("availableUsers", availableUsers);
		}
		
		Boolean applyConnectedVisibility = document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection();
		referenceData.put("applyConnectedVisibility", applyConnectedVisibility);
		if ( applyConnectedVisibility ) {
			List<User> availableConnectedUsers = User.findEnabledUsersByPermission(Permission.ROLE_COMMUNICATIONS_EDIT);
			referenceData.put("availableConnectedUsers", availableConnectedUsers);
		}
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
		TouchpointVariantVisibilityWrapper command;
		if(this.getSelectedIds(selectedIdsS).size() == 1){
			TouchpointSelection touchpointSelection = TouchpointSelection.findById(this.getSelectedIds(selectedIdsS).iterator().next());
			command = new TouchpointVariantVisibilityWrapper(touchpointSelection);
		}else{
			command = new TouchpointVariantVisibilityWrapper();
		}
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.SelectionVisibility)
				.setAction(Actions.Update);

		try {
			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
			long tpSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
			String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
			TouchpointVariantVisibilityWrapper command = (TouchpointVariantVisibilityWrapper)commandObj;

			ServiceExecutionContext context = UpdateTouchpointVariantVisibiltyService.createContext(this.getSelectedIds(selectedIdsS), command.isFullyVisible(), command.getSelectedVisibleUserIds(), command.isConnectedFullyVisible(), command.getSelectedConnectedVisibleUserIds());

			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTouchpointVariantVisibiltyService.SERVICE_NAME, UpdateTouchpointVariantVisibiltyService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateTouchpointVariantVisibiltyService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" selected Users were not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
				parms.put(REQ_PARAM_SELECTION_ID_PARAM, tpSelectionId);
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
	
	/**
	 * Convert selected id string to set
     */
	private Set<Long> getSelectedIds(String selectedIdsS){		
		Set<Long> selectionIds = new HashSet<>();
		if(selectedIdsS != null && !selectedIdsS.isEmpty()){
			for(String idS : selectedIdsS.split("_")){
				selectionIds.add(Long.valueOf(idS));
			}
		}
		return selectionIds;
	}
}