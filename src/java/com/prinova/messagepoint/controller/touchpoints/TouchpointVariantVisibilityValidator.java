package com.prinova.messagepoint.controller.touchpoints;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointVariantVisibilityValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointVariantVisibilityWrapper wrapper = (TouchpointVariantVisibilityWrapper) commandObj;
		if(!wrapper.isFullyVisible()){
			if(wrapper.getSelectedVisibleUserIds() == null || wrapper.getSelectedVisibleUserIds().isEmpty()){
				errors.reject("error.touchpointselection.visibility.no.user.selected");
			}
		}
	}
}
