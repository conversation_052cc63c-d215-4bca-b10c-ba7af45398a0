package com.prinova.messagepoint.controller.touchpoints;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.ArrayList;
import java.util.List;

public class SelectionStatusFilterType extends StaticType {
	private static final long serialVersionUID = 546293711499914404L;

	public static final int ID_WORKING_COPIES 		= 1;
	public static final int ID_ACTIVE 				= 2;
	public static final int ID_ARCHIVED				= 4;

	public static final String MESSAGE_CODE_WORKING_COPIES 	= "page.label.list.filter.type.working.copy";
	public static final String MESSAGE_CODE_ACTIVE 			= "page.label.list.filter.type.active";
	public static final String MESSAGE_CODE_ARCHIVED 		= "page.label.list.filter.type.archived";

	public SelectionStatusFilterType(Integer id) {
		super();
		switch (id) {
		case ID_WORKING_COPIES:
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
			break;
		case ID_ACTIVE:
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
			break;
		case ID_ARCHIVED:
			this.setId(ID_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
			break;
		default:
			break;
		}
	}

	public SelectionStatusFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES))) { 
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE))) { 
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED))) {
			this.setId(ID_ARCHIVED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ARCHIVED));
			this.setDisplayMessageCode(MESSAGE_CODE_ARCHIVED);
		}
	}
	
	public static List<SelectionStatusFilterType> listAll() {
		List<SelectionStatusFilterType> allListFilterTypes = new ArrayList<>();

		SelectionStatusFilterType listFilterType = null;

		listFilterType = new SelectionStatusFilterType(ID_WORKING_COPIES);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new SelectionStatusFilterType(ID_ACTIVE);
		allListFilterTypes.add(listFilterType);

		listFilterType = new SelectionStatusFilterType(ID_ARCHIVED);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}

	public static List<SelectionStatusFilterType> listWorkingActive() {
		List<SelectionStatusFilterType> allListFilterTypes = new ArrayList<>();

		SelectionStatusFilterType listFilterType = null;

		listFilterType = new SelectionStatusFilterType(ID_WORKING_COPIES);
		allListFilterTypes.add(listFilterType);

		listFilterType = new SelectionStatusFilterType(ID_ACTIVE);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}
