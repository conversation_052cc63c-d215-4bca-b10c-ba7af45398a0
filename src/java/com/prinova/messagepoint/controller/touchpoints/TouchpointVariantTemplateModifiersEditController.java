package com.prinova.messagepoint.controller.touchpoints;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointEvents;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.testing.TestingUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.email.TemplateModifier;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTemplateModifiersService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.TemplateModifierUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointVariantTemplateModifiersEditController extends MessagepointController {
//	private String templateModifierEditRedirect;
	
	public static final String REQ_PARAM_DOCUMENTID 	= "documentId";
	public static final String REQ_PARAM_SELECTION_ID = "touchpointSelectionId";
	public static final String REQ_PARAM_SELECTED_IDS = "selectedIds";


	@Override
	protected Map<String,Object> referenceData(HttpServletRequest request, Object commandObj, Errors errors) throws Exception {
		
		Map<String, Object> referenceData = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		// If alternate layout get channel root document
		if ( document.getParent() != null )
			document = document.getParent();
		document = UserUtil.getCurrentChannelDocumentContext(document);
		
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS, "");
		Set<TouchpointSelection> tpSelections = new HashSet<>();

		if ( !selectedIdsS.equals("-1") ) {
			
			for ( Long tpId : getSelectedIds(selectedIdsS) )
				tpSelections.add(TouchpointSelection.findById(tpId));

			if ( getSelectedIds(selectedIdsS).size() == 1 ) {
				
				TouchpointSelection tps = tpSelections.iterator().next();
				
				List<TemplateModifier> currentModifiers = new ArrayList<>();
				if ( tps.isMaster() || tps.getTemplateVariant() == null )
					currentModifiers.addAll(document.getMasterTemplateModifiers());
				else
					currentModifiers.addAll(TemplateModifierUtil.getRefModifiersMapFromVariantTemplatePackage(tps, document.getDefaultTouchpointLanguageCode()));
				
				List<String> connectorList = new ArrayList<>();
				for ( TemplateModifier refTm : currentModifiers ) {
					connectorList.add(refTm.getConnectorName().toString());
				}
				
				// List of active modifiers based on template
				referenceData.put("connectorList", connectorList);
			}
			
			referenceData.put("tpSelections", tpSelections);
			referenceData.put("ancesters", tpSelections.iterator().next().getAncesters());
			
		} else {
			
			List<String> connectorList = new ArrayList<>();
			for ( TemplateModifier refTm : TemplateModifier.findAllMasterModifiersByTouchpoint(document) ) {
				connectorList.add(refTm.getConnectorName().toString());
			}
			
			// List of active modifiers
			referenceData.put("connectorList", connectorList);
			
		}

		referenceData.put("document", document);
		referenceData.put("defaultEditorCSSFilePath", TemplateModifier.getDefaultEditorCSSFilePath());
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( TouchpointSelection.class, new IdCustomEditor<>(TouchpointSelection.class) );
		binder.registerCustomEditor( TemplateModifier.class, new IdCustomEditor<>(TemplateModifier.class) );
		binder.registerCustomEditor( String.class, new StringXSSEditor() );
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		
		TouchpointVariantTemplateModifiersEditWrapper command = null;
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		Document document = Document.findById(documentId);
		// If alternate layout get channel root document
		if ( document.getParent() != null )
			document = document.getParent();
		document = UserUtil.getCurrentChannelDocumentContext(document);

		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS, "");

		if ( !document.isEnabledForVariation() ) {
			TouchpointSelection tps = null;
			command = new TouchpointVariantTemplateModifiersEditWrapper(document, tps);
		} else {
			if ( selectedIdsS.equals("-1") ){
				selectedIdsS = String.valueOf( document.getMasterTouchpointSelection().getId() );
				command = new TouchpointVariantTemplateModifiersEditWrapper(document, document.getMasterTouchpointSelection());
				command.setTouchpointSelectionIds(getSelectedIds(selectedIdsS));
			} else {
				if ( getSelectedIds(selectedIdsS).size() == 1 ) {
					TouchpointSelection touchpointSelection = TouchpointSelection.findById(getSelectedIds(selectedIdsS).iterator().next());
					command = new TouchpointVariantTemplateModifiersEditWrapper(document, touchpointSelection);
				} else {
					command = new TouchpointVariantTemplateModifiersEditWrapper(document, getSelectedIds(selectedIdsS));
				}
				command.setTouchpointSelectionIds(getSelectedIds(selectedIdsS));
			}
		}
		
		return command;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors)
			throws Exception {

		AnalyticsEvent<TouchpointEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointEvents.SelectionsTemplateModifierEdit)
				.setAction(Actions.Update);

		try {
			TouchpointVariantTemplateModifiersEditWrapper command = (TouchpointVariantTemplateModifiersEditWrapper) commandObj;

			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
			Document document = Document.findById(documentId);
			// If alternate layout get channel root document
			if ( document.getParent() != null )
				document = document.getParent();
			document = UserUtil.getCurrentChannelDocumentContext(document);

			String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS, "");

			// If alternate layout get channel root document
			if ( document.getParent() != null )
				document = document.getParent();

			// MODIFIER VALUE: Scrub rendered items for persistence
			for ( TemplateModifier currentModifier: command.getTemplateModifiers() )
				if ( currentModifier.getComplexValue() != null )
					currentModifier.getComplexValue().setEncodedValue( ContentObjectContentUtil.translateContentForPersistance(currentModifier.getComplexValue().getEncodedValue()) );

			ServiceExecutionContext modifierContext = UpdateTemplateModifiersService.createContextForTouchpointSelection(document, command.getTemplateModifiers(), this.getSelectedIds(selectedIdsS));
			Service updateTemplateModifiersService = MessagepointServiceFactory.getInstance().lookupService(UpdateTemplateModifiersService.SERVICE_NAME, UpdateTemplateModifiersService.class);
			updateTemplateModifiersService.execute(modifierContext);
			ServiceResponse modifierServiceResponse = modifierContext.getResponse();

			if(!modifierServiceResponse.isSuccessful()){
				ServiceResponseConverter.convertToSpringErrors(modifierServiceResponse, errors);
				return super.showForm(request, response, errors);
			}
			Map<String, Object> parms = TestingUtils.getContextMapParms(request);
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
		} finally {
			analyticsEvent.send();
		}
	}
	
	/**
	 * Convert selected id string to set
     */
	private Set<Long> getSelectedIds(String selectedIdsS){		
		Set<Long> selectionIds = new HashSet<>();
		if(selectedIdsS != null && !selectedIdsS.isEmpty()){
			for(String idS : selectedIdsS.split("_")){
				selectionIds.add(Long.valueOf(idS));
			}
		}
		return selectionIds;
	}
}
