package com.prinova.messagepoint.controller.touchpoints;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.file.CompositionFileSet;

public class TouchpointCollectionListDetailWrapper {
	private String								actionValue;
	
	private List<CompositionFileSetListVO>		compositionFileSetsList = new ArrayList<>();
	private Map<Long, CompositionFileSetListVO>	compositionFileSetsMap 	= new HashMap<>();
	
	public TouchpointCollectionListDetailWrapper(TouchpointCollection tpCollection){
		if (tpCollection != null){
			
			List<CompositionFileSet> compositionFileSets = CompositionFileSet.findByTpCollectionId(tpCollection.getId());
			for (CompositionFileSet currentFileSet: compositionFileSets) {
				CompositionFileSetListVO currentVO = new CompositionFileSetListVO();
				currentVO.setCompositionFileSet(currentFileSet);
				currentVO.setDeletable(false);
				compositionFileSetsList.add(currentVO);
				compositionFileSetsMap.put(currentFileSet.getId(),currentVO);
			}
		}		
	}
	
	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<CompositionFileSetListVO> getCompositionFileSetsList() {
		return compositionFileSetsList;
	}

	public void setCompositionFileSetsList(List<CompositionFileSetListVO> compositionFileSetsList) {
		this.compositionFileSetsList = compositionFileSetsList;
	}

	public Map<Long, CompositionFileSetListVO> getCompositionFileSetsMap() {
		return compositionFileSetsMap;
	}

	public void setCompositionFileSetsMap(Map<Long, CompositionFileSetListVO> compositionFileSetsMap) {
		this.compositionFileSetsMap = compositionFileSetsMap;
	}

	public List<CompositionFileSet> getSelectedCompositionFiles(){
		List<CompositionFileSet> selectedFileSetIds = new ArrayList<>();
		for(Long key : compositionFileSetsMap.keySet()){
			CompositionFileSetListVO listVO = compositionFileSetsMap.get(key);
			if(listVO.selectedForAction){
				selectedFileSetIds.add(listVO.getCompositionFileSet());
			}
		}
		return selectedFileSetIds;
	}
	
	public static class CompositionFileSetListVO implements Serializable {
		private static final long serialVersionUID = 9173342994671497003L;
		
		private boolean selectedForAction;
		private boolean isDeletable;
		private CompositionFileSet compositionFileSet;
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}
		public boolean isDeletable() {
			return isDeletable;
		}
		public boolean getIsDeletable() {
			return this.isDeletable();
		}
		public void setDeletable(boolean isDeletable) {
			this.isDeletable = isDeletable;
		}
		public CompositionFileSet getCompositionFileSet() {
			return compositionFileSet;
		}
		public void setCompositionFileSet(CompositionFileSet compositionFileSet) {
			this.compositionFileSet = compositionFileSet;
		}
	}	
}
