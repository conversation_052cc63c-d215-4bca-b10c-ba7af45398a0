package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.common.ContentSelectionStatusType;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.bouncycastle.util.Arrays;

import java.util.*;

public class AsyncTouchpointSyncListComparator implements Comparator<Object> {
    public static final String SORT_KEY_OBJECT_NAME = "objectName";
    public static final String SORT_KEY_OBJECT_TYPE = "objectType";
    public static final String SORT_KEY_VARIANT = "variantName";
    public static final String SORT_KEY_STATUS = "status";
    public static final String SORT_KEY_DEPENDENCIES = "dependencies";

    public static final String SORT_DIRECTION_DESC = "desc";

    private static final String SORT_STATUS_CONFLICT = "page.label.conflict";
    private static final String SORT_STATUS_NO_CONFLICT = "page.label.no.conflict";

    private static final Log log = LogUtil.getLog(AsyncTouchpointSyncListComparator.class);

    private final String sortKey;
    private final String sortDirection;

    public AsyncTouchpointSyncListComparator(String sortKey, String sortDirection) {
        this.sortKey = sortKey;
        this.sortDirection = sortDirection;
    }

    @Override
    public int compare(Object o1, Object o2) {
        try {
            HashMap jsonObject1 = (HashMap) o1;
            HashMap jsonObject2 = (HashMap) o2;
            int compareResult;

            switch (sortKey) {
                case SORT_KEY_OBJECT_NAME:
                    return compareByName(jsonObject1, jsonObject2);
                case SORT_KEY_OBJECT_TYPE:
                    String typeName1 = (String) jsonObject1.get("objectTypeName");
                    String typeName2 = (String) jsonObject2.get("objectTypeName");

                    compareResult = compareWithDirection(typeName1.toLowerCase(), typeName2.toLowerCase());
                    if (compareResult == 0) {
                        compareResult = compareByName(jsonObject1, jsonObject2);
                    }
                    return compareResult;
                case SORT_KEY_VARIANT:
                    String variant1 = Objects.toString(jsonObject1.get("variant"), "");
                    String variant2 = Objects.toString(jsonObject2.get("variant"), "");

                    compareResult = compareWithDirection(variant1.toLowerCase(), variant2.toLowerCase());
                    if (compareResult == 0) {
                        compareResult = compareByName(jsonObject1, jsonObject2);
                    }
                    return compareResult;
                case SORT_KEY_STATUS:
                    String  status1 = getStatusName(jsonObject1).toLowerCase(),
                            status2 = getStatusName(jsonObject2).toLowerCase();

                    String  copyType1 = status1.substring(0,2),
                            copyType2 = status2.substring(0,2);

                    boolean sameTypeOfCopy = copyType1.equals(copyType2);

                    if(sameTypeOfCopy){
                        compareResult = compareWithDirection(status1, status2);
                    }
                    else {
                        compareResult = copyType1.compareTo(copyType2);
                    }

                    if (compareResult == 0) {
                        compareResult = compareByName(jsonObject1, jsonObject2);
                    }

                    return compareResult;
                case SORT_KEY_DEPENDENCIES:
                    Integer numberOfDependencies1 = 0,
                            numberOfDependencies2 = 0;
                    try {
                        numberOfDependencies1 = getNumberOfDependencies(jsonObject1);
                        numberOfDependencies2 = getNumberOfDependencies(jsonObject2);
                    } catch (NumberFormatException ex) {
                        // do nothing
                    }
                    compareResult = compareWithDirection(numberOfDependencies1, numberOfDependencies2);
                    if (compareResult == 0) {
                        compareResult = compareByName(jsonObject1, jsonObject2);
                    }
                    return compareResult;
            }
        } catch (Exception ex) {
            log.error("Failed to sort sync objects", ex);
        }

        return 0;
    }

    private int compareByName(HashMap jsonObject1, HashMap jsonObject2){
        String name1 = (String) jsonObject1.get("name");
        String name2 = (String) jsonObject2.get("name");

        if(name1 == null && name2 == null){
            return 0;
        }
        else if(name1 == null){
            return -1;
        }
        else if(name2 == null){
            return 1;
        }
        else{
            return compareWithDirection(name1.toLowerCase(), name2.toLowerCase());
        }
    }

    private int compareWithDirection(Comparable o1, Comparable o2){
        if(sortDirection.equals(SORT_DIRECTION_DESC)){
            return o2.compareTo(o1);
        }

        return o1.compareTo(o2);
    }

    private String getStatusName(HashMap jsonObject){
        String retVal = "";
        HashMap status = (HashMap) jsonObject.get("status");
        if(status != null){
            HashMap source = (HashMap) status.get("source");
            HashMap sourceActiveCopy = (HashMap) status.get("sourceActiveCopy");

            if(sourceActiveCopy != null && ContentSelectionStatusType.ID_NOT_APPLICABLE != (int)sourceActiveCopy.get("id")){
                retVal =  "AC:" + sourceActiveCopy.get("name");
            }
            else{
                retVal =  "WC:"+ source.get("name");
            }
        }

        return retVal;
    }

    public static Boolean isConflict(HashMap status1, HashMap status2){
        int[] CONFLICT_STATUSES = {ContentSelectionStatusType.ID_CHANGED,ContentSelectionStatusType.ID_DELETED, ContentSelectionStatusType.ID_FIRST_TIME_SYNC};

        if(status1 == null || status2 == null){
            return false;
        }

        try{
            Integer statusId1 = (Integer) status1.get("id");
            Integer statusId2 = (Integer) status2.get("id");

            return (
                // Conflict is the changes made on target and will be overwritten,
                // indespite of whether or not source is changed
                // Arrays.contains(CONFLICT_STATUSES, statusId1) &&
                Arrays.contains(CONFLICT_STATUSES, statusId2)
            );
        }
        catch(Exception ex){
            log.error("Error in isConflict", ex);
        }

        return false;
    }

    public static Boolean isConflicted(Map<String, Object> o1){
        HashMap status = (HashMap) o1.get("status");
        if (status != null) {
            HashMap source = (HashMap) status.get("source");
            HashMap target = (HashMap) status.get("target");

            HashMap sourceActiveCopy = (HashMap) status.get("sourceActiveCopy");
            HashMap targetActiveCopy = (HashMap) status.get("targetActiveCopy");

            if(isConflict(source, target) || isConflict(sourceActiveCopy, targetActiveCopy)) {
                return true;
            }

            Boolean multiVersion = (Boolean) o1.get("multiVersion");
            if(multiVersion != null && multiVersion.booleanValue()) {
                Boolean syncActiveOnly = (Boolean) o1.get("syncActiveOnly");
                if((syncActiveOnly != null && syncActiveOnly.booleanValue()) || true) {
                    Boolean workingCopyWillBeDeleted =  (Boolean) o1.get("workingCopyWillBeDeleted");
                    if(workingCopyWillBeDeleted != null && workingCopyWillBeDeleted.booleanValue()) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public Integer getNumberOfDependencies(HashMap jsonObject){
        int numberOfDependencies = 0;

        HashMap sourceDependencies = (HashMap) jsonObject.get("sourceDependencies");
        Boolean hasDependencies = (Boolean) sourceDependencies.get("hasDependencies");

        if(hasDependencies){
            if(sourceDependencies.get("needExisting") != null){
                numberOfDependencies = ((List) sourceDependencies.get("needExisting")).size();
            }
        }

        return numberOfDependencies;
    }
}
