package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public class AsyncReferenceCheckController implements Controller {
	private static final Log log = LogUtil.getLog(AsyncReferenceCheckController.class);
	
	public static final String PARAM_OBJECT_ID 			= "objectId";
	public static final String PARAM_OBJECT_TYPE		= "objectType";
	
	public static final int TYPE_TEXT_STYLE 			= 1;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getIsReferencedResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for reference check: "+e.getMessage(),e);
		}
		
		return null;
	}
	
	private String getIsReferencedResponseJSON (HttpServletRequest request) {

		long objectId 			= ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, 0);
		int objectType 			= ServletRequestUtils.getIntParameter(request, PARAM_OBJECT_TYPE, 0);
		
		JSONObject returnObj = new JSONObject();
		try {
			boolean isReferenced = false;
			switch(objectType){
				case TYPE_TEXT_STYLE:
					isReferenced = TextStyle.findById(objectId).isReferenced();
			}
			returnObj.put("isReferenced", isReferenced);
		} catch (JSONException e) {
			log.error("Error: " + e );
		}
		return returnObj.toString();
	}

}
