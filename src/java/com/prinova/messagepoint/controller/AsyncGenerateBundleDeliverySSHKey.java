package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.deserver.DEServerSSHKeyPair;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

public class AsyncGenerateBundleDeliverySSHKey implements Controller {
    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        JSONObject result = new JSONObject();

        HashMap<String, String> keys = DEServerSSHKeyPair.createKeyPair();


        String privateKey = keys.get(DEServerSSHKeyPair.PRIVATE_KEY);
        String publicKey = keys.get(DEServerSSHKeyPair.PUBLIC_KEY);


        result.put("public", new String(Base64.encodeBase64(publicKey.getBytes())));
        result.put("private", new String(Base64.encodeBase64(privateKey.getBytes())));

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        out.write(result.toString().getBytes());
        out.flush();


        return null;
    }
}
