package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.navigation.DropDownMenuItem;
import com.prinova.messagepoint.model.navigation.NavigationTab;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class DefaultPageController implements Controller {

	private String successView;
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		long currentNavigationTabId = 0;
		if (request.getParameter("CURRENT_TAB_ID") != null) {
			try{
				currentNavigationTabId = Long.valueOf(request.getParameter("CURRENT_TAB_ID").toString()).longValue();
			}catch (NumberFormatException e) {
				throw new IllegalStateException("CURRENT_TAB_ID is not valid");
			}
		}
		if( currentNavigationTabId == 0 ) throw new IllegalStateException("CURRENT_TAB_ID is not provided");
		NavigationTab currentTab = HibernateUtil.getManager().getObject(NavigationTab.class, currentNavigationTabId);
		DropDownMenuItem defaultMenuItem = currentTab.getDefaultAuthorizedMenuItem();
		String defaultPage = "";
		if (defaultMenuItem != null ) {
			defaultPage = defaultMenuItem.getUrl();
			
		} else {
            if (currentNavigationTabId == NavigationTab.TAB_ID_TOUCHPOINTS) {
                if (UserUtil.getPrincipalUser().isPermitted(Permission.ROLE_COMMUNICATIONS_VIEW)) {
                    defaultPage = "touchpoints/touchpoint_communications_list.form";
                }
                if (UserUtil.getPrincipalUser().isPermitted(Permission.ROLE_ECATALOG_VIEW) || UserUtil.getPrincipalUser().isPermitted(Permission.ROLE_ECATALOG_EDIT)) {
     				defaultPage = "rationalizer/rationalizer_documents_list.form";
     			}
            }
        }
		
		if(ApplicationUtil.isCSRFPreventionEnabled() && request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY) != null && !request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY).trim().isEmpty()) {
			if(defaultPage.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "=") == -1) {
				defaultPage = ApplicationUtil.addToken(defaultPage, request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY).trim());
			}
		}

		return new ModelAndView( new RedirectView( request.getContextPath() + "/" + defaultPage ) );
	}

}