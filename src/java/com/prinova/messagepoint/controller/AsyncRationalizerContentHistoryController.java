package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerSharedContent;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.prinova.messagepoint.util.RationalizerUtil.constructLastActionUser;
import static com.prinova.messagepoint.util.RationalizerUtil.constructLastHistoryActionUIString;

public class AsyncRationalizerContentHistoryController implements Controller {
    private static final String ACTION = "action_by";
    private static final Log log = LogUtil.getLog(AsyncRationalizerContentHistoryController.class);
    private static final String PARAM_HC_1_ID = "hc1Id";
    private static final String PARAM_HC_2_ID = "hc2Id";
    private static final String PARAM_TYPE = "type";
    private static final String TYPE_RATIONALIZER_CONTENT_HISTORY_SUMMARY = "rationalizerContentHistorySummary";
    private static final String TYPE_HISTORY_COMPARE = "histCompare";
    private static final String UPDATED_BY = "updated_by";
    private static final String TEXT_CONTENT = "text_content";
    private static final String CONTENTS = "contents";
    public static final String NBSP = "&nbsp;";
    public static final String COMPARED_CONTENT = "compared_content";
    public static final String IS_GRAPHIC_CONTENT = "is_graphic_content";

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);

        if (TYPE_RATIONALIZER_CONTENT_HISTORY_SUMMARY.equalsIgnoreCase(type)) {
            try {
                out.write(getRationalizerHistorySummaryResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error - Unable to resolve request for history list data: " + e.getMessage(), e);
            }
        } else if (TYPE_HISTORY_COMPARE.equalsIgnoreCase(type)) {
            try {
                out.write(getRationalizerHistoryCompareResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error - Unable to resolve request for history summary data: " + e.getMessage(), e);
            }
        }

        return null;
    }

    private String getRationalizerHistorySummaryResponseJSON(HttpServletRequest request) {
        JSONObject returnObj = new JSONObject();
        long hcId = ServletRequestUtils.getLongParameter(request, "hcId", -1);
        returnObj.put("name", "Summary");

        HistoricalRationalizerDocumentContent hist = HistoricalRationalizerDocumentContent.findById(hcId);
        if (hist != null) {
            constructHistContentSummary(returnObj, hist);
            return returnObj.toString();
        }
        constructHistSharedSummary(returnObj, hcId);
        return returnObj.toString();
    }

    /**
     * The json response should contain for example
     * {"contents":[
     *            {"action_by":"Updated by",                   //last action display text
     *             "text_content":"<p>Kern County<\/p>",      //markup text
     *             "updated_by":"claudia Test"}],             //user full name
     *             "name":"Summary"}
     */
    private void constructHistSharedSummary(JSONObject returnObj, long hcId) {
        JSONArray contentArray = new JSONArray();
        JSONObject contentObj = new JSONObject();
        HistoricalRationalizerSharedContent sharedHist = HistoricalRationalizerSharedContent.findById(hcId);
        if (sharedHist != null) {
            contentObj.put(TEXT_CONTENT, sharedHist.getMarkupContent());
            String userName = StringUtils.isEmpty(sharedHist.getLastActionBy()) ? constructLastActionUser(sharedHist) : sharedHist.getLastActionBy();
            contentObj.put(UPDATED_BY, userName);
            String actionName = constructLastHistoryActionUIString(sharedHist.getLastActionName());
            contentObj.put(ACTION, actionName);
            contentArray.put(contentObj);
            returnObj.put(CONTENTS, contentArray);
        } else {
            log.warn("Error: Unable to get rationalizer shared history for id : " + hcId);
        }
    }

    /**
     * The json response should contain for example
     * {"contents":[
     *            {"action_by":"Updated by",                   //last action display text
     *             "text_content":"<p>Kern County<\/p>",      //markup text
     *             "updated_by":"claudia Test"}],             //user full name
     *             "name":"Summary"}
     */
    private void constructHistContentSummary(JSONObject returnObj, HistoricalRationalizerDocumentContent hist) {
        JSONArray contentArray = new JSONArray();
        JSONObject contentObj = new JSONObject();
        contentObj.put(TEXT_CONTENT, hist.getMarkupContent());
        String actionName = constructLastHistoryActionUIString(hist.getLastActionName());
        contentObj.put(ACTION, actionName);
        String userName = StringUtils.isEmpty(hist.getLastActionBy()) ? constructLastActionUser(hist) : hist.getLastActionBy();
        contentObj.put(UPDATED_BY, userName);
        contentArray.put(contentObj);
        returnObj.put(CONTENTS, contentArray);
    }

    private String getRationalizerHistoryCompareResponseJSON(HttpServletRequest request) {
        JSONObject returnObj = new JSONObject();

        long hc1Id = ServletRequestUtils.getLongParameter(request, PARAM_HC_1_ID, -1);
        long hc2Id = ServletRequestUtils.getLongParameter(request, PARAM_HC_2_ID, -1);
        HistoricalRationalizerDocumentContent hc1 = HistoricalRationalizerDocumentContent.findById(hc1Id);
        HistoricalRationalizerDocumentContent hc2 = HistoricalRationalizerDocumentContent.findById(hc2Id);

        if (hc1 != null) {
            constructHistContentCompare(returnObj, hc1, hc2);
            return returnObj.toString();
        }
        constructHistSharedCompare(returnObj, hc1Id, hc2Id);

        return returnObj.toString();
    }

    private void constructHistSharedCompare(JSONObject returnObj, long hc1Id, long hc2Id) {
        HistoricalRationalizerSharedContent sharedHc1 = HistoricalRationalizerSharedContent.findById(hc1Id);
        HistoricalRationalizerSharedContent sharedHc2 = HistoricalRationalizerSharedContent.findById(hc2Id);

        if (sharedHc1 != null) {
            String firstCompMarkup = sharedHc1.getMarkupContent();
            firstCompMarkup = StringUtils.replace(firstCompMarkup, NBSP, " ");
            String secondCompMarkup = null;
            if (sharedHc2 != null) {
                secondCompMarkup = StringUtils.replace(sharedHc2.getMarkupContent(), NBSP, " ");
                secondCompMarkup = StringEscapeUtils.unescapeXml(secondCompMarkup);
            }

            String diffText = generateDiffText(firstCompMarkup, secondCompMarkup);

            returnObj.put(COMPARED_CONTENT, diffText);

            returnObj.put(IS_GRAPHIC_CONTENT, false);
        } else {
            log.warn("Error: Unable to get rationalizer history for history id : " + hc1Id);
            returnObj.put(COMPARED_CONTENT, StringUtils.EMPTY);
        }
    }

    private void constructHistContentCompare(JSONObject returnObj, HistoricalRationalizerDocumentContent hc1, HistoricalRationalizerDocumentContent hc2) {
        String firstCompMarkup = hc1.getMarkupContent();
        firstCompMarkup = StringUtils.replace(firstCompMarkup, NBSP, " ");
        String secondCompMarkup = null;
        if (hc2 != null) {
            secondCompMarkup = StringUtils.replace(hc2.getMarkupContent(), NBSP, " ");
            secondCompMarkup = StringEscapeUtils.unescapeXml(secondCompMarkup);
        }

        String diffText = generateDiffText(firstCompMarkup, secondCompMarkup);

        returnObj.put(COMPARED_CONTENT, diffText);
        returnObj.put(IS_GRAPHIC_CONTENT, false);
    }

    private String generateDiffText(String firstCompMarkup, String secondCompMarkup) {
        String diffText = firstCompMarkup;
        try {
            if (StringUtils.isNotEmpty(secondCompMarkup)) {
                diffText = StringsDiffUtils.diff(firstCompMarkup, secondCompMarkup);
            }
        } catch (Exception ex) {
            log.error("Error while executing StringsDiffUtils.diff" +
                    " with parameters:" +
                    "\n     first=\"" + firstCompMarkup + "\"" +
                    "\n     second=\"" + secondCompMarkup + "\".", ex);
        }
        return diffText;
    }

}
