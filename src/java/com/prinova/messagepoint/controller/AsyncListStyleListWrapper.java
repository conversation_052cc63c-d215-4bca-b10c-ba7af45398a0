package com.prinova.messagepoint.controller;

import java.util.*;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;

import com.prinova.messagepoint.controller.AsyncListStyleListVO.ListStyleListVOFlags;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncListStyleListWrapper extends AsyncAbstractListWrapper {

	private List<ListStyle> listStyleList;
	
	public AsyncListStyleListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
		this.listStyleList = new ArrayList<>();
		this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex);
	}

	private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
		// Alias init
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
        Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
        
        // MessagepointCriterion init
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
        List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
        
        // Join Type init
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
        
        // MessagepointOrder list init
        List<MessagepointOrder> orderList 						= new ArrayList<>();
        
        // Search for the variable name
        if(sSearch != null && !sSearch.isEmpty() && !sSearch.equals("NULL")){
        	firstLevelCriterionList.add( MessagepointRestrictions.ilike("name", "%" + sSearch + "%") );
        }
        
        // Sort
        this.addTableColumnsSort(orderByMap, orderList);
        
        PostQueryHandler postHandler = null;

        ServiceExecutionContext context = HibernatePaginationService.createContext(ListStyle.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, pageIndex, pageSize, orderList, null, postHandler);
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
        List<?> list = serviceResponse.getPage().getList();

        for (Object o : list) {
        	if(o instanceof ListStyle) {
        		ListStyle listStyle = (ListStyle) o;
        		this.listStyleList.add(listStyle);
            }
        }
		
		super.setiTotalRecords(serviceResponse.getPage().getRowCount());
		super.setiTotalDisplayRecords(serviceResponse.getPage().getRowCount()); 		
	}

	/**
	 * Add the sort criterion from the list table plug-in to the order list
     */
	private void addTableColumnsSort(Map<String, String> orderedByMap, List<MessagepointOrder> orderList){
		Set<String> keySet = orderedByMap.keySet();
		for(String key : keySet){
			String value = orderedByMap.get(key);
			String sortField = "";
			
			if(key.equals("name")){	// Sort by name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name";
			}else if(key.equals("connectorname")){	// Sort by connector name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".connectorName";
			}
			
			if(!sortField.isEmpty()){
				// Add to order list
				if(value.equals("asc")){
					orderList.add(MessagepointOrder.asc(sortField));
				}else{
					orderList.add(MessagepointOrder.desc(sortField));
				}
			}		
		}
	}
	
	@Override
	public void init() {
		List<AsyncListStyleListVO> aaData 		= new ArrayList<>();
		boolean styleUpdatePermission				= UserUtil.isPermissionGranted(Permission.ROLE_STYLES_UPDATE);
		
		for (ListStyle listStyle : this.listStyleList) {
			AsyncListStyleListVO vo = new AsyncListStyleListVO();
			vo.setDisplayMode(getDisplayMode());

			vo.setListStyle(listStyle);

			// For drill-down
			vo.setDT_RowId(listStyle.getId());
			
			// List Properties
			vo.setName(listStyle.getName());
			vo.setConnectorName(listStyle.getConnectorName());

			vo.setListSpacing(listStyle.getListSpacingDisplay());
			vo.setBulletSpacing(listStyle.getBulletSpacingDisplay());

			// Action flags
			ListStyleListVOFlags flags = new ListStyleListVOFlags();
			setActionFlags(listStyle, flags);
			
			vo.setFlags(flags);
			aaData.add(vo);
		}
		super.setAaData(aaData);
	}

	public static void setActionFlags(ListStyle listStyle, ListStyleListVOFlags flags) {
		boolean styleUpdatePermission	= UserUtil.isPermissionGranted(Permission.ROLE_STYLES_UPDATE);

		flags.setCanUpdate				(styleUpdatePermission);
		flags.setCanDelete				(styleUpdatePermission);
		flags.setCanClone				(styleUpdatePermission);
	}
}
