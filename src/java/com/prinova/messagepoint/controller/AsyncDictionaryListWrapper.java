package com.prinova.messagepoint.controller;

import java.util.*;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;
import com.prinova.messagepoint.controller.AsyncDictionaryListVO.DictionaryListVOFlags;
import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.model.dictionary.DictionaryType;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.handler.SimplePostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncDictionaryListWrapper extends AsyncAbstractListWrapper {

	private List<Dictionary> dictionaryList;
	
	public AsyncDictionaryListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, int dictionaryTypeFilterId, int dictionaryStatusFilterId) {
		this.dictionaryList = new ArrayList<>();
		this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex, dictionaryTypeFilterId, dictionaryStatusFilterId);
	}

	private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, int dictionaryTypeFilterId, int dictionaryStatusFilterId) {
		// Alias init
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
        Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
        
        // MessagepointCriterion init
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
        List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
        
        // Join Type init
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
        
        // MessagepointOrder list init
        List<MessagepointOrder> orderList 						= new ArrayList<>();
        
        // Search for the variable name
        if(sSearch != null && !sSearch.isEmpty() && !sSearch.equals("NULL")){
        		firstLevelCriterionList.add( MessagepointRestrictions.ilike("name", "%" + sSearch + "%") );
        }
        
        long userId = UserUtil.getPrincipalUserId();
        if(dictionaryTypeFilterId>0){
        		firstLevelCriterionList.add(MessagepointRestrictions.eq("type", dictionaryTypeFilterId));
        		if(dictionaryTypeFilterId == DictionaryType.ID_TYPE_USER){
        			firstLevelCriterionList.add(MessagepointRestrictions.eq("createdBy", userId));
        		}
        }else{
        		firstLevelCriterionList.add(
        				MessagepointRestrictions.or(
        						MessagepointRestrictions.eq("type", DictionaryType.ID_TYPE_SYSTEM),
        						MessagepointRestrictions.eq("type", DictionaryType.ID_TYPE_GLOBAL),
        						MessagepointRestrictions.and(
        								MessagepointRestrictions.eq("type", DictionaryType.ID_TYPE_USER),
        								MessagepointRestrictions.eq("createdBy", userId))));
        }
        
        if(dictionaryStatusFilterId>0){
        		firstLevelCriterionList.add(MessagepointRestrictions.eq("enabled", dictionaryStatusFilterId==1?true:false));
        }
        
        // Sort
        this.addTableColumnsSort(orderByMap, orderList);
        
        PostQueryHandler postHandler = null;
        
        // Sort by dicitonary type (Post-Sort)
        if(orderByMap.containsKey("type")){
        		SimplePostQueryHandler<Dictionary> typePostHandler = new SimplePostQueryHandler<>();
        		if(orderByMap.get("type").equals("asc")){
        			typePostHandler.setComparator((d1, d2) -> d1.getTypeStr().compareTo(d2.getTypeStr()));
        		}else{
        			typePostHandler.setComparator((d1, d2) -> d2.getTypeStr().compareTo(d1.getTypeStr()));
        		}
	        	postHandler = typePostHandler;
        }
        
        if(orderByMap.containsKey("status")){
	        	SimplePostQueryHandler<Dictionary> statusPostHandler = new SimplePostQueryHandler<>();
	    		if(orderByMap.get("status").equals("asc")){
	    			statusPostHandler.setComparator((d1, d2) -> d1.getStatus().compareTo(d2.getStatus()));
	    		}else{
	    			statusPostHandler.setComparator((d1, d2) -> d2.getStatus().compareTo(d1.getStatus()));
	    		}
	        	postHandler = statusPostHandler;
	    }  

        ServiceExecutionContext context = HibernatePaginationService.createContext(Dictionary.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, pageIndex, pageSize, orderList, null, postHandler);
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
        List<?> list = serviceResponse.getPage().getList();

        for (Object o : list) {
        	if(o instanceof Dictionary) {
        		Dictionary dictionary = (Dictionary) o;
        			this.dictionaryList.add(dictionary);
            }
        }
		
		super.setiTotalRecords(serviceResponse.getPage().getRowCount());
		super.setiTotalDisplayRecords(serviceResponse.getPage().getRowCount()); 		
	}

	/**
	 * Add the sort criterion from the list table plug-in to the order list
     */
	private void addTableColumnsSort(Map<String, String> orderedByMap, List<MessagepointOrder> orderList){
		Set<String> keySet = orderedByMap.keySet();
		for(String key : keySet){
			String value = orderedByMap.get(key);
			String sortField = "";
			
			if(key.equals("name")){	// Sort by name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name";
			}else if(key.equals("langcode")){	// Sort by date
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".langCode";
			}else if(key.equals("localecode")){	// Sort by date
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".localeCode";
			}
			
			if(!sortField.isEmpty()){
				// Add to order list
				if(value.equals("asc")){
					orderList.add(MessagepointOrder.asc(sortField));
				}else{
					orderList.add(MessagepointOrder.desc(sortField));
				}
			}		
		}
	}
	
	@Override
	public void init() {
		List<AsyncDictionaryListVO> aaData 		= new ArrayList<>();
		boolean dictionaryEditPermission				= UserUtil.isPermissionGranted(Permission.ROLE_DICTIONARY_EDIT);
		
		for (Dictionary dictionary : this.dictionaryList) {
			AsyncDictionaryListVO vo = new AsyncDictionaryListVO();
			vo.setDisplayMode(getDisplayMode());
			DictionaryListVOFlags flags = new DictionaryListVOFlags();
			vo.setDictionary(dictionary);

			// For drill-down
			vo.setDT_RowId(dictionary.getId());
			
			// List Properties
			vo.setName(dictionary.getName());

			boolean canBeModified = dictionary.canBeModified();
			boolean isEnabled = dictionary.getEnabled();
			
			// Action flags
			flags.setCanUpdate				(dictionaryEditPermission && canBeModified);
			flags.setCanDelete				(dictionaryEditPermission && canBeModified);
			flags.setCanEnable				(dictionaryEditPermission && canBeModified && !isEnabled);
			flags.setCanDisable				(dictionaryEditPermission && canBeModified && isEnabled);
			flags.setCanClone					(dictionaryEditPermission);
			
			vo.setFlags(flags);
			aaData.add(vo);
		}
		super.setAaData(aaData);
	}	
}
