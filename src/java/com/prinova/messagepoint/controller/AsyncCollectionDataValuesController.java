package com.prinova.messagepoint.controller;

import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollectionTreeNodeVO;
import com.prinova.messagepoint.tag.view.tree.TreeConstants;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public class AsyncCollectionDataValuesController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncCollectionDataValuesController.class);
	
	public static final String REQ_PARAM_COLLECTION_ID = "collectionId";
	public static final String REQ_PARAM_PGI_FULL_VALUE = "fullValue";
	public static final String REQ_PARAM_NODE_LEVEL = "nodeLevel";
	public static final String REQ_PARAM_VERBOSE_DATA = "verboseData";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseXML(request, response).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Selector values retrieval failed - "+e.getMessage(),e);
		}
		
		return null;
	}
	
	private String getResponseXML (HttpServletRequest request, HttpServletResponse response) {
		long pgiCollectionId = getParameterGroupInstanceCollectionId(request);
		String fullValue = getFullValue(request);
		Boolean requestForVerboseData = getVerboseDataFlag(request);
		
		response.setContentType("application/json");
		
		String tree = "";
		if (fullValue == null) {
			// GET ROOT TREE NODES
			List<ParameterGroupInstanceCollectionTreeNodeVO> firstTwoLevelTreeNodes = ParameterGroupInstanceCollection.loadFirstTwoLevelTreeNodes(pgiCollectionId);
			tree = getJSONfromTreeNodeList(firstTwoLevelTreeNodes, 1, requestForVerboseData);
		} else { 
			// GET CHILD NODE VALUES
			int nodeLevel = getNodeLevel(request);
			List<String> pgItemValues = ParameterGroupInstanceCollectionTreeNodeVO.deflat(fullValue);
			List<ParameterGroupInstanceCollectionTreeNodeVO> treeNodes = 
				ParameterGroupInstanceCollection.loadDataTreeNodesAtLevel(pgiCollectionId, pgItemValues, nodeLevel+1);
			tree = getJSONfromTreeNodeList(treeNodes, nodeLevel+1, requestForVerboseData);
		}
		return tree;
	}
	
	private String getJSONfromTreeNodeList(List<ParameterGroupInstanceCollectionTreeNodeVO> treeNodeList, int nodeListLevel, Boolean requestForVerboseData) {
		StringBuilder treeJSON = new StringBuilder(TreeConstants.JSON_LIST_OPEN);
		
		boolean firstChildNode = true;
		for (ParameterGroupInstanceCollectionTreeNodeVO currentChildNode: treeNodeList) {
			
			StringBuilder treeNodeJSON = new StringBuilder();

			if( !firstChildNode ) treeNodeJSON.append(TreeConstants.JSON_ELEMENT_SEPARATOR);
			firstChildNode = false;
			
			treeNodeJSON.append(TreeConstants.JSON_ELEMENT_OPEN);
			if (requestForVerboseData)
				treeNodeJSON.append(" \"attr\" : {\"id\":\"").append(currentChildNode.getFullValue()).append("\",\"level\":\"").append(nodeListLevel).append("\",\"data\":\"").append(currentChildNode.getValue()).append("\"} , ");
			else
				treeNodeJSON.append(" \"attr\" : {\"id\":\"").append(currentChildNode.getFullValue()).append("\",\"level\":\"").append(nodeListLevel).append("\"} , ");
			treeNodeJSON.append(" \"data\" : { \"title\" : \"").append(currentChildNode.getLabel()).append("\", \"attr\" : { \"title\" : \"").append(currentChildNode.getLabel()).append("\" }  } , ");
			
			if ( currentChildNode.getChildren() != null && !currentChildNode.getChildren().isEmpty() ) {
				treeNodeJSON.append(" \"children\" :").append(getJSONfromTreeNodeList(currentChildNode.getChildren(), nodeListLevel + 1, requestForVerboseData)).append(",");
			}
			
			if (nodeListLevel > 1) {
				treeNodeJSON.append(" \"state\" : \"closed\"");
			} else {
				treeNodeJSON.append(" \"state\" : \"open\"");	
			}
			
			treeNodeJSON.append(TreeConstants.JSON_ELEMENT_CLOSE);
			treeJSON.append(treeNodeJSON);
		}
		
		treeJSON.append(TreeConstants.JSON_LIST_CLOSE);
		return treeJSON.toString();
	}
	
	private Long getParameterGroupInstanceCollectionId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_COLLECTION_ID, -9);
	}
	private int getNodeLevel(HttpServletRequest request) {
		return (int)ServletRequestUtils.getLongParameter(request, REQ_PARAM_NODE_LEVEL, -9);
	}
	private String getFullValue(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_PGI_FULL_VALUE, null);
	}
	private Boolean getVerboseDataFlag(HttpServletRequest request) {
		return ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_VERBOSE_DATA, false);
	}
}