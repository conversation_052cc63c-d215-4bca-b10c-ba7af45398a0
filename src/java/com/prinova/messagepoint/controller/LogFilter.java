package com.prinova.messagepoint.controller;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import com.prinova.messagepoint.util.LogUtil;
import org.slf4j.MDC;

public class LogFilter implements Filter {

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
	
		if(request instanceof HttpServletRequest){
			HttpServletRequest httpServletRequest = (HttpServletRequest)request;
			
			String requestedSessionId = httpServletRequest.getRequestedSessionId();
			MDC.put(LogUtil.MDC_IDENTIFIER, (requestedSessionId==null)?"unknown session":requestedSessionId);
			
			String extraInfo = (httpServletRequest.getUserPrincipal()!=null? httpServletRequest.getUserPrincipal().getName() : "Anonymous user")
				+":" + httpServletRequest.getRemoteHost();
			MDC.put(LogUtil.MDC_EXTRA_INFO, extraInfo);
			
			chain.doFilter(request, response);
		}
		
		
	}
	
	public void destroy() {}
	public void init(FilterConfig arg0) throws ServletException { }
}
