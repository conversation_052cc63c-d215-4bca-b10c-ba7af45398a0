package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.ContentTargeting;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.content.CreateOrUpdateContentTargetingService;

public class AsyncContentTargetingController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentTargetingController.class);

	public static final String REQ_PARM_ACTION 					= "action";
	public static final String REQ_PARM_CONTENT_TARGETING_ID 	= "contentTargetingId";
	
	public static final String TYPE_CREATE_NEW					= "create_new";
	public static final String TYPE_HAS_TARGETING				= "has_targeting";
	
	
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, "null");
		
		JSONObject returnObj = new JSONObject();
		
		if ( action.equalsIgnoreCase(TYPE_CREATE_NEW) ) {
			
			try {
			
				ServiceExecutionContext context = CreateOrUpdateContentTargetingService.createContextForCreateContentTargeting();
				Service createContentTargetingService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateContentTargetingService.SERVICE_NAME, CreateOrUpdateContentTargetingService.class);
				createContentTargetingService.execute(context);
				
				if ( context.getResponse().getResultValueBean() != null ) {
					long contentTargetingId = (Long)context.getResponse().getResultValueBean();
					returnObj.put("content_targeting_id", contentTargetingId);
				}
			
			} catch (Exception e) {
				
				returnObj.put("error", true);
				returnObj.put("message", e.getMessage());
				
			}
			
		} else if ( action.equalsIgnoreCase(TYPE_HAS_TARGETING) ) {
			
			Long contentTargetingId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_TARGETING_ID, -1L);
			
			if ( contentTargetingId > 0 ) {
				ContentTargeting targeting = ContentTargeting.findById(contentTargetingId);
				if ( targeting != null ) {
					returnObj.put("has_targeting", targeting.getTargetGroupCount() != 0);
					returnObj.put("content_targeting_id", contentTargetingId);
				} else {
					returnObj.put("error", true);
					returnObj.put("message", "Invalide content targeting id");
				}
			}
			
		} else {
			returnObj.put("error", true);
			returnObj.put("message", "Async Content Targeting: Invalid action type");
		}
		
		return returnObj.toString();
	}

}