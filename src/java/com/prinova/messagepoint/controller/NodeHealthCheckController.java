package com.prinova.messagepoint.controller;

import com.mchange.v2.c3p0.ComboPooledDataSource;
import com.mchange.v2.c3p0.PooledDataSource;
import com.mchange.v2.c3p0.management.C3P0RegistryManager;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.PropertyUtils;
import com.prinova.messagepoint.util.SecretProperties;
import com.prinova.messagepoint.util.UserUtil;
import com.sun.management.UnixOperatingSystemMXBean;
import net.sf.ehcache.util.PropertyUtil;
import org.apache.commons.httpclient.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.util.List;

public class NodeHealthCheckController implements Controller {

    private static Boolean frontendDisabled = null;
    private static boolean startupComplete = false;

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        try {
            if (!startupComplete || getFrontendDisabled()) {
                response.setStatus(HttpStatus.SC_SERVICE_UNAVAILABLE);
                return null;
            }
            
            boolean verbose = request.getParameterMap().containsKey("verbose");

            JSONObject systemHealth = new JSONObject();

            if (verbose || request.getParameterMap().containsKey("dbconnections")) {
                systemHealth.put("dbconnections", getDatabaseConnectionCount());
            }

            if (verbose || request.getParameterMap().containsKey("mpnodeid")) {
                systemHealth.put("mpnodeid", PropertyUtils.getNodeId());
            }

            if (verbose || request.getParameterMap().containsKey("filedescriptors")) {
                systemHealth.put("filedescriptors", getFileDescriptorCounts());
            }

            if (UserUtil.getPrincipalUser() != null && UserUtil.getPrincipalUser().isPodAdminUser()) {
                if (verbose || request.getParameterMap().containsKey("secret_props")) {
                    String secretsProperties = System.getProperty(SecretProperties.RUNTIME_SYSTEM_PROP_NAME);
                    systemHealth.put("secret_props", secretsProperties != null ? secretsProperties : "disabled");
                }

                if (verbose || request.getParameterMap().containsKey("input_arguments")) {
                    List<String> arguments = ManagementFactory.getRuntimeMXBean().getInputArguments();
                    systemHealth.put("input_arguments", new JSONArray(arguments));
                }
            }

            response.setStatus(HttpStatus.SC_OK);

            if (!systemHealth.keySet().isEmpty()) {
                response.getWriter().write(systemHealth.toString(1));
            }

            return null;
        } finally {
            if (request.getSession() != null) {
                request.getSession().invalidate();
            }
        }
    }

    public static void setStartupComplete(boolean startupComplete) {
        NodeHealthCheckController.startupComplete = startupComplete;
    }

    private static Boolean getFrontendDisabled() {

        if (NodeHealthCheckController.frontendDisabled == null) {
            try {
                NodeHealthCheckController.frontendDisabled = PropertyUtil.parseBoolean(System.getProperty("messagepoint.frontend.disabled"));
            } catch (Exception e) {
                LogUtil.getLog(NodeHealthCheckController.class).error("Error:", e);
            }
        }

        return NodeHealthCheckController.frontendDisabled;
    }

    private static JSONObject getDatabaseConnectionCount() {
        C3P0RegistryManager mbean = new C3P0RegistryManager();

        JSONObject connectionCounts = new JSONObject();

        for (Object pool : mbean.getAllPooledDataSources()) {
            if (pool instanceof ComboPooledDataSource) {
                try {
                    JSONObject poolCount = new JSONObject();

                    poolCount.put("numConnections", ((PooledDataSource) pool).getNumConnectionsAllUsers());
                    poolCount.put("numIdleConnections", ((PooledDataSource) pool).getNumIdleConnectionsAllUsers());
                    poolCount.put("numBusyConnections", ((PooledDataSource) pool).getNumBusyConnectionsAllUsers());
                    poolCount.put("poolUtilization",(float) ((PooledDataSource) pool).getNumConnectionsAllUsers() /(float) ((ComboPooledDataSource)pool).getMaxPoolSize());
                    poolCount.put("poolDataSourceName", ((PooledDataSource) pool).getDataSourceName());


                    connectionCounts.put(((PooledDataSource) pool).getDataSourceName(), poolCount);
                } catch (Exception e) {
                    LogUtil.getLog(NodeHealthCheckController.class).error("Error:", e);
                }
            }
        }

        return connectionCounts;
    }

    private static JSONObject getFileDescriptorCounts() {
        JSONObject counts = new JSONObject();

        try {
            OperatingSystemMXBean os = ManagementFactory.getOperatingSystemMXBean();

            if (os instanceof UnixOperatingSystemMXBean) {
                counts.put("openfd", ((UnixOperatingSystemMXBean) os).getOpenFileDescriptorCount());
                counts.put("maxfd", ((UnixOperatingSystemMXBean) os).getMaxFileDescriptorCount());
                counts.put("fdUtilization", (float)((UnixOperatingSystemMXBean) os).getOpenFileDescriptorCount() / (float)((UnixOperatingSystemMXBean) os).getMaxFileDescriptorCount());
            }

        } catch (Exception e) {
            LogUtil.getLog(NodeHealthCheckController.class).error("Error", e);
        }

        return counts;
    }
}
