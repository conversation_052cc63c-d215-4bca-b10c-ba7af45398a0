package com.prinova.messagepoint.controller.contentintelligence;

import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.util.HibernateUtil;

import java.util.ArrayList;
import java.util.List;

public class ContentAssistantListWrapper {
    private List<Long>						selectedIds;
    private String							actionValue;



    public ContentAssistantListWrapper() {
        super();
        this.selectedIds 		= new ArrayList<>();
    }

    public String getActionValue() {
        return actionValue;
    }
    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public List<Long> getSelectedIds() {
        return selectedIds;
    }
    public void setSelectedIds(List<Long> selectedIds) {
        this.selectedIds = selectedIds;
    }

      public List<ContentAssistant> getSelected(){
        List<ContentAssistant> selected = new ArrayList<>();
        for(Long selectedId : this.selectedIds){
            selected.add(HibernateUtil.getManager().getObject(ContentAssistant.class, selectedId));
        }
        return selected;
    }

}

