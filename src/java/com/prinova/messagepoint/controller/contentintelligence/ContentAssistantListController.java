package com.prinova.messagepoint.controller.contentintelligence;

import com.google.gson.JsonObject;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ContentEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.contentintelligence.ContentAssistantListWrapper;
import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.openai.HtmlValidator;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.platform.services.contentintelligence.ContentAssistantBatchService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class ContentAssistantListController extends MessagepointController {

    private static final Log log = LogUtil.getLog(com.prinova.messagepoint.controller.contentintelligence.ContentAssistantListController.class);

    public static final String REQUEST_PARAM_ACTION 		= "action";

    public static final int ACTION_DELETE 					= 3;
    public static final int ACTION_EXECUTE_BATCH 				= 4;

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        JsonObject jval = HtmlValidator.validateHtml("<span>**************</span>", "Is the phone number formatted correctly?");

        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return new ContentAssistantListWrapper();
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

        setOriginForm(request, null);
        return super.showForm(request, response, errors);

    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
                                    BindException errors) throws Exception {

        AnalyticsEvent<ContentEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentEvents.ContentAssistantList);

        try {
            ContentAssistantListWrapper command = (ContentAssistantListWrapper)commandObj;
            int action = ServletRequestUtils.getIntParameter(request, REQUEST_PARAM_ACTION, -1);
            ContentAssistant contentAssistant = command.getSelected().iterator().next();

            if (action == ACTION_DELETE) {
                analyticsEvent.setAction(Actions.Delete);
                ServiceExecutionContext context = DeleteModelService.createContext(contentAssistant.getId(), ContentAssistant.class.getName());
                Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(DeleteModelService.SERVICE_NAME, DeleteModelService.class);
                deleteModelService.execute(context);

                ServiceResponse serviceResponse = context.getResponse();
                if (!serviceResponse.isSuccessful()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(DeleteModelService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" text style transformation profile is not deleted. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                    return showForm(request, response, errors);
                } else {
                    return new ModelAndView(new RedirectView(getSuccessView()));
                }
            } else if (action == ACTION_EXECUTE_BATCH) {
                analyticsEvent.setAction(Actions.ContentAssistantBatch);

                ContentAssistantListWrapper contentAssistantListWrapper = new ContentAssistantListWrapper();
                contentAssistantListWrapper.setSelectedIds(command.getSelectedIds());

                ServiceExecutionContext context = ContentAssistantBatchService.createContext(contentAssistantListWrapper);
                Service batchService = MessagepointServiceFactory.getInstance().lookupService(ContentAssistantBatchService.SERVICE_NAME, ContentAssistantBatchService.class);
                batchService.execute(context);

                ServiceResponse serviceResponse = context.getResponse();
                if (!serviceResponse.isSuccessful()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(DeleteModelService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" content assistant batch failed to execute. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                    return showForm(request, response, errors);
                } else {
                    return new ModelAndView(new RedirectView(getSuccessView()));
                }
            } else {
                return showForm(request, response, errors);
            }

        } finally {
            analyticsEvent.send();
        }
    }
}
