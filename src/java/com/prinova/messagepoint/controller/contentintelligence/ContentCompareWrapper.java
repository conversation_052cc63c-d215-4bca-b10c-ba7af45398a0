package com.prinova.messagepoint.controller.contentintelligence;

import java.io.Serializable;

public class ContentCompareWrapper implements Serializable {

	private static final long serialVersionUID = 373513436586103093L;
	
	private String 								actionValue;

	public ContentCompareWrapper(){
		super();
	}
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

}