package com.prinova.messagepoint.controller.contentintelligence;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.controller.contentintelligence.AsyncContentCompareListVO.ContentCompareListVOFlags;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.FilterUtil;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.jsoup.Jsoup;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.extractRawContentSourceList;

public class AsyncContentCompareListWrapper extends AsyncAbstractListWrapper {

	private static final Log log = LogUtil.getLog(AsyncContentCompareListWrapper.class);

	private List<ContentCompareResultVO> contentCompareResultList;

	public AsyncContentCompareListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, String listSubType, Boolean isSnapOffView, int lowerThreshold, long contentObjectId) {
		this.contentCompareResultList = new ArrayList<>();
		this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex, listSubType, isSnapOffView, lowerThreshold, contentObjectId);
	}

	private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex, String listSubType, Boolean isSnapOffView, int lowerThreshold, long contentObjectId) {

		String comparisonContent = "";
		try {

			// %uFEFF: No width space: comes from cursor placement (can't be decoded)
			comparisonContent = URLDecoder.decode(sSearch.replace("%uFEFF", ""), "UTF-8");
			comparisonContent = StringEscapeUtils.unescapeXml( comparisonContent );
			comparisonContent = ContentObjectContentUtil.translateContentForPersistance( comparisonContent );

			org.jsoup.nodes.Document document = Jsoup.parse(comparisonContent != null ? comparisonContent : "");

			comparisonContent = ContentObjectContentUtil.getUnformattedTextContentForMarcie(document);

		} catch (UnsupportedEncodingException e) {
			log.error(MessageFormat.format("Error while decoding {0} string using {1} encoding: {2}",
					sSearch, "UTF-8", e.toString()), e);
		}

		System.setProperty("marcie.similarity.search.threshold","0.2");

		List<ContentCompareResultVO> list = new ArrayList<>();
		String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
		MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
		MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

		String contentHash = ElasticsearchContentUtils.calculateContentHash(comparisonContent);
		if (listSubType.equalsIgnoreCase("duplicates")) {
			JsonArray duplicateArr = messagepointElasticSearchHandler.searchDuplicatesByHash(contentHash);
			duplicateArr = extractRawContentSourceList(duplicateArr);
			parseElasticsearchResult(duplicateArr, list, listSubType, isSnapOffView);
			if (!UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_UNRESTRICTED_SEARCH)) {
				filterResultForVisibility(list);
			}
		} else {
			float minRange = Float.valueOf(lowerThreshold) / 100f;
			float maxRange = 1.0f;

			JsonArray similarArr = messagepointElasticSearchHandler.searchSimilaritiesByText(comparisonContent, FilterUtil.createSimilarityContext(minRange, maxRange, contentHash));
			parseElasticsearchResult(similarArr, list, listSubType, isSnapOffView);
			if (!UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_UNRESTRICTED_SEARCH)) {
				filterResultForVisibility(list);
			}
		}

		System.getProperties().remove("marcie.similarity.search.threshold");

		ContentObject referenceContentObject = ContentObject.findById(contentObjectId);

		for (Object o : list) {
			if(o instanceof ContentCompareResultVO) {
				ContentCompareResultVO currentResult = (ContentCompareResultVO) o;

				// Remove 'self' from results
				if ( referenceContentObject != null && currentResult.getMarcieID().indexOf( referenceContentObject.getGuid() ) != -1 )
					continue;

				this.contentCompareResultList.add(currentResult);
			}
		}

		// DUAL VERSION ENTRIES: Collapse entries that are WC/Active of the same content
		Collections.sort(this.contentCompareResultList, (o1, o2)->(o2.getMarcieID()).compareTo(o1.getMarcieID()));
		List<ContentCompareResultVO> duplicateStatusEntries = new ArrayList<>();
		for ( int i = 0; i < this.contentCompareResultList.size(); i++ ) {
			if ( i < this.contentCompareResultList.size() - 1 ) {
				// If IDs are the same except version status indicator then items are two versions of the same object
				String currentID = this.contentCompareResultList.get(i).getMarcieID().replace("_WC","_Active");
				String nextID = this.contentCompareResultList.get(i + 1).getMarcieID().replace("_WC","_Active");
				if ( currentID.equals( nextID ) && this.contentCompareResultList.get(i).getText().equals( this.contentCompareResultList.get(i+1).getText() ) ) {
					// Prefer WC entry
					duplicateStatusEntries.add( this.contentCompareResultList.get(i).getMarcieID().indexOf("_Active") != -1 ? this.contentCompareResultList.get(i) : this.contentCompareResultList.get(i + 1) );
					this.contentCompareResultList.get(i).setObjectStatus( ApplicationUtil.getMessage("page.text.wc.and.active") );
					this.contentCompareResultList.get(i).setObjectStatusId(3);
					this.contentCompareResultList.get(i + 1).setObjectStatus( ApplicationUtil.getMessage("page.text.wc.and.active") );
					this.contentCompareResultList.get(i + 1).setObjectStatusId(3);
				}
			}
		}
		this.contentCompareResultList.removeAll( duplicateStatusEntries );

		// SORT
		if ( orderByMap.containsKey("similarity") && orderByMap.get("similarity").equalsIgnoreCase("asc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o2.getSimilarity()).compareTo(o1.getSimilarity()));
		else if ( orderByMap.containsKey("similarity") && orderByMap.get("similarity").equalsIgnoreCase("desc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o1.getSimilarity()).compareTo(o2.getSimilarity()));
		else if ( orderByMap.containsKey("content") && orderByMap.get("content").equalsIgnoreCase("asc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o2.getContent().getUnformattedText()).compareTo(o1.getContent().getUnformattedText()));
		else if ( orderByMap.containsKey("content") && orderByMap.get("content").equalsIgnoreCase("desc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o1.getContent().getUnformattedText()).compareTo(o2.getContent().getUnformattedText()));
		else if ( orderByMap.containsKey("name") && orderByMap.get("name").equalsIgnoreCase("asc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o1.getName()).compareTo(o2.getName()));
		else if ( orderByMap.containsKey("name") && orderByMap.get("name").equalsIgnoreCase("desc") )
			Collections.sort(this.contentCompareResultList, (o1, o2)->(o2.getName()).compareTo(o1.getName()));

		// RETURN RESULT
		int startIndex = (pageIndex - 1) * pageSize;
		int endIndex = pageIndex * pageSize;
		if (endIndex > this.contentCompareResultList.size() )
			endIndex = this.contentCompareResultList.size();

		int resultSize = this.contentCompareResultList.size();
		this.contentCompareResultList = this.contentCompareResultList.subList(startIndex, endIndex);

		super.setiTotalRecords( resultSize );
		super.setiTotalDisplayRecords( resultSize );
	}

	private void filterResultForVisibility(List<ContentCompareResultVO> voList) {

		List<Long> codIds = new ArrayList<>();
		for (ContentCompareResultVO currentItem : voList) {
			if ( currentItem.getInstance() instanceof ContentObjectData )
				codIds.add( ((ContentObjectData)currentItem.getInstance()).getId() );
		}

		User requester = UserUtil.getPrincipalUser();

		List<Document> visibleDocuments = Document.getVisibleDocuments();
		List<String> visibleDocumentIds = new ArrayList<>();
		for ( Document currentDoc: visibleDocuments )
			visibleDocumentIds.add( String.valueOf(currentDoc.getId()) );
		String visibleDocumentIdsStr = !visibleDocumentIds.isEmpty() ? StringUtil.join(visibleDocumentIds,",") : "0";

		List<Long> filteredCodIds = getVisibilityFilteredCoIds(codIds, visibleDocumentIdsStr, requester);

		List<ContentCompareResultVO> filteredList = new ArrayList<>();
		for ( ContentCompareResultVO currentItem: voList )
			if ( (currentItem.getInstance() instanceof ContentObjectData && filteredCodIds.contains( ((ContentObjectData)currentItem.getInstance()).getId() )) )
				filteredList.add( currentItem );

		voList.clear();
		voList.addAll(filteredList);
	}

	public static List<Long> getVisibilityFilteredCoIds(List<Long> coIds, String visibleDocumentIdsStr, User requester) {
		String query =
				"SELECT DISTINCT(co.id) FROM content_object co " +

				"LEFT OUTER JOIN zone z ON co.zone_id = z.id " +
				"LEFT OUTER JOIN workgroup_zone wz ON wz.zone_id = z.id " +
				"LEFT OUTER JOIN workgroup w ON w.id = wz.workgroup_id " +
				"LEFT OUTER JOIN touchpoint_selection tps ON tps.id = co.tp_selection_id " +
				"LEFT OUTER JOIN tp_sel_visible_user tpvu 	ON tpvu.tp_selection_id = tps.id " +
				"LEFT OUTER JOIN content_object_document_map codm ON codm.content_object_id = co.id " +

				"WHERE ( " +

				// SMART TEXT
				"	co.object_type = 4 " +
				"	AND codm.document_id IN (" + visibleDocumentIdsStr + ") " +
				" ) OR ( " +

				// MESSAGES/LST
				" 	( (co.object_type = 1 " +
				"	AND co.document_id IN ("+visibleDocumentIdsStr+ ") ) " +
				"		OR		( co.object_type = 0 AND w.id = " + (requester != null? requester.getWorkgroupId(): 1) + " AND z.enabled = true) ) " +
				"	AND		(co.tp_selection_id IS NULL " +
				"		OR 		(co.tp_selection_id IS NOT NULL AND ( (tps.fully_visible = true AND tps.archive_type_id != 1) OR tpvu.user_id ="  + requester.getId() + " ) ) ) " +
				" ) " +
				getIdInStatement(coIds, "co");

		NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);
		List ids = sqlQuery.list();
		List<Long> filteredCoIds = new ArrayList<>();
		for(Object idObj : ids)
			filteredCoIds.add(((Number)idObj).longValue());

		return filteredCoIds;
	}

	public static String getIdInStatement(List<Long> ids, String alias) {

		if (ids.isEmpty())
			return "AND ( " + alias + ".id IN (0) )";

		StringBuilder inStatement = new StringBuilder("AND (");

		StringBuilder currentIdSet = new StringBuilder();
		int index = 0;
		boolean applyOR = false;
		for (Long currentId : ids) {
			if (!currentIdSet.isEmpty())
				currentIdSet.append(",");
			currentIdSet.append(currentId);
			if ((index % 999 == 0 && index != 0) || (index == ids.size() - 1)) {
				if (applyOR)
					inStatement.append(" OR ");
				inStatement.append(" ").append(alias).append(".id IN (").append(currentIdSet).append(")");
				currentIdSet = new StringBuilder();
				applyOR = true;
			}
			index++;
		}
		inStatement.append(" )");

		return inStatement.toString();
	}

	private void parseElasticsearchResult(JsonArray resultArr, List<ContentCompareResultVO> voList, String listSubType, Boolean isSnapOffView){

		if ( resultArr != null ) {

			for ( JsonElement resultEle : resultArr ) {

				JsonObject resultJsonObj = resultEle.getAsJsonObject();
				String contentId = resultJsonObj.get("id").getAsString();
				String score = resultJsonObj.get("score") != null ? resultJsonObj.get("score").getAsString() : "";
//				String contentText = resultJsonObj.get("text") != null ? resultJsonObj.get("text").getAsString() : "";
				String[] contentIdSpl = contentId.split("_");
				String modelType = contentIdSpl[0];
				String modelGuid = contentIdSpl[1];
				String pgtnGuid = contentIdSpl[2];
//				String langCodeId = contentIdSpl[3];
				String status = contentIdSpl[4];
				JsonElement attributeEle = resultJsonObj.get("attribute");
				JsonObject attributeJsonObj = attributeEle.getAsJsonObject();
				String contentGuid = attributeJsonObj.get("content_guid").getAsString();
				String assetInstanceName = "";
				String instanceStatus = "";
				int instanceStatusId = 0;

				Content content = Content.findByGuid(contentGuid);
				if(content!=null) {
					ContentCompareResultVO voItem = new ContentCompareResultVO();

					ContentObject co = ContentObject.findByGuid(modelGuid);
					if ( co == null )
						continue;

					ContentObjectData cod = null;
					if (status.equals("WC") && co.hasWorkingData()) {
						cod = co.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
					} else if (status.equals("Active") && co.hasActiveData()) {
						cod = co.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
					}
					if(cod==null){
						// As delete is not implemented yet, it might be the case that the WC is still in elasticsearch
						// but not in db
						continue;
					}
					assetInstanceName = co.getName();
					instanceStatus = cod.getStatusDisplay();
					instanceStatusId = cod.isWorking() ? 1 : 2;

					voItem.setInstance(cod);

					voItem.setId(contentId.replaceAll("_","-"));
					voItem.setName(assetInstanceName);
					voItem.setText(content.getUnformattedText());
					voItem.setContent(content);
					voItem.setMarcieID(contentId);
					voItem.setSnapOffView(isSnapOffView);

					if (!pgtnGuid.equals("0"))
						voItem.setParameterGroupTreeNode(ParameterGroupTreeNode.findByGuid(pgtnGuid));

					if (listSubType.equalsIgnoreCase("similarities")) {
						if (score != null && !score.isEmpty()) {
							voItem.setSimilarity(Math.round(Float.valueOf(score) * 10000f) / 100f);
						}
					}
					voItem.setObjectStatus(instanceStatus);
					voItem.setObjectStatusId(instanceStatusId);
					voList.add(voItem);
				}
			}
		}
	}

	@Override
	public void init() {

		List<AsyncContentCompareListVO> aaData 		= new ArrayList<>();

		for (ContentCompareResultVO compareResult : this.contentCompareResultList) {
			AsyncContentCompareListVO vo = new AsyncContentCompareListVO();
			vo.setDisplayMode(getDisplayMode());
			ContentCompareListVOFlags flags = new ContentCompareListVOFlags();
			vo.setContentCompareResult(compareResult);

			vo.setName(compareResult.getName());
			vo.setDT_RowId(compareResult.getContent().getId());

			// Action flags
			setActionFlags(compareResult, flags);

			vo.setFlags(flags);
			aaData.add(vo);
		}
		super.setAaData(aaData);
	}

	public static void setActionFlags(ContentCompareResultVO compareResult, AsyncContentCompareListVO.ContentCompareListVOFlags flags) {
		boolean canInsert = false;
		StateProviderVersionModel inst = compareResult.getInstance();
		if (inst instanceof ContentObjectData) {
			flags.setEmbeddedContentId( ((ContentObjectData)inst).getContentObject().getId() );
			if (((ContentObjectData) inst).getContentObject().getIsTouchpointLocal())
			{
				canInsert = true;
			}
			else
			{
				canInsert = compareResult.getObjectStatusId() != 1;
			}

		}

		flags.setCanInsert( canInsert );
	}
}
