package com.prinova.messagepoint.controller.contentintelligence;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.model.wrapper.AsyncListVOIFrameData;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncContentCompareListVO extends AsyncListVO{

	private ContentCompareResultVO 		contentCompareResult;
	private String						name;
	private AsyncListVOIFrameData		iFrameData;
	private ContentCompareListVOFlags	flags;

	public void setContentCompareResult(ContentCompareResultVO contentCompareResult) {
		this.contentCompareResult = contentCompareResult;
	}

	public String generateCrossLink() {
		String onClickLink = "", itemName = "";

		if (contentCompareResult.getInstance() instanceof ContentObjectData) {

			ContentObject contentObject = ((ContentObjectData) contentCompareResult.getInstance()).getContentObject();
			Content content = contentCompareResult.getContent();
			ParameterGroupTreeNode pgtn = Content.findPGTN(content.getId());

			String pgtnParam = "";
			if ( pgtn != null )
				if ( contentObject.isDynamicVariantEnabled() )
					pgtnParam = "&paramInstId=" + pgtn.getId();
				else if ( contentObject.isStructuredContentEnabled() ) {
					TouchpointContentObjectContentSelection contentSelection = new TouchpointContentObjectContentSelection(contentObject, pgtn, null);
					if ( !contentSelection.getTouchpointSelection().isMaster() )
						pgtnParam = "&contentSelectionId=" + contentSelection.getId();
				}
			if (contentObject.isGlobalSmartText())
			{
				onClickLink = "onclick=\"javascript:crossLink('" + ApplicationUtil.getWebRoot() + "content/global_content_list.form?documentId=" + contentObject.getDocuments().iterator().next().getId() + "&contentObjectId=" + contentObject.getId() +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2") + "&isFreeform=" + contentObject.getIsSharedFreeform() + "'); return false;\"";
			} else if ( contentObject.getIsTouchpointLocal() )
				onClickLink = "onclick=\"javascript:crossLink('" + ApplicationUtil.getWebRoot() + "touchpoints/local_content_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() + "&localContext=1" +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ?" 1" : "2") + "'); return false;\"";
			else
				onClickLink = "onclick=\"javascript:crossLink('" + ApplicationUtil.getWebRoot() + "touchpoints/touchpoint_content_object_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2") + "'); return false;\"";

		}

		if (itemName.isEmpty())
			itemName = contentCompareResult.getInstance().getName();

		String nameHTML = "<a href=\"#\" " + onClickLink + ">" + itemName + "</a>";

		return nameHTML;
	}

	public String getName() {

		String divider = "<span style=\"padding: 0px 8px;\">|</span>";
		
		StringBuilder cardHTML = new StringBuilder();
		cardHTML.append("<div style=\"white-space: nowrap;\">");

		// Tooltip: Touchpoint name for messages
		String tooltip = "";
		Document touchpoint = null;
		if ( contentCompareResult.getInstance() instanceof ContentObjectData) {
			ContentObject contentObject = ((ContentObjectData) contentCompareResult.getInstance()).getContentObject();
			touchpoint = contentObject.getFirstDocumentDelivery();
			tooltip = " title=\"" + touchpoint.getName() + "\" data-toggle=\"tooltip\" " ;
		}

		String objectNameDisplay = contentCompareResult.getName();
		if ( contentCompareResult.getIsSnapOffView() )
			objectNameDisplay = generateCrossLink();

		// Name
		cardHTML.append("	<div ").append(tooltip).append(" style=\"display: inline-block; max-width: 90%; min-width: 90%; vertical-align: top; font-weight: 700; margin-bottom: 4px; white-space: normal;\">").append(objectNameDisplay).append("	</div>");

		// Similarity
		if ( contentCompareResult.getSimilarity() != null )
			cardHTML.append("<div style=\"display: inline-block; min-width: 10%; vertical-align: top; text-align: right;\">").append(contentCompareResult.getSimilarity()).append("%</div>");
		
		cardHTML.append("</div>");

		// Content
		if ( contentCompareResult.getText() != null ) {
			cardHTML.append("<div id=\"resultContent_").append(contentCompareResult.getContent().getId()).append("\" class=\"resultContentContainer\" style=\"white-space: normal; display: none;\">");
			if(touchpoint != null){
				cardHTML.append(contentCompareResult.getContent().getContent(touchpoint));
			}else {
				cardHTML.append(contentCompareResult.getContent().getContent());
			}
			cardHTML.append("</div>");
			cardHTML.append("<div id=\"diffContent_").append(contentCompareResult.getContent().getId()).append("\" class=\"resultDiffContainer\" style=\"white-space: normal; display: none;\">");
			cardHTML.append("</div>");
		}
		
		cardHTML.append("<div style=\"white-space: nowrap; color: #777; font-weight: 100;\">");

		// Icons
		String icons = ContentObjectContentUtil.getTagIcons( (ContentObject) contentCompareResult.getInstance().getModel(), true );
		if ( !icons.isEmpty() )
			cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle; padding-right: 4px; opacity: 0.75;\">").append(icons).append("</div>");

		// Status
		cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(ApplicationUtil.getMessage("page.label.status")).append(":</div>");
		cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(contentCompareResult.getObjectStatus()).append("</div>");

		// Variant
		if ( contentCompareResult.getParameterGroupTreeNode() != null ) {
			cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(divider).append(ApplicationUtil.getMessage("page.label.variant")).append(":</div>");
			cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(contentCompareResult.getParameterGroupTreeNode().getName()).append("</div>");
		}

		cardHTML.append("</div>");

		return cardHTML.toString();
	}

	public void setName(String name) {
		this.name = name;
	}

	public AsyncListVOIFrameData getiFrameData() {
		return iFrameData;
	}

	public void setiFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}
	
	public String getBinding() {
		return "<input id='listItemCheck_"+contentCompareResult.getContent().getId()+"' type='checkbox' value='"+contentCompareResult.getContent().getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}

	public ContentCompareListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(ContentCompareListVOFlags flags) {
		this.flags = flags;
	}

	public static class ContentCompareListVOFlags{
		private boolean	canInsert;
		private Long contentObjectId;

		public boolean isCanInsert() {
			return canInsert;
		}
		public void setCanInsert(boolean canInsert) {
			this.canInsert = canInsert;
		}

		public Long getEmbeddedContentId() {
			return contentObjectId;
		}
		public void setEmbeddedContentId(Long contentObjectId) {
			this.contentObjectId = contentObjectId;
		}
	}
}
