package com.prinova.messagepoint.controller.contentintelligence;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.util.LogUtil;
import okhttp3.*;
import org.apache.commons.logging.Log;
import org.apache.commons.text.StringEscapeUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class AsyncContentAssistantController implements Controller {

    private static final Log log = LogUtil.getLog(com.prinova.messagepoint.controller.contentintelligence.AsyncContentAssistantController.class);

    public static final String PARAM_ACTION;

    static {
        PARAM_ACTION = "action";
    }

    public static final String PARAM_CONTENT_OBJECT_ID 	= "contentObjectId";
    public static final String PARAM_CONTENTS 			= "contents";

    public static final String ACTION_GET_ASSISTANT_INFO = "get_assistant_info";
    public static final String ACTION_QUERY = "query";



    private void writeResponse(HttpServletResponse response, JSONObject jsonResponse, int statusCode) {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setStatus(statusCode);
        try (ServletOutputStream out = response.getOutputStream()) {
            out.write(jsonResponse.toString().getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("Error writing response: ", e);
        }
    }

    public ModelAndView handleRequest(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response) throws Exception {
        String action = ServletRequestUtils.getStringParameter(request, PARAM_ACTION, "unknown");
        JSONObject returnObj = new JSONObject();

        try {
            if (action.equalsIgnoreCase(ACTION_GET_ASSISTANT_INFO)) {
                returnObj = getContentAssistantInfoResponseJSON(request);
                writeResponse(response, returnObj, HttpServletResponse.SC_OK);
            } else if (action.equalsIgnoreCase(ACTION_QUERY)) {
                returnObj = getContentAssistantQueryResponseJSON(request);
                writeResponse(response, returnObj, HttpServletResponse.SC_OK);
            } else {
                returnObj.put("error", true);
                returnObj.put("message", "Invalid action");
                writeResponse(response, returnObj, HttpServletResponse.SC_BAD_REQUEST);
            }
        } catch (Exception e) {
            returnObj.put("error", true);
            returnObj.put("message", "Error - Unable to process request");
            log.error("Error: Unable to process request: ", e);
            writeResponse(response, returnObj, HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return null;
    }

    private JSONObject getContentAssistantInfoResponseJSON(HttpServletRequest request) {
        long contentId = ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_OBJECT_ID, -1L);

        ContentObject contentObject = ContentObject.findById(contentId);

        JSONObject returnObj = new JSONObject();

        List<ContentAssistant> contentAssistants = ContentAssistant.findValidatorsForContentObject(contentObject);

        returnObj.put("count", contentAssistants.size());
        returnObj.put("names", contentAssistants.stream().map(ContentAssistant::getName).toArray());

        return returnObj;
    }

    private String prepareQuery(ContentAssistant contentAssistant, JSONArray contents) {
        String query = contentAssistant.getQuery().replaceAll("<br>", "\n");
        query = StringEscapeUtils.unescapeHtml4(query);
        StringBuilder queryContentInsert = new StringBuilder();
        for (int i = 0; i < contents.length(); i++) {
            JSONObject contentObj = contents.getJSONObject(i);
            String contentText = contentObj.getString("content");
            int localeId = contentObj.getInt("locale");
            MessagepointLocale currentLocale = MessagepointLocale.findById(localeId);
            queryContentInsert.append("START MARKUP FOR LOCALE ").append(currentLocale.getDisplayName().toUpperCase()).append("\n\n").append(contentText).append("\nEND MARKUP FOR LOCALE ").append(currentLocale.getDisplayName().toUpperCase()).append("\n\n");
        }
        query = query.replace("{content_markup}", queryContentInsert.toString());
        query += "\n\nPlease respond in the following JSON format:\n" +
                "{\n  \"result\": \"<PASS|FAIL|NOT_APPLICABLE|UNCERTAIN>\",\n  \"reason\": \"<text justification for the result>\"\n}";
        return query;
    }

    private JSONObject getContentAssistantQueryResponseJSON(HttpServletRequest request) {
        long contentObjectId = ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_OBJECT_ID, -1L);
        ContentObject contentObject = ContentObject.findById(contentObjectId);
        JSONObject returnObj = new JSONObject();
        if (contentObject == null) {
            returnObj.put("error", true).put("message", "Invalid content object ID");
            return returnObj;
        }

        List<ContentAssistant> contentAssistants = ContentAssistant.findValidatorsForContentObject(contentObject);
        String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENTS, "[]");
        JSONArray resultsArray = new JSONArray();

        JSONArray contents = new JSONArray(content);
        contentAssistants.forEach(contentAssistant -> {

            // Skip this ContentAssistant if no selector match is made against the content
            if (contentAssistant.getContentSelector() != null && !contentAssistant.getContentSelector().isEmpty()) {
                boolean selectorMatch = false;
                for (int i = 0; i < contents.length(); i++) {
                    JSONObject contentObj = contents.getJSONObject(i);
                    String contentText = contentObj.getString("content");
                    Document doc = Jsoup.parse(contentText);
                    Elements selectedElements = doc.select(contentAssistant.getContentSelector());
                    if (!selectedElements.isEmpty()) {
                        selectorMatch = true;
                        break;
                    }
                }
                if (!selectorMatch) {
                    // Skip this ContentAssistant if no content matches the selector
                    return;
                }
            }

            String query = prepareQuery(contentAssistant, contents);
            MediaType mediaType = MediaType.parse("application/json");
            JSONObject jsonBody = new JSONObject().put("prompt", query);
            RequestBody body = RequestBody.create(mediaType, jsonBody.toString());
            Request queryRequest = new Request.Builder()
                    .url("https://tjpriw2omfpe2ufrm3qlovmtoa0cpxic.lambda-url.us-east-1.on.aws/prompt")
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("X-API-KEY", "b7523605-e3647ba0-6f7f57c6-18d009c3b70")
                    .build();

            try {
                String responseBody = executeHttpRequestWithRetries(queryRequest, 3);
                JSONObject assistantResult = processQueryAPIResponse(responseBody);
                assistantResult.put("name", contentAssistant.getName());
                resultsArray.put(assistantResult);
            } catch (IOException e) {
                JSONObject errorResult = new JSONObject()
                        .put("error", true)
                        .put("message", "Error - Unable to retrieve content assistant response after retries")
                        .put("name", contentAssistant.getName());
                resultsArray.put(errorResult);
                log.error("Error: Unable to retrieve content assistant response: ", e);
            }
        });

        returnObj.put("results", resultsArray);
        return returnObj;
    }

    private JSONObject processQueryAPIResponse(String jsonResponse) {
        try {
            JSONObject responseObj = new JSONObject(jsonResponse);
            JSONObject statusObj = responseObj.getJSONObject("status");
            boolean success = statusObj.getBoolean("success");
            int code = statusObj.getInt("code");

            if (success && code == 200) {
                String resultString = responseObj.getJSONObject("result").getString("response");
                JSONObject resultObj = new JSONObject(resultString);

                JSONObject newResponseObj = new JSONObject();
                newResponseObj.put("result", resultObj.getString("result"));
                newResponseObj.put("reason", resultObj.getString("reason"));

                return newResponseObj;
            } else {
                return new JSONObject().put("error", "Invalid status");
            }
        } catch (Exception e) {
            log.error("Error processing sample response: ", e);
            return new JSONObject().put("error", "Error processing response");
        }
    }

    private String executeHttpRequestWithRetries(Request queryRequest, int maxRetries) throws IOException {
        OkHttpClient client = new OkHttpClient();
        boolean success = false;
        int attempts = 0;
        String responseBody = "";
        while (!success && attempts < maxRetries) {
            try (Response response = client.newCall(queryRequest).execute()) {
                if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
                assert response.body() != null;
                responseBody = response.body().string();
                success = true;
            } catch (SocketTimeoutException ste) {
                attempts++;
                log.error("Attempt " + attempts + ": Timeout occurred. Retrying...");
                if (attempts >= maxRetries) {
                    log.error("All retry attempts failed.");
                    throw new IOException("Unable to retrieve content assistant response after retries");
                }
            }
        }
        return responseBody;
    }

}
