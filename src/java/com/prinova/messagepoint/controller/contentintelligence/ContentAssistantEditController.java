package com.prinova.messagepoint.controller.contentintelligence;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ContentEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.contentintelligence.AcceptanceCriteriaType;
import com.prinova.messagepoint.model.contentintelligence.ContentAssistant;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.contentintelligence.CreateOrUpdateContentAssistantService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serial;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

public class ContentAssistantEditController extends MessagepointController implements Serializable {

    private static final long serialVersionUID = -1436337223220487398L;

    public static final int ACTION_UPDATE 				        = 1;
    public static final String PARAM_CONTENT_ASSISTANT_ID 	    = "contentAssistantId";

    public static String REQUEST_PARM_SAVE_SUCCESS		        = "saveSuccess";

    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
        binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
        binder.registerCustomEditor(String.class, new StringXSSEditor());

    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        HashMap<String, Object> referenceData = new HashMap<>();

        referenceData.put("documents", Document.findAllDocumentsAndProjectsVisible(true));

        referenceData.put("locales", MessagepointLocale.getAllLocales());

        referenceData.put("acceptanceCriteriaTypes", AcceptanceCriteriaType.listAll());

        return referenceData;
    }

    protected Object formBackingObject(@NotNull HttpServletRequest request) {
        ContentAssistant contentAssistant = getContentAssistantObject(request);
        Command command = new Command();

        command.setName(contentAssistant.getName());

        command.setQuery(contentAssistant.getQuery());


        command.setEnabled(contentAssistant.isEnabled());

        String triggerCriteria = contentAssistant.getTriggerCriteria();
        Set<Integer> triggerCriteriaSet = Arrays.stream(triggerCriteria.split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
        command.setTriggerCriteria(triggerCriteriaSet);

        command.setSystemControlled(contentAssistant.isSystemControlled());
        command.setContentSelector(contentAssistant.getContentSelector());
        command.setBatchThrottle(contentAssistant.getBatchThrottle());
        command.setTargetAllTouchpoints(contentAssistant.isTargetAllTouchpoints());
        command.setTargetTouchpoints(contentAssistant.getTargetTouchpoints());
        command.setTargetAllObjects(contentAssistant.isTargetAllObjects());
        command.setTargetObjectsStatus(contentAssistant.getTargetObjectsStatus());

        String targetObjects = contentAssistant.getTargetObjects();
        Set<Long> targetObjectsSet = Arrays.stream(targetObjects.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        command.setTargetObjects(targetObjectsSet);

        command.setTargetAllLocales(contentAssistant.isTargetAllLocales());
        command.setTargetLocales(contentAssistant.getTargetLocales());

        return command;
    }

    protected ContentAssistant getContentAssistantObject(HttpServletRequest request) {
        long contentAssistantId = ServletRequestUtils.getLongParameter(request, "contentAssistantId", -1);
        if (contentAssistantId == -1) {
            return new ContentAssistant();
        } else {
            return ContentAssistant.findById(contentAssistantId);
        }
    }

    @Override
    protected boolean suppressValidation(HttpServletRequest request, Object command) {
        // Implement validation suppression logic if needed
        return super.suppressValidation(request, command);
    }

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<ContentEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentEvents.ContentAssistantEdit);

        try {
            analyticsEvent.setAction(Actions.ContentAssistantEdit);

            String submitType = ServletRequestUtils.getStringParameter(request, SUBMIT_PARAM_SUBMIT_TYPE, "");

            if(submitType.equals(MessagepointController.SUBMIT_PARAM_CANCEL_AND_EXIT)) {

                String [] originForm = getOriginForm(request, true);
                String origin = originForm[0];

                if (!originForm[1].isEmpty())
                    origin += "?" + originForm[1];

                return new ModelAndView(new RedirectView(origin));

            }

            ContentAssistantEditController.Command command = (ContentAssistantEditController.Command)commandObj;

            String triggerCriteria = command.getTriggerCriteria().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String targetObjects = command.getTargetObjects().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            ServiceExecutionContext context = CreateOrUpdateContentAssistantService.createContextForCreateOrUpdate(
                    command.getContentAssistantId(),
                    command.getName(),
                    command.getQuery(),
                    command.isEnabled(),
                    triggerCriteria,
                    command.isSystemControlled(),
                    command.getContentSelector(),
                    command.getBatchThrottle(),
                    command.isTargetAllTouchpoints(),
                    command.getTargetTouchpoints(),
                    command.isTargetAllObjects(),
                    targetObjects,
                    command.getTargetObjectsStatus(),
                    command.isTargetAllLocales(),
                    command.getTargetLocales()
            );

            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateContentAssistantService.SERVICE_NAME, CreateOrUpdateContentAssistantService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            if (context.getResponse().isSuccessful()) {

                String current = request.getRequestURL().toString();
                String contentAssistantId = String.valueOf(context.getResponse().getResultValueBean());

                switch (submitType) {

                    case MessagepointController.SUBMIT_PARAM_SAVE_AND_ADD : {

                        Map<String, Object> params = new HashMap<>();
                        params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getStringParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, null));
                        params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                        return new ModelAndView(new RedirectView(current), params);

                    }

                    case MessagepointController.SUBMIT_PARAM_SAVE_AND_GOBACK : {

                        String [] originForm = getOriginForm(request, true);
                        String origin = originForm[0];

                        if (!originForm[1].isEmpty())
                            origin += "?" + originForm[1];

                        Map<String, Object> params = new HashMap<>();
                        params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                        return new ModelAndView(new RedirectView(origin), params);

                    }

                    case MessagepointController.SUBMIT_PARAM_SAVE_AND_GOTOLIST : {

                        Map<String, Object> params = new HashMap<>();
                        params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                        return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "contentintelligence/content_assistant_list.form"), params);

                    }

                    case MessagepointController.SUBMIT_PARAM_SAVE_AND_STAY :
                    default: {

                        Map<String, Object> params = new HashMap<>();
                        params.put(PARAM_CONTENT_ASSISTANT_ID, contentAssistantId);
                        params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getStringParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, null));
                        params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                        return new ModelAndView(new RedirectView(current), params);

                    }

                }

            } else {

                ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                return super.showForm(request, response, errors);

            }
        } finally {
            analyticsEvent.send();
        }

    }

    public static class Command implements Serializable {
        @Serial
        private static final long serialVersionUID = 2378941586266023294L;
        private Long contentAssistantId = 0L;

        private String name;
        private String query;
        private boolean enabled;
        private Set<Integer> triggerCriteria;
        private boolean systemControlled;
        private String contentSelector;
        private Integer batchThrottle;

        private boolean targetAllTouchpoints;
        private Set<Document> targetTouchpoints;
        private boolean targetAllObjects;
        private String targetObjectsStatus;
        private Set<Long> targetObjects;
        private boolean targetAllLocales;
        private Set<MessagepointLocale> targetLocales;

        public Long getContentAssistantId() {
            return contentAssistantId;
        }

        public void setContentAssistantId(Long contentAssistantId) {
            this.contentAssistantId = contentAssistantId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Set<Integer> getTriggerCriteria() { return triggerCriteria; }

        public void setTriggerCriteria(Set<Integer> triggerCriteria) { this.triggerCriteria = triggerCriteria; }

        public boolean isSystemControlled() {
            return systemControlled;
        }

        public void setSystemControlled(boolean systemControlled) {
            this.systemControlled = systemControlled;
        }

        public String getContentSelector() { return contentSelector; }

        public void setContentSelector(String contentSelector) { this.contentSelector = contentSelector; }

        public Integer getBatchThrottle() { return batchThrottle; }

        public void setBatchThrottle(Integer batchThrottle) { this.batchThrottle = batchThrottle; }

        public boolean isTargetAllTouchpoints() {
            return targetAllTouchpoints;
        }

        public void setTargetAllTouchpoints(boolean targetAllTouchpoints) {
            this.targetAllTouchpoints = targetAllTouchpoints;
        }

        public Set<Document> getTargetTouchpoints() {
            return targetTouchpoints;
        }

        public void setTargetTouchpoints(Set<Document> targetTouchpoints) {
            this.targetTouchpoints = targetTouchpoints;
        }

        public boolean isTargetAllObjects() {
            return targetAllObjects;
        }

        public void setTargetAllObjects(boolean targetAllObjects) {
            this.targetAllObjects = targetAllObjects;
        }

        public String getTargetObjectsStatus() { return targetObjectsStatus; }

        public void setTargetObjectsStatus(String targetObjectsStatus) { this.targetObjectsStatus = targetObjectsStatus; }

        public Set<Long> getTargetObjects() { return targetObjects; }

        public void setTargetObjects(Set<Long> targetObjects) { this.targetObjects = targetObjects; }

        public boolean isTargetAllLocales() {
            return targetAllLocales;
        }

        public void setTargetAllLocales(boolean targetAllLocales) {
            this.targetAllLocales = targetAllLocales;
        }

        public Set<MessagepointLocale> getTargetLocales() {
            return targetLocales;
        }

        public void setTargetLocales(Set<MessagepointLocale> targetLocales) {
            this.targetLocales = targetLocales;
        }
    }
}
