package com.prinova.messagepoint.controller.contentintelligence;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ContentCompareValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		ContentCompareWrapper wrapper = (ContentCompareWrapper) commandObj;

		int action = Integer.valueOf(wrapper.getActionValue() );

	}
}
