package com.prinova.messagepoint.controller.contentintelligence;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.ContentIntelligenceEvents;
import com.prinova.messagepoint.security.StringXSSEditor;

public class ContentCompareController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(ContentCompareController.class);

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long variantId = ServletRequestUtils.getLongParameter(request, "variantId", -1L);

		String contextLink = "", contextName = "";

		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantId);

		if ( contentObject != null ) {

			String pgtnParam = "";
			if ( pgtn != null )
				if ( contentObject.isDynamicVariantEnabled() )
					pgtnParam = "&paramInstId=" + pgtn.getId();
				else if ( contentObject.isStructuredContentEnabled() ) {
					TouchpointContentObjectContentSelection contentSelection = new TouchpointContentObjectContentSelection(contentObject, pgtn, null);
					pgtnParam = "&contentSelectionId=" + contentSelection.getId();
				}
			if (contentObject.isGlobalSmartText())
			{
				contextLink = ApplicationUtil.getWebRoot() + "content/global_content_list.form?documentId=" + contentObject.getDocuments().iterator().next().getId() + "&contentObjectId=" + contentObject.getId() +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2") + "&isFreeform=" + contentObject.getIsSharedFreeform();

			} else if ( contentObject.isLocalSmartText() )
				contextLink = ApplicationUtil.getWebRoot() + "touchpoints/local_content_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() + "&localContext=1" +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ?" 1" : "2");
			else
				contextLink = ApplicationUtil.getWebRoot() + "touchpoints/touchpoint_content_object_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() +
						pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2");

			contextName = contentObject.getName();

		}

		referenceData.put("contextLink", contextLink);
		referenceData.put("contextName", contextName);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected ContentCompareWrapper formBackingObject(HttpServletRequest request) throws Exception {
		AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.AssistedAuthoring);
		analyticsEvent.setAction(ContentIntelligenceEvents.AssistedAuthoringActions.Matches);
		analyticsEvent.send();
		return new ContentCompareWrapper();
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.AssistedAuthoring);
		analyticsEvent.setAction(ContentIntelligenceEvents.AssistedAuthoringActions.Matches);

		try {
			ContentCompareWrapper wrapper = (ContentCompareWrapper) commandObj;

			int action = -1;
			if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
				action = Integer.valueOf(wrapper.getActionValue()).intValue();
			}

			if ( action == 1 ) {

				return null;

			}
			
			return null;
		} finally {
			analyticsEvent.send();
		}
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> params = new HashMap<>();
		
		//long documentId 		= ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_DOCUMENTID, -1L);
		//params.put(TouchpointContentObjectListController.REQ_PARM_DOCUMENTID, documentId);

		return params;
	}

	
	
}
