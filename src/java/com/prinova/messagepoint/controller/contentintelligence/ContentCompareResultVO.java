package com.prinova.messagepoint.controller.contentintelligence;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.stateprovider.StateProviderVersionModel;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;

public class ContentCompareResultVO extends AsyncListVO{

	private String 						id;
	private String						marcieID;
	private String						name;
	private String 						text;
	private String						objectStatus;
	private int							objectStatusId;
	private ParameterGroupTreeNode		parameterGroupTreeNode;
	private Float						similarity;
	private StateProviderVersionModel 	instance;
	private Content 					content;
	private Boolean						isSnapOffView;

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

	public String getMarcieID() { return marcieID; }
	public void setMarcieID(String marcieID) { this.marcieID = marcieID; }

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}

	public String getObjectStatus() {
		return objectStatus;
	}
	public void setObjectStatus(String objectStatus) {
		this.objectStatus = objectStatus;
	}

	public int getObjectStatusId() { return objectStatusId; }
	public void setObjectStatusId(int objectStatusId) {
		this.objectStatusId = objectStatusId;
	}

	public ParameterGroupTreeNode getParameterGroupTreeNode() {
		return parameterGroupTreeNode;
	}
	public void setParameterGroupTreeNode(ParameterGroupTreeNode parameterGroupTreeNode) {
		this.parameterGroupTreeNode = parameterGroupTreeNode;
	}

	public Float getSimilarity() {
		return similarity;
	}
	public void setSimilarity(Float similarity) {
		this.similarity = similarity;
	}
	
	public StateProviderVersionModel getInstance() {
		return instance;
	}
	public void setInstance(StateProviderVersionModel instance) {
		this.instance = instance;
	}

	public Content getContent() { return content; }
	public void setContent(Content content) { this.content = content; }

	public Boolean getIsSnapOffView() { return isSnapOffView; }
	public void setSnapOffView(Boolean snapOffView) { isSnapOffView = snapOffView; }

}
