package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.AsyncBackgroundTaskDetailUtil;
import com.prinova.messagepoint.util.LogUtil;

public class AsyncBackgroundTaskDetailController implements Controller {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(AsyncBackgroundTaskDetailController.class);

	public static final String REQ_PARM_TASK_THREAD_ID 		= "task_thread_id";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Long backgroundThreadId 	= ServletRequestUtils.getLongParameter(request, REQ_PARM_TASK_THREAD_ID, -1L);
		AsyncBackgroundTaskDetail taskDetail = AsyncBackgroundTaskDetailUtil.getTaskDetailObject(backgroundThreadId);
		if(taskDetail != null) {
			return taskDetail.show(request, response);
		}
		return null;
	}
}
