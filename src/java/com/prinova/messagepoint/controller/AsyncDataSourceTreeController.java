package com.prinova.messagepoint.controller;


import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.jstree.DataRecordTreeSerializer;
import com.prinova.messagepoint.util.jstree.JsonDataDefTreeSerializer;
import com.prinova.messagepoint.util.jstree.XmlDataTagTreeSerializer;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;


public class AsyncDataSourceTreeController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncDataSourceTreeController.class);

    private enum RequestMethod {
        ATTRIBUTES,
        DATARECORD,
        DATARECORD_SUMMARY,
        ELEMENTS,
        TREEDATA,
        UNKNOWN,
        XML_DATAELEMENTS,
        XML_TAG_SUMMARY,
        JSON_DATAELEMENTS,
        JSON_DEF_SUMMARY,
    }

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        switch (getRequestType(request)) {
            case TREEDATA:
                sendResponse(response, getTreeData(request));
                break;
            case ATTRIBUTES:
                sendResponse(response, getAttributes(request));
                break;
            case ELEMENTS:
                sendResponse(response, getElements(request));
                break;
            case XML_DATAELEMENTS:
                sendResponse(response, getXmlElements(request));
                break;
            case XML_TAG_SUMMARY:
                sendResponse(response, getXmlTagSummary(request));
                break;
            case JSON_DATAELEMENTS:
                sendResponse(response, getJsonElements(request));
                break;
            case JSON_DEF_SUMMARY:
                sendResponse(response, getJsonDefSummary(request));
                break;
            case DATARECORD_SUMMARY:
                sendResponse(response, getDataRecordSummary(request));
                break;
            default:
                response.setStatus(HttpStatus.SC_BAD_REQUEST);

        }

        return null;
    }



    private void sendResponse(HttpServletResponse response, Object result) {
        try {
            response.setContentType("application/json");
            ServletOutputStream out = response.getOutputStream();
            out.write(result.toString().getBytes("UTF-8"));
        } catch (IOException ex) {
            log.error("Error processing async request: " + ex.getMessage());
        }
    }

    private String getTreeData(HttpServletRequest request) {
        String result;
        long dataSourceId = ServletRequestUtils.getLongParameter(request, "dsid", Long.MIN_VALUE);
        long selectedId = ServletRequestUtils.getLongParameter(request, "selectedNodeId", Long.MIN_VALUE);
        DataSource source = DataSource.findById(dataSourceId);

        source.getUpdatedDate();

        Set<XmlDataTagDefinition> xmlTags = source.getXmlDataTagDefinitions();
        Set<JSONDataDefinition> jsonDefs = source.getJsonDataDefinitions();
        if (xmlTags != null && !xmlTags.isEmpty()) {
            XmlDataTagTreeSerializer treeSerializer = new XmlDataTagTreeSerializer(source, selectedId);
            result = treeSerializer.getTreeStructureJson();
        } else if(jsonDefs != null && !jsonDefs.isEmpty()){
            JsonDataDefTreeSerializer treeSerializer = new JsonDataDefTreeSerializer(source, selectedId);
            result = treeSerializer.getTreeStructureJson();
        } else {
            DataRecordTreeSerializer treeSerializer = new DataRecordTreeSerializer(source, selectedId);
            result = treeSerializer.getTreeStructureJson();
        }

        return result;
    }

    private JSONArray getAttributes(HttpServletRequest request) throws JSONException {
        JSONArray results = new JSONArray();
        long xdtid = ServletRequestUtils.getLongParameter(request, "xdtid", -1L);
        XmlDataTagDefinition item = XmlDataTagDefinition.findById(xdtid);
        XmlDataElement[] sortedElements = Stream.of(item.getDataElementArray()).sorted(Comparator.comparing(IdentifiableMessagePointModel::getName, Comparator.nullsFirst(Comparator.naturalOrder()))).toArray(XmlDataElement[]::new);

        for (XmlDataElement element : sortedElements) {
            if (element.getIsAttribute()) {
                JSONObject attribute = new JSONObject();
                attribute.put("name", element.getName());
                attribute.put("id", element.getId());
                DataSubtype subtype = DataSubtype.findById(element.getDataSubtypeId());
                attribute.put("type", ApplicationUtil.getMessage(subtype != null ? subtype.getName() : ""));
                attribute.put("attributeName", element.getAttributeName());
                attribute.put("externalFormatText", element.getExternalFormatText());
                attribute.put("isAnonymized", element.getAnonymized());
                results.put(attribute);
            }
        }

        return results;
    }

    private JSONArray getElements(HttpServletRequest request) throws JSONException {
        JSONArray results = new JSONArray();
        long drid = ServletRequestUtils.getLongParameter(request, "drid", -1L);
        DataElement[] sortedElements = Stream.of(DataRecord.findById(drid).getDataElementArray()).sorted(Comparator.comparing(IdentifiableMessagePointModel::getName, Comparator.nullsFirst(Comparator.naturalOrder()))).toArray(DataElement[]::new);

        for (DataElement element : sortedElements) {
            JSONObject attribute = new JSONObject();
            attribute.put("name", element.getName());
            attribute.put("id", element.getId());
            DataSubtype subtype = DataSubtype.findById(element.getDataSubtypeId());
            attribute.put("type", ApplicationUtil.getMessage(subtype != null ? subtype.getName() : ""));
            attribute.put("startLocation", element.getStartLocation());
            attribute.put("isAnonymized", element.getAnonymized());
            results.put(attribute);
        }
        return results;
    }

    private JSONArray getXmlElements(HttpServletRequest request) throws JSONException {
        JSONArray results = new JSONArray();
        long xdtid = ServletRequestUtils.getLongParameter(request, "xdtid", -1L);
        XmlDataElement[] sortedElements = Stream.of(XmlDataTagDefinition.findById(xdtid).getDataElementArray()).sorted(Comparator.comparing(IdentifiableMessagePointModel::getName, Comparator.nullsFirst(Comparator.naturalOrder()))).toArray(XmlDataElement[]::new);

        for (XmlDataElement element : sortedElements) {
            JSONObject attribute = new JSONObject();
            if (!element.getIsAttribute()) {
                attribute.put("name", element.getName());
                attribute.put("id", element.getId());
                DataSubtype subtype = DataSubtype.findById(element.getDataSubtypeId());
                attribute.put("type", ApplicationUtil.getMessage(subtype != null ? subtype.getName() : ""));
                attribute.put("externalFormatText", element.getExternalFormatText());
                attribute.put("isAnonymized", element.getAnonymized());
                results.put(attribute);
            }
        }

        return results;
    }

    private JSONObject getXmlTagSummary(HttpServletRequest request) throws JSONException {
        long xdtid = ServletRequestUtils.getLongParameter(request, "xdtid", -1L);
        XmlDataTagDefinition node = XmlDataTagDefinition.findById(xdtid);
        JSONObject item = new JSONObject();

        item.put("name", node.getName());
        item.put("id", node.getId());
        item.put("isRepeating", node.isRepeating());
        item.put("isStartCustomer", node.isStartCustomer());
        item.put("parentName", node.getParentTag() != null ? node.getParentTag().getName() : StringUtils.EMPTY);
        item.put("isStartDataGroup", node.isStartDataGroup());
        item.put("dataGroup", node.getDisplayDataGroup() != null ? node.getDisplayDataGroup().getName() : StringUtils.EMPTY);
        item.put("breakIndicator", node.getBreakIndicator());

        JSONArray variables = getVariables(node.getDataElementArray());
        item.put("hasVariables", !variables.isEmpty());
        item.put("variables", variables);

        return item;
    }

    private JSONArray getJsonElements(HttpServletRequest request) throws JSONException {
        JSONArray results = new JSONArray();
        long jddid = ServletRequestUtils.getLongParameter(request, "jddid", -1L);
        JSONDataElement element = JSONDataDefinition.findById(jddid).getJsonDataElement();

        JSONObject attribute = new JSONObject();
        attribute.put("name", element.getName());
        attribute.put("id", element.getId());
        DataSubtype subtype = DataSubtype.findById(element.getDataSubtypeId());
        attribute.put("type", ApplicationUtil.getMessage(subtype != null ? subtype.getName() : ""));
        attribute.put("externalFormatText", element.getExternalFormatText());
        attribute.put("isAnonymized", element.getAnonymized());
        results.put(attribute);

        return results;
    }

    private JSONObject getJsonDefSummary(HttpServletRequest request) throws JSONException {
        long jddid = ServletRequestUtils.getLongParameter(request, "jddid", -1L);
        JSONDataDefinition node = JSONDataDefinition.findById(jddid);
        JSONObject item = new JSONObject();

        item.put("name", node.getName());
        item.put("id", node.getId());
        switch (node.getDefinitionType()){
            case JSONDefinitionType.ID_KEY:
                item.put("isStartCustomer", node.isStartCustomer());
                break;
            default:
                item.put("isRepeating", node.isRepeating());
                item.put("isStartDataGroup", node.isStartDataGroup());
                item.put("dataGroup", node.getDisplayDataGroup() != null ? node.getDisplayDataGroup().getName() : StringUtils.EMPTY);
                break;
        }

        item.put("parentName", node.getParentDefinition() != null ? node.getParentDefinition().getName() : StringUtils.EMPTY);
        item.put("breakIndicator", node.getBreakIndicator());

        JSONDataElement dataElement = node.getJsonDataElement();
        if(dataElement != null){
            JSONArray variables = getVariables(new AbstractDataElement[]{dataElement});
            item.put("hasVariables", !variables.isEmpty());
            item.put("variables", variables);
        }

        return item;
    }

    private JSONObject getDataRecordSummary(HttpServletRequest request) throws JSONException {
        long drid = ServletRequestUtils.getLongParameter(request, "drid", -1L);
        DataRecord node = DataRecord.findById(drid);
        JSONObject item = new JSONObject();

        item.put("id", node.getId());
        item.put("position", node.getRecordPosition());
        item.put("recordIndicator", node.getRecordIndicator());
        item.put("enabled", getYesNoString(node.isEnabled()));
        item.put("startDataGroup", getYesNoString(node.getDataGroup() != null
                && node.getDataGroup().getStartDataRecord() != null
                && node.getDataGroup().getStartDataRecord().getId() == node.getId()));
        item.put("dataGroup", node.getDataGroup() != null ? node.getDataGroup().getName() : ApplicationUtil.getMessage("page.label.none") );

        JSONArray variables = getVariables(node.getDataElementArray());
        item.put("hasVariables", !variables.isEmpty());
        item.put("variables", variables);

        return item;
    }

    private JSONArray getVariables(AbstractDataElement[] dataElements) throws JSONException {
        JSONArray variables = new JSONArray();
        List<DataElementVariable> dataElementVariables = null;

        for (AbstractDataElement element : dataElements) {
            List<VariableDataElementMap> variableMap = VariableDataElementMap.findAllByDataElement(element);

            if (variableMap != null || element.isReferenced()) {

                if (dataElementVariables == null) {
                    dataElementVariables = DataElementVariable.findAll();
                }

                HashSet<VariableDataElementMap> variableElements = new HashSet<>();

                if (variableMap != null) {
                    variableElements.addAll(variableMap);
                }

                for (DataElementVariable variable : dataElementVariables) {

                    for (VariableDataElementMap variableDataElementMap: variable.getDataElementMap().values()) {
                        if (variableElements.contains(variableDataElementMap)) {
                            JSONObject value = new JSONObject();
                            value.put("id", variable.getId());
                            value.put("name", variable.getName() + " (" + element.getName() + ")");
                            variables.put(value);
                        }
                    }
                }
            }
        }

        return variables;
    }

    private RequestMethod getRequestType(HttpServletRequest request) {
        String requestType = ServletRequestUtils.getStringParameter(request, "method", StringUtils.EMPTY);

        switch (requestType.toLowerCase()) {
            case "treedata":
                return RequestMethod.TREEDATA;
            case "datarecord":
                return RequestMethod.DATARECORD;
            case "attributes":
                return RequestMethod.ATTRIBUTES;
            case "elements":
                return RequestMethod.ELEMENTS;
            case "xmldataelements":
                return RequestMethod.XML_DATAELEMENTS;
            case "xmltagsummary":
                return RequestMethod.XML_TAG_SUMMARY;
            case "jsondataelements":
                return RequestMethod.JSON_DATAELEMENTS;
            case "jsondefsummary":
                return RequestMethod.JSON_DEF_SUMMARY;
            case "datarecordsummary":
                return RequestMethod.DATARECORD_SUMMARY;
            default:
                return RequestMethod.UNKNOWN;
        }
    }

    private String getYesNoString(boolean value) {
        String result;

        if (value) {
            result = ApplicationUtil.getMessage("page.label.yes");
        } else {
            result = ApplicationUtil.getMessage("page.label.no");
        }

        return result;
    }
}
