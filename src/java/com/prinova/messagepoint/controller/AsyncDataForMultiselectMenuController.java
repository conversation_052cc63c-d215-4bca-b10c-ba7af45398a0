package com.prinova.messagepoint.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import ai.mpr.marcie.ingestion.input.VariablesTypeEnum;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.rationalizer.RationalizerSupportedCountriesEnum;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameComparator;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.HibernateUtil;

public class AsyncDataForMultiselectMenuController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncDataForMultiselectMenuController.class);

	public static final String PARAM_TYPE						= "type";
	public static final String PARAM_SELECTED_ITEM_IDS			= "selectedIds";
	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_RATIONALIZER_APP_ID		= "rationalizerApplicationId";
	public static final String PARAM_NUM_CAP					= "numCap";
	public static final String PARAM_SEARCH_STRING				= "sSearch";
	public static final String PARAM_ACTIVE_ONLY				= "activeOnly";
	public static final String PARAM_IS_CONNECTED_CONTEXT		= "isConnectedContext";
	public static final String PARAM_INCLUDE_ARCHIVED			= "includeArchived";
	
	public static final String VALUE_SMART_ASSETS				= "smart_assets";
	public static final String VALUE_IMAGE_LIBRARY				= "image_library";
	public static final String VALUE_TARGET_GROUP				= "target_group";
	public static final String VALUE_MESSAGE					= "message";
	public static final String VALUE_VARIANT					= "variant";
	public static final String VALUE_LOCAL_TEXT					= "local_text";
	public static final String VALUE_LOCAL_IMAGE				= "local_image";
	public static final String VALUE_INSERT						= "insert";
	public static final String VALUE_INSERT_SCHEDULE			= "insert_schedule";
	public static final String VALUE_TEXT_STYLE					= "text_style";
	public static final String VALUE_PARAGRAPH_STYLE			= "paragraph_style";
	public static final String VALUE_LIST_STYLE					= "list_style";
	public static final String VALUE_RATIONALIZER_METADATA_DEFS	= "rationalizer_metadata_definitions";
	public static final String VALUE_RATIONALIZER_INGESTION_VARIABLES_NOTATIONS = "variablesNotations";
	public static final String VALUE_RATIONALIZER_INGESTION_COUNTRIES = "phoneNumberSupportedCountries";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			String type	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
			
			if ( type == null ) {
				JSONObject returnObj = new JSONObject();
				returnObj.put("error", true);
				returnObj.put("message", "Error - unspecified request menu type");
				out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
			} else {
				
				if ( type.equalsIgnoreCase(VALUE_SMART_ASSETS) ) {
					out.write( getCombinedEmbeddedContentDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_IMAGE_LIBRARY) ) {
					out.write( getContentLibraryDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_TARGET_GROUP) ) {
					out.write( getTargetGroupDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_MESSAGE) ) {
					out.write( getMessageDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_VARIANT) ) {
					out.write( getSelectionDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_LOCAL_TEXT) ) {
					out.write( getLocalTextDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_LOCAL_IMAGE) ) {
					out.write( getLocalImageDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_INSERT) ) {
					out.write( getInsertDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_INSERT_SCHEDULE) ) {
					out.write( getInsertScheduleDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_TEXT_STYLE) ) {
					out.write( getTextStyleDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_PARAGRAPH_STYLE) ) {
					out.write( getParagraphStyleDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_LIST_STYLE) ) {
					out.write( getListStyleDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_RATIONALIZER_METADATA_DEFS) ) {
					out.write( getRationalizerMetadataDefinitionsDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_RATIONALIZER_INGESTION_VARIABLES_NOTATIONS) ) {
					out.write( getRationalizerIngestionVariablesNotationResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_RATIONALIZER_INGESTION_COUNTRIES) ) {
			    	out.write( getRationalizerIngestionSupportedCountriesResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
			    } else {
					JSONObject returnObj = new JSONObject();
					returnObj.put("error", true);
					returnObj.put("message", "Error - invalide request menu type");
					out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				}
			}
			
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for multiselect menu items: "+e.getMessage(),e);
		}

		return null;
	}

	// SMART TEXT, SMART CANVAS, SMART TABLES
	private String getCombinedEmbeddedContentDataResponseJSON( HttpServletRequest request ) {
		JSONObject returnObj = new JSONObject();

		try {
			Document documentContext = null;
			if ( getDocumentId(request) > 0 )
				documentContext = Document.findById( getDocumentId(request) );
			
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			// TODO restrict the select
			List<ContentObject> selectedEmbeddedContent = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
			List<ContentObject> items = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documentContext, null, null, false, getIsConnectedContext(request), -1,
															getActiveOnly(request), getIncludeArchived(request), getSearchStr(request), getNumCap(request));
			
			for ( ContentObject currentSelectedItem: selectedEmbeddedContent )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ContentObject currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedEmbeddedContent.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				List<ContentObject> allItems = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documentContext, null, null, false, getIsConnectedContext(request), -1,
						getActiveOnly(request), getIncludeArchived(request), getSearchStr(request), -1);
				returnObj.put("full_list_count", allItems.size());
				returnObj.put("display_list_count", items.size());
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getContentLibraryDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			Document documentContext = null;
			if ( getDocumentId(request) > 0 )
				documentContext = Document.findById( getDocumentId(request) );
			
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			// TODO restrict the select
			List<ContentObject> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
			List<ContentObject> items = ContentObject.findAllImagesByAdvancedQuery(documentContext, getIsConnectedContext(request), getActiveOnly(request),
																				getSearchStr(request), getNumCap(request));
			
			for ( ContentObject currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ContentObject currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				List<ContentObject> allItems = ContentObject.findAllImagesByAdvancedQuery(documentContext, getIsConnectedContext(request), getActiveOnly(request),
						getSearchStr(request), -1);
				returnObj.put("full_list_count", allItems.size());
				returnObj.put("display_list_count", items.size());
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getTargetGroupDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<TargetGroup> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(TargetGroup.class, critList);
			List<TargetGroup> items = TargetGroup.findAllByAdvancedQuery(null, false, getSearchStr(request), getNumCap(request));
			
			for ( TargetGroup currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( TargetGroup currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getMessageDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			// TODO restrict the select
			List<ContentObject> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
			List<ContentObject> items = ContentObject.findAllByAdvancedQuery(Document.findById(getDocumentId(request)), null, true,
					-1, -1, getSearchStr(request), getNumCap(request));
			
			for ( ContentObject currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ContentObject currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getSelectionDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<TouchpointSelection> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(TouchpointSelection.class, critList);
			List<TouchpointSelection> items = TouchpointSelection.findAllByAdvancedQuery(Document.findById(getDocumentId(request)), false, getSearchStr(request), getNumCap(request));
			
			for ( TouchpointSelection currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( TouchpointSelection currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getNameWithContext());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				int allItemsCount = TouchpointSelection.findAllByAdvancedQuery(Document.findById(getDocumentId(request)), false, "", -1).size();
				returnObj.put("full_list_count", allItemsCount);
				returnObj.put("display_list_count", items.size());
			}
			
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}	
	
	private String getLocalTextDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			// TODO restrict the select
			List<ContentObject> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
			List<ContentObject> items = ContentObject.findAllLocalAssetByAdvancedQuery(Document.findById(getDocumentId(request)), null, true,
					ContentType.TEXT, getSearchStr(request), getNumCap(request), true);
			
			for ( ContentObject currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ContentObject currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				int allItemsCount = ContentObject.findAllLocalAssetByAdvancedQuery(Document.findById(getDocumentId(request)), null, true,
						ContentType.TEXT, getSearchStr(request), 2000, true).size();
				returnObj.put("full_list_count", allItemsCount);
				returnObj.put("display_list_count", items.size());
			}

		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getLocalImageDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			// TODO restrict the select
			List<ContentObject> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ContentObject.class, critList);
			List<ContentObject> items = ContentObject.findAllLocalAssetByAdvancedQuery(Document.findById(getDocumentId(request)), null, true,
					ContentType.GRAPHIC, getSearchStr(request), getNumCap(request), true);
			
			for ( ContentObject currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ContentObject currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, currentItem.getMetatags());
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getInsertDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<Insert> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(Insert.class, critList);
			List<Insert> items = Insert.findAllByAdvancedQuery(null, getSearchStr(request), getNumCap(request));
			
			for ( Insert currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( Insert currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getInsertScheduleDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<InsertSchedule> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(InsertSchedule.class, critList);
			List<InsertSchedule> items = InsertSchedule.findAllByAdvancedQuery(Document.findById(getDocumentId(request)), getSearchStr(request), getNumCap(request));
			
			for ( InsertSchedule currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( InsertSchedule currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getTextStyleDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<TextStyle> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(TextStyle.class, critList);
			
			List<TextStyle> items = TextStyle.findAllByAdvancedQuery(getNumCap(request), getSearchStr(request), false );
			
			for ( TextStyle currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( TextStyle currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				int allItemsCount = TextStyle.findCountByAdvancedQuery( false );
				returnObj.put("full_list_count", allItemsCount);
				returnObj.put("display_list_count", items.size());
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getParagraphStyleDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<ParagraphStyle> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ParagraphStyle.class, critList);
			List<ParagraphStyle> items = ParagraphStyle.findAllByAdvancedQuery(getNumCap(request), getSearchStr(request));
			
			for ( ParagraphStyle currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ParagraphStyle currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				returnObj.put("full_list_count", ParagraphStyle.findCountByAdvancedQuery());
				returnObj.put("display_list_count", items.size());
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getListStyleDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<ListStyle> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(ListStyle.class, critList);
			List<ListStyle> items = ListStyle.findAllByAdvancedQuery(getNumCap(request), getSearchStr(request));
			
			for ( ListStyle currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( ListStyle currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);
			if( getNumCap(request) > 0 ) {
				returnObj.put("full_list_count", ListStyle.findCountByAdvancedQuery());
				returnObj.put("display_list_count", items.size());
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getRationalizerMetadataDefinitionsDataResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();

		try {
			RationalizerApplication rationalizerApplication = RationalizerApplication.findById( getRationalizerApplicationId(request) );
			String searchStr = getSearchStr(request);

			List<MessagepointCriterion> critList = new ArrayList<>();
			critList.add(MessagepointRestrictions.in("id", getSelectedIds(request)));
			List<MetadataFormItemDefinition> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(MetadataFormItemDefinition.class, critList);
			
			List<MetadataFormItemDefinition> items = new ArrayList<>();
			if ( rationalizerApplication.getParsedDocumentFormDefinition() != null )
				for ( MetadataFormItemDefinition currentItem: rationalizerApplication.getParsedDocumentFormDefinition().getFormItemDefinitions() )
					if (searchStr == null || searchStr.isEmpty() || currentItem.getName().toLowerCase().indexOf(searchStr.toLowerCase()) != -1 )
							items.add( currentItem );
			if ( rationalizerApplication.getParsedContentFormDefinition() != null )
				for ( MetadataFormItemDefinition currentItem: rationalizerApplication.getParsedContentFormDefinition().getFormItemDefinitions() )
					if (searchStr == null || searchStr.isEmpty() || currentItem.getName().toLowerCase().indexOf(searchStr.toLowerCase()) != -1 )
							items.add( currentItem );
			Collections.sort(items, new IdentifiableMessagepointModelNameComparator());
			
			for ( MetadataFormItemDefinition currentSelectedItem: selectedItems )
				if ( !items.contains(currentSelectedItem) )
					items.add(currentSelectedItem);
			
			JSONArray itemsArray = new JSONArray();
			for ( MetadataFormItemDefinition currentItem: items ) {
				JSONObject itemObj = new JSONObject();
				
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				
				itemsArray.put(itemObj);
			}
			
			returnObj.put("menu_items", itemsArray);

		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}

		return returnObj.toString();
	}

	private List<Long> getSelectedIds( HttpServletRequest request ) {
		String selectedIdStr = ServletRequestUtils.getStringParameter(request, PARAM_SELECTED_ITEM_IDS, null);
		List<Long> ids = new ArrayList<>();
		
		if ( selectedIdStr != null ) {
			String[] idArr = selectedIdStr.split(",");
			for ( String currentId : idArr ) {
				ids.add(Long.valueOf(currentId));
			}
		}
		if (ids.isEmpty())
			ids.add(0L);
		
		return ids;
	}

	private String getRationalizerIngestionSupportedCountriesResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try {
			List<Long> selectedIds = getSelectedIds(request);
			List<RationalizerSupportedCountriesEnum> selectedItems = new ArrayList<>();
			for (long selectedId : selectedIds) {
				selectedItems.add(RationalizerSupportedCountriesEnum.fromId((int)selectedId));
			}
			JSONArray itemsArray = new JSONArray();
			for ( RationalizerSupportedCountriesEnum currentItem: RationalizerSupportedCountriesEnum.values() ) {
				JSONObject itemObj = new JSONObject();
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, ApplicationUtil.getMessage(currentItem.getDisplayName()));
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				itemsArray.put(itemObj);
			}
			returnObj.put("menu_items", itemsArray);
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}
		return returnObj.toString();
	}

	private String getRationalizerIngestionVariablesNotationResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try {
			List<Long> selectedIds = getSelectedIds(request);
			List<VariablesTypeEnum> selectedItems = new ArrayList<>();
			 for (long selectedId : selectedIds) {
				 selectedItems.add(VariablesTypeEnum.fromId((int)selectedId));
			 }
			JSONArray itemsArray = new JSONArray();
			for ( VariablesTypeEnum currentItem: VariablesTypeEnum.values() ) {
				JSONObject itemObj = new JSONObject();
				itemObj.put("id"		, currentItem.getId());
				itemObj.put("name"		, currentItem.getName());
				itemObj.put("tags"		, "");
				itemObj.put("selected"	, selectedItems.contains(currentItem));
				itemsArray.put(itemObj);
			}
			returnObj.put("menu_items", itemsArray);
		} catch (JSONException e) {
			log.error("Error: Unable to build multiselect menu items response: ", e );
		}
		return returnObj.toString();
	}

	private int getNumCap( HttpServletRequest request ) {
		return ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
	}

	private String getSearchStr( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_STRING, null);
	}
	
	private long getDocumentId(HttpServletRequest request){
		return ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1);
	}
	
	private Boolean getActiveOnly(HttpServletRequest request){
		return ServletRequestUtils.getBooleanParameter(request, PARAM_ACTIVE_ONLY, false);
	}
	
	private Boolean getIsConnectedContext(HttpServletRequest request){
		return ServletRequestUtils.getBooleanParameter(request, PARAM_IS_CONNECTED_CONTEXT, false);
	}

	private Boolean getIncludeArchived(HttpServletRequest request){
		return ServletRequestUtils.getBooleanParameter(request, PARAM_INCLUDE_ARCHIVED, false);
	}
	
	private long getRationalizerApplicationId(HttpServletRequest request){
		return ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APP_ID, -1);
	}

}