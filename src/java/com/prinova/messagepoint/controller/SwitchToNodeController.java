package com.prinova.messagepoint.controller;

import java.io.Serializable;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.admin.ApplicationLocale;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninService;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninServiceResponse;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.security.sso.SSOFilter;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class SwitchToNodeController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(SwitchToNodeController.class);
	
	public static final String REQ_PARM_NODE_ID				= "gd";
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		Command command = new Command();
		String guid = ServletRequestUtils.getStringParameter(request, REQ_PARM_NODE_ID, "");
		Node node = Node.findByGuid(guid);
		User user = UserUtil.getPrincipalUser();
		
		if (node != null)
		{
			command.setNodeId(node.getId());
			command.setNodeName(node.getName());
			command.setBranchName(node.getBranch().getName());
			command.setSchemaName(node.getSchemaName());
		}
		
		if (user != null)
		{
			command.setUsername(user.getUsername());
			command.setSSOSessionType(user.getSSOSessionType());
			command.setSSORequestToken(user.getSSORequestToken());
		}
		
    	return command;
    }
    
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		Command c = (Command)command;
		
		String username = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
		String password = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_PASSWORD_KEY);
		String redirectUrl = null;

		User currentUser = UserUtil.getPrincipalUser();
		String switchFromNodeGuid = Node.getCurrentNode().getGuid();

		if (c.isSSOExternalType())
		{
			if (currentUser != null)
			{
				username = currentUser.getUsername() + "#" + SSOFilter.PING_SSO_IDENTIFIER;
				password = SSOFilter.PING_SSO_USER_PASSWORD;
				
				User homeDCSUser = currentUser.getHomeDcsUser();
				Node targetNode = Node.findBySchema(c.getSchemaName());
				// switch DB session to requested schema
				SessionHolder localSessionHolder = HibernateUtil.getManager().openTemporarySession(c.getSchemaName());
				User user = User.findByIdPUserGuid(currentUser.getIdPUserGuid(), currentUser.getIdPIdentifier());
				if(homeDCSUser != null && user == null && (homeDCSUser.isPodAdminUser() || homeDCSUser.isDomainAdminUser())){
					user = UserUtil.CreateInstnaceUserForPodAdmin(homeDCSUser, targetNode);
				}
				if (user != null)
				{
					username = username + "#" + user.getId();
				}
				
				HibernateUtil.getManager().restoreSession(localSessionHolder);
			}
		}

		if (username != null) {
	        if (password == null) {
	            password = "";
	        }

			username = username.trim();

	        if ((currentUser != null) && !currentUser.isSSOUser()) {
				Branch homeDCSDomain = currentUser.getHomeDcsDomain();
				if (homeDCSDomain != null && !homeDCSDomain.getName().equals(c.getBranchName())) {
					username = username + "#" + homeDCSDomain.getName();
				}
			}

			SessionHolder mainSessionHolder = null;
			try {

				Object details = null;
				HttpSession httpSession = request.getSession();
				SecurityContext context = (SecurityContext) httpSession.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
				if(context != null)
				{
					Authentication currentAuth = context.getAuthentication();
					if (currentAuth != null) 
					{
						// get original details of sign in (we will keep them for new authentication)
						details = currentAuth.getDetails();
					}
				}
					
				// get authentication manager
				ProviderManager authenticationManager = ApplicationUtil.getBean("authenticationManager", ProviderManager.class);
		        
				// create a new authentication token
				UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
	
				// set details (IP address and session) the same as the current authentication token
		        authRequest.setDetails(details);

		        // switch DB session to requested schema
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(c.getSchemaName());

		        // try to authenticate
		        Authentication authenticationResult = authenticationManager.authenticate( authRequest );

		        if (authenticationResult != null && authenticationResult.isAuthenticated())
		        {
		        	// the authentication was successful
					Object authorizedUser = authenticationResult.getPrincipal();
					User user = null;
					if (authorizedUser instanceof User) {
						user = (User) authorizedUser;
						if (c.getSSOSessionType() > 0)
						{
							// This is SSO session
							user.setSSOSessionType(c.getSSOSessionType());
							user.setSSORequestToken(c.getSSORequestToken());
						}
						User localDcsUser = user.getLocalDcsUser();
						if (localDcsUser != null && localDcsUser.getDefaultNodeId() != user.getDefaultNodeId())
						{
							user.setDefaultNodeId(localDcsUser.getDefaultNodeId());
							user.save();
						}
						user.setLastLogin(DateUtil.now());
						user.save();
					}
		        	
					// set new successful authentication 
		        	SecurityContextHolder.getContext().setAuthentication( authenticationResult );
		        	
		        	// Load the user locale setting
		    		request.getSession().setAttribute(ApplicationLocale.SESSION_ATTR_NAME, user.getAppLocaleLangCode());

		    		// Get old CSRF session key
		    		String oldCsrfSessionKey = (String) request.getSession().getAttribute(WebAppSecurity.CSRF_SESSION_KEY);

					Object csrfKeyHistory = request.getSession().getAttribute(WebAppSecurity.CSRF_SESSION_HISTORY_KEY);

					if (csrfKeyHistory == null) {
						csrfKeyHistory = new HashMap<String, Long>();
						request.getSession().setAttribute(WebAppSecurity.CSRF_SESSION_HISTORY_KEY, csrfKeyHistory);
					}

					((HashMap<String, String>)csrfKeyHistory).put(oldCsrfSessionKey, switchFromNodeGuid);
					request.getSession().setAttribute(WebAppSecurity.CSRF_SESSION_HISTORY_KEY, csrfKeyHistory);

		    		// Change the CSRF token
					request.getSession().setAttribute(WebAppSecurity.CSRF_SESSION_KEY, MessagepointStartFilter.generateCSRFToken());
		    		
		    		// Validate the password expiration
		    		if(user != null && user.checkIsPasswordExpired())
		    		{
		    			redirectUrl = ApplicationUtil.addToken(MessagepointAuthenticationSuccessHandler.passwordChangeRedirect, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
		    		}
		    		// Audit (Authentication success)
		    		AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, user.getUsername(), user.getId(), AuditActionType.ID_AUTHENTICATION_SUCCESS, null);

				}
				
			}
			catch(AuthenticationException exception)
			{
				String error = exception.getMessage();
				if (exception instanceof BadCredentialsException) {
					String errorMsgKey = processInvalidSigninAttempt(request);
					if (c.getSSOSessionType() == User.SSO_TYPE_PING)
					{
						errorMsgKey = "code.text.accessdenied";
					}
					error += ": ";
					error += ApplicationUtil.getMessage(errorMsgKey);
				}
				if (exception instanceof LockedException) {
					error += ";";
				}
				log.info("Authentication failed; " + error + " Username: " + username + " Schema: " + c.getSchemaName());
				// Audit (Authentication failure)
				AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, username, null, AuditActionType.ID_AUTHENTICATION_FAILURE, null);		
				errors.reject(null, error);				
				return super.showForm(request, response, errors);
			}
			finally
			{
				// set DB schema back to the original one the finish this HTTP session 
				if (mainSessionHolder != null)
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}
		
		if (redirectUrl != null)
			return new ModelAndView(new RedirectView( ApplicationUtil.getWebRoot()+redirectUrl));
		else
			return new ModelAndView(new RedirectView( ApplicationUtil.getWebRoot()+getSuccessView()));
	}

	private String processInvalidSigninAttempt(HttpServletRequest request) {
		ServiceExecutionContext context = ProcessInvalidSigninService.createContext(request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY));

		Service invalidSigninService = MessagepointServiceFactory.getInstance().lookupService(ProcessInvalidSigninService.SERVICE_NAME, ProcessInvalidSigninService.class);
		invalidSigninService.execute(context);
		if (context.getResponse().isSuccessful()) {
			ProcessInvalidSigninServiceResponse response = (ProcessInvalidSigninServiceResponse) context.getResponse();
			return response.getFailMsgKey();
		} else {
			return null;
		}

	}
    
	public static class Command implements Serializable{
		private static final long serialVersionUID = 2404641014165018656L;

		private long nodeId;
		private long userId;
		private String username = "";
		private String branchName = "";
		private String nodeName = "";
		private String schemaName = null;
		private int ssoSessionType = 0;
		private String ssoRequestToken = null;
		private boolean ssoExternalType = false;
		
		public long getNodeId() {
			return nodeId;
		}
		public void setNodeId(long nodeId) {
			this.nodeId = nodeId;
		}
		public long getUserId() {
			return userId;
		}
		public void setUserId(long userId) {
			this.userId = userId;
		}
		public String getUsername() {
			return username;
		}
		public void setUsername(String value) {
			username = value;
		}
		public String getBranchName() {
			return branchName;
		}
		public void setBranchName(String value) {
			branchName = value;
		}
		public String getNodeName() {
			return nodeName;
		}
		public void setNodeName(String value) {
			nodeName = value;
		}
		public String getSchemaName() {
			return schemaName;
		}
		public void setSchemaName(String value) {
			if (value != null)
				schemaName = value;
			else
				schemaName = null;
		}
	    public int getSSOSessionType()
	    {
	        return ssoSessionType;
	    }
	    public void setSSOSessionType(int value)
	    {
	    	ssoSessionType = value;
	    	ssoExternalType = (value == User.SSO_TYPE_PING || value == User.SSO_TYPE_PING_PROXY_PRODUCTION || value == User.SSO_TYPE_PING_PROXY_TRANSITION);
	    }
	    public String getSSORequestToken()
	    {
	        return this.ssoRequestToken;
	    }
	    public void setSSORequestToken(String ssoRequestToken)
	    {
	        this.ssoRequestToken = ssoRequestToken;
	    }
	    public boolean isSSOExternalType()
	    {
	        return this.ssoExternalType;
	    }
	    public void setSSOExternalType(boolean ssoExternalType)
	    {
	        this.ssoExternalType = ssoExternalType;
	    }
	}
}
