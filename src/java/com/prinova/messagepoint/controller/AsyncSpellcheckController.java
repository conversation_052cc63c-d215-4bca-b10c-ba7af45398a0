package com.prinova.messagepoint.controller;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.SpellCheckerInstance;
import com.prinova.messagepoint.util.UserUtil;
import com.swabunga.spell.engine.Word;

public class AsyncSpellcheckController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncSpellcheckController.class);

	public static final String METHOD_CHECK_WORDS 		= "checkWords";
	public static final String METHOD_SUGGESTIONS 		= "getSuggestions";
	public static final String METHOD_SPELL_CHECK			= "spellCheck";
	public static final String METHOD_ADD_TO_DICTIONARY				= "addToDictionary";
	
	public static final String PARAM_METHOD 						= "method";
	public static final String PARAM_ID 									= "id";
	public static final String PARAM_RESULTS 						= "results";
	public static final String PARAM_PARAMS 						= "params";
	public static final String PARAM_TEXT								= "text";
	public static final String PARAM_WORD							= "word";
	public static final String PARAM_LANG								= "lang";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        PrintWriter out = response.getWriter();
        
        try {
	        	String method 	= ServletRequestUtils.getStringParameter(request, PARAM_METHOD,  "");
	        	String text 	= ServletRequestUtils.getStringParameter(request, PARAM_TEXT,  "");
	        	String word 	= ServletRequestUtils.getStringParameter(request, PARAM_WORD,  "");
	        	String langCode	= ServletRequestUtils.getStringParameter(request, PARAM_LANG,  "");
	        	
	        	long userId = UserUtil.getPrincipalUserId();
	        	
	        	boolean isDictionaryAvailable = SpellCheckerInstance.isDictionaryAvailable(langCode, userId);
	        	if(isDictionaryAvailable){
		        	if ( method.equalsIgnoreCase(METHOD_SPELL_CHECK) ) {
		            JSONObject responseJSON = new JSONObject();
		        		JSONObject wordObjs = new JSONObject();
		            	for (String currentWord: SpellCheckerInstance.getBadWords(text, langCode, userId)) {
		        			List<String> suggestionArray = new ArrayList<>();
		                List<Word> suggestions = SpellCheckerInstance.getSuggestionsList(currentWord, langCode, userId);
		                for (Iterator<Word> iter = suggestions.iterator(); iter.hasNext();) {
			                	Word currentSuggestion = (Word) iter.next();
			                	suggestionArray.add(currentSuggestion.getWord());
		                }
		                
		                wordObjs.put(currentWord, suggestionArray);
		        		}
		            	
		            responseJSON.put("words", wordObjs);
		            responseJSON.put("dictionary", true);
		            responseJSON.write(out);
		    		    out.flush();
		        } else if(method.equalsIgnoreCase(METHOD_ADD_TO_DICTIONARY)){
		        		boolean success = SpellCheckerInstance.addWord(word, langCode, userId);
		        		if(success){
		        			out.println("{\"success\":true}");
		        			out.flush();
		        		}else{
		        			String errorMsg = ApplicationUtil.getMessage("error.message.dictionary.user.not.available");
				    		out.println("{\"error\":\""+ errorMsg + "\"}");
						out.flush();
		        		}
		        }else {
		            out.println("{\"error\":\"Invalid request, method not yet implemented\"}");
		    		    out.flush();
	            }
		    	}else{
		    		String errorMsg = ApplicationUtil.getMessage("error.message.dictionary.not.available");
		    		out.println("{\"error\":\""+ errorMsg + "\"}");
				out.flush();
		    	}
        } catch (JSONException je) {
        		log.error(je.getMessage(),je);
            out.println("JSON Error: Unable to generate return object");
		    out.flush();
        } 
        
		return null;
	}

}