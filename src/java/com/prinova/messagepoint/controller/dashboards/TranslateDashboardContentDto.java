package com.prinova.messagepoint.controller.dashboards;

public class TranslateDashboardContentDto{
    private String translatedContentElasticId;

    private String translatedContentGuid;

    private String translateErrorMessage;

    private String translatedLangCode;
    private String defaultContentGuid;

    public String getTranslatedContentElasticId() {
        return translatedContentElasticId;
    }

    public void setTranslatedContentElasticId(String translatedContentElasticId) {
        this.translatedContentElasticId = translatedContentElasticId;
    }

    public String getTranslatedContentGuid() {
        return translatedContentGuid;
    }

    public void setTranslatedContentGuid(String translatedContentGuid) {
        this.translatedContentGuid = translatedContentGuid;
    }

    public String getTranslateErrorMessage() {
        return translateErrorMessage;
    }

    public void setTranslateErrorMessage(String translateErrorMessage) {
        this.translateErrorMessage = translateErrorMessage;
    }

    public String getTranslatedLangCode() {
        return translatedLangCode;
    }

    public void setTranslatedLangCode(String translatedLangCode) {
        this.translatedLangCode = translatedLangCode;
    }

    public String getDefaultContentGuid() {
        return defaultContentGuid;
    }

    public void setDefaultContentGuid(String defaultContentGuid) {
        this.defaultContentGuid = defaultContentGuid;
    }
}
