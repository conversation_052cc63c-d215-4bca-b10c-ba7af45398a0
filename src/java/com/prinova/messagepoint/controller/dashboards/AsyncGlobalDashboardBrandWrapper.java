package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.constructEnabledBrandPropertiesList;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;

public class AsyncGlobalDashboardBrandWrapper extends AsyncAbstractListWrapper {

    private Map<String, Integer> brandPerPageMap = new LinkedHashMap<>();

    public AsyncGlobalDashboardBrandWrapper(int pageSize, int pageIndex, long selectedItemId) {
        this.buildItemsList(pageSize, pageIndex, selectedItemId);
    }

    private void buildItemsList(int pageSize, int pageIndex, long selectedItemId) {
        int brandCountsMapSize = 0;
        BrandProfile brandProfile = BrandProfile.findPrimaryProfile();
        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        List<String> enabledBrandProfileProperties = constructEnabledBrandPropertiesList(brandProfile);
        Map<String, Integer> brandViolationsCountsMap = messagepointElasticSearchHandler.computeBrandViolationsCountsWithMetadataFilter(enabledBrandProfileProperties, filterMap);

        if (CollectionUtils.isEmpty(enabledBrandProfileProperties) || MapUtils.isEmpty(brandViolationsCountsMap)) {
            brandPerPageMap = new HashMap<>();
            return;
        }

        Map<String, Integer> resultMap = computeResultsMapForEnabledItems(enabledBrandProfileProperties, brandViolationsCountsMap);
        resultMap = sortInReverseOrderByCounts(resultMap);

        if (!MapUtils.isEmpty(resultMap)) {
            brandPerPageMap = resultMap.entrySet().stream().skip((pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                    (oldValue, newValue) -> oldValue, LinkedHashMap::new));
            brandCountsMapSize = resultMap.size();
        }

        super.setiTotalRecords(brandCountsMapSize);
        super.setiTotalDisplayRecords(brandCountsMapSize);
    }

    @Override
    public void init() {
        List<AsyncGlobalDashboardBrandVO> aaData = new ArrayList<>();

        if (MapUtils.isNotEmpty(brandPerPageMap)) {
            brandPerPageMap.forEach((key, value) -> {
                AsyncGlobalDashboardBrandVO vo = new AsyncGlobalDashboardBrandVO();
                vo.setDescription(key);
                vo.setCount(value);
                vo.setDisplayMode(getDisplayMode());

                // For drill-down
                vo.setDT_RowId(BrandProfileEnum.fromValue(key).getId());

                aaData.add(vo);
            });
        }
        super.setAaData(aaData);
    }

    private Map<String, Integer> computeResultsMapForEnabledItems(List<String> enabledBrandProperties, Map<String, Integer> elasticSearchResult) {
        if (CollectionUtils.isEmpty(enabledBrandProperties)) {
            return null;
        }

        Map<String, Integer> result = new HashMap<>();
        enabledBrandProperties.forEach(crtProperty -> result.put(crtProperty, elasticSearchResult.get(crtProperty) != null ? elasticSearchResult.get(crtProperty) : 0));

        return result;
    }

    private LinkedHashMap<String, Integer> sortInReverseOrderByCounts(Map<String, Integer> collectedDataMap) {
        return collectedDataMap.entrySet().stream()
                .sorted(Collections.reverseOrder(Map.Entry.comparingByValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }
}
