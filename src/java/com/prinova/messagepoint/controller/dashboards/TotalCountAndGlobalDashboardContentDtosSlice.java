package com.prinova.messagepoint.controller.dashboards;

import java.util.Collection;

public class TotalCountAndGlobalDashboardContentDtosSlice {

    private Integer totalCount;
    private Collection<GlobalDashboardContentDto> globalDashboardContentDtosSlice;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Collection<GlobalDashboardContentDto> getGlobalDashboardContentDtosSlice() {
        return globalDashboardContentDtosSlice;
    }

    public void setGlobalDashboardContentDtosSlice(Collection<GlobalDashboardContentDto> globalDashboardContentDtosSlice) {
        this.globalDashboardContentDtosSlice = globalDashboardContentDtosSlice;
    }
}
