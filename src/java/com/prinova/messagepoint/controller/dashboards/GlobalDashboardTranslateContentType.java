package com.prinova.messagepoint.controller.dashboards;

public class GlobalDashboardTranslateContentType{

    private int id;
    private String name;
    private String displayName;

    GlobalDashboardTranslateContentType(int id, String shortName, String displayName) {
        this.id = id;
        this.name = shortName;
        this.displayName = displayName;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDisplayName() {
        return displayName;
    }
}
