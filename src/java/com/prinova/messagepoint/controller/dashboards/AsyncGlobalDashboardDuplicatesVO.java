package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;

import java.text.DecimalFormat;

public class AsyncGlobalDashboardDuplicatesVO extends AsyncListVO {
    private static final DecimalFormat FORMATTER = new DecimalFormat("###,###");
    private GlobalDashboardVOModel model;
    private int count;

    public AsyncGlobalDashboardDuplicatesVO() {
        super();
    }

    public String getName() {
        return computeNameTag();
    }

    public String getCountDisplayString() {
        return "<span style=\"float: right;\">" + FORMATTER.format(count) + "</span";
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setModel(GlobalDashboardVOModel model) {
        this.model = model;
    }

    public String getBinding() {
        return "<input id='listItemCheck_" + this.model.getContentId() + "' type='checkbox' value='" + this.model.getContentId() +
                "' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
    }

    private String computeNameTag() {
        Document touchpoint = null;


        ContentObject contentObject = ContentObject.findById(model.getObjId());

        touchpoint = contentObject.getFirstDocumentDelivery();

        StringBuilder cardHTML = new StringBuilder();
        // Content
        if (model.getText() != null) {
            cardHTML.append("<div id=\"resultContent_").append(model.getContentId()).append("\" class=\"resultContentContainer contentContainer position-relative\" style=\"white-space: normal;\">");
            if (touchpoint != null) {
                cardHTML.append(Content.findById(model.getContentId()).getContent(touchpoint));
            } else {
                cardHTML.append(Content.findById(model.getContentId()).getContent());
            }
            cardHTML.append("</div>");


            return cardHTML.toString();
        }
        return cardHTML.toString();

    }
}
