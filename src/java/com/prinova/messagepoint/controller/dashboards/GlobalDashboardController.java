package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.ContentIntelligenceEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentBulkIndexService;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GlobalDashboardController extends MessagepointController {
    private static final String REQ_PARAM_ACTION = "action";
    private static final String REG_PARAM_SELECTED_ITEM_ID = "selectedItemId";
    private static final int ACTION_NAVIGATE_TO_DUPLICATES = 1;
    private static final int ACTION_NAVIGATE_TO_READING_COMPREHENSION = 3;
    private static final int ACTION_NAVIGATE_TO_SENTIMENT = 4;
    private static final int ACTION_NAVIGATE_TO_BRAND = 5;
    private static final int ACTION_MARCIE_RECYNC = 6;
    private static final int ACTION_NAVIGATE_TO_TRANSLATE_ACCURACY = 9;


    protected Map<String, Object> referenceData(HttpServletRequest request) {
        Map<String, Object> referenceData = new HashMap<>();
        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);
        referenceData.put("marcieIndexExist", messagepointElasticSearchHandler.indexExists());
        List<StatusPollingBackgroundTask> activeTasks = StatusPollingBackgroundTask.findAllActiveBackgroundTask();
        boolean reindexRunning = false;
        for (StatusPollingBackgroundTask activeTask : activeTasks) {
            if (activeTask.getType() == StatusPollingBackgroundTask.TYPE_PUSH_CONTENT_TO_ELASTIC) {
                reindexRunning = true;
                break;
            }
        }
        if (reindexRunning) {
            referenceData.put("isMarcieResyncRunning",true);
        } else {
            referenceData.put("isMarcieResyncRunning",false);
        }

        referenceData.put("isTranslateLicensed", MessagepointLicenceManager.getInstance().isLicencedForAITranslate());
        return referenceData;
    }
    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_CONTENT_INTELLIGENCE, null, null, AuditActionType.ID_GLOBAL_DASHBOARD, null);

        return super.showForm(request, response, errors);
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.GlobalDashboard);
        analyticsEvent.send();
        return new GlobalDashboardWrapper();
    }

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
        switch (action) {
            case (ACTION_NAVIGATE_TO_DUPLICATES): {
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dashboards/global_dashboard_duplicates.form"), getSuccessViewParams(request));
            }
            case (ACTION_NAVIGATE_TO_READING_COMPREHENSION): {
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dashboards/global_dashboard_reading.form"), getSuccessViewParams(request));
            }
            case (ACTION_NAVIGATE_TO_SENTIMENT): {
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dashboards/global_dashboard_sentiment.form"), getSuccessViewParams(request));
            }
            case (ACTION_NAVIGATE_TO_TRANSLATE_ACCURACY): {
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dashboards/global_dashboard_translation.form"), getSuccessViewTranslateParams(request));
            }
            case (ACTION_NAVIGATE_TO_BRAND): {
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dashboards/global_dashboard_brand.form"), getSuccessViewParams(request));
            }
            case (ACTION_MARCIE_RECYNC): {
                SystemPropertyManager.getInstance().updateProperty(SystemPropertyKeys.ContentIntelligence.KEY_ContentCompareEnabled, "true");
                AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_CONTENT_INTELLIGENCE, null, null, AuditActionType.ID_GLOBAL_DASHBOARD, null);

                ElasticsearchContentBulkIndexService task = new ElasticsearchContentBulkIndexService(UserUtil.getPrincipalUser());
                MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
                return new ModelAndView(new RedirectView(request.getContextPath() + "/dashboards/global_dashboard.form"), getSuccessViewParams(request));
            }
        }
        return null;
    }

    private Map<String, Object> getSuccessViewParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        long actionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ACTION, -1L);
        if (actionId != -1L) {
            params.put(REQ_PARAM_ACTION, actionId);
        }
        long selectedItemId = ServletRequestUtils.getLongParameter(request, REG_PARAM_SELECTED_ITEM_ID, -1L);
        if (actionId != -1L) {
            params.put(REG_PARAM_SELECTED_ITEM_ID, selectedItemId);
        }

        return params;
    }

    private Map<String, Object> getSuccessViewTranslateParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        long actionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ACTION, -1L);
        if (actionId != -1L) {
            params.put(REQ_PARAM_ACTION, actionId);
        }
        long selectedItemId = ServletRequestUtils.getLongParameter(request, REG_PARAM_SELECTED_ITEM_ID, -1L);
        if (actionId != -1L) {
            params.put(REG_PARAM_SELECTED_ITEM_ID, selectedItemId);
        }
        String selectedLangId = ServletRequestUtils.getStringParameter(request, "selectedLangId", "");
        if (StringUtils.isNotEmpty(selectedLangId)) {
            params.put("selectedLangId", selectedLangId);
        }
        String selectedStatusId = ServletRequestUtils.getStringParameter(request, "selectedStatusId", "");
        if (StringUtils.isNotEmpty(selectedStatusId)) {
            params.put("selectedStatusId", selectedStatusId);
        }
        String selectedContentTypeId = ServletRequestUtils.getStringParameter(request, "selectedContentTypeId", "");
        if (StringUtils.isNotEmpty(selectedContentTypeId)) {
            params.put("selectedContentTypeId", selectedContentTypeId);
        }
        if(selectedItemId == -2){
            params.put("selectedContentTypeId", 3);
        }
        return params;
    }

}
