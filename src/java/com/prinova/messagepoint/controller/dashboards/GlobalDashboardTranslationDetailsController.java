package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.TranslateAccuracyEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.platform.services.backgroundtask.GlobalDashboardPushToTranslationInaccuracyBackgroundTask;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.prinova.messagepoint.platform.services.backgroundtask.GlobalDashboardPushToTranslationInaccuracyBackgroundTask.TranslationInaccuracy;

public class GlobalDashboardTranslationDetailsController extends AbstractBaseGlobalDashboardController {

    private static final String SELECTED_TRANSLATION_ID = "selectedTranslationId";
    private static final int ACTION_PUSH_TO_TRANSLATIONS = 4;

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        return new GlobalDashboardTranslationDetailsWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        long selectedTranslationId = ServletRequestUtils.getLongParameter(request, SELECTED_TRANSLATION_ID, -1L);
        if (selectedTranslationId != -1) {
            TranslateAccuracyEnum trAccuracy = TranslateAccuracyEnum.fromId(selectedTranslationId);
            switch(trAccuracy) {
                case ACCURATE -> {
                    referenceData.put("translationTableHeader", "client_messages.text.content_translation_accurate");
                }
                case INACCURATE -> {
                    referenceData.put("translationTableHeader", "client_messages.text.content_translation_inaccurate");
                }
                case NOT_TRANSLATED -> {
                    referenceData.put("translationTableHeader", "client_messages.text.content_translation_not_translated");
                }
                case EMPTY -> {
                    referenceData.put("translationTableHeader", "client_messages.text.content_translation_empty");
                }
            }
        }

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));
        referenceData.put("translateAccuracyType", TranslateAccuracyEnum.fromId(selectedTranslationId).getValue());

        return referenceData;
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
                                    BindException errors) throws Exception {
        int action = ServletRequestUtils.getIntParameter(request, "action", -1);

        if(action == ACTION_PUSH_TO_TRANSLATIONS) {
            GlobalDashboardPushToTranslationInaccuracyBackgroundTask task = new GlobalDashboardPushToTranslationInaccuracyBackgroundTask(getTranslationInaccuracies(request), UserUtil.getPrincipalUser());
            MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY, UserUtil.getPrincipalUser());
        }

        return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewTranslateParams(request));
    }

    private Set<TranslationInaccuracy> getTranslationInaccuracies(HttpServletRequest request) {
        Set<TranslationInaccuracy> translationInaccuracies = new HashSet<>();
        String[] selectedIds = request.getParameterValues("selectedIds");
        if (selectedIds != null) {
            for (String id : selectedIds) {
                if (StringUtils.isBlank(id)) {
                    continue;
                }
                String[] parts = id.split("_");
                String contentObjectId = parts[0];
                String languageId = parts[1];
                String pgTreeNodeGuid = parts[2];

                if(StringUtils.isBlank(contentObjectId)){
                    continue;
                }

                TranslationInaccuracy inaccuracy = new TranslationInaccuracy(Long.parseLong(contentObjectId));
                if(StringUtils.isNotBlank(pgTreeNodeGuid)) {
                    inaccuracy.setPgTreeNodeGuid(pgTreeNodeGuid);
                }
                if(StringUtils.isNotBlank(languageId)) {
                    inaccuracy.setLanguageId(Long.parseLong(languageId));
                }

                translationInaccuracies.add(inaccuracy);
            }
        }
        return translationInaccuracies;
    }

    private Map<String, Object> getSuccessViewTranslateParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        long selectedItemId = ServletRequestUtils.getLongParameter(request, SELECTED_TRANSLATION_ID, -1L);
        params.put(SELECTED_TRANSLATION_ID, selectedItemId);

        String selectedLangId = ServletRequestUtils.getStringParameter(request, "selectedLangId", "all");
        params.put("selectedLangId", selectedLangId);

        String selectedStatusId = ServletRequestUtils.getStringParameter(request, "selectedStatusId", "0");
        params.put("selectedStatusId", selectedStatusId);

        String selectedContentTypeId = ServletRequestUtils.getStringParameter(request, "selectedContentTypeId", "ALL");
        params.put("selectedContentTypeId", selectedContentTypeId);

        params.put("selectedItemId", ServletRequestUtils.getLongParameter(request, "selectedItemId", -1L));
        String selectedTotalCountString = decode(ServletRequestUtils.getStringParameter(request, "selectedTotal", "0"));
        int selectedTotalCount = Integer.parseInt(selectedTotalCountString);
        params.put("selectedTotal", selectedTotalCount);

        params.put("refreshTasks", true);

        return params;
    }

    private String decode(String value){
        if(StringUtils.isBlank(value)) {
            return value;
        }

        String decoded = value;
        while (decoded.contains("%")) {
            String next = URLDecoder.decode(decoded, StandardCharsets.UTF_8);
            if (next.equals(decoded)) break;
            decoded = next;
        }
        return decoded.replaceAll(",", "").replaceAll("\\.", "").replaceAll(" ", "");
    }
}