package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class GlobalDashboardNavigationWidgetController extends MessagepointController {

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		referenceData.put("globalDashboardNavigationWrapper", new GlobalDashboardNavigationWrapper());

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		
	}
	
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    		return new ModelAndView(new RedirectView(getSuccessView()));
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}

}
