package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.wrapper.AsyncListVO;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.computeNameTag;

public class AsyncGlobalDashboardSentimentDetailsVO extends AsyncListVO {

    private GlobalDashboardVOModel model;

    private GlobalDashboardSentimentDetailsVOFlags flags;

    public String getName() {
        return computeNameTag(this.model, false, null, null);
    }

    public void setModel(GlobalDashboardVOModel model) {
        this.model = model;
    }

    public String getBinding() {
        return "<input id='listItemCheck_" + this.model.getObjId() + "' type='checkbox' value='" + this.model.getObjId() +
                "' objectType='" + this.model.getObjTypeId()+ "' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
    }

    public GlobalDashboardSentimentDetailsVOFlags getFlags() {
        return flags;
    }

    public void setFlags(GlobalDashboardSentimentDetailsVOFlags flags) {
        this.flags = flags;
    }

    public static class GlobalDashboardSentimentDetailsVOFlags{
        private boolean                     canAddTask;

        public boolean isCanAddTask() {
            return canAddTask;
        }
        public void setCanAddTask(boolean canAddTask) {
            this.canAddTask = canAddTask;
        }
    }
}
