package com.prinova.messagepoint.controller.dashboards;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.ContentIntelligenceEvents;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.version.Approvable;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.model.homepage.WidgetActionType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tasks.BulkUpdateTaskService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.tag.EncodeUrlTag;
import com.prinova.messagepoint.theme.MessagepointTheme;
import com.prinova.messagepoint.theme.ThemeManager;
import com.prinova.messagepoint.util.UserUtil;

public class MyDashboardController extends MessagepointController {
	private static final Log log = LogUtil.getLog(MyDashboardController.class);
	public static final String REQ_PARM_ACTION 				= "action";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		List<HomepageWidget> homepageWidgets = HomepageWidget.listAll();
		if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGES));
		}
		if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_APPROVE_EDIT)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_EMBEDDED_CONTENT_VIEW)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_EMBEDDED_CONTENT_APPROVE)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_CONTENT_LIBRARY_VIEW)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_IMAGES));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_CONTENT_LIBRARY_APPROVE)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_SELECTIONS_VIEW)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_VARIANTS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_TEST_VIEW)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_RECENTLY_COMPLETED_TESTS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_RECENTLY_COMPLETED_PROOFS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_TASKS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_PROJECT_APPROVE)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_MY_TASK_APPROVALS));
		}
		if(!UserUtil.isPermissionGranted(Permission.ROLE_GLOBAL_SEARCH_VIEW)){
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_METADATA));
			homepageWidgets.remove(new HomepageWidget(HomepageWidget.ID_WIDGET_TYPE_CONTENT));
		}
		referenceData.put("availableWidgets", homepageWidgets);
		
		referenceData.put("touchpointFilterList", Document.findAllVisible(true));
		
		// Logo Image
		MessagepointTheme messagepointTheme = ThemeManager.getTheme(request);
		String requestedNodeGUID = request.getParameter("gd");
		String logoImageName = EncodeUrlTag.encodeFilename(messagepointTheme.getCorporateLogo());
		if (requestedNodeGUID != null)
			logoImageName = logoImageName + "&gd=" + requestedNodeGUID;
		referenceData.put("logoImage", logoImageName);
		
		return referenceData;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

		setOriginForm(request, null);
		return super.showForm(request, response, errors);

	}
	
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.MyDashboard);
		analyticsEvent.send();
		return new MyDashboardWrapper();
	}
	
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		String actionStr = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION);
		MyDashboardWrapper c = (MyDashboardWrapper) command;
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		String[] actionSpl = actionStr.split("_");
		int widgetId = Integer.valueOf(actionSpl[0]);
		int action = Integer.valueOf(actionSpl[1]);
		Map<Integer, List<Long>> selectedIdsForAll = c.getSelectedIds();
 		switch (action) {
			case (WidgetActionType.ID_ACTION_TYPE_APPROVE): {	
				if (widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS ||
						widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS ||
						widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS){

					List<Long> selectedIds;
					if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS)
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
					else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS)
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS);
					else
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS);

					List<ContentObject> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(ContentObject.findById(selectedId));
					}
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, "", false, list.toArray(new ContentObject[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (ContentObject contentObject : list) {
							sb.append(" id ").append(contentObject.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  content object are not approved. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS){
					List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS);
					List<TouchpointSelection> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(TouchpointSelection.findById(selectedId));
					}
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, "", false,  list.toArray(new TouchpointSelection[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView())); 
					}
				}else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_TASK_APPROVALS){
					List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_TASK_APPROVALS);
					List<Task> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(Task.findById(selectedId));
					}
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, "", false, list.toArray(new Task[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (Task task : list) {
							sb.append(" id ").append(task.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  tasks are not approved. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);

					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}
			}
			case (WidgetActionType.ID_ACTION_TYPE_REJECT): {
				if (widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS ||
						widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS ||
						widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS){

					List<Long> selectedIds;
					if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS)
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_SMART_TEXT_APPROVALS);
					else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS)
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_IMAGE_APPROVALS);
					else
						selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGE_APPROVALS);

					List<ContentObject> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(ContentObject.findById(selectedId));
					}

					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, c.getAssignedToUser(), c.getUserNote(), list.toArray(new ContentObject[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (ContentObject contentObject : list) {
							sb.append(" id ").append(contentObject.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  content objects are not rejected. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);

					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS){
					List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS);
					List<TouchpointSelection> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(TouchpointSelection.findById(selectedId));
					}
					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, c.getAssignedToUser(), c.getUserNote(), list.toArray(new TouchpointSelection[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView())); 
					}
				}else if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_TASK_APPROVALS){
					List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_TASK_APPROVALS);
					List<Task> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						list.add(Task.findById(selectedId));
					}
					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, c.getAssignedToUser(), c.getUserNote(), list.toArray(new Task[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (Task task : list) {
							sb.append(" id ").append(task.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  tasks are not rejected. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);

					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}
			}
			case (WidgetActionType.ID_ACTION_TYPE_MARK_COMPLETE): {	
				if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_TASKS){
					boolean taskEditPerm = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_EDIT);
					if(taskEditPerm){
						List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_TASKS);
						List<Task> list = new ArrayList<>();
						for(Long selectedId : selectedIds){
							Task task = Task.findById(selectedId);
							if(task.getStatusId()==TaskStatus.ID_ACTIVE){
								list.add(task);
							}
						}
						ServiceExecutionContext context = BulkUpdateTaskService.createContextForMarkComplete(list, requestor);
						Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateTaskService.SERVICE_NAME,
								BulkUpdateTaskService.class);				
						service.execute(context);
						ServiceResponse serviceResponse = context.getResponse();
						if (!serviceResponse.isSuccessful()) {
							StringBuilder sb = new StringBuilder();
							sb.append(BulkUpdateTaskService.SERVICE_NAME);
							sb.append(" service call is not successful ");
							sb.append(" in ").append(this.getClass().getName());
							for(Task task: list){
								sb.append(" id ").append(task.getId());
							}
							sb.append(" requestor=").append(requestor.getUsername());
							sb.append("  tasks are not marked complete. ");
							log.error(sb.toString());
							ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
							return showForm(request, response, errors);
						} else {
							return new ModelAndView(new RedirectView(getSuccessView()));
						}
					}
				}
			}
			case (WidgetActionType.ID_ACTION_TYPE_RELEASE_FROM_TRANSLATION): {
				if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_TRANSLATIONS){
					List<Long> selectedIds = selectedIdsForAll.get(HomepageWidget.ID_WIDGET_TYPE_MY_TRANSLATIONS);
					List<Approvable> list = new ArrayList<>();
					for(Long selectedId : selectedIds){
						ContentObject contentObject = ContentObject.findById(selectedId);
						if(contentObject!=null){
							list.add(contentObject);
						}
					}
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, "", false, list.toArray(new Approvable[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (Approvable instance : list) {
							sb.append(" id ").append(((IdentifiableMessagePointModel) instance).getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  models are not released from translation. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()));
					}
				}
			}
 		}
		return null;
	}
}
