package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.createModel;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;

public class AsyncGlobalDashboardDuplicatesWrapper extends AsyncAbstractListWrapper {

    private Map<GlobalDashboardContentDto, Integer> duplicatesPerPageMap = new LinkedHashMap<>();

    public AsyncGlobalDashboardDuplicatesWrapper(int pageSize, int pageIndex, long selectedItemId) {
        this.buildItemsList(pageSize, pageIndex, selectedItemId);
    }

    private void buildItemsList(int pageSize, int pageIndex, long selectedItemId) {
        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
        Map<GlobalDashboardContentDto, Integer> duplicatesMap = messagepointElasticSearchHandler.searchDuplicatesWithFilterMap(filterMap, dashboardFilterList);
        int duplicatesMapSize = 0;
        if (!MapUtils.isEmpty(duplicatesMap)) {
            duplicatesPerPageMap = duplicatesMap.entrySet().stream()
                    .skip((pageIndex - 1) * pageSize).limit(pageSize)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
            duplicatesMapSize = duplicatesMap.size();
        }
        super.setiTotalRecords(duplicatesMapSize);
        super.setiTotalDisplayRecords(duplicatesMapSize);
    }

    @Override
    public void init() {
        List<AsyncGlobalDashboardDuplicatesVO> aaData = new ArrayList<>();
        if (MapUtils.isEmpty(duplicatesPerPageMap)) {
            super.setAaData(aaData);
            return;
        }

        for (GlobalDashboardContentDto currentDto : duplicatesPerPageMap.keySet()) {
            GlobalDashboardVOModel modelItem = createModel(currentDto);
            AsyncGlobalDashboardDuplicatesVO vo = new AsyncGlobalDashboardDuplicatesVO();
            vo.setModel(modelItem);
            vo.setCount(duplicatesPerPageMap.get(currentDto));
            vo.setDisplayMode(getDisplayMode());

            // For drill-down
            vo.setDT_RowId(modelItem.getContentId());

            aaData.add(vo);
        }
        super.setAaData(aaData);
    }
}
