package com.prinova.messagepoint.controller.dashboards;

import ai.mpr.marcie.content.rationalizer.steps.translateaccuracy.TranslateAccuracyProcessor;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.search.GlobalSearchTargetType;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.select.Elements;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.constructLocaleSelectionOnClick;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.constructMarkupForTranslateViewHtml;


public class AsyncGlobalDashboardTranslationAccuracyDetailsVO extends AsyncListVO{

    private TranslateDashboardVOModel model;

    private GlobalDashboardTranslationAccuracyDetailsVOFlags flags;

    private String selectedLanguageId;
    private String pgtnGuid;
    private String taskInfo;

    public String getTaskInfo() {
        if (taskInfo != null) {
            return taskInfo;
        }
        return "";
    }

    public void setTaskInfo(final String taskInfo) {
        this.taskInfo = taskInfo;
    }

    public String getName() {
        return computeNameTag(this.model, false);
    }

    public void setModel(TranslateDashboardVOModel model) {
        this.model = model;
    }

    public String getSelectedLanguageId() {
        return selectedLanguageId;
    }

    public void setSelectedLanguageId(String selectedLanguageId) {
        this.selectedLanguageId = selectedLanguageId;
    }

    public String getPgtnGuid() {
        return pgtnGuid;
    }

    public void setPgtnGuid(String pgtnGuid) {
        this.pgtnGuid = pgtnGuid;
    }

    public String getBinding() {
        return "<input id='listItemCheck_" + this.model.getObjId() + "' type='checkbox' value='" + this.model.getObjId() + '_' + ((StringUtils.isNotBlank(this.model.getTranslatedLanguageId()) ? this.model.getTranslatedLanguageId() : "null"))  + '_' + (StringUtils.isNotBlank(this.model.getPgtnGuid()) ?  this.model.getPgtnGuid() : "null") +
                "' objectType='" + this.model.getObjTypeId() + "' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
    }

    public GlobalDashboardTranslationAccuracyDetailsVOFlags getFlags() {
        return flags;
    }

    public void setFlags(GlobalDashboardTranslationAccuracyDetailsVOFlags flags) {
        this.flags = flags;
    }

    public static class GlobalDashboardTranslationAccuracyDetailsVOFlags{
        private boolean canAddTask;

        public boolean isCanAddTask() {
            return canAddTask;
        }

        public void setCanAddTask(boolean canAddTask) {
            this.canAddTask = canAddTask;
        }
    }

    private String computeNameTag(TranslateDashboardVOModel model, Boolean showMarkup) {

        String divider = "<span style=\"padding: 0px 8px;\">|</span>";

        StringBuilder cardHTML = new StringBuilder();
        cardHTML.append("<div style=\"white-space: normal !important;\">");

        // Tooltip: Touchpoint name for messages
        String tooltip = "";
        Document touchpoint = null;

        ContentObject contentObject = null;
        if(model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE) {
            contentObject = ContentObject.findByIdWorkingDataFocusCentric(model.getObjId());
            if(contentObject != null) {
                tooltip = " title=\"" + contentObject.getDocument().getName() + "\" data-toggle=\"tooltip\" ";
                touchpoint = contentObject.getDocument();
            }
        } else {
            contentObject = ContentObject.findByIdActiveDataFocusCentric(model.getObjId());
            if(contentObject != null && contentObject.getVisibleDocuments() != null && !contentObject.getVisibleDocuments().isEmpty()) {
                touchpoint = contentObject.getVisibleDocuments().iterator().next();
            }
        }

        String objectNameDisplay = "<a href=\"#\" " + generateOnClickLink(model) + ">" + model.getObjName() + "</a>";

        // Name
        cardHTML.append("	<div ").append(tooltip).append(" style=\"display: inline-block; max-width: 90%; min-width: 90%; vertical-align: top; font-weight: 700; margin-bottom: 4px; white-space: normal;\">").append(objectNameDisplay).append("	</div>");

        cardHTML.append("</div>");

        String html = "";

        Content content = Content.findById(model.getContentId());
        if(content != null){
            html = touchpoint != null ? content.getContent(touchpoint) : content.getContent();
        }

        String defaultHtml = constructMarkupForTranslateViewHtml(model.getDefaultLanguageHtml());
        String trHtml = constructMarkupForTranslateViewHtml(html);
        String modelErrMessage = model.getTranslateErrorMessage();
        if(StringUtils.isNotBlank(modelErrMessage) && modelErrMessage.contains( ApplicationUtil.getMessage("client_messages.content_editor.content_errors"))) {
            String[] split = modelErrMessage.split(ApplicationUtil.getMessage("client_messages.content_editor.content_errors"));
            String errvars = split[1].replace(ApplicationUtil.getMessage("client_messages.content_editor.content_errors"), "");
            String[] varsList = errvars.split("<br/>");
            org.jsoup.nodes.Document document = Jsoup.parse(defaultHtml + trHtml);
            for(String var : varsList) {
                if(StringUtils.isEmpty(var)){
                    continue;
                }
                if(var.contains("=")) {
                    String[] attList = var.split("=");
                    Elements elementsByAttributeValue = document.body().getElementsByAttributeValue(attList[0], attList[1]);
                    if(elementsByAttributeValue != null && !elementsByAttributeValue.isEmpty()) {
                        modelErrMessage = modelErrMessage.replace(var, elementsByAttributeValue.get(0).toString());
                    }
                } else {
                    if(TranslateAccuracyProcessor.INLINE_TARGETING_ERROR.equals(var)) {
                        modelErrMessage = modelErrMessage.replace(var, ApplicationUtil.getMessage("client_messages.content_editor.inline_targeting_errors"));
                    } else if(TranslateAccuracyProcessor.IMAGE_ERROR.equals(var)) {
                        modelErrMessage = modelErrMessage.replace(var, ApplicationUtil.getMessage("client_messages.content_editor.image_errors"));
                    }
                }
            }
        }

        // Translate accuracy message
        if(StringUtils.isNotEmpty(modelErrMessage)) {
            cardHTML.append("<div id=\"resultContent_").append(model.getContentId()).append("\" class=\"resultContentContainer contentContainer position-relative\" style=\"white-space: normal;\">");

            String messageHtml = modelErrMessage.replaceAll("\\n", "<br>");
            cardHTML.append(messageHtml);
            cardHTML.append("<div>&nbsp;</div>");

            cardHTML.append("</div>");
        }

        cardHTML.append("<table width=\"99%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\">");

        cardHTML.append("<tr width=\"1%\" align=\"left\" style=\"padding: 0px; padding-right: 1px; padding-left: 11px\">");

        cardHTML.append("<td style=\"border: 1px solid; text-wrap: wrap; border-style:ridge; padding-right:1px;\" width=\"99%\"><details id='defaultText_").append(model.getContentId()).append("' style='overflow: auto; position: relative; max-height: 150px; max-width:725px; display:block; '> <summary id='defaultText_").append(model.getContentId()).append("_summary' style ='font-weight: bold;'>").append(ApplicationUtil.getMessage("page.text.default.language.content")).append("</summary><div style=\"position:relative;\">").append(defaultHtml).append("</div></details></td></tr>");

        cardHTML.append("<tr width=\"1%\" align=\"left\" style=\"padding: 0px; padding-right: 1px; padding-left: 11px\">");
        cardHTML.append("<td style=\"border: 1px solid; text-wrap: wrap; border-style:ridge; padding-right:1px;\" width=\"99%\"><details id='translatedText_").append(model.getContentId()).append("' style='overflow: auto; position: relative; max-height: 150px; max-width:725px; display:block; '> <summary id='translatedText_").append(model.getContentId()).append("_summary' style ='font-weight: bold;'>").append(ApplicationUtil.getMessage("page.text.translated.language.content")).append("</summary><div style=\"position:relative;\">").append(trHtml).append("</div></details></td>");

        cardHTML.append("</tr></table>");

        cardHTML.append("<div style=\"white-space: normal !important; color: #777; font-weight: 100;\"></div>");

        cardHTML.append("<div style=\"white-space: normal !important; color: #777; font-weight: 100;\">");

        // Icons
        String icons = ContentObjectContentUtil.getTagIcons(contentObject);
        if(!icons.isEmpty()) {
            cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle; padding-right: 4px; opacity: 0.75;\">").append(icons).append("</div>");
        }

        // Language
        cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(ApplicationUtil.getMessage("page.label.language")).append(":</div>");
        cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(StringUtils.isNotBlank(model.getTranslatedLanguageCode()) ? MessagepointLocale.getLanguageLocaleByLocaleCode(model.getTranslatedLanguageCode()).getName() : ApplicationUtil.getMessage("page.label.unknown")).append("</div>");

        // Status
        cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(divider).append(ApplicationUtil.getMessage("page.label.status")).append(":</div>");
        cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(StringUtils.isNotBlank(model.getObjectStatus()) ? model.getObjectStatus() : ApplicationUtil.getMessage("page.label.unknown")).append("</div>");

        // Variant
            if ( model.getPgtnGuid() != null && !model.getPgtnGuid().isEmpty()) {
                ParameterGroupTreeNode parameterGroupTreeNode = ParameterGroupTreeNode.findByGuid(model.getPgtnGuid());

                String fullpath = ParameterGroupTreeNode.getFullPath(parameterGroupTreeNode);
                if(StringUtils.isNotEmpty(fullpath)) {
                    if(fullpath.startsWith("/")){
                        fullpath = fullpath.substring(1);
                    }
                    fullpath = fullpath.replace("/", " > ");
                    if(!fullpath.equalsIgnoreCase("Master")) {
                        cardHTML.append("   <div style=\"display: inline-block; vertical-align: middle;\">").append(divider).append(ApplicationUtil.getMessage("page.label.variant")).append(":</div>");
                        cardHTML.append("   <div style=\"display: inline-block; vertical-align: middle;\">").append(fullpath).append("</div>");
                    }
                }
            }

        cardHTML.append("</div>");

        return cardHTML.toString();

    }

    private String generateOnClickLink(TranslateDashboardVOModel model) {

        String onClickLink = "";

        if (model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_SMART_TEXT ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_IMAGE ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_SMART_TEXT ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_IMAGE) {

            ContentObject contentObject = ContentObject.findById(this.model.getObjId());
            ParameterGroupTreeNode pgtn = Content.findPGTN(this.model.getContentId());

            String pgtnParam = "";
            if (pgtn != null) {
                if (contentObject.isDynamicVariantEnabled()) {
                    pgtnParam = "&paramInstId=" + pgtn.getId();
                } else if (contentObject.isStructuredContentEnabled()) {
                    TouchpointSelection tpSelection = TouchpointSelection.findByPgTreeNodeId(pgtn.getId());
                    pgtnParam = "&touchpointSelectionId=" + tpSelection.getId();
                }
            }

            String languageSelection = StringUtils.isEmpty(selectedLanguageId) ? "" : constructLocaleSelectionOnClick(contentObject, selectedLanguageId);
            if (contentObject.isDynamicVariantEnabled()) {
                onClickLink = "onclick=\"javascript:crossLinkOpen('content/content_object_view_content_dynamic_variant.form?documentId=" + contentObject.getDocuments().iterator().next().getId() + "&contentObjectId=" + contentObject.getId() + pgtnParam +
                        "&statusViewId=" + model.getObjectStatusId() + "');"+ languageSelection +" return false;\"";
              } else if (contentObject.isStructuredContentEnabled() && StringUtils.isNotEmpty(pgtnParam)) {
                onClickLink = "onclick=\"javascript:crossLinkOpen('tpadmin/touchpoint_content_selection_view.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() + pgtnParam +
                        "&statusViewId=" + model.getObjectStatusId() + "');"+ languageSelection +" return false;\"";
            } else {
                if(CollectionUtils.isNotEmpty(contentObject.getDocuments())) {
                    onClickLink = "onclick=\"javascript:crossLinkOpen('content/content_object_view.form?documentId=" + contentObject.getDocuments().iterator().next().getId() + "&contentObjectId=" + contentObject.getId() +
                            "&statusViewId=" + model.getObjectStatusId() + "');"+ languageSelection +" return false;\"";
                }
            }
        }
        return onClickLink;
    }


}
