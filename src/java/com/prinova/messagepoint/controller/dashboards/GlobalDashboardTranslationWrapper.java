package com.prinova.messagepoint.controller.dashboards;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GlobalDashboardTranslationWrapper implements Serializable{

    @Serial
    private static final long serialVersionUID = 10835077309427545L;

    private int inaccurateCount = 0;
    private  String translationDisplayString;
    private List<Integer> graphData = new ArrayList<>();
    private List<String> graphLabels = Arrays.asList(
            ApplicationUtil.getMessage("page.label.dashboard.translation.accurate"),
            ApplicationUtil.getMessage("page.label.dashboard.translation.inaccurate"),
            ApplicationUtil.getMessage("page.label.dashboard.translation.not.translated"),
            ApplicationUtil.getMessage("page.label.dashboard.translation.empty"));

    private String actionValue;
    private List<Long> selectedIds;


    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public List<Integer> getGraphData() {
        return graphData;
    }

    public void setGraphData(List<Integer> graphData) {
        this.graphData = graphData;
    }

    public String getGraphLabelsAsJS() throws IOException {
        return new ObjectMapper().writeValueAsString(graphLabels);
    }


    public String getTranslationDisplayString() {
        return translationDisplayString;
    }

    public void setTranslationDisplayString(String translationDisplayString) {
        this.translationDisplayString = translationDisplayString;
    }

    public int getInaccurateCount() {
        return inaccurateCount;
    }

    public void setInaccurateCount(int inaccurateCount) {
        this.inaccurateCount = inaccurateCount;
    }

    public List<String> getGraphLabels() {
        return graphLabels;
    }

    public void setGraphLabels(List<String> graphLabels) {
        this.graphLabels = graphLabels;
    }

    public List<Long> getSelectedIds() {
        return selectedIds;
    }

    public void setSelectedIds(List<Long> selectedIds) {
        this.selectedIds = selectedIds;
    }
}