package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import org.json.JSONArray;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GlobalDashboardBrandDetailsController extends AbstractBaseGlobalDashboardController {


    private static final String SELECTED_BRAND_ITEM_ID = "selectedBrandItemId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long selectedBrandItemId = ServletRequestUtils.getLongParameter(request, SELECTED_BRAND_ITEM_ID, -1L);
        BrandProfileEnum selectedBrandProfile = BrandProfileEnum.fromId(selectedBrandItemId);
        BrandProfile brandProfile = BrandProfile.findPrimaryProfile();

        GlobalDashboardBrandDetailsWrapper wrapper = new GlobalDashboardBrandDetailsWrapper();
        if (BrandProfileEnum.RESTRICTED_TERM.equals(selectedBrandProfile)) {
            JSONArray enabledRestrictedTerms = brandProfile.getEnabledRestrictedTerms();
            wrapper.setFilterItemsList(extractEnabledItems(enabledRestrictedTerms));
        } else if (BrandProfileEnum.PREFERRED_CONTRACTION.equals(selectedBrandProfile)) {
            JSONArray enabledPreferredContractions = brandProfile.getEnabledPreferredContractions();
            wrapper.setFilterItemsList(extractEnabledItems(enabledPreferredContractions));
        } else if (BrandProfileEnum.RESTRICTED_CONTRACTION.equals(selectedBrandProfile)) {
            JSONArray enabledRestrictedContractions = brandProfile.getEnabledRestrictedContractions();
            wrapper.setFilterItemsList(extractEnabledItems(enabledRestrictedContractions));
        }

        return wrapper;
    }

    private List<String> extractEnabledItems(JSONArray enabledTermsArray) {
        if (enabledTermsArray == null || enabledTermsArray.isEmpty()) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (int i = 0; i < enabledTermsArray.length(); i++) {
            result.add(enabledTermsArray.getJSONObject(i).getString("target"));
        }

        return result;
    }
}