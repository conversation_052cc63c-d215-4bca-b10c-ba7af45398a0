package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;

public class AsyncGlobalDashboardBrandDetailsVO extends AsyncListVO {

    private GlobalDashboardVOModel model;

    private Boolean showMarkup;

    private BrandProfile brandProfile;

    private BrandProfileEnum selectedBrand;

    private GlobalDashboardBrandDetailsVOFlags flags;

    public String getName() {
        return GlobalDashboardTagUtil.computeNameTag(this.model, this.showMarkup, this.brandProfile, this.selectedBrand);
    }

    public void setModel(GlobalDashboardVOModel model) {
        this.model = model;
    }

    public void setShowMarkup(Boolean showMarkup) {
        this.showMarkup = showMarkup;
    }

    public void setBrandProfile(BrandProfile brandProfile) {
        this.brandProfile = brandProfile;
    }

    public void setSelectedBrand(BrandProfileEnum selectedBrand) {
        this.selectedBrand = selectedBrand;
    }

    public String getBinding() {
        return "<input id='listItemCheck_" + this.model.getObjId() + "' type='checkbox' value='" + this.model.getObjId() +
                "' objectType='" + this.model.getObjTypeId()+ "' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
    }

    public GlobalDashboardBrandDetailsVOFlags getFlags() {
        return flags;
    }

    public void setFlags(GlobalDashboardBrandDetailsVOFlags flags) {
        this.flags = flags;
    }

    public static class GlobalDashboardBrandDetailsVOFlags{
        private boolean                     canAddTask;

        public boolean isCanAddTask() {
            return canAddTask;
        }
        public void setCanAddTask(boolean canAddTask) {
            this.canAddTask = canAddTask;
        }
    }
}
