package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.ReadingComprehensionEnum;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.contentintelligence.FleschReadabilityLevelType;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.RangeOperation;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.createModel;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;

public class AsyncGlobalDashboardReadingDetailsWrapper extends AsyncAbstractListWrapper {
    private Collection<GlobalDashboardContentDto> contentDtoList;

    public AsyncGlobalDashboardReadingDetailsWrapper(int pageSize, int pageIndex, long selectedReadingId, long selectedItemId) {
        this.buildItemsList(pageSize, pageIndex, selectedReadingId, selectedItemId);
    }

    private void buildItemsList(int pageSize, int pageIndex, long selectedReadingId, long selectedItemId) {

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        RangeOperation operation = ReadingComprehensionEnum.fromId(selectedReadingId).getOperation();
        int readabilityTargetId = Integer.parseInt(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ContentIntelligence.KEY_TargetFleschReadabilityLevel));
        FleschReadabilityLevelType readabilityTargetLevel = new FleschReadabilityLevelType(readabilityTargetId);
        Double readabilityTargetValue = Double.valueOf(readabilityTargetLevel.getTopGradeLevelThreshold());

        int startIndexInclusive = (pageIndex - 1) * pageSize;
        int endIndexExclusive = startIndexInclusive + pageSize;

        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.readability);

        final TotalCountAndGlobalDashboardContentDtosSlice totalCountAndGlobalDashboardContentDtosSlice = messagepointElasticSearchHandler.getContentIdsWithReadabilityComparedToTarget(
                operation,
                readabilityTargetValue,
                filterMap,
                dashboardFilterList,
                startIndexInclusive,
                endIndexExclusive
        );

        int elasticResponseSize = totalCountAndGlobalDashboardContentDtosSlice.getTotalCount();
        contentDtoList = totalCountAndGlobalDashboardContentDtosSlice.getGlobalDashboardContentDtosSlice();

        super.setiTotalRecords(elasticResponseSize);
        super.setiTotalDisplayRecords(elasticResponseSize);
    }

    @Override
    public void init() {
        List<AsyncGlobalDashboardReadingDetailsVO> aaData = new ArrayList<>();
        if (CollectionUtils.isEmpty(contentDtoList)) {
            super.setAaData(aaData);
            return;
        }

        for (GlobalDashboardContentDto crtContentDto : contentDtoList) {
            GlobalDashboardVOModel modelItem = createModel(crtContentDto);

            AsyncGlobalDashboardReadingDetailsVO vo = new AsyncGlobalDashboardReadingDetailsVO();
            AsyncGlobalDashboardReadingDetailsVO.GlobalDashboardReadingDetailsVOFlags flags = new AsyncGlobalDashboardReadingDetailsVO.GlobalDashboardReadingDetailsVOFlags();
            vo.setDisplayMode(getDisplayMode());
            vo.setModel(modelItem);

            int objectTypeId = modelItem.getObjTypeId();
            int taskItemType = 0;
            long objId = modelItem.getObjId();

            ContentObject contentObject = ContentObject.findById(objId);

            if (contentObject.isGlobalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_SMART_TEXT;
            }
            else if (contentObject.isLocalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_LOCAL_TEXT;
            }
            else
            {
                taskItemType = TaskListItemFilterType.ID_MESSAGE;
            }

            flags.setCanAddTask         (Task.findAllByItemType(taskItemType, objId).isEmpty());
            vo.setFlags(flags);
            vo.setDT_RowId(modelItem.getObjId());

            aaData.add(vo);
        }
        super.setAaData(aaData);
    }
}
