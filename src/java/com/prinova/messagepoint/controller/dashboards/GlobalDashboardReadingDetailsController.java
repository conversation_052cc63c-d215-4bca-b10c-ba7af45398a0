package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.ReadingComprehensionEnum;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class GlobalDashboardReadingDetailsController extends AbstractBaseGlobalDashboardController {

    private static final String SELECTED_READING_ID = "selectedReadingId";

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        return new GlobalDashboardWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);

        long selectedReadingId = ServletRequestUtils.getLongParameter(request, SELECTED_READING_ID, -1L);
        if (selectedReadingId != -1) {
            ReadingComprehensionEnum readingComprehensionEnum = ReadingComprehensionEnum.fromId(selectedReadingId);
            switch (readingComprehensionEnum) {
                case GOOD: {
                    referenceData.put("readabilityTableHeader", "client_messages.text.content_good_readability");
                    break;
                }
                case POOR: {
                    referenceData.put("readabilityTableHeader", "client_messages.text.content_poor_readability");
                    break;
                }
            }
        }

        return referenceData;
    }
}