package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.createModel;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;

public class AsyncGlobalDashboardDuplicatesDetailsWrapper extends AsyncAbstractListWrapper {

    private List<GlobalDashboardContentDto> contentDtoList;

    public AsyncGlobalDashboardDuplicatesDetailsWrapper(int pageSize, int pageIndex, long comparisonContentId, long selectedItemId) {
        this.buildItemsList(pageSize, pageIndex, comparisonContentId, selectedItemId);
    }

    private void buildItemsList(int pageSize, int pageIndex, long comparisonContentId, long selectedItemId) {
        Content content = Content.findById(comparisonContentId);

        String comparisonContent = ContentObjectContentUtil.getUnformattedTextContentForMarcie(content.getEncodedContent());
        String contentHash = ElasticsearchContentUtils.calculateContentHash(comparisonContent);

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
        List<GlobalDashboardContentDto> elasticSearchResultList = messagepointElasticSearchHandler.searchDuplicatesByHashWithFilterMap(contentHash, filterMap, dashboardFilterList);

        int elasticResponseSize = 0;
        if (CollectionUtils.isNotEmpty(elasticSearchResultList)) {
            contentDtoList = elasticSearchResultList.stream()
                    .skip((pageIndex - 1) * pageSize).limit(pageSize)
                    .collect(Collectors.toList());
            elasticResponseSize = elasticSearchResultList.size();
        }

        super.setiTotalRecords(elasticResponseSize);
        super.setiTotalDisplayRecords(elasticResponseSize);
    }

    @Override
    public void init() {
        List<AsyncGlobalDashboardDuplicatesDetailsVO> aaData = new ArrayList<>();
        if (CollectionUtils.isEmpty(contentDtoList)) {
            super.setAaData(aaData);
            return;
        }

        for (GlobalDashboardContentDto crtContentDto : contentDtoList) {
            GlobalDashboardVOModel modelItem = createModel(crtContentDto);

            AsyncGlobalDashboardDuplicatesDetailsVO vo = new AsyncGlobalDashboardDuplicatesDetailsVO();
            AsyncGlobalDashboardDuplicatesDetailsVO.GlobalDashboardDuplicatesDetailsVOFlags flags = new AsyncGlobalDashboardDuplicatesDetailsVO.GlobalDashboardDuplicatesDetailsVOFlags();
            vo.setDisplayMode(getDisplayMode());
            vo.setModel(modelItem);

            int objectTypeId = modelItem.getObjTypeId();
            int taskItemType = 0;
            long objId = modelItem.getObjId();

            ContentObject contentObject = ContentObject.findById(objId);

            if (contentObject.isGlobalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_SMART_TEXT;
            }
            else if (contentObject.isLocalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_LOCAL_TEXT;
            }
            else
            {
                taskItemType = TaskListItemFilterType.ID_MESSAGE;
            }

            flags.setCanAddTask         (Task.findAllByItemType(taskItemType, objId).isEmpty());
            vo.setFlags(flags);
            vo.setDT_RowId(modelItem.getObjId());

            aaData.add(vo);
        }
        super.setAaData(aaData);
    }
}
