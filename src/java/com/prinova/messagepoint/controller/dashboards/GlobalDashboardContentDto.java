package com.prinova.messagepoint.controller.dashboards;

import java.util.Objects;

public class GlobalDashboardContentDto {

    private String elasticSearchId;

    private String guid;

    public GlobalDashboardContentDto(String elasticSearchId, String guid) {
        this.elasticSearchId = elasticSearchId;
        this.guid = guid;
    }

    public String getElasticSearchId() {
        return elasticSearchId;
    }

    public void setElasticSearchId(String elasticSearchId) {
        this.elasticSearchId = elasticSearchId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GlobalDashboardContentDto that = (GlobalDashboardContentDto) o;
        return Objects.equals(elasticSearchId, that.elasticSearchId) &&
                Objects.equals(guid, that.guid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(elasticSearchId, guid);
    }

}
