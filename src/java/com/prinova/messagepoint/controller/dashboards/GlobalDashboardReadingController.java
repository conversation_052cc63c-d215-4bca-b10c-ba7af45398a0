package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.rationalizer.RationalizerDashboardReadingWrapper;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.contentintelligence.FleschReadabilityLevelType;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.RangeOperation;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;


public class GlobalDashboardReadingController extends AbstractBaseGlobalDashboardController {
    private static final String REG_PARAM_SELECTED_ITEM_ID = "selectedItemId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long selectedItemId = ServletRequestUtils.getLongParameter(request, REG_PARAM_SELECTED_ITEM_ID, -1L);
        RationalizerDashboardReadingWrapper wrapper = new RationalizerDashboardReadingWrapper();

        int readabilityTarget = Integer.parseInt(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ContentIntelligence.KEY_TargetFleschReadabilityLevel));
        FleschReadabilityLevelType readabilityTargetLevel = new FleschReadabilityLevelType(readabilityTarget);

        Double readabilityTargetValue = Double.valueOf(readabilityTargetLevel.getTopGradeLevelThreshold());

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);

        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.readability);

        Integer goodReading = messagepointElasticSearchHandler.countContentWithReadabilityComparedToTarget(RangeOperation.LT, readabilityTargetValue, filterMap, dashboardFilterList);
        Integer poorReading = messagepointElasticSearchHandler.countContentWithReadabilityComparedToTarget(RangeOperation.GTE, readabilityTargetValue, filterMap, dashboardFilterList);
        List<Integer> readingCountsList = new LinkedList<>();
        readingCountsList.add(goodReading == null ? 0 : goodReading);
        readingCountsList.add(poorReading == null ? 0 : poorReading);
        wrapper.setGraphData(readingCountsList);

        Float readingAverage = messagepointElasticSearchHandler.computeReadabilityFleschReadingAverageWithFilterMap(filterMap, dashboardFilterList);
        readingAverage = readingAverage == null ? 0 : readingAverage;

        String averageFleschScoreDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.reading.flesch.kincaid.score"), String.format("%.2f", readingAverage),
                FleschReadabilityLevelType.getDescriptionFromGradeScore(readingAverage));
        wrapper.setAverageFleschScoreDisplayString(averageFleschScoreDisplayString);

        String averageFleschKincaidDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.reading.flesch.kincaid"),
                ApplicationUtil.getMessage(readabilityTargetLevel.getDisplayMessageCode()));
        wrapper.setAverageFleschKincaidDisplayString(averageFleschKincaidDisplayString);

        return wrapper;
    }
}