package com.prinova.messagepoint.controller.dashboards;

import java.util.List;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListController;
import com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListValidator;
import com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListWrapper;
import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.model.homepage.WidgetActionType;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class MyDashboardValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		MyDashboardWrapper command = (MyDashboardWrapper) commandObj;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			String widgetActionStr = command.getActionValue().split(",")[0];
			String[] actionSpl = widgetActionStr.split("_");
			int widgetId = Integer.valueOf(actionSpl[0]);
			int action = Integer.valueOf(actionSpl[1]);
			
			List<Long> selectedIds = command.getSelectedIds().get(widgetId);
	 		switch (action) {
				case (WidgetActionType.ID_ACTION_TYPE_APPROVE): {
					if(widgetId == HomepageWidget.ID_WIDGET_TYPE_MY_VARIANT_APPROVALS){
						TouchpointVariantListValidator tsListValidator = new TouchpointVariantListValidator();
						TouchpointVariantListWrapper tsListWrapper = new TouchpointVariantListWrapper(-1);
						tsListWrapper.setAction(Integer.toString(TouchpointVariantListController.ACTION_APPROVE));
						tsListWrapper.setSelectedIds(selectedIds);
						tsListValidator.validateNotGenericInputs(tsListWrapper, errors);
					}
				}
	 		}
		}
	}
}
