package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.Document;

import java.io.Serializable;
import java.util.List;

public class GlobalDashboardNavigationWrapper implements Serializable {

    private List<Document> allDocumentsAndProjectsEnabled;

    public GlobalDashboardNavigationWrapper() {
        buildWrapper();
    }

    private void buildWrapper() {
        this.allDocumentsAndProjectsEnabled = Document.findAllDocumentsAndProjectsEnabled(true);
    }

    public List<Document> getAllDocumentsAndProjectsEnabled() {
        return allDocumentsAndProjectsEnabled;
    }

}
