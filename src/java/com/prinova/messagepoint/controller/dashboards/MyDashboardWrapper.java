package com.prinova.messagepoint.controller.dashboards;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.UserUtil;

public class MyDashboardWrapper {
	private List<HomepageWidget> 		enabledWidgets 	= new ArrayList<>();
	private Map<Integer, List<Long>>	selectedIds		= new HashMap<>();
	private String 						actionValue;
	private String 						userNote;
	private User 						assignedToUser;
	
	public MyDashboardWrapper(){
		this.enabledWidgets.addAll(UserUtil.getEnabledWidgetTypesContext());
	}

	public List<HomepageWidget> getEnabledWidgets() {
		return enabledWidgets;
	}

	public void setEnabledWidgets(List<HomepageWidget> enabledWidgets) {
		this.enabledWidgets = enabledWidgets;
	}

	public Map<Integer, List<Long>> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(Map<Integer, List<Long>> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public String getUserNote() {
		return userNote;
	}

	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}

	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}
}
