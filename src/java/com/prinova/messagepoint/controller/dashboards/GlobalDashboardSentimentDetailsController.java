package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.SentimentEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class GlobalDashboardSentimentDetailsController extends AbstractBaseGlobalDashboardController {

    private static final String SELECTED_SENTIMENT_ID = "selectedSentimentId";

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        return new GlobalDashboardWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        long selectedSentimentId = ServletRequestUtils.getLongParameter(request, SELECTED_SENTIMENT_ID, -1L);
        if (selectedSentimentId != -1) {
            SentimentEnum sentiment = SentimentEnum.fromId(selectedSentimentId);
            switch (sentiment) {
                case NEGATIVE: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_negative_sentiment");
                    break;
                }
                case NEUTRAL: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_neutral_sentiment");
                    break;
                }
                case POSITIVE: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_positive_sentiment");
                    break;
                }
            }
        }

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

        return referenceData;
    }
}