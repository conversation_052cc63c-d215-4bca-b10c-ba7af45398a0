package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.wrapper.AsyncListVO;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.computeNameTag;

public class AsyncGlobalDashboardReadingDetailsVO extends AsyncListVO {

    private GlobalDashboardVOModel model;

    private GlobalDashboardReadingDetailsVOFlags flags;

    public String getName() {
        return computeNameTag(this.model, false, null, null);
    }

    public void setModel(GlobalDashboardVOModel model) {
        this.model = model;
    }

    public String getBinding() {
        return "<input id='listItemCheck_" + this.model.getObjId() + "' type='checkbox' value='" + this.model.getObjId() +
                "' objectType='" + this.model.getObjTypeId()+ "' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
    }

    public GlobalDashboardReadingDetailsVOFlags getFlags() {
        return flags;
    }

    public void setFlags(GlobalDashboardReadingDetailsVOFlags flags) {
        this.flags = flags;
    }

    public static class GlobalDashboardReadingDetailsVOFlags{
        private boolean                     canAddTask;

        public boolean isCanAddTask() {
            return canAddTask;
        }
        public void setCanAddTask(boolean canAddTask) {
            this.canAddTask = canAddTask;
        }
    }
}
