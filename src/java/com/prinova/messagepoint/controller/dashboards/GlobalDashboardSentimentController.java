package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.model.SentimentEnum.NEGATIVE;
import static com.prinova.messagepoint.model.SentimentEnum.NEUTRAL;
import static com.prinova.messagepoint.model.SentimentEnum.POSITIVE;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;
import static com.prinova.messagepoint.util.SentimentUtil.buildCountValueForSentiment;

public class GlobalDashboardSentimentController extends AbstractBaseGlobalDashboardController {
    private static final String REG_PARAM_SELECTED_ITEM_ID = "selectedItemId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long selectedItemId = ServletRequestUtils.getLongParameter(request, REG_PARAM_SELECTED_ITEM_ID, -1L);
        GlobalDashboardSentimentWrapper wrapper = new GlobalDashboardSentimentWrapper();

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.sentiment);
        Map<String, Integer> sentimentMap = messagepointElasticSearchHandler.computeSentimentWithFilterMap(filterMap, dashboardFilterList);

        Integer negativeCount = buildCountValueForSentiment(NEGATIVE, sentimentMap);
        Integer neutralCount = buildCountValueForSentiment(NEUTRAL, sentimentMap);
        Integer positiveCount = buildCountValueForSentiment(POSITIVE, sentimentMap);

        List<Integer> sentimentCountsList = new LinkedList<>();
        sentimentCountsList.add(negativeCount);
        sentimentCountsList.add(neutralCount);
        sentimentCountsList.add(positiveCount);

        wrapper.setGraphData(sentimentCountsList);
        wrapper.setNegativeCount(negativeCount);

        int totalCount = negativeCount + neutralCount + positiveCount;
        double negativePercent = negativeCount == 0 ? 0 : (double) (negativeCount * 100) / totalCount;

        String averageSentimentDisplayString;
        if (positiveCount >= negativeCount && positiveCount >= neutralCount) {
            averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.positive"),
                    String.format("%.2f", negativePercent));
        } else if (neutralCount >= negativeCount && neutralCount > positiveCount) {
            averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.neutral"),
                    String.format("%.2f", negativePercent));
        } else {
            averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.negative"),
                    String.format("%.2f", negativePercent));
        }
        wrapper.setAverageSentimentDisplayString(averageSentimentDisplayString);

        return wrapper;
    }
}