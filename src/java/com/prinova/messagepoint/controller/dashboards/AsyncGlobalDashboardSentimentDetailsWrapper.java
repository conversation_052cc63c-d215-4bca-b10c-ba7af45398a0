package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.model.SentimentEnum;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.controller.dashboards.GlobalDashboardTagUtil.createModel;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;

public class AsyncGlobalDashboardSentimentDetailsWrapper extends AsyncAbstractListWrapper {
    private Collection<GlobalDashboardContentDto> contentDtoList;

    public AsyncGlobalDashboardSentimentDetailsWrapper(int pageSize, int pageIndex, long selectedSentimentId, long selectedItemId) {
        this.buildItemsList(pageSize, pageIndex, selectedSentimentId, selectedItemId);
    }

    private void buildItemsList(int pageSize, int pageIndex, long selectedSentimentId, long selectedItemId) {
        String sentiment = SentimentEnum.fromId(selectedSentimentId).getValue();
        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        int startIndexInclusive = (pageIndex - 1) * pageSize;
        int endIndexExclusive = startIndexInclusive + pageSize;

        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.sentiment);
        final TotalCountAndGlobalDashboardContentDtosSlice totalCountAndGlobalDashboardContentDtosSlice = messagepointElasticSearchHandler.getContentIdsBySentimentWithFilterMap(
                sentiment,
                filterMap,
                dashboardFilterList,
                startIndexInclusive,
                endIndexExclusive
        );

        int elasticResponseSize = totalCountAndGlobalDashboardContentDtosSlice.getTotalCount();
        contentDtoList = totalCountAndGlobalDashboardContentDtosSlice.getGlobalDashboardContentDtosSlice();

        super.setiTotalRecords(elasticResponseSize);
        super.setiTotalDisplayRecords(elasticResponseSize);
    }

    @Override
    public void init() {
        List<AsyncGlobalDashboardSentimentDetailsVO> aaData = new ArrayList<>();
        if (CollectionUtils.isEmpty(contentDtoList)) {
            super.setAaData(aaData);
            return;
        }
        for (GlobalDashboardContentDto crtContentDto : contentDtoList) {
            GlobalDashboardVOModel modelItem = createModel(crtContentDto);

            AsyncGlobalDashboardSentimentDetailsVO vo = new AsyncGlobalDashboardSentimentDetailsVO();
            AsyncGlobalDashboardSentimentDetailsVO.GlobalDashboardSentimentDetailsVOFlags flags = new AsyncGlobalDashboardSentimentDetailsVO.GlobalDashboardSentimentDetailsVOFlags();
            vo.setDisplayMode(getDisplayMode());
            vo.setModel(modelItem);

            int objectTypeId = modelItem.getObjTypeId();
            int taskItemType = 0;
            long objId = modelItem.getObjId();

            ContentObject contentObject = ContentObject.findById(objId);

            if (contentObject.isGlobalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_SMART_TEXT;
            }
            else if (contentObject.isLocalSmartText())
            {
                taskItemType = TaskListItemFilterType.ID_LOCAL_TEXT;
            }
            else
            {
                taskItemType = TaskListItemFilterType.ID_MESSAGE;
            }

            flags.setCanAddTask         (Task.findAllByItemType(taskItemType, objId).isEmpty());
            vo.setFlags(flags);
            vo.setDT_RowId(modelItem.getObjId());

            aaData.add(vo);
        }

        super.setAaData(aaData);
    }
}
