package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.text.DecimalFormat;

public class AsyncGlobalDashboardBrandVO extends AsyncListVO {
    private static final DecimalFormat FORMATTER  = new DecimalFormat("###,###");

    private String description;
    private int count;

    public AsyncGlobalDashboardBrandVO() {
        super();
    }

    public String getName() {
        String brandStringValue = ApplicationUtil.getMessage("page.label.dashboard.brand." + description);
        return "<span style=\"font-weight: bold;\">" + brandStringValue + "</span>";
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCountDisplayString() {
        return "<span style=\"float: right;\">" + FORMATTER.format(count) + "</span";
    }

    public void setCount(int count) {
        this.count = count;
    }
}
