package com.prinova.messagepoint.controller.dashboards;

import org.springframework.validation.Errors;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class GlobalDashboardDuplicatesController extends AbstractBaseGlobalDashboardController {

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        return new GlobalDashboardWrapper();
    }
}