package com.prinova.messagepoint.controller.dashboards;

import java.util.Collection;

public class TotalCountAndTranslateDashboardContentDtosSlice{
    private Integer totalCount;
    private Collection<TranslateDashboardContentDto> translateDashboardContentDtosSlice;


    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Collection<TranslateDashboardContentDto> getTranslateDashboardContentDtosSlice() {
        return translateDashboardContentDtosSlice;
    }

    public void setTranslateDashboardContentDtosSlice(Collection<TranslateDashboardContentDto> translateDashboardContentDtosSlice) {
        this.translateDashboardContentDtosSlice = translateDashboardContentDtosSlice;
    }
}
