package com.prinova.messagepoint.controller.dashboards;

public class GlobalDashboardVOModel {
    // For name and tag search
    private long objId;
    private String objName;
    private int objTypeId;
    private String objType;
    private int instanceCount;

    private boolean isContentSearch;
    private boolean isStatusSearch;
    // For content search
    private String marcieID;
    private String text;
    private String objectStatus;
    private int objectStatusId;
    private String pgtnGuid;
    private long contentId;
    private String assignedTo;
    private String highlightText;
    private String zone;
    private String zonePart;

    public long getObjId() {
        return objId;
    }

    public void setObjId(long objId) {
        this.objId = objId;
    }

    public String getObjName() {
        return objName;
    }

    public void setObjName(String objName) {
        this.objName = objName;
    }

    public int getObjTypeId() {
        return objTypeId;
    }

    public void setObjTypeId(int objTypeId) {
        this.objTypeId = objTypeId;
    }

    public String getObjType() {
        return objType;
    }

    public void setObjType(String objType) {
        this.objType = objType;
    }

    public int getInstanceCount() {
        return instanceCount;
    }

    public void setInstanceCount(int instanceCount) {
        this.instanceCount = instanceCount;
    }

    public boolean isContentSearch() {
        return isContentSearch;
    }

    public void setContentSearch(boolean contentSearch) {
        isContentSearch = contentSearch;
    }

    public boolean isStatusSearch() {
        return this.isStatusSearch;
    }

    public void setStatusSearch(final boolean statusSearch) {
        this.isStatusSearch = statusSearch;
    }

    public String getMarcieID() {
        return marcieID;
    }

    public void setMarcieID(String marcieID) {
        this.marcieID = marcieID;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getObjectStatus() {
        return objectStatus;
    }

    public void setObjectStatus(String objectStatus) {
        this.objectStatus = objectStatus;
    }

    public int getObjectStatusId() {
        return objectStatusId;
    }

    public void setObjectStatusId(int objectStatusId) {
        this.objectStatusId = objectStatusId;
    }

    public String getPgtnGuid() {
        return pgtnGuid;
    }

    public void setPgtnGuid(String pgtnGuid) {
        this.pgtnGuid = pgtnGuid;
    }

    public long getContentId() {
        return contentId;
    }

    public void setContentId(long contentId) {
        this.contentId = contentId;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public String getHighlightText() {
        return highlightText;
    }

    public void setHighlightText(String highlightText) {
        this.highlightText = highlightText;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getZonePart() {
        return zonePart;
    }

    public void setZonePart(String zonePart) {
        this.zonePart = zonePart;
    }
}
