package com.prinova.messagepoint.controller.dashboards;

import com.github.jknack.handlebars.internal.lang3.StringUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.TranslateAccuracyEnum;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionEnum;
import com.prinova.messagepoint.search.GlobalSearchTargetType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.platform.services.elasticsearch.Utils.constructSelectedLanguagesList;
import static com.prinova.messagepoint.platform.services.elasticsearch.Utils.generateStatusFromId;

public class AsyncGlobalDashboardTranslationAccuracyDetailsWrapper extends AsyncAbstractListWrapper{
    private Collection<TranslateDashboardContentDto> contentDtoList;

    public AsyncGlobalDashboardTranslationAccuracyDetailsWrapper(int pageSize, int pageIndex, long selectedTranslationAccuracyId, long selectedItemId,
                                                                 String selectedLanguageCode, long selectedStatusId, int selectedTotalCount, String selectedContentTypeFilterValue) {
        this.buildItemsList(pageSize, pageIndex, selectedTranslationAccuracyId, selectedItemId, selectedLanguageCode, selectedStatusId, selectedTotalCount, selectedContentTypeFilterValue);
    }

    private void buildItemsList(int pageSize, int pageIndex, long selectedTranslationAccuracyId, long selectedItemId, String selectedLanguageCode,
                                long selectedStatusId, int selectedTotalCount, String selectedContentTypeFilterValue) {
        String trAccuracy = TranslateAccuracyEnum.fromId(selectedTranslationAccuracyId).getValue();
        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        int startIndexInclusive = (pageIndex - 1) * pageSize;
        int endIndexExclusive = startIndexInclusive + pageSize;

        if("all".equalsIgnoreCase(selectedLanguageCode) || "undefined".equalsIgnoreCase(selectedLanguageCode)) {
            selectedLanguageCode = "";
        }

        if("all".equalsIgnoreCase(selectedContentTypeFilterValue) || "undefined".equalsIgnoreCase(selectedContentTypeFilterValue)) {
            selectedContentTypeFilterValue = "";
        }
        String statusFilerValue = generateStatusFromId((int) selectedStatusId);
        Map<String, String> filterMap = buildMessagepointFilterMapForTranslate(selectedItemId);

        List<String> selecetdLanguagesList = constructSelectedLanguagesList(selectedLanguageCode);

        final TotalCountAndTranslateDashboardContentDtosSlice totalCountAndTranslateDashboardContentDtosSlice = messagepointElasticSearchHandler.getContentsByTranslationAccuracy(
                trAccuracy,
                filterMap,
                startIndexInclusive,
                endIndexExclusive,
                selecetdLanguagesList,
                statusFilerValue,
                selectedContentTypeFilterValue
        );
        if(totalCountAndTranslateDashboardContentDtosSlice != null) {
            contentDtoList = totalCountAndTranslateDashboardContentDtosSlice.getTranslateDashboardContentDtosSlice();
        }
        super.setiTotalRecords(selectedTotalCount);
        super.setiTotalDisplayRecords(selectedTotalCount);
    }

    private Map<String, String> buildMessagepointFilterMapForTranslate(long selectedItemId) {
        Map<String, String> result = new HashMap<>();
        if (MessagepointTreeSelectionEnum.ALL.getId().equals(selectedItemId)) {
            return result;
        } else if (MessagepointTreeSelectionEnum.SMART_TEXT.getId().equals(selectedItemId)) {
            result.put("attribute.type.keyword", "ST");
        }  else if (selectedItemId > 0) {
            Document touchpoint = Document.findById(selectedItemId);
            if (touchpoint != null) {
                result.put("attribute.touchpoint_guids.keyword", touchpoint.getGuid());
            }
        }

        return result;
    }
    @Override
    public void init() {
        List<AsyncGlobalDashboardTranslationAccuracyDetailsVO> aaData = new ArrayList<>();
        if(CollectionUtils.isEmpty(contentDtoList)) {
            super.setAaData(aaData);
            return;
        }
        for(TranslateDashboardContentDto crtContentDto : contentDtoList) {
            TranslateDashboardVOModel modelItem =createModel(crtContentDto);

            AsyncGlobalDashboardTranslationAccuracyDetailsVO vo = new AsyncGlobalDashboardTranslationAccuracyDetailsVO();
            AsyncGlobalDashboardTranslationAccuracyDetailsVO.GlobalDashboardTranslationAccuracyDetailsVOFlags flags = new AsyncGlobalDashboardTranslationAccuracyDetailsVO.GlobalDashboardTranslationAccuracyDetailsVOFlags();
            vo.setDisplayMode(getDisplayMode());
            vo.setModel(modelItem);
            vo.setDT_RowId(modelItem.getObjId());
            vo.setSelectedLanguageId(modelItem.getTranslatedLanguageId());
            if(modelItem != null && StringUtils.isNotBlank(modelItem.getPgtnGuid())){
                vo.setPgtnGuid(modelItem.getPgtnGuid());
            }
            flags.setCanAddTask(true);
            vo.setFlags(flags);
            ParameterGroupTreeNode parameterGroupTreeNode = ParameterGroupTreeNode.findByGuid(vo.getPgtnGuid());
            List<Task> taskList =  Task.findTranslateTasksByIdLocaleAndVariant(modelItem.getObjId(), Integer.parseInt(modelItem.getTranslatedLanguageId()), parameterGroupTreeNode == null ? ""  : parameterGroupTreeNode.getDna());
            vo.setTaskInfo(constructTaskInfoString(taskList, vo.getDT_RowId().toString()));
            aaData.add(vo);

        }
        super.setAaData(aaData);
    }


    public static String constructTaskInfoString(List<Task> taskList, String contentGuid) {
        if(CollectionUtils.isEmpty(taskList)) {
            return "";

        }
        int noOfTasks = taskList.size();

        Task task = taskList.get(0);
        String assigneeName = task.getAssignee() == null ? "" : task.getAssignee().getName();
        String reporterName = task.getReporter() == null ? "" : task.getReporter().getName();
        String description = task.getRequirementStr() == null ? "" : task.getRequirementStr();
        if(description.length() > 120) {
            description = description.substring(0, 120) + "...";
        }

        String taskInfo = "<p>&nbsp;</p>" + "<p>Task Type: " + ApplicationUtil.getMessage("page.label.task.type.translate") + "</p><p>Task Owner: " + assigneeName + "</p><p>Reported by: " + reporterName
                + "</p><p>Due Date: " + DateUtil.formatDate(task.getDueDate()) + "</p><p>Description: " + description + "</p>";

        String taskNo = noOfTasks > 1 ? String.valueOf(noOfTasks) : "";
        return "<div style=\"display: inline-block; padding-right: 2px; padding-left: 2px;\" id='tasksinfo'>" + taskNo + "&nbsp;&nbsp;<i id=\"activeLink_" + contentGuid + "\" " +
                "title=\"<div class='taskTipText' >" + taskInfo + "</div>\" " +
                "data-toggle=\"tooltip\" data-html=\"true\" data-placement=\"left\"" +
                "class=\"activeIconDiv taskDetailTip far fa-calendar-check\"></i></div>";

    }

    private TranslateDashboardVOModel createModel(TranslateDashboardContentDto crtContentDto) {
        String contentId = crtContentDto.getTranslatedContentElasticId();
        String[] contentIdSpl = contentId.split("_");
        String modelGuid = contentIdSpl[1];
        String pgtnGuid = contentIdSpl[2];
		String langCodeId = contentIdSpl[3];
        String status = contentIdSpl[4];
        String assetInstanceName = "";
        String instanceStatus = "";
        int instanceStatusId = 0;

        TranslateDashboardVOModel nullModel = new TranslateDashboardVOModel();
        nullModel.setObjName(ApplicationUtil.getMessage("error.not.found"));
        nullModel.setDefaultLanguageHtml(ApplicationUtil.getMessage("error.content.not.found", new String[]{crtContentDto.getTranslatedContentGuid()}));

        Content content = Content.findByGuid(crtContentDto.getTranslatedContentGuid());
        if (content != null) {
            TranslateDashboardVOModel modelItem = new TranslateDashboardVOModel();
            ContentObject contentObject = ContentObject.findByGuid(modelGuid);

            if (contentObject == null) {
                return nullModel;
            }

            if(contentObject.isStructuredContentEnabled() && contentObject.getDocument().isEnabledForVariantWorkflow() && !pgtnGuid.equals("0")){
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findByGuid(pgtnGuid);
                if(pgtn != null) {
                    TouchpointSelection ts = TouchpointSelection.findByPgTreeNodeId(pgtn.getId());
                    if(status.equals("WC") && ts.getHasWorkingCopy()) {
                        instanceStatus = VersionStatus.findById(VersionStatus.VERSION_WIP).getLocaledString();
                        instanceStatusId = 1;
                    } else if(status.equals("Active") && ts.getHasActiveCopy()) {
                        instanceStatus = VersionStatus.findById(VersionStatus.VERSION_PRODUCTION).getLocaledString();
                        instanceStatusId = 2;
                    }
                }
            } else {
                ContentObjectData cod = null;
                if (status.equals("WC") && contentObject.hasWorkingData()) {
                    cod = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
                } else if (status.equals("Active") && contentObject.hasActiveData()) {
                    cod = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
                }

                if (cod == null) {
                    // As delete is not implemented yet, it might be the case that the WC is still in elasticsearch
                    // but not in db
                    return nullModel;
                }

                instanceStatus = cod.getStatusDisplay();
                instanceStatusId = cod.isWorking() ? 1 : 2;
            }

            assetInstanceName = contentObject.getName();

            modelItem.setObjId(contentObject.getId());
            if (contentObject.isMessage()) {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_MESSAGE);
            } else if (contentObject.isGlobalSmartText()) {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_SMART_TEXT);
            } else {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_LOCAL_SMART_TEXT);
            }

            modelItem.setObjName(assetInstanceName);
            modelItem.setText(content.getUnformattedText());
            modelItem.setContentId(content.getId());
            modelItem.setMarcieID(contentId);

            if (!pgtnGuid.equals("0")) {
                modelItem.setPgtnGuid(pgtnGuid);
            }

            modelItem.setObjectStatus(instanceStatus);
            modelItem.setObjectStatusId(instanceStatusId);
            modelItem.setContentSearch(true);
            Content defaultContent = Content.findByGuid(crtContentDto.getDefaultContentGuid());
            modelItem.setDefaultLanguageHtml(defaultContent != null ? defaultContent.getContent() : ApplicationUtil.getMessage("error.content.not.found", new String[]{crtContentDto.getDefaultContentGuid()}));
            modelItem.setTranslatedLanguageCode(crtContentDto.getTranslatedLangCode());
            modelItem.setTranslateErrorMessage(crtContentDto.getTranslateErrorMessage());
            modelItem.setTranslatedLanguageId(langCodeId);

            return modelItem;

        }

        return nullModel;
    }
}
