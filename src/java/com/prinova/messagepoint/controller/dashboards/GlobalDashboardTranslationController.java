package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.touchpoints.VersionStatusFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.TranslateAccuracyEnum;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionEnum;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.model.TranslateAccuracyEnum.ACCURATE;
import static com.prinova.messagepoint.model.TranslateAccuracyEnum.EMPTY;
import static com.prinova.messagepoint.model.TranslateAccuracyEnum.INACCURATE;
import static com.prinova.messagepoint.model.TranslateAccuracyEnum.NOT_TRANSLATED;
import static com.prinova.messagepoint.platform.services.elasticsearch.Utils.constructSelectedLanguagesList;
import static com.prinova.messagepoint.platform.services.elasticsearch.Utils.generateStatusFromId;

public class GlobalDashboardTranslationController extends AbstractBaseGlobalDashboardController{
    private static final String REG_PARAM_SELECTED_ITEM_ID = "selectedItemId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = super.referenceData(request);
        referenceData.put("languages",  TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales());
        List<VersionStatusFilterType> versionStatusFilterTypes = new ArrayList<>();
        versionStatusFilterTypes.add(new VersionStatusFilterType(ContentObject.DATA_TYPE_ACTIVE));
        versionStatusFilterTypes.add(new VersionStatusFilterType(ContentObject.DATA_TYPE_WORKING));
        referenceData.put("statusFilterTypes", versionStatusFilterTypes);

        List<GlobalDashboardTranslateContentType> contentFilterTypes = new ArrayList<>();
        contentFilterTypes.add(new GlobalDashboardTranslateContentType(0, "ALL", "All"));
        contentFilterTypes.add(new GlobalDashboardTranslateContentType(1, "M","Message"));
        contentFilterTypes.add(new GlobalDashboardTranslateContentType(2, "LST", "Local Smart Text"));
        contentFilterTypes.add(new GlobalDashboardTranslateContentType(3, "ST", "Smart Text"));
        referenceData.put("contentFilterTypes", contentFilterTypes);
        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long selectedItemId = ServletRequestUtils.getLongParameter(request, REG_PARAM_SELECTED_ITEM_ID, -1L);
        String selectedLangId = ServletRequestUtils.getStringParameter(request, "selectedLangId", "");
        long selectedStatusId = ServletRequestUtils.getLongParameter(request, "selectedStatusId", -1L);
        String contentTypeFilterValue = ServletRequestUtils.getStringParameter(request, "selectedContentTypeId", "");


        GlobalDashboardTranslationWrapper wrapper = new GlobalDashboardTranslationWrapper();

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        Map<String, String> filterMap = buildMessagepointFilterMapForTranslate(selectedItemId);

        String statusFilerValue = generateStatusFromId((int) selectedStatusId);
        if (MessagepointTreeSelectionEnum.SMART_TEXT.getId().equals(selectedItemId)) {
            contentTypeFilterValue = "ST";
        }
        if("all".equalsIgnoreCase(contentTypeFilterValue)) {
            contentTypeFilterValue = "";
        }
        List<String> selecetdLanguagesList = constructSelectedLanguagesList(selectedLangId);

        Map<String, Integer> translateAccuracyCountMap = messagepointElasticSearchHandler.computeTranslateAccuracyCount(filterMap, selecetdLanguagesList, statusFilerValue, contentTypeFilterValue);

        Integer accurateCount = buildCountValueForTranslateAccuracy(ACCURATE, translateAccuracyCountMap);
        Integer inaccurateCount = buildCountValueForTranslateAccuracy(INACCURATE, translateAccuracyCountMap);
        Integer emptyCount = buildCountValueForTranslateAccuracy(EMPTY, translateAccuracyCountMap);
        Integer notTranslatedCount = buildCountValueForTranslateAccuracy(NOT_TRANSLATED, translateAccuracyCountMap);

        List<Integer> trAccCountsList = new LinkedList<>();
        trAccCountsList.add(accurateCount);
        trAccCountsList.add(inaccurateCount);
        trAccCountsList.add(notTranslatedCount);
        trAccCountsList.add(emptyCount);

        wrapper.setGraphData(trAccCountsList);
        wrapper.setInaccurateCount(inaccurateCount);

        int totalCount = accurateCount + inaccurateCount + emptyCount + notTranslatedCount;
        int totalInaccurate = inaccurateCount + emptyCount + notTranslatedCount;
        double inaccuratePercent = totalInaccurate == 0 ? 0 : (double) (totalInaccurate * 100) / totalCount;

        String translateAccuracyDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.translation.average"),
                totalCount, totalInaccurate, String.format("%.2f", inaccuratePercent));
        wrapper.setTranslationDisplayString(translateAccuracyDisplayString);

        return wrapper;

    }
    private Map<String, String> buildMessagepointFilterMapForTranslate(long selectedItemId) {
        Map<String, String> result = new HashMap<>();
        if (MessagepointTreeSelectionEnum.ALL.getId().equals(selectedItemId)) {
            return result;
        } else if (MessagepointTreeSelectionEnum.SMART_TEXT.getId().equals(selectedItemId)) {
            result.put("attribute.type.keyword", "ST");
        }  else if (selectedItemId > 0) {
            Document touchpoint = Document.findById(selectedItemId);
            if (touchpoint != null) {
                result.put("attribute.touchpoint_guids.keyword", touchpoint.getGuid());
            }
        }

        return result;
    }
    private  Integer buildCountValueForTranslateAccuracy(TranslateAccuracyEnum accuracyEnum, Map<String, Integer> translateAccuracyMap) {
        if (MapUtils.isEmpty(translateAccuracyMap)) {
            return 0;
        }

        Integer trAccValue = translateAccuracyMap.get(accuracyEnum.getValue());
        return trAccValue != null ? trAccValue : 0;
    }
}