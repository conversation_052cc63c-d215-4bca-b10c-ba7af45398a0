package com.prinova.messagepoint.controller.dashboards;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.TranslateAccuracyEnum;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.filters.DashboardFilters;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.rationalizer.MessagepointCountsCacheEnum;
import com.prinova.messagepoint.platform.services.rationalizer.MessagepointCountsCacheHandler;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.model.SentimentEnum.NEGATIVE;
import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.constructEnabledBrandPropertiesList;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointFilterMap;
import static com.prinova.messagepoint.platform.services.elasticsearch.MessagepointTreeSelectionUtils.buildMessagepointTreeSelectionString;
import static com.prinova.messagepoint.util.SentimentUtil.buildCountValueForSentiment;

public class AsyncGlobalDashboardInfoController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncGlobalDashboardInfoController.class);
    private static final DecimalFormat FORMATTER = new DecimalFormat("###,###");
    private static final String REQ_PARAM_ACTION = "action";
    private static final String REG_PARAM_SELECTED_ITEM_ID = "selectedItemId";
    private static final String REQ_PARAM_STATS_ID = "statsId";

    private static final int ACTION_DUPLICATES_COUNT = 1;
    private static final int ACTION_SIMILARITIES_COUNT = 2;
    private static final int ACTION_READING_COMPREHENSION_COUNT = 3;
    private static final int ACTION_SENTIMENT_COUNT = 4;
    private static final int ACTION_BRAND_COUNT = 5;
    private static final int ACTION_TREE_SELECTION_INFO = 6;
    private static final int ACTION_DASHBOARD_STATS = 7;
    private static final int ACTION_DASHBOARD_STATS_ERROR_DOWNLOAD = 8;
    private static final int ACTION_TRANSLATE_ACCURACY_COUNT = 9;
    private static final String REDIRECT_PAGE_TEMPLATE = "<html>\n" +
            "<head>\n" +
            "<meta http-equiv=\"refresh\" content=\"0;url={redirectURL}\" />\n" +
            "</head>\n" +
            "<body>\n" +
            "&nbsp;\n" +
            "</body>\n" +
            "</html>";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();
        int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, 0);

        String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
        MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
        MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

        if (action == ACTION_DASHBOARD_STATS_ERROR_DOWNLOAD) {
            handleDashboardStatsErrorDownload(request, response, messagepointElasticSearchHandler);

            return null;
        }

        int selectedItemId = ServletRequestUtils.getIntParameter(request, REG_PARAM_SELECTED_ITEM_ID, 0);
        Map<String, String> filterMap = buildMessagepointFilterMap(selectedItemId);

        if (action == ACTION_DASHBOARD_STATS) {
            // This is a request done periodically from JavaScript so Spring Framework could redirect to it after login.
            if (checkRedirectAfterLogin(request, response)) {
                return null;
            }

            handleDashboardStats(messagepointElasticSearchHandler, out);

            return null;
        }

        boolean elasticSearchAppExists = messagepointElasticSearchHandler.indexExists();
        final long schemaNodeId = Node.getCurrentNode().getId();
        final List<Map<String, String>> sourceTreeFiltersList = List.of(filterMap);

        if (action == ACTION_DUPLICATES_COUNT) {
            try {
                SystemPropertyManager manager = SystemPropertyManager.getInstance();
                List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);

                int duplicatesCount = 0;
                MessagepointCountsCacheHandler messagepointCountsCacheHandler =
                        MessagepointCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .schemaNodeId(schemaNodeId)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .messagepointElasticSearchHandler(messagepointElasticSearchHandler)
                                .dashboardFiltersHash(new DashboardFilters(dashboardFilterList).calculateHash())
                                .countsCacheEnum(MessagepointCountsCacheEnum.MESSAGEPOINT_DUPLICATES_COUNT)
                                .countLabel("exact matches count")
                                .build();

                Integer cachedCount = (Integer) messagepointCountsCacheHandler.readCount();
                if (cachedCount != null) {
                    duplicatesCount = cachedCount;
                } else {
                    Map<GlobalDashboardContentDto, Integer> duplicatesMap = messagepointElasticSearchHandler.searchDuplicatesWithFilterMap(filterMap, dashboardFilterList);
                    if (!MapUtils.isEmpty(duplicatesMap)) {
                        duplicatesCount = duplicatesMap.size();
                    }

                    if (elasticSearchAppExists) {
                        messagepointCountsCacheHandler.saveCount(duplicatesCount);
                    }
                }

                writePropertyInOutput(out, "duplicatesCount", FORMATTER.format(duplicatesCount));
            } catch (Exception e) {
                log.error("Error: Unable to retrieve duplicates dashboard count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_READING_COMPREHENSION_COUNT) {
            try {
                SystemPropertyManager manager = SystemPropertyManager.getInstance();
                List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.readability);

                Number readingAverage = 0.0f;
                MessagepointCountsCacheHandler messagepointCountsCacheHandler =
                        MessagepointCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .schemaNodeId(schemaNodeId)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .messagepointElasticSearchHandler(messagepointElasticSearchHandler)
                                .dashboardFiltersHash(new DashboardFilters(dashboardFilterList).calculateHash())
                                .countsCacheEnum(MessagepointCountsCacheEnum.MESSAGEPOINT_READABILITY_COUNT)
                                .countLabel("readability count")
                                .build();

                Number cachedCount = messagepointCountsCacheHandler.readCount();
                if (cachedCount != null) {
                    readingAverage = cachedCount;
                } else {
                    readingAverage = messagepointElasticSearchHandler.computeReadabilityFleschReadingAverageWithFilterMap(filterMap, dashboardFilterList);

                    if (elasticSearchAppExists) {
                        messagepointCountsCacheHandler.saveCount(readingAverage);
                    }
                }

                String readingString = String.format("%.2f", readingAverage == null ? 0.0f : readingAverage.floatValue());
                writePropertyInOutput(out, "readingCount", readingString);
            } catch (Exception e) {
                log.error("Error: Unable to retrieve reading comprehension dashboard count response: " + e.getMessage(), e);
            }

        } else if (action == ACTION_SENTIMENT_COUNT) {
            try {
                SystemPropertyManager manager = SystemPropertyManager.getInstance();
                List<DashboardFilter> dashboardFilterList = manager.getDashboardFiltersForSection(DashboardFilter.Section.sentiment);

                Integer negativeSentimentCount = 0;
                MessagepointCountsCacheHandler messagepointCountsCacheHandler =
                        MessagepointCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .schemaNodeId(schemaNodeId)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .messagepointElasticSearchHandler(messagepointElasticSearchHandler)
                                .dashboardFiltersHash(new DashboardFilters(dashboardFilterList).calculateHash())
                                .countsCacheEnum(MessagepointCountsCacheEnum.MESSAGEPOINT_SENTIMENT_COUNT)
                                .countLabel("negative sentiment count")
                                .build();

                Integer cachedCount = (Integer) messagepointCountsCacheHandler.readCount();
                if (cachedCount != null) {
                    negativeSentimentCount = cachedCount;
                } else {
                    Map<String, Integer> sentimentMap = messagepointElasticSearchHandler.computeSentimentWithFilterMap(filterMap, dashboardFilterList);
                    negativeSentimentCount = buildCountValueForSentiment(NEGATIVE, sentimentMap);

                    if (elasticSearchAppExists) {
                        messagepointCountsCacheHandler.saveCount(negativeSentimentCount);
                    }
                }

                writePropertyInOutput(out, "sentimentCount", FORMATTER.format(negativeSentimentCount));
            } catch (Exception e) {
                log.error("Error: Unable to retrieve sentiment dashboard count response: " + e.getMessage(), e);
            }

        } else if (action == ACTION_BRAND_COUNT) {
            try {
                int brandCount = 0;

                MessagepointCountsCacheHandler messagepointCountsCacheHandler =
                        MessagepointCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .schemaNodeId(schemaNodeId)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .messagepointElasticSearchHandler(messagepointElasticSearchHandler)
                                .countsCacheEnum(MessagepointCountsCacheEnum.MESSAGEPOINT_BRAND_COUNT)
                                .countLabel("brand count")
                                .build();

                Integer cachedCount = (Integer) messagepointCountsCacheHandler.readCount();
                if (cachedCount != null) {
                    brandCount = cachedCount;
                } else {
                    BrandProfile brandProfile = BrandProfile.findPrimaryProfile();
                    List<String> enabledBrandProfileProperties = constructEnabledBrandPropertiesList(brandProfile);
                    Map<String, Integer> brandViolationsCountsMap = messagepointElasticSearchHandler.computeBrandViolationsCountsWithMetadataFilter(enabledBrandProfileProperties, filterMap);
                    if (!MapUtils.isEmpty(brandViolationsCountsMap)) {
                        brandCount = brandViolationsCountsMap.values().stream().mapToInt(Integer::intValue).sum();
                    }

                    if (elasticSearchAppExists) {
                        messagepointCountsCacheHandler.saveCount(brandCount);
                    }
                }

                writePropertyInOutput(out, "brandCount", FORMATTER.format(brandCount));
            } catch (Exception e) {
                log.error("Error: Unable to retrieve brand dashboard count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_TRANSLATE_ACCURACY_COUNT) {
            try {
                int translateInaccuracyTotalCount;
                MessagepointCountsCacheHandler messagepointCountsCacheHandler =
                        MessagepointCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .schemaNodeId(schemaNodeId)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .messagepointElasticSearchHandler(messagepointElasticSearchHandler)
                                .countsCacheEnum(MessagepointCountsCacheEnum.MESSAGEPOINT_TRANSLATE_ACCURACY_COUNT)
                                .countLabel("translate inaccurate count")
                                .build();

                Integer cachedCount = (Integer) messagepointCountsCacheHandler.readCount();
                if (cachedCount != null) {
                    translateInaccuracyTotalCount = cachedCount;
                } else {
                    List<String> selecetdLanguagesList = new ArrayList<>();
                    for(MessagepointLocale mp : TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales()) {
                        selecetdLanguagesList.add(mp.getCode());
                    }

                    Map<String, Integer> translateAccuracyCountMap = messagepointElasticSearchHandler.computeTranslateAccuracyCount(filterMap, selecetdLanguagesList, "", "");
                    int translateInaccuracyCount = buildCountValueForTranslateAccuracy(TranslateAccuracyEnum.INACCURATE, translateAccuracyCountMap);
                    int translateEmptyCount = buildCountValueForTranslateAccuracy(TranslateAccuracyEnum.EMPTY, translateAccuracyCountMap);
                    translateInaccuracyTotalCount = translateInaccuracyCount + translateEmptyCount;
                    if(elasticSearchAppExists) {
                        messagepointCountsCacheHandler.saveCount(translateInaccuracyTotalCount);
                    }
                }

                writePropertyInOutput(out, "translateAccuracyCount", FORMATTER.format(translateInaccuracyTotalCount));
            } catch (Exception e) {
                log.error("Error: Unable to retrieve translate accuracy count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_TREE_SELECTION_INFO) {
            try {
                writePropertyInOutput(out, "titleSelection", buildMessagepointTreeSelectionString(selectedItemId));
            } catch (Exception e) {
                log.error("Error: Unable to retrieve brand dashboard count response: " + e.getMessage(), e);
            }
        }
        return null;
    }
    private  Integer buildCountValueForTranslateAccuracy(TranslateAccuracyEnum accuracyEnum, Map<String, Integer> translateAccuracyMap) {
        if (MapUtils.isEmpty(translateAccuracyMap)) {
            return 0;
        }

        Integer trAccValue = translateAccuracyMap.get(accuracyEnum.getValue());
        return trAccValue != null ? trAccValue : 0;
    }

    private void handleDashboardStats(
            MessagepointElasticSearchHandler messagepointElasticSearchHandler,
            ServletOutputStream out
    ) {
        try {
            JsonObject marcieStatsJsonObject = messagepointElasticSearchHandler.loadMarcieStats();

            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty("elasticSearchApplicationId", messagepointElasticSearchHandler.getElasticAppId())
                    .add("stats", marcieStatsJsonObject)
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to retrieve dashboard stats response: " + e.getMessage(), e);
        }
    }

    private boolean checkRedirectAfterLogin(HttpServletRequest request, HttpServletResponse response) {
        String referer = request.getHeader("Referer");
        if (StringUtils.isEmpty(referer)) {
            return false;
        }

        // Only Global Dashboard pages are allowed to execute this Ajax call.
        // The below regex will match the following urls and other similar that will be created in the future:
        //        global_dashboard.form
        //        global_dashboard_duplicates.form
        //        global_dashboard_reading.form
        //        global_dashboard_sentiment.form
        //        global_dashboard_brand.form
        if (referer.matches("^.*?/global_dashboard.*$")) {
            return false;
        }

        StringBuilder dashboardUrlBuilder = new StringBuilder();
        dashboardUrlBuilder.append("dashboards/global_dashboard.form");
        Map<String, String> params = new LinkedHashMap<>(){{
            put("tk", request.getParameter("tk"));
        }};
        StringBuilder dashboardParamsBuilder = new StringBuilder();
        params.forEach((key, value) -> {
            if (StringUtils.isEmpty(value)) {
                return;
            }

            if (dashboardParamsBuilder.isEmpty()) {
                dashboardParamsBuilder.append("?");
            } else {
                dashboardParamsBuilder.append("&");
            }

            dashboardParamsBuilder.append(key).append("=").append(value);
        });
        dashboardUrlBuilder.append(dashboardParamsBuilder);

        String responseText = StringUtils.replace(REDIRECT_PAGE_TEMPLATE, "{redirectURL}", dashboardUrlBuilder.toString());

        returnTextHtml(response, responseText);

        return true;
    }

    private void returnTextHtml(HttpServletResponse response, String responseString) {
        try {
            response.setContentType("text/html");

            ServletOutputStream out = response.getOutputStream();

            out.write(responseString.getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to return text/html.", e);
        }
    }

    private void handleDashboardStatsErrorDownload(
            HttpServletRequest request,
            HttpServletResponse response,
            MessagepointElasticSearchHandler messagepointElasticSearchHandler
    ) {
        try {
            String statsId = ServletRequestUtils.getStringParameter(request, REQ_PARAM_STATS_ID, "");

            String resultError = StringUtils.isEmpty(statsId) ? StringUtils.EMPTY :
                    messagepointElasticSearchHandler.loadMarcieStatsException(statsId);
            byte[] binaryContent = resultError.getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING);

            response.setContentType("text/html");
            response.setContentLength( binaryContent.length );
            response.setHeader( "Content-disposition", "attachment; filename=\"" + statsId + "_exception.text\"" );

            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(binaryContent);
            outputStream.flush();
        } catch (Exception e) {
            log.error("Error: Unable to retrieve global dashboard stats response.", e);
        }
    }

    private void writePropertyInOutput(ServletOutputStream out, String propertyName, String propertyValue) throws IOException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty(propertyName, propertyValue);
        out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
        out.flush();
    }
}
