package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.search.GlobalSearchTargetType;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.lang3.StringUtils;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.constructHtmlTextStringApplyingHighlights;

public class GlobalDashboardTagUtil {

    private GlobalDashboardTagUtil(){}

    public static GlobalDashboardVOModel createModel(GlobalDashboardContentDto crtContentDto) {
        String contentId = crtContentDto.getElasticSearchId();
        String[] contentIdSpl = contentId.split("_");
        String modelType = contentIdSpl[0];
        String modelGuid = contentIdSpl[1];
        String pgtnGuid = contentIdSpl[2];
//				String langCodeId = contentIdSpl[3];
        String status = contentIdSpl[4];
        String contentGuid = crtContentDto.getGuid();
        String assetInstanceName = "";
        String instanceStatus = "";
        int instanceStatusId = 0;

        Content content = Content.findByGuid(contentGuid);
        if (content != null) {
            GlobalDashboardVOModel modelItem = new GlobalDashboardVOModel();
            int objTypeId = 0;
            ContentObject contentObject = ContentObject.findByGuid(modelGuid);

            if (contentObject == null) {
                return null;
            }

            if(contentObject.isStructuredContentEnabled() && contentObject.getDocument().isEnabledForVariantWorkflow() && !pgtnGuid.equals("0")){
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findByGuid(pgtnGuid);
                if(pgtn != null) {
                    TouchpointSelection ts = TouchpointSelection.findByPgTreeNodeId(pgtn.getId());
                    if(status.equals("WC") && ts.getHasWorkingCopy()) {
                        instanceStatus = VersionStatus.findById(VersionStatus.VERSION_WIP).getLocaledString();
                        instanceStatusId = 1;
                    } else if(status.equals("Active") && ts.getHasActiveCopy()) {
                        instanceStatus = VersionStatus.findById(VersionStatus.VERSION_PRODUCTION).getLocaledString();
                        instanceStatusId = 2;
                    }
                }
            } else {
                ContentObjectData cod = null;
                if (status.equals("WC") && contentObject.hasWorkingData()) {
                    cod = contentObject.getContentObjectData(ContentObject.DATA_TYPE_WORKING);
                } else if (status.equals("Active") && contentObject.hasActiveData()) {
                    cod = contentObject.getContentObjectData(ContentObject.DATA_TYPE_ACTIVE);
                }

                if (cod == null) {
                    // As delete is not implemented yet, it might be the case that the WC is still in elasticsearch
                    // but not in db
                    return null;
                }

                instanceStatus = cod.getStatusDisplay();
                instanceStatusId = cod.isWorking() ? 1 : 2;
            }

            assetInstanceName = contentObject.getName();

            modelItem.setObjId(contentObject.getId());
            if (contentObject.isMessage()) {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_MESSAGE);
            } else if (contentObject.isGlobalSmartText()) {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_SMART_TEXT);
            } else {
                modelItem.setObjTypeId(GlobalSearchTargetType.ID_LOCAL_SMART_TEXT);
            }

            //modelItem.setId(contentId.replaceAll("_","-"));
            modelItem.setObjName(assetInstanceName);
            modelItem.setText(content.getUnformattedText());
            modelItem.setContentId(content.getId());
            modelItem.setMarcieID(contentId);

            if (!pgtnGuid.equals("0")) {
                modelItem.setPgtnGuid(pgtnGuid);
            }

            modelItem.setObjectStatus(instanceStatus);
            modelItem.setObjectStatusId(instanceStatusId);
            modelItem.setContentSearch(true);

            return modelItem;

        }

        return null;
    }

    public static String computeNameTag(GlobalDashboardVOModel model, Boolean showMarkup, BrandProfile brandProfile, BrandProfileEnum selectedBrand) {
        if (!model.isContentSearch()) {
            boolean applyVersion = model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_SMART_TEXT ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_IMAGE ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_SMART_TEXT ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_IMAGE;

            String nameHTML = "";
            if (applyVersion) {
                String objName = model.getObjName();
                long objId = model.getObjId();
                ContentObject contentObject = ContentObject.findById(objId);
                if (contentObject.hasWorkingData() && contentObject.hasActiveData()) {
                    return "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"workingCopyLink_" + objId + "\" class=\"far table-icon workingCopyIconDiv fa-pencil ml-3\"></i>" +
                            "<i id=\"activeLink_" + objId + "\" class=\"far table-icon activeIconDiv fa-check ml-2\"></i>" +
                            "</div>";
                }else if (contentObject.hasActiveData()){
                    nameHTML += "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"activeLink_" + objId + "\" class=\"far table-icon activeIconDiv fa-check ml-2\"></i>" +
                            "</div>";
                } else {
                    nameHTML += "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"workingCopyLink_" + objId + "\" class=\"far table-icon workingCopyIconDiv fa-pencil ml-2\"></i>" +
                            "</div>";
                }
            } else {
                nameHTML = "<div>" +
                        "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                        model.getObjName() +
                        "</a>" +
                        "</div>";
            }
            return nameHTML;
        } else {
            String divider = "<span style=\"padding: 0px 8px;\">|</span>";

            StringBuilder cardHTML = new StringBuilder();
            cardHTML.append("<div style=\"white-space: normal !important;\">");

            // Tooltip: Touchpoint name for messages
            String tooltip = "";
            Document touchpoint = null;

            ContentObject contentObject = null;
            if (model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE) {
                contentObject = ContentObject.findByIdWorkingDataFocusCentric(model.getObjId());
                if (contentObject != null) {
                    tooltip = " title=\"" + contentObject.getDocument().getName() + "\" data-toggle=\"tooltip\" ";
                    touchpoint = contentObject.getDocument();
                }
            } else {
                contentObject = ContentObject.findByIdActiveDataFocusCentric(model.getObjId());
                if (contentObject != null && contentObject.getVisibleDocuments() != null && !contentObject.getVisibleDocuments().isEmpty()) {
                    touchpoint = contentObject.getVisibleDocuments().iterator().next();
                }
            }

            String objectNameDisplay = "<a href=\"#\" " + generateOnClickLink(model) + ">" + model.getObjName() + "</a>";

            // Name
            cardHTML.append("	<div ").append(tooltip).append(" style=\"display: inline-block; max-width: 90%; min-width: 90%; vertical-align: top; font-weight: 700; margin-bottom: 4px; white-space: normal;\">").append(objectNameDisplay).append("	</div>");

            cardHTML.append("</div>");

            // Content
            if (model.getText() != null) {
                cardHTML.append("<div id=\"resultContent_").append(model.getContentId()).append("\" class=\"resultContentContainer contentContainer position-relative\" style=\"white-space: normal;\">");
                if (touchpoint != null) {
                    String html = Content.findById(model.getContentId()).getContent(touchpoint);
                    if (selectedBrand != null)
                        html = constructHtmlTextStringApplyingHighlights(showMarkup, html, brandProfile, selectedBrand);
                    cardHTML.append(html);
                } else {
                    String html = Content.findById(model.getContentId()).getContent();
                    if (selectedBrand != null)
                        html = constructHtmlTextStringApplyingHighlights(showMarkup, html, brandProfile, selectedBrand);
                    cardHTML.append(html);
                }
                cardHTML.append("</div>");
            }

            cardHTML.append("<div style=\"white-space: normal !important; color: #777; font-weight: 100;\">");

            // Icons
            String icons = ContentObjectContentUtil.getTagIcons(contentObject);
            if (!icons.isEmpty()) {
                cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle; padding-right: 4px; opacity: 0.75;\">").append(icons).append("</div>");
            }

            // Status
            cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(ApplicationUtil.getMessage("page.label.status")).append(":</div>");
            cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(model.getObjectStatus()).append("</div>");

            // Variant
            if (model.getPgtnGuid() != null && !model.getPgtnGuid().isEmpty()) {
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findByGuid(model.getPgtnGuid());
                cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(divider).append(ApplicationUtil.getMessage("page.label.variant")).append(":</div>");
                cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(pgtn.getName()).append("</div>");
            }

            cardHTML.append("</div>");

            return cardHTML.toString();
        }
    }
    public static String computeNameTagWithContent(GlobalDashboardVOModel model, Boolean showMarkup, String compareText) throws Exception {
        if (!model.isContentSearch()) {
            boolean applyVersion = model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_SMART_TEXT ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_IMAGE ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_SMART_TEXT ||
                    model.getObjTypeId() == GlobalSearchTargetType.ID_IMAGE;

            String nameHTML = "";
            if (applyVersion) {
                String objName = model.getObjName();
                long objId = model.getObjId();
                ContentObject contentObject = ContentObject.findById(objId);
                if (contentObject.hasWorkingData() && contentObject.hasActiveData()) {
                    return "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"workingCopyLink_" + objId + "\" class=\"far table-icon workingCopyIconDiv fa-pencil ml-3\"></i>" +
                            "<i id=\"activeLink_" + objId + "\" class=\"far table-icon activeIconDiv fa-check ml-2\"></i>" +
                            "</div>";
                }else if (contentObject.hasActiveData()){
                    nameHTML += "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"activeLink_" + objId + "\" class=\"far table-icon activeIconDiv fa-check ml-2\"></i>" +
                            "</div>";
                } else {
                    nameHTML += "<div>" +
                            "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                            objName +
                            "</a>" +
                            "<i id=\"workingCopyLink_" + objId + "\" class=\"far table-icon workingCopyIconDiv fa-pencil ml-2\"></i>" +
                            "</div>";
                }
            } else {
                nameHTML = "<div>" +
                        "<a class=\"dataTableItemName d-inline-block text-truncate align-middle\" href=\"#\" " + generateOnClickLink(model) + ">" +
                        model.getObjName() +
                        "</a>" +
                        "</div>";
            }
            return nameHTML;
        } else {
            String divider = "<span style=\"padding: 0px 8px;\">|</span>";

            StringBuilder cardHTML = new StringBuilder();
            cardHTML.append("<div style=\"white-space: normal !important;\">");

            // Tooltip: Touchpoint name for messages
            String tooltip = "";
            Document touchpoint = null;

            ContentObject contentObject = null;
            if (model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE) {
                contentObject = ContentObject.findByIdWorkingDataFocusCentric(model.getObjId());
                if (contentObject != null) {
                    tooltip = " title=\"" + contentObject.getDocument().getName() + "\" data-toggle=\"tooltip\" ";
                    touchpoint = contentObject.getDocument();
                }
            } else {
                contentObject = ContentObject.findByIdActiveDataFocusCentric(model.getObjId());
                if (contentObject != null && contentObject.getVisibleDocuments() != null && !contentObject.getVisibleDocuments().isEmpty()) {
                    touchpoint = contentObject.getVisibleDocuments().iterator().next();
                }
            }

            String objectNameDisplay = "<a href=\"#\" " + generateOnClickLink(model) + ">" + model.getObjName() + "</a>";

            // Name
            cardHTML.append("	<div ").append(tooltip).append(" style=\"display: inline-block; max-width: 90%; min-width: 90%; vertical-align: top; font-weight: 700; margin-bottom: 4px; white-space: normal;\">").append(objectNameDisplay).append("	</div>");

            cardHTML.append("</div>");

            // Content
            if (model.getText() != null) {
                cardHTML.append("<div id=\"resultContent_").append(model.getContentId()).append("\" class=\"resultContentContainer contentContainer position-relative\" style=\"white-space: normal;\">");
                if (touchpoint != null) {
                    String html = Content.findById(model.getContentId()).getContent(touchpoint);
                    if(showMarkup) {
                        html = StringsDiffUtils.diff(compareText, html);
                    }
                    cardHTML.append(html);
                } else {
                    String html = Content.findById(model.getContentId()).getContent();
                    if(showMarkup) {
                        html = StringsDiffUtils.diff(compareText, html);
                    }
                    cardHTML.append(html);
                }
                cardHTML.append("</div>");
            }

            cardHTML.append("<div style=\"white-space: normal !important; color: #777; font-weight: 100;\">");

            // Icons
            String icons = ContentObjectContentUtil.getTagIcons(contentObject);
            if (!icons.isEmpty()) {
                cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle; padding-right: 4px; opacity: 0.75;\">").append(icons).append("</div>");
            }

            // Status
            cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(ApplicationUtil.getMessage("page.label.status")).append(":</div>");
            cardHTML.append("	<div style=\"display: inline-block; vertical-align: middle;\">").append(model.getObjectStatus()).append("</div>");

            // Variant
            if (model.getPgtnGuid() != null && !model.getPgtnGuid().isEmpty()) {
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findByGuid(model.getPgtnGuid());
                cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(divider).append(ApplicationUtil.getMessage("page.label.variant")).append(":</div>");
                cardHTML.append("<div style=\"display: inline-block; vertical-align: middle;\">").append(pgtn.getName()).append("</div>");
            }

            cardHTML.append("</div>");

            return cardHTML.toString();
        }
    }


    private static String generateOnClickLink(GlobalDashboardVOModel model) {
        String onClickLink = "";

        if (model.getObjTypeId() == GlobalSearchTargetType.ID_MESSAGE ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_SMART_TEXT ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_LOCAL_IMAGE ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_SMART_TEXT ||
                model.getObjTypeId() == GlobalSearchTargetType.ID_IMAGE) {

            ContentObject contentObject = ContentObject.findById(model.getObjId());
            ParameterGroupTreeNode pgtn = Content.findPGTN(model.getContentId());

            String pgtnParam = "";
            if (pgtn != null) {
                if (contentObject.isDynamicVariantEnabled()) {
                    pgtnParam = "&paramInstId=" + pgtn.getId();
                } else if (contentObject.isStructuredContentEnabled()) {
                     TouchpointContentObjectContentSelection contentSelection = new TouchpointContentObjectContentSelection(contentObject, pgtn, null);
                    pgtnParam = "&contentSelectionId=" + contentSelection.getId();
                }
            }

            if (contentObject.isGlobalSmartText())
            {
                long documentId = -1;
                if(contentObject.getDocuments() != null && !contentObject.getDocuments().isEmpty()){
                    documentId = contentObject.getDocuments().iterator().next().getId();
                }
                onClickLink = "onclick=\"javascript:crossLinkOpen('content/global_content_list.form?documentId=" + documentId + "&contentObjectId=" + contentObject.getId() +
                        pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2") + "&isFreeform=" + contentObject.getIsSharedFreeform() + "'); return false;\"";
            } else if (contentObject.getIsTouchpointLocal()) {
                onClickLink = "onclick=\"javascript:crossLinkOpen('touchpoints/local_content_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() + "&localContext=1" +
                        pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? " 1" : "2") + "'); return false;\"";
            } else {
                onClickLink = "onclick=\"javascript:crossLinkOpen('touchpoints/touchpoint_content_object_list.form?documentId=" + contentObject.getDocument().getId() + "&contentObjectId=" + contentObject.getId() +
                        pgtnParam + "&statusViewId=" + (contentObject.hasWorkingData() ? "1" : "2") + "'); return false;\"";
            }

        } else if (model.getObjTypeId() == GlobalSearchTargetType.ID_VARIABLE) {
            DataElementVariable variable = DataElementVariable.findById(model.getObjId());
            String tpParam = "";
            if (!variable.getDocuments().isEmpty()) {
                tpParam = "&documentId=" + variable.getDocuments().iterator().next().getId();
            }
            onClickLink = "onclick=\"javascript:crossLinkOpen('dataadmin/variable_list.form?variableId=" + variable.getId() + tpParam +
                    "'); return false;\"";
        } else if (model.getObjTypeId() == GlobalSearchTargetType.ID_TARGET_GROUP) {
            onClickLink = "onclick=\"javascript:crossLinkOpen('dataadmin/target_group_edit.form?targetgroupid=" + model.getObjId() + "&newtgedit=true" +
                    "'); return false;\"";
        } else if (model.getObjTypeId() == GlobalSearchTargetType.ID_RULE) {
            onClickLink = "onclick=\"javascript:crossLinkOpen('dataadmin/condition_element_edit.form?elementid=" + model.getObjId() +
                    "'); return false;\"";
        }

        return onClickLink;
    }

    public static String constructLocaleSelectionOnClick(ContentObject contentObject, String selectedLanguageId) {
        if(contentObject == null || StringUtils.isEmpty(selectedLanguageId)) {
            return "";
        }
        String languageSelection = "  var contentLanguages = JSON.parse(localStorage.getItem('content-language')) || [];\n" +
                "var index = contentLanguages.findIndex(e => e.contentObjectId == " + contentObject.getId() + "	);\n" +
                "if (index !== -1) {\n" +
                "contentLanguages[index].language = '" + selectedLanguageId + "';\n" +
                "} else {\n" +
                "var contentObjectId = '" + contentObject.getId() + "'; var language = '" + selectedLanguageId + "';" +
                "contentLanguages.push({contentObjectId, language});\n" +
                "}\n" +
                "localStorage.setItem('content-language',JSON.stringify(contentLanguages));";
        return languageSelection;
    }
}
