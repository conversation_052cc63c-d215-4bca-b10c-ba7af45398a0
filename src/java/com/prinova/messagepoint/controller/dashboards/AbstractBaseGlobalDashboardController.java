package com.prinova.messagepoint.controller.dashboards;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class AbstractBaseGlobalDashboardController extends MessagepointController {

    private static final Log log = LogUtil.getLog(AbstractBaseGlobalDashboardController.class);

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        referenceData.put( "default_css"				, ContentStyleUtils.generateDefaultCSS(ContentStyleUtils.CONTENT_TYPE_VIEW));

        return referenceData;
    }

}