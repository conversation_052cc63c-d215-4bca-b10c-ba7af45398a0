package com.prinova.messagepoint.controller.multipart;

import com.prinova.messagepoint.controller.content.ContentObjectContentEditValidator;
import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SubContentType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 
 * Prinova Inc. 1998-2011 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6 All
 * rights reserved.
 * 
 * MultipartContentUtility
 * 
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class MultipartContentUtility {

	/**
	 * Validates the MultipartContent for both TEXT and GRAPHIC contentType can
	 * be TEXT = 1 or GRAPHIC = 2
	 */
	public static void validateMultipartContent(Errors errors, Long contentType, long subContentType, Map<Long, ContentVO> zoneContents, Map<Long, Boolean> imagesToConsider,
			boolean isSubLevel, boolean isDeliveredExactTargetTP) {

		if (contentType == null) {
			errors.reject("error.message.contenttypeinvalid", "Content type is invalid");
			return;
		}

		// check the contentType for TEXT = 1 or GRAPHIC = 2
		if (contentType.intValue() == ContentType.TEXT) {
			validateText(errors, zoneContents, isSubLevel);
		}
		// GRAPHIC
		else if (contentType.intValue() == ContentType.GRAPHIC) {
			validateGraphic(errors, zoneContents, imagesToConsider, subContentType, isSubLevel, isDeliveredExactTargetTP);
		}
	}

	/**
	 * Validates the content for all languages. If check box is checked skips the validation
	 * 
	 */
	private static void validateText(Errors errors, Map<Long, ContentVO> zoneContents, boolean isSubLevel) {
		// key is language code
		for (Long localeId : zoneContents.keySet()) {
			ContentVO vo = zoneContents.get(localeId);

			if (vo.isSameAsDefault() || isSubLevel)
			{
					continue;
			}

			String value = vo.getContent();
			String trimmedValue = ContentObjectContentEditValidator.removeSpacesFromContent(value);
			boolean hasContent = (trimmedValue == null) ? false : !StringUtils.isBlank(trimmedValue.trim());

			if (hasContent) {
				if (ContentObjectContentUtil.hasForbiddenCharacters(trimmedValue)) {
					errors.reject("error.message.invalid.text.content");
				}
			}
		}
	}

	/**
	 * Validates the file for all 3 languages. If check box is checked skips the
	 * validation, except for the English Validates the extension of the file is
	 * JPG
	 */
	private static void validateGraphic(Errors errors, Map<Long, ContentVO> zoneContents, Map<Long, Boolean> imagesToConsider, long subTypeId,
			boolean isSubLevel, boolean isDeliveredExactTargetTP) {

		long subType = 0;
		boolean firstFlag = true;
		List<String> appliedImageNames = new ArrayList<>();
		
		for (Long localeId : zoneContents.keySet()) {
			ContentVO vo = zoneContents.get(localeId);
			
			// Enforce the unique applied image name
			boolean sameAsDefault = vo.isShared()?vo.isGraphicSameAsDefault():vo.isSameAsDefault();
			boolean sameAsParent = false;
			if(vo.getMessageId() > 0){
				ContentObject contentObject = HibernateUtil.getManager().getObject(ContentObject.class, vo.getMessageId());
				if(contentObject.isDynamicVariantEnabled() || contentObject.isStructuredContentEnabled()){
					sameAsParent = vo.isReference();
				}
			}
			String appliedImageName = vo.getAppliedImageFilename();
			if(!sameAsDefault && appliedImageName != null && !appliedImageName.trim().isEmpty() && !sameAsParent && !vo.isUseImageLibrary()){
				if(appliedImageNames.contains(appliedImageName)){
					errors.reject("error.message.duplicateAppliedImageName", new String[] { appliedImageName }, "");
					break;
				}else{
					appliedImageNames.add(appliedImageName);
				}
			}
			
			if (sameAsDefault) {
				continue;
			}

			Boolean considerFile = imagesToConsider.get(localeId);
			if (considerFile) {
				MultipartFile file = vo.getFile();
				if (file != null)
					MessagepointInputValidationUtil.validateGraphicFileNameValue(ApplicationUtil.getMessage("page.label.file.name"), file.getOriginalFilename(), errors);

				boolean fileEmpty = ((file == null) || (file.isEmpty()) || (file.getOriginalFilename() == null) || (file.getOriginalFilename().isEmpty()));

				if(!fileEmpty) {
					String languageCode = MessagepointLocale.findById(localeId).getLanguageCode();
					if (SubContentType.isGraphicJPEG(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicTIFF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicPDF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicRTF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicGIF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicIMG(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicPNG(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicDXF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicDLF(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicDOCX(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicEPS(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else if (SubContentType.isGraphicPSEG(subTypeId)) {
						// check file extension
						if (!SubContentType.isGraphicFileSubTypeCompatible(file.getOriginalFilename(), subTypeId))
							errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
					} else {
						if (firstFlag) {
							subType = SubContentType.getGraphicSubTypeIdByFileName(file.getOriginalFilename());
							firstFlag = false;
						} else {
							long nextType = SubContentType.getGraphicSubTypeIdByFileName(file.getOriginalFilename());
							if (nextType != subType)
								errors.reject("error.message.imagefile.not.compatible" + "." + languageCode);
						}
					}
					// Check the graphic file size for the exactTarget TP
		    		if(isDeliveredExactTargetTP){
		    			if(vo.getFile().getSize() > ExactTargetConfiguration.MAX_UPLOAD_IMAGE_SIZE_KB * 1000){
		    				errors.reject("error.message.graphicfilesize.too.large", new String[] { vo.getFile().getOriginalFilename(), Long.toString(ExactTargetConfiguration.MAX_UPLOAD_IMAGE_SIZE_KB) }, "");
		    				break;
		    			}
		    		}
				}
			}
		}
	}
}
