package com.prinova.messagepoint.controller;

import java.text.ParseException;
import java.util.*;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import javax.persistence.criteria.JoinType;

import com.prinova.messagepoint.controller.AsyncParagraphStyleListVO.ParagraphStyleListVOFlags;
import com.prinova.messagepoint.model.font.BorderType;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.font.LineSpacingType;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.wrapper.AsyncAbstractListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.query.handler.ParagraphStyleAlignmentHandler;
import com.prinova.messagepoint.query.handler.ParagraphStyleIndentationHandler;
import com.prinova.messagepoint.query.handler.ParagraphStyleLineSpacingHandler;
import com.prinova.messagepoint.query.handler.PostQueryHandler;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;

public class AsyncParagraphStyleListWrapper extends AsyncAbstractListWrapper {

	private static final Log log = LogUtil.getLog(AsyncParagraphStyleListWrapper.class);

	private List<ParagraphStyle> paragraphStyleList;
	
	public AsyncParagraphStyleListWrapper(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
		this.paragraphStyleList = new ArrayList<>();
		this.buildItemsList(sSearch, orderByMap, pageSize, pageIndex);
	}

	private void buildItemsList(String sSearch, Map<String, String> orderByMap, int pageSize, int pageIndex) {
		// Alias init
        Map<String, String> firstLevelJoinAlias 		= new LinkedHashMap<>();
        Map<String, String> secondLevelJoinAlias 		= new LinkedHashMap<>();
        
        // MessagepointCriterion init
        List<MessagepointCriterion> firstLevelCriterionList 	= new ArrayList<>();
        List<MessagepointCriterion> secondLevelCriterionList 	= new ArrayList<>();
        
        // Join Type init
        Map<String, JoinType> secondLevelJoinType	= new HashMap<>();
        
        // MessagepointOrder list init
        List<MessagepointOrder> orderList 						= new ArrayList<>();
        
        // Search for the variable name
        if(sSearch != null && !sSearch.isEmpty() && !sSearch.equals("NULL")){
        	firstLevelCriterionList.add( MessagepointRestrictions.ilike("name", "%" + sSearch + "%") );
        }
        
        // Sort
        this.addTableColumnsSort(orderByMap, orderList);
        
        PostQueryHandler postHandler = null;
        
        // Sort by paragraph style's alignment (Post-Sort)
        if(orderByMap.containsKey("alignment")){
        	ParagraphStyleAlignmentHandler paraStyleAlignmentHandler = new ParagraphStyleAlignmentHandler();
        	paraStyleAlignmentHandler.setOrderAsc(orderByMap.get("alignment").equals("asc"));
        	postHandler = paraStyleAlignmentHandler;	            	
        }
        
        // Sort by paragraph style's alignment (Post-Sort)
        if(orderByMap.containsKey("linespacing")){
        	ParagraphStyleLineSpacingHandler paraStyleLineSpacingHandler = new ParagraphStyleLineSpacingHandler();
        	paraStyleLineSpacingHandler.setOrderAsc(orderByMap.get("linespacing").equals("asc"));
        	postHandler = paraStyleLineSpacingHandler;	            	
        }
        
        // Sort by paragraph style's indentation (Post-Sort)
        if(orderByMap.containsKey("indentation")){
        	ParagraphStyleIndentationHandler paraStyleIndentHandler = new ParagraphStyleIndentationHandler();
        	paraStyleIndentHandler.setOrderAsc(orderByMap.get("indentation").equals("asc"));
        	postHandler = paraStyleIndentHandler;	            	
        }
        
        ServiceExecutionContext context = HibernatePaginationService.createContext(ParagraphStyle.class, null, firstLevelCriterionList, secondLevelCriterionList, firstLevelJoinAlias, secondLevelJoinAlias, secondLevelJoinType, pageIndex, pageSize, orderList, null, postHandler);
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();
        List<?> list = serviceResponse.getPage().getList();

        for (Object o : list) {
        	if(o instanceof ParagraphStyle) {
        		ParagraphStyle paragraphStyle = (ParagraphStyle) o;
        		this.paragraphStyleList.add(paragraphStyle);
            }
        }
		
		super.setiTotalRecords(serviceResponse.getPage().getRowCount());
		super.setiTotalDisplayRecords(serviceResponse.getPage().getRowCount()); 		
	}

	/**
	 * Add the sort criterion from the list table plug-in to the order list
     */
	private void addTableColumnsSort(Map<String, String> orderedByMap, List<MessagepointOrder> orderList){
		Set<String> keySet = orderedByMap.keySet();
		for(String key : keySet){
			String value = orderedByMap.get(key);
			String sortField = "";
			
			if(key.equals("name")){	// Sort by name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".name";
			}else if(key.equals("connectorname")){	// Sort by connector name
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".connectorName";
			}else if(key.equals("border")){	// Sort by border type id
				sortField = HibernateObjectManager.MODEL_ALIAS_PREFIX + ".borderTypeId";
			}
			
			if(!sortField.isEmpty()){
				// Add to order list
				if(value.equals("asc")){
					orderList.add(MessagepointOrder.asc(sortField));
				}else{
					orderList.add(MessagepointOrder.desc(sortField));
				}
			}		
		}
	}
	
	@Override
	public void init() {
		List<AsyncParagraphStyleListVO> aaData 		= new ArrayList<>();

		for (ParagraphStyle paragraphStyle : this.paragraphStyleList) {
			AsyncParagraphStyleListVO vo = new AsyncParagraphStyleListVO();
			vo.setDisplayMode(getDisplayMode());
			vo.setParagraphStyle(paragraphStyle);

			// For drill-down
			vo.setDT_RowId(paragraphStyle.getId());
			
			// Paragraph Properties
			vo.setName(paragraphStyle.getName());
			vo.setConnectorName(paragraphStyle.getConnectorName());
			if ( paragraphStyle.isToggleAlignment() ) {
				vo.setAlignment(ApplicationUtil.getMessage("page.label.selectable"));
			} else {
				vo.setAlignment(paragraphStyle.getAlignment().getName());
			}
			
			if ( paragraphStyle.isToggleLineSpacing() ) {
				vo.setLineSpacing(ApplicationUtil.getMessage("page.label.selectable"));
			} else {
				try {
					vo.setLineSpacing(DecimalValueUtil.dehydrate(paragraphStyle.getLineSpacing()) + 
							(paragraphStyle.getLineSpacingType() == LineSpacingType.ID_FIXED ? "pt" : "" ));
				} catch (ParseException e) {
					log.error("Error: ", e);
				}
			}
			vo.setLineSpacingType(paragraphStyle.getLineSpacingType());
			vo.setIndentation(paragraphStyle.getIndentAsHTMLDisplay());
			String marginLeft = paragraphStyle.isToggleLeftMargin() ? ApplicationUtil.getMessage("page.label.selectable") : paragraphStyle.getLeftMarginAsString() + "\"";
			vo.setMarginLR(marginLeft + ", " + paragraphStyle.getRightMarginAsString() + "\"");
			vo.setMarginTB(paragraphStyle.getParagraphSpacingBeforeAsString() + ", " + paragraphStyle.getParagraphSpacingAfterAsString() + "");
			vo.setBorder( ApplicationUtil.getMessage( paragraphStyle.getBorder().getId() == BorderType.ID_BORDER_NONE ? "page.label.disabled" : "page.label.enabled" ) );
			
			// Action flags
			ParagraphStyleListVOFlags flags = new ParagraphStyleListVOFlags();
			setActionFlags(paragraphStyle, flags);
			
			vo.setFlags(flags);
			aaData.add(vo);
		}
		super.setAaData(aaData);
	}

	public static void setActionFlags(ParagraphStyle paragraphStyle, ParagraphStyleListVOFlags flags) {
		boolean styleUpdatePermission	= UserUtil.isPermissionGranted(Permission.ROLE_STYLES_UPDATE);

		flags.setCanUpdate				(styleUpdatePermission);
		flags.setCanDelete				(styleUpdatePermission);
		flags.setCanClone				(styleUpdatePermission);
	}
}
