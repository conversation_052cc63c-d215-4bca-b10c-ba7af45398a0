package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;

public class RationalizerConsolidateWrapper implements Serializable {

    private static final long serialVersionUID = -288914342836914507L;

    private String viewType;

    private String similaritiesDisplayString;

    private String duplicatesDisplayString;

    private String targetContent;

    private Integer similaritiesCount;

    private Integer duplicatesCount;

    private String similaritiesIdsArrayGuid;

    private String duplicatesIdsArrayGuid;

    public String getViewType() {
        return viewType;
    }

    public void setViewType(String viewType) {
        this.viewType = viewType;
    }

    public String getSimilaritiesDisplayString() {
        return similaritiesDisplayString;
    }

    public void setSimilaritiesDisplayString(String similaritiesDisplayString) {
        this.similaritiesDisplayString = similaritiesDisplayString;
    }

    public String getDuplicatesDisplayString() {
        return duplicatesDisplayString;
    }

    public void setDuplicatesDisplayString(String duplicatesDisplayString) {
        this.duplicatesDisplayString = duplicatesDisplayString;
    }

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    public Integer getSimilaritiesCount() {
        return similaritiesCount;
    }

    public void setSimilaritiesCount(Integer similaritiesCount) {
        this.similaritiesCount = similaritiesCount;
    }

    public Integer getDuplicatesCount() {
        return duplicatesCount;
    }

    public void setDuplicatesCount(Integer duplicatesCount) {
        this.duplicatesCount = duplicatesCount;
    }

    public String getSimilaritiesIdsArrayGuid() {
        return similaritiesIdsArrayGuid;
    }

    public void setSimilaritiesIdsArrayGuid(String similaritiesIdsArrayGuid) {
        this.similaritiesIdsArrayGuid = similaritiesIdsArrayGuid;
    }

    public String getDuplicatesIdsArrayGuid() {
        return duplicatesIdsArrayGuid;
    }

    public void setDuplicatesIdsArrayGuid(String duplicatesIdsArrayGuid) {
        this.duplicatesIdsArrayGuid = duplicatesIdsArrayGuid;
    }
}