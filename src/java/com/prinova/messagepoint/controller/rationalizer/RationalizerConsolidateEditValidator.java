package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

import java.util.List;

public class RationalizerConsolidateEditValidator extends MessagepointInputValidator {

    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        RationalizerConsolidateEditWrapper wrapper = (RationalizerConsolidateEditWrapper) commandObj;
        int action = Integer.parseInt(wrapper.getActionValue());

        if (action == 1) {
            String label = ApplicationUtil.getMessage("page.label.name");
            String sharedContentName = StringUtils.strip(wrapper.getName().trim());

            MessagepointInputValidationUtil.validateStringValue(label, sharedContentName == null ? sharedContentName :
                    sharedContentName, true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);

            if (sharedContentName != null && sharedContentName.contains("  ")) {
                sharedContentName = sharedContentName.replaceAll("\\s+", " ");
            }

            List<RationalizerSharedContent> rationalizerSharedContentList = RationalizerSharedContent.findByNameAndApplication(sharedContentName, wrapper.getRationalizerApplicationId());
            RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(wrapper.getRationalizerContentId());
            RationalizerSharedContent rationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();

            RationalizerSharedContent otherRationalizerSharedContentObject = null;
            if (rationalizerSharedContent != null) {
                otherRationalizerSharedContentObject = rationalizerSharedContentList.stream()
                        .filter(el -> el.getId() != rationalizerSharedContent.getId())
                        .findFirst()
                        .orElse(null);
            }
            if ( (otherRationalizerSharedContentObject != null)
                    || (rationalizerSharedContent == null && !rationalizerSharedContentList.isEmpty())
            ) {
                errors.reject("error.rationalizer.shared.content.name.unique");
            }
        }

        if (action == 2 && StringUtils.isEmpty(wrapper.getFoundSharedObjectId())) {
            errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.shared.content")}, "");
        }
    }
}
