package com.prinova.messagepoint.controller.rationalizer;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.filters.DashboardFilters;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataPointsOfInterest;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerCountsCacheEnum;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerCountsCacheHandler;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.prinova.messagepoint.model.SentimentEnum.NEGATIVE;
import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.constructEnabledBrandPropertiesList;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MAX_SIMILARITY;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MIN_SIMILARITY_FOR_DASHBOARD;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;
import static com.prinova.messagepoint.util.SentimentUtil.buildCountValueForSentiment;
import static java.text.MessageFormat.format;

public class AsyncRationalizerDashboardInfoController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncRationalizerDashboardInfoController.class);
    private static final DecimalFormat FORMATTER = new DecimalFormat("###,###");
    private static final String REQ_PARAM_ACTION = "action";
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String REQ_PARAM_STATS_ID = "statsId";

    private static final int ACTION_DUPLICATES_COUNT = 20;
    private static final int ACTION_SIMILARITIES_COUNT = 21;
    private static final int ACTION_READING_COMPREHENSION_COUNT = 22;
    private static final int ACTION_SENTIMENT_COUNT = 23;
    private static final int ACTION_METADATA_COUNT = 24;
    private static final int ACTION_BRAND_COUNT = 25;
    private static final int ACTION_DASHBOARD_INFO = 26;
    private static final int ACTION_DASHBOARD_STATS = 27;
    private static final int ACTION_DASHBOARD_STATS_ERROR_DOWNLOAD = 28;

    private static final String TITLE_DELIMITER = " > ";
    private static final String REDIRECT_PAGE_TEMPLATE = "<html>\n" +
            "<head>\n" +
            "<meta http-equiv=\"refresh\" content=\"0;url={redirectURL}\" />\n" +
            "</head>\n" +
            "<body>\n" +
            "&nbsp;\n" +
            "</body>\n" +
            "</html>";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

        if (rationalizerApplicationId < 0) {
            returnEmptyJson(response);

            return null;
        }

        RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
        if (rationalizerApp == null) {
            returnEmptyJson(response);

            return null;
        }

        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApp, true);

        int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, 0);
        if (action == ACTION_DASHBOARD_STATS_ERROR_DOWNLOAD) {
            handleDashboardStatsErrorDownload(request, response, rationalizerElasticSearchHandler);

            return null;
        }

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        if (action == ACTION_DASHBOARD_STATS) {
            // This is a request done periodically from JavaScript so Spring Framework could redirect to it after login.
            if (checkRedirectAfterLogin(request, response)) {
                return null;
            }

            handleDashboardStats(rationalizerElasticSearchHandler, out);

            return null;
        }

        BrandProfile brandProfile = BrandProfile.getAppliedProfile(null, rationalizerApp);

        String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, "sourceDashboardCompareSelection", "-9");
        String targetDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, "targetDashboardCompareSelection", "-9");
        List<Map<String, String>> sourceTreeFiltersList = RationalizerUtil.processDashboardTreeSelections(rationalizerApplicationId, sourceDashboardCompareSelection);
        List<Map<String, String>> targetTreeFiltersList = RationalizerUtil.processDashboardTreeSelections(rationalizerApplicationId, targetDashboardCompareSelection);

        boolean elasticSearchAppExists = rationalizerElasticSearchHandler.indexExists();

        if (action == ACTION_DUPLICATES_COUNT) {
            try {
                int duplicatesCount = 0;
                int duplicatesFoundSum = 0;
                List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
                RationalizerCountsCacheHandler rationalizerDuplCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.DUPLICATES_COUNT)
                                .countLabel("exact matches count")
                                .build();
                RationalizerCountsCacheHandler rationalizerDuplTotalCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.DUPLICATES_COUNT_TOTAL)
                                .countLabel("exact matches total count")
                                .build();

                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {
                    Integer chachedDuplicatesCount = (Integer) rationalizerDuplCountsCacheHandler.readCount();
                    Integer chachedDuplicatesTotalCount = (Integer) rationalizerDuplTotalCacheHandler.readCount();
                    if(chachedDuplicatesCount != null && chachedDuplicatesTotalCount != null) {
                        duplicatesCount = chachedDuplicatesCount;
                        duplicatesFoundSum = chachedDuplicatesTotalCount;
                    } else {
                        Map<String, Integer> duplicatesHashCountMap = rationalizerElasticSearchHandler.searchDuplicatesWithCompareContext(sourceTreeFiltersList, targetTreeFiltersList, dashboardResultsFilterList);

                        if(!MapUtils.isEmpty(duplicatesHashCountMap)) {
                            duplicatesCount = duplicatesHashCountMap.size();
                            duplicatesFoundSum = duplicatesHashCountMap.values()
                                    .stream()
                                    .mapToInt(Integer::valueOf)
                                    .sum();
                        }

                        if(elasticSearchAppExists) {
                            rationalizerDuplCountsCacheHandler.saveCount(duplicatesCount);
                            rationalizerDuplTotalCacheHandler.saveCount(duplicatesFoundSum);
                        }
                    }
                } else {
                    rationalizerDuplCountsCacheHandler.saveCount(duplicatesCount);
                    rationalizerDuplTotalCacheHandler.saveCount(duplicatesFoundSum);
                }
                String dashboarDuplMessage = format(getMessage("client_messages.text.rat_dashboard_duplicates_number_gt_zero"),FORMATTER.format(duplicatesCount), FORMATTER.format(duplicatesFoundSum));
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("duplicatesCount", FORMATTER.format(duplicatesCount));
                jsonObject.addProperty("dashboarDuplMessage", dashboarDuplMessage);
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer duplicates dashboard count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_SIMILARITIES_COUNT) {
            try {
                int similaritiesCount = 0;
                int similaritiesFoundSum = 0;
                List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
                RationalizerCountsCacheHandler rationalizerCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.SIMILARITY_COUNT)
                                .countLabel("similarities count")
                                .build();
                RationalizerCountsCacheHandler rationalizerTotalCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.SIMILARITY_COUNT_TOTAL)
                                .countLabel("similarities count total")
                                .build();
                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {
                    Integer chachedSimilaritiesCount = (Integer) rationalizerCountsCacheHandler.readCount();
                    Integer chachedSimilaritiesTotalCount = (Integer) rationalizerTotalCountsCacheHandler.readCount();
                    if(chachedSimilaritiesCount != null && chachedSimilaritiesTotalCount != null) {
                        similaritiesCount = chachedSimilaritiesCount;
                        similaritiesFoundSum = chachedSimilaritiesTotalCount;
                    } else {
                        Map<String, Integer> similaritiesHashCountMap = rationalizerElasticSearchHandler.searchSimilaritiesCountsWithCompareContext(
                                sourceTreeFiltersList, targetTreeFiltersList, dashboardResultsFilterList, MIN_SIMILARITY_FOR_DASHBOARD, MAX_SIMILARITY
                        );
                        if(!MapUtils.isEmpty(similaritiesHashCountMap)) {
                            similaritiesCount = similaritiesHashCountMap.size();
                            similaritiesFoundSum = similaritiesHashCountMap.values()
                                    .stream()
                                    .mapToInt(Integer::valueOf)
                                    .sum();
                        }
                        if(elasticSearchAppExists) {
                            rationalizerCountsCacheHandler.saveCount(similaritiesCount);
                            rationalizerTotalCountsCacheHandler.saveCount(similaritiesFoundSum);
                        }
                    }
                } else {
                    rationalizerCountsCacheHandler.saveCount(similaritiesCount);
                    rationalizerTotalCountsCacheHandler.saveCount(similaritiesFoundSum);
                }
                String dashboarSimilMessage = format(getMessage("client_messages.text.rationalizer_dashboard_similarities_number"),FORMATTER.format(similaritiesCount), FORMATTER.format(similaritiesFoundSum));

                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("similaritiesCount", FORMATTER.format(similaritiesCount));
                jsonObject.addProperty("dashboarSimilarityMessage", dashboarSimilMessage);
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer similarities dashboard count response: " + e.getMessage(), e);
            }

        } else if (action == ACTION_READING_COMPREHENSION_COUNT) {
            try {
                Float readingAverage = 0.0f;
                List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.readability);
                RationalizerCountsCacheHandler rationalizerCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.READABILITY_COUNT)
                                .countLabel("readability count")
                                .build();

                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {
                     Float cachedCount = (Float) rationalizerCountsCacheHandler.readCount();
                    if(cachedCount != null) {
                        readingAverage = cachedCount;
                    } else {
                        readingAverage = rationalizerElasticSearchHandler.computeReadabilityFleschReadingAverage(sourceTreeFiltersList, dashboardResultsFilterList);

                        if(elasticSearchAppExists) {
                            rationalizerCountsCacheHandler.saveCount(readingAverage);
                        }
                    }
                } else{
                    rationalizerCountsCacheHandler.saveCount(readingAverage);
                }

                String readingString = String.format("%.2f", readingAverage == null ? 0.0f : readingAverage);
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("readingCount", readingString);
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer reading comprehension dashboard count response: " + e.getMessage(), e);
            }

        } else if (action == ACTION_SENTIMENT_COUNT) {
            try {
                Integer negativeSentimentCount = 0;
                List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.sentiment);
                RationalizerCountsCacheHandler rationalizerCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .dashboardFiltersHash(new DashboardFilters(dashboardResultsFilterList).calculateHash())
                                .countsCacheEnum(RationalizerCountsCacheEnum.SENTIMENT_COUNT)
                                .countLabel("negative sentiment count")
                                .build();
                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {

                    Integer cachedCount = (Integer) rationalizerCountsCacheHandler.readCount();
                    if(cachedCount != null) {
                        negativeSentimentCount = cachedCount;
                    } else {
                        Map<String, Integer> sentimentMap = rationalizerElasticSearchHandler.computeSentiment(sourceTreeFiltersList, dashboardResultsFilterList);
                        negativeSentimentCount = buildCountValueForSentiment(NEGATIVE, sentimentMap);

                        if(elasticSearchAppExists) {
                            rationalizerCountsCacheHandler.saveCount(negativeSentimentCount);
                        }
                    }
                } else {
                    rationalizerCountsCacheHandler.saveCount(negativeSentimentCount);
                }

                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("sentimentCount", FORMATTER.format(negativeSentimentCount));
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer sentiment dashboard count response: " + e.getMessage(), e);
            }

        } else if (action == ACTION_METADATA_COUNT) {
            try {
                int metadataCount = 0;
                RationalizerCountsCacheHandler rationalizerCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .countsCacheEnum(RationalizerCountsCacheEnum.METADATA_COUNT)
                                .countLabel("metadata count")
                                .build();
                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {

                    Integer cachedCount = (Integer) rationalizerCountsCacheHandler.readCount();
                    if(cachedCount != null) {
                        metadataCount = cachedCount;
                    } else {
                        List<MetadataPointsOfInterest> metadataPointsOfInterestList = RationalizerApplication.findMetadataPointsOfInterest(rationalizerApplicationId);
                        Map<String, Integer> metadataPointOfInterestCountsMap = new HashMap<>();
                        if(!CollectionUtils.isEmpty(metadataPointsOfInterestList)) {
                            metadataPointOfInterestCountsMap = rationalizerElasticSearchHandler.computeMetadataPointOfInterestCounts(metadataPointsOfInterestList, sourceTreeFiltersList);
                        }
                        if(!MapUtils.isEmpty(metadataPointOfInterestCountsMap)) {
                            metadataCount = metadataPointOfInterestCountsMap.values().stream().mapToInt(Integer::intValue).sum();
                        }

                        if(elasticSearchAppExists) {
                            rationalizerCountsCacheHandler.saveCount(metadataCount);
                        }
                    }
                } else{
                    rationalizerCountsCacheHandler.saveCount(metadataCount);
                }

                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("metadataCount", FORMATTER.format(metadataCount));
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer metadata dashboard count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_BRAND_COUNT) {
            try {
                int brandCount = 0;
                RationalizerCountsCacheHandler rationalizerCountsCacheHandler =
                        RationalizerCountsCacheHandler.builder()
                                .schemaName(MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier())
                                .rationalizerApplication(rationalizerApp)
                                .rationalizerElasticSearchHandler(rationalizerElasticSearchHandler)
                                .sourceTreeFiltersList(sourceTreeFiltersList)
                                .targetTreeFiltersList(targetTreeFiltersList)
                                .countsCacheEnum(RationalizerCountsCacheEnum.BRAND_COUNT)
                                .countLabel("brand count")
                                .build();
                if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() != rationalizerApp.getAppSyncStatus()
                        && RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() != rationalizerApp.getAppSyncStatus()) {

                    Integer cachedCount = (Integer) rationalizerCountsCacheHandler.readCount();
                    if(cachedCount != null) {
                        brandCount = cachedCount;
                    } else {
                        List<String> enabledBrandProfileProperties = constructEnabledBrandPropertiesList(brandProfile);
                        Map<String, Integer> brandViolationsCountsMap = rationalizerElasticSearchHandler.computeBrandViolationsCountsWithMetadataFilter(enabledBrandProfileProperties, sourceTreeFiltersList);
                        if(!MapUtils.isEmpty(brandViolationsCountsMap)) {
                            brandCount = brandViolationsCountsMap.values().stream().mapToInt(Integer::intValue).sum();
                        }

                        if(elasticSearchAppExists) {
                            rationalizerCountsCacheHandler.saveCount(brandCount);
                        }
                    }
                } else{
                    rationalizerCountsCacheHandler.saveCount(brandCount);
                }
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("brandCount", FORMATTER.format(brandCount));
                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer brand dashboard count response: " + e.getMessage(), e);
            }
        } else if (action == ACTION_DASHBOARD_INFO) {
            try {
                List<String> sourceSelectedNodesList = Arrays.asList(sourceDashboardCompareSelection.split(","));
                List<Map<MetadataFormItemDefinition, MetadataFormItem>> sourceNavTreePathSelectionsList
                        = MetadataFormItem.findRationalizerNavTreeMultiplePathSelections(rationalizerApplicationId, sourceSelectedNodesList);

                int docNo = countFilteredRationalizerDocuments(rationalizerApplicationId, sourceNavTreePathSelectionsList);
                int totalDocNo = rationalizerApp.computeDocumentCount();

                int totalContentsNo = rationalizerApp.getDocumentContentCount();
                int contentsNo = CollectionUtils.isEmpty(sourceNavTreePathSelectionsList) ? totalContentsNo : countFilteredContents(rationalizerApplicationId, sourceNavTreePathSelectionsList);

                JsonObject jsonObject = new JsonObject();

                jsonObject.addProperty("documentsNumberDisplayString",
                        MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.documents"), docNo));
                jsonObject.addProperty("documentsNumber", docNo);


                double docPercent = totalDocNo != 0 ? (double) docNo * 100 / totalDocNo : 100;
                String docPercentString = String.format("%.2f", docPercent);
                jsonObject.addProperty("documentsPercentageDisplayString",
                        MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.documents.of.total"), docPercentString));

                jsonObject.addProperty("contentObjectsNumberDisplayString",
                        MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.content.objects"), contentsNo));
                jsonObject.addProperty("contentObjectsNumber", contentsNo);

                double contentPercent = totalContentsNo != 0 ? (double) contentsNo * 100 / totalContentsNo : 100;
                String docContentString = String.format("%.2f", contentPercent);
                jsonObject.addProperty("contentObjectsPercentageDisplayString",
                        MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.content.objects.of.total"), docContentString));

                String titleMetadata = StringUtils.EMPTY;
                if (CollectionUtils.isEmpty(sourceNavTreePathSelectionsList)) {
                    titleMetadata = ApplicationUtil.getMessage("page.label.master");

                } else {
                    if (sourceNavTreePathSelectionsList.size() > 1) {
                        titleMetadata = format(getMessage("page.label.multiple.selections"), sourceNavTreePathSelectionsList.size());
                    } else {
                        Map<MetadataFormItemDefinition, MetadataFormItem> selectionMap = sourceNavTreePathSelectionsList.get(0);
                        if (MapUtils.isNotEmpty(selectionMap)) {
                            List<String> valueList = new LinkedList<>();
                            selectionMap.values().forEach(item -> {
                                String value = ApplicationUtil.getMessage("page.label.NO.VALUE");
                                if (item != null && StringUtils.isNotEmpty(item.getValue())) {
                                    value = item.getValue();
                                }
                                valueList.add(value);
                            });
                            titleMetadata = titleMetadata + String.join(TITLE_DELIMITER, valueList);
                        }
                    }
                }

                jsonObject.addProperty("titleMetadata", titleMetadata);

                out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
                out.flush();
            } catch (Exception e) {
                log.error("Error: Unable to retrieve rationalizer dashboard info dashboard count response: " + e.getMessage(), e);
            }
        }

        return null;
    }

    private boolean checkRedirectAfterLogin(HttpServletRequest request, HttpServletResponse response) {
        String referer = request.getHeader("Referer");
        if (StringUtils.isEmpty(referer)) {
            return false;
        }

        // Only Rationalizer Dashboard pages are allowed to execute this Ajax call.
        // The below regex will match the following urls and other similar that will be created in the future:
        //  rationalizer_dashboard.form
        //  rationalizer_dashboard_duplicates.form
        //  rationalizer_dashboard_similarities.form
        //  rationalizer_dashboard_reading.form
        //  rationalizer_dashboard_sentiment.form
        //  rationalizer_dashboard_metadata.form
        //  rationalizer_dashboard_brand.form
        if (referer.matches("^.*?/rationalizer_dashboard.*$")) {
            return false;
        }

        StringBuilder dashboardUrlBuilder = new StringBuilder();
        dashboardUrlBuilder.append("rationalizer/rationalizer_dashboard.form");
        Map<String, String> params = new LinkedHashMap<>() {{
            put("tk", request.getParameter("tk"));
            put(REQ_PARAM_RATIONALIZER_APP_ID, request.getParameter(REQ_PARAM_RATIONALIZER_APP_ID));
        }};
        StringBuilder dashboardParamsBuilder = new StringBuilder();
        params.forEach((key, value) -> {
            if (StringUtils.isEmpty(value)) {
                return;
            }

            if (dashboardParamsBuilder.isEmpty()) {
                dashboardParamsBuilder.append("?");
            } else {
                dashboardParamsBuilder.append("&");
            }

            dashboardParamsBuilder.append(key).append("=").append(value);
        });
        dashboardUrlBuilder.append(dashboardParamsBuilder);

        String responseText = StringUtils.replace(REDIRECT_PAGE_TEMPLATE, "{redirectURL}", dashboardUrlBuilder.toString());

        returnTextHtml(response, responseText);

        return true;
    }

    private void returnEmptyJson(HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();

            JsonObject jsonObject = new JsonObject();
            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to return empty json.", e);
        }
    }

    private void returnTextHtml(HttpServletResponse response, String responseString) {
        try {
            response.setContentType("text/html");

            ServletOutputStream out = response.getOutputStream();

            out.write(responseString.getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to return text/html.", e);
        }
    }

    private void handleDashboardStats(RationalizerElasticSearchHandler rationalizerElasticSearchHandler, ServletOutputStream out) {
        try {
            JsonObject marcieStatsJsonObject = rationalizerElasticSearchHandler.loadMarcieStats();

            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty("elasticSearchApplicationId", rationalizerElasticSearchHandler.getElasticAppId())
                    .add("stats", marcieStatsJsonObject)
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to retrieve rationalizer dashboard stats response.", e);
        }
    }

    private void handleDashboardStatsErrorDownload(
            HttpServletRequest request,
            HttpServletResponse response,
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler
    ) {
        try {
            String statsId = ServletRequestUtils.getStringParameter(request, REQ_PARAM_STATS_ID, "");

            String resultError = StringUtils.isEmpty(statsId) ? StringUtils.EMPTY : rationalizerElasticSearchHandler.loadMarcieStatsException(statsId);
            byte[] binaryContent = resultError.getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING);

            response.setContentType("text/html");
            response.setContentLength( binaryContent.length );
            response.setHeader( "Content-disposition", "attachment; filename=\"" + statsId + "_exception.text\"" );

            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(binaryContent);
            outputStream.flush();
        } catch (Exception e) {
            log.error("Error: Unable to retrieve rationalizer dashboard stats response.", e);
        }
    }

    private int countFilteredContents(long rationalizerApplicationId, List<Map<MetadataFormItemDefinition, MetadataFormItem>> rationalizerNavTreePathMapList) {
        Set<Long> docContentIds = new HashSet<>();
        for(Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap : rationalizerNavTreePathMapList){
            docContentIds.addAll(RationalizerUtil.getNavTreeFilteredContents(rationalizerApplicationId, rationalizerNavTreePathMap));
        }
        return docContentIds.isEmpty() ? 0 : docContentIds.size();
    }

    private int countFilteredRationalizerDocuments(long rationalizerApplicationId, List<Map<MetadataFormItemDefinition, MetadataFormItem>> rationalizerNavTreePathMapList) {
        List<MessagepointCriterion> criterionList = new ArrayList<>();
        criterionList.add(MessagepointRestrictions.eq("rationalizerApplication.id", rationalizerApplicationId));

        List<MessagepointCriterion> andCriterionList = new ArrayList<>();
        for (Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap : rationalizerNavTreePathMapList) {
            List<MessagepointCriterion> criterionListForSelection = new ArrayList<>();
            RationalizerUtil.addRationalizerDocumentsNavTreeFiltersToCriterionList(rationalizerApplicationId, rationalizerNavTreePathMap, criterionListForSelection);
            if(CollectionUtils.isNotEmpty(criterionListForSelection)) {
                andCriterionList.add(MessagepointRestrictions.and(criterionListForSelection.toArray(new MessagepointCriterion[0])));
            }
        }
        if(CollectionUtils.isNotEmpty(andCriterionList)) {
            criterionList.add(MessagepointRestrictions.and(andCriterionList.toArray(new MessagepointCriterion[0])));
        }
        List<RationalizerDocument> rationalizerDocumentList = HibernateUtil.getManager().getObjectsAdvanced(RationalizerDocument.class, criterionList);
        return rationalizerDocumentList.isEmpty() ? 0 : rationalizerDocumentList.size();
    }
}