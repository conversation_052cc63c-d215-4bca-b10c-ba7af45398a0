package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.brand.BrandProfileEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import org.json.JSONArray;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RationalizerDashboardBrandDetailsController extends MessagepointController {

    private static final String SELECTED_BRAND_ITEM_ID = "selectedBrandItemId";
    private static final String RATIONALIZER_APPLICATION_ID = "rationalizerApplicationId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

        return referenceData;
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long selectedBrandItemId = ServletRequestUtils.getLongParameter(request, SELECTED_BRAND_ITEM_ID, -1L);
        BrandProfileEnum selectedBrandProfile = BrandProfileEnum.fromId(selectedBrandItemId);
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, RATIONALIZER_APPLICATION_ID, -1L);
        RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
        BrandProfile brandProfile = BrandProfile.getAppliedProfile(null, rationalizerApp);

        RationalizerDashboardBrandDetailsWrapper wrapper = new RationalizerDashboardBrandDetailsWrapper();
        if (BrandProfileEnum.RESTRICTED_TERM.equals(selectedBrandProfile)) {
            JSONArray enabledRestrictedTerms = brandProfile.getEnabledRestrictedTerms();
            wrapper.setFilterItemsList(extractEnabledItems(enabledRestrictedTerms));
        } else if (BrandProfileEnum.PREFERRED_CONTRACTION.equals(selectedBrandProfile)) {
            JSONArray enabledPreferredContractions = brandProfile.getEnabledPreferredContractions();
            wrapper.setFilterItemsList(extractEnabledItems(enabledPreferredContractions));
        } else if (BrandProfileEnum.RESTRICTED_CONTRACTION.equals(selectedBrandProfile)) {
            JSONArray enabledRestrictedContractions = brandProfile.getEnabledRestrictedContractions();
            wrapper.setFilterItemsList(extractEnabledItems(enabledRestrictedContractions));
        }

        return wrapper;
    }

    private List<String> extractEnabledItems(JSONArray enabledTermsArray) {
        if (enabledTermsArray == null || enabledTermsArray.isEmpty()) {
            return null;
        }
        List<String> result = new ArrayList<>();
        for (int i = 0; i < enabledTermsArray.length(); i++) {
            result.add(enabledTermsArray.getJSONObject(i).getString("target"));
        }

        return result;
    }
}
