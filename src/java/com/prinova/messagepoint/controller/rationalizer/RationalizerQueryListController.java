package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.platform.services.export.GenerateRationalizerObjectExportService;
import com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerQueryService;
import com.prinova.messagepoint.platform.services.rationalizer.CloneRationalizerAppBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.DeleteRationalizerAppBackgroundTask;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.triggerBrandUpdateBackgroundTaskIfNeeded;
import static com.prinova.messagepoint.platform.services.backgroundtask.ExportRationalizerObjectBackgroundTask.EXPORT_EXT_TYPE_EXCEL;

public class RationalizerQueryListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerDocumentsListController.class);

	public static final String REQ_PARAM_RATIONALIZER_APP_ID 		= "rationalizerApplicationId";
	public static final String REQ_PARAM_TYPE 										= "type";
	public static final String REQ_PARAM_ACTION 									= "action";
	
	public static final int ACTION_UPDATE 												= 1;
	public static final int ACTION_DELETE 													= 2;
	public static final int ACTION_ADD_APPLICATION							= 3;
	public static final int ACTION_UPDATE_APPLICATION						= 4;
	public static final int ACTION_GENERATE_REPORT							= 6;
	public static final int ACTION_CLONE_APPLICATION						= 7;
	public static final int ACTION_DELETE_REWRITE_APPLICATION  	= 8;
	public static final int ACTION_DELETE_APPLICATION  					= 9;

	private String editView;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		//referenceData.put("applications", RationalizerApplication.findAll() );
		// filter application list by visibility
		referenceData.put("applications", RationalizerApplication.filterVisibleApplications(UserUtil.getPrincipalUser()) );
		
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		RationalizerApplication application = RationalizerApplication.findById(rationalizerApplicationId);

		referenceData.put("parsedDocumentMetadataFormDefinitionId", application != null && application.getParsedDocumentFormDefinition() != null ?
																application.getParsedDocumentFormDefinition().getId() : -1 );
		referenceData.put("parsedContentMetadataFormDefinitionId", application != null && application.getParsedContentFormDefinition() != null ?
																application.getParsedContentFormDefinition().getId() : -1 );
		
		referenceData.put("documentsExist", application != null && application.computeDocumentCount() > 0);
		StatusPollingBackgroundTask task = null;
		if ( application != null ){
			task = StatusPollingBackgroundTask.findLastestByNameAndDescriptionAndByUser(UserUtil.getPrincipalUser(), 
					application.getName(), StatusPollingBackgroundTask.SUB_TYPE_RATIONLIZER_METADATA_EXPORT);
		}
		referenceData.put("report", task);
		if (task != null) {
			referenceData.put("reportType", FileUtil.getFileExtension(task.getOutputFilename()));
		}
		if(application != null){
			referenceData.put("application", application);
			RationalizerApplication clonedApplication = RationalizerApplication.findUniqueByParentId(application.getId());
			RationalizerApplication parentApplication = (RationalizerApplication)application.getParentObject();
			referenceData.put("hasClonedAppContext", (clonedApplication != null || parentApplication != null));

			if(clonedApplication != null){
				referenceData.put("clonedApplication", clonedApplication);
			}
			if(parentApplication != null){
				referenceData.put("parentApplication", parentApplication);
			}
		}

		// BRAND
		referenceData.put("brandProfiles", BrandProfile.listAll());
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(MetadataFormDefinition.class, new IdCustomEditor<>(MetadataFormDefinition.class));
		binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
		binder.registerCustomEditor(BrandProfile.class, new IdCustomEditor<>(BrandProfile.class));
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		
		RationalizerQueryListWrapper wrapper = new RationalizerQueryListWrapper();
		
		if ( rationalizerApplicationId > 0 ){
			RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
			wrapper.setCurrentApplication(rationalizerApp);
			wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
		}
		
		return wrapper;
    }

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

		Map<String, Object> params = new HashMap<>();
		if (rationalizerApplicationId == -1) { // Manage default rationalizer application
			RationalizerApplication applicationContext = UserUtil.getCurrentRationalizerApplicationContext();
			if ( applicationContext == null ) {
				applicationContext = RationalizerApplication.getLatest();
				
				if ( applicationContext != null ) {
					HashMap<String,String> contextAttr = new HashMap<>();
					contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
					UserUtil.updateUserContextAttributes(contextAttr);
				}
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext != null ? applicationContext.getId() : -2);
			} else {
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext.getId());
			}
			return new ModelAndView(new RedirectView(getSuccessView()), params);
		}else if(rationalizerApplicationId > 0){
			RationalizerApplication applicationContext = RationalizerApplication.findById(rationalizerApplicationId);
			if(applicationContext == null){
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			} else {
				HashMap<String, String> contextAttr = new HashMap<>();
				contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
				UserUtil.updateUserContextAttributes(contextAttr);
			}
		}
		
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.QueryList);

		try {
			int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
			RationalizerQueryListWrapper command = (RationalizerQueryListWrapper) commandObj;

			switch (action) {
				case (ACTION_DELETE): {
					analyticsEvent.setAction(Actions.Delete);

					ServiceExecutionContext context = BulkDeleteRationalizerQueryService.createContext(command.getSelectedQueryList());
					Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteRationalizerQueryService.SERVICE_NAME, BulkDeleteRationalizerQueryService.class);
					deleteModelService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(DeleteModelService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" paragraph style is not deleted. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView( new RedirectView( getSuccessView() ), getSuccessViewParms(request) );
					}
				}
				case (ACTION_ADD_APPLICATION): {
					analyticsEvent.setAction(Actions.AddApplication);

					ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewApplication(
															command.getApplicationName(),
															command.getMetatags(),
															command.getDescription());

					Service createApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
					createApplicationService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer application not created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> params = new HashMap<>();

						long newApplicationId = (Long)serviceResponse.getResultValueBean();
						params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

						return new ModelAndView( new RedirectView( getSuccessView() ), params );
					}
				}
				case (ACTION_UPDATE_APPLICATION): {
					analyticsEvent.setAction(Actions.UpdateApplication);

					BrandProfile existingApplicationProfile = command.getExistingApplicationBrandProfile();
					BrandProfile selectedBrandProfile = command.getCurrentApplication().getBrandProfile();

					ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateApplication(
															command.getCurrentApplication().getId(),
															command.getCurrentApplication().getName(),
															command.getCurrentApplication().getMetatags(),
															command.getCurrentApplication().getDescription(),
															command.getCurrentApplication().getQueryFilterFormItemDefinitions(),
															null,
															command.getReSync());

					Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
					updateApplicationService.execute(context);

					if (Boolean.FALSE.equals(command.getReSync())) {
						triggerBrandUpdateBackgroundTaskIfNeeded(existingApplicationProfile, selectedBrandProfile, command.getCurrentApplication());
					}

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer application not updated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> params = new HashMap<>();

						long newApplicationId = (Long)serviceResponse.getResultValueBean();
						params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

						return new ModelAndView( new RedirectView( getSuccessView() ), params );
					}
				}
				case (ACTION_GENERATE_REPORT): {
					/**
					 * ACTION_GENERATE_REPORT
					 */
					analyticsEvent.setAction(Actions.GenerateReport);
					AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.RationalizerMetadata);
					auditEvent.setAction(Actions.GenerateReport);
					try {
						ServiceExecutionContext context = GenerateRationalizerObjectExportService.createContext(
								command.getExportId(),
								command.getCurrentApplication().getId(),
								true,
								MetadataFormItemDefinition.class,
								UserUtil.getPrincipalUser(),
								EXPORT_EXT_TYPE_EXCEL);
						Service service = MessagepointServiceFactory.getInstance()
								.lookupService(GenerateRationalizerObjectExportService.SERVICE_NAME,
										GenerateRationalizerObjectExportService.class);
						service.execute(context);

						if (!context.getResponse().isSuccessful()) {
							log.error(" unexpected exception when invoking GenerateRationalizerObjectExportService execute method");
							ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
							return showForm(request, response, errors);
						} else {
							return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
						}
					}finally {
						auditEvent.send();
					}
				}
				case (ACTION_CLONE_APPLICATION) : {
					analyticsEvent.setAction(Actions.CloneApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					String name = rationalizerApp.getName();
					CloneRationalizerAppBackgroundTask task = new CloneRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), command.getCloneName());
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

					Map<String, Object> parms = new HashMap<>();
					parms.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());

					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_DELETE_REWRITE_APPLICATION) : {
					analyticsEvent.setAction(Actions.DeleteRewriteApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					String name = rationalizerApp.getName();
					DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), null, true);
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

					RationalizerApplication applicationContext = RationalizerApplication.findById(command.getCurrentApplication().getParentObject().getId());

					Map<String, Object> parms = new HashMap<>();
					parms.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext!=null?applicationContext.getId():-1);

					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_DELETE_APPLICATION) : {
					analyticsEvent.setAction(Actions.DeleteApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					RationalizerApplication clonedRationalizerApp = RationalizerApplication.findUniqueByParentId(rationalizerApp.getId());
					CountDownLatch latch = null;
					if(clonedRationalizerApp != null){
						latch = new CountDownLatch(1);
						DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(clonedRationalizerApp.getId(), UserUtil.getPrincipalUser(), latch, true);
						MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
					}
					String name = rationalizerApp.getName();
					DeleteRationalizerAppBackgroundTask task2 = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), latch, false);
					MessagePointRunnableUtil.startThread(task2, Thread.MAX_PRIORITY);

					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);

					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}
				default:
					break;
			}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) throws Exception {
		Map<String, Object> params = new HashMap<>();
		
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		if ( rationalizerApplicationId != -1L )
			params.put(REQ_PARAM_RATIONALIZER_APP_ID, rationalizerApplicationId);

		return params;
	}	

	public String getEditView() {
		return this.editView;
	}

	public void setEditView(String editView) {
		this.editView = editView;
	}
}