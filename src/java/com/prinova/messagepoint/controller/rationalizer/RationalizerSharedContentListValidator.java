package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.Errors;

import java.util.List;

public class RationalizerSharedContentListValidator extends MessagepointInputValidator {
    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        RationalizerSharedContentListWrapper wrapper = (RationalizerSharedContentListWrapper) commandObj;

        int action = -1;
        if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
            action = Integer.valueOf(wrapper.getActionValue()).intValue();
        }

        if (action == RationalizerDocumentsListController.ACTION_ADD_APPLICATION) {
            if (wrapper.getApplicationName() == null || wrapper.getApplicationName().trim().isEmpty()) {
                errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
                return;
            }
            // check if application name is unique
            List<RationalizerApplication> applicationList = RationalizerApplication.findAll();
            if(CollectionUtils.isNotEmpty(applicationList)) {
                for (RationalizerApplication rationalizerApplication : applicationList) {
                    if (!StringUtil.isEmptyOrNull(rationalizerApplication.getName())) {
                        if (rationalizerApplication.getName().equalsIgnoreCase(wrapper.getApplicationName())) {
                            errors.reject("error.message.rationalizer.name.unique", new String[]{}, "");
                            break;
                        }
                    }
                }
            }
            String label = ApplicationUtil.getMessage("page.label.name");
            MessagepointInputValidationUtil.validateStringValue(label, wrapper.getApplicationName() == null ? wrapper.getApplicationName() :
                    wrapper.getApplicationName().trim(), true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
        }
    }
}
