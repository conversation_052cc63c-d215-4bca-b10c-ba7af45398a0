package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerAppNavigationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


public class RationalizerApplicationNavigationController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerApplicationNavigationController.class);

	public static final String REQ_PARAM_APP_ID_PARAM 	= "rationalizerApplicationId";
	
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_APP_ID_PARAM, -1);	
		RationalizerApplication application = RationalizerApplication.findById(appId);

		final MetadataFormDefinition parsedDocumentFormDefinition = application.getParsedDocumentFormDefinition();
		Set<MetadataFormItemDefinition> availableMetadataItemDefs = parsedDocumentFormDefinition == null ? new LinkedHashSet<>() :
		parsedDocumentFormDefinition.getFormItemDefinitions();
		List<MetadataFormItemDefinition> toRemoveItemDefs = new ArrayList<>();
		for(MetadataFormItemDefinition availableMetadataItemDef : availableMetadataItemDefs){
			if(availableMetadataItemDef.getTypeId() == MetadataFormItemType.ID_FILE){
				toRemoveItemDefs.add(availableMetadataItemDef);
			}
		}
		toRemoveItemDefs.forEach(availableMetadataItemDefs::remove);
		referenceData.put("availableMetadataItemDefs", availableMetadataItemDefs.stream().sorted(Comparator.comparing(MetadataFormItemDefinition::getName)).collect(Collectors.toList()));
		referenceData.put("rationalizerApplicationId", appId);
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_APP_ID_PARAM, -1);		
		RationalizerApplicationNavigationWrapper command;
		if(appId != -1){
			RationalizerApplication application = RationalizerApplication.findById(appId);
			command = new RationalizerApplicationNavigationWrapper(application);
		}else{
			command = new RationalizerApplicationNavigationWrapper();
		}
		
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.ApplicationNavigation);
		analyticsEvent.setAction(Actions.Update);

		try {
			RationalizerApplicationNavigationWrapper command = (RationalizerApplicationNavigationWrapper)commandObj;
			ServiceExecutionContext context = UpdateRationalizerAppNavigationService.createContext(command.getRationalizerApplication(), command.getItemDefinitions());

			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateRationalizerAppNavigationService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" selected items were not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_APP_ID_PARAM, command.getRationalizerApplication().getId());
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}