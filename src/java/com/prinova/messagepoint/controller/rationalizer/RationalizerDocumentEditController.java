package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameComparator;
import com.prinova.messagepoint.model.SystemProperty;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemOriginTypeEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerContentService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerQueryService;
import com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerAppNavigationService;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class RationalizerDocumentEditController extends MessagepointController {
	private static final Log log = LogUtil.getLog(RationalizerDocumentEditController.class);

	public static final String REQ_PARAM_RATIONALIZER_DOCUMENT_ID 	= "rationalizerDocumentId";
	public static final String REQ_PARAM_SELECTED_CONTENT_ID        = "selectedContentId";

	public static final String REQ_PARAM_ACTION 					= "action";

	public static final int ACTION_ADD_CONTENT						= 1;
	public static final int ACTION_SET_CONTENT_METADATA				= 2;
	public static final int ACTION_SUBMIT							= 3;
	public static final int ACTION_BULK_DELETE_CONTENT				= 4;
	public static final int ACTION_MERGE_CONTENT					= 5;
	public static final int ACTION_SPLIT_CONTENT					= 6;
	public static final int ACTION_SUBMIT_AND_RELOAD				= 7;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		RationalizerDocumentEditWrapper wrapper = (RationalizerDocumentEditWrapper) command;
		wrapper.computeFieldsForDisplay();

		Map<String, Object> referenceData = new HashMap<>();

		RationalizerDocument document = wrapper.getRationalizerDocument();

		referenceData.put("rationalizerDocument", document);
		final RationalizerApplication rationalizerApplication = document.getRationalizerApplication();
		referenceData.put("rationalizerApplication", rationalizerApplication);

		List<MetadataFormItemDefinition> contentFormItemDefinitions = new ArrayList<>();
		if (rationalizerApplication.getParsedContentFormDefinition() != null)
			contentFormItemDefinitions.addAll(rationalizerApplication.getParsedContentFormDefinition().getFormItemDefinitions());

		List<MetadataFormItemDefinition> modifyContentFormItemDefinitions = new ArrayList<>();
		for (MetadataFormItemDefinition currentItem : contentFormItemDefinitions) {
			if (currentItem.getPrimaryConnector() == null) {
				continue;
			}
			if ("Order".equalsIgnoreCase(currentItem.getPrimaryConnector())) {
				continue;
			}
			if (currentItem.getOriginTypeId() == MetadataFormItemOriginTypeEnum.CONTENT_PARSED.getId()) {
				continue;
			}

			if (currentItem.getTypeId() == MetadataFormItemType.ID_TEXT || currentItem.getTypeId() == MetadataFormItemType.ID_TEXTAREA ||
					currentItem.getTypeId() == MetadataFormItemType.ID_SELECT_MENU || currentItem.getTypeId() == MetadataFormItemType.ID_MULTISELECT_MENU) {
				modifyContentFormItemDefinitions.add(currentItem);
			}
		}
		Collections.sort(modifyContentFormItemDefinitions, new IdentifiableMessagepointModelNameComparator());

		referenceData.put("modifyContentFormItemDefinitions", modifyContentFormItemDefinitions);

		boolean appliesContentMetadata = false;
		for (RationalizerDocumentContent currentContent : document.shallowCopyOfContents()) {
			if (currentContent.getParsedMetadataFormEditWrapper() != null) {
				appliesContentMetadata = true;
				break;
			}
		}

		referenceData.put("appliesContentMetadata", appliesContentMetadata);

		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

		JSONObject marcieFlagsObj = SystemProperty.getMarcieKeyValues();
		marcieFlagsObj.put("content_compare_enabled", false);
		marcieFlagsObj.put("brand_check_enabled", BrandProfile.isBrandProfileConfigured(null, rationalizerApplication));
		referenceData.put("marcieFlags", marcieFlagsObj);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.setAutoGrowCollectionLimit((int) Math.pow(2, 16));
		binder.registerCustomEditor(RationalizerDocumentContent.class, new IdCustomEditor<>(RationalizerDocumentContent.class));
		// DO NOT APPLY StringXSSEditor()
	}

	protected RationalizerDocumentEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
		long rationalizerDocumentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_DOCUMENT_ID, -1);

		RationalizerDocument document = RationalizerDocument.findById(rationalizerDocumentId);

		RationalizerDocumentEditWrapper wrapper = new RationalizerDocumentEditWrapper(document);

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.DocumentEdit);

		try {
			RationalizerDocumentEditWrapper wrapper = (RationalizerDocumentEditWrapper) commandObj;
			wrapper.removeDeletedContents();
			wrapper.computeFieldsForProcessing();

			RationalizerDocument document = wrapper.getRationalizerDocument();

			int action = -1;
			if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
				action = Integer.valueOf(wrapper.getActionValue()).intValue();
			}

			if ( action == ACTION_ADD_CONTENT ) {

				analyticsEvent.setAction(Actions.AddContent);

				ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForAddDocumentContent(
						document,
						wrapper.getAddContentCount() != null ? Integer.valueOf(wrapper.getAddContentCount()) : 0,
						wrapper.computeAdjustedInsertAfterOrder(),
						wrapper.getInsertedMarkupContent(),
						wrapper.getDocumentContents(),
						wrapper.getReorderedContentActions(),
						wrapper.buildTranslatedReorderedMarkupContents(),
						wrapper.getInitialDocumentName(),
						wrapper.getInitialDocumentTags()
				);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (serviceResponse.isSuccessful()) {
					if(wrapper.getFormWrapper().hasDocMetadataItemValueChanges()){
						// Update nav tree JSON
						context = UpdateRationalizerAppNavigationService.createContext(
								document.getRationalizerApplication(),
								null
						);
						service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
						service.execute(context);
						if (!context.getResponse().isSuccessful()) {
							context.getResponse().mergeResultMessages(context.getResponse());
						}
					}

					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());
					return new ModelAndView( new RedirectView(getSuccessView()), params );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" content was not added to RationalizerDocument. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return super.showForm(request, response, errors);
				}

			} else if ( action == ACTION_SPLIT_CONTENT ) {

				analyticsEvent.setAction(Actions.SplitContent);

				ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForSplitDocumentContent(
						document,
						wrapper.getAddContentCount() != null ? Integer.valueOf(wrapper.getAddContentCount()) : 0,
						wrapper.computeAdjustedInsertAfterOrder(),
						wrapper.getInsertedMarkupContent(),
						wrapper.getDocumentContents(),
						wrapper.getReorderedContentActions(),
						wrapper.buildTranslatedReorderedMarkupContents(),
						wrapper.getInitialDocumentName(),
						wrapper.getInitialDocumentTags()
				);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (serviceResponse.isSuccessful()) {
					if(wrapper.getFormWrapper().hasDocMetadataItemValueChanges()){
						// Udate nav tree JSON
						context = UpdateRationalizerAppNavigationService.createContext(
								document.getRationalizerApplication(),
								null
						);
						service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
						service.execute(context);
						if (!context.getResponse().isSuccessful()) {
							context.getResponse().mergeResultMessages(context.getResponse());
						}
					}

					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());

					int insertAfterOrder = Integer.parseInt(wrapper.getInsertAfterOrder());
					Collection<RationalizerDocumentContent> documentContents = wrapper.getRationalizerDocument().shallowCopyOfContents();
					for (RationalizerDocumentContent documentContent : documentContents) {
						if (insertAfterOrder == documentContent.getOrder()) {
							params.put(REQ_PARAM_SELECTED_CONTENT_ID, documentContent.buildElasticSearchGuid());
							break;
						}
					}
					return new ModelAndView( new RedirectView(getSuccessView()), params );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" content was not added to RationalizerDocument. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return super.showForm(request, response, errors);
				}

			} else if ( action == ACTION_SET_CONTENT_METADATA ) {

				analyticsEvent.setAction(Actions.SetContentMetadata);

				ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForSetContentMetadata(
						wrapper.getModifyContentMetadataConnectorValue(),
						wrapper.getModifyContentMetadataReplaceValue(),
						wrapper.getSelectedIds(),
						document.getRationalizerApplication().getId(),
						wrapper.getFormWrapper().getMetadataItemPreValueMap()
				);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (serviceResponse.isSuccessful()) {
					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());
					return new ModelAndView( new RedirectView(getSuccessView()), params );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" Rationalizer Metadata was not updated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return super.showForm(request, response, errors);
				}

			} else if ( action == ACTION_BULK_DELETE_CONTENT ) {

				analyticsEvent.setAction(Actions.Delete);

				List<String> selectedIds = wrapper.getSelectedIds();
				List<RationalizerContent> selectedContentsList = RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);

				ServiceExecutionContext context = BulkDeleteRationalizerContentService.createContext(selectedContentsList);
				Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteRationalizerContentService.SERVICE_NAME, BulkDeleteRationalizerContentService.class);
				deleteModelService.execute(context);

				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteRationalizerContentService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" rationalizer document content is not deleted. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());
					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}
			} else if ( action == ACTION_MERGE_CONTENT ) {

				analyticsEvent.setAction(Actions.MergeContent);

				if ( wrapper.getFormWrapper() != null )
					document.setParsedDocumentForm( wrapper.getFormWrapper().getMetadataForm() );

				ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForMergeDocumentContents(
						document,
						wrapper.getDocumentContents(),
						wrapper.getReorderedContentActions(),
						wrapper.buildTranslatedReorderedMarkupContents(),
						wrapper.getInitialDocumentName(),
						wrapper.getInitialDocumentTags(),
						wrapper.getSelectedIds()
				);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (serviceResponse.isSuccessful()) {
					if(wrapper.getFormWrapper().hasDocMetadataItemValueChanges()){
						// Udate nav tree JSON
						context = UpdateRationalizerAppNavigationService.createContext(
								document.getRationalizerApplication(),
								null
						);
						service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
						service.execute(context);
						if (!context.getResponse().isSuccessful()) {
							context.getResponse().mergeResultMessages(context.getResponse());
						}
					}

					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());
					params.put(REQ_PARAM_SELECTED_CONTENT_ID, wrapper.getSelectedIds().get(0));
					return new ModelAndView( new RedirectView(getSuccessView()), params );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" RationalizerDocument was not updated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return super.showForm(request, response, errors);
				}

			} else {

				// For ACTION_SUBMIT and ACTION_SUBMIT_AND_RELOAD

				analyticsEvent.setAction(Actions.Update);

				if ( wrapper.getFormWrapper() != null )
					document.setParsedDocumentForm( wrapper.getFormWrapper().getMetadataForm() );

				ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateDocument(
						document,
						wrapper.getDocumentContents(),
						wrapper.getReorderedContentActions(),
						wrapper.buildTranslatedReorderedMarkupContents(),
						wrapper.getInitialDocumentName(),
						wrapper.getInitialDocumentTags()
				);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (serviceResponse.isSuccessful()) {
					if(wrapper.getFormWrapper().hasDocMetadataItemValueChanges()){
						// Udate nav tree JSON
						context = UpdateRationalizerAppNavigationService.createContext(
								document.getRationalizerApplication(),
								null
						);
						service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
						service.execute(context);
						if (!context.getResponse().isSuccessful()) {
							context.getResponse().mergeResultMessages(context.getResponse());
						}
					}

					if (action == ACTION_SUBMIT_AND_RELOAD) {
						Map<String, Object> params = new HashMap<>();
						params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());
						return new ModelAndView(new RedirectView(getSuccessView()), params);
					}

					// action == ACTION_SUBMIT
					Map<String, Object> params = new HashMap<>();
					params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
					return new ModelAndView( new RedirectView("../frameClose.jsp"), params );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" RationalizerDocument was not updated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return super.showForm(request, response, errors);
				}

			}
		} finally {
			analyticsEvent.send();
		}
	}
}
