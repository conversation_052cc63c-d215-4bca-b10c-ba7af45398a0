package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

public class RationalizerConsolidateEditWrapper implements Serializable {

    private static final long serialVersionUID = -288914342836914507L;

    private String name;

    private boolean isPartOfSharedObject;

    private String selectedObjectsString;

    private String rationalizerContentId;

    private Set<String> selectedIdsSet;

    private List<RationalizerSharedContent> sharedContentList;

    private String foundSharedObjectId;

    private String actionValue;

    private String guidForAllMatchedIds;

    private String guidForSelectedIds;

    private Boolean selectAllPages;

    private long rationalizerApplicationId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getIsPartOfSharedObject() {
        return isPartOfSharedObject;
    }

    public void setPartOfSharedObject(boolean partOfSharedObject) {
        isPartOfSharedObject = partOfSharedObject;
    }

    public String getSelectedObjectsString() {
        return selectedObjectsString;
    }

    public void setSelectedObjectsString(String selectedObjectsString) {
        this.selectedObjectsString = selectedObjectsString;
    }

    public String getRationalizerContentId() {
        return rationalizerContentId;
    }

    public void setRationalizerContentId(String rationalizerContentId) {
        this.rationalizerContentId = rationalizerContentId;
    }

    public Set<String> getSelectedIdsSet() {
        return selectedIdsSet;
    }

    public void setSelectedIdsSet(Set<String> selectedIdsSet) {
        this.selectedIdsSet = selectedIdsSet;
    }

    public List<RationalizerSharedContent> getSharedContentList() {
        return sharedContentList;
    }

    public void setSharedContentList(List<RationalizerSharedContent> sharedContentList) {
        this.sharedContentList = sharedContentList;
    }

    public String getFoundSharedObjectId() {
        return foundSharedObjectId;
    }

    public void setFoundSharedObjectId(String foundSharedObjectId) {
        this.foundSharedObjectId = foundSharedObjectId;
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public String getGuidForAllMatchedIds() {
        return guidForAllMatchedIds;
    }

    public void setGuidForAllMatchedIds(String guidForAllMatchedIds) {
        this.guidForAllMatchedIds = guidForAllMatchedIds;
    }

    public String getGuidForSelectedIds() {
        return guidForSelectedIds;
    }

    public void setGuidForSelectedIds(String guidForSelectedIds) {
        this.guidForSelectedIds = guidForSelectedIds;
    }

    public Boolean getSelectAllPages() {
        return selectAllPages;
    }

    public void setSelectAllPages(Boolean selectAllPages) {
        this.selectAllPages = selectAllPages;
    }

    public long getRationalizerApplicationId() {
        return rationalizerApplicationId;
    }

    public void setRationalizerApplicationId(long rationalizerApplicationId) {
        this.rationalizerApplicationId = rationalizerApplicationId;
    }
}