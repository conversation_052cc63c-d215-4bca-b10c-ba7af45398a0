package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

import java.util.List;

import static com.prinova.messagepoint.controller.rationalizer.RationalizerSharedContentEditController.ACTION_EDIT;

public class RationalizerSharedContentEditValidator extends MessagepointInputValidator {

    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        RationalizerSharedContentEditWrapper wrapper = (RationalizerSharedContentEditWrapper) commandObj;

        // NAME
        if (ACTION_EDIT.equals(wrapper.getAction()) && StringUtils.isEmpty(StringUtils.strip(wrapper.getSharedContentName()))) {
            errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
        }

        // NAME must be unique
        if (ACTION_EDIT.equals(wrapper.getAction()) && !StringUtils.isEmpty(StringUtils.strip(wrapper.getSharedContentName()))) {
            String sharedContentName = StringUtils.strip(wrapper.getSharedContentName().trim());
            if (sharedContentName != null && sharedContentName.contains("  ")) {
                sharedContentName = sharedContentName.replaceAll("\\s+", " ");
            }
            List<RationalizerSharedContent> rationalizerSharedContentList = RationalizerSharedContent.findByNameAndApplication(sharedContentName, wrapper.getRationalizerSharedContent().getRationalizerApplication().getId());
            RationalizerSharedContent otherRationalizerSharedContentObject = rationalizerSharedContentList.stream()
                    .filter(el -> el.getId() != wrapper.getRationalizerSharedContent().getId()).findFirst().orElse(null);
            if(otherRationalizerSharedContentObject != null) {
                errors.reject("error.rationalizer.shared.content.name.unique");
            }
        }

        // TEXT
        if (ACTION_EDIT.equals(wrapper.getAction()) && StringUtils.isEmpty(wrapper.getContentMarkup())) {
            errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.content")}, "");
        }
    }
}
