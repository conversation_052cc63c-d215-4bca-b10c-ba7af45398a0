package com.prinova.messagepoint.controller.rationalizer;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class RationalizerApplicationVisibilityValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RationalizerApplicationVisibilityWrapper wrapper = (RationalizerApplicationVisibilityWrapper) commandObj;
		if(!wrapper.isFullVisible()){
			if(wrapper.getSelectedVisibleUserIds() == null || wrapper.getSelectedVisibleUserIds().isEmpty()){
				errors.reject("error.touchpointselection.visibility.no.user.selected");
			}
			
			// Remove the current user: User shouldn't be able to remove themselves from having visibility.
			User requestor = UserUtil.getPrincipalUser();
			if(wrapper.getSelectedVisibleUserIds() != null && !wrapper.getSelectedVisibleUserIds().contains(requestor.getId())){
				errors.reject("error.touchpointselection.visibility.current.user.not.selected");
			}
		}
	}
}
