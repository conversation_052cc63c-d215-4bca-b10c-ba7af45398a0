package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import org.apache.commons.lang3.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class RationalizerMetadataEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerMetadataEditController.class);
	
	public static final String REQ_PARAM_DOCUMENT_CONTENT_ID 	= "rationalizerDocumentContentId";
	public static final String REQ_PARAM_DOCUMENT_ID 			= "rationalizerDocumentId";
	
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		Map<String, Object> referenceData = new HashMap<>();
		
		String rationalizerDocumentContentGuid	= ServletRequestUtils.getStringParameter(request, REQ_PARAM_DOCUMENT_CONTENT_ID, "");
		long rationalizerDocumentId			= ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		
		RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerDocumentContentGuid);
		RationalizerDocument document = RationalizerDocument.findById(rationalizerDocumentId);
		
		referenceData.put("rationalizerApplication", rationalizerContent != null ? rationalizerContent.computeRationalizerApplication() : document.getRationalizerApplication() );

		referenceData.put("dateFormat", DateUtil.DATE_FORMAT);
		
		// EDITOR INIT
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
		// DO NOT APPLY StringXSSEditor()
	}

	protected RationalizerMetadataEditWrapper formBackingObject(HttpServletRequest request) throws Exception {

		String rationalizerDocumentContentGuid	= ServletRequestUtils.getStringParameter(request, REQ_PARAM_DOCUMENT_CONTENT_ID, "");
		long rationalizerDocumentId			= ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		
		RationalizerMetadataEditWrapper wrapper = null;
		if ( StringUtils.isNotEmpty(rationalizerDocumentContentGuid) ) {
			RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerDocumentContentGuid);
			final RationalizerSharedContent rationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();

			if (rationalizerSharedContent != null) {
				wrapper = new RationalizerSharedContentMetadataEditWrapper(  rationalizerSharedContent);
			} else if (rationalizerContent instanceof RationalizerDocumentContent) {
				wrapper = new RationalizerDocumentContentMetadataEditWrapper(  (RationalizerDocumentContent) rationalizerContent);
			}
		} else if ( rationalizerDocumentId > 0 ) {
			RationalizerDocument document = RationalizerDocument.findById(rationalizerDocumentId);
			wrapper = new RationalizerDocumentMetadataEditWrapper(document);
		}

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.MetadataEdit)
				.setAction(Actions.Update);

		try {
			RationalizerMetadataEditWrapper wrapper = (RationalizerMetadataEditWrapper) commandObj;

			if (wrapper instanceof RationalizerDocumentMetadataEditWrapper) {
				return onSubmitForRationalizerDocumentMetadataEditWrapper((RationalizerDocumentMetadataEditWrapper) wrapper, request, response, errors);
			} else if (wrapper instanceof RationalizerDocumentContentMetadataEditWrapper) {
				return onSubmitForRationalizerDocumentContentMetadataEditWrapper((RationalizerDocumentContentMetadataEditWrapper) wrapper, request, response, errors);
			} else if (wrapper instanceof RationalizerSharedContentMetadataEditWrapper) {
				return onSubmitForRationalizerSharedContentMetadataEditWrapper((RationalizerSharedContentMetadataEditWrapper) wrapper, request, response, errors);
			}

			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private ModelAndView onSubmitForRationalizerDocumentMetadataEditWrapper(
			RationalizerDocumentMetadataEditWrapper wrapper,
			HttpServletRequest request,
			HttpServletResponse response,
			BindException errors
	) throws Exception {
		if ( wrapper.getFormWrapper() != null ) {
			wrapper.getRationalizerDocument().setParsedDocumentForm( wrapper.getFormWrapper().getMetadataForm() );
		}

		ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateDocumentMetadata(wrapper.getRationalizerDocument());

		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}

	private ModelAndView onSubmitForRationalizerDocumentContentMetadataEditWrapper(
			RationalizerDocumentContentMetadataEditWrapper wrapper,
			HttpServletRequest request,
			HttpServletResponse response,
			BindException errors
	) throws Exception {

		if ( wrapper.getFormWrapper() != null ) {
			wrapper.getRationalizerDocumentContent().setParsedContentForm( wrapper.getFormWrapper().getMetadataForm() );
		}

		ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateDocumentContentMetadata(wrapper.getRationalizerDocumentContent(), wrapper.getFormWrapper().getMetadataItemPreValueMap());

		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}


	private ModelAndView onSubmitForRationalizerSharedContentMetadataEditWrapper(
			RationalizerSharedContentMetadataEditWrapper wrapper,
			HttpServletRequest request,
			HttpServletResponse response,
			BindException errors
	) throws Exception {

		if ( wrapper.getFormWrapper() != null ) {
			wrapper.getRationalizerSharedContent().setParsedContentForm( wrapper.getFormWrapper().getMetadataForm() );
		}

		ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateSharedContentMetadata(wrapper.getRationalizerSharedContent(), wrapper.getFormWrapper().getMetadataItemPreValueMap());

		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}
}