package com.prinova.messagepoint.controller.rationalizer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerSharedContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerHistoryDto;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.security.StringXSSEditor;

public class RationalizerContentHistoryController extends MessagepointController {

	public static String REQ_PRAM_RATIONALIZER_CONTENT_ID 	= "rationalizerContentId";
	public static String REQ_PRAM_RATIONALIZER_SHARED_CONTENT_ID 	= "rationalizerSharedContentId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		String documentContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PRAM_RATIONALIZER_CONTENT_ID, "");
		RationalizerDocumentContent rationalizerContent = RationalizerDocumentContent.findDocumentContentByElasticSearchGuid(documentContentGuid);
		if (rationalizerContent != null) {
			final RationalizerSharedContent tmpRationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();
			if (tmpRationalizerSharedContent == null) {
				generateReferenceDataForContentHistory(referenceData, rationalizerContent);
			} else {
				generateReferenceDataForSharedHistory(referenceData, tmpRationalizerSharedContent.buildElasticSearchGuid());
			}
		} else {
			String rationalizerSharedContentGuid = StringUtils.isNotEmpty(documentContentGuid) ? documentContentGuid : ServletRequestUtils.getStringParameter(request, REQ_PRAM_RATIONALIZER_SHARED_CONTENT_ID, "");
			generateReferenceDataForSharedHistory(referenceData, rationalizerSharedContentGuid);
		}

		return referenceData;
	}

	private void generateReferenceDataForContentHistory(Map<String, Object> referenceData, RationalizerDocumentContent rationalizerContent) {
		referenceData.put("rationalizerContent", rationalizerContent);
		String name =  ApplicationUtil.getMessage("page.label.document") + ": " + rationalizerContent.getRationalizerDocument().getName()
				  + " | " + ApplicationUtil.getMessage("page.label.order") + ": " + rationalizerContent.getOrder();
		referenceData.put("name", name);
		referenceData.put("title", ApplicationUtil.getMessage("page.label.content.history"));
		referenceData.put("isRationalizerSharedContent", false);
		List<HistoricalRationalizerDocumentContent> histRationalizerContentList = rationalizerContent.getContentHistOrderByCreated();
		List<RationalizerHistoryDto> contentHistoryList = new ArrayList<>();
		histRationalizerContentList.forEach(hist -> {
				RationalizerHistoryDto histDto = new RationalizerHistoryDto();
				histDto.setId(hist.getId());
				histDto.setUpdatedBy(hist.getUpdatedBy());
				histDto.setCreatedDate(hist.getCreated());
				contentHistoryList.add(histDto);
		});
		if(CollectionUtils.isEmpty(contentHistoryList)){
			referenceData.put("histRationalizerContentList", new ArrayList<>());
		}else{
			referenceData.put("histRationalizerContentList", contentHistoryList);
		}
	}

	private void generateReferenceDataForSharedHistory(Map<String, Object> referenceData, String rationalizerSharedContentGuid) {
		if( StringUtils.isNotEmpty(rationalizerSharedContentGuid) ) {
			RationalizerSharedContent rationalizerSharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(rationalizerSharedContentGuid);
			referenceData.put("title", ApplicationUtil.getMessage("page.label.shared.content.history"));
			referenceData.put("isRationalizerSharedContent", true);
			if (rationalizerSharedContent != null) {
				StringBuilder returnNameHtml = new StringBuilder();
				returnNameHtml.append("<div style=\"display: inline-block; padding-left: 12px;\"><i class=\"fa fa-circle mp-ico-embeddedcontent fa-mp-ed-ico\" data-toggle=\"tooltip\" title=\"").append(ApplicationUtil.getMessage("page.label.shared.content.object")).append("\"></i>&nbsp;&nbsp;").append(rationalizerSharedContent.getName()).append("</div>");
				referenceData.put("name", returnNameHtml);
				referenceData.put("rationalizerContent", rationalizerSharedContent);
				List<HistoricalRationalizerSharedContent> histRationalizerContentList = rationalizerSharedContent.getSharedContentHistOrderByCreated();
				List<RationalizerHistoryDto> contentHistoryList = new ArrayList<>();
				histRationalizerContentList.forEach(hist -> {
					if (StringUtils.isNotEmpty(hist.getTextContent())) {
						RationalizerHistoryDto histDto = new RationalizerHistoryDto();
						histDto.setId(hist.getId());
						histDto.setUpdatedBy(hist.getUpdatedBy());
						histDto.setCreatedDate(hist.getCreated());
						contentHistoryList.add(histDto);
					}
				});
				if (CollectionUtils.isEmpty(contentHistoryList)) {
					referenceData.put("histRationalizerContentList", new ArrayList<>());
				} else {
					referenceData.put("histRationalizerContentList", contentHistoryList);
				}
			} else {
				referenceData.put("name", StringUtils.EMPTY);
				referenceData.put("histRationalizerContentList", new ArrayList<>());
			}
		}
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new RationalizerContentHistoryWrapper();
	}
}
