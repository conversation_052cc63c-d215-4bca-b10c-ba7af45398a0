package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;

public class RationalizerDashboardBrandController extends AbstractBaseRationalizerDashboardController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";

    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

        RationalizerDashboardWrapper wrapper = new RationalizerDashboardWrapper();

        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(rationalizerApp);
            wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
        }

        return wrapper;
    }
}