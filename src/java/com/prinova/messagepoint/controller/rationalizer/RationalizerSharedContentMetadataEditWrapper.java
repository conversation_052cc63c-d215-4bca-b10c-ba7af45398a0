package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;

public class RationalizerSharedContentMetadataEditWrapper implements RationalizerMetadataEditWrapper {

	private static final long serialVersionUID = -8762045351897741463L;

	private RationalizerSharedContent rationalizerSharedContent;
	private MetadataFormEditWrapper		formWrapper;

	public RationalizerSharedContentMetadataEditWrapper(RationalizerSharedContent rationalizerSharedContent) {
		this.setRationalizerSharedContent(rationalizerSharedContent);
		if ( rationalizerSharedContent.getParsedContentForm() != null ) {
			this.formWrapper = new MetadataFormEditWrapper( rationalizerSharedContent.getParsedContentForm() );
		}
	}

	public RationalizerSharedContent getRationalizerSharedContent() {
		return rationalizerSharedContent;
	}
	public void setRationalizerSharedContent(RationalizerSharedContent rationalizerSharedContent) {
		this.rationalizerSharedContent = rationalizerSharedContent;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	@Override
	public String getMetatags() {
		return rationalizerSharedContent.getMetatags();
	}

	public void setMetatags(String metatags) {
		rationalizerSharedContent.setMetatags(metatags);
	}
}