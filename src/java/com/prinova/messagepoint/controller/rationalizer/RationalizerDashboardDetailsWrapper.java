package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerContent;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RationalizerDashboardDetailsWrapper implements Serializable {
    private String actionValue;
    private List<String> selectedIds = new ArrayList<>();
    private String objectName;

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public List<String> getSelectedIds() {
        return selectedIds;
    }

    public void setSelectedIds(List<String> selectedIds) {
        this.selectedIds = selectedIds;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public List<RationalizerContent> getSelectedContents() {
        return RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);
    }
}