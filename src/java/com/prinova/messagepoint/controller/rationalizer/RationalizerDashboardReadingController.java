package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.contentintelligence.FleschReadabilityLevelType;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.search.query.RangeOperation;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class RationalizerDashboardReadingController extends AbstractBaseRationalizerDashboardController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String REQ_PARAM_FORM_ITEM_ID = "formItemId";

    @Override
    protected Object formBackingObject(HttpServletRequest request) {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

        RationalizerDashboardReadingWrapper wrapper = new RationalizerDashboardReadingWrapper();

        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(rationalizerApp);
            wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());

            int readabilityTarget = Integer.parseInt(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ContentIntelligence.KEY_TargetFleschReadabilityLevel));
            FleschReadabilityLevelType readabilityTargetLevel = new FleschReadabilityLevelType(readabilityTarget);

            Double readabilityTargetValue = Double.valueOf(readabilityTargetLevel.getTopGradeLevelThreshold());

            String[] sourceDashboardCompareSelection = request.getParameterMap().get("sourceDashboardCompareSelection");
            String sourceDashboardCompareSelectionString = sourceDashboardCompareSelection != null && sourceDashboardCompareSelection.length > 0 ? sourceDashboardCompareSelection[0] : "-9";
            List<Map<String, String>> sourceMetadataFilterMapList = RationalizerUtil.processDashboardTreeSelections(rationalizerApplicationId, sourceDashboardCompareSelectionString);

            RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApp, true);

            List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.readability);

            Integer goodReading = rationalizerElasticSearchHandler.countContentWithReadabilityComparedToTarget(sourceMetadataFilterMapList, dashboardResultsFilterList, RangeOperation.LT, readabilityTargetValue);
            Integer poorReading = rationalizerElasticSearchHandler.countContentWithReadabilityComparedToTarget(sourceMetadataFilterMapList, dashboardResultsFilterList, RangeOperation.GTE, readabilityTargetValue);

            List<Integer> readingCountsList = new LinkedList<>();
            readingCountsList.add(goodReading == null ? 0 : goodReading);
            readingCountsList.add(poorReading == null ? 0 : poorReading);
            wrapper.setGraphData(readingCountsList);

            Float readingAverage = rationalizerElasticSearchHandler.computeReadabilityFleschReadingAverage(sourceMetadataFilterMapList, dashboardResultsFilterList);
            readingAverage = readingAverage == null ? 0 : readingAverage;

            String averageFleschScoreDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.reading.flesch.kincaid.score"), String.format("%.2f", readingAverage),
                    FleschReadabilityLevelType.getDescriptionFromGradeScore(readingAverage));
            wrapper.setAverageFleschScoreDisplayString(averageFleschScoreDisplayString);

            String averageFleschKincaidDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.reading.flesch.kincaid"),
                   ApplicationUtil.getMessage(readabilityTargetLevel.getDisplayMessageCode()));
            wrapper.setAverageFleschKincaidDisplayString(averageFleschKincaidDisplayString);
        }

        return wrapper;
    }
}