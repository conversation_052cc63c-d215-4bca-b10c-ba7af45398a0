package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;

public class RationalizerApplicationVisibilityWrapper implements Serializable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8998943035902822196L;
	private List<Long>					selectedVisibleUserIds = new ArrayList<>();
	private RationalizerApplication		application;
	private boolean 					isFullVisible = true;

	public RationalizerApplicationVisibilityWrapper(){
		super();
	}
	
	public RationalizerApplicationVisibilityWrapper(RationalizerApplication application){
		super();
		
		this.application = application;
		if (application != null) {
			if(!this.application.isFullVisibility()){
				for (User user: this.application.getVisibleUsersSorted()){
					selectedVisibleUserIds.add(user.getId());
				}
				this.setFullVisible(false);
			}
		}
	}

	public void setSelectedVisibleUserIds(List<Long> selectedVisibleUserIds) {
		this.selectedVisibleUserIds = selectedVisibleUserIds;
	}
	
	public List<Long> getSelectedVisibleUserIds() {
		return selectedVisibleUserIds;
	}
	
	public List<Long> getAllAvailUserIds(){
		List<Long> allAvailUserIds = new ArrayList<>();
		for (User user: User.findAllActiveUsersSorted()){
			allAvailUserIds.add(user.getId());
		}
		return allAvailUserIds;
	}

	public RationalizerApplication getRationalizerApplication() {
		return application;
	}

	public void setApplication(RationalizerApplication application) {
		this.application = application;
	}

	public boolean isFullVisible() {
		return isFullVisible;
	}

	public void setFullVisible(boolean isFullVisible) {
		this.isFullVisible = isFullVisible;
	}	
}
