package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportRationalizerDashboardBackgroundTask;
import com.prinova.messagepoint.platform.services.export.rationalizer.dashboard.RationalizerDashboardExportType;
import com.prinova.messagepoint.platform.services.rationalizer.CloneRationalizerAppBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.DeleteRationalizerAppBackgroundTask;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.triggerBrandUpdateBackgroundTaskIfNeeded;

public class AbstractBaseRationalizerDashboardController extends MessagepointController {
    private static final Log log = LogUtil.getLog(RationalizerDashboardDuplicatesController.class);
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String REQ_PARAM_ACTION = "action";
    private static final int ACTION_ADD_APPLICATION = 3;
    private static final int ACTION_UPDATE_APPLICATION = 4;
    private static final int ACTION_CLONE_APPLICATION = 7;
    private static final int ACTION_DELETE_REWRITE_APPLICATION = 8;
    private static final int ACTION_DELETE_APPLICATION = 9;
    private static final int ACTION_GENERATE_DASHBOARD_DUPLICATES_REPORT = 100;
    private static final int ACTION_GENERATE_DASHBOARD_SIMILARITY_REPORT = 101;
    public static final String SOURCE_DASHBOARD_COMPARE_SELECTION = "sourceDashboardCompareSelection";
    public static final String TARGET_DASHBOARD_COMPARE_SELECTION = "targetDashboardCompareSelection";

    @Override
    public Map<String, Object> referenceData(HttpServletRequest request) {
        Map<String, Object> referenceData = new HashMap<>();

        // filter application list by visibility
        referenceData.put("applications", RationalizerApplication.filterVisibleApplications(UserUtil.getPrincipalUser()));

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerApplication application = RationalizerApplication.findById(rationalizerApplicationId);

        referenceData.put("parsedDocumentMetadataFormDefinitionId", application != null && application.getParsedDocumentFormDefinition() != null ?
                application.getParsedDocumentFormDefinition().getId() : -1);
        referenceData.put("parsedContentMetadataFormDefinitionId", application != null && application.getParsedContentFormDefinition() != null ?
                application.getParsedContentFormDefinition().getId() : -1);

        if (application != null) {
            referenceData.put("application", application);
            RationalizerApplication clonedApplication = RationalizerApplication.findUniqueByParentId(application.getId());
            RationalizerApplication parentApplication = (RationalizerApplication) application.getParentObject();
            referenceData.put("hasClonedAppContext", (clonedApplication != null || parentApplication != null));

            if (clonedApplication != null) {
                referenceData.put("clonedApplication", clonedApplication);
            }
            if (parentApplication != null) {
                referenceData.put("parentApplication", parentApplication);
            }
            referenceData.put("navTreeBreadCrumbTrail", MetadataFormItem.buildRationalizerNavTreeBreadCrumbTrail(rationalizerApplicationId));
            referenceData.put("hasLinkedTouchpoint", application.getLinkedDocument() != null);

        }

        // BRAND
        referenceData.put("brandProfiles", BrandProfile.listAll());

        List<Document> documents = Document.findAllDocumentsAndProjectsVisible(true);
        referenceData.put("documents", documents);
        addDashboardReportReference(rationalizerApplicationId, referenceData);

        return referenceData;
    }

    public void addDashboardReportReference(long applicationId, Map<String, Object> referenceData) {}

    @Override
    public void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(MetadataFormDefinition.class, new IdCustomEditor<>(MetadataFormDefinition.class));
        binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
        binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
        binder.registerCustomEditor(BrandProfile.class, new IdCustomEditor<>( BrandProfile.class ));
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        Map<String, Object> params = new HashMap<>();
        if (rationalizerApplicationId == -1) { // Manage default rationalizer application
            RationalizerApplication applicationContext = UserUtil.getCurrentRationalizerApplicationContext();
            if (applicationContext == null || (applicationContext != null && !applicationContext.isVisible(UserUtil.getPrincipalUser()))) {
                applicationContext = RationalizerApplication.getUserLatest(UserUtil.getPrincipalUser());

                if (applicationContext != null) {
                    HashMap<String, String> contextAttr = new HashMap<>();
                    contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
                    UserUtil.updateUserContextAttributes(contextAttr);
                }
                params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext != null ? applicationContext.getId() : -2);
            } else {
                params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext.getId());
            }
            return new ModelAndView(new RedirectView(getSuccessView()), params);
        } else if (rationalizerApplicationId > 0) {
            RationalizerApplication applicationContext = RationalizerApplication.findById(rationalizerApplicationId);
            if (applicationContext == null) {
                params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);
                return new ModelAndView(new RedirectView(getSuccessView()), params);
            } else {
                HashMap<String, String> contextAttr = new HashMap<>();
                contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
                UserUtil.updateUserContextAttributes(contextAttr);
            }
        }
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.Dashboard);
        try {
            int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
            RationalizerDashboardWrapper command = (RationalizerDashboardWrapper) commandObj;
            switch (action) {
                case (ACTION_ADD_APPLICATION): {
                    analyticsEvent.setAction(Actions.AddApplication);

                    ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewApplication(
                            command.getApplicationName(),
                            command.getMetatags(),
                            command.getDescription());

                    Service createApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                    createApplicationService.execute(context);

                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        sb.append(" rationalizer application not created. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();

                        long newApplicationId = (Long) serviceResponse.getResultValueBean();
                        params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                }
                case (ACTION_UPDATE_APPLICATION): {
                    analyticsEvent.setAction(Actions.UpdateApplication);

                    BrandProfile existingApplicationProfile = command.getExistingApplicationBrandProfile();
                    BrandProfile selectedBrandProfile = command.getCurrentApplication().getBrandProfile();

                    ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateApplication(
                            command.getCurrentApplication().getId(),
                            command.getCurrentApplication().getName(),
                            command.getCurrentApplication().getMetatags(),
                            command.getCurrentApplication().getDescription(),
                            command.getCurrentApplication().getQueryFilterFormItemDefinitions(),
                            null,
                            command.getReSync());

                    Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                    updateApplicationService.execute(context);

                    if (Boolean.FALSE.equals(command.getReSync())) {
                        triggerBrandUpdateBackgroundTaskIfNeeded(existingApplicationProfile, selectedBrandProfile, command.getCurrentApplication());
                    }

                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        sb.append(" rationalizer application not updated. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();

                        long newApplicationId = (Long) serviceResponse.getResultValueBean();
                        params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                }
                case (ACTION_CLONE_APPLICATION): {
                    analyticsEvent.setAction(Actions.CloneApplication);
                    CloneRationalizerAppBackgroundTask task = new CloneRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), command.getCloneName());
                    MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

                    Map<String, Object> parms = new HashMap<>();
                    parms.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());

                    return new ModelAndView(new RedirectView(getSuccessView()), parms);
                }
                case (ACTION_DELETE_REWRITE_APPLICATION): {
                    analyticsEvent.setAction(Actions.DeleteRewriteApplication);

                    DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), null, true);
                    MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

                    RationalizerApplication applicationContext = RationalizerApplication.findById(command.getCurrentApplication().getParentObject().getId());

                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext != null ? applicationContext.getId() : -1);

                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                }
                case (ACTION_DELETE_APPLICATION): {
                    analyticsEvent.setAction(Actions.DeleteApplication);

                    RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
                    RationalizerApplication clonedRationalizerApp = RationalizerApplication.findUniqueByParentId(rationalizerApp.getId());
                    CountDownLatch latch = null;
                    if (clonedRationalizerApp != null) {
                        latch = new CountDownLatch(1);
                        DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(clonedRationalizerApp.getId(), UserUtil.getPrincipalUser(), latch, true);
                        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
                    }
                    DeleteRationalizerAppBackgroundTask task2 = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), latch, false);
                    MessagePointRunnableUtil.startThread(task2, Thread.MAX_PRIORITY);

                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);

                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                }
                case (ACTION_GENERATE_DASHBOARD_DUPLICATES_REPORT): {
                    analyticsEvent.setAction(Actions.GenerateReport);
                    String sourceSelections = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    String targetSelections = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    return runRationalizerDashboardExportTask(sourceSelections, targetSelections, command, RationalizerDashboardExportType.DUPLICATES);
                }
                case (ACTION_GENERATE_DASHBOARD_SIMILARITY_REPORT): {
                    analyticsEvent.setAction(Actions.GenerateReport);
                    String sourceSelections = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    String targetSelections = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    return runRationalizerDashboardExportTask(sourceSelections, targetSelections, command, RationalizerDashboardExportType.SIMILARITY);
                }
                default:
                    break;
            }

        } finally {
            analyticsEvent.send();
        }

        return null;
    }

    private ModelAndView runRationalizerDashboardExportTask(String sourceSelections, String targetSelections, RationalizerDashboardWrapper command,
                                                            RationalizerDashboardExportType dashboardExportType) {
        long rationalizerApplicationId = command.getCurrentApplication().getId();
        ExportRationalizerDashboardBackgroundTask task = new ExportRationalizerDashboardBackgroundTask(
                command.getExportId(),
                rationalizerApplicationId,
                command.getSelectedIds(),
                sourceSelections,
                targetSelections,
                UserUtil.getPrincipalUser(),
                dashboardExportType);
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
        Map<String, Object> params = new HashMap<>();

        if (rationalizerApplicationId != -1L) {
            params.put(REQ_PARAM_RATIONALIZER_APP_ID, rationalizerApplicationId);
        }
        params.put(SOURCE_DASHBOARD_COMPARE_SELECTION, sourceSelections);
        params.put(TARGET_DASHBOARD_COMPARE_SELECTION, targetSelections);
        params.put(REQ_PARAM_ACTION, command.getActionValue());

        return new ModelAndView(new RedirectView(getSuccessView()), params);
    }
}