package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.wrapper.elastic.dto.ExpandSharedContentsDto;
import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;

import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryComponent;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

public class RationalizerQueryEditWrapper implements Serializable {

	private static final long serialVersionUID = -288914342836914507L;
	private static final Log log = LogUtil.getLog(RationalizerQueryEditWrapper.class);

	private String								actionValue;
	
	private List<String> selectedIds;
	
	private RationalizerQuery					rationalizerQuery;
	
	private List<RationalizerQueryComponent>	queryComponents;
	
	private String								exportId 								= DateUtil.exportDateTimeStamp();
	private String								exportName								= "";
	
	private int									modifyContentAction						= 1;
	private int									modifyContentType 						= 1;
	private String								modifyContentMetadataConnectorValue;
	private String								modifyContentSearchValue;
	private String								modifyContentReplaceValue;
	
	private int									modifyDocumentAction					= 1;
	private int									modifyDocumentType 						= 1;
	private String								modifyDocumentMetadataConnectorValue;
	private String								modifyDocumentSearchValue;
	private String								modifyDocumentReplaceValue;
	
	private String								documentTags;
	private String								contentTags;

	private Boolean expandSharedContents = false;
	private int expandSCZoneConnectorSelect = 0;
	private String expandSCZoneConnectorText = "";
	private int expandSCMessageNameSelect = 0;
	private String expandSCMessageNameText = "";

	@SuppressWarnings("unchecked")
	public RationalizerQueryEditWrapper(RationalizerQuery rationalizerQuery, boolean lazyDecorate) {
		super();
		
		this.rationalizerQuery = rationalizerQuery;
		
		if ( lazyDecorate )
			this.queryComponents = LazyList.decorate(new ArrayList<RationalizerQueryComponent>(), FactoryUtils.instantiateFactory(RationalizerQueryComponent.class));
		else
			this.queryComponents = new ArrayList<>();
		
		this.queryComponents.addAll( rationalizerQuery.getQueryComponents() );

		ExpandSharedContentsDto expandSharedContentsDto = ExpandSharedContentsDto.buildFromRationalizerQueryId(rationalizerQuery.getId());
		this.expandSharedContents = expandSharedContentsDto.isExpandSharedContents();
		this.expandSCZoneConnectorSelect = expandSharedContentsDto.getZoneConnectorFilterOption().getId();
		this.expandSCZoneConnectorText = expandSharedContentsDto.getZoneConnector();
		this.expandSCMessageNameSelect = expandSharedContentsDto.getMessageNameFilterOption().getId();
		this.expandSCMessageNameText = expandSharedContentsDto.getMessageName();
	}

	public void saveContentConsolidationOptions() {
		if (this.rationalizerQuery == null) {
			return;
		}

		try {
			ExpandSharedContentsDto expandSharedContentsDto = ExpandSharedContentsDto.buildFromRationalizerQueryEditWrapper(this);

			this.rationalizerQuery.setOtherSettings(
					expandSharedContentsDto.saveToRationalizerQueryOtherSettings(
							this.rationalizerQuery.getOtherSettings()
					)
			);
			this.rationalizerQuery.save();
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}


	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<String> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<String> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public RationalizerQuery getRationalizerQuery() {
		return rationalizerQuery;
	}
	public void setRationalizerQuery(RationalizerQuery rationalizerQuery) {
		this.rationalizerQuery = rationalizerQuery;
	}

	public List<RationalizerQueryComponent> getQueryComponents() {
		return queryComponents;
	}
	public void setQueryComponents(List<RationalizerQueryComponent> queryComponents) {
		this.queryComponents = queryComponents;
	}

	public String getExportId() {
		return exportId;
	}
	public void setExportId(String exportId) {
		this.exportId = exportId;
	}

	public String getExportName() {
		return exportName;
	}
	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public int getModifyContentAction() {
		return modifyContentAction;
	}
	public void setModifyContentAction(int modifyContentAction) {
		this.modifyContentAction = modifyContentAction;
	}

	public int getModifyContentType() {
		return modifyContentType;
	}
	public void setModifyContentType(int modifyContentType) {
		this.modifyContentType = modifyContentType;
	}

	public String getModifyContentMetadataConnectorValue() {
		return modifyContentMetadataConnectorValue;
	}
	public void setModifyContentMetadataConnectorValue(String modifyContentMetadataConnectorValue) {
		this.modifyContentMetadataConnectorValue = modifyContentMetadataConnectorValue;
	}

	public String getModifyContentSearchValue() {
		return modifyContentSearchValue;
	}
	public void setModifyContentSearchValue(String modifyContentSearchValue) {
		this.modifyContentSearchValue = modifyContentSearchValue;
	}

	public String getModifyContentReplaceValue() {
		return modifyContentReplaceValue;
	}
	public void setModifyContentReplaceValue(String modifyContentReplaceValue) {
		this.modifyContentReplaceValue = modifyContentReplaceValue;
	}

	public int getModifyDocumentAction() {
		return modifyDocumentAction;
	}
	public void setModifyDocumentAction(int modifyDocumentAction) {
		this.modifyDocumentAction = modifyDocumentAction;
	}
	
	public int getModifyDocumentType() {
		return modifyDocumentType;
	}
	public void setModifyDocumentType(int modifyDocumentType) {
		this.modifyDocumentType = modifyDocumentType;
	}

	public String getModifyDocumentMetadataConnectorValue() {
		return modifyDocumentMetadataConnectorValue;
	}
	public void setModifyDocumentMetadataConnectorValue(String modifyDocumentMetadataConnectorValue) {
		this.modifyDocumentMetadataConnectorValue = modifyDocumentMetadataConnectorValue;
	}

	public String getModifyDocumentSearchValue() {
		return modifyDocumentSearchValue;
	}
	public void setModifyDocumentSearchValue(String modifyDocumentSearchValue) {
		this.modifyDocumentSearchValue = modifyDocumentSearchValue;
	}

	public String getModifyDocumentReplaceValue() {
		return modifyDocumentReplaceValue;
	}
	public void setModifyDocumentReplaceValue(String modifyDocumentReplaceValue) {
		this.modifyDocumentReplaceValue = modifyDocumentReplaceValue;
	}

	public String getDocumentTags() {
		return documentTags;
	}
	public void setDocumentTags(String documentTags) {
		this.documentTags = documentTags;
	}
	
	public String getContentTags() {
		return contentTags;
	}
	public void setContentTags(String contentTags) {
		this.contentTags = contentTags;
	}

	public Boolean getExpandSharedContents() {
		return expandSharedContents;
	}

	public void setExpandSharedContents(Boolean expandSharedContents) {
		this.expandSharedContents = expandSharedContents;
	}

	public int getExpandSCZoneConnectorSelect() {
		return expandSCZoneConnectorSelect;
	}

	public void setExpandSCZoneConnectorSelect(int expandSCZoneConnectorSelect) {
		this.expandSCZoneConnectorSelect = expandSCZoneConnectorSelect;
	}

	public String getExpandSCZoneConnectorText() {
		return expandSCZoneConnectorText;
	}

	public void setExpandSCZoneConnectorText(String expandSCZoneConnectorText) {
		this.expandSCZoneConnectorText = expandSCZoneConnectorText;
	}

	public int getExpandSCMessageNameSelect() {
		return expandSCMessageNameSelect;
	}

	public void setExpandSCMessageNameSelect(int expandSCMessageNameSelect) {
		this.expandSCMessageNameSelect = expandSCMessageNameSelect;
	}

	public String getExpandSCMessageNameText() {
		return expandSCMessageNameText;
	}

	public void setExpandSCMessageNameText(String expandSCMessageNameText) {
		this.expandSCMessageNameText = expandSCMessageNameText;
	}
}