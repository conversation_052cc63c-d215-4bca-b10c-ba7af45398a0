package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.tasks.TaskListItemFilterType;
import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.metadata.*;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.export.GenerateRationalizerObjectExportService;
import com.prinova.messagepoint.platform.services.rationalizer.*;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CountDownLatch;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.triggerBrandUpdateBackgroundTaskIfNeeded;

public class RationalizerContentListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerContentListController.class);

	public static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
	public static final String REQ_PARAM_FORM_ITEM_ID = "formItemId";
	public static final String REQ_PARAM_TYPE = "type";
	public static final String REQ_PARAM_ACTION = "action";

	public static final int ACTION_UPDATE = 1;
	public static final int ACTION_DELETE = 2;
	public static final int ACTION_ADD_APPLICATION = 3;
	public static final int ACTION_UPDATE_APPLICATION = 4;
	public static final int ACTION_ADD_DOCUMENT = 5;
	public static final int ACTION_GENERATE_REPORT = 6;
	public static final int ACTION_CLONE_APPLICATION = 7;
	public static final int ACTION_DELETE_REWRITE_APPLICATION = 8;
	public static final int ACTION_DELETE_APPLICATION = 9;
	public static final int ACTION_REASSIGN = 12;
	public static final int ACTION_RELEASE_FOR_APPROVAL = 13;
	public static final int ACTION_APPROVE = 14;
	public static final int ACTION_REJECT = 15;
	public static final int ACTION_ACTIVATE = 16;
	public static final int ACTION_DEACTIVATE = 19;
	public static final int ACTION_UPDATE_ZONE_CONNECTOR = 30;

	private String editView;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		//referenceData.put("applications", RationalizerApplication.findAll() );
		// filter application list by visibility
		referenceData.put("applications", RationalizerApplication.filterVisibleApplications(UserUtil.getPrincipalUser()) );
		
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		
		RationalizerApplication application = RationalizerApplication.findById(rationalizerApplicationId);

		referenceData.put("parsedDocumentMetadataFormDefinitionId", application != null && application.getParsedDocumentFormDefinition() != null ?
																application.getParsedDocumentFormDefinition().getId() : -1 );
		referenceData.put("parsedContentMetadataFormDefinitionId", application != null && application.getParsedContentFormDefinition() != null ?
																application.getParsedContentFormDefinition().getId() : -1 );

		boolean hasAssignedWorkflow = application.getRationalizerWorkflow() != null ||
                !Task.findAllByRationalizerItemTypeAndAppId(TaskListItemFilterType.ID_RATIONALIZER_CONTENT, application.getId()).isEmpty();
		referenceData.put("hasAssignedWorkflow", hasAssignedWorkflow);
		referenceData.put("documentsExist", application != null && application.computeDocumentCount() > 0);
		StatusPollingBackgroundTask task = null;
		if ( application != null ){
			task = StatusPollingBackgroundTask.findLastestByNameAndByUser(UserUtil.getPrincipalUser(), StatusPollingBackgroundTask.SUB_TYPE_RATIONLIZER_DOCUMENT_EXPORT);
		}
		referenceData.put("report", task);
		if (task != null) {
			referenceData.put("reportType", FileUtil.getFileExtension(task.getOutputFilename()));
		}
		if(application != null){
			referenceData.put("application", application);
			RationalizerApplication clonedApplication = RationalizerApplication.findUniqueByParentId(application.getId());
			RationalizerApplication parentApplication = (RationalizerApplication)application.getParentObject();
			referenceData.put("hasClonedAppContext", (clonedApplication != null || parentApplication != null));
			
			if(clonedApplication != null){
				referenceData.put("clonedApplication", clonedApplication);
			}
			if(parentApplication != null){
				referenceData.put("parentApplication", parentApplication);
			}

			referenceData.put("navTreeBreadCrumbTrail", MetadataFormItem.buildRationalizerNavTreeBreadCrumbTrail(rationalizerApplicationId));
		}

		// BRAND
		referenceData.put("brandProfiles", BrandProfile.listAll());
		
		// Filters
		List<AssignmentFilterType> contentAssignmentFilterTypes = AssignmentFilterType.listAllStandard();
		referenceData.put("contentAssignmentFilterTypes", contentAssignmentFilterTypes);
		
		List<RationalizerContentStatusFilterType> contentStatusFilterTypes = RationalizerContentStatusFilterType.listAll();
		referenceData.put("contentStatusFilterTypes", contentStatusFilterTypes);
		
		List<RationalizerContentStateFilterType> contentStateFilterTypes = RationalizerContentStateFilterType.listAll();
		referenceData.put("contentStateFilterTypes", contentStateFilterTypes);

		// Task metadata form definition
		referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
		referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

		List<MetadataFormItemDefinition> contentFormItemDefinitions = new ArrayList<>();
		if (application.getParsedContentFormDefinition() != null)
			contentFormItemDefinitions.addAll(application.getParsedContentFormDefinition().getFormItemDefinitions());

		List<MetadataFormItemDefinition> modifyContentFormItemDefinitions = new ArrayList<>();
		for (MetadataFormItemDefinition currentItem : contentFormItemDefinitions) {
			if (currentItem.getPrimaryConnector() == null) {
				continue;
			}
			if ("zone_connector".equalsIgnoreCase(currentItem.getPrimaryConnector())) {
				modifyContentFormItemDefinitions.add(currentItem);
				break;
			}
		}

		referenceData.put("modifyContentFormItemDefinitions", modifyContentFormItemDefinitions);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(MetadataFormDefinition.class, new IdCustomEditor<>(MetadataFormDefinition.class));
		binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(BrandProfile.class, new IdCustomEditor<>(BrandProfile.class));
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		
		RationalizerContentListWrapper wrapper = new RationalizerContentListWrapper();
		
		if ( rationalizerApplicationId > 0 ){
			RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
			wrapper.setCurrentApplication(rationalizerApp);
			if(rationalizerApp != null){
				wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
			}
		}
		
		return wrapper;
    }

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

		Map<String, Object> params = new HashMap<>();
		if (rationalizerApplicationId == -1) { // Manage default rationalizer application
			RationalizerApplication applicationContext = UserUtil.getCurrentRationalizerApplicationContext();
			if ( applicationContext == null || (applicationContext != null && !applicationContext.isVisible(UserUtil.getPrincipalUser()))){
				applicationContext = RationalizerApplication.getUserLatest(UserUtil.getPrincipalUser());
				
				if ( applicationContext != null ) {
					HashMap<String,String> contextAttr = new HashMap<>();
					contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
					UserUtil.updateUserContextAttributes(contextAttr);
				}
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext != null ? applicationContext.getId() : -2);
			} else{ 
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext.getId());
			}
			return new ModelAndView(new RedirectView(getSuccessView()), params);
		}else if(rationalizerApplicationId > 0){
			RationalizerApplication applicationContext = RationalizerApplication.findById(rationalizerApplicationId);
			if(applicationContext == null){
				params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);
				return new ModelAndView(new RedirectView(getSuccessView()), params);
			} else {
				HashMap<String, String> contextAttr = new HashMap<>();
				contextAttr.put(UserUtil.CONTEXT_KEY_RATIONALIZER_APPLICATION_CONTEXT, String.valueOf(applicationContext.getId()));
				UserUtil.updateUserContextAttributes(contextAttr);
			}
		}
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.ContentList);

		try {
			int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
			RationalizerContentListWrapper command = (RationalizerContentListWrapper) commandObj;

			User principalUser = UserUtil.getPrincipalUser();
			User requestor = User.findById(principalUser.getId());

			switch (action) {
				case (ACTION_DELETE): {
					analyticsEvent.setAction(Actions.Delete);
					ServiceExecutionContext context = BulkDeleteRationalizerContentService.createContext(command.getSelectedContentList());
					Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteRationalizerContentService.SERVICE_NAME, BulkDeleteRationalizerContentService.class);
					deleteModelService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(BulkDeleteRationalizerContentService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer document content is not deleted. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView( new RedirectView( getSuccessView() ), getSuccessViewParams(request) );
					}
				}
				case (ACTION_ADD_APPLICATION): {
					analyticsEvent.setAction(Actions.AddApplication);

					ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewApplication(
															command.getApplicationName(),
															command.getMetatags(),
															command.getDescription());

					Service createApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
					createApplicationService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer application not created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> params = new HashMap<>();

						long newApplicationId = (Long)serviceResponse.getResultValueBean();
						params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

						return new ModelAndView( new RedirectView( getSuccessView() ), params );
					}
				}
				case (ACTION_UPDATE_APPLICATION): {
					analyticsEvent.setAction(Actions.UpdateApplication);

					BrandProfile existingApplicationProfile = command.getExistingApplicationBrandProfile();
					BrandProfile selectedBrandProfile = command.getCurrentApplication().getBrandProfile();

					ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateApplication(
															command.getCurrentApplication().getId(),
															command.getCurrentApplication().getName(),
															command.getCurrentApplication().getMetatags(),
															command.getCurrentApplication().getDescription(),
															command.getCurrentApplication().getQueryFilterFormItemDefinitions(),
															null,
															command.getReSync());

					Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
					updateApplicationService.execute(context);

					if (Boolean.FALSE.equals(command.getReSync())) {
						triggerBrandUpdateBackgroundTaskIfNeeded(existingApplicationProfile, selectedBrandProfile, command.getCurrentApplication());
					}

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer application not updated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> params = new HashMap<>();

						long newApplicationId = (Long)serviceResponse.getResultValueBean();
						params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

						return new ModelAndView( new RedirectView( getSuccessView() ), params );
					}
				}
				case (ACTION_ADD_DOCUMENT): {
					analyticsEvent.setAction(Actions.AddDocument);

					ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewDocument(
															command.getDocumentName(),
															command.getCurrentApplication().getId());

					Service newDocumentService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
					newDocumentService.execute(context);

					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" rationalizer document not created. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> params = new HashMap<>();
						params.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());
						return new ModelAndView( new RedirectView( getSuccessView() ), params );
					}
				}

				case (ACTION_GENERATE_REPORT): {
					/**
					 * ACTION_GENERATE_REPORT
					 */
					analyticsEvent.setAction(Actions.GenerateReport);
					AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Rationalizer);
					auditEvent.setAction(Actions.GenerateReport);
					try {

						ServiceExecutionContext context = GenerateRationalizerObjectExportService.createContext(
								command.getExportId(),
								command.getCurrentApplication().getId(),
								true,
								RationalizerApplication.class,
								UserUtil.getPrincipalUser(),
								command.getExtType(),
								command.getHistoryExtType());
						Service service = MessagepointServiceFactory.getInstance()
								.lookupService(GenerateRationalizerObjectExportService.SERVICE_NAME,
										GenerateRationalizerObjectExportService.class);
						service.execute(context);

						if (!context.getResponse().isSuccessful()) {
							log.error(" unexpected exception when invoking GenerateRationalizerObjectExportService execute method");
							ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
							return showForm(request, response, errors);
						} else {
							return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
						}
					}finally {
						auditEvent.send();
					}
				}
				case (ACTION_CLONE_APPLICATION) : {
					analyticsEvent.setAction(Actions.CloneApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					String name = rationalizerApp.getName();
					CloneRationalizerAppBackgroundTask task = new CloneRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), command.getCloneName());
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

					Map<String, Object> parms = new HashMap<>();
					parms.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());

					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_DELETE_REWRITE_APPLICATION) : {
					analyticsEvent.setAction(Actions.DeleteRewriteApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					String name = rationalizerApp.getName();
					DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), null, true);
					MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

					RationalizerApplication applicationContext = RationalizerApplication.findById(command.getCurrentApplication().getParentObject().getId());

					Map<String, Object> parms = new HashMap<>();
					parms.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext!=null?applicationContext.getId():-1);

					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}
				case (ACTION_DELETE_APPLICATION) : {
					analyticsEvent.setAction(Actions.DeleteApplication);

					RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
					RationalizerApplication clonedRationalizerApp = RationalizerApplication.findUniqueByParentId(rationalizerApp.getId());
					CountDownLatch latch = null;
					if(clonedRationalizerApp != null){
						latch = new CountDownLatch(1);
						DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(clonedRationalizerApp.getId(), UserUtil.getPrincipalUser(), latch, true);
						MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
					}
					String name = rationalizerApp.getName();
					DeleteRationalizerAppBackgroundTask task2 = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), latch, false);
					MessagePointRunnableUtil.startThread(task2, Thread.MAX_PRIORITY);

					Map<String, Object> params = new HashMap<>();
					params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);

					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}
				case (ACTION_ACTIVATE): {
					/**
					 * ACTION_ACTIVATE:
					 *
					 */
					analyticsEvent.setAction(Actions.Activate);

					List<RationalizerDocumentContent> contentList = command.getSelectedContentList();
					ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), contentList.toArray(new RationalizerDocumentContent[]{}));
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (RationalizerDocumentContent content : contentList) {
							sb.append(" id ").append(content.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  rationalizer content are not activated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_DEACTIVATE): {
					/**
					 * ACTION_DEACTIVATE:
					 *
					 */
					analyticsEvent.setAction(Actions.Deactivate);

					List<RationalizerDocumentContent> contentList = command.getSelectedContentList();
					ServiceExecutionContext context = DeactivateRationalizerContentService.createContext(contentList, command.getAssignedToUser().getId(), requestor.getId(), command.getUserNote());
					Service service = MessagepointServiceFactory.getInstance().lookupService(DeactivateRationalizerContentService.SERVICE_NAME, DeactivateRationalizerContentService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(DeactivateRationalizerContentService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						for (RationalizerDocumentContent content : contentList) {
							sb.append(" id ").append(content.getId());
						}
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  rationalizer content are not deactivated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_RELEASE_FOR_APPROVAL): {
					analyticsEvent.setAction(Actions.ReleaseForApproval);

					List<RationalizerDocumentContent> contentList = command.getSelectedContentList();
					ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, command.getUserNote(), contentList.toArray(new RationalizerDocumentContent[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" release for approval was not completed. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_APPROVE): {
					analyticsEvent.setAction(Actions.Approve);

					List<RationalizerDocumentContent> contentList = command.getSelectedContentList();
					ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, command.getUserNote(), false,  contentList.toArray(new RationalizerDocumentContent[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowApproveService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_REJECT): {
					analyticsEvent.setAction(Actions.Reject);

					List<RationalizerDocumentContent> contentList = command.getSelectedContentList();
					ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, command.getAssignedToUser(), command.getUserNote(), contentList.toArray(new RationalizerDocumentContent[]{}));

					Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(WorkflowRejectService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" approvals not applied. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_REASSIGN): {
					/**
					 * ACTION_REASSIGN: - reassign to another user
					 *
					 */
					analyticsEvent.setAction(Actions.Reassign);

					ServiceExecutionContext context = RationalizerContentUnlockModelService.createReassignContext(command.getSelectedContentList(),
							requestor.getId(),
							command.getAssignedToUser().getId(),
							command.getUserNote(),false);
					Service service = MessagepointServiceFactory.getInstance().lookupService(RationalizerContentUnlockModelService.SERVICE_NAME,
							RationalizerContentUnlockModelService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(RationalizerContentUnlockModelService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor=").append(requestor.getUsername());
						sb.append("  rationalizer content is not reassigned. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					}
				}
				case (ACTION_UPDATE_ZONE_CONNECTOR): {



					analyticsEvent.setAction(Actions.SetContentMetadata);

					ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForSetContentMetadata(
							command.getModifyContentMetadataConnectorValue(),
							command.getModifyContentMetadataReplaceValue(),
							command.getSelectedIds(),
							command.getCurrentApplication().getId(),
							//command.getFormWrapper().getMetadataItemPreValueMap()
							null
					);
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (serviceResponse.isSuccessful()) {
						Map<String, Object> params = new HashMap<>();
						//params.put(REQ_PARAM_RATIONALIZER_DOCUMENT_ID, wrapper.getRationalizerDocument().getId());

						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
					} else {
						StringBuilder sb = new StringBuilder();
						sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" Rationalizer Metadata was not updated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
						return super.showForm(request, response, errors);
					}

				}
				default:
					break;
			}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request) throws Exception {
		Map<String, Object> params = new HashMap<>();
		
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		if ( rationalizerApplicationId != -1L )
			params.put(REQ_PARAM_RATIONALIZER_APP_ID, rationalizerApplicationId);
		
		String selectionIdsString = ServletRequestUtils.getStringParameter(request, REQ_PARAM_FORM_ITEM_ID, "-9");
		params.put(REQ_PARAM_FORM_ITEM_ID, selectionIdsString);

		return params;
	}

	public String getEditView() {
		return this.editView;
	}

	public void setEditView(String editView) {
		this.editView = editView;
	}
}