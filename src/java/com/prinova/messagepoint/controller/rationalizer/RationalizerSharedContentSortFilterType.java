package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.ArrayList;
import java.util.List;

public class RationalizerSharedContentSortFilterType extends StaticType {
    private static final long serialVersionUID = 546293711499914404L;

    public static final int ID_ITEMS_ASC = 0;
    public static final int ID_ITEMS_DESC = 1;
    public static final int ID_NAME_ASC = 2;
    public static final int ID_NAME_DESC = 3;

    public static final String MESSAGE_ITEMS_ASC = "page.label.list.filter.type.items.asc";
    public static final String MESSAGE_ITEMS_DESC = "page.label.list.filter.type.items.desc";
    public static final String MESSAGE_NAME_ASC = "page.label.list.filter.type.name.asc";
    public static final String MESSAGE_NAME_DESC = "page.label.list.filter.type.name.desc";

    public RationalizerSharedContentSortFilterType(Integer id) {
        super();
        switch (id) {
            case ID_ITEMS_ASC:
                this.setId(ID_ITEMS_ASC);
                this.setName(ApplicationUtil.getMessage(MESSAGE_ITEMS_ASC));
                this.setDisplayMessageCode(MESSAGE_ITEMS_ASC);
                break;
            case ID_ITEMS_DESC:
                this.setId(ID_ITEMS_DESC);
                this.setName(ApplicationUtil.getMessage(MESSAGE_ITEMS_DESC));
                this.setDisplayMessageCode(MESSAGE_ITEMS_DESC);
                break;
            case ID_NAME_ASC:
                this.setId(ID_NAME_ASC);
                this.setName(ApplicationUtil.getMessage(MESSAGE_NAME_ASC));
                this.setDisplayMessageCode(MESSAGE_NAME_ASC);
                break;
            case ID_NAME_DESC:
                this.setId(ID_NAME_DESC);
                this.setName(ApplicationUtil.getMessage(MESSAGE_NAME_DESC));
                this.setDisplayMessageCode(MESSAGE_NAME_DESC);
                break;
            default:
                break;
        }
    }

    public RationalizerSharedContentSortFilterType(String name) {
        super();
        if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_ITEMS_ASC))) {
            this.setId(ID_ITEMS_ASC);
            this.setName(ApplicationUtil.getMessage(MESSAGE_ITEMS_ASC));
            this.setDisplayMessageCode(MESSAGE_ITEMS_ASC);
        }
        if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_ITEMS_DESC))) {
            this.setId(ID_ITEMS_DESC);
            this.setName(ApplicationUtil.getMessage(MESSAGE_ITEMS_DESC));
            this.setDisplayMessageCode(MESSAGE_ITEMS_DESC);
        }
        if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_NAME_ASC))) {
            this.setId(ID_NAME_ASC);
            this.setName(ApplicationUtil.getMessage(MESSAGE_NAME_ASC));
            this.setDisplayMessageCode(MESSAGE_NAME_ASC);
        }
        if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_NAME_DESC))) {
            this.setId(ID_NAME_DESC);
            this.setName(ApplicationUtil.getMessage(MESSAGE_NAME_DESC));
            this.setDisplayMessageCode(MESSAGE_NAME_DESC);
        }
    }

    public static List<RationalizerSharedContentSortFilterType> listAll() {
        List<RationalizerSharedContentSortFilterType> allListFilterTypes = new ArrayList<>();

        RationalizerSharedContentSortFilterType listFilterType = null;

        listFilterType = new RationalizerSharedContentSortFilterType(ID_ITEMS_ASC);
        allListFilterTypes.add(listFilterType);

        listFilterType = new RationalizerSharedContentSortFilterType(ID_ITEMS_DESC);
        allListFilterTypes.add(listFilterType);

        listFilterType = new RationalizerSharedContentSortFilterType(ID_NAME_ASC);
        allListFilterTypes.add(listFilterType);

        listFilterType = new RationalizerSharedContentSortFilterType(ID_NAME_DESC);
        allListFilterTypes.add(listFilterType);

        return allListFilterTypes;
    }
}
