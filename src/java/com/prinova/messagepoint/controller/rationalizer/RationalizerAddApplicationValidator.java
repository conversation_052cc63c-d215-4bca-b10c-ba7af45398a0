package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.collections.CollectionUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.Errors;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

public class RationalizerAddApplicationValidator extends MessagepointInputValidator {

    private static final Log log = LogUtil.getLog(RationalizerAddApplicationValidator.class);

    @Override
    public boolean supports(Class clazz) {
        if(clazz.getTypeName().equals(RationalizerDashboardWrapper.class.getTypeName())) {
            return true;
        }
        if(clazz.getTypeName().equals(RationalizerDashboardReadingWrapper.class.getTypeName())) {
            return true;
        }
        if(clazz.getTypeName().equals(RationalizerDashboardSentimentWrapper.class.getTypeName())) {
            return true;
        }

        return false;
    }

    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        int action = -1;
        String applicationName = null;
        Class wrapperClass = commandObj.getClass();
        try {
            Method method = wrapperClass.getMethod("getActionValue", new Class[] {});
            String result = (String) method.invoke(commandObj, new Object[] {});
            if (result != null && !result.trim().isEmpty()) {
                action = Integer.valueOf(result).intValue();
            }

            // get application name
            method = wrapperClass.getMethod("getApplicationName", new Class[] {});
            applicationName = (String) method.invoke(commandObj, new Object[] {});
        } catch (NoSuchMethodException e) {
            log.error("Unexpected error when calling wrapper methods.", e);
        } catch (IllegalAccessException e) {
            log.error("Unexpected error when calling wrapper methods.", e);
        } catch (InvocationTargetException e) {
            log.error("Unexpected error when calling wrapper methods.", e);
        }


        final String label = ApplicationUtil.getMessage("page.label.name");

        if ( action == RationalizerDashboardController.ACTION_ADD_APPLICATION) {
            if (applicationName == null || applicationName.trim().isEmpty()) {
                errors.reject("error.input.mandatory", new String[]{label}, "");
                return;
            }

            // check if application name is unique
            List<RationalizerApplication> applicationList = RationalizerApplication.findAll();
            if (CollectionUtils.isNotEmpty(applicationList)) {
                for (RationalizerApplication rationalizerApplication : applicationList) {
                    if (!StringUtil.isEmptyOrNull(rationalizerApplication.getName())) {
                        if (rationalizerApplication.getName().equalsIgnoreCase(applicationName)) {
                            errors.reject("error.message.rationalizer.name.unique", new String[]{}, "");
                            break;
                        }
                    }
                }
            }

            MessagepointInputValidationUtil.validateStringValue(label, applicationName == null ? applicationName :
                    applicationName.trim(), true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
        }
    }
}
