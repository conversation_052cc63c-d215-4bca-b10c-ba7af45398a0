package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class RationalizerDocumentContentMetadataEditWrapper implements RationalizerMetadataEditWrapper {

	private static final long serialVersionUID = -8762045351897741463L;

	private RationalizerDocumentContent rationalizerDocumentContent;
	private MetadataFormEditWrapper		formWrapper;

	public RationalizerDocumentContentMetadataEditWrapper(RationalizerDocumentContent documentContent) {
		this.setRationalizerDocumentContent(documentContent);

		documentContent.updateMessageNameInContentMetadata();
		documentContent.updateZoneConnectorInContentMetadata();

		final MetadataForm contentForm = documentContent.getParsedContentForm();
		if (contentForm == null) {
			return;
		}

		formWrapper = new MetadataFormEditWrapper(contentForm);
		Comparator<MetadataFormItemDefinition> compareMetaFormItem = Comparator
				.comparingInt(MetadataFormItemDefinition::getOriginTypeId)
				.thenComparing(MetadataFormItemDefinition::getPrimaryConnector);

		List<MetadataFormItem> sortedList = formWrapper.getMetadataFormItemsList().stream().sorted((s1, s2) -> compareMetaFormItem.compare(s1.getItemDefinition(), s2.getItemDefinition())).collect(Collectors.toList());
		formWrapper.setMetadataFormItemsList(sortedList);
	}

	public RationalizerDocumentContent getRationalizerDocumentContent() {
		return rationalizerDocumentContent;
	}
	public void setRationalizerDocumentContent(RationalizerDocumentContent rationalizerDocumentContent) {
		this.rationalizerDocumentContent = rationalizerDocumentContent;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}

	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	@Override
	public String getMetatags() {
		return rationalizerDocumentContent.getMetatags();
	}

	public void setMetatags(String metatags) {
		rationalizerDocumentContent.setMetatags(metatags);
	}
}