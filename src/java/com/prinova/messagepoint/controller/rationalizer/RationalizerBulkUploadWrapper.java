package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;

import java.io.Serializable;

public class RationalizerBulkUploadWrapper implements Serializable {

    private static final long serialVersionUID = -8998943035902822196L;
    private RationalizerApplication application;
    private Boolean allowSpaces;
    private Boolean allowLowerCase;
    private String maxLength;
    private Boolean generateSharedContents;
    private String generatedSharedContentPrefix;
    private Boolean includeContentCharsInGeneratedSharedContent;
    private Integer generatedSharedContentMinWordCount;

    public RationalizerBulkUploadWrapper() {
        super();
    }

    public RationalizerBulkUploadWrapper(
            RationalizerApplication application,
            Boolean allowSpaces,
            Boolean allowLowerCase,
            String maxLength,
            Boolean generateSharedContents,
            String generatedSharedContentPrefix,
            Boolean includeContentCharsInGeneratedSharedContent,
            Integer generatedSharedContentMinWordCount
    ) {
        this.application = application;
        this.allowSpaces = allowSpaces;
        this.allowLowerCase = allowLowerCase;
        this.maxLength = maxLength;
        this.generateSharedContents = generateSharedContents;
        this.generatedSharedContentPrefix = generatedSharedContentPrefix;
        this.includeContentCharsInGeneratedSharedContent = includeContentCharsInGeneratedSharedContent;
        this.generatedSharedContentMinWordCount = generatedSharedContentMinWordCount;
    }

    public RationalizerApplication getRationalizerApplication() {
        return application;
    }

    public void setApplication(RationalizerApplication application) {
        this.application = application;
    }

    public Boolean getAllowSpaces() {
        return allowSpaces;
    }

    public void setAllowSpaces(Boolean allowSpaces) {
        this.allowSpaces = allowSpaces;
    }

    public Boolean getAllowLowerCase() {
        return allowLowerCase;
    }

    public void setAllowLowerCase(Boolean allowLowerCase) {
        this.allowLowerCase = allowLowerCase;
    }

    public String getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(String maxLength) {
        this.maxLength = maxLength;
    }

    public Boolean getGenerateSharedContents() {
        return generateSharedContents;
    }

    public void setGenerateSharedContents(Boolean generateSharedContents) {
        this.generateSharedContents = generateSharedContents;
    }

    public String getGeneratedSharedContentPrefix() {
        return generatedSharedContentPrefix;
    }

    public void setGeneratedSharedContentPrefix(String generatedSharedContentPrefix) {
        this.generatedSharedContentPrefix = generatedSharedContentPrefix;
    }

    public Boolean getIncludeContentCharsInGeneratedSharedContent() {
        return includeContentCharsInGeneratedSharedContent;
    }

    public void setIncludeContentCharsInGeneratedSharedContent(Boolean includeContentCharsInGeneratedSharedContent) {
        this.includeContentCharsInGeneratedSharedContent = includeContentCharsInGeneratedSharedContent;
    }

    public Integer getGeneratedSharedContentMinWordCount() {
        return generatedSharedContentMinWordCount;
    }

    public void setGeneratedSharedContentMinWordCount(Integer generatedSharedContentMinWordCount) {
        this.generatedSharedContentMinWordCount = generatedSharedContentMinWordCount;
    }
}
