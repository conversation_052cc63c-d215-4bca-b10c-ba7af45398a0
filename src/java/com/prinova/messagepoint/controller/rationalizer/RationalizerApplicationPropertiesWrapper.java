package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class RationalizerApplicationPropertiesWrapper implements Serializable {
    private RationalizerApplication currentApplication;
    private String cloneName;
    private BrandProfile existingApplicationBrandProfile;
    private List<DashboardFilter> dashboardFilters;

    private Boolean reSync = false;

    public RationalizerApplication getCurrentApplication() {
        return currentApplication;
    }

    public void setCurrentApplication(RationalizerApplication currentApplication) {
        this.currentApplication = currentApplication;
    }

    public String getCloneName() {
        return cloneName;
    }

    public void setCloneName(String cloneName) {
        this.cloneName = cloneName;
    }

    public Boolean getReSync() {
        return reSync;
    }

    public void setReSync(Boolean reSync) {
        this.reSync = reSync;
    }

    public BrandProfile getExistingApplicationBrandProfile() {
        return existingApplicationBrandProfile;
    }

    public void setExistingApplicationBrandProfile(BrandProfile existingApplicationBrandProfile) {
        this.existingApplicationBrandProfile = existingApplicationBrandProfile;
    }

    public List<DashboardFilter> getDashboardFilters() {
        return dashboardFilters;
    }

    public void setDashboardFilters(List<DashboardFilter> dashboardFilters) {
        this.dashboardFilters = dashboardFilters;
    }

    public LinkedHashMap<String, List<DashboardFilter>> getDashboardFiltersAsMap() {
        return this.dashboardFilters.stream().sorted((e1, e2)-> Long.valueOf(e1.getId()).compareTo(Long.valueOf(e2.getId())))
                .collect(Collectors.groupingBy(filter -> filter.getSection(), LinkedHashMap::new, Collectors.toList()));
    }

    public int dashboardFilterIndexById(String filterId) {
        AtomicInteger position = new AtomicInteger(-1);
        dashboardFilters.stream()
                .peek(pos -> position.incrementAndGet())
                .filter(filter -> filter.getId() == Long.valueOf(filterId))
                .findFirst();

        return position.get();
    }

    public String computeLabel(String type, String key) {
        if(type.equalsIgnoreCase("section")) {
            return "page.label.rat.wordCount.section."  + key;
        }

        return "label_code_not_found";
    }
}
