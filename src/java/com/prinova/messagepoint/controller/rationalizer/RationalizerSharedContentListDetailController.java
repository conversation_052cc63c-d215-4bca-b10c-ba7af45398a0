package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.util.DateUtil;
import org.parboiled.common.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class RationalizerSharedContentListDetailController extends MessagepointController {

    public static final String RATIONALIZER_SHARED_CONTENT_ID = "rationalizerSharedContentId";
    public static final String REQ_PARAM_ACTION = "action";

    public static final int ACTION_REMOVE = 1;

    @Override
    protected RationalizerSharedContentListWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerSharedContentListWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        String rationalizerSharedContentGuid = ServletRequestUtils.getStringParameter(request, RATIONALIZER_SHARED_CONTENT_ID, "");

        if (StringUtils.isNotEmpty(rationalizerSharedContentGuid) ) {
            RationalizerSharedContent rationalizerSharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(rationalizerSharedContentGuid);
            referenceData.put("rationalizerSharedContent", rationalizerSharedContent);
            referenceData.put("dateTimeFormat", DateUtil.DATE_TIME_FORMAT);
        }

        return referenceData;
    }
}