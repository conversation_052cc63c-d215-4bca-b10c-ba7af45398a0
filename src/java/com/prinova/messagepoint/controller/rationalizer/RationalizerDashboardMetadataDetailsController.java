package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.MessagepointController;

import javax.servlet.http.HttpServletRequest;

public class RationalizerDashboardMetadataDetailsController extends MessagepointController {

    protected RationalizerDashboardDetailsWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerDashboardDetailsWrapper();
    }
}
