package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerWorkflowAssignmentService;
import com.prinova.messagepoint.util.NestedPageUtils;

public class RationalizerWorkflowAssignmentEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerWorkflowAssignmentEditController.class);

	public static final String REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM 	= "rationalizerApplicationId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
		
		referenceData.put("rationalizerWorkflows", ConfigurableWorkflow.findByDocumentModelAndUsageTypes(-1, ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER));
		
		referenceData.put("rationalizerApplication", RationalizerApplication.findById(rationalizerApplicationId));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(RationalizerApplication.class, new IdCustomEditor<>(RationalizerApplication.class));
		binder.registerCustomEditor(ConfigurableWorkflow.class, new IdCustomEditor<>(ConfigurableWorkflow.class));
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
		RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);
		return rationalizerApplication;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.WorkflowAssignmentEdit)
				.setAction(Actions.Update);

		try {
			long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
			RationalizerApplication command = (RationalizerApplication)commandObj;

			ServiceExecutionContext context = UpdateRationalizerWorkflowAssignmentService.createContext(command);
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerWorkflowAssignmentService.SERVICE_NAME, UpdateRationalizerWorkflowAssignmentService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();

			if ( !serviceResponse.isSuccessful() ) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateRationalizerWorkflowAssignmentService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" Rationalizer application workflow was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, rationalizerApplicationId);
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}