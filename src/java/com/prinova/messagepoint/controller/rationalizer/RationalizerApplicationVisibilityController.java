package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerApplicationVisibiltyService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;


public class RationalizerApplicationVisibilityController extends MessagepointController {

	private static final Log log = LogUtil.getLog(RationalizerApplicationVisibilityController.class);

	public static final String REQ_PARAM_APP_ID_PARAM 	= "rationalizerApplicationId";
	
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		Set<User> availableUsers = new HashSet<>();
		availableUsers.addAll(User.findEnabledUsersByPermission(Permission.ROLE_ECATALOG_VIEW));
		availableUsers.addAll(User.findEnabledUsersByPermission(Permission.ROLE_ECATALOG_EDIT));
		availableUsers.addAll(User.findEnabledUsersByPermission(Permission.ROLE_ECATALOG_ADMIN));
		availableUsers.addAll(User.findEnabledUsersByPermission(Permission.ROLE_ECATALOG_APPROVE));
		referenceData.put("availableUsers", availableUsers);
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_APP_ID_PARAM, -1);		
		RationalizerApplicationVisibilityWrapper command;
		if(appId != -1){
			RationalizerApplication application = RationalizerApplication.findById(appId);
			command = new RationalizerApplicationVisibilityWrapper(application);
		}else{
			command = new RationalizerApplicationVisibilityWrapper();
		}
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.ApplicationVisibility)
				.setAction(Actions.Update);

		try {
			RationalizerApplicationVisibilityWrapper command = (RationalizerApplicationVisibilityWrapper)commandObj;

			ServiceExecutionContext context = UpdateRationalizerApplicationVisibiltyService.createContext(command.isFullVisible(), command.getRationalizerApplication(), command.getSelectedVisibleUserIds());

			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerApplicationVisibiltyService.SERVICE_NAME, UpdateRationalizerApplicationVisibiltyService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateRationalizerApplicationVisibiltyService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" selected Users were not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_APP_ID_PARAM, command.getRationalizerApplication().getId());
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}