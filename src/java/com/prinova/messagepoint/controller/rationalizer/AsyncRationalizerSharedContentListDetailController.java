package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerActionTypeEnum;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.buildElasticSearchGuids;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.sendRationalizerContentsToElasticSearch;


public class AsyncRationalizerSharedContentListDetailController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncRationalizerSharedContentListDetailController.class);

    public static final String REQ_RATIONALIER_SHARED_CONTENT_ID = "rationalizerSharedContentId";
    public static final String REQ_RATIONALIER_CONTENT_ID = "rationalizerContentId";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();
        String rationalizerSharedContentGuid = ServletRequestUtils.getStringParameter(request, REQ_RATIONALIER_SHARED_CONTENT_ID, "");
        String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_RATIONALIER_CONTENT_ID, "");
        JSONObject obj = new JSONObject();

        try {
            RationalizerSharedContent rationalizerSharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(rationalizerSharedContentGuid);
            RationalizerDocumentContent rationalizerContent = RationalizerDocumentContent.findDocumentContentByElasticSearchGuid(rationalizerContentGuid);
            if (rationalizerContent != null) {
                final RationalizerSharedContent tmpRationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();
                if(tmpRationalizerSharedContent != null && tmpRationalizerSharedContent.equals(rationalizerSharedContent)) {
                    Map<String, String> contentGuidTextMap = new LinkedHashMap<>();
                    final Set<String> elasticSearchContentGuids = buildElasticSearchGuids(Collections.singleton(rationalizerContent));
                    elasticSearchContentGuids.add(rationalizerSharedContent.buildElasticSearchGuid());

                    rationalizerSharedContent.removeContent(rationalizerContent);

                    contentGuidTextMap.put(rationalizerContent.buildElasticSearchGuid(), rationalizerContent.getTextContent());
                    contentGuidTextMap.put(rationalizerSharedContent.buildElasticSearchGuid(), rationalizerSharedContent.getTextContent());

                    rationalizerContent.setLastAction(RationalizerActionTypeEnum.SEPARATE);
                    HistoricalRationalizerDocumentContent histRatContent = RationalizerUtil.generateHistoryForRationalizerDocumentContent(rationalizerContent, UserUtil.getPrincipalUser());
                    rationalizerContent.getHistRationalizerContent().add(histRatContent);

                    rationalizerContent.save();

                    RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
                    RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerSharedContent.getRationalizerApplication());

                    sendRationalizerContentsToElasticSearch(
                            UserUtil.getPrincipalUser(),
                            rationalizerElasticSearchHandler,
                            elasticSearchContentGuids,
                            contentGuidTextMap,
                            contentGuidTextMap
                    );
                    rationalizerElasticSearchHandler.refreshIndex();
                }
            }
            out.write(obj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Error - Unable to resolve request to remove content from shared object: " + e.getMessage(), e);
        }

        return null;
    }

}