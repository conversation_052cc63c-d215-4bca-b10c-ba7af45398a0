package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;

import java.io.Serializable;

public class RationalizerDCFieldsWrapper implements Serializable {

	private static final long serialVersionUID = 49040380574648779L;

	private RationalizerDocumentContent rationalizerDocumentContent;
	private String actionValue;
	private Integer order;
	private String messageName;
	private String zoneConnector;


	public RationalizerDCFieldsWrapper(RationalizerDocumentContent rationalizerDocumentContent) {
		super();
		this.rationalizerDocumentContent = rationalizerDocumentContent;
		this.order = rationalizerDocumentContent.getOrder();
		this.messageName = rationalizerDocumentContent.getMessageName();
		this.zoneConnector = rationalizerDocumentContent.getZoneConnector();
	}

	public RationalizerDocumentContent getRationalizerDocumentContent() {
		return rationalizerDocumentContent;
	}

	public void setRationalizerDocumentContent(RationalizerDocumentContent rationalizerDocumentContent) {
		this.rationalizerDocumentContent = rationalizerDocumentContent;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public String getMessageName() {
		return messageName;
	}

	public void setMessageName(String messageName) {
		this.messageName = messageName;
	}

	public String getZoneConnector() {
		return zoneConnector;
	}

	public void setZoneConnector(String zoneConnector) {
		this.zoneConnector = zoneConnector;
	}
}