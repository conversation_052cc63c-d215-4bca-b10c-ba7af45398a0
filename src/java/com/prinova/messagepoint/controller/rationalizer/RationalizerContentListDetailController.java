package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;

public class RationalizerContentListDetailController implements Controller {

	public static String REQ_PRAM_RATIONALIZER_CONTENT_ID 	= "rationalizerContentId";
	
	private String successView;
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PRAM_RATIONALIZER_CONTENT_ID, "");
		
		RationalizerDocumentContent rationalizerContent = RationalizerDocumentContent.findDocumentContentByElasticSearchGuid(rationalizerContentGuid);
		params.put("model", rationalizerContent);

		return new ModelAndView(getFormView(), params);
	}
	
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
