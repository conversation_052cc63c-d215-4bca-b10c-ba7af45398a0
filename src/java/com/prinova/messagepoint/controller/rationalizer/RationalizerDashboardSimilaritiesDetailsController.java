package com.prinova.messagepoint.controller.rationalizer;

import com.google.gson.JsonElement;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.dto.TotalCountAndGuidsSliceDto;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.apache.commons.collections.MapUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.readJsonObject;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MAX_SIMILARITY;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticSearchConstants.MIN_SIMILARITY_FOR_DASHBOARD;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;
import static java.text.MessageFormat.format;

public class RationalizerDashboardSimilaritiesDetailsController extends MessagepointController {
    public static final String REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID = "rationalizerComparisonContentId";
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    public static final String TARGET_DASHBOARD_COMPARE_SELECTION = "targetDashboardCompareSelection";

    private static final Log log = LogUtil.getLog(RationalizerDashboardSimilaritiesDetailsController.class);

    protected RationalizerDashboardDetailsWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerDashboardDetailsWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        String comparisonContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID, "");
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

        String targetSelections = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
        RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(comparisonContentGuid);
        referenceData.put("rationalizerContent", rationalizerContent);

        if (rationalizerContent == null) {
            addTaskInfo(referenceData);

            return referenceData;

        }

        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        List<Map<String, String>> targetMetadataFiltersList = RationalizerUtil.processDashboardTreeSelections(rationalizerApplicationId, targetSelections);

        int duplicates = 0;
        int similarities = 0;
        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        final TotalCountAndGuidsSliceDto totalCountAndGuidsSliceDto = rationalizerElasticSearchHandler.searchDuplicatesByHashAndDocumentFileNameWithMetadataFilter(
                targetMetadataFiltersList,
                rationalizerContent.getHashCode(),
                null,
                0,
                1,
                new LinkedHashMap<>() {{
                    put("sort", readJsonObject(
                            "{\n" +
                                    "  \"_script\": {\n" +
                                    "    \"type\": \"string\",\n" +
                                    "    \"script\": {\n" +
                                    "      \"lang\": \"painless\",\n" +
                                    "      \"source\": \"doc['id'].value.startsWith(\\\"rsc\\\") ? \\\"1_\\\" + doc['id'].value :   \\\"2_\\\" + doc['id'].value\"\n" +
                                    "    },\n" +
                                    "    \"order\": \"asc\"\n" +
                                    "  }\n" +
                                    "}"
                            )
                    );
                }}
        );
        duplicates = totalCountAndGuidsSliceDto.getTotalCount();

        List<DashboardFilter> dashboardResultsFilterList = rationalizerApplication.getDashboardFiltersForSection(DashboardFilter.Section.exact_matches_similarities);
        Map<String, Float> similaritiesMapComparedToTarget = rationalizerElasticSearchHandler.getFromMatchesSimilarityFieldByContentIdAndDocumentFileNameWithCompareContext(
                rationalizerContent.buildElasticSearchGuid(),
                MIN_SIMILARITY_FOR_DASHBOARD,
                MAX_SIMILARITY,
                null,
                targetMetadataFiltersList,
                dashboardResultsFilterList
        );

        if(MapUtils.isNotEmpty(similaritiesMapComparedToTarget)) {
            similarities = similaritiesMapComparedToTarget.size();
        }
        referenceData.put("exactMatchesDisplayString", format(getMessage("page.label.exact.matches"), duplicates));
        referenceData.put("similaritiesDisplayString", format(getMessage("page.label.dashboard.similarities.count"), similarities));
        referenceData.put("similaritiesCount", similarities);


        addTaskInfo(referenceData);

        return referenceData;
    }

    private void addTaskInfo(Map<String, Object> referenceData) {
        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.Dashboard);
        try {

            return null;
        } finally {
            analyticsEvent.send();
        }
    }
}
