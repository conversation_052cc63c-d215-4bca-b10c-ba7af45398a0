package com.prinova.messagepoint.controller.rationalizer;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class RationalizerContentStateFilterType extends StaticType {
	private static final long serialVersionUID = 987109319734062846L;
	
	public static final int ID_ACTIVE					= 1;
	public static final int ID_INACTIVE				= 2;

	public static final String MESSAGE_INACTIVE 	= "page.label.list.filter.type.inactive";	
	public static final String MESSAGE_ACTIVE			= "page.label.list.filter.type.active";

	public RationalizerContentStateFilterType(Integer id) {
		super();
		switch (id) {
		case ID_INACTIVE:
			this.setId(ID_INACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_INACTIVE));
			this.setDisplayMessageCode(MESSAGE_INACTIVE);
			break;
		case ID_ACTIVE:
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_ACTIVE);
			break;
		default:
			break;
		}
	}

	public RationalizerContentStateFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_INACTIVE))) { 
			this.setId(ID_INACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_INACTIVE));
			this.setDisplayMessageCode(MESSAGE_INACTIVE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_ACTIVE))) { 
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_ACTIVE);
		}
	}
	
	public static List<RationalizerContentStateFilterType> listAll() {
		List<RationalizerContentStateFilterType> allListFilterTypes = new ArrayList<>();

		RationalizerContentStateFilterType listFilterType = null;
		
		listFilterType = new RationalizerContentStateFilterType(ID_ACTIVE);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new RationalizerContentStateFilterType(ID_INACTIVE);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}