package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.filters.WordCountFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.regex.Pattern;

public class RationalizerApplicationPropertiesValidator extends MessagepointInputValidator {

    private static final Log log = LogUtil.getLog(RationalizerApplicationPropertiesValidator.class);

    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        final String label = ApplicationUtil.getMessage("page.label.name");

        String applicationName = ((RationalizerApplicationPropertiesWrapper)commandObj).getCurrentApplication().getName();
        if (applicationName == null || applicationName.trim().isEmpty()) {
            errors.reject("error.input.mandatory", new String[]{label}, "");
            return;
        }

        Long applicationId = RationalizerApplication.getIdByName(applicationName);
        if(applicationId != null && applicationId != ((RationalizerApplicationPropertiesWrapper) commandObj).getCurrentApplication().getId()) {
            errors.reject("error.message.rationalizer.name.unique", new String[]{}, "");
            return;
        }

        MessagepointInputValidationUtil.validateStringValue(label, applicationName == null ? applicationName :
                applicationName.trim(), true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);

        List<DashboardFilter> dashboardFiltersList = ((RationalizerApplicationPropertiesWrapper) commandObj).getDashboardFilters();
        // validate wordCount integer values and (gt, lt) ranges
        String section = "";
        String previouGtValue = "";
        for(DashboardFilter filter: dashboardFiltersList) {
            if(filter instanceof WordCountFilter) {
                if(filter.getType().equals(DashboardFilter.KeywordType.integer.toString())) {
                    String value = filter.getNormalizedValueForDisplay();
                    if (!StringUtil.isEmptyOrNull(value)) {
                        if (!Pattern.matches("^(?:[2-9]|\\d\\d\\d*)$", value)) {
                            errors.reject("error.message.rationalizer." + filter.getSection() + ".validate.wordCount",
                                    new String[]{ApplicationUtil.getMessage("page.label.wordCount.section." + filter.getSection())}, "");
                        }
                    }
                    if(filter.getOperation().equals(DashboardFilter.Operator.gt.toString())) {
                        previouGtValue = filter.getNormalizedValueForDisplay();
                    }
                    if(!filter.getSection().equals(section)) {
                        section = filter.getSection();
                    } else {
                        if(filter.getOperation().equals(DashboardFilter.Operator.lt.toString())) {
                            if(!StringUtils.isEmpty(value)) {
                                if (!StringUtil.isEmptyOrNull(previouGtValue) &&
                                        Integer.valueOf(value) < Integer.valueOf(previouGtValue)) {
                                    errors.reject("error.message.rationalizer." + filter.getSection() + ".validate.wordCount.range",
                                            new String[]{ApplicationUtil.getMessage("page.label.wordCount.section." + filter.getSection())}, "");
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}