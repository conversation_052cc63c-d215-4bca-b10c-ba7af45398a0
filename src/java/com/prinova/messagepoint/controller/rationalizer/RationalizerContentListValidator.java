package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class RationalizerContentListValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RationalizerContentListWrapper wrapper = (RationalizerContentListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		if ( action == RationalizerDocumentsListController.ACTION_ADD_APPLICATION ) {
			String label = ApplicationUtil.getMessage("page.label.name");
			MessagepointInputValidationUtil.validateStringValue(label, wrapper.getApplicationName() == null ? wrapper.getApplicationName() :
					wrapper.getApplicationName().trim(), true, 1, 255, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
			
		}
		if ( action == RationalizerDocumentsListController.ACTION_UPDATE_APPLICATION ) {
			
			if ( wrapper.getCurrentApplication().getName() == null || wrapper.getCurrentApplication().getName().trim().isEmpty())
				errors.reject("error.input.mandatory", new String[] {ApplicationUtil.getMessage("page.label.name")}, "");
			
		}
		
		if ( action == RationalizerDocumentsListController.ACTION_ADD_DOCUMENT ) {
			
			if ( wrapper.getDocumentName() == null || wrapper.getDocumentName().trim().isEmpty())
				errors.reject("error.input.mandatory", new String[] {ApplicationUtil.getMessage("page.label.name")}, "");
			
		}

	}
}
