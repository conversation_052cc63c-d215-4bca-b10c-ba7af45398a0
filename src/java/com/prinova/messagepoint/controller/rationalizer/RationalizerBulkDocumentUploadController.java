package com.prinova.messagepoint.controller.rationalizer;

import ai.mpr.marcie.ingestion.input.VariablesTypeEnum;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerSupportedCountriesEnum;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class RationalizerBulkDocumentUploadController extends MessagepointController {
    public static final String REQ_PARAM_RATIONALIZER_APPLICATION_ID 	= "rationalizerApplicationId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID, -1);
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        referenceData.put("rationalizerApplication", rationalizerApplication );
        referenceData.put("variablesNotations", new ArrayList<>(Arrays.asList(VariablesTypeEnum.values())));

        referenceData.put("phoneNumberSupportedCountries", Arrays.asList(RationalizerSupportedCountriesEnum.values()));

        boolean reindexInProgress = false;
        if(rationalizerApplication != null && rationalizerApplication.isUploadInProgress()) {
            reindexInProgress = true;
        }
        referenceData.put(("reindexInProgress"), reindexInProgress);

        String log = rationalizerApplication == null ? "" : LogUtil.getRationalizerApplicationLogFilePath(rationalizerApplication.getGuid());

        referenceData.put("hasApplicationLog", FileUtil.fileExists(log));

            String resourceToken = HttpRequestUtil.buildResourceToken()
                    .add("type", "download_file")
                    .add("file", log)
                    .getValue();
            String applicationLog = ApplicationUtil.getWebRoot() + "download/log.form?resource=" + resourceToken;
            referenceData.put("applicationLog", applicationLog);

        Integer systemPropMaxDocs = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.DefaultValues.KEY_RationalizerMaxDocPerApp));
        int maxAlloweDocsPerApp =  systemPropMaxDocs != null ? systemPropMaxDocs.intValue() : 500;
        String uploadLimitError = MessageFormat.format(ApplicationUtil.getMessage("email.rationalizer.upload.limit.body"),maxAlloweDocsPerApp);
        referenceData.put("uploadLimitError", uploadLimitError);

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(RationalizerApplication.class, new IdCustomEditor<>(RationalizerApplication.class));
    }

    protected RationalizerBulkUploadWrapper formBackingObject(HttpServletRequest request) throws Exception {
        long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID, -1);
        RationalizerBulkUploadWrapper command;
        if(appId != -1){
            RationalizerApplication application = RationalizerApplication.findById(appId);
            command = new RationalizerBulkUploadWrapper(
                    application,
                    false,
                    false,
                    "100",
                    false,
                    "",
                    true,
                    0
            );
        }else{
            command = new RationalizerBulkUploadWrapper();
        }

        return command;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        return new ModelAndView(new RedirectView(getFormView()));
    }
}
