package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.rationalizer.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerConsolidateService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class RationalizerConsolidateEditController extends MessagepointController {

    public static final Integer ACTION_CREATE_NEW = 1;
    public static final Integer ACTION_FIND_OTHER = 2;

    public static final String REQ_PARAM_RATIONALIZER_CONTENT_ID = "rationalizerContentId";
    public static final String REQ_PARAM_GUID_FOR_ALL_MATCHED_IDS_PARAM = "guidForAllMatchedIds";
    public static final String REQ_PARAM_GUID_FOR_SELECTED_IDS = "guidForSelectedIds";
    public static final String REQ_PARAM_SELECT_ALL_PAGES_PARAM = "selectAllPages";
    public static final String REQ_PARAM_ACTION = "action";
    public static final String REQ_VIEW_TYPE = "viewType";

    protected RationalizerConsolidateEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
        String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_CONTENT_ID, "");
        String guidForSelectedIds = ServletRequestUtils.getStringParameter(request, REQ_PARAM_GUID_FOR_SELECTED_IDS, "");
        String guidForAllMatchedIds = ServletRequestUtils.getStringParameter(request, REQ_PARAM_GUID_FOR_ALL_MATCHED_IDS_PARAM, "");
        boolean selectAllPages = ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_SELECT_ALL_PAGES_PARAM, false);
        final String action = ServletRequestUtils.getStringParameter(request, REQ_PARAM_ACTION, "-1");

        RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerContentGuid);
        RationalizerSharedContent rationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();
        boolean isSharedObject = rationalizerSharedContent != null;
        User principal = UserUtil.getPrincipalUser();
        User requestor = User.findById(principal.getId());

        RationalizerConsolidateEditWrapper wrapper = new RationalizerConsolidateEditWrapper();
        wrapper.setActionValue(action);
        wrapper.setRationalizerContentId(rationalizerContentGuid);
        wrapper.setPartOfSharedObject(isSharedObject);
        wrapper.setSelectAllPages(selectAllPages);
        wrapper.setGuidForSelectedIds(guidForSelectedIds);
        wrapper.setGuidForAllMatchedIds(guidForAllMatchedIds);
        wrapper.setSelectedIdsSet(new LinkedHashSet<>());
        if (isSharedObject) {
            wrapper.setName(rationalizerSharedContent.getName());
        }

        if (StringUtils.isNotEmpty(guidForSelectedIds)) {
            SearchResultIds searchResultIds = SearchResultIds.findSearchResultEntry(guidForSelectedIds, requestor);
            if (searchResultIds != null) {
                wrapper.setSelectedIdsSet(searchResultIds.extractListItemIds());
            }
        }

        if (selectAllPages && StringUtils.isNotEmpty(guidForAllMatchedIds)) {
            SearchResultIds searchResultIds = SearchResultIds.findSearchResultEntry(guidForAllMatchedIds, requestor);
            if (searchResultIds != null) {
                wrapper.setSelectedIdsSet(searchResultIds.extractListItemIds());
            }
        }

        int documentContentsCount = wrapper.getSelectedIdsSet().stream()
                .filter(elasticSearchGuid -> !RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchGuid))
                .mapToInt(elasticSearchGuid -> 1)
                .sum();
        if (rationalizerContent instanceof RationalizerDocumentContent) {
            documentContentsCount += 1;
        }

        String selectedObjectsString = "";
        if (documentContentsCount == 1) {
            selectedObjectsString += ApplicationUtil.getMessage("page.label.consolidate.selected.single.object");
        } else {
            selectedObjectsString += MessageFormat.format(ApplicationUtil.getMessage("page.label.consolidate.selected.objects"), documentContentsCount);
        }

        int sharedContentsCount = wrapper.getSelectedIdsSet().size() + 1 - documentContentsCount;
        String sharedContentsString = "";
        if (sharedContentsCount > 0) {
            if (sharedContentsCount == 1) {
                sharedContentsString += ApplicationUtil.getMessage("page.label.consolidate.selected.single.shared.content");
            } else {
                sharedContentsString += MessageFormat.format(ApplicationUtil.getMessage("page.label.consolidate.selected.shared.contents"), sharedContentsCount);
            }
        }

        if (StringUtils.isNotEmpty(selectedObjectsString) && StringUtils.isNotEmpty(sharedContentsString)) {
            sharedContentsString = "<br/>" + sharedContentsString;
        }

        selectedObjectsString += sharedContentsString;

        wrapper.setSelectedObjectsString(selectedObjectsString);

        RationalizerApplication rationalizerApplication = rationalizerContent.computeRationalizerApplication();
        wrapper.setRationalizerApplicationId(rationalizerApplication.getId());

        List<RationalizerSharedContent> rationalizerSharedContentList = RationalizerSharedContent.findByApplication(rationalizerApplication);
        wrapper.setSharedContentList(rationalizerSharedContentList);

        return wrapper;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.RationalizerConsolidateEdit);
        RationalizerConsolidateEditWrapper wrapper = (RationalizerConsolidateEditWrapper) commandObj;
        String type = ServletRequestUtils.getStringParameter(request, REQ_VIEW_TYPE, "");
        Integer action = Integer.parseInt(wrapper.getActionValue());
        User principal = UserUtil.getPrincipalUser();
        User requestor = User.findById(principal.getId());
        RationalizerConsolidateService rationalizerConsolidateService = (RationalizerConsolidateService) ApplicationUtil.getBean("rationalizerConsolidateService");

        try {
            if (ACTION_CREATE_NEW.equals(action)) {
                analyticsEvent.setAction(Actions.CombineAsShared);

                handleCreateNewSharedContent(wrapper, requestor, rationalizerConsolidateService);

                Map<String, Object> params = new HashMap<>();
                params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                params.put(REQ_VIEW_TYPE, type);

                return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
            } else if (ACTION_FIND_OTHER.equals(action)) {

                handleFindOtherSharedContents(wrapper, requestor, rationalizerConsolidateService);

                Map<String, Object> params = new HashMap<>();
                params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                params.put(REQ_VIEW_TYPE, type);

                return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
            }
        } finally {
            analyticsEvent.send();
        }
        return null;
    }

    private void handleCreateNewSharedContent(RationalizerConsolidateEditWrapper wrapper, User requestor, RationalizerConsolidateService rationalizerConsolidateService) {
        RationalizerContent targetContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(wrapper.getRationalizerContentId());
        final RationalizerSharedContent targetRationalizerSharedContent = targetContent.computeRationalizerSharedContent();
        if (targetRationalizerSharedContent == null) {
            rationalizerConsolidateService.createNewSharedObject(wrapper.getName(), (RationalizerDocumentContent) targetContent, createRationalizerContents(wrapper, requestor));
        } else {
            rationalizerConsolidateService.updateExistingSharedObject(targetRationalizerSharedContent, createRationalizerContents(wrapper, requestor));
        }
    }

    private void handleFindOtherSharedContents(RationalizerConsolidateEditWrapper wrapper, User requestor, RationalizerConsolidateService rationalizerConsolidateService) {
        RationalizerSharedContent sharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(wrapper.getFoundSharedObjectId());
        if (sharedContent == null) {
            return;
        }

        Collection<RationalizerContent> rationalizerContents = createRationalizerContents(wrapper, requestor);
        RationalizerContent targetContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(wrapper.getRationalizerContentId());
        if (targetContent != null) {
            rationalizerContents.add(targetContent);
        }

        rationalizerConsolidateService.updateExistingSharedObject(sharedContent, rationalizerContents);
    }

    private Collection<RationalizerContent> createRationalizerContents(RationalizerConsolidateEditWrapper wrapper, User requestor) {
        Set<String> contentIds;

        if (wrapper.getSelectAllPages()) {
            SearchResultIds searchResultIds = SearchResultIds.findSearchResultEntry(wrapper.getGuidForAllMatchedIds(), requestor);
            contentIds = searchResultIds != null ? searchResultIds.extractListItemIds() : null;
        } else {
            contentIds = wrapper.getSelectedIdsSet();
        }

        if (CollectionUtils.isEmpty(contentIds)) {
            return new LinkedHashSet<>();
        }

        return RationalizerContent.findRationalizerContentsByElasticSearchGuids(contentIds);
    }
}
