package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;

import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class RationalizerSharedContentEditWrapper implements Serializable {

    private static final long serialVersionUID = -288914342836914507L;

    private Integer action;

    private List<String> selectedIds;

    private String sharedContentName;

    private String contentMarkup;

    private RationalizerSharedContent rationalizerSharedContent;

    private MetadataFormEditWrapper formWrapper;

    private String modifyContentMetadataConnectorValue;

    private String modifyContentMetadataReplaceValue;

    public static Set<String> SHARED_METADATA_NOT_TO_DISPAY = Arrays.asList(
                    "Fonts",
                    "Height",
                    "Page_height",
                    "Page_width",
                    "Page_number",
                    "Position_(bottom)",
                    "Position_(left)",
                    "Position_(right)",
                    "Position_(top)",
                    "Width",
                    "Matches",
                    "Order",
                    "Alignment",
                    "Indent_(left)",
                    "Indent_(right)",
                    "Style"
            ).stream()
            .map(String::toLowerCase)
            .collect(Collectors.toCollection(LinkedHashSet::new));

    public RationalizerSharedContentEditWrapper(List<String> selectedIds, String sharedContentName, Integer action, String contentMarkup, RationalizerSharedContent rationalizerSharedContent) {
        this.setRationalizerSharedContent(rationalizerSharedContent);
        this.formWrapper = new MetadataFormEditWrapper( rationalizerSharedContent.getParsedContentForm() ,SHARED_METADATA_NOT_TO_DISPAY);
        this.selectedIds = selectedIds;
        this.sharedContentName = sharedContentName;
        this.action = action;
        this.contentMarkup = contentMarkup;
    }

    public RationalizerSharedContent getRationalizerSharedContent() {
        return rationalizerSharedContent;
    }
    public void setRationalizerSharedContent(RationalizerSharedContent rationalizerSharedContent) {
        this.rationalizerSharedContent = rationalizerSharedContent;
    }

    public MetadataFormEditWrapper getFormWrapper() {
        return formWrapper;
    }
    public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
        this.formWrapper = formWrapper;
    }

    public String getMetatags() {
        return rationalizerSharedContent.getMetatags();
    }

    public void setMetatags(String metatags) {
        rationalizerSharedContent.setMetatags(metatags);
    }

    public List<String> getSelectedIds() {
        return selectedIds;
    }

    public String getSharedContentName() {
        return sharedContentName;
    }

    public void setSharedContentName(String sharedContentName) {
        this.sharedContentName = sharedContentName;
    }


    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getContentMarkup() {
        return contentMarkup;
    }

    public void setContentMarkup(String contentMarkup) {
        this.contentMarkup = contentMarkup;
    }

    public String getModifyContentMetadataConnectorValue() {
        return this.modifyContentMetadataConnectorValue;
    }

    public void setModifyContentMetadataConnectorValue(final String modifyContentMetadataConnectorValue) {
        this.modifyContentMetadataConnectorValue = modifyContentMetadataConnectorValue;
    }

    public String getModifyContentMetadataReplaceValue() {
        return this.modifyContentMetadataReplaceValue;
    }

    public void setModifyContentMetadataReplaceValue(final String modifyContentMetadataReplaceValue) {
        this.modifyContentMetadataReplaceValue = modifyContentMetadataReplaceValue;
    }
}