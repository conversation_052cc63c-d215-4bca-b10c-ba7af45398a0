package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RationalizerUtil;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.prinova.messagepoint.model.SentimentEnum.NEGATIVE;
import static com.prinova.messagepoint.model.SentimentEnum.NEUTRAL;
import static com.prinova.messagepoint.model.SentimentEnum.POSITIVE;
import static com.prinova.messagepoint.util.SentimentUtil.buildCountValueForSentiment;

public class RationalizerDashboardSentimentController extends AbstractBaseRationalizerDashboardController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";

    protected Object formBackingObject(HttpServletRequest request) {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerDashboardSentimentWrapper wrapper = new RationalizerDashboardSentimentWrapper();

        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(rationalizerApp);
            wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());

            String[] sourceDashboardCompareSelections = request.getParameterMap().get("sourceDashboardCompareSelection");
            String sourceDashboardCompareSelection = sourceDashboardCompareSelections != null && sourceDashboardCompareSelections.length > 0 ? sourceDashboardCompareSelections[0] : "-9";
            List<Map<String, String>> sourceMetadataFilterMapList = RationalizerUtil.processDashboardTreeSelections(rationalizerApplicationId, sourceDashboardCompareSelection);

            RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApp, true);

            List<DashboardFilter> dashboardResultsFilterList = rationalizerApp.getDashboardFiltersForSection(DashboardFilter.Section.sentiment);
            Map<String, Integer> sentimentMap = rationalizerElasticSearchHandler.computeSentiment(sourceMetadataFilterMapList, dashboardResultsFilterList);

            Integer negativeCount = buildCountValueForSentiment(NEGATIVE, sentimentMap);
            Integer neutralCount = buildCountValueForSentiment(NEUTRAL, sentimentMap);
            Integer positiveCount = buildCountValueForSentiment(POSITIVE, sentimentMap);

            List<Integer> sentimentCountsList = new LinkedList<>();
            sentimentCountsList.add(negativeCount);
            sentimentCountsList.add(neutralCount);
            sentimentCountsList.add(positiveCount);

            wrapper.setGraphData(sentimentCountsList);
            wrapper.setNegativeCount(negativeCount);

            int totalCount = negativeCount + neutralCount + positiveCount;
            double negativePercent = negativeCount == 0 ? 0 : (double) (negativeCount * 100) / totalCount;

            String averageSentimentDisplayString;
            if (positiveCount >= negativeCount && positiveCount >= neutralCount) {
                averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.positive"),
                        String.format("%.2f", negativePercent));
            } else if (neutralCount >= negativeCount && neutralCount > positiveCount) {
                averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.neutral"),
                        String.format("%.2f", negativePercent));
            } else {
                averageSentimentDisplayString = MessageFormat.format(ApplicationUtil.getMessage("page.label.dashboard.sentiment.average.negative"),
                        String.format("%.2f", negativePercent));
            }
            wrapper.setAverageSentimentDisplayString(averageSentimentDisplayString);
        }

        return wrapper;
    }
}