package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameComparator;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.admin.DataComparison;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemOriginTypeEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationSyncStatus;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryComponentComparatorType;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryComponentFilterType;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryComponentTargetDataType;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryTab;
import com.prinova.messagepoint.model.rationalizer.RationalizerQueryType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.export.GenerateRationalizerObjectExportService;
import com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerContentService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerQueryService;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.StringSimilarityAlgorithmType;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.LOWERCASE_METADATA_CONNECTORS_NOT_SENT_TO_ELASTICSEARCH;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;

public class RationalizerQueryEditController extends MessagepointController{
    private static final Log log = LogUtil.getLog(RationalizerQueryEditController.class);

    public static final String REQ_PARAM_RATIONALIZER_QUERY_ID = "rationalizerQueryId";

    public static final String REQ_PARAM_ACTION = "action";

    public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";

    public static final int ACTION_MODIFY_DOCUMENTS = 1;
    public static final int ACTION_MODIFY_CONTENT = 2;
    public static final int ACTION_TAG_DOCUMENTS = 3;
    public static final int ACTION_TAG_CONTENT = 4;
    public static final int ACTION_DELETE_CONTENT = 5;
    public static final int ACTION_GENERATE_REPORT = 6;

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, RationalizerQueryListController.REQ_PARAM_RATIONALIZER_APP_ID, -1);
        long rationalizerQueryId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_QUERY_ID, -1);
        RationalizerQuery query = RationalizerQuery.findById(rationalizerQueryId);

        RationalizerApplication rationalizerApplication = null;
        if(rationalizerApplicationId > 0)
            rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);
        else
            rationalizerApplication = query.getRationalizerApplication();

        referenceData.put("rationalizerApplication", rationalizerApplication);

        referenceData.put("queryTypes", RationalizerQueryType.listAll());
        referenceData.put("queryTabs", RationalizerQueryTab.listAll());
        referenceData.put("targetDataTypes", RationalizerQueryComponentTargetDataType.listAll());
        referenceData.put("comparatorTypes", RationalizerQueryComponentComparatorType.listAll());
        referenceData.put("stringSimilarityAlgorithmTypes", StringSimilarityAlgorithmType.listAll());
        referenceData.put("documentFilterBasedOnContentTypes", RationalizerQueryComponentFilterType.listAll());

        referenceData.put("newComponentId", Long.MAX_VALUE);
        referenceData.put("removedComponentId", Long.MIN_VALUE);

        referenceData.put("showResults", query != null);

        boolean isContentQuery = query != null && query.getQueryTypeId() == RationalizerQueryType.ID_CONTENT;
        boolean isCompareToAll = query != null && ((query.getSimilarityComparisonValue() != null && query.getSimilarityComparisonValue().trim().equals("*")) || query.getCompareToAll());
        boolean appliesSimilarity = query != null && query.getAppliesSimilarity();
        referenceData.put("queryAppliesSimilarity", isContentQuery && appliesSimilarity);
        referenceData.put("queryAppliesUniqueSimilarity", isContentQuery && appliesSimilarity && query.getSimilarityComparisonValue() != null && isCompareToAll);
        referenceData.put("isContentQuery", isContentQuery);
        referenceData.put("isCompareAllQuery", isContentQuery && isCompareToAll);
        referenceData.put("exactMatchOnly", query != null && query.getExactMatchesOnly());

        long resultCount = 0;
        if(query != null && query.getApplyGrouping()) {
            resultCount = RationalizerDocumentContent.countDistinctContentsForApplication(query.getRationalizerApplication().getId());

        }
        referenceData.put("resultCount", resultCount);
        referenceData.put("isIndexingComplete", true);

        referenceData.put("numericComparators", DataComparison.getNumericComparisonTypes());

        StatusPollingBackgroundTask task = null;
        if(query != null && (query.getQueryTabId() == RationalizerQueryTab.ID_ADVANCED || query.getQueryTabId() == RationalizerQueryTab.ID_SIMPLE_SIMILARITY))
            task = StatusPollingBackgroundTask.findLastestByDescriptionAndByUser(UserUtil.getPrincipalUser(), query.getName());
        referenceData.put("similarityReport", task);
        if(task != null && task.getOutputFilename() != null) {
            referenceData.put("reportType", FileUtil.getFileExtension(task.getOutputFilename()));
        }

        List<MetadataFormItemDefinition> documentFormItemDefinitions = new ArrayList<>();
        List<MetadataFormItemDefinition> contentFormItemDefinitions = new ArrayList<>();
        if(rationalizerApplication.getParsedDocumentFormDefinition() != null)
            documentFormItemDefinitions.addAll(rationalizerApplication.getParsedDocumentFormDefinition().getFormItemDefinitions());
        if(rationalizerApplication.getParsedContentFormDefinition() != null) {
            final Set<MetadataFormItemDefinition> tmpFormItemDefinitions = rationalizerApplication.getParsedContentFormDefinition().getFormItemDefinitions();
            for(MetadataFormItemDefinition tmpFormItemDefinition : tmpFormItemDefinitions) {
                String tmpPrimaryConnector = tmpFormItemDefinition.getPrimaryConnector();
                if(StringUtils.isEmpty(tmpPrimaryConnector)) {
                    continue;
                }

                final String tmpPrimaryConnectorLowerCase = tmpPrimaryConnector.toLowerCase();
                if(LOWERCASE_METADATA_CONNECTORS_NOT_SENT_TO_ELASTICSEARCH.contains(tmpPrimaryConnectorLowerCase)) {
                    continue;
                }
                String tmpPC = tmpFormItemDefinition.getPrimaryConnector().replace("(", "LeftParenthesis").replace(")", "RightParenthesis");
                tmpFormItemDefinition.setPrimaryConnector(tmpPC);
                contentFormItemDefinitions.add(tmpFormItemDefinition);
            }
        }

        Collections.sort(documentFormItemDefinitions, new IdentifiableMessagepointModelNameComparator());
        Collections.sort(contentFormItemDefinitions, new IdentifiableMessagepointModelNameComparator());
        referenceData.put("documentFormItemDefinitions", documentFormItemDefinitions);
        referenceData.put("contentFormItemDefinitions", contentFormItemDefinitions);

        List<MetadataFormItemDefinition> modifyDocumentFormItemDefinitions = new ArrayList<>();
        List<MetadataFormItemDefinition> modifyContentFormItemDefinitions = new ArrayList<>();
        for(MetadataFormItemDefinition currentItem : documentFormItemDefinitions)
            if(currentItem.getTypeId() == MetadataFormItemType.ID_TEXT || currentItem.getTypeId() == MetadataFormItemType.ID_TEXTAREA || currentItem.getTypeId() == MetadataFormItemType.ID_TEXT_EDITOR)
                modifyDocumentFormItemDefinitions.add(currentItem);
        for(MetadataFormItemDefinition currentItem : contentFormItemDefinitions) {
            if(currentItem.getOriginTypeId() == MetadataFormItemOriginTypeEnum.CONTENT_PARSED.getId()) {
                continue;
            }

            if(currentItem.getTypeId() == MetadataFormItemType.ID_TEXT || currentItem.getTypeId() == MetadataFormItemType.ID_TEXTAREA || currentItem.getTypeId() == MetadataFormItemType.ID_TEXT_EDITOR) {
                modifyContentFormItemDefinitions.add(currentItem);
            }
        }
        referenceData.put("modifyDocumentFormItemDefinitions", modifyDocumentFormItemDefinitions);
        referenceData.put("modifyContentFormItemDefinitions", buildValueForFormItemDefinitionsKey(modifyContentFormItemDefinitions));

        referenceData.put("isExtendedSimpleSearch", query != null && (
                !(query.getAnyOfTheseSearchValue() == null || query.getAnyOfTheseSearchValue().isEmpty()) ||
                        !(query.getAllOfTheseSearchValue() == null || query.getAllOfTheseSearchValue().isEmpty()) ||
                        !(query.getNoneOfTheseSearchValue() == null || query.getNoneOfTheseSearchValue().isEmpty())
        ));

        return referenceData;
    }

    private List<MetadataFormItemDefinitionDto> buildValueForFormItemDefinitionsKey(List<MetadataFormItemDefinition> formItemDefinitions) {
        if(CollectionUtils.isEmpty(formItemDefinitions)) {
            return new LinkedList<>();
        }

        final LinkedList<MetadataFormItemDefinitionDto> formItemDefinitionDtos = formItemDefinitions.stream()
                .map(formItem -> new MetadataFormItemDefinitionDto(formItem.getPrimaryConnector(), formItem.getName()))
                .collect(Collectors.toCollection(LinkedList::new));

        return formItemDefinitionDtos;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(RationalizerQueryComponentTargetDataType.class, new StaticTypeIdCustomEditor<>(RationalizerQueryComponentTargetDataType.class));
        binder.registerCustomEditor(RationalizerQueryComponentComparatorType.class, new StaticTypeIdCustomEditor<>(RationalizerQueryComponentComparatorType.class));
        binder.registerCustomEditor(RationalizerQueryType.class, new StaticTypeIdCustomEditor<>(RationalizerQueryType.class));
    }

    protected RationalizerQueryEditWrapper formBackingObject(HttpServletRequest request) throws Exception {

        long rationalizerQueryId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_QUERY_ID, -1);
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, RationalizerQueryListController.REQ_PARAM_RATIONALIZER_APP_ID, -1);
        RationalizerQuery query = null;

        if(rationalizerQueryId > 0) {
            query = RationalizerQuery.findById(rationalizerQueryId);
        } else {
            query = new RationalizerQuery();
            query.setRationalizerApplication(RationalizerApplication.findById(rationalizerApplicationId));
        }

        return new RationalizerQueryEditWrapper(query, isFormSubmission(request));
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, RationalizerQueryListController.REQ_PARAM_RATIONALIZER_APP_ID, -1);
        long rationalizerQueryId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_QUERY_ID, -1);
        RationalizerApplication app = null;
        if(rationalizerApplicationId > 0) {
            app = RationalizerApplication.findById(rationalizerApplicationId);
        } else {
            if(rationalizerQueryId > 0) {
                RationalizerQuery query = RationalizerQuery.findById(rationalizerQueryId);
                app = query.getRationalizerApplication();
            }
        }
        if(app != null) {
            if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == app.getAppSyncStatus()) {
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_note"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_down1"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_down2"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_down3"));
            } else if(RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() == app.getAppSyncStatus()) {
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_note"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_not_synch1"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_not_synch2"));
            } else if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_IN_PROGRESS.getStatusValue() == app.getAppSyncStatus()) {
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_note"));
                errors.reject(null, getMessage("client_messages.text.rationalizer_marcie_synch_in_progress"));

            }
        }
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.QueryEdit);

        try {
            RationalizerQueryEditWrapper wrapper = (RationalizerQueryEditWrapper) commandObj;
            wrapper.saveContentConsolidationOptions();

            int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);

            if(action == ACTION_GENERATE_REPORT) {
                /**
                 * ACTION_GENERATE_REPORT
                 */

                analyticsEvent.setAction(Actions.GenerateReport);
                AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Rationalizer);
                auditEvent.setAction(Actions.GenerateReport);
                try {

                    ServiceExecutionContext context = GenerateRationalizerObjectExportService.createContext(
                            wrapper.getExportId(),
                            wrapper.getRationalizerQuery().getId(),
                            wrapper.getSelectedIds(),
                            true,
                            RationalizerQuery.class,
                            UserUtil.getPrincipalUser(),
                            1);
                    Service service = MessagepointServiceFactory.getInstance()
                            .lookupService(GenerateRationalizerObjectExportService.SERVICE_NAME,
                                    GenerateRationalizerObjectExportService.class);
                    service.execute(context);

                    if(!context.getResponse().isSuccessful()) {
                        log.error(" unexpected exception when invoking GenerateRationalizerObjectExportService execute method");
                        ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();
                        params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, wrapper.getRationalizerQuery().getId());
                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                } finally {
                    auditEvent.send();
                }
            } else if(action == ACTION_MODIFY_DOCUMENTS) {

                analyticsEvent.setAction(Actions.ModifyDocuments);

                ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForModifyQueryDocuments(
                        wrapper.getModifyDocumentAction(),
                        wrapper.getModifyDocumentType(),
                        wrapper.getModifyDocumentMetadataConnectorValue(),
                        wrapper.getModifyDocumentSearchValue(),
                        wrapper.getModifyDocumentReplaceValue(),
                        wrapper.getSelectedIds(),
                        wrapper.getRationalizerQuery().getRationalizerApplication().getId()
                );
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if(serviceResponse.isSuccessful()) {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, wrapper.getRationalizerQuery().getId());
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" RationalizerDocument was not updated. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }

            } else if(action == ACTION_MODIFY_CONTENT) {

                analyticsEvent.setAction(Actions.ModifyContent);


                ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForModifyQueryContent(
                        wrapper.getModifyContentAction(),
                        wrapper.getRationalizerQuery(),
                        wrapper.getModifyContentType(),
                        MetadataFormItemDefinitionDto.revertReplacedCharsInConnectorValue(wrapper.getModifyContentMetadataConnectorValue()),
                        wrapper.getModifyContentSearchValue(),
                        wrapper.getModifyContentReplaceValue(),
                        wrapper.getSelectedIds(),
                        wrapper.getRationalizerQuery().getRationalizerApplication().getId()
                );
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if(serviceResponse.isSuccessful()) {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, wrapper.getRationalizerQuery().getId());
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" RationalizerDocument was not updated. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }

            } else if(action == ACTION_TAG_DOCUMENTS) {

                analyticsEvent.setAction(Actions.TagDocuments);

                ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForDocumentTagging(
                        wrapper.getDocumentTags(),
                        wrapper.getSelectedIds(),
                        wrapper.getRationalizerQuery().getRationalizerApplication().getId()
                );
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if(serviceResponse.isSuccessful()) {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, wrapper.getRationalizerQuery().getId());
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" RationalizerDocument was not tags. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }

            } else if(action == ACTION_TAG_CONTENT) {

                analyticsEvent.setAction(Actions.TagContent);

                ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForContentTagging(
                        wrapper.getContentTags(),
                        wrapper.getSelectedIds(),
                        wrapper.getRationalizerQuery().getRationalizerApplication().getId()
                );
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if(serviceResponse.isSuccessful()) {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, wrapper.getRationalizerQuery().getId());
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful. ");
                    sb.append(" No tags were found in ").append(this.getClass().getName());
                    sb.append(" RationalizerContent. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }
            } else if(action == ACTION_DELETE_CONTENT) {

                analyticsEvent.setAction(Actions.Delete);

                List<String> selectedIds = wrapper.getSelectedIds();
                List<RationalizerContent> selectedContentsList = RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);
                RationalizerQuery query = wrapper.getRationalizerQuery();

                ServiceExecutionContext context = BulkDeleteRationalizerContentService.createContext(selectedContentsList);
                Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteRationalizerContentService.SERVICE_NAME, BulkDeleteRationalizerContentService.class);
                deleteModelService.execute(context);

                ServiceResponse serviceResponse = context.getResponse();
                if(!serviceResponse.isSuccessful()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(BulkDeleteRationalizerContentService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" rationalizer document content is not deleted. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                    return showForm(request, response, errors);
                } else {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, query.getId());
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                }

            } else {

                analyticsEvent.setAction(Actions.UpdateQuery);


                // UPDATE QUERY
                RationalizerQuery query = wrapper.getRationalizerQuery();
                RationalizerApplication app = query.getRationalizerApplication();
                if(app != null) {
                    if(RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == app.getAppSyncStatus()
                            || RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue() == app.getAppSyncStatus()
                            || RationalizerApplicationSyncStatus.SYNCHRONIZATION_IN_PROGRESS.getStatusValue() == app.getAppSyncStatus()) {
                        Map<String, Object> params = new HashMap<>();
                        params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, query.getId());
                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                }

                if(query.getQueryTabId() != RationalizerQueryTab.ID_ADVANCED) {
                    query.setContentComparisonValue("");
                    query.setTrimWhitespace(false);
                    query.setCaseSensitive(false);
                    query.setStringSimilarityAlgorithmTypeId(StringSimilarityAlgorithmType.ID_COSINE_SIMILARITY_MANUAL);
                    query.setCompareResultsToAll(true);
                    if(query.getApplyGrouping()) {
                        query.setComparisonThreshold(80);
                        query.setComparisonThresholdMax(100);
                    }
                    wrapper.getQueryComponents().clear();
                    wrapper.setQueryComponents(new ArrayList<>());
                } else if(query.getQueryTabId() != RationalizerQueryTab.ID_SIMPLE_SIMILARITY) {
                    query.setApplyGrouping(false);
                }

                ServiceExecutionContext context = CreateOrUpdateRationalizerQueryService.createContextForCreateOrUpdateQuery(query, wrapper.getQueryComponents());
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerQueryService.SERVICE_NAME, CreateOrUpdateRationalizerQueryService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if(serviceResponse.isSuccessful()) {
                    long queryId = (Long) serviceResponse.getResultValueBean();
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, queryId);
                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" RationalizerQuery was not updated. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }

            }
        } finally {
            analyticsEvent.send();
        }

    }

    protected boolean isFormSubmission(HttpServletRequest request) {
        String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
        return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
    }

    public static class MetadataFormItemDefinitionDto implements Serializable{
        private String primaryConnector;
        private String name;

        public MetadataFormItemDefinitionDto(String primaryConnectorParam, String nameParam) {
            String tmpPrimaryConnector = primaryConnectorParam;
            if(StringUtils.isNotEmpty(tmpPrimaryConnector)) {
                tmpPrimaryConnector = StringUtils.replace(tmpPrimaryConnector, "(", "_LeftParenthesis_");
                tmpPrimaryConnector = StringUtils.replace(tmpPrimaryConnector, ")", "_RightParenthesis_");
            }

            this.primaryConnector = tmpPrimaryConnector;
            this.name = nameParam;
        }

        public static String revertReplacedCharsInConnectorValue(String primaryConnectorValue) {
            String tmpResult = primaryConnectorValue;
            if(StringUtils.isEmpty(tmpResult)) {
                return tmpResult;
            }

            tmpResult = StringUtils.replace(tmpResult, "_LeftParenthesis_", "(");
            tmpResult = StringUtils.replace(tmpResult, "_RightParenthesis_", ")");

            return tmpResult;
        }

        public String getPrimaryConnector() {
            return this.primaryConnector;
        }

        public String getName() {
            return this.name;
        }

        public void setPrimaryConnector(String primaryConnector) {
            this.primaryConnector = primaryConnector;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean equals(final Object o) {
            if(o == this) {
                return true;
            }
            if(!(o instanceof MetadataFormItemDefinitionDto)) {
                return false;
            }
            final MetadataFormItemDefinitionDto other = (MetadataFormItemDefinitionDto) o;
            if(!other.canEqual((Object) this)) {
                return false;
            }
            final Object this$primaryConnector = this.getPrimaryConnector();
            final Object other$primaryConnector = other.getPrimaryConnector();
            if(this$primaryConnector == null ? other$primaryConnector != null : !this$primaryConnector.equals(other$primaryConnector)) {
                return false;
            }
            final Object this$name = this.getName();
            final Object other$name = other.getName();
            if(this$name == null ? other$name != null : !this$name.equals(other$name)) {
                return false;
            }
            return true;
        }

        protected boolean canEqual(final Object other) {
            return other instanceof MetadataFormItemDefinitionDto;
        }

        public int hashCode() {
            final int PRIME = 59;
            int result = 1;
            final Object $primaryConnector = this.getPrimaryConnector();
            result = result * PRIME + ($primaryConnector == null ? 43 : $primaryConnector.hashCode());
            final Object $name = this.getName();
            result = result * PRIME + ($name == null ? 43 : $name.hashCode());
            return result;
        }

        public String toString() {
            return "MetadataFormItemDefinitionDto(primaryConnector=" + this.getPrimaryConnector() + ", name=" + this.getName() + ")";
        }
    }
}
