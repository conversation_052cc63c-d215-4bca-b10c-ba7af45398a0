package com.prinova.messagepoint.controller.rationalizer;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditValidator;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class RationalizerDocumentEditValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RationalizerDocumentEditWrapper wrapper = (RationalizerDocumentEditWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		// NAME
		if ( wrapper.getRationalizerDocument() == null || wrapper.getRationalizerDocument().getName().trim().isEmpty())
			errors.reject("error.input.mandatory", new String[] {ApplicationUtil.getMessage("page.label.name")}, "");
		
		// DOCUMENT METADATA
		MetadataFormEditValidator.validateMandatoryInputs(wrapper.getFormWrapper(), errors);
		
		// ADD CONTENT
		if ( action == RationalizerDocumentEditController.ACTION_ADD_CONTENT ) {
			
			if ( wrapper.getAddContentCount() == null || Integer.valueOf(wrapper.getAddContentCount()) <= 0 )
				errors.reject("error.input.minvalue", 
						new String[] {ApplicationUtil.getMessage("page.label.added.content"), String.valueOf(1)}, 
						"The minimum value for " + ApplicationUtil.getMessage("page.label.added.content") + " is 1");
			
		}
		


	}
}
