package com.prinova.messagepoint.controller.rationalizer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RationalizerDashboardReadingWrapper extends RationalizerDashboardWrapper implements Serializable {
    private String averageFleschScoreDisplayString;
    private String averageFleschKincaidDisplayString;
    private List<Integer> graphData =  new ArrayList<>();
    private List<String> graphLabels = Arrays.asList(ApplicationUtil.getMessage("page.label.dashboard.reading.good"), ApplicationUtil.getMessage("page.label.dashboard.reading.poor"));

    public RationalizerDashboardReadingWrapper() {
        super();
    }

    public String getAverageFleschScoreDisplayString() {
        return averageFleschScoreDisplayString;
    }

    public String getAverageFleschKincaidDisplayString() {
        return averageFleschKincaidDisplayString;
    }

    public List<Integer> getGraphData() {
        return graphData;
    }

    public String getGraphLabelsAsJS() throws IOException {
        return new ObjectMapper().writeValueAsString(graphLabels);
    }

    public void setGraphData(List<Integer> graphData) {
        this.graphData = graphData;
    }

    public void setAverageFleschScoreDisplayString(String averageFleschScoreDisplayString) {
        this.averageFleschScoreDisplayString = averageFleschScoreDisplayString;
    }

    public void setAverageFleschKincaidDisplayString(String averageFleschKincaidDisplayString) {
        this.averageFleschKincaidDisplayString = averageFleschKincaidDisplayString;
    }
}