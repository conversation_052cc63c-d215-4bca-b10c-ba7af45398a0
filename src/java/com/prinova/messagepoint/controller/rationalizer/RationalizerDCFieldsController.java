package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerDCFieldsService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class RationalizerDCFieldsController extends MessagepointController {
	private static final Log log = LogUtil.getLog(RationalizerDCFieldsController.class);

	public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";
	public static final String REQ_PARAM_ACTION = "action";
	public static final String REQ_PARAM_DOCUMENT_CONTENT_ID = "rationalizerDocumentContentId";

	public static final int ACTION_UPDATE_FIELDS = 1;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		RationalizerDCFieldsWrapper wrapper = (RationalizerDCFieldsWrapper) command;

		Map<String, Object> referenceData = new HashMap<>();

		RationalizerDocumentContent documentContent = wrapper.getRationalizerDocumentContent();

		referenceData.put("rationalizerDocumentContent", documentContent);
		referenceData.put("order", wrapper.getOrder());
		referenceData.put("messageName", wrapper.getMessageName());
		referenceData.put("zoneConnector", wrapper.getZoneConnector());

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(RationalizerDocumentContent.class, new IdCustomEditor<>(RationalizerDocumentContent.class));
	}

	protected RationalizerDCFieldsWrapper formBackingObject(HttpServletRequest request) throws Exception {
		String rationalizerDocumentContentGuid	= ServletRequestUtils.getStringParameter(request, REQ_PARAM_DOCUMENT_CONTENT_ID, "");

		RationalizerDocumentContent documentContent = RationalizerDocumentContent.findDocumentContentByElasticSearchGuid(rationalizerDocumentContentGuid);

		RationalizerDCFieldsWrapper wrapper = new RationalizerDCFieldsWrapper(documentContent);

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.DocumentContentEdit);

		try {
			RationalizerDCFieldsWrapper wrapper = (RationalizerDCFieldsWrapper) commandObj;

			int action = -1;
			if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
				action = Integer.valueOf(wrapper.getActionValue()).intValue();
			}

			if ( action == ACTION_UPDATE_FIELDS ) {

				analyticsEvent.setAction(Actions.Edit);

				RationalizerDCFieldsService rationalizerDCFieldsService = (RationalizerDCFieldsService) ApplicationUtil.getBean("rationalizerDCFieldsService");
				ServiceResponse serviceResponse = rationalizerDCFieldsService.updateFields(wrapper);

				if (serviceResponse.isSuccessful()) {

					Map<String, Object> parms = new HashMap<>();
					parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
					return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
				} else {
					StringBuilder sb = new StringBuilder();
					sb.append("RationalizerDCFieldsService");
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" content was not added to RationalizerDocumentContent. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return super.showForm(request, response, errors);
				}
			}

			return null;

		} finally {
			analyticsEvent.send();
		}
	}
}
