package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RationalizerQueryListWrapper implements Serializable {

    private static final long serialVersionUID = 10835077309427545L;

    private String actionValue;
    private List<Long> selectedIds;
    private String userNote;
    private User assignedToUser;

    private String applicationName;
    private String metatags;
    private String description;
    private String cloneName;

    private String documentName;

    private RationalizerApplication currentApplication;

    private String exportId = DateUtil.exportDateTimeStamp();
    private String exportName = "";
    private int extType = 0;
    private int historyExtType = 0;

    private Boolean reSync = false;

    private BrandProfile existingApplicationBrandProfile;

    public RationalizerQueryListWrapper() {
        super();
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public List<Long> getSelectedIds() {
        return selectedIds;
    }

    public void setSelectedIds(List<Long> selectedIds) {
        this.selectedIds = selectedIds;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getMetatags() {
        return metatags;
    }

    public void setMetatags(String metatags) {
        this.metatags = metatags;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public RationalizerApplication getCurrentApplication() {
        return currentApplication;
    }

    public void setCurrentApplication(RationalizerApplication currentApplication) {
        this.currentApplication = currentApplication;
    }

    public List<RationalizerQuery> getSelectedQueryList() {
        List<RationalizerQuery> selectedList = new ArrayList<>();
        for (Long selectedId : this.selectedIds) {
            selectedList.add(HibernateUtil.getManager().getObject(RationalizerQuery.class, selectedId));
        }
        return selectedList;
    }

    public String getExportId() {
        return exportId;
    }

    public void setExportId(String exportId) {
        this.exportId = exportId;
    }

    public String getExportName() {
        return exportName;
    }

    public void setExportName(String exportName) {
        this.exportName = exportName;
    }

    public int getExtType() {
        return extType;
    }

    public void setExtType(int extType) {
        this.extType = extType;
    }

    public Boolean getReSync() {
        return reSync;
    }

    public void setReSync(Boolean reSync) {
        this.reSync = reSync;
    }

    public String getCloneName() {
        return cloneName;
    }

    public void setCloneName(String cloneName) {
        this.cloneName = cloneName;
    }

    public String getUserNote() {
        return userNote;
    }

    public void setUserNote(String userNote) {
        this.userNote = userNote;
    }

    public User getAssignedToUser() {
        return assignedToUser;
    }

    public void setAssignedToUser(User assignedToUser) {
        this.assignedToUser = assignedToUser;
    }

    public BrandProfile getExistingApplicationBrandProfile() {
        return existingApplicationBrandProfile;
    }

    public void setExistingApplicationBrandProfile(BrandProfile existingApplicationBrandProfile) {
        this.existingApplicationBrandProfile = existingApplicationBrandProfile;
    }

    public int getHistoryExtType() {
        return historyExtType;
    }

    public void setHistoryExtType(int historyExtType) {
        this.historyExtType = historyExtType;
    }
}