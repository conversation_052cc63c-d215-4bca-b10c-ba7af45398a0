package com.prinova.messagepoint.controller.rationalizer;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditValidator;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;

public class RationalizerMetadataEditValidator extends MetadataFormEditValidator {

	public boolean supports(Class clazz) {
		boolean result = false;
		try {
			result = Class.forName(this.getClassName()).isAssignableFrom(clazz);
		} catch (ClassNotFoundException e) {
			throw new RuntimeException("Invalid class name");
		}
		return result;
	}

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RationalizerMetadataEditWrapper wrapper = (RationalizerMetadataEditWrapper) commandObj;
		
		if ( wrapper.getFormWrapper() != null ){
			MetadataFormEditValidator.validateMandatoryInputs(wrapper.getFormWrapper(), errors);
			
			for (MetadataFormItem currentItem: wrapper.getFormWrapper().getMetadataForm().getFormItems() ){
				if ( currentItem.getItemDefinition().getPrimaryConnector() != null && currentItem.getItemDefinition().getPrimaryConnector().equalsIgnoreCase("order") ){
					if(currentItem.getValue() == null || currentItem.getValue().isEmpty()){
						errors.reject("error.rationalizer.order.number.required");
					}
				}
			}
		}
	}
}