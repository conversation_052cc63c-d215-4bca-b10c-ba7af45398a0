package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.rationalizer.SearchResultIds;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerConsolidateService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.RandomGUID;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateController.ViewTypeEnum.DUPLICATES;
import static com.prinova.messagepoint.controller.rationalizer.RationalizerConsolidateController.ViewTypeEnum.SIMILARITIES;
import static com.prinova.messagepoint.model.util.RationalizerDashbordTagUtil.constructContentNameHtmlTag;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;
import static java.text.MessageFormat.format;

public class RationalizerConsolidateController extends MessagepointController {
    public static final String REQ_PARAM_RATIONALIZER_CONTENT_ID = "rationalizerContentId";
    public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";
    public static final String REQ_VIEW_TYPE = "viewType";

    @Override
    protected RationalizerConsolidateWrapper formBackingObject(HttpServletRequest request) throws Exception {
        String type = ServletRequestUtils.getStringParameter(request, REQ_VIEW_TYPE, "");
        String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_CONTENT_ID, "");
        RationalizerConsolidateWrapper rationalizerConsolidateWrapper = new RationalizerConsolidateWrapper();

        RationalizerContent contextContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerContentGuid);
        if (contextContent == null) {
            return rationalizerConsolidateWrapper;
        }

        final RationalizerSharedContent rationalizerSharedContent = contextContent.computeRationalizerSharedContent();
        if (rationalizerSharedContent != null) {
            contextContent = rationalizerSharedContent;
        }

        RationalizerApplication rationalizerApplication = contextContent.computeRationalizerApplication();

        if (rationalizerApplication == null) {
            return rationalizerConsolidateWrapper;
        }

        RationalizerConsolidateService rationalizerConsolidateService = (RationalizerConsolidateService) ApplicationUtil.getBean("rationalizerConsolidateService");

        RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
        RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

        List<String> similaritiesGuids = rationalizerConsolidateService.retrieveSimilaritiesGuids(rationalizerElasticSearchHandler, contextContent);
        List<String> duplicatesGuids = rationalizerConsolidateService.retrieveDuplicatedGuids(rationalizerElasticSearchHandler, contextContent);

        String similaritiesIdsArrayGuid = RandomGUID.getGUID().toLowerCase();
        String duplicatesIdsArrayGuid = RandomGUID.getGUID().toLowerCase();

        int similaritiesCount = similaritiesGuids.size();
        int duplicatesCount =  duplicatesGuids.size();

        User principal = UserUtil.getPrincipalUser();
        User requestor = User.findById(principal.getId());

        SearchResultIds.insertSearchResultEntry(similaritiesIdsArrayGuid, requestor, similaritiesGuids);
        SearchResultIds.insertSearchResultEntry(duplicatesIdsArrayGuid, requestor, duplicatesGuids);

        rationalizerConsolidateWrapper.setSimilaritiesDisplayString(format(getMessage("page.label.consolidate.similarities"), similaritiesCount));
        rationalizerConsolidateWrapper.setDuplicatesDisplayString(format(getMessage("page.label.consolidate.duplicates"), duplicatesCount));
        rationalizerConsolidateWrapper.setTargetContent(constructContentNameHtmlTag(contextContent));
        rationalizerConsolidateWrapper.setSimilaritiesCount(similaritiesCount);
        rationalizerConsolidateWrapper.setSimilaritiesIdsArrayGuid(similaritiesIdsArrayGuid);
        rationalizerConsolidateWrapper.setDuplicatesCount(duplicatesCount);
        rationalizerConsolidateWrapper.setDuplicatesIdsArrayGuid(duplicatesIdsArrayGuid);
        rationalizerConsolidateWrapper.setViewType(constructSelectedViewType(type, similaritiesCount, duplicatesCount));

        return rationalizerConsolidateWrapper;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {

        String elasticSearchContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_CONTENT_ID, "");
        if (StringUtils.isEmpty(elasticSearchContentGuid) || RationalizerSharedContent.isElasticSearchGuidForSharedContent(elasticSearchContentGuid)) {
            return super.showForm(request, response, errors);
        }

        final RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(elasticSearchContentGuid);
        if (rationalizerContent == null) {
            return super.showForm(request, response, errors);
        }

        final RationalizerSharedContent rationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();
        if (rationalizerSharedContent == null) {
            return super.showForm(request, response, errors);
        }

        final Map<String, String[]> parameterMap = request.getParameterMap();

        Map<String, Object> params = parameterMap.entrySet().stream()
                .map(entry -> {
                    final String key = entry.getKey();
                    if (key.equals(REQ_PARAM_RATIONALIZER_CONTENT_ID)) {
                        return Pair.of(key, new String[] {
                                        rationalizerSharedContent.buildElasticSearchGuid()
                                }
                        );
                    }

                    return entry;
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));


        return new ModelAndView(new RedirectView(getSuccessView()), params);
    }

    private String constructSelectedViewType(String type, int similaritiesCount, int duplicatesCount) {
        if (StringUtils.isNotEmpty(type)) {
            return type;
        }
        if (duplicatesCount > similaritiesCount) {
            return DUPLICATES.name;
        }

        return SIMILARITIES.name;
    }

    public enum ViewTypeEnum {
        SIMILARITIES("similarities"),
        DUPLICATES("duplicates");

        private String name;

        ViewTypeEnum(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

}
