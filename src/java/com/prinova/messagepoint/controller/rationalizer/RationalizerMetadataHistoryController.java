package com.prinova.messagepoint.controller.rationalizer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.model.metadata.HistoricalMetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.security.StringXSSEditor;

public class RationalizerMetadataHistoryController extends MessagepointController {
	private static final Log log = LogUtil.getLog(RationalizerMetadataHistoryController.class);
	
	public static String REQ_PRAM_RATIONALIZER_CONTENT_ID 	= "rationalizerContentId";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PRAM_RATIONALIZER_CONTENT_ID, "");
		RationalizerDocumentContent rationalizerContent = RationalizerDocumentContent.findDocumentContentByElasticSearchGuid(rationalizerContentGuid);
		referenceData.put("rationalizerContent", rationalizerContent);
		
		List<HistoricalMetadataForm> histRationlizerMetadataList = rationalizerContent.getMetadataHistOrderByCreated();
		if(histRationlizerMetadataList.isEmpty()){
			List<MetadataForm> metadataFormList = new ArrayList<>();
			metadataFormList.add(rationalizerContent.getParsedContentForm());
			referenceData.put("histRationlizerMetadataList", metadataFormList);
		}else{
			referenceData.put("histRationlizerMetadataList", histRationlizerMetadataList);
		}
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new RationalizerContentHistoryWrapper();
	}
}
