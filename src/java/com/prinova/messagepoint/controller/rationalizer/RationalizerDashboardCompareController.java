package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerAppNavigationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.RationalizerUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class RationalizerDashboardCompareController extends MessagepointController {

    private static final Log log = LogUtil.getLog(RationalizerDashboardCompareController.class);

    public static final String REQ_PARAM_APP_ID_PARAM 	= "rationalizerApplicationId";
    public static final String SOURCE_DASHBOARD_COMPARE_SELECTION = "sourceDashboardCompareSelection";
    public static final String TARGET_DASHBOARD_COMPARE_SELECTION = "targetDashboardCompareSelection";


    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_APP_ID_PARAM, -1);
        referenceData.put("rationalizerApplicationId", rationalizerApplicationId);
        referenceData.put("navTreeBreadCrumbTrail", RationalizerUtil.buildDashboardCompareTreeBreadCrumbTrail(rationalizerApplicationId));
        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_APP_ID_PARAM, -1);
        RationalizerApplicationNavigationWrapper command;
        if(appId != -1){
            RationalizerApplication application = RationalizerApplication.findById(appId);
            command = new RationalizerApplicationNavigationWrapper(application);
        }else{
            command = new RationalizerApplicationNavigationWrapper();
        }

        return command;
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.ApplicationNavigation);
        analyticsEvent.setAction(Actions.Update);

        try {
            RationalizerApplicationNavigationWrapper command = (RationalizerApplicationNavigationWrapper)commandObj;
            ServiceExecutionContext context = UpdateRationalizerAppNavigationService.createContext(command.getRationalizerApplication(), command.getItemDefinitions());

            Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateRationalizerAppNavigationService.SERVICE_NAME, UpdateRationalizerAppNavigationService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(UpdateRationalizerAppNavigationService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                sb.append(" selected items were not saved. ");
                log.error(sb.toString());
                ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                return super.showForm(request, response, errors);
            } else {
                String sourceSelections = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                String targetSelections = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                Map<String, Object> params = new HashMap<>();
                params.put(REQ_PARAM_APP_ID_PARAM, command.getRationalizerApplication().getId());
                params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                params.put(SOURCE_DASHBOARD_COMPARE_SELECTION, sourceSelections);
                params.put(TARGET_DASHBOARD_COMPARE_SELECTION, targetSelections);
                return new ModelAndView(new RedirectView(getSuccessView()), params);
            }
        } finally {
            analyticsEvent.send();
        }
    }
}
