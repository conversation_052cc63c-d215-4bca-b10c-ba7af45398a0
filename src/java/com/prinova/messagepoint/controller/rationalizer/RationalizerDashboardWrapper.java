package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.DateUtil;

import java.io.Serializable;
import java.util.List;

public class RationalizerDashboardWrapper implements Serializable {

    private static final long serialVersionUID = 10835077309427545L;

    private String actionValue;
    private List<String> selectedIds;
    private String applicationName;
    private RationalizerApplication currentApplication;
    private String exportId = DateUtil.exportDateTimeStamp();
    private String exportName = "";
    private int extType = 0;
    private String cloneName;
    private String description;
    private String metatags;
    private int brand = 13;
    private Boolean reSync = false;
    private BrandProfile existingApplicationBrandProfile;
    private String sourceDashboardCompareSelection;
    private String targetDashboardCompareSelection;

    public RationalizerDashboardWrapper() {
        super();
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public RationalizerApplication getCurrentApplication() {
        return currentApplication;
    }

    public void setCurrentApplication(RationalizerApplication currentApplication) {
        this.currentApplication = currentApplication;
    }

    public int getBrand() {
        return brand;
    }

    public void setBrand(int brand) {
        this.brand = brand;
    }

    public String getCloneName() {
        return cloneName;
    }

    public void setCloneName(String cloneName) {
        this.cloneName = cloneName;
    }

    public String getExportId() {
        return exportId;
    }

    public void setExportId(String exportId) {
        this.exportId = exportId;
    }

    public String getExportName() {
        return exportName;
    }

    public void setExportName(String exportName) {
        this.exportName = exportName;
    }

    public int getExtType() {
        return extType;
    }

    public void setExtType(int extType) {
        this.extType = extType;
    }

    public Boolean getReSync() {
        return reSync;
    }

    public void setReSync(Boolean reSync) {
        this.reSync = reSync;
    }

    public String getMetatags() {
        return metatags;
    }

    public void setMetatags(String metatags) {
        this.metatags = metatags;
    }

    public String getDescription() {
        return description;
    }

    public List<String> getSelectedIds() {
        return selectedIds;
    }

    public void setSelectedIds(List<String> selectedIds) {
        this.selectedIds = selectedIds;
    }

    public BrandProfile getExistingApplicationBrandProfile() {
        return existingApplicationBrandProfile;
    }

    public void setExistingApplicationBrandProfile(BrandProfile existingApplicationBrandProfile) {
        this.existingApplicationBrandProfile = existingApplicationBrandProfile;
    }

    public String getSourceDashboardCompareSelection() {
        return sourceDashboardCompareSelection;
    }

    public void setSourceDashboardCompareSelection(String sourceDashboardCompareSelection) {
        this.sourceDashboardCompareSelection = sourceDashboardCompareSelection;
    }

    public String getTargetDashboardCompareSelection() {
        return targetDashboardCompareSelection;
    }

    public void setTargetDashboardCompareSelection(String targetDashboardCompareSelection) {
        this.targetDashboardCompareSelection = targetDashboardCompareSelection;
    }
}