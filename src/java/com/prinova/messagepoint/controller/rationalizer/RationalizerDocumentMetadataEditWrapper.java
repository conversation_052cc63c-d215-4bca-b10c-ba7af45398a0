package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;

import java.io.Serial;

public class RationalizerDocumentMetadataEditWrapper implements RationalizerMetadataEditWrapper {

	@Serial
	private static final long serialVersionUID = -8762045351897741463L;

	private RationalizerDocument		rationalizerDocument;
	private MetadataFormEditWrapper		formWrapper;

	public RationalizerDocumentMetadataEditWrapper(RationalizerDocument document) {
		super();
		this.setRationalizerDocument(document);
		if ( document.getParsedDocumentForm() != null ) {
			this.formWrapper = new MetadataFormEditWrapper(document.getParsedDocumentForm());
			formWrapper.removeMetadataFromEditWrapperByConnectorName("Name");
		}
	}

	public RationalizerDocument getRationalizerDocument() {
		return rationalizerDocument;
	}
	public void setRationalizerDocument(RationalizerDocument rationalizerDocument) {
		this.rationalizerDocument = rationalizerDocument;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	@Override
	public String getMetatags() {
		return rationalizerDocument.getMetatags();
	}

	public void setMetatags(String metatags) {
		rationalizerDocument.setMetatags(metatags);
	}
}