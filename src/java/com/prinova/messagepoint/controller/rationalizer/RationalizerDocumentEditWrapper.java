package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.rationalizer.HistoricalRationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent.buildElasticSearchGuidsForDocumentContents;
import static com.prinova.messagepoint.model.rationalizer.RationalizerContent.deleteRationalizerContentsFromElasticSearchDelayed;
import static com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent.sendSharedContentsToElasticSearchDelayed;

public class RationalizerDocumentEditWrapper implements Serializable {

	private static final long serialVersionUID = 49040380574648779L;

	private String 								actionValue;

	private RationalizerDocument				rationalizerDocument;
	private List<RationalizerDocumentContent> documentContents;

	private List<String> elasticSearchGuids;
	private List<Integer> documentContentsOrdersList;
	private List<String> markupContent = new ArrayList<>();
	private List<String> reorderedMarkupContent = new ArrayList<>();
	private List<String> textContent = new ArrayList<>();
	private List<String> reorderedTextContent = new ArrayList<>();
	private List<Integer> contentActions = new ArrayList<>();
	private List<Integer> reorderedContentActions = new ArrayList<>();
	private List<String> selectedIds = new ArrayList<>();

	private MetadataFormEditWrapper				formWrapper;

	private String								addContentCount			= "1";
	private String								insertAfterOrder;
	private String								insertedMarkupContent;

	private String								modifyContentMetadataConnectorValue;
	private String								modifyContentMetadataReplaceValue;

	private String initialDocumentName;
	private String initialDocumentTags;

	public RationalizerDocumentEditWrapper(RationalizerDocument document) {
		super();
		this.setRationalizerDocument(document);
		this.initialDocumentName = document.getName();
		this.initialDocumentTags = document.getMetatags();
	}

	public void removeDeletedContents() {
		if (CollectionUtils.isEmpty(elasticSearchGuids) || CollectionUtils.isEmpty(contentActions)) {
			return;
		}

		Collection<RationalizerDocumentContent> rationalizerDocumentContents = rationalizerDocument.shallowCopyOfContents();
		if (CollectionUtils.isEmpty(rationalizerDocumentContents)) {
			return;
		}

		Map<String, RationalizerDocumentContent> documentContentMap = rationalizerDocumentContents.stream()
				.collect(Collectors.toMap(RationalizerDocumentContent::buildElasticSearchGuid, Function.<RationalizerDocumentContent>identity()));

		List<RationalizerDocumentContent> removedContents = new LinkedList<>();
		Set<RationalizerSharedContent> sharedContentsToUpdate = new LinkedHashSet<>();

		Iterator<String> elasticSearchGuidsIterator = elasticSearchGuids.iterator();
		Iterator<Integer> contentActionsIterator = contentActions.iterator();
		while (elasticSearchGuidsIterator.hasNext() && contentActionsIterator.hasNext()) {
			String elasticSearchGuid = elasticSearchGuidsIterator.next();
			Integer contentAction = contentActionsIterator.next();

			RationalizerDocumentContent rationalizerDocumentContent = documentContentMap.get(elasticSearchGuid);
			if (rationalizerDocumentContent == null) {
				continue;
			}

			if (contentAction != null && contentAction == -1) {
				removedContents.add(rationalizerDocumentContent);
				final RationalizerSharedContent sharedContent = rationalizerDocumentContent.computeRationalizerSharedContent();
				if (sharedContent != null) {
					sharedContentsToUpdate.add(sharedContent);
				}

				rationalizerDocumentContent.removeFromRationalizerDocument();
				HistoricalRationalizerDocumentContent.deleteByRationalizerContentId(rationalizerDocumentContent.getId());
			}
		}

		if (removedContents.size() <= 0) {
			return;
		}

		rationalizerDocument.save();
		HibernateUtil.getManager().getSession().flush();

		RationalizerApplication rationalizerApplication = rationalizerDocument.getRationalizerApplication();

		RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
		RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication, true);

		deleteRationalizerContentsFromElasticSearchDelayed(
				UserUtil.getPrincipalUser(),
				rationalizerElasticSearchHandler,
				buildElasticSearchGuidsForDocumentContents(removedContents)
		);

		sendSharedContentsToElasticSearchDelayed(
				rationalizerElasticSearchHandler,
				sharedContentsToUpdate
		);
	}

	public void computeFieldsForProcessing() {
		this.documentContents = buildDocumentContentsInUserInterfaceOrder();
		this.reorderedMarkupContent = computeReorderedOutputList(documentContents, elasticSearchGuids, markupContent);
		this.reorderedContentActions = computeReorderedOutputList(documentContents, elasticSearchGuids, contentActions);
		this.reorderedTextContent = computeReorderedOutputList(documentContents, elasticSearchGuids, textContent);

		if ( rationalizerDocument.getParsedDocumentForm() != null ) {
			this.formWrapper = new MetadataFormEditWrapper(rationalizerDocument.getParsedDocumentForm());
		} else if ( rationalizerDocument.getRationalizerApplication().getParsedDocumentFormDefinition() != null ) {
			this.formWrapper = new MetadataFormEditWrapper(rationalizerDocument.getRationalizerApplication().getParsedDocumentFormDefinition());
		}
	}

	public void computeFieldsForDisplay() {
		this.documentContents = rationalizerDocument.getRationalizerDocumentContentsInOrder();

		if ( rationalizerDocument.getParsedDocumentForm() != null ) {
			this.formWrapper = new MetadataFormEditWrapper(rationalizerDocument.getParsedDocumentForm());
		} else if ( rationalizerDocument.getRationalizerApplication().getParsedDocumentFormDefinition() != null ) {
			this.formWrapper = new MetadataFormEditWrapper(rationalizerDocument.getRationalizerApplication().getParsedDocumentFormDefinition());
		}
		formWrapper.removeMetadataFromEditWrapperByConnectorName("Name");

		elasticSearchGuids = new ArrayList<>();
		documentContentsOrdersList = new ArrayList<>();
		markupContent = new ArrayList<>();
		textContent = new ArrayList<>();
		contentActions = new ArrayList<>();

		for (RationalizerDocumentContent currentContent: this.documentContents) {
			elasticSearchGuids.add(currentContent.buildElasticSearchGuid());
			documentContentsOrdersList.add(currentContent.getOrder());

			String markupContentForView = currentContent.buildDisplayedMarkupContent();
			markupContent.add(markupContentForView);
			String textContentForView = markupContentForView.replaceAll("> <", ">&nbsp;<");
			textContent.add(textContentForView);

			contentActions.add(0);
		}
	}

	public int computeAdjustedInsertAfterOrder() {
		return this.insertAfterOrder != null ? Integer.valueOf(this.insertAfterOrder) : this.documentContents.size();
	}

	private List<RationalizerDocumentContent> buildDocumentContentsInUserInterfaceOrder() {
		List<RationalizerDocumentContent> result = new LinkedList<>();

		if (CollectionUtils.isEmpty(elasticSearchGuids) || CollectionUtils.isEmpty(contentActions)) {
			return result;
		}

		Collection<RationalizerDocumentContent> tmpDocumentContents = rationalizerDocument.shallowCopyOfContents();
		if (CollectionUtils.isEmpty(tmpDocumentContents)) {
			return result;
		}

		Map<String, RationalizerDocumentContent> documentContentMap = tmpDocumentContents.stream()
				.collect(Collectors.toMap(RationalizerDocumentContent::buildElasticSearchGuid, Function.<RationalizerDocumentContent>identity()));

		boolean updatesDone = false;

		Iterator<String> elasticSearchGuidsIterator = elasticSearchGuids.iterator();
		Iterator<Integer> contentActionsIterator = contentActions.iterator();
		Iterator<Integer> documentContentsOrdersIterator = documentContentsOrdersList.iterator();
		while (elasticSearchGuidsIterator.hasNext()
				&& contentActionsIterator.hasNext()
				&& documentContentsOrdersIterator.hasNext()
		) {
			String elasticSearchGuid = elasticSearchGuidsIterator.next();
			Integer contentAction = contentActionsIterator.next();
			Integer documentContentOrder = documentContentsOrdersIterator.next();

			RationalizerDocumentContent rationalizerDocumentContent = documentContentMap.get(elasticSearchGuid);
			if (rationalizerDocumentContent == null) {
				continue;
			}

			if (contentAction == null || contentAction == -1) {
				continue;
			}

			if (!Objects.equals(documentContentOrder, rationalizerDocumentContent.getOrder())) {
				rationalizerDocumentContent.setOrder(documentContentOrder);
				rationalizerDocumentContent.save();
				updatesDone = true;
			}
			result.add(rationalizerDocumentContent);
		}

		result.sort(Comparator.comparing(RationalizerDocumentContent::getOrder,
				(order01, order02) -> {
					if (order01 == null && order02 == null) {
						return 0;
					} else if (order01 == null) {
						return -1;
					} else if (order02 == null) {
						return 1;
					} else {
						return order01.compareTo(order02);
					}
				})
		);

		if (updatesDone) {
			HibernateUtil.getManager().getSession().flush();
		}

		return result;
	}


	private static <T> List<T> computeReorderedOutputList(
			List<RationalizerDocumentContent> contentsParam,
			List<String> elasticSearchGuidsParam,
			List<T> initialListParam
	) {
		if (CollectionUtils.isEmpty(elasticSearchGuidsParam) || CollectionUtils.isEmpty(initialListParam)) {
			return new ArrayList<>();
		}

		Map<String, T> elasticSearchGuidToOutputMap = new LinkedHashMap<>();
		Iterator<String> elasticSearchGuidsIterator = elasticSearchGuidsParam.iterator();
		Iterator<T> initialListIterator = initialListParam.iterator();
		while (elasticSearchGuidsIterator.hasNext() && initialListIterator.hasNext()) {
			String elasticSearchGuid = elasticSearchGuidsIterator.next();
			T tmpValue = initialListIterator.next();

			elasticSearchGuidToOutputMap.put(elasticSearchGuid, tmpValue);
		}

		return contentsParam.stream()
				.map(content -> elasticSearchGuidToOutputMap.get(content.buildElasticSearchGuid()))
				.collect(Collectors.toCollection(ArrayList::new));
	}

	public List<String> buildTranslatedReorderedMarkupContents() {
		return new ArrayList<>(this.getReorderedMarkupContent());
	}

	public List<String> getReorderedMarkupContent() {
		return reorderedMarkupContent;
	}

	public List<String> getReorderedTextContent() {
		return reorderedTextContent;
	}

	public List<Integer> getReorderedContentActions() {
		return reorderedContentActions;
	}

	public String getInitialDocumentTags() {
		return initialDocumentTags;
	}

	public void setInitialDocumentTags(String initialDocumentTags) {
		this.initialDocumentTags = initialDocumentTags;
	}

	public String getInitialDocumentName() {
		return initialDocumentName;
	}

	public void setInitialDocumentName(String initialDocumentName) {
		this.initialDocumentName = initialDocumentName;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public RationalizerDocument getRationalizerDocument() {
		return rationalizerDocument;
	}
	public void setRationalizerDocument(RationalizerDocument rationalizerDocument) {
		this.rationalizerDocument = rationalizerDocument;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	public String getAddContentCount() {
		return addContentCount;
	}
	public void setAddContentCount(String addContentCount) {
		this.addContentCount = addContentCount;
	}

	public List<RationalizerDocumentContent> getDocumentContents() {
		return documentContents;
	}

	public List<String> getElasticSearchGuids() {
		return elasticSearchGuids;
	}

	public void setElasticSearchGuids(List<String> elasticSearchGuids) {
		this.elasticSearchGuids = elasticSearchGuids;
	}

	public List<Integer> getDocumentContentsOrdersList() {
		return documentContentsOrdersList;
	}

	public void setDocumentContentsOrdersList(List<Integer> documentContentsOrdersList) {
		this.documentContentsOrdersList = documentContentsOrdersList;
	}

	public List<String> getMarkupContent() {
		return markupContent;
	}

	public void setMarkupContent(List<String> markupContent) {
		this.markupContent = markupContent;
	}

	public List<String> getTextContent() {
		return textContent;
	}

	public void setTextContent(List<String> textContent) {
		this.textContent = textContent;
	}

	public List<Integer> getContentActions() {
		return contentActions;
	}

	public void setContentActions(List<Integer> contentActions) {
		this.contentActions = contentActions;
	}

	public List<String> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<String> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getInsertAfterOrder() {
		return insertAfterOrder;
	}
	public void setInsertAfterOrder(String insertAfterOrder) {
		this.insertAfterOrder = insertAfterOrder;
	}

	public String getInsertedMarkupContent() {
		return insertedMarkupContent;
	}

	public void setInsertedMarkupContent(String insertedMarkupContent) {
		this.insertedMarkupContent = insertedMarkupContent;
	}

	public String getModifyContentMetadataConnectorValue() {
		return modifyContentMetadataConnectorValue;
	}
	public void setModifyContentMetadataConnectorValue(String modifyContentMetadataConnectorValue) {
		this.modifyContentMetadataConnectorValue = modifyContentMetadataConnectorValue;
	}

	public String getModifyContentMetadataReplaceValue() {
		return modifyContentMetadataReplaceValue;
	}
	public void setModifyContentMetadataReplaceValue(String modifyContentMetadataReplaceValue) {
		this.modifyContentMetadataReplaceValue = modifyContentMetadataReplaceValue;
	}
}