package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.SentimentEnum;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

public class RationalizerDashboardSentimentDetailsController extends MessagepointController {

    private static final String SELECTED_CONTENT_ID = "selectedContentId";
    private static final String SELECTED_SENTIMENT_ID = "selectedSentimentId";

    protected RationalizerDashboardDetailsWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerDashboardDetailsWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        String comparisonContentGuid = ServletRequestUtils.getStringParameter(request, SELECTED_CONTENT_ID, "");
        if (StringUtils.isNotEmpty(comparisonContentGuid)) {
            RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(comparisonContentGuid);
            referenceData.put("rationalizerContent", rationalizerContent);
        }
        long selectedSentimentId = ServletRequestUtils.getLongParameter(request, SELECTED_SENTIMENT_ID, -1L);
        if (selectedSentimentId != -1) {
            SentimentEnum sentiment = SentimentEnum.fromId(selectedSentimentId);
            switch (sentiment) {
                case NEGATIVE: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_negative_sentiment");
                    break;
                }
                case NEUTRAL: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_neutral_sentiment");
                    break;
                }
                case POSITIVE: {
                    referenceData.put("sentimentTableHeader", "client_messages.text.content_positive_sentiment");
                    break;
                }
            }
        }

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

        return referenceData;
    }
}
