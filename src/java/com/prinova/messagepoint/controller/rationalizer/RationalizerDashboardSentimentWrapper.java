package com.prinova.messagepoint.controller.rationalizer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RationalizerDashboardSentimentWrapper extends RationalizerDashboardWrapper implements Serializable {

    private int negativeCount = 0;
    private  String averageSentimentDisplayString;
    private List<Integer> graphData = new ArrayList<>();
    private List<String> graphLabels = Arrays.asList(ApplicationUtil.getMessage("page.label.dashboard.sentiment.negative"), ApplicationUtil.getMessage("page.label.dashboard.sentiment.neutral"), ApplicationUtil.getMessage("page.label.dashboard.sentiment.positive"));

    public RationalizerDashboardSentimentWrapper() {
        super();
    }

    public List<Integer> getGraphData() {
        return graphData;
    }

    public void setGraphData(List<Integer> graphData) {
        this.graphData = graphData;
    }

    public String getGraphLabelsAsJS() throws IOException {
        return new ObjectMapper().writeValueAsString(graphLabels);
    }

    public int getNegativeCount() {
        return negativeCount;
    }

    public void setNegativeCount(int negativeCount) {
        this.negativeCount = negativeCount;
    }

    public String getAverageSentimentDisplayString() {
        return averageSentimentDisplayString;
    }

    public void setAverageSentimentDisplayString(String averageSentimentDisplayString) {
        this.averageSentimentDisplayString = averageSentimentDisplayString;
    }
}