package com.prinova.messagepoint.controller.rationalizer;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.SearchResultIds;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.RandomGUID;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Set;

public class AsyncRationalizerConsolidateController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncRationalizerConsolidateController.class);

    public static final String REQ_PARAM_RATIONALIZER_CONTENT_ID = "rationalizerContentId";
    public static final String REQ_PARAM_SELECTED_IDS_PARAM = "selectedIds";
    public static final String REQ_PARAM_ACTION = "action";

    private static final String ACTION_GENERATE_GUID_FOR_SELECTED_IDS = "generateGuidForSelectedIds";

    public static final String GUID_FOR_SELECTED_IDS = "guidForSelectedIds";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_CONTENT_ID, "");

        if (StringUtils.isEmpty(rationalizerContentGuid)) {
            returnEmptyJson(response);

            return null;
        }

        RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerContentGuid);
        if (rationalizerContent == null) {
            returnEmptyJson(response);

            return null;
        }

        RationalizerApplication rationalizerApp = rationalizerContent.computeRationalizerApplication();
        if (rationalizerApp == null) {
            returnEmptyJson(response);

            return null;
        }

        String selectedIdsString = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        String action = ServletRequestUtils.getStringParameter(request, REQ_PARAM_ACTION, "");
        if (StringUtils.equals(action, ACTION_GENERATE_GUID_FOR_SELECTED_IDS)) {
            handleGenerateGuidForSelectedIds(out, selectedIdsString);

            return null;
        }

        return null;
    }

    private void returnEmptyJson(HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();

            JsonObject jsonObject = new JsonObject();
            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to return empty json.", e);
        }
    }

    private void handleGenerateGuidForSelectedIds(ServletOutputStream out, String selectedIdsString) {
        try {
            User principal = UserUtil.getPrincipalUser();
            User requestor = User.findById(principal.getId());

            final Set<String> selectedIdsSet = SearchResultIds.splitIdsString(selectedIdsString);
            String generatedGuid = RandomGUID.getGUID().toLowerCase();
            SearchResultIds.insertSearchResultEntry(generatedGuid, requestor, selectedIdsSet);

            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty(GUID_FOR_SELECTED_IDS, generatedGuid)
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to generate guid for selected ids.", e);
        }
    }
}