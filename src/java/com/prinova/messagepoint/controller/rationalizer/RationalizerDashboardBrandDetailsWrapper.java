package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;
import java.util.List;

public class RationalizerDashboardBrandDetailsWrapper extends RationalizerDashboardWrapper implements Serializable {

    private List<String> filterItemsList;
    private String selectedFilterTerm;

    public RationalizerDashboardBrandDetailsWrapper() {
        super();
    }

    public List<String> getFilterItemsList() {
        return filterItemsList;
    }

    public void setFilterItemsList(List<String> filterItemsList) {
        this.filterItemsList = filterItemsList;
    }

    public String getSelectedFilterTerm() {
        return selectedFilterTerm;
    }

    public void setSelectedFilterTerm(String selectedFilerTerm) {
        this.selectedFilterTerm = selectedFilerTerm;
    }
}