package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.CloneRationalizerAppBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.DeleteRationalizerAppBackgroundTask;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.triggerBrandUpdateBackgroundTaskIfNeeded;

public class RationalizerApplicationSetupController extends MessagepointController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String REQ_PARAM_ACTION = "action";

    private static final String SUBMIT_TYPE_COPY = "copy";
    private static final String SUBMIT_TYPE_DELETE = "delete";
    private static final String SUBMIT_TYPE_SAVE = "save";

    private static final Log log = LogUtil.getLog(RationalizerApplicationSetupController.class);

    @Override
    public Map<String, Object> referenceData(HttpServletRequest request) {
        Map<String, Object> referenceData = new HashMap<>();

        // filter application list by visibility
        referenceData.put("applications", RationalizerApplication.filterVisibleApplications(UserUtil.getPrincipalUser()));

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerApplication application = RationalizerApplication.findById(rationalizerApplicationId);

        referenceData.put("parsedDocumentMetadataFormDefinitionId", application != null && application.getParsedDocumentFormDefinition() != null ?
                application.getParsedDocumentFormDefinition().getId() : -1);
        referenceData.put("parsedContentMetadataFormDefinitionId", application != null && application.getParsedContentFormDefinition() != null ?
                application.getParsedContentFormDefinition().getId() : -1);

        if (application != null) {
            referenceData.put("application", application);
            RationalizerApplication clonedApplication = RationalizerApplication.findUniqueByParentId(application.getId());
            RationalizerApplication parentApplication = (RationalizerApplication) application.getParentObject();
            referenceData.put("hasClonedAppContext", (clonedApplication != null || parentApplication != null));

            if (clonedApplication != null) {
                referenceData.put("clonedApplication", clonedApplication);
            }
            if (parentApplication != null) {
                referenceData.put("parentApplication", parentApplication);
            }
            referenceData.put("navTreeBreadCrumbTrail", MetadataFormItem.buildRationalizerNavTreeBreadCrumbTrail(rationalizerApplicationId));
            referenceData.put("hasLinkedTouchpoint", application.getLinkedDocument() != null);

        }

        // BRAND
        referenceData.put("brandProfiles", BrandProfile.listAll());

        return referenceData;
    }

    @Override
    public void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(MetadataFormDefinition.class, new IdCustomEditor<>(MetadataFormDefinition.class));
        binder.registerCustomEditor(MetadataFormItemDefinition.class, new IdCustomEditor<>(MetadataFormItemDefinition.class));
        binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
        binder.registerCustomEditor(BrandProfile.class, new IdCustomEditor<>( BrandProfile.class ));
    }

    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerApplicationPropertiesWrapper wrapper =  new RationalizerApplicationPropertiesWrapper();
        if (rationalizerApplicationId > 0) {
            RationalizerApplication currentApplication = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(currentApplication);
            if (currentApplication != null) {
                wrapper.setCloneName(currentApplication.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
                wrapper.setExistingApplicationBrandProfile(currentApplication.getBrandProfile());
            }
            List<DashboardFilter> dashboardFilters = currentApplication.getDashboardFilters();
            DashboardFilter.normalizeValues(dashboardFilters);
            wrapper.setDashboardFilters(dashboardFilters);
        }

        return wrapper;
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.Dashboard);
        try {
            String action = ServletRequestUtils.getStringParameter(request, REQ_PARAM_ACTION, "");
            RationalizerApplicationPropertiesWrapper command = (RationalizerApplicationPropertiesWrapper) commandObj;
            switch (action) {
                case (SUBMIT_TYPE_SAVE): {
                    analyticsEvent.setAction(Actions.UpdateApplication);

                    BrandProfile existingApplicationProfile = command.getExistingApplicationBrandProfile();
                    BrandProfile selectedBrandProfile = command.getCurrentApplication().getBrandProfile();

                    ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateApplication(
                            command.getCurrentApplication().getId(),
                            command.getCurrentApplication().getName(),
                            command.getCurrentApplication().getMetatags(),
                            command.getCurrentApplication().getDescription(),
                            command.getCurrentApplication().getQueryFilterFormItemDefinitions(),
                            command.getDashboardFilters(),
                            command.getReSync());

                    Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                    updateApplicationService.execute(context);

                    if (Boolean.FALSE.equals(command.getReSync())) {
                        triggerBrandUpdateBackgroundTaskIfNeeded(existingApplicationProfile, selectedBrandProfile, command.getCurrentApplication());
                    }

                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        sb.append(" Rationalizer application not updated. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();

                        long newApplicationId = (Long) serviceResponse.getResultValueBean();
                        params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

                        return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_dashboard.form"), params);
                    }
                }
                case (SUBMIT_TYPE_COPY): {
                    analyticsEvent.setAction(Actions.CloneApplication);
                    CloneRationalizerAppBackgroundTask task = new CloneRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), command.getCloneName());
                    MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());

                    return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_dashboard.form"), params);
                }
                case (SUBMIT_TYPE_DELETE): {
                    analyticsEvent.setAction(Actions.DeleteApplication);

                    RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
                    long parentApplicationId = -1;
                    if(rationalizerApp.getParentObject() != null) {
                        // application is a clone
                        parentApplicationId = rationalizerApp.getParentObject().getId();
                    }

                    // delete clone if exists
                    RationalizerApplication clonedRationalizerApp = RationalizerApplication.findUniqueByParentId(rationalizerApp.getId());
                    CountDownLatch latch = null;
                    if (clonedRationalizerApp != null) {
                        latch = new CountDownLatch(1);
                        DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(clonedRationalizerApp.getId(), UserUtil.getPrincipalUser(), latch, true);
                        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
                    }

                    // delete application
                    DeleteRationalizerAppBackgroundTask task2 = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), latch, false);
                    MessagePointRunnableUtil.startThread(task2, Thread.MAX_PRIORITY);

                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, parentApplicationId);

                    return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_dashboard.form"), params);
                }
                default:
                {
                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);
                    return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_dashboard.form"), params);
                }
            }
        } finally {
            analyticsEvent.send();
        }
    }

}
