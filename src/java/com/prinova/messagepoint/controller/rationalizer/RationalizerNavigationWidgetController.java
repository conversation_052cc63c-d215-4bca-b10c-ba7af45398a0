package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;

public class RationalizerNavigationWidgetController extends MessagepointController {
	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(RationalizerNavigationWidgetController.class);
	
	public static final String REQ_PARAM_RATIONALIZER_APP_ID 			= "rationalizerApplicationId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
		
		RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);
		if (rationalizerApplication != null) {
			referenceData.put("rationalizerApplication", rationalizerApplication);
			referenceData.put("appliesTreeStructure", !rationalizerApplication.getTreeStructureFormItemDefinitions().isEmpty());
		}
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		
	}
	
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    		return new ModelAndView(new RedirectView(getSuccessView()));
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}
}
