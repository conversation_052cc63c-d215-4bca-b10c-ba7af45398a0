package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.rationalizer.RationalizerContent;

public class RationalizerQueryEditDetailWrapper implements Serializable {

	private static final long serialVersionUID = 49040380574648779L;
	
	private String 								actionValue;

	private List<Integer>						contentActions		= new ArrayList<>();
	private List<String>						selectedIds			= new ArrayList<>();
	
	private String								objectName;
	
	
	public RationalizerQueryEditDetailWrapper(){
		super();
	}
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<Integer> getContentActions() {
		return contentActions;
	}
	public void setContentActions(List<Integer> contentActions) {
		this.contentActions = contentActions;
	}

	public List<String> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<String> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getObjectName() {
		return objectName;
	}
	public void setObjectName(String objectName) {
		this.objectName = objectName;
	}

	public List<RationalizerContent> getSelectedContents() {
		return RationalizerContent.findRationalizerContentsByElasticSearchGuids(selectedIds);
	}
}