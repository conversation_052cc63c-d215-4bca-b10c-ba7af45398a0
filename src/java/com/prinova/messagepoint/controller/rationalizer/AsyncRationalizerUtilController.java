package com.prinova.messagepoint.controller.rationalizer;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncRationalizerUtilController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncRationalizerUtilController.class);

    public static final String REQ_PARAM_RATIONALIZER_CONTENT_ID = "rationalizerContentId";
    public static final String REQ_PARAM_ACTION = "action";

    private static final String ACTION_GET_SHARED_CONTENT_GUID = "getSharedContentGuid";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String action = ServletRequestUtils.getStringParameter(request, REQ_PARAM_ACTION, "");
        if (StringUtils.equals(action, ACTION_GET_SHARED_CONTENT_GUID)) {
            handleGetSharedContentGuid(request, response);

            return null;
        }

        return null;
    }

    private void returnEmptyJson(HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();

            JsonObject jsonObject = new JsonObject();
            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to return empty json.", e);
        }
    }

    private void handleGetSharedContentGuid(HttpServletRequest request, HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();
            String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_CONTENT_ID, "");

            if (StringUtils.isEmpty(rationalizerContentGuid)) {
                returnEmptyJson(response);

                return;
            }

            RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerContentGuid);
            if (rationalizerContent == null) {
                returnEmptyJson(response);

                return;
            }

            final RationalizerSharedContent sharedContent = rationalizerContent.computeRationalizerSharedContent();
            String sharedContentGuid = sharedContent == null ? "" : sharedContent.buildElasticSearchGuid();
            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty("rationalizerSharedContentGuid", sharedContentGuid)
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to compute the rationalizer shared content guid.", e);
        }
    }
}