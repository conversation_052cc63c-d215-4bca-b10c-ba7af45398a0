package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.CloneRationalizerAppBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.DeleteRationalizerAppBackgroundTask;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static com.prinova.messagepoint.model.brand.util.BrandProfileUtil.triggerBrandUpdateBackgroundTaskIfNeeded;

public class RationalizerDashboardController extends AbstractBaseRationalizerDashboardController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String REQ_PARAM_FORM_ITEM_ID = "formItemId";
    private static final String REQ_PARAM_ACTION = "action";
    private static final String REQ_PARAM_WINDOW_TYPE = "windowType";
    public static final int ACTION_ADD_APPLICATION = 3;
    private static final int ACTION_UPDATE_APPLICATION = 4;
    private static final int ACTION_CLONE_APPLICATION = 7;
    private static final int ACTION_DELETE_REWRITE_APPLICATION = 8;
    private static final int ACTION_DELETE_APPLICATION = 9;
    private static final int ACTION_NAVIGATE_TO_DUPLICATES = 20;
    private static final int ACTION_NAVIGATE_TO_SIMILARITIES = 21;
    private static final int ACTION_NAVIGATE_TO_READING_COMPREHENSION = 22;
    private static final int ACTION_NAVIGATE_TO_SENTIMENT = 23;
    private static final int ACTION_NAVIGATE_TO_METADATA = 24;
    private static final int ACTION_NAVIGATE_TO_BRAND = 25;

    private static final Log log = LogUtil.getLog(RationalizerDashboardController.class);

    protected Object formBackingObject(HttpServletRequest request) {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerDashboardWrapper wrapper = new RationalizerDashboardWrapper();

        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(rationalizerApp);
            if (rationalizerApp != null) {
                wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
                wrapper.setExistingApplicationBrandProfile(rationalizerApp.getBrandProfile());
            }
        }

        return wrapper;
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.Dashboard);

        try {
            int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
            RationalizerDashboardWrapper command = (RationalizerDashboardWrapper) commandObj;
            switch (action) {
                case (ACTION_ADD_APPLICATION): {
                    analyticsEvent.setAction(Actions.AddApplication);

                    ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewApplication(
                            command.getApplicationName(),
                            command.getMetatags(),
                            command.getDescription());

                    Service createApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                    createApplicationService.execute(context);

                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        sb.append(" rationalizer application not created. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();

                        long newApplicationId = (Long) serviceResponse.getResultValueBean();
                        params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                }
                case (ACTION_UPDATE_APPLICATION): {
                    analyticsEvent.setAction(Actions.UpdateApplication);

                    BrandProfile existingApplicationProfile = command.getExistingApplicationBrandProfile();
                    BrandProfile selectedBrandProfile = command.getCurrentApplication().getBrandProfile();

                    ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateApplication(
                            command.getCurrentApplication().getId(),
                            command.getCurrentApplication().getName(),
                            command.getCurrentApplication().getMetatags(),
                            command.getCurrentApplication().getDescription(),
                            command.getCurrentApplication().getQueryFilterFormItemDefinitions(),
                            null,
                            command.getReSync());

                    Service updateApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                    updateApplicationService.execute(context);

                    if (Boolean.FALSE.equals(command.getReSync())) {
                        triggerBrandUpdateBackgroundTaskIfNeeded(existingApplicationProfile, selectedBrandProfile, command.getCurrentApplication());
                    }

                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        sb.append(" rationalizer application not updated. ");
                        log.error(sb.toString());
                        ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                        return showForm(request, response, errors);
                    } else {
                        Map<String, Object> params = new HashMap<>();

                        long newApplicationId = (Long) serviceResponse.getResultValueBean();
                        params.put(REQ_PARAM_RATIONALIZER_APP_ID, newApplicationId);

                        return new ModelAndView(new RedirectView(getSuccessView()), params);
                    }
                }
                case (ACTION_CLONE_APPLICATION): {
                    analyticsEvent.setAction(Actions.CloneApplication);

                    CloneRationalizerAppBackgroundTask task = new CloneRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), command.getCloneName());
                    MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

                    Map<String, Object> parms = new HashMap<>();
                    parms.put(REQ_PARAM_RATIONALIZER_APP_ID, command.getCurrentApplication().getId());

                    return new ModelAndView(new RedirectView(getSuccessView()), parms);
                }
                case (ACTION_DELETE_REWRITE_APPLICATION): {
                    analyticsEvent.setAction(Actions.DeleteRewriteApplication);

                    DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), null, true);
                    MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

                    RationalizerApplication applicationContext = RationalizerApplication.findById(command.getCurrentApplication().getParentObject().getId());

                    Map<String, Object> parms = new HashMap<>();
                    parms.put(REQ_PARAM_RATIONALIZER_APP_ID, applicationContext != null ? applicationContext.getId() : -1);

                    return new ModelAndView(new RedirectView(getSuccessView()), parms);
                }
                case (ACTION_DELETE_APPLICATION): {
                    analyticsEvent.setAction(Actions.DeleteApplication);

                    RationalizerApplication rationalizerApp = RationalizerApplication.findById(command.getCurrentApplication().getId());
                    RationalizerApplication clonedRationalizerApp = RationalizerApplication.findUniqueByParentId(rationalizerApp.getId());
                    CountDownLatch latch = null;
                    if (clonedRationalizerApp != null) {
                        latch = new CountDownLatch(1);
                        DeleteRationalizerAppBackgroundTask task = new DeleteRationalizerAppBackgroundTask(clonedRationalizerApp.getId(), UserUtil.getPrincipalUser(), latch, true);
                        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
                    }
                    DeleteRationalizerAppBackgroundTask task2 = new DeleteRationalizerAppBackgroundTask(command.getCurrentApplication().getId(), UserUtil.getPrincipalUser(), latch, false);
                    MessagePointRunnableUtil.startThread(task2, Thread.MAX_PRIORITY);

                    Map<String, Object> params = new HashMap<>();
                    params.put(REQ_PARAM_RATIONALIZER_APP_ID, -1);

                    return new ModelAndView(new RedirectView(getSuccessView()), params);
                }
                case (ACTION_NAVIGATE_TO_DUPLICATES): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_dashboard_duplicates.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                case (ACTION_NAVIGATE_TO_SIMILARITIES): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() +  "rationalizer/rationalizer_dashboard_similarities.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                case (ACTION_NAVIGATE_TO_READING_COMPREHENSION): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() +  "rationalizer/rationalizer_dashboard_reading.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                case (ACTION_NAVIGATE_TO_SENTIMENT): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() +  "rationalizer/rationalizer_dashboard_sentiment.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                case (ACTION_NAVIGATE_TO_METADATA): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() +  "rationalizer/rationalizer_dashboard_metadata.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                case (ACTION_NAVIGATE_TO_BRAND): {
                    ModelAndView modelAndView = new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() +  "rationalizer/rationalizer_dashboard_brand.form"), getSuccessViewParams(request));
                    modelAndView.addObject("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
                    modelAndView.addObject("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));
                    return modelAndView;
                }
                default:
                    break;
            }

        } finally {
            analyticsEvent.send();
        }

        return null;
    }

    private Map<String, Object> getSuccessViewParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        if (rationalizerApplicationId != -1L) {
            params.put(REQ_PARAM_RATIONALIZER_APP_ID, rationalizerApplicationId);
        }

        params.put("sourceDashboardCompareSelection", request.getParameterMap().get("sourceDashboardCompareSelection"));
        params.put("targetDashboardCompareSelection", request.getParameterMap().get("targetDashboardCompareSelection"));

        long actionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ACTION, -1L);
        if (actionId != -1L) {
            params.put(REQ_PARAM_ACTION, actionId);
        }

        String windowType = ServletRequestUtils.getStringParameter(request, REQ_PARAM_WINDOW_TYPE, null);
        if ( windowType != null ) {
            params.put(REQ_PARAM_WINDOW_TYPE, windowType);
        }

        return params;
    }
}