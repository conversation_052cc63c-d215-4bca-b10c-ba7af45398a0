package com.prinova.messagepoint.controller.rationalizer;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import org.apache.commons.lang3.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.security.StringXSSEditor;

public class RationalizerQueryEditDetailController extends MessagepointController {

    private static final Log log = LogUtil.getLog(RationalizerQueryEditDetailController.class);

    public static final String REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID = "rationalizerComparisonContentId";
    public static final String REQ_PARAM_RATIONALIZER_QUERY_ID = "rationalizerQueryId";
    public static final String REQ_PARAM_RATIONALIZER_DETAIL_TYPE = "listAccessType";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        long queryId = ServletRequestUtils.getLongParameter(request, RationalizerQueryEditDetailController.REQ_PARAM_RATIONALIZER_QUERY_ID, -1L);
        String detailType = ServletRequestUtils.getStringParameter(request, RationalizerQueryEditDetailController.REQ_PARAM_RATIONALIZER_DETAIL_TYPE, "default");

        final RationalizerContent rationalizerContent = getRationalizerContentContext(request);
        referenceData.put("rationalizerContent", rationalizerContent);
        referenceData.put("displayedMarkupContent", rationalizerContent.buildDisplayedMarkupContent());

        if (rationalizerContent instanceof RationalizerDocumentContent) {
            RationalizerDocumentContent rationalizerDocumentContent = (RationalizerDocumentContent) rationalizerContent;
            referenceData.put("rationalizerDocument", rationalizerDocumentContent.getRationalizerDocument());
        }

        RationalizerQuery query = queryId != -1L ? RationalizerQuery.findById(queryId) : null;

        referenceData.put("query", query);
        referenceData.put("queryAppliesSimilarity", (query != null && query.getAppliesSimilarity()) || detailType.equalsIgnoreCase("popup"));

        referenceData.put("detailType", detailType);
        referenceData.put("rangeStart",  ServletRequestUtils.getIntParameter(request, "rangeStart", -1));
        referenceData.put("rangeEnd", ServletRequestUtils.getIntParameter(request, "rangeEnd", -1));

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    protected RationalizerQueryEditDetailWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerQueryEditDetailWrapper();
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.QueryEditDetail);
        try {

            return null;
        } finally {
            analyticsEvent.send();
        }
    }

    private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        String comparisonContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID, "");
        long rationalizerQueryId = ServletRequestUtils.getLongParameter(request, RationalizerQueryEditController.REQ_PARAM_RATIONALIZER_QUERY_ID, -1L);
        String listAccessType = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_DETAIL_TYPE, "default");
        String listSubType = ServletRequestUtils.getStringParameter(request, "listSubType", null);

        if (StringUtils.isNotEmpty(comparisonContentGuid)) {
            params.put(REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID, comparisonContentGuid);
        }

        params.put(REQ_PARAM_RATIONALIZER_QUERY_ID, rationalizerQueryId);
        params.put(REQ_PARAM_RATIONALIZER_DETAIL_TYPE, listAccessType);

        if (listSubType != null) {
            params.put("listSubType", listSubType);
        }

        return params;
    }

    private RationalizerContent getRationalizerContentContext(HttpServletRequest request) {
        String comparisonContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID, "");

        RationalizerContent rationalizerContent = null;
        if (StringUtils.isNotEmpty(comparisonContentGuid)) {
            rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(comparisonContentGuid);
        }

        return rationalizerContent;
    }
}
