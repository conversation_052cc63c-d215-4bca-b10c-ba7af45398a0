package com.prinova.messagepoint.controller.rationalizer;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class RationalizerContentStatusFilterType extends StaticType {
	private static final long serialVersionUID = 5187567905572166960L;
	
	public static final int ID_ACTIVE				= 1;
	public static final int ID_NEW					= 2;
	public static final int ID_EDITED				= 3;
	public static final int ID_UNEDITED			= 4;

	public static final String MESSAGE_ACTIVE 		= "page.label.list.filter.type.active";	
	public static final String MESSAGE_NEW				= "page.label.list.filter.type.new";
	public static final String MESSAGE_EDITED			= "page.label.list.filter.type.edited";
	public static final String MESSAGE_UNEDITED	= "page.label.list.filter.type.unedited";

	public RationalizerContentStatusFilterType(Integer id) {
		super();
		switch (id) {
		case ID_ACTIVE:
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_ACTIVE);
			break;
		case ID_NEW:
			this.setId(ID_NEW);
			this.setName(ApplicationUtil.getMessage(MESSAGE_NEW));
			this.setDisplayMessageCode(MESSAGE_NEW);
			break;
		case ID_EDITED:
			this.setId(ID_EDITED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_EDITED));
			this.setDisplayMessageCode(MESSAGE_EDITED);
			break;
		case ID_UNEDITED:
			this.setId(ID_UNEDITED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_UNEDITED));
			this.setDisplayMessageCode(MESSAGE_UNEDITED);
			break;
		default:
			break;
		}
	}

	public RationalizerContentStatusFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_ACTIVE))) { 
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_ACTIVE);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_NEW))) { 
			this.setId(ID_NEW);
			this.setName(ApplicationUtil.getMessage(MESSAGE_NEW));
			this.setDisplayMessageCode(MESSAGE_NEW);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_EDITED))) { 
			this.setId(ID_EDITED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_EDITED));
			this.setDisplayMessageCode(MESSAGE_EDITED);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_UNEDITED))) { 
			this.setId(ID_UNEDITED);
			this.setName(ApplicationUtil.getMessage(MESSAGE_UNEDITED));
			this.setDisplayMessageCode(MESSAGE_UNEDITED);
		} 
	}
	
	public static List<RationalizerContentStatusFilterType> listAll() {
		List<RationalizerContentStatusFilterType> allListFilterTypes = new ArrayList<>();

		RationalizerContentStatusFilterType listFilterType = null;
		
		listFilterType = new RationalizerContentStatusFilterType(ID_NEW);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new RationalizerContentStatusFilterType(ID_EDITED);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new RationalizerContentStatusFilterType(ID_UNEDITED);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}