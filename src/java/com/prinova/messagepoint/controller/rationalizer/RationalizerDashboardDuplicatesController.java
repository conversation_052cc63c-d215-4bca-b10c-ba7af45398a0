package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.export.ExportUtil;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.web.bind.ServletRequestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public class RationalizerDashboardDuplicatesController extends AbstractBaseRationalizerDashboardController {
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";

    protected Object formBackingObject(HttpServletRequest request) {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);

        RationalizerDashboardWrapper wrapper = new RationalizerDashboardWrapper();

        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setCurrentApplication(rationalizerApp);
            wrapper.setCloneName(rationalizerApp.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
        }

        return wrapper;
    }

    @Override
    public void addDashboardReportReference(long applicationId, Map<String, Object> referenceData) {
        StatusPollingBackgroundTask task = StatusPollingBackgroundTask.findLastestByNameAndDescriptionAndByUser(UserUtil.getPrincipalUser(),
                ExportUtil.ExportType.RATIONALIZER_DASHBOARD_DUP.name() + "_" + applicationId, StatusPollingBackgroundTask.SUB_TYPE_RATIONLIZER_DASHBOARD_EXPORT);
        referenceData.put("dashboardReport", task);
        if (task != null) {
            referenceData.put("reportType", FileUtil.getFileExtension(task.getOutputFilename()));
        }
    }
}