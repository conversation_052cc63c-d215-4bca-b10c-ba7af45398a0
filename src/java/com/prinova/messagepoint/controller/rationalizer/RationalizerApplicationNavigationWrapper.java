package com.prinova.messagepoint.controller.rationalizer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;

public class RationalizerApplicationNavigationWrapper implements Serializable{

	private static final long serialVersionUID = -8998943035902822196L;
	private RationalizerApplication		application;
	private List<MetadataFormItemDefinition> itemDefinitions = new ArrayList<>();

	public RationalizerApplicationNavigationWrapper(){
		super();
	}
	
	public RationalizerApplicationNavigationWrapper(RationalizerApplication application){
		super();
		
		this.application = application;
		if (application != null) {
			this.itemDefinitions = application.getTreeStructureFormItemDefinitions();
		}
	}
	
	public List<Long> getAllAvailUserIds(){
		List<Long> allAvailUserIds = new ArrayList<>();
		for (User user: User.findAllActiveUsersSorted()){
			allAvailUserIds.add(user.getId());
		}
		return allAvailUserIds;
	}

	public RationalizerApplication getRationalizerApplication() {
		return application;
	}

	public void setApplication(RationalizerApplication application) {
		this.application = application;
	}

	public List<MetadataFormItemDefinition> getItemDefinitions() {
		return itemDefinitions;
	}

	public void setItemDefinitions(List<MetadataFormItemDefinition> itemDefinitions) {
		this.itemDefinitions = itemDefinitions;
	}	
}
