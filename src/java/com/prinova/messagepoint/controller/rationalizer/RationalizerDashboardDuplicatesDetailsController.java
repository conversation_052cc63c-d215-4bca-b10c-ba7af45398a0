package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class RationalizerDashboardDuplicatesDetailsController extends MessagepointController {

    public static final String REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID = "rationalizerComparisonContentId";

    private static final Log log = LogUtil.getLog(RationalizerDashboardDuplicatesDetailsController.class);

    protected RationalizerDashboardDetailsWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerDashboardDetailsWrapper();
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        String comparisonContentGuid = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_COMPARISION_CONTENT_ID, "");
        if (StringUtils.isNotEmpty(comparisonContentGuid)) {
            RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(comparisonContentGuid);
            referenceData.put("rationalizerContent", rationalizerContent);
        }

        // Task metadata form definition
        referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
        referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

        return referenceData;
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.Dashboard);
        try {

            return null;
        } finally {
            analyticsEvent.send();
        }
    }

}
