package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

public class RationalizerDCFieldsValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		RationalizerDCFieldsWrapper wrapper = (RationalizerDCFieldsWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		// ADD CONTENT
		if ( action == RationalizerDCFieldsController.ACTION_UPDATE_FIELDS ) {
			
			if ( wrapper.getOrder() == null || wrapper.getOrder() <= 0 )
				errors.reject("error.order.mandatory.and.has.to.be.greater.than.0",
						new String[] {},
						""
				);
			
		}
		


	}
}
