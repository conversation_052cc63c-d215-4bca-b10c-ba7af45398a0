package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.licence.webapp.utils.Utils;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplicationTpReference;
import com.prinova.messagepoint.platform.services.rationalizer.dto.*;
import com.prinova.messagepoint.platform.services.rationalizer.dto.validators.ValidationException;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public class RationalizerExportToMessagepointWrapper implements Serializable {

    private static final long serialVersionUID = -288914342836914507L;

    public static int DISPLAY_MAX_NO_OF_REPORTED_ITEMS = 100;
    private static  int TP_OLD_DAYS = 5;

    private Set<ZoneConfigDto> zonesSet;

    private List<String> metadataWithNoValues;

    private boolean combineWithinDocument;

    private RationalizerApplication rationalizerApplication;

    private TouchpointMatchingStatsDto touchpointMatchingStats;

    private List<TextStyleConfigDto> touchpointTextStyles;

    private List<TouchpointMessageCollisionDto> touchpointMessageCollisionsList;

    private List<TouchpointEmbeddedContentCollisionDto> touchpointEmbeddedContentCollisionsList;

    private boolean touchpointVariationEnabled;

    private boolean touchpointFileValidDefinition;

    private boolean validFontFamily;

    private boolean updateTouchpointOnExport = true;

    private List<TouchpointLanguageDto> languageList;

    private ValidationException validationException;

    private Map<Integer, ContentStyleConfigDto> rationalizerContentStylesMap;

    private Map<String, Long> fontNameAndFontFileidMap;

    private int touchpointConnectorType;

    private boolean migrateTextStyle = true;

    private String incorrectFontFamily ;
    private String uploadedFileName;
    private String touchpointGuid;
    private Date requestedDate;

    public boolean isCombineWithinDocument() {
        return combineWithinDocument;
    }

    public void setCombineWithinDocument(boolean combineWithinDocument) {
        this.combineWithinDocument = combineWithinDocument;
    }

    public boolean isTouchpointVariationEnabled() {
        return touchpointVariationEnabled;
    }

    public void setTouchpointVariationEnabled(boolean touchpointVariationEnabled) {
        this.touchpointVariationEnabled = touchpointVariationEnabled;
    }

    public boolean isTouchpointFileValidDefinition() {
        return touchpointFileValidDefinition;
    }

    public void setTouchpointFileValidDefinition(boolean touchpointFileValidDefinition) {
        this.touchpointFileValidDefinition = touchpointFileValidDefinition;
    }

    public boolean isValidFontFamily() {
        return validFontFamily;
    }

    public void setValidFontFamily(boolean validFontFamily) {
        this.validFontFamily = validFontFamily;
    }

    public Set<ZoneConfigDto> getZonesSet() {
        return zonesSet;
    }

    public void setZonesSet(Set<ZoneConfigDto> zonesSet) {
        this.zonesSet = zonesSet;
    }

    public RationalizerApplication getRationalizerApplication() {
        return rationalizerApplication;
    }

    public void setRationalizerApplication(RationalizerApplication rationalizerApplication) {
        this.rationalizerApplication = rationalizerApplication;
    }

    public List<String> getValidationErrorMessageCode() {
        List<String> validationErrorMessageCode = new ArrayList<>();
        if(!touchpointFileValidDefinition) {
            validationErrorMessageCode.add(ApplicationUtil.getMessage("page.label.rationalizer.export.input.file.invalid",
                    (validationException != null) ? (new String[] {validationException.getMessageAsHtml()}) : new String[] {""}));

        }
        if(!touchpointVariationEnabled) {
            validationErrorMessageCode.add(ApplicationUtil.getMessage("page.label.rationalizer.export.variation.disabled",
                    new String[] {}));
        }
        if(!isValidFontFamily() ) {
            validationErrorMessageCode.add(ApplicationUtil.getMessage("page.label.rationalizer.export.incorrect.font", new String[]{incorrectFontFamily}));
        }

        return validationErrorMessageCode;
    }

    public boolean getIsValidTouchpoint(){
       return isValidFontFamily() && isTouchpointVariationEnabled() && isTouchpointFileValidDefinition();
    }

    public ExportToMessagepointReportDto getUnmatchedMessagepointVariants() {
        return touchpointMatchingStats.getUnmatchedMessagepointVariants(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getUnmatchedRationalizerVariants() {
        return touchpointMatchingStats.getUnmatchedRationalizerVariants(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getMatchedVariants() {
        return touchpointMatchingStats.getMatchedVariants(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getUnmatchedMessagepointMessages() {
        return touchpointMatchingStats.getUnmatchedMessagepointMessages(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getMatchedMessagesNoCollision() {
        return touchpointMatchingStats.getMatchedMessagesNoCollision(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public String getMatchedMessagesWithCollisionString() {
        return MessageFormat.format(ApplicationUtil.getMessage("page.label.touchpoint.export.matched.messages.with.collision"), touchpointMatchingStats.getMatchedMessagesWithCollisionCount());
    }

    public ExportToMessagepointReportDto getUnmatchedMessagepointEmbeddedContents() {
        return touchpointMatchingStats.getUnmatchedMessagepointEmbeddedContents(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getUnmatchedRationalizerEmbeddedContents() {
        return touchpointMatchingStats.getUnmatchedRationalizerEmbeddedContents(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getMatchedEmbeddedContentsNoCollision() {
        return touchpointMatchingStats.getMatchedEmbeddedContentsNoCollision(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public String getMatchedEmbeddedContentsWithCollisionString() {
        return MessageFormat.format(ApplicationUtil.getMessage("page.label.touchpoint.export.matched.embedded.contents.with.collision"), touchpointMatchingStats.getMatchedEmbeddedContentsWithCollisionCount());
    }

    public ExportToMessagepointReportDto getUnmatchedMessagepointVariables() {
        return touchpointMatchingStats.getUnmatchedMessagepointVariables(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getUnmatchedRationalizerVariables() {
        return touchpointMatchingStats.getRationalizerUnmatchedVariablesForReport(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public ExportToMessagepointReportDto getMatchedVariables() {
        return touchpointMatchingStats.getMatchedVariablesForReport(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public boolean getIsRationalizerDataSourceReferencePresent() {
        return touchpointMatchingStats.getRationalizerReferenceDataSourceExists();
    }

    public void setTouchpointMatchingStatsAndPopulateCollisions(TouchpointMatchingStatsDto touchpointMatchingStats) {
        this.touchpointMatchingStats = touchpointMatchingStats;
        populateTouchpointMessagesCollisionList();
        populateTouchpointEmbeddedContentCollisionsList();
    }

    public List<TouchpointMessageCollisionDto> getTouchpointMessageCollisionsList() {
        return touchpointMessageCollisionsList;
    }

    public ExportToMessagepointReportDto getUnmatchedRationalizerMessages() {
        return touchpointMatchingStats.getUnmatchedRationalizerMessages(DISPLAY_MAX_NO_OF_REPORTED_ITEMS);
    }

    public List<TouchpointEmbeddedContentCollisionDto> getTouchpointEmbeddedContentCollisionsList() {
        return touchpointEmbeddedContentCollisionsList;
    }

    public TouchpointMatchingStatsDto getTouchpointMatchingStats() {
        return touchpointMatchingStats;
    }

    public ValidationException getValidationException() {
        return validationException;
    }

    public void setValidationException(ValidationException validationException) {
        this.validationException = validationException;
    }

    private void populateTouchpointMessagesCollisionList() {
        touchpointMessageCollisionsList = new LinkedList<>();
        if (CollectionUtils.isEmpty(touchpointMatchingStats.getTouchpointMessagesCollisionsList())) {
            return;
        }

        for (MessageCollisionDto crtCollision : touchpointMatchingStats.getTouchpointMessagesCollisionsList()) {
            touchpointMessageCollisionsList.add(new TouchpointMessageCollisionDto(crtCollision));
        }
    }

    private void populateTouchpointEmbeddedContentCollisionsList() {
        touchpointEmbeddedContentCollisionsList = new LinkedList<>();
        if (CollectionUtils.isEmpty(touchpointMatchingStats.getTouchpointEmbeddedContentCollisionsList())) {
            return;
        }

        for (EmbeddedContentCollisionDto crtCollision : touchpointMatchingStats.getTouchpointEmbeddedContentCollisionsList()) {
            touchpointEmbeddedContentCollisionsList.add(new TouchpointEmbeddedContentCollisionDto(crtCollision));
        }
    }

    public boolean isUpdateTouchpointOnExport() {
        return updateTouchpointOnExport;
    }

    public void setUpdateTouchpointOnExport(boolean updateTouchpointOnExport) {
        this.updateTouchpointOnExport = updateTouchpointOnExport;
    }

    public List<TouchpointLanguageDto> getLanguageList() {
        return languageList;
    }

    public TouchpointLanguageDto getDefaultLanguage() {
        if(CollectionUtils.isNotEmpty(languageList)) {
            for(TouchpointLanguageDto languageDto: languageList) {
                if(languageDto.isDefaultLanguage()) {
                    return languageDto;
                }
            }
        }

        return null;
    }

    public void setLanguageList(List<TouchpointLanguageDto> languageList) {
        this.languageList = languageList;
    }

    public String computeSelectedPath(String param) {
        StringBuilder path = new StringBuilder(" ");

        if(!StringUtil.isEmptyOrNull(param)) {
            List<Long> selectedBranchIds = Stream.of(param.split(","))
                    .map(String::trim)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            if(selectedBranchIds.contains(-9L)) {
                selectedBranchIds.remove(-9L);
            }
            for (Long id : selectedBranchIds) {
                if (id != -1 ) {
                    MetadataFormItem formItem = MetadataFormItem.findById(id);
                    if(formItem != null) {
                        path.append(" > ").append(formItem.getDisplayValue() != null ? formItem.getDisplayValue() : " NO VALUE");
                    }
                } else {
                    path.append(" > NO VALUE");
                }
            }
        }

        return path.toString();
    }

    public Map<Integer, ContentStyleConfigDto> getRationalizerContentStylesMap() {
        return rationalizerContentStylesMap;
    }

    public void setRationalizerContentStylesMap(Map<Integer, ContentStyleConfigDto> rationalizerContentStylesMap) {
        this.rationalizerContentStylesMap = rationalizerContentStylesMap;
    }

    public Map<String, Long> getFontNameAndFontFileidMap() {
        return fontNameAndFontFileidMap;
    }

    public void setFontNameAndFontFileidMap(Map<String, Long> fontNameAndFontFileidMap) {
        this.fontNameAndFontFileidMap = fontNameAndFontFileidMap;
    }

    public boolean isMigrateTextStyle() {
        return migrateTextStyle;
    }

    public void setMigrateTextStyle(boolean migrateTextStyle) {
        this.migrateTextStyle = migrateTextStyle;
    }

    public String  getIncorrectFontFamily() {
        return incorrectFontFamily;
    }

    public void setIncorrectFontFamily(String incorrectFontFamily) {
        this.incorrectFontFamily = incorrectFontFamily;
    }

    public String getMetadataWithNoValues() {
       if (!metadataWithNoValues.isEmpty()) {
           return metadataWithNoValues.stream().collect(Collectors.joining("; "));
        }
        return "";
    }

    public void setMetadataWithNoValues(List<String> metadataWithNoValues) {
        this.metadataWithNoValues = metadataWithNoValues;
    }

    public boolean getHasMetadataWithNoValues() {
        return !this.metadataWithNoValues.isEmpty();
    }

    public List<TextStyleConfigDto> getTouchpointTextStyles() {
        return touchpointTextStyles;
    }

    public void setTouchpointTextStyles(List<TextStyleConfigDto> touchpointTextStyles) {
        this.touchpointTextStyles = touchpointTextStyles;
    }

    public List<RationalizerApplicationTpReference> getTpReference(){
        return RationalizerApplicationTpReference.findTpReferenceByDna(touchpointGuid);
    }

    public String  getTouchpointGuid() {
        return touchpointGuid;
    }

    public boolean getIsAlreadyUsedTp(){
        return (long) getTpReference().size() > 0;
    }

    public boolean getIsOlderTp(){
        long daysBetween =  Utils.getDaysBetween(requestedDate, new Date());
        if(daysBetween>TP_OLD_DAYS) return true;
        return false;
    }

    public void setTouchpointGuid(String touchpointGuid) {
        this.touchpointGuid = touchpointGuid;
    }

    public String  getUploadedFileName() {
        return uploadedFileName;
    }

    public void setUploadedFileName(String uploadedFileName) {
        this.uploadedFileName = uploadedFileName;
    }

    public Date  getRequestDate() {
        return requestedDate;
    }

    public void setRequestedDate(Date requestedDate) {
        this.requestedDate = requestedDate;
    }

    public int  getTouchpointConnectorType() {
        return touchpointConnectorType;
    }

    public void setTouchpointConnectorType(int touchpointConnectorType) {
        this.touchpointConnectorType = touchpointConnectorType;
    }

}
