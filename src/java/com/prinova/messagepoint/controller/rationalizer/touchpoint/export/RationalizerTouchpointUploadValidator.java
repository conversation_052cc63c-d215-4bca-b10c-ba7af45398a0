package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.text.MessageFormat;

public class RationalizerTouchpointUploadValidator extends MessagepointInputValidator {

    private static final String CONTENT_TYPE = "text/xml";
    private static final String CONTENT_TYPE_ambiguous = "application/octet-stream";

    public void validateNotGenericInputs(Object command, Errors errors) {
        RationalizerTouchpointUploadWrapper rationalizerTouchpointUploadWrapper = (RationalizerTouchpointUploadWrapper) command;
        if (rationalizerTouchpointUploadWrapper.getImportFilename() == null) {
            errors.rejectValue("importFilename", "unknown", ApplicationUtil.getMessage("error.select.file.to.import"));
            return;
        }
        if (rationalizerTouchpointUploadWrapper.getImportFilename().isEmpty()) {
            if (rationalizerTouchpointUploadWrapper.getImportFilename().getOriginalFilename().equalsIgnoreCase("")) {
                errors.rejectValue("importFilename", "unknown", ApplicationUtil.getMessage("error.select.file.to.import"));
            } else {
                errors.rejectValue("importFilename", "unknown", MessageFormat.format(ApplicationUtil.getMessage("error.selected.import.file.empty"), new Object[]{rationalizerTouchpointUploadWrapper.getImportFilename().getOriginalFilename()}));
            }
            return;
        }
        if (!CONTENT_TYPE.equals(rationalizerTouchpointUploadWrapper.getImportFilename().getContentType()) && !CONTENT_TYPE_ambiguous.equals(rationalizerTouchpointUploadWrapper.getImportFilename().getContentType())) {
            errors.rejectValue("importFilename", "unknown", MessageFormat.format(ApplicationUtil.getMessage("error.selected.import.file.not.xml"), new Object[]{rationalizerTouchpointUploadWrapper.getImportFilename().getOriginalFilename()}));
        }
    }
}
