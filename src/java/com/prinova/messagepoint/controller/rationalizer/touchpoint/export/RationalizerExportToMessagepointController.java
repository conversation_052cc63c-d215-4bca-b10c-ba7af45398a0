package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.backgroundtask.ExportRationalizerToMessagepointBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerTouchpointExportService;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerTouchpointExportTreeCompareService;
import com.prinova.messagepoint.platform.services.rationalizer.dto.*;
import com.prinova.messagepoint.platform.services.rationalizer.dto.validators.ValidationException;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.RedisUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class RationalizerExportToMessagepointController extends MessagepointController {
    public static final String REQ_PARAM_UPLOADED_FILE_NAME = "uploadedFileName";
    public static final String REQ_PARAM_VIEW_TYPE = "viewType";
    public static final String ZONE_CONFIG_KEY_PREFIX = "zonesConfig-";
    public static final String RATIONALIZER_TREE_KEY_PREFIX = "rationalizerTree-";
    private static final String REQ_PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    private static final String TOUCHPOINT_GUID = "touchpointGuid";
    public static final String REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID = "rationalizerDocFormItemId";
    private static final Log log = LogUtil.getLog(RationalizerExportToMessagepointController.class);

    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        int viewType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_VIEW_TYPE, -1);
        String uploadedFileName = ServletRequestUtils.getStringParameter(request, REQ_PARAM_UPLOADED_FILE_NAME, "");
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        RationalizerExportToMessagepointWrapper wrapper = new RationalizerExportToMessagepointWrapper();
        if (rationalizerApplicationId > 0) {
            RationalizerApplication rationalizerApp = RationalizerApplication.findById(rationalizerApplicationId);
            wrapper.setRationalizerApplication(rationalizerApp);
        }

        String tmpFilerootPath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir);
        File file = new File(tmpFilerootPath + uploadedFileName + ".xml");
        if (viewType == 1) {
            RationalizerTouchpointXmlInputValidator validator = new RationalizerTouchpointXmlInputValidator(file);
            validator.processTouchpointForValidations();
            wrapper.setUploadedFileName(uploadedFileName + ".xml");
            if (validator.IsValidTouchpointFile()) {
                RationalizerTouchpointXmInputProcessor processor = new RationalizerTouchpointXmInputProcessor();
                try (Reader reader = new InputStreamReader(new BufferedInputStream(new FileInputStream(file)))) {
                    processor.extractTouchpointElements(reader, wrapper);
                } catch (ValidationException exception) {
                    log.error(exception.getMessageAsHtml(), exception);
                    wrapper.setValidationException(exception);
                }
            }
            setWrapperDataForValidation(validator, wrapper);
        } else {
            String key = ZONE_CONFIG_KEY_PREFIX + UserUtil.getPrincipalUser().getId();
            TouchpointExportSelectionTree messagepointSelectionTree;
            try (Reader reader = new InputStreamReader(new BufferedInputStream(new FileInputStream(file)))) {
                RationalizerTouchpointXmInputProcessor processor = new RationalizerTouchpointXmInputProcessor();
                messagepointSelectionTree = processor.parseSelectionTree(reader);
                wrapper.setLanguageList(messagepointSelectionTree.getLanguageList());
            }
            TouchpointZonesConfigDto touchpointZonesConfigDto = RedisUtil.getRedisContext().getValue(key, TouchpointZonesConfigDto.class);
            touchpointZonesConfigDto.populateMessagesToZoneDeliveryMapForMessagepointRecursive(messagepointSelectionTree.getTouchpointExportSelectionNode());
            RationalizerTouchpointExportService rationalizerTouchpointExportService = (RationalizerTouchpointExportService) ApplicationUtil.getBean("rationalizerTouchpointExportService");
            // consider user selection for the tree
            String rationalizerDocFormItemId = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, "-9");
            // retrieve default language
            if(CollectionUtils.isNotEmpty(wrapper.getLanguageList())) {
                wrapper.getLanguageList().forEach(languageDto -> {
                    if (languageDto.isDefaultLanguage()) {
                        rationalizerTouchpointExportService.setDefaultLanguage(languageDto);
                    }
                });
            }

            key = RATIONALIZER_TREE_KEY_PREFIX + UserUtil.getPrincipalUser().getId();
            TouchpointExportSelectionTree rationalizerTree = RedisUtil.getRedisContext().getValue(key, TouchpointExportSelectionTree.class);
            if(rationalizerTree == null) {
                rationalizerTree = rationalizerTouchpointExportService.createRationalizerExportTree(rationalizerApplication, touchpointZonesConfigDto, rationalizerDocFormItemId, messagepointSelectionTree);
                RedisUtil.getRedisContext().putValue(key, rationalizerTree);
            }
            wrapper.setMetadataWithNoValues(rationalizerTree.getMetadataWithNoValues());
            wrapper.setTouchpointTextStyles(messagepointSelectionTree.getTextStyleConfigDto());
            // store Rationalizer unique content styles
            wrapper.setRationalizerContentStylesMap(rationalizerTree.getContentStylesMap());
            // store Messagepoint font name-font file id map
            wrapper.setFontNameAndFontFileidMap(messagepointSelectionTree.getFontNameAndFontFileidMap());
            wrapper.setTouchpointConnectorType(messagepointSelectionTree.geConnectorType());

            RationalizerTouchpointExportTreeCompareService rationalizerTouchpointExportTreeCompareService = (RationalizerTouchpointExportTreeCompareService) ApplicationUtil.getBean("rationalizerTouchpointExportTreeCompareService");
            TouchpointMatchingStatsDto touchpointMatchingStatsDto = rationalizerTouchpointExportTreeCompareService.compare(messagepointSelectionTree, rationalizerTree, touchpointZonesConfigDto, wrapper.isUpdateTouchpointOnExport());
            wrapper.setTouchpointMatchingStatsAndPopulateCollisions(touchpointMatchingStatsDto);

        }

        return wrapper;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
                                    BindException errors) throws Exception {
        int viewType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_VIEW_TYPE, -1);
        String uploadedFileName = ServletRequestUtils.getStringParameter(request, REQ_PARAM_UPLOADED_FILE_NAME, "");
        String rationalizerDocFormItemId = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, "-9");
        RationalizerExportToMessagepointWrapper wrapper = (RationalizerExportToMessagepointWrapper) command;

        if (viewType == 1) {
            String key = RATIONALIZER_TREE_KEY_PREFIX + UserUtil.getPrincipalUser().getId();
            RedisUtil.getRedisContext().delete(key);
            Map<String, Object> params = new HashMap<>();
            params.put(REQ_PARAM_RATIONALIZER_APP_ID, wrapper.getRationalizerApplication().getId());
            if(wrapper.getIsValidTouchpoint()) {
                params.put(REQ_PARAM_VIEW_TYPE, 2);
                params.put(REQ_PARAM_UPLOADED_FILE_NAME, uploadedFileName);
                params.put(REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, rationalizerDocFormItemId);
                params.put(TOUCHPOINT_GUID, wrapper.getTouchpointGuid());
                key = ZONE_CONFIG_KEY_PREFIX + UserUtil.getPrincipalUser().getId();
                RedisUtil.getRedisContext().putValue(key, new TouchpointZonesConfigDto(wrapper.getZonesSet(), wrapper.isCombineWithinDocument()));
                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_export_to_messagepoint.form"), params);
            }
        } else if (viewType == 2) {
            wrapper.setTouchpointGuid(ServletRequestUtils.getStringParameter(request, TOUCHPOINT_GUID, null));
            generateReport(request, wrapper, uploadedFileName, wrapper.isMigrateTextStyle());
            Map<String, Object> params = new HashMap<>();
            params.put(REQ_PARAM_RATIONALIZER_APP_ID, wrapper.getRationalizerApplication().getId());
            params.put(REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, rationalizerDocFormItemId);
            return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_documents_list.form"), params);
        }

        return null;
    }

    private ModelAndView generateReport(HttpServletRequest request, RationalizerExportToMessagepointWrapper wrapper, String uploadedFileName, boolean migrateTextStyles) {
        String uploadedFilePath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir) + uploadedFileName + ".xml";
        HashMap<String, Object> exportParameters = new HashMap<>();
        exportParameters.put("updateTouchpointOnExport", wrapper.isUpdateTouchpointOnExport());
        TouchpointExportUserPreferencesDto userPreferencesDto = new TouchpointExportUserPreferencesDto(wrapper, uploadedFilePath, exportParameters, migrateTextStyles);
        userPreferencesDto.setLanguageList(wrapper.getLanguageList());
        userPreferencesDto.setTouchpointConnectorType(wrapper.getTouchpointConnectorType());
        if(migrateTextStyles) {
            userPreferencesDto.setFontNameAndFontFileidMap(wrapper.getFontNameAndFontFileidMap());
        }
        ExportRationalizerToMessagepointBackgroundTask task = new ExportRationalizerToMessagepointBackgroundTask(wrapper.getRationalizerApplication().getId(), UserUtil.getPrincipalUser(), userPreferencesDto);
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

        return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request));
    }

    private Map<String, Object> getSuccessViewParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APP_ID, -1L);
        if (rationalizerApplicationId != -1L) {
            params.put(REQ_PARAM_RATIONALIZER_APP_ID, rationalizerApplicationId);
        }

        return params;
    }

    private void setWrapperDataForValidation(RationalizerTouchpointXmlInputValidator validator, RationalizerExportToMessagepointWrapper wrapper){
        wrapper.setIncorrectFontFamily(validator.getInvalidFontFamily());
        wrapper.setValidFontFamily(validator.IsValidFontFamily());
        wrapper.setTouchpointFileValidDefinition(validator.IsValidFile());
        wrapper.setTouchpointVariationEnabled(validator.IsVariationEnabled());
    }
}
