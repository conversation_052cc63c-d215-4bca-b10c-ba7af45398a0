package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.platform.services.rationalizer.dto.*;
import com.prinova.messagepoint.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Instant;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.jsoup.parser.Parser;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.Reader;
import java.util.*;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.platform.services.export.RationalizerTextHashUtil.generateUniqueLongId;

public class RationalizerTouchpointXmInputProcessor {

    private static final String ZONE_FRIENDLY_NAME_ATR = "friendlyname";
    private static final String ZONE_NAME_ATR = "name";
    private static final String TEXT_STYLES = "TextStyles";
    private static final String STYLE = "Style";
    private static final String TEXT_STYLE_FONT = "TextStyleFont";
    private static final String TOGGLE_POINT_SIZE_VALUES = "TogglePointSizeValues";
    private static final String TOGGLE_POINT_SIZE_VALUE = "TogglePointSizeValue";
    private static final String TOGGLE_COLOR_VALUES = "ToggleColorValues";
    private static final String TOGGLE_COLOR_VALUE = "ToggleColorValue";
    private static final String FONT = "Font";
    private static final String REQUEST_DATE = "RequestDate";
    private static final String TP_GUID = "GUID";
    private static final String TOUCHPOINT = "Touchpoint";
    private static final String CONNECTOR_TYPE_ATR = "connectortype";


    public RationalizerTouchpointXmInputProcessor() {
    }

    public TouchpointExportSelectionTree parseSelectionTree(Reader r) throws Exception {
        XMLStreamReader reader = XMLInputFactory.newInstance().createXMLStreamReader(r);
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (elementName.equals("MpTouchpointDefinition")) {
                    return this.readTouchpointExportConfigTree(reader);
                }
            }
        }
        reader.close();

        return null;
    }

    private TouchpointExportSelectionTree readTouchpointExportConfigTree(XMLStreamReader reader) throws Exception {
        TouchpointExportSelectionTree touchpointExportSelectionTree = new TouchpointExportSelectionTree();
        boolean setTextStyle = false;
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (elementName.equals("Languages")) {
                    touchpointExportSelectionTree.setLanguageList(parseLanguageList(reader));
                }
                if (elementName.equals(TOUCHPOINT)) {
                    int touchpointConnectorType = Integer.parseInt(readAttributeValue(reader, CONNECTOR_TYPE_ATR));
                    touchpointExportSelectionTree.setConnectorType(touchpointConnectorType);
                }
                if (elementName.equals("Selections")) {
                    touchpointExportSelectionTree.setTouchpointExportSelectionNode(readSelectionNode(reader, touchpointExportSelectionTree));
                }
                if (elementName.equals("Messages")) {
                    updateMessagesWithAllInformations(reader, touchpointExportSelectionTree);
                }
                if (elementName.equals("EmbeddedContents")) {
                    readEmbeddedContents(reader, touchpointExportSelectionTree);
                    resolveVarReferencesForEmbeddedContents(touchpointExportSelectionTree);
                }
                if (elementName.equals("UserVariables")) {
                    touchpointExportSelectionTree.setUserVariableDtoList(readUserVariables(reader));
                }
                if (elementName.equals("DataSources")) {
                    touchpointExportSelectionTree.setDataSourceDtoList(readDataSources(reader));
                }
                if (elementName.equals("ContentLibrary")) {
                    GenericXmlElementDto specificElement = new GenericXmlElementDto();
                    specificElement.setTagName(elementName);
                    readChildren(specificElement, reader, elementName);
                    touchpointExportSelectionTree.addSpecificElement(specificElement);
                }
                if(elementName.equals("Fonts")) {
                    touchpointExportSelectionTree.setFontNameAndFontFileidMap(readFontsMap(reader));
                }
                if(elementName.equals(TEXT_STYLES) && !setTextStyle){
                    touchpointExportSelectionTree.setTextStyleConfigDto(readTextStyleConfig(reader));
                    setTextStyle = true;
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && reader.getLocalName().equals("MpTouchpointDefinition")) {
                return touchpointExportSelectionTree;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private Map<String, Long> readFontsMap(XMLStreamReader reader) throws XMLStreamException {
        Map<String, Long> fontNameAndFontFileidMap = new HashMap<>();

        while (reader.hasNext()) {
            int eventType = reader.next();

            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Font".equals(elementName)) {
                    readFont(reader, fontNameAndFontFileidMap);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Fonts".equals(reader.getLocalName())) {
                return fontNameAndFontFileidMap;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<TextStyleConfigDto> readTextStyleConfig(XMLStreamReader reader) throws XMLStreamException {
        List<TextStyleConfigDto> textStyleConfigDtos = new ArrayList<>();
        TextStyleConfigDto textStyleConfigDto = new TextStyleConfigDto();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (STYLE.equals(elementName)) {
                    textStyleConfigDto = new TextStyleConfigDto();
                    textStyleConfigDto.setStyleAttributes(readAttributes(reader));
                } else if (TEXT_STYLE_FONT.equals(elementName)) {
                    textStyleConfigDto.setTextStyleFont(readCharacters(reader));
                } else if (TOGGLE_POINT_SIZE_VALUES.equals(elementName)) {
                    textStyleConfigDto.setTogglePointSizeValue(getFontPointSizeValues(reader));
                } else if (TOGGLE_COLOR_VALUES.equals(elementName)) {
                    textStyleConfigDto.setToggleColorValues(getTextColorValues(reader, textStyleConfigDto));
                } else if (FONT.equals(elementName)) {
                    textStyleConfigDto.setFontAttributes(readAttributes(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (STYLE.equals(elementName)) {
                    textStyleConfigDtos.add(textStyleConfigDto);
                } else if (TEXT_STYLES.equals(elementName)) {
                    return textStyleConfigDtos;
                }
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<Double> getFontPointSizeValues(XMLStreamReader reader) throws XMLStreamException{
        List<Double> fontSizes = new ArrayList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType && TOGGLE_POINT_SIZE_VALUE.equals(reader.getLocalName())) {
                String fontSize = readCharacters(reader);
                if(fontSize != null && !fontSize.isEmpty()) {
                    fontSize = fontSize.replace("pt", "");
                    fontSizes.add(Double.parseDouble(fontSize));
                }
            }
            if (XMLStreamReader.END_ELEMENT == eventType && TOGGLE_POINT_SIZE_VALUES.equals(reader.getLocalName())) {
                return fontSizes;
            }
        }
        throw new XMLStreamException("No TogglePointSizeValues end tag found");
    }

    private Set<String> getTextColorValues(XMLStreamReader reader, TextStyleConfigDto textStyleConfigDto) throws XMLStreamException{
        Set<String> toggleColorValues = new HashSet<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType && TOGGLE_COLOR_VALUE.equals(reader.getLocalName())) {
                String toggleColorValue = readCharacters(reader);
                toggleColorValues.add(toggleColorValue.startsWith("cmyk")? ContentStyleUtils.cleanCMYKValue(toggleColorValue)
                        : ContentStyleUtils.HEXtoCMYK(toggleColorValue)) ;
            }
            if (XMLStreamReader.END_ELEMENT == eventType && TOGGLE_COLOR_VALUES.equals(reader.getLocalName())) {
                return toggleColorValues;
            }
        }
        throw new XMLStreamException("No ToggleColorValues end tag found");
    }

    private void readFont(XMLStreamReader reader, Map<String, Long> fontNameAndFontFileidMap) throws XMLStreamException {
        String fontName = readAttributeValue(reader, "name");

        while (reader.hasNext()) {
            int eventType = reader.next();

            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("FontFile".equals(elementName)) {
                    try {
                        String ttfrefid=readAttributeValue(reader, "ttfrefid");
                        if(ttfrefid!= null && !ttfrefid.isEmpty()) {
                            fontNameAndFontFileidMap.put(fontName, Long.valueOf(ttfrefid));
                        }
                    } catch (Exception ex) {
                        throw  new XMLStreamException("The touchpoind does not have an Only TrueType (TTF), OpenType (OTF), or Embedded OpenType (EOT) fonts file for the font name " + fontName);
                    }
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Font".equals(reader.getLocalName())) {
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");

    }

    private void updateMessagesWithAllInformations(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                if ("Message".equals(reader.getLocalName())) {
                    updateOrAddMessage(reader, touchpointExportSelectionTree);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Messages".equals(reader.getLocalName())) {
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private void readEmbeddedContents(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("EmbeddedContent".equals(elementName)) {
                    if(CollectionUtils.isEmpty(touchpointExportSelectionTree.getEmbeddedContentConfigDtoList())) {
                        touchpointExportSelectionTree.setEmbeddedContentConfigDtoList(new LinkedList<>());
                    }
                    touchpointExportSelectionTree.getEmbeddedContentConfigDtoList().add(readEmbeddedContent(reader, touchpointExportSelectionTree));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "EmbeddedContents".equals(reader.getLocalName())) {
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<UserVariableDto> readUserVariables(XMLStreamReader reader) throws Exception {
        List<UserVariableDto> result = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("UserVariable".equals(elementName)) {
                    result.add(readUserVariable(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "UserVariables".equals(reader.getLocalName())) {
                return result;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }


    private UserVariableDto readUserVariable(XMLStreamReader reader) throws Exception {
        UserVariableDto userVariableDto = new UserVariableDto();
        userVariableDto.setApplication(UserVariableDto.VAR_APP_MESSAGEPOINT);
        String idString = readAttributeValue(reader, "id");
        userVariableDto.setId(StringUtils.isNotEmpty(idString) ? Long.valueOf(idString) : null);
        String dna = readAttributeValue(reader, "dna");
        userVariableDto.setDna(dna);
        userVariableDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Name".equals(elementName)) {
                    userVariableDto.setName(readCharacters(reader));
                }
                if ("FriendlyName".equals(elementName)) {
                    userVariableDto.setFriendlyName(readCharacters(reader));
                }
                if ("DataElement".equals(elementName)) {
                    UserVariableDataElementDto dataElementDto = new UserVariableDataElementDto();
                    dataElementDto.setAttributes(readAttributes(reader));
                    dataElementDto.setValue(readCharacters(reader));
                    userVariableDto.setDataElementDto(dataElementDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "UserVariable".equals(reader.getLocalName())) {
                return userVariableDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<DataSourceDto> readDataSources(XMLStreamReader reader) throws Exception {
        List<DataSourceDto> result = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();

            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("DataSource".equals(elementName)) {
                    result.add(readDataSource(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "DataSources".equals(reader.getLocalName())) {
                return result;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private DataSourceDto readDataSource(XMLStreamReader reader) throws Exception {
        DataSourceDto dataSourceDto = new DataSourceDto();
        String name = readAttributeValue(reader, "name");
        dataSourceDto.setName(name);
        dataSourceDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Encoding".equals(elementName)) {
                    dataSourceDto.setEncoding(readCharacters(reader));
                }
                if ("RecordLayout".equals(elementName)) {
                    dataSourceDto.setRecordLayoutDto(readRecordLayout(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "DataSource".equals(reader.getLocalName())) {
                return dataSourceDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private RecordLayoutDto readRecordLayout(XMLStreamReader reader) throws Exception {
        RecordLayoutDto recordLayoutDto = new RecordLayoutDto();
        recordLayoutDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("IndicatorSegment".equals(elementName)) {
                    recordLayoutDto.setIndicatorSegmentAttributes(readAttributes(reader));
                }
                if ("RecordList".equals(elementName)) {
                    recordLayoutDto.setRecordDtoList(readRecordsList(reader));
                }
                if ("DataTagList".equals(elementName)) {
                    GenericXmlElementDto dataTagListElementDto = new GenericXmlElementDto();
                    dataTagListElementDto.setTagName(elementName);
                    dataTagListElementDto.setAttributes(readAttributes(reader));
                    readChildren(dataTagListElementDto, reader, "DataTagList");
                    recordLayoutDto.setDataTagListDto(dataTagListElementDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "RecordLayout".equals(reader.getLocalName())) {
                return recordLayoutDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<RecordDto> readRecordsList(XMLStreamReader reader) throws XMLStreamException {
        List<RecordDto> result = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Record".equals(elementName)) {
                    result.add(readRecord(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "RecordList".equals(reader.getLocalName())) {
                return result;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private RecordDto readRecord(XMLStreamReader reader) throws XMLStreamException {
        RecordDto recordDto = new RecordDto();
        recordDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Indicator".equals(elementName)) {
                    recordDto.setIndicator(readCharacters(reader));
                }
                if ("PositionInRecordSet".equals(elementName)) {
                    recordDto.setPositionInRecordSet(readCharacters(reader));
                }
                if ("FieldList".equals(elementName)) {
                    recordDto.setFieldDtoList(readFieldsList(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Record".equals(reader.getLocalName())) {
                return recordDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<FieldDto> readFieldsList(XMLStreamReader reader) throws XMLStreamException {
        List<FieldDto> result = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Field".equals(elementName)) {
                    result.add(readField(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "FieldList".equals(reader.getLocalName())) {
                return result;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private FieldDto readField(XMLStreamReader reader) throws XMLStreamException {
        FieldDto fieldDto = new FieldDto();
        fieldDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("FieldName".equals(elementName)) {
                    fieldDto.setName(readCharacters(reader));
                }
                if ("Format".equals(elementName)) {
                    fieldDto.setFormat(readCharacters(reader));
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Field".equals(reader.getLocalName())) {
                return fieldDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private EmbeddedContentConfigDto readEmbeddedContent(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        EmbeddedContentConfigDto embeddedContentConfigDto = new EmbeddedContentConfigDto();
        String idString = readAttributeValue(reader, "id");
        String dna = readAttributeValue(reader, "dna");
        embeddedContentConfigDto.setId(StringUtils.isNotEmpty(idString) ? Long.valueOf(idString) : generateUniqueLongId());
        embeddedContentConfigDto.setDna(dna);
        String type = readAttributeValue(reader, "type");
        embeddedContentConfigDto.setType(type);
        embeddedContentConfigDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Version".equals(elementName)) {
                    VersionConfigDto versionConfigDto = readVersionNode(reader, touchpointExportSelectionTree);
                    versionConfigDto.setParent(embeddedContentConfigDto);
                    embeddedContentConfigDto.setVersionConfigDto(versionConfigDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "EmbeddedContent".equals(reader.getLocalName())) {
                return embeddedContentConfigDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private void updateOrAddMessage(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        String idString = readAttributeValue(reader, "id");
        if (StringUtils.isNotEmpty(idString)) {
            MessageConfigDto messageConfigDto = touchpointExportSelectionTree.findMessageById(Long.valueOf(idString));
            if (messageConfigDto != null) {
                updateMessageFields(reader, messageConfigDto, touchpointExportSelectionTree);
            } else {
                GenericXmlElementDto genericElementDto = new GenericXmlElementDto();
                genericElementDto.setTagName("Message");
                genericElementDto.setAttributes(readAttributes(reader));
                readChildren(genericElementDto, reader, "Message");
                touchpointExportSelectionTree.addSpecificElement(genericElementDto);
            }
        }
    }

    private void updateMessageFields(XMLStreamReader reader, MessageConfigDto messageConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        messageConfigDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Version".equals(elementName)) {
                    VersionConfigDto versionConfigDto = readVersionNode(reader, touchpointExportSelectionTree);
                    versionConfigDto.setParent(messageConfigDto);
                    messageConfigDto.setVersionConfigDto(versionConfigDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Message".equals(reader.getLocalName())) {
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private VersionConfigDto readVersionNode(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        VersionConfigDto versionConfigDto = new VersionConfigDto();
        versionConfigDto.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Name".equals(elementName)) {
                    versionConfigDto.setName(readCharacters(reader));
                }
                if ("Delivery".equals(elementName)) {
                    DeliveryDto deliveryDto = new DeliveryDto();
                    deliveryDto.setType(readAttributeValue(reader, "type"));
                    deliveryDto.setPriority(readAttributeValue(reader, "priority"));
                    deliveryDto.setZoneRefId(readAttributeValue(reader, "zonerefid"));
                    versionConfigDto.setDeliveryDto(deliveryDto);
                }
                if ("Metatags".equals(elementName)) {
                    versionConfigDto.setMetatags(readCharacters(reader));
                }
                if ("Description".equals(elementName)) {
                    versionConfigDto.setDescription(readCharacters(reader));
                }
                if ("Contents".equals(elementName)) {
                    List<ContentConfigDto> contentConfigDtoList = readContents(reader, touchpointExportSelectionTree);
                    versionConfigDto.setContentConfigDtoList(contentConfigDtoList);
                }
                if ("Selections".equals(elementName)) {
                    GenericXmlElementDto selectionsElementDto = new GenericXmlElementDto();
                    selectionsElementDto.setTagName(elementName);
                    selectionsElementDto.setAttributes(readAttributes(reader));
                    readChildren(selectionsElementDto, reader, "Selections");
                    versionConfigDto.setSelectionsDto(selectionsElementDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Version".equals(reader.getLocalName())) {
                return versionConfigDto;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<ContentConfigDto> readContents(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        List<ContentConfigDto> contentConfigDtoList = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Content".equals(elementName)) {
                    ContentConfigDto contentConfigDto = new ContentConfigDto();
                    Map<String, String> attributes = readAttributes(reader);
                    contentConfigDto.setAttributes(attributes);
                    String contentHtml = readCharacters(reader);
                    contentConfigDto.setContentHtml(contentHtml);
                    resolveVarReferencesForMessages(contentConfigDto, touchpointExportSelectionTree);
                    contentConfigDtoList.add(contentConfigDto);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Contents".equals(reader.getLocalName())) {
                return contentConfigDtoList;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private void resolveVarReferencesForMessages(ContentConfigDto contentConfigDto, TouchpointExportSelectionTree touchpointExportSelectionTree) {
        if(!StringUtil.isEmptyOrNull(contentConfigDto.getContentHtml())) {
            Document document = Jsoup.parse(contentConfigDto.getContentHtml(), "", Parser.xmlParser());
            document.outputSettings().prettyPrint(false);
            document.getElementsByTag("var").forEach(element -> {
                String elementText = element.text();
                if(elementText.equals("RENDERED_ITEM")) {
                    String elementId = element.attr("id");
                    String elementDna = element.attr("dna");
                    String key = computeKeyByVariableIdOrDna(elementId, elementDna);
                    if (!StringUtil.isEmptyOrNull(key)) {
                        String type = element.attr("type");
                        if(type.equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT)) {
                            EmbeddedContentConfigDto referencedEmbeddedContent = searchAndRetrieveEmbeddedContent(elementId, elementDna, touchpointExportSelectionTree.getEmbeddedContentConfigDtoList());
                            if(referencedEmbeddedContent != null) {
                                element.html(referencedEmbeddedContent.getVersionConfigDto().getName());
                            } else {
                                element.html("SMART TEXT NOT FOUND");
                            }
                        } else if (type.equals(ContentObjectContentUtil.VAR_TYPE_VARIABLE)) {
                            Set<String> keySet = touchpointExportSelectionTree.getUserVariablesLookupMap().keySet()
                                    .stream()
                                    .filter(s -> s.contains(key))
                                    .collect(Collectors.toSet());
                            if (CollectionUtils.isNotEmpty(keySet)) {
                                element.text(touchpointExportSelectionTree.getUserVariablesLookupMap().get(keySet.iterator().next()).getName());
                            } else {
                                element.html("VARIABLE NOT FOUND");
                            }
                        }
                    }
                    contentConfigDto.setContentHtml(document.toString());
                }
            });
        }
    }

    private void resolveVarReferencesForEmbeddedContents(TouchpointExportSelectionTree touchpointExportSelectionTree) {
        List<EmbeddedContentConfigDto> embeddedContentConfigDtoList = touchpointExportSelectionTree.getEmbeddedContentConfigDtoList();
        if(CollectionUtils.isNotEmpty(embeddedContentConfigDtoList)) {
            for(EmbeddedContentConfigDto embeddedContentConfigDto: embeddedContentConfigDtoList) {
                List<ContentConfigDto> contentConfigDtoList = embeddedContentConfigDto.getVersionConfigDto().getContentConfigDtoList();
                if(CollectionUtils.isNotEmpty(contentConfigDtoList)) {
                    for(ContentConfigDto contentConfigDto: contentConfigDtoList) {
                        Document document = Jsoup.parse(contentConfigDto.getContentHtml(), "", Parser.xmlParser());
                        document.outputSettings().prettyPrint(false).escapeMode(Entities.EscapeMode.extended);
                        document.getElementsByTag("var").forEach(element -> {
                            String elementText = element.text();
                            if(elementText.equals("RENDERED_ITEM")) {
                                String elementId = element.attr("id");
                                String elementDna = element.attr("dna");
                                String key = computeKeyByVariableIdOrDna(elementId, elementDna);
                                if (!StringUtil.isEmptyOrNull(key)) {
                                    String type = element.attr("type");
                                    if(type.equals(ContentObjectContentUtil.VAR_TYPE_EMBEDDED_CONTENT)) {
                                        EmbeddedContentConfigDto referencedEmbeddedContent = searchAndRetrieveEmbeddedContent(elementId, elementDna, touchpointExportSelectionTree.getEmbeddedContentConfigDtoList());
                                        if(referencedEmbeddedContent != null) {
                                            element.html(referencedEmbeddedContent.getVersionConfigDto().getName());
                                        } else {
                                            element.html("SMART TEXT NOT FOUND");
                                        }
                                    } else if (type.equals(ContentObjectContentUtil.VAR_TYPE_VARIABLE)) {
                                        Set<String> keySet = touchpointExportSelectionTree.getUserVariablesLookupMap().keySet()
                                                .stream()
                                                .filter(s -> s.contains(key))
                                                .collect(Collectors.toSet());
                                        if (CollectionUtils.isNotEmpty(keySet)) {
                                            element.text(touchpointExportSelectionTree.getUserVariablesLookupMap().get(keySet.iterator().next()).getName());
                                        } else {
                                            element.html("VARIABLE NOT FOUND");
                                        }
                                    }
                                }
                            }
                            contentConfigDto.setContentHtml(document.toString());
                        });
                    }
                }
            }
        }
    }

    private EmbeddedContentConfigDto searchAndRetrieveEmbeddedContent(String elementId, String elementDna, List<EmbeddedContentConfigDto> embeddedContentConfigDtoList) {
        for (EmbeddedContentConfigDto embeddedContentConfigDto : embeddedContentConfigDtoList) {
            if(StringUtil.isEmptyOrNull(elementId)) {
                if (embeddedContentConfigDto.getDna().equals(elementDna)) {
                    return embeddedContentConfigDto;
                }
            } else {
                if (embeddedContentConfigDto.getId() == Long.parseLong(elementId)) {
                    return embeddedContentConfigDto;
                }
            }
        }
        return null;
    }

    private String computeKeyByVariableIdOrDna(String id, String dna) {
        String key = "";

        if(!StringUtil.isEmptyOrNull(id)) {
            key = "[" + id + "]";
        }

        if(!StringUtil.isEmptyOrNull(dna)) {
            key += "[" + dna + "]";
        }

        return key;
    }

    private TouchpointExportSelectionNode readSelectionNode(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        TouchpointExportSelectionNode root = null;
        while (reader.hasNext()) {
            int eventType = reader.next();
            switch (eventType) {
                case XMLStreamReader.START_ELEMENT:
                    String elementName = reader.getLocalName();
                    if ("Selection".equals(elementName)) {
                        root = readSelection(reader, touchpointExportSelectionTree);
                    }
                    break;
                case XMLStreamReader.END_ELEMENT:
                    return root;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private TouchpointExportSelectionNode readSelection(XMLStreamReader reader, TouchpointExportSelectionTree touchpointExportSelectionTree) throws Exception {
        TouchpointExportSelectionNode root = new TouchpointExportSelectionNode();
        String idString = readAttributeValue(reader, "id");
        root.setId(StringUtils.isNotEmpty(idString) ? Long.valueOf(idString) : null);
        root.setAttributes(readAttributes(reader));
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if ("Name".equals(elementName)) {
                    root.setName(readCharacters(reader));
                }
                if ("Contents".equals(elementName)) {
                    GenericXmlElementDto contentsElement = new GenericXmlElementDto();
                    contentsElement.setTagName(elementName);
                    contentsElement.setParent(root);
                    readChildren(contentsElement, reader, "Contents");
                    root.addChildNode(contentsElement);
                }
                if ("Messages".equals(elementName)) {
                    root.setMessageConfigDtoList(readMessages(reader));
                }
                if ("Selection".equals(elementName)) {
                    TouchpointExportSelectionNode child = readSelection(reader, touchpointExportSelectionTree);
                    child.setParent(root);
                    if (CollectionUtils.isEmpty(root.getChildren())) {
                        root.setChildren(new LinkedList<>());
                    }
                    root.getChildren().add(child);
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Selection".equals(reader.getLocalName())) {
                return root;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private void readChildren(GenericXmlElementDto root, XMLStreamReader reader, String rootTagName) throws Exception {
        GenericXmlElementDto child;
        StringBuilder elementTextContent = new StringBuilder();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                child = new GenericXmlElementDto();
                child.setTagName(reader.getLocalName());
                if(reader.getAttributeCount() > 0) {
                    child.setAttributes(readAttributes(reader));
                }
                readChildren(child, reader, child.getTagName());
                child.setParent(root);
                root.addChild(child);
            } else if (XMLStreamReader.CHARACTERS == eventType || XMLStreamReader.CDATA == eventType) {
                elementTextContent.append(reader.getText().trim());
            } else if (XMLStreamReader.END_ELEMENT == eventType && rootTagName.equals(reader.getLocalName())) {
                root.setTextContent(elementTextContent.toString());
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private List<MessageConfigDto> readMessages(XMLStreamReader reader) throws Exception {
        List<MessageConfigDto> messageConfigDtos = new ArrayList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType && "Message".equals(reader.getLocalName())) {
                String idString = readAttributeValue(reader, "refid");
                String isLocalAttributeValue = readAttributeValue(reader, "islocal");
                if(isLocalAttributeValue.equalsIgnoreCase("true")) {
                    continue;
                }
                MessageConfigDto crtMessage = new MessageConfigDto();
                crtMessage.setId(StringUtils.isNotEmpty(idString) ? Long.valueOf(idString) : generateUniqueLongId());
                messageConfigDtos.add(crtMessage);
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Messages".equals(reader.getLocalName())) {
                return messageConfigDtos;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private ZoneConfigDto readZone(XMLStreamReader reader) throws Exception {
        ZoneConfigDto zoneConfigDto = new ZoneConfigDto();
        zoneConfigDto.setZoneName(this.readAttributeValue(reader, ZONE_NAME_ATR));
        zoneConfigDto.setZoneFriendlyName(this.readAttributeValue(reader, ZONE_FRIENDLY_NAME_ATR));
        zoneConfigDto.setZoneId(Integer.parseInt(Objects.requireNonNull(this.readAttributeValue(reader, "id"))));
        zoneConfigDto.setAttributes(readAttributes(reader));
        zoneConfigDto.validate();
        return zoneConfigDto;
    }

    private String readAttributeValue(XMLStreamReader r, String attributeName) {
        for (int i = 0; i < r.getAttributeCount(); ++i) {
            if (StringUtils.isNotEmpty(attributeName) && attributeName.equals(r.getAttributeLocalName(i))) {
                return r.getAttributeValue(i);
            }
        }

        return null;
    }

    /**
     * Completely reads the all the characters under the active
     * XML tag being processed.
     */
    String readCharacters(XMLStreamReader r) throws XMLStreamException {
        StringBuilder result = new StringBuilder();
        while (r.hasNext()) {
            int eventType = r.next();
            switch (eventType) {
                case XMLStreamReader.CHARACTERS:
                case XMLStreamReader.CDATA:
                    result.append(r.getText());
                    break;
                case XMLStreamReader.END_ELEMENT:
                    return result.toString().trim();
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    /**
     * Reads all the attributes of a given XML entity.
     */
    LinkedHashMap<String, String> readAttributes(XMLStreamReader r) {
        LinkedHashMap<String, String> p = new LinkedHashMap<>();

        for (int i = 0; i < r.getAttributeCount(); i++) {
            p.put(r.getAttributeName(i).getLocalPart(), r.getAttributeValue(i));
        }

        return p;
    }

    public List<TouchpointLanguageDto> parseLanguageList(XMLStreamReader reader) throws XMLStreamException {
        List<TouchpointLanguageDto> languageDtoList = new LinkedList<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType && "Language".equals(reader.getLocalName())) {
                TouchpointLanguageDto language = new TouchpointLanguageDto();
                language.setCode(readAttributeValue(reader, "code"));
                language.setDefaultLanguage(Boolean.parseBoolean(readAttributeValue(reader, "default")));
                parseLanguage(reader, language);
                languageDtoList.add(language);
            } else if (XMLStreamReader.END_ELEMENT == eventType && "Languages".equals(reader.getLocalName())) {
                return languageDtoList;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    private void parseLanguage(XMLStreamReader reader, TouchpointLanguageDto touchpointLanguageDto) throws  XMLStreamException {
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (elementName.equals("Name")) {
                    if(StringUtil.isEmptyOrNull(touchpointLanguageDto.getName())) {
                        touchpointLanguageDto.setName(readCharacters(reader));
                    }
                }
            } else if (XMLStreamReader.END_ELEMENT == eventType && reader.getLocalName().equals("Language")) {
                return;
            }
        }
        throw new XMLStreamException("Premature end of file");
    }

    public void extractTouchpointElements(Reader r, RationalizerExportToMessagepointWrapper wrapper) throws Exception {
        XMLStreamReader reader = XMLInputFactory.newInstance().createXMLStreamReader(r);
        Set<ZoneConfigDto> zoneSet = new LinkedHashSet<>();
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                if (reader.getLocalName().equals(REQUEST_DATE)) {
                    String date = readCharacters(reader);
                    if(date!=null && !date.isEmpty()) {
                        wrapper.setRequestedDate(Instant.parse(date).toDate());
                    }
                }
                if (reader.getLocalName().equals(TP_GUID)) {
                    String guid = readCharacters(reader);
                    wrapper.setTouchpointGuid(guid);
                }
                if (reader.getLocalName().equals("Languages")) {
                    wrapper.setLanguageList(parseLanguageList(reader));
                }
                if (reader.getLocalName().equals("Zone")) {
                    ZoneConfigDto zoneConfigDto = readZone(reader);
                    zoneSet.add(zoneConfigDto);
                }
            }
            if (XMLStreamReader.START_ELEMENT == eventType && reader.getLocalName().equals("AlternateLayouts")) {
                wrapper.setZonesSet(zoneSet);
                reader.close();
                return;
            }
        }
    }
}
