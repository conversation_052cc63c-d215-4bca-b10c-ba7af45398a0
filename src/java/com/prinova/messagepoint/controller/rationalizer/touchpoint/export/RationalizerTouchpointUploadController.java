package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class RationalizerTouchpointUploadController extends MessagepointController {
    public static final String REQ_PARAM_RATIONALIZER_APPLICATION_ID = "rationalizerApplicationId";
    public static final String REQ_PARAM_UPLOADED_FILE_NAME = "uploadedFileName";
    public static final String REQ_PARAM_VIEW_TYPE = "viewType";
    public static final String REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID = "rationalizerDocFormItemId";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID, -1);
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        referenceData.put("rationalizerApplication", rationalizerApplication);

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(RationalizerApplication.class, new IdCustomEditor<>(RationalizerApplication.class));
    }

    protected RationalizerTouchpointUploadWrapper formBackingObject(HttpServletRequest request) throws Exception {
        return new RationalizerTouchpointUploadWrapper();
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID, -1);
        long viewType = ServletRequestUtils.getLongParameter(request, REQ_PARAM_VIEW_TYPE, -1);
        String rationalizerDocFormItemId = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, "-9");
        RationalizerTouchpointUploadWrapper rationalizerTouchpointUploadWrapper = (RationalizerTouchpointUploadWrapper) commandObj;
        String tmpFilerootPath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir);
        FileUtil.createDirectoryIfNeeded(tmpFilerootPath);
        String tmpFilename = "temp_" + UserUtil.getPrincipalUserId() + "_" + DateUtil.timeStamp();

        File importFile = new File(tmpFilerootPath + tmpFilename + ".xml");
        rationalizerTouchpointUploadWrapper.getImportFilename().transferTo(importFile);

        String redirectUrl = ApplicationUtil.getWebRoot() + "rationalizer/rationalizer_export_to_messagepoint.form?rationalizerApplicationId=" + rationalizerApplicationId;
        redirectUrl += "&uploadedFileName=" + tmpFilename;
        redirectUrl += "&viewType=" + viewType;
        redirectUrl += "&rationalizerDocFormItemId=" + rationalizerDocFormItemId;

        Map<String, Object> params = new HashMap<>();
        params.put("redirectUrl", encodeValue(redirectUrl));

        return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
    }

    private String encodeValue(String value) throws UnsupportedEncodingException {
        return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
    }
}
