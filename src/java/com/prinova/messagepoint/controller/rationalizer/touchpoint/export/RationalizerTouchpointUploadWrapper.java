package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

public class RationalizerTouchpointUploadWrapper implements Serializable {

    private MultipartFile importFilename;

    public RationalizerTouchpointUploadWrapper() {
    }

    public MultipartFile getImportFilename() {
        return importFilename;
    }

    public void setImportFilename(MultipartFile importFilename) {
        this.importFilename = importFilename;
    }
}
