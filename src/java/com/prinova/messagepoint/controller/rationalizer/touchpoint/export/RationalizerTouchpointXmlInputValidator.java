package com.prinova.messagepoint.controller.rationalizer.touchpoint.export;

import com.prinova.messagepoint.platform.services.rationalizer.dto.validators.ValidationException;
import org.apache.commons.lang3.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;


import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RationalizerTouchpointXmlInputValidator {

    private static final String ELEMENT_MpTouchpointDefinition = "MpTouchpointDefinition";
    private static final String ELEMENT_TP = "Touchpoint";
    private static final String ELEMENT_TP_SELECTIONS = "Selections";
    private static final String ATTR_TP_SELECTIONS_SELECTORREFID = "selectorrefid";
    private static final String ATTR_TP_SELECTIONS_SELECTIONTYPE = "selectiontype";
    private static final String ATTR_TP_FONT_TTFREFID = "ttfrefid";
    private static final String ELEMENT_SELECTORS = "Selectors";
    private static final String ELEMENT_SELECTOR = "Selector";
    private static final String ELEMENT_FONT_FILE = "FontFile";
    private static final String ELEMENT_FONT = "Font";
    private static final String ELEMENT_FONTS = "Fonts";
    private static final String ATTR_SELECTOR_ID = "id";

    public static final String IsValidFile = "IsValidFile";
    public static final String IsValidFontFamily = "HasInvalidFontFamily";
    public static final String IsVariationEnabled = "IsTouchpointVariationEnabled";

    private static final Log log = LogUtil.getLog(RationalizerTouchpointXmlInputValidator.class);

    private final File touchpointXmlFile;

    private String selectionRefId;

    private Map<String, Boolean> touchpointValidations = new HashMap<>();

    private String invalidFontFamily ="";

    public String getInvalidFontFamily() {
        return invalidFontFamily;
    }

    public Boolean IsValidFile() {
        return touchpointValidations.get(IsValidFile);
    }

    public Boolean IsValidFontFamily () {
        return touchpointValidations.get(IsValidFontFamily);
    }

    public Boolean IsVariationEnabled () {
        return touchpointValidations.get(IsVariationEnabled);
    }

    protected RationalizerTouchpointXmlInputValidator(File touchpointXmlFile) {
        this.touchpointXmlFile = touchpointXmlFile;
    }

    /**
     * Validate reference touchpoint.
     * Granular validation are put in a hashmap touchpointValidations
     * The invalid font family is keep in the text invalidFontFamily
     */
    public void processTouchpointForValidations() throws Exception{
        List<String> selectorIds = new ArrayList<>();
        boolean touchPointVariationsEnabled = false;

        try (Reader inputStreamReader = new InputStreamReader(new BufferedInputStream(new FileInputStream(touchpointXmlFile)))) {
            XMLStreamReader reader = XMLInputFactory.newInstance().createXMLStreamReader(inputStreamReader);
            boolean processTouchpointDefinition = false;
            boolean processInvalidFont = false;
            boolean processSelectors = false;
            boolean processTouchpointVariation = false;
             while (reader.hasNext()) {
                 int eventType = reader.next();
                 if (XMLStreamReader.START_ELEMENT == eventType) {
                     String elementName = reader.getLocalName();
                    if (elementName.equals(ELEMENT_MpTouchpointDefinition)) {
                        touchpointValidations.put(IsValidFile, true);
                        processTouchpointDefinition = true;
                        continue;
                    }
                    if (elementName.equals(ELEMENT_FONTS)) {
                        writeInvalidFontFamily(reader);
                        processInvalidFont = true;
                        continue;
                    }
                    if (elementName.equals(ELEMENT_SELECTORS)) {
                        selectorIds = getSelectorIds(reader);
                        processSelectors = true;
                    }
                    if (elementName.equals(ELEMENT_TP)) {
                        touchPointVariationsEnabled = isTouchpointVariationEnabled(reader);
                        processTouchpointVariation = true;
                    }
                }
                 if(processTouchpointDefinition && processInvalidFont && processSelectors && processTouchpointVariation){
                     break;
                 }
            }
             reader.close();
        }catch (ValidationException exception) {
            log.error(exception.getMessageAsHtml(), exception);
        }

        if(invalidFontFamily.isEmpty()){
            touchpointValidations.put(IsValidFontFamily, true);
        }
        if(touchPointVariationsEnabled && selectorIds.contains(selectionRefId)){
            touchpointValidations.put(IsVariationEnabled, true);
        } else {
            touchpointValidations.put(IsVariationEnabled, false);
        }
        if(!touchpointValidations.containsKey(IsValidFile)){
            touchpointValidations.put(IsValidFile,false);
        }

    }

    public boolean IsValidTouchpointFile(){
        return IsValidFontFamily() && IsValidFile() && IsVariationEnabled();
    }

    private void writeInvalidFontFamily(XMLStreamReader reader) throws Exception {
        String fontName = "";
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.START_ELEMENT == eventType) {
                String elementName = reader.getLocalName();
                if (elementName.equals(ELEMENT_FONT)) {
                    fontName = readAttributeValue(reader, "name");
                }

                if (elementName.equals(ELEMENT_FONT_FILE)) {
                    String ttrefId = readAttributeValue(reader, ATTR_TP_FONT_TTFREFID);

                    if (ttrefId != null && ttrefId.equalsIgnoreCase("null")) {
                        if (!invalidFontFamily.contains(fontName)) {
                            invalidFontFamily += fontName + "; ";
                        }
                        if (!touchpointValidations.containsKey(IsValidFontFamily)) {
                            touchpointValidations.put(IsValidFontFamily, false);
                        }
                    }
                }

            }

            if (XMLStreamReader.END_ELEMENT == eventType && reader.getLocalName().equals(ELEMENT_FONTS)) {
                if(!invalidFontFamily.isEmpty()) {
                    invalidFontFamily = invalidFontFamily.substring(0, invalidFontFamily.length() - 2);
                }
                break;
            }
        }

    }

    private String readAttributeValue(XMLStreamReader r, String attributeName) {
        for (int i = 0; i < r.getAttributeCount(); ++i) {
            if (StringUtils.isNotEmpty(attributeName) && attributeName.equals(r.getAttributeLocalName(i))) {
                return r.getAttributeValue(i);
            }
        }

        return null;
    }

    private final boolean isTouchpointVariationEnabled(XMLStreamReader reader) throws Exception {
        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.END_ELEMENT == eventType && reader.getLocalName().equals(ELEMENT_TP)) {
                return false;
            }
            if (XMLStreamReader.START_ELEMENT == eventType) {
                final String elementName = reader.getLocalName();
                switch (elementName) {
                    case ELEMENT_TP_SELECTIONS:
                        if(reader.getAttributeCount()>0) {
                            selectionRefId =  readAttributeValue(reader, ATTR_TP_SELECTIONS_SELECTORREFID) ;
                            log.debug("Touchpoint selectionrefid = " + selectionRefId);
                            String selectionType =   readAttributeValue(reader, ATTR_TP_SELECTIONS_SELECTIONTYPE) ;
                            log.debug("Selection type = " + selectionType);
                            if (selectionRefId != null) {
                                return true;
                            }
                        } else {
                            return false;
                        }
                        break;
                }
            }
        }

        return false;
    }

    private List<String> getSelectorIds(XMLStreamReader reader) throws Exception {
        List<String> selectorIds = new ArrayList<>();

        while (reader.hasNext()) {
            int eventType = reader.next();
            if (XMLStreamReader.END_ELEMENT == eventType && reader.getLocalName().equals(ELEMENT_SELECTORS)) {
                return selectorIds;
            }
            if (XMLStreamReader.START_ELEMENT == eventType) {
                final String elementName = reader.getLocalName();
                switch (elementName) {
                    case ELEMENT_SELECTOR:
                        String selectorId =  readAttributeValue(reader,ATTR_SELECTOR_ID);
                        if (selectorId != null && !selectorId.isEmpty()) {
                            selectorIds.add(selectorId);
                        }
                        break;
                }
            }
        }

        return selectorIds;
    }

}
