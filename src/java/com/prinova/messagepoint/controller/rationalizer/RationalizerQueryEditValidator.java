package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class RationalizerQueryEditValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		RationalizerQueryEditWrapper wrapper = (RationalizerQueryEditWrapper) commandObj;

		// NAME
		if ( wrapper.getRationalizerQuery() == null )
			errors.reject("error.input.mandatory", new String[] {ApplicationUtil.getMessage("page.label.name")}, "");
		else {
			String label = ApplicationUtil.getMessage("page.label.name");
			MessagepointInputValidationUtil.validateStringValue(label, wrapper.getRationalizerQuery().getName().trim(), true, 1, 255,
					ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
		}

		if ( !wrapper.getActionValue().equals("submit") ) {
			if ( wrapper.getActionValue() != null && Integer.valueOf(wrapper.getActionValue()) > 0 ) {
				
				if ( Integer.valueOf(wrapper.getActionValue()) == RationalizerQueryEditController.ACTION_MODIFY_CONTENT ) {
					if ( wrapper.getModifyContentAction() == 1 && (wrapper.getModifyContentSearchValue() == null || wrapper.getModifyContentSearchValue().isEmpty()) )
						errors.reject("error.rationalizer.query.search.value.required", "A search value is required");
				}
					
				if ( Integer.valueOf(wrapper.getActionValue()) == RationalizerQueryEditController.ACTION_MODIFY_DOCUMENTS ) {
					if ( wrapper.getModifyDocumentAction() == 1 && (wrapper.getModifyDocumentSearchValue() == null || wrapper.getModifyDocumentSearchValue().isEmpty()) )
						errors.reject("error.rationalizer.query.search.value.required", "A search value is required");
				}
			
			}
		}

	}
}
