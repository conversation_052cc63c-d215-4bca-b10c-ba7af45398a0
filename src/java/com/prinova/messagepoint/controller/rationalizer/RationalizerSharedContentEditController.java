package com.prinova.messagepoint.controller.rationalizer;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.SystemProperty;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerSharedContent;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerContentService;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerSharedContentsMetaUpdateBackgroundTask;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.prinova.messagepoint.model.rationalizer.SearchResultIds.splitIdsString;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;

public class RationalizerSharedContentEditController extends MessagepointController {

    private static final Log log = LogUtil.getLog(RationalizerMetadataEditController.class);

    public static final Integer ACTION_EDIT = 1;
    public static final Integer ACTION_SEPARATE = 2;
    public static final Integer ACTION_UPDATE_CONTENTS_METADATA = 3;

    public static final String REQ_PARAM_RATIONALIZER_SHARED_CONTENT_IDS = "rationalizerSharedContentIds";
    public static final String REQ_ACTION = "action";
    public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";


    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

        String rationalizerSharedContentIdsString = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_SHARED_CONTENT_IDS, "");
        Set<String> selectedIds = splitIdsString(rationalizerSharedContentIdsString);
        if (CollectionUtils.isEmpty(selectedIds)) {
            return new LinkedHashMap<>();
        }

        RationalizerSharedContent sharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(selectedIds.iterator().next());
        if (sharedContent == null) {
            return new LinkedHashMap<>();
        }

        final List<RationalizerDocument> sharedContentDocuments = sharedContent.getDocuments();
        if (sharedContentDocuments.isEmpty()) {
            return new LinkedHashMap<>();
        }

        RationalizerDocument document = sharedContentDocuments.get(0);

        Map<String, Object> referenceData = new LinkedHashMap<>();
        referenceData.put("contextDocument", document);
        referenceData.put("rationalizerApplication", document.getRationalizerApplication());
        referenceData.put("rationalizerApplicationId", document.getRationalizerApplication().getId());

        // EDITOR INIT
        referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

        JSONObject marcieFlagsObj = SystemProperty.getMarcieKeyValues();
        marcieFlagsObj.put("content_compare_enabled", false);
        marcieFlagsObj.put("brand_check_enabled", BrandProfile.isBrandProfileConfigured(null, document.getRationalizerApplication()));
        referenceData.put("marcieFlags", marcieFlagsObj);

        referenceData.put("touchpointContext", document.getTouchpoint());

        // SYSTEM VARIABLES
        List<DataElementVariable> systemVariables = new ArrayList<>();
        if (document.getTouchpoint() != null) {
            systemVariables = DataElementVariable.findAllSystemVariablesEnabledForContent(!document.getTouchpoint().isNativeCompositionTouchpoint());
        }
        referenceData.put("systemVariablesData", DataElementVariable.getListAsJSON(systemVariables));


        referenceData.put("dateFormat", DateUtil.DATE_FORMAT);

        List<String> modifyContentMetaConnectorNames = new ArrayList<>();
        modifyContentMetaConnectorNames.add(getMessage("page.label.rationalizer.document.content.field.zoneConnector"));
        modifyContentMetaConnectorNames.add(getMessage("page.label.rationalizer.document.content.field.messageName"));

        referenceData.put("modifyContentMetaConnectorNames", modifyContentMetaConnectorNames);

        return referenceData;
    }

    protected RationalizerSharedContentEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
        String rationalizerSharedContentIdsString = ServletRequestUtils.getStringParameter(request, REQ_PARAM_RATIONALIZER_SHARED_CONTENT_IDS, "");
        Set<String> selectedIds = splitIdsString(rationalizerSharedContentIdsString);
        Integer action = ServletRequestUtils.getIntParameter(request, REQ_ACTION, -1);
        RationalizerSharedContent sharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(selectedIds.iterator().next());
        String contentMarkup = sharedContent.getMarkupContent();

        return new RationalizerSharedContentEditWrapper(new ArrayList<>(selectedIds), sharedContent.getName(), action, contentMarkup, sharedContent);
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
            throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
        AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.SharedContentEdit);
        Map<String, Object> params = new HashMap<>();

        try {
            RationalizerSharedContentEditWrapper wrapper = (RationalizerSharedContentEditWrapper) commandObj;

            if (ACTION_EDIT.equals(wrapper.getAction())) {
                List<String> selectedIds = wrapper.getSelectedIds();
                if (CollectionUtils.isEmpty(selectedIds)) {
                    return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
                }
                ServiceExecutionContext context = CreateOrUpdateRationalizerContentService.createContextForUpdateShared(wrapper.getSelectedIds(), Collections.singletonList(wrapper.getContentMarkup()),
                        wrapper.getSharedContentName());
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerContentService.SERVICE_NAME, CreateOrUpdateRationalizerContentService.class);
                service.execute(context);

                if ( wrapper.getFormWrapper() != null ) {
                    wrapper.getRationalizerSharedContent().setParsedContentForm( wrapper.getFormWrapper().getMetadataForm() );
                }

                context = CreateOrUpdateRationalizerApplicationService.createContextForUpdateSharedContentMetadata(wrapper.getRationalizerSharedContent(), wrapper.getFormWrapper().getMetadataItemPreValueMap());

                service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
                service.execute(context);
                ServiceResponse serviceResponse = context.getResponse();
                if (serviceResponse.isSuccessful()) {
                    Map<String, Object> parms = new HashMap<>();
                    parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                    return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                    sb.append(" service call is not successful ");
                    sb.append(" in ").append(this.getClass().getName());
                    sb.append(" Metadata Form was not saved. ");
                    log.error(sb.toString());
                    ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                    return super.showForm(request, response, errors);
                }
            }
            else if (ACTION_SEPARATE.equals(wrapper.getAction())) {
                ServiceExecutionContext context = CreateOrUpdateRationalizerContentService.createContextForRemoveFromShared(computeRationalizerDocumentContentList(wrapper.getSelectedIds()));
                Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerContentService.SERVICE_NAME, CreateOrUpdateRationalizerContentService.class);
                service.execute(context);

                params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, context.getResponse().isSuccessful());

                return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
            } else if (ACTION_UPDATE_CONTENTS_METADATA.equals(wrapper.getAction())) {
                //update zone and message name for all contents contained in shared
                RationalizerSharedContentsMetaUpdateBackgroundTask task = new RationalizerSharedContentsMetaUpdateBackgroundTask( wrapper, UserUtil.getPrincipalUser());
                MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
                params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);

                return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
            }
        } finally {
            analyticsEvent.send();
        }
        return null;
    }

    protected boolean isFormSubmission(HttpServletRequest request) {
        String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
        return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
    }

    private List<RationalizerDocumentContent> computeRationalizerDocumentContentList(List<String> selectedIds) {
        List<RationalizerDocumentContent> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(selectedIds)) {
            return result;
        }

        final List<RationalizerSharedContent> sharedContents = RationalizerSharedContent.findSharedContentByElasticSearchGuids(selectedIds);
        for (RationalizerSharedContent sharedContent : sharedContents) {
            sharedContent.streamContents().forEach(content -> result.add(content));
        }

        return result;
    }
}
