package com.prinova.messagepoint.controller;

import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateAlternateLayoutService;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncLayoutController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncLayoutController.class);

	public static final String REQ_PARM_ACTION 				= "action";
	public static final String REQ_PARM_NAME 				= "name";
	public static final String REQ_PARM_DOCUMENT_ID			= "documentId";
	
	public static final String ACTION_ADD_LAYOUT			= "add_layout";
	public static final String ACTION_REMOVE_LAYOUT			= "remove_layout";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		
		
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENT_ID, -1);
		
		JSONObject returnObj = new JSONObject();
		
		if ( action.equalsIgnoreCase(ACTION_ADD_LAYOUT) ) {
			
			String name = ServletRequestUtils.getStringParameter(request, REQ_PARM_NAME, null);
			
			Document document = Document.findById(documentId);
			if (!document.getAlternateLayoutsByName(name).isEmpty()) {
				
				returnObj.put("error", true);
				returnObj.put("message", ApplicationUtil.getMessage("error.message.duplicate.layout.name"));
				
			} else {
				
				try {
					
					ServiceExecutionContext context = CreateOrUpdateAlternateLayoutService.createContextForCreateLayout(documentId, name);
					Service createLayoutService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAlternateLayoutService.SERVICE_NAME, CreateOrUpdateAlternateLayoutService.class);
					createLayoutService.execute(context);
					
					if ( context.getResponse().getResultValueBean() != null ) {
						long newDocumentId = (Long)context.getResponse().getResultValueBean();
						returnObj.put("document_id", newDocumentId);
					}
				
				} catch (Exception e) {
					
					returnObj.put("error", true);
					returnObj.put("message", e.getMessage());
					
				}
				
			}
			
		} else if ( action.equalsIgnoreCase(ACTION_REMOVE_LAYOUT) ) {
			
			try {
				
				Document targetAlternate = Document.findById(documentId);
				if ( targetAlternate.getChannelParent() != null )
					targetAlternate = targetAlternate.getChannelParent();
				
				if ( targetAlternate.isAlternate() ) {
					List<Document> alternates = targetAlternate.getRootDocument().getAppliedAlternateLayouts();
					if ( alternates.contains(targetAlternate) ) {
						returnObj.put("error", true);
						returnObj.put("message", ApplicationUtil.getMessage("error.message.cannot.delete.referenced.alternate"));
					} else {
						ServiceExecutionContext context = CreateOrUpdateAlternateLayoutService.createContextForDeleteLayout(documentId);
						Service deleteLayoutService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAlternateLayoutService.SERVICE_NAME, CreateOrUpdateAlternateLayoutService.class);
						deleteLayoutService.execute(context);
						
						if ( context.getResponse().isSuccessful() )
							returnObj.put("success", true);
					}
				} else {
					returnObj.put("error", true);
					returnObj.put("message", "Delete failed: Specified item is not an alternate layout");
				}
			
			} catch (Exception e) {
				
				returnObj.put("error", true);
				returnObj.put("message", e.getMessage());
				
			}

		} else {
			returnObj.put("error", true);
			returnObj.put("message", "Async Layout: Invalid action type");
		}
		
		return returnObj.toString();
	}

}