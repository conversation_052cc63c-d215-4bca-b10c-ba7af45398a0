package com.prinova.messagepoint.controller;

import com.prinova.licence.webapp.models.Licence;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.content.ConnectedImageData;
import com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerDewsProvider;
import com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerPMGRProvider;
import com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerProvider;
import com.prinova.messagepoint.connected.touchpointrendering.ConnectedTouchpointRenderingManagerZipProvider;
import com.prinova.messagepoint.connected.util.ObjectMapperUtil;
import com.prinova.messagepoint.connected.util.RenderingManagerUtil;
import com.prinova.messagepoint.connected.util.SupportingDataUtil;
import com.prinova.messagepoint.controller.communication.CommunicationPortalGatewayController;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.job.JobZipFile;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.deserver.DEServer;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataReport;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.model.util.JobPackerUtil;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.stats.StatisticsUtils;
import com.prinova.messagepoint.theme.ThemeManager;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.http.HttpStatus;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.action.PDActionJavaScript;
import org.jpedal.PdfDecoderServer;
import org.jpedal.constants.JPedalSettings;
import org.jpedal.exception.PdfException;
import org.jpedal.fonts.FontMappings;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.activation.MimetypesFileTypeMap;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.*;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.List;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class BinaryStreamController implements Controller {

	private static final Log log = LogUtil.getLog(BinaryStreamController.class);

    public static final String REQ_PARAM_FILE 						= "file";
	public static final String REQ_PARAM_REPORT_TYPE				= "reportType";
	public static final String REQ_PARAM_TYPE						= "type";
	public static final String REQ_PARAM_ACTION						= "action";
	public static final String REQ_PARAM_NODE_GUID					= "gd";
	public static final String REQ_PARAM_COMMUNICATION_PROOF_ID		= "comm_proof_id";
    public static final String REQ_PARAM_COMMUNICATION_PROOF_VID	= "comm_proof_vid";
	public static final String REQ_PARAM_FONT_ID					= "font_id";
	public static final String REQ_PARAM_EMBEDDED					= "embedded_pdf";
	public static final String REQ_PARAM_HIGH_RESOLUTION			= "hi_res";
	public static final String REQ_PARAM_LOOKUP_TABLE_ID			= "lookup_table_id";
	public static final String REQ_PARAM_TOUCHPOINT_ID				= "documentId";
	public static final String REQ_PARAM_DOMAIN_ID                  = "domainId";
	public static final String REQ_PARAM_METADATA_ITEM_ID           = "metadata_item_id";
	public static final String REQ_PARAM_ORDER_ENTRY_ITEM_ID        = "order_entry_item_id";
	public static final String REQ_PARAM_COMPOSITION_FILESET        = "composition_fileset_id";
	public static final String REQ_PARAM_FILE_UPLOAD_KEY            = "file_upload_key";
	public static final String REQ_PARAM_JOB_ID                     = "job_id";
	public static final String REQ_PARAM_BUNDLE_TYPE                = "bundle_type";
	public static final String REQ_PARAM_BACKEND_COMPONENT          = "backend_component";
	public static final String REQ_PARAM_DE_SERVER_ID               = "server_id";
	public static final String REQ_PARAM_FILE_EXIST_CHECK           = "file_exists_check";
	public static final String REQ_PARAM_RESOURCE_TOKEN             = "resource";
	public static final String REQ_PARAM_DOCUMENT_HISTORY           = "document_history";
    public static final String REQ_PARAM_FILENAME 				    = "filename";

	public static final String TYPE_LICENCE_XML						= "licence_xml";
	public static final String TYPE_REMOTE_COMMUNICATION_PDF		= "remote_comm_pdf";
    public static final String TYPE_REMOTE_COMMUNICATION_PDFV		= "remote_comm_pdfv";
	public static final String TYPE_EXCEL							= "excel";
	public static final String TYPE_CONTENT_IMAGE					= "content_image";
	public static final String TYPE_FONT_TTF						= "font_ttf";
    public static final String TYPE_FONT_TTF_BOLD					= "font_ttf_bold";
    public static final String TYPE_FONT_TTF_ITALIC					= "font_ttf_italic";
    public static final String TYPE_FONT_TTF_BOLD_ITALIC			= "font_ttf_bold_italic";
	public static final String TYPE_RENDERED_PDF					= "rendered_pdf";
	public static final String TYPE_LOOKUP_TABLE_DATA				= "lookup_table_data";
	public static final String TYPE_METADATA						= "metadata";
	public static final String TYPE_CSV								= "csv";
	public static final String TYPE_AUDIT_REPORT					= "audit_report";
	public static final String TYPE_TP_AUDIT_REPORT					= "tp_report";
	public static final String TYPE_LOCAL_AUDIT_REPORT				= "local_audit_report";
	public static final String TYPE_SFTPREPO_FILE 					= "sftprepo_file";
	public static final String TYPE_METADATA_UPLOAD_FILE			= "metadata_upload_file";
	public static final String TYPE_ORDER_ENTRY_UPLOAD_FILE			= "order_entry_upload_file";
	public static final String TYPE_COMPOSITION_FILE                = "composition_file";
	public static final String TYPE_BUNDLE_FILE                     = "bundle_file";
	public static final String TYPE_BACKEND_COMPONENT               = "backend_component";
	public static final String TYPE_TEMPLATE						= "tp_template";
    public static final String TYPE_PUBLIC_KEY                      = "public_key";
    public static final String TYPE_DOWNLOAD_FILE_FROM_FILEROOT     = "download_file";
    public static final String TYPE_POD_STATISTIC_REPORTS	        = "pod_stats_reports";
    public static final String TYPE_IMPORTED_XML                    = "importedxml";
    public static final String TYPE_DOCUMENT_ORIGINATION_LOG        = "doc_origination_log";
    public static final String TYPE_RENDERING_MANAGER_DEBUG_FILE    = "rendering_debug_file";
    public static final String TYPE_TOUCHPOINT_EXPORT_XSD           = "touchpoint_export_xsd";

	public static final String ACTION_SAVE_FILE						= "saveFile";

    private static final String TPI_XSD_PATH = "com/prinova/messagepoint/model/util/tpi.xsd";

	private boolean downloadFromWebRoot = false;

    private CacheDataService dataService = null;

	public boolean isDownloadFromWebRoot() {
		return downloadFromWebRoot;
	}

	public void setDownloadFromWebRoot(boolean downloadFromWebRoot) {
		this.downloadFromWebRoot = downloadFromWebRoot;
	}

    public void setDataService (CacheDataService dataService) {
        this.dataService = dataService;
    }

    private String getStringParam(Map<String, String> params, String key, String defaultValue) {
        String value = params.get(key);

        if (value == null) {
            return defaultValue;
        }

        return value;
    }

    private Long getLongParam(Map<String, String> params, String key, Long defaultValue) {
        String value = params.get(key);

        if (value == null) {
            return defaultValue;
        }

        try {
            return Long.valueOf(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private Boolean getBooleanParam(Map<String, String> params, String key, Boolean defaultValue) {
        String value = params.get(key);

        if (value == null) {
            return defaultValue;
        }

        return (value.equalsIgnoreCase("true") ||
                    value.equalsIgnoreCase("on") ||
                    value.equalsIgnoreCase("yes") ||
                    value.equals("1"));
    }

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> rawKeyValues = URIUtils.splitQuery(request.getQueryString());
        Map<String, String> params = new HashMap<>();
        for (String key : request.getParameterMap().keySet()) {
            if (key.equals(REQ_PARAM_RESOURCE_TOKEN)) {
                JSONObject resourceParameters = HttpRequestUtil.getResourceParametersJSON(request);
                for (String name : resourceParameters.keySet()) {
                    Object value = resourceParameters.get(name);
                    params.put(name, value instanceof String ? (String) value : String.valueOf(value));
                }
            } else {
                // Special treatment for handling '+' in the URL
                String value = rawKeyValues.get(key);
                value = value.replace("+", "\\PLUS\\");
                value = URLDecoder.decode(value, "UTF-8");
                value = value.replace("\\PLUS\\", "+");
                params.put(key, value);
            }
        }

	    String filePath = getStringParam(params, REQ_PARAM_FILE, null);
        String reportType = getStringParam(params, REQ_PARAM_REPORT_TYPE, null);
        String type = getStringParam(params, REQ_PARAM_TYPE, null);
        String action = getStringParam(params, REQ_PARAM_ACTION, "default");
        String nodeGUID = getStringParam(params, REQ_PARAM_NODE_GUID, null);
        Boolean embeddedPDF = getBooleanParam(params, REQ_PARAM_EMBEDDED, false);
        Boolean hiRes = getBooleanParam(params, REQ_PARAM_HIGH_RESOLUTION, false);
        boolean isFileCheck = getStringParam(params, REQ_PARAM_FILE_EXIST_CHECK, StringUtils.EMPTY).equals("true");
        long domainId = getLongParam(params, REQ_PARAM_DOMAIN_ID, -1L);
        long documentHistoryId = getLongParam(params, REQ_PARAM_DOCUMENT_HISTORY, -1L);

        if (request.getParameterMap().containsKey(REQ_PARAM_FILE)) {
            LogUtil.getLog(BinaryStreamController.class).warn("Cleartext file path request access via: " + MessageFormat.format("{0}?{1}", request.getRequestURI(), request.getQueryString()));
        }

        SessionHolder mainReqSessionHolder = null;

        // CONNECTED: External URL (remote or local proof retrieval)
        String orderGUID = getStringParam(params, CommunicationPortalGatewayController.REQ_PARAM_ORDER_GUID, null);
        if ( orderGUID != null ) {
            Communication communication = Communication.findByGuid(orderGUID);
            if ( communication == null ) {
                HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Invalid order GUID ", request, response);
                return null;
            }

            CommunicationProof proof	= communication.getLatestProof();
            if ( proof == null ) {
                HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "No proof exists ", request, response);
                return null;
            }

            Document document = proof.getDocument();
            if ( document.isCommunicationWebServiceCompositionResultsEnabled() )
                type = TYPE_REMOTE_COMMUNICATION_PDF;
            else
                filePath = proof.getOutputPath();
        }

        try {

            if (domainId != -1L) {
                Branch domain = Branch.findById(domainId);
                mainReqSessionHolder = HibernateUtil.getManager().openTemporarySession(domain.getExchangeNode().getSchemaName());
            }

            // Handle bad parameter request
            if ((filePath == null || filePath.isEmpty()) && type != null
                    && !type.equalsIgnoreCase(TYPE_COMPOSITION_FILE)
                    && !type.equalsIgnoreCase(TYPE_FONT_TTF)
                    && !type.equalsIgnoreCase(TYPE_FONT_TTF_BOLD)
                    && !type.equalsIgnoreCase(TYPE_FONT_TTF_ITALIC)
                    && !type.equalsIgnoreCase(TYPE_FONT_TTF_BOLD_ITALIC)
                    && !type.equalsIgnoreCase(TYPE_LOOKUP_TABLE_DATA)
                    && !type.equalsIgnoreCase(TYPE_METADATA)
                    && !type.equalsIgnoreCase(TYPE_METADATA_UPLOAD_FILE)
                    && !type.equalsIgnoreCase(TYPE_ORDER_ENTRY_UPLOAD_FILE)
                    && !type.equalsIgnoreCase(TYPE_REMOTE_COMMUNICATION_PDF)
                    && !type.equalsIgnoreCase(TYPE_REMOTE_COMMUNICATION_PDFV)
                    && !type.equalsIgnoreCase(TYPE_BUNDLE_FILE)
                    && !type.equalsIgnoreCase(TYPE_BACKEND_COMPONENT)
                    && !type.equalsIgnoreCase(TYPE_PUBLIC_KEY)
                    && !type.equalsIgnoreCase(TYPE_POD_STATISTIC_REPORTS)
                    && !type.equalsIgnoreCase(TYPE_IMPORTED_XML)
                    && !type.equalsIgnoreCase(TYPE_DOCUMENT_ORIGINATION_LOG)
                    && !type.equalsIgnoreCase(TYPE_RENDERING_MANAGER_DEBUG_FILE)
                    && !type.equalsIgnoreCase(TYPE_TOUCHPOINT_EXPORT_XSD)
            ) {
                HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Requested resource file path unspecified", request, response);
                return null;
            }

            if ( type != null && type.equalsIgnoreCase(TYPE_METADATA) ) {

                Long documentId	= getLongParam(params, REQ_PARAM_TOUCHPOINT_ID, -1L);

                if ( documentId > 0 ) {

                    Document document = Document.findById(documentId);
                    MetadataReport metadataReport = new MetadataReport();
                    metadataReport.setRequestor(UserUtil.getPrincipalUser());
                    metadataReport.generateMetadataXML(document);
                    metadataReport.writeToXMLFile();

                    try {
                        response.setContentType("text/xml");
                        response.setHeader( "Content-disposition", "attachment; filename=\"" +
                                MetadataReport.getReportXmlFileName( document.getName(), UserUtil.getPrincipalUser().getId() ) +
                                "\"" );
                        PrintWriter out = response.getWriter();
                        String fileContent = FileUtil.fileToString(MetadataReport.getReportXmlFilePath(document.getName(), UserUtil.getPrincipalUser().getId()));
                        out.write(fileContent);
                        out.close();
                    } catch(Exception e) {
                        log.error("Error: ", e);
                    }

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown touchpoint", request, response);
                    return null;
                }

            } else if ( type != null && type.equals(AsyncFileUploadController.TYPE_SANDBOX_FILE) ) {

                // START: Database stream
                SandboxFile sandboxFile = SandboxFile.findById(Long.valueOf(filePath));
                if (sandboxFile != null) {

                    byte[] bSandboxFile = sandboxFile.getFileContent();

                    if ( embeddedPDF ) {
                        response.setContentType("application/pdf");

                        try (PDDocument document = Loader.loadPDF(bSandboxFile)) {
                            stripPdfEmbeddedJavascript(document);

                            ByteArrayOutputStream bos = new ByteArrayOutputStream(bSandboxFile.length);
                            document.save(bos);

                            bSandboxFile = bos.toByteArray();
                        }

                    } else {
                        response.setContentType( sandboxFile.getContentType() );
                        response.setHeader( "Content-disposition", "attachment; filename=\"" + sandboxFile.getFileName() + "\"" );
                    }
                    response.setContentLength( (int) bSandboxFile.length );

                    outputDatabasePersistedFile(response, action, bSandboxFile, filePath, sandboxFile.getFileName(), isFileCheck);

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown sandbox file", request, response);
                    return null;
                }
                // End: Database stream

            } else if ( type != null && (type.equals(TYPE_FONT_TTF)  || type.equals(TYPE_FONT_TTF_BOLD)  || type.equals(TYPE_FONT_TTF_ITALIC) ||  type.equals(TYPE_FONT_TTF_BOLD_ITALIC)) ) {
                Long fontId 	= getLongParam(params, REQ_PARAM_FONT_ID, -1L);
                Long debugOrderId = getLongParam(params, "debugOrderId", -1L);
                Long bundleId = getLongParam(params, "bundleId", -1L);
                boolean isPullingFontsEnabled = FeatureFlag.isEnabled(FeatureFlag.Features.ConnectedPullFonts, request);
                //retrieve font file from bundle
                if (isPullingFontsEnabled ) {
                    String fontFile = getStringParam(params, REQ_PARAM_FILE, null);
                    if (bundleId != -1L && debugOrderId != -1L) {
                        //debug order
                        if (fontFile != null) {
                            sendFontFileResponse(request, response, debugOrderId, fontFile, true);
                        } else {
                            HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Cannot download font file: repository not found", request, response);
                        }
                    } else if (bundleId != -1L) {
                        if (fontFile != null) {
                            sendFontFileResponse(request, response, bundleId, fontFile, false);
                        } else {
                            HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Cannot download font file: debug order not found", request, response);
                        }
                    }
                } else {
                    // START: Database stream
                    TextStyleFont font = TextStyleFont.findById(fontId);
                    if ( font != null ) {

                        DatabaseFile databaseFile = null;
                        if ( type.equals(TYPE_FONT_TTF) )
                            databaseFile = font.getTtfFile();
                        else if ( type.equals(TYPE_FONT_TTF_BOLD) )
                            databaseFile = font.getTtfBoldFile();
                        else if ( type.equals(TYPE_FONT_TTF_ITALIC) )
                            databaseFile = font.getTtfItalicFile();
                        else if ( type.equals(TYPE_FONT_TTF_BOLD_ITALIC) )
                            databaseFile = font.getTtfBoldItalicFile();

                        sendDatabaseFileResponse(response, filePath, action, databaseFile, isFileCheck);

                    } else {
                        HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Unknown font file", request, response);
                        return null;
                    }
                    // End: Database stream
                }
            } else if ( type != null && type.equals(TYPE_CONTENT_IMAGE) ) {

                // START: Database stream
                DatabaseFile databaseFile = DatabaseFile.findById(Long.valueOf(filePath));
                if (databaseFile == null) {

                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Unknown sandbox file", request, response);
                    return null;

                }

                sendDatabaseFileResponse(response, filePath, action, databaseFile, isFileCheck);
                // End: Database stream

            } else if ( type != null && type.equals(TYPE_LICENCE_XML) ) {
                Licence licence = Licence.findById(Long.valueOf(filePath));
                if (licence != null) {
                    if (action.equalsIgnoreCase(ACTION_SAVE_FILE)) {
                        try {
                            response.setContentType("text/xml");
                            response.setHeader("Content-disposition", "attachment; filename=\"licence.xml\"");
                            PrintWriter out = response.getWriter();
                            out.write(licence.getXml());
                            out.close();
                        } catch (Exception e) {
                            log.error("Error: ", e);
                        }
                    }
                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown licence file", request, response);
                    return null;
                }
            } else if (type != null && type.equals(TYPE_REMOTE_COMMUNICATION_PDFV)) {
                String communicationProofVersionId 	= getStringParam(params, REQ_PARAM_COMMUNICATION_PROOF_VID, null);
                if (communicationProofVersionId == null || communicationProofVersionId.isEmpty()) {
                    return null;
                }
                Long communicationProofId 	= getLongParam(params, REQ_PARAM_COMMUNICATION_PROOF_ID, -1L);
                CommunicationProof proof = CommunicationProof.findById(communicationProofId);
                if (proof == null) {
                    return null;
                }

                Document document = proof.getDocument();

                SandboxFile sandboxFile = CommunicationWSClient.getPdfFileVersion(document.getCommunicationCompositionResultsWebService().getUrl(),
                        document.getCommunicationCompositionResultsWebService().getUsername(),
                        document.getCommunicationCompositionResultsWebService().getPassword(),
                        communicationProofVersionId);
                if (sandboxFile == null) {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Invalid response from content storage.", request, response);
                    return null;
                }

                response.setContentType("application/pdf");
                copyFileToResponse(response, sandboxFile);

            } else if ( type != null && type.equals(TYPE_REMOTE_COMMUNICATION_PDF) ) {

                Long communicationProofId 	= getLongParam(params, REQ_PARAM_COMMUNICATION_PROOF_ID, -1L);
                CommunicationProof proof 	= null;
                if ( communicationProofId != -1 )
                    proof 	= CommunicationProof.findById(communicationProofId);

                if ( orderGUID != null ) {
                    Communication communication = Communication.findByGuid(orderGUID);
                    proof	= communication.getLatestProof();
                }

                Document document = proof.getDocument();

                if ( !document.isCommunicationWebServiceCompositionResultsEnabled() )
                    return null;

                SandboxFile sandboxFile = null;
                if (document.isCommunicationUseBeta()) {
                    String proofJobUUID = SupportingDataUtil.getJobUUID(proof.getCommunication(), proof.getIsPreProof(), String.valueOf(proof.getId()));
                    sandboxFile = CommunicationWSClient.getPdfFile(document.getCommunicationCompositionResultsWebService().getUrl(),
                            document.getCommunicationCompositionResultsWebService().getUsername(),
                            document.getCommunicationCompositionResultsWebService().getPassword(),
                            proofJobUUID);
                } else if (proof.getDeliveryEvent() != null) {
                    sandboxFile = CommunicationWSClient.getPdfFile(document.getCommunicationCompositionResultsWebService().getUrl(),
                            document.getCommunicationCompositionResultsWebService().getUsername(),
                            document.getCommunicationCompositionResultsWebService().getPassword(),
                            proof.getDeliveryEvent());
                }

                if (sandboxFile == null) {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Invalid response from DEWS API", request, response);
                    return null;
                }
                if ( sandboxFile.getFileName().endsWith(".html") ) {
                    response.setContentType("text/html");
                } else {
                    response.setContentType("application/pdf");
                }


                copyFileToResponse(response, sandboxFile);


            } else if ( type != null && type.equals(TYPE_LOOKUP_TABLE_DATA)) {

                Long lookupTableId 	= getLongParam(params, REQ_PARAM_LOOKUP_TABLE_ID, -1L);

                // START: Database stream
                LookupTableInstance instance = LookupTableInstance.findById(lookupTableId);
                if ( instance != null ) {
                    DatabaseFile databaseFile = null;
                    databaseFile = instance.getLookupFile();

                    sendDatabaseFileResponse(response, filePath, action, databaseFile, isFileCheck);

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown lookup table file", request, response);
                    return null;
                }
                // End: Database stream

            } else if ( type != null && type.equals(TYPE_SFTPREPO_FILE) ) {
                SftpRepoClient sftp = new SftpRepoClient();
                if ( sftp.isValid() )
                {
                    int s = filePath.lastIndexOf('/');
                    String folder = filePath.substring(0, s);
                    String filename = filePath.substring( s + 1 );
                    sftp.setFolder(folder);

                    byte[] fileContent = sftp.downloadFile( filePath, 2, 1 );
                    if ( fileContent == null )
                    {
                        HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown SFTP file request", request, response);
                        return null;
                    }

//				response.setContentType("image/" + FileUtil.getFileExtension(filename) );
//				response.setContentLength( fileContent.length );
//				response.setHeader( "Content-disposition", "attachment; filename=\"" + filename + "\"" );

                    InputStream is = new ByteArrayInputStream(fileContent);
                    BufferedInputStream in = null;
                    ServletOutputStream out = response.getOutputStream();

                    try {
                        if ( filename.toLowerCase().endsWith(".tiff") || filename.toLowerCase().endsWith(".tif") ) {
                            Image ima = ImageIO.read(is);
                            RenderedImage ri = (RenderedImage) ima;
                            ImageIO.write(ri, "GIF", response.getOutputStream());
                        } else {
                            in = new BufferedInputStream(is);
                            byte[] buffer = new byte[524288];
                            int numberOfBytesRead = in.read(buffer);

                            while (numberOfBytesRead != -1) {
                                out.write(buffer, 0, numberOfBytesRead);
                                out.flush();
                                numberOfBytesRead = in.read(buffer);
                            }
                        }
                    } catch (IOException e) {
                        log.error("Error downloading SFTP file: " + filePath + " output error: ", e);
                    } finally {
                        if ( in != null )
                            in.close();
                    }
                    is.close();

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown SFTP request", request, response);
                    return null;
                }

            } else if ( type != null && type.equals(TYPE_METADATA_UPLOAD_FILE)) {

                Long metadataItemId 	= getLongParam(params, REQ_PARAM_METADATA_ITEM_ID, -1L);

                // START: Database stream
                MetadataFormItem metadataFormItem = MetadataFormItem.findById(metadataItemId);
                if ( metadataFormItem != null ) {
                    DatabaseFile databaseFile = null;
                    databaseFile = metadataFormItem.getUploadedFile();

                    sendDatabaseFileResponse(response, filePath, action, databaseFile, isFileCheck);

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown metadata upload file", request, response);
                    return null;
                }
                // End: Database stream

            } else if ( type != null && type.equals(TYPE_ORDER_ENTRY_UPLOAD_FILE)) {

                Long orderEntryId 	= getLongParam(params, REQ_PARAM_ORDER_ENTRY_ITEM_ID, -1L);

                // START: Database stream
                CommunicationOrderEntryItem orderEntryItem = CommunicationOrderEntryItem.findById(orderEntryId);
                if ( orderEntryItem != null ) {
                    DatabaseFile databaseFile = null;
                    databaseFile = orderEntryItem.getUploadedFile();

                    sendDatabaseFileResponse(response, filePath, action, databaseFile, isFileCheck);

                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Unknown order entry upload file", request, response);
                    return null;
                }
                // End: Database stream

            } else if (type != null && type.equals(TYPE_COMPOSITION_FILE)) {
                long fileId = getLongParam(params, REQ_PARAM_COMPOSITION_FILESET, Long.MIN_VALUE);
                String syncId = getStringParam(params, REQ_PARAM_FILE_UPLOAD_KEY, StringUtils.EMPTY);
                String requestFile = getStringParam(params, REQ_PARAM_FILE, StringUtils.EMPTY);

                CompositionFileSet fileSet = fileId != Long.MIN_VALUE ? CompositionFileSet.findById(fileId) : CompositionFileSet.findBySyncKey(syncId);

                if (fileSet != null && !requestFile.isEmpty()) {
                    if (fileSet.getTemplateFileName() != null && fileSet.getTemplateFileName().equals(requestFile)) {
                        sendFileResponse(response, fileSet.getTemplateFile(), isFileCheck, nodeGUID, false);
                    } else if (fileSet.getCompositionConfigurationFileName() != null && fileSet.getCompositionConfigurationFileName().equals(requestFile)) {
                        sendFileResponse(response, fileSet.getCompositionConfigurationFile(), isFileCheck, nodeGUID, false);
                    } else {
                        for (DatabaseFile file : fileSet.getAdditionalFiles()) {
                            if (file.getFileName().equals(requestFile)) {
                                sendDatabaseFileResponse(response, filePath, action, file, isFileCheck);
                                break;
                            }
                        }
                    }
                }

            } else if (type != null && type.equals(TYPE_BUNDLE_FILE)) {
                long jobId = getLongParam(params, REQ_PARAM_JOB_ID, Long.MIN_VALUE);
                String bundleType = getStringParam(params, REQ_PARAM_BUNDLE_TYPE, StringUtils.EMPTY);

                if (jobId != Long.MIN_VALUE && !bundleType.isEmpty()) {

                    String incomingSuccessDir = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_IncomingSuccessDir);
                    String branchId = Node.getCurrentBranchName();
                    String nodeId = Node.getCurrentNodeName();
                    String suffix = "";

                    switch (bundleType) {
                        case "client":
                            suffix = JobZipFile.CLIENT_BUNDLE_ZIP_SUFIX;
                            break;
                        case "server":
                            suffix = JobZipFile.SERVER_BUNDLE_ZIP_SUFIX;
                            break;
                        case "diagnostic":
                            suffix = JobZipFile.DIAGNOSTIC_BUNDLE_ZIP_SUFIX;
                            break;
                    }

                    String path = JobPackerUtil.lookForJob(jobId, incomingSuccessDir, branchId, nodeId, suffix);
                    if (!isFileCheck && path == null) {
                        throw new FileNotFoundException(MessageFormat.format("Unable to locate {0} bundle files for requested job id: {1}", bundleType, Long.toString(jobId)));
                    }

                    File requestedBundleFile = path != null ? new File(path) : null;

                    if (!isFileCheck && !requestedBundleFile.exists()) {
                        throw new FileNotFoundException(MessageFormat.format("Unable to locate {0} bundle files for requested job id: {1}", bundleType, Long.toString(jobId)));
                    }

                    sendFileResponse(response, requestedBundleFile, isFileCheck, nodeGUID, false);
                }

            } else if (type != null && type.equals(TYPE_BACKEND_COMPONENT)){

                boolean hasJobCenterPerm = UserUtil.isPermissionGranted(Permission.ROLE_JOB_CENTER_SETUP);
                //MessagePointCSRFFilter csrfFilter = new MessagePointCSRFFilter();
                //boolean isValidTk = csrfFilter.validToken(request, response, params.get(REQ_PARAM_RESOURCE_TOKEN), request.getSession(true)) == 1;
                boolean isValidTk = true; // TMP Block to restore OPS-8285

                if (!hasJobCenterPerm || !isValidTk) {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Rejected file request", request, response);
                    return null;
                }

                String compType = getStringParam(params, REQ_PARAM_BACKEND_COMPONENT, null);

                if (compType == null) {
                    throw new MissingServletRequestParameterException(REQ_PARAM_BACKEND_COMPONENT, "String");
                }

                File file = null;


                if (compType.equalsIgnoreCase("declient")) {
                    file = new File(MessageFormat.format("{0}/DEClient/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("deserver")) {
                    file = new File(MessageFormat.format("{0}/DEServer/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("dedigital")) {
                    file = new File(MessageFormat.format("{0}/DEDigital/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("dews")) {
                    file = new File(MessageFormat.format("{0}/DEWS/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("mpcomposer")) {
                    file = new File(MessageFormat.format("{0}/MPComposer/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("mpcomp")) {
                    file = new File(MessageFormat.format("{0}/MPComp/{1}/latestBuild/{2}", BackendComponentDownloadsController.getReleasedBuildsPath(), MessagePointStartUp.getMajorBuildRevision(), filePath));
                } else if (compType.equalsIgnoreCase("pmgr")) {
                    file = new File(MessageFormat.format("{0}/PMGR/latestBuild/{1}", BackendComponentDownloadsController.getReleasedBuildsPath(), filePath));
                }

                if (file != null && file.exists()) {
                    sendFileResponse(response, file, isFileCheck, nodeGUID, false);
                } else {
                    //todo: if file == null then NPE will be produced by next statement
                    throw new FileNotFoundException(MessageFormat.format("Requested file not found: {0}", file.getAbsolutePath()));
                }

            } else if ( type != null && type.equals(TYPE_PUBLIC_KEY) ) {

                Long serverId = getLongParam(params, REQ_PARAM_DE_SERVER_ID, -1L);

                if (serverId != -1) {
                    DEServer server = DEServer.findById(serverId);

                    if (server.getPublicSshKeyFile() != null) {
                        sendDatabaseFileResponse(response, server.getPublicSshKeyFile().getFileName(), action, server.getPublicSshKeyFile(), isFileCheck);
                    } else {
                        HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Requested public key not found", request, response);
                    }
                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Requested bundle delivery server not found", request, response);
                }
            } else if (type != null && type.equals(TYPE_DOWNLOAD_FILE_FROM_FILEROOT)) {
                sendFileResponse(response, new File(filePath), isFileCheck, nodeGUID, true);
            } else if (type != null && type.equals(TYPE_POD_STATISTIC_REPORTS)){
                File podReports = new File(StatisticsUtils.generatePodStatisticsZip());
                if(podReports.exists()){
                    sendFileResponse(response, podReports, isFileCheck, nodeGUID, false);
                }
            } else if (type != null && type.equals(TYPE_IMPORTED_XML)) {
                DocumentHistory documentHistory = DocumentHistory.findById(documentHistoryId);
                if(documentHistory != null) {
                    String xmlOriginFileName = documentHistory.getImportFileName();
                    String xmlFilePath = documentHistory.getImportedXmlFilePathOnServer();
                    File xmlFile = new File(xmlFilePath);
                    if(xmlFile.exists()) {
                        response.setContentType("application/xml");
                        response.setHeader("Content-length", Long.toString(xmlFile.length()));
                        response.setHeader( "Content-disposition", "attachment; filename=\"" + xmlOriginFileName + "\"" );

                        try (FileInputStream fileInputStream = new FileInputStream(xmlFile)) {
                            StreamUtils.copy(fileInputStream, response.getOutputStream());
                        } finally {
                            response.getOutputStream().flush();
                        }

                    } else {
                        HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Requested file not found", request, response);
                    }
                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Requested file not found", request, response);
                }
            } else if (type != null && type.equals(TYPE_DOCUMENT_ORIGINATION_LOG)) {
                DocumentHistory documentHistory = DocumentHistory.findById(documentHistoryId);
                if(documentHistory != null) {
                    String logFilePath = documentHistory.getLogFilePathOnServer();
                    File logFile = new File(logFilePath);
                    if(logFile.exists()) {
                        sendFileResponse(response, logFile, isFileCheck, nodeGUID, false);
                    } else {
                        HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Requested file not found", request, response);
                    }
                } else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Requested file not found", request, response);
                }
            }
            else if (type != null && type.equals(TYPE_RENDERING_MANAGER_DEBUG_FILE)) {
                long proofId = getLongParam(params, "proof_id", -1L);

                String path =null;
                CommunicationProof proof = CommunicationProof.findById(proofId);
                if (proof == null) {
                    return null;
                }

                Long jobId = (proof.getDocument() != null && proof.getDocument().isCommunicationUseBeta() && RenderingManagerUtil.isNoJob(RenderingManagerUtil.CONNECTED_NOJOB_FOR_PROOF) ) ?
                        proofId : proof.getDeliveryEvent().getJob().getId();
                String diagnosticPath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir) + File.separator +
                        jobId + File.separator + "1";
                if (diagnosticPath == null) {
                    log.warn(String.format(" Diagnostic path does not exists %s for job %d", diagnosticPath, jobId));
                    throw new FileNotFoundException(MessageFormat.format("Unable to locate rendering manager debug file requested for job id: {0}", Long.toString(jobId)));
                }
                log.info(" Using debug files from source " + diagnosticPath);
                try {
                    ConnectedTouchpointRenderingManagerProvider provider = new ConnectedTouchpointRenderingManagerDewsProvider();

                    path = MessagepointMultiTenantConnectionProvider.getPodMasterCode() +
                            "_job" + String.valueOf(jobId) + "_" + Node.getCurrentBranchName() +
                            "_" + Node.getCurrentNodeName() + "_rendering_manager_debug.zip";
                    log.info("Starting to create debug zip file for " + path);
                    FileOutputStream fos = new FileOutputStream(path);
                    ZipOutputStream zipOut = new ZipOutputStream(fos);

                    ZipEntry zipEntry = new ZipEntry("selections.json");
                    zipOut.putNextEntry(zipEntry);
                    byte[] data = provider.getSelectionsData(proofId).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    zipEntry = new ZipEntry("VariableValues.json");
                    zipOut.putNextEntry(zipEntry);
                    data = provider.getVariablesData(proofId).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    zipEntry = new ZipEntry("style.json");
                    zipOut.putNextEntry(zipEntry);
                    data = provider.getStyleData(proofId).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    zipEntry = new ZipEntry("RecipientContents.json");
                    zipOut.putNextEntry(zipEntry);
                    data = provider.getContentData(proofId).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    zipEntry = new ZipEntry("allimages.json");
                    zipOut.putNextEntry(zipEntry);
                    List<ConnectedImageData> images = provider.getImagesData(proofId, Collections.emptyList(), "large");
                    data = ObjectMapperUtil.writeValueAsString(images).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    data = SupportingDataUtil.getSupportingData(proof.getCommunication().getId());
                    if (data != null) {
                        zipEntry = new ZipEntry("supportingData.json");
                        zipOut.putNextEntry(zipEntry);
                        zipOut.write(data, 0, data.length);
                        zipOut.closeEntry();
                    }

                    if (FeatureFlag.isEnabled(FeatureFlag.Features.ConnectedPullFonts, request)) {
                        zipEntry = new ZipEntry("fonts.zip");
                        zipOut.putNextEntry(zipEntry);
                        byte[] fonts = provider.getFonts(proofId);
                        if (fonts != null) {
                            zipOut.write(fonts, 0, fonts.length);
                            zipOut.closeEntry();
                        }
                    }

                    zipEntry = new ZipEntry("interview.json");
                    zipOut.putNextEntry(zipEntry);
                    data = provider.getRefData(proofId).getBytes();
                    zipOut.write(data, 0, data.length);
                    zipOut.closeEntry();

                    zipOut.close();
                    log.info("Added files to debug zip!");

                    zipOut.close();
                    fos.close();
                } catch (IOException e) {
                    throw new FileNotFoundException(MessageFormat.format("Unable to locate rendering manager debug file requested for proof id: {0}", Long.toString(proofId)));

                }
                if (!isFileCheck && path == null) {
                    throw new FileNotFoundException(MessageFormat.format("Unable to locate rendering manager debug file requested for proof id: {0}", Long.toString(proofId)));
                }

                File requestedBundleFile = path != null ? new File(path) : null;

                if (!isFileCheck && !requestedBundleFile.exists()) {
                    throw new FileNotFoundException(MessageFormat.format("Unable to locate rendering manager debug file requested for proof id: {0}", Long.toString(proofId)));
                }

                sendFileResponse(response, requestedBundleFile, isFileCheck, nodeGUID, false);
            }
            else if (type != null && type.equals(TYPE_TOUCHPOINT_EXPORT_XSD)) {
                InputStream tpiFileStream = getClass().getClassLoader().getResourceAsStream(TPI_XSD_PATH);
                if (tpiFileStream != null) {
                    byte[] tpiData = StreamUtils.copyToByteArray(tpiFileStream);
                    tpiFileStream.close();

                    String revision = MessagePointStartUp.getBuildRevision();
                    revision = revision.length() <= 19 ? revision : revision.substring(0, 19);

                    {
                        String tpiXml = new String(tpiData);
                        org.jsoup.nodes.Document document = Jsoup.parse(tpiXml, Parser.xmlParser());
                        Elements schemaTags = document.select("xs|schema");
                        if (schemaTags != null) {
                            for (Element currentElement : schemaTags) {
                                if(currentElement.tagName().equalsIgnoreCase("xs:schema")) {
                                    currentElement.attr("version", revision);
                                }
                            }
                        }
                        tpiXml = ContentObjectContentUtil.formatBody(document, false);
                        tpiData = tpiXml.getBytes();
                    }

                    String defaultFilename = "Touchpoint_XML_Schema_Definition_" + revision + ".xsd";
                    String filename = getStringParam(params, REQ_PARAM_FILENAME, defaultFilename);

                    if(filename == null || filename.isBlank()) filename = defaultFilename;

                    response.setContentType("application/xml");
                    response.setHeader("Content-length", Long.toString(tpiData.length));
                    response.setHeader("Content-disposition", "attachment; filename=\"" + filename + "\"");

                    try {
                        StreamUtils.copy(tpiData, response.getOutputStream());
                    } finally {
                        response.getOutputStream().flush();
                    }
                }
                else {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "Touchpoint export XML schema definition not found", request, response);
                }
            }
            else {

                // START: File system stream
                if ( downloadFromWebRoot)
                    if( (reportType == null && type == null) || type != null && type.equals(TYPE_LOCAL_AUDIT_REPORT))		// Except from the the TP/ST/IL/Project audit report
                        filePath = ApplicationUtil.getRootPath() + filePath;

                File file = new File(filePath);
                boolean isDirectory = file.isDirectory();
                boolean canReadFile = file.canRead();

                Node node = null;
                SessionHolder mainSessionHolder = null;
                boolean isFileAccessAllowed = false;

                try {
                    if (nodeGUID != null)
                    {
                        node = Node.findByGuid(nodeGUID);
                        if (node != null)
                            mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
                    }
                    // This is for TPM-3150 Path Traversal Vulnerability fix
                    // isFileAccessAllowed(file) has to be called with proper schema in hibernate session
                    isFileAccessAllowed = isFileAccessAllowed(file, !request.getRequestURI().toLowerCase().endsWith("image.form"));
                } finally {
                    if (node != null) {
                        HibernateUtil.getManager().restoreSession(mainSessionHolder);

                    }
                }

                if (isDirectory || !canReadFile || !isFileAccessAllowed) {
                    HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "The file " + file.getName() + " is not available", request, response);
                } else {

                    if ( !action.equalsIgnoreCase(ACTION_SAVE_FILE) && (filePath.toLowerCase().endsWith(".tiff") || filePath.toLowerCase().endsWith(".tif")) ) {
                        InputStream is = null;
                        try {
                            is = new FileInputStream(file);
                            Image ima = ImageIO.read(is);
                            RenderedImage ri = (RenderedImage) ima;
                            ImageIO.write(ri, "GIF", response.getOutputStream());
                            if (is != null) {
                                is.close();
                            }
                        } catch (FileNotFoundException e) {
                            log.error(filePath + " not found!", e);
                            if(is != null)
                                is.close();
                        } catch (IOException e) {
                            log.error("image: " + filePath + " parsing error!", e);
                            if(is != null)
                                is.close();
                        } finally {
                            if(is != null)
                                is.close();
                        }
                    } else {
                        if ( type != null && type.equals(TYPE_RENDERED_PDF) ) {

                            PdfDecoderServer decode_pdf = new PdfDecoderServer(true);
                            FontMappings.setFontReplacements();
                            FontMappings.addFontFile("ZapfDingbats.ttf", ApplicationUtil.getRootPath() + "includes/fonts/pdf-embedded/");

                            try {
                                decode_pdf.openPdfFile(filePath); //file
                                decode_pdf.setExtractionMode(0, 1f); //do not save images

                                BufferedImage img = null;
                                if ( hiRes ) {
                                    Map mapValues = new HashMap();
                                    mapValues.put(JPedalSettings.EXTRACT_AT_BEST_QUALITY_MAXSCALING, 2);
                                    mapValues.put(JPedalSettings.EXTRACT_AT_PAGE_SIZE, new String[]{"2000","1600"});
                                    mapValues.put(JPedalSettings.PAGE_SIZE_OVERRIDES_IMAGE, Boolean.TRUE);
                                    PdfDecoderServer.modifyJPedalParameters(mapValues);
                                    img = decode_pdf.getPageAsHiRes(1);
                                } else {
                                    img = decode_pdf.getPageAsImage(1);
                                }

                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                ImageIO.write(img, "jpg", baos);
                                byte[] data = baos.toByteArray();

                                response.setHeader("Content-disposition", "attachment; filename=\"pdf_image.jpg\"");
                                response.setContentType("image/jpeg");
                                response.setContentLength(data.length);

                                ServletOutputStream out = response.getOutputStream();
                                out.write(data);
                                out.flush();

                                /**close the pdf file*/
                                decode_pdf.closePdfFile();

                            } catch (PdfException e) {
                                log.error("Error: " + e.getMessage());
                                e.printStackTrace();
                            }

                        } else {

                            // File Name
                            String filename = file.getName();
                            if ( filename.indexOf("job") != -1 ) {
                                for ( String currentSubStr: filename.split("_") ) {
                                    if ( currentSubStr.indexOf("job") != -1 ) {
                                        try {
                                            long jobId = Long.parseLong(currentSubStr.replace("job", ""));
                                            DeliveryEvent deliveryEvent = DeliveryEvent.findByJob(jobId);
                                            if ( deliveryEvent != null ) {
                                                if ( deliveryEvent.getItem() instanceof TestScenario ) {
                                                    TestScenario testScenario = (TestScenario)deliveryEvent.getItem();
                                                    filename = testScenario.getPdfFileName();
                                                }
                                            }
                                        } catch (NumberFormatException nfe) {
                                            // Invalid job ID: Continue
                                        }
                                    }
                                }
                            }

                            BufferedInputStream in = null;
                            int contentLength = -1;

                            if ( embeddedPDF ) {
                                response.setContentType("application/pdf");
                                response.setHeader("Content-disposition", "filename=\"" + filename + "\"");

                                try (PDDocument document = Loader.loadPDF(file)) {
                                    stripPdfEmbeddedJavascript(document);

                                    ByteArrayOutputStream bos = new ByteArrayOutputStream((int) file.length());
                                    document.save(bos);

                                    byte[] embeddedPdfBytes = bos.toByteArray();
                                    contentLength = embeddedPdfBytes.length;
                                    in = new BufferedInputStream(new ByteArrayInputStream(embeddedPdfBytes));
                                }
                            } else if (filename.endsWith("svg")) {
                                response.setContentType("image/svg+xml");
                            } else if (filename.endsWith(".xlsx")) {
                                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                                response.setHeader("Content-disposition", "filename=\"" + filename + "\"");
                            } else {
                                response.setContentType(getContentType(file));
                                response.setHeader("Content-disposition", "attachment; filename=\"" + filename + "\"");
                            }

                            if(contentLength == -1) {
                                long fileLength = file.length();

                                if (fileLength <= Integer.MAX_VALUE) {
                                    response.setContentLength((int) fileLength);
                                } else {
                                    response.setHeader("Content-Length", Long.toString(fileLength));
                                }
                            } else {
                                response.setContentLength(contentLength);
                            }

                            ServletOutputStream out = response.getOutputStream();

                            try {

                                if (in == null) {
                                    in = new BufferedInputStream(Files.newInputStream(file.toPath()));
                                }

                                byte[] buffer = new byte[512];
                                int numberOfBytesRead = in.read(buffer);

                                while (numberOfBytesRead != -1) {
                                    out.write(buffer, 0, numberOfBytesRead);
                                    out.flush();
                                    numberOfBytesRead = in.read(buffer);
                                }
                            } catch (IOException e) {
                                //log.error("image: " + filePath + " output error!",e);
                            } finally {
                                if ( in != null )
                                    in.close();
                                if( type != null && type.equals(TYPE_TEMPLATE) && filePath.toLowerCase().endsWith(".zip") ){
                                    // Audit (Touchpoint Template downloaded)
                                    AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT,
                                            UserUtil.getCurrentTouchpointContext()!= null?UserUtil.getCurrentTouchpointContext().getName():"",
                                            UserUtil.getCurrentTouchpointContext()!= null?UserUtil.getCurrentTouchpointContext().getId():null, AuditActionType.ID_CHANGES,
                                            AuditMetadataBuilder.forSimpleAuditMessage(ApplicationUtil.getMessage("page.text.touchpoint.template.downloaded") + ": " + file.getName(), null, null));
                                }
                            }

                        }
                    }
                }
                // END: File system stream

            }
        } catch (FileNotFoundException ex) {
            log.error("Error:", ex);
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
        catch (Exception ex) {
            log.error("Error:", ex);
        }
        finally {
            if (mainReqSessionHolder != null) {
                if (mainReqSessionHolder != null) {
                    HibernateUtil.getManager().restoreSession(mainReqSessionHolder);
                }
            }
        }

        return null;
    }

    private void stripPdfEmbeddedJavascript(PDDocument document) throws IOException {

        if (document == null || document.getDocumentCatalog() == null) {
            return;
        }

        if (document.getDocumentCatalog().getOpenAction() instanceof PDActionJavaScript) {
            document.getDocumentCatalog().setOpenAction(null);
        }

        if (document.getDocumentCatalog().getNames() != null && document.getDocumentCatalog().getNames().getJavaScript() != null) {
            document.getDocumentCatalog().getNames().setJavascript(null);
        }
    }

    private void sendFileResponse(HttpServletResponse response, File downloadFile, boolean isFileCheck, String nodeGUID, boolean checkFileAccess) throws IOException {

        boolean isFileAccessAllowed = false;

        // We need to check File Access in case that the path can be entered / modified by a user
        if (checkFileAccess) {
            Node node = null;
            SessionHolder mainSessionHolder = null;
            try {
                if (nodeGUID != null) {
                    node = Node.findByGuid(nodeGUID);
                    if (node != null)
                        mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
                }
                // This is for TPM-3150 Path Traversal Vulnerability fix
                // isFileAccessAllowed(file) has to be called with proper schema in hibernate session
                isFileAccessAllowed = isFileAccessAllowed(downloadFile, true);
            } finally {
                if (node != null) {
                    HibernateUtil.getManager().restoreSession(mainSessionHolder);

                }
            }
        } else {
            isFileAccessAllowed = true;
        }

	    if (isFileCheck) {
            JSONObject result = new JSONObject();

            try {
                result.put("exists", downloadFile != null && downloadFile.exists() && downloadFile.canRead() && isFileAccessAllowed);
            } catch (JSONException e) {
                log.error(e);
            }

            response.setStatus(HttpStatus.SC_OK);
            response.setContentType("application/json");
            response.getOutputStream().write(result.toString().getBytes());
            response.getOutputStream().flush();
            return;
        }


	    if (downloadFile != null && downloadFile.exists() && downloadFile.canRead() && isFileAccessAllowed) {
	        response.setContentType("application/octet-stream");
	        response.setHeader("Content-length", Long.toString(downloadFile.length()));
            response.setHeader( "Content-disposition", "attachment; filename=\"" + downloadFile.getName() + "\"" );

            try (FileInputStream fileInputStream = new FileInputStream(downloadFile)) {
                StreamUtils.copy(fileInputStream, response.getOutputStream());
            } finally {
                response.getOutputStream().flush();
            }

	    } else {
	        response.setStatus(HttpStatus.SC_NOT_FOUND);
        }

    }

    private void sendDatabaseFileResponse(HttpServletResponse response, String filePath, String action, DatabaseFile databaseFile, boolean isFileCheck) {
        if (databaseFile != null) {

            byte[] bDatabaseFile = databaseFile.getFileContent();

            response.setContentType( databaseFile.getContentType() );
            response.setContentLength( (int) bDatabaseFile.length );
            response.setHeader( "Content-disposition", "attachment; filename=\"" + databaseFile.getFileName() + "\"" );

            outputDatabasePersistedFile(response, action, bDatabaseFile, filePath, databaseFile.getFileName(), isFileCheck);
        }
    }

    private void outputDatabasePersistedFile(HttpServletResponse response, String action, byte[] fileByteArray, String filepath, String filename, boolean isFileCheck) {
        try {

        	InputStream is = new ByteArrayInputStream(fileByteArray);

			if ( !action.equalsIgnoreCase(ACTION_SAVE_FILE) && (filename.toLowerCase().endsWith(".tiff") || filename.toLowerCase().endsWith(".tif")) ) {
				try {
					Image ima = ImageIO.read(is);
					RenderedImage ri = (RenderedImage) ima;
                    response.setHeader("Content-length", null);
					ImageIO.write(ri, "GIF", response.getOutputStream());
					if ( is != null )
						is.close();
				} catch (FileNotFoundException e) {
					log.error(filepath + " not found!", e);
					if ( is != null )
						is.close();
				} catch (IOException e) {
					log.error("image: " + filepath + " parsing error!", e);
					if ( is != null )
						is.close();
				} finally {
					if ( is != null )
						is.close();
				}
			} else {

				BufferedInputStream in = null;
				ServletOutputStream out = response.getOutputStream();

				try {
					in = new BufferedInputStream(is);
					byte[] buffer = new byte[512];
					int numberOfBytesRead = in.read(buffer);

					while (numberOfBytesRead != -1) {
						out.write(buffer, 0, numberOfBytesRead);
						out.flush();
						numberOfBytesRead = in.read(buffer);
					}
				} catch (IOException e) {
					log.error(e.getMessage(), e);
				} finally {
					if ( in != null )
						in.close();
				}

			}

        } catch(Exception e) {
            log.error("Error: ", e);
        }
	}

	private String getContentType(File file) {
		return new MimetypesFileTypeMap().getContentType(file);
	}

	private boolean isFileAccessAllowed(File f, boolean includeAllFolders) {
        ArrayList<String> allowedFolders = new ArrayList<>();

        allowedFolders.add(new File(ThemeManager.getBaseFolder()).getAbsolutePath());
        allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir)).getAbsolutePath());
        allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_HistoricalMsgImageDir)).getAbsolutePath());
        allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_EmailPreviewDir)).getAbsolutePath());
        allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir)).getAbsolutePath());
        allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_RationalizerFilesDir)).getAbsolutePath());

        if (includeAllFolders && UserUtil.getPrincipalUser() != null) {
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_SimulationExportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessageExportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_XMLConnectorXSLTDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_InsertScheduleExportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_EmailTemplateDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_WhereUsedReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_AuditReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_TransformReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ContentAssistantReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_DiagnosticsReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MetadataReportDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_MessagepointQEFileroot)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Uploaded.KEY_CompositionFileSetDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_TargetingDataFilesDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.Uploaded.KEY_DataFilesDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_TaskLogDir)).getAbsolutePath());
            allowedFolders.add(new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_DictionaryDir)).getAbsolutePath());

            String insertWorkingImagesDirectory = AsyncUploadedImageEventController.getInsertImagesWorkingDirectory();
            String applicationRootPath = ApplicationUtil.getRootPath();

            if (StringUtils.isNotEmpty(insertWorkingImagesDirectory)) {
                allowedFolders.add(new File(insertWorkingImagesDirectory).getAbsolutePath());
            }

            if (StringUtils.isNotEmpty(applicationRootPath)) {
                allowedFolders.add(new File(applicationRootPath).getAbsolutePath());
            }
        }

		if (f == null || f.getParentFile() == null)
		{
			return false;
		}

		String parentPath = f.getParentFile().getAbsolutePath();
        Path normalizedParentPath = Paths.get(parentPath).normalize(); // TPM-3150 Path Traversal Vulnerability fix

		boolean allowed = false;
		for (String folder : allowedFolders) {
			allowed = allowed || normalizedParentPath.startsWith(folder);
		}

		return allowed;
	}

    private void sendFontFileResponse(HttpServletRequest request, HttpServletResponse response, Long bundleId, String fontFile, boolean isDebugOrder) throws Exception {
        ConnectedTouchpointRenderingManagerProvider provider = (!isDebugOrder) ?
                (RenderingManagerUtil.isRESTApiEnabled() ? new ConnectedTouchpointRenderingManagerPMGRProvider() : new ConnectedTouchpointRenderingManagerDewsProvider())
            :  new ConnectedTouchpointRenderingManagerZipProvider();
        SandboxFile sandboxFile = provider.getFontFileContent(bundleId, fontFile);
        if (sandboxFile != null) {
            sendFontFileResponse(response, sandboxFile);
        } else {
            HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, "Invalid response from DEWS API", request, response);
        }
    }

    private static void sendFontFileResponse(HttpServletResponse response, SandboxFile sandboxFile) throws IOException {
        byte[] bSandboxFile = sandboxFile.getFileContent();
        response.setContentType( "application/octet-stream" );
        response.setContentLength( bSandboxFile.length );
        response.setHeader( "Content-disposition", "attachment; filename=\"" + sandboxFile.getFileName() + "\"" );
        response.setHeader( "Cache-Control", "public, max-age=3600" );

        BufferedInputStream in = null;
        ServletOutputStream out = response.getOutputStream();

        try {
            in = new BufferedInputStream(new ByteArrayInputStream(bSandboxFile));
            byte[] buffer = new byte[512];
            int numberOfBytesRead = in.read(buffer);

            while (numberOfBytesRead != -1) {
                out.write(buffer, 0, numberOfBytesRead);
                out.flush();
                numberOfBytesRead = in.read(buffer);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            if ( in != null )
                in.close();
        }
    }

    private static void copyFileToResponse (HttpServletResponse response, SandboxFile sandboxFile) {

        byte[] bSandboxFile = sandboxFile.getFileContent();

        try{

            InputStream is = new ByteArrayInputStream(bSandboxFile);

            BufferedInputStream in = null;
            ServletOutputStream out = response.getOutputStream();

            try {
                in = new BufferedInputStream(is);
                byte[] buffer = new byte[512];
                int numberOfBytesRead = in.read(buffer);

                while (numberOfBytesRead != -1) {
                    out.write(buffer, 0, numberOfBytesRead);
                    out.flush();
                    numberOfBytesRead = in.read(buffer);
                }
            } catch (IOException e) {
                //log.error("image: " + filePath + " output error!",e);
            } finally {
                if ( in != null )
                    in.close();
            }

        } catch(Exception e) {
            log.error("Error: ", e);
        }
    }


}