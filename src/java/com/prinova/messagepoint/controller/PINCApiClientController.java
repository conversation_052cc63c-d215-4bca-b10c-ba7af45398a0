package com.prinova.messagepoint.controller;

import com.google.common.base.Charsets;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class PINCApiClientController implements Controller {


    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/javascript");
        response.setStatus(HttpServletResponse.SC_OK);

        String apiClientJs = StreamUtils.copyToString(request.getServletContext().getResourceAsStream("/includes/javascript/pinc3/Pinc3APIClient.js"), Charsets.UTF_8);

        HttpRequestUtil.ResourceToken token = new HttpRequestUtil.ResourceToken();

        token.add("tk", request.getParameter("tk"));
        token.add("branch", Node.getCurrentBranch().getGuid());
        token.add("node", Node.getCurrentNode().getGuid());
        token.add("user", UserUtil.getPrincipalUser().getGuid());
        token.add("pod", request.getServerName().substring(request.getServerName().indexOf('.')));



        response.getOutputStream().write(apiClientJs.replace("%api_client_id%", token.getValue()).getBytes());

        return null;
    }
}
