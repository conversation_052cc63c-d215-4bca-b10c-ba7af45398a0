package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.webservice.Services;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class AsyncMarcieAssistController implements Controller {
	
	private static final Log log = LogUtil.getLog(AsyncMarcieAssistController.class);

	public static final String PARAM_ACTION = "action";
	public static final String ACTION_GET_CHAT_METADATA = "getChatMetadata";
	public static final String ACTION_SAVE_FEEDBACK = "saveFeedback";
	public static final String ACTION_GET_AWS_CREDENTIALS = "getAWSCredentials";

	private static final String CLIENT_ID = "QZSHT7EwgeF6vCaPQM0i8tAVyY2U4Bj5qCmj";
	private static final String CLIENT_SECRET = "cWYxv4QyxEpEpjGb0o0X3vFfwZx5XUCh0WI7";
	private static final String AWS_ACCESS_KEY = "********************";
	private static final String AWS_SECRET_KEY = "/9f1zgKGpQ/PrjgrDqUqwAI8Vqrl6cV5EbJBRcDf";
	private static final String AWS_REGION = "us-east-2";
	private static final String AWS_BUCKET_NAME = "messagepoint-bucket";
	private static final String GRANT_TYPE = "client_credentials";

	// Hardcoded values for local development
	private static final String LOCALHOST = "localhost";
	private static final String POD_URL = "https://tt252anx.messagepoint.com/mp";
	private static final String POD_COMPANY = "sean";
	private static final String POD_INSTANCE = "t1";

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String action = ServletRequestUtils.getStringParameter(request, PARAM_ACTION);
		response.setContentType("application/json");

		try (ServletOutputStream out = response.getOutputStream()) {
			if (action == null || action.isEmpty()) {
				log.warn("Action parameter is missing");
				sendErrorResponse(out, "Action parameter is missing");
				return null;
			}

			if (action.equalsIgnoreCase(ACTION_GET_CHAT_METADATA)) {
				out.write(getChatMetadataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if (action.equalsIgnoreCase(ACTION_SAVE_FEEDBACK)) {
				out.write(saveFeedback(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if (action.equalsIgnoreCase(ACTION_GET_AWS_CREDENTIALS)) {
				out.write(getAWSCredentials(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else {
				log.warn("Unknown action: " + action);
				sendErrorResponse(out, "Unknown action: " + action);
			}

			out.flush();
		} catch (IOException e) {
			log.error("Error writing response", e);
		} catch (Exception e) {
			log.error("Unexpected error occurred", e);
		}

		return null;
	}

	private String getChatMetadataResponseJSON(HttpServletRequest request) {
		JSONObject responseObject = new JSONObject();
		try {
			User user = UserUtil.getPrincipalUser();
			if (user == null) {
				log.warn("User not authenticated");
				responseObject.put("error", "User not authenticated");
				return responseObject.toString();
			}

			if (!MessagepointLicenceManager.getInstance().isLicencedForMarcieAssist()) {
				log.warn("Marcie Assist is not licensed");
				responseObject.put("error", "Marcie Assist is not licensed");
				return responseObject.toString();
			}

			ChatUserVO chatUser = new ChatUserVO(user);
			String accessToken = getAccessToken(request);

			if (accessToken == null || accessToken.isEmpty()) {
				responseObject.put("error", "Failed to fetch access token");
				return responseObject.toString();
			}

			responseObject.put("user", chatUser.toJson());
			responseObject.put("access_token", accessToken);
			responseObject.put("instance", getInstanceDetails());
			responseObject.put("touchpoint", getTouchpointDetails());
			responseObject.put("version", MessagePointStartUp.getBuildRevision());
			responseObject.put("service_available", isServiceAvailable());

		} catch (Exception e) {
			log.error("Error while creating response object", e);
			responseObject.put("error", e.getMessage());
		}
		return responseObject.toString();
	}

	private String getAccessToken(HttpServletRequest request) {
		String baseUrl = isLocalhost(request) ? POD_URL: ApplicationUtil.getBaseUrl(request);
		String apiUrl = baseUrl + "/idp/oauth/token";
		String audienceUrl = baseUrl + "/rest";

		try {
			HttpURLConnection connection = createConnection(apiUrl);
			String requestBody = createRequestBody(audienceUrl, request);

			sendRequest(connection, requestBody);

			return handleResponse(connection);

		} catch (IOException e) {
			throw new RuntimeException("Failed to connect to the API endpoint", e);
		} catch (Exception e) {
			throw new RuntimeException("Error while fetching access token", e);
		}
	}

	private static boolean isLocalhost(HttpServletRequest request) {
		return LOCALHOST.equals(request.getServerName());
	}

	private static HttpURLConnection createConnection(String apiUrl) throws IOException {
		URL url = new URL(apiUrl);
		HttpURLConnection connection = (HttpURLConnection) url.openConnection();
		connection.setRequestMethod("POST");
		connection.setRequestProperty("Content-Type", "application/json;");
		connection.setDoOutput(true); // Enable output for sending data
		return connection;
	}

	private static String createRequestBody(String audienceUrl, HttpServletRequest request) {
		Node currentNode = Node.getCurrentNode();
		User user = UserUtil.getPrincipalUser();

		JSONObject jsonInput = new JSONObject();
		jsonInput.put("client_id", CLIENT_ID);
		jsonInput.put("client_secret", CLIENT_SECRET);
		jsonInput.put("grant_type", GRANT_TYPE);
		jsonInput.put("audience", audienceUrl);
		jsonInput.put("mp-company", isLocalhost(request) ? POD_COMPANY : Node.getCurrentBranchName());
		jsonInput.put("mp-instance", isLocalhost(request) ? POD_INSTANCE : currentNode.getName());
		jsonInput.put("mp-user-guid", user.getGuid());

		return jsonInput.toString();
	}

	private static void sendRequest(HttpURLConnection connection, String requestBody) throws IOException {
		try (OutputStream os = connection.getOutputStream()) {
			byte[] input = requestBody.getBytes("utf-8");
			os.write(input, 0, input.length);
		}
	}

	private static String handleResponse(HttpURLConnection connection) throws IOException {
		int responseCode = connection.getResponseCode();
		if (responseCode != HttpURLConnection.HTTP_OK) {
			return null;
		}

		try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
			StringBuilder response = new StringBuilder();
			String responseLine;

			while ((responseLine = br.readLine()) != null) {
				response.append(responseLine.trim());
			}

			JSONObject jsonResponse = new JSONObject(response.toString());
			return jsonResponse.getString("access_token");
		}
	}

	private JSONObject getInstanceDetails() {
		JSONObject instanceDetails = new JSONObject();
		Node currentNode = Node.getCurrentNode();
		if (currentNode != null) {
			instanceDetails.put("name", currentNode.getName());
			instanceDetails.put("guid", currentNode.getGuid());
		}
		return instanceDetails;
	}

	private JSONObject getTouchpointDetails() {
		JSONObject touchpointDetails = new JSONObject();
		var currentTouchpoint = UserUtil.getCurrentTouchpointContext();
		if (currentTouchpoint != null) {
			touchpointDetails.put("name", currentTouchpoint.getName());
			touchpointDetails.put("guid", currentTouchpoint.getGuid());
		}
		return touchpointDetails;
	}

	private boolean isServiceAvailable() {
		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
			Services service = Services.findServiceByClientId(CLIENT_ID);
			return service != null && service.isServiceStatusEnabled() && service.isServiceStatusActive();
		} catch (Exception e) {
			throw new RuntimeException("Error while checking service availability", e);
		} finally {
			if (mainSessionHolder != null) {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		}
	}

	private String saveFeedback(HttpServletRequest request) {
		JSONObject responseObject = new JSONObject();

		String feedback = ServletRequestUtils.getStringParameter(request, "feedback", "");
		if (feedback.isEmpty()) {
			log.warn("Feedback is empty");
			responseObject.put("error", "Feedback cannot be empty");
			return responseObject.toString();
		}

		try {
			// TODO: save the feedback to the database or an external service
			log.info("Feedback received: " + feedback);
			responseObject.put("message", "Feedback saved successfully");

		} catch (Exception e) {
			log.error("Error while saving feedback", e);
			responseObject.put("error", e.getMessage());
		}
		return  responseObject.toString();
	}

	private String getAWSCredentials(HttpServletRequest request) {
		// TODO: Implement logic to fetch AWS credentials securely
		JSONObject responseObject = new JSONObject();
		try {
			responseObject.put("accessKeyId", AWS_ACCESS_KEY);
			responseObject.put("secretAccessKey", AWS_SECRET_KEY);
			responseObject.put("region", AWS_REGION);
			responseObject.put("bucketName", AWS_BUCKET_NAME);
		} catch (Exception e) {
			log.error("Error while fetching AWS credentials", e);
			responseObject.put("error", "Failed to fetch AWS credentials: " + e.getMessage());
		}
		return responseObject.toString();
	}

	private void sendErrorResponse(ServletOutputStream out, String errorMessage) throws IOException {
		JSONObject errorResponse = new JSONObject();
		errorResponse.put("error", errorMessage);
		out.write(errorResponse.toString().getBytes());
		out.flush();
	}

	private static class ChatUserVO {
		private final Long id;
		private final String guid;
		private final String email;
		private final String firstName;
		private final String lastName;

		public ChatUserVO(User user) {
			this.id = user.getId();
			this.guid = user.getGuid();
			this.email = user.getEmail();
			this.firstName = user.getFirstName();
			this.lastName = user.getLastName();
		}

		public JSONObject toJson() {
			JSONObject json = new JSONObject();
			json.put("id", id);
			json.put("guid", guid);
			json.put("email", email);
			json.put("firstName", firstName);
			json.put("lastName", lastName);
			return json;
		}
	}
}
