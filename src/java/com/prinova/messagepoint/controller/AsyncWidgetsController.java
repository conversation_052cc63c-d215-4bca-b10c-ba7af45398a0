package com.prinova.messagepoint.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.mobile.device.DeviceUtils;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.model.wrapper.AsyncWidgetWrapper;

public class AsyncWidgetsController implements Controller {
	public static final String PARAM_DOCUMENT_ID							= "documentId";
	public static final String PARAM_DISPLAY_MODE							= "displayMode";
	
	public static final String PARAM_WIDGET_TYPE 							= "widgetType";
	public static final String PARAM_ECHO									= "sEcho";
	public static final String PARAM_SEARCH_NAME							= "sSearch";
	public static final String PARAM_SORT_BY								= "sortBy";
	public static final String PARAM_PAGE_SIZE								= "iDisplayLength";
	public static final String PARAM_DISPLAY_START							= "iDisplayStart";
	public static final String PARAM_LIST_DISPLAY_FMT						= "listDisplayFormat";
	
	public static final String LIST_DISPLAY_FORMAT_NAME						= "name";
	
	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();
		out.write(getWidgetBytes(request));
		out.flush();
		return null;
	}

	private byte[] getWidgetBytes(HttpServletRequest request) throws Exception{
		String echo 							= ServletRequestUtils.getStringParameter(request, PARAM_ECHO);
		int widgetType 							= ServletRequestUtils.getIntParameter(request, PARAM_WIDGET_TYPE, HomepageWidget.ID_WIDGET_TYPE_MY_MESSAGES);
		long documentId 						= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1L);
		
		int pageSize 							= ServletRequestUtils.getIntParameter(request, PARAM_PAGE_SIZE, 0);
		int displayStart						= ServletRequestUtils.getIntParameter(request, PARAM_DISPLAY_START, 0);
		int pageIndex 							= pageSize == 0 ? 1 : Math.round(displayStart / pageSize) + 1;
		String sSearch 							= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_NAME);
		String displayMode						= ServletRequestUtils.getStringParameter(request, PARAM_DISPLAY_MODE, "limited");
		String listDisplayFormat				= ServletRequestUtils.getStringParameter(request, PARAM_LIST_DISPLAY_FMT, LIST_DISPLAY_FORMAT_NAME);
		
		boolean isMobileView					= DeviceUtils.getCurrentDevice(request).isMobile();
		
		Map<String, String>	orderByMap			= getOrderedByMap(request);
		
		AsyncWidgetWrapper wrapper = new AsyncWidgetWrapper();
		wrapper.setsEcho(echo);
		wrapper.setDisplayMode(displayMode);
		wrapper.setListDisplayFormat(listDisplayFormat);
		wrapper.buildItemsList(widgetType, sSearch, orderByMap, pageSize, pageIndex, documentId, isMobileView);
		wrapper.init();
		ObjectMapper mapper = new ObjectMapper();	
		return mapper.writeValueAsBytes(wrapper);
	}
	
	/**
	 * Build the ordered by map
	 * key: column name 
	 * value: order string (asc, desc)
     */
	private Map<String, String> getOrderedByMap(HttpServletRequest request){
		Map<String, String> orderedByMap = new HashMap<>();
		
		int numColumns = ServletRequestUtils.getIntParameter(request, "iColumns", 0);
		for(int i=0; i<numColumns; i++){
			int sortColumnIdx = ServletRequestUtils.getIntParameter(request, "iSortCol_"+i, -1);
			// ??? Index 0 Skipped: Expected to be drill down toggle
			// Widget with only one column such as My Variants didn't work because of "Index 0 Skipped". I changed it to >= 0
			if (sortColumnIdx >= 0) {
				// Search for the column property name
				String dataProp = ServletRequestUtils.getStringParameter(request, "mDataProp_" + sortColumnIdx, "name");
				String sortDir = ServletRequestUtils.getStringParameter(request, "sSortDir_"+i, "asc");
				// Add to the map
				if(sortDir.equals("asc")){
					orderedByMap.put(dataProp.toLowerCase(), "asc");				
				}else{
					orderedByMap.put(dataProp.toLowerCase(), "desc");
				}			
			}
		}
		if (orderedByMap.isEmpty())
			orderedByMap.put("name", "asc");
		
		return orderedByMap;
	}
}
