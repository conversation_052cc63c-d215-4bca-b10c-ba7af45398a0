package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.DataSecurityUtils;
import org.apache.commons.lang.StringUtils;

import org.springframework.context.NoSuchMessageException;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;

public class RequestParameterInputFilter implements Filter {

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		if(request instanceof HttpServletRequest){
			
			// Gateway: Don't validate URL params - passing data through params
			if (((HttpServletRequest) request).getServletPath().contains("connected_portal_gateway")) {
				chain.doFilter(request, response);
				return;
			}

			// OAuth gateway - pass through all params
			if (((HttpServletRequest)request).getServletPath().startsWith("/auth")) {
				chain.doFilter(request, response);
				return;
			}

			HttpServletRequest httpServletRequest = (HttpServletRequest)request;
			Enumeration parameterNames = httpServletRequest.getParameterNames();
			Set<String> reqParams = getRequestQueryString(httpServletRequest.getQueryString());
			String err_message = "";
			boolean must_redirect = false;	
			while(parameterNames.hasMoreElements()) {
				String parameterName = parameterNames.nextElement().toString();
				String parameterValue = httpServletRequest.getParameter(parameterName);
				parameterValue = new String(parameterValue.getBytes("ISO-8859-1"), "UTF-8");

				boolean containsNullByte 		= false;
				try {
					containsNullByte 		= !DataSecurityUtils.verifyNullByteDoesNotExist(parameterValue);
				} catch (Exception e) {
					containsNullByte 		= true; 
				}
				//boolean containsSQLinjection 	= AttackDetectorUtils.verifySQLInjectionAttack(parameterValue);  // Protection too aggressive: Can't use insert, delete
				boolean containsXSS 			= DataSecurityUtils.verifyXSSAttack(parameterValue);
				
				if (containsNullByte || containsXSS) {
					err_message = "The parameter value for " + parameterName + " is not valid.";
					must_redirect = true;
					break;
				} else if (parameterValue != null && !parameterValue.trim().isEmpty()) {
					if (parameterValue != null && !parameterValue.isEmpty()) {
						String paramRegexString = "";
						try {
							paramRegexString = ApplicationUtil.getRequestParameterType(parameterName);
						} catch (NoSuchMessageException e) {
							paramRegexString = "";
						}
						
						if (paramRegexString.trim().isEmpty() && reqParams.contains(parameterName)) { // use default ilter
							paramRegexString = ApplicationUtil.getRequestParameterType("defaultRegex");
						}
						
						if (!paramRegexString.trim().isEmpty() && parameterValue != null && !Pattern.matches(paramRegexString, parameterValue.trim())) {
							err_message = "The parameter value for " + parameterName + " is not valid.";
							must_redirect = true;
							break;
						}
					}
				}else if(parameterValue != null && parameterValue.trim().isEmpty()){
					// Only check the empty URL parameters - those included in requestParamterTypes.properties permitting all values are ok
					String requestParamRegex = null;
					try {
						requestParamRegex = ApplicationUtil.getRequestParameterType(parameterName);
					} catch (Exception e) {
						// Param not found, reject value
					}
					if ( requestParamRegex == null || !requestParamRegex.equals(".*") ) {

						if (httpServletRequest.getParameter("debug") != null && parameterName.equalsIgnoreCase("proofid")) {
							// Ignore
							// TODO find better fix.
							continue;
						}


						String queryString = httpServletRequest.getQueryString();
						if(queryString != null && queryString.indexOf(parameterName + "=") > -1){
							err_message = "The parameter value for " + parameterName + " is not valid.";
							must_redirect = true;
							break;					
						}
					}
				}
			}
			if ( must_redirect ) {
				HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_BAD_REQUEST, err_message, (HttpServletRequest) request, (HttpServletResponse) response);
			}
			else {
				chain.doFilter(request, response);
			}
		}
	}
	
	public void destroy() {}
	public void init(FilterConfig arg0) throws ServletException { }
	
	private Set<String> getRequestQueryString(String queryStr) {
		Set<String> params = new HashSet<>();
		if(queryStr != null) {
			for(String kvs : StringUtils.split(queryStr, "&")) {
				String[] ks = StringUtils.split(kvs, "=");
				if(ks != null && ks.length > 0)
					params.add(ks[0]);
			}
		}
		return params;
	}
}
