package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

public class CheckSessionFilter implements Filter {

	Log log = LogUtil.getLog(CheckSessionFilter.class);

	public void destroy() {

	}

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
			ServletException {
		if (request instanceof HttpServletRequest) {

			boolean invalidSessionFlag = false;
			HttpServletRequest httpRequest = (HttpServletRequest) request;
			String contextRoot = httpRequest.getContextPath();

			invalidSessionFlag = isSessionInvalid(httpRequest);
			if (invalidSessionFlag && !ApplicationUtil.isSSOAuthenticationMode()) {

				String url = contextRoot + "/signin.jsp?timeout=true";
				HttpServletResponse hres = (HttpServletResponse) response;
				log.debug("Session is redirecting to " + url);
				hres.sendRedirect(hres.encodeRedirectURL(url));
			} else {
				log.debug("Session continues" + httpRequest.getRequestURI());
				chain.doFilter(request, response);
			}
		}

	}

	private boolean isSessionInvalid(HttpServletRequest httpRequest) {
		// String sessionId = httpRequest.getRequestedSessionId();
		// System.out.println("sessionid is " + sessionId);
		HttpSession session = httpRequest.getSession(false);
		if (session == null && httpRequest.getRequestedSessionId() != null && !httpRequest.isRequestedSessionIdValid()) {
			// check if it is the sign in page to avoid recurring loop
			String fromUrl = httpRequest.getRequestURI();
			log.debug("Session is timed out! url" + fromUrl);
			if (fromUrl != null) {
				if (isExcludedUrl(fromUrl)) {
					log.debug(fromUrl + "is in the exclusion list, so not redirected");
					return false;
				}
			}
			return true;
		}
		return false;
	}

	private boolean isExcludedUrl(String url) {
		boolean result = false;
		String[] reserverUrlList = { "signin.jsp", "timeout.jsp", "signout", "displaymessage", "j_spring_security_check" };
		if (url != null) {
			for (int i = 0; i < reserverUrlList.length; i++) {
				if (reserverUrlList[i] != null) {
					if (url.contains(reserverUrlList[i]))
						return true;
				}
			}
		}
		return result;

	}

	public void init(FilterConfig config) throws ServletException {

	}

}
