package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.AesUtil;
import com.prinova.messagepoint.util.RandomGUID;

public class AsyncKeyGenerateController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncKeyGenerateController.class);

	public static final String REQ_PARM_TYPE 				= "type";
	public static final String TYPE_GUID					= "guid";
	public static final String TYPE_SECRET_KEY				= "ssoSecretKey";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseXML(request,response).getBytes());
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for key generateing: "+e.getMessage(),e);
		}

		return null;
	}

	private String getResponseXML (HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		String requestedType 	= getTypeParam(request);
			
		if ( requestedType.equalsIgnoreCase(TYPE_GUID) ) {	
			return generateGuid(request);
		}else if( requestedType.equalsIgnoreCase(TYPE_SECRET_KEY)){
			return generateSecretKey(request);
		}
		return null;
	}
	
	private String generateGuid(HttpServletRequest request) throws Exception {
		String key = RandomGUID.getGUID();
		
		JSONObject returnObj = new JSONObject();
		
		if (key.trim().isEmpty()) {
			returnObj.put("error", true);
			returnObj.put("message", "Failed to generate Key");
		}else{
			
			returnObj.put("error", false);
			returnObj.put("key", key);
		}
		return returnObj.toString();
	}
	
	private String generateSecretKey(HttpServletRequest request) throws Exception {
		AesUtil aes = new AesUtil(256);
		String key = aes.generateSecretKey();
		
		JSONObject returnObj = new JSONObject();
		
		if (key.trim().isEmpty()) {
			returnObj.put("error", true);
			returnObj.put("message", "Failed to generate Key");
		}else{
			
			returnObj.put("error", false);
			returnObj.put("key", key);
		}
		return returnObj.toString();
	}

	private String getTypeParam( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, "null");
	}
}