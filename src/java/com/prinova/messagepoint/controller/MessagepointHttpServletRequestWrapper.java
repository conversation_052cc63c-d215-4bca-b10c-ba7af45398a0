package com.prinova.messagepoint.controller;

import org.apache.commons.io.IOUtils;

import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;

public class MessagepointHttpServletRequestWrapper extends HttpServletRequestWrapper {
    private ByteArrayOutputStream cachedRequest;

    public MessagepointHttpServletRequestWrapper(ServletRequest request) {
        super((HttpServletRequest)request);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (cachedRequest == null)
            cacheRequestInputStream();

        return new CachedServletInputStream();
    }

    @Override
    public BufferedReader getReader() throws IOException{
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    private void cacheRequestInputStream() throws IOException {
        cachedRequest = new ByteArrayOutputStream();
        IOUtils.copy(super.getInputStream(), cachedRequest);
    }

    public class CachedServletInputStream extends ServletInputStream {
        private ByteArrayInputStream input;

        public CachedServletInputStream() {
            input = new ByteArrayInputStream(cachedRequest.toByteArray());
        }

        @Override
        public int read() {
            return input.read();
        }
    }
}
