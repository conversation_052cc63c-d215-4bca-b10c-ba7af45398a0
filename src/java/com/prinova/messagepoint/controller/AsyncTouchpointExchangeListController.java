package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.TPInstanceVisibilityMap;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.backgroundtask.CloneTouchpointBackgroundTask;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;

import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncTouchpointExchangeListController implements Controller {

    private static final String REQ_PARAM_TOUCHPOINT 	            = "touchpoint";
    private static final String REQ_PARAM_TOUCHPOINTNAME 	        = "touchpointName";
    private static final String REQ_PARM_DOMAIN_ID 		            = "domainId";
    private static final String REQ_PARM_INSTANCE_ID 		        = "instanceId";
    private static final String REQ_PARM_LANGUAGEFORSYNC_IDS 		= "languageForSyncIds";
    private static final String REQ_PARM_DONT_OVERRIDE_OBJECTS 		= "dontOverrideObjects";

    private static final Log log = LogUtil.getLog(AsyncTouchpointExchangeListController.class);

    private enum RequestMethod {
        SUBSCRIBE,
        UNSUBSCRIBE,
        UPDATE,
        UNKNOWN,
    }



    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        long touchpountId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT, -1L);
		long domainId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOMAIN_ID, -1L);
		long instanceId = ServletRequestUtils.getLongParameter(request, REQ_PARM_INSTANCE_ID, -1L);

        switch (getRequestType(request)) {
            case SUBSCRIBE:
                sendResponse(response, getSubscribeResult(request, touchpountId, domainId, instanceId));
                break;
            case UNSUBSCRIBE:
                sendResponse(response, getUnsubscribeResult(request, touchpountId));
                break;
            case UPDATE:
                sendResponse(response, getUpdateResult(request, touchpountId));
                break;
            default:
                response.setStatus(HttpStatus.SC_BAD_REQUEST);

        }

        return null;
    }

    private JSONObject getSubscribeResult(HttpServletRequest request, long touchpountId, long domainId, long instanceId) throws JSONException {
        JSONObject result = new JSONObject();
        boolean isSuccess = true;

        String touchpointName = ServletRequestUtils.getStringParameter(request, REQ_PARAM_TOUCHPOINTNAME, StringUtils.EMPTY);

        // Implement Touchpoint Exchange Subscribe

		try {
			
			Node selectedExchangeNode = null;
			
            if (instanceId > 0)
            	selectedExchangeNode = Node.findById(instanceId);
            else
            {
            	selectedExchangeNode = Branch.findById(domainId).getExchangeNode();
            }

//            long[] languageForSyncIDs = ServletRequestUtils.getLongParameters(request, REQ_PARM_LANGUAGEFORSYNC_IDS);
            String languageForSyncIDsParameterString = ServletRequestUtils.getStringParameter(request, REQ_PARM_LANGUAGEFORSYNC_IDS, "");
            String [] languageForSyncIDsString = languageForSyncIDsParameterString.split(",");
//            languageForSyncIDs = Long.parseLong();

            String dontOverrideObjectsString = ServletRequestUtils.getStringParameter(request, REQ_PARM_DONT_OVERRIDE_OBJECTS, "");
            String[] dontOverrideObjects = dontOverrideObjectsString.split(",");

            Set<Long> enabledMessagepointLocaleIDs = new HashSet<>();

            Node currentNode = Node.getCurrentNode();
            Branch currentBranch = currentNode.getBranch();
            String curretNodeGuid = currentNode.getGuid();
            String currentBranchDcsNodeGuid = currentBranch.getDcsNode().getGuid();

            if(selectedExchangeNode.getBranch().getId() != currentBranch.getId() || ((!currentNode.isExchangeNode()) && selectedExchangeNode.isExchangeNode())) {
                SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(selectedExchangeNode.getSchemaName());
                Document doc = Document.findById(touchpountId);
                Set<Long> touchpointLanguageLocales = doc.getTouchpointLanguagesAsLocales()
                        .stream()
                        .map(MessagepointLocale::getId)
                        .collect(Collectors.toSet());

                TPInstanceVisibilityMap domainTPInstanceVisibilityMap = TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), currentBranchDcsNodeGuid);
                TPInstanceVisibilityMap nodeTPInstanceVisibilityMap = domainTPInstanceVisibilityMap != null ? null : TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), curretNodeGuid);
                if (domainTPInstanceVisibilityMap != null || nodeTPInstanceVisibilityMap != null) {
                    Set<MessagepointLocale> enabledMessagepointLocales =
                            domainTPInstanceVisibilityMap != null ?
                                    domainTPInstanceVisibilityMap.getMessagepointLocales() :
                                    nodeTPInstanceVisibilityMap.getMessagepointLocales();

                    enabledMessagepointLocaleIDs.addAll(enabledMessagepointLocales
                            .stream()
                            .map(MessagepointLocale::getId)
                            .filter(id->touchpointLanguageLocales.contains(id))
                            .collect(Collectors.toSet()));
                }
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            } else {
                SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(selectedExchangeNode.getSchemaName());

                Document doc = Document.findById(touchpountId);

                enabledMessagepointLocaleIDs.addAll(doc.getTouchpointLanguagesAsLocales()
                        .stream()
                        .map(MessagepointLocale::getId)
                        .collect(Collectors.toSet()));

                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }

            Set<Long> languagesForSync = CloneHelper.queryInSchema(selectedExchangeNode.getSchemaName(), ()->{
                Set<Long> languages = new HashSet<>();
                for(String idString : languageForSyncIDsString) {
                    long id = Long.parseLong(idString);
                    MessagepointLocale messagepointLocale = MessagepointLocale.findById(id);
                    if(messagepointLocale != null) {
                        Long messagepointLocaleId = messagepointLocale.getId();
                        if(enabledMessagepointLocaleIDs.contains(messagepointLocaleId)) {
                            languages.add(messagepointLocaleId);
                        }
                    }
                }
                return languages;
            });

            Set<Integer> dontOverrideObjectTypeIds = new HashSet<>();
            for(String objType : dontOverrideObjects) {
                if(objType != null && !objType.isBlank()) {
                    Integer objectTypeId = Integer.parseInt(objType);
                    dontOverrideObjectTypeIds.add(objectTypeId);
                }
            }

            if(SyncTouchpointUtil.hasActiveBackgroundTasks(selectedExchangeNode.getId(), currentNode.getId())) {
                throw new Exception(ApplicationUtil.getMessage("error.message.cannot.run.task"));
            }

            CloneTouchpointBackgroundTask task = new CloneTouchpointBackgroundTask(touchpountId, touchpointName, true, true, true, false, UserUtil.getPrincipalUser(), selectedExchangeNode.getSchemaName(), 0, languagesForSync, dontOverrideObjectTypeIds);
			Thread thread = MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
			
			result.put("status_polling_id", StatusPollingBackgroundTask.findByThreadId(thread.getId()));
	        result.put("success", isSuccess);
			
		} catch (Exception e) {
			
			result.put("error", true);
	        result.put("success", false);
			result.put("message", e.getMessage());
			
		}

        return result;
    }

    private JSONObject getUnsubscribeResult(HttpServletRequest request, long touchpountId) throws JSONException {
        JSONObject result = new JSONObject();
        boolean isSuccess = true;

        // TODO: Implement Touchpoint Exchange Unsubscribe

        Document document = Document.findById(touchpountId);
        
        if (document != null)
        {
        	document.setExchangeTouchpointGuid(null);
        	document.save();
        }
        
        result.put("success", isSuccess);

        return result;
    }

    private JSONObject getUpdateResult(HttpServletRequest request, long touchpountId) throws JSONException {
        JSONObject result = new JSONObject();
        boolean isSuccess = true;

        // TODO: Implement Touchpoint Exchange Update

        result.put("success", isSuccess);

        return result;
    }

    private RequestMethod getRequestType(HttpServletRequest request) {
        String requestType = ServletRequestUtils.getStringParameter(request, "method", StringUtils.EMPTY);

        switch (requestType.toLowerCase()) {
            case "subscribe":
                return RequestMethod.SUBSCRIBE;
            case "unsubscribe":
                return RequestMethod.UNSUBSCRIBE;
            case "update":
                return RequestMethod.UPDATE;
            default:
                return RequestMethod.UNKNOWN;
        }
    }

    private void sendResponse(HttpServletResponse response, Object result) {
        try {
            response.setContentType("application/json");
            ServletOutputStream out = response.getOutputStream();
            out.print(result.toString());
        } catch (IOException ex) {
            log.error("Error processing async request: " + ex.getMessage());
        }
    }
}
