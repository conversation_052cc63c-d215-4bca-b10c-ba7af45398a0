package com.prinova.messagepoint.controller;

import java.util.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.version.ModelVersionMapping;
import com.prinova.messagepoint.model.version.VersionedInstance;
import com.prinova.messagepoint.model.version.VersionedModel;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.backgroundtask.RecalculateHashBackgroundTask;
import com.prinova.messagepoint.platform.services.message.UpdateMessageHashService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncMessageSyncOperationsController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncMessageSyncOperationsController.class);
	public static final String PARAM_ACTION                            = "action";
    public static final String PARAM_DOCUMENT_ID                       = "documentId";
    public static final String PARAM_OTHER_DOCUMENT_ID                 = "otherDocumentId";
    public static final String PARAM_INSTANCE_ID                       = "instanceId";
	public static final String PARAM_OBJECT_TYPE			           = "objectType";
    public static final String PARAM_OBJECT_ID                         = "objectId";
    public static final String PARAM_OBJECT_INSTANCE_ID                = "objectInstanceId";
    public static final String PARAM_OBJECT_STATUS                     = "objectStatus";
    public static final String PARAM_OBJECT_REFBY                      = "referencedByObjectIds";
    public static final String PARAM_OBJECT_REFERENCING                = "referencingObjectIds";
    public static final String PARAM_OBJECT_REFERENCINGALWAYSSYNC      = "referencingAwaysSyncObjectIds";
    public static final String PARAM_HASH_ALGORITHMCHANGED             = "hashAlgorithmChanged";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

        try {
            out.write( getResponseJSON(request, response).getBytes() );
        } catch (JSONException e) {
            JSONObject returnObj = new JSONObject();
            returnObj.put("error", true);
            returnObj.put("message", e.getMessage());
            out.write(returnObj.toString().getBytes());
        }
        out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws Exception {

        String action = ServletRequestUtils.getStringParameter(request, PARAM_ACTION, null);

        if(action != null && action.equalsIgnoreCase("makeHash")) {
            return getMakeHashResponseJSON(request, response);
        } else if(action != null && action.equalsIgnoreCase("runRehashTask")) {
            return runRehashTask(request, response);
        } else if(action != null && action.equalsIgnoreCase("findReferencingDependencies")) {
        	return findReferencingDependencies(request, response);
        } else if(action != null && action.equalsIgnoreCase("findReferencedByDependencies")) {
        	return findReferencedByDependencies(request, response);
        }
		return "";
	}

    private String runRehashTask(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();
        long principalUserId = UserUtil.getPrincipalUserId();
        User requestor = User.findById(principalUserId);
        Integer objectType = ServletRequestUtils.getIntParameter(request, PARAM_OBJECT_TYPE, RecalculateHashBackgroundTask.OBJECT_ALL);
        Long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1);
        Long objectId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, -1);
        Long objectInstanceId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_INSTANCE_ID, -1);
        boolean isAlgorithmChanged = ServletRequestUtils.getBooleanParameter(request, PARAM_HASH_ALGORITHMCHANGED, false);
        RecalculateHashBackgroundTask task = new RecalculateHashBackgroundTask(objectType, documentId, objectId, objectInstanceId, isAlgorithmChanged, requestor);
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
        returnObj.put("started",  true);
        return returnObj.toString();
    }

    private String getMakeHashResponseJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();

        long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1);
        long otherDocumentId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_DOCUMENT_ID, -1);
        long instanceId = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1);
        String objectType = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
        long objectId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, -1);
        long objectStatus = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_STATUS, 0);
        boolean isAlgorithmChanged = ServletRequestUtils.getBooleanParameter(request, PARAM_HASH_ALGORITHMCHANGED, false);

        if(documentId > 0 && otherDocumentId > 0 && objectId > 0) {
            Node currentNode = Node.getCurrentNode();
            Node otherNode = currentNode;
            String currentSchema = currentNode.getSchemaName();
            String otherSchema = currentSchema;
            if(instanceId > 0) {
                otherNode = Node.findById(instanceId);
            }
            if(otherNode != null) {
                otherSchema = otherNode.getSchemaName();
            }
            Document document = Document.findById(documentId);
            Document otherDocument = CloneHelper.queryInSchema(otherSchema, ()->Document.findById(otherDocumentId));
            boolean objectIsFromOther = (objectStatus & 0x10000) != 0;
            ContentObject objectMessage = objectIsFromOther ? CloneHelper.queryInSchema(otherSchema, ()->ContentObject.findById(objectId)) : ContentObject.findById(objectId);
            ContentObject message = objectIsFromOther ? ContentObject.findByDnaAndDocument(objectMessage, document) : objectMessage;
            ContentObject otherMessage = objectIsFromOther ? objectMessage : CloneHelper.queryInSchema(otherSchema, ()->ContentObject.findByDnaAndDocument(objectMessage, otherDocument));

            if(message != null) {
                Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateMessageHashService.SERVICE_NAME, UpdateMessageHashService.class);
                ServiceExecutionContext context = UpdateMessageHashService.createContext(message.getId(), currentNode.getId(), SyncObjectType.ID_MESSAGE, isAlgorithmChanged);
                service.execute(context);
            }

            if(otherMessage != null) {
                Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateMessageHashService.SERVICE_NAME, UpdateMessageHashService.class);
                ServiceExecutionContext context = UpdateMessageHashService.createContext(otherMessage.getId(), otherNode.getId(), SyncObjectType.ID_MESSAGE, isAlgorithmChanged);
                service.execute(context);
            }

            String newHash = "";
            String otherHash = "";

            if(message != null) {
                ContentObject newMessage = ContentObject.findById(message.getId());
                newHash = getModelAndInstanceHash(newMessage, newMessage.getLatestContentObjectDataWorkingCentric());
            }

            if(otherMessage != null) {
                otherHash = CloneHelper.queryInSchema(otherSchema, ()->{
                    ContentObject newOtherMessage = ContentObject.findById(otherMessage.getId());
                    return getModelAndInstanceHash(newOtherMessage, newOtherMessage.getLatestContentObjectDataWorkingCentric());
                });
            }

            returnObj.put("isSame", newHash.equals(otherHash));
        }

        return returnObj.toString();
    }

    private <MODEL extends IdentifiableMessagePointModel & VersionedModel, INSTANCE extends IdentifiableMessagePointModel & VersionedInstance>
    String getModelAndInstanceHash(MODEL model, INSTANCE instance) {
    	String modelAndInstanceHash = model.getHashSafe();
        ModelVersionMapping archivedVersionMapping = null; // TODO (model.hasActiveData()) ? null : model.getLatestArchivedVersionInfo();
        if(archivedVersionMapping != null) {
        	INSTANCE archivedCopy = archivedVersionMapping.getModelInstance();
        	if(archivedCopy != null) {
        		modelAndInstanceHash = modelAndInstanceHash + " AR: " + archivedCopy.getHashSafe();
        	}
        }
        INSTANCE activeCopy = (model.isArchived()) ? null : (INSTANCE) model.getProduction();
        if(activeCopy != null) {
        	modelAndInstanceHash = modelAndInstanceHash + " AC: " + activeCopy.getHashSafe();
        }
        INSTANCE workingCopy = (INSTANCE) model.getWorkingCopy();
        if(workingCopy != null) {
        	modelAndInstanceHash = modelAndInstanceHash + " WC: " + workingCopy.getHashSafe();
        }
        return modelAndInstanceHash;
    }

    private String findReferencingDependencies(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();

        long instanceId = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1);
        String objectType = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
        Long objectId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, -1);
        String referencingIds = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_REFERENCING, null);
        String referencingAlwaysSyncIds = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_REFERENCINGALWAYSSYNC, null);

        Node node = Node.getCurrentNode();

        if(instanceId != -1) {
        	node = Node.findById(instanceId);
        }

        String schema = node.getSchemaName();

    	Set<JSONObject> allReferenceObjects = new HashSet<>();
    	String [] referencingIdsArray = referencingIds.split(",");

    	String [] referencingAlwaysSyncIdsArray = referencingAlwaysSyncIds == null ? null : referencingAlwaysSyncIds.split(",");

    	List<String> referencingAlwaysSyncIdsList = Arrays.asList(referencingAlwaysSyncIdsArray);

        boolean includeDocumentName = false; // ! objectType.equalsIgnoreCase("message");

    	for(String referencingId : referencingIdsArray) {
    		CloneHelper.execInSchema(schema, ()->{
    			JSONObject referencedByJSONObject = getRefObject(referencingId, referencingAlwaysSyncIdsList, includeDocumentName);
    			if(referencedByJSONObject != null) {
    				allReferenceObjects.add(referencedByJSONObject);
	    		}
    		});
    	}

/*
        if(objectType.equalsIgnoreCase("message")) {
        	CloneHelper.execInSchema(schema, ()->{
	        	Set<Long> referencedLocalContentIDs = new HashSet<>();
	        	Set<Long> referencedLocalContentLibraryIDs = new HashSet<>();
	        	Set<Long> referencedPlaceholderIDs = new HashSet<>();
	        	Set<Long> referencedEmbeddedContentIDs = new HashSet<>();
	        	Set<Long> referencedContentLibraryIDs= new HashSet<>();
	        	Message sourceMessage = Message.findById(objectId);
	        	ContentAssociation.getContentReferencedIDs(sourceMessage, referencedLocalContentIDs, referencedLocalContentLibraryIDs, referencedPlaceholderIDs, referencedEmbeddedContentIDs, referencedContentLibraryIDs);
	        	for(Long referencedId : referencedLocalContentIDs) {
	            	Message referencedMessage = Message.findById(referencedId);
	            	String referencedMessageName = referencedMessage.getLatestMessageInstanceName();
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.local.smart.text"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedMessageName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}
	        	for(Long referencedId : referencedLocalContentLibraryIDs) {
	            	Message referencedMessage = Message.findById(referencedId);
	            	String referencedMessageName = referencedMessage.getLatestMessageInstanceName();
	            	JSONObject referencedJSONObject = new JSONObject();
	            	try {
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.local.image.library"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedMessageName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}
	        	for(Long referencedId : referencedPlaceholderIDs) {
	            	Message referencedMessage = Message.findById(referencedId);
	            	MessageInstance referencedMessageInstance = referencedMessage.getLatestProductionCentric();
	            	String referencedMessageName = referencedMessage.getLatestMessageInstanceName();
	            	long contentTypeId = referencedMessageInstance.getContentType().getId();
	            	String referencedObjectType = ApplicationUtil.getMessage("page.label.message");
					if(contentTypeId == ContentType.TEXT){	// Local Smart Text
						referencedObjectType = ApplicationUtil.getMessage("page.label.local.smart.text");
					} else if(contentTypeId == ContentType.GRAPHIC) {	// Local Image Library
						referencedObjectType = ApplicationUtil.getMessage("page.label.local.image.library");
					}
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", referencedObjectType);
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedMessageName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}

	        	for(Long referencedId : referencedEmbeddedContentIDs) {
	        		EmbeddedContent referencedEmbeddedContent = EmbeddedContent.findById(referencedId);
	        		EmbeddedContentInstance referencedEmbeddedContentInstance = referencedEmbeddedContent.getLatestProductionCentric();
	            	String referencedreferencedEmbeddedContentName = referencedEmbeddedContentInstance.getName();
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.embedded.content"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedreferencedEmbeddedContentName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}

	        	for(Long referencedId : referencedContentLibraryIDs) {
	        		ContentLibrary referencedContentLibrary = ContentLibrary.findById(referencedId);
	        		ContentLibraryInstance referencedContentLibraryInstance = referencedContentLibrary.getLatestProductionCentric();
	            	String referencedreferencedContentLibraryName = referencedContentLibraryInstance.getName();
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.content.library"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedreferencedContentLibraryName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}

        		List<TargetGroup> sourceTargetGroups = TargetGroup.findAll();
        		int total = sourceTargetGroups.size();
        		int count = 0;
                long startTime = System.currentTimeMillis();
        		log.info(" Checking target group " + count + " / " + total);
        		for(TargetGroup targetGroup : sourceTargetGroups) {
        			count = count + 1;
        			List<IdentifiableMessagePointModel> referencableObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(  targetGroup.getDirectReferences());
        			if(referencableObjects.stream().anyMatch(ro->ro.getId() == sourceMessage.getId())) {
    	            	try {
    		            	JSONObject referencedJSONObject = new JSONObject();
    		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.targetgroup"));
    		            	referencedJSONObject.put("objectId", targetGroup.getId());
    		            	referencedJSONObject.put("objectName", targetGroup.getName());
    		            	allReferenceObjects.add(referencedJSONObject);
    	            	} catch(JSONException ex) {
    	            		ex.printStackTrace();
    	            	}
        			}
                    long currentTime = System.currentTimeMillis();
                    if(currentTime - startTime > 5000) {
                    	log.info(" Checking target group " + count + " / " + total);
                    	startTime = currentTime;
                    }
        		}
        		log.info(" Checking target group " + count + " / " + total);
        	});
        } else if(objectType.equalsIgnoreCase("smartText")) {
        	CloneHelper.execInSchema(schema, ()->{
	        	EmbeddedContent sourceEmbeddedContent = EmbeddedContent.findById(objectId);
	        	Set<Long> referencedEmbeddedContentIDs = new HashSet<>();
	        	Set<Long> referencedContentLibraryIDs= new HashSet<>();
	        	ContentAssociation.getContentReferencedIDs(sourceEmbeddedContent, referencedEmbeddedContentIDs, referencedContentLibraryIDs);
	        	for(Long referencedId : referencedEmbeddedContentIDs) {
	        		EmbeddedContent referencedEmbeddedContent = EmbeddedContent.findById(referencedId);
	        		EmbeddedContentInstance referencedEmbeddedContentInstance = referencedEmbeddedContent.getLatestProductionCentric();
	            	String referencedreferencedEmbeddedContentName = referencedEmbeddedContentInstance.getName();
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.embedded.content"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedreferencedEmbeddedContentName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}

	        	for(Long referencedId : referencedContentLibraryIDs) {
	        		ContentLibrary referencedContentLibrary = ContentLibrary.findById(referencedId);
	        		ContentLibraryInstance referencedContentLibraryInstance = referencedContentLibrary.getLatestProductionCentric();
	            	String referencedreferencedContentLibraryName = referencedContentLibraryInstance.getName();
	            	try {
		            	JSONObject referencedJSONObject = new JSONObject();
		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.content.library"));
		            	referencedJSONObject.put("objectId", referencedId);
		            	referencedJSONObject.put("objectName", referencedreferencedContentLibraryName);
		            	allReferenceObjects.add(referencedJSONObject);
	            	} catch(JSONException ex) {
	            		ex.printStackTrace();
	            	}
	        	}
        		List<TargetGroup> sourceTargetGroups = TargetGroup.findAll();
        		for(TargetGroup targetGroup : sourceTargetGroups) {
        			List<IdentifiableMessagePointModel> referencableObjects = SyncTouchpointUtil.getObjectFromReferencableObjectList(  targetGroup.getDirectReferences());
        			if(referencableObjects.stream().anyMatch(ro->ro.getId() == sourceEmbeddedContent.getId())) {
    	            	try {
    		            	JSONObject referencedJSONObject = new JSONObject();
    		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.targetgroup"));
    		            	referencedJSONObject.put("objectId", targetGroup.getId());
    		            	referencedJSONObject.put("objectName", targetGroup.getName());
    		            	allReferenceObjects.add(referencedJSONObject);
    	            	} catch(JSONException ex) {
    	            		ex.printStackTrace();
    	            	}
        			}
        		}
        	});
        } else if(objectType.equalsIgnoreCase("targetGroup")) {
        	CloneHelper.execInSchema(schema, ()->{
	        	TargetGroup sourceTargetGroup = TargetGroup.findById(objectId);
	        	if(sourceTargetGroup != null) {
	        		TargetGroupInstance sourceTargetGroupInstance = sourceTargetGroup.getInstance();
	        		if(sourceTargetGroupInstance != null) {
	        			Set<ConditionItem> conditionItems = sourceTargetGroupInstance.getConditionItems();
	        			if(conditionItems != null) {
	        				for(ConditionItem conditionItem : conditionItems) {
	        					if(conditionItem != null) {
	        						ConditionElement targetRule = conditionItem.getConditionElement();
	        						if(targetRule != null) {
	        	    	            	try {
		            		            	JSONObject referencedJSONObject = new JSONObject();
		            		            	referencedJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.target.rule"));
		            		            	referencedJSONObject.put("objectId",   targetRule.getId());
		            		            	referencedJSONObject.put("objectName", targetRule.getName());
		            		            	allReferenceObjects.add(referencedJSONObject);
	        	    	            	} catch(JSONException ex) {
	        	    	            		ex.printStackTrace();
	        	    	            	}
	        						}
	        					}
	        				}
	        			}
	        		}
	        	}
        	});
        } else if(objectType.equalsIgnoreCase("targetRule")) {
        	CloneHelper.execInSchema(schema, ()->{
	        	ConditionElement sourceTargetRule = ConditionElement.findById(objectId);
	        	List<ReferencableObject> referencableObjects = sourceTargetRule.getDirectReferences();

	        	if(referencableObjects != null && ! referencableObjects.isEmpty()) {
	        		for(ReferencableObject referencableObject : referencableObjects) {
	        			JSONObject referencedJSONObject = SyncTouchpointUtil.getJSONObjectFromReferencableObject(referencableObject);
		            	allReferenceObjects.add(referencedJSONObject);
	        		}
	        	}
        	});
        }
*/
    	returnObj.put("objectId", 		objectId);
    	returnObj.put("dependencyType", "referencing");
    	returnObj.put("dependencies", allReferenceObjects);
        return returnObj.toString();
    }

    private String findReferencedByDependencies(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();

        long instanceId = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1);
        String objectType = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
        Long objectId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, -1);
        String referencedByIds = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_REFBY, null);
        Node node = Node.getCurrentNode();

        if(instanceId != -1) {
        	node = Node.findById(instanceId);
        }

        boolean referencedSharedObject = ! objectType.equalsIgnoreCase("message");

        String schema = node.getSchemaName();

    	Set<JSONObject> allReferenceByObjects = new HashSet<>();
    	String [] referencedByIdsArray = referencedByIds.split(",");
    	for(String refById : referencedByIdsArray) {
    		CloneHelper.execInSchema(schema, ()->{
    			JSONObject referencedByJSONObject = getRefObject(refById, null, referencedSharedObject);
    			if(referencedByJSONObject != null) {
    				allReferenceByObjects.add(referencedByJSONObject);
	    		}
    		});
    	}
    	returnObj.put("objectId", 		objectId);
    	returnObj.put("dependencyType", "referenceBy");
    	returnObj.put("dependencies", 	allReferenceByObjects);
        return returnObj.toString();
    }

    private JSONObject getRefObject(String refById, Collection<String> alwaysSyncIds, boolean includeDocumentName) {
    	JSONObject referencedByJSONObject = null;
    	String [] refByIdStrings = refById.split("[-]");
		String refObjectType = refByIdStrings[0];
		String refObjectIdString = refByIdStrings[1];
		long refObjectId = Long.parseLong(refObjectIdString);
		if(refObjectType.equalsIgnoreCase("message") || refObjectType.equals("LocalSmartText") || refObjectType.equals("LocalImage")) {
            ContentObject refObject = ContentObject.findByIdActiveDataFocusCentric(refObjectId);

			refObjectType = ApplicationUtil.getMessage("page.label.message");
			if(refObject.isLocalSmartText()) {
				refObjectType = ApplicationUtil.getMessage("page.label.local.smart.text");
			} else if(refObject.isLocalSmartText()) {	// Local Image Library
			        refObjectType = ApplicationUtil.getMessage("page.label.local.image.library");
			}

        	try {
            	referencedByJSONObject = new JSONObject();
            	referencedByJSONObject.put("objectType", refObjectType);
            	referencedByJSONObject.put("objectId",   refObject.getId());
            	referencedByJSONObject.put("objectName", refObject.getName());
    			if(includeDocumentName) {
	            	referencedByJSONObject.put("documentName", refObject.getDocument().getName());
    			}
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
		} else if(refObjectType.equalsIgnoreCase("smartText")) {
            ContentObject refObject = ContentObject.findByIdActiveDataFocusCentric(refObjectId);

        	try {
            	referencedByJSONObject = new JSONObject();
            	referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.embedded.content"));
            	referencedByJSONObject.put("objectId",   refObject.getId());
            	referencedByJSONObject.put("objectName", refObject.getName());
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
		} else if(refObjectType.equalsIgnoreCase("image")) {
			ContentObject refObject = ContentObject.findByIdActiveDataFocusCentric(refObjectId);

        	try {
            	referencedByJSONObject = new JSONObject();
            	referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.content.library"));
            	referencedByJSONObject.put("objectId",   refObject.getId());
            	referencedByJSONObject.put("objectName", refObject.getName());
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
		} else if(refObjectType.equalsIgnoreCase("targetGroup")) {
			TargetGroup refTargetGroup = TargetGroup.findById(refObjectId);
			String referencingTargetGroupName = refTargetGroup.getName();

        	try {
            	referencedByJSONObject = new JSONObject();
            	referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.targetgroup"));
            	referencedByJSONObject.put("objectId",   refTargetGroup.getId());
            	referencedByJSONObject.put("objectName", referencingTargetGroupName);
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
		} else if(refObjectType.equalsIgnoreCase("targetRule") || refObjectType.equalsIgnoreCase("rule")) {
			ConditionElement refTargetRule = ConditionElement.findById(refObjectId);
			String referencingTargetRuleName = refTargetRule.getName();

        	try {
            	referencedByJSONObject = new JSONObject();
            	referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.target.rule"));
            	referencedByJSONObject.put("objectId",   refTargetRule.getId());
            	referencedByJSONObject.put("objectName", referencingTargetRuleName);
        	} catch (Exception ex) {
        		ex.printStackTrace();
        	}
        } else if(refObjectType.equalsIgnoreCase("parameterGroup")) {
            ParameterGroup refParameterGroup = ParameterGroup.findById(refObjectId);
            String referencingParameterGroupName = refParameterGroup.getName();

            try {
                referencedByJSONObject = new JSONObject();
                referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.parameter.group"));
                referencedByJSONObject.put("objectId",   refParameterGroup.getId());
                referencedByJSONObject.put("objectName", referencingParameterGroupName);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else if(refObjectType.equalsIgnoreCase("variable") || refObjectType.equalsIgnoreCase("DataElementVariable")) {
            DataElementVariable refVariable = DataElementVariable.findById(refObjectId);
            String referencingVariableName = refVariable.getName();

            try {
                referencedByJSONObject = new JSONObject();
                referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.variable"));
                referencedByJSONObject.put("objectId",   refVariable.getId());
                referencedByJSONObject.put("objectName", referencingVariableName);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else if(refObjectType.equalsIgnoreCase("dataSource")) {
            DataSource refDataSource = DataSource.findById(refObjectId);
            if(! refDataSource.isLookupTableDataSource()) {
                String referencingDataSourceName = refDataSource.getName();

                try {
                    referencedByJSONObject = new JSONObject();
                    referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.data.source"));
                    referencedByJSONObject.put("objectId", refDataSource.getId());
                    referencedByJSONObject.put("objectName", referencingDataSourceName);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } else if(refObjectType.equalsIgnoreCase("dataCollection")) {
            DataSourceAssociation refDataSourceAssociation = DataSourceAssociation.findById(refObjectId);
            String referencingDataSourceAssociationName = refDataSourceAssociation.getName();

            try {
                referencedByJSONObject = new JSONObject();
                referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.data.source.association"));
                referencedByJSONObject.put("objectId",   refDataSourceAssociation.getId());
                referencedByJSONObject.put("objectName", referencingDataSourceAssociationName);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else if(refObjectType.equalsIgnoreCase("lookupTable")) {
            LookupTable refLookupTable = LookupTable.findById(refObjectId);
            String referencingLookupTableName = refLookupTable.getName();

            try {
                referencedByJSONObject = new JSONObject();
                referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.lookup.table"));
                referencedByJSONObject.put("objectId",   refLookupTable.getId());
                referencedByJSONObject.put("objectName", referencingLookupTableName);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else if(refObjectType.equalsIgnoreCase("MetadataFormTemplate")) {
            MetadataFormDefinition refMetadataFormDefinition = MetadataFormDefinition.findById(refObjectId);
            String referencingMetadataFormDefinitionName = refMetadataFormDefinition.getName();

            try {
                referencedByJSONObject = new JSONObject();
                referencedByJSONObject.put("objectType", ApplicationUtil.getMessage("page.label.metadata.template"));
                referencedByJSONObject.put("objectId",   refMetadataFormDefinition.getId());
                referencedByJSONObject.put("objectName", referencingMetadataFormDefinitionName);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
		}

		boolean alwaysSync = false;
		if(referencedByJSONObject != null) {
		    if(alwaysSyncIds != null) {
		        if(alwaysSyncIds.contains(refById)) {
		            alwaysSync = true;
                }
            }

            referencedByJSONObject.put("alwaysSync",   alwaysSync);
        }

		return referencedByJSONObject;
    }
}

