package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.job.ProcessJobDEServerClient;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.deserver.DEServer;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.platform.ws.client.WsClientDEWS;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class AsyncBundleDeliveryTestConnectionController implements Controller {

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        try {
            JSONObject result = new JSONObject();
            
            boolean isCertificateAuth = ServletRequestUtils.getBooleanParameter(request, "authentication[isCertificateAuth]", false);
            String password = getPassword(request);
            String privateKeyBase64 = ServletRequestUtils.getStringParameter(request, "authentication[privateKey]", null);
            String uri = getUri(request).toLowerCase();

            ProcessJobDEServerClient.TestConnectionResult testConnectionResult = ProcessJobDEServerClient.TestConnectionResult.UnknownError;

            if (uri.startsWith("http")) {
                String user = parseServerURIUser(getUri(request));
                WsClientDEWS dewsClient = null;
                dewsClient = new WsClientDEWS( getUri(request), user, password);
                result.put( "success", dewsClient.ping(MessagepointMultiTenantConnectionProvider.getPodMasterCode(),
								                		  Node.getCurrentBranchName(),
								                		  Node.getCurrentNodeName()) );
            } else {

                ProcessJobDEServerClient client = new ProcessJobDEServerClient();
                DEServer existingServer = getExistingServer(request);

                if (isCertificateAuth) {

                    String privateKey = null;

                    if (existingServer != null && existingServer.getPrivateSshKeyFile() != null) {
                        privateKey = new String(existingServer.getPrivateSshKeyFile().getFileContent());
                    }

                    if (StringUtils.hasText(privateKeyBase64)) {
                        privateKey = getFileContent(privateKeyBase64);
                    }

                    if (privateKey != null) {
                        testConnectionResult = client.testCertificateAuthConnection(getUri(request), privateKey, password);
                        result.put("success", testConnectionResult == ProcessJobDEServerClient.TestConnectionResult.Success);
                    }

                } else {
                    testConnectionResult = client.testPasswordAuthConnection(getUri(request), password);
                    result.put("success", testConnectionResult == ProcessJobDEServerClient.TestConnectionResult.Success);
                }

                if (!client.isSuccessful()) {
                    result.put("error", client.getErrorLog());
                }
            }

            if (!result.has("success")) {
                result.put("success", false);
            }

            result.put("resultCode", testConnectionResult.toString());
            result.put("resultValue", testConnectionResult.getValue());
            if(result.has("success") && result.get("success").toString().equals("true")){
				// Audit: Test connection 
				AuditEventUtil.push(UserUtil.getPrincipalUser(), AuditEventType.ID_EXECUTE, AuditObjectType.ID_BUNDLE_DELIVERY, uri, null, AuditActionType.ID_TEST_CONNECTION_REQUESTED_SUCCEED, 
						ApplicationUtil.getMessage(AuditActionType.MESSAGE_CODE_TEST_CONNECTION_REQUESTED_SUCCEED));
            }else{
            	// Audit: Test connection failed
    			AuditEventUtil.push(UserUtil.getPrincipalUser(), AuditEventType.ID_EXECUTE, AuditObjectType.ID_BUNDLE_DELIVERY, getUri(request).toLowerCase(), null, AuditActionType.ID_TEST_CONNECTION_REQUESTED_FAIL, 
    					ApplicationUtil.getMessage(AuditActionType.MESSAGE_CODE_TEST_CONNECTION_REQUESTED_FAIL));
            }


            response.setContentType("application/json");
            response.setStatus(HttpStatus.SC_OK);
            ServletOutputStream out = response.getOutputStream();

            out.write(result.toString().getBytes());
            out.flush();

        } catch (Exception | Error e) {
            LogUtil.getLog(AsyncBundleDeliveryTestConnectionController.class).error("Error:", e);
            response.setStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR);
			// Audit: Test connection failed
			AuditEventUtil.push(UserUtil.getPrincipalUser(), AuditEventType.ID_EXECUTE, AuditObjectType.ID_BUNDLE_DELIVERY, getUri(request).toLowerCase(), null, AuditActionType.ID_TEST_CONNECTION_REQUESTED_FAIL, 
					ApplicationUtil.getMessage(AuditActionType.MESSAGE_CODE_TEST_CONNECTION_REQUESTED_FAIL));

        }

        return null;
    }

    private String getPassword(HttpServletRequest request) {
        String password = ServletRequestUtils.getStringParameter(request, "authentication[password]", null);
        DEServer server = getExistingServer(request);

        if (password != null && !password.isEmpty() && server != null) {
            String existingPassHash = DigestUtils.md5Hex("messagepoint-" + server.getPassword());

            if (existingPassHash.equals(password)) {
                password = server.getPassword();
            }
        }

        return password;
    }

    private DEServer getExistingServer(HttpServletRequest request) {
        long deServerId = ServletRequestUtils.getLongParameter(request, "bundleDelivery[deServerId]", -1);
        long branchId = ServletRequestUtils.getLongParameter(request, "bundleDelivery[branchId]", -1);

        if (deServerId != -1 && branchId != -1) {
            Branch branch = Branch.findById(branchId);
            if (branch != null) {
                return DEServer.findById(deServerId, branch);
            }
        }

        return null;
    }

    private String getUri(HttpServletRequest httpServletRequest) throws ServletRequestBindingException {

        try {
            int port = ServletRequestUtils.getIntParameter(httpServletRequest, "server[port]", -1);
            String host = ServletRequestUtils.getStringParameter(httpServletRequest, "server[host]");
            String path = ServletRequestUtils.getStringParameter(httpServletRequest, "server[path]");
            String protocol = ServletRequestUtils.getStringParameter(httpServletRequest, "server[protocol]");
            String username = ServletRequestUtils.getStringParameter(httpServletRequest, "server[user]");

            switch (protocol) {
                case "sftp":
                case "scp":
                    port = port == -1 ? 22 : port;
                    break;
                case "http":
                    port = port == -1 ? 80 : port;
                    break;
                case "https":
                    port = port == -1 ? 443 : port;
                    break;

            }

            URI uri = new URI(protocol + "://" + URLEncoder.encode(username, StandardCharsets.UTF_8.name()) + "@" + host + ":" + port);

            if (org.apache.commons.lang3.StringUtils.isNotBlank(path)) {
                if(!StringUtils.startsWithIgnoreCase(path,"/")){
                    path = '/' + path;
                }
                uri = new URI(uri.getScheme(), uri.getUserInfo(), uri.getHost(), uri.getPort(), path, uri.getQuery(), uri.getFragment());
            }

            return uri.toString();
        } catch (Exception e) {
            return null;
        }

    }

    private String getFileContent(String data) {
        return new String(Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8)));
    }

    private String parseServerURIUser(String url) {
        URI uri = URI.create(url);
        return uri.getUserInfo() != null && uri.getUserInfo().contains(":") ? uri.getUserInfo().split(":")[0] : uri.getUserInfo();
    }

}
