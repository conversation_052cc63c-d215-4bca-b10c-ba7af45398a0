package com.prinova.messagepoint.controller.tagcloud;


import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.util.HibernateUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class TagCloudListWrapper  implements Serializable {

    private static final long serialVersionUID = 5826298241998379117L;

    private List<Long> selectedIds;
    private String actionValue;

    public TagCloudListWrapper() {
        super();
    }

    public TagCloudListWrapper(List<Long> selectedIds){
        super();
        this.selectedIds = selectedIds;
    }

    public List<Long> getSelectedIds() {
        return selectedIds;
    }

    public void setSelectedIds(List<Long> selectedIds) {
        this.selectedIds = selectedIds;
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue;
    }

    public List<TagCloud> getSelectedList() {
        List<TagCloud> selectedList = new ArrayList<>();
        for (Long selectedId : this.selectedIds)
            selectedList.add(HibernateUtil.getManager().getObject(TagCloud.class, selectedId));
        return selectedList;
    }

}