package com.prinova.messagepoint.controller.tagcloud;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.wrapper.AsyncTagCloudListVO;
import com.prinova.messagepoint.model.wrapper.AsyncTagCloudListWrapper;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.function.Predicate;

public class TagCloudListValidator extends MessagepointInputValidator{
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TagCloudListWrapper wrapper = (TagCloudListWrapper)commandObj;
			
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}

		switch (action) {
//			case TagCloudListController.ACTION_EDIT:
//				validateActionPermission(errors, "error.message.action.not.permitted", AsyncTagCloudListVO.TagCloudListVOFlags::isCanUpdate);
//				break;
			case TagCloudListController.ACTION_DELETE:
				validateActionPermission(errors, "error.message.action.not.permitted", AsyncTagCloudListVO.TagCloudListVOFlags::isCanDelete);
				break;
			default:
				break;
		}

	}

	//There is no check on individual tag clouds, so we just check if the user has permission to delete any tag clouds
	private void validateActionPermission(Errors errors, String errorMessage, Predicate<AsyncTagCloudListVO.TagCloudListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncTagCloudListVO vo = new AsyncTagCloudListVO();
		AsyncTagCloudListVO.TagCloudListVOFlags flags = new AsyncTagCloudListVO.TagCloudListVOFlags();
		AsyncTagCloudListWrapper.setActionFlags(flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorMessage);
		}
	}

}
