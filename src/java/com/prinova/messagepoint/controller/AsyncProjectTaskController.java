package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.tasks.TaskEditWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tasks.BulkUpdateTaskService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AsyncProjectTaskController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncProjectTaskController.class);

	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_TASK_ID 		= "task_id";
	public static final String REQ_PARM_ITEM_TYPE 		= "item_type";
	public static final String REQ_PARM_ITEM_ID 		= "item_id";
	
	public static final String ACTION_TASK_DATA			= "task_data";
	public static final String ACTION_TASK_SUMMARY		= "task_summary";
	public static final String ACTION_COMPLETE_TASK		= "complete";
	public static final String ACTION_TASK_COUNT		= "task_count";
	
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		String action 		= ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);
		long taskId 		= ServletRequestUtils.getLongParameter(request, REQ_PARM_TASK_ID, -1L);
		
		JSONObject returnObj = new JSONObject();

		if ( action.equalsIgnoreCase(ACTION_TASK_DATA) ) {
			
			int itemType 	= ServletRequestUtils.getIntParameter(request, REQ_PARM_ITEM_TYPE, 0);
			long itemId			= ServletRequestUtils.getLongParameter(request, REQ_PARM_ITEM_ID, -1L);
			
			boolean hasTaskViewAllPermission 	= UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL);
			boolean hasTaskViewMyPermission 	= UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
			
			JSONArray taskArray = new JSONArray();

			try {

				List<Task> tasks = new ArrayList<>();
				if ( itemType == 99 ) {
					List<Long> docIds = new ArrayList<>();
					docIds.add(itemId);
					List<Long> taskIds = Task.findAllByDocumentIds(docIds, false);
					for ( Long currentTaskId: taskIds )
						tasks.add( Task.findById(currentTaskId) );
				} else {
					tasks = Task.findAllByItemType(itemType, itemId);
				}
									
				if (!tasks.isEmpty()) {
					
					long now = new Date().getTime();
					
					for (Task task : tasks) {
						
						if ( hasTaskViewAllPermission || hasTaskViewMyPermission ) {
							if ( task.isMine() || (!task.isMine() && hasTaskViewAllPermission) ) {
						
								JSONObject taskObj = new JSONObject();
		
								taskObj.put("requirement"	, task.getRequirement() != null ? StringUtil.truncate(new String(task.getRequirement(), "UTF-8"), 100) : "" );
								taskObj.put("due_date"		, DateUtil.formatDate(task.getDueDate()) );
								taskObj.put("id"			, task.getId() );
								taskObj.put("is_new"		, (now - task.getCreated().getTime()) < 86400000 );
								taskObj.put("near_term"		, task.isNearTerm() );
								taskObj.put("overdue"		, task.isOverdue() );
								taskObj.put("is_mine"		, task.isMine() );
								taskObj.put("status"		, !task.isComplete() ? "active" : "complete" );
								taskObj.put("can_mark_complete", !task.isAssetAssociatedWithWorkflow());
								taskObj.put("task_name", task.getItemName());
								
								taskArray.put(taskObj);
							
							}
						}
						
					} // END FOR TASK LOOP
					
				} // END IF TASKS EXIST

			} catch (Exception e) {
				
				returnObj.put("error"	, true);
				returnObj.put("message"	, e.getMessage());
				
			}
			
			returnObj.put("tasks", taskArray);
			
			// END: ACTION TASK DATA

		} else if ( action.equalsIgnoreCase(ACTION_COMPLETE_TASK) ) {

			Task task = Task.findById(taskId);
			
			if ( task != null ) {
				
				User principal = UserUtil.getPrincipalUser();
				User requestor = User.findById(principal.getId());
				
				List<Task> list = new ArrayList<>();
				list.add(task);
				ServiceExecutionContext context = BulkUpdateTaskService.createContextForMarkComplete(list, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateTaskService.SERVICE_NAME,
						BulkUpdateTaskService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateTaskService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" id ").append(task.getId());
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
				}
				
				returnObj.put("status", "success");
			} else {
				returnObj.put("error", true);
				returnObj.put("message", "Task mark complete failed: task not found!");
			}
			
		} else if ( action.equalsIgnoreCase(ACTION_TASK_SUMMARY) ) {
			
			Task task = Task.findById(taskId);
			
			if ( task != null ) {
				
				try {
					returnObj.put("status"		, task.getStatus());
					returnObj.put("near_term"	, task.isNearTerm());
					returnObj.put("overdue"		, task.isOverdue());
					returnObj.put("assignee"	, task.getAssignees().iterator().next().getName() );
					returnObj.put("due"			, DateUtil.formatDate(task.getDueDate()) );
					returnObj.put("task_name", task.getItemName());

					returnObj.put("requirement"	, task.getRequirement() != null && task.getRequirement().length != 0 ? 
													new String(task.getRequirement(), "UTF-8").trim() : 
													ApplicationUtil.getMessage("page.label.none") );
					if(task.isInWorkflow()){
						returnObj.put("project_requirement", task.findNotesInCurrentStep() != null? 
														task.findNotesInCurrentStep():
														ApplicationUtil.getMessage("page.label.none"));
					}
					returnObj.put("id"			, task.getId() );
					returnObj.put("created"		, DateUtil.formatDate(task.getCreated()) );
					
					JSONArray metadataArray = new JSONArray();
					
					if ( task.getMetadataForm() != null ) {
						for ( MetadataFormItem currentItem: task.getMetadataForm().getFormItemsInOrder() ) {
							JSONObject metadataItemObj = new JSONObject();
							metadataItemObj.put("label", currentItem.getItemDefinition() != null ? currentItem.getItemDefinition().getName() : "");
							metadataItemObj.put("value", currentItem.getDisplayValue() != null && !currentItem.getDisplayValue().trim().isEmpty() ?
															currentItem.getDisplayValue() : 
															ApplicationUtil.getMessage("page.label.none") );
							metadataArray.put(metadataItemObj);
						}
					}
					returnObj.put("metadata"	, metadataArray );
					
					TaskEditWrapper taskWrapper = new TaskEditWrapper(task);
					
					JSONArray taskAssetsArray = new JSONArray();
					
					JSONObject assetsObj = null;
					JSONArray assetsArray = null;
					
					// Smart text
					if ( taskWrapper.getEmbeddedContent() != null) {
						assetsObj = new JSONObject();
						assetsArray = new JSONArray();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.embedded.content.and.canvas"));
						assetsArray.put(taskWrapper.getEmbeddedContent());
						assetsObj.put("items", assetsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Image library
					if ( taskWrapper.getContentLibrary() != null ) {
						assetsObj = new JSONObject();
						assetsArray = new JSONArray();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.content.library"));
						assetsArray.put(taskWrapper.getContentLibrary());
						assetsObj.put("items", assetsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Target groups
					if ( taskWrapper.getTargetGroup() != null ) {
						assetsObj = new JSONObject();
						assetsArray = new JSONArray();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.targetgroups"));
						assetsArray.put(taskWrapper.getTargetGroup());
						assetsObj.put("items", assetsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Inserts
					if ( taskWrapper.getInsert() != null ) {
						assetsObj = new JSONObject();
						assetsArray = new JSONArray();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.inserts"));
						assetsArray.put(taskWrapper.getInsert());
						assetsObj.put("items", assetsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					JSONObject assetsGroupObj = null;
					JSONArray assetsGroupsArray = null;
					JSONArray assetsGroupArray = null;
					
					// Messages
					if ( taskWrapper.getMessage() != null ) {
						assetsObj = new JSONObject();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.messages"));
						assetsGroupsArray = new JSONArray();
						assetsGroupObj = new JSONObject();
						assetsGroupArray = new JSONArray();
						Document doc = taskWrapper.getMessage().getFirstDocumentDelivery();
						assetsGroupObj.put("label", doc.getName());
						assetsGroupArray.put(taskWrapper.getMessage());

						assetsGroupObj.put("items", assetsGroupArray);
						assetsGroupsArray.put(assetsGroupObj);
						assetsObj.put("groups", assetsGroupsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Local Smart Text
					if ( taskWrapper.getLocalText() != null ) {
						assetsObj = new JSONObject();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.local.smart.text"));
						assetsGroupsArray = new JSONArray();
						assetsGroupObj = new JSONObject();
						assetsGroupArray = new JSONArray();
						Document doc = taskWrapper.getLocalText().getDocument();
						assetsGroupObj.put("label", doc.getName());
						assetsGroupArray.put(taskWrapper.getLocalText().getName());

						assetsGroupObj.put("items", assetsGroupArray);
						assetsGroupsArray.put(assetsGroupObj);
						assetsObj.put("groups", assetsGroupsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Local Images
					if ( taskWrapper.getLocalImage() != null ) {
						assetsObj = new JSONObject();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.local.images"));
						assetsGroupsArray = new JSONArray();
						assetsGroupObj = new JSONObject();
						assetsGroupArray = new JSONArray();
						Document doc = taskWrapper.getLocalImage().getDocument();
						assetsGroupObj.put("label", doc.getName());
						assetsGroupArray.put(taskWrapper.getLocalImage().getName());

						assetsGroupObj.put("items", assetsGroupArray);
						assetsGroupsArray.put(assetsGroupObj);
						assetsObj.put("groups", assetsGroupsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Variants
					if ( taskWrapper.getVariant() != null ) {
						assetsObj = new JSONObject();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.variants"));
						assetsGroupsArray = new JSONArray();
						assetsGroupObj = new JSONObject();
						assetsGroupArray = new JSONArray();
						Document doc = taskWrapper.getVariant().getDocument();
						assetsGroupObj.put("label", doc.getName());
						assetsGroupArray.put(taskWrapper.getVariant().getName());

						assetsGroupObj.put("items", assetsGroupArray);
						assetsGroupsArray.put(assetsGroupObj);
						assetsObj.put("groups", assetsGroupsArray);
						taskAssetsArray.put(assetsObj);
					}
					
					// Insert Schedules
					if ( taskWrapper.getInsertSchedule() != null ) {
						assetsObj = new JSONObject();
						assetsObj.put("label", ApplicationUtil.getMessage("page.label.insert.schedules"));
						assetsGroupsArray = new JSONArray();
						assetsGroupObj = new JSONObject();
						assetsGroupArray = new JSONArray();
						Document doc = null;
						assetsGroupObj.put("label", "Touchpoint");
						assetsGroupArray.put(taskWrapper.getInsertSchedule().getName());

						assetsGroupObj.put("items", assetsGroupArray);
						assetsGroupsArray.put(assetsGroupObj);
						assetsObj.put("groups", assetsGroupsArray);
						taskAssetsArray.put(assetsObj);
					}

					returnObj.put("assets", taskAssetsArray);

				} catch (Exception e) {
					
					returnObj.put("error"	, true);
					returnObj.put("message"	, e.getMessage());
					
				}

			} else {
				returnObj.put("error", true);
				returnObj.put("message", "Task summary failed: task not found!");
			}
			
		} else if ( action.equalsIgnoreCase(ACTION_TASK_COUNT) ) {
			
			int itemType 	= ServletRequestUtils.getIntParameter(request, REQ_PARM_ITEM_TYPE, 0);
			long itemId			= ServletRequestUtils.getLongParameter(request, REQ_PARM_ITEM_ID, -1L);

			List<Task> tasks = new ArrayList<>();
			if ( itemType == 99 ) {
				List<Long> docIds = new ArrayList<>();
				docIds.add(itemId);
				List<Long> taskIds = Task.findAllByDocumentIds(docIds, false);
				for ( Long currentTaskId: taskIds )
					tasks.add( Task.findById(currentTaskId) );
			} else {
				tasks = Task.findAllByItemType(itemType, itemId);
			}

			int myActiveTaskCount = 0;
			for ( Task currentTask: tasks )
				if ( !currentTask.isComplete() && currentTask.isMine() )
					myActiveTaskCount++;
			
			returnObj.put("task_count"	, myActiveTaskCount );

		} else {
			
			returnObj.put("error", true);
			returnObj.put("message", "Async Task: Invalid action type");
		}
		
		return returnObj.toString();
	}

}