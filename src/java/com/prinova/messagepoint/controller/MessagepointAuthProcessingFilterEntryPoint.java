package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;

public class MessagepointAuthProcessingFilterEntryPoint extends LoginUrlAuthenticationEntryPoint {


	/**
	 * @param loginFormUrl URL where the login page can be found. Should either be
	 *                     relative to the web-app context path (include a leading {@code /}) or an absolute
	 *                     URL.
	 */
	public MessagepointAuthProcessingFilterEntryPoint(String loginFormUrl) {
		super(loginFormUrl);
	}

	public String determineUrlToUseForThisRequest(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception){
		String targetUrl = super.determineUrlToUseForThisRequest(request, response, exception);

		// added node GUID into signin.jsp request
		//
		String requestedNodeGUID = request.getParameter("gd");
		if (requestedNodeGUID != null && !requestedNodeGUID.isEmpty())
			targetUrl += "?gd=" + requestedNodeGUID;
		else
		{
			DefaultSavedRequest savedRequest = (DefaultSavedRequest)request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST");
			if (savedRequest != null)
			{
				String[] requestedNodeGUIDValue = savedRequest.getParameterValues("gd");
				if ( requestedNodeGUIDValue != null && requestedNodeGUIDValue.length > 0 )
					targetUrl += "?gd=" + requestedNodeGUIDValue[0];
			}
		}
		
		return targetUrl;
	}
}
