package com.prinova.messagepoint.controller;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.prinova.messagepoint.model.audit.AuditEvent;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;

public class AsyncAuditingListVO extends AsyncListVO{

	private AuditEvent 				auditEvent;
	private String					date;
	private String					event;
	private String					action;
	private String					user;
	private String					target;
	private AuditingListVOFlags		flags;

	public void setAuditEvent(AuditEvent auditEvent) {
		this.auditEvent = auditEvent;
	}
	
	public AuditingListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(AuditingListVOFlags flags) {
		this.flags = flags;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getEvent() {
		return event;
	}

	public void setEvent(String event) {
		this.event = event;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getTarget() {
		if(target != null){
			return " <span class=\"dataTableItemName\">" + target + "</span>";
		}else{
			return "";
		}
	}

	public void setTarget(String target) {
		this.target = target;
	}

	public String getInfo() {
		if(this.auditEvent.getMetadata()!=null && !this.auditEvent.getMetadata().isEmpty()){
			// The metadata may be a JSON string or a HTML string
			try{
				new JSONObject(this.auditEvent.getMetadata());
			}catch(JSONException je){
				try{
					new JSONArray(this.auditEvent.getMetadata());
				}catch(JSONException je2){
					return  " <i class=\"fa fa-info-circle html-info\" style=\"padding-right: 8px; padding-left: 4px;\"><span style=\"display:none;\">" + this.auditEvent.getMetadata() + "</span></i>";
				}
			}
			return  " <i class=\"fa fa-info-circle json-info\" style=\"padding-right: 8px; padding-left: 4px;\"><span style=\"display:none;\">" + this.auditEvent.getMetadata() + "</span></i>";
		}else{
			return "";
		}
	}

	public String getUser() {
		return user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public static class AuditingListVOFlags{
		private boolean canEditAuditEvent;

		public boolean isCanEditAuditEvent() {
			return canEditAuditEvent;
		}

		public void setCanEditAuditEvent(boolean canEditAuditEvent) {
			this.canEditAuditEvent = canEditAuditEvent;
		}
	}
}
