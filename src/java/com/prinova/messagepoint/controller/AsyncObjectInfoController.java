package com.prinova.messagepoint.controller;

import ai.mpr.marcie.content.rationalizer.misc.CollectionUtils;
import ai.mpr.marcie.content.rationalizer.misc.JsonArrayBuilder;
import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ConnectionResource;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.model.admin.DataSubtype;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.font.*;
import com.prinova.messagepoint.model.insert.*;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.metadata.*;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.rationalizer.*;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.tag.TagUtils;
import com.prinova.messagepoint.tag.view.DocumentTag;
import com.prinova.messagepoint.util.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.getValueForJsonPath;
import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.readJsonObject;

public class AsyncObjectInfoController implements Controller {

    private static final String PAGE_LABEL_NONE = "page.label.none";
    private static final String VALUE = "value";
    private static final String LABEL = "label";
    private static final String ORIGIN_TYPE = "origin_type";
    private static final String IS_ORDER_VALUE = "is_order_value";
    private static final String ID = "id";
    private static final Log log = LogUtil.getLog(AsyncObjectInfoController.class);

    public static final String PARAM_OBJECTID = "objectId";
    public static final String PARAM_OBJECTDNA = "objectDna";
    public static final String PARAM_CONNECTOR = "connector";
    public static final String PARAM_CONNECTORS = "connectors[]";
    public static final String PARAM_TYPE = "type";
    public static final String PARAM_SUB_TYPE = "sub_type";
    public static final String PARAM_ZONE_ID = "zoneId";
    public static final String PARAM_TOUCHPOINT_SELECTION_CONTEXT_ID = "touchpointSelectionContextId";

    public static final String PARAM_RATIONALIZER_APPLICATION_ID = "rationalizerApplicationId";

    public static final String TYPE_INSERT_TARGETING = "insertTargeting";
    public static final String TYPE_MESSAGE_TARGETING = "messageTargeting";
    public static final String TYPE_TAG_TARGETING = "tagTargeting";
    public static final String TYPE_MESSAGE_TIMING = "messageTiming";
    public static final String TYPE_MESSAGE_PREVIEW = "messagePreview";
    public static final String TYPE_MESSAGE_SUMMARY = "messageSummary";
    public static final String TYPE_INSERT_SCHEDULE_SUMMARY = "insertScheduleSummary";
    public static final String TYPE_INSERT_SUMMARY = "insertSummary";
    public static final String TYPE_RATE_SHEET_SUMMARY = "rateSheetSummary";
    public static final String TYPE_RATE_SHEET_THRESHOLDS = "rateSheetThresholds";
    public static final String TYPE_TOUCHPOINT_SELECTION_SUMMARY = "touchpointSelectionSummary";
    public static final String TYPE_TOUCHPOINT_SELECTION_PROOF = "touchpointSelectionProof";
    public static final String TYPE_TAG_ASSOCIATIONS = "tagAssociations";
    public static final String TYPE_TAG_SUMMARY = "tagSummary";
    public static final String TYPE_VARIABLE = "variable";
    public static final String TYPE_DATA_RESOURCE = "dataResource";
    public static final String TYPE_ZONE_SUMMARY = "zoneSummary";
    public static final String TYPE_ZONE_REFERENCES = "zoneReferences";
    public static final String TYPE_MESSAGE_ZONE_SUMMARY = "messageZoneSummary";
    public static final String TYPE_LOCAL_CONTENT_STATUS = "localContentStatus";
    public static final String TYPE_EMBEDDED_CONTENT_STATUS = "embeddedContentStatus";
    public static final String TYPE_RATIONALIZER_CONTENT = "rationalizerContent";
    public static final String TYPE_RATIONALIZER_DOCUMENT = "rationalizerDocument";
    public static final String TYPE_METADATA_ITEM_VALUES = "metadataItemValues";
    public static final String TYPE_RATIONALIZER_QUERY_VALUES = "rationalizerQueryValues";
    public static final String RATIONALIZER_SYNCHRONIZATION_PROGRESS = "rationalizerSynchronizationProgress";
    public static final String RATIONALIZER_REINDEX_PROGRESS = "rationalizerReindexingInProgress";
    public static final String TYPE_TEXT_STYLE = "textStyle";
    public static final String TYPE_PARAGRAPH_STYLE = "paragraphStyle";
    public static final String TYPE_TEXT_STYLES = "textStyles";
    public static final String TYPE_RATIONALIZER_SHARED_CONTENT = "rationalizerSharedContent";
    public static final String TYPE_RATIONALIZER_CURRENT_CRUMB_PATH = "rationalizerCrumbPath";
    public static final String TYPE_RATIONALIZER_APPLICATION_PROPERTIES = "rationalizerApplicationProperties";
    public static final String TYPE_RATIONALIZER_SHARED_CONTENT_TEXT = "rationalizerSharedContentText";
    public static final String GLOBAL_SEARCH_EXPORT_PROGRESS = "globalSearchExportProgress";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE);

        if (type.equalsIgnoreCase(TYPE_VARIABLE) || type.equalsIgnoreCase(TYPE_DATA_RESOURCE) ||
                type.equalsIgnoreCase(TYPE_ZONE_SUMMARY) || type.equalsIgnoreCase(TYPE_LOCAL_CONTENT_STATUS) ||
                type.equalsIgnoreCase(TYPE_RATIONALIZER_DOCUMENT) || type.equalsIgnoreCase(TYPE_RATIONALIZER_CONTENT) ||
                type.equalsIgnoreCase(TYPE_RATIONALIZER_SHARED_CONTENT) || type.equalsIgnoreCase(TYPE_METADATA_ITEM_VALUES) ||
                type.equalsIgnoreCase(RATIONALIZER_SYNCHRONIZATION_PROGRESS) || type.equalsIgnoreCase(TYPE_TEXT_STYLE) ||
                type.equalsIgnoreCase(TYPE_MESSAGE_ZONE_SUMMARY) || type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_STATUS) ||
                type.equalsIgnoreCase(TYPE_TEXT_STYLES) ||
                type.equalsIgnoreCase(TYPE_RATIONALIZER_CURRENT_CRUMB_PATH) || type.equalsIgnoreCase(TYPE_PARAGRAPH_STYLE) ||
                type.equalsIgnoreCase(TYPE_RATIONALIZER_APPLICATION_PROPERTIES) || type.equalsIgnoreCase(TYPE_ZONE_REFERENCES) ||
                type.equalsIgnoreCase(TYPE_RATIONALIZER_SHARED_CONTENT_TEXT) || type.equalsIgnoreCase(RATIONALIZER_REINDEX_PROGRESS)) {
            response.setContentType("application/json");
        } else {
            response.setContentType("text/xml");
        }

        ServletOutputStream out = response.getOutputStream();

        try {
            out.write(getResponseXML(request, type).getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
        }

        return null;
    }

    private String getResponseXML(HttpServletRequest request, String type) {
        String returnContent = "Object info type invalid";

        if (type.equalsIgnoreCase(TYPE_TEXT_STYLE)) {

            JSONArray returnObj = new JSONArray();

            TextStyle textStyle = TextStyle.findByName(getConnector(request));
            if (textStyle != null) {
                long zoneId = getZoneId(request);
                if (zoneId <= 0) {
                    ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
                    zoneId = contentObject != null && contentObject.getZone() != null ? contentObject.getZone().getId() : -9;
                }

                Zone zone = Zone.findById(zoneId);
                TextStyleVO textStyleVO = new TextStyleVO(textStyle, zone != null ? zone.getDocument() : null);

                List<TextStyleVO> voList = new ArrayList<>();
                voList.add(textStyleVO);

                returnObj = ContentStyleUtils.getTextStyleData(voList);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_TEXT_STYLES)) {
            JSONArray returnObj = new JSONArray();
            String[] connectors = getConnectors(request);
            for (String connector : connectors) {
                TextStyle textStyle = TextStyle.findByName(connector);
                if (textStyle != null) {
                    long zoneId = getZoneId(request);
                    if (zoneId <= 0) {
                        ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
                        zoneId = contentObject != null && contentObject.getZone() != null ? contentObject.getZone().getId() : -9;
                    }

                    Zone zone = Zone.findById(zoneId);
                    TextStyleVO textStyleVO = new TextStyleVO(textStyle, zone != null ? zone.getDocument() : null);

                    List<TextStyleVO> voList = new ArrayList<>();
                    voList.add(textStyleVO);
                    JSONObject textStyleData = new JSONObject();
                    textStyleData.put(connector, ContentStyleUtils.getTextStyleData(voList));
                    returnObj.put(textStyleData);
                }
            }
            return returnObj.toString();
        } else if (type.equalsIgnoreCase(TYPE_PARAGRAPH_STYLE)) {

            JSONArray returnObj = new JSONArray();

            ParagraphStyle paragraphStyle = ParagraphStyle.findByName(getConnector(request));
            if (paragraphStyle != null) {
                long zoneId = getZoneId(request);
                if (zoneId <= 0) {
                    ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
                    zoneId = contentObject != null && contentObject.getZone() != null ? contentObject.getZone().getId() : -9;
                }

                Zone zone = Zone.findById(zoneId);
                ParagraphStyleVO paragraphStyleVO = new ParagraphStyleVO(paragraphStyle, zone != null ? zone.getDocument() : null);

                List<ParagraphStyleVO> voList = new ArrayList<>();
                voList.add(paragraphStyleVO);

                returnObj = ContentStyleUtils.getParagraphStyleData(voList);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(RATIONALIZER_SYNCHRONIZATION_PROGRESS)) {

            JSONObject returnObj = new JSONObject();
            long applicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APPLICATION_ID, -1);

            RationalizerApplication application = RationalizerApplication.findById(applicationId);

            if (application == null) {
                returnObj.put("error", true);
                returnObj.put("message", "Unable to retrieve rationalizer application ID " + applicationId);
                return returnObj.toString();
            }

            RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(application, true);

            boolean indexExists = rationalizerElasticSearchHandler.indexExists();
            if(indexExists && RationalizerApplicationSyncStatus.SYNCHRONIZATION_SERVER_DOWN.getStatusValue() == application.getAppSyncStatus()) {
                application.setAppSyncStatus(RationalizerApplicationSyncStatus.NOT_SYNCHRONIZED.getStatusValue());
                application.save();
            }
            try {
                RationalizerApplicationSyncStatus status = RationalizerApplicationSyncStatus.getByStatusValue(application.getAppSyncStatus());
                returnObj.put("iconType", status.getStatusIconName());
                returnObj.put("iconStyle", status.getStatusIconStyle());
                returnObj.put("tooltipText", ApplicationUtil.getMessage(status.getStatusIconTooltip()));
            } catch(JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }
            return returnObj.toString();

        } else if (type.equalsIgnoreCase(RATIONALIZER_REINDEX_PROGRESS)) {
            JSONObject returnObj = new JSONObject();
            long applicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APPLICATION_ID, -1);
            RationalizerApplication rationalizerApplication = RationalizerApplication.findById(applicationId);
            boolean reindexInProgress = false;
            if (rationalizerApplication != null && rationalizerApplication.isUploadInProgress()) {
                reindexInProgress = true;
            }
            try {
                if (rationalizerApplication == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer application ID " + applicationId);
                } else {
                    returnObj.put("reindexInProgress", reindexInProgress);
                }
            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }
            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_APPLICATION_PROPERTIES)) {
            JSONObject returnObj = new JSONObject();
            long applicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APPLICATION_ID, -1);
            RationalizerApplication application = RationalizerApplication.findById(applicationId);

            try {
                if (application == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer application ID " + applicationId);
                } else {
                    returnObj.put("currentApplicationName", application.getName());
                    returnObj.put("currentApplicationMetatags", application.getMetatags());
                    if (application.getLinkedDocument() != null) {
                        returnObj.put("touchpointSelectValue", application.getLinkedDocument().getId());
                    }
                }
            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }
            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_QUERY_VALUES)) {

            return new RationalizerQueryValuesProcessor(request, type).apply();

        } else if (type.equalsIgnoreCase(TYPE_METADATA_ITEM_VALUES)) {

            JSONObject returnObj = new JSONObject();

            long metadataFormItemDefinitionId = getObjectIdAsLong(request);
            MetadataFormItemDefinition formItemDefinition = MetadataFormItemDefinition.findById(metadataFormItemDefinitionId);
            try {

                if (formItemDefinition == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve metadata form item values ID " + metadataFormItemDefinitionId);
                } else {

                    JSONArray metadataItemValuesJSONArray = new JSONArray();
                    List<String> metadataItemValuesList = new ArrayList<>();

                    List<MetadataFormItem> formItems = MetadataFormItemDefinition.findMetadataFormItemsByDefinition(formItemDefinition);

                    for (MetadataFormItem currentFormItem : formItems) {
                        if (currentFormItem.getValue() == null) {
                            continue;
                        }
                        String[] currentValuesArray = currentFormItem.getValue().split(",");
                        for (int i = 0; i < currentValuesArray.length; i++) {
                            if (!metadataItemValuesList.contains(currentValuesArray[i].trim())) {
                                metadataItemValuesList.add(currentValuesArray[i].trim());
                                metadataItemValuesJSONArray.put(currentValuesArray[i].trim());
                            }
                        }
                    }

                    returnObj.put("values", metadataItemValuesJSONArray);
                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(GLOBAL_SEARCH_EXPORT_PROGRESS)) {

            JSONObject returnObj = new JSONObject();

            List<StatusPollingBackgroundTask> activeTasks = StatusPollingBackgroundTask.findAllActiveBackgroundTask();
            boolean globalSearchExportRunning = false;
            for (StatusPollingBackgroundTask activeTask : activeTasks) {
                if (activeTask.getType() == StatusPollingBackgroundTask.TYPE_GLOBAL_SEARCH_EXPORT_AS_CSV) {
                    globalSearchExportRunning = true;
                    break;
                }
            }
            returnObj.put("finished", !globalSearchExportRunning);

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_DOCUMENT)) {

            JSONObject returnObj = new JSONObject();

            long rationalizerDocumentId = getObjectIdAsLong(request);
            RationalizerDocument document = RationalizerDocument.findById(rationalizerDocumentId);
            try {

                if (document == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer document ID " + rationalizerDocumentId);
                } else {

                    JSONArray contentMetadataArray = new JSONArray();

                    if (document.getParsedDocumentForm() != null) {
                        for (MetadataFormItem currentFormItem : document.getParsedDocumentForm().getFormItems()) {
                            JSONObject metadataObj = new JSONObject();
                            metadataObj.put(ID, currentFormItem.getId());
                            metadataObj.put(VALUE, currentFormItem.getValue() != null && !currentFormItem.getValue().isEmpty() ?
                                    currentFormItem.getValue() :
                                    "<i>" + ApplicationUtil.getMessage(PAGE_LABEL_NONE) + "</i>");
                            contentMetadataArray.put(metadataObj);
                        }
                    }

                    returnObj.put("document_metadata", contentMetadataArray);

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_SHARED_CONTENT)) {

            JSONObject returnObj = new JSONObject();

            RationalizerSharedContent sharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(getObjectIdAsString(request));
            try {

                if (sharedContent == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer shared content");
                } else {

                    JSONArray sharedContentDocumentArray = new JSONArray();

                    List<RationalizerDocument> documents = sharedContent.getDocuments();
                    for (RationalizerDocument currentDocument : documents) {
                        JSONObject documentObj = new JSONObject();
                        documentObj.put(ID, currentDocument.getId());
                        documentObj.put("name", currentDocument.getName());
                        sharedContentDocumentArray.put(documentObj);
                    }

                    returnObj.put("documents", sharedContentDocumentArray);

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_SHARED_CONTENT_TEXT)) {
            JSONObject returnObj = new JSONObject();
            RationalizerSharedContent sharedContent = RationalizerSharedContent.findSharedContentByElasticSearchGuid(getObjectIdAsString(request));
            try {
                if (sharedContent == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer shared content");
                } else {
                    returnObj.put("sharedContentText", sharedContent.getMarkupContent());
                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();
        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_CURRENT_CRUMB_PATH)) {

            JSONObject returnObj = new JSONObject();
            JSONArray crumbValueArray = new JSONArray();

            long applicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APPLICATION_ID, -1);

            try {
                String selectionIdsString = ServletRequestUtils.getStringParameter(request, PARAM_OBJECTID, "-9");
                if (StringUtils.isNotEmpty(selectionIdsString)) {
                    Map<MetadataFormItemDefinition, MetadataFormItem> rationalizerNavTreePathMap = MetadataFormItem.findRationalizerNavTreePathMap(applicationId, selectionIdsString);
                    if (MapUtils.isNotEmpty(rationalizerNavTreePathMap)) {
                        for (MetadataFormItem formItem : rationalizerNavTreePathMap.values()) {
                            if (formItem == null || StringUtils.isEmpty(formItem.getValue())) {
                                crumbValueArray.put(ApplicationUtil.getMessage("page.label.NO.VALUE"));
                            } else {
                                crumbValueArray.put(formItem.getValue());
                            }
                        }
                    }
                }
                returnObj.put("crumb_values", crumbValueArray);

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_CONTENT)) {

            JSONObject returnObj = new JSONObject();

            RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(getObjectIdAsString(request));
            try {

                if (rationalizerContent == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer content");
                } else {

                    // If the content has associated a shared content, return object info for the shared content.
                    // Otherwise return the object info for the document content.

                    MetadataForm parsedContentForm = rationalizerContent.getParsedContentForm();

                    RationalizerSharedContent rationalizerSharedContent = rationalizerContent.computeRationalizerSharedContent();
                    if (rationalizerSharedContent != null) {
                        parsedContentForm = rationalizerSharedContent.getParsedContentForm();
                    } else {
                        RationalizerDocumentContent rationalizerDocumentContent = (RationalizerDocumentContent) rationalizerContent;
                        if (StringUtils.isEmpty(rationalizerDocumentContent.getMessageName())) {
                            rationalizerDocumentContent.updateMessageNameInContentMetadata();
                        }
                        if (StringUtils.isEmpty(rationalizerDocumentContent.getZoneConnector())) {
                            rationalizerDocumentContent.updateZoneConnectorInContentMetadata();
                        }
                    }

                    JSONArray contentMetadataArray = new JSONArray();

					if ( parsedContentForm != null ) {
						Set<MetadataFormItem> formItems = parsedContentForm.getFormItems();
						List<MetadataFormItem> sortedFormItems =
								formItems.stream().sorted((o1, o2) -> org.apache.commons.lang3.StringUtils.compareIgnoreCase(o1.getItemDefinition().getPrimaryConnector(), o2.getItemDefinition().getPrimaryConnector())).collect(Collectors.toList());
						for (MetadataFormItem currentFormItem : sortedFormItems) {
							JSONObject metadataObj = new JSONObject();
							metadataObj.put(ID, currentFormItem.getId());
							MetadataFormItemDefinition itemDefinition = currentFormItem.getItemDefinition();
							int originTypeId = itemDefinition.getOriginTypeId();
							String value = currentFormItem.getValue();
							if(originTypeId == MetadataFormItemOriginTypeEnum.CONTENT_PARSED.getId() && StringUtils.isNotEmpty(value)) {
						     	value =currentFormItem.computeMetadataJsonDisplayValue();
							} else {
								value = StringEscapeUtils.escapeHtml(value);
							}
							metadataObj.put(VALUE, StringUtils.isNotEmpty(value) ?
									value :
									"<i>" + ApplicationUtil.getMessage(PAGE_LABEL_NONE) + "</i>");
							metadataObj.put(LABEL, itemDefinition.getName());
							metadataObj.put(ORIGIN_TYPE, originTypeId);
							if (currentFormItem.getIsOrderItem()) {
								metadataObj.put(IS_ORDER_VALUE, true);
							}
							contentMetadataArray.put(metadataObj);
						}
					}

                    if (rationalizerContent instanceof RationalizerDocumentContent && rationalizerSharedContent != null) {
                        final RationalizerDocumentContent rationalizerDocumentContent = (RationalizerDocumentContent) rationalizerContent;
                        final Integer order = rationalizerDocumentContent.getOrder();
                        JSONObject crtJsonObject = new JSONObject();
                        crtJsonObject.put(ID, "order");
                        crtJsonObject.put(VALUE, order != null ? Integer.toString(order) : "");
                        crtJsonObject.put(LABEL, ApplicationUtil.getMessage("page.label.rationalizer.document.content.field.order"));
                        crtJsonObject.put(IS_ORDER_VALUE, true);
                        crtJsonObject.put(ORIGIN_TYPE, 0);
                        contentMetadataArray.put(crtJsonObject);

                        crtJsonObject = new JSONObject();
                        crtJsonObject.put(ID, "messageName");
                        crtJsonObject.put(VALUE, StringUtils.isNotEmpty(rationalizerDocumentContent.getMessageName()) ?
                                rationalizerDocumentContent.getMessageName() :
                                "<i>" + ApplicationUtil.getMessage(PAGE_LABEL_NONE) + "</i>");
                        crtJsonObject.put(LABEL, ApplicationUtil.getMessage("page.label.rationalizer.document.content.field.messageName"));
                        crtJsonObject.put(ORIGIN_TYPE, 0);
                        contentMetadataArray.put(crtJsonObject);

                        crtJsonObject = new JSONObject();
                        crtJsonObject.put(ID, "zoneConnector");
                        crtJsonObject.put(VALUE, StringUtils.isNotEmpty(rationalizerDocumentContent.getZoneConnector()) ?
                                rationalizerDocumentContent.getZoneConnector() :
                                "<i>" + ApplicationUtil.getMessage(PAGE_LABEL_NONE) + "</i>");
                        crtJsonObject.put(LABEL, ApplicationUtil.getMessage("page.label.rationalizer.document.content.field.zoneConnector"));
                        crtJsonObject.put(ORIGIN_TYPE, 0);
                        contentMetadataArray.put(crtJsonObject);
                    }

                    returnObj.put("content_metadata", contentMetadataArray);

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();


        } else if (type.equalsIgnoreCase(TYPE_LOCAL_CONTENT_STATUS)) {

            JSONObject returnObj = new JSONObject();

            ContentObject contentObject = ContentObject.findByDnaActiveDataFocusCentric(getObjectDna(request));
            try {

                if (contentObject == null || !contentObject.getIsTouchpointLocal()) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve local content");
                } else {

                    TouchpointSelection selection = getTouchpointSelectionContextId(request) > 0 ? TouchpointSelection.findById(getTouchpointSelectionContextId(request)) :
                            UserUtil.getCurrentSelectionContext(contentObject);
                    Document document = contentObject.getDocument();


                    boolean touchpointContentEditPerm = UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_CONTENT_EDIT);
                    boolean powerEditPerm = UserUtil.isPermissionGranted(Permission.ROLE_POWER_EDIT);
                    boolean isEnabledForVariantWorkflow = document.isEnabledForVariantWorkflow();

                    boolean workingCopyInitRequired = false;
                    boolean reassignRequired = false;

                    if (touchpointContentEditPerm) {

                        boolean isMineOrCanReassign = (contentObject.isMine() || (powerEditPerm && contentObject.canReassign())) && !contentObject.isReadyForApproval();
                        reassignRequired = !contentObject.isMine();

                        if (!contentObject.isStructuredContentEnabled() || selection == null || selection.isMaster() || contentObject.getOwningTouchpointSelection() == selection) {
                            // ROOT MESSAGE
                            if (!contentObject.hasWorkingData()) {
                                returnObj.put("editable", true);
                                workingCopyInitRequired = true;
                            } else {
                                returnObj.put("editable", isMineOrCanReassign);
                                if (!isMineOrCanReassign) {
                                    if (contentObject.isEditLocked()) {
                                        returnObj.put("message", ApplicationUtil.getMessage("page.text.content.currently.being.edited"));
                                    } else {
                                        returnObj.put("message", ApplicationUtil.getMessage("page.text.touchpoint.content.not.assigned.to.user"));
                                    }
                                }
                            }

                        } else {
                            // CHILD STRUCTURED MESSAGE
                            if (isEnabledForVariantWorkflow && selection.hasWorkingCopy()) {
                                returnObj.put("editable", selection.canUpdate(UserUtil.getPrincipalUser()));
                                if (!contentObject.hasWorkingData()) {
                                    workingCopyInitRequired = true;
                                }
                                if (!selection.canUpdate(UserUtil.getPrincipalUser())) {
                                    returnObj.put("message", ApplicationUtil.getMessage("page.text.not.authorized.to.edit.structured.touchpoint.content"));
                                }
                            } else if (isEnabledForVariantWorkflow && !selection.hasWorkingCopy()) {
                                returnObj.put("editable", false);
                                returnObj.put("message", ApplicationUtil.getMessage("page.text.variant.working.copy.required.to.edit.touchpoint.content"));
                            } else {
                                if (!contentObject.hasWorkingData()) {
                                    returnObj.put("editable", true);
                                    workingCopyInitRequired = true;
                                } else {
                                    returnObj.put("editable", isMineOrCanReassign);
                                    if (!isMineOrCanReassign) {
                                        if (contentObject.isEditLocked()) {
                                            returnObj.put("message", ApplicationUtil.getMessage("page.text.content.currently.being.edited"));
                                        } else {
                                            returnObj.put("message", ApplicationUtil.getMessage("page.text.touchpoint.content.not.assigned.to.user"));
                                        }
                                    }
                                }
                            }

                            TouchpointContentObjectContentSelection contentSelection = new TouchpointContentObjectContentSelection(contentObject, selection.getParameterGroupTreeNode(), null);
                            returnObj.put("contentSelectionId", contentSelection != null ? contentSelection.getId() : -1);

                        }
                    } else {
                        // NO EDIT PERM
                        returnObj.put("editable", false);
                        returnObj.put("message", ApplicationUtil.getMessage("page.text.no.touchpoint.content.edit.permission"));
                    }

                    returnObj.put("working_copy_init_required", workingCopyInitRequired);
                    returnObj.put("reassign_required", reassignRequired);
                    returnObj.put("type", "local_content");

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_STATUS)) {

            JSONObject returnObj = new JSONObject();

            ContentObject contentObject = ContentObject.findByDnaActiveDataFocusCentric(getObjectDna(request));
            try {

                if (contentObject == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve embedded content");
                } else {

                    boolean embeddedContentEditPerm = UserUtil.isPermissionGranted(Permission.ROLE_EMBEDDED_CONTENT_EDIT);
                    boolean powerEditPerm = UserUtil.isPermissionGranted(Permission.ROLE_POWER_EDIT);

                    boolean workingCopyInitRequired = false;
                    boolean reassignRequired = false;

                    if (embeddedContentEditPerm) {

                        boolean isMineOrCanReassign = (contentObject.isMine() || (powerEditPerm && contentObject.canReassign())) && !contentObject.isReadyForApproval();
                        reassignRequired = !contentObject.isMine();

                        if (contentObject.isFocusOnActiveData()) {
                            returnObj.put("editable", true);
                            workingCopyInitRequired = true;
                        } else {
                            returnObj.put("editable", isMineOrCanReassign);
                            if (!isMineOrCanReassign) {
                                if (contentObject.isEditLocked()) {
                                    returnObj.put("message", ApplicationUtil.getMessage("page.text.content.currently.being.edited"));
                                } else {
                                    returnObj.put("message", ApplicationUtil.getMessage("page.text.touchpoint.content.not.assigned.to.user"));
                                }
                            }
                        }

                    } else {
                        // NO EDIT PERM
                        returnObj.put("editable", false);
                        returnObj.put("message", ApplicationUtil.getMessage("page.text.no.embedded.content.edit.permission"));
                    }

                    returnObj.put("working_copy_init_required", workingCopyInitRequired);
                    returnObj.put("reassign_required", reassignRequired);
                    returnObj.put("type", "embedded_content");

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_DATA_RESOURCE)) {

            JSONObject returnObj = new JSONObject();

            DataResource dataResource = DataResource.findById(getObjectIdAsLong(request));
            try {

                if (dataResource == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve data resource");
                } else {

                    String sourceType = null;
                    if (dataResource.getDocument() != null) {
                        sourceType = dataResource.getDocument().getDataSourceAssociation().getPrimaryDataSource().isXML() ? "xml" : "flat";
                    } else if (dataResource.getTouchpointCollection() != null) {
                        sourceType = dataResource.getTouchpointCollection().getDataSourceAssociation().getPrimaryDataSource().isXML() ? "xml" : "flat";
                    }

                    JSONObject primaryDataFileObj = new JSONObject();
                    if (dataResource.isPrimaryLocal()) {
                        primaryDataFileObj.put("type", "local");
                        primaryDataFileObj.put("name", dataResource.getPrimaryDataFile().getName());
                        primaryDataFileObj.put("file_name", dataResource.getPrimaryDataFileName());
                        primaryDataFileObj.put("resource", HttpRequestUtil.getFileResourceToken(dataResource.getPrimaryDataFile().getFile().getPath()));
                        primaryDataFileObj.put(ID, dataResource.getPrimaryDataFile().getId());
                        primaryDataFileObj.put("source_type", sourceType);
                    } else {
                        primaryDataFileObj.put("type", "remote");
                    }
                    returnObj.put("primary_data_file", primaryDataFileObj);

                    JSONArray referenceDataFileArray = new JSONArray();
                    for (ConnectionResource currentConnectionResource : dataResource.getConnectionResources()) {

                        JSONObject referenceDataFileObj = new JSONObject();
                        if (currentConnectionResource.isLocal()) {
                            referenceDataFileObj.put("type", "local");
                            referenceDataFileObj.put("name", currentConnectionResource.getReferenceDataFile().getName());
                            referenceDataFileObj.put("file_name", currentConnectionResource.getReferenceDataFile().getFilename());
                            referenceDataFileObj.put("resource", HttpRequestUtil.getFileResourceToken(currentConnectionResource.getReferenceDataFile().getFile().getPath()));
                            referenceDataFileObj.put(ID, currentConnectionResource.getReferenceDataFile().getId());
                            referenceDataFileObj.put("source_type", sourceType);
                        } else {
                            referenceDataFileObj.put("type", "remote");
                        }
                        referenceDataFileArray.put(referenceDataFileObj);

                    }
                    returnObj.put("reference_data_files", referenceDataFileArray);

                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_VARIABLE)) {

            JSONObject returnObj = new JSONObject();

            DataElementVariable variable = DataElementVariable.findById(getObjectIdAsLong(request));
            try {
                AbstractDataElement defaultDataElement = variable.getDefaultDataElement();
                returnObj.put("var_type", defaultDataElement != null ? defaultDataElement.getDataSubtypeName() : null);

                Boolean hasFormat = defaultDataElement != null ? variable.getDefaultDataElement().getDataSubtypeId() == DataSubtype.DATA_SUBTYPE_DATE : false;
                returnObj.put("has_format", hasFormat);

                if (hasFormat) {
                    returnObj.put("default_date_format", ApplicationUtil.getProperty(SystemPropertyKeys.DataFormat.KEY_DATE_DefaultFormat));
                }

                returnObj.put("sample_value", variable.getSampleValue());
                returnObj.put("dna", variable.getDna());

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_ZONE_REFERENCES)) {

            JSONObject returnObj = new JSONObject();
            JSONArray refMessageArray = new JSONArray();

            Zone zone = Zone.findById(getObjectIdAsLong(request));
            if (zone != null) {
                Zone contextZone = Zone.findZoneInContextByParent(zone);
                try {
                    List<ContentObject> referenceMessages = contextZone.getMessageDeliveries();
                    for (ContentObject contentObject : referenceMessages) {
                        JSONObject msgObj = new JSONObject();
                        msgObj.put("name", contentObject.getName());
                        msgObj.put("variant", (contentObject.getOwningTouchpointSelection() != null ? contentObject.getOwningTouchpointSelection().getName() : ""));
                        refMessageArray.put(msgObj);
                    }

                    returnObj.put("messages", refMessageArray);
                    returnObj.put("communication_count", contextZone.getCommunicationsCount());

                } catch (JSONException e) {
                    log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
                }
            }

            return returnObj.toString();

        } else if (type.equalsIgnoreCase(TYPE_ZONE_SUMMARY) || type.equalsIgnoreCase(TYPE_MESSAGE_ZONE_SUMMARY)) {

            JSONObject returnObj = new JSONObject();

            Zone zone = null;
            if (type.equalsIgnoreCase(TYPE_MESSAGE_ZONE_SUMMARY)) {
                ContentObject contentObject = ContentObject.findById(getObjectIdAsLong(request));
                zone = contentObject.getZone();
            } else if (type.equalsIgnoreCase(TYPE_ZONE_SUMMARY)) {
                if(getObjectIdAsLong(request)>0) {
                    zone = Zone.findById(getObjectIdAsLong(request));
                }else {
                    zone = Zone.findByDnaAndDocument(getObjectDna(request), UserUtil.getCurrentTouchpointContext());
                }
            }

            if (zone != null) {
                Zone contextZone = Zone.findZoneInContextByParent(zone);
                try {

                    // ZONE
                    returnObj.put("name", contextZone.getFriendlyName().replaceAll("'", ""));
                    returnObj.put("connector", contextZone.getName());
                    returnObj.put("top", DecimalValueUtil.dehydrate(contextZone.getTopY()));
                    returnObj.put("left", DecimalValueUtil.dehydrate(contextZone.getTopX()));
                    returnObj.put("is_transparent", contextZone.getBackgroundColor().equals(Zone.ZONE_BACKGROUND_TRANSPARENT));
                    returnObj.put("is_multipart", contextZone.isMultipart());
                    returnObj.put("is_flowzone", contextZone.isFlowZone());

                    // PARTS
                    if (!contextZone.getParts().isEmpty()) {
                        JSONArray parts = new JSONArray();
                        for (ZonePart currentPart : contextZone.getPartsInOrder()) {
                            JSONObject part = new JSONObject();
                            part.put("name", currentPart.getName());
                            part.put("content_type", ApplicationUtil.getMessage(currentPart.getContentType().getName()));
                            if (currentPart.getSubContentType() != null) {
                                part.put("sub_content_type", currentPart.getSubContentType().getName());
                            }

                            parts.put(part);
                        }
                        returnObj.put("parts", parts);
                    }

                    // SECTION
                    JSONObject sectionObj = new JSONObject();
                    DocumentSection section = contextZone.getSection();

                    if (section != null) {
                        double mT = Double.valueOf(DecimalValueUtil.dehydrate(section.getMarginTop())) * 100;
                        double mB = Double.valueOf(DecimalValueUtil.dehydrate(section.getMarginBottom())) * 100;
                        double mR = Double.valueOf(DecimalValueUtil.dehydrate(section.getMarginRight())) * 100;
                        double mL = Double.valueOf(DecimalValueUtil.dehydrate(section.getMarginLeft())) * 100;
                        double headerHeight = Double.valueOf(DecimalValueUtil.dehydrate(section.getHeaderHeight())) * 100;
                        double footerHeight = Double.valueOf(DecimalValueUtil.dehydrate(section.getFooterHeight())) * 100;
                        double regionLeftWidth = Double.valueOf(DecimalValueUtil.dehydrate(section.getRegionLeftWidth())) * 100;
                        double regionRightWidth = Double.valueOf(DecimalValueUtil.dehydrate(section.getRegionRightWidth())) * 100;
                        double bodyWidth = (int) Math.round(DocumentTag.getFullScaleSectionWidth(section) - mL - mR - regionLeftWidth - regionRightWidth);
                        double bodyHeight = (int) Math.round(DocumentTag.getFullScaleSectionHeight(section) - mT - mB - headerHeight - footerHeight);
                        double width = (int) Math.round(DocumentTag.getFullScaleSectionWidth(section));
                        double height = (int) Math.round(DocumentTag.getFullScaleSectionHeight(section));

                        sectionObj.put("margin_top", mT);
                        sectionObj.put("margin_right", mB);
                        sectionObj.put("margin_bottom", mR);
                        sectionObj.put("margin_left", mL);
                        sectionObj.put("header_height", headerHeight);
                        sectionObj.put("footer_height", footerHeight);
                        sectionObj.put("region_left_width", regionLeftWidth);
                        sectionObj.put("region_right_width", regionRightWidth);
                        sectionObj.put("body_width", bodyWidth);
                        sectionObj.put("body_height", bodyHeight);
                        sectionObj.put("width", width);
                        sectionObj.put("height", height);

                        sectionObj.put("apply_background_image", section.isApplyBackgroundImageForTesting());
                        sectionObj.put("fit_to_canvas", section.isShrinkBackgroundToFit());
                        if (section.getImageLocation() != null) {
                            String sectionImgSrc = ApplicationUtil.getWebRoot() + "download/image.form?resource=" + section.getImageFileResourceToken();
                            sectionObj.put("background_image", sectionImgSrc);
                        }
                    }

                    returnObj.put("section", sectionObj);

                } catch (JSONException e) {
                    log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
                } catch (ParseException e1) {
                    e1.printStackTrace();
                }
            }

            return returnObj.toString();

        } else if (type.indexOf("insertSchedule") != -1) {
            InsertSchedule insertSchedule = InsertSchedule.findById(getObjectIdAsLong(request));

            if (insertSchedule == null) {
                return "<?xml version='1.0'?><content><error>InvalidInsertScheduleID</error></content>";
            }

            if (type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_SUMMARY)) {
                returnContent = getInsertScheduleSummaryXML(insertSchedule);
            }

        } else if (type.indexOf("insert") != -1) {
            Insert insert = Insert.findById(getObjectIdAsLong(request));

            if (insert == null) {
                return "<?xml version='1.0'?><content><error>InvalidInsertID</error></content>";
            }

            if (type.equalsIgnoreCase(TYPE_INSERT_TARGETING)) {
                returnContent = getTargetingXML(insert);
            } else if (type.equalsIgnoreCase(TYPE_INSERT_SUMMARY)) {
                returnContent = getInsertSummaryXML(insert);
            }

        } else if (type.indexOf("rateSheet") != -1) {
            RateSchedule rateSchedule = RateSchedule.findById(getObjectIdAsLong(request));

            if (rateSchedule == null) {
                return "<?xml version='1.0'?><content><error>InvalidRateSheetID</error></content>";
            }

            if (type.equalsIgnoreCase(TYPE_RATE_SHEET_SUMMARY)) {
                returnContent = getRateSheetSummaryXML(rateSchedule);
            } else if (type.equalsIgnoreCase(TYPE_RATE_SHEET_THRESHOLDS)) {
                returnContent = getRateSheetThresholdsXML(rateSchedule);
            }

        } else if (type.indexOf("message") != -1) {
            ContentObject message = ContentObject.findById(getObjectIdAsLong(request));

            if (message == null) {
                return "<?xml version='1.0'?><content><error>InvalidMessageID</error></content>";
            }

            if (type.equalsIgnoreCase(TYPE_MESSAGE_TARGETING)) {
                returnContent = getTargetingXML(message);
            } else if (type.equalsIgnoreCase(TYPE_MESSAGE_TIMING)) {
                returnContent = getMessageTimingXML(message);
            } else if (type.equalsIgnoreCase(TYPE_MESSAGE_PREVIEW)) {
                returnContent = getMessagePreviewXML(message);
            } else if (type.equalsIgnoreCase(TYPE_MESSAGE_SUMMARY)) {
                returnContent = getMessageSummaryXML(message);
            }

        } else if (type.indexOf("touchpointSelection") != -1) {
            TouchpointSelection touchpointSelection = TouchpointSelection.findById(getObjectIdAsLong(request));

            if (touchpointSelection == null) {
                return "<?xml version='1.0'?><content><error>InvalidTouchpointSelectionID</error></content>";
            }

            if (type.indexOf(TYPE_TOUCHPOINT_SELECTION_SUMMARY) != -1) {
                returnContent = getTouchpointSelectionSummaryXML(touchpointSelection, type.substring(type.indexOf("_") + 1));
            } else if (type.equalsIgnoreCase(TYPE_TOUCHPOINT_SELECTION_PROOF)) {
                returnContent = getTouchpointSelectionProofXML(touchpointSelection);
            }

        } else if (type.indexOf("tag") != -1) {
            Tag tag = Tag.findById(getObjectIdAsLong(request));

            if (tag == null) {
                return "<?xml version='1.0'?><content><error>InvalidTagID</error></content>";
            }

            if (type.equalsIgnoreCase(TYPE_TAG_TARGETING)) {
                returnContent = getTargetingXML(tag);
            } else if (type.equalsIgnoreCase(TYPE_TAG_SUMMARY)) {
                returnContent = getTagSummaryXML(tag);
            } else if (type.equalsIgnoreCase(TYPE_TAG_ASSOCIATIONS)) {
                returnContent = getTagAssociationsXML(tag);
            }
        }

        return "<?xml version='1.0'?><content>" + returnContent + "</content>";
    }

    private String getTargetingXML(Targetable object) {
        String xmlContent = "<targetingContent>";
        StringBuilder targeting = new StringBuilder("<b>" + ApplicationUtil.getMessage("page.label.targeting.summary") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")<br>");
        if (object.getTargetGroupCount() != 0) {
            if (!object.getIncludedTargetGroups().isEmpty()) {
                targeting.append(TagUtils.formatLabel("page.label.include.groups")).append("<br>");
                for (TargetGroup currentGroup : object.getIncludedTargetGroups()) {
                    targeting.append("&nbsp;&nbsp;").append(currentGroup.getName()).append("<br>");
                }
            }
            if (!object.getExtendedTargetGroups().isEmpty()) {
                targeting.append(TagUtils.formatLabel("page.label.and.of.these.include.groups")).append("<br>");
                for (TargetGroup currentGroup : object.getExtendedTargetGroups()) {
                    targeting.append("&nbsp;&nbsp;").append(currentGroup.getName()).append("<br>");
                }
            }
            if (!object.getExcludedTargetGroups().isEmpty()) {
                targeting.append(TagUtils.formatLabel("page.label.exclude.customers")).append("<br>");
                for (TargetGroup currentGroup : object.getExcludedTargetGroups()) {
                    targeting.append("&nbsp;&nbsp;").append(currentGroup.getName()).append("<br>");
                }
            }
        } else {
            targeting.append(TagUtils.formatLabel("page.text.playtoallcustomers"));
        }
        xmlContent += StringEscapeUtils.escapeXml(targeting.toString()) + "</targetingContent>";
        return xmlContent;
    }

    private String getMessageTimingXML(ContentObject message) {
        String xmlContent = "<timingContent>";

        String timing = "<b>" + ApplicationUtil.getMessage("page.label.timing.summary") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")<br>";
        timing += TagUtils.formatLabel("page.label.start.date") + ": ";
        if (message.getStartDate() == null) {
            timing += TagUtils.formatLabel(PAGE_LABEL_NONE) + "<br>";
        } else {
            timing += DateUtil.formatDate(message.getStartDate()) + "<br>";
        }

        timing += TagUtils.formatLabel("page.label.end.date") + ": ";
        if (message.getEndDate() == null) {
            timing += TagUtils.formatLabel(PAGE_LABEL_NONE) + "<br>";
        } else {
            timing += DateUtil.formatDate(message.getEndDate()) + "<br>";
        }

        if (message.getStartDate() != null && message.isRepeatDatesAnnually()) {
            timing += ApplicationUtil.getMessage("page.text.repeats.annually.brackets");
        }

        xmlContent += StringEscapeUtils.escapeXml(timing) + "</timingContent>";
        return xmlContent;
    }

    private String getMessagePreviewXML(ContentObject message) {
        DocumentPreview preview = message.getLatestPreview();

        String xmlContent = "<previewContent>";

        String previewInfo = "<b>" + ApplicationUtil.getMessage("page.label.last.preview") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")<br>" +
                ApplicationUtil.getMessage("page.label.generated") + ": " + (preview.getRequestDate() != null ? DateUtil.formatDateTime(preview.getRequestDate()) : ApplicationUtil.getMessage("page.label.na")) + "<br>";

        previewInfo += ApplicationUtil.getMessage("page.label.by") + ": " + preview.getUserFullName();

        xmlContent += StringEscapeUtils.escapeXml(previewInfo) + "</previewContent>";
        return xmlContent;
    }

    private String getTagSummaryXML(Tag tag) {
        String xmlContent = "<summaryContent>";
        String summaryInfo = ApplicationUtil.getMessage("page.label.created.on") + ": " + (!tag.getCreatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(tag.getCreated()) : ApplicationUtil.getMessage("page.label.na")) + "<br>" +
                ApplicationUtil.getMessage("page.label.last.edited") + ": " + (!tag.getUpdatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(tag.getUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getInsertScheduleSummaryXML(InsertSchedule insertSchedule) {
        String xmlContent = "<summaryContent>";
        String summaryInfo = ApplicationUtil.getMessage("page.label.created.on") + ": " + (!insertSchedule.getCreatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(insertSchedule.getCreated()) : ApplicationUtil.getMessage("page.label.na")) + "<br>" +
                ApplicationUtil.getMessage("page.label.last.edited") + ": " + (!insertSchedule.getUpdatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(insertSchedule.getUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getInsertSummaryXML(Insert insert) {
        int defaultWeightUnitId = Integer.parseInt(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Insert.KEY_WeightUnits));
        WeightUnit defaultWeightUnit = new WeightUnit(defaultWeightUnitId);
        String xmlContent = "<summaryContent>";
        String summaryInfo = ApplicationUtil.getMessage("page.label.delivery") + ": " + insert.getDeliveryType().getDisplayText() + "<br>" +
                ApplicationUtil.getMessage("page.label.weight") + ": " + insert.getDehydratedWeight() + " " + defaultWeightUnit.getDisplayText() + "<br>" +
                ApplicationUtil.getMessage("page.label.created.on") + ": " + (!insert.getCreatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(insert.getCreated()) : ApplicationUtil.getMessage("page.label.na")) + "<br>" +
                ApplicationUtil.getMessage("page.label.last.edited") + ": " + (!insert.getUpdatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(insert.getUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getRateSheetSummaryXML(RateSchedule rateSchedule) {
        String xmlContent = "<summaryContent>";
        String summaryInfo = ApplicationUtil.getMessage("page.label.created.on") + ": " + (!rateSchedule.getCreatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(rateSchedule.getCreated()) : ApplicationUtil.getMessage("page.label.na")) + "<br>" +
                ApplicationUtil.getMessage("page.label.last.edited") + ": " + (!rateSchedule.getUpdatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(rateSchedule.getUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getMessageSummaryXML(ContentObject message) {
        String xmlContent = "<summaryContent>";
        String summaryInfo = "";
        if (message.isFocusOnWorkingData()) {
            summaryInfo = "<b>" + ApplicationUtil.getMessage("page.label.status.wip") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")" + "<br>" +
                    ApplicationUtil.getMessage("page.label.ID") + ": " + message.getExternalId() + "<br>" +
                    ApplicationUtil.getMessage("page.label.created.on") + ": " + (!message.getCreatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(message.getCreated()) : ApplicationUtil.getMessage("page.label.na")) + "<br>" +
                    ApplicationUtil.getMessage("page.label.last.edited") + ": " + (!message.getUpdatedDate().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(message.getUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        } else if (message.isFocusOnActiveData()) {
            summaryInfo = "<b>" + ApplicationUtil.getMessage("page.label.status.production") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")" + "<br>" +
                    ApplicationUtil.getMessage("page.label.ID") + ": " + message.getExternalId() + "<br>";
        } else if (message.isFocusOnArchivedData()) {
            summaryInfo = "<b>" + ApplicationUtil.getMessage("page.label.status.archived") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")" + "<br>" +
                    ApplicationUtil.getMessage("page.label.ID") + ": " + message.getExternalId() + "<br>";
        }
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getTagAssociationsXML(Tag tag) {
        String xmlContent = "<summaryContent>";
        String summaryInfo = ApplicationUtil.getMessage("page.label.message.associations") + ": " + tag.getMessages().size() + "<br>";
        if (tag.getDocuments().iterator().next().isEnabledForInserts()) {
            summaryInfo += ApplicationUtil.getMessage("page.label.insert.associations") + ": " + tag.getInserts().size();
        }
        xmlContent += StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
        return xmlContent;
    }

    private String getRateSheetThresholdsXML(RateSchedule rateSchedule) {
        String xmlContent = "<summaryContent>";
        StringBuilder summaryInfo = new StringBuilder();
        for (RateScheduleDetail currentThreshold : rateSchedule.getRateScheduleDetailsSorted()) {
            summaryInfo.append(ApplicationUtil.getMessage("page.text.less.than")).append(" <b>").append(currentThreshold.getDehydratedWeight()).append("</b> ").append(rateSchedule.getRateScheduleCollection().getWeightUnit().getDisplayText()).append(", ").append(ApplicationUtil.getMessage("page.text.for")).append(" $<b>").append(currentThreshold.getDehydratedRate()).append("</b><br>");
        }

        xmlContent += StringEscapeUtils.escapeXml(summaryInfo.toString()) + "</summaryContent>";
        return xmlContent;
    }

    private String getTouchpointSelectionSummaryXML(TouchpointSelection touchpointSelection, String status) {

        String summaryInfo = "";
        if (status.equals("workingCopy")) {
            summaryInfo = "<b>" + ApplicationUtil.getMessage("page.label.status.wip") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")" + "<br>" +
                    ApplicationUtil.getMessage("page.label.content.last.edited") + ": " + (!touchpointSelection.getContentLastUpdatedStr().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(touchpointSelection.getContentLastUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        } else if (status.equals("active")) {
            summaryInfo = "<b>" + ApplicationUtil.getMessage("page.label.status.production") + "</b> (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")" + "<br>" +
                    ApplicationUtil.getMessage("page.label.approved") + ": " + (touchpointSelection.getLatestProductionDate() != null ? DateUtil.formatDateTime(touchpointSelection.getLatestProductionDate()) : ApplicationUtil.getMessage("page.label.not.specified")) + "<br>" +
                    ApplicationUtil.getMessage("page.label.content.last.edited") + ": " + (!touchpointSelection.getContentLastUpdatedStr().equalsIgnoreCase(ApplicationUtil.getMessage(PAGE_LABEL_NONE)) ? DateUtil.formatDateTime(touchpointSelection.getContentLastUpdated()) : ApplicationUtil.getMessage("page.label.na"));
        }

        return "<summaryContent>" + StringEscapeUtils.escapeXml(summaryInfo) + "</summaryContent>";
    }

    private String getTouchpointSelectionProofXML(TouchpointSelection touchpointSelection) {
        Proof proof = touchpointSelection.getLatestCompletedProof();

        String xmlContent = "<previewContent>";

        String proofInfo = "<b>" + ApplicationUtil.getMessage("page.label.last.proof") + "</b>" +
                (proof.isStaled() ? " - " + ApplicationUtil.getMessage("page.label.stale") + "" : "") +
                (proof.isActive() ? " - " + ApplicationUtil.getMessage("page.label.status.production") + "" : "") +
                " (" + ApplicationUtil.getMessage("page.text.click.to.view") + ")<br>" +
                ApplicationUtil.getMessage("page.label.generated") + ": " + (proof.getRequestDate() != null ? DateUtil.formatDateTime(proof.getRequestDate()) : ApplicationUtil.getMessage("page.label.na")) + "<br>";
        if (proof.getCreatedBy() != null) {
            proofInfo += ApplicationUtil.getMessage("page.label.by") + ": " + proof.getCreatedByName();
        } else {
            proofInfo += ApplicationUtil.getMessage("page.label.by") + ": n/a";
        }

        xmlContent += StringEscapeUtils.escapeXml(proofInfo) + "</previewContent>";
        return xmlContent;
    }

    private String getObjectIdAsString(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, PARAM_OBJECTID, "");
    }

    private static Long getObjectIdAsLong(HttpServletRequest request) {
        return ServletRequestUtils.getLongParameter(request, PARAM_OBJECTID, -9);
    }

    private static String getObjectDna(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, PARAM_OBJECTDNA, "");
    }

    private String getConnector(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, PARAM_CONNECTOR, null);
    }

    private String[] getConnectors(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameters(request, PARAM_CONNECTORS);
    }

    private Long getZoneId(HttpServletRequest request) {
        return ServletRequestUtils.getLongParameter(request, PARAM_ZONE_ID, -9);
    }

    private Long getTouchpointSelectionContextId(HttpServletRequest request) {
        return ServletRequestUtils.getLongParameter(request, PARAM_TOUCHPOINT_SELECTION_CONTEXT_ID, -9);
    }

    public static class RationalizerQueryValuesProcessor {

        public static final long ITEM_DEF_PRESENT_IN_DOCUMENT_METADATA = 2L;
        public static final long ITEM_DEF_PRESENT_IN_CONTENT_METADATA = 3L;
        public static final long TAG_CLOUD_FOR_DOCUMENT = 4L;
        public static final long TAG_CLOUD_FOR_CONTENT = 5L;
        private HttpServletRequest request;
        private String type;

        private RationalizerQueryValuesProcessor(HttpServletRequest request, String type) {
            this.request = request;
            this.type = type;
        }

        private String apply() {
            JSONObject returnObj = new JSONObject();

            long applicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APPLICATION_ID, -1);

            RationalizerApplication application = RationalizerApplication.findById(applicationId);
            try {

                if (application == null) {
                    returnObj.put("error", true);
                    returnObj.put("message", "Unable to retrieve rationalizer application");

                    return returnObj.toString();
                }


                String connector = ServletRequestUtils.getStringParameter(request, PARAM_SUB_TYPE, null);
                int inputType = MetadataFormItemType.ID_TEXT;
                long valueType = getObjectIdAsLong(request);

                if (connector == null) {

                    return handleNullConnector(returnObj, application, inputType);
                }

                if (valueType == ITEM_DEF_PRESENT_IN_DOCUMENT_METADATA || valueType == ITEM_DEF_PRESENT_IN_CONTENT_METADATA) {

                    return handleValueType_2_or_3(returnObj, application, connector, inputType, valueType);
                }

                if (valueType == TAG_CLOUD_FOR_DOCUMENT || valueType == TAG_CLOUD_FOR_CONTENT) {

                    return handleValueType_4_or_5(returnObj, application, valueType);
                }

            } catch (JSONException e) {
                log.error("Error - Unable to process request for object[" + type + "] data: " + e.getMessage(), e);
            }

            return returnObj.toString();
        }

        private String handleValueType_4_or_5(JSONObject returnObj, RationalizerApplication application, long valueType) {
            JSONArray valuesArray = new JSONArray();
            List<TagCloud> tagCloudList = new ArrayList<>();

            if (valueType == TAG_CLOUD_FOR_DOCUMENT) {
                tagCloudList = TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_DOCUMENT, -1, application.getId());
            } else if (valueType == TAG_CLOUD_FOR_CONTENT) {
                tagCloudList = TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_CONTENT, -1, application.getId());
            }

            for (TagCloud tagCloud : tagCloudList) {
                valuesArray.put(tagCloud.getName());
            }

            returnObj.put("values", valuesArray);
            return returnObj.toString();
        }

        private String handleValueType_2_or_3(
                JSONObject returnObj,
                RationalizerApplication application,
                String connector,
                int inputType,
                long valueType
        ) {
            Map<String, Integer> valuesMap = new HashMap<>();

            connector = connector.replace("LeftParenthesis", "(").replace("RightParenthesis", ")");
            List<MetadataFormItemDefinition> itemDefinitions = collectItemDefinitions_for_valueType_2_or_3(application, connector, valueType);

            addToValuesMultipleItemDefinitions(application, valuesMap, itemDefinitions, valueType);
            valuesMap = valuesMap.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByKey())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
            JSONArray valuesArray = new JSONArray();
            for (String value : valuesMap.keySet()) {
                valuesArray.put(value);
            }

            JSONObject obj = new JSONObject(valuesMap);
            returnObj.put("valuesMap", obj);
            returnObj.put("input_type", inputType);

            returnObj.put("values", valuesArray);
            return returnObj.toString();
        }

        public List<MetadataFormItemDefinition> collectItemDefinitions_for_valueType_2_or_3(RationalizerApplication application, String connector, long valueType) {
            List<MetadataFormItemDefinition> itemDefinitions = new LinkedList<>();
            if (valueType == ITEM_DEF_PRESENT_IN_DOCUMENT_METADATA) {
                if (application.getParsedDocumentFormDefinition() != null) {
                    for (MetadataFormItemDefinition itemDefinition : application.getParsedDocumentFormDefinition().getFormItemDefinitions()) {
                        if (itemDefinition.getPrimaryConnector().trim().equals(connector)) {
                            itemDefinitions.add(itemDefinition);
                        }
                    }
                }
            } else if (valueType == ITEM_DEF_PRESENT_IN_CONTENT_METADATA) {
                if (application.getParsedContentFormDefinition() != null) {
                    for (MetadataFormItemDefinition itemDefinition : application.getParsedContentFormDefinition().getFormItemDefinitions()) {
                        if (itemDefinition.getPrimaryConnector().trim().equals(connector)) {
                            itemDefinitions.add(itemDefinition);
                        }
                    }
                }
            }

            return itemDefinitions;
        }

        private String handleNullConnector(JSONObject returnObj, RationalizerApplication application, int inputType) {
            Map<String, Integer> valuesMap = new HashMap<>();

            MetadataFormItemDefinition itemDefinition = MetadataFormItemDefinition.findById(getObjectIdAsLong(request));
            long valueType = application.getParsedDocumentFormDefinition().getFormItemDefinitions().contains(itemDefinition) ? ITEM_DEF_PRESENT_IN_DOCUMENT_METADATA : ITEM_DEF_PRESENT_IN_CONTENT_METADATA;

            addToValuesMultipleItemDefinitions(
                    application,
                    valuesMap,
                    Collections.singletonList(itemDefinition),
                    valueType
            );
            valuesMap = valuesMap.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByKey())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
            JSONArray valuesArray = new JSONArray();
            for (String value : valuesMap.keySet()) {
                valuesArray.put(value);
            }

            JSONObject obj = new JSONObject(valuesMap);
            returnObj.put("valuesMap", obj);
            returnObj.put("input_type", inputType);

            returnObj.put("values", valuesArray);
            return returnObj.toString();
        }

        public static void addToValuesMultipleItemDefinitions(
                RationalizerApplication application,
                Map<String, Integer> valuesMap,
                Collection<MetadataFormItemDefinition> itemDefinitions,
                long valueType
        ) {

            String elasticSearchKeywordPrefix = "";
            if (valueType == ITEM_DEF_PRESENT_IN_DOCUMENT_METADATA) {
                elasticSearchKeywordPrefix = "document.metadata";
            } else if (valueType == ITEM_DEF_PRESENT_IN_CONTENT_METADATA) {
                elasticSearchKeywordPrefix = "metadata";
            }

            RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
            RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(application, true);

            for (MetadataFormItemDefinition itemDefinition : itemDefinitions) {
                addToValuesItemDefinition(valuesMap, elasticSearchKeywordPrefix, rationalizerElasticSearchHandler, itemDefinition);
            }
        }

        private static void addToValuesItemDefinition(
                Map<String, Integer> valuesMap,
                String elasticSearchKeywordPrefix,
                RationalizerElasticSearchHandler rationalizerElasticSearchHandler,
                MetadataFormItemDefinition itemDefinition
        ) {
            final String primaryConnector = itemDefinition.getPrimaryConnector();
            if (StringUtils.isEmpty(primaryConnector)) {
                return;
            }

            final String fieldId = elasticSearchKeywordPrefix + "." + primaryConnector;
            final String keywordFieldId = fieldId + ".keyword";
            final JsonObject matchAllQuery = readJsonObject("{\"match_all\": {}}");
            Map<String, Integer> fieldValueToCountMap = rationalizerElasticSearchHandler.aggregateOnField(matchAllQuery, keywordFieldId);
            if (MapUtils.isNotEmpty(fieldValueToCountMap)) {
                fieldValueToCountMap.forEach((fieldValue, count) -> {
                    if (StringUtils.isEmpty(fieldValue)) {
                        return;
                    }

                    valuesMap.put(fieldValue, count);
                });
            }

            final Set<String> collectedValues = valuesMap.keySet();

            // Process entries for which keyword field was null, because their size exceeds "ignore_above".
            final Function<JsonElement, Boolean> mappingFunction = (jsonElement) -> {
                final String fieldValue = getValueForJsonPath(jsonElement, "_source." + fieldId, "\\.", String.class);
                Integer crtCount = valuesMap.get(fieldValue);
                if (crtCount == null) {
                    crtCount = 1;
                } else {
                    crtCount += 1;
                }

                valuesMap.put(fieldValue, crtCount);

                return true;
            };

            final JsonObject queryField;
            if (CollectionUtils.isNotEmpty(collectedValues)) {
                queryField = new JsonObjectBuilder()
                        .add("bool", new JsonObjectBuilder()
                                .add("must_not", new JsonObjectBuilder()
                                        .add("terms", new JsonObjectBuilder()
                                                .add(keywordFieldId, new JsonArrayBuilder()
                                                        .addAll(collectedValues)
                                                        .build())
                                                .build())
                                        .build()
                                )
                                .build()
                        )
                        .build();
            } else {
                queryField = new JsonObjectBuilder()
                        .add("match_all", new JsonObject())
                        .build();
            }

            JsonObject query = new JsonObjectBuilder()
                    .add("_source", new JsonArrayBuilder()
                            .addAll(
                                    List.of(fieldId)
                            )
                            .build()
                    )
                    .add("query", queryField)
                    .build();

            AtomicInteger totalCountHolder = new AtomicInteger();

            rationalizerElasticSearchHandler.contentGetAllForQuery(
                    query,
                    mappingFunction,
                    jsonObject -> true,
                    totalCountHolder,
                    new LinkedHashMap<>() {{
                        put("filter_path", "" +
                                "hits.hits._source." + fieldId +
                                ",_scroll_id" +
                                ",hits.total"
                        );
                    }},
                    new LinkedHashMap<>()
            );
        }
    }
}