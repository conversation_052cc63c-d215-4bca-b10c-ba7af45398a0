package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncDeliveryEventStatusController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncDeliveryEventStatusController.class);

    public static final String PARAM_DELIVERY_EVENT_ID = "deliveryEventId";

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            out.write(getResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error while trying to get delivery event status asynchronously "+e.getMessage(),e);
        }

        return null;
    }

    private String getResponseJSON (HttpServletRequest request) {

        long deliveryEventId = ServletRequestUtils.getLongParameter(request, PARAM_DELIVERY_EVENT_ID, 0);

        JSONObject returnObj = new JSONObject();

        if (deliveryEventId != 0) {
            DeliveryEvent deliveryEvent = HibernateUtil.getManager().getObject(DeliveryEvent.class, deliveryEventId);
            if (deliveryEvent != null) {
                returnObj.put("status", deliveryEvent.getStatusDisplayString());
                returnObj.put("deliveryEventId", deliveryEventId);
                returnObj.put("completed", deliveryEvent.isCompletedStateExist());
                returnObj.put("error", deliveryEvent.isError());
            }
        }

        return returnObj.toString();
    }
}
