package com.prinova.messagepoint.controller;

import java.util.EnumSet;

public enum WebAssetContentType {
    JAVASCRIPT("js", "application/javascript"),
    STYLESHEET("css", "text/css");

    private String value;
    private String htmlContentType;

    WebAssetContentType(String value, String htmlContentType) {
        this.value = value;
        this.htmlContentType = htmlContentType;
    }

    public String getValue() {
        return value;
    }

    public String getHtmlContentType() {
        return htmlContentType;
    }

    public static WebAssetContentType resolveContentTypeByValue(String value) {
        for (WebAssetContentType contentType : EnumSet.allOf(WebAssetContentType.class)) {
            if (contentType.getValue().equals(value)) {
                return contentType;
            }
        }
        throw new UnsupportedOperationException("'" + value + "' is not supported value for Content Type. Supported values are 'js' and 'css'.");
    }
}
