package com.prinova.messagepoint.controller.simulation;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.job.JobBundleXmlParser;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.simulation.ConditionCoverageReport;
import com.prinova.messagepoint.model.simulation.MessageCoverageReport;
import com.prinova.messagepoint.model.simulation.RuleCoverageReport;
import com.prinova.messagepoint.model.simulation.TargetGroupCoverageReport;
import com.prinova.messagepoint.model.simulation.ZoneCoverageReport;
import com.prinova.messagepoint.util.HibernateUtil;

public class MessageCoverageReportViewController implements Controller {
	
	public static final String REQ_PARAM = "msgcovid";
	private static final String JOB_PARAM = "jobid";

	public static final String MESSAGE_INSTANCE_NAME = "messageInstanceName";
	public static final String ZONE_NAME = "zoneName";
	public static final String DATA_GROUP_NAME = "dataGroupName";
	public static final String DATA_GROUP_OCCURRENCES = "dataGroupOccurrences";
	public static final String MESSAGE_COVERAGE_REPORT = "messageCoverageReport";
	public static final String TARGET_GROUP_DATA_GROUPS = "targetGroupDataGroups";
	public static final String RULE_DATA_GROUPS = "ruleDataGroups";
	public static final String CONDITION_DATA_GROUPS = "conditionDataGroups";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();
		
		long id = getSimulationIdFromRequest(request);
		long jobId = getJobIdFromRequest(request);
		
		if (id != -1) {
			MessageCoverageReport messageCoverageReport = HibernateUtil.getManager().getObject(MessageCoverageReport.class, id);
			ZoneCoverageReport zcr = messageCoverageReport.getZoneCoverageReport();
			
			JobBundleXmlParser jbxp = new JobBundleXmlParser(Long.valueOf(jobId));
			jbxp.UpdateReports(zcr);
			
			dataMap.put(MESSAGE_COVERAGE_REPORT, messageCoverageReport);
			
			String messageInstanceName = messageCoverageReport.getName();
			
			dataMap.put(MESSAGE_INSTANCE_NAME, messageInstanceName);
			
			DataGroup zoneDataGroup = zcr.getDataGroup();

			dataMap.put(DATA_GROUP_NAME, zoneDataGroup.getName());
			dataMap.put(DATA_GROUP_OCCURRENCES, zcr.getDataGroupCount());
			dataMap.put(ZONE_NAME, zcr.getName());
			dataMap.put(CONDITION_DATA_GROUPS, new HashMap<Long, String>());
			dataMap.put(RULE_DATA_GROUPS, new HashMap<Long, String>());
			dataMap.put(TARGET_GROUP_DATA_GROUPS, new HashMap<Long, String>());
			
			Document simulationDocument = messageCoverageReport.getZoneCoverageReport().getSimulationCoverageReport().getSimulation().getDocument();
			
			Set<TargetGroupCoverageReport> targetGroupCoverageReports = messageCoverageReport.getTargetGroupCoverageReports();
			addDataGroupsToMap(dataMap, targetGroupCoverageReports, simulationDocument);
			
		}

		return new ModelAndView(getFormView(), dataMap);
	}
	
	@SuppressWarnings("unchecked")
	private void addDataGroupsToMap(Map<String, Object> dataMap, Set<TargetGroupCoverageReport> targetGroupCoverageReports, Document simulationDocument) {

		Map<Long, String> conditionDataGroups = (Map<Long, String>)dataMap.get(CONDITION_DATA_GROUPS);
		Map<Long, String> ruleDataGroups = (Map<Long, String>)dataMap.get(RULE_DATA_GROUPS);
		Map<Long, String> targetGroupDataGroups = (Map<Long, String>)dataMap.get(TARGET_GROUP_DATA_GROUPS);

		DataGroup targetGroupDataGroup = null;
		for (TargetGroupCoverageReport targetGroupCoverageReport : targetGroupCoverageReports) {
			Set<RuleCoverageReport> ruleCoverageReports = targetGroupCoverageReport.getRuleCoverageReports();

			for (RuleCoverageReport ruleCoverageReport : ruleCoverageReports) {
				Set<ConditionCoverageReport> conditionCoverageReports = ruleCoverageReport.getConditionCoverageReports();
				DataGroup ruleDataGroup = null;
				
				for (ConditionCoverageReport conditionCoverageReport : conditionCoverageReports) 
				{
					DataGroup conditionDataGroup = conditionCoverageReport.getDataGroup();

					if (conditionDataGroup != null) {
						conditionDataGroups.put(conditionCoverageReport.getId(), conditionDataGroup.getName());
					} else {
						conditionDataGroups.put(conditionCoverageReport.getId(), "");
					}

					if (ruleDataGroup == null) {
						ruleDataGroup = conditionDataGroup;
					} else if (conditionDataGroup != null && ruleDataGroup != null ) 
					{
						if (ruleDataGroup.getLevel() < conditionDataGroup.getLevel()) {
							ruleDataGroup = conditionDataGroup;
						}
					}
				}

				if (ruleDataGroup != null) {
					ruleDataGroups.put(ruleCoverageReport.getId(), ruleDataGroup.getName());
				} else {
					ruleDataGroups.put(ruleCoverageReport.getId(), "");
				}
				
				if (targetGroupDataGroup == null) {
					targetGroupDataGroup = ruleDataGroup;
				} else if (ruleDataGroup != null && targetGroupDataGroup != null ) 
				{
					if (targetGroupDataGroup.getLevel() < ruleDataGroup.getLevel()) {
						targetGroupDataGroup = ruleDataGroup;
					}
				}
				
			}
			
			if (targetGroupDataGroup != null) {
				targetGroupDataGroups.put(targetGroupCoverageReport.getId(), targetGroupDataGroup.getName());
			} else {
				targetGroupDataGroups.put(targetGroupCoverageReport.getId(), "");
			}
			
		}
		
		dataMap.put(CONDITION_DATA_GROUPS, conditionDataGroups);
		dataMap.put(RULE_DATA_GROUPS, ruleDataGroups);
		dataMap.put(TARGET_GROUP_DATA_GROUPS, targetGroupDataGroups);
		
	}
	
	private long getSimulationIdFromRequest(HttpServletRequest request){
		if(request.getParameterMap().containsKey(REQ_PARAM)){
			return ServletRequestUtils.getLongParameter(request, REQ_PARAM, -1);
		}
		return -1L;
	}
	private long getJobIdFromRequest(HttpServletRequest request)
	{
		if(request.getParameterMap().containsKey(JOB_PARAM))
		{
			return ServletRequestUtils.getLongParameter(request, JOB_PARAM, -1);
		}
		return -1L;
	}
}
