package com.prinova.messagepoint.controller.simulation;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.simulation.MessageSimulationWrapper;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;

public class MessageSimulationController extends MessagepointController {
	public MessageSimulationController() {
		// TODO Auto-generated constructor stub
	}
	
    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
    	binder.registerCustomEditor(String.class, new StringXSSEditor());
    }	
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new MessageSimulationWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		MessageSimulationWrapper messageSimulationWrapper = (MessageSimulationWrapper)command;
		messageSimulationWrapper.save();
		return new ModelAndView( new RedirectView( getSuccessView() ) );
	}
}
