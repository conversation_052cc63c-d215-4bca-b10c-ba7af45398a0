package com.prinova.messagepoint.controller.simulation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.job.JobBundleXmlParser;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.model.simulation.SimulationCoverageReport;
import com.prinova.messagepoint.model.simulation.ZoneCoverageReport;
import com.prinova.messagepoint.util.HibernateUtil;

public class CoverageReportViewController implements Controller {

	public static final String SIMULATION_NAME = "simulationName";
	public static final String SIMULATION_ID = "simulationId";
	public static final String SIMULATION_DOCUMENT = "simulationDocument";
	public static final String SIMULATION_RUN_DATE = "simulationRunDate";
	public static final String DATA_GROUPS_W_RESULTS = "dataGroups";
	public static final String DATA_GROUP_OCCURRENCES = "dataGroupOccurrences";
	public static final String DATA_GROUP_ZONE_COVERAGES = "dataGroupZoneCoverages";
	public static final String SIMULATION_USE_ALL_INPROCESS = "simulationUseAllInProcess";
	public static final String SIMULATION_JOB_ID = "simulationJobId";
	public static final String SIMULATION_CUSTOMER_REPORTING = "simulationCoverageReporting";
	public static final String SIMULATION_DOC_NAME = "simulationDocName";
	public static final String SIMULATION_INCLUDED_MESSAGE_IDS = "simulationIncludedMessageIds";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {

		Map<String, Object> dataMap = new HashMap<>();

		long id = getSimulationIdFromRequest(request);

		if (id != -1) {
			Simulation simulation = HibernateUtil.getManager().getObject(Simulation.class, id);
			dataMap.put(SIMULATION_NAME, simulation.getName());
			dataMap.put(SIMULATION_DOCUMENT, simulation.getDocument());
			dataMap.put(SIMULATION_RUN_DATE, simulation.getRunDate());
			dataMap.put(SIMULATION_ID, simulation.getId());
			dataMap.put(SIMULATION_USE_ALL_INPROCESS, simulation.isUseAllInProcess());
			dataMap.put(SIMULATION_JOB_ID, simulation.getDeliveryEvent().getJob().getId());
			dataMap.put(SIMULATION_CUSTOMER_REPORTING, simulation.isCustomerLevelFlag());
			dataMap.put(SIMULATION_INCLUDED_MESSAGE_IDS, simulation.getSelectedMessageIds());
			
			SimulationCoverageReport simulationCoverageReport = simulation.getSimulationCoverageReport();

			Map<DataGroup, Long> dataGroupOccurrencesMap = new HashMap<>();
			Map<DataGroup, List<ZoneCoverageReport>> dataGroupZonesMap = new HashMap<>();

			JobBundleXmlParser jbxp = new JobBundleXmlParser(simulation.getDeliveryEvent().getJob().getId());
			List<DataGroup> dataGroups = new ArrayList<>();
			jbxp.UpdateReports(simulationCoverageReport, dataGroups);

			List<ZoneCoverageReport> zoneCoverageReports = simulationCoverageReport.getZoneCoverageReportsSorted();

			dataMap.put(SIMULATION_DOC_NAME, simulationCoverageReport.getDocName());

//			if ( dataGroups.isEmpty() )
//				dataGroups = HibernateUtil.getManager().getObjects(DataGroup.class, MessagepointOrder.asc("name"));

			for (DataGroup dataGroup : dataGroups) 
			{
				dataGroupOccurrencesMap.put(dataGroup, Long.valueOf(0));
				dataGroupZonesMap.put(dataGroup, new ArrayList<>());
			}

			for (ZoneCoverageReport zoneCoverageReport : zoneCoverageReports) 
			{
				DataGroup zoneDataGroup = zoneCoverageReport.getDataGroup();
				dataGroupOccurrencesMap.put(zoneDataGroup, zoneCoverageReport.getDataGroupCount());

				List<ZoneCoverageReport> DGZoneCoverageReports = dataGroupZonesMap.get(zoneDataGroup);
				DGZoneCoverageReports.add(zoneCoverageReport);
				dataGroupZonesMap.put(zoneDataGroup, DGZoneCoverageReports);
			}

			List<DataGroup> dataGroupsWithResults = new ArrayList<>();
			for (DataGroup dataGroup : dataGroups) {
				Long dataGroupCount = dataGroupOccurrencesMap.get(dataGroup);
				if (dataGroupCount != null && dataGroupCount > 0)
					dataGroupsWithResults.add(dataGroup);
			}

			dataMap.put(DATA_GROUPS_W_RESULTS, dataGroupsWithResults);
			dataMap.put(DATA_GROUP_OCCURRENCES, dataGroupOccurrencesMap);
			dataMap.put(DATA_GROUP_ZONE_COVERAGES, dataGroupZonesMap);		
		}

		return new ModelAndView(getFormView(), dataMap);
	}

	private long getSimulationIdFromRequest(HttpServletRequest request){
		if(request.getParameterMap().containsKey(SimulationEditController.REQ_PARAM)){
			return ServletRequestUtils.getLongParameter(request, SimulationEditController.REQ_PARAM, -1);
		}
		return -1L;
	}
}
