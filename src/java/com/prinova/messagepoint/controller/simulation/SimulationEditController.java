package com.prinova.messagepoint.controller.simulation;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.model.wrapper.AsyncSimulationListVO;
import com.prinova.messagepoint.model.wrapper.AsyncSimulationListWrapper;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.simulation.CreateSimulationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class SimulationEditController extends MessagepointController {
	
	public static final String REQ_PARAM = "simid";
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "process";
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(ContentObject.class, new IdCustomEditor<>(ContentObject.class));
		binder.registerCustomEditor(DataResource.class, new IdCustomEditor<>(DataResource.class));
		binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true) );
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object commandObj, Errors errors) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();

		Document document = UserUtil.getCurrentTouchpointContext();
		referenceData.put("document", document);

        List<DataResource> dataResourceList = new ArrayList<>(HibernateUtil.getManager().getObjectsAdvanced(DataResource.class, MessagepointRestrictions.eq("document", document), MessagepointOrder.asc("name")));
		referenceData.put("dataResourceList", dataResourceList);	
		
		referenceData.put("nodeGUID", Node.getCurrentNode().getGuid());
		referenceData.put("webDAVToken", UserUtil.getPrincipalUser().getWebDAVToken());
		referenceData.put("canEditDataFiles", UserUtil.isPermissionGranted(Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT));

		long simId = ServletRequestUtils.getLongParameter(request, REQ_PARAM, -1);
		Simulation simulation = Simulation.findById(simId);
		boolean canUpdate = (simulation == null);
		if(simulation != null){
			AsyncSimulationListVO vo = new AsyncSimulationListVO();
			vo.setSimulation(simulation);
			AsyncSimulationListVO.SimulationListVOFlags flags = new AsyncSimulationListVO.SimulationListVOFlags();
			AsyncSimulationListWrapper.setActionFlags(simulation, flags);
			canUpdate = flags.isCanUpdate();
		}
		referenceData.put("canUpdate", canUpdate);
		return referenceData;
	}

    protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return getCommandObject(request);
    }
    
    private Command getCommandObject(HttpServletRequest request) {
    	Command command = new Command(); 
    	command = bind(request);
    	Document document = UserUtil.getCurrentTouchpointContext();
		TouchpointCollection tpCollection = UserUtil.getCurrentCollectionContext();
		if(tpCollection != null){
			command.setTouchpointCollection(tpCollection);
		}else if(document != null){
			command.setDocument(document);
		}    	
		return command;
    }
    
	private Command bind( HttpServletRequest request) {
		Command command = new Command();
		try {
			ServletRequestDataBinder binder = createBinder(request, command );
			binder.bind( request );
		} catch ( Exception e ) {
			throw new RuntimeException("Could not bind the Command", e);
		}
		return command;
	}

	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object commandObj,
            BindException errors) throws Exception {

		Command command = (Command)commandObj;

		//execute the service
		ServiceExecutionContext ctx = CreateSimulationService.createContext(command.getName(),
																			command.getDocument(),
																			command.getTouchpointCollection(),
																			command.getDataResource(),
																			command.getRunDate(),
																			command.isUseAllInProcess(),
																			command.isCustomerLevelFlag(),
																			command.getMessages(),
																			command.getDEServerGuid(),
																			command.getBundleNameOverride());
		
		Service createSimulationService = MessagepointServiceFactory.getInstance().lookupService(CreateSimulationService.SERVICE_NAME, CreateSimulationService.class);
		createSimulationService.execute(ctx);
		
		SimpleServiceResponse serviceResponse = (SimpleServiceResponse)ctx.getResponse();
		if(!serviceResponse.isSuccessful()){
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
			return showForm(request, response, errors);
 		} else {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true); 			
			return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
 		}
	}
	
	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if (FORM_SUBMIT_TYPE_SUBMIT.equals(submitType)) {
			return true;
		}
		return false;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}
	
	public static class Command {
		private String name;
		private Document document;
		private DataResource dataResource;
		private Date runDate;
		private boolean useAllInProcess=true;
		private Set<Integer> messages;
		private TouchpointCollection touchpointCollection;
		private boolean customerLevelFlag=false;

		private String DEServerGuid;
		private String bundleNameOverride;
		
		public Command() {
			
		}
		
		public boolean isCustomerLevelFlag() {
			return customerLevelFlag;
		}

		public void setCustomerLevelFlag(boolean customerLevelFlag) {
			this.customerLevelFlag = customerLevelFlag;
		}

		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public Document getDocument() {
			return document;
		}
		public void setDocument(Document document) {
			this.document = document;
		}
		public Date getRunDate() {
			return runDate;
		}
		public void setRunDate(Date runDate) {
			this.runDate = runDate;
		}
	
		public boolean isUseAllInProcess() {
			return useAllInProcess;
		}
		public void setUseAllInProcess(boolean useAllInProcess) {
			this.useAllInProcess = useAllInProcess;
		}
		public Set<Integer> getMessages() {
			return messages;
		}
		public void setMessages(Set<Integer> messages) {
			this.messages = messages;
		}

		public TouchpointCollection getTouchpointCollection() {
			return touchpointCollection;
		}

		public void setTouchpointCollection(TouchpointCollection touchpointCollection) {
			this.touchpointCollection = touchpointCollection;
		}

		public DataResource getDataResource() {
			return dataResource;
		}

		public void setDataResource(DataResource dataResource) {
			this.dataResource = dataResource;
		}

		public String getDEServerGuid() {
			return !StringUtils.isEmpty(DEServerGuid) ? DEServerGuid : null;
		}

		public void setDEServerGuid(String DEServerGuid) {
			this.DEServerGuid = DEServerGuid;
		}

		public String getBundleNameOverride() {
			return !StringUtils.isEmpty(bundleNameOverride) ? bundleNameOverride : null;
		}

		public void setBundleNameOverride(String bundleNameOverride) {
			this.bundleNameOverride = bundleNameOverride;
		}
	}

}