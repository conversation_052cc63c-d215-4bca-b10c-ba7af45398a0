package com.prinova.messagepoint.controller.simulation;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.util.HibernateUtil;

public class SimulationsListWrapper implements Serializable {

	private static final long serialVersionUID = 5943379396918546320L;
	
	private List<Long>				selectedIds;
	private String 					actionValue;

	public SimulationsListWrapper(){
		super();
		this.selectedIds 	= new ArrayList<>();
	}	
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public List<Simulation> getSelectedList(){
		List<Simulation> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(Simulation.class, selectedId));
		}
		return selectedList;
	}
	
	public String getActionValue() {
		return actionValue;
	}
	
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public static class SimulationListVO implements Serializable{

		private static final long serialVersionUID = -4872225916638600159L;
		private boolean 		selectedForAction;		
		private Simulation		simulation;
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}
		
		public Simulation getSimulation(){
			return this.simulation;
		}
		
		public void setSimulation(Simulation simulation){
			this.simulation = simulation;
		}			
	}
}

