package com.prinova.messagepoint.controller.simulation;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.deliveryevent.Job;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.reports.model.report.JobStatisticsProvider;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;


public class SimScenarioReportMessageController implements Controller{

	private String formView;
	public static  String PARAM_JOB_ID="jobid";
	public static 	String 	PARAM_SCENARIO_ID="scenarioid";
	private static final Log log = LogUtil.getLog(SimScenarioReportMessageController.class);
	
	public String getFormView() 
	{
		return formView;
	}
	public void setFormView(String formView) 
	{
		this.formView = formView;
	}


	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception 
	{
		Map<String, Object> dataMap = new HashMap<>();
		
		long jobId = ServletRequestUtils.getLongParameter(request, PARAM_JOB_ID, -1);
		Job job = null;

		if( jobId > 0 )
			job = HibernateUtil.getManager().getObject(Job.class, jobId);

		log.info("job id is "+ jobId);
		Simulation scenario = null;
		
		long scenarioId =  ServletRequestUtils.getLongParameter(request, PARAM_SCENARIO_ID, -1);
		log.info("scenarioId is "+ scenarioId);

		if( scenarioId > 0 )
		{
			scenario = HibernateUtil.getManager().getObject(Simulation.class, scenarioId);
		}
		
		if(job != null && scenario != null)
		{
			JobStatisticsProvider jobStatsProvider = new JobStatisticsProvider();
			log.info("before invoke initJobStatistics");
			jobStatsProvider.initJobStatistics( job,scenario );
			dataMap.put("jobStatsProvider", jobStatsProvider);
		}

		return new ModelAndView(getFormView(), dataMap);
	}
}
