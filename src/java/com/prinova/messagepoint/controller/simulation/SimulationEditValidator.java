package com.prinova.messagepoint.controller.simulation;

import java.util.List;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.simulation.SimulationEditController.Command;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class SimulationEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		Command command = (Command)commandObj;
		if (command.getDocument() == null && command.getTouchpointCollection() == null) {
			errors.rejectValue("document", "error.simulation.selectdocument");
		}
		if (command.getDataResource() == null) {
			errors.rejectValue("dataResource", "error.simulation.selectdataResource");
		}
		if (command.getRunDate() == null) {
			errors.rejectValue("runDate", "error.simulation.enterrundate");
		}
		if (!command.isUseAllInProcess()) {
			if ((command.getMessages() == null || command.getMessages().isEmpty()) && noProductionMessage(command.getDocument()) ) {
				List<ContentObject> inProductionMessages = ContentObjectUtil.getInProductionMessages();
				if (inProductionMessages.size() <= 0) {
					errors.reject("error.simulation.selectmessage");
				}
			}
		}
	}
	private boolean noProductionMessage(Document tp) {
		return !tp.hasActiveMessages();
	}
}
