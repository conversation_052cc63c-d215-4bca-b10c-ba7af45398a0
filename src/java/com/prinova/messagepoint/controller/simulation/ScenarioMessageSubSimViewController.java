package com.prinova.messagepoint.controller.simulation;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.scenario.AbstractScenario;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.reports.ScenarioCustomerViewCommand;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.GetCustomerDeliveries;
import com.prinova.messagepoint.util.HibernateUtil;

public class ScenarioMessageSubSimViewController extends MessagepointController implements Serializable
{
	private static final long serialVersionUID = 2099271955839962381L;

	private static final String SCENARIO_ID = "scenarioid";
	private static final String CUSTOMER_ID = "customerId";
	private static final String FIELD1 = "field1";
	private static final String FIELD2 = "field2";
	private static final String JOB_ID = "jobid";
	private static final String QUALIFIED = "qualified";
	private static final String DELIVERED = "delivered";

	private String completeReportsQuery = "from JobStatistics j where j.jobId = {0} AND j.messageId = {1,number,#}";
	private long messageId;
	private long scenarioId;
	private String jobId;
	private boolean qualified;
	private boolean delivered;

	private List<Long> jobIds;

	public void setJobIds(String jobIds) {
		this.jobIds = new ArrayList<>();

		String[] ids = jobIds.split(",");
		for (String jobId : ids) {
			this.jobIds.add(Long.parseLong(jobId));
		}
	}

	protected Map<String, Object> referenceData(HttpServletRequest request, Object target, Errors errors) throws Exception 
	{
		Map<String, Object> dataMap = new HashMap<>();
		
		dataMap.put("message", ContentObject.findById(messageId));

		String formattedCompleteReportsQuery = MessageFormat.format(completeReportsQuery, new Object[] { jobId, messageId});
		List<?> completeReportsList = HibernateUtil.getManager().getObjectsAdvanced(formattedCompleteReportsQuery);
		dataMap.put("completeReports", completeReportsList);

		if(request.getParameterMap().containsKey(SCENARIO_ID)){
			scenarioId = ServletRequestUtils.getLongParameter(request, SCENARIO_ID, -1);
		}

		AbstractScenario scenario = HibernateUtil.getManager().getObject(Simulation.class, scenarioId);
		ScenarioCustomerViewCommand command = (ScenarioCustomerViewCommand)target;
		Document doc = null;
		
		if (scenario != null) {
			doc = HibernateUtil.getManager().getObject(Document.class, scenario.getDocumentId());
			dataMap.put("scenario", scenario);
			String reportsQuery = generateQuery(command);
			List<?> reportsList = HibernateUtil.getManager().getObjectsAdvanced(reportsQuery);
			dataMap.put("reports", reportsList);
		}

		if (doc != null){
			dataMap.put("customerDisplayNameOfReportingVariableA", doc.getDisplayNameOfCustomerReportingVariableA());
			dataMap.put("customerDisplayNameOfReportingVariableB", doc.getDisplayNameOfCustomerReportingVariableB());
		}
		else
		{
			dataMap.put("customerDisplayNameOfReportingVariableA", "NONE");
			dataMap.put("customerDisplayNameOfReportingVariableB", "NONE");			
		}

		return dataMap;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected Object formBackingObject(HttpServletRequest request) {
		jobIds = null;
		loadParamsFromRequest(request);

		ScenarioCustomerViewCommand command = new ScenarioCustomerViewCommand();

		if (request.getSession().getAttribute(CUSTOMER_ID) != null) {
			command.setCustomerId(request.getSession().getAttribute(CUSTOMER_ID).toString());
			request.getSession().removeAttribute(CUSTOMER_ID);
		}
		if (request.getSession().getAttribute(FIELD1) != null) {
			command.setField1(request.getSession().getAttribute(FIELD1).toString());
			request.getSession().removeAttribute(FIELD1);
		}
		if (request.getSession().getAttribute(FIELD2) != null){
			command.setField2(request.getSession().getAttribute(FIELD2).toString());
			request.getSession().removeAttribute(FIELD2);
		}

		return command;
	}

	private void loadParamsFromRequest(HttpServletRequest request) {
		if (request.getParameterMap().containsKey(ContentObject.REQ_PARM_CONTENT_OBJECT_ID)) {
			messageId = ServletRequestUtils.getLongParameter(request,
					ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
		}
		if (request.getParameterMap().containsKey(SCENARIO_ID)) {
			scenarioId = ServletRequestUtils.getLongParameter(request,
					SCENARIO_ID, -1);
		}
		if (request.getParameterMap().containsKey(JOB_ID)) {
			jobId = ServletRequestUtils
					.getStringParameter(request, JOB_ID, "");
		}
		if (request.getParameterMap().containsKey(QUALIFIED)) {
			qualified = ServletRequestUtils.getBooleanParameter(request,
					QUALIFIED, false);
		}
		if (request.getParameterMap().containsKey(DELIVERED)) {
			delivered = ServletRequestUtils.getBooleanParameter(request,
					DELIVERED, false);
		}
	}

	private String generateQuery(ScenarioCustomerViewCommand command) {
		String query = "FROM Report WHERE messageId = " + messageId;

		// attempt to build a single query string that represents what is
		// needed.
		List<MessagepointCriterion> critList = new ArrayList<>();
		if (jobIds != null && !jobIds.isEmpty())
			critList.add(MessagepointRestrictions.in("jobId", jobIds));
		else if (scenarioId != 0) {
			// load the report scenario and get the job ids from it
			// add those job id's to the critList.

			List<MessagepointCriterion> scenCritList = new ArrayList<>();
			scenCritList.add(MessagepointRestrictions.eq("id", scenarioId));

			Simulation scen = HibernateUtil.getManager().getObjectUnique(Simulation.class, scenCritList);
			setJobIds(scen.getJobIds());
			critList.add(MessagepointRestrictions.in("jobId", jobIds));
		}

		if (command.getCustomerId() != null && !command.getCustomerId().isEmpty()) {
			query += " AND customerId like '" + command.getCustomerId() + "%'";
		}

		CreateCSStringFromList<Long> longConverter = new CreateCSStringFromList<>();
		query += " AND jobId in (" + longConverter.convert(jobIds, true) + ")";

		List<String> custSet1 = null;
		if (command.getField1() != null && !command.getField1().isEmpty())
			custSet1 = GetCustomerDeliveries.CreateConditionFromField(command.getField1(),
					"21", jobIds, command.getCustomerId());

		List<String> custSet2 = null;
		if (command.getField2() != null && !command.getField2().isEmpty())
			custSet2 = GetCustomerDeliveries.CreateConditionFromField(command.getField2(),
					"22", jobIds, command.getCustomerId());

		List<String> totalSet = GetCustomerDeliveries.AndSets(custSet1,
				custSet2);

		if (totalSet == null) {
		} else if (totalSet.isEmpty()) {
			return query + " AND customerId in ('')";
		} else {
			CreateCSStringFromList<String> stringConverter = new CreateCSStringFromList<>();
			query += " AND customerId in ("
					+ stringConverter.convert(totalSet, false) + ")";
		}

		if (delivered) {
			query += " AND played = true";
		}

		return query;
	}

	public static class CreateCSStringFromList<T> {
		public String convert(List<T> input, boolean numeric) {
			boolean first = true;
			StringBuilder out = new StringBuilder();
			for (Object i : input) {
				if (!first)
					out.append(", ");
				if (numeric)
					out.append(i.toString());
				else {
					String v = (String) (i);
					if (v.contains("'")) {
						v.replace("'", "\\'");
					}
					out.append("\'").append(v).append("\'");
				}
				first = false;
			}
			return out.toString();
		}
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object target, BindException errors) throws Exception {
		ScenarioCustomerViewCommand command = (ScenarioCustomerViewCommand) target;
		
		request.getSession().setAttribute(CUSTOMER_ID, command.getCustomerId());
		request.getSession().setAttribute(FIELD1, command.getField1());
		request.getSession().setAttribute(FIELD2, command.getField2());

		return new ModelAndView(new RedirectView(constructSuccessURL()));		
	}
	
	private String constructSuccessURL() {
		String url = getSuccessView();
		url += "?" + SCENARIO_ID + "=" + scenarioId;
		url += "&" + ContentObject.REQ_PARM_CONTENT_OBJECT_ID + "=" + messageId;
		url += "&" + JOB_ID + "=" + jobId;
		url += "&" + QUALIFIED + "=" + qualified;
		url += "&" + DELIVERED + "=" + delivered;
		return url;
	}
}
