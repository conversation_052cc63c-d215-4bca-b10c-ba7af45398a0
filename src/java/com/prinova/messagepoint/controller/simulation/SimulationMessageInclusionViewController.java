package com.prinova.messagepoint.controller.simulation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.job.JobBundleXmlParser;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.util.HibernateUtil;

public class SimulationMessageInclusionViewController implements Controller {

	public static final String REQ_PARAM = "simid";
	private static final String JOB_PARAM = "jobid";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();
		
		long id = getSimulationIdFromRequest(request);
		
		if (id == -1) {
			return new ModelAndView(getFormView(), dataMap);
		}
		Simulation simulation = HibernateUtil.getManager().getObject(Simulation.class, id);
		dataMap.put("simulation", simulation);

		long jobId = getJobIdFromRequest(request);
		if ( jobId != -1 )
		{
			JobBundleXmlParser jbxp = new JobBundleXmlParser(Long.valueOf(jobId));
			List<ContentObject> msgList = jbxp.getContentObjects();
			dataMap.put("messages", msgList);
			dataMap.put("docname", jbxp.getDocName());
		}

		return new ModelAndView(getFormView(), dataMap);
	}
	
	private long getJobIdFromRequest(HttpServletRequest request)
	{
		if(request.getParameterMap().containsKey(JOB_PARAM)){
			return ServletRequestUtils.getLongParameter(request, JOB_PARAM, -1);
		}
		return -1L;
	}		
	private long getSimulationIdFromRequest(HttpServletRequest request){
		if(request.getParameterMap().containsKey(REQ_PARAM)){
			return ServletRequestUtils.getLongParameter(request, REQ_PARAM, -1);
		}
		return -1L;
	}

}
