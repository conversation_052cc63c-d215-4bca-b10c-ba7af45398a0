package com.prinova.messagepoint.controller.simulation;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.simulation.ExportSimulationToXMLService;
import com.prinova.messagepoint.platform.services.simulation.ExportSimulationToXMLServiceResponse;

public class SimulationExportController implements Controller {
	
	public static final String REQ_PARAM = "simid";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();
		
		long simulationId = getSimulationReportIdFromRequest(request);
			
		ServiceExecutionContext context = ExportSimulationToXMLService.createContext(simulationId);
		Service exportSimulationService = MessagepointServiceFactory.getInstance().lookupService(ExportSimulationToXMLService.SERVICE_NAME, ExportSimulationToXMLService.class);
		exportSimulationService.execute(context);
		ExportSimulationToXMLServiceResponse serviceResponse = (ExportSimulationToXMLServiceResponse)context.getResponse();
		
		String returnXML = "<?xml version='1.0'?>";
		if(!serviceResponse.isSuccessful()){
			returnXML += "<content><error>Export Failed</error></content>";
		} else {
			returnXML += "<content><file>"+serviceResponse.getFilePath()+"</file></content>";
		}
		out.write(returnXML.getBytes());
		out.flush();
		
		return null;
	}
	
	private long getSimulationReportIdFromRequest(HttpServletRequest request){
		if(request.getParameterMap().containsKey(REQ_PARAM)){
			return ServletRequestUtils.getLongParameter(request, REQ_PARAM, -1);
		}
		return -1L;
	}

}
