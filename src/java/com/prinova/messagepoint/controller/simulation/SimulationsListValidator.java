package com.prinova.messagepoint.controller.simulation;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.model.wrapper.AsyncSimulationListVO;
import com.prinova.messagepoint.model.wrapper.AsyncSimulationListWrapper;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.function.Predicate;

public class SimulationsListValidator extends MessagepointInputValidator{
	
    public void validateNotGenericInputs(Object command, Errors errors){
    	SimulationsListWrapper wrapper = (SimulationsListWrapper)command;

		int action = -1;

		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		switch (action) {
			case SimulationsListController.ACTION_UPDATE_SIM:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSimulationListVO.SimulationListVOFlags::isCanUpdate);
				break;
			case SimulationsListController.ACTION_DELETE_SIM:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSimulationListVO.SimulationListVOFlags::isCanDelete);
				break;
			case SimulationsListController.ACTION_CANCEL_SIM:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncSimulationListVO.SimulationListVOFlags::isCanCancel);
				break;
			default:
				break;
		}
    }

	private void validateActionPermission(List<Simulation> simulations, Errors errors, String errorMessage, Predicate<AsyncSimulationListVO.SimulationListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( simulations.isEmpty() )
			errors.reject(errorMessage);

		for (Simulation simulation : simulations) {
			AsyncSimulationListVO vo = new AsyncSimulationListVO();
			AsyncSimulationListVO.SimulationListVOFlags flags = new AsyncSimulationListVO.SimulationListVOFlags();
			AsyncSimulationListWrapper.setActionFlags(simulation, flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}
}
