package com.prinova.messagepoint.controller.simulation;

import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import com.prinova.messagepoint.model.simulation.MessageSimulationWrapper;

public class MessageSimulationReportValidator implements Validator
{
    public boolean supports(Class clazz)
    {
        return clazz.equals(MessageSimulationWrapper.class);
    }

    /**
     * Validates the Message command object.  Ensures that the name property has been specified.
     */
    public void validate(Object command, Errors errors)
    {
    	MessageSimulationWrapper messageSimulationWrapper = (MessageSimulationWrapper)command;
    	
    	if( ( messageSimulationWrapper.getSelectedDocumentIds() == null ) || (messageSimulationWrapper.getSelectedDocumentIds().isEmpty()) ) {
    		errors.rejectValue("selectedDocumentIds", "error.message.mustselectdocument");
    	} else if( ( messageSimulationWrapper.getSelectedMessageIds() == null ) || (messageSimulationWrapper.getSelectedMessageIds().isEmpty()) ) {
    		errors.rejectValue("selectedMessageIds", "error.message.mustselectmessage");
    	}
    }
}
