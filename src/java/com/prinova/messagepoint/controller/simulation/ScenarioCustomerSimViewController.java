package com.prinova.messagepoint.controller.simulation;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.scenario.AbstractScenario;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.reports.ScenarioCustomerViewCommand;
import com.prinova.messagepoint.reports.model.report.CustomerField;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class ScenarioCustomerSimViewController extends MessagepointController implements Serializable
{
	private static final long serialVersionUID = 1L;

	private static final String CUSTOMER_ID = "customerId";
	private static final String FIELD1 = "field1";
	private static final String FIELD2 = "field2";
	private static final String SCENARIO_ID = "scenarioid";

	private static final String listQuery = "select distinct s.customerId, jobId from Report s where s.jobId in ( {0} ) {1} order by s.customerId asc";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object target, Errors errors) throws Exception 
    {
    	Map<String, Object> referenceData = new HashMap<>();

    	long scenarioId = -1;
		if(request.getParameterMap().containsKey(SCENARIO_ID))
		{
			scenarioId = ServletRequestUtils.getLongParameter(request, SCENARIO_ID, -1);
		}

		AbstractScenario scenario = HibernateUtil.getManager().getObject(Simulation.class, scenarioId);
		ScenarioCustomerViewCommand command = (ScenarioCustomerViewCommand)target;
		Document doc = HibernateUtil.getManager().getObject( Document.class, scenario.getDocumentId());
		if (scenario != null) 
		{
			referenceData.put("scenario", scenario);
			String query = MessageFormat.format(listQuery, new Object[] { scenario.getJobIds(), generateRestrictionString(command, scenarioId) });
			List<?> list = HibernateUtil.getManager().getObjectsAdvanced(query);
			referenceData.put("reports", list);
		}
		if (doc != null){
			referenceData.put("customerDisplayNameOfReportingVariableA", doc.getDisplayNameOfCustomerReportingVariableA());
			referenceData.put("customerDisplayNameOfReportingVariableB", doc.getDisplayNameOfCustomerReportingVariableB());
		}
		else
		{
			referenceData.put("customerDisplayNameOfReportingVariableA", "NONE");
			referenceData.put("customerDisplayNameOfReportingVariableB", "NONE");			
		}

    	return referenceData;
    }
    
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected Object formBackingObject(HttpServletRequest request) 
	{
		ScenarioCustomerViewCommand command = new ScenarioCustomerViewCommand();

		if (request.getSession().getAttribute(CUSTOMER_ID) != null) 
		{
			command.setCustomerId(request.getSession().getAttribute(CUSTOMER_ID).toString());
			request.getSession().removeAttribute(CUSTOMER_ID);
		}
		if (request.getSession().getAttribute(FIELD1) != null) 
		{
			command.setField1(request.getSession().getAttribute(FIELD1).toString());
			request.getSession().removeAttribute(FIELD1);
		}
		if (request.getSession().getAttribute(FIELD2) != null)
		{
			command.setField2(request.getSession().getAttribute(FIELD2).toString());
			request.getSession().removeAttribute(FIELD2);
		}

		return command;
	}

	private String generateRestrictionString(ScenarioCustomerViewCommand command, long scenarioId) 
	{
		StringBuilder query = new StringBuilder();

		if (command.getCustomerId() != null && !command.getCustomerId().isEmpty())
		{
			query.append(" and customerId like '").append(command.getCustomerId()).append("%'");
		}

		List<Long> jobIds = new ArrayList<>();
		
		AbstractScenario scenario = HibernateUtil.getManager().getObject(Simulation.class, scenarioId);

		if (!scenario.getJobIds().isEmpty())
		{
			for (String id : scenario.getJobIds().split(",")) 
			{
				jobIds.add(Long.parseLong(id));
			}
		}
		else 
		{
			return "";
		}

		List<String> firstCustIdSet = null;
		if (command.getField1() != null && !command.getField1().isEmpty())
		{
			firstCustIdSet = CreateConditionFromField(command.getField1(), "21", jobIds,
					command.getCustomerId());
		}
		List<String> secondCustIdSet = null;
		if (command.getField2() != null && !command.getField2().isEmpty())
		{
			secondCustIdSet = CreateConditionFromField(command.getField2(), "22", jobIds,
					command.getCustomerId());
		}

		List<String> totalSet = AndSets(firstCustIdSet, secondCustIdSet);

		if (totalSet == null)
			return query.toString();

		if (totalSet.isEmpty())
			return query + "AND customerId in ('')";

		query.append(" AND customerId in (");
		boolean first = true;
		for (String cid : totalSet) 
		{
			if (!first)
				query.append(",");

			if (cid.contains("'"))
				cid.replace("'", "\\'");

			query.append("'").append(cid).append("'");
			first = false;
		}
		query.append(") ");
		return query.toString();
	}

	static public List<String> CreateConditionFromField(String likeString,
			String fieldId, List<Long> jobIds, String custIdLike) 
	{
		HibernateObjectManager hom = HibernateUtil.getManager();
		List<MessagepointCriterion> critList = new ArrayList<>();

		critList.add(MessagepointRestrictions.in("jobId", jobIds));
		critList.add(MessagepointRestrictions.eq("field", fieldId));
		critList.add(MessagepointRestrictions.like("value", likeString + "%"));
		if (!custIdLike.isEmpty())
		{
			critList.add(MessagepointRestrictions.like("customerId", custIdLike + "%"));
		}
		List<CustomerField> custFields = hom.getObjectsAdvanced(
				CustomerField.class, critList);
		ArrayList<String> custIds = new ArrayList<>();
		if (custFields.isEmpty()) 
		{
			// can't have an in without values
			custIds.add("''");
			return custIds;
		}

		for (CustomerField cf : custFields) 
		{
			custIds.add(cf.getCustomerId());
		}
		return custIds;
	}

	static public List<String> AndSets(List<String> lhs, List<String> rhs) 
	{
		List<String> toRet = new ArrayList<>();

		if (lhs == null)
			return rhs;

		if (rhs == null)
			return lhs;

		for (String s : lhs) 
		{
			if (rhs.contains(s))
				toRet.add(s);
		}

		return toRet;
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object target, BindException errors) throws Exception 
	{
		ScenarioCustomerViewCommand command = (ScenarioCustomerViewCommand) target;
		
		request.getSession().setAttribute(CUSTOMER_ID, command.getCustomerId());
		request.getSession().setAttribute(FIELD1, command.getField1());
		request.getSession().setAttribute(FIELD2, command.getField2());

		return new ModelAndView(new RedirectView(getSuccessView(), true), SCENARIO_ID, ServletRequestUtils.getLongParameter(request, SCENARIO_ID, -1));		
	}
}
