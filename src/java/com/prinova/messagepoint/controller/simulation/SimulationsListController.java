package com.prinova.messagepoint.controller.simulation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.testing.TestReportStatusFilterType;
import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.simulation.BulkCancelSimulationService;
import com.prinova.messagepoint.platform.services.simulation.BulkDeleteSimulationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;

public class SimulationsListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(SimulationsListController.class);
	
	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_DOCUMENTID 		= "documentId";
	public static final String REQ_PARM_TESTID 			= "testid";
	
	public static final int ACTION_UPDATE_SIM 			= 1;
	public static final int ACTION_DELETE_SIM 			= 2;
	public static final int ACTION_CANCEL_SIM 			= 3;	
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	public Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		// Filters
		List<AssignmentFilterType> testAssignmentFilterTypes = AssignmentFilterType.listAllStandard();
		referenceData.put("simulationAssignmentFilterTypes", testAssignmentFilterTypes);
		
		List<TestReportStatusFilterType> simStatusFilterTypes = TestReportStatusFilterType.listAll();
		referenceData.put("simulationStatusFilterTypes", simStatusFilterTypes);
		
		return referenceData;
	}
	
	public Object formBackingObject(HttpServletRequest request){
		return new SimulationsListWrapper();		
	}
	
	@Override
	public ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		SimulationsListWrapper command = (SimulationsListWrapper) commandObj;
		Map<String, Object> parms = new HashMap<>();
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
			case ACTION_DELETE_SIM:{
				// ACTION_DELETE_SIM: - delete the simulation(s)
				List<Simulation> list = command.getSelectedList(); 
				ServiceExecutionContext context = BulkDeleteSimulationService.createContext(list);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteSimulationService.SERVICE_NAME, BulkDeleteSimulationService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteSimulationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(Simulation sim: list){
						sb.append(" id ").append(sim.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}				
			}
			case ACTION_CANCEL_SIM:{
				// ACTION_CANCEL_SIM: - cancel the simulation(s)
				List<Simulation> list = command.getSelectedList();
				ServiceExecutionContext ctx = BulkCancelSimulationService.createContext(list);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkCancelSimulationService.SERVICE_NAME, BulkCancelSimulationService.class);
				service.execute(ctx);
				ServiceResponse serviceResponse = ctx.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkCancelSimulationService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(Simulation sim: list){
						sb.append(" id ").append(sim.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), parms);
				}				
			}
		}
		return null;
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> parms = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
		long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);
		if (documentId != -1L) {
			parms.put(AsyncListTableController.PARAM_DOCUMENT_ID, documentId);
		} else if (collectionId != -1L) {
			parms.put(AsyncListTableController.PARAM_COLLECTION_ID, collectionId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			parms.put(AsyncListTableController.PARAM_DOCUMENT_ID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
		}
		return parms;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);
		long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;

		} else if (collectionId != -1) { // Secondary access: Navigating post entry
			boolean contextMismatch = false;
			TouchpointCollection contextCollection = UserUtil.getCurrentCollectionContext();
			if ( contextCollection != null && contextCollection.getId() != collectionId )
				contextMismatch = true;
			
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr.put(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT, String.valueOf(collectionId));
			// Update user context properties
			UserUtil.updateUserContextAttributes(contextAttr);
			
			if (contextMismatch)
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), false);
		}
		// ******* End User context persist/recall **************

		// Audit (Page access: Simulation list)
		AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_NONE, null, null, AuditActionType.ID_SIMULATION_LIST, null);		
		return super.showForm(request, response, errors);
	}	
}
