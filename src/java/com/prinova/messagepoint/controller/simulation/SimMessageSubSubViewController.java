package com.prinova.messagepoint.controller.simulation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.reports.model.report.Report;
import com.prinova.messagepoint.util.HibernateUtil;

public class SimMessageSubSubViewController implements Controller 
{
	private static final String TOUCHPOINT_ID = "touchpointid";
	private static final String ZONE_ID = "zoneid";
	private static final String SCENARIO_ID = "scenarioid";
	private static final String JOB_ID = "jobid";
	private static final String CUSTOMER_ID = "customerid";

	private long messageId;
	private long touchpointid;
	private long zoneId;
	private long scenarioId;
	private long jobid;
	private String customerid;

	private String formView;

	public String getFormView() 
	{
		return formView;
	}

	public void setFormView(String formView) 
	{
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception 
	{
		Map<String, Object> dataMap = new HashMap<>();

		loadParamsFromRequest(request);

		dataMap.put("message", ContentObject.findById(messageId));

		Document document = HibernateUtil.getManager().getObject(Document.class, touchpointid);
		dataMap.put("document", document);

		if (document != null){
			dataMap.put("customerDisplayNameOfReportingVariableA", document.getDisplayNameOfCustomerReportingVariableA());
			dataMap.put("customerDisplayNameOfReportingVariableB", document.getDisplayNameOfCustomerReportingVariableB());
		}
		else
		{
			dataMap.put("customerDisplayNameOfReportingVariableA", "NONE");
			dataMap.put("customerDisplayNameOfReportingVariableB", "NONE");			
		}

		Zone zone = HibernateUtil.getManager().getObject(Zone.class, zoneId);
		dataMap.put("zone", zone);

		Simulation simScenario = HibernateUtil.getManager().getObject(Simulation.class, scenarioId);
		dataMap.put("command", simScenario);

		ArrayList<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq("jobId", jobid));
		critList.add(MessagepointRestrictions.eq("messageId", messageId));
		if (customerid != null) 
		{
			critList.add(MessagepointRestrictions.eq("customerId", customerid));
		}

		critList.add(MessagepointRestrictions.eq("zoneId", zoneId));
		List<Report> reports = HibernateUtil.getManager().getObjectsAdvanced(Report.class, critList);
		dataMap.put("reports", reports);

		ArrayList<MessagepointCriterion> critList2 = new ArrayList<>();
		critList2.add(MessagepointRestrictions.eq("job.id", jobid));
		List<DeliveryEvent> deliveryEvents = HibernateUtil.getManager().getObjectsAdvanced(DeliveryEvent.class,
				critList2);
		dataMap.put("deliveryEvents", deliveryEvents);

		return new ModelAndView(getFormView(), dataMap);
	}

	private void loadParamsFromRequest(HttpServletRequest request) 
	{
		if (request.getParameterMap().containsKey(ContentObject.REQ_PARM_CONTENT_OBJECT_ID))
		{
			messageId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
		}
		if (request.getParameterMap().containsKey(SCENARIO_ID)) 
		{
			scenarioId = ServletRequestUtils.getLongParameter(request, SCENARIO_ID, -1);
		}
		if (request.getParameterMap().containsKey(ZONE_ID)) 
		{
			zoneId = ServletRequestUtils.getLongParameter(request, ZONE_ID, -1);
		}
		if (request.getParameterMap().containsKey(TOUCHPOINT_ID)) 
		{
			touchpointid = ServletRequestUtils.getLongParameter(request, TOUCHPOINT_ID, -1);
		}
		if (request.getParameterMap().containsKey(TOUCHPOINT_ID)) 
		{
			touchpointid = ServletRequestUtils.getLongParameter(request, TOUCHPOINT_ID, -1);
		}
		if (request.getParameterMap().containsKey(JOB_ID)) 
		{
			jobid = ServletRequestUtils.getLongParameter(request, JOB_ID, -1);
		}
		if (request.getParameterMap().containsKey(CUSTOMER_ID)) 
		{
			customerid = ServletRequestUtils.getStringParameter(request, CUSTOMER_ID, null);
		}
	}
}
