package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import org.apache.commons.text.StringEscapeUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncContentCompareController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentCompareController.class);
	
	public static final String PARAM_INSTANCE_ID				= "instanceId";
	public static final String PARAM_MODEL_ID					= "modelId";
	public static final String PARAM_VARIANT_ID 				= "variantId";
	public static final String PARAM_ZONE_PART_ID				= "zonePartId";
	public static final String PARAM_LANG_CODE_ID				= "langCodeId";
	public static final String PARAM_LOCALE_ID					= "localeId";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_OBJECT_TYPE				= "objectType";
	public static final String PARAM_COMPARE_TO_ID				= "compareToId";
	public static final String PARAM_COMPARE_TO_NODE_ID			= "compareToNodeId";
	public static final String PARAM_COMPARE_STATE				= "compareState";
    public static final String PARAM_OBJECT_TYPE_ID             = "objectType";
    public static final String PARAM_DATA_TYPE_ID               = "dataType";
    public static final String PARAM_DIRECTION                	= "direction";
	
	public static final String TYPE_SYNC_CONTENT_COMPARE		= "syncContentCompare";
	public static final String TYPE_VARIANT_CONTENT_COMPARE		= "varContentCompare";

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
        String objectType        = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE_ID, "");
		
		if( type.equalsIgnoreCase(TYPE_SYNC_CONTENT_COMPARE)){
			try{
			    String jsonResult = "";
			    if(objectType.equalsIgnoreCase("contentObject")
                        || objectType.equalsIgnoreCase("message")
                        || objectType.equalsIgnoreCase("localSmartText")
                        || objectType.equalsIgnoreCase("localImage")
                        || objectType.equalsIgnoreCase("smartText")
                        || objectType.equalsIgnoreCase("contentLibrary")
                        || objectType.equalsIgnoreCase("image")
                ) {
			        jsonResult = getSyncContentObjectContentCompareResponseJSON(request);
			    } else if(objectType.equalsIgnoreCase("lookupTable")) {
                    jsonResult = getSyncLookupTableContentCompareResponseJSON(request);
                }
				out.write(jsonResult.getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for sync content compare data: "+e.getMessage(),e);
			}	
		}else if( type.equalsIgnoreCase(TYPE_VARIANT_CONTENT_COMPARE)){
			try{
				out.write(getVariantContentCompareResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for sync content compare data: "+e.getMessage(),e);
			}	
		}

		return null;
	}
	
	private String getSyncContentObjectContentCompareResponseJSON(HttpServletRequest request) {
		long instanceId 			= ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1);
        long modelId                = ServletRequestUtils.getLongParameter(request, PARAM_MODEL_ID, -1);
		long variantId				= ServletRequestUtils.getLongParameter(request, PARAM_VARIANT_ID, -1);
		long zonePartId				= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_PART_ID, -1);
		long localeId				= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, -1);
		long compareToId 			= ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_ID, -1);
		int compareDataType         = ServletRequestUtils.getIntParameter(request, PARAM_DATA_TYPE_ID, 0);
		long compareToNodeId 		= ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_NODE_ID, -1);
		String compareState			= ServletRequestUtils.getStringParameter(request, PARAM_COMPARE_STATE, "");
        String direction            = ServletRequestUtils.getStringParameter(request, PARAM_DIRECTION, "update");
        boolean isUpdating          = direction.equalsIgnoreCase("update");

        boolean variantIsDynamicMaster = variantId == ParameterGroupTreeNode.MASTER_VARIANCE_ID;
        boolean variantIsFromOther = variantId != ParameterGroupTreeNode.MASTER_VARIANCE_ID && variantId < 0;
        if (variantIsFromOther) {
            variantId = -variantId - ParameterGroupTreeNode.OTHER_PGTNID_OFFSET;
        }

		long variantIdFinal = variantId;

        String sourceSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        if(instanceId == -1) {
            sourceSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        }
		else {
            Node sourceNode = Node.findById(instanceId);
            if(sourceNode != null) {
                sourceSchema = sourceNode.getSchemaName();
            }
        }
        String fromSchema = sourceSchema;

		String targetSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		if(compareToNodeId == -1) {
            targetSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		}
        else {
		    Node otherInstance = Node.findById(compareToNodeId);
		    if(otherInstance != null) {
                targetSchema = otherInstance.getSchemaName();
		    } else {
		    	otherInstance = Node.findById(compareToNodeId);
		    }
		}
		String compareToSchema = targetSchema;
		
		JSONObject returnObj = new JSONObject();
		try {
            CloneHelper.execInSchema(fromSchema, ()->{
                ContentObject compareFromContentObject = ContentObject.findById(modelId);
                ContentObject compareToContentObject = CloneHelper.queryInSchema(compareToSchema, ()->ContentObject.findById(compareToId));

                Document compareFromDocument = compareFromContentObject.getDocument();
                Document compareToDocument = CloneHelper.queryInSchema(compareToSchema, ()->compareToContentObject.getDocument());

                ParameterGroupTreeNode compareFromPgtn = null, compareToPgtn = null;
                if(compareFromContentObject.isStructuredContentEnabled()){
                    TouchpointSelection ts = variantIsFromOther ? CloneHelper.queryInSchema(compareToSchema, ()->TouchpointSelection.findById(variantIdFinal)) : TouchpointSelection.findById(variantIdFinal);
                    if (ts != null)
                    {
                        ParameterGroupTreeNode pgtn = ts.getParameterGroupTreeNode();
                        if(pgtn != null) {
                            String pgtnDna = pgtn.getDna();
                            compareFromPgtn = ParameterGroupTreeNode.findByDnaAndDocument(pgtnDna, compareFromDocument);
                            compareToPgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findByDnaAndDocument(pgtnDna, compareToDocument));
                        }
                    }
                    else {
                        ParameterGroupTreeNode pgtn = variantIsFromOther ? CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findById(variantIdFinal)) : ParameterGroupTreeNode.findById(variantIdFinal);
                        if(pgtn != null) {
                            String pgtnDna = pgtn.getDna();
                            compareFromPgtn = ParameterGroupTreeNode.findByDnaAndDocument(pgtnDna, compareFromDocument);
                            compareToPgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findByDnaAndDocument(pgtnDna, compareToDocument));
                        }
                    }
                }
                else if(compareFromContentObject.isDynamicVariantEnabled()){
                    ParameterGroupTreeNode pgtn = variantIsDynamicMaster ? null : variantIsFromOther ? CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findById(variantIdFinal)) : ParameterGroupTreeNode.findById(variantIdFinal);
                    if(pgtn != null) {
                        String pgtnDna = pgtn.getDna();
                        if(variantIsFromOther) {
                            compareFromPgtn = null;
                        } else {
                            compareFromPgtn = pgtn;
                        }
                        compareToPgtn = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroupTreeNode.findAllNodesForDynamicAsset(compareToContentObject, compareDataType).stream().filter(p->p.getDna().equals(pgtnDna)).findFirst().orElse(null));
                    }
                    else {
                        compareFromPgtn = null;
                        compareToPgtn = null;
                    }
                }

                MessagepointLocale localeFrom = MessagepointLocale.findById(localeId);
                MessagepointLocale localeTo = CloneHelper.queryInSchema(compareToSchema, ()->MessagepointLocale.findById(localeId));
                ContentObjectAssociation ca1 = null;
                ContentObjectAssociation ca2 = null;

                if (compareFromContentObject.isGlobalSmartText() || compareFromContentObject.isGlobalImage())
                {
                    ca1 = getContentFromContentObjectAndAssociation(compareFromContentObject, compareDataType, null, localeFrom, compareFromPgtn);

                    ParameterGroupTreeNode compareToPgtnFinal = compareToPgtn;
                    ca2 = CloneHelper.queryInSchema(compareToSchema, ()->getContentFromContentObjectAndAssociation(compareToContentObject, compareDataType, null, localeTo, compareToPgtnFinal));
                }
                else
                {
                    // We don't know where the zone part is from, origin or child.
                    // So we always find by dna and document
                    ZonePart zonePart = (zonePartId == 0 || zonePartId == -1) ? null : ZonePart.findById(zonePartId);
                    ZonePart compareFromZonePart = zonePart == null ? null : ZonePart.findByDnaAndDocument(zonePart, compareFromDocument);
                    ZonePart compareToZonePart = zonePart == null ? null : CloneHelper.queryInSchema(compareToSchema, () -> ZonePart.findByDnaAndDocument(zonePart, compareToDocument));

                    if ((compareFromZonePart == null && compareToZonePart != null) || (compareFromPgtn == null && compareToPgtn != null))
                        ca1 = new ContentObjectAssociation();
                    else
                        ca1 = getContentFromContentObjectAndAssociation(compareFromContentObject, compareDataType, compareFromZonePart, localeFrom, compareFromPgtn);

                    ParameterGroupTreeNode compareToPgtnFinal = compareToPgtn;
                    if ((compareFromZonePart != null && compareToZonePart == null) || (compareFromPgtn != null && compareToPgtn == null))
                        ca2 = new ContentObjectAssociation();
                    else
                        ca2 = CloneHelper.queryInSchema(compareToSchema, () -> getContentFromContentObjectAndAssociation(compareToContentObject, compareDataType, compareToZonePart, localeTo, compareToPgtnFinal));
                }

                if(ca1 == null) ca1 = new ContentObjectAssociation();
                if(ca2 == null) ca2 = new ContentObjectAssociation();

                if(ca1 != null && ca2 != null){
                    ContentObjectAssociation ca2Final = ca2;
                    returnObj.put("content_suppressed_1", ca1.getTypeId() == ContentAssociationType.ID_SUPPRESSES);
                    returnObj.put("content_suppressed_2",  CloneHelper.queryInSchema(compareToSchema, ()->ca2Final.getTypeId()) == ContentAssociationType.ID_SUPPRESSES);

                    String content1 = ca1.getContent()!=null?ca1.getContent().getContent(compareFromContentObject.getDocument()):"";
                    String content2 = CloneHelper.queryInSchema(compareToSchema, ()->ca2Final.getContent()!=null?ca2Final.getContent().getContent(compareFromContentObject.getDocument()):"");

                    if(content1 == null) {
                        content1 = "";
                    }

                    if(content2 == null) {
                        content2 = "";
                    }
                    if(isUpdating) {
                        compareContent(returnObj, ca1, ca2, content1, content2, compareToId==-1);
                    }
                    else {
                        compareContent(returnObj, ca2, ca1, content2, content1, compareToId==-1);
                    }
                }
                else{
                    returnObj.put("compared_content", "");
                }
            });
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

	private void compareContent(JSONObject returnObj, ContentObjectAssociation ca1, ContentObjectAssociation ca2, String content1, String content2, boolean singleSelect){
		try {
			if(content1.isEmpty() && content2.isEmpty()) {
			    String result = getSuppressedOrEmptyOrBlankMsg(ca1.getTypeId());
			    if(!singleSelect){
			    		result += ((ca1.getTypeId() == ca2.getTypeId()) ? "" : getSuppressedOrEmptyOrBlankMsg(ca2.getTypeId()));
			    }
	            returnObj.put("compared_content", result);
			} else {
				if(singleSelect){
					returnObj.put("compared_content", content1);
				}else{
				    if(content1 != null && ! content1.isEmpty()) {
				        String content1Final = content1;
				        content1 = CloneHelper.queryInSchema(ca1.getObjectSchemaName(), ()->ContentObjectContentUtil.replaceIdWithDna(content1Final, ca1.getContent()));
				    }
				    
				    if(content2 != null && ! content2.isEmpty()) {
				        String content2Final = content2;
				        content2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ContentObjectContentUtil.replaceIdWithDna(content2Final, ca2.getContent()));
				    }
				    
					String result = StringsDiffUtils.diff(content1, content2);
					returnObj.put("compared_content", result);
				}
			}
			String imagePath1 = "", imagePath2 = "", imageName1 = "", imageName2 = "", appliedImageName1 = "", appliedImageName2 = "";

            if(ca1.getContent()!=null && ca1.getContent().getImageLocation() != null) {
				imagePath1 = ca1.getContent().getImageLocation();
				imageName1 = ca1.getContent().getImageName();
				appliedImageName1 = ca1.getContent().getAppliedImageFilename()!=null?ca1.getContent().getAppliedImageFilename(): ApplicationUtil.getMessage("page.label.none");
			}

			if(CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent() !=null && ca2.getContent().getImageLocation() != null)) {
				imagePath2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getImageLocation());				
				imageName2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getImageName());
				appliedImageName2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(), ()->ca2.getContent().getAppliedImageFilename()!=null?ca2.getContent().getAppliedImageFilename(): ApplicationUtil.getMessage("page.label.none"));
			}

            String imageAltText1 = (ca1.getContent() == null || ca1.getContent().getImageAltText() == null) ?
                    null :
                    ca1.getContent().getImageAltText().getViewContent();

            String imageAltText2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(),()->(
                    ca2.getContent() == null || ca2.getContent().getImageAltText() == null) ?
                        null :
                        ca2.getContent().getImageAltText().getViewContent()
            );

            String imageExtLink1 = (ca1.getContent() == null || ca1.getContent().getImageExtLink() == null) ?
                    null :
                    ca1.getContent().getImageExtLink().getViewContent();

            String imageExtLink2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(),()->(
                    ca2.getContent() == null || ca2.getContent().getImageExtLink() == null) ?
                        null :
                        ca2.getContent().getImageExtLink().getViewContent()
            );

            String imageExtPath1 = (ca1.getContent() == null || ca1.getContent().getImageExtPath() == null) ?
                    null :
                    ca1.getContent().getImageExtPath().getViewContent();

            String imageExtPath2 = CloneHelper.queryInSchema(ca2.getObjectSchemaName(),()->(
                    ca2.getContent() == null || ca2.getContent().getImageExtPath() == null) ?
                        null :
                        ca2.getContent().getImageExtPath().getViewContent()
            );

            returnObj.put("graphic_path_1", imagePath1.isEmpty() ? imagePath1 : HttpRequestUtil.getFileResourceToken(imagePath1));
			returnObj.put("graphic_name_1", imageName1);
			String tipText1 = "<table><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("page.label.content.file") + ": " + imageName1
											+ "</td></tr><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("client_messages.content_editor.applied_image_name") + ": " + appliedImageName1
											+ "</td></tr></table>";
			returnObj.put("tip_text_1", tipText1);
			returnObj.put("graphic_path_2", imagePath2.isEmpty() ? imagePath2 : HttpRequestUtil.getFileResourceToken(imagePath2));
			returnObj.put("graphic_name_2", imageName2);
			String tipText2 = "<table><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("page.label.content.file") + ": " + imageName2
											+ "</td></tr><tr><td style='text-align: left;'>"
											+ ApplicationUtil.getMessage("client_messages.content_editor.applied_image_name") + ": " + appliedImageName2
											+ "</td></tr></table>";
			returnObj.put("tip_text_2", tipText2);

            if (imageAltText1 == null) imageAltText1 = "";
            if (imageAltText2 == null) imageAltText2 = "";

            if (imageExtLink1 == null) imageExtLink1 = "";
            if (imageExtLink2 == null) imageExtLink2 = "";

            if(imageExtPath1 == null) imageExtPath1 = "";
            if(imageExtPath2 == null) imageExtPath2 = "";

            returnObj.put("image_alt_text", StringsDiffUtils.diff(imageAltText1, imageAltText2));
            returnObj.put("image_ext_link", StringsDiffUtils.diff(imageExtLink1, imageExtLink2));
            returnObj.put("image_ext_path", StringsDiffUtils.diff(imageExtPath1, imageExtPath2));

			if(!imagePath1.isEmpty() || !imagePath2.isEmpty()){
                returnObj.put("gd_1", Node.findBySchema(ca1.getObjectSchemaName()).getGuid());
                returnObj.put("gd_2", Node.findBySchema(ca2.getObjectSchemaName()).getGuid());
				returnObj.put("is_graphic_content", true);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
	}

    private String getSuppressedOrEmptyOrBlankMsg(int typeId) {
        String result = "";
        
        if(typeId == ContentAssociationType.ID_SUPPRESSES) {
            result = "<div class=\"contentEditor_SuppressContainer\" >" +
                                "<div class=\"contentEditor_InfoDivContainer\">" +
                                    ApplicationUtil.getMessage("client_messages.text.content_suppressed") +
                                "</div>" + 
                            "</div>";
        } else if(typeId == ContentAssociationType.ID_EMPTY || typeId == ContentAssociationType.ID_NONE) {
            result = "<div class=\"contentEditor_SuppressContainer\" >" +
                    "<div class=\"contentEditor_InfoDivContainer\">" +
                        ApplicationUtil.getMessage("client_messages.text.content_empty") +
                    "</div>" + 
                "</div>";
        }
        
        return result;
    }
    
    private String getVariantContentCompareResponseJSON(HttpServletRequest request) {
		long modelId 				= ServletRequestUtils.getLongParameter(request, PARAM_MODEL_ID, -1);
		long zonePartId				= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_PART_ID, -1);
		long localeId				= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, -1);
		long compareFromId			= ServletRequestUtils.getLongParameter(request, PARAM_VARIANT_ID, -1);
		long compareToId 			= ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_ID, -1);
		String objectType        	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE_ID, "");
        int compareDataType         = ServletRequestUtils.getIntParameter(request, PARAM_DATA_TYPE_ID, 0);

        JSONObject returnObj = new JSONObject();

        try{
			ContentObject contentObject = ContentObject.findByIdWorkingDataFocusCentric(modelId);

			// No need for if(objectType.equalsIgnoreCase("smartText")) ... if(objectType.equalsIgnoreCase("contentLibrary"))

			ParameterGroupTreeNode fromPgtn = null, toPgtn = null;

			if (contentObject.isDynamicVariantEnabled())
			{
				fromPgtn = ParameterGroupTreeNode.findById(compareFromId);
				toPgtn = ParameterGroupTreeNode.findById(compareToId);
			}
			else if (contentObject.isStructuredContentEnabled())
			{
				TouchpointSelection fromVariant = TouchpointSelection.findById(compareFromId);
				if(fromVariant != null){
					long fromPgtnId = fromVariant.getParameterGroupTreeNode().getId();
					fromPgtn = ParameterGroupTreeNode.findById(fromPgtnId);
				}
				TouchpointSelection toVariant = TouchpointSelection.findById(compareToId);
				if(toVariant != null){
					long toPgtnId = toVariant.getParameterGroupTreeNode().getId();
					toPgtn = ParameterGroupTreeNode.findById(toPgtnId);
				}
		    }
			
			String fromPgtnName = "", toPgtnName = "";
			if(fromPgtn != null){
				fromPgtnName = fromPgtn.getName();
			}else{
				if(compareFromId == ParameterGroupTreeNode.MASTER_VARIANCE_ID){
					fromPgtnName = ParameterGroupTreeNode.MASTER_VARIANCE_NAME;
				}
			}
			if(toPgtn != null){
				toPgtnName = toPgtn.getName();
			}else{
				if(compareToId == ParameterGroupTreeNode.MASTER_VARIANCE_ID){
					toPgtnName = ParameterGroupTreeNode.MASTER_VARIANCE_NAME;
				}
			}
			returnObj.put("compare_from", fromPgtnName);
			returnObj.put("compare_to", toPgtnName);
			
			MessagepointLocale locale = MessagepointLocale.findById(localeId);
			ContentObjectAssociation ca1 = null;
			ContentObjectAssociation ca2 = null;
			
			ZonePart zonePart = ZonePart.findById(zonePartId);
			
			if(fromPgtn != null || (fromPgtn == null && compareFromId == ParameterGroupTreeNode.MASTER_VARIANCE_ID)){
				ca1 = getContentFromContentObjectAndAssociation(contentObject, compareDataType, zonePart, locale, fromPgtn);
				if(ca1.getReferencingImageLibrary()!=null){
					ca1 = ContentObjectAssociation.getDefaultProductionContentAssociation(ca1.getReferencingImageLibrary(), ContentObject.DATA_TYPE_ACTIVE, locale);
				}
				while(ca1.getTypeId() == ContentAssociationType.ID_REFERENCES || ca1.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
					ca1 = ca1.getReferencedCA();
				}
			}else{
				ca1 = new ContentObjectAssociation();
			}
			
			if(toPgtn != null || (toPgtn == null && compareToId == ParameterGroupTreeNode.MASTER_VARIANCE_ID)){
				ca2 = getContentFromContentObjectAndAssociation(contentObject, compareDataType, zonePart, locale, toPgtn);
				if(ca2.getReferencingImageLibrary()!=null){
					ca2 = ContentObjectAssociation.getDefaultProductionContentAssociation(ca2.getReferencingImageLibrary(), ContentObject.DATA_TYPE_ACTIVE, locale);
				}
				while(ca2.getTypeId() == ContentAssociationType.ID_REFERENCES || ca2.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
					ca2 = ca2.getReferencedCA();
				}
			}else{
				ca2 = new ContentObjectAssociation();
			}
			
			if(ca1 != null && ca2 != null){
				returnObj.put("content_suppressed_1", ca1.getTypeId() == ContentAssociationType.ID_SUPPRESSES);
				returnObj.put("content_suppressed_2", ca2.getTypeId() == ContentAssociationType.ID_SUPPRESSES);

                String content1 = ca1.getContent()!=null?ca1.getContent().getContent():"";
				String content2 = ca2.getContent()!=null?ca2.getContent().getContent():"";
				
				if(content1 == null) {
				    content1 = "";
				}
				
				if(content2 == null) {
				    content2 = "";
				}
				
				compareContent(returnObj, ca1, ca2, content1, content2, compareToId==-1);
			}else{
				returnObj.put("compared_content", "");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

    private ContentObjectAssociation getContentFromContentObjectAndAssociation(ContentObject compareFromContentObject, int dataType, ZonePart compareFromZonePart, MessagepointLocale locale, ParameterGroupTreeNode compareFromPgtn) {
        MessagepointLocale systemDefaultLanguage = MessagepointLocale.getDefaultSystemLanguageLocale();
		ContentObjectAssociation ca = null;
        if(compareFromContentObject.isStatic()) {
            ca = ContentObjectAssociation.findByContentObjectAndParameters(compareFromContentObject, dataType, null, locale, compareFromZonePart);
        } else {
            ca = ContentObjectAssociation.findByContentObjectAndParameters(compareFromContentObject, dataType, compareFromPgtn, locale, compareFromZonePart);
            if (ca == null) {
                if(compareFromContentObject.isDynamicVariantEnabled(dataType) || compareFromContentObject.isStructuredContentEnabled()) {
                    ParameterGroupTreeNode compareFromPgtnParent = compareFromPgtn == null ? null : compareFromPgtn.getParentNode();
                    if(compareFromPgtnParent != null) {
                        ca = getContentFromContentObjectAndAssociation(compareFromContentObject, dataType, compareFromZonePart, locale, compareFromPgtnParent);
                    }
                }
            }
        }
        
        if(ca == null){
			ca = ContentObjectAssociation.findByContentObjectAndParameters(compareFromContentObject, dataType, compareFromPgtn, systemDefaultLanguage, compareFromZonePart);
        } else if(ca.getTypeId () == ContentAssociationType.ID_SAME_AS_DEFAULT) {
            ca = getContentFromContentObjectAndAssociation(compareFromContentObject, dataType, compareFromZonePart, systemDefaultLanguage, compareFromPgtn);
        } else if(ca.getTypeId () == ContentAssociationType.ID_REFERENCES) {
            if(compareFromPgtn != null) {
                ParameterGroupTreeNode referencingNode = ca.getReferencingPGTreeNode();
                ca = getContentFromContentObjectAndAssociation(compareFromContentObject, dataType, compareFromZonePart, locale, referencingNode);
            }
        }
        return ca;
    }
    
    private String getSyncLookupTableContentCompareResponseJSON(HttpServletRequest request) {
        long instanceId                     = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1);
        long variantId                      = ServletRequestUtils.getLongParameter(request, PARAM_VARIANT_ID, -1);
        long zonePartId                 = ServletRequestUtils.getLongParameter(request, PARAM_ZONE_PART_ID, -1);
        long langCodeId                 = ServletRequestUtils.getLongParameter(request, PARAM_LANG_CODE_ID, -1);
        long compareToId                = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_ID, -1);
        int compareDataType         = ServletRequestUtils.getIntParameter(request, PARAM_DATA_TYPE_ID, 0);
        long nodeId                 = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_NODE_ID, -1);
        String compareState             = ServletRequestUtils.getStringParameter(request, PARAM_COMPARE_STATE, "");
        String direction            = ServletRequestUtils.getStringParameter(request, PARAM_DIRECTION, "update");
        boolean isUpdating          = direction.equalsIgnoreCase("update");

        boolean variantIsFromOther = variantId < 0;
        if(variantIsFromOther) {
            variantId = - variantId - ParameterGroupTreeNode.OTHER_PGTNID_OFFSET;
        }
        long variantIdFinal = variantId;

        String schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        if(nodeId == -1) {
            schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
        } else {
            Node otherInstance = Node.findById(nodeId);
            if(otherInstance != null) {
                schema = otherInstance.getSchemaName();
            } else {
                otherInstance = Node.findById(nodeId);
            }
        }
        String compareToSchema = schema;

        JSONObject returnObj = new JSONObject();
        try{
            LookupTable compareFromModel = LookupTable.findById(instanceId);
            LookupTable compareToModel = CloneHelper.queryInSchema(compareToSchema, ()->LookupTable.findById(compareToId));

            LookupTableInstance compareFromInstance = compareFromModel == null ?
                    LookupTableInstance.findById(instanceId) :
                    (
                            compareDataType == ContentObject.DATA_TYPE_WORKING ?
                            compareFromModel.getWorkingCopy() :
                                    compareDataType == ContentObject.DATA_TYPE_ACTIVE ?
                                            (LookupTableInstance) compareFromModel.getActiveCopy() :
                                            null

                    );

            LookupTable compareToModelFinal = compareToModel;
            LookupTableInstance compareToInstance = CloneHelper.queryInSchema(compareToSchema, ()->
                        compareToModelFinal == null ?
                                LookupTableInstance.findById(compareToId) :
                                (
                                        compareDataType == ContentObject.DATA_TYPE_WORKING ?
                                                compareToModelFinal.getWorkingCopy() :
                                                compareDataType == ContentObject.DATA_TYPE_ACTIVE ?
                                                        (LookupTableInstance) compareToModelFinal.getActiveCopy() :
                                                        null

                                )
                    );

            compareFromModel = compareFromInstance.getModel();
            compareToModel = CloneHelper.queryInSchema(compareToSchema, ()->compareToInstance.getModel());

            DatabaseFile compareFromLookupFile = compareFromInstance.getLookupFile();
            DatabaseFile compareToLookupFile = CloneHelper.queryInSchema(compareToSchema, ()->compareToInstance.getLookupFile());

            String content1 = getLookupFileContent(compareFromInstance);
            String content2 = CloneHelper.queryInSchema(compareToSchema, ()->getLookupFileContent(compareToInstance));

            String result = isUpdating ? StringsDiffUtils.diff(content1, content2) : StringsDiffUtils.diff(content2, content1);
            returnObj.put("compared_content", result);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error: Unable to get content: " + e );
        }
        return returnObj.toString();
    }

    private String getLookupFileContent(LookupTableInstance lookupTableInstance) {
	    try {
            DatabaseFile databaseFile = lookupTableInstance.getLookupFile();
            String encoding = lookupTableInstance.getInputCharacterEncodingString();
            if (encoding == null)
                encoding = ApplicationLanguageUtils.FILE_ENCODING;

            String fileContent = new String(databaseFile.getFileContent(), encoding);
            String [] allLines = fileContent.split("\\r?\\n");
            StringBuilder sb = new StringBuilder();
            for(String line : allLines) {
                String escaped = StringEscapeUtils.escapeHtml4(line);
                sb.append("<p>");
                sb.append(escaped);
                sb.append("</p>");
            }
            return sb.toString();
        } catch(Exception ex) {
	        ex.printStackTrace();
        }
	    return "";
    }
}
