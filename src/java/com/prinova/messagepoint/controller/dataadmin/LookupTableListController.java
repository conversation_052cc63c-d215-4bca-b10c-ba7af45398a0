package com.prinova.messagepoint.controller.dataadmin;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.controller.touchpoints.VersionStatusFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dataadmin.*;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LookupTableListController extends MessagepointController {

	private static final Log log = LogUtil.getLog(LookupTableListController.class);
	
	public static final String REQ_PARM_ACTION					= "action";
	public static final String REQ_PARM_DOCUMENTID 				= "documentId";
	
	public static final int ACTION_UPDATE 						= 1;
	public static final int ACTION_REASSIGN 					= 3;
	public static final int ACTION_CREATE_WORKING_COPY 			= 4;
	public static final int ACTION_DISCARD_WORKING 				= 5;
	public static final int ACTION_ARCHIVE 						= 6;
	public static final int ACTION_DELETE_ARCHIVE 				= 7;
	public static final int ACTION_RELEASE_FOR_APPROVAL 				= 9;
	public static final int ACTION_APPROVE						= 17;
	public static final int ACTION_REJECT						= 18;	

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		
		// Filters
		List<AssignmentFilterType> primaryFilterTypes = AssignmentFilterType.listAllStandard();
		referenceData.put("primaryFilterTypes", primaryFilterTypes);
		
		List<VersionStatusFilterType> lookupTableStatusFilterTypes = VersionStatusFilterType.listAll();
		referenceData.put("lookupTableStatusFilterTypes", lookupTableStatusFilterTypes);
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception{
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(LookupTableInstance.class, new IdCustomEditor<>(LookupTableInstance.class));
		binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
		binder.registerCustomEditor(String.class, new StringXSSEditor());		
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		return new LookupTableListWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		LookupTableListWrapper c = (LookupTableListWrapper)command;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch (action){
		case (ACTION_REASSIGN): {
			/**
			 * ACTION_REASSIGN: - reassign the first selected working copy to another user
			 * 
			 */
			LookupTableInstance instance = c.getSelectedList().iterator().next();
			ServiceExecutionContext context = LookupTableUnlockModelService.createContext(instance,
					requestor.getId(),
					c.getAssignedToUser().getId(),
					c.getUserNote(),false);
			Service service = MessagepointServiceFactory.getInstance().lookupService(LookupTableUnlockModelService.SERVICE_NAME,
					LookupTableUnlockModelService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(LookupTableUnlockModelService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" id=").append(instance.getId());
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  Image Library is not reassigned. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}
		}
		case (ACTION_CREATE_WORKING_COPY): {
			/**
			 * ACTION_CREATE_WORKING_COPY: - for each of the selected instance, create working copy for each
			 * 
			 */
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = LookupTableBulkCheckoutService.createContext(list, requestor, userNote);
			Service service = MessagepointServiceFactory.getInstance().lookupService(LookupTableBulkCheckoutService.SERVICE_NAME,
					LookupTableBulkCheckoutService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(LookupTableBulkCheckoutService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  working copies not created. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}
		}
		case (ACTION_DISCARD_WORKING): {
			/**
			 * ACTION_DISCARD_WORKING: - for each of the selected instance, abort the working copy
			 * 
			 */
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = LookupTableBulkAbortWIPService.createContext(list, requestor, userNote);
			Service service = MessagepointServiceFactory.getInstance().lookupService(LookupTableBulkAbortWIPService.SERVICE_NAME,
					LookupTableBulkAbortWIPService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(LookupTableBulkAbortWIPService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  working copies not aborted. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}
		}
		case (ACTION_ARCHIVE): {
			/**
			 * ACTION_ARCHIVE: - for each of the selected instance, archive the active copy
			 * 
			 */
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = LookupTableBulkArchiveService.createContext(list, requestor, userNote);
			Service service = MessagepointServiceFactory.getInstance().lookupService(LookupTableBulkArchiveService.SERVICE_NAME,
					LookupTableBulkArchiveService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(LookupTableBulkArchiveService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  active copies are not archived. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				for(LookupTableInstance instance: c.getSelectedList()){
					for(Document doc:instance.getDocuments()){
						if(doc != null && !doc.isTpContentChanged()){
							doc.setTpContentChanged(true);
							doc.save();
						}
					}
					AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_LOOKUP_TABLE, instance.getName(), instance.getId(), AuditActionType.ID_CHANGE_ARCHIVED, null);
				}
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}
		}
		case (ACTION_DELETE_ARCHIVE): {
			/**
			 * ACTION_DELETE_ARCHIVE: - for each of the selected instance, perform delete archive action
			 * 
			 */
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = LookupTableBulkDeleteArchiveService.createContext(list, requestor, userNote);
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(LookupTableBulkDeleteArchiveService.SERVICE_NAME, LookupTableBulkDeleteArchiveService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(LookupTableBulkDeleteArchiveService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  content libraries are not removed. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				for (LookupTableInstance instance : list) {
					AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_LOOKUP_TABLE, instance.getName(), instance.getId(), AuditActionType.ID_CHANGE_DELETED, null);
				}
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}
		}
		case (ACTION_RELEASE_FOR_APPROVAL): {
			/**
			 * ACTION_RELEASE_FOR_APPROVAL: - mark Release for Approval
			 * 
			 */			
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = WorkflowReleaseForApprovalService.createContext(requestor, userNote, list.toArray(new LookupTableInstance[]{}));
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(WorkflowReleaseForApprovalService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  content libraries are not released for approval. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}			
		}
		case (ACTION_APPROVE): {			
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, userNote, false, list.toArray(new LookupTableInstance[]{}));
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(WorkflowApproveService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  content libraries are not approved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}			
		}
		
		case (ACTION_REJECT): {			
			List<LookupTableInstance> list = c.getSelectedList();
			String userNote = c.getUserNote();
			ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, c.getAssignedToUser(), userNote, list.toArray(new LookupTableInstance[]{}));
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(WorkflowRejectService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for (LookupTableInstance instance : list) {
					sb.append(" id ").append(instance.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  content libraries are not rejected. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);

			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParmsRetainSearch(request,c));
			}			
		}
		
		default:
			break;
		}
		
		return null;
	}
	
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;
			
		} else if (collectionId != -1) { // Secondary access: Navigating post entry
			boolean contextMismatch = false;
			TouchpointCollection contextCollection = UserUtil.getCurrentCollectionContext();
			if ( contextCollection != null && contextCollection.getId() != collectionId )
				contextMismatch = true;
			
			HashMap<String,String> contextAttr = new HashMap<>();
			contextAttr.put(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT, String.valueOf(collectionId));
			// Update user context properties
			UserUtil.updateUserContextAttributes(contextAttr);
			
			if (contextMismatch)
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), true);
		}
		// ******* End User context persist/recall **************
		
		return super.showForm(request, response, errors);
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> parms = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		long collectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_COLLECTION_ID, -1L);

		if (documentId != -1L) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		} else if (collectionId != -1L) {
			parms.put(AsyncListTableController.PARAM_COLLECTION_ID, collectionId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			parms.put(REQ_PARM_DOCUMENTID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
		}

		return parms;
	}
	
	private Map<String, Object> getSuccessViewParmsRetainSearch(HttpServletRequest request, LookupTableListWrapper c) {
		Map<String, Object> parms = getSuccessViewParms(request);
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		if (action != -1) {
			parms.put(REQ_PARM_ACTION, action);
		}
		return parms;
	}	
}
