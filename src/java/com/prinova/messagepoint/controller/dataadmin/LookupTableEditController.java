package com.prinova.messagepoint.controller.dataadmin;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.CreateNewLookupTableService;
import com.prinova.messagepoint.platform.services.dataadmin.CreateNewLookupTableServiceRequest;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateLookupTableService;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class LookupTableEditController extends MessagepointController {

	public static final String PARAM_LOOKUP_TABLE_ID 		= "lookupTableId";
	public static final String PARAM_DOCUMENT_ID			= "documentId";
	public static final String PARAM_COLLECTION_ID			= "collectionId";	
	public static final String PARAM_TP_CONTEXT				= "touchpointContext";	

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> map = new HashMap<>();
		boolean tpContext = ServletRequestUtils.getBooleanParameter(request, LookupTableEditController.PARAM_TP_CONTEXT, false);
		
    	if(tpContext){
    		map.put("documents", Document.findAllDocumentsAndProjectsVisible(true));
    		map.put("tpCollections", TouchpointCollection.findAllWithAllDocumentsVisible(true));
    	}else{
    		map.put("documents", Document.findAllDocumentsAndProjectsEnabled(true));
    		map.put("tpCollections", TouchpointCollection.findAll(true));
    	}		
		return map;
	}
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( Document.class, new IdCustomEditor<>(Document.class) );
		binder.registerCustomEditor( TouchpointCollection.class, new IdCustomEditor<>(TouchpointCollection.class) );
		binder.registerCustomEditor(LookupTableInstance.class, new IdCustomEditor<>(LookupTableInstance.class) );
	}
	
	@Override
    protected Object formBackingObject(HttpServletRequest request) {
		LookupTableEditWrapper wrapper;
		long lookupTableId = ServletRequestUtils.getLongParameter(request, LookupTableEditController.PARAM_LOOKUP_TABLE_ID, -1);
		if (lookupTableId == -1) {
			wrapper = new LookupTableEditWrapper();
		} else {
			LookupTableInstance lookupTableInstance = HibernateUtil.getManager().getObject(LookupTableInstance.class, lookupTableId);
			wrapper = new LookupTableEditWrapper(lookupTableInstance);
		}		
		return wrapper;
	}
	
	@Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		LookupTableEditWrapper wrapper = (LookupTableEditWrapper)command;
		LookupTableInstance lookupTableInstance = wrapper.getLookupTableInstance();
		
		if (lookupTableInstance.getId() <= 0) {
			// create new Lookup Table
			ServiceExecutionContext context = CreateNewLookupTableService.createContext(UserUtil.getPrincipalUserId());
			CreateNewLookupTableServiceRequest serviceRequest = (CreateNewLookupTableServiceRequest) context.getRequest();
			serviceRequest.setName(lookupTableInstance.getName());
			serviceRequest.setDelimiter(lookupTableInstance.getDelimiter());
			serviceRequest.setInputCharacterEncoding(wrapper.getInputCharacterEncoding());
			serviceRequest.setFullyVisible(lookupTableInstance.isFullyVisible());
			serviceRequest.setDataFileSandboxFileId(wrapper.getDataFileSandboxFileId());
			serviceRequest.setStartDate(lookupTableInstance.getStartDate());
			serviceRequest.setEndDate(lookupTableInstance.getEndDate());
			serviceRequest.setCreated(lookupTableInstance.getCreated());
			serviceRequest.setDocuments(wrapper.getDocuments());
			serviceRequest.setTpCollections(wrapper.getTpCollections());
			
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateNewLookupTableService.SERVICE_NAME, CreateNewLookupTableService.class);
			service.execute(context);

			if(context.getResponse().isSuccessful()){
				Map<String, Object> parms = new HashMap<>();
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);
			}else{
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} 
		} else {
			ServiceExecutionContext context = UpdateLookupTableService.createContext(wrapper);
			Service updateLookupTableService = MessagepointServiceFactory.getInstance().lookupService(UpdateLookupTableService.SERVICE_NAME, UpdateLookupTableService.class);
			updateLookupTableService.execute(context);
			
			if(context.getResponse().isSuccessful()){
				Map<String, Object> parms = new HashMap<>();
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_LOOKUP_TABLE, lookupTableInstance.getName(), lookupTableInstance.getId(), AuditActionType.ID_CHANGE_EDITED, null);
				return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);
			}else{
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} 			
		}		
	}
}
