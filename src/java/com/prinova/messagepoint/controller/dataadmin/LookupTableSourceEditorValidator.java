package com.prinova.messagepoint.controller.dataadmin;

import java.util.Scanner;
import java.util.regex.Pattern;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.file.SourceEditorController.SourceCommand;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class LookupTableSourceEditorValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		SourceCommand command = (SourceCommand)commandObj;
		LookupTableInstance instance = LookupTableInstance.findByLookupFileId(command.getDatabaseFileId());	

    	// Parse the lookup table file to check the structure
		String delimiter = instance.getDelimiter();
		
		Scanner scanner = new Scanner(command.getSource());
		// Read the title line
		String line = scanner.nextLine();
		String[] splTitle = line.split(Pattern.quote(delimiter), -1);
		int columnsCount = splTitle.length;
		
    	if (instance.getId() > 0)
    	{
			if(instance.getDataSource().getDataElementCount() != splTitle.length){
				scanner.close();
				errors.reject("error.lookup.table.file.structure.changed");
    			return;
			}
    	}
    	
    	int columnCount = 0;
		for (String titleName : splTitle)
		{
			columnCount++;
			if(titleName == null || titleName.trim().isEmpty()){
				scanner.close();
				errors.reject("error.lookup.table.file.structure.column", new String[]{String.valueOf(columnCount)}, null);
    			return;
			}
		}
		
		int lineCount = 1;
		while (scanner.hasNext())
		{
			lineCount++;
			line = scanner.nextLine();
			splTitle = line.split(Pattern.quote(delimiter) + "(?=([^\"]*\"[^\"]*\")*[^\"]*$)", -1);
			
			if(columnsCount != splTitle.length){
				scanner.close();
				errors.reject("error.lookup.table.file.structure.values", new String[]{String.valueOf(lineCount)}, null);
    			return;
			}
		}
		
		scanner.close();					
	}
}
