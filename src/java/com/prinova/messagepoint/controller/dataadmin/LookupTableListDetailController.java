package com.prinova.messagepoint.controller.dataadmin;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.admin.DataRecord;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.util.HibernateUtil;

public class LookupTableListDetailController implements Controller {
	private String successView;
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long lookupTableId = ServletRequestUtils.getLongParameter(request, LookupTableEditController.PARAM_LOOKUP_TABLE_ID, -1L);
		
		LookupTableInstance lookupTableInstance = HibernateUtil.getManager().getObject(LookupTableInstance.class, lookupTableId);
		params.put("lookupTableInstance", lookupTableInstance);
		DataSource ds = lookupTableInstance.getDataSource();
		if(ds != null){
			params.put("dsId", ds.getId());
			DataRecord dr = ds.getDataRecords().iterator().next();
			params.put("drId", dr.getId());
		}

		return new ModelAndView(getFormView(), params);
	}
	
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
