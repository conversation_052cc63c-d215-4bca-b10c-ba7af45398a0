package com.prinova.messagepoint.controller.dataadmin;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.file.SourceEditorController;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateLookupTableService;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;

public class LookupTableSourceEditorController extends SourceEditorController {
	public static final String PARAM_LOOKUP_TABLE_ID 		= "lookupTableId";
	
	@Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		super.onSubmit(request, response, command, errors);
		
		// Construct the LookupTableEditWrapper
		long lookupTableId = ServletRequestUtils.getLongParameter(request, LookupTableEditController.PARAM_LOOKUP_TABLE_ID, -1);
		LookupTableInstance lookupTableInstance = HibernateUtil.getManager().getObject(LookupTableInstance.class, lookupTableId);
		LookupTableEditWrapper wrapper = new LookupTableEditWrapper(lookupTableInstance);
		ServiceExecutionContext context = UpdateLookupTableService.createContext(wrapper);
		Service updateLookupTableService = MessagepointServiceFactory.getInstance().lookupService(UpdateLookupTableService.SERVICE_NAME, UpdateLookupTableService.class);
		updateLookupTableService.execute(context);
		
		if(context.getResponse().isSuccessful()){
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);	
		}else{
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}
}
