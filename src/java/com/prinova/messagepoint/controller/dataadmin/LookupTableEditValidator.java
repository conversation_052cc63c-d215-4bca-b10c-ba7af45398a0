package com.prinova.messagepoint.controller.dataadmin;

import java.io.UnsupportedEncodingException;
import java.util.Scanner;
import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Pattern;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.wrapper.AsyncLookupTableListVO;
import com.prinova.messagepoint.model.wrapper.AsyncLookupTablesListWrapper;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class LookupTableEditValidator extends MessagepointInputValidator {

	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		LookupTableEditWrapper command = (LookupTableEditWrapper)commandObj;
		LookupTableInstance instance = command.getLookupTableInstance();

		if (instance.getId() > 0) {
			validateActionPermission(instance, errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanUpdate);
		}

		if(instance.getDelimiter() == null || instance.getDelimiter().isEmpty()){
			errors.reject("error.delimiter.can.not.be.empty");
			return;
		}
    	if (command.getDataFileSandboxFileId() == null && (command.getDataFilePath() == null || command.getDataFilePath().isEmpty())) {
        	errors.reject("error.message.mustselectfile");
        	return;
    	}
    	
    	if(!instance.isFullyVisible()){
	    	if( (command.getDocuments() == null || command.getDocuments().isEmpty()) &&
	    			(command.getTpCollections() == null || command.getTpCollections().isEmpty()) ) {
	    		errors.rejectValue("documents", "error.message.mustselecttouchpointorcollection");
	    		return;
	    	}
	    	
	    	// Make sure a touchpoint is not removed if the lookup table is in used in a variable which requires its visibility
	    	if(instance.isReferenced()){
		    	Set<Document> documentsInDb = instance.getDocuments();
		    	if(documentsInDb.isEmpty()){	// Full visible before
		    		documentsInDb.addAll(Document.findAllDocumentsAndProjectsEnabled(true));
		    	}
	    		for(Document documentInDb : documentsInDb){
	    			if(documentInDb.isEnabled() && !command.getDocuments().contains(documentInDb)){
						errors.reject("error.message.lookuptable.referenced.cannot.restrict");
						return;
	    			}
	    		}
	    	}	    	
    	}

    	// Parse the lookup table file to check the structure
		try{
    		String delimiter = instance.getDelimiter();
    		// Parse the lookup table file and update the data source
			byte[] fileContent = null;
			Long sandboxFileId = command.getDataFileSandboxFileId();
			if(sandboxFileId != null){
				SandboxFile dataSb = SandboxFile.findById(sandboxFileId);
				fileContent = dataSb.getFileContent();
			}else{
				DatabaseFile dataDf = instance.getLookupFile();
				fileContent = dataDf.getFileContent();
			}
			if(fileContent != null){
				
				String fileEncoding = null;
				switch(command.getInputCharacterEncoding())
				{
					case 1: fileEncoding = "windows-1252";
							break;
							
					case 2: fileEncoding = "windows-1252";
							break;
							
					case 3: fileEncoding = "UTF-8";
							break;
				}
				
				String content;
				if (fileEncoding != null)
					content = new String(fileContent, fileEncoding);
				else
					content = new String(fileContent);
				
    			Scanner scanner = new Scanner(content);
    			// Read the title line
    			String line = scanner.nextLine();
    			String[] splTitle = line.split(Pattern.quote(delimiter), -1);
    			int columnsCount = splTitle.length;
    			
    	    	if (instance.getId() > 0)
    	    	{
	    			if(instance.getDataSource().getDataElementCount() != splTitle.length){
	    				scanner.close();
	    				errors.reject("error.lookup.table.file.structure.changed");
		    			return;
	    			}
    	    	}
    	    	
    	    	int columnCount = 0;
    			for (String titleName : splTitle)
    			{
    				columnCount++;
    				if(titleName == null || titleName.trim().isEmpty()){
	    				scanner.close();
    					errors.reject("error.lookup.table.file.structure.column", new String[]{String.valueOf(columnCount)}, null);
    	    			return;
    				}
    			}
    			
    			int lineCount = 1;
    			while (scanner.hasNext())
    			{
    				lineCount++;
    				line = scanner.nextLine();
    				splTitle = line.split(Pattern.quote(delimiter) + "(?=([^\"]*\"[^\"]*\")*[^\"]*$)", -1);
    				
	    			if(columnsCount != splTitle.length){
	    				scanner.close();
	    				errors.reject("error.lookup.table.file.structure.values", new String[]{String.valueOf(lineCount)}, null);
		    			return;
	    			}
    			}
    			
    			scanner.close();					
			}
    	}catch(UnsupportedEncodingException e){
    	}
	}

	private void validateActionPermission(LookupTableInstance lookupTableInstance, Errors errors, String errorCode, Predicate<AsyncLookupTableListVO.LookupTableListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncLookupTableListVO vo = new AsyncLookupTableListVO();
		vo.setLookupTableInstance(lookupTableInstance);

		AsyncLookupTableListVO.LookupTableListVOFlags flags = new AsyncLookupTableListVO.LookupTableListVOFlags();
		AsyncLookupTablesListWrapper.setActionFlags(lookupTableInstance, flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorCode);
		}
	}
}
