package com.prinova.messagepoint.controller.dataadmin;

import java.math.BigInteger;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocument;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dataadmin.MetatagsUpdateService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

public class MetatagsEditController extends MessagepointController {

    private static final Log log = LogUtil.getLog(MetatagsEditController.class);

    public static final int ACTION_UPDATE 				        = 1;
    public static final int ACTION_ADD_APPLICATION			    = 30;

    public static final String PARAM_RATIONALIZER_APP_ID 	    = "rationalizerApplicationId";

    public static String REQUEST_PARM_SAVE_SUCCESS		        = "saveSuccess";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();

        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APP_ID, -1L);
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        List<TagCloudType> types;
        if ( rationalizerApplication != null )
            types = TagCloudType.listAllRationalizer();
        else
            types = TagCloudType.listAllCore();
        referenceData.put("types", types);

        List<String> typeIds = new ArrayList<>();
        for ( TagCloudType currentType: types )
            typeIds.add( String.valueOf(currentType.getId()) );

        long tagCount = 0L;
        String query = "SELECT COUNT(distinct(tc.id)) FROM tag_cloud tc WHERE tc.type_id IN (" + StringUtils.join(typeIds,",")+ ") ";
        if ( rationalizerApplication != null )
            query += " AND tc.rationlizer_application_id = " + rationalizerApplicationId + " ";
        NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery(query);

        List objs = sqlQuery.list();
        for( Object obj : objs )
            tagCount = ((BigInteger)(obj)).longValue();

        referenceData.put("tagCount", tagCount);

        referenceData.put("rationalizerView", rationalizerApplication != null);

        referenceData.put("applications", RationalizerApplication.filterVisibleApplications(UserUtil.getPrincipalUser()) );
        referenceData.put("rationalizerApplicationId", rationalizerApplicationId>0);

        RationalizerApplication application = RationalizerApplication.findById(rationalizerApplicationId);
        if(application != null){
            referenceData.put("application", application);
            RationalizerApplication clonedApplication = RationalizerApplication.findUniqueByParentId(application.getId());
            RationalizerApplication parentApplication = (RationalizerApplication)application.getParentObject();
            referenceData.put("hasClonedAppContext", (clonedApplication != null || parentApplication != null));
            referenceData.put("hasLinkedTouchpoint", application.getLinkedDocument() != null);


            if(clonedApplication != null){
                referenceData.put("clonedApplication", clonedApplication);
            }
            if(parentApplication != null){
                referenceData.put("parentApplication", parentApplication);
            }

            referenceData.put("navTreeBreadCrumbTrail", MetadataFormItem.buildRationalizerNavTreeBreadCrumbTrail(rationalizerApplicationId));

            referenceData.put("shouldShowDocumentLink", RationalizerDocument.atLeastOneDocumentInApplicationHasFileAttached(rationalizerApplicationId));
        }

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        MetatagsEditWrapper wrapper = (MetatagsEditWrapper) commandObj;

        if ( wrapper.getActionValue() == ACTION_UPDATE  ) {

            ServiceExecutionContext context = MetatagsUpdateService.createContext(wrapper);

            Service service = MessagepointServiceFactory.getInstance().lookupService(MetatagsUpdateService.SERVICE_NAME, MetatagsUpdateService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            if ( !serviceResponse.isSuccessful() ) {
                ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                return super.showForm(request, response, errors);
            } else {
                Map<String, Object> params = new HashMap<>();
                params.put(REQUEST_PARM_SAVE_SUCCESS, true);

                if ( wrapper.getCurrentApplication() != null )
                    params.put(PARAM_RATIONALIZER_APP_ID, wrapper.getCurrentApplication().getId());

                return new ModelAndView(new RedirectView("metatags_edit.form"), params);
            }

        }
        else if ( wrapper.getActionValue() == ACTION_ADD_APPLICATION  ) {
            AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.TaskList);
            analyticsEvent.setAction(Actions.AddApplication);

            ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForNewApplication(
                    wrapper.getApplicationName(),
                    wrapper.getMetatags(),
                    wrapper.getDescription());

            Service createApplicationService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
            createApplicationService.execute(context);

            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                sb.append(" rationalizer application not created. ");
                log.error(sb.toString());
                ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
                return showForm(request, response, errors);
            } else {
                Map<String, Object> params = new HashMap<>();

                long newApplicationId = (Long) serviceResponse.getResultValueBean();
                params.put(PARAM_RATIONALIZER_APP_ID, newApplicationId);

                return new ModelAndView(new RedirectView(getSuccessView()), params);
            }
        }

        return new ModelAndView(new RedirectView(getFormView()));
    }

    protected MetatagsEditWrapper formBackingObject(HttpServletRequest request) throws Exception {

        Long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APP_ID, -1L);

        return new MetatagsEditWrapper(rationalizerApplicationId);
    }

}