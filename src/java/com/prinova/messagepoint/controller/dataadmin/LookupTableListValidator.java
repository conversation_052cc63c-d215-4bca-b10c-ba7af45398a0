package com.prinova.messagepoint.controller.dataadmin;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.wrapper.AsyncLookupTableListVO;
import com.prinova.messagepoint.model.wrapper.AsyncLookupTablesListWrapper;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

public class LookupTableListValidator extends MessagepointInputValidator{
		public void validateNotGenericInputs(Object commandObj, Errors errors) {
		LookupTableListWrapper wrapper = (LookupTableListWrapper)commandObj;
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.parseInt(wrapper.getActionValue());
		}

		switch (action) {
			case LookupTableListController.ACTION_UPDATE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanUpdate);
				break;
			case LookupTableListController.ACTION_REASSIGN:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanReassign);
				break;
			case LookupTableListController.ACTION_CREATE_WORKING_COPY:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanCreateWorkingCopy);
				break;
			case LookupTableListController.ACTION_DISCARD_WORKING:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanDiscardWorkingCopy);
				break;
			case LookupTableListController.ACTION_ARCHIVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanArchive);
				break;
			case LookupTableListController.ACTION_DELETE_ARCHIVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanDeleteArchive);
				break;
			case LookupTableListController.ACTION_RELEASE_FOR_APPROVAL:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanReleaseForApproval);
				break;
			case LookupTableListController.ACTION_APPROVE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanApprove);
				break;
			case LookupTableListController.ACTION_REJECT:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncLookupTableListVO.LookupTableListVOFlags::isCanReject);
				break;
			default:
				break;
		}

		if(action == LookupTableListController.ACTION_ARCHIVE){
			for(LookupTableInstance instance : wrapper.getSelectedList()){
				boolean referenced = instance.isReferenced();
				if(referenced){
					List<DataElementVariable> variableList = instance.findReferencedVariables();
					List<String> names = new ArrayList<>();
					for (DataElementVariable dev : variableList) {
						names.add(dev.getName());
					}
					errors.reject("error.lookup.table.is.being.referenced.on.archive",
							new Object[]{names.toString()}, "");
					break;
				}
			}			
		}else if(action == LookupTableListController.ACTION_DELETE_ARCHIVE){
			for(LookupTableInstance instance : wrapper.getSelectedList()){
				boolean referenced = instance.isReferenced();
				if(referenced){
					errors.reject("error.lookup.table.is.being.referenced.on.archive.delete");
					break;
				}
			}
		}		
	}

	private void validateActionPermission(List<LookupTableInstance> lookupTableInstances, Errors errors, String errorCode, Predicate<AsyncLookupTableListVO.LookupTableListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if (lookupTableInstances.isEmpty()) {
			errors.reject(errorCode);
		}

		for (LookupTableInstance lookupTableInstance : lookupTableInstances) {
			AsyncLookupTableListVO vo = new AsyncLookupTableListVO();
			vo.setLookupTableInstance(lookupTableInstance);

			AsyncLookupTableListVO.LookupTableListVOFlags flags = new AsyncLookupTableListVO.LookupTableListVOFlags();
			AsyncLookupTablesListWrapper.setActionFlags(lookupTableInstance, flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorCode);
				break;
			}
		}
	}

}
