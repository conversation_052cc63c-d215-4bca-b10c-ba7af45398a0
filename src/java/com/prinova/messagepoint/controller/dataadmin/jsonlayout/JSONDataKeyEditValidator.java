package com.prinova.messagepoint.controller.dataadmin.jsonlayout;

import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.util.DataGroupUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.stream.Collectors;

public class JSONDataKeyEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object command, Errors errors) {
		JSONDataDefinitionEditController.Command commandJson = (JSONDataDefinitionEditController.Command)command;

		long dataSourceId = commandJson.getDataSource().getId();
		DataSource lazyDataSource = HibernateUtil.getManager().getObject(DataSource.class, dataSourceId );
		String newDataGroupName = commandJson.getDataGroupName() != null ? commandJson.getDataGroupName().trim() : "";

		if (JSONDataDefinition.S_ARRAY.equals(newDataGroupName) || JSONDataDefinition.S_OBJECT.equals(newDataGroupName)) {
			errors.rejectValue("dataGroupName", "error.dataadmin.data.group.invalid.name");
		}

		if (commandJson.isStartCustomer()){
			//Check if a starts customer is already exists
			JSONDataDefinition startsCustomerJsonDataTag = JSONDataDefinition.getStartsCustomerJsonDataDefinition(lazyDataSource.getId());
			if (startsCustomerJsonDataTag != null && startsCustomerJsonDataTag.getId() != commandJson.getId()) {
				errors.reject("error.message.another.json.start.customer.in.use",
						new String[] {String.valueOf(startsCustomerJsonDataTag.getId()), lazyDataSource.getName()}, "");
			}

			if (!commandJson.isStartDataGroup()) {
				//Extra serverside check to see that starts data group is checked
				errors.rejectValue("commandJson.startCustomer", "error.message.start.customer.and.start.data.group");
			} else {
				if (newDataGroupName.isEmpty()) {
					newDataGroupName = DataGroupUtil.findNextStartCustomerDataGroupNamePlaceholder(dataSourceId);
					commandJson.setDataGroupName(newDataGroupName);
				}
			}
		}

		//DataGroup name validation check
		boolean isNewJsonDef = commandJson.getId() < 1;
		if (isNewJsonDef) {
			if (commandJson.isStartDataGroup())
				validateUniqueDataGroupName(errors, commandJson.getId(), newDataGroupName, dataSourceId);
		} else {
			JSONDataDefinition origDefinition = JSONDataDefinition.findById(commandJson.getId());
			if (origDefinition == null) {
				errors.reject("error.message.action.not.permitted");
			} else {
				DataGroup origDataGroup = origDefinition.getDataGroup();
				boolean isNewDataGroup = origDataGroup == null;
				if (isNewDataGroup) {
					if (commandJson.isStartDataGroup())
						validateUniqueDataGroupName(errors, commandJson.getId(), newDataGroupName, dataSourceId);
				} else {
					validateUniqueDataGroupName(errors, commandJson.getId(), newDataGroupName, dataSourceId);
				}
			}
		}

		if(commandJson.getId()>0) {
			JSONDataDefinition dataDefinition = HibernateUtil.getManager().getObject(JSONDataDefinition.class, commandJson.getId());
			if (dataDefinition.getDefinitionType()==JSONDefinitionType.ID_KEY && commandJson.isStartCustomer()) {
				List<JSONDataDefinition> arrayChildRepeatingList = dataDefinition.getChildList().stream().filter(d -> d.getDefinitionType()==JSONDefinitionType.ID_ARRAY && d.isRepeating()).toList();
				if(!arrayChildRepeatingList.isEmpty()){
					errors.rejectValue( "startCustomer", "error.message.jsond.cannot.start.customer.with.array.child.repeating");
				}
			}
		}


	}

	//Checking for unique data group name
	private void validateUniqueDataGroupName(Errors errors, long commandDefinitionId, String newDataGroupName, long dataSourceId) {
		if (newDataGroupName.isEmpty()) {
			errors.rejectValue("dataGroupName", "error.message.data.group.name.required");
			return;
		}

		JSONDataDefinition origJsonDataDef = JSONDataDefinition.findById(commandDefinitionId);
		//If Json is new, or DataGroup name changed.
		if(origJsonDataDef == null || origJsonDataDef.getDataGroup() == null
				|| !origJsonDataDef.getDataGroup().getName().equals(newDataGroupName)) {
			//Look for any DataGroups with the same name for given DataSource
			List<DataGroup> dataGroups = DataGroupUtil.findByNameAndDataSource(newDataGroupName, dataSourceId);
			if (dataGroups != null && !dataGroups.isEmpty()) {
				//Retrieves JsonDataKey names that are associated with the DataGroup
				String jsonNames = dataGroups.stream()
						.flatMap(dg -> dg.getJsonDataDefinitions().stream())
						.map(JSONDataDefinition::getName)
						.collect(Collectors.joining(", "));
				errors.rejectValue("dataGroupName", "error.message.data.group.name.unique", new Object[]{jsonNames}, null);
			}
		}
	}
}
