package com.prinova.messagepoint.controller.dataadmin.jsonlayout;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.model.DataDefinitionVO;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.util.DataGroupUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.UpdateJSONDataDefinitionService;
import com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.UpdateJSONDataDefinitionServiceRequest;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JSONDataDefinitionEditController extends MessagepointController implements Serializable {
	private static final long serialVersionUID = -5916373414501044559L;
	public static final String PARAMETER_JSON_DATA_DEFINITION_COMMAND = "JsonDataDefinitionCommand";
	public static final String PARM_PARENT_CHANGED = "parentChanged";
	public static final String PARM_DATA_GROUP_CHANGED = "dataGroupChanged";
	public static final String PARM_ACTION = "action";
	public static final String PARM_PARENT_ID = "parentid";
	public static final String ACTION_UPDATE_DEF = "updatejsondatadef";
	public static final String ACTION_UPDATE_KEY = "updatejsondatakey";
	public static final String ACTION_INSERT_DEF = "insertjsondatadef";
	public static final String ACTION_INSERT_KEY = "insertjsondatakey";

	public static final String FORM_SUBMIT_TYPE_SUBMIT = "process";
	private String formViewRedirect;

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(JSONDataDefinition.class, new IdCustomEditor<>(JSONDataDefinition.class));
    	// binder.registerCustomEditor(DataGroup.class, new IdCustomEditorInt<DataGroup>(DataGroup.class));
   	   	binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
   		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(JSONDefinitionType.class, new StaticTypeIdCustomEditor<>(JSONDefinitionType.class));
    }
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
    	Command command = getCommandObject(request);
    	
    	long dataSourceId = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
    	command.setDataSource(HibernateUtil.getManager().getObject(DataSource.class, dataSourceId ));

        command = bind(request, command);
		return command;
    }
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
		long dsid = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		long myid = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_JSON_DATA_DEF_ID, -1);
		long parentDefId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		if (dsid >= 0) {
			DataSource dataSource = DataSource.findById(dsid);
			referenceData.put("dataSource", dataSource);
			if (dataSource != null) {
				referenceData.put("isReferenceDataSource", dataSource.isReferenceDataSource());
			}
			referenceData.put("startCustomerDataGroupNamePlaceholder", DataGroupUtil.findNextStartCustomerDataGroupNamePlaceholder(dsid));
		}


		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION);
		if(action!=null && (action.equals(ACTION_UPDATE_DEF) || action.equals(ACTION_UPDATE_KEY))){
			referenceData.put("parentDefs", getMyParentJsonDefs(dsid, myid));
		} else {
			// insert action
			referenceData.put("parentDefs", getMyParentJsonDefs(parentDefId));
		}
		referenceData.put("defTypes", JSONDefinitionType.listDefinitionTypes());
		JSONDataDefinition currDef = HibernateUtil.getManager().getObject(JSONDataDefinition.class, myid);
		JSONDataDefinition parentDef = HibernateUtil.getManager().getObject(JSONDataDefinition.class, parentDefId);
		if(parentDef == null && currDef != null){
			parentDef = currDef.getParentDefinition();
		}
		boolean isParentKeyStartsCustomer = parentDef != null && parentDef.getDefinitionType() == JSONDefinitionType.ID_KEY && parentDef.isStartCustomer();
		referenceData.put("canRepeatingRecord", !isParentKeyStartsCustomer);
		boolean hasDataGroup = false;
		if (currDef != null) {
			hasDataGroup = currDef.getDataGroup() != null;
		}
		referenceData.put("hasDataGroup", hasDataGroup);
    	return referenceData;
    }
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION);
		
    	if(action!=null && action.equals(ACTION_INSERT_DEF)){
    		
			String errorMsgKey = validateJsonDataDef(request);
			if (errorMsgKey != null) {
				String url = "json_data_invalid.jsp";
				Map<String, Object> params = new HashMap<>();
				params.put(JSONDataElementEditController.PARAMETER_ERROR_MESSAGE_KEY, errorMsgKey);
				return new ModelAndView(new RedirectView(url), params);
			} else {
				long parentDefId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		    	
				Command command = getCommandObject(request);
				//for insert action, current definition is the parent.
	    		command.setParentDefId(parentDefId);
				command.setId(0);
				command.setName(null);
				return super.showForm(request, response, errors);
			}
		} else {
			return super.showForm(request, response, errors);
		}
	}
	private String validateJsonDataDef(HttpServletRequest request){
		long dsid = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		long jddid = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		JSONDataDefinition rootDefinition = JSONDataDefinition.findRootDefinitionByDataSource(dsid);
		String errorKey = null;
		if (jddid <= 0 && rootDefinition != null){
			errorKey = "error.dataadmin.root.data.definition.exists";
		}
		return errorKey;
	}
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	boolean parentChanged = ServletRequestUtils.getBooleanParameter(request, PARM_PARENT_CHANGED, false);
    	boolean dataGroupChanged = ServletRequestUtils.getBooleanParameter(request, PARM_DATA_GROUP_CHANGED, false);
		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION, "");
    	Command command = (Command)commandObj;
    	long dsid = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		JSONDataDefinition dataDefinition = null;
		DataDefinitionVO dataDefinitionVO = null;
    	boolean isNew = command.getId() == 0?true:false;
    	if (command.getId() == 0) {
    		dataDefinition = new JSONDataDefinition();
    		DataSource dataSource = HibernateUtil.getManager()
					.getObject(DataSource.class, dsid);
    		dataDefinition.setDataSource(dataSource);
    		parentChanged=true;
			dataDefinition.setDefinitionType(action.equals(ACTION_INSERT_KEY)?JSONDefinitionType.ID_KEY:command.getDefTypeId());
    	} else {
    		dataDefinition = HibernateUtil.getManager().getObject(JSONDataDefinition.class, command.getId());
			dataDefinition.setName(command.getName());
			dataDefinitionVO = new DataDefinitionVO(dataDefinition);
    	}

		dataDefinition.setRepeating(command.isRepeating());
		dataDefinition.setStartCustomer(command.isStartCustomer());

		switch(dataDefinition.getDefinitionType()){
			case JSONDefinitionType.ID_OBJECT:
				if(command.getDefTypeId()==JSONDefinitionType.ID_ARRAY){	// Change from Object to Array
					dataDefinition.setDefinitionType(JSONDefinitionType.ID_ARRAY);
					dataDefinition.setName(JSONDefinitionType.getArrayType().getName().toUpperCase());
				}else {
					dataDefinition.setName(JSONDefinitionType.getObjectType().getName().toUpperCase());
				}
				break;
			case JSONDefinitionType.ID_ARRAY:
				if(command.getDefTypeId()==JSONDefinitionType.ID_OBJECT){	// Change from Array to Object
					dataDefinition.setDefinitionType(JSONDefinitionType.ID_OBJECT);
					dataDefinition.setName(JSONDefinitionType.getObjectType().getName().toUpperCase());
					dataDefinition.setRepeating(false);
				}else {
					dataDefinition.setName(JSONDefinitionType.getArrayType().getName().toUpperCase());
				}
				break;
			case JSONDefinitionType.ID_KEY:
				dataDefinition.setName(command.getKeyName());
				break;
		}
		
    	if(command.isStartDataGroup()){
    		//breakIndicator is for startDataGroup only
    		dataDefinition.setBreakIndicator(command.getBreakIndicator());
    	} else {
    		//other it should be removed
    		dataDefinition.setBreakIndicator("");
    	}
    	dataDefinition.setStartDataGroup(command.isStartDataGroup());
    	if(parentChanged){
    		dataDefinition.setParentDefinition(HibernateUtil.getManager().getObject(JSONDataDefinition.class, command.getParentDefId()));
    	}
    	ServiceExecutionContext context = UpdateJSONDataDefinitionService.createContext();
    	if(!isValidJsonDataDefinition(errors, dataDefinition)){
    		ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
    		return super.showForm(request, response, errors);
    	}

    	UpdateJSONDataDefinitionServiceRequest serviceRequest = (UpdateJSONDataDefinitionServiceRequest)context.getRequest();
		if (command.isStartDataGroup())
			serviceRequest.setDataGroupName(command.getDataGroupName().trim());
		else {
			serviceRequest.setDataGroupName("");
		}
    	serviceRequest.setDataSourceId(ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_DATASOURCE_ID, -1));
    	serviceRequest.setJsonDataDefinition(dataDefinition);
    	
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateJSONDataDefinitionService.SERVICE_NAME, UpdateJSONDataDefinitionService.class);
		service.execute(context);
		if (context.getResponse().isSuccessful()) {
			//Audit JSON Data Definition changes
			int defType = dataDefinition.getDefinitionType()==JSONDefinitionType.ID_KEY?AuditObjectType.ID_JSON_DATA_KEY:AuditObjectType.ID_JSON_DATA_DEFINITION;
			if(isNew){
				// Audit (JSON Data Definition Add)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, defType, dataDefinition.getName(), null, AuditActionType.ID_CREATION,
						AuditMetadataBuilder.forJSONDataDefinitionChanges(null, dataDefinition));
			}else{
				// Audit (JSON Data Definition Edit)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, defType, dataDefinition.getName(), null, AuditActionType.ID_CHANGES,
					AuditMetadataBuilder.forJSONDataDefinitionChanges(dataDefinitionVO, dataDefinition));
			}

			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			parms.put(JSONDataElementEditController.PARAMETER_JSON_DATA_DEF_ID, dataDefinition.getId());
			parms.put(PARM_PARENT_CHANGED, parentChanged);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms);
		} else {
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
    }

    protected Command getCommandObject(HttpServletRequest request) throws Exception {
		long jsonDefId = ServletRequestUtils.getLongParameter(request, JSONDataElementEditController.PARAMETER_JSON_DATA_DEF_ID, -1);
		long parentDefId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
    	JSONDataDefinition dataDefinition = null;
    	Command command = new Command();

		if(jsonDefId >0 ){
			dataDefinition = HibernateUtil.getManager().getObject(JSONDataDefinition.class, jsonDefId);
			JSONDataDefinition parentDef = dataDefinition.getParentDefinition();
			if(parentDef != null){
				command.setParentDefId(parentDef.getId());
			} else {
				command.setParentDefId(-1);
			}
			command.setId(dataDefinition.getId());
			command.setDefTypeId(dataDefinition.getDefinitionType());
			command.setName(dataDefinition.getName());
			if(dataDefinition.getDefinitionType()==JSONDefinitionType.ID_KEY){
				command.setKeyName(dataDefinition.getName());
			}
			command.setBreakIndicator(dataDefinition.getBreakIndicator());
			command.setRepeating(dataDefinition.isRepeating());
			command.setStartCustomer(dataDefinition.isStartCustomer());
			command.setStartDataGroup(dataDefinition.isStartDataGroup());

			if(dataDefinition.getDataGroup() != null){
				command.setDataGroupId(dataDefinition.getDataGroup().getId());
				command.setDataGroupName(dataDefinition.getDataGroup().getName());
				command.setDataGroupLevel(dataDefinition.getDataGroup().getLevel());
			} else {
				command.setDataGroupName("");
				command.setDataGroupId(-1);
				command.setDataGroupLevel(-1);
			}
		}else{
			JSONDataDefinition parentDef = HibernateUtil.getManager().getObject(JSONDataDefinition.class, parentDefId);
			if(parentDef != null){
				command.setParentDefId(parentDef.getId());
			} else {
				command.setParentDefId(-1);
			}
			//New Definition
			command.setDataGroupName("");
			command.setDataGroupId(-1);
			command.setDataGroupLevel(-1);
		}
    	return command;
    }

	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if( FORM_SUBMIT_TYPE_SUBMIT.equals(submitType) ){
			return true;
		}
		return false;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}

	private Command bind(HttpServletRequest request, Command command) {
		try {
			// Bind the submitType field.
			ServletRequestDataBinder binder = createBinder(request, command);

			request.getSession().setAttribute(JSONDataDefinitionEditController.PARAMETER_JSON_DATA_DEFINITION_COMMAND, command);
			binder.bind(request);
		} catch (Exception e) {
			throw new RuntimeException("Could not bind the DataRecord command object!", e);
		}
		return command;
	}

	public String getFormViewRedirect() {
		return formViewRedirect;
	}

	public void setFormViewRedirect(String formViewRedirect) {
		this.formViewRedirect = formViewRedirect;
	}
	
	@SuppressWarnings("unchecked")
	private List<JSONDataDefinition> getMyParentJsonDefs(long datasourceId, long myid){
		//get all tags that are not data element
		String hql = "from JSONDataDefinition d where "+
				" d.dataSource.id = :datasourceid and " +
				" d.id !=:myid order by name";
		Map<String, Object> params = new HashMap<>();
    	params.put("datasourceid", datasourceId);
    		params.put("myid", myid);
		List<JSONDataDefinition> definitions = (List<JSONDataDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
		return filterParentDefs(definitions, myid);
	}
	private static List<JSONDataDefinition> filterParentDefs(List<JSONDataDefinition> inList, long myDefId){
		List<JSONDataDefinition> outList = new ArrayList<>();
		for(JSONDataDefinition definition: inList){
			if(!definition.isDescendentOfThisDef(myDefId)){ //not a descendent of myTag
			 definition.setName(getFullName(definition, null));
				outList.add(definition);
			}
		}
		return outList;
	}

	private static String getFullName(JSONDataDefinition tagDefinition, String fullName){
		if(fullName == null){
			fullName = tagDefinition.getName();
		} else {
			fullName = tagDefinition.getName() + "." + fullName;
		}
		if(tagDefinition.getParentDefinition() != null) {
			return getFullName(tagDefinition.getParentDefinition(), fullName);
		} else {
			return fullName;
		}
	}

	private List<JSONDataDefinition> getMyParentJsonDefs(long parentId){
		//get all tags that are not data element
		List<JSONDataDefinition> definitions = new ArrayList<>();
		if(parentId > 0){
			definitions.add(JSONDataDefinition.findById(parentId));
		}
		return definitions;
	}
	

	/* validation rules:
b.	Only children of parent definition which starts customer can be repeated, which means all global definitions cannot be repeated.
c.	Starts Customer flag is unique for a data model (only one definition can start customer in the data model).

	 */
	@SuppressWarnings("unchecked")
	private boolean isValidJsonDataDefinition(BindException errors, JSONDataDefinition definition){
		boolean isValid = true;
		//check if the start customer definition already exists in the data source.
		JSONDataDefinition startCustomerdef = getStartCustomerDef(definition);
		if(startCustomerdef !=null && definition.isStartCustomer()){
			if((definition.getId() > 0 && definition.getId() != startCustomerdef.getId()) //existing definition
					|| definition.getId() <=0) { // new tag
				errors.reject("error.dataadmin.another.data.key.start.customer",
					new String[] {startCustomerdef.getName(), definition.getDataSource().getName()}, "");
				definition.setStartCustomer(false);
				isValid = false;
			}
		} 

		if(definition.isRepeating()){
			//check if the definition is a descendent of startCustomer definition.
			String hql = "from JSONDataDefinition jdd where jdd.startCustomer = true and jdd.dataSource.id =:datasourceId";
			HashMap<String, Object> params = new HashMap<>();
	    	params.put("datasourceId", definition.getDataSource().getId());
	    	List<JSONDataDefinition> startCustmerDef = (List<JSONDataDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
			if(startCustmerDef !=null && !startCustmerDef.isEmpty() &&
					!definition.isDescendentOfThisDef(startCustmerDef.get(0))){
				errors.reject("error.dataadmin.not.descendent.start.customer.for.json");
				definition.setRepeating(false);
				isValid = false;
			}
		}
		return isValid;
	}

	@SuppressWarnings("unchecked")
	private static JSONDataDefinition getStartDataGroupDef(long groupId, long dataSourceId){
		String hql = "from JSONDataDefinition jdd where jdd.dataGroup.id = :groupId and jdd.dataSource.id = :datasourceId";
		HashMap<String, Object> params = new HashMap<>();
    	params.put("groupId", groupId);
    	params.put("datasourceId", dataSourceId);
    	List<JSONDataDefinition> definitions = (List<JSONDataDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
    	//There should be only one.
    	if(definitions != null && !definitions.isEmpty()){
    		return definitions.get(0);
    	} else return null;
	}

	@SuppressWarnings("unchecked")
	private static JSONDataDefinition getStartCustomerDef(JSONDataDefinition definition){
		String hql = "from JSONDataDefinition jdd where jdd.startCustomer = true and jdd.dataSource.id = :datasourceId";
		HashMap<String, Object> params = new HashMap<>();
    	params.put("datasourceId", definition.getDataSource().getId());
    	List<JSONDataDefinition> definitions = (List<JSONDataDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
    	//There should be only one.
    	if(definitions != null && !definitions.isEmpty()){
    		return definitions.get(0);
    	} else return null;
	}
	
	public static class Command implements Serializable{
		private static final long serialVersionUID = -5152092525556413486L;
		private long id;
		private long parentDefId;
		private int defTypeId;
		private long dataGroupId;
		private String name;
		private String keyName;
		private String breakIndicator;
		private boolean startCustomer;
		private boolean repeating;
		private boolean enabled;
		private DataSource dataSource;
		private boolean startDataGroup;
		private String dataGroupName;
		private int dataGroupLevel;

		public long getId() {
			return id;
		}
		public void setId(long id) {
			this.id = id;
		}
		
		public long getParentDefId() {
			return parentDefId;
		}

		public void setParentDefId(long parentDefId) {
			this.parentDefId = parentDefId;
		}

		public int getDefTypeId() {
			return defTypeId;
		}

		public void setDefTypeId(int defTypeId) {
			this.defTypeId = defTypeId;
		}

		public long getDataGroupId() {
			return dataGroupId;
		}
		public void setDataGroupId(long dataGroupId) {
			this.dataGroupId = dataGroupId;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}

		public String getKeyName() {
			return keyName;
		}
		public void setKeyName(String keyName) {
			this.keyName = keyName;
		}

		public String getBreakIndicator() {
			return breakIndicator;
		}
		public void setBreakIndicator(String breakIndicator) {
			this.breakIndicator = breakIndicator;
		}
		public boolean isStartCustomer() {
			return startCustomer;
		}
		public void setStartCustomer(boolean startCustomer) {
			this.startCustomer = startCustomer;
		}
		public boolean isRepeating() {
			return repeating;
		}
		public void setRepeating(boolean repeating) {
			this.repeating = repeating;
		}
		public boolean isEnabled() {
			return enabled;
		}
		public void setEnabled(boolean enabled) {
			this.enabled = enabled;
		}
		public DataSource getDataSource() {
			return dataSource;
		}
		public void setDataSource(DataSource dataSource) {
			this.dataSource = dataSource;
		}
		
		
		public boolean isStartDataGroup() {
			return startDataGroup;
		}
		public void setStartDataGroup(boolean startDataGroup) {
			this.startDataGroup = startDataGroup;
		}
		public String getDataGroupName() {
			return dataGroupName;
		}
		public void setDataGroupName(String dataGroupName) {
			this.dataGroupName = dataGroupName;
		}
		public int getDataGroupLevel() {
			return dataGroupLevel;
		}
		public void setDataGroupLevel(int dataGroupLevel) {
			this.dataGroupLevel = dataGroupLevel;
		}
	}
}
