package com.prinova.messagepoint.controller.dataadmin.jsonlayout;

import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

public class JSONDataElementEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object command, Errors errors) {
		//XmlDataElement dataElement = (XmlDataElement)command;
		//TODO:
	}
}
