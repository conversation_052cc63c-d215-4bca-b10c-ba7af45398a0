package com.prinova.messagepoint.controller.dataadmin.jsonlayout;

import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

public class JSONDataDefinitionEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object command, Errors errors) {
		JSONDataDefinitionEditController.Command commandJson = (JSONDataDefinitionEditController.Command)command;
		if(commandJson.getId()>0) {
			JSONDataDefinition dataDefinition = HibernateUtil.getManager().getObject(JSONDataDefinition.class, commandJson.getId());
			if(dataDefinition.getDefinitionType()==JSONDefinitionType.ID_ARRAY && commandJson.getDefTypeId()==JSONDefinitionType.ID_OBJECT) { // Change from Array to Object
				if (dataDefinition.getJsonDataElement() != null) {
					errors.rejectValue("defTypeId", "error.message.jsond.cannot.convert.array.element.exists");
				}
			}
		}
	}
}
