package com.prinova.messagepoint.controller.dataadmin.xmllayout;

import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.model.util.DataGroupUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.stream.Collectors;

public class XmlDataTagDefinitionEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object command, Errors errors) {

		XmlDataTagDefinitionEditController.Command commandXmlTag = (XmlDataTagDefinitionEditController.Command)command;
		long dataSourceId = commandXmlTag.getDataSource().getId();
		DataSource lazyDataSource = HibernateUtil.getManager().getObject(DataSource.class, dataSourceId );
		String newDataGroupName = commandXmlTag.getDataGroupName() != null ? commandXmlTag.getDataGroupName().trim() : "";

		if (commandXmlTag.isStartCustomer()){
			//Check if a starts customer is already exists
			XmlDataTagDefinition startsCustomerXmlDataTag = XmlDataTagDefinition.getStartsCustomerXmlDataTagDefinition(lazyDataSource.getId());
			if (startsCustomerXmlDataTag != null && startsCustomerXmlDataTag.getId() != commandXmlTag.getId()) {
				errors.reject("error.message.another.xml.start.customer.in.use",
						new String[] {String.valueOf(startsCustomerXmlDataTag.getId()), lazyDataSource.getName()}, "");
			}

			if (!commandXmlTag.isStartDataGroup()) {
				//Extra serverside check to see that starts data group is checked
				errors.rejectValue("commandXmlTag.startCustomer", "error.message.start.customer.and.start.data.group");
			} else {
				if (newDataGroupName.isEmpty()) {
					newDataGroupName = DataGroupUtil.findNextStartCustomerDataGroupNamePlaceholder(dataSourceId);
					commandXmlTag.setDataGroupName(newDataGroupName);
				}
			}
		}
		//DataGroup name validation check
		boolean isNewXmlTag = commandXmlTag.getId() < 1;
		if (isNewXmlTag) {
			if (commandXmlTag.isStartDataGroup())
				validateUniqueDataGroupName(errors, commandXmlTag.getId(), newDataGroupName, dataSourceId);
		} else {
			XmlDataTagDefinition origXmlTag = XmlDataTagDefinition.findById(commandXmlTag.getId());
			if (origXmlTag == null) {
				errors.reject("error.message.action.not.permitted");
			} else {
				DataGroup origDataGroup = origXmlTag.getDataGroup();
				boolean isNewDataGroup = origDataGroup == null;
				if (isNewDataGroup) {
					if (commandXmlTag.isStartDataGroup())
						validateUniqueDataGroupName(errors, commandXmlTag.getId(), newDataGroupName, dataSourceId);
				} else {
					validateUniqueDataGroupName(errors, commandXmlTag.getId(), newDataGroupName, dataSourceId);
				}
			}
		}
	}

	//Checking for unique data group name
	private void validateUniqueDataGroupName(Errors errors, long commandXmlDataTagId, String newDataGroupName, long dataSourceId) {
		if (newDataGroupName.isEmpty()) {
			errors.rejectValue("dataGroupName", "error.message.data.group.name.required");
			return;
		}

		XmlDataTagDefinition origXmlDataTag = XmlDataTagDefinition.findById(commandXmlDataTagId);
		//If XmlDataTag is new, or DataGroup name changed.
		if(origXmlDataTag == null || origXmlDataTag.getDataGroup() == null
				|| !origXmlDataTag.getDataGroup().getName().equals(newDataGroupName)) {
			//Look for any DataGroups with the same name for given DataSource
			List<DataGroup> dataGroups = DataGroupUtil.findByNameAndDataSource(newDataGroupName, dataSourceId);
			if (dataGroups != null && !dataGroups.isEmpty()) {
				//Retrieves xml tag names that are associated with the DataGroup
				String xmlNames = dataGroups.stream()
						.flatMap(dg -> dg.getXmlDataTagDefinitions().stream())
						.map(XmlDataTagDefinition::getName)
						.collect(Collectors.joining(", "));
				errors.rejectValue("dataGroupName", "error.message.data.group.name.unique", new Object[]{xmlNames}, null);
			}
		}
	}
}
