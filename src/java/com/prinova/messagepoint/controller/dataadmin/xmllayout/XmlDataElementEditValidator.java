package com.prinova.messagepoint.controller.dataadmin.xmllayout;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class XmlDataElementEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object command, Errors errors) {
		//XmlDataElement dataElement = (XmlDataElement)command;
		//TODO:
	}
}
