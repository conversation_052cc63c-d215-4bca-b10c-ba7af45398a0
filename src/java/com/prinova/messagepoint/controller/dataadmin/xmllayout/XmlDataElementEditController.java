package com.prinova.messagepoint.controller.dataadmin.xmllayout;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.IdCustomEditorInt;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.XmlDataElementVO;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataElementService;
import com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataElementServiceRequest;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XmlDataElementEditController extends MessagepointController implements Serializable {
	private static final long serialVersionUID = -5916373414501044559L;

	public static final String PARAMETER_DATAELEMENT_ID = "deid";
	public static final String PARAMETER_DATAELEMENT = "XmlDataElement";
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "process";
	public static final String PARAMETER_IS_ATTRIBUTE = "isAttributeDataElement";
	public static final String PARAMETER_XML_DATA_TAG_ID = "xdtid";
	public static final String PARAMETER_DATASOURCE_ID ="dsid";
	public static final String PARAMETER_ERROR_MESSAGE_KEY="errormsgkey";
	public static final int ERROR_VALUE_DATA_EXIST = 1 ;

	private String formViewRedirect;

	@Override
    protected Map<String, Object>  referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
    	
    	referenceData.put("dataTypes"   , HibernateUtil.getManager().getObjects(DataType.class, MessagepointOrder.asc("id")));
    	referenceData.put("dataSubTypes", HibernateUtil.getManager().getObjects(DataSubtype.class, MessagepointOrder.asc("id")) );
		referenceData.put("dataImageTypes", SubContentType.listAllForImageDataElement());
		long dsid = ServletRequestUtils.getLongParameter(request, PARAMETER_DATASOURCE_ID, -1);
		if (dsid >= 0)
			referenceData.put("dataSource", HibernateUtil.getManager().getObject(DataSource.class, dsid) );
		
		boolean licencedForDataAnonymizer = MessagepointLicenceManager.getInstance().isLicencedForDataAnonymizer();
		referenceData.put("licencedForDataAnonymizer", licencedForDataAnonymizer);
		referenceData.put("anonymizationMasks", DataAnonymizationType.listAll());
		referenceData.put("dataAnonymizerDelimiter", ApplicationUtil.getProperty(SystemPropertyKeys.Data.KEY_DataAnonymizerDelimeter));
    	
    	return referenceData;
    }
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		Boolean isAttribute = ServletRequestUtils.getBooleanParameter(request, PARAMETER_IS_ATTRIBUTE);
		if(isAttribute!=null && isAttribute.booleanValue()){
			return super.showForm(request, response, errors);
		} 
		// do validation only for value date element
    	String errorMsgKey = validateXmlDataTag(request);
    	if (errorMsgKey != null) {
    		String url = "xml_data_invalid.jsp";
    		Map<String, Object> params = new HashMap<>();
    		params.put(PARAMETER_ERROR_MESSAGE_KEY, errorMsgKey);
			return new ModelAndView(new RedirectView(url), params);
    	} else {
    		return super.showForm(request, response, errors);
    	}
	}
	private String validateXmlDataTag(HttpServletRequest request){
		long xdtid = ServletRequestUtils.getLongParameter(request, PARAMETER_XML_DATA_TAG_ID, -1);
		String errorKey = null;
		if (xdtid >= 0){
			XmlDataTagDefinition xmlTag =HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, xdtid) ;
			if(xmlTag.isValueTag()){
				XmlDataElement dataElement = getCommandObject(request);
				if(dataElement.getId() <=0){
					// insert action
					errorKey ="error.dataadmin.data.tag.value.data.exist";
				}
			} else {
				List<XmlDataTagDefinition> children= xmlTag.getChildList(xmlTag.getId());
				if(children !=null && !children.isEmpty()){
					errorKey ="error.dataadmin.data.tag.child.tag.exist";					
				}
			}			
		}
		return errorKey;
	}
    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(XmlDataElement.class, new IdCustomEditor<>(XmlDataElement.class));
    	binder.registerCustomEditor(DataType.class, new IdCustomEditorInt<>(DataType.class));
    	binder.registerCustomEditor(DataSubtype.class, new IdCustomEditorInt<>(DataSubtype.class));
   	   	binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
   		binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    protected Object formBackingObject(HttpServletRequest request) {   	   	
    	XmlDataElement command = getCommandObject(request);
    	XmlDataElement dataElement = bind(request, command);
		return dataElement;
    }
    
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
    	
    	XmlDataElement dataElement = (XmlDataElement)command;
    	
    	boolean isNew = dataElement.getId() > 0? false:true;
    	XmlDataElement orgDtaElement = isNew? null:(XmlDataElement) XmlDataElement.findById(dataElement.getId());
    	XmlDataElementVO dataElementVO = orgDtaElement == null? null:new XmlDataElementVO(orgDtaElement);
 	
      	ServiceExecutionContext context = UpdateXmlDataElementService.createContext();
      	boolean attributeDE = ServletRequestUtils.getBooleanParameter(request, PARAMETER_IS_ATTRIBUTE, false);
      	dataElement.setIsAttribute(attributeDE);
      	UpdateXmlDataElementServiceRequest serviceRequest =(UpdateXmlDataElementServiceRequest) context.getRequest();
      	serviceRequest.setXmlDataElement(dataElement);
      	serviceRequest.setXmlDataTagDefinitionId(ServletRequestUtils.getLongParameter(request, PARAMETER_XML_DATA_TAG_ID, -1));
      	
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateXmlDataElementService.SERVICE_NAME, UpdateXmlDataElementService.class);
		service.execute(context);
		if(context.getResponse().isSuccessful()){
			if(isNew){
				// Audit (XML DataElement New)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, AuditObjectType.ID_DATA_SOURCE, dataElement.getName(), null, AuditActionType.ID_CREATION, 
						AuditMetadataBuilder.forXMLDataElementChanges(dataElementVO, serviceRequest));
			}else{
				// Audit (XML DataElement Edit)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, AuditObjectType.ID_DATA_SOURCE, dataElement.getName(), null, AuditActionType.ID_CHANGES, 
						AuditMetadataBuilder.forXMLDataElementChanges(dataElementVO, serviceRequest));
			}
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			parms.put(PARAMETER_DATAELEMENT_ID, dataElement.getId());	
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms ); 
		}else{
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
    }

    protected XmlDataElement getCommandObject(HttpServletRequest request) {
    	long dataElementId = ServletRequestUtils.getLongParameter(request, PARAMETER_DATAELEMENT_ID, -1);
		XmlDataElement command = (dataElementId == -1)? new XmlDataElement() : HibernateUtil.getManager().getObject(XmlDataElement.class, dataElementId);
    	if(command.getExternalFormatText()!=null){
	    	if (command.getExternalFormatText().trim().contains("  ")) {
	    		command.setExternalFormatText(command.getExternalFormatText().replace("  ", " "));
	    	}
    	}
    	long tagId = ServletRequestUtils.getLongParameter(request, PARAMETER_XML_DATA_TAG_ID, -1);
    	command.setXmlDataTagDefinition(HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, tagId ));
    	return command;
    }

	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if( FORM_SUBMIT_TYPE_SUBMIT.equals(submitType) ){
			return true;
		}
		return false;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}

	private XmlDataElement bind(HttpServletRequest request, XmlDataElement command) {
		
		try {
			// Bind the submitType field.
			ServletRequestDataBinder binder = createBinder(request, command);
			
			long xmlTagId =ServletRequestUtils.getLongParameter(request, PARAMETER_XML_DATA_TAG_ID, -1);
			if(command.getXmlDataTagDefinition() !=null){
				command.getXmlDataTagDefinition().setId(xmlTagId);
			} 
			request.getSession().setAttribute(XmlDataElementEditController.PARAMETER_DATAELEMENT, command);
			binder.bind(request);
		} catch (Exception e) {
			throw new RuntimeException("Could not bind the DataElement command object!", e);
		}
		return command;
	}

	public String getFormViewRedirect() {
		return formViewRedirect;
	}

	public void setFormViewRedirect(String formViewRedirect) {
		this.formViewRedirect = formViewRedirect;
	}
}
