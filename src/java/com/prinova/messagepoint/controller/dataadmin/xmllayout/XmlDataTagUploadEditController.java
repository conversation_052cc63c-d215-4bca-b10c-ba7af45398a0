package com.prinova.messagepoint.controller.dataadmin.xmllayout;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.*;
import org.apache.commons.io.FileUtils;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.DataSubtype;
import com.prinova.messagepoint.model.admin.DataType;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.platform.services.backgroundtask.UploadXMLDataSourceTagsBackgroundTask;
import com.prinova.messagepoint.security.StringXSSEditor;

public class XmlDataTagUploadEditController extends MessagepointController implements Serializable {
	private static final long serialVersionUID = -5916474414501044559L;
	public static final String PARAMETER_XML_DATA_TAG_UPLOAD_COMMAND = "XmlDataTagUploadCommand";
	public static final String PARM_PARENT_CHANGED = "parentChanged";
	public static final String PARM_ACTION = "action";
	public static final String PARM_PARENT_ID = "parentid";
	
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "process";

	private String formViewRedirect;
	
    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(XmlDataTagDefinition.class, new IdCustomEditor<>(XmlDataTagDefinition.class));
   	   	binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
   		binder.registerCustomEditor(String.class, new StringXSSEditor());
    }
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
    	Command command = getCommandObject(request);
    	
    	long dataSourceId = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
    	command.setDataSource(HibernateUtil.getManager().getObject(DataSource.class, dataSourceId ));

        command = bind(request, command);
		return command;
    }
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
    	
    	referenceData.put("dataTypes"   , HibernateUtil.getManager().getObjects(DataType.class, MessagepointOrder.asc("id")));
    	referenceData.put("dataSubTypes", HibernateUtil.getManager().getObjects(DataSubtype.class, MessagepointOrder.asc("id")) );
    	
		long dsid = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		long parentTagId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		if (dsid >= 0)
			referenceData.put("dataSource", HibernateUtil.getManager().getObject(DataSource.class, dsid) );
		referenceData.put("dataGroups", getDataGroups(dsid));
		referenceData.put("xmlTags", getMyParentXmlTags(parentTagId));
    	return referenceData;
    }
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION);
		
    	if(action!=null && action.equals("uploadxmldatatags")){
    		
			String errorMsgKey = validateXmlDataTag(request);
			if (errorMsgKey != null) {
				String url = "xml_data_invalid.jsp";
				Map<String, Object> params = new HashMap<>();
				params.put(XmlDataElementEditController.PARAMETER_ERROR_MESSAGE_KEY, errorMsgKey);
				return new ModelAndView(new RedirectView(url), params);
			} else {
				long parentTagId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		    	
				Command command = getCommandObject(request);
				//for upload action, current tag is the parent.
	    		command.setParentTagId(parentTagId);
				command.setId(0);
				return super.showForm(request, response, errors);
			}
		} else {
			return super.showForm(request, response, errors);
		}
	}
	private String validateXmlDataTag(HttpServletRequest request){
		long xdtid = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		String errorKey = null;
		if (xdtid > 0){
			XmlDataTagDefinition xmlTag =HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, xdtid) ;
			if(xmlTag.isValueTag()){				
				errorKey ="error.dataadmin.data.tag.value.data.exist";
			} 
		}
		return errorKey;
	}
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	Command command = (Command)commandObj;
    	long dsid = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);

		String tmpFilerootPath 	= ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir );
		FileUtil.createDirectoryIfNeeded(tmpFilerootPath);
		String tmpFilename		= "temp_" + UserUtil.getPrincipalUserId() + "_" + DateUtil.timeStamp() + ".xml";
		
		File importFile = new File(tmpFilerootPath + tmpFilename);
		FileUtils.writeByteArrayToFile(importFile, command.getImportFileContent());

    	UploadXMLDataSourceTagsBackgroundTask task = new UploadXMLDataSourceTagsBackgroundTask(dsid, command.getParentTagId(), importFile,
    																							command.isCreateDataElements(), command.isCreateVariables(),
    																							command.getDataSubTypeForView(), command.getDecimalPlaces(),
    																							command.getExternalFormatText(), command.getImportOriginalFilename(),
    																							UserUtil.getPrincipalUser());
		MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
		Map<String, Object> parms = new HashMap<>();
		parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		parms.put(PARM_PARENT_CHANGED, false);
		return new ModelAndView( new RedirectView("../frameClose.jsp"), parms);
    }

    protected Command getCommandObject(HttpServletRequest request) throws Exception {
    	Command command = null;
    	
    	if (request != null) {
    		command = (Command)request.getSession().getAttribute(XmlDataTagUploadEditController.PARAMETER_XML_DATA_TAG_UPLOAD_COMMAND);
    	}
    	
    	if (command == null) {
    		command = new Command();
    	}
    	
    	return command;
    }

	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if( FORM_SUBMIT_TYPE_SUBMIT.equals(submitType) ){
			return true;
		}
		return false;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}

	private Command bind(HttpServletRequest request, Command command) {
		try {
			// Bind the submitType field.
			ServletRequestDataBinder binder = createBinder(request, command);

			request.getSession().setAttribute(XmlDataTagUploadEditController.PARAMETER_XML_DATA_TAG_UPLOAD_COMMAND, command);
			binder.bind(request);
		} catch (Exception e) {
			throw new RuntimeException("Could not bind the DataRecord command object!", e);
		}
		return command;
	}

	public String getFormViewRedirect() {
		return formViewRedirect;
	}

	public void setFormViewRedirect(String formViewRedirect) {
		this.formViewRedirect = formViewRedirect;
	}
	
	private List<XmlDataTagDefinition> getMyParentXmlTags(long parentId) {
		//get all tags that are not data element
		List<XmlDataTagDefinition> xmlTags = new ArrayList<>();
		if(parentId > 0){
			xmlTags.add(XmlDataTagDefinition.findById(parentId));
		}
		return xmlTags;
	}
	
	@SuppressWarnings("unchecked")
	private List<DataGroup> getDataGroups(long datasourceId) {
		//get all tags that are not data element
		String hql = "from DataGroup dg where  dg.dataSource.id = :datasourceid order by name";
		Map<String, Object> params = new HashMap<>();
    	params.put("datasourceid", datasourceId);
    	
		List<DataGroup> dataGroups = (List<DataGroup>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
		
		return dataGroups;
	}
	
	public static class Command implements Serializable {
		private static final long serialVersionUID = 5899504065858164196L;

		private long id;
		private long parentTagId;
		private String importOriginalFilename;
		private byte[] importFileContent;
		private DataSource dataSource;
		private int dataSubTypeForView;
		private int dataSubtypeId;
		protected int dataImageTypeId;
		private int decimalPlaces;
		private String externalFormatText;
		private boolean createDataElements = true;
		private boolean createVariables = false;
		
		public long getId() {
			return id;
		}
		public void setId(long id) {
			this.id = id;
		}
		
		public long getParentTagId() {
			return parentTagId;
		}
		public void setParentTagId(long parentTagId) {
			this.parentTagId = parentTagId;
		}
		
		public MultipartFile getImportFilename() {
			return null;
		}
		public void setImportFilename(MultipartFile importFilename) {
			try {
				this.importOriginalFilename = importFilename.getOriginalFilename();
				this.importFileContent = importFilename.getBytes();
			} catch (Exception e) {
				LogUtil.getLog(XmlDataTagUploadEditController.class).error("Error", e);
			}
		}

		public String getImportOriginalFilename() {
			return importOriginalFilename;
		}

		public void setImportOriginalFilename(String importOriginalFilename) {
			this.importOriginalFilename = importOriginalFilename;
		}

		public byte[] getImportFileContent() {
			return importFileContent;
		}

		public void setImportFileContent(byte[] importFileContent) {
			this.importFileContent = importFileContent;
		}

		public DataSource getDataSource() {
			return dataSource;
		}
		public void setDataSource(DataSource dataSource) {
			this.dataSource = dataSource;
		}
		
		public int getDataSubTypeForView() {
			return dataSubTypeForView;
		}
		public void setDataSubTypeForView(int dataSubTypeForView) {
			this.dataSubTypeForView = dataSubTypeForView;
		}
		
		public int getDataSubtypeId() {
			return dataSubtypeId;
		}
		public void setDataSubtypeId(int dataSubtypeId) {
			this.dataSubtypeId = dataSubtypeId;
		}

		public int getDataImageTypeId() { return dataImageTypeId; }
		public void setDataImageTypeId(int dataImageTypeId) { this.dataImageTypeId = dataImageTypeId; }
		
		public int getDecimalPlaces() {
			return decimalPlaces;
		}
		public void setDecimalPlaces(int decimalPlaces) {
			this.decimalPlaces = decimalPlaces;
		}
		
		public String getExternalFormatText() {
			return externalFormatText;
		}
		public void setExternalFormatText(String externalFormatText) {
			this.externalFormatText = externalFormatText;
		}
		
		public boolean isCreateDataElements() {
			return createDataElements;
		}
		public void setCreateDataElements(boolean createDataElements) {
			this.createDataElements = createDataElements;
		}
		
		public boolean isCreateVariables() {
			return createVariables;
		}
		public void setCreateVariables(boolean createVariables) {
			this.createVariables = createVariables;
		}
		
	}
}
