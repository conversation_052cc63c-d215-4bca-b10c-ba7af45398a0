package com.prinova.messagepoint.controller.dataadmin.xmllayout;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.DataTagVO;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.model.util.DataGroupUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataTagDefinitionService;
import com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataTagDefinitionServiceRequest;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XmlDataTagDefinitionEditController extends MessagepointController implements Serializable {
	private static final long serialVersionUID = -5916373414501044559L;
	public static final String PARAMETER_XML_DATA_TAG_DEFINITION_COMMAND = "XmlDataTagDefinitionCommand";
	public static final String PARAM_XML_DATA_TAG_ID = "xdtid";
	public static final String PARM_PARENT_CHANGED = "parentChanged";
	public static final String PARM_DATA_GROUP_CHANGED = "dataGroupChanged";
	public static final String PARM_ACTION = "action";
	public static final String ACTION_UPDATE = "updatexmldatatag";
	public static final String ACTION_INSERT = "insertxmldatatag";
	public static final String PARM_PARENT_ID = "parentid";
	
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "process";

	private String formViewRedirect;
	
    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(XmlDataTagDefinition.class, new IdCustomEditor<>(XmlDataTagDefinition.class));
    	// binder.registerCustomEditor(DataGroup.class, new IdCustomEditorInt<DataGroup>(DataGroup.class));
   	   	binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
   		binder.registerCustomEditor(String.class, new StringXSSEditor());
    }
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
    	Command command = getCommandObject(request);
		command = bind(request, command);
		return command;
    }
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();

		long dsid = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		if (dsid >= 0){
			DataSource dataSource = DataSource.findById(dsid);
			referenceData.put("dataSource", dataSource);
			if (dataSource != null) {
				referenceData.put("isReferenceDataSource",dataSource.isReferenceDataSource());
			}
			referenceData.put("startCustomerDataGroupNamePlaceholder", DataGroupUtil.findNextStartCustomerDataGroupNamePlaceholder(dsid));
		}

		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION);
		if( action!=null && (action.equals(ACTION_UPDATE) || action.equals(ACTION_INSERT)) ){
			//Get tags by data source
			referenceData.put("xmlTags", XmlDataTagDefinition.findXmlTagsByDatasource(dsid));
		}

		long xdtid = ServletRequestUtils.getLongParameter(request, PARAM_XML_DATA_TAG_ID, -1);
		boolean hasDataGroup = false;
		if (xdtid > 0){
			XmlDataTagDefinition currDef = HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, xdtid);
			if (currDef != null) {
				hasDataGroup = currDef.getDataGroup() != null;
			}

		}
		referenceData.put("hasDataGroup", hasDataGroup);

    	return referenceData;
    }
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		String action = ServletRequestUtils.getStringParameter(request, PARM_ACTION);
		
    	if(action!=null && action.equals(ACTION_INSERT)){
    		
			String errorMsgKey = validateXmlDataTag(request);
			if (errorMsgKey != null) {
				String url = "xml_data_invalid.jsp";
				Map<String, Object> params = new HashMap<>();
				params.put(XmlDataElementEditController.PARAMETER_ERROR_MESSAGE_KEY, errorMsgKey);
				return new ModelAndView(new RedirectView(url), params);
			} else {
				long parentTagId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		    	
				Command command = getCommandObject(request);
				//for insert action, current tag is the parent.
	    		command.setParentTagId(parentTagId);
				command.setId(0);
				command.setName(null);
				return super.showForm(request, response, errors);
			}
		} else {
			return super.showForm(request, response, errors);
		}
	}
	private String validateXmlDataTag(HttpServletRequest request){
		long xdtid = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
		String errorKey = null;
		if (xdtid > 0){
			XmlDataTagDefinition xmlTag =HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, xdtid) ;
			if(xmlTag.isValueTag()){				
				errorKey ="error.dataadmin.data.tag.value.data.exist";
			} 
		}
		return errorKey;
	}
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	boolean parentChanged = ServletRequestUtils.getBooleanParameter(request, PARM_PARENT_CHANGED, false);
    	boolean dataGroupChanged = ServletRequestUtils.getBooleanParameter(request, PARM_DATA_GROUP_CHANGED, false);    	
    	Command command = (Command)commandObj;
    	long dsid = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
    	XmlDataTagDefinition xmlTag = null;
    	DataTagVO tagVO = null;
    	boolean isNew = command.getId() == 0?true:false;
    	if (command.getId() == 0) {
    		xmlTag = new XmlDataTagDefinition();
    		DataSource dataSource = HibernateUtil.getManager()
					.getObject(DataSource.class, dsid);
    		xmlTag.setDataSource(dataSource);
    		parentChanged=true;
    	} else {
    		xmlTag = HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, command.getId());
    		tagVO = new DataTagVO(xmlTag);
    	}
    	xmlTag.setName(command.getName());
    	
    	xmlTag.setRepeating(command.isRepeating());
    	xmlTag.setStartCustomer(command.isStartCustomer());
    	if(command.isStartDataGroup()){
    		//breakIndicator is for startDataGroup only
    		xmlTag.setBreakIndicator(command.getBreakIndicator());
    	} else {
    		//other it should be removed
    		xmlTag.setBreakIndicator("");
    	}
    	xmlTag.setStartDataGroup(command.isStartDataGroup());
    	if(parentChanged){
    		xmlTag.setParentTag((XmlDataTagDefinition) HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, command.getParentTagId()));
    	} 
    	ServiceExecutionContext context = UpdateXmlDataTagDefinitionService.createContext();
    	
    	if(!isValidXmlDataTag(errors, xmlTag)){
    		ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
    		return super.showForm(request, response, errors);
    	}

    	UpdateXmlDataTagDefinitionServiceRequest serviceRequest = (UpdateXmlDataTagDefinitionServiceRequest)context.getRequest();
    	serviceRequest.setDataSourceId(ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1));
		if (command.isStartDataGroup())
			serviceRequest.setDataGroupName(command.getDataGroupName().trim());
		else {
			serviceRequest.setDataGroupName("");
		}
    	serviceRequest.setXmlDataTagDefinition(xmlTag);
    	
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateXmlDataTagDefinitionService.SERVICE_NAME, UpdateXmlDataTagDefinitionService.class);
		service.execute(context);
		if (context.getResponse().isSuccessful()) {
			if(isNew){
				// Audit (XML Data Tag Add)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, AuditObjectType.ID_XML_DATA_TAG, xmlTag.getName(), null, AuditActionType.ID_DATA_ELEMENT, 
						AuditMetadataBuilder.forXMLDataTagChanges(null, xmlTag));
			}else{
				// Audit (XML Data Tag Edit)
				AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, AuditObjectType.ID_XML_DATA_TAG, xmlTag.getName(), null, AuditActionType.ID_DATA_ELEMENT, 
					AuditMetadataBuilder.forXMLDataTagChanges(tagVO, xmlTag));
			}
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			parms.put(XmlDataElementEditController.PARAMETER_XML_DATA_TAG_ID, xmlTag.getId());
			parms.put(PARM_PARENT_CHANGED, parentChanged);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms);
		} else {
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
    }

    protected Command getCommandObject(HttpServletRequest request) throws Exception {
		Command command = new Command();

		long dataSourceId = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_DATASOURCE_ID, -1);
		DataSource dataSource = HibernateUtil.getManager().getObject(DataSource.class, dataSourceId );
		command.setDataSource(dataSource);

		XmlDataTagDefinition xmlTag = null;
		long xmlTagId = ServletRequestUtils.getLongParameter(request, XmlDataElementEditController.PARAMETER_XML_DATA_TAG_ID, -1);

		if(xmlTagId >0 ){
			xmlTag = HibernateUtil.getManager().getObject(XmlDataTagDefinition.class, xmlTagId);
			XmlDataTagDefinition parentTag = xmlTag.getParentTag();
			if(parentTag != null){
				command.setParentTagId(parentTag.getId());
			} else {
				command.setParentTagId(-1);
			}
			command.setId(xmlTag.getId());

			command.setName(xmlTag.getName());
			command.setBreakIndicator(xmlTag.getBreakIndicator());
			command.setRepeating(xmlTag.isRepeating());
			command.setStartCustomer(xmlTag.isStartCustomer());
			command.setStartDataGroup(xmlTag.isStartDataGroup());
			command.setValueTag(xmlTag.isValueTag());

			if(xmlTag.getDataGroup() != null){
				command.setDataGroupId(xmlTag.getDataGroup().getId());
				command.setDataGroupName(xmlTag.getDataGroup().getName());
				command.setDataGroupLevel(xmlTag.getDataGroup().getLevel());
			} else {
				command.setDataGroupName("");
				command.setDataGroupId(-1);
				command.setDataGroupLevel(-1);
			}
		} else {
			//New Definition
			long parentTagId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
			command.setParentTagId(parentTagId);
			command.setDataGroupName("");
			command.setDataGroupId(-1);
			command.setDataGroupLevel(-1);
		}

    	return command;
    }

	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if( FORM_SUBMIT_TYPE_SUBMIT.equals(submitType) ){
			return true;
		}
		return false;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}

	private Command bind(HttpServletRequest request, Command command) {
		try {
			// Bind the submitType field.
			ServletRequestDataBinder binder = createBinder(request, command);

			request.getSession().setAttribute(XmlDataTagDefinitionEditController.PARAMETER_XML_DATA_TAG_DEFINITION_COMMAND, command);
			binder.bind(request);
		} catch (Exception e) {
			throw new RuntimeException("Could not bind the DataRecord command object!", e);
		}
		return command;
	}

	public String getFormViewRedirect() {
		return formViewRedirect;
	}

	public void setFormViewRedirect(String formViewRedirect) {
		this.formViewRedirect = formViewRedirect;
	}

	private static String getFullName(XmlDataTagDefinition tagDefinition, String fullName){
		if(fullName == null){
			fullName = tagDefinition.getName();
		} else {
			fullName = tagDefinition.getName() + "." + fullName;
		}
		if(tagDefinition.getParentTag() != null) {
			return getFullName(tagDefinition.getParentTag(), fullName);
		} else {
			return fullName;
		}
	}
	
	/* validation rules:
b.	Only children of parent tag which starts customer can be repeated, which means all global tags cannot be repeated.
c.	Starts Customer flag is unique for a data model (only one tag can start customer in the data model).

	 */
	@SuppressWarnings("unchecked")
	private boolean isValidXmlDataTag(BindException errors, XmlDataTagDefinition theTag){
		boolean isValid = true;
		//check if the start customer tag already exists in the data source.
		XmlDataTagDefinition startCustomertag = getStartCustomerTag(theTag);
		if(startCustomertag !=null && theTag.isStartCustomer()){
			if((theTag.getId() > 0 && theTag.getId() != startCustomertag.getId()) //existing tag
					|| theTag.getId() <=0) { // new tag		
			
				errors.reject("error.dataadmin.another.tag.start.customer", 
					new String[] {startCustomertag.getName(), theTag.getDataSource().getName()}, "");
				theTag.setStartCustomer(false);
				isValid = false;
			}
		} 

		if(theTag.isRepeating()){
		//check if the tag is a descendent of startCustomer tag.
			
			String hql = "from XmlDataTagDefinition xdt  where xdt.startCustomer = true and xdt.dataSource.id =:datasourceId";
			HashMap<String, Object> params = new HashMap<>();
	    	params.put("datasourceId", theTag.getDataSource().getId());
	    	List<XmlDataTagDefinition> startCustmerTag = (List<XmlDataTagDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
			if(startCustmerTag !=null && !startCustmerTag.isEmpty() &&
					!theTag.isDescendentOfThisTag(startCustmerTag.get(0))){
				errors.reject("error.dataadmin.not.descendent.start.customer");
				theTag.setRepeating(false);
				isValid = false;
			}
		}
		return isValid;
	}


	@SuppressWarnings("unchecked")
	private static XmlDataTagDefinition getStartCustomerTag(XmlDataTagDefinition aTag){
		String hql = "from XmlDataTagDefinition xdt  where xdt.startCustomer = true and xdt.dataSource.id = :datasourceId";
		HashMap<String, Object> params = new HashMap<>();
    	params.put("datasourceId", aTag.getDataSource().getId());
    	List<XmlDataTagDefinition> tags = (List<XmlDataTagDefinition>) HibernateUtil.getManager().getObjectsAdvanced(hql, params);
    	//There should be only one.
    	if(tags != null && !tags.isEmpty()){
    		return tags.get(0);
    	} else return null;
	}
	
	public static class Command implements Serializable{
		private static final long serialVersionUID = 5899503065858163196L;

		private long id;
		private long parentTagId;
		private long dataGroupId;
		private String name;	
		private String breakIndicator;
		private boolean startCustomer;
		private boolean repeating;
		private boolean enabled;
		private DataSource dataSource;
		private boolean startDataGroup;
		private boolean valueTag;
		private String dataGroupName;
		private int dataGroupLevel;
		public long getId() {
			return id;
		}
		public void setId(long id) {
			this.id = id;
		}
		
		public boolean isValueTag() {
			return valueTag;
		}
		public void setValueTag(boolean valueTag) {
			this.valueTag = valueTag;
		}
		public long getParentTagId() {
			return parentTagId;
		}
		public void setParentTagId(long parentTagId) {
			this.parentTagId = parentTagId;
		}
		
		public long getDataGroupId() {
			return dataGroupId;
		}
		public void setDataGroupId(long dataGroupId) {
			this.dataGroupId = dataGroupId;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		
		public String getBreakIndicator() {
			return breakIndicator;
		}
		public void setBreakIndicator(String breakIndicator) {
			this.breakIndicator = breakIndicator;
		}
		public boolean isStartCustomer() {
			return startCustomer;
		}
		public void setStartCustomer(boolean startCustomer) {
			this.startCustomer = startCustomer;
		}
		public boolean isRepeating() {
			return repeating;
		}
		public void setRepeating(boolean repeating) {
			this.repeating = repeating;
		}
		public boolean isEnabled() {
			return enabled;
		}
		public void setEnabled(boolean enabled) {
			this.enabled = enabled;
		}
		public DataSource getDataSource() {
			return dataSource;
		}
		public void setDataSource(DataSource dataSource) {
			this.dataSource = dataSource;
		}
		public boolean isStartDataGroup() {
			return startDataGroup;
		}
		public void setStartDataGroup(boolean startDataGroup) {
			this.startDataGroup = startDataGroup;
		}
		public String getDataGroupName() {
			return dataGroupName;
		}
		public void setDataGroupName(String dataGroupName) {
			this.dataGroupName = dataGroupName;
		}
		public int getDataGroupLevel() {
			return dataGroupLevel;
		}
		public void setDataGroupLevel(int dataGroupLevel) {
			this.dataGroupLevel = dataGroupLevel;
		}

		
	}
}
