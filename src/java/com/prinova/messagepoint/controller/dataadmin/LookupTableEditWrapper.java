package com.prinova.messagepoint.controller.dataadmin;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagepointModelNameComparator;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;

public class LookupTableEditWrapper implements Serializable{

	private static final long serialVersionUID = -5860024598879037859L;

	private LookupTableInstance 	lookupTableInstance;
	private LookupTable				lookupTable;
	private String 					dataFilePath;
	private Long 					dataFileSandboxFileId;
	private int						inputCharacterEncoding;
    private Set<Document> 			documents 							= new HashSet<>();
    private Set<TouchpointCollection> tpCollections 					= new HashSet<>();
    
	public LookupTableEditWrapper(){
		super();
		this.lookupTableInstance = new LookupTableInstance();
		this.lookupTable = new LookupTable();
	}
	
	public LookupTableEditWrapper(LookupTableInstance lookupTableInstance){
		this.lookupTableInstance = lookupTableInstance;
		this.lookupTable = lookupTableInstance.getModel();
		this.inputCharacterEncoding = lookupTableInstance.getInputCharacterEncoding();
		this.setDocuments(lookupTableInstance.getDocuments());
		this.setTpCollections(lookupTableInstance.getTouchpointCollections());
		
		if(lookupTableInstance.getLookupFile() != null)
			this.dataFilePath = String.valueOf(lookupTableInstance.getId()) + "|" + lookupTableInstance.getLookupFile().getId() + "|" + lookupTableInstance.getLookupFile().getFileName();
	}
	
	public LookupTableInstance getLookupTableInstance() {
		return lookupTableInstance;
	}
	public void setLookupTableInstance(LookupTableInstance lookupTableInstance) {
		this.lookupTableInstance = lookupTableInstance;
	}
	public LookupTable getLookupTable() {
		return lookupTable;
	}
	public void setLookupTable(LookupTable lookupTable) {
		this.lookupTable = lookupTable;
	}

	public Long getDataFileSandboxFileId() {
		return dataFileSandboxFileId;
	}

	public void setDataFileSandboxFileId(Long dataFileSandboxFileId) {
		this.dataFileSandboxFileId = dataFileSandboxFileId;
	}

	public String getDataFilePath() {
		return dataFilePath;
	}

	public void setDataFilePath(String dataFilePath) {
		this.dataFilePath = dataFilePath;
	}

	public int getInputCharacterEncoding() {
		return inputCharacterEncoding;
	}

	public void setInputCharacterEncoding(int inputCharacterEncoding) {
		this.inputCharacterEncoding = inputCharacterEncoding;
	}

	public Set<Document> getDocuments() {
		return documents;
	}

	public void setDocuments(Set<Document> documents) {
		this.documents = documents;
	}

	public Set<TouchpointCollection> getTpCollections() {
		return tpCollections;
	}

	public void setTpCollections(Set<TouchpointCollection> tpCollections) {
		this.tpCollections = tpCollections;
	}
	
	public String getSelectedTpAndCsNameStr(){
		List<Document> tps = new ArrayList<>(this.lookupTableInstance.getDocuments());
		Collections.sort(tps, new IdentifiableMessagepointModelNameComparator());
		List<TouchpointCollection> collections = new ArrayList<>(this.lookupTableInstance.getTouchpointCollections());
		Collections.sort(collections, new IdentifiableMessagepointModelNameComparator());
		StringBuilder nameStr = new StringBuilder();
		for(Document tp : tps){
			nameStr.append(tp.getName()).append(", ");
		}
		for(TouchpointCollection collection : collections){
			nameStr.append(collection.getName()).append(", ");
		}
		if(nameStr.length() > 0){
			nameStr = new StringBuilder(nameStr.toString().trim().substring(0, nameStr.length() - 2));
		}
		return nameStr.toString();
	}
}
