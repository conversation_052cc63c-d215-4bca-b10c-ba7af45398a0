package com.prinova.messagepoint.controller.dataadmin;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;

public class LookupTableListWrapper {

	private List<Long>						selectedIds;
	private String 							userNote;
	private User 							assignedToUser;
	private String							actionValue;
	private String 							cloneName;

	public LookupTableListWrapper() {
		super();
		this.selectedIds 		= new ArrayList<>();
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public String getCloneName() {
		return cloneName;
	}

	public void setCloneName(String cloneName) {
		this.cloneName = cloneName;
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public List<LookupTableInstance> getSelectedList(){
		List<LookupTableInstance> selectedList = new ArrayList<>();
		if (this.selectedIds != null)
		{
			for(Long selectedId : this.selectedIds){
				selectedList.add(HibernateUtil.getManager().getObject(LookupTableInstance.class, selectedId));
			}
		}
		return selectedList;
	}
}
