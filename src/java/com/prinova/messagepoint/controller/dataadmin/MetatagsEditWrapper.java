package com.prinova.messagepoint.controller.dataadmin;

import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.ArrayList;
import java.util.List;

public class MetatagsEditWrapper {

    private int			actionValue;

    private List<Long> removeTagIds = new ArrayList<>();
    private List<Long> renameTagIds = new ArrayList<>();
    private List<String> newTagsNames = new ArrayList<>();

    private RationalizerApplication     currentApplication;
    private String 				        cloneName;

    private String applicationName;
    private String description;
    private String metatags;

    public MetatagsEditWrapper( Long rationalizerApplicationId ) {
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        if ( rationalizerApplication != null ) {
            setCurrentApplication(rationalizerApplication);
            setCloneName(rationalizerApplication.getName() + " " + ApplicationUtil.getMessage("page.label.copy").toLowerCase());
        }
    }

    public int getActionValue() {
        return actionValue;
    }
    public void setActionValue(int actionValue) {
        this.actionValue = actionValue;
    }

    public List<Long> getRemoveTagIds() {
        return removeTagIds;
    }
    public void setRemoveTagIds(List<Long> removeTagIds) {
        this.removeTagIds = removeTagIds;
    }

    public List<Long> getRenameTagIds() {
        return renameTagIds;
    }
    public void setRenameTagIds(List<Long> renameTagIds) {
        this.renameTagIds = renameTagIds;
    }

    public List<String> getNewTagsNames() {
        return newTagsNames;
    }
    public void setNewTagsNames(List<String> newTagsNames) {
        this.newTagsNames = newTagsNames;
    }

    public RationalizerApplication getCurrentApplication() {
        return currentApplication;
    }
    public void setCurrentApplication(RationalizerApplication currentApplication) {
        this.currentApplication = currentApplication;
    }

    public String getCloneName() {
        return cloneName;
    }
    public void setCloneName(String cloneName) {
        this.cloneName = cloneName;
    }

    public String getApplicationName() {
        return applicationName;
    }
    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetatags() {
        return metatags;
    }
    public void setMetatags(String metatags) {
        this.metatags = metatags;
    }
}
