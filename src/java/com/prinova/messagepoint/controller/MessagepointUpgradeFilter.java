package com.prinova.messagepoint.controller;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import com.prinova.messagepoint.model.SystemPropertyManager;

public class MessagepointUpgradeFilter implements Filter {

	private Boolean upgradeCompleted;
	private String messagePage=null;
	
	public void destroy() { }

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		if(upgradeCompleted == null){
			upgradeCompleted = isSystemUpgradeDone();
		}
		
		if(!upgradeCompleted){
			request.getRequestDispatcher(messagePage).forward(request, response);
			
		} else {
			chain.doFilter(request, response);
		}
	}

	
	public boolean isSystemUpgradeDone(){
		String value = SystemPropertyManager.getInstance().getSystemProperty("upgrade_completed");
		
		if(value == null){
			return true;
		}
		
		return "true".equals(value.toLowerCase());
	}
	
	public void init(FilterConfig filterConfig) throws ServletException { 
		messagePage = filterConfig.getInitParameter("error-page");
	}


}
