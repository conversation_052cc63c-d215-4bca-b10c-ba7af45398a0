package com.prinova.messagepoint.controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.util.HibernateUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class AsyncContentPowerEditController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncLayoutController.class);

    public static final String REQ_PARM_ACTION 				= "action";
    public static final String REQ_PARM_OBJECT_STATUS		= "status";
    public static final String REQ_PARM_OBJECT_TYPES		= "obj_type";
    public static final String REQ_PARM_DOCUMENT_IDS		= "document_ids";
    public static final String REQ_PARM_CONTENT     		= "content";

    public static final String ACTION_GET_CONTENT			= "get_content";
    public static final String ACTION_GET_OBJECTS			= "get_objects";
    public static final String ACTION_COMPARE_CONTENT		= "compare_content";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);

            if ( action.equalsIgnoreCase(ACTION_GET_CONTENT) ) {
                out.write(getGetContentResponseJSON(request, response).toString().getBytes());
            } else if ( action.equalsIgnoreCase(ACTION_GET_OBJECTS) ) {
                out.write(getGetObjectsResponseJSON(request, response).toString().getBytes());
            } else if ( action.equalsIgnoreCase(ACTION_COMPARE_CONTENT) ) {
                out.write(getCompareContentResponseJSON(request, response).toString().getBytes());
            } else {
                JSONObject returnObj = new JSONObject();
                returnObj.put("error", true);
                returnObj.put("message", "Power edit: Invalid action type");
                out.write(returnObj.toString().getBytes());
            }

        } catch (JSONException e) {
            JSONObject returnObj = new JSONObject();
            returnObj.put("error", true);
            returnObj.put("message", e.getMessage());
            out.write(returnObj.toString().getBytes());
        }

        out.flush();

        return null;
    }

    private JSONObject getGetObjectsResponseJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        boolean debugMode = true;

        long startTime = 0;
        if (debugMode)
            startTime = new Date().getTime();


        String documentIds = ServletRequestUtils.getStringParameter(request, REQ_PARM_DOCUMENT_IDS, null);
        String objectTypes = ServletRequestUtils.getStringParameter(request, REQ_PARM_OBJECT_TYPES, null);

        JSONObject returnObj = new JSONObject();
        JsonArray contentJsonArray = new JsonArray();

        if (documentIds != null)
            documentIds = "(" + documentIds + ")";

        String selectQueryStr =
                " SELECT co.object_type AS object_type, co.document_id AS document_id, co.name AS name, tppgtn.name AS tpselectionname, " +
                        " co.id AS co_id, tppgtn.id AS tpselectionid "
                        + " FROM content_object co "
                        + "	LEFT OUTER JOIN touchpoint_selection tpsel ON co.tp_selection_id = tpsel.id "
                        + "	LEFT OUTER JOIN pg_tree_node tppgtn ON tpsel.pg_tree_node_id = tppgtn.id ";


        // DOCUMENTS
        if (documentIds != null) {
            selectQueryStr += "	LEFT OUTER JOIN content_object_document_map codm ON codm.content_object_id = co.id ";
        }

        selectQueryStr += " WHERE co.name IS NOT NULL ";

        // DOCUMENTS
        if (documentIds != null)
            selectQueryStr += " AND (codm.document_id IN " + documentIds + " OR  co.document_id IN " + documentIds + ") ";

        // 0 OBJECT_TYPE_MESSAGE			// 0000
        // 1 OBJECT_TYPE_LOCAL_SMART_TEXT	// 0001
        // 4 OBJECT_TYPE_GLOBAL_SMART_TEXT	// 0100
        if (objectTypes != null) {
            String[] objectTypesArray = objectTypes.split(",");
            for (int i = 0; i < objectTypesArray.length; i++)
                objectTypesArray[i] = "co.object_type = " + objectTypesArray[i];
            selectQueryStr += " AND (" + StringUtils.join(objectTypesArray, " OR ") + ") ";
        } else
            selectQueryStr += " AND (co.object_type = 0 OR (co.object_type & 5) != 0) ";

        NativeQuery selectQuery = HibernateUtil.getManager().getSession().createNativeQuery(selectQueryStr);
        List<Object> values = selectQuery.list();

        if (values != null && !values.isEmpty()) {

            try {

                Iterator<Object> iterator = values.iterator();
                while (iterator.hasNext()) {
                    Object rs = iterator.next();

                    JsonObject contentObject = new JsonObject();

                    // SELECT co.object_type AS object_type, co.document_id AS document_id, co.name AS name, tppgtn.name AS tpselectionname,
                    // co.id AS co_id, tppgtn.id AS tpselectionid
                    Object[] rsItems = (Object[]) rs;
                    int objectType = rsItems[0] != null ? (Integer) rsItems[0] : -1;
                    long documentId = rsItems[1] != null ? ((BigInteger) rsItems[1]).longValue() : -1;
                    String name = rsItems[2] != null ? ((String) rsItems[2]).toString() : null;
                    String tpselectionname = rsItems[3] != null ? ((String) rsItems[3]).toString() : null;
                    long contentObjectId = rsItems[4] != null ? ((BigInteger) rsItems[4]).longValue() : -1;
                    long tpselectionId = rsItems[5] != null ? ((BigInteger) rsItems[5]).longValue() : -1;

                    contentObject.addProperty("content", "null");

                    contentObject.addProperty("obj_id", contentObjectId);
                    contentObject.addProperty("c", -1);
                    contentObject.addProperty("co", contentObjectId);
                    contentObject.addProperty("lc", -1);
                    contentObject.addProperty("s", -1);
                    contentObject.addProperty("ot", objectType);
                    contentObject.addProperty("d", documentId);
                    contentObject.addProperty("name", name);
                    contentObject.addProperty("sel", tpselectionname);
                    contentObject.addProperty("selid", tpselectionId);
                    contentObject.addProperty("coa",-1);

                    contentJsonArray.add(contentObject);

                }

            } catch (Exception e) {
                returnObj.put("error", true);
                returnObj.put("message", e.getMessage());
            }

        }

        returnObj.put("object_array", contentJsonArray);

        if (debugMode)
            log.error("Power edit: Objects: " + (new Date().getTime() - startTime) + "ms");

        return returnObj;
    }

    private JSONObject getGetContentResponseJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {

        boolean debugMode = true;

        long startTime = 0;
        if (debugMode)
            startTime = new Date().getTime();


        String documentIds = ServletRequestUtils.getStringParameter(request, REQ_PARM_DOCUMENT_IDS, null);
        String objectTypes = ServletRequestUtils.getStringParameter(request, REQ_PARM_OBJECT_TYPES, null);
        String status = ServletRequestUtils.getStringParameter(request, REQ_PARM_OBJECT_STATUS, null);

        JSONObject returnObj = new JSONObject();
        JsonArray contentJsonArray = new JsonArray();

        if (documentIds != null)
            documentIds = "(" + documentIds + ")";

        String selectQueryStr =
                " SELECT c.id AS content_id, coa.messagepoint_locale_id AS locale_code_id, coa.data_type AS status_id, c.text_content AS encoded_content, " +
                        " co.object_type AS object_type, co.document_id AS document_id, co.name AS name, tppgtn.name AS tpselectionname, " +
                        " copgtn.name AS coselectionname, coa.id AS coa_id, co.id AS co_id, tppgtn.id AS tpselectionid, copgtn.id AS coselectionid "
                        + " FROM content c "
                        + " INNER JOIN content_object_association coa ON c.id = coa.content_id "
                        + " INNER JOIN content_object co ON co.id = coa.content_object_id "
                        + "	LEFT OUTER JOIN pg_tree_node tppgtn ON coa.tp_pg_tn_id = tppgtn.id "
                        + "	LEFT OUTER JOIN pg_tree_node copgtn ON coa.co_pg_tn_id = copgtn.id ";

        // DOCUMENTS
        if (documentIds != null) {
            selectQueryStr += "	LEFT OUTER JOIN content_object_document_map codm ON codm.content_object_id = co.id ";
        }

        selectQueryStr += " WHERE c.text_content IS NOT NULL AND coa.type_id = 2 ";

        // DOCUMENTS
        if (documentIds != null)
            selectQueryStr += " AND (codm.document_id IN " + documentIds + " OR  co.document_id IN " + documentIds + ") ";

        // 0 OBJECT_TYPE_MESSAGE			// 0000
        // 1 OBJECT_TYPE_LOCAL_SMART_TEXT	// 0001
        // 4 OBJECT_TYPE_GLOBAL_SMART_TEXT	// 0100
        if (objectTypes != null) {
            String[] objectTypesArray = objectTypes.split(",");
            for (int i = 0; i < objectTypesArray.length; i++)
                objectTypesArray[i] = "co.object_type = " + objectTypesArray[i];
            selectQueryStr += " AND (" + StringUtils.join(objectTypesArray, " OR ") + ") ";
        } else
            selectQueryStr += " AND (co.object_type = 0 OR (co.object_type & 5) != 0) ";

        // DATA TYPE
        // 1 working
        // 2 active
        // 3 working, active
        // 7 working, active, archive
        if (status != null)
            selectQueryStr += " AND (coa.data_type & " + status + ") != 0 ";

        NativeQuery selectQuery = HibernateUtil.getManager().getSession().createNativeQuery(selectQueryStr);
        List<Object> values = selectQuery.list();

        if (values != null && !values.isEmpty()) {

            try {

                Iterator<Object> iterator = values.iterator();
                while (iterator.hasNext()) {
                    Object rs = iterator.next();

                    JsonObject contentObject = new JsonObject();

                    // SELECT c.id AS content_id, coa.messagepoint_locale_id AS locale_code_id, coa.data_type AS status_id, c.text_content AS encoded_content,
                    // co.object_type AS object_type, co.document_id AS document_id, co.name AS name, tppgtn.name AS tpselectionname,
                    // copgtn.name AS coselectionname, coa.id AS coa_id "
                    Object[] rsItems = (Object[]) rs;
                    long contentId = rsItems[0] != null ? ((BigInteger) rsItems[0]).longValue() : -1;
                    long localeCodeId = rsItems[1] != null ? ((BigInteger) rsItems[1]).longValue() : -1;
                    int statusId = rsItems[2] != null ? (Integer) rsItems[2] : -1;
                    String textContent = rsItems[3] != null ? ((String) rsItems[3]) : null;
                    int objectType = rsItems[4] != null ? (Integer) rsItems[4] : -1;
                    long documentId = rsItems[5] != null ? ((BigInteger) rsItems[5]).longValue() : -1;
                    String name = rsItems[6] != null ? ((String) rsItems[6]).toString() : null;
                    String tpselectionname = rsItems[7] != null ? ((String) rsItems[7]).toString() : null;
                    String coselectionname = rsItems[8] != null ? ((String) rsItems[8]).toString() : null;
                    long contentObjectAssociationId = rsItems[9] != null ? ((BigInteger) rsItems[9]).longValue() : -1;
                    long contentObjectId = rsItems[10] != null ? ((BigInteger) rsItems[10]).longValue() : -1;
                    long tpselectionId = rsItems[11] != null ? ((BigInteger) rsItems[11]).longValue() : -1;
                    long coselectionId = rsItems[12] != null ? ((BigInteger) rsItems[12]).longValue() : -1;

                    contentObject.addProperty("content", StringEscapeUtils.escapeXml(textContent));

                    contentObject.addProperty("obj_id", contentId);
                    contentObject.addProperty("c", contentId);
                    contentObject.addProperty("co", contentObjectId);
                    contentObject.addProperty("lc", localeCodeId);
                    contentObject.addProperty("s", statusId);
                    contentObject.addProperty("ot", objectType);
                    contentObject.addProperty("d", documentId);
                    contentObject.addProperty("name", name);
                    contentObject.addProperty("sel", coselectionname != null ? coselectionname : tpselectionname);
                    contentObject.addProperty("selid", coselectionId > 0 ? coselectionId : tpselectionId);
                    contentObject.addProperty("coa", contentObjectAssociationId);

                    contentJsonArray.add(contentObject);

                }

            } catch (Exception e) {
                returnObj.put("error", true);
                returnObj.put("message", e.getMessage());
            }

        }

        returnObj.put("object_array", contentJsonArray);

        if (debugMode)
            log.error("Content power edit: Contents: " + (new Date().getTime() - startTime));

        return returnObj;
    }

    private JSONObject getCompareContentResponseJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {

        JSONObject returnObj = new JSONObject();

        String content = ServletRequestUtils.getStringParameter(request, REQ_PARM_CONTENT, null);
        JSONObject contentJson = new JSONObject(content);
        String match = contentJson.getString("match");
        String alter = contentJson.getString("alter");

        try {
            if ( match != null && alter != null ) {
                String result = StringsDiffUtils.diff(match, alter);
                returnObj.put("compared_content", result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnObj;
    }

}
