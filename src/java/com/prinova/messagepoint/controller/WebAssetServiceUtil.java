package com.prinova.messagepoint.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.model.SystemPropertyManager;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.util.LogUtil;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.ServletContext;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static com.prinova.messagepoint.model.SystemPropertyKeys.ContentDeliveryNetwork.KEY_ContentDeliveryNetworkEnabled;
import static com.prinova.messagepoint.model.SystemPropertyKeys.ContentDeliveryNetwork.KEY_WebAssetsServiceUrl;

// Utility class contains static operations to work with Web Asset Service (external rest service).
public class WebAssetServiceUtil {

    private static final Log log = LogUtil.getLog(WebAssetServiceUtil.class);

    // Primary Assets: javascript, stylesheet
    private static ConcurrentHashMap<String, String> cdnAssets = new ConcurrentHashMap<>();
    // Secondary Assets (assets referenced from primary assets): images, fonts
    private static ConcurrentHashMap<String, String> cdnSecondaryAssets = new ConcurrentHashMap<>();

    private static boolean isSecondaryAssetsUploaded = false;


    private WebAssetServiceUtil() {
        // Default constructor is private because this class should not be instantiated.
    }


    public static void init() {
        if (WebAssetServiceUtil.isContentDeliveryNetworkDisabled()) {
            return;
        }

        isSecondaryAssetsUploaded = false;

        CompletableFuture
                .runAsync(WebAssetServiceUtil::initSecondaryCdnAssetsList)
                .thenRun(WebAssetServiceUtil::uploadSecondaryAssetsToCdn)
                .thenRun(WebAssetServiceUtil::verifySecondaryAssetsUploaded)
                .thenRun(WebAssetServiceUtil::initPrimaryCdnAssetsList);
    }

    public static String getCdnAssetUrl(final String assetId) {
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        boolean isCdnEnabled = manager.getPodMasterBooleanSystemProperty(KEY_ContentDeliveryNetworkEnabled, false);
        if (isCdnEnabled && isSecondaryAssetsUploaded) {
            return cdnAssets.get(assetId);
        } else {
            return null;
        }
    }

    private static void verifySecondaryAssetsUploaded() {
        Set<String> candidates = getSecondaryAssetCandidates();
        Set<String> missingAssets = new HashSet<>();

        for (String candidate : candidates) {
            String assetId = getAssetIdFromPath(candidate);
            if (!cdnSecondaryAssets.containsKey(assetId)) {
                missingAssets.add(assetId);
            }
        }

        if (missingAssets.isEmpty()) {
            isSecondaryAssetsUploaded = true;
        } else {
            isSecondaryAssetsUploaded = false;
            for (String assetId : missingAssets) {
                log.warn("Secondary Web asset " + assetId + " is missing in Content Delivery Network.");
            }
        }
    }

    static void uploadPrimaryAssetToCdn(final String assetId, final WebAssetContentType contentType, final byte[] content) {
        if (isContentDeliveryNetworkDisabled()) {
            return;
        }

        String assetCdnKey = uploadAssetToCdn(assetId, contentType, content);
        if (assetCdnKey != null) {
            cdnAssets.put(assetId, assetCdnKey);
        }
    }

    // Returns key of asset (asset path) in CDN
    private static String uploadAssetToCdn(final String assetId, final WebAssetContentType contentType, final byte[] content) {
        String version = MessagePointStartUp.getMajorBuildRevision();
        String urlString = getWebAssetsServiceUrl() + "/asset/put";
        String result = null;

        try {
            JSONObject asset = new JSONObject();
            asset.put("assetId", assetId);
            asset.put("contentType", contentType != null ? contentType.getValue() : null);
            asset.put("content", content);
            asset.put("version", version);

            URL url = new URL(urlString);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("PUT");
            urlConnection.setRequestProperty("Content-Type", "application/json; utf-8");
            urlConnection.setRequestProperty("Accept", "application/json");
            urlConnection.setDoOutput(true);

            try (OutputStream os = urlConnection.getOutputStream()) {
                byte[] input = asset.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            if (urlConnection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                StringBuilder response = new StringBuilder();
                try (
                        BufferedReader br = new BufferedReader(
                                new InputStreamReader(urlConnection.getInputStream(), StandardCharsets.UTF_8)
                        )
                ) {
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                }

                result = response.toString();

                log.info("Web asset " + result + " was successfully uploaded to Content Delivery Network.");
            }

            urlConnection.disconnect();
        }
        catch (IOException | JSONException e) {
            log.error(e);
        }

        return result;
    }

    private static void uploadSecondaryAssetsToCdn() {
        ServletContext servletContext = MessagePointStartUp.getServletContext();
        Set<String> candidates = getSecondaryAssetCandidates();

        log.info("Secondary asset candidates list size: " + candidates.size());

        for (String candidate : candidates) {
            String assetId = getAssetIdFromPath(candidate);
            if (cdnSecondaryAssets.containsKey(assetId)) {
                continue;
            }

            try (InputStream inputStream = servletContext.getResourceAsStream("/" + candidate)) {
                byte[] resourceContent = IOUtils.toByteArray(inputStream);
                String assetCdnKey = uploadAssetToCdn(candidate, null, resourceContent);
                if (assetCdnKey != null) {
                    cdnSecondaryAssets.put(assetId, assetCdnKey);
                }
            }
            catch (IOException e) {
                log.error(e);
            }
        }
    }

    private static Set<String> getSecondaryAssetCandidates() {
        Set<String> result = new HashSet<>();
        ServletContext servletContext = MessagePointStartUp.getServletContext();

        try (InputStream inputStream = servletContext.getResourceAsStream("/dynamiccontent/pkg/resourceAssetMap.json")) {
            if (inputStream == null) {
                return Collections.emptySet();
            }

            JSONArray resources = new JSONArray(IOUtils.toString(inputStream, StandardCharsets.UTF_8));

            for (int i = 0; i < resources.length(); i++) {
                JSONObject resource = resources.getJSONObject(i);
                String dynamicPath = resource.getString("dynamicPath");
                result.add(dynamicPath);
            }
        }
        catch (IOException | JSONException e) {
            log.error(e);
        }

        return result;
    }

    private static void initSecondaryCdnAssetsList() {
        String version = MessagePointStartUp.getMajorBuildRevision();

        cdnSecondaryAssets.clear();
        cdnSecondaryAssets.putAll(getCdnAssetsList(version + "/dynamiccontent/pkg/res"));

        log.info("List of secondary CDN assets initiated, contains " + cdnSecondaryAssets.size() + " items.");
    }

    private static void initPrimaryCdnAssetsList() {
        String version = MessagePointStartUp.getMajorBuildRevision();

        cdnAssets.clear();
        cdnAssets.putAll(getCdnAssetsList(version + "/" + WebAssetContentType.JAVASCRIPT.getValue()));
        cdnAssets.putAll(getCdnAssetsList(version + "/" + WebAssetContentType.STYLESHEET.getValue()));

        log.info("List of primary CDN assets initiated, contains " + cdnAssets.size() + " items.");
    }

    private static Map<String, String> getCdnAssetsList(final String prefix) {
        Map<String, String> result = new HashMap<>();
        String webAssetsServiceUrl = getWebAssetsServiceUrl();

        String url = webAssetsServiceUrl + "/asset/list";
        if (prefix != null && !prefix.isEmpty()) {
            url = url + "?prefix=" + prefix;
        }

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        HttpStatus httpStatus = responseEntity.getStatusCode();
        if (httpStatus == HttpStatus.OK) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode root = mapper.readTree(responseEntity.getBody());
                for (JsonNode item : root) {
                    String path = item.asText();
                    String id = getAssetIdFromPath(path);
                    result.put(id, path);
                }
            } catch (IOException e) {
                log.error(e);
            }
        }

        return result;
    }

    private static boolean isContentDeliveryNetworkDisabled() {
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        return !manager.getPodMasterBooleanSystemProperty(KEY_ContentDeliveryNetworkEnabled, false);
    }

    private static String getWebAssetsServiceUrl() {
        SystemPropertyManager manager = SystemPropertyManager.getInstance();
        return manager.getPodMasterSystemProperty(KEY_WebAssetsServiceUrl);
    }

    private static String getAssetIdFromPath(final String assetPath) {
        if (assetPath == null) {
            return null;
        }

        String assetId = null;

        try {
            int beginIndex = assetPath.lastIndexOf('/') + 1;
            int endIndex = assetPath.lastIndexOf('.');

            if (endIndex > beginIndex) {
                assetId = assetPath.substring(beginIndex, endIndex);
            } else {
                assetId = assetPath.substring(beginIndex);
            }
        }
        catch (IndexOutOfBoundsException e) {
            log.error(e);
        }

        return assetId;
    }
}
