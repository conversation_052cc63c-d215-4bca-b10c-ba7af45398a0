package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.navigation.WorkflowTab;
import com.prinova.messagepoint.model.workflow.Workflow;
import com.prinova.messagepoint.model.workflow.WorkflowPosition;
import com.prinova.messagepoint.tag.view.WorkflowTabTag;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;

public abstract class StatelessFlowController<T extends IdentifiableMessagePointModel, V> extends ConfirmationCommandController<V> {
	public static final String REQ_PARAM_STATUS_VIEW_ID = "statusViewId";
	public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID = "touchpointSelectionId";		

	
	protected Class<?> persistedClass;
	private String persistedClassName;
	private String parameter;

	@Override
	protected Object formBackingObject(HttpServletRequest request) {
		Object command = super.formBackingObject(request);

		if (command instanceof IdentifiableMessagePointModel && command.getClass().equals(persistedClass)) {
			IdentifiableMessagePointModel model = (IdentifiableMessagePointModel) command;
			String parameterValue = request.getParameter(getParameter());
			if (parameterValue != null && !parameterValue.isEmpty()) {
				long id = Long.valueOf(parameterValue).longValue();
				if (model.getId() != id)
					command = HibernateUtil.getManager().getObject(persistedClass, id);
			} else {
				command = formBackingObjectInternal(request);
			}
			if (command instanceof ContentObject) {
				ContentObject contentObject = (ContentObject) command;
				boolean isMessageInProduction = false;
				try {
					isMessageInProduction = contentObject.hasActiveData();
				} catch (Exception e) {
				}
				contentObject.setConfirmationNeeded(isMessageInProduction);
			}
		}
		
		return command;
	}

	@SuppressWarnings("unchecked")
	@Override
	protected V formBackingObjectInternal(HttpServletRequest request) {
		return (V) getBackingObject(request);
	}

	@SuppressWarnings("unchecked")
	protected T getBackingObject(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, getParameter(), -1);
		T object = (T) HibernateUtil.getManager().getObject(persistedClass, id);
		return object;
	}

	@Override
	protected ModelAndView handleConfirm(HttpServletRequest request, HttpServletResponse response, V command, BindException errors) throws Exception {
		String submitType = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_TYPE_PARAMETER, "");
		if (!WorkflowTabTag.SUBMIT_TYPE_CANCEL.equals(submitType)) {
			doSave(request, command, errors);
			if (errors.hasErrors()) {
				return showForm(request, response, errors);
			}
		}
		return new ModelAndView(new RedirectView(getSuccessView(request, command)), getParameter(), request.getParameter(getParameter()));
	}

	@Override
	protected ModelAndView handleDiscard(HttpServletRequest request, V command) {
		Workflow workflow = Workflow.getWorkflow(persistedClass);
		WorkflowPosition current = workflow.getSelected(((HttpServletRequest) request).getRequestURL().toString());
		if (current == null) {
			String requestURL = ((HttpServletRequest) request).getRequestURL().toString();
			requestURL = requestURL.replace(getConfirmationView(), getFormView());
			// current is null because we are on a confirmation view.
			current = workflow.getSelected(requestURL);
		}
		return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + current.getTab().getEditUrl()), getParameter(), request.getParameter(getParameter()));
	}

	@Override
	protected boolean needConfirmation(HttpServletRequest request, V command) {
		// Override this method if you need confirmation support.
		return false;
	}

	public abstract void doSave(HttpServletRequest request, V command, BindException errors) throws Exception;

	public abstract T getObject(V command);

	protected String getSuccessView(HttpServletRequest request, V command) {
		Workflow workflow = Workflow.getWorkflow(persistedClass);
		WorkflowPosition current = workflow.getSelected(((HttpServletRequest) request).getRequestURL().toString());

		if (current == null) {
			// current is null because we are on a confirmation view.
			String requestURL = ((HttpServletRequest) request).getRequestURL().toString();
			requestURL = requestURL.replace(getConfirmationView(), getFormView());
			current = workflow.getSelected(requestURL);
		}
		String submitType = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_TYPE_PARAMETER, "");
		String flowParameter = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_FLOW_PARAMETER, "");
		if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_BACK.equals(submitType)) {
			WorkflowTab workflowTab = workflow.getPrevWorkflowPositionWithTab(current).getTab();
			return ApplicationUtil.getWebRoot()+ workflowTab.getEditUrl() +
					(flowParameter.isEmpty() ? "" : "?"+WorkflowTabTag.SUBMIT_FLOW_PARAMETER+"="+flowParameter );
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE.equals(submitType)) {
			if (flowParameter.equals("newTpContSelMsg"))
				return  ApplicationUtil.getWebRoot() + "tpadmin/touchpoint_content_selections_list.form?" + 
						HttpRequestUtil.getBackToListURL(request,HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_TP_CONTENT_SELECTION);
			else
				return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_PREVIEW_VIEW.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_PREVIEW_EDIT.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getEditUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_CANCEL.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_FORWARD.equals(submitType)) {
			WorkflowTab workflowTab = workflow.getNextWorkflowPositionWithTab(current).getTab();
			return ApplicationUtil.getWebRoot() + workflowTab.getEditUrl() +
					(flowParameter.isEmpty() ? "" : "?"+WorkflowTabTag.SUBMIT_FLOW_PARAMETER+"="+flowParameter );
		}

		return getSuccessView() == null ? "" : getSuccessView();
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}

	public String getPersistedClassName() {
		return persistedClassName;
	}

	public void setPersistedClassName(String persistedClassName) {
		this.persistedClassName = persistedClassName;
		try {
			this.persistedClass = Class.forName(persistedClassName);
		} catch (ClassNotFoundException e) {
			throw new RuntimeException("CLass not found in FlowController setPersistedClassName. Must be a Configuration error.",
					e);
		}
	}
}
