package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.touchpoints.SelectionStatusFilterType;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.message.content.SelectableContentUtil;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.tpselection.CreateTouchpointVariantProofService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.FeatureFlag;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.jstree.TouchpointSelectionWrapper;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TouchpointContentSelectionViewController extends MessagepointController {

	// private static final Log log = LogUtil.getLog(TouchpointContentSelectionViewController.class);

	public static final String REQ_PARAM_CONTENT_SELECTION_ID_PARAM = "contentSelectionId";
	public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID_PARAM = "touchpointSelectionId";
	public static final String REQ_PARM_FAST_VIEW = "fastView";
	public static final String REQ_PARM_ACTION = "action";
	public static final String REQ_PARAM_LOCALE_ID = "localeId";
	public static final String REQ_PARAM_VIEW_ID = "statusViewId";

	public static final int ACTION_PROOF_VARIANT = 2;
	public static final int ACTION_SEARCH = 10;
	public static final int ACTION_CANCEL = 99;

	public static final int VIEW_WORKING_COPY = 1;
	public static final int VIEW_ACTIVE = 2;

	private String editView;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Document document;
		TouchpointSelection touchpointSelection;
		ContentObject contentObject;
		Map<String, Object> referenceData = new HashMap<>();
		long zoneId;

		long statusViewId = getStatusViewIdParam(request);
		boolean isStatusViewIdActive = statusViewId == SelectionStatusFilterType.ID_ACTIVE;
		TouchpointContentObjectContentSelection contentSelection = getTouchpointContentObjectContentSelection(request);
		document = contentSelection.getTouchpointSelection().getDocument();
		touchpointSelection = contentSelection.getTouchpointSelection();

		contentObject = contentSelection.getContentObject();
		contentObject.checkAndAdjustFocus();

		zoneId = contentSelection.getContentObject().getZone() != null ? contentSelection.getContentObject().getZone().getId() : -1;
		referenceData.put("contentSelection", contentSelection);

		referenceData.put("isEnabledForVariantWorkflow", document.isEnabledForVariantWorkflow());

		List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
		referenceData.put("locales", langLocales);
		referenceData.put("languagesJSON", MessagepointLocale.getAsJSON(langLocales));
		
		Document touchpointContent = UserUtil.getCurrentTouchpointContext();
		referenceData.put("defaultLocaleId", touchpointContent.getDefaultTouchpointLanguageLocale().getId());
		referenceData.put("defaultLanguage", touchpointContent.getDefaultTouchpointLanguage().getMessagepointLocale().getName());
		referenceData.put("focusLocaleId", UserUtil.getCurrentLanguageLocaleContext().getId());
		
		referenceData.put("document", document);
		referenceData.put("zoneId", zoneId > 0 ? zoneId : null);
		referenceData.put("backToListURL", getBackToListURL(request));

		referenceData.put("touchpointSelection", touchpointSelection);
		referenceData.put("isFastView", ServletRequestUtils.getBooleanParameter(request, REQ_PARM_FAST_VIEW, false));

		TouchpointSelectionWrapper masterTouchpointSelectionWrapper = new TouchpointSelectionWrapper(document.getMasterTouchpointSelection(), contentObject.getOwningTouchpointSelection(), contentObject.getId(), isStatusViewIdActive);
		referenceData.put("masterTouchpointSelectionWrapper", masterTouchpointSelectionWrapper);

		referenceData.put("isMaster", touchpointSelection.isMaster());

		referenceData.put("searchParameters", document.getSelectionParameterGroup().getParameterGroupItemsSorted());

		if (contentSelection.getContentAssociationType().getId() == ContentAssociationType.ID_EMPTY)
			referenceData.put("contentType", new ContentAssociationType(ContentAssociationType.ID_OWNS));
		else
			referenceData.put("contentType", contentSelection.getContentAssociationType());

		referenceData.put("contentType_CUSTOM",ContentAssociationType.ID_OWNS);
		referenceData.put("contentType_SUPPRESSES",ContentAssociationType.ID_SUPPRESSES);
		referenceData.put("contentType_SAME_AS",ContentAssociationType.ID_REFERENCES);
		
		Map<Long, List<Proof>> proofs = new HashMap<>();
		referenceData.put("proofs", proofs);
		Map<Long, List<Proof>> activeProofs = new HashMap<>();
		referenceData.put("activeProofs", activeProofs);		
		
		for (MessagepointLocale langLocale : langLocales) {
			if((!document.isEnabledForVariantWorkflow() && !contentObject.isFocusOnActiveData()) || !isStatusViewIdActive) {
				// Non active proofs
				List<Proof> langProofs = Proof.findAllNoneActiveProofsForLang(touchpointSelection, langLocale.getLanguageCode(), document.getIsOmniChannel());
				List<Proof> twoContentLangProofs = new ArrayList<>();
				List<Proof> twoVariantLangProofs = new ArrayList<>();
				for (Proof proof : langProofs) {
					if (proof.getType() == Proof.PROOF_TYPE_VARIANT) {
						if (twoVariantLangProofs.size() < 2) {
							twoVariantLangProofs.add(proof);
						}
					} else {
						if (proof.getContentObject().getId() == contentObject.getId()
								&& twoContentLangProofs.size() < 2) {
							twoContentLangProofs.add(proof);
						}
					}
				}
				proofs.put(langLocale.getId(), twoContentLangProofs);
				proofs.get(langLocale.getId()).addAll(twoVariantLangProofs);
			}

			if((!document.isEnabledForVariantWorkflow() && contentObject.isFocusOnActiveData()) || isStatusViewIdActive) {
				// Active proofs
				List<Proof> activeLangProofs = Proof.findActiveProofsForLang(touchpointSelection, langLocale.getLanguageCode(), document.getIsOmniChannel());
				List<Proof> contentActiveLangProofs = new ArrayList<>();
				List<Proof> variantActiveLangProofs = new ArrayList<>();
				for (Proof proof : activeLangProofs) {
					if (proof.getType() == Proof.PROOF_TYPE_VARIANT) {
						if (variantActiveLangProofs.isEmpty()) {
							variantActiveLangProofs.add(proof);
						}
					} else {
						if (proof.getContentObject().getId() == contentObject.getId()
								&& contentActiveLangProofs.isEmpty()) {
							contentActiveLangProofs.add(proof);
						}
					}
				}
				activeProofs.put(langLocale.getId(), contentActiveLangProofs);
				activeProofs.get(langLocale.getId()).addAll(variantActiveLangProofs);
			}
		}

		referenceData.put("canProofForLanguage", touchpointSelection.getCanProofForLanguage(langLocales));

		if (document.isEnabledForVariantWorkflow())
			referenceData.put("isStatusViewActive", isStatusViewIdActive);
		else
			referenceData.put("isStatusViewActive", contentObject.isFocusOnActiveData());
		
		TouchpointSelection currentSelection = touchpointSelection;
		List<TouchpointSelection> selectionHierarchyList = new ArrayList<>();
		selectionHierarchyList.add(currentSelection);
		
		while (currentSelection.getParent() != null) {
			currentSelection = currentSelection.getParent();
			selectionHierarchyList.add(currentSelection);
		}
		Collections.reverse(selectionHierarchyList);
		referenceData.put("selectionHierarchy", selectionHierarchyList);
		
		String assignedTo = null;
		if (document.isEnabledForVariantWorkflow() && touchpointSelection != null && !touchpointSelection.isMaster() &&
			( contentObject.getOwningTouchpointSelection() == null || contentObject.getOwningTouchpointSelection().getId() != touchpointSelection.getId()) ) {
			assignedTo = touchpointSelection.getAssignedToUserName();
		} else {
			assignedTo = contentObject.getAssignedToUserName();
		}
		referenceData.put("assignedTo", assignedTo);
		
		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );
		
		if (contentObject.isVariantType())
			referenceData.put("isOwningTouchpointSelection", contentObject.getOwningTouchpointSelection().getId() == currentSelection.getId());
		referenceData.put("isOmniChannel", document != null && document.getIsOmniChannel());
		referenceData.put("isContentObjectTimeStampEnable", FeatureFlag.isEnabled(FeatureFlag.Features.AdditionalContentObjectTimeStamp, request));

		// TODO
		// referenceData.put("lastWorkflowAction", ConfigurableWorkflowActionHistory.findLatestHistoryByModel(contentObject));


		boolean canEditInTranslation = false;
		if(document.isEnabledForVariantWorkflow()){
			ConfigurableWorkflowAction wfAction = touchpointSelection.getWorkflowAction();
			boolean isUserInApprovalList	= wfAction != null && wfAction.getActionApprovers().stream().anyMatch(u->u.getId()==UserUtil.getPrincipalUserId());
			canEditInTranslation = touchpointSelection.isInTranslationStep() && touchpointSelection.hasDirtyContentForDefaultLanguage(contentObject) && isUserInApprovalList;
		}else{
			ConfigurableWorkflowAction wfAction = contentObject.getWorkflowAction();
			List<User> currentTranslators	= contentObject.getCurrentTranslators();
			boolean isCurrentTranslator		= currentTranslators.stream().anyMatch(ct->ct.getId()==UserUtil.getPrincipalUserId());
			canEditInTranslation = contentObject.isInTranslationStep() && isCurrentTranslator;
		}
		referenceData.put("canEditInTranslation", canEditInTranslation);

		referenceData.put("contentObject", contentObject);
		List<String> includedTargetGroupDetailsList = new ArrayList<>();
		for (TargetGroup currentTargetGroup : contentObject.getIncludedTargetGroups()) {
			if (currentTargetGroup != null)
				includedTargetGroupDetailsList.add(currentTargetGroup.getDetails(contentObject.getContentObjectData()));
		}
		List<String> extendedTargetGroupDetailsList = new ArrayList<>();
		for (TargetGroup currentTargetGroup : contentObject.getExtendedTargetGroups()) {
			if (currentTargetGroup != null)
				extendedTargetGroupDetailsList.add(currentTargetGroup.getDetails(contentObject.getContentObjectData()));
		}
		List<String> excludedTargetGroupDetailsList = new ArrayList<>();
		for (TargetGroup currentTargetGroup : contentObject.getExcludedTargetGroups()) {
			if (currentTargetGroup != null)
				excludedTargetGroupDetailsList.add(currentTargetGroup.getDetails(contentObject.getContentObjectData()));
		}
		referenceData.put("includedTargetGroupDetailsList", includedTargetGroupDetailsList);
		referenceData.put("extendedTargetGroupDetailsList", extendedTargetGroupDetailsList);
		referenceData.put("excludedTargetGroupDetailsList", excludedTargetGroupDetailsList);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(Zone.class, new IdCustomEditor<>(Zone.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected TouchpointContentSelectionViewWrapper formBackingObject(HttpServletRequest request) throws Exception {
		User requestor = UserUtil.getPrincipalUser();
		
		TouchpointContentObjectContentSelection contentSelection = getTouchpointContentObjectContentSelection(request);
		TouchpointContentSelectionViewWrapper wrapper = new TouchpointContentSelectionViewWrapper(contentSelection, requestor);
		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		long contentSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_SELECTION_ID_PARAM, -1);		
		int viewid = getStatusViewIdParam(request);
		
		TouchpointSelection touchpointSelection;

		if (contentSelectionId != -1) {
			TouchpointContentObjectContentSelection contentSelection = getTouchpointContentObjectContentSelection(request);
			touchpointSelection = contentSelection.getTouchpointSelection();
		} else {
			long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID_PARAM, -1);
			touchpointSelection = TouchpointSelection.findById(touchpointSelectionId);
		}


		try {
			// Selection Context: Update to match current context
			if ( touchpointSelection != null ) {
				TouchpointSelection persistedContextSelection = UserUtil.getCurrentSelectionContext();
				
				HashMap<String,String> contextAttr = new HashMap<>();
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(touchpointSelection.getId()));
				UserUtil.updateUserContextAttributes(contextAttr);

				// Update selection context and reload
				if ( touchpointSelection != persistedContextSelection ) 
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, contentSelectionId, viewid));

			}
		} catch (Exception e) {
			logger.error(e);
		}
		
		// Modify back to list URL to match latest touchpoint selection made from nav tree
		String backToListURL = getBackToListURL(request);
		String modifiedBackToListURL = HttpRequestUtil.removeParameterFromQueryString(backToListURL, TouchpointContentObjectListController.REQ_PARM_SELECTION_ID);
		modifiedBackToListURL += "&" + TouchpointContentObjectListController.REQ_PARM_SELECTION_ID + "=" + touchpointSelection.getId();
		HttpRequestUtil.saveBackToListURL(request, modifiedBackToListURL, HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_TP_CONTENT_SELECTION);

		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.TouchpointContentSelectionView);

		try {
			TouchpointContentSelectionViewWrapper wrapper = (TouchpointContentSelectionViewWrapper) commandObj;
			int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
			long contentSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_SELECTION_ID_PARAM, -1);
			long principalUserId = UserUtil.getPrincipalUserId();
			int viewid = getStatusViewIdParam(request);

			ContentObject contentObject = wrapper.getContentSelection().getContentObject();
			switch (action) {
			case (ACTION_CANCEL): {
				/**
				 * ACTION_CANCEL: - redirects the page to the origin source.
				 */
				analyticsEvent.setAction(Actions.Cancel);

				return  getOriginModelAndView(request);
			}
			case (ACTION_SEARCH): {
				analyticsEvent.setAction(Actions.Search);


				long resultTreeNodeId = SelectableContentUtil.searchByParameterValue(contentObject, wrapper.getSearchValues(), principalUserId, viewid);
				if (resultTreeNodeId == ParameterGroupTreeNode.MASTER_VARIANCE_ID) {
					resultTreeNodeId = wrapper.getContentSelection().getTouchpointSelection().getDocument().getMasterTouchpointSelection().getParameterGroupTreeNode().getId();
				}

				long newContentSelectionId = -1;
				ContentObjectAssociation contentAssociation = ContentObjectAssociation.findByContentObjectAndParameters(contentObject, ContentObject.DATA_TYPE_FOCUS_ON, ParameterGroupTreeNode.findById(resultTreeNodeId), null, null);
				if (contentAssociation != null)
					newContentSelectionId = contentAssociation.getId();

				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, newContentSelectionId, viewid));

			} case (ACTION_PROOF_VARIANT): {

				analyticsEvent.setAction(Actions.RequestProof);

				TouchpointSelection selection = wrapper.getContentSelection().getTouchpointSelection();
				long localeId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID, -1);
				long selectionId = selection != null ? selection.getId() : -1;

				Long channelContextId = null;
				if ( selection.getDocument().getIsOmniChannel() )
					channelContextId = UserUtil.getCurrentChannelContext().getId();

				ServiceExecutionContext context = CreateTouchpointVariantProofService.createContext(selectionId, localeId, UserUtil.getPrincipalUser(), viewid,
						channelContextId, wrapper.getProofType(), contentObject, wrapper.getTargetProofZone());
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateTouchpointVariantProofService.SERVICE_NAME, CreateTouchpointVariantProofService.class);
				service.execute(context);

				if (!context.getResponse().isSuccessful()) {
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return showForm(request, response, errors);

				} else {
					return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request, contentSelectionId, viewid));
				}
			}
			default:
				break;
			}
			return null;
		} finally {
			analyticsEvent.send();
		}
	}

	private ModelAndView getOriginModelAndView(HttpServletRequest request) {

		String [] originForm = getOriginForm(request, true);
		String origin = originForm[0];

		if (origin.contains("message/message_edit_targeting.form") ||
				origin.contains("message/message_edit_content.form") ||
				origin.contains("message/message_edit.form")) {
			originForm = getOriginForm(request, true);
			origin = originForm[0];
		}

		if (!originForm[1].isEmpty())
			origin += "?" + originForm[1];

		return new ModelAndView(new RedirectView(origin));

	}

	public String getEditView() {
		return editView;
	}

	public void setEditView(String editView) {
		this.editView = editView;
	}

	private String getBackToListURL(HttpServletRequest request) {
		return HttpRequestUtil.getBackToListURL(request, HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_TP_CONTENT_SELECTION);
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request, long contentSelectionId, int viewid) {

		boolean isFastView = ServletRequestUtils.getBooleanParameter(request, REQ_PARM_FAST_VIEW, false);
		long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID_PARAM, -1);
		long contentObjectId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);

		Map<String, Object> parms = new HashMap<>();
		if (contentSelectionId > 0) {
			parms.put(REQ_PARAM_CONTENT_SELECTION_ID_PARAM, contentSelectionId);
		} else {
			parms.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID_PARAM, touchpointSelectionId);
			parms.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);
		}
		if (viewid != -1L) {
			parms.put(REQ_PARAM_VIEW_ID, viewid);
		}

		if (isFastView)
			parms.put(REQ_PARM_FAST_VIEW, isFastView);

		return parms;
	}
	
	private int getStatusViewIdParam(HttpServletRequest request) {
		int urlStatusViewId = ServletRequestUtils.getIntParameter(request, REQ_PARAM_VIEW_ID, -1);
		if (urlStatusViewId > 0)
			return urlStatusViewId;
		return UserUtil.getCurrentSelectionStatusContext().getId();
	}

	private TouchpointSelection getTouchpointSelection(HttpServletRequest request) {
		long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID_PARAM, -1);
		TouchpointSelection touchpointSelection = TouchpointSelection.findById(touchpointSelectionId);

		return touchpointSelection;
	}

	private TouchpointContentObjectContentSelection getTouchpointContentObjectContentSelection(HttpServletRequest request) {
		long statusViewId = getStatusViewIdParam(request);
        long contentSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_SELECTION_ID_PARAM, -1);
        TouchpointContentObjectContentSelection result = null;

        if (contentSelectionId > 0) {
            result = TouchpointContentObjectContentSelection.findById(contentSelectionId);
            if (result != null) {
				ContentObject contentObject = result.getContentObject();
				if (contentObject != null) {
					if (statusViewId == SelectionStatusFilterType.ID_ACTIVE)
						contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
					else
						contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);

					result.setContentObject(contentObject);
				}
			}
		}

		if (result == null)
		{
            TouchpointSelection touchpointSelection = getTouchpointSelection(request);
            ContentObject contentObject = ContentObject.findByHttpServletRequest(request);

            if (touchpointSelection != null && contentObject != null) {
				if (statusViewId == SelectionStatusFilterType.ID_ACTIVE) {
					contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
				} else if (statusViewId == SelectionStatusFilterType.ID_ARCHIVED) {
					contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ARCHIVED);
				} else {
					contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
					if(!touchpointSelection.getDocument().isEnabledForVariantWorkflow() || touchpointSelection.isMaster()){
						contentObject.checkAndAdjustFocus();
					}
				}
                result = new TouchpointContentObjectContentSelection(contentObject, touchpointSelection.getParameterGroupTreeNode(), null);
            }
        }

        return result;
	}

}