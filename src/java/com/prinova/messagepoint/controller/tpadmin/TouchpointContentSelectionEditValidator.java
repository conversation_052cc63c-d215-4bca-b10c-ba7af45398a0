package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.content.ContentValidationUtil;
import com.prinova.messagepoint.model.ContentObjectMultipartContentVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.SubContentType;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.exacttarget.ExactTargetConfiguration;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.*;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * TouchpointContentSelectionEditValidator
 *
 * @contentRefactor
 * 
 * @since 3.5
 * <AUTHOR> Team
 */
public class TouchpointContentSelectionEditValidator extends MessagepointInputValidator {

	private final static  String SUBMIT_PARAM_CANCEL_AND_EXIT = "cancelandexit";

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointContentSelectionEditWrapper command = (TouchpointContentSelectionEditWrapper) commandObj;
		
		if (command.getActionValue().equals(SUBMIT_PARAM_CANCEL_AND_EXIT))
			return;
			
		Document doc = command.getContentSelection().getTouchpointSelection().getDocument();
		//String defaultLangCode = TouchpointLanguage.getDefaultTouchpointLanguageCode(doc);

		if (command.getContentType() == null || command.getContentType().getId() == ContentAssociationType.ID_EMPTY) {
			errors.reject("error.touchpointselection.content.type.must.be.selected");
		} else {
			if (command.getContentType().getId() == ContentAssociationType.ID_OWNS) {
				ContentType contentType = command.getContentSelection().getContentObject().getContentType();
				if (contentType.getId() == ContentType.MULTIPART) {
					ContentObjectMultipartContentVO multipartContentVO = command.getMpContentVO();
					ContentObjectMultipartContentVO.MultipartContentZoneVO mpZoneVO = multipartContentVO.getZoneVO();
					ArrayList<ContentObjectMultipartContentVO.ZonePartVO> zoneParts = mpZoneVO.getParts();
					for (ContentObjectMultipartContentVO.ZonePartVO zonePartVO : zoneParts) {
						Map<Long, ContentVO> zoneContents = zonePartVO.getLanguageContentVOs();
						for (Long localeId : zoneContents.keySet()) {
							zoneContents.get(localeId).setMessageId(command.getContentSelection().getContentObject().getId());
						}
					}
					ContentValidationUtil.validateMultipartContentObjectContent(multipartContentVO, errors, false);
				} else {
					//boolean contentProvided = false;
					//boolean defaultLangContentProvided = false;
					List<String> appliedImageNames = new ArrayList<>();
					
					for (Long localeId : command.getContentVO().getLangContentMap().keySet()) {

						ContentVO contentVO = command.getContentVO().getLangContentMap().get(localeId);
						if (contentType.getId() == ContentType.TEXT) {

							// Reject text content if it contains forbidden characters
							if ( ContentObjectContentUtil.hasForbiddenCharacters(contentVO.getContent()) )
								errors.reject("error.message.invalid.text.content");
							
//							if ( command.getContentSelection().getContentObject().getIsTouchpointLocal() && !command.getContentSelection().getContentObject().isInsertAsBlockContent() && 
//								 ContentObjectContentUtil.hasBlockContent(contentVO.getContent()) )
//									errors.reject("error.message.block.content.not.permitted.for.inline");

							if ( ContentValidationUtil.hasListStyleConflicts(contentVO) )
								errors.reject("error.message.text.styles.cannot.wrap.lists", new String[]{ MessagepointLocale.findById(localeId).getName() }, "");

							if ( ContentValidationUtil.checkOverlappingFreeformContainers(contentVO) )
								errors.reject("error.message.overlapping.freeform.containers", new String[]{ MessagepointLocale.findById(localeId).getName() }, "");

							// Validate variables in the content
				    		List<String> invalidVariables = ContentObjectContentUtil.hasIncompatibleVariables(contentVO.getContent(), command.getContentSelection().getContentObject().getZone());
							if ( !invalidVariables.isEmpty() )
								errors.reject("error.message.variables.in.content.not.data.model.compatible", new String[] {StringUtil.join(invalidVariables, ", ") }, "");
					    	
						} else {
							// Enforce the unique applied image name
							String appliedImageName = contentVO.getAppliedImageFilename();
							if(appliedImageName != null && !appliedImageName.trim().isEmpty() && !contentVO.isUseImageLibrary()){
								if(appliedImageNames.contains(appliedImageName)){
									errors.reject("error.message.duplicateAppliedImageName", new String[] { appliedImageName }, "");
								}else{
									appliedImageNames.add(appliedImageName);
								}
							}							
							boolean fileExists = contentVO.getImageLocation() != null && !contentVO.getImageLocation().trim().isEmpty();
							boolean fileSubmitted = !((contentVO.getFile() == null) || (contentVO.getFile()
                                    .getOriginalFilename().isEmpty()));
							if (fileSubmitted) {
								// Local Image: Set graphic type if unset (allows comparison between images in same message)
								if ( command.getContentSelection().getContentObject().getIsTouchpointLocal() && command.getContentSelection().getContentObject().getGraphicSubTypeId() == 0 ) {
									SubContentType subContentType = ContentObjectContentUtil.getSubContentTypeFromFilename(contentVO.getFile().getOriginalFilename());
									if ( subContentType != null )
										command.getContentSelection().getContentObject().setGraphicTypeId(Integer.valueOf(String.valueOf(subContentType.getId())));
								}
								
								boolean rejectFileType = command.getContentSelection().getContentObject().getIsTouchpointLocal() ?
										!ContentValidationUtil.checkGraphicFileTypeCompatible(contentVO, command.getContentSelection().getContentObject().getGraphicTypeId() != null ? command.getContentSelection().getContentObject().getGraphicTypeId() : 0):
										!ContentValidationUtil.checkGraphicFileTypeCompatible(contentVO, command.getContentSelection().getContentObject().getZone());
								if ( rejectFileType ) {
									String errorKey = "error.message.imagefile.not.compatible." + MessagepointLocale.findById(localeId).getLanguageCode();
									errors.reject(errorKey);
								}
							}
							if (fileSubmitted) {
								MessagepointInputValidationUtil.validateGraphicFileNameValue(ApplicationUtil.getMessage("page.label.file.name"), contentVO.getFile().getOriginalFilename(), errors);
								// Validate the graphic file size for the exact target touchpoint
								if(doc.isExactTargetTouchpoint()){
									if(contentVO.getFile().getSize() > ExactTargetConfiguration.MAX_UPLOAD_IMAGE_SIZE_KB * 1000){
										errors.reject("error.message.graphicfilesize.too.large", new String[] { contentVO.getFile().getOriginalFilename(), Long.toString(ExactTargetConfiguration.MAX_UPLOAD_IMAGE_SIZE_KB) }, "");
					    				break;
					    			}									
								}
							}
							//if (fileSubmitted || fileExists) {
								//contentProvided = true;
							//	if (/*isDefaultSelection &&*/ localeId.equals(defaultLangCode)) {
									//defaultLangContentProvided = true;
							//	}
							//}
							
							// Validate Content Library Reference
							if(contentVO.isUseImageLibrary() && contentVO.getImageLibraryId() <= 0){
								errors.reject("error.message.invalid.content.library.reference", new String[]{MessagepointLocale.findById(localeId).getName()}, "");
			    				break;					
							}
						}
					}
					// From version 4.0 the content can be empty
					//if (/*isDefaultSelection &&*/ !defaultLangContentProvided) {
					//	errors.reject("error.touchpointselection.content.default.lang.content.must.be.provided");
					//} else if (!contentProvided) {
					//	errors.reject("error.touchpointselection.content.custom.content.must.be.provided");
					//}
				}
			}
		}
	}
}
