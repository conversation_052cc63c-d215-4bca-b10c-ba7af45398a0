package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.ReferenceConnection;
import com.prinova.messagepoint.model.admin.SourceType;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.tpadmin.TouchpointDataSourceAssociationEditController.Command;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

import java.util.List;

public class TouchpointDataSourceAssociationEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		Command command = (Command) commandObj;

		if (command.getDocument() == null) {
			errors.rejectValue("document", "error.message.mustselecttouchpoint");
		}
		if (command.getDataSourceAssociation() == null) {
			errors.rejectValue("dataSourceAssociation", "error.message.mustselectdatasourceassociation");
		}
		if (command.getDocument().isConnectedEnabled()) {
			if (command.getDataSourceAssociation() != null && command.getDocument().isCommunicationUseBeta()) {
				List<Document> documents = Document.findAll();
				for (Document doc : documents) {
					if (!doc.isCommunicationUseBeta() && doc.getDataSourceAssociation() != null && command.getDataSourceAssociation().getId() == doc.getDataSourceAssociation().getId()) {
						errors.rejectValue("dataSourceAssociation", "error.message.mustselectdatasourceassociationnotlinkedtooldcconnected");
					}
				}
			} else if (!command.getDocument().isCommunicationUseBeta()) {
				for (ReferenceConnection rc : command.getDataSourceAssociation().getReferenceConnections()) {
					DataSource ds = rc.getReferenceDataSource();
					if (rc.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED && ds.isJSON()) {
						errors.rejectValue("dataSourceAssociation", "error.message.mustselectdatasourceassociationnotjson");
					}
				}
			}
		}

	}
}