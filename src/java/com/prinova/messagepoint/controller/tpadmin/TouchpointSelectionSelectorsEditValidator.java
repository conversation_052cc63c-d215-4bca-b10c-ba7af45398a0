package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.controller.content.SelectableValidationUtil;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

public class TouchpointSelectionSelectorsEditValidator extends MessagepointInputValidator {
		
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointSelectionSelectorsEditWrapper command = (TouchpointSelectionSelectorsEditWrapper) commandObj;
		TouchpointSelection touchpointSelection = TouchpointSelection.findById(command.getMessagepointSelection().getId());
		ParameterGroup parameterGroup = touchpointSelection.getDocument().getSelectionParameterGroup();
		List<ParameterGroupItem> pgItems = parameterGroup.getParameterGroupItemsSorted();
		
		// Making sure that the data values obey the input conventions
		List<String> dataValues = new ArrayList<>();
		for (int i = 0; i < command.getDataValues().length; i++) {
			if (command.getDataValues()[i] != null && !command.getDataValues()[i].trim().isEmpty()) {
				String dataValue = command.getDataValues()[i].trim();
				dataValues.add(dataValue);
				String label = pgItems.get(i).getParameter().getName();
				SelectableValidationUtil.checkDataValue(label, dataValue, errors);
			}
		}

		if (errors.hasErrors()) {
			return;
		}
		
		String action = command.getSelectorAction().trim();

		checkForEmptyValues(command.getDataValues(), touchpointSelection.getParameterGroupTreeNode().getParameterGroup().getParameters(), errors);

		if (errors.hasErrors()) {
			return;
		}

		List<List<String>> flatDataValues = ParameterGroupInstance.flattenValue(dataValues);
		
		if (action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_ADD)) {

			if (flatDataValues.size() <= 0) {
				errors.reject("error.touchpointselection.selectors.add.no.value.provided");
				return;
			}
			
			// Making sure that there's no repeating entries in entered values 
			SelectableValidationUtil.checkForDuplicateInEnteredValue(command.getDataValues(), errors);

			for (List<String> value : flatDataValues) {
				if (command.getPgiCollectionId() > 0) {
					// Making sure that none of the entered values has any overlap with other nodes
					List<ParameterGroupInstance> pgInstances = ParameterGroupInstance.findRelativeMatchInCollection(command.getPgiCollectionId(), value);
					if (pgInstances != null && !pgInstances.isEmpty()) {
						pgInstances.get(0);
						String stringValue = ParameterGroupTreeNode.getStringValue(value);
						String errorCode = "error.touchpointselection.selectors.combination.is.added.before";
						errors.reject(errorCode, new String[]{stringValue}, "");
						return;
					}					
				}
				// Making sure that none of the entered values sibling values
				if (valueExistsInSiblings(touchpointSelection, value, errors)) {
					return;
				} else {
					if (touchpointSelection.getLevel() > 1) {
						
						// Making sure that first level value exists in the root parent node
						TouchpointSelection firstLevelAncester = touchpointSelection.getFirstLevelAncester();
						
						if (firstLevelAncester != null) {
							boolean firstLevelValueExistsInParentNode = ParameterGroupTreeNode.firstLevelValueExistsInNode(firstLevelAncester.getParameterGroupTreeNode().getId(), value.get(0));
							if (!firstLevelValueExistsInParentNode) {
								String stringValue = ParameterGroupTreeNode.getStringValue(value);
								errors.reject("error.touchpointselection.selectors.add.selection.criteria.falls.outside.criteria.for.its.parent.selection", new String[]{stringValue, firstLevelAncester.getParameterGroupTreeNode().getName()}, "");
							}
						}
					}
				}
			}
		} else if (action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_REMOVE)) {
			if (command.getPgiCollectionId() <= 0L) {
				errors.reject("error.touchpointselection.selectors.remove.no.value.to.remove");
			} else {
				if (flatDataValues.isEmpty()) {
					errors.reject("error.touchpointselection.selectors.remove.no.value.provided");
					return;
				} else {
					ParameterGroupTreeNode pgTreeNode = ParameterGroupTreeNode.findChildNodeByValue(touchpointSelection.getParameterGroupTreeNode().getId(), flatDataValues.get(0));
					if (pgTreeNode != null) {
						StringBuilder removedValues= new StringBuilder("[");
						for (String itemRemoved : flatDataValues.get(0)) {
							removedValues.append(itemRemoved).append(" ");
						}
						removedValues.append("]");
						
						errors.reject("error.touchpointselection.selectors.add.selection.redefined.selection.criteria.would.result.in.orphaning.the.criteria.for.the.child.selection", new String[] {removedValues.toString(), pgTreeNode.getName() }, "");
					}
				}
			}
		}
	}
	
	private boolean valueExistsInSiblings(TouchpointSelection touchpointSelection, List<String> value, Errors errors) {
		List<Long> pgTreeNodeIds = touchpointSelection.getDocument().getTouchpointSelectionTreeNodeIds();
		ParameterGroupTreeNode pgTreeNode = ParameterGroupTreeNode.searchForExactMatch(pgTreeNodeIds, value);
		if (pgTreeNode != null) {
			String stringValue = ParameterGroupTreeNode.getStringValue(value);
			TouchpointSelection tpSelection = TouchpointSelection.findByPgTreeNodeId(pgTreeNode.getId());
			errors.reject("error.touchpointselection.selectors.add.selection.criteria.conflicts.with.an.existing.selection", new String[] { stringValue, tpSelection.getName() }, "");
			return true;
		}
		return false;
	}
	
	private static void checkForEmptyValues(String[] newNodeValues, List<Parameter> parameters, Errors errors) {
		boolean reachedEmptyValue = false;
		for (int i = 0; i < newNodeValues.length; i++) {
			String value = newNodeValues[i];
			String parameterName = parameters.get(i).getName(); 
			if (value == null || value.trim().isEmpty()) {
				if (i == 0) {
					errors.reject("error.message.must.enter.data", new String[]{parameterName}, "");
					return;
				} else {
					reachedEmptyValue = true;
				}
			} else {
				// For a ParameterGroup like CCG, prevent: C1: :G1
				if (reachedEmptyValue) {
					errors.reject("error.message.there.is.at.least.one.empty.value.before.parameter", new String[]{parameterName}, "");
					return;
				}
			}
		}
	}
}