package com.prinova.messagepoint.controller.tpadmin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.LanguageSelection;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.languageselection.CreateOrUpdateLanguageSelectionService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class LanguageSelectionSelectorsEditController extends MessagepointController {
	
	public static final String REQ_PARAM_SELECTION_ID_PARAM = "languageSelectionid";
	public static final String REQ_PARAM_DOCUMENT_ID_PARAM = "documentId";
	public static final String FORM_SUBMIT_TYPE_UPDATE = "update";
	
	private static final Log log = LogUtil.getLog(LanguageSelectionSelectorsEditController.class);
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> referenceData = new HashMap<>();

		LanguageSelection languageSelection = getLanguageSelection(request);
		referenceData.put("languageSelection", languageSelection);
		
		ParameterGroup parameterGroup = languageSelection.getParameterGroupTreeNode().getParameterGroup();
		if (parameterGroup != null) {
			List<Parameter> parameters = parameterGroup.getParameters();
			referenceData.put("parameters", parameters);
		}

    	return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected LanguageSelectionSelectorsEditWrapper formBackingObject(HttpServletRequest request) {
		LanguageSelection languageSelection = getLanguageSelection(request);
		LanguageSelectionSelectorsEditWrapper command = new LanguageSelectionSelectorsEditWrapper(); 
		if (languageSelection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted() != null)
			command.setDataValues(new String[languageSelection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted().size()]);
		if (languageSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection() != null)
			command.setPgiCollectionId(languageSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection().getId());
		command.setMessagepointSelection(languageSelection);
		
		return command;
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.LanguageSelectionSelectorsEdit);

		try {
			LanguageSelectionSelectorsEditWrapper command = (LanguageSelectionSelectorsEditWrapper)commandObj;
			User principalUser = UserUtil.getPrincipalUser();
			String action = command.getSelectorAction().trim();
			ServiceExecutionContext context = null;

			if (action.equals(LanguageSelectionSelectorsEditWrapper.ACTION_ADD)) {
				analyticsEvent.setAction(Actions.Add);
				context = CreateOrUpdateLanguageSelectionService.createContextForAddDataValues(command.getMessagepointSelection().getId(), command.getDataValues(), principalUser);
			} else if (action.equals(LanguageSelectionSelectorsEditWrapper.ACTION_REMOVE)) {
				analyticsEvent.setAction(Actions.Remove);
				context = CreateOrUpdateLanguageSelectionService.createContextForRemoveDataValues(command.getMessagepointSelection().getId(), command.getDataValues(), principalUser);
			}

			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateLanguageSelectionService.SERVICE_NAME, CreateOrUpdateLanguageSelectionService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateLanguageSelectionService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor=").append(principalUser.getUsername());
				sb.append(" LanguageSelection Selector was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessRedirectParams(request, command));
			}
		} finally {
			analyticsEvent.send();
		}
	}
	
	protected Map<String, Object> getSuccessRedirectParams(HttpServletRequest request, LanguageSelectionSelectorsEditWrapper command) {
		String action = command.getSelectorAction().trim();
		
		long tpSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		
		Map<String, Object> params = new HashMap<>();
		if (action.equals(LanguageSelectionSelectorsEditWrapper.ACTION_ADD) || action.equals(LanguageSelectionSelectorsEditWrapper.ACTION_REMOVE)) {
			params.put(REQ_PARAM_SELECTION_ID_PARAM, tpSelectionId);
			params.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
		}
		
		return params;
	}

	protected LanguageSelection getLanguageSelection(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
		LanguageSelection languageSelection = HibernateUtil.getManager().getObject(LanguageSelection.class, id);
		return languageSelection;
	}
}