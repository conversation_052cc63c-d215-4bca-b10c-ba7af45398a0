package com.prinova.messagepoint.controller.tpadmin;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SegmentationAnalyzable;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.CreateSegmentationAnalysisService;
import com.prinova.messagepoint.platform.services.tpselection.CreateOrUpdateTouchpointVariantService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointSelectionSelectorsEditController extends MessagepointController {
	
	public static final String REQ_PARAM_SELECTION_ID_PARAM = AsyncListTableController.PARAM_SELECTION_ID;
	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 	= AsyncListTableController.PARAM_DOCUMENT_ID;

	public static final String FORM_SUBMIT_TYPE_UPDATE 		= "update";
	
	private static final Log log = LogUtil.getLog(TouchpointSelectionSelectorsEditController.class);
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> referenceData = new HashMap<>();

		TouchpointSelection touchpointSelection = getTouchpointSelection(request);
		referenceData.put("touchpointSelection", touchpointSelection);
		
		ParameterGroup parameterGroup = touchpointSelection.getParameterGroupTreeNode().getParameterGroup();
		if (parameterGroup != null) {
			List<Parameter> parameters = parameterGroup.getParameters();
			referenceData.put("parameters", parameters);
		}
		
		// Put the parent pgi collection id
		TouchpointSelection parentSelection = touchpointSelection.getParent();
		if(parentSelection.isMaster()){	// No parent
			referenceData.put("parentPgiCollectionId", -1);
		}else if(touchpointSelection.getParent().getParameterGroupTreeNode().getParameterGroupInstanceCollection() == null){ // Parent does not have data values
			referenceData.put("parentPgiCollectionId", 0);
		}else{
			long parentPgiCollectionId = touchpointSelection.getParent().getParameterGroupTreeNode().getParameterGroupInstanceCollection().getId();	
			referenceData.put("parentPgiCollectionId", parentPgiCollectionId);			
		}
		
    	return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected TouchpointSelectionSelectorsEditWrapper formBackingObject(HttpServletRequest request) {
		TouchpointSelection touchpointSelection = getTouchpointSelection(request);
		TouchpointSelectionSelectorsEditWrapper command = new TouchpointSelectionSelectorsEditWrapper(); 
		if (touchpointSelection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted() != null)
			command.setDataValues(new String[touchpointSelection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted().size()]);
		if (touchpointSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection() != null)
			command.setPgiCollectionId(touchpointSelection.getParameterGroupTreeNode().getParameterGroupInstanceCollection().getId());
		command.setMessagepointSelection(touchpointSelection);
		
		return command;
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.TouchpointSelectionSelectorsEdit);

		try {
			TouchpointSelectionSelectorsEditWrapper command = (TouchpointSelectionSelectorsEditWrapper)commandObj;
			User principalUser = UserUtil.getPrincipalUser();
			String action = command.getSelectorAction().trim();
			ServiceExecutionContext context = null;

			if (action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_ADD)) {
				analyticsEvent.setAction(Actions.Add);

				context = CreateOrUpdateTouchpointVariantService.createContextForAddDataValues(command.getMessagepointSelection().getId(), command.getDataValues(), principalUser);
			} else if (action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_REMOVE)) {
				analyticsEvent.setAction(Actions.Remove);

				context = CreateOrUpdateTouchpointVariantService.createContextForRemoveDataValues(command.getMessagepointSelection().getId(), command.getDataValues(), principalUser);
			}

			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTouchpointVariantService.SERVICE_NAME, CreateOrUpdateTouchpointVariantService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateTouchpointVariantService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor=").append(principalUser.getUsername());
				sb.append(" TouchpointSelection Selector was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				// Set TPContentChanged to true
				TouchpointSelection ts = (TouchpointSelection)command.getMessagepointSelection();
				if(ts != null && ts.getDocument() != null && !ts.getDocument().isTpContentChanged()
						&& ((ts.getDocument().isEnabledForVariantWorkflow() && (ts.isMaster() || ts.isActive() ))
								|| !ts.getDocument().isEnabledForVariantWorkflow())){
					ts.getDocument().setTpContentChanged(true);
					ts.getDocument().save();
				}
				// Update segmentation data
				Document document = UserUtil.getCurrentTouchpointContext();
				if(UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW) && document.isSegmentationAnalysisEnabled()){
					Service sgService = MessagepointServiceFactory.getInstance().lookupService(CreateSegmentationAnalysisService.SERVICE_NAME, CreateSegmentationAnalysisService.class);
					Set<SegmentationAnalyzable> sas = new HashSet<>();
					sas.add(ts);
					ServiceExecutionContext sgContext = CreateSegmentationAnalysisService.createContext(ts.getDocument().getId(), sas, UserUtil.getPrincipalUser());
					if ( sgService != null && sgContext != null )
						sgService.execute(sgContext);
				}
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessRedirectParams(request, command));
			}
		} finally {
			analyticsEvent.send();
		}
	}
	
	protected Map<String, Object> getSuccessRedirectParams(HttpServletRequest request, TouchpointSelectionSelectorsEditWrapper command) {
		String action = command.getSelectorAction().trim();
		
		long tpSelectionId = 	ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
		long documentId = 		ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		
		Map<String, Object> params = new HashMap<>();
		if (action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_ADD) || action.equals(TouchpointSelectionSelectorsEditWrapper.ACTION_REMOVE)) {
			params.put(REQ_PARAM_SELECTION_ID_PARAM, tpSelectionId);
			params.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
		}
		
		return params;
	}

	protected TouchpointSelection getTouchpointSelection(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
		TouchpointSelection touchpointSelection = HibernateUtil.getManager().getObject(TouchpointSelection.class, id);
		return touchpointSelection;
	}
}