package com.prinova.messagepoint.controller.tpadmin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.common.TouchpointSelectionListFilterType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpselection.GetTouchpointVariantListService;
import com.prinova.messagepoint.platform.services.tpselection.UpdateVariantWorkflowAssignmentService;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class VariantWorkflowAssignmentEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(VariantWorkflowAssignmentEditController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 	= "documentId";
	public static final String REQUEST_PARM_SAVE_SUCCESS	= "saveSuccess";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		
		referenceData.put("variantWorkflows", ConfigurableWorkflow.findByDocumentModelAndUsageTypes(documentId, ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_VARIANT));
		referenceData.put("connectedWorkflows", ConfigurableWorkflow.findByDocumentModelAndUsageTypes(documentId, ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_CONNECTED));
		
		referenceData.put("document", Document.findById(documentId));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(ConfigurableWorkflow.class, new IdCustomEditor<>(ConfigurableWorkflow.class));
	}
	
	@SuppressWarnings("unchecked")
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		VariantWorkflowAssignmentEditWrapper command = new VariantWorkflowAssignmentEditWrapper();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);
		
		User requestor = UserUtil.getPrincipalUser();
		
		if ( document.isEnabledForVariation() ) {

			ServiceExecutionContext context = GetTouchpointVariantListService.createContextForList(documentId, TouchpointSelectionListFilterType.ID_ALL, false, requestor);
			Service service = MessagepointServiceFactory.getInstance().lookupService(GetTouchpointVariantListService.SERVICE_NAME, GetTouchpointVariantListService.class);
			service.execute(context);
			if ( context.getResponse().isSuccessful() ) {
				List<TouchpointSelectionsListVO> tpSelectionsList = (List<TouchpointSelectionsListVO>) context.getResponse().getResultValueBean();
				command.setTpSelectionslist(tpSelectionsList);
			} else {
				StringBuilder sb = new StringBuilder();
				sb.append(GetTouchpointVariantListService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor = ").append(requestor.getUsername());
				sb.append(" list service was not successful. ");
				log.error(sb.toString());
			}
		
		}

		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.VariantWorkflowAssignmentEdit)
				.setAction(Actions.Update);
		try {
			VariantWorkflowAssignmentEditWrapper command = (VariantWorkflowAssignmentEditWrapper)commandObj;

			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);

			ServiceExecutionContext context = UpdateVariantWorkflowAssignmentService.createContext(command.getTpSelectionslist());
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateVariantWorkflowAssignmentService.SERVICE_NAME, UpdateVariantWorkflowAssignmentService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();

			if ( !serviceResponse.isSuccessful() ) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateVariantWorkflowAssignmentService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" Touchpoint Proofing Data was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> params = new HashMap<>();

				params.put(REQUEST_PARM_SAVE_SUCCESS, true);
				params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				if ( documentId == -1 ) {
					return new ModelAndView(new RedirectView("variant_workflow_assignment_edit.form"), params);
				} else {
					params.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
					return new ModelAndView(new RedirectView("variant_workflow_assignment_edit.form"), params);
				}
				}
		} finally {
			analyticsEvent.send();
		}
	}
}