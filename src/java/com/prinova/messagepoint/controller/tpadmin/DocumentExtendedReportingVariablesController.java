package com.prinova.messagepoint.controller.tpadmin;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataElementVariableComparator;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentExtendedReportingVariablesService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class DocumentExtendedReportingVariablesController extends MessagepointController {

	private static final Log log = LogUtil.getLog(DocumentExtendedReportingVariablesController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM = "documentId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);

		List<DataElementVariable> availableVariables = new ArrayList<>();
		if (document != null) {
			List<DataElementVariable> documentVariables = DataElementVariable.findAllForDocument(document);
			for (DataElementVariable currentVariable: documentVariables)
			{
				DataGroup currentVariableDataGroup = currentVariable.getDataGroup(document);
				if (currentVariableDataGroup == null || (currentVariableDataGroup != null && currentVariableDataGroup.isRecepientLevel()))
					availableVariables.add(currentVariable);
			}
			
			Set<DataElementVariable> reportingVariables = document.getExtReportingDataVariables();
			for (DataElementVariable currentVariable : reportingVariables)
			{
				if (!availableVariables.contains(currentVariable))
					availableVariables.add(currentVariable);
			}
		}
		
		// Sort the list alpha
		Collections.sort(availableVariables, new DataElementVariableComparator());		
		referenceData.put("availableVariables", availableVariables);
		
		if (document.getCustomerReportingVariableA() != null && !availableVariables.contains(document.getCustomerReportingVariableA()))
		{
			availableVariables.add(document.getCustomerReportingVariableA());
		}
		if (document.getCustomerReportingVariableB() != null && !availableVariables.contains(document.getCustomerReportingVariableB()))
		{
			availableVariables.add(document.getCustomerReportingVariableB());
		}
		referenceData.put("availableReportingVariables", availableVariables);
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( Document.class, new IdCustomEditor<>(Document.class) );
		binder.registerCustomEditor( DataElementVariable.class, new IdCustomEditor<>(DataElementVariable.class) );
		binder.registerCustomEditor( String.class, new StringXSSEditor() );
    }
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);

		DocumentExtendedReportingVariablesWrapper command = new DocumentExtendedReportingVariablesWrapper(document);
		
		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.DocumentExtendedReportingVariables)
				.setAction(Actions.Update);

		try {
			DocumentExtendedReportingVariablesWrapper command = (DocumentExtendedReportingVariablesWrapper)commandObj;

			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);

			ServiceExecutionContext context = UpdateDocumentExtendedReportingVariablesService.createContext(
																									documentId,
																									command.getSelectedExtReportingVariableIds(),
																									command.getCustomerReportingVariableA(),
																									command.getCustomerReportingVariableB());
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDocumentExtendedReportingVariablesService.SERVICE_NAME, UpdateDocumentExtendedReportingVariablesService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateDocumentExtendedReportingVariablesService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" selected Variables were not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				// Audit (TP Extended Reporting Variables Edit)
				AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, command.getDocument().getName(), command.getDocument().getId(), AuditActionType.ID_CHANGES,
						AuditMetadataBuilder.forTPExtendedReportingVariablesChanges(command));
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}