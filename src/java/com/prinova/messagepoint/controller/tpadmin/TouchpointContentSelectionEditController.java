package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.interfaces.AnalyticsGroupEvent;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.controller.*;
import com.prinova.messagepoint.controller.touchpoints.SelectionStatusFilterType;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.controller.touchpoints.TouchpointVariantListController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.wrapper.AsyncContentObjectListVO;
import com.prinova.messagepoint.model.wrapper.AsyncContentObjectListWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.TouchpointContentSelectionUpdateService;
import com.prinova.messagepoint.platform.services.version.CheckoutFromProductionService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.beans.propertyeditors.CustomBooleanEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * <p>
 * TouchpointContentSelectionEditController
 *
 * <AUTHOR> Team
 * @contentRefactor
 * @since 3.5
 */
public class TouchpointContentSelectionEditController extends MessagepointController {

    private static final Log log = LogUtil.getLog(TouchpointContentSelectionEditController.class);

    public static final String REQ_PARAM_CONTENT_SELECTION_ID = "contentSelectionId";
    public static final String REQ_PARAM_DOCUMENT_ID = TouchpointVariantListController.REQ_PARAM_DOCUMENTID;
    public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID = TouchpointContentObjectListController.REQ_PARM_SELECTION_ID;
    public static final String REQ_PARM_RETURN_VIEW = "returnView";
    public static final String REQ_PARM_ACTION = "submittype";
    public static final String REQ_PARM_MODEL_ID = "modelId";
    public static final String REQ_PARM_FAST_EDIT = "fastEdit";
    public static final String REQ_PARAM_VIEW_ID = "statusViewId";
    public static final String REQ_PARM_INIT_WORKING_COPY = "initWorkingCopy";
    public static final String REQ_PARM_REASSIGN = "reassign";
    public static final String REQ_PARM_CONTINUE_ID = "continueInstId";

    public static final String REQ_PARM_MASTER_VARIANT_ID = "masterVariantId";

    public static final String RETURN_VIEW_TYPE_LIST = "list";
    public static final String ACTION_UPDATE = "1";


    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

        Map<String, Object> referenceData = new HashMap<>();
        TouchpointContentSelectionEditWrapper wrapper = (TouchpointContentSelectionEditWrapper) command;

        ContentObject message = wrapper.getContentSelection().getContentObject();
        referenceData.put("messageZoneDelivery", message.getZone() != null ? message.getZone() : 0);

        Document touchpointContext = UserUtil.getCurrentTouchpointContext();

        Document messageTouchpointContext = message.getIsTouchpointLocal() ? message.getDocument() : touchpointContext;
        Zone sharedAssetZoneContext = message.getIsTouchpointLocal() && !message.isDeliveredToPlaceholder() ? null : message.getZone();

        // SMART CANVAS
        List<ContentObject> smartCanvas = new ArrayList<>();
        if (message.getIsFreeform() && !message.getIsSharedFreeform() && touchpointContext.isNativeCompositionTouchpoint()) {
            String[] zoneDims = sharedAssetZoneContext.getCanvasDimensions().split(":");
            smartCanvas = ContentObject.findAllCanvasWithActiveInstanceByDocumentAndZone(messageTouchpointContext, sharedAssetZoneContext, ContentType.SHARED_FREEFORM,
                    sharedAssetZoneContext.isSupportsTables(), sharedAssetZoneContext.isSupportsForms(), sharedAssetZoneContext.isSupportsBarcodes(), false, DecimalValueUtil.hydrate(zoneDims[0]), DecimalValueUtil.hydrate(zoneDims[1]));
        }
        referenceData.put("smartCanvasContentData", ContentObject.getListAsJSON(smartCanvas));

        // PLACEHOLDERS
        List<Zone> placeholders = new ArrayList<>();
        if (!message.getIsTouchpointLocal())
            placeholders = Zone.findPlaceholdersByDocumentId(touchpointContext);
        referenceData.put("placeholdersData", Zone.getListAsJSON(placeholders));

        // LOCAL SMART CANVAS
        List<ContentObject> localSmartCanvas = new ArrayList<>();
        if (message.getIsFreeform() && !message.getIsSharedFreeform() && touchpointContext.isNativeCompositionTouchpoint()) {
            String[] zoneDims = sharedAssetZoneContext.getCanvasDimensions().split(":");
            localSmartCanvas = ContentObject.findAllLocalCanvasByDocumentAndSelection(messageTouchpointContext, UserUtil.getCurrentSelectionContext(message), sharedAssetZoneContext.isSupportsTables(), sharedAssetZoneContext.isSupportsForms(), DecimalValueUtil.hydrate(zoneDims[0]), DecimalValueUtil.hydrate(zoneDims[1]));
        }
        referenceData.put("localSmartCanvasContentData", ContentObject.getListAsJSON(localSmartCanvas));

        List<MessagepointLocale> langLocales = message.getContentObjectLanguagesAsLocales();
        MessagepointLocale defaultLocale = touchpointContext.getDefaultTouchpointLanguageLocale();
        // Limit the locales if the message is in translation step
        boolean isInTranslationStep = false;
        List<ConfigurableWorkflowAction> workflowActions = new ArrayList<>();
        if(messageTouchpointContext.isEnabledForVariantWorkflow()){
            isInTranslationStep = wrapper.getContentSelection().getTouchpointSelection().isInTranslationStep();
            ConfigurableWorkflowAction workflowAction = wrapper.getContentSelection().getTouchpointSelection().getWorkflowAction();
            if(workflowAction != null) {
                workflowActions.add(workflowAction);
            }
        }else{
            isInTranslationStep = message.isInTranslationStep();
            workflowActions.addAll(message.getWorkflowActions());
        }
        if (isInTranslationStep) {
            Set<MessagepointLocale> filteredLocales = new HashSet<>();
            for(ConfigurableWorkflowAction workflowAction : workflowActions) {
                List<MessagepointLocale> wfaFilteredLocales = new ArrayList<>(langLocales);
                workflowAction.filterLangLocales(wfaFilteredLocales, defaultLocale.getId());
                filteredLocales.addAll(wfaFilteredLocales);
            }
            langLocales.clear();
            langLocales.addAll(filteredLocales);
        }
        referenceData.put("locales", langLocales);
        referenceData.put("languagesJSON", MessagepointLocale.getAsJSON(langLocales));

        referenceData.put("defaultLocaleId", touchpointContext.getDefaultTouchpointLanguageLocale().getId());
        referenceData.put("defaultLanguage", touchpointContext.getDefaultTouchpointLanguage().getMessagepointLocale().getName());

        MessagepointLocale currentLanguageLocale = UserUtil.getCurrentLanguageLocaleContext();
        if (currentLanguageLocale != null && langLocales.contains(currentLanguageLocale)) {
            referenceData.put("focusLocaleId", currentLanguageLocale.getId());
        } else {
            referenceData.put("focusLocaleId", langLocales.get(0).getId());
        }

        ParameterGroupTreeNode topParameterGroupTreeNode = null;
        if (message != null && message.getOwningTouchpointSelection() != null)
            topParameterGroupTreeNode = message.getOwningTouchpointSelection().getParameterGroupTreeNode();

        JSONObject copyFromJSON = new JSONObject();
        List<TouchpointSelection> ancesters = wrapper.getContentSelection().getTouchpointSelection().getAncesters(topParameterGroupTreeNode);
        if (!ancesters.isEmpty()) {
            JSONObject ancestorsGroup = new JSONObject();
            ancestorsGroup.put("name", ApplicationUtil.getMessage("page.label.ancestors"));
            ancestorsGroup.put("options", TouchpointSelection.getAsJSON(ancesters));
        }

        List<TouchpointSelection> allFamily = wrapper.getContentSelection().getTouchpointSelection().getAllChildrenAndGrandchildrenOrderByHierarchy(topParameterGroupTreeNode);

        // Removing current touchpointSelection from list
        ParameterGroupTreeNode treeNode = wrapper.getContentSelection().getTouchpointSelection().getParameterGroupTreeNode();
        allFamily.remove(TouchpointSelection.findByPgTreeNodeId(treeNode.getId()));

        if (!allFamily.isEmpty()) {
            JSONObject variantsGroup = new JSONObject();
            ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
            variantsGroup.put("name", "");
            variantsGroup.put("options", TouchpointSelection.getAsJSONArray(allFamily, contentObject));
            copyFromJSON.append("groups", variantsGroup);
        }
        referenceData.put("copyFromJSON", copyFromJSON);

        referenceData.put("ancesters", ancesters);

        if (wrapper.getContentSelection().getContentAssociationType().getId() == ContentAssociationType.ID_EMPTY)
            referenceData.put("contentType", new ContentAssociationType(ContentAssociationType.ID_OWNS));
        else
            referenceData.put("contentType", wrapper.getContentSelection().getContentAssociationType());

        TouchpointSelection touchpointSelection = wrapper.getContentSelection().getTouchpointSelection();
        referenceData.put("touchpointSelection", touchpointSelection);
        if (message != null && message.isVariantType()) {
            referenceData.put("isMasterTouchpointSelection", touchpointSelection.getId() == message.getOwningTouchpointSelection().getId());
        } else {
            referenceData.put("isMasterTouchpointSelection", touchpointSelection.isMaster());
        }

        referenceData.put("contentType_CUSTOM", ContentAssociationType.ID_OWNS);
        referenceData.put("contentType_SUPPRESSES", ContentAssociationType.ID_SUPPRESSES);
        referenceData.put("contentType_SAME_AS", ContentAssociationType.ID_REFERENCES);

        referenceData.put("touchpointContext", touchpointContext);
        referenceData.put("document", touchpointSelection.getDocument());

        referenceData.put("nodeGUID", Node.getCurrentNode().getGuid());
        referenceData.put("webDAVToken", UserUtil.getPrincipalUser().getWebDAVToken());

        referenceData.put("isFastEdit", ServletRequestUtils.getBooleanParameter(request, REQ_PARM_FAST_EDIT, false));

        referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));
        JSONObject marcieFlags = SystemProperty.getMarcieKeyValues();
        marcieFlags.put("brand_check_enabled", BrandProfile.isBrandProfileConfigured(touchpointContext, null));
        referenceData.put("marcieFlags", marcieFlags);

        referenceData.put("applySuperSubScript", message.supportsSuperSubScript());

        // SYSTEM VARIABLES
        List<DataElementVariable> systemVariables = new ArrayList<>();
        systemVariables = DataElementVariable.findAllSystemVariablesEnabledForContent(!messageTouchpointContext.isNativeCompositionTouchpoint());
        referenceData.put("systemVariablesData", DataElementVariable.getListAsJSON(systemVariables));

        referenceData.put("applyContentRotation", message.getIsFreeform() && messageTouchpointContext.isNativeCompositionTouchpoint());

        boolean canUpdate = (message == null);
        boolean actionValidationEnabled = Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION));
        if(message != null){
            AsyncContentObjectListVO vo = new AsyncContentObjectListVO();
            vo.setContentObject(message);
            AsyncContentObjectListVO.ContentObjectListVOFlags flags = new AsyncContentObjectListVO.ContentObjectListVOFlags();
            AsyncContentObjectListWrapper.setActionFlags(message, flags, request);
            canUpdate = flags.isCanUpdate();
        }
        referenceData.put("canUpdate", canUpdate || !actionValidationEnabled);

        referenceData.put("tableOfContentsEnabled", FeatureFlag.isEnabled(FeatureFlag.Features.TableOfContents, request));

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
        binder.registerCustomEditor(ContentAssociationType.class, new StaticTypeIdCustomEditor<>(ContentAssociationType.class));
        binder.registerCustomEditor(TouchpointSelection.class, new IdCustomEditor<>(TouchpointSelection.class));
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor( Long.class, "contentVO.langContentMap.imageLibraryId", new IdStringArrayToLongEditor());
        binder.registerCustomEditor( Long.class, "mpContentVO.zoneVO.parts.languageContentVOs.imageLibraryId", new IdStringArrayToLongEditor());
        binder.registerCustomEditor( Boolean.class, "mpContentVO.zoneVO.parts.languageContentVOs.useImageLibrary", new CustomBooleanEditor(true));
    }

    protected TouchpointContentSelectionEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
        User principalUser = UserUtil.getPrincipalUser();
        User requestor = User.findById(principalUser.getId());
        TouchpointContentObjectContentSelection contentSelection = getTouchpointContentSelection(request);

        TouchpointContentSelectionEditWrapper wrapper = new TouchpointContentSelectionEditWrapper(contentSelection, requestor);
        return wrapper;

    }

    private TouchpointContentObjectContentSelection getTouchpointContentSelection(HttpServletRequest request) {

        TouchpointContentObjectContentSelection contentSelection = null;
        long touchpointSelectionId = getTouchpointSelectionId(request);
        long contentSelectionId = getContentSelectionIdParam(request);

        if (touchpointSelectionId <= 0 && contentSelectionId > 0) {
            ContentObjectAssociation coa = ContentObjectAssociation.findById(contentSelectionId);
            if (coa != null && coa.getTouchpointPGTreeNode() != null) {
                touchpointSelectionId = TouchpointSelection.findByPgTreeNodeId(coa.getTouchpointPGTreeNode().getId()).getId();
            }
        }

//        if (touchpointSelectionId > 0)
//            contentSelection = TouchpointContentObjectContentSelection.findById(contentSelectionId);

        if (contentSelection == null)
        {
            TouchpointSelection touchpointSelection = TouchpointSelection.findById(touchpointSelectionId);
            ContentObject contentObject = ContentObject.findByHttpServletRequest(request);

            if (touchpointSelection != null && contentObject != null) {
                contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                contentSelection = new TouchpointContentObjectContentSelection(contentObject, touchpointSelection.getParameterGroupTreeNode(), null);
            }
        }
        else
        {
            ContentObject contentObject = contentSelection.getContentObject();
            if (contentObject != null)
            {
                contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                contentSelection.setContentObject(contentObject);
            }
        }

        return contentSelection;
    }

    private long getTouchpointSelectionId(HttpServletRequest request) {
        long touchpointSelectionid = ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID, -1);
        return touchpointSelectionid;
    }

    private long getContentSelectionIdParam(HttpServletRequest request) {
        long contentSelectionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_SELECTION_ID, -1);
        return contentSelectionId;
    }

    @Override
    protected boolean suppressValidation(HttpServletRequest request, Object command) {

        String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, "");

        if (action.equals(SUBMIT_PARAM_CANCEL_AND_EXIT))
            return true;

        return super.suppressValidation(request, command);
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

        boolean initWorkingCopy = ServletRequestUtils.getBooleanParameter(request, REQ_PARM_INIT_WORKING_COPY, false);
        long modelId = ServletRequestUtils.getLongParameter(request, REQ_PARM_MODEL_ID, -1L);
        long contentSelectionId = getContentSelectionIdParam(request);
        TouchpointContentObjectContentSelection contentSelection = getTouchpointContentSelection(request);

        ContentObject message = null;
        if (modelId != -1)
            message = ContentObject.findById(modelId);

        if (initWorkingCopy && message != null && !message.hasWorkingData()) {

            // REDIRECT: Create working copy

            message.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
            User principal = UserUtil.getPrincipalUser();
            User requestor = User.findById(principal.getId());
            String userNote = ApplicationUtil.getMessage("page.text.fast.edit.working.copy.init");

            ServiceExecutionContext context = CheckoutFromProductionService.createContext(message, requestor, userNote);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CheckoutFromProductionService.SERVICE_NAME, CheckoutFromProductionService.class);
            service.execute(context);

            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {

                StringBuilder sb = new StringBuilder();
                sb.append(CheckoutFromProductionService.SERVICE_NAME);
                sb.append(" service call is not successful in ").append(this.getClass().getName());
                sb.append(" id ").append(modelId).append(" requestor=").append(requestor.getUsername()).append(" local text fast edit working copy not created. ");
                log.error(sb.toString());
                ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);

                Map<String, Object> parms = new HashMap<>();
                parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);

            } else {

                Map<String, Object> params = new HashMap<>();
                params.put(REQ_PARAM_CONTENT_SELECTION_ID, contentSelectionId);
                params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, contentSelection.getTouchpointSelection().getId());
                params.put(REQ_PARM_FAST_EDIT, true);

                return new ModelAndView(new RedirectView("touchpoint_content_selection_edit.form"), params);
            }

        } else if (contentSelection != null) {

            // SELECTION CONTEXT: Update to match current context
            TouchpointSelection selection = contentSelection.getTouchpointSelection();
            if (selection != null) {
                TouchpointSelection contextSelection = UserUtil.getCurrentSelectionContext();

                HashMap<String, String> contextAttr = new HashMap<>();
                contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_SELECTION_CONTEXT, String.valueOf(selection.getId()));
                UserUtil.updateUserContextAttributes(contextAttr);

                // Update selection context and reload
                if (contextSelection != selection){
                    String view = selection.isMaster() ? "../content/content_object_edit_content.form" : "touchpoint_content_selection_edit.form";
                    return new ModelAndView(new RedirectView(view), getSuccessViewParams(request, null));
                }
            }


        }

        return super.showForm(request, response, errors);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {

        AnalyticsEvent<AnalyticsGroupEvent> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.TouchpointContentSelectionEdit);

        try {
            String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, "");
            boolean isFastEdit = ServletRequestUtils.getBooleanParameter(request, REQ_PARM_FAST_EDIT, false);

            TouchpointContentSelectionEditWrapper wrapper = (TouchpointContentSelectionEditWrapper) commandObj;

            User requestor = UserUtil.getPrincipalUser();

            switch (action) {

                case (ACTION_UPDATE):
                case (SUBMIT_PARAM_SAVE):
                case (SUBMIT_PARAM_SAVE_AND_STAY):
                case (SUBMIT_PARAM_SAVE_AND_VIEW):
                case (SUBMIT_PARAM_SAVE_AND_GOTOLIST):
                case (SUBMIT_PARAM_SAVE_AND_GOBACK): {

                    analyticsEvent.setAction(Actions.Update);

                    ContentAssociationType oldContAssocType = new ContentAssociationType(wrapper.getContentSelection().getContentAssociationType().getId());
                    /**
                     * ACTION_UPDATE: - pick the first selected message and redirect to the edit page of that message instance
                     */
                    ServiceExecutionContext context = TouchpointContentSelectionUpdateService.createContext(wrapper, requestor, true, null, null, null, null);

                    Service updateContentService = MessagepointServiceFactory.getInstance().lookupService(TouchpointContentSelectionUpdateService.SERVICE_NAME, TouchpointContentSelectionUpdateService.class);
                    updateContentService.execute(context);

                    if (!context.getResponse().isSuccessful()) {
                        log.error(" unexpected exception when invoking TouchpointContentSelectionUpdateService execute method");
                        ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
                        return showForm(request, response, errors);
                    } else {
                        // Audit (Asset update: Message content edit)
                        this.auditContentUpdates(request, wrapper, oldContAssocType);

                        String current = request.getRequestURL().toString();
                        long contentObjectId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
                        long contentSelectionId = getContentSelectionIdParam(request);
                        long statusViewId = getStatusViewIdParam(request);


                        switch (action) {

                            case (ACTION_UPDATE): {

                                this.auditContentUpdates(request, wrapper, oldContAssocType);

                                if ( isFastEdit ) {
                                    Map<String, Object> params = new HashMap<>();
                                    params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
                                    return new ModelAndView(new RedirectView("../frameClose.jsp"),params);
                                } else {
                                    return new ModelAndView(new RedirectView(getReturnView(request)), getSuccessViewParams(request,wrapper));
                                }

                            }
                            case (SUBMIT_PARAM_SAVE_AND_STAY): {

                                Map<String, Object> params = new HashMap<>();
                                params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getBooleanParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, false));


                                params.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);
                                params.put(REQ_PARAM_CONTENT_SELECTION_ID, contentSelectionId);
                                params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, getTouchpointSelectionId(request));

                                params.put(REQ_PARAM_VIEW_ID, statusViewId);
                                params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                                return new ModelAndView(new RedirectView(current), params);

                            }
                            case (SUBMIT_PARAM_SAVE_AND_GOTOLIST): {

                                Map<String, Object> params = new HashMap<>();
                                params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "touchpoints/touchpoint_content_object_list.form"), params);

                            }
                            case (SUBMIT_PARAM_SAVE_AND_VIEW): {

                                Map<String, Object> params = new HashMap<>();
                                params.put(REQUEST_PARAM_SAVE_SUCCESS, true);
                                params.put(REQ_PARAM_VIEW_ID, statusViewId);
                                params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getBooleanParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, false));

                                params.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);
                                params.put(REQ_PARAM_CONTENT_SELECTION_ID, contentSelectionId);
                                params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, getTouchpointSelectionId(request));

                                return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "tpadmin/touchpoint_content_selection_view.form"), params);

                            }
                            case (SUBMIT_PARAM_SAVE_AND_GOBACK): {

                                String [] originForm = getOriginForm(request, true);
                                String origin = originForm[0];

                                if (origin.contains("content/content_object_edit_targeting.form") ||
                                        origin.contains("content/content_object_edit_content.form") ||
                                        origin.contains("content/content_object_edit.form")) {
                                    originForm = getOriginForm(request, true);
                                    origin = originForm[0];
                                }

                                if (!originForm[1].isEmpty())
                                    origin += "?" + originForm[1];

                                Map<String, Object> params = new HashMap<>();
                                params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

                                return new ModelAndView(new RedirectView(origin), params);

                            }
                            default:
                                break;

                        }
                    }
                }
                case (SUBMIT_PARAM_CANCEL_AND_EXIT): {
                    analyticsEvent.setAction(Actions.Cancel);

                    String [] originForm = getOriginForm(request, true);
                    String origin = originForm[0];

                    if (origin.contains("content/content_object_edit_targeting.form") ||
                            origin.contains("content/content_object_edit_content.form") ||
                            origin.contains("content/content_object_edit.form")) {
                        originForm = getOriginForm(request, true);
                        origin = originForm[0];
                    }

                    if (!originForm[1].isEmpty())
                        origin += "?" + originForm[1];

                    return new ModelAndView(new RedirectView(origin));
                }
                default:
                    break;
            }

            return null;
        } finally {
            analyticsEvent.send();
        }

    }

    private void auditContentUpdates(HttpServletRequest request, TouchpointContentSelectionEditWrapper wrapper, ContentAssociationType oldContAssocType) throws Exception {
        ContentObject mi = wrapper.getContentSelection().getContentObject();
        int actionType = AuditActionType.ID_MESSAGE_CONTENT_EDIT;

        if (mi.getIsTouchpointLocal()) {
            if (mi.getContentType().getId() == ContentType.TEXT) {
                actionType = AuditActionType.ID_LOCAL_SMART_TEXT_CONTENT_EDIT;
            } else if (mi.getContentType().getId() == ContentType.GRAPHIC) {
                actionType = AuditActionType.ID_LOCAL_IMAGE_CONTENT_EDIT;
            }
        }
        String auditResult = null, fromType = null, toType = null;
        TouchpointSelection touchpointSelection = wrapper.getContentSelection().getTouchpointSelection();
        if (touchpointSelection != null) {
            auditResult = touchpointSelection.getNameWithParentInfo();
        }

        if (oldContAssocType.getId() != wrapper.getContentType().getId()) {
            fromType = oldContAssocType.getName();
            toType = new ContentAssociationType(wrapper.getContentType().getId()).getName();
        }
        AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, AuditObjectType.ID_MESSAGE, mi != null ? mi.getName() : null, mi != null ? mi.getId() : null,
                actionType, (auditResult != null ? AuditMetadataBuilder.forSimpleAuditMessage(auditResult, fromType, toType) : null));

    }

    private String getReturnView(HttpServletRequest request) {
        long continueId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTINUE_ID, -1);
        long masterVariantId = ServletRequestUtils.getLongParameter(request, REQ_PARM_MASTER_VARIANT_ID, -1);

        if (continueId != -1L && continueId != ParameterGroupTreeNode.MASTER_VARIANCE_ID && continueId != masterVariantId) {
            return "touchpoint_content_selection_edit.form";
        } else if (continueId == ParameterGroupTreeNode.MASTER_VARIANCE_ID || continueId == masterVariantId) {
            return "../content/content_object_edit_content.form";
        }

        return getSuccessView();
    }

    private Map<String, Object> getSuccessViewParams(HttpServletRequest request, TouchpointContentSelectionEditWrapper wrapper) {

        Map<String, Object> params = new HashMap<>();

        boolean isFastEdit = ServletRequestUtils.getBooleanParameter(request, REQ_PARM_FAST_EDIT, false);
        long touchpointSelectionId = getTouchpointSelectionId(request);
        long continueId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTINUE_ID, -1);
        long contentObjectId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
        String objectDna = ServletRequestUtils.getStringParameter(request, ContentObject.REQ_PARM_OBJECT_DNA, null);

        params.put(ContentObject.REQ_PARM_CONTENT_OBJECT_ID, contentObjectId);

        if (isFastEdit)
            params.put(REQ_PARM_FAST_EDIT, isFastEdit);

        if (getReturnViewTypeParam(request).equals(RETURN_VIEW_TYPE_LIST) && wrapper != null) {
            TouchpointSelection touchpointSelection = wrapper.getContentSelection().getTouchpointSelection();
            params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, touchpointSelection.getId());
            params.put(TouchpointContentObjectListController.REQ_PARM_VIEWID, HttpRequestUtil.getTpSelectionViewType(request));
        } else {
            long contentSelectionId = getContentSelectionIdParam(request);
            if (contentSelectionId != -1L) {
                params.put(REQ_PARAM_CONTENT_SELECTION_ID, contentSelectionId);
                params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, touchpointSelectionId);
                params.put(TouchpointContentSelectionViewController.REQ_PARAM_VIEW_ID, SelectionStatusFilterType.ID_WORKING_COPIES);
            }
        }

        if (touchpointSelectionId != -1 || continueId != -1L) {
            params.put(REQ_PARAM_TOUCHPOINT_SELECTION_ID, continueId == -1L ? touchpointSelectionId : continueId);
        }

        if (objectDna != null) {
            params.put(ContentObject.REQ_PARM_OBJECT_DNA, objectDna);
        }

        return params;
    }

    private String getReturnViewTypeParam(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, REQ_PARM_RETURN_VIEW, "view");
    }

    public int getStatusViewIdParam(HttpServletRequest request) {
        int viewIdParam = ServletRequestUtils.getIntParameter(request, REQ_PARAM_VIEW_ID, -1);
        if (viewIdParam > 0) {
            HttpRequestUtil.saveTpSelectionViewType(request, viewIdParam);
            return viewIdParam;
        } else {
            return HttpRequestUtil.getTpSelectionViewType(request);
        }
    }

}