package com.prinova.messagepoint.controller.tpadmin;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataElementVariableComparator;

public class DocumentExtendedReportingVariablesWrapper implements Serializable{

	private static final long serialVersionUID = -2526588383459857342L;

	private List<Long>				selectedExtReportingVariableIds = new ArrayList<>();
	private Document				document;
	private DataElementVariable customerReportingVariableA;
	private DataElementVariable customerReportingVariableB;
	
	// For Audit
	private List<Long>				orgSelectedExtReportingVariableIds = new ArrayList<>();
	private String					customerReportingVariableAName;
	private String					customerReportingVariableBName;
	
	public DataElementVariable getCustomerReportingVariableA(){
		return customerReportingVariableA;
	}
	public void setCustomerReportingVariableA(DataElementVariable customerReportingVariableA){
		this.customerReportingVariableA = customerReportingVariableA;
	}
	public DataElementVariable getCustomerReportingVariableB(){
		return customerReportingVariableB;
	}
	public void setCustomerReportingVariableB(DataElementVariable customerReportingVariableB){
		this.customerReportingVariableB = customerReportingVariableB;
	}

	public DocumentExtendedReportingVariablesWrapper(){
		super();
	}
	
	public DocumentExtendedReportingVariablesWrapper(Document document){
		super();
		
		this.document = document;
		if (document != null) {
			this.customerReportingVariableA = document.getCustomerReportingVariableA();
			if(document.getCustomerReportingVariableA() != null)
				this.customerReportingVariableAName = document.getCustomerReportingVariableA().getName();
			this.customerReportingVariableB = document.getCustomerReportingVariableB();
			if(document.getCustomerReportingVariableB() != null)
				this.customerReportingVariableBName = document.getCustomerReportingVariableB().getName();
			List<DataElementVariable> selectedVariables = new ArrayList<>(document.getExtReportingDataVariables());
			Collections.sort(selectedVariables, new DataElementVariableComparator());
	
			for (DataElementVariable currentVariable: selectedVariables){
				selectedExtReportingVariableIds.add(currentVariable.getId());
				orgSelectedExtReportingVariableIds.add(currentVariable.getId());
			}
		}
	}

	public List<Long> getSelectedExtReportingVariableIds() {
		return selectedExtReportingVariableIds;
	}
	public void setSelectedExtReportingVariableIds(List<Long> selectedExtReportingVariableIds) {
		this.selectedExtReportingVariableIds = selectedExtReportingVariableIds;
	}

	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	public List<Long> getOrgSelectedExtReportingVariableIds() {
		return orgSelectedExtReportingVariableIds;
	}
	public void setOrgSelectedExtReportingVariableIds(List<Long> orgSelectedExtReportingVariableIds) {
		this.orgSelectedExtReportingVariableIds = orgSelectedExtReportingVariableIds;
	}
	public String getCustomerReportingVariableAName() {
		return customerReportingVariableAName;
	}
	public void setCustomerReportingVariableAName(String customerReportingVariableAName) {
		this.customerReportingVariableAName = customerReportingVariableAName;
	}
	public String getCustomerReportingVariableBName() {
		return customerReportingVariableBName;
	}
	public void setCustomerReportingVariableBName(String customerReportingVariableBName) {
		this.customerReportingVariableBName = customerReportingVariableBName;
	}

}
