package com.prinova.messagepoint.controller.tpadmin;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.TouchpointAdminEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.testing.TestingUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.RemoveStyleCustomizationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointStyleCustomizationsController extends MessagepointController {
	private static final Log log = LogUtil.getLog(TouchpointStyleCustomizationsController.class);
	
	public static final String 	REQ_PARAM_ACTION 				= "action";
	public static final String	REQ_PARAM_DOCUMENTID			= "documentId";
	
	public static final int ACTION_REMOVE_TEXT_STYLE_CUST		= 1;
	public static final int ACTION_REMOVE_PARA_STYLE_CUST		= 3;
	public static final int ACTION_REMOVE_LIST_STYLE_CUST		= 5;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1);
		Document rootDocument = Document.findById(documentId);
		referenceData.put("channelContext", UserUtil.getCurrentChannelDocumentContext(rootDocument));
		
		referenceData.put("returnFromSetupPagePath", UserUtil.getReturnFromSetupPageContextPath());
		referenceData.put("fontfaceCSS", TextStyleFont.getAllFontFaceCSS());
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1);
		Document rootDocument = Document.findById(documentId);
		
		long channelContextId = -1L;
		if ( rootDocument != null )
			channelContextId = UserUtil.getCurrentChannelDocumentContext(rootDocument).getId();
				
		return new TouchpointStyleCustomizationsWrapper(channelContextId);
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

		setOriginForm(request, null);
		return super.showForm(request, response, errors);

	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {

		AnalyticsEvent<TouchpointAdminEvents> analyticsEvent = AnalyticsUtil.requestFor(TouchpointAdminEvents.TouchpointStyleCustomizations);

		try {
			TouchpointStyleCustomizationsWrapper command = (TouchpointStyleCustomizationsWrapper)commandObj;

			int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
			long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1);

			Document rootDocument = Document.findById(documentId);

			long channelContextId = -1L;
			if ( rootDocument != null )
				channelContextId = UserUtil.getCurrentChannelDocumentContext(rootDocument).getId();

			switch (action) {
				case ACTION_REMOVE_TEXT_STYLE_CUST:{

					analyticsEvent.setAction(Actions.RemoveTextStyleCustomizations);


					ServiceExecutionContext context = RemoveStyleCustomizationService.createContext(channelContextId, command.getSelectedTextStyleList().get(0).getId(), TextStyle.class);
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(RemoveStyleCustomizationService.SERVICE_NAME, RemoveStyleCustomizationService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(RemoveStyleCustomizationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> parms = TestingUtils.getContextMapParms(request);
						parms.put(REQ_PARAM_DOCUMENTID, documentId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case ACTION_REMOVE_PARA_STYLE_CUST:{

					analyticsEvent.setAction(Actions.RemoveParagraphStyleCustomizations);


					ServiceExecutionContext context = RemoveStyleCustomizationService.createContext(channelContextId, command.getSelectedParaList().get(0).getId(), ParagraphStyle.class);
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(RemoveStyleCustomizationService.SERVICE_NAME, RemoveStyleCustomizationService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(RemoveStyleCustomizationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> parms = TestingUtils.getContextMapParms(request);
						parms.put(REQ_PARAM_DOCUMENTID, documentId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
				case ACTION_REMOVE_LIST_STYLE_CUST:{
					analyticsEvent.setAction(Actions.RemoveListStyleCustomizations);

					ServiceExecutionContext context = RemoveStyleCustomizationService.createContext(channelContextId, command.getSelectedListStyleList().get(0).getId(), ListStyle.class);
					Service service = MessagepointServiceFactory.getInstance()
							.lookupService(RemoveStyleCustomizationService.SERVICE_NAME, RemoveStyleCustomizationService.class);
					service.execute(context);
					ServiceResponse serviceResponse = context.getResponse();
					if (!serviceResponse.isSuccessful()) {
						StringBuilder sb = new StringBuilder();
						sb.append(RemoveStyleCustomizationService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						Map<String, Object> parms = TestingUtils.getContextMapParms(request);
						parms.put(REQ_PARAM_DOCUMENTID, documentId);
						return new ModelAndView(new RedirectView(getSuccessView()), parms);
					}
				}
			}

			return null;
		} finally {
			analyticsEvent.send();
		}
	}
}
