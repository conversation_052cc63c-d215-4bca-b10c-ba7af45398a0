package com.prinova.messagepoint.controller.tpadmin;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.dataadmin.LookupTable;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.testing.DataFile;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.common.SyncObjectType;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.util.SyncDifferenceVO;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.model.version.VersionedInstance;
import com.prinova.messagepoint.model.version.VersionedModel;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;

public class TouchpointSyncDifferencesListController extends MessagepointController {
	public static final String PARM_ACTION 						= "action";
	public static final String PARAM_INSTANCE_ID 				= "instanceId";
	public static final String PARAM_COMPARE_TO_ID				= "compareToId";
	public static final String PARAM_COMPARE_TO_NODE_ID			= "compareToNodeId";
	public static final String PARAM_DOCUMENT_ID                = "documentId";
	public static final String PARAM_OTHER_DOCUMENT_ID          = "otherDocumentId";
    public static final String PARAM_OBJECT_TYPE_ID				= "objectType";
    public static final String PARAM_DIRECTION					= "direction";
	public static final String PARAM_SYNC_FROM_ORGIN			= "syncFromOrigin";
	public static final String PARAM_SYNC_DATA_TYPE_ID          = "dataType";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		long instanceId = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1L);
		long compareToId = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_ID, -1L);
		long nodeId = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_NODE_ID, -1L);
		long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1L);
        long otherDocumentId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_DOCUMENT_ID, -1L);
		String compareObjectType = ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE_ID, "message");
		String compareDataType = ServletRequestUtils.getStringParameter(request, PARAM_SYNC_DATA_TYPE_ID, "0");
		String direction = ServletRequestUtils.getStringParameter(request, PARAM_DIRECTION, "update");
		boolean isUpdating = direction.equalsIgnoreCase("update");
		String syncFromOrigin = ServletRequestUtils.getStringParameter(request, PARAM_SYNC_FROM_ORGIN, "yes");

		String schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		if(nodeId == -1) { 
			schema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		} else {
		    Node otherInstance = Node.findById(nodeId);
		    if(otherInstance != null) {
		    	schema = otherInstance.getSchemaName();
		    } else {
		    	otherInstance = Node.findById(nodeId);
		    }
		}
		
		String compareToSchema = schema;

		SyncObjectType objectType = null;
        
		VersionedInstance instance = null;
        VersionedInstance otherInstance = null;

		VersionedModel model = null;
        VersionedModel otherModel = null;
		
		TargetGroup targetGroup = null;
		TargetGroup otherTargetGroup = null;
		
		ConditionElement targetRule = null;
		ConditionElement otherTargetRule = null;
		
		ParameterGroup parameterGroup = null;
		ParameterGroup otherParameterGroup = null;

        DocumentSettingsModel documentSettingsModel = null;
        DocumentSettingsModel otherDocumentSettingsModel = null;

        DataElementVariable variable = null;
        DataElementVariable otherVariable = null;

        DataSource dataSource = null;
        DataSource otherDataSource = null;

        DataSourceAssociation dataSourceAssociation = null;
        DataSourceAssociation otherDataSourceAssociation = null;

        LookupTable lookupTable = null;
        LookupTable otherLookupTable = null;

        CompositionFileSet compositionFileSet = null;
        CompositionFileSet otherCompositionFileSet = null;

        DataFile dataFile = null;
        DataFile otherDataFile = null;

        TouchpointSelection touchpointSelection = null;
        TouchpointSelection otherTouchpointSelection = null;

        ListStyle listStyle = null;
        ListStyle otherListStyle = null;

        ParagraphStyle paragraphStyle = null;
        ParagraphStyle otherParagraphStyle = null;

        MetadataFormDefinition metadataFormDefinition = null;
        MetadataFormDefinition otherMetadataFormDefinition = null;

        boolean hasContent = true;
		
		if(compareObjectType.equalsIgnoreCase("message") ||
                compareObjectType.equalsIgnoreCase("localSmartText") ||
                compareObjectType.equalsIgnoreCase("localImage") ||
                compareObjectType.equalsIgnoreCase("smartText") ||
                compareObjectType.equalsIgnoreCase("image")
        ) {
		    int contentObjectDataTypeId = Integer.parseInt(compareDataType);

            model = ContentObject.findById(instanceId);
            otherModel = CloneHelper.queryInSchema(compareToSchema, ()->ContentObject.findById(compareToId));

            ContentObject contentObject = (ContentObject) model;

            instance = ((ContentObject)model).getContentObjectData(contentObjectDataTypeId);
            {
                ContentObject otherContentObject = (ContentObject) otherModel;
                otherInstance = CloneHelper.queryInSchema(compareToSchema, () -> otherContentObject.getContentObjectData(contentObjectDataTypeId));
            }

            Map<String, Object> messageAttributesMap = SyncTouchpointUtil.getContentObjectAttributes(model.getId(), null);
            Map<String, Object> messageInstanceAttributesMap = SyncTouchpointUtil.getContentObjectDataAttributes(model.getId(), contentObjectDataTypeId, null);

            Map<String, Object> otherMessageAttributesMap = SyncTouchpointUtil.getContentObjectAttributes(otherModel.getId(), compareToSchema);
            Map<String, Object> otherMessageInstanceAttributesMap = SyncTouchpointUtil.getContentObjectDataAttributes(otherModel.getId(), contentObjectDataTypeId, compareToSchema);
            
            Map<String, Object> attributesMap = new HashMap<>();
            attributesMap.putAll(messageAttributesMap);
            attributesMap.putAll(messageInstanceAttributesMap);
            
            Map<String, Object> otherAttributesMap = new HashMap<>();
            otherAttributesMap.putAll(otherMessageAttributesMap);
            otherAttributesMap.putAll(otherMessageInstanceAttributesMap);
            
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(attributesMap, otherAttributesMap);

            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);

            if (contentObject.isLocalSmartText()) {
                objectType = new SyncObjectType(SyncObjectType.ID_LOCAL_SMART_TEXT);
            }
            else if (contentObject.isLocalImage()) {
                objectType = new SyncObjectType(SyncObjectType.ID_LOCAL_IMAGE);
            }
            else if (contentObject.isGlobalSmartText()) {
                objectType = new SyncObjectType(SyncObjectType.ID_SMART_TEXT);
            }
            else if (contentObject.isGlobalImage()) {
                objectType = new SyncObjectType(SyncObjectType.ID_CONTENT_LIBRARY);
            } else {
                objectType = new SyncObjectType(SyncObjectType.ID_MESSAGE);
            }

            referenceData.put("contentObject", contentObject);
            referenceData.put("isdynamic", contentObject.isDynamicVariantEnabled());
            referenceData.put("isstrucutred", contentObject.isStructuredContentEnabled());
            referenceData.put("objectName", contentObject.getName());

            hasContent = true;
		} else if(compareObjectType.equalsIgnoreCase("targetGroup")) {
            objectType = new SyncObjectType(SyncObjectType.ID_TARGET_GROUP);
            instance = null;
            otherInstance = null;
            targetGroup = TargetGroup.findById(instanceId);
            otherTargetGroup = CloneHelper.queryInSchema(compareToSchema, ()->TargetGroup.findById(compareToId));
            Map<String, Object> targetGroupAttributesMap = SyncTouchpointUtil.getTargetGroupAttributes(targetGroup.getId(), null);
            Map<String, Object> otherTargetGroupAttributesMap = SyncTouchpointUtil.getTargetGroupAttributes(otherTargetGroup.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(targetGroupAttributesMap, otherTargetGroupAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", targetGroup.getName());

            hasContent = false;
		} else if(compareObjectType.equalsIgnoreCase("targetRule")) {
            objectType = new SyncObjectType(SyncObjectType.ID_TARGET_RULE);
            instance = null;
            otherInstance = null;
            targetRule = ConditionElement.findById(instanceId);
            otherTargetRule = CloneHelper.queryInSchema(compareToSchema, ()->ConditionElement.findById(compareToId));
            Map<String, Object> targetRuleAttributesMap = SyncTouchpointUtil.getTargetRuleAttributes(targetRule.getId(), null);
            Map<String, Object> otherTargetRuleAttributesMap = SyncTouchpointUtil.getTargetRuleAttributes(otherTargetRule.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(targetRuleAttributesMap, otherTargetRuleAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", targetRule.getName());
            hasContent = false;
		} else if(compareObjectType.equalsIgnoreCase("parameterGroup")) {
            objectType = new SyncObjectType(SyncObjectType.ID_PARAMETER_GROUP);
            instance = null;
            otherInstance = null;
            parameterGroup = ParameterGroup.findById(instanceId);
            otherParameterGroup = CloneHelper.queryInSchema(compareToSchema, ()->ParameterGroup.findById(compareToId));
            Map<String, Object> parameterGroupAttributesMap = SyncTouchpointUtil.getParameterGroupAttributes(parameterGroup.getId(), null);
            Map<String, Object> otherParameterGroupAttributesMap = SyncTouchpointUtil.getParameterGroupAttributes(otherParameterGroup.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(parameterGroupAttributesMap, otherParameterGroupAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", parameterGroup.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("documentSettings")) {
            objectType = new SyncObjectType(SyncObjectType.ID_DOCUMENT_SETTING);
            instance = null;
            otherInstance = null;

            Document document = Document.findById(documentId);
            Document otherDocument = CloneHelper.queryInSchema(compareToSchema, ()->Document.findById(otherDocumentId));

            Map<String, Object> attributesMap = new HashMap<>();
            Map<String, Object> otherAttributesMap = new HashMap<>();

            if(instanceId == DocumentSettingsModel.ID_SETTING_CONNECTED_INTERVIEW) {
                attributesMap = document.getConnectedInterviewSettingsMap();
                otherAttributesMap = CloneHelper.queryInSchema(compareToSchema, ()->otherDocument.getConnectedInterviewSettingsMap());
            }
            else if(instanceId == DocumentSettingsModel.ID_SETTING_CHANNEL_TEMPLATE) {
                attributesMap = document.getLayoutTemplateAttributesMap();
                otherAttributesMap = CloneHelper.queryInSchema(compareToSchema, ()->otherDocument.getLayoutTemplateAttributesMap());
            }
            else if(instanceId == DocumentSettingsModel.ID_SETTING_TOUCHPOINT_METADATA) {
                attributesMap = document.getTouchpointMetadataSettingsMap();
                otherAttributesMap = CloneHelper.queryInSchema(compareToSchema, ()->otherDocument.getTouchpointMetadataSettingsMap());
            }
            else if(instanceId == DocumentSettingsModel.ID_SETTING_VARIANT_METADATA_TEMPLATE) {
                attributesMap = document.getVariantMetadataSettingsMap();
                otherAttributesMap = CloneHelper.queryInSchema(compareToSchema, ()->otherDocument.getVariantMetadataSettingsMap());
            }

            documentSettingsModel = SyncTouchpointUtil.getPsuedoDocumentSettingsObject((int) instanceId, document, attributesMap);
            Map<String, Object> otherAttributesMapFinal = otherAttributesMap;
            otherDocumentSettingsModel = CloneHelper.queryInSchema(compareToSchema, ()->SyncTouchpointUtil.getPsuedoDocumentSettingsObject((int) compareToId, otherDocument, otherAttributesMapFinal));

            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(attributesMap, otherAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", SyncTouchpointUtil.getPsuedoDocumentSettingsName((int) instanceId));
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("variable")) {
            objectType = new SyncObjectType(SyncObjectType.ID_VARIABLE);
            instance = null;
            otherInstance = null;

            variable = DataElementVariable.findById(instanceId);
            otherVariable = CloneHelper.queryInSchema(compareToSchema, ()->DataElementVariable.findById(compareToId));
            Map<String, Object> variableAttributesMap = SyncTouchpointUtil.getVariableAttributes(variable.getId(), documentId, null);
            Map<String, Object> otherVariableAttributesMap = SyncTouchpointUtil.getVariableAttributes(otherVariable.getId(), otherDocumentId, compareToSchema);
            DataElementVariable otherVariableFinal = otherVariable;
            int usageFlags = variable.getUsageFlags();
            int otherUsageFlags = CloneHelper.queryInSchema(compareToSchema, ()->otherVariableFinal.getUsageFlags());
            int sourceUsageFlags = isUpdating ? otherUsageFlags : usageFlags;
            int targetUsageFlags = isUpdating ? usageFlags : otherUsageFlags;
            int newUsageFlags = sourceUsageFlags & (sourceUsageFlags ^ targetUsageFlags);
            if(newUsageFlags != 0) {
                Map<String, Object> usageDifferences = DataElementVariable.getUsageAttributesMap(newUsageFlags);
                if(isUpdating) otherVariableAttributesMap.putAll(usageDifferences);
                else variableAttributesMap.putAll(usageDifferences);
            }
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(variableAttributesMap, otherVariableAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", variable.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("dataSource")) {
		    objectType = new SyncObjectType(SyncObjectType.ID_DATASOURCE);
            instance = null;
            otherInstance = null;

            dataSource = DataSource.findById(instanceId);
            otherDataSource = CloneHelper.queryInSchema(compareToSchema, ()->DataSource.findById(compareToId));
            Map<String, Object> dataSourceAttributesMap = SyncTouchpointUtil.getDataSourceAttributes(dataSource.getId(), null);
            Map<String, Object> otherDataSourceAttributesMap = SyncTouchpointUtil.getDataSourceAttributes(otherDataSource.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(dataSourceAttributesMap, otherDataSourceAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", dataSource.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("dataCollection")) {
            objectType = new SyncObjectType(SyncObjectType.ID_DATACOLLECTION);
            instance = null;
            otherInstance = null;

            dataSourceAssociation = DataSourceAssociation.findById(instanceId);
            otherDataSourceAssociation = CloneHelper.queryInSchema(compareToSchema, ()->DataSourceAssociation.findById(compareToId));
            Map<String, Object> dataSourceAssociationAttributesMap = SyncTouchpointUtil.getDataSourceAssociationAttributes(dataSourceAssociation.getId(), null);
            Map<String, Object> otherDataSourceAssociationAttributesMap = SyncTouchpointUtil.getDataSourceAssociationAttributes(otherDataSourceAssociation.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(dataSourceAssociationAttributesMap, otherDataSourceAssociationAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", dataSourceAssociation.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("lookupTable")) {
            int objectDataTypeId = Integer.parseInt(compareDataType);

            objectType = new SyncObjectType(SyncObjectType.ID_LOOKUPTABLE);

            model = LookupTable.findById(instanceId);
            otherModel = CloneHelper.queryInSchema(compareToSchema, ()-> {
                        return LookupTable.findById(compareToId);
                    });

            instance = model == null ?
                    LookupTableInstance.findById(instanceId) :
                    (
                            (int) objectDataTypeId == ContentObject.DATA_TYPE_WORKING ?
                                    ((LookupTable) model).getWorkingCopy() :
                                    ((LookupTable) model).getLatestProductionCentric()
                    );
            {
                VersionedModel otherModelFinal = otherModel;
                otherInstance = CloneHelper.queryInSchema(compareToSchema, ()->otherModelFinal == null ?
                        LookupTableInstance.findById(compareToId) :
                        (
                                (int) objectDataTypeId == ContentObject.DATA_TYPE_WORKING ?
                                        ((LookupTable) otherModelFinal).getWorkingCopy() :
                                        ((LookupTable) otherModelFinal).getLatestProductionCentric())
                        );
            }
            model = instance.getModel();
            {
                long otherInstanceId = otherInstance.getId();
                otherModel = CloneHelper.queryInSchema(compareToSchema, () -> {
                    LookupTableInstance otherInstanceFinal = LookupTableInstance.findById(otherInstanceId);
                    return otherInstanceFinal.getModel();
                });
            }

            Map<String, Object> lookupTableAttributesMap = SyncTouchpointUtil.getLookupTableAttributes(model.getId(), null);
            Map<String, Object> instanceAttributesMap = SyncTouchpointUtil.getLookupTableInstanceAttributes(instance.getId(), null);

            Map<String, Object> otherLookupTableAttributesMap = SyncTouchpointUtil.getLookupTableAttributes(otherModel.getId(), compareToSchema);
            Map<String, Object> otherInstanceAttributesMap = SyncTouchpointUtil.getLookupTableInstanceAttributes(otherInstance.getId(), compareToSchema);

            Map<String, Object> attributesMap = new HashMap<>();
            attributesMap.putAll(lookupTableAttributesMap);
            attributesMap.putAll(instanceAttributesMap);

            Map<String, Object> otherAttributesMap = new HashMap<>();
            otherAttributesMap.putAll(otherLookupTableAttributesMap);
            otherAttributesMap.putAll(otherInstanceAttributesMap);

            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(attributesMap, otherAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);

            referenceData.put("isdynamic", false);
            referenceData.put("isstrucutred", false);

            referenceData.put("objectName", instance.getName());

            hasContent = true;

            lookupTable = (LookupTable) model;
        } else if(compareObjectType.equalsIgnoreCase("compositionPackage")) {
            objectType = new SyncObjectType(SyncObjectType.ID_COMPOSITION_PACKAGE);
            instance = null;
            otherInstance = null;

            compositionFileSet = CompositionFileSet.findById(instanceId);
            otherCompositionFileSet = CloneHelper.queryInSchema(compareToSchema, ()->CompositionFileSet.findById(compareToId));
            Map<String, Object> compositionFileSetAttributesMap = SyncTouchpointUtil.getCompositionFileSetAttributes(compositionFileSet.getId(), null);
            Map<String, Object> otherCompositionFileSetAttributesMap = SyncTouchpointUtil.getCompositionFileSetAttributes(otherCompositionFileSet.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(compositionFileSetAttributesMap, otherCompositionFileSetAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", compositionFileSet.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("dataFile")) {
            objectType = new SyncObjectType(SyncObjectType.ID_DATA_FILE);
            instance = null;
            otherInstance = null;

            dataFile = DataFile.findById(instanceId);
            otherDataFile = CloneHelper.queryInSchema(compareToSchema, ()->DataFile.findById(compareToId));
            Map<String, Object> dataFileSetAttributesMap = SyncTouchpointUtil.getDataFileAttributesMap(dataFile.getId(), null);
            Map<String, Object> otherDataFileAttributesMap = SyncTouchpointUtil.getDataFileAttributesMap(otherDataFile.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(dataFileSetAttributesMap, otherDataFileAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", dataFile.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("touchpointSelection")
            || compareObjectType.equalsIgnoreCase("variant")
            || compareObjectType.equalsIgnoreCase("touchpointVariant")
        ) {
            objectType = new SyncObjectType(SyncObjectType.ID_VARIANT);
            instance = null;
            otherInstance = null;

            touchpointSelection = TouchpointSelection.findById(instanceId);
            otherTouchpointSelection = CloneHelper.queryInSchema(compareToSchema, ()->TouchpointSelection.findById(compareToId));
            Map<String, Object> touchpointSelectionAttributesMap = SyncTouchpointUtil.getTouchpointSelectionAttributesMap(touchpointSelection.getId(), null);
            Map<String, Object> otherTouchpointSelectionAttributesMap = SyncTouchpointUtil.getTouchpointSelectionAttributesMap(otherTouchpointSelection.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(touchpointSelectionAttributesMap, otherTouchpointSelectionAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", touchpointSelection.getName());
            hasContent = false;
            if(touchpointSelection.getDocument().isEnabledForVariantWorkflow() && touchpointSelection.getHasWorkingCopy()) {
                TouchpointSelection otherTouchpointSelectionFinal = otherTouchpointSelection;
                if(CloneHelper.queryInSchema(compareToSchema, ()->otherTouchpointSelectionFinal.getDocument().isEnabledForVariantWorkflow() && otherTouchpointSelectionFinal.getHasWorkingCopy())) {
                    hasContent = true;
                }

            }
        } else if(compareObjectType.equalsIgnoreCase("listStyle")) {
            objectType = new SyncObjectType(SyncObjectType.ID_LIST_STYLE);
            instance = null;
            otherInstance = null;

            listStyle = ListStyle.findById(instanceId);
            otherListStyle = CloneHelper.queryInSchema(compareToSchema, ()->ListStyle.findById(compareToId));
            Map<String, Object> listStyleSetAttributesMap = SyncTouchpointUtil.getListStyleAttributesMap(listStyle.getId(), null);
            Map<String, Object> otherListStyleAttributesMap = SyncTouchpointUtil.getListStyleAttributesMap(otherListStyle.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(listStyleSetAttributesMap, otherListStyleAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", listStyle.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("paragraphStyle")) {
            objectType = new SyncObjectType(SyncObjectType.ID_PARAGRAPH_STYLE);
            instance = null;
            otherInstance = null;

            paragraphStyle = ParagraphStyle.findById(instanceId);
            otherParagraphStyle = CloneHelper.queryInSchema(compareToSchema, ()->ParagraphStyle.findById(compareToId));
            Map<String, Object> paragraphStyleAttributesMap = SyncTouchpointUtil.getParagraphStyleAttributesMap(paragraphStyle.getId(), null);
            Map<String, Object> otherParagraphStyleAttributesMap = SyncTouchpointUtil.getParagraphStyleAttributesMap(otherParagraphStyle.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(paragraphStyleAttributesMap, otherParagraphStyleAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", paragraphStyle.getName());
            hasContent = false;
        } else if(compareObjectType.equalsIgnoreCase("MetadataFormTemplate")) {
            objectType = new SyncObjectType(SyncObjectType.ID_METADATAFORM_DEFINITION);
            instance = null;
            otherInstance = null;

            metadataFormDefinition = MetadataFormDefinition.findById(instanceId);
            otherMetadataFormDefinition = CloneHelper.queryInSchema(compareToSchema, ()->MetadataFormDefinition.findById(compareToId));
            Map<String, Object> metadataFormDefinitionAttributesMap = SyncTouchpointUtil.getMetadataFormDefinitionAttributesMap(metadataFormDefinition.getId(), null);
            Map<String, Object> otherMetadataFormDefinitionAttributesMap = SyncTouchpointUtil.getMetadataFormDefinitionAttributesMap(otherMetadataFormDefinition.getId(), compareToSchema);
            List<SyncDifferenceVO> differences = SyncTouchpointUtil.findDifferentAttributes(metadataFormDefinitionAttributesMap, otherMetadataFormDefinitionAttributesMap);
            Collections.sort(differences, (d1, d2)->d1.getAttributeDisplayText().compareTo(d2.getAttributeDisplayText()));
            referenceData.put("differences", differences);
            referenceData.put("objectName", metadataFormDefinition.getName());
            hasContent = false;
        }

		if(model instanceof ContentObject) {
		    ContentObject contentObject = (ContentObject) model;
            referenceData.put("defaultViewCSSFilePath", contentObject.getDefaultViewCSSFilePath());
            if(! contentObject.getStyles().isEmpty()) {
                referenceData.put("CSSFilename", contentObject.getCSSFilename());
            }
            if(! contentObject.getParagraphStyles().isEmpty()) {
                referenceData.put("paragraphCSSFilename", contentObject.getParagraphCSSFilename());
            }
            referenceData.put("languages", contentObject.getContentObjectLanguagesAsLocales());
        }
        else {
            referenceData.put("languages", TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales());
        }

		if(model != null) referenceData.put("model", model);
		if(instance != null) referenceData.put("instance", instance);
		
		if(targetGroup != null) {
			referenceData.put("targetGroup", targetGroup);
			referenceData.put("model", targetGroup);
			referenceData.put("instance", targetGroup);
		}
		
		if(targetRule != null) {
			referenceData.put("targetGroup", targetRule);
			referenceData.put("model", targetRule);
			referenceData.put("instance", targetRule);
		}
		
		if(parameterGroup != null) {
			referenceData.put("parameterGroup", parameterGroup);
			referenceData.put("model", parameterGroup);
			referenceData.put("instance", parameterGroup);
		}

		if(documentSettingsModel != null) {
            referenceData.put("documentSettingsModel", documentSettingsModel);
            referenceData.put("model", documentSettingsModel);
            referenceData.put("instance", documentSettingsModel);
        }

        if(variable != null) {
            referenceData.put("variable", variable);
            referenceData.put("model", variable);
            referenceData.put("instance", variable);
        }

        if(dataSource != null) {
            referenceData.put("dataSource", dataSource);
            referenceData.put("model", dataSource);
            referenceData.put("instance", dataSource);
        }

        if(dataSourceAssociation != null) {
            referenceData.put("dataCollection", dataSourceAssociation);
            referenceData.put("model", dataSourceAssociation);
            referenceData.put("instance", dataSourceAssociation);
        }

        if(lookupTable != null) {
            referenceData.put("lookupTable", lookupTable);
            referenceData.put("model", lookupTable);
            referenceData.put("instance", lookupTable);
        }

        if(dataFile != null) {
            referenceData.put("dataFile", dataFile);
            referenceData.put("model", dataFile);
            referenceData.put("instance", dataFile);
        }

        if(touchpointSelection != null) {
            referenceData.put("touchpointSelection", touchpointSelection);
            referenceData.put("model", touchpointSelection);
            referenceData.put("instance", touchpointSelection);
        }

        if(listStyle != null) {
            referenceData.put("listStyle", listStyle);
            referenceData.put("model", listStyle);
            referenceData.put("instance", listStyle);
        }

        if(paragraphStyle != null) {
            referenceData.put("paragraphStyle", paragraphStyle);
            referenceData.put("model", paragraphStyle);
            referenceData.put("instance", paragraphStyle);
        }

        if(metadataFormDefinition != null) {
            referenceData.put("metadataFormDefinition", metadataFormDefinition);
            referenceData.put("model", metadataFormDefinition);
            referenceData.put("instance", metadataFormDefinition);
        }

        referenceData.put("compareToObjectId", compareToId);
		referenceData.put("compareToNodeId", nodeId);
		
        referenceData.put("compareObjectType", compareObjectType);
		referenceData.put("objectType", objectType);
		referenceData.put(PARAM_DIRECTION, direction);
		referenceData.put(PARAM_SYNC_FROM_ORGIN, syncFromOrigin);

        referenceData.put("hasContent", hasContent);

		return referenceData;
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new TouchpointSyncContentCompareWrapper();
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
			BindException errors) throws Exception {
		return null;
	}
}
