package com.prinova.messagepoint.controller.tpadmin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataSourceAssociation;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTouchpointDataSourceAssociationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;

/**
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 * 
 * TouchpointDataSourceEditController
 *
 * 2011-01-24 - 2:17:08 PM
 * 
 * <AUTHOR>
 */
public class TouchpointDataSourceAssociationEditController extends MessagepointController {

	// private static final Log log = LogUtil.getLog(TouchpointDataSourceAssociationEditController.class);

	public static final long UNUSED_REFERENCE_CONNECTION_ID_VALUE = -1L;
	public static final long REMOVED_REFERENCE_CONNECTION_ID_VALUE = -100L;
	public static final String REQ_PARAM = "documentId";
	public static final String REQUEST_PARM_DOCUMENTID = "docid";
	public static final int MAX_REFERENCES_ALLOWED = 30;
	public static final long VARIABLE_NO_SELECTION_VALUE = -1L;
	public static final long VARIABLE_COMPOUND_KEY_VALUE = -99L;
	
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
    	long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
    	Document document = HibernateUtil.getManager().getObject(Document.class, id);
    	referenceData.put("document", document);

    	List<DataSourceAssociation> allDataSourceAssociations = new ArrayList<>();
    	DataSourceAssociation firstItem = new DataSourceAssociation();
    	if (document.getDataSourceAssociation() == null)
    	{
	    	firstItem.setId(0);
	    	firstItem.setName(ApplicationUtil.getMessage("page.text.none.please.select"));
	    	firstItem.setIsOneOfSeparatorFromSystemProperty();
	    	allDataSourceAssociations.add(firstItem);
    	}
    	allDataSourceAssociations.addAll(HibernateUtil.getManager().getObjects(DataSourceAssociation.class, MessagepointOrder.asc("name")));

		referenceData.put("allDataSourceAssociations", allDataSourceAssociations );
		
		return referenceData;
	}
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( Document.class, new IdCustomEditor<>(Document.class) );
		binder.registerCustomEditor( DataSourceAssociation.class, new IdCustomEditor<>(DataSourceAssociation.class) );
		binder.registerCustomEditor(String.class, new StringXSSEditor());

    }
	
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
    	Command command = new Command();
		if(request.getParameterMap().containsKey(REQ_PARAM)){
			long id;
			id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
	    	Document document = HibernateUtil.getManager().getObject(Document.class, id);
			command.setDocument(document);
			command.setDataSourceAssociation(document.getDataSourceAssociation());
		}
		
		return command;
    }

	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object commandObj,
            BindException errors) throws Exception {
		
		Command command = (Command)commandObj;
		Document document = command.getDocument();
		// Audit
		String fromDSName = "";
		String toDSName = "";
		boolean dataSourceAssociationChanged = false;
		if (command.getDataSourceAssociation() != null && command.getDataSourceAssociation().getId() > 0) {
			if (!command.getDataSourceAssociation().equals(document.getDataSourceAssociation())) {
				dataSourceAssociationChanged = true;
				fromDSName = document.getDataSourceAssociation() == null ? ApplicationUtil.getMessage("page.label.none") : document.getDataSourceAssociation().getName();
			}
			document.setDataSourceAssociation(command.getDataSourceAssociation());
		}
		if (command.getDataSourceAssociation() == null){
			fromDSName = ApplicationUtil.getMessage("page.label.none");
		}
		//execute the service
		ServiceExecutionContext ctx = UpdateTouchpointDataSourceAssociationService.createContext(document, dataSourceAssociationChanged);
		
		Service updateTouchpointDataSourceAssociationService = MessagepointServiceFactory.getInstance().lookupService(UpdateTouchpointDataSourceAssociationService.SERVICE_NAME, UpdateTouchpointDataSourceAssociationService.class);
		updateTouchpointDataSourceAssociationService.execute(ctx);
		ServiceResponse serviceResponse = ctx.getResponse();
		if(!serviceResponse.isSuccessful()){
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
			return showForm(request, response, errors);
 		} else {
 			if(document != null && !document.isTpContentChanged()){
				document.setTpContentChanged(true);
				document.save();
			}
 			toDSName = document.getDataSourceAssociation() == null? ApplicationUtil.getMessage("page.label.none"):document.getDataSourceAssociation().getName();
 			// Audit (Touchpoint Data collection changed)
			AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
					AuditMetadataBuilder.forSimpleAuditMessage(ApplicationUtil.getMessage("page.label.data.collection.configuration"), fromDSName, toDSName));
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
 			return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);	
 		}
	}
	
	public static class Command {
		private Document document;
		private DataSourceAssociation dataSourceAssociation;
		
		public Document getDocument() {
			return document;
		}
		public void setDocument(Document document) {
			this.document = document;
		}
		public DataSourceAssociation getDataSourceAssociation() {
			return dataSourceAssociation;
		}
		public void setDataSourceAssociation(DataSourceAssociation dataSourceAssociation) {
			this.dataSourceAssociation = dataSourceAssociation;
		}
	}
}