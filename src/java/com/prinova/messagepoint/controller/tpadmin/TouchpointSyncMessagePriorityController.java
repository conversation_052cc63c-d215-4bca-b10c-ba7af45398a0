package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * TouchpointSyncProjectController
 *
 */
public class TouchpointSyncMessagePriorityController extends MessagepointController {

	// private static final Log log = LogUtil.getLog(TouchpointSyncProjectController.class);

	public static final String REQ_PARAM = "documentId";
	public static final String REQUEST_PARM_DOCUMENTID = "docid";
    public static final String REQ_IS_EXCHANGE_TP = "isExchangeTouchpoint";

	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
	    	Map<String, Object> referenceData = new HashMap<>();
	    	long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
        boolean isExchangeTouchpoint = ServletRequestUtils.getBooleanParameter(request, REQ_IS_EXCHANGE_TP, false);

	    	Document document = HibernateUtil.getManager().getObject(Document.class, id);
	    	referenceData.put("document", document);
        referenceData.put(REQ_IS_EXCHANGE_TP, isExchangeTouchpoint);
		
		return referenceData;
	}
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor( Document.class, new IdCustomEditor<>(Document.class) );
		binder.registerCustomEditor(String.class, new StringXSSEditor());

    }
	
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
    		SyncCommand command = new SyncCommand();
		if(request.getParameterMap().containsKey(REQ_PARAM)){
			long id;
			id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
			Document document = HibernateUtil.getManager().getObject(Document.class, id);
			command.setDocument(document);
		}
		
		return command;
    }

	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object commandObj,
            BindException errors) throws Exception {
		
		SyncCommand command = (SyncCommand)commandObj;
		Document document = command.getDocument();
		
//		SyncTouchpointMessagePriorityBackgroundTask task = new SyncTouchpointMessagePriorityBackgroundTask(document.getId(), command.isSyncFromOrigin(), UserUtil.getPrincipalUser());
//		MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
		
		Map<String, Object> parms = new HashMap<>();
		parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);	
	}
	
	public static class SyncCommand {
		private Document document;
		private boolean syncFromOrigin = true;
		
		public Document getDocument() {
			return document;
		}
		public void setDocument(Document document) {
			this.document = document;
		}
		public boolean isSyncFromOrigin() {
			return syncFromOrigin;
		}
		public void setSyncFromOrigin(boolean syncFromOrigin) {
			this.syncFromOrigin = syncFromOrigin;
		}
		
	}
}