package com.prinova.messagepoint.controller.tpadmin;

import com.prinova.messagepoint.model.MessagepointSelection;
import com.prinova.messagepoint.model.TouchpointSelection;

public class TouchpointSelectionSelectorsEditWrapper extends BaseSelectionSelectorsEditWrapper {
		
	private TouchpointSelection touchpointSelection;

	@Override
	public MessagepointSelection getMessagepointSelection() {
		return touchpointSelection;
	}

	@Override
	public void setMessagepointSelection(MessagepointSelection messagepointSelection) {
		this.touchpointSelection = (TouchpointSelection) messagepointSelection;		
	}	
}