package com.prinova.messagepoint.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollection;
import com.prinova.messagepoint.model.admin.ParameterGroupInstanceCollectionTreeNodeVO;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;

public class AsyncCollectionDataValuesTreeNodesController implements Controller{

    private static final Log log = LogUtil.getLog(AsyncCollectionDataValuesController.class);

    public static final String REQ_PARAM_COLLECTION_ID = "collectionId";
    public static final String REQ_PARAM_PGI_FULL_VALUE = "fullValue";
    public static final String REQ_PARAM_NODE_LEVEL = "nodeLevel";
    public static final String REQ_PARAM_VERBOSE_DATA = "verboseData";

    @Override
    public ModelAndView handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        ServletOutputStream out = httpServletResponse.getOutputStream();

        try {
            out.write(getResponseJSON(httpServletRequest, httpServletResponse).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Selector values retrieval failed - "+e.getMessage(),e);
        }

        return null;
    }

    public String getResponseJSON(HttpServletRequest request, HttpServletResponse response) {
        long pgiCollectionId = getParameterGroupInstanceCollectionId(request);
        String fullValue = getFullValue(request);
        Boolean requestForVerboseData = getVerboseDataFlag(request);
        String tree = "";

        try {

            response.setContentType("application/json");

            if (fullValue == null) {
                // GET ROOT TREE NODES
                List<ParameterGroupInstanceCollectionTreeNodeVO> firstTwoLevelTreeNodes = ParameterGroupInstanceCollection.loadFirstTwoLevelTreeNodes(pgiCollectionId);
                tree = getJSONfromTreeNodeList(firstTwoLevelTreeNodes, 1, requestForVerboseData).toString();
            } else {
                // GET CHILD NODE VALUES
                int nodeLevel = getNodeLevel(request);
                List<String> pgItemValues = ParameterGroupInstanceCollectionTreeNodeVO.deflat(fullValue);
                List<ParameterGroupInstanceCollectionTreeNodeVO> treeNodes =
                        ParameterGroupInstanceCollection.loadDataTreeNodesAtLevel(pgiCollectionId, pgItemValues, nodeLevel+1);
                tree = getJSONfromTreeNodeList(treeNodes, nodeLevel+1, requestForVerboseData).toString();
            }
        }
        catch (Exception e) {
            log.error("Error building JSON response for collecton data value tree: " + e.getMessage(), e);
        }

        return tree;
    }
    
    private String fmtDataValueLabel(String label) {
    	if ( label.contains("@~@") ) {
    		String[] dates = label.split("@~@");
			try {
				boolean hasEndDate = dates.length == 2 && !dates[1].isEmpty();
				boolean hasStartDate = dates.length > 0 && !dates[0].isEmpty();
	    		DateFormat df 	= new SimpleDateFormat("yyyy-MM-dd");
	    		Date startDate 	= dates.length == 0 || dates[0].isEmpty() ? null : df.parse(dates[0]);
	    		Date endDate 	= !hasEndDate ? null : df.parse(dates[1]);
	    		
	    		if ( !hasStartDate && !hasEndDate )
	    			return ApplicationUtil.getMessage("page.text.any.date");
	    		else if ( !hasStartDate )
	    			return ApplicationUtil.getMessage("page.text.ending") + " " + DateUtil.formatDate(endDate);
	    		else if ( !hasEndDate )
	    			return ApplicationUtil.getMessage("page.text.starting") + " " + DateUtil.formatDate(startDate);
	    		else
	    			return DateUtil.formatDate(startDate) + " " + ApplicationUtil.getMessage("page.text.range_to") + " " + DateUtil.formatDate(endDate);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
    	}
    	return label;
    }

    private JSONArray getJSONfromTreeNodeList(List<ParameterGroupInstanceCollectionTreeNodeVO> treeNodeList, int nodeListLevel, Boolean requestForVerboseData) throws JSONException {
        JSONArray tree = new JSONArray();

        for (ParameterGroupInstanceCollectionTreeNodeVO currentChildNode : treeNodeList) {
            JSONObject treeNodeJSON = new JSONObject();

            treeNodeJSON.put("id", currentChildNode.getFullValue());
            treeNodeJSON.put("text", fmtDataValueLabel(currentChildNode.getLabel()));
            treeNodeJSON.put("level", nodeListLevel);
            treeNodeJSON.put("type", "variantData");

            List<ParameterGroupInstanceCollectionTreeNodeVO> nodeChildren = currentChildNode.getChildren();

            if (nodeChildren != null && !nodeChildren.isEmpty()) {
                treeNodeJSON.put("children", getJSONfromTreeNodeList(nodeChildren, nodeListLevel + 1, requestForVerboseData));
            }
            else {
                // required to show expand button client side and trigger a sync load for child nodes
                treeNodeJSON.put("children", true);
            }

            if (requestForVerboseData) {
                treeNodeJSON.put("data", currentChildNode.getValue());
            }

            JSONObject nodeState = new JSONObject();
            nodeState.put("opened", nodeListLevel == 1);
            treeNodeJSON.put("state", nodeState);

            tree.put(treeNodeJSON);
        }

        return tree;
    }

    private Long getParameterGroupInstanceCollectionId(HttpServletRequest request) {
        return ServletRequestUtils.getLongParameter(request, REQ_PARAM_COLLECTION_ID, -9);
    }
    private int getNodeLevel(HttpServletRequest request) {
        return (int)ServletRequestUtils.getLongParameter(request, REQ_PARAM_NODE_LEVEL, -9);
    }
    private String getFullValue(HttpServletRequest request) {
        String s = ServletRequestUtils.getStringParameter(request, REQ_PARAM_PGI_FULL_VALUE, null);
        String charset = "UTF-8";
        if (s == null) {
            return null;
        }
        try {
            return URLDecoder.decode(s, charset);
        }
        catch (UnsupportedEncodingException e) {
            return s;
        }
    }
    private Boolean getVerboseDataFlag(HttpServletRequest request) {
        return ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_VERBOSE_DATA, false);
    }
}
