package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;

import com.prinova.messagepoint.model.IdentifiableMessagePointModelInt;
import com.prinova.messagepoint.util.HibernateUtil;

public class IdCustomEditorInt<T extends IdentifiableMessagePointModelInt> extends PropertyEditorSupport {
	private Class<T> clazz;

	public IdCustomEditorInt(Class<T> clazz) {
		this.clazz = clazz;
	}

	public void setAsText(String text) throws IllegalArgumentException {
		int id = Integer.parseInt(text);
		if (id > 0) {
			Object obj = HibernateUtil.getManager().getObject(clazz,id);
			setValue(obj);
		} else {
			setValue(null);
		}
	}

	public String getAsText() {
		IdentifiableMessagePointModelInt object = (IdentifiableMessagePointModelInt)getValue();
		if (object == null) return "0";
		return "" + object.getId();
	}
}
