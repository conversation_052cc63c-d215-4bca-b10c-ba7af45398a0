package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.admin.ApplicationLocale;
import com.prinova.messagepoint.model.admin.SecuritySettings;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.BranchUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;

/**
 * 
 * <AUTHOR>
 * 
 */
public class MessagepointAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

	private String unmatchingUserIdUrl;
	private String unmatchingPasswordUrl;
	private String unmatchingUserIdAndPasswordUrl;
	
    private RequestCache requestCache = new HttpSessionRequestCache();
	
	public static String passwordChangeRedirect = "/user/password_change.form?msgkey=error.message.password.expired";

	public String getUnmatchingUserIdUrl() {
		return unmatchingUserIdUrl;
	}

	public void setUnmatchingUserIdUrl(String unmatchingUserIdUrl) {
		this.unmatchingUserIdUrl = unmatchingUserIdUrl;
	}

	public String getUnmatchingPasswordUrl() {
		return unmatchingPasswordUrl;
	}

	public void setUnmatchingPasswordUrl(String unmatchingPasswordUrl) {
		this.unmatchingPasswordUrl = unmatchingPasswordUrl;
	}

	public String getUnmatchingUserIdAndPasswordUrl() {
		return unmatchingUserIdAndPasswordUrl;
	}

	public void setUnmatchingUserIdAndPasswordUrl(String unmatchingUserIdAndPasswordUrl) {
		this.unmatchingUserIdAndPasswordUrl = unmatchingUserIdAndPasswordUrl;
	}

    public void setRequestCache(RequestCache requestCache) {
        this.requestCache = requestCache;
    }
    
	@Override
	public void onAuthenticationSuccess(javax.servlet.http.HttpServletRequest request,
            javax.servlet.http.HttpServletResponse response,
            Authentication authentication) throws javax.servlet.ServletException, IOException{
		
        SavedRequest savedRequest = requestCache.getRequest(request, response);

        if (savedRequest == null) {
            super.onAuthenticationSuccess(request, response, authentication);
        }
        else
        {
	        //String targetUrlParameter = getTargetUrlParameter();
	        
	        // FB3498:  Timeout from iframe returns user to top document view of iframe content on sign-in (context locked thereafter)
	        //     * We need some way to determine an iFrame context and default to index or to detect this occurrence client side 
	        //     * and redirect to a top page
	        
	        requestCache.removeRequest(request, response);
            super.onAuthenticationSuccess(request, response, authentication);
	        
//	        TEMP BYPASS
//          if (isAlwaysUseDefaultTargetUrl() || (targetUrlParameter != null && StringUtils.hasText(request.getParameter(targetUrlParameter)))) {
//	            requestCache.removeRequest(request, response);
//	            super.onAuthenticationSuccess(request, response, authentication);
//	        }
//	        else
//	        {
//	            clearAuthenticationAttributes(request);
//
//	            // Use the DefaultSavedRequest URL
//	            String targetUrl = savedRequest.getRedirectUrl();
//	            if (ApplicationUtil.isCSRFPreventionEnabled()) {
//					targetUrl = replacePreviousToken(targetUrl, request);
//				}
//	            
//	            getRedirectStrategy().sendRedirect(request, response, targetUrl);		
//	        }
        }
		
		SecuritySettings securitySettings = SecuritySettings.findForCurrentDomain();
		if (securitySettings != null)
		{
			int sessionExpireMins = Integer.valueOf(securitySettings.getSessionExpireMins());
			request.getSession().setMaxInactiveInterval(sessionExpireMins*60); //in seconds
		}
		
		User principal = (User)authentication.getPrincipal();
		principal.setLastLogin(DateUtil.now());
		principal.save();
		// Load the user locale setting
		request.getSession().setAttribute(ApplicationLocale.SESSION_ATTR_NAME, principal.getAppLocaleLangCode());
		// Audit (Authentication success)
		AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, principal.getUsername(), principal.getId(), AuditActionType.ID_AUTHENTICATION_SUCCESS, null);
	}
	
	@Override
	protected String determineTargetUrl(HttpServletRequest request, HttpServletResponse response) {

		String targetUrl = (String) request.getSession().getAttribute("MESSAGEPOINT_SAVED_URL_REQUEST");

		if (targetUrl != null)
		{
			request.getSession().removeAttribute("MESSAGEPOINT_SAVED_URL_REQUEST");
			if (targetUrl.contains("&gd=") || targetUrl.contains("?gd="))
			{
				// using else for performance improvement (most likely the URL will contain "&gd=")
				//
				// don't change token if it's coming from email link with node GUID in it
			}
			else
			{
				if (targetUrl.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "=") == -1 || request.getSession(false) == null) {
					targetUrl = super.determineTargetUrl(request, response);
				}
				if (ApplicationUtil.isCSRFPreventionEnabled()) {
					targetUrl = replacePreviousToken(targetUrl, request);
				}				
			}
		}
		else
		{
			targetUrl = super.determineTargetUrl(request, response);
			if (ApplicationUtil.isCSRFPreventionEnabled()) {
				targetUrl = replacePreviousToken(targetUrl, request);
			}
		}

		//
		// Check userid and passwords against system settings to ensure
		// compliance
		// If no longer compliant, add special permission and redirect to
		// special pages.
		//
		
		User user = UserUtil.getPrincipalUser();
		if(user != null){
			boolean softDeactivatedError = false;
			if(user.isAccountSoftDeactivated()){
				//need to get homeBranch and homeUser if there's enough license to activate this user again.
				Branch currentBranch = Branch.findById(Node.getCurrentBranch().getId());
				if(!UserUtil.canActivateThisUser(user, currentBranch, null).equals("true")){
					softDeactivatedError = true;
				}else{
					// set softDeactivated false
					BranchUtil.setSoftDeactivation(user, currentBranch, false);
				}
				if (softDeactivatedError == true) {
					Authentication auth = SecurityContextHolder.getContext().getAuthentication();
					new SecurityContextLogoutHandler().logout(request, response, auth);
					SecurityContextHolder.getContext().setAuthentication(null);
					SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
					String errorMessage = ApplicationUtil.getMessage("code.text.soft.deactivated.error");
					try {
						HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_FORBIDDEN, errorMessage, request, response);
					} catch (ServletException e) {
					} catch (IOException e) {
					}
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
					
					return "/access_forbidden_error.jsp" .concat(MessagepointAuthenticationProcessingFilter.AUTHENTICATION_FAIL_SOFT_DEACTIVATED);	
				}
			}
		}

		if (user != null && user.isSSOUser()) {
			return targetUrl;
		}
		
		// String username = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
		String password = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_PASSWORD_KEY);
		
		// User user = User.findByUsername(username, false);
		boolean validUsername = UserUtil.isValidUsername(user);
		boolean validPassword = true;
		
		if (user != null) {
			if (password != null && !password.trim().isEmpty() && !UserUtil.isValidPassword(password, user)) {
				if(!user.isSuperuser() && !user.isDomainAdminUser() )
					validPassword = false;
			}
		} else {
			// user doesn't exist in this node
			// logout the user 
			//
			Authentication auth = SecurityContextHolder.getContext().getAuthentication();
			new SecurityContextLogoutHandler().logout(request, response, auth);
			SecurityContextHolder.getContext().setAuthentication(null);
			SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
			String errorMessage = "The requested instance doesn't contain log-in user.";
			try {
				HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_FORBIDDEN, errorMessage, request, response);
			} catch (ServletException e) {
			} catch (IOException e) {
			}
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
			
			return "/access_forbidden_error.jsp";			
		}

		if (!validUsername && !validPassword) {
			return getTargetUrlWhenSecuritySettingsViolated(request, MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID_PASSWORD);
		} else if (!validUsername) {
			return getTargetUrlWhenSecuritySettingsViolated(request, MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID);
		} else if (!validPassword) {
			return getTargetUrlWhenSecuritySettingsViolated(request, MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_PASSWORD);
		}

		// STEP 3: Validate the expiry
		if (!user.isSuperuser() && !user.isDomainAdminUser() && user.checkIsPasswordExpired()) {
			targetUrl = ApplicationUtil.addToken(passwordChangeRedirect, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
		}
		
		return targetUrl;
	}

	private String replacePreviousToken(String url, HttpServletRequest request) {
		if (url.contains(WebAppSecurity.CSRF_TOKEN_KEY + "=") && request.getSession(false) != null) {
			String newToken = (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY);
			String tokenURL = url.substring(url.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "="));
			String[] kvs = tokenURL.split("&");
			for (String pairs : kvs) {
				if (pairs.contains(WebAppSecurity.CSRF_TOKEN_KEY + "=")) {
					String[] tks = pairs.split("=");
					if (tks.length > 1 && tks[1].trim().length() == WebAppSecurity.TOKEN_LENGTH) {
						return url.replaceAll(tks[1], newToken);
					}
				}
			}
		}
		return url;
	}

	protected String getTargetUrlWhenSecuritySettingsViolated(HttpServletRequest request, String failureReason) {

		List<GrantedAuthority> newAuthorities = new ArrayList<>();
		switch (failureReason) {
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID:
				newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_USERID));
				break;
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_PASSWORD:
				newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_PASSWORD));
				break;
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_SOFT_DEACTIVATED:
				newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_USERID_PASSWORD));
				break;
			default:
				newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_USERID_PASSWORD));
				break;
		}

		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		((User) auth.getPrincipal()).setAuthorities(newAuthorities);
		UsernamePasswordAuthenticationToken newAuth = new UsernamePasswordAuthenticationToken(auth.getPrincipal(), auth.getCredentials(), newAuthorities);
		newAuth.setDetails(auth.getDetails());
		SecurityContextHolder.getContext().setAuthentication(newAuth);

		String url = "";
		switch (failureReason) {
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID:
				url = getUnmatchingUserIdUrl();
				break;
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_PASSWORD:
				url = getUnmatchingPasswordUrl();
				break;
			case MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_SOFT_DEACTIVATED:
				url = getUnmatchingUserIdAndPasswordUrl();
				break;
			default:
				url = getUnmatchingUserIdAndPasswordUrl();
				break;
		}

		// Query parameter tk will be added at the beginning of the redirect url with ?
		return ApplicationUtil.addToken(url,
				(String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY))
				.concat(MessagepointAuthenticationProcessingFilter.AUTHENTICATION_FAIL_SECURITY_SETTINGS_KEY);
	}
}
