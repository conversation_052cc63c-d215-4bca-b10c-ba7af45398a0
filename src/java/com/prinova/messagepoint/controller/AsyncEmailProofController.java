package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.migrate.util.FileUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class AsyncEmailProofController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncEmailProofController.class);

    private static final String PARAM_REQUEST_METHOD = "method";
    private static final String PARAM_REQUEST_PROOF_ID = "proofId";

    private static final String REQUEST_METHOD_PROOF_TYPES = "proofTypes";
    private static final String REQUEST_METHOD_EMAIL_ID = "emailId";

    private static final String RESULT_KEY_LITMUSEMAILID = "litmusEmailId";
    private static final String RESULT_KEY_API_ERROR = "litmusApiError";
    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String method = ServletRequestUtils.getStringParameter(request, PARAM_REQUEST_METHOD, StringUtils.EMPTY);
        String responseData = null;

        switch (method) {
            case REQUEST_METHOD_PROOF_TYPES:
                responseData = getProofTypes(request);
                break;
            case REQUEST_METHOD_EMAIL_ID:
                responseData = getEmailId(request);
                break;
        }

        if (responseData != null) {
            response.setContentType("application/json");
            response.getOutputStream().write(responseData.getBytes());
        }

        return null;
    }

    private String getEmailId(HttpServletRequest request) {

        JSONObject result = new JSONObject();
        try {
            long proofId = ServletRequestUtils.getLongParameter(request, PARAM_REQUEST_PROOF_ID, -1);

            String externalEmailId = null;
            String errorProofReason = null;

            if (proofId != -1) {
                Proof proof = Proof.findById(proofId);

                if (proof.getExternalId() != null) {
                    externalEmailId = proof.getExternalId();
                } else {
                    Map<String, String> litmusEmailResult = requestLitmusEmailProofId(proof);
                    if (litmusEmailResult != null) {
                        String litmusEmailId = litmusEmailResult.getOrDefault(RESULT_KEY_LITMUSEMAILID, null);
                        errorProofReason = litmusEmailResult.getOrDefault(RESULT_KEY_API_ERROR, null);
                        if (litmusEmailId != null) {
                            proof.setExternalId(litmusEmailId);
                            HibernateUtil.getManager().saveObject(proof);
                        }
                        externalEmailId = proof.getExternalId();
                    }
                }
            }

            if (externalEmailId != null) {
                result.put("emailProofId", externalEmailId);
            } else {
                result.put("emailProofingError", true);

                if (errorProofReason != null) {
                    result.put(RESULT_KEY_API_ERROR, errorProofReason);
                }
            }
        } catch (Exception e) {
            log.error("Error:", e);
        }

        return result.toString();
    }

    private String getProofTypes(HttpServletRequest request) {

        String result = null;
        try {

            HttpClient client = new HttpClient();
            GetMethod method = new GetMethod("https://instant-api.litmus.com/v1/clients/configurations");

            client.executeMethod(method);
            String strResponse = new String(method.getResponseBody());
            JSONObject responseJson = new JSONObject(strResponse);

            JSONArray keys = responseJson.names();

            ArrayList<String> mobile = new ArrayList<>();
            ArrayList<String> desktop = new ArrayList<>();
            ArrayList<String> web = new ArrayList<>();

            HashMap<String, JSONObject> records = new HashMap<>();

            for (int i = 0; i < keys.length(); i++) {
                if (responseJson.get(keys.get(i).toString()) instanceof JSONObject) {
                    JSONObject obj = (JSONObject) responseJson.get(keys.get(i).toString());

                    if (obj.has("group")) {
                        String group = obj.get("group").toString().toLowerCase();

                        JSONObject record = new JSONObject();
                        record.put("name", obj.has("platform") ? MessageFormat.format("{0} ({1})", obj.get("name").toString(), obj.get("platform").toString()): obj.get("name").toString());
                        record.put("id", keys.get(i).toString());

                        if (obj.has("images_options") && obj.get("images_options") instanceof JSONArray) {
                            record.put("images_options", obj.getJSONArray("images_options"));
                        }

                        if (obj.get("name").toString().toLowerCase().contains("plain text")) {
                            continue;
                        }

                        if (obj.has("status") && !obj.get("status").toString().toLowerCase().equals("normal")) {
                            continue;
                        }

                        switch (group) {
                            case "mobile/tablet":
                                mobile.add(record.get("name").toString());
                                break;
                            case "web-based":
                                web.add(record.get("name").toString());
                                break;
                            default:
                                desktop.add(record.get("name").toString());
                                break;
                        }

                        records.put(record.get("name").toString(), record);


                    }
                }
            }

            mobile.sort(String.CASE_INSENSITIVE_ORDER);
            desktop.sort(String.CASE_INSENSITIVE_ORDER);
            web.sort(String.CASE_INSENSITIVE_ORDER);

            JSONArray desktopJson = new JSONArray();
            JSONArray mobileJson = new JSONArray();
            JSONArray webJson = new JSONArray();

            for (String key : desktop) {
                desktopJson.put(records.get(key));
            }

            for (String key : mobile) {
                mobileJson.put(records.get(key));
            }

            for (String key : web) {
                webJson.put(records.get(key));
            }

            JSONObject values = new JSONObject();
            values.put("desktop", desktopJson);
            values.put("mobile", mobileJson);
            values.put("web", webJson);

            result = values.toString();

        } catch (Exception ex) {
            log.error("Error:", ex);
        }

        return result;
    }

    private Map<String, String> requestLitmusEmailProofId(Proof proof) throws IOException, JSONException {
        if (proof != null) {

            File file = new File(proof.getOutputPath());

            if (!file.exists() || !file.canRead()) {
                return null;
            }

            String rawEmail = null;

            try {
                rawEmail = FileUtils.readFileToString(file);
            } catch (IOException e) {
                log.error(e);
            }

            if (rawEmail == null) {
                return null;
            }

            JSONObject requestObj = getCreateEmailRequestObject(rawEmail);

            HttpClient client = new HttpClient();
            PostMethod method = new PostMethod("https://instant-api.litmus.com/v1/emails");
            method.setRequestHeader("Content-type", "application/json");


            byte[] encoding = Base64.encodeBase64("h1rg9i3hge2m7iakcwum0jrmuozhckftutxu:".getBytes());
            method.setRequestHeader("Authorization", "Basic " + new String(encoding));

            StringRequestEntity entity = new StringRequestEntity(requestObj.toString(), "application/json", "UTF-8");
            method.setRequestEntity(entity);

            int rawResponse = client.executeMethod(method);
            HashMap<String, String> result = new HashMap<>();

            if (rawResponse == 200) {

                String strResponse = new String(method.getResponseBody());
                JSONObject responseJson = new JSONObject(strResponse);
                result.put(RESULT_KEY_LITMUSEMAILID, responseJson.getString("email_guid"));

            } else if (rawResponse == 413) {
                String errorMessage = MessageFormat.format(ApplicationUtil.getMessage("page.label.proofing.message.size.error"), FileUtil.getFileSizeKB(file));
                result.put(RESULT_KEY_API_ERROR, errorMessage);
            }
            else {
                log.error(MessageFormat.format("HTTP {0} Error: {1}", rawResponse, new String(method.getResponseBody())));
            }

            return result;
        }

        return null;
    }

    private JSONObject getCreateEmailRequestObject(String rawEmail) {

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("raw_source", rawEmail);
            jsonObject.put("end_user_id", UserUtil.getPrincipalUser().getSchemaOfThisUser());
        } catch (JSONException e) {
            log.error(e);
        }
        return jsonObject;
    }
}
