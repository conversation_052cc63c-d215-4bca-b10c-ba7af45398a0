package com.prinova.messagepoint.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.security.StringXSSEditor;


public abstract class ConfirmationCommandController<T> extends MessagepointController {
    protected static final String CONFIRM_PARAMETER = "confirm";
    protected static final String ORIGINAL_OBJECT_ATTRIBUTE_NAME = "originalObjectParameter";
    protected static final String MODIFIED_OBJECT_ATTRIBUTE_NAME = "modifiedObjectParameter";
	
    private String confirmationView;
    private String formViewRedirect;
    private String confirmationViewRedirect;
    private String cancelConfirmViewRedirect;
    private String parameter;
    
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
    
    /**
     * Returns a new Content Object if the "MESSAGE_ID" HTTP parameter
     * is specified; other returns instance of object in database matching the
     * value of the "MESSAGE_ID" parameter.
     * @see ContentObject
     */
    @SuppressWarnings("unchecked")
	protected Object formBackingObject(HttpServletRequest request)
    {
    	T commandObject = null;
    	if( request.getSession().getAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME + getFormView()) != null ) {
    		commandObject = (T)request.getSession().getAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView());
    		request.getSession().setAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView(), null );
    	} else {
    		commandObject = formBackingObjectInternal(request);
    	}
        return commandObject;
    }    
    
    
    /**
     * Processes the submission of the form. Will call needConfirmation 
     * to determine if the confirmation page will need to be displayed.
     *
     */
    @SuppressWarnings("unchecked")
	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object command,
            BindException errors) throws Exception
    {
        T commandObject = (T) command;
        if( request.getParameter(CONFIRM_PARAMETER) != null ) {
        	if( request.getParameter(CONFIRM_PARAMETER).equals("true") ) {
        		request.getSession().setAttribute(ORIGINAL_OBJECT_ATTRIBUTE_NAME+ getFormView(), command);
        		request.getSession().setAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView(), null);
        		return handleConfirm( request, response, commandObject, errors );
	        } else if ( request.getParameter(CONFIRM_PARAMETER).equals("false") ) {
	        	request.getSession().setAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView(), null);
	        	return handleDiscard( request, commandObject );
	        }
        	// Cancel - don't clear command object.

        	String parameter = this.getParameter();
        	String value = request.getParameter(parameter);
        	String modifiedQueryString = parameter+"="+value;
        	
	        return new ModelAndView(new RedirectView( getCancelConfirmViewRedirect() + "?" + modifiedQueryString ));
        } else if( needConfirmation( request, commandObject ) ) {
        	request.getSession().setAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView(), command);
            return new ModelAndView(new RedirectView( getConfirmationViewRedirect() + "?" + request.getQueryString() ));
    	}
		request.getSession().setAttribute(MODIFIED_OBJECT_ATTRIBUTE_NAME+ getFormView(), null );
        return handleConfirm( request, response, commandObject, errors );
    }
    
    @Override
	protected ModelAndView showForm(
			HttpServletRequest request, HttpServletResponse response, BindException errors, Map controlModel)
			throws Exception {
    	if( request.getRequestURI().indexOf(CONFIRM_PARAMETER) != -1 ) {
    		return showForm(request, errors, getConfirmationView(), controlModel);
    	}
		return showForm(request, errors, getFormView(), controlModel);
	}
    
    @SuppressWarnings("unchecked")
	protected T getOriginalObject( HttpServletRequest request ) {
    	return (T)request.getSession().getAttribute(ORIGINAL_OBJECT_ATTRIBUTE_NAME+ getFormView());

    }
    
    /**
     * Handle the case where the changes were discarded.
     * Implementation will likely involve returning 
     * a ModelAndView based on the formView or formViewRedirect property.
     *  
     * @return The model and view for the form view.
     */
    protected abstract ModelAndView handleDiscard(HttpServletRequest request, T command ) throws Exception;
    
    /**
     * Handle the command object now that the save has been confirmed.
     * Implementor should return a ModelAndView based on the sucessView
     * property.
     * 
     * @param command The command object that was confirmed.
     * 
     * @return The model and view for the successpage. 
     */
    protected abstract ModelAndView handleConfirm(HttpServletRequest request, HttpServletResponse response, T command, BindException errors ) throws Exception;
    
    /**
     * Determine if the confirmation page needs to be loaded. If not
     * then the handleSucess( command ) function will subsequently be 
     * called. If it does then the confirmation page will be loaded.
     * 
     * @param command The command object
     * 
     * @return true if confirmation is required. false otherwise.
     */
    protected abstract boolean needConfirmation(HttpServletRequest request, T command );
    
    /**
     * Returns the command object in the case where the confirmation
     * view isn't the current view. 
     * 
     * @param request The current request.
     * @return The command object that will be used in the form.
     */
    protected abstract T formBackingObjectInternal( HttpServletRequest request ) ;
    
	/**
	 * @return the confirmationView
	 */
	public String getConfirmationView() {
		return confirmationView;
	}

	/**
	 * @param confirmationView the confirmationView to set
	 */
	public void setConfirmationView(String confirmationView) {
		this.confirmationView = confirmationView;
	}

	/**
	 * @return the confirmationViewRedirect
	 */
	public String getConfirmationViewRedirect() {
		return confirmationViewRedirect;
	}

	/**
     */
	public void setConfirmationViewRedirect(String confirmViewRedirect) {
		this.confirmationViewRedirect = confirmViewRedirect;
	}

	public String getCancelConfirmViewRedirect() {
		return cancelConfirmViewRedirect;
	}


	public void setCancelConfirmViewRedirect(String cancelConfirmViewRedirect) {
		this.cancelConfirmViewRedirect = cancelConfirmViewRedirect;
	}

	public String getParameter() {
		return parameter;
	}


	public void setParameter(String parameter) {
		this.parameter = parameter;
	}

	/**
	 * @return the formViewRedirect
	 */
	public String getFormViewRedirect() {
		return formViewRedirect;
	}

	/**
	 * @param formViewRedirect the formViewRedirect to set
	 */
	public void setFormViewRedirect(String formViewRedirect) {
		this.formViewRedirect = formViewRedirect;
	}
}
