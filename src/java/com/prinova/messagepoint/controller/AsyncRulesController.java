package com.prinova.messagepoint.controller;

import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.ConditionElementUtil;

public class AsyncRulesController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncRulesController.class);

	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_RULE_ID 					= "ruleId";
	public static final String PARAM_NAME_SEARCH				= "sSearch";
	public static final String PARAM_NUM_CAP					= "numCap";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_ADVANCED 					= "advanced";

	public static final String TYPE_RULE_LIST					= "rulesList";
	public static final String TYPE_RULE_DEFINITION				= "ruleDefinition";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
		
		if ( type.equalsIgnoreCase(TYPE_RULE_LIST) ) {
			try {
				out.write(getRuleListResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(TYPE_RULE_DEFINITION) ) {
			try {
				out.write(getRuleDefinitionResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for rule definition data: "+e.getMessage(),e);
			}
		}
		
		return null;
	}
	
	private String getRuleDefinitionResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		long conditionElementId 			= ServletRequestUtils.getLongParameter(request, PARAM_RULE_ID, 0);
		ConditionElement conditionElement 	= ConditionElement.findById(conditionElementId);
		
		try {

			returnObj.put("name"			, conditionElement.getName());
			returnObj.put("id"				, conditionElement.getId());
			returnObj.put("metatags"		, conditionElement.getMetatags() != null ? conditionElement.getMetatags() :  ApplicationUtil.getMessage("page.label.none") );
			returnObj.put("select_type"		, conditionElement.getConditionType().getId() == 1 ? "radio" : "checkbox" );
			
			Boolean ruleCanBeParameterized	= false;
			
			JSONArray conditionArray = new JSONArray();

			for ( ConditionSubelement currentCondition: conditionElement.getSubElements() ) {
				JSONObject conditionObj = new JSONObject();

				conditionObj.put("name"				, currentCondition.getName() );
				conditionObj.put("id"				, currentCondition.getId() );
				conditionObj.put("variable_name"	, currentCondition.getDataElementVariable() != null ? currentCondition.getDataElementVariable().getDisplayName() : ApplicationUtil.getMessage("page.text.no.variable.brackets")  );
				conditionObj.put("comparator"		, ApplicationUtil.getMessage(currentCondition.getDataComparison().getName()) );
				conditionObj.put("comparator_id"	, currentCondition.getDataComparison().getId() );
				conditionObj.put("is_parameterized"	, currentCondition.isParameterized() );
				conditionObj.put("relationship"		, currentCondition.getConditionType().getId() == 2 ? ApplicationUtil.getMessage("page.label.and") : ApplicationUtil.getMessage("page.label.or") );
				
				String conditionSubelementType = ConditionElementUtil.getDataElementValueType(	currentCondition.getDataElementComparisonId(), 
						currentCondition.getDataElementVariable() != null ? currentCondition.getDataElementVariable().getDataSubtypeId() : 0);
				conditionObj.put("is_date_value"	, ConditionElementUtil.TYPE_DATE.equalsIgnoreCase(conditionSubelementType) || ConditionElementUtil.TYPE_DATE_RANGE.equalsIgnoreCase(conditionSubelementType) );
				conditionObj.put("is_date_file_value", currentCondition.getDataFilePath() != null);
				if ( currentCondition.isParameterized() ) {
					ruleCanBeParameterized = true;
					conditionObj.put("is_range_value"		, ConditionElementUtil.TYPE_SIMPLE_RANGE.equalsIgnoreCase(conditionSubelementType) || ConditionElementUtil.TYPE_DATE_RANGE.equalsIgnoreCase(conditionSubelementType) );
				} else {
					conditionObj.put("display_value"		, currentCondition.getDataElementDisplayValue() );
				}
				
				if ( currentCondition.getFilterCondition() != null )
					conditionObj.put("filter_display_value"	,  currentCondition.getFilterCondition().getDisplayValue() );
				
				conditionArray.put(conditionObj);
	
			}
			
			returnObj.put("can_be_parameterized"	, ruleCanBeParameterized );
			
			returnObj.put("conditions", conditionArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve rule definition: " + e );
		}

		return returnObj.toString();
	}

	private String getRuleListResponseJSON (HttpServletRequest request) {

		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		String nameSearch 			= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);
		int numCap 					= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
		boolean isAdvancedSearch 	= ServletRequestUtils.getBooleanParameter(request, PARAM_ADVANCED, false);
		
		JSONObject returnObj = new JSONObject();
		
		try {

			returnObj.put("documentId", documentId);
			returnObj.put("search", 	nameSearch);
			returnObj.put("maxResults", numCap);

			List<ConditionElement> ruleList = null;

			ruleList = ConditionElement.findAllByDocument(Document.findById(documentId), nameSearch, isAdvancedSearch, numCap);

			JSONArray ruleListArray = new JSONArray();
			
			for (int i=0; i < ruleList.size(); i++) {
				JSONObject rulegObj = new JSONObject();
				rulegObj.put("name"	, ruleList.get(i).getName());
				rulegObj.put("id"	, ruleList.get(i).getId());
				
				ruleListArray.put(rulegObj);
			}
			
			returnObj.put("rules", ruleListArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve rule list: " + e );
		}

		return returnObj.toString();
	}
}