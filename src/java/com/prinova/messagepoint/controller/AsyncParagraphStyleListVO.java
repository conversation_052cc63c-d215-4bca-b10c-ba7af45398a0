package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.model.wrapper.AsyncListVOIFrameData;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncParagraphStyleListVO extends AsyncListVO{

	private ParagraphStyle 				paragraphStyle;
	private String						name;
	private String 						connectorName;
	private String						alignment;
	private String						lineSpacing;
	private int							lineSpacingType;
	private String 						indentation;
	private String						marginLR;
	private String						marginTB;
	private String						border;
	private AsyncListVOIFrameData		iFrameData;
	private ParagraphStyleListVOFlags	flags;

	public void setParagraphStyle(ParagraphStyle paragraphStyle) {
		this.paragraphStyle = paragraphStyle;
	}

	public String getName() {
		String editURL = ApplicationUtil.getWebRoot() + "content/paragraph_style_edit.form";
		String nameHTML = "<a id=\"actionLabel_" + this.paragraphStyle.getId() +"\" itemName=\"ITEM_NAME\" href=\"" + editURL + "?paraStyleId=" + this.paragraphStyle.getId() + "\" class=\"d-inline-block w-100 text-truncate\" title=\"" + name + "\">" + name + "</a>";
		nameHTML = nameHTML.replace("ITEM_NAME",name);
		return "<div class=\"position-relative mt-n2\"><div class=\"text-truncate-wrapper\">" + nameHTML + "</div></div>";
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getConnectorName() {
		return "<div class=\"position-relative mt-n2\"><div class=\"text-truncate-wrapper\"><span class=\"d-inline-block w-100 text-truncate\" title=\"" + connectorName + "\" >" + connectorName + "</span></div></div>";
	}

	public void setConnectorName(String connectorName) {
		this.connectorName = connectorName;
	}

	public String getAlignment() {
		return alignment;
	}

	public void setAlignment(String alignment) {
		this.alignment = alignment;
	}

	public String getLineSpacing() {
		return lineSpacing;
	}

	public void setLineSpacing(String lineSpacing) {
		this.lineSpacing = lineSpacing;
	}

	public int getLineSpacingType() {
		return lineSpacingType;
	}

	public void setLineSpacingType(int lineSpacingType) {
		this.lineSpacingType = lineSpacingType;
	}

	public String getIndentation() {
		return indentation;
	}

	public void setIndentation(String indentation) {
		this.indentation = indentation;
	}

	public String getMarginLR() {
		return marginLR;
	}

	public void setMarginLR(String marginLR) {
		this.marginLR = marginLR;
	}

	public String getMarginTB() {
		return marginTB;
	}

	public void setMarginTB(String marginTB) {
		this.marginTB = marginTB;
	}

	public String getBorder() {
		return border;
	}

	public void setBorder(String border) {
		this.border = border;
	}

	public AsyncListVOIFrameData getiFrameData() {
		return iFrameData;
	}

	public void setiFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}
	
	public String getBinding() {
		return "<input id='listItemCheck_"+this.paragraphStyle.getId()+"' type='checkbox' value='"+this.paragraphStyle.getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}

	public ParagraphStyleListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(ParagraphStyleListVOFlags flags) {
		this.flags = flags;
	}

	public static class ParagraphStyleListVOFlags{
		private boolean	canUpdate;
		private boolean canDelete;
		private boolean canClone;

		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanDelete() {
			return canDelete;
		}
		public void setCanDelete(boolean canDelete) {
			this.canDelete = canDelete;
		}
		public boolean isCanClone() {
			return canClone;
		}
		public void setCanClone(boolean canClone) {
			this.canClone = canClone;
		}
	}	
}
