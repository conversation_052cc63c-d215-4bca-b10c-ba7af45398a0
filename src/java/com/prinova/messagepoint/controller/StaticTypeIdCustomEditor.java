package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

public class StaticTypeIdCustomEditor<T extends StaticType> extends PropertyEditorSupport {

	private static final Log log = LogUtil.getLog(StaticTypeIdCustomEditor.class);
	private Class<T> clazz;

	public StaticTypeIdCustomEditor(Class<T> clazz) {
		this.clazz = clazz;
	}

	public void setAsText(String text) throws IllegalArgumentException {
		int id = Integer.parseInt(text);
		if (id > 0) {
			Object obj = null;
			try {
				obj = clazz.getConstructor(Integer.class).newInstance(id);
			} catch (Exception e) {
				log.error("Error: ", e);
			}
			setValue(obj);
		} else {
			setValue(null);
		}
	}

	public String getAsText() {
		StaticType object = (StaticType)getValue();
		if (object == null) return "0";
		return "" + object.getId();
	}
}