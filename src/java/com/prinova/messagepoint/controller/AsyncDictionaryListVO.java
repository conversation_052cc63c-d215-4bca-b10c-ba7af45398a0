package com.prinova.messagepoint.controller;

import java.util.Locale;

import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.model.dictionary.DictionaryType;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.model.wrapper.AsyncListVOIFrameData;
import com.prinova.messagepoint.tag.EncodeUrlTag;
import com.prinova.messagepoint.tag.layout.TxtFmtTag;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncDictionaryListVO extends AsyncListVO{

	private Dictionary 									dictionary;
	private String											name;
	private AsyncListVOIFrameData		iFrameData;
	private DictionaryListVOFlags			flags;

	public void setDictionary(Dictionary dictionary) {
		this.dictionary = dictionary;
	}

	public String getName() {
		String nameHTML = "";
		if ( this.dictionary.canBeModified()) {
			String editURL = ApplicationUtil.getWebRoot() + "dictionary/dictionary_edit.form";
			nameHTML = TxtFmtTag.maxTxtLengh("<a id=\"actionLabel_"+ this.dictionary.getId() +"\" itemName=\"ITEM_NAME\" href=\"#\" onclick=\"javascript:iFrameView('"+editURL+"?dictionaryId=" + this.dictionary.getId() + "', 'actionLabel_"+this.dictionary.getId()+"', event); return false;\" class=\"dataTableLink dataTableItemName\">" + name + "</a>", 200, false);
			nameHTML = nameHTML.replace("ITEM_NAME",name);
		}else{
			String nameHtml = "<span class=\"dataTableItemName\">" + name + "</span>";
			nameHTML = TxtFmtTag.maxTxtLengh(nameHtml, 200, false);
		}
		return nameHTML;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getLangCode(){
		return this.dictionary.getLangCode() + " ("+ (new Locale(this.dictionary.getLangCode()).getDisplayLanguage()) + ")";
	}
	
	public String getLocaleCode(){
		return this.dictionary.getLocaleCode();
	}
	
	public String getType(){
		return (new DictionaryType(this.dictionary.getType())).getName();
	}
	
	public String getFile(){
		String encodedSourcePath = "";
		String fileNameStr = "";

		encodedSourcePath 		= EncodeUrlTag.encodeFilename( this.dictionary.getDictionaryPath().replace("\\", "/") );
						
		String readOnlyClass = "";
		if ( !this.dictionary.canBeModified() ) {
			readOnlyClass = "readOnly";
		}
		fileNameStr 	   += "<i id=\"sourceEditor_"+this.dictionary.getId()+"\" class=\"sourceEditor fa fa-pencil " +readOnlyClass+ "\"><input type=\"hidden\" value=\""+encodedSourcePath+"\"/></i>";
		
		return fileNameStr;
	}
	
	public String getStatus(){
		return this.dictionary.getStatus();
	}

	public AsyncListVOIFrameData getiFrameData() {
		return iFrameData;
	}

	public void setiFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}
	
	public String getBinding() {
		return "<input id='listItemCheck_"+this.dictionary.getId()+"' type='checkbox' value='"+this.dictionary.getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}

	public DictionaryListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(DictionaryListVOFlags flags) {
		this.flags = flags;
	}

	public static class DictionaryListVOFlags{
		private boolean	canUpdate;
		private boolean canDelete;
		private boolean canEnable;
		private boolean canDisable;
		private boolean canClone;

		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanDelete() {
			return canDelete;
		}
		public void setCanDelete(boolean canDelete) {
			this.canDelete = canDelete;
		}
		public boolean isCanEnable() {
			return canEnable;
		}
		public void setCanEnable(boolean canEnable) {
			this.canEnable = canEnable;
		}
		public boolean isCanDisable() {
			return canDisable;
		}
		public void setCanDisable(boolean canDisable) {
			this.canDisable = canDisable;
		}
		public boolean isCanClone() {
			return canClone;
		}
		public void setCanClone(boolean canClone) {
			this.canClone = canClone;
		}
	}	
}
