package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagepointDeferrable;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.SystemEvents;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil;
import com.prinova.messagepoint.util.*;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.logging.Log;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.*;

public class MessagepointStartFilter implements Filter {

    private final static Log log = LogUtil.getLog(MessagepointStartFilter.class);

    public static final String ANALYTICS_REQUEST_ATTR = "AnalyticsRequest";

    public void destroy() {
    }

    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws ServletException, IOException {
        try {
            MessagepointDeferrable.setRequestIdentifier(UUID.randomUUID().toString());
            AnalyticsEvent<SystemEvents> analytics = AnalyticsUtil.requestFor(SystemEvents.HttpRequest);
            servletRequest.setAttribute(ANALYTICS_REQUEST_ATTR, analytics);
            MessagepointHttpServletRequestWrapper requestWrapper = new MessagepointHttpServletRequestWrapper(servletRequest);
            MinifiedServletResponseWrapper responseWrapper = new MinifiedServletResponseWrapper(servletRequest, servletResponse);

            HttpSession session = ((HttpServletRequest) servletRequest).getSession();

            if (session.getAttribute(SystemPropertyKeys.WebAppSecurity.CSRF_SESSION_KEY) == null) {
                if(ApplicationUtil.isCSRFPreventionEnabled()) {
                    session.setAttribute(SystemPropertyKeys.WebAppSecurity.CSRF_SESSION_KEY, generateCSRFToken());
                }
            }

            if (session.getAttribute("SessionMaxAge") == null) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.SECOND, 36000);
                session.setAttribute("SessionMaxAge", calendar.getTimeInMillis());
            } else {
                if (Instant.now().toEpochMilli() > (Long)session.getAttribute("SessionMaxAge")) {
                    ((HttpServletRequest) servletRequest).getSession().invalidate();
                    HttpRequestUtil.sendMessagepointError(HttpStatus.SC_BAD_REQUEST, ApplicationUtil.getMessage("page.label.SESSION.TIMED.OUT"), (HttpServletRequest)servletRequest, responseWrapper);
                }
            }

            // for application debugging and testing purposes, a unique identifier for each node in a cluster
            responseWrapper.setHeader("X-MP-NodeId", PropertyUtils.getNodeId());

            HashMap<String, String> packageLookup = WebAssetPackageController.getDynamicScriptLookup(servletRequest.getServletContext());

            if (packageLookup == null || packageLookup.isEmpty()) {

                String userAgent =  ((HttpServletRequest) servletRequest).getHeader("user-agent");
                boolean isBrowser = userAgent != null && userAgent.toLowerCase().contains("mozilla");

                if (!responseWrapper.isDeveloperMode() && isBrowser) {

                    Cookie developerModeCookie = new Cookie("developer-mode", "true");
                    developerModeCookie.setPath("/");
                    ((HttpServletResponse) responseWrapper).addCookie(developerModeCookie);

                    ((HttpServletResponse) responseWrapper).setStatus(HttpStatus.SC_TEMPORARY_REDIRECT);
                    ((HttpServletResponse) responseWrapper).setHeader("Location", ApplicationUtil.getWebRoot());

                    return;
                }
            }

            if (servletRequest.getParameterMap().containsKey(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH)) {
                PINCOpenIdConnectUtil.beginOpenIdAuthFlow((HttpServletRequest) servletRequest);
            }

            chain.doFilter(requestWrapper, responseWrapper);

            responseWrapper.commitResponse();
        } catch (Exception e) {
            LogUtil.getLog(MessagepointStartFilter.class).error("Error:", e);
            throw e;
        }
    }

    public static String generateCSRFToken() {
        byte random[] = new byte[16];

        // Render the result as a String of hexadecimal digits
        StringBuilder buffer = new StringBuilder();
        SecureRandom randomSource = new SecureRandom();
        randomSource.nextBytes(random);

        for (int j = 0; j < random.length; j++) {
            byte b1 = (byte) ((random[j] & 0xf0) >> 4);
            byte b2 = (byte) (random[j] & 0x0f);
            if (b1 < 10)
                buffer.append((char) ('0' + b1));
            else
                buffer.append((char) ('A' + (b1 - 10)));
            if (b2 < 10)
                buffer.append((char) ('0' + b2));
            else
                buffer.append((char) ('A' + (b2 - 10)));

            if (((j+1) % 4) == 0 && j != 0 && j < 15) {
                buffer.append('-');
            }
        }
        return buffer.toString().toLowerCase();
    }


    public void init(FilterConfig config) throws ServletException {

    }
}
