package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.util.PropertyUtils;
import org.springframework.validation.BindException;
import org.springframework.web.servlet.ModelAndView;

import java.io.File;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class BackendComponentDownloadsController extends MessagepointController {


    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

        return super.showForm(request, response, errors);
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        BackendComponentDownloadsWrapper command = new BackendComponentDownloadsWrapper();

        command.setDEDigital(getDEDigital());
        command.setDEServer(getDEServer());
        command.setMPComposer(getMPComposer());
        command.setMPComp(getMPComp());
        command.setProductionManager(getProductionManager());
        return command;
    }

    private File[] getDEDigital() {
        File deDigial = new File(getReleasedBuildsPath() + "/DEDigital/"+ MessagePointStartUp.getMajorBuildRevision() +"/latestBuild");
        File[] result = deDigial.listFiles();
        return result != null ? result : new File[0];
    }

    private File[] getDEServer() {
        File deServer = new File(getReleasedBuildsPath() + "/DEServer/"+ MessagePointStartUp.getMajorBuildRevision() +"/latestBuild");
        File[] result = deServer.listFiles();
        return result != null ? result : new File[0];
    }

    private File[] getMPComposer() {
        if (!MessagepointLicenceManager.getInstance().isLicensedForSefasHCSConnector()) {
            return new File[0];
        }

        File mpComposer = new File(getReleasedBuildsPath() + "/MPComposer/"+ MessagePointStartUp.getMajorBuildRevision() +"/latestBuild");
        File[] result = mpComposer.listFiles();
        return result != null ? result : new File[0];
    }

    private File[] getMPComp() {
        File mpComp = new File(getReleasedBuildsPath() + "/MPComp/"+ MessagePointStartUp.getMajorBuildRevision() +"/latestBuild");
        File[] result = mpComp.listFiles();
        return result != null ? result : new File[0];
    }

    private File[] getProductionManager() {
        File productionManager = new File(getReleasedBuildsPath() + "/PMGR/latestBuild");
        File[] result = productionManager.listFiles();
        return result != null ? result : new File[0];
    }

    public static String getReleasedBuildsPath() {
        String releasedBuildsPath = PropertyUtils.getRuntimeProperty("app.system.server.released.builds.path");

        return Objects.requireNonNullElse(releasedBuildsPath, "/installBuilds");
    }

}
