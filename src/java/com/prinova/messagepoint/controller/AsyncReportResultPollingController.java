package com.prinova.messagepoint.controller;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.wtu.WhereUsedVariableReport;
import com.prinova.messagepoint.model.util.DiagnosticsReport;
import com.prinova.messagepoint.model.util.DomainReport;
import com.prinova.messagepoint.model.util.MessagepointObjectImportReport;
import com.prinova.messagepoint.model.util.UsersReport;
import com.prinova.messagepoint.model.util.UnifiedLoginReport;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncReportResultPollingController implements Controller {
	
	private static final Log log = LogUtil.getLog(AsyncReportResultPollingController.class);

	public static final String REQ_PARAM_REPORT_ID 			= "reportId";
	public static final String REQ_PARAM_TYPE 				= "type";
	
	public static final String TYPE_WHERE_USED_REPORT 		= "whereUsed";
	public static final String TYPE_DIAGNOSTICS_REPORT 		= "diagnostics";
	public static final String TYPE_IMPORT_REPORT 			= "import";
	public static final String TYPE_USERS_REPORT 			= "users";
	public static final String TYPE_UNIFIEDLOGIN_REPORT 	= "unifiedLogin";
	public static final String TYPE_DOMAIN_REPORT 			= "domain";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		
		ServletOutputStream out = response.getOutputStream();

		String type = getType(request);
		long reportId = getReportId(request);

		JSONObject obj = new JSONObject();
		if ( type.equals(TYPE_WHERE_USED_REPORT) ) {
			
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = WhereUsedVariableReport.getReportXmlFilePath(reportId, userId);
			File reportFile = new File(reportFilePath);

			if (reportFile.exists()) {
				String reportHTML = WhereUsedVariableReport.transformXML(reportId, userId);
				
				String reportHtmlFilePath = WhereUsedVariableReport.getReportHtmlFilePath(reportId, userId);
				writeReportToFile(reportHtmlFilePath, reportHTML);
				
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportHtmlFilePath);
				obj.put("reportHTML", 		reportHTML);
			} else
				obj.put("result", 			"PROCESSING");

		}else if(type.equals(TYPE_DIAGNOSTICS_REPORT)){
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = DiagnosticsReport.getReportXmlFilePath(reportId, userId);
			File reportFile = new File(reportFilePath);

			if (reportFile.exists()) {
				String reportHTML = DiagnosticsReport.transformXML(reportId, userId);
				
				String reportHtmlFilePath = DiagnosticsReport.getReportHtmlFilePath(reportId, userId);
				writeReportToFile(reportHtmlFilePath, reportHTML);
				
				// Create the Zip file
				DiagnosticsReport.createZipFile(DiagnosticsReport.getReportZipFilePath(reportId, userId), reportFilePath, reportHtmlFilePath);
				
				String reportZIPPath = DiagnosticsReport.getReportZipFilePath(reportId, userId);
				
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportHtmlFilePath);
				obj.put("reportZIPpath", 	reportZIPPath);
				obj.put("reportHTML", 		reportHTML);
			} else
				obj.put("result", 			"PROCESSING");			
		}else if(type.equals(TYPE_USERS_REPORT)){
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = UsersReport.getReportXmlFilePath(reportId, userId);
			File reportFile = new File(reportFilePath);

			if (reportFile.exists()) {
				String reportHTML = UsersReport.transformXML(reportId, userId);
				
				String reportHtmlFilePath = UsersReport.getReportHtmlFilePath(reportId, userId);
				writeReportToFile(reportHtmlFilePath, reportHTML);
				
				// Create the Zip file
				UsersReport.createZipFile(UsersReport.getReportZipFilePath(reportId, userId), reportFilePath, reportHtmlFilePath);
				
				String reportZIPPath = UsersReport.getReportZipFilePath(reportId, userId);
				
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportHtmlFilePath);
				obj.put("reportZIPpath", 	reportZIPPath);
				obj.put("reportHTML", 		reportHTML);
			} else
				obj.put("result", 			"PROCESSING");	
		}else if(type.equals(TYPE_UNIFIEDLOGIN_REPORT)){
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = UnifiedLoginReport.getReportXmlFilePath(reportId, userId);
			File reportFile = new File(reportFilePath);

			if (reportFile.exists()) {
				String reportHTML = UnifiedLoginReport.transformXML(reportId, userId);
				
				String reportHtmlFilePath = UnifiedLoginReport.getReportHtmlFilePath(reportId, userId);
				writeReportToFile(reportHtmlFilePath, reportHTML);
				
				// Create the Zip file
				UnifiedLoginReport.createZipFile(UnifiedLoginReport.getReportZipFilePath(reportId, userId), reportFilePath, reportHtmlFilePath);
				
				String reportZIPPath = UnifiedLoginReport.getReportZipFilePath(reportId, userId);
				
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportHtmlFilePath);
				obj.put("reportZIPpath", 	reportZIPPath);
				obj.put("reportHTML", 		reportHTML);
			} else
				obj.put("result", 			"PROCESSING");			
		}else if(type.equals(TYPE_DOMAIN_REPORT)){
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = DomainReport.getReportXmlFilePath(reportId, userId);
			File reportFile = new File(reportFilePath);

			if (reportFile.exists()) {
				String reportHTML = DomainReport.transformXML(reportId, userId);
				
				String reportHtmlFilePath = DomainReport.getReportHtmlFilePath(reportId, userId);
				writeReportToFile(reportHtmlFilePath, reportHTML);
				
				// Create the Zip file
				DomainReport.createZipFile(DomainReport.getReportZipFilePath(reportId, userId), reportFilePath, reportHtmlFilePath);
				
				String reportZIPPath = DomainReport.getReportZipFilePath(reportId, userId);
				
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportHtmlFilePath);
				obj.put("reportZIPpath", 	reportZIPPath);
				obj.put("reportHTML", 		reportHTML);
			} else
				obj.put("result", 			"PROCESSING");			
		} else if(type.equals(TYPE_IMPORT_REPORT)){
			long userId = UserUtil.getPrincipalUserId();
			String reportFilePath = MessagepointObjectImportReport.getReportLogFilePath(userId);
			File reportFile = new File(reportFilePath);
			if (reportFile.exists()) {
				obj.put("result", 			"COMPLETE");
				obj.put("reportXMLpath", 	reportFilePath);
				obj.put("reportHTMLpath", 	reportFilePath);
			}else{
				obj.put("result", 			"PROCESSING");	
			}
			
		}
		
		out.write(obj.toString().getBytes());
		out.flush();

		return null;
	}
	
	private static void writeReportToFile (String filePath, String report) {
		try {
			BufferedWriter out = new BufferedWriter(new FileWriter(filePath));
			out.write(report);
			out.close();
		} catch (IOException e) {
			log.error("Error - Unable to write out template file '"+filePath+"': Exception: "+e.getMessage(),e);
		}
	}

	private long getReportId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_REPORT_ID, -1);
	}

	private String getType(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, null);
	}

}