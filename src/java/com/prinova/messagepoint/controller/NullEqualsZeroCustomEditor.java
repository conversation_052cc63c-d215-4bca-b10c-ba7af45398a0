package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;

public class NullEqualsZeroCustomEditor extends PropertyEditorSupport {

	public void setAsText(String text) throws IllegalArgumentException {
		if ( (text == null) || (text.isEmpty())) {
			setValue(0);
		}
	}

	public String getAsText() {
		if ( (getValue() == null) || (getValue().equals("")) ) {
			return "0";
		} else {
			return getValue().toString();
		}
	}
}   