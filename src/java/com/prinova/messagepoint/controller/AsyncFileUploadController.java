package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.admin.Connector;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.email.TemplateVariant;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.ContentObjectBulkImageCreateService;
import com.prinova.messagepoint.platform.services.file.CreateOrUpdateSandboxFileService;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerIngestionUploadService;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerUploadBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerUploadService;
import com.prinova.messagepoint.platform.services.rationalizer.dto.RationalizerUploadIngestionDto;
import com.prinova.messagepoint.platform.services.rationalizer.removeduplicates.RationalizerXmlImportRemoveDuplicatesParams;
import com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateCompositionFileSetService;
import com.prinova.messagepoint.security.SecureFileUpload;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.EmailManager;
import com.prinova.messagepoint.util.EmailUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.RedisUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;
import org.w3c.dom.NodeList;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.DatatypeConverter;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.prinova.messagepoint.model.rationalizer.RationalizerApplicationRedisKeys.buildUploadedFilesMapByRequestIdCacheKey;
import static com.prinova.messagepoint.model.rationalizer.RationalizerApplicationRedisKeys.buildUploadedIngestionFilesDocNoByRequestIdCacheKey;
import static com.prinova.messagepoint.model.rationalizer.RationalizerApplicationRedisKeys.buildUploadedIngestionFilesMapByRequestIdCacheKey;
import static com.prinova.messagepoint.model.rationalizer.RationalizerApplicationRedisKeys.buildUploadedOrigDocsMapByRequestIdCacheKey;

public class AsyncFileUploadController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncFileUploadController.class);

    public static final String REQ_PARM_TYPE = "type";
    public static final String REQ_PARM_ACTION = "action";
    public static final String REQ_PARM_ITEM_ID = "itemId";
    public static final String REQ_PARM_TEMPLATE_VARIANT_ID = "templateVariantId";
    public static final String REQ_PARM_RATIONALIZER_APP_ID = "rationalizerApplicationId";

    public static final String ACTION_UPLOAD = "upload";
    public static final String ACTION_DELETE = "delete";

    public static final String TYPE_IMAGE_LIBRARY = "imageLibrary";
    public static final String TYPE_GRAPHIC_CONTENT = "graphicContent";
    public static final String TYPE_COMPOSITION_FILES = "compositionFiles";
    public static final String TYPE_TARGETING_DATA_FILE = "targetingData";
    public static final String TYPE_TEMPLATE_FILES = "templateVariant";
    public static final String TYPE_TEXT_STYLE_FONTS = "textStyleFonts";
    public static final String TYPE_TEXT_STYLE_FONTS_BOLD = "textStyleFontsBold";
    public static final String TYPE_TEXT_STYLE_FONTS_ITALIC = "textStyleFontsItalic";
    public static final String TYPE_TEXT_STYLE_FONTS_BOLD_ITALIC = "textStyleFontsBoldItalic";
    public static final String TYPE_DATA_FILE = "dataFile";
    public static final String TYPE_LOOKUP_TABLE_DATA = "lookupTableData";
    public static final String TYPE_RATIONALIZER_DOCUMENTS = "rationalizerDocuments";
    public static final String TYPE_RATIONALIZER_ZIP_FILES = "rationalizerZipFiles";
    public static final String TYPE_METADATA_UPLOAD_FILE = "metadataUploadFile";

    public static final String TYPE_SANDBOX_FILE = "sandbox_file";

    public static final String TYPE_CONNECTED_BETA_UPLOAD_FILE = "connectedBetaUploadFile";
    public static final String TYPE_CONNECTED_DEBUG_ZIP_UPLOAD = "connectedDebugZipUploadFile";
    public static final String ANY_IMAGE_FILE_EXTENSIONS = "gif,jpg,jpeg,pdf,rtf,tif,tiff,img,png,dxf,dlf,docx,eps,pseg";

    private CacheDataService cacheDataService;
    private CacheDataRepository cacheDataRepository;

    public AsyncFileUploadController(
            CacheDataService cacheDataService,
            CacheDataRepository cacheDataRepository
    ) {
        this.cacheDataService = cacheDataService;
        this.cacheDataRepository = cacheDataRepository;
    }

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        // Response set to 'text/plain' instead of 'application/json' for IE compatibility with plug-in
        response.setContentType("text/plain");
        ServletOutputStream out = response.getOutputStream();

        // IMAGE LIBRARY: Upload files
        if (getTypeParam(request).equals(TYPE_IMAGE_LIBRARY) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            processImageLibraryUpload(request, out);
            // GRAPHIC CONTENT: Upload files
        } else if (getTypeParam(request).equals(TYPE_GRAPHIC_CONTENT) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            processGraphicContentUpload(request, out);
            // DATA FILE: Upload files
        } else if (getTypeParam(request).equals(TYPE_DATA_FILE) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            processDataFileUpload(request, out);
            // TEMPLATE FILES CONTENT: Upload files
        } else if (getTypeParam(request).equals(TYPE_TEMPLATE_FILES) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            processTemplateFilesUpload(request, out);
            // COMPOSITION FILE SET: Upload files
        } else if (getTypeParam(request).equals(TYPE_COMPOSITION_FILES) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleCompositionFileSetUpload(request, out);
        } else if (getTypeParam(request).equals(TYPE_COMPOSITION_FILES) && getActionParam(request).equals(ACTION_DELETE)) {
            handleCompositionFileSetDeleteFile(request, out);
            // GRAPHIC CONTENT: Upload files
        } else if (getTypeParam(request).equals(TYPE_TARGETING_DATA_FILE) && request instanceof MultipartHttpServletRequest) {
            handleGraphicContentUpload(request, out);
        } else if ((getTypeParam(request).equals(TYPE_TEXT_STYLE_FONTS_BOLD_ITALIC) || getTypeParam(request).equals(TYPE_TEXT_STYLE_FONTS_ITALIC) || getTypeParam(request).equals(TYPE_TEXT_STYLE_FONTS_BOLD)  || getTypeParam(request).equals(TYPE_TEXT_STYLE_FONTS) )&& request instanceof MultipartHttpServletRequest) {
            handleTextStyleFontUpload(request, out);
        }
        else if (getTypeParam(request).equals(TYPE_LOOKUP_TABLE_DATA) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleLookupTableContentUpload(request, out);
        } else if (getTypeParam(request).equals(TYPE_RATIONALIZER_DOCUMENTS) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleRationalizerDataUpload(request, out);
        } else if (getTypeParam(request).equals(TYPE_METADATA_UPLOAD_FILE) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleMetadataUpload(request, out);
        } else if (getTypeParam(request).equals(TYPE_CONNECTED_BETA_UPLOAD_FILE) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleConnectedBetaUpload(request, out);
        } else if (getTypeParam(request).equals(TYPE_CONNECTED_DEBUG_ZIP_UPLOAD) && getActionParam(request).equals(ACTION_UPLOAD) && request instanceof MultipartHttpServletRequest) {
            handleConnectedDebugZipFileUploadFiles(request, out, response);
        }
        return null;
    }

    private void processImageLibraryUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        String[] imageNames = multipartRequest.getParameterValues("imageNames[]");
        String[] metatags = multipartRequest.getParameterValues("metatags");
        String[] variableContentEnabled = multipartRequest.getParameterValues("variableContentEnabled");
        String[] acceptFileTypes = multipartRequest.getParameterValues("acceptFileTypes");

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        Set<Long> docIds = new HashSet<>();
        if (UserUtil.getCurrentGlobalContext()) {
            for (Document doc : Document.findAllDocumentsAndProjectsVisible()) {
                docIds.add(doc.getId());
            }
        } else {
            docIds.add(UserUtil.getCurrentTouchpointContext().getId());
        }

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);
            // Check file type and extension
            if(acceptFileTypes != null && acceptFileTypes.length > 0 && !SecureFileUpload.canUploadFile(uploadedFile, Arrays.stream(acceptFileTypes[0].split("\\R|,|\\s")).toList())){
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), "", "image_file", request);
                returnFileObj.put("error", "File type of " + uploadedFile.getOriginalFilename() + " is not allowed");
                filesArray.put(returnFileObj);
                continue;
            }

            // Content Library Instance creation
            List<MultipartFile> fileList = new ArrayList<>();
            List<String> fileNameList = new ArrayList<>();
            fileList.add(uploadedFile);
            fileNameList.add(imageNames[0]);
            ServiceExecutionContext context = ContentObjectBulkImageCreateService.createContext(fileList, fileNameList, metatags[0], docIds, variableContentEnabled[0].equalsIgnoreCase("true"));
            Service service = MessagepointServiceFactory.getInstance().lookupService(ContentObjectBulkImageCreateService.SERVICE_NAME, ContentObjectBulkImageCreateService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(ContentObjectBulkImageCreateService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                List<String> imagePaths = (List<String>) serviceResponse.getResultValueBean();
                String imagePath = imagePaths.get(0);

                String name = uploadedFile.getOriginalFilename().equals(imageNames[0]) ?
                        imageNames[0] :
                        imageNames[0] + " [" + uploadedFile.getOriginalFilename() + "]";

                JSONObject returnFileObj = generateFileReturnObj(name, uploadedFile.getSize(), imagePath, "image_file", request);
                filesArray.put(returnFileObj);
            }
        }

        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void processGraphicContentUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);
            // Check file type and extension
            if(!SecureFileUpload.canUploadFile(uploadedFile, Arrays.stream(ANY_IMAGE_FILE_EXTENSIONS.split("\\R|,|\\s")).toList())){
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), "", "image_file", request);
                returnFileObj.put("error", "File type of " + uploadedFile.getOriginalFilename() + " is not allowed");
                filesArray.put(returnFileObj);
                continue;
            }

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, SandboxFile.TYPE_IMAGE_FILE);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                filesArray.put(returnFileObj);
            }

        }
        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void processDataFileUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, SandboxFile.TYPE_DATA_FILE);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                filesArray.put(returnFileObj);
            }

        }
        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void processTemplateFilesUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        long documentId = ServletRequestUtils.getLongParameter(request, "documentId", -1L);
        long templateVariantId = ServletRequestUtils.getLongParameter(request, REQ_PARM_TEMPLATE_VARIANT_ID, -1L);
        if (documentId == -1 || templateVariantId == -1) {

            log.error("Exception: Bad parameter on alternate template file package upload");
            returnObj.put("error", "Exception: Bad parameter on alternate template file package upload");

        } else {

            Document document = Document.findById(documentId);
            TemplateVariant templateVariant = null;
            if (templateVariantId == Long.MAX_VALUE) {
                templateVariant = new TemplateVariant();
                templateVariant.setTouchpoint(document);
                // new templateVariant
            } else if (templateVariantId > 0) {
                // existing variant, means update
                templateVariant = TemplateVariant.findById(templateVariantId);
            } else {
                // none
            }
            if (templateVariant != null) {

                for (String currentIndex : fileMap.keySet()) {

                    MultipartFile uploadedFile = fileMap.get(currentIndex);
                    String path = EmailTemplateUtils.getFilerootTemplateVariantTempPath(documentId, templateVariant.getGuid());
                    try {

                        // Make any directories that aren't yet in existence.
                        File directory = new File(path);
                        directory.mkdirs();

                        // Delete any existing files in directory since there should only
                        // ever be one in each folder.
                        File[] filesInDirectory = directory.listFiles();
                        if (filesInDirectory != null) {
                            for (File fileToDelete : filesInDirectory) {
                                if (!fileToDelete.isDirectory()) {
                                    fileToDelete.delete();
                                }
                            }
                        }

                        String fileLocation = path + uploadedFile.getOriginalFilename();
                        uploadedFile.transferTo(new File(fileLocation));
                        //return fileLocation;
                        JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), fileLocation, TYPE_TEMPLATE_FILES, request);
                        if (templateVariantId == 0 || templateVariantId == Long.MAX_VALUE) {
                            returnFileObj.put("guid", templateVariant.getGuid());
                        }
                        filesArray.put(returnFileObj);

                    } catch (IOException e) {
                        log.error("File write error: Failed to write temp file", e);
                    }
                }
                returnObj.put("files", filesArray);
            }
        }

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleMetadataUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, SandboxFile.TYPE_METADATA_UPLOAD_FILE);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                filesArray.put(returnFileObj);
            }

        }
        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleRationalizerDataUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARM_RATIONALIZER_APP_ID, -1L);
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        boolean isIngestionUpload = Boolean.parseBoolean(multipartRequest.getParameterValues("isIngestionUpload")[0]);

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        final RationalizerXmlImportRemoveDuplicatesParams rationalizerXmlImportRemoveDuplicatesParams = buildRationalizerXmlImportRemoveDuplicatesParams(multipartRequest);

        if (isIngestionUpload) {
            for (String currentIndex : fileMap.keySet()) {
                JSONObject returnFileObj = handleRationalizerIngestionUploadFiles(request, rationalizerApplication, fileMap.get(currentIndex), rationalizerXmlImportRemoveDuplicatesParams);
                filesArray.put(returnFileObj);
            }
        } else {
            for (String currentIndex : fileMap.keySet()) {
                JSONObject returnFileObj = handleRationalizerUploadedFile(request, rationalizerApplication, fileMap.get(currentIndex), rationalizerXmlImportRemoveDuplicatesParams);
                filesArray.put(returnFileObj);
            }
        }
        returnObj.put("files", filesArray);
        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private RationalizerXmlImportRemoveDuplicatesParams buildRationalizerXmlImportRemoveDuplicatesParams(MultipartHttpServletRequest multipartRequest) {
        final RationalizerXmlImportRemoveDuplicatesParams params = new RationalizerXmlImportRemoveDuplicatesParams();

        boolean generateSharedContents = Boolean.parseBoolean(multipartRequest.getParameterValues("generateSharedContents")[0]);
        String generatedSharedContentPrefix = multipartRequest.getParameterValues("generatedSharedContentPrefix")[0];
        boolean includeContentCharsInGeneratedSharedContent = Boolean.parseBoolean(multipartRequest.getParameterValues("includeContentCharsInGeneratedSharedContent")[0]);
        int generatedSharedContentMinWordCount = Integer.parseInt(multipartRequest.getParameterValues("generatedSharedContentMinWordCount")[0]);

        params.setGenerateSharedContents(generateSharedContents);
        params.setGeneratedSharedContentPrefix(generatedSharedContentPrefix);
        params.setAutoNumberStartingAt(null);
        if (includeContentCharsInGeneratedSharedContent) {
            params.setKeepFirstNCharactersOfContent(RationalizerXmlImportRemoveDuplicatesParams.KEEP_FIRST_N_CHARACTERS_OF_CONTENT_DEFAULT);
        } else {
            params.setKeepFirstNCharactersOfContent(0);
        }
        params.setGeneratedSharedContentMinWordCount(generatedSharedContentMinWordCount);
        params.setNamePartsSeparator(" ");

        return params;
    }

    private void handleConnectedBetaUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {
            MultipartFile uploadedFile = fileMap.get(currentIndex);
            JSONObject returnFileObj = generateConnectedBetaFileReturnObj(
                    uploadedFile.getOriginalFilename(), uploadedFile.getContentType(), uploadedFile.getSize(),
                    DatatypeConverter.printBase64Binary(uploadedFile.getBytes()));
            filesArray.put(returnFileObj);
        }
        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleConnectedDebugZipFileUploadFiles(HttpServletRequest request, ServletOutputStream out, HttpServletResponse response) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String communicationId = request.getParameter("communicationId");
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        List<String> acceptedFiles = Arrays.asList("selections.json", "VariableValues.json", "RecipientContents.json");
        for (String currentIndex : fileMap.keySet()) {
            MultipartFile uploadedFile = fileMap.get(currentIndex);
            log.info("Import connected debug zip file with name " + uploadedFile.getOriginalFilename());

            ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(uploadedFile.getBytes()));
            ZipEntry entry = null;
            JSONObject result = new JSONObject();
            int numberOfBytesRead = 0;
            byte[] data = new byte[524288];
            ByteArrayOutputStream boas = new ByteArrayOutputStream();
            String fileName;
            while ((entry = zis.getNextEntry()) != null) {
                fileName = entry.getName();
                if (entry.getName().contains(File.separator) && entry.getName().split(File.separator).length > 1) {
                    fileName = entry.getName().split(File.separator)[1];
                }
                if (acceptedFiles.contains(fileName)) {
                    log.info("Extracting: " + fileName + " file from debug zip");
                    data = new byte[524288];
                    numberOfBytesRead = 0;
                    while ((numberOfBytesRead = zis.read(data)) != -1) {
                        boas.write(data, 0, numberOfBytesRead);
                        boas.flush();
                    }
                    String jsonString = new String(boas.toByteArray());
                    if (!StringUtils.EMPTY.equals(jsonString)) {
                        try {
                            result.put(fileName, new JSONObject(jsonString));
                        } catch (Exception e) {

                        }
                        boas.reset();
                    }
                }
            }
            boas.close();
            zis.close();

            try {
                if (result.getJSONObject("selections.json") == null
                        || result.getJSONObject("selections.json") == null ||
                        result.getJSONObject("selections.json") == null) {
                    log.error("Exception: uploaded zip does not contain the required files.");
                    out.println("Exception: uploaded zip does not contain the required files.");
                    out.flush();
                    response.setStatus(HttpStatus.SC_NOT_ACCEPTABLE);
                    return;
                }
                Long toSelectionId = result.getJSONObject("selections.json")
                        .getJSONArray("DocumentList").getJSONObject(0).getLong("touchpointselectionid");
                String masterGUID = null;
                long masterId = 0;
                String alternateLayoutGUID = null;
                long alternateId = 0;
                Document document = null;

                if (toSelectionId != null && toSelectionId != 0) {
                    TouchpointSelection touchpointSelection = TouchpointSelection.findById(toSelectionId);
                    masterGUID = touchpointSelection.getDocument().getGuid();
                    masterId = touchpointSelection.getDocument().getId();
                    Document alternateLayout = touchpointSelection.getAlternateLayout();
                    if (alternateLayout != null) {
                        alternateLayoutGUID = alternateLayout.getGuid();
                        alternateId = alternateLayout.getId();
                    }
                } else {
                    masterId = result.getJSONObject("selections.json")
                            .getJSONArray("DocumentList").getJSONObject(0).getLong("id");
                    document = Document.findById(masterId);
                    masterGUID = document.getGuid();
                    alternateLayoutGUID = masterGUID;
                    alternateId = masterId;
                }
                cacheDataService.saveDebugFilesData(masterGUID, masterId, alternateLayoutGUID, alternateId, result.toString(),
                        Long.valueOf(communicationId));
            } catch (Exception e) {
                log.error("Exception: Unable to complete async file upload request for debug: " + e.getMessage(), e);
                out.println("Exception: Unable to complete async file upload request for debug.");
                out.flush();
                response.setStatus(HttpStatus.SC_NOT_ACCEPTABLE);
            }
        }
    }

    private JSONObject handleRationalizerIngestionUploadFiles(
            HttpServletRequest request,
            RationalizerApplication rationalizerApplication,
            MultipartFile uploadedFile,
            RationalizerXmlImportRemoveDuplicatesParams rationalizerXmlImportRemoveDuplicatesParams
    ) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        String[] documentNames = multipartRequest.getParameterValues("documentNames[]");
        String[] metatags = multipartRequest.getParameterValues("metatags");
        int noOfFilesToBeUploaded = Integer.parseInt(multipartRequest.getParameterValues("noOfFilesToBeUploaded")[0]);
        String requestGuid = String.valueOf(multipartRequest.getParameterValues("requestGuid")[0]);

        Map<String, Map<String, List<String>>> uploadedIngestionFilesMapForRequest = getSessionStoredRationalizerIngestionUploadedMap(rationalizerApplication);
        Map<String, List<String>> uploadedIngestionFilesMap = getRationalizerIngestionUploadedMapByRequest(requestGuid, uploadedIngestionFilesMapForRequest, rationalizerApplication);

        Map<String, Integer> uploadedIngestionFilesNoForRequest = getSessionStoredRationalizerIngestionUploadedDocsNo(rationalizerApplication);
        Integer uploadedIngestionDocsNo = uploadedIngestionFilesNoForRequest.get(requestGuid) == null ? 0 : uploadedIngestionFilesNoForRequest.get(requestGuid);

        RationalizerUploadIngestionDto rationalizerUploadIngestionDto = new RationalizerUploadIngestionDto(multipartRequest, rationalizerApplication, uploadedIngestionFilesMap, uploadedIngestionDocsNo);
        ServiceExecutionContext context = RationalizerIngestionUploadService.createContextForIngestionUpload(uploadedFile, metatags[0], rationalizerApplication, rationalizerUploadIngestionDto);
        RationalizerIngestionUploadService rationalizerIngestionService = (RationalizerIngestionUploadService) ApplicationUtil.getBean("rationalizerIngestionUploadService");
        rationalizerIngestionService.execute(context);
        ServiceResponse serviceResponse = context.getResponse();

        String name = uploadedFile.getOriginalFilename().equals(documentNames[0]) ? documentNames[0] : documentNames[0] + " [" + uploadedFile.getOriginalFilename() + "]";

        storeUploadedIngestionFilesMap(requestGuid, uploadedIngestionFilesMapForRequest, uploadedIngestionFilesMap, rationalizerApplication);
        storeUploadedIngestionFilesDocsNo(requestGuid, uploadedIngestionFilesNoForRequest, rationalizerUploadIngestionDto.getUploadedIngestionDocsNo(), rationalizerApplication);
        //wait for all uploaded files before starting RationalizerUploadBackgroundTask for all of them
        if (uploadedIngestionFilesMap.size() == noOfFilesToBeUploaded) {
            Integer systemPropMaxDocs = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.DefaultValues.KEY_RationalizerMaxDocPerApp));
            int maxAlloweDocsPerApp =  systemPropMaxDocs != null ? systemPropMaxDocs.intValue() : 500;
            JSONObject errorMessage = checkForUploadDocLimitPerApplication(rationalizerApplication, uploadedFile, uploadedIngestionFilesNoForRequest.get(requestGuid), maxAlloweDocsPerApp);
            if(errorMessage != null) {
                //delete files uploaded documents and send email notif
                deleteUploadedDocs(uploadedIngestionFilesMap.values());
                sendUploadLimitNotificationEmail(maxAlloweDocsPerApp);
                uploadedIngestionFilesMapForRequest.remove(requestGuid);
                RedisUtil.getRedisContext().putValue(
                        buildUploadedIngestionFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                        (Serializable) uploadedIngestionFilesMapForRequest
                );
                uploadedIngestionFilesNoForRequest.remove(requestGuid);
                RedisUtil.getRedisContext().putValue(
                        buildUploadedIngestionFilesDocNoByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                        (Serializable) uploadedIngestionFilesNoForRequest
                );
                return errorMessage;
            }

            RationalizerUploadBackgroundTask uploadTask = new RationalizerUploadBackgroundTask(
                    rationalizerUploadIngestionDto,
                    Arrays.toString(metatags),
                    UserUtil.getPrincipalUser(),
                    rationalizerXmlImportRemoveDuplicatesParams
            );
            MessagePointRunnableUtil.startThread(uploadTask, Thread.MAX_PRIORITY);

            uploadedIngestionFilesMapForRequest.remove(requestGuid);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedIngestionFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedIngestionFilesMapForRequest
            );
            uploadedIngestionFilesNoForRequest.remove(requestGuid);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedIngestionFilesDocNoByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedIngestionFilesNoForRequest
            );
        }
        if (!serviceResponse.isSuccessful()) {
            JSONObject returnFileObj = new JSONObject();
            try {
                returnFileObj.put("name", name);
                returnFileObj.put("size", uploadedFile.getSize());
                returnFileObj.put("error", " ");
            } catch (JSONException e) {
                log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            }

            return returnFileObj;
        }

        return generateFileReturnObj(name, uploadedFile.getSize(), null, TYPE_RATIONALIZER_DOCUMENTS, request);
    }

    private void deleteUploadedDocs(Collection<List<String>> collection) {
        List<String> uploadedFileNames = new ArrayList<>();
        for(List<String> listUploaded : collection){
            uploadedFileNames.addAll(listUploaded);
        }
        for (String fileNameToDelete : uploadedFileNames) {
            File fileToDelete = new File(fileNameToDelete);
            if(fileToDelete.exists()) {
                fileToDelete.delete();
            }
        }
    }

    private JSONObject checkForUploadDocLimitPerApplication(RationalizerApplication rationalizerApplication, MultipartFile uploadedFile,  int uploadedFilesNo, int maxAlloweDocsPerApp) {
        int noOfDocsAlreadyUploaded = rationalizerApplication.computeDocumentCount();

        if(uploadedFilesNo + noOfDocsAlreadyUploaded > maxAlloweDocsPerApp) {
            JSONObject errorMessage = new JSONObject();
            errorMessage.put("uploadLimitError", "Alert - Large Upload Detected");
            errorMessage.put("name", uploadedFile.getOriginalFilename());
            errorMessage.put("size", uploadedFile.getSize());
            errorMessage.put("error", " Exceeded Upload Limit");

            log.error("Messagepoint Alert - Large Upload Detected");
            return errorMessage;
        }
        return null;
    }

    private void sendUploadLimitNotificationEmail(int maxAllowedDocNo) {
        EmailManager emailManager = EmailUtil.getEmailManagerBean();
        String subject = ApplicationUtil.getMessage("email.rationalizer.upload.limit.title");
        String text = MessageFormat.format(ApplicationUtil.getMessage("email.rationalizer.upload.limit.body"), maxAllowedDocNo);
        emailManager.sendMail(UserUtil.getPrincipalUser().getEmail(), text, subject, "");
    }

    private JSONObject handleRationalizerUploadedFile(
            HttpServletRequest request,
            RationalizerApplication rationalizerApplication,
            MultipartFile uploadedFile,
            RationalizerXmlImportRemoveDuplicatesParams rationalizerXmlImportRemoveDuplicatesParams
    ) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        int noOfFilesToBeUploaded = Integer.parseInt(multipartRequest.getParameterValues("noOfFilesToBeUploaded")[0]);
        String requestGuid = String.valueOf(multipartRequest.getParameterValues("requestGuid")[0]);
        String[] metatags = multipartRequest.getParameterValues("metatags");

        Map<String, List<String>> uploadedXmlFilesMapForRequest = getSessionStoredRationalizerUploadedMap(requestGuid, rationalizerApplication);
        Map<String, List<String>> uploadedOrigDocFilesMapForRequest = getSessionStoredRationalizerOrigDocsUploadedMap(requestGuid, rationalizerApplication);
        ServiceExecutionContext context = RationalizerUploadService.createContextForBulkDocumentUpload(uploadedFile,
                metatags[0], rationalizerApplication, uploadedXmlFilesMapForRequest, uploadedOrigDocFilesMapForRequest, requestGuid);
        RationalizerUploadService rationalizerUploadService = (RationalizerUploadService) ApplicationUtil.getBean("rationalizerUploadService");
        rationalizerUploadService.execute(context);
        ServiceResponse serviceResponse = context.getResponse();
        RedisUtil.getRedisContext().putValue(
                buildUploadedFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                (Serializable) uploadedXmlFilesMapForRequest
        );
        RedisUtil.getRedisContext().putValue(
                buildUploadedOrigDocsMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                (Serializable) uploadedOrigDocFilesMapForRequest
        );
        //wait for all uploaded (XML, ZIP) files before starting RationalizerUploadXmlBackgroundTask for all of them
        if (uploadedXmlFilesMapForRequest.get(requestGuid).size() == noOfFilesToBeUploaded) {

            List<File> xmlFilesToUpload= new ArrayList<>();
            int uploadedDocsNo = 0;
            for(String path : uploadedXmlFilesMapForRequest.get(requestGuid)) {
                if(path != null) {
                    xmlFilesToUpload.add(new File(path));
                    uploadedDocsNo = uploadedDocsNo + countRatDocsInXMLUpload(path);
                }
            }

            if (CollectionUtils.isNotEmpty(xmlFilesToUpload)) {
                Integer systemPropMaxDocs = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.DefaultValues.KEY_RationalizerMaxDocPerApp));
                int maxAlloweDocsPerApp =  systemPropMaxDocs != null ? systemPropMaxDocs.intValue() : 500;
                JSONObject errorMessage = checkForUploadDocLimitPerApplication(rationalizerApplication, uploadedFile, uploadedDocsNo, maxAlloweDocsPerApp);
                if(errorMessage != null) {
                    List<File> filesToDelete= new ArrayList<>();
                    for(String path : uploadedXmlFilesMapForRequest.get(requestGuid)) {
                        if(path != null) {
                            filesToDelete.add(new File(path));
                        }
                    }
                    for(String path : uploadedOrigDocFilesMapForRequest.get(requestGuid)) {
                        if(path != null) {
                            filesToDelete.add(new File(path));
                        }
                    }

                    for (File xmlFileNameToDelete : filesToDelete) {
                        if(xmlFileNameToDelete.exists()) {
                            xmlFileNameToDelete.delete();
                        }
                    }
                    sendUploadLimitNotificationEmail(maxAlloweDocsPerApp);
                    uploadedXmlFilesMapForRequest.remove(requestGuid);
                    uploadedOrigDocFilesMapForRequest.remove(requestGuid);
                    RedisUtil.getRedisContext().putValue(
                            buildUploadedFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                            (Serializable) uploadedXmlFilesMapForRequest
                    );
                    RedisUtil.getRedisContext().putValue(
                            buildUploadedOrigDocsMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                            (Serializable) uploadedOrigDocFilesMapForRequest
                    );
                    return errorMessage;
                }

                RationalizerUploadBackgroundTask uploadTask = new RationalizerUploadBackgroundTask(
                        xmlFilesToUpload,
                        metatags[0],
                        rationalizerApplication,
                        UserUtil.getPrincipalUser(),
                        rationalizerXmlImportRemoveDuplicatesParams
                );
                MessagePointRunnableUtil.startThread(uploadTask, Thread.MAX_PRIORITY);
            }
            uploadedXmlFilesMapForRequest.remove(requestGuid);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedXmlFilesMapForRequest
            );
            uploadedOrigDocFilesMapForRequest.remove(requestGuid);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedOrigDocsMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedOrigDocFilesMapForRequest
            );
        }
        if (!serviceResponse.isSuccessful()) {
            log.error(buildServiceCallErrorMessage(RationalizerUploadService.SERVICE_NAME));

            return null;
        }

        return generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), null, TYPE_RATIONALIZER_DOCUMENTS, request);
    }


    private int countRatDocsInXMLUpload(String path){
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        int count = 0;
        try (InputStream is = new FileInputStream(path)) {
            DocumentBuilder db = dbf.newDocumentBuilder();
            org.w3c.dom.Document doc = db.parse(is);
            XPath xpath = XPathFactory.newInstance().newXPath();
            NodeList nodes = (NodeList)
                    xpath.evaluate("//Document", doc, XPathConstants.NODESET);
            count = nodes.getLength();
            return count;
        } catch (ParserConfigurationException | IOException | XPathExpressionException | org.xml.sax.SAXException e) {
           log.warn(e.getMessage());
        }
        return count;
    }

    private String buildServiceCallErrorMessage(String serviceName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(serviceName);
        stringBuilder.append(" service call is not successful ");
        stringBuilder.append(" in ").append(this.getClass().getName());
        return stringBuilder.toString();
    }

    private void handleLookupTableContentUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, SandboxFile.TYPE_LOOKUP_TABLE_DATA_FILE);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                filesArray.put(returnFileObj);
            }

        }
        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleTextStyleFontUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);

            MultipartFile ttfFile = null;

            if (uploadedFile.getOriginalFilename().toUpperCase().endsWith(".TTF") || uploadedFile.getOriginalFilename().toUpperCase().endsWith(".OTF")) {
                ttfFile = uploadedFile;
            }

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, ttfFile != null ? SandboxFile.TYPE_FONT_TTF : SandboxFile.TYPE_FONT_EOT);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                returnFileObj.put("fontType", ttfFile != null ? "ttf" : "eot");
                filesArray.put(returnFileObj);
            }

        }

        try {
            HibernateUtil.getManager().getSession().flush();
            cacheDataRepository.updateStylesData();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleGraphicContentUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        for (String currentIndex : fileMap.keySet()) {

            MultipartFile uploadedFile = fileMap.get(currentIndex);

            ServiceExecutionContext context = CreateOrUpdateSandboxFileService.createContextForCreate(uploadedFile, SandboxFile.TYPE_TARGETING_DATA_FILE);
            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateSandboxFileService.SERVICE_NAME, CreateOrUpdateSandboxFileService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();
            if (!serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
                sb.append(" service call is not successful ");
                sb.append(" in ").append(this.getClass().getName());
                log.error(sb.toString());
            } else {
                Long sandboxFileId = (Long) serviceResponse.getResultValueBean();
                JSONObject returnFileObj = generateFileReturnObj(uploadedFile.getOriginalFilename(), uploadedFile.getSize(), String.valueOf(sandboxFileId), TYPE_SANDBOX_FILE, request);
                returnFileObj.put("sandboxId", sandboxFileId);
                filesArray.put(returnFileObj);
            }

        }

        returnObj.put("files", filesArray);

        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleCompositionFileSetUpload(HttpServletRequest request, ServletOutputStream out) throws JSONException, IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

        String[] metatags = multipartRequest.getParameterValues("metatags");
        String[] touchpointDefault = multipartRequest.getParameterValues("isTouchpointDefault");
        String[] collectionDefault = multipartRequest.getParameterValues("isCollectionDefault");

        Long documentId = ServletRequestUtils.getLongParameter(request, "documentId", -1L);
        long tpCollectionId = ServletRequestUtils.getLongParameter(request, "tpCollectionId", -1L);
        String fileUploadSyncKey = ServletRequestUtils.getStringParameter(request, "fileUploadSyncKey", null);
        long compositionFileSetId = ServletRequestUtils.getLongParameter(request, "compositionFileSetId", -1L);
        Boolean isTouchpointDefault = touchpointDefault[0].equalsIgnoreCase("true");
        Boolean isCollectionDefault = collectionDefault[0].equalsIgnoreCase("true");

        JSONObject returnObj = new JSONObject();
        JSONArray filesArray = new JSONArray();

        if ((documentId == -1 && tpCollectionId == -1) || (fileUploadSyncKey == null && compositionFileSetId == -1)) {

            log.error("Exception: Bad parameter on composition file package upload");
            returnObj.put("error", "Exception: Bad parameter on composition file package upload");

        } else {

            CompositionFileSet compositionFileSet = null;
            String oldName = "";
            if (compositionFileSetId > 0) {
                compositionFileSet = CompositionFileSet.findById(compositionFileSetId);
                if (compositionFileSet != null) {
                    oldName = compositionFileSet.getName();
                }
            } else {
                compositionFileSet = CompositionFileSet.findBySyncKey(fileUploadSyncKey);
            }
            Document document = Document.findById(documentId);
            boolean orgIsTouchpointDefault = false;

            if (document != null && document.getCompositionFileSet() != null && compositionFileSet != null && document.getCompositionFileSet().getId() == compositionFileSet.getId()) {
                orgIsTouchpointDefault = true;
            }

            Set<MultipartFile> additionalFiles = new HashSet<>();

            for (String currentIndex : fileMap.keySet()) {

                MultipartFile uploadedFile = fileMap.get(currentIndex);

                MultipartFile compConfigFile = null;
                MultipartFile templateFile = null;
                MultipartFile zipFile = null;

                boolean isSefasMPHCSTouchpoint = false;
                TouchpointCollection tpCollection = TouchpointCollection.findById(tpCollectionId);
                if (document != null)
                    isSefasMPHCSTouchpoint = document.isSefasCompositionTouchpoint() || document.isMPHCSCompositionTouchpoint();
                if (tpCollection != null)
                    isSefasMPHCSTouchpoint = tpCollection.getIsSefasCollection() || tpCollection.getIsMPHCSCollection();

                if ( ( (isSefasMPHCSTouchpoint) && uploadedFile.getOriginalFilename().toUpperCase().endsWith(".ZIP")) ||
                        uploadedFile.getOriginalFilename().toUpperCase().endsWith(".PUB") ||
                        uploadedFile.getOriginalFilename().toUpperCase().endsWith(".WFD")) {
                    compConfigFile = uploadedFile;
                } else if (uploadedFile.getOriginalFilename().toUpperCase().endsWith("_ADDZIP.ZIP")) {  //Temp solution for additional zip file (CDD-5652)
                    additionalFiles.add(uploadedFile);
                } else if (uploadedFile.getOriginalFilename().toUpperCase().endsWith(".ZIP")) {
                    zipFile = uploadedFile;
                } else if (uploadedFile.getOriginalFilename().toUpperCase().equals("CFTEMPLATE.DAT")) {
                    templateFile = uploadedFile;
                } else {
                    additionalFiles.add(uploadedFile);
                }

                boolean isValidCompPack = true;

                // If it is ZIP file, verify it contains PUB or WFD (not TempControlled XML) file inside
                String compFileInZipName = null;

                //If it is a touchpoint collection, double check for the touchpoint collections to have same connector
                if (tpCollection != null && !allDocumentsHaveSameConnectorId(tpCollection)) {
                    JSONObject returnFileObj = new JSONObject();
                    returnFileObj.put("error", ApplicationUtil.getMessage("error.message.packaged.touchpoint.collections.must.be.same.connector"));
                    filesArray.put(returnFileObj);
                }

                if (zipFile != null) {

                    Connector connector = null;
                    if (tpCollection != null) {
                        connector = tpCollection.getDocuments().get(0).getConnectorConfiguration().getConnector();
                    } else if (document != null) {
                        connector = document.getConnectorConfiguration().getConnector();
                    }

                    ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(zipFile.getBytes()));
                    ZipEntry entry = null;

                    while ((entry = zis.getNextEntry()) != null) {
                        String zipPath = entry.getName();
                        if (!entry.isDirectory()) {
                            if (!zipPath.toUpperCase().endsWith(".WFD") && !zipPath.toUpperCase().endsWith(".PUB")) {
                                isValidCompPack = false;
                                break;
                            } else if ((zipPath.toUpperCase().endsWith(".PUB") && connector.getId() != Connector.DIALOGUE_FILE_ID)
                                    || (zipPath.toUpperCase().endsWith(".WFD") && connector.getId() != Connector.GMC_FILE_ID)) {
                                isValidCompPack = false;
                                break;
                            } else if (!zipPath.startsWith("__MACOSX")) {
                                compConfigFile = new MockMultipartFile(zipPath, zipPath, null, entry.getExtra());
                                compFileInZipName = zipPath;
                            }
                        } else {
                            if (!zipPath.contains("__MACOSX")) {
                                isValidCompPack = false;
                                break;
                            }
                        }
                    }
                    zis.close();
                }


                if (!isValidCompPack) {

                    JSONObject returnFileObj = new JSONObject();
                    returnFileObj.put("name", zipFile.getOriginalFilename());
                    returnFileObj.put("error", ApplicationUtil.getMessage("error.message.zip.does.not.contain.valid.composition.file"));
                    filesArray.put(returnFileObj);

                }
                else {

                    ServiceExecutionContext context = null;
                    if (compositionFileSet == null) {
                        context = CreateOrUpdateCompositionFileSetService.createContextForNew(metatags[0], compConfigFile, templateFile, additionalFiles, documentId, tpCollectionId, isTouchpointDefault, isCollectionDefault, fileUploadSyncKey);
                    } else {
                        context = CreateOrUpdateCompositionFileSetService.createContextForUpdate(compositionFileSet.getId(), metatags[0], compConfigFile, templateFile, additionalFiles, isTouchpointDefault, isCollectionDefault, fileUploadSyncKey);
                    }

                    Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCompositionFileSetService.SERVICE_NAME, CreateOrUpdateCompositionFileSetService.class);
                    service.execute(context);
                    ServiceResponse serviceResponse = context.getResponse();
                    if (!serviceResponse.isSuccessful()) {

                        StringBuilder sb = new StringBuilder();
                        sb.append(CreateOrUpdateCompositionFileSetService.SERVICE_NAME);
                        sb.append(" service call is not successful ");
                        sb.append(" in ").append(this.getClass().getName());
                        log.error(sb.toString());

                    } else {

                        JSONObject returnFileObj = generateFileReturnObj(compFileInZipName != null ? compFileInZipName : uploadedFile.getOriginalFilename(), uploadedFile.getSize(), null, TYPE_COMPOSITION_FILES, request);
                        filesArray.put(returnFileObj);

                        // Audit (Composition Package Add/Edit)
                        if (document != null && !filesArray.isEmpty()) {
                            String action = "";
                            StringBuilder fileNames = new StringBuilder();
                            for (int i = 0; i < filesArray.length(); i++) {
                                if (i > 0) {
                                    fileNames.append(", ");
                                }
                                fileNames.append(filesArray.getJSONObject(i).get("name"));
                            }
                            if (compositionFileSet != null) {
                                action = compositionFileSet.getName() + " updated with new files: " + fileNames;
                                if (!oldName.equals(metatags[0])) {
                                    action += ", package name changed from: " + oldName + " to: " + metatags[0];
                                }
                            } else {
                                action = metatags[0] + " added with new files: " + fileNames;
                            }
                            if (isTouchpointDefault != orgIsTouchpointDefault) {
                                action += " TOUCHPOINT DEFAULT changed from " + (orgIsTouchpointDefault ? ApplicationUtil.getMessage("page.label.yes") : ApplicationUtil.getMessage("page.label.no"))
                                        + " to " + (isTouchpointDefault ? ApplicationUtil.getMessage("page.label.yes") : ApplicationUtil.getMessage("page.label.no"));
                            }
                            AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, document.getName(), document.getId(), AuditActionType.ID_CHANGES,
                                    AuditMetadataBuilder.forSimpleAuditMessage(action, null, null));
                        }

                    }
                }
            }
            returnObj.put("files", filesArray);
        }
        try {
            out.write(returnObj.toString().getBytes());
            out.flush();
        } catch (Exception e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
            out.println("Exception: Unable to complete async file upload request.");
            out.flush();
        }
    }

    private void handleCompositionFileSetDeleteFile(HttpServletRequest request, ServletOutputStream out) {
        Long compositionFileSetId = ServletRequestUtils.getLongParameter(request, "compositionFileSetId", -1L);
        long removeFileId = ServletRequestUtils.getLongParameter(request, "fileid", -1L);
//        CompositionFileSet fileSet = CompositionFileSet.findById(compositionFileSetId);
        CompositionFileSet fileSet = HibernateUtil.getManager().getObject(CompositionFileSet.class, compositionFileSetId);
        if (fileSet != null) {
            DatabaseFile remove = null;
            for (DatabaseFile file : fileSet.getAdditionalFiles()) {
                if (file.getId() == removeFileId) {
                    remove = file;
                    break;
                }
            }
            if (remove != null) {
                String fileName = remove.getFileName();
                fileSet.getAdditionalFiles().remove(remove);
                remove.delete();
                if (fileSet.getDocument() != null) {
                    // Audit (Composition file delete)
                    String action = fileName + " deleted from package: " + fileSet.getName();
                    AuditEventUtil.push(AuditEventType.ID_TOUCHPOINT_SETTING, AuditObjectType.ID_TOUCHPOINT, fileSet.getDocument().getName(), fileSet.getDocument().getId(), AuditActionType.ID_CHANGES,
                            AuditMetadataBuilder.forSimpleAuditMessage(action, null, null));

                    String chnSo = "# Additional DB file: (" + remove.getFileName() + ",";
                    chnSo = chnSo + remove.getFileContent().length + "B,";
                    chnSo = chnSo + remove.getContentType() + ") > ";

                    AuditEventUtil.push(AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_COMPOSITION_PKG, fileSet.getName(), fileSet.getId(), AuditActionType.ID_CHANGE_EDITED, chnSo);
                }
            }
            HibernateUtil.getManager().saveObject(fileSet);
        }
    }

    private JSONObject generateFileReturnObj(String filename, long size, String file, String type, HttpServletRequest request) {

        JSONObject returnFileObj = new JSONObject();

        try {

            String imgURL = "";
            if (type.equals(TYPE_SANDBOX_FILE) || type.equals(TYPE_TEMPLATE_FILES)) {
                imgURL = file + "&type=" + type;
            } else if (type.equals(TYPE_COMPOSITION_FILES) || type.equals(TYPE_RATIONALIZER_DOCUMENTS) || type.equals(TYPE_RATIONALIZER_ZIP_FILES)) {
                imgURL = null;
            } else {
                imgURL = ApplicationUtil.getWebRoot() +
                        "download/image.form?resource=" + HttpRequestUtil.getFileResourceToken(file) +
                        "&type=" + (type.equals("image_file") && file.toLowerCase().contains(".pdf") ? "rendered_pdf" : type) +
                        ApplicationUtil.getCSRFTokenParam(request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY));
            }

            returnFileObj.put("name", filename);
            returnFileObj.put("size", size);
            returnFileObj.put("url", imgURL);
            returnFileObj.put("thumbnail_url", imgURL);
            returnFileObj.put("delete_url", ApplicationUtil.getWebRoot() +
                    "uploadFileHandler.form?type=" + type +
                    "&action=delete" +
                    "&itemId=" + file +
                    ApplicationUtil.getCSRFTokenParam(request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY)));
            returnFileObj.put("delete_type", "DELETE");

        } catch (JSONException e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
        }

        return returnFileObj;
    }

    private JSONObject generateConnectedBetaFileReturnObj(String filename, String contentType, long size, String content) {

        JSONObject returnFileObj = new JSONObject();

        try {

            returnFileObj.put("name", filename);
            returnFileObj.put("contentType", contentType);
            returnFileObj.put("size", size);
            returnFileObj.put("content", content);

        } catch (JSONException e) {
            log.error("Exception: Unable to complete async file upload request: " + e.getMessage(), e);
        }

        return returnFileObj;
    }

    private String getTypeParam(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, null);
    }

    private String getActionParam(HttpServletRequest request) {
        return ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);
    }

    private Long getItemIdParam(HttpServletRequest request) {
        return ServletRequestUtils.getLongParameter(request, REQ_PARM_ITEM_ID, -1L);
    }

    /**
     * Gets the stored on session map of uploaded files for rationalizer upload - non ingestion. The map key is the request guid. The map value is the List of file Path to be uploaded.
     *
     */
    private Map<String, List<String>> getSessionStoredRationalizerUploadedMap(String requestGuid, RationalizerApplication rationalizerApplication) {
        Map<String, List<String>> uploadedFilesMapForRequest;
        final Map uploadedFilesMapByRequestId = RedisUtil.getRedisContext().getValue(
                buildUploadedFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                Map.class
        );
        if (uploadedFilesMapByRequestId == null) {
            uploadedFilesMapForRequest = new ConcurrentHashMap<>();
        } else {
            uploadedFilesMapForRequest = (Map<String, List<String>>) uploadedFilesMapByRequestId;
        }
        if (uploadedFilesMapForRequest.get(requestGuid) == null) {
            List<String> uploadedFilesSet = new LinkedList<>();
            uploadedFilesMapForRequest.put(requestGuid, uploadedFilesSet);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedFilesMapForRequest
            );
        }

        return uploadedFilesMapForRequest;
    }

    private Map<String, List<String>> getSessionStoredRationalizerOrigDocsUploadedMap(String requestGuid, RationalizerApplication rationalizerApplication) {
        Map<String, List<String>> uploadedOrigDocsMapForRequest;
        final Map uploadedFilesMapByRequestId = RedisUtil.getRedisContext().getValue(
                buildUploadedOrigDocsMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                Map.class
        );
        if (uploadedFilesMapByRequestId == null) {
            uploadedOrigDocsMapForRequest = new ConcurrentHashMap<>();
        } else {
            uploadedOrigDocsMapForRequest = (Map<String, List<String>>) uploadedFilesMapByRequestId;
        }
        if (uploadedOrigDocsMapForRequest.get(requestGuid) == null) {
            List<String> uploadedFilesSet = new LinkedList<>();
            uploadedOrigDocsMapForRequest.put(requestGuid, uploadedFilesSet);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedOrigDocsMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedOrigDocsMapForRequest
            );
        }

        return uploadedOrigDocsMapForRequest;
    }


    private void storeUploadedIngestionFilesMap(String requestGuid, Map<String, Map<String, List<String>>> uploadedIngestionFilesMapForRequest,
                                                Map<String, List<String>> uploadedIngestionFilesMap, RationalizerApplication rationalizerApplication
    ) {
        uploadedIngestionFilesMapForRequest.put(requestGuid, uploadedIngestionFilesMap);
        RedisUtil.getRedisContext().putValue(
                buildUploadedIngestionFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                (Serializable) uploadedIngestionFilesMapForRequest
        );
    }

    private void storeUploadedIngestionFilesDocsNo(String requestGuid, Map<String, Integer> uploadedIngestionFilesDocsNoForRequest,
                                                Integer uploadedIngestionFilesDocsNo, RationalizerApplication rationalizerApplication
    ) {
        uploadedIngestionFilesDocsNoForRequest.put(requestGuid, uploadedIngestionFilesDocsNo);
        RedisUtil.getRedisContext().putValue(
                buildUploadedIngestionFilesDocNoByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                (Serializable) uploadedIngestionFilesDocsNoForRequest
        );
    }
    private Map<String, Map<String, List<String>>> getSessionStoredRationalizerIngestionUploadedMap(RationalizerApplication rationalizerApplication) {
        Map<String, Map<String, List<String>>> uploadedIngestionFilesMapForRequest;
        final Map uploadedIngestionFilesMapByRequestId = RedisUtil.getRedisContext().getValue(
                buildUploadedIngestionFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                Map.class
        );
        if (uploadedIngestionFilesMapByRequestId == null) {
            uploadedIngestionFilesMapForRequest = new ConcurrentHashMap<>();
        } else {
            uploadedIngestionFilesMapForRequest = (Map<String, Map<String, List<String>>>) uploadedIngestionFilesMapByRequestId;
        }
        return uploadedIngestionFilesMapForRequest;
    }

    private Map<String, Integer> getSessionStoredRationalizerIngestionUploadedDocsNo(RationalizerApplication rationalizerApplication) {
        Map<String, Integer> uploadedIngestionFilesMapForRequest;
        final Map uploadedIngestionFilesDocNoByRequestId = RedisUtil.getRedisContext().getValue(
                buildUploadedIngestionFilesDocNoByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                Map.class
        );
        if (uploadedIngestionFilesDocNoByRequestId == null) {
            uploadedIngestionFilesMapForRequest = new ConcurrentHashMap<>();
        } else {
            uploadedIngestionFilesMapForRequest = (Map<String, Integer>) uploadedIngestionFilesDocNoByRequestId;
        }
        return uploadedIngestionFilesMapForRequest;
    }

    private Map<String, List<String>> getRationalizerIngestionUploadedMapByRequest(
            String requestGuid, Map<String, Map<String, List<String>>> uploadedIngestionFilesMapForRequest, RationalizerApplication rationalizerApplication
    ) {
        Map<String, List<String>> uploadedIngestionFilesMap;
        if (uploadedIngestionFilesMapForRequest.get(requestGuid) == null) {
            uploadedIngestionFilesMap = new ConcurrentHashMap<>();
            uploadedIngestionFilesMapForRequest.put(requestGuid, uploadedIngestionFilesMap);
            RedisUtil.getRedisContext().putValue(
                    buildUploadedIngestionFilesMapByRequestIdCacheKey(rationalizerApplication.getObjectSchemaName(), rationalizerApplication.getId()),
                    (Serializable) uploadedIngestionFilesMap
            );
        } else {
            uploadedIngestionFilesMap = uploadedIngestionFilesMapForRequest.get(requestGuid);
        }
        return uploadedIngestionFilesMap;
    }

    // Check if all documents in the collection have the same connector ID
    // If empty, it returns true
    private boolean allDocumentsHaveSameConnectorId(TouchpointCollection tpCollection) {
        List<Document> documents = tpCollection.getDocuments();
        if (documents == null || documents.isEmpty()) {
            return true;
        }

        // Get the connector ID of the first document
        Long firstConnectorId = documents.get(0).getConnectorConfiguration().getConnector().getId();

        // Check if all other documents have the same connector ID
        for (Document document : documents) {
            if (!firstConnectorId.equals(document.getConnectorConfiguration().getConnector().getId())) {
                return false;
            }
        }
        return true;
    }
}
