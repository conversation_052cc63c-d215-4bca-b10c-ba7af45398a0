package com.prinova.messagepoint.controller.metadata;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class MetadataFormEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		MetadataFormEditWrapper wrapper = (MetadataFormEditWrapper) commandObj;
		validateNonMandatoryInputs( wrapper, errors);
	}

	public static void validateNonMandatoryInputs(MetadataFormEditWrapper wrapper, Errors errors) {
		if ( wrapper == null )
			return;

		for ( MetadataFormItem item: wrapper.getMetadataFormItemsList() ) {
			if (item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_TEXT) {
				if (item.getItemDefinition().getInputValidationTypeId() == MetadataFormInputValidationType.ID_ALPHANUM) {
					if (!StringUtils.isEmpty(item.getValue()) && !item.getValue().matches("[A-Za-z0-9]*")) {
						errors.reject("error.message.value.not.match.validation.type");
					}
				}
			}

			if (item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_TEXT || item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_TEXT) {
				if (item.getItemDefinition().getFieldMaxLength() != null && item.getItemDefinition().getFieldMaxLength() > 0) {
					if (item.getValue() != null && item.getValue().length() > item.getItemDefinition().getFieldMaxLength()) {
						errors.reject("error.message.length.exceeds.max.length");
					}
				}

				if (item.getItemDefinition().getFieldMinLength() != null && item.getItemDefinition().getFieldMinLength() > 0) {
					if (item.getValue() != null && item.getValue().length() < item.getItemDefinition().getFieldMinLength()) {
						errors.reject("error.message.length.below.min.length");
					}
				}
			}
		}
	}
	
	public static void validateMandatoryInputs(MetadataFormEditWrapper wrapper, Errors errors) {
		
		if ( wrapper == null )
			return;
		
		List<String> mandatoryValuesList = new ArrayList<>();
		int index = 0;
		for ( MetadataFormItem item: wrapper.getMetadataFormItemsList() ) {
			String value = item.getValue();
			if ( value != null )
				value = value.trim();
			if ( item.getItemDefinition().getIsManadatory()) {
				boolean missingInput = false;
				
				if(item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_SELECT_MENU || item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MENU){
					if("0".equals(value)){
						missingInput = true;
					}
				}else if(item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR || item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR){
					if(item.getDateStrInput() == null || item.getDateStrInput().isEmpty()){
						missingInput = true;
					}
				}else if(item.getItemDefinition().getTypeId() == MetadataFormItemType.ID_FILE){
					if((item.getSandboxFileId() == null || item.getSandboxFileId() == 0) && item.getUploadedFile() == null){
						missingInput = true;
					}
				}else{
					if(value == null || value.isEmpty()){
						missingInput = true;
					}
				}
				
				if(missingInput){
					if ( item.getItemDefinition().getParentItemOrder() == null || 
						 (item.getItemDefinition().getParentItemOrder() != null && wrapper.getMetadataFormItemsDisplayed().get(index)) ) {
						mandatoryValuesList.add(item.getItemDefinition().getName());
					}
				}
			}
			
			if ( item.getItemDefinition().isUniqueValue()){
				List<String> itemValues = MetadataFormItem.findAllUsedValues(item);
				for(String itemValue : itemValues){
					if(value != null && itemValue != null){
						if(value.equals(itemValue)){
							errors.reject("error.metadata.form.item.value.is.used",  new String[]{item.getItemDefinition().getName()}, "");
							break;
						}
					}
				}
			}
			
			index++;
		}

		if (!mandatoryValuesList.isEmpty())
			errors.reject("error.communication.mandatory.values", new String[] { StringUtil.join(mandatoryValuesList,", ") }, "");
	}
}
