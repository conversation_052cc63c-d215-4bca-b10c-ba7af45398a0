package com.prinova.messagepoint.controller.metadata;

public class MetadataPointsOfInterestDto {
    private long id;

    private long itemDefinitionId;

    private String label;

    private String description;

    private int comparisionOperator; // [1 - equal, 2- not equal, 3 - contains, 4 - not contains]

    private String comparisionValue;

    private boolean markedAsDeleted;

    public long getItemDefinitionId() {
        return itemDefinitionId;
    }

    public void setItemDefinitionId(long itemDefinitionId) {
        this.itemDefinitionId = itemDefinitionId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getComparisionOperator() {
        return comparisionOperator;
    }

    public void setComparisionOperator(int comparisionOperator) {
        this.comparisionOperator = comparisionOperator;
    }

    public String getComparisionValue() {
        return comparisionValue;
    }

    public void setComparisionValue(String comparisionValue) {
        this.comparisionValue = comparisionValue;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public boolean isMarkedAsDeleted() {
        return markedAsDeleted;
    }

    public void setMarkedAsDeleted(boolean markedAsDeleted) {
        this.markedAsDeleted = markedAsDeleted;
    }
}
