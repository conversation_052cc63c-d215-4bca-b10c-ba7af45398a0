package com.prinova.messagepoint.controller.metadata;

import com.prinova.messagepoint.model.metadata.MetadataPointsOfInterest;

import java.util.List;
import java.util.stream.Collectors;

public class MetadataPointsOfInterestMapper {
    private MetadataPointsOfInterestMapper() {

    }

    public static List<MetadataPointsOfInterestDto> convertAllToDtos(List<MetadataPointsOfInterest> metadataPointsOfInterest) {
        return metadataPointsOfInterest.stream()
                .map(MetadataPointsOfInterestMapper::convertToDto)
                .collect(Collectors.toList());
    }

    private static MetadataPointsOfInterestDto convertToDto(MetadataPointsOfInterest metadataPointsOfInterest) {
        MetadataPointsOfInterestDto result = new MetadataPointsOfInterestDto();
        result.setId(metadataPointsOfInterest.getId());
        result.setItemDefinitionId(metadataPointsOfInterest.getItemDefinition().getId());
        result.setComparisionOperator(metadataPointsOfInterest.getComparisionOperator());
        result.setComparisionValue(metadataPointsOfInterest.getComparisionValue());
        result.setLabel(metadataPointsOfInterest.getLabel());
        result.setDescription(metadataPointsOfInterest.getDescription());

        return result;
    }
}
