package com.prinova.messagepoint.controller.metadata;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointSelectionMetadataFormEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointSelectionMetadataFormEditController.class);
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		Map<String, Object> referenceData = new HashMap<>();

		referenceData.put("dateFormat", DateUtil.DATE_FORMAT);
		
		// EDITOR INIT
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected TouchpointSelectionMetadataFormEditWrapper formBackingObject(HttpServletRequest request) throws Exception {

		long selectionId		= ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_SELECTION_ID, -1);
		
		TouchpointSelection selection = TouchpointSelection.findById(selectionId);

		TouchpointSelectionMetadataFormEditWrapper wrapper = null;
		if ( selection.getMetadataForm() != null && (!selection.isInheritMetadata() || selection.isMaster()) ) {
			wrapper = new TouchpointSelectionMetadataFormEditWrapper( selection.getMetadataForm() );
		} else {
			TouchpointSelection referenceSelection = selection;
			while ( referenceSelection.isInheritMetadata() && referenceSelection.getParent() != null )
				referenceSelection = referenceSelection.getParent();
			
			wrapper = new TouchpointSelectionMetadataFormEditWrapper( selection.getDocument().getVariantMetadataFormDefinition(), referenceSelection.getMetadataForm() );
		}
		
		wrapper.setTouchpointSelection(selection);

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		TouchpointSelectionMetadataFormEditWrapper wrapper = (TouchpointSelectionMetadataFormEditWrapper) commandObj;

    	ServiceExecutionContext context = CreateOrUpdateMetadataFormService.createContextForUpdateVariantMetadataForm(wrapper.getTouchpointSelection(), wrapper.getFormWrapper());
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateMetadataFormService.SERVICE_NAME, CreateOrUpdateMetadataFormService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {	
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateMetadataFormService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}

	}


}