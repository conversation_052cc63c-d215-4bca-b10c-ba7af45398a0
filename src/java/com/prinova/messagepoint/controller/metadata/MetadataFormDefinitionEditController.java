package com.prinova.messagepoint.controller.metadata;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.metadata.MetadataFormFieldSizeType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.metadata.MetadataWebserviceValueRefreshType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormDefinitionService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class MetadataFormDefinitionEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(MetadataFormDefinitionEditController.class);
	
	public static final String 	REQ_PARAM_FORM_DEFINITION_ID 	= "formDefinitionId";
	public static final String 	REQ_PARAM_SAVE_SUCCESS			= "saveSuccess";
	
	public static final String 	FORM_SUBMIT_TYPE_SUBMIT 		= "submit";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		long formDefinitionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_FORM_DEFINITION_ID, -1);
		
		Map<String, Object> referenceData = new HashMap<>();
		
		referenceData.put("metadataFormItemTypes", MetadataFormItemType.listAll() );
		
		referenceData.put("metadataFormDefinitionTypes", MetadataFormDefinitionType.listAll() );
		
		referenceData.put("metadataFormFieldSizeTypes", MetadataFormFieldSizeType.listAll());
		
		referenceData.put("metadataFormFieldValidationTypes", MetadataFormInputValidationType.listAll());
		
		referenceData.put("webServiceRefreshTypes", MetadataWebserviceValueRefreshType.listAll() );
		
		boolean isReferenced = false;
		if ( formDefinitionId > 0 )
			isReferenced = MetadataFormDefinition.findById(formDefinitionId).isReferenced();
		referenceData.put("isReferenced", isReferenced);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.setAutoGrowCollectionLimit(1024);
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(AbstractDataElement.class, new IdCustomEditor<>(AbstractDataElement.class));
		binder.registerCustomEditor(MetadataFormItemType.class, new StaticTypeIdCustomEditor<>(MetadataFormItemType.class));
	}

	protected MetadataFormDefinitionWrapper formBackingObject(HttpServletRequest request) throws Exception {
		
		long formDefinitionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_FORM_DEFINITION_ID, -1);
		
		MetadataFormDefinitionWrapper wrapper = new MetadataFormDefinitionWrapper();
		MetadataFormDefinition formDefinition = MetadataFormDefinition.findById(formDefinitionId);
		if ( formDefinition != null )
			wrapper = new MetadataFormDefinitionWrapper(formDefinition, isFormSubmission(request));
		
		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		long formDefinitionId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_FORM_DEFINITION_ID, -1);
		MetadataFormDefinition formDefinition = MetadataFormDefinition.findById(formDefinitionId);

		MetadataFormDefinitionWrapper wrapper = (MetadataFormDefinitionWrapper) commandObj;

		User requestor = UserUtil.getPrincipalUser();

    	ServiceExecutionContext context = CreateOrUpdateMetadataFormDefinitionService.createContextForFormUpdate(formDefinition, wrapper, requestor);	
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME, CreateOrUpdateMetadataFormDefinitionService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form Definition was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}

	}
	
 	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
	}

}