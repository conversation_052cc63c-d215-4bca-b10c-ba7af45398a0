package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;

public class TouchpointSelectionMetadataFormEditWrapper implements Serializable {

	private static final long serialVersionUID = -748274759793668723L;

	private static final Log log = LogUtil.getLog(TouchpointSelectionMetadataFormEditWrapper.class);

	private MetadataFormEditWrapper 			formWrapper;
	private TouchpointSelection					touchpointSelection;
	
	public TouchpointSelectionMetadataFormEditWrapper(MetadataFormDefinition metadataFormDefinition, MetadataForm referenceForm) {
		super();
		formWrapper = new MetadataFormEditWrapper(metadataFormDefinition);
		
		// PREFILL: Prefill inherited values
		if ( referenceForm != null )
			for ( MetadataFormItem currentItem : formWrapper.getMetadataFormItemsList() )
				for ( MetadataFormItem currentReferenceItem : referenceForm.getFormItems() )
					if ( currentItem.getItemDefinition().getId() == currentReferenceItem.getItemDefinition().getId() )
						currentItem.setValue( currentReferenceItem.getValue() );
	}
	
	public TouchpointSelectionMetadataFormEditWrapper(MetadataFormDefinition metadataFormDefinition) {
		super();
		formWrapper = new MetadataFormEditWrapper(metadataFormDefinition);
	}
	
	public TouchpointSelectionMetadataFormEditWrapper(MetadataForm metadataForm) {
		super();
		formWrapper = new MetadataFormEditWrapper(metadataForm);
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	public TouchpointSelection getTouchpointSelection() {
		return touchpointSelection;
	}
	public void setTouchpointSelection(TouchpointSelection touchpointSelection) {
		this.touchpointSelection = touchpointSelection;
	}

}