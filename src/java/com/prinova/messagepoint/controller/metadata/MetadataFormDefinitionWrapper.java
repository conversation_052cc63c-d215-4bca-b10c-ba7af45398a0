package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;

public class MetadataFormDefinitionWrapper implements Serializable {

	private static final long serialVersionUID = -6270726680130949027L;

	private List<MetadataFormItemDefinitionVO> metadataFormItemDefinitionVOs;
	
	// MetadataFormDefinition properties
	private String 							name;
	private String 							metatags;
	private String							description;
	private int								type					= 1;
	private Document						document;
	private MetadataFormDefinition			metadataFormDefinition;
	private String							url;
	private String							username;
	private String							password;
	private String							hashedWebServicePassword;
	private String							webServicePassword;
	private long							primaryDataVariableId;
	private long							referenceDataVariableId;
	
	public MetadataFormDefinitionWrapper() {
		super();
	}
	
	public MetadataFormDefinitionWrapper(Document document, boolean lazyDecorate) throws Exception {

		if ( lazyDecorate ) {
			
			LazyList.decorate(new ArrayList<MetadataFormItemDefinitionVO>(), FactoryUtils.instantiateFactory(MetadataFormItemDefinitionVO.class));
		
		} else {
			
			metadataFormItemDefinitionVOs = new ArrayList<>();
			
	    	for( CommunicationOrderEntryItemDefinition currentItemDefinition : document.getCommunicationOrderEntryItemDefinitionsInOrder() )
	    		if( currentItemDefinition != null ) 
	    			metadataFormItemDefinitionVOs.add( new MetadataFormItemDefinitionVO( currentItemDefinition ) );
		}

	}
	public MetadataFormDefinitionWrapper(MetadataFormDefinition formDefinition, boolean lazyDecorate) throws Exception {

		if ( lazyDecorate ) {
			
			LazyList.decorate(new ArrayList<MetadataFormItemDefinitionVO>(), FactoryUtils.instantiateFactory(MetadataFormItemDefinitionVO.class));
		
		} else {
			
			metadataFormItemDefinitionVOs = new ArrayList<>();
			
	    	for ( MetadataFormItemDefinition currentItemDefinition : formDefinition.getMetadataFormItemDefinitionsInOrder() )
	    		if ( currentItemDefinition != null ) 
	    			metadataFormItemDefinitionVOs.add( new MetadataFormItemDefinitionVO( currentItemDefinition ) );
		}
		
		name 					= formDefinition.getName();
		metatags 				= formDefinition.getMetatags();
		description 			= formDefinition.getDescription();
		type 					= formDefinition.getType();
		metadataFormDefinition	= formDefinition;
		
		WebServiceConfiguration webServiceConfig = formDefinition.getWebServiceConfiguration();
		if ( webServiceConfig != null ) {
			url 		= webServiceConfig.getUrl();
			username 	= webServiceConfig.getUsername();
			password 	= webServiceConfig.getPassword();
		}

	}

	public List<MetadataFormItemDefinitionVO> getMetadataFormItemDefinitionVOs() {
		return metadataFormItemDefinitionVOs;
	}
	public void setMetadataFormItemDefinitionVOs(List<MetadataFormItemDefinitionVO> metadataFormItemDefinitionVOs) {
		this.metadataFormItemDefinitionVOs = metadataFormItemDefinitionVOs;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getMetatags() {
		return metatags;
	}
	public void setMetatags(String metatags) {
		this.metatags = metatags;
	}

	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}

	public MetadataFormDefinition getMetadataFormDefinition() {
		return metadataFormDefinition;
	}
	public void setMetadataFormDefinition(MetadataFormDefinition metadataFormDefinition) {
		this.metadataFormDefinition = metadataFormDefinition;
	}

	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}

	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}

	public String getHashedWebServicePassword() {

		if (document.getCommunicationDataFeedWebService() != null) {
			return DigestUtils.md5Hex("messagepoint-" + document.getCommunicationDataFeedWebService().getPassword());
		}

		return "";
	}

	public void setHashedWebServicePassword(String hashedWebServicePassword) {
		if (hashedWebServicePassword != null && !hashedWebServicePassword.equals(getHashedWebServicePassword())) {
			webServicePassword = hashedWebServicePassword;
		}
	}

	public String getWebServicePassword() {
		return webServicePassword;
	}

	public void setWebServicePassword(String webServicePassword) {
		this.webServicePassword = webServicePassword;
	}

	public long getPrimaryDataVariableId() {
		return primaryDataVariableId;
	}
	public void setPrimaryDataVariableId(long primaryDataVariableId) {
		this.primaryDataVariableId = primaryDataVariableId;
	}
	public long getReferenceDataVariableId() {
		return referenceDataVariableId;
	}
	public void setReferenceDataVariableId(long referenceDataVariableId) {
		this.referenceDataVariableId = referenceDataVariableId;
	}
}