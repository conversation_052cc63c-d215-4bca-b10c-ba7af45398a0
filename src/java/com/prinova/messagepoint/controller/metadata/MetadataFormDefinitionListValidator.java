package com.prinova.messagepoint.controller.metadata;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import com.prinova.messagepoint.controller.tasks.TaskListController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.wrapper.AsyncMetadataFormsListVO;
import com.prinova.messagepoint.model.wrapper.AsyncMetadataFormsListWrapper;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class MetadataFormDefinitionListValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		MetadataFormDefinitionListWrapper command = (MetadataFormDefinitionListWrapper)commandObj;

		List<MetadataFormDefinition> formDefinitions = command.getSelectedList();
		
		int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.parseInt(command.getActionValue());
		}

		switch (action) {
			case TaskListController.ACTION_EDIT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncMetadataFormsListVO.MetadataFormsListVOFlags::isCanUpdate);
				break;
			case TaskListController.ACTION_DELETE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncMetadataFormsListVO.MetadataFormsListVOFlags::isCanDelete);
				break;
			default:
				break;
		}

		if ( action == MetadataFormDefinitionListController.ACTION_DELETE ) {
			List<String> referencedFormNames = new ArrayList<>();
			for ( MetadataFormDefinition currentDefinition : formDefinitions ) {
				if ( currentDefinition.isReferenced() )
					referencedFormNames.add( currentDefinition.getName() );			
			}
			
			if (!referencedFormNames.isEmpty()) {
				errors.reject("error.metadata.form.definition.is.being.referenced.on.delete", new String[] {StringUtil.join(referencedFormNames, ", ")}, "");
			}
		}
					
	}

	private void validateActionPermission(List<MetadataFormDefinition> metadataFormDefinitions, Errors errors, String errorMessage, Predicate<AsyncMetadataFormsListVO.MetadataFormsListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( metadataFormDefinitions.isEmpty() )
			errors.reject(errorMessage);

		for (MetadataFormDefinition definition : metadataFormDefinitions) {
			AsyncMetadataFormsListVO vo = new AsyncMetadataFormsListVO();
			vo.setMetadataFormDefinition(definition);

			AsyncMetadataFormsListVO.MetadataFormsListVOFlags flags = new AsyncMetadataFormsListVO.MetadataFormsListVOFlags();
			AsyncMetadataFormsListWrapper.setActionFlags(flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}

}
