package com.prinova.messagepoint.controller.metadata;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.wrapper.AsyncMetadataFormsListVO;
import com.prinova.messagepoint.model.wrapper.AsyncMetadataFormsListWrapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class MetadataFormDefinitionEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		MetadataFormDefinitionWrapper wrapper = (MetadataFormDefinitionWrapper) commandObj;

		if (wrapper.getMetadataFormDefinition() != null && wrapper.getMetadataFormDefinition().getId() > 0) {
			MetadataFormDefinition metadataFormDefinition = MetadataFormDefinition.findById(wrapper.getMetadataFormDefinition().getId());
			if (metadataFormDefinition != null) {
				validateActionPermission(metadataFormDefinition, errors, "error.message.action.not.permitted", AsyncMetadataFormsListVO.MetadataFormsListVOFlags::isCanUpdate);
			}
		}

		boolean webServiceApplied = false;
		
		if ( wrapper.getName() == null || wrapper.getName().trim().isEmpty())
			errors.reject("error.input.mandatory", new String[] {ApplicationUtil.getMessage("page.label.name")}, "");
		
		if ( wrapper.getMetadataFormItemDefinitionVOs() == null || wrapper.getMetadataFormItemDefinitionVOs().isEmpty()) {
			errors.reject("error.message.metadata.template.requires.at.least.one.input");
		} else {
			if ( wrapper.getType() == MetadataFormDefinitionType.ID_RATIONALIZER_PARSED ) {
				for ( MetadataFormItemDefinitionVO currentVO: wrapper.getMetadataFormItemDefinitionVOs() ) {
					if ( currentVO.getPrimaryConnector() == null || currentVO.getPrimaryConnector().trim().isEmpty()) {
						errors.reject("error.message.metadata.template.required.connector.value");
						break;
					}
				}
			}
			for ( MetadataFormItemDefinitionVO currentVO: wrapper.getMetadataFormItemDefinitionVOs() ) {
				if ( currentVO.getAction() != -9 ) {
					
					if ( currentVO.getType().getId() == MetadataFormItemType.ID_WEB_SERVICE_MENU || currentVO.getType().getId() == MetadataFormItemType.ID_WEB_SERVICE_TEXT )
						webServiceApplied = true;

					if ( currentVO.getType().getId() == MetadataFormItemType.ID_TEXT || currentVO.getType().getId() == MetadataFormItemType.ID_WEB_SERVICE_TEXT ){
						if ( currentVO.getMaxLength() != null && currentVO.getMinLength() != null) {
							if (currentVO.getMinLength() > currentVO.getMaxLength()) {
								errors.reject("error.message.min.length.greater.than.max.length");
							}
						}
					}

					if ( currentVO.getName().isEmpty() || currentVO.getName().trim().isEmpty()) {
						String label = ApplicationUtil.getMessage("page.label.name");
						MessagepointInputValidationUtil.validateStringValue(label, currentVO.getName().trim(), true, 1, 96, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
					}
					
					if ( currentVO.getParentOrder() != null && ( currentVO.getDisplayCriteria() == null || currentVO.getDisplayCriteria().trim().isEmpty()) )
						errors.reject("error.message.order.entry.display.criteria.required");
					
					if ( currentVO.getType().getId() == MetadataFormItemType.ID_SELECT_MENU ) {
						if ( currentVO.getMenuValueItems() == null || currentVO.getMenuValueItems().isEmpty())
							errors.reject("error.message.order.entry.menu.items.required");
					} else if ( currentVO.getType().getId() == MetadataFormItemType.ID_TEXT){
						if(currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_INTEGER){
							if(!StringUtils.isEmpty(currentVO.getTextDefaultValue())) {
								try {
									Integer.parseInt(currentVO.getTextDefaultValue());
								} catch (Exception e) {
									errors.reject("error.message.default.value.not.match.validation.type");
								}
							}
						}else if(currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_DECIMAL){
							if (!StringUtils.isEmpty(currentVO.getTextDefaultValue()) && !currentVO.getTextDefaultValue().matches("[0-9.]*")) {
								errors.reject("error.message.default.value.not.match.validation.type");
							}
						}else if(currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_ALPHANUM){
							if (!StringUtils.isEmpty(currentVO.getTextDefaultValue()) && !currentVO.getTextDefaultValue().matches("[A-Za-z0-9]*")) {
								errors.reject("error.message.default.value.not.match.validation.type");
							}
						}
					}
				}
			}

			
			if ( webServiceApplied ) {

				if ( 	wrapper.getUrl().isEmpty() || wrapper.getUrl().trim().isEmpty() ||
						wrapper.getUsername().isEmpty() || wrapper.getUsername().trim().isEmpty() ||
						wrapper.getPassword().isEmpty() || wrapper.getPassword().trim().isEmpty()) {
					//return;
					//errors.reject("error.message.order.entry.web.service.attributes.required");
				}
				
				List<String> primaryConnectorValues = new ArrayList<>();
				for ( MetadataFormItemDefinitionVO currentVO: wrapper.getMetadataFormItemDefinitionVOs() ) {
					if ( currentVO.getAction() != -9 && currentVO.getPrimaryConnector() != null && 
						 !currentVO.getPrimaryConnector().isEmpty() && !currentVO.getPrimaryConnector().trim().isEmpty()) {
						if ( primaryConnectorValues.contains( currentVO.getPrimaryConnector().trim() ) ) {
							errors.reject("error.message.order.entry.unique.connector.values.required");
							break;
						} else {
							primaryConnectorValues.add( currentVO.getPrimaryConnector() );
						}
					}
				}
				if (primaryConnectorValues.isEmpty())
					errors.reject("error.message.order.entry.unique.connector.values.required");
			}
		
		}

	}

	private void validateActionPermission(MetadataFormDefinition metadataFormDefinition, Errors errors, String errorMessage, Predicate<AsyncMetadataFormsListVO.MetadataFormsListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncMetadataFormsListVO vo = new AsyncMetadataFormsListVO();
		vo.setMetadataFormDefinition(metadataFormDefinition);

		AsyncMetadataFormsListVO.MetadataFormsListVOFlags flags = new AsyncMetadataFormsListVO.MetadataFormsListVOFlags();
		AsyncMetadataFormsListWrapper.setActionFlags(flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorMessage);
		}
	}

}
