package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class MetadataFormEditWrapper implements Serializable {

	private static final long serialVersionUID = -8812371810171657152L;

	private static final Log log = LogUtil.getLog(MetadataFormEditWrapper.class);

	private MetadataForm metadataForm;
	private List<MetadataFormItem> metadataFormItemsList;
	private List<String> metadataFormItemsDefIdList = new ArrayList<>();
	private List<String> metadataFormItemsIdList = new ArrayList<>();
	private List<String> metadataFormItemsConnectorList = new ArrayList<>();
	private List<Boolean> metadataFormItemsDisplayed = new ArrayList<>();
	private List<String> metadataFormMenuItems = new ArrayList<>();
	private List<String> metadataFormUploadFilePathList = new ArrayList<>();
	private List<String> metadataFormItemPreValueList = new ArrayList<>();
	
	public MetadataFormEditWrapper(MetadataFormDefinition metadataFormDefinition) {
		super();
		initFormFromDefinition(metadataFormDefinition, false);
	}

	public MetadataFormEditWrapper(MetadataFormDefinition metadataFormDefinition, Boolean autoGenerateOnImportItemsOnly) {
		super();
		initFormFromDefinition(metadataFormDefinition, autoGenerateOnImportItemsOnly);
	}

	private void initFormFromDefinition(MetadataFormDefinition metadataFormDefinition, Boolean autoGenerateOnImportItemsOnly) {
		MetadataForm metadataForm = new MetadataForm();
		metadataForm.setFormDefinition(metadataFormDefinition);
		List<MetadataFormItem> metadataFormItemsList = new ArrayList<>();
			
		List<MetadataFormItemDefinition> metadataFormItemDefs = metadataFormDefinition.getMetadataFormItemDefinitionsInOrder();
		Set<MetadataFormItem> metadataFormItemsSet = new HashSet<>();
		
		for (MetadataFormItemDefinition currentDef: metadataFormItemDefs) {
			
			if ( !autoGenerateOnImportItemsOnly || (autoGenerateOnImportItemsOnly && currentDef.getAutoGenerateOnImport()) ) {
				MetadataFormItem newMetadataFormItem = new MetadataFormItem();
				newMetadataFormItem.setItemDefinition(currentDef);
	
				metadataFormItemsList.add( newMetadataFormItem );
				metadataFormItemsSet.add( newMetadataFormItem );
				
				metadataFormItemsDefIdList.add( String.valueOf(currentDef.getId()) );
				metadataFormItemsIdList.add( "0" );
				metadataFormItemsConnectorList.add( currentDef.getPrimaryConnector() );
				metadataFormItemsDisplayed.add(false);
				metadataFormMenuItems.add(currentDef.getMenuValueItemsListAsString());
				metadataFormItemPreValueList.add("");
				
				// Set the default value
				boolean isDefaultValueSet = currentDef.getDefaultInputValue() != null && !currentDef.getDefaultInputValue().isEmpty();
				if(isDefaultValueSet){
					if(currentDef.getTypeId() == MetadataFormItemType.ID_TEXT || currentDef.getTypeId() == MetadataFormItemType.ID_TEXTAREA){
						String textDefaultInput = currentDef.getDefaultInputValue();
						if(textDefaultInput != null && !textDefaultInput.isEmpty()){
							// If TODAYS_DATE appears, replace it with today's date in format: ddMMMyyyy
							SimpleDateFormat persistedDF = new SimpleDateFormat("ddMMMyyyy");
							textDefaultInput = textDefaultInput.replaceAll("TODAYS_DATE", persistedDF.format(DateUtil.today()));
							if(currentDef.getTypeId() == MetadataFormItemType.ID_TEXT && currentDef.isUniqueValue()){
								// Unique value for default value
								List<String> values = MetadataFormItem.findAllUsedValues(newMetadataFormItem);
								if(values.contains(textDefaultInput)){
									if(currentDef.getInputValidationTypeId() == MetadataFormInputValidationType.ID_NONE){
										int idx = 1;
										while(values.contains(textDefaultInput+"_"+idx)){
											idx++;
										}
										textDefaultInput += "_" + idx;
									}else if(currentDef.getInputValidationTypeId() == MetadataFormInputValidationType.ID_INTEGER){
										try{
											int intValue = Integer.valueOf(textDefaultInput);
											while(values.contains(String.valueOf(intValue))){
												intValue++;
											}
											textDefaultInput = String.valueOf(intValue);
										}catch(NumberFormatException nfe){
										}
									}else if(currentDef.getInputValidationTypeId() == MetadataFormInputValidationType.ID_DECIMAL){
										try{
											float decimalValue = Float.valueOf(textDefaultInput);
											while(values.contains(String.valueOf(decimalValue))){
												decimalValue++;
											}
											textDefaultInput = String.valueOf(decimalValue);
										}catch(NumberFormatException nfe){
										}
									}else if(currentDef.getInputValidationTypeId() == MetadataFormInputValidationType.ID_ALPHANUM){
										try{
											String alphanumericValue = "";
											if (textDefaultInput.matches("[A-Za-z0-9]*")) {
												alphanumericValue = textDefaultInput;
											}
											textDefaultInput = alphanumericValue;
										}catch(NumberFormatException nfe){
										}
									}
								}
							}
						}
						newMetadataFormItem.setValue(textDefaultInput);
					}else if(currentDef.getTypeId() == MetadataFormItemType.ID_SELECT_MENU){
						int defaultValueIdx = Integer.valueOf(currentDef.getDefaultInputValue());
						newMetadataFormItem.setValue(currentDef.getMenuValueItemsList().get(defaultValueIdx));
					}else if(currentDef.getTypeId() == MetadataFormItemType.ID_MULTISELECT_MENU){
						StringBuilder valueStr = new StringBuilder();
						for(String defaultValue : currentDef.getDefaultInputValue().split(",")){
							int defaultValueIdx = Integer.valueOf(defaultValue);
							if(!valueStr.isEmpty()){
								valueStr.append(",");
							}
							valueStr.append(currentDef.getMenuValueItemsList().get(defaultValueIdx));
						}
						newMetadataFormItem.setValue(valueStr.toString());
					}
				}
			
				if(currentDef.getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR){
					if(currentDef.isDefaultDateValueToTodaysDate()){
						newMetadataFormItem.setDateStrInput(DateUtil.formatDate(DateUtil.today()));
					}else{
						try {
							if ( currentDef.getDefaultInputValue() != null ) {
								SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
								Date date= persistedDF.parse(currentDef.getDefaultInputValue());
								
								Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
								SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
								newMetadataFormItem.setDateStrInput(viewDF.format(date));
							}
						} catch (ParseException e) {
							log.error("ERROR: Unable to parse metadata persisted date: " + e);
						}
					}
				}
				
				if(currentDef.getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR){
					try {
						if ( currentDef.getDefaultInputValue() != null ) {
							SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
							Date date= persistedDF.parse(currentDef.getDefaultInputValue());
							
							Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
							SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
							newMetadataFormItem.setDateStrInput(viewDF.format(date));
						}
					} catch (ParseException e) {
						log.error("ERROR: Unable to parse metadata persisted date: " + e);
					}
				}
				
				if (currentDef.getTypeId() == MetadataFormItemType.ID_FILE){
					if(newMetadataFormItem.getUploadedFile() != null){
						String uploadFilePath = String.valueOf(newMetadataFormItem.getId()) + "|" + newMetadataFormItem.getUploadedFile().getId() + "|" + newMetadataFormItem.getUploadedFile().getFileName();
						metadataFormUploadFilePathList.add(uploadFilePath);
					}else{
						metadataFormUploadFilePathList.add("");
					}
				}else{
					metadataFormUploadFilePathList.add("");
				}
			}
			
		}
		
		metadataForm.setFormItems( metadataFormItemsSet );

		this.metadataFormItemsList 	= metadataFormItemsList;
		
		this.metadataForm = metadataForm;
	}

	public MetadataFormEditWrapper(MetadataForm metadataForm) {
		super();

		this.metadataForm = metadataForm;
		this.metadataFormItemsList 	= metadataForm.getFormItemsInOrder();

		for ( MetadataFormItem currentItem: this.metadataFormItemsList ) {
			addItemsToFormWrapper(currentItem);
		}
	}

	public MetadataFormEditWrapper(MetadataForm metadataForm, Set<String> escapeMetadataItemsLoweCaseConecctors) {
		super();

		this.metadataForm = metadataForm;
		this.metadataFormItemsList 	= metadataForm.getFormItemsInOrder();
		List<MetadataFormItem> metadataFormItemsListToRemove = new ArrayList<>();
		for ( MetadataFormItem currentItem: this.metadataFormItemsList ) {
			if(!escapeMetadataItemsLoweCaseConecctors.contains(currentItem.getItemDefinition().getPrimaryConnector().toLowerCase())) {
				addItemsToFormWrapper(currentItem);
			} else {
				metadataFormItemsListToRemove.add(currentItem);
			}
		}
		metadataFormItemsList.removeAll(metadataFormItemsListToRemove);
	}

	private void addItemsToFormWrapper(MetadataFormItem currentItem) {
		metadataFormItemsDefIdList.add("0");
		metadataFormItemsIdList.add(String.valueOf(currentItem.getId()));
		metadataFormItemsConnectorList.add(currentItem.getItemDefinition().getPrimaryConnector());
		metadataFormItemsDisplayed.add(false);
		metadataFormMenuItems.add(currentItem.getItemDefinition().getMenuValueItemsListAsString());

		if(currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_FILE) {
			if(currentItem.getUploadedFile() != null) {
				String uploadFilePath = String.valueOf(currentItem.getId()) + "|" + currentItem.getUploadedFile().getId() + "|" + currentItem.getUploadedFile().getFileName();
				metadataFormUploadFilePathList.add(uploadFilePath);
				metadataFormItemPreValueList.add(currentItem.getUploadedFile().getFileName());
			} else {
				metadataFormUploadFilePathList.add("");
				metadataFormItemPreValueList.add(currentItem.getValue());
			}
		} else {
			metadataFormUploadFilePathList.add("");
			metadataFormItemPreValueList.add(currentItem.getValue());
		}

		if(currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR &&
				currentItem.getValue() != null && !currentItem.getValue().isEmpty()) {
			currentItem.setDateStrInput(currentItem.getValue());
			try {
				SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
				Date date = persistedDF.parse(currentItem.getValue());

				Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
				SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
				currentItem.setDateStrInput(viewDF.format(date));
			} catch(ParseException e) {
				log.error("ERROR: Unable to parse metadata persisted date: " + e);
			}
		} else if(currentItem.getItemDefinition().getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR &&
				currentItem.getValue() != null && !currentItem.getValue().isEmpty()) {
			currentItem.setDateStrInput(currentItem.getValue());
			try {
				SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
				Date date = persistedDF.parse(currentItem.getValue());

				Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
				SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
				currentItem.setDateStrInput(viewDF.format(date));
			} catch(ParseException e) {
				log.error("ERROR: Unable to parse metadata persisted date: " + e);
			}
		}
	}

	public MetadataForm getMetadataForm() {
		return metadataForm;
	}
	public void setMetadataForm(MetadataForm metadataForm) {
		this.metadataForm = metadataForm;
	}

	public List<MetadataFormItem> getMetadataFormItemsList() {
		return metadataFormItemsList;
	}
	public void setMetadataFormItemsList(List<MetadataFormItem> metadataFormItemsList) {
		this.metadataFormItemsList = metadataFormItemsList;
	}

	public List<String> getMetadataFormItemsDefIdList() {
		return metadataFormItemsDefIdList;
	}
	public void setMetadataFormItemsDefIdList(List<String> metadataFormItemsDefIdList) {
		this.metadataFormItemsDefIdList = metadataFormItemsDefIdList;
	}

	public List<String> getMetadataFormItemsIdList() {
		return metadataFormItemsIdList;
	}
	public void setMetadataFormItemsIdList(List<String> metadataFormItemsIdList) {
		this.metadataFormItemsIdList = metadataFormItemsIdList;
	}

	public List<String> getMetadataFormItemsConnectorList() {
		return metadataFormItemsConnectorList;
	}
	public void setMetadataFormItemsConnectorList(
			List<String> metadataFormItemsConnectorList) {
		this.metadataFormItemsConnectorList = metadataFormItemsConnectorList;
	}

	public List<Boolean> getMetadataFormItemsDisplayed() {
		return metadataFormItemsDisplayed;
	}
	public void setMetadataFormItemsDisplayed(List<Boolean> metadataFormItemsDisplayed) {
		this.metadataFormItemsDisplayed = metadataFormItemsDisplayed;
	}

	public List<String> getMetadataFormMenuItems() {
		return metadataFormMenuItems;
	}
	public void setMetadataFormMenuItems(List<String> metadataFormMenuItems) {
		this.metadataFormMenuItems = metadataFormMenuItems;
	}
	public List<String> getMetadataFormUploadFilePathList() {
		return metadataFormUploadFilePathList;
	}
	public void setMetadataFormUploadFilePathList(List<String> metadataFormUploadFilePathList) {
		this.metadataFormUploadFilePathList = metadataFormUploadFilePathList;
	}
	public List<String> getMetadataFormItemPreValueList() {
		return metadataFormItemPreValueList;
	}
	public void setMetadataFormItemPreValueList(List<String> metadataFormItemPreValueList) {
		this.metadataFormItemPreValueList = metadataFormItemPreValueList;
	}
	
	public Map<Long, String> getMetadataItemPreValueMap(){
		Map<Long, String> preValueMap = new HashMap<>();
		for(int i=0; i<this.metadataFormItemsList.size(); i++){
			long formItemId = Long.valueOf(this.metadataFormItemsIdList.get(i));
			String preValue = this.metadataFormItemPreValueList.get(i);
			preValueMap.put(formItemId, preValue);
		}
		return preValueMap;
	}
	
	public boolean hasDocMetadataItemValueChanges(){
		for(int i=0; i<this.metadataFormItemsList.size(); i++){
			String preValue = this.metadataFormItemPreValueList.get(i);
			String postValue = this.metadataFormItemsList.get(i).getValue();
			
			if(preValue == null || preValue.isEmpty()){
				if(postValue != null && !postValue.isEmpty()){
					return true;
				}
			}else{
				if(postValue == null || postValue.isEmpty()){
					return true;
				}else{
					if(!preValue.trim().equals(postValue.trim())){
						return true;
					}
				}
			}
		}
		
		return false;
	}

	public void removeMetadataFromEditWrapperByConnectorName(String connectorName) {
		List<String> connectorList = this.getMetadataFormItemsConnectorList();
		if (org.apache.commons.collections.CollectionUtils.isEmpty(connectorList) || !connectorList.contains(connectorName)) {
			return;
		}
		int indexOfConnector = connectorList.indexOf(connectorName);
		connectorList.remove(connectorName);
		this.getMetadataFormItemsList().remove(indexOfConnector);
		this.getMetadataFormItemsDefIdList().remove(indexOfConnector);
		this.getMetadataFormItemsIdList().remove(indexOfConnector);
		this.getMetadataFormItemsDisplayed().remove(indexOfConnector);
		this.getMetadataFormMenuItems().remove(indexOfConnector);
		this.getMetadataFormUploadFilePathList().remove(indexOfConnector);
		this.getMetadataFormItemPreValueList().remove(indexOfConnector);
	}
}