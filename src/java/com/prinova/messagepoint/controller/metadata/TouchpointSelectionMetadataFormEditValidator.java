package com.prinova.messagepoint.controller.metadata;

import org.springframework.validation.Errors;

public class TouchpointSelectionMetadataFormEditValidator extends MetadataFormEditValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointSelectionMetadataFormEditWrapper wrapper = (TouchpointSelectionMetadataFormEditWrapper) commandObj;
		
		// Validate mandatory fields
		if ( !wrapper.getTouchpointSelection().isInheritMetadata() || wrapper.getTouchpointSelection().isMaster()) {
			MetadataFormEditValidator.validateMandatoryInputs(wrapper.getFormWrapper(), errors);
		}

		super.validateNotGenericInputs(wrapper.getFormWrapper(), errors);
	}
}
