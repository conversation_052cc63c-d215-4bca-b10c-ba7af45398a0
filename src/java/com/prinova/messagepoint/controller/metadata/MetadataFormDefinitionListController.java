package com.prinova.messagepoint.controller.metadata;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.platform.services.metadata.BulkDeleteMetadataFormDefinitionService;
import com.prinova.messagepoint.security.StringXSSEditor;

public class MetadataFormDefinitionListController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(MetadataFormDefinitionListController.class);
	
	public static final String REQ_PARM_ACTION 				= "action";
	
	public static final int ACTION_EDIT 					= 1;
	public static final int ACTION_DELETE 					= 2;
	
	public static final String PARAM_FORM_DEFINITION_ID		= "formDefinitionId";
	
	@Override
	protected Map<String,Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new MetadataFormDefinitionListWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors)
			throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		
		MetadataFormDefinitionListWrapper c = (MetadataFormDefinitionListWrapper)command;
		
		if ( action == ACTION_DELETE) {
			ServiceExecutionContext context = BulkDeleteMetadataFormDefinitionService.createContext(c.getSelectedIds());
			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteMetadataFormDefinitionService.SERVICE_NAME, BulkDeleteMetadataFormDefinitionService.class);
			service.execute(context);

			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(DeleteModelService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" metadata forms are not deleted. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				return new ModelAndView( new RedirectView( getSuccessView() ) );
			}
		}

		return null;
	}

}
