package com.prinova.messagepoint.controller.metadata;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.util.HibernateUtil;

public class MetadataFormDefinitionListDetailController implements Controller {

	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long formDefinitionId = ServletRequestUtils.getLongParameter(request, MetadataFormDefinitionListController.PARAM_FORM_DEFINITION_ID, -1L);
		
		MetadataFormDefinition formDefinition = HibernateUtil.getManager().getObject(MetadataFormDefinition.class, formDefinitionId);
		params.put("formDefinition", formDefinition);

		return new ModelAndView(getFormView(), params);
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
