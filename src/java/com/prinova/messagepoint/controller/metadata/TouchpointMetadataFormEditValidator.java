package com.prinova.messagepoint.controller.metadata;

import org.springframework.validation.Errors;

public class TouchpointMetadataFormEditValidator extends MetadataFormEditValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointMetadataFormEditWrapper wrapper = (TouchpointMetadataFormEditWrapper) commandObj;
		
		MetadataFormEditValidator.validateMandatoryInputs(wrapper.getFormWrapper(), errors);

		super.validateNotGenericInputs(wrapper.getFormWrapper(), errors);
	}
}