package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.metadata.MetadataForm;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;

public class TouchpointMetadataFormEditWrapper implements Serializable {

	private static final long serialVersionUID = -8812371810171657152L;

	private static final Log log = LogUtil.getLog(TouchpointMetadataFormEditWrapper.class);

	private MetadataFormEditWrapper 			formWrapper;
	private Document							document;
	
	public TouchpointMetadataFormEditWrapper(MetadataFormDefinition metadataFormDefinition) {
		super();
		formWrapper = new MetadataFormEditWrapper(metadataFormDefinition);
	}
	
	public TouchpointMetadataFormEditWrapper(MetadataForm metadataForm) {
		super();
		formWrapper = new MetadataFormEditWrapper(metadataForm);
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}
	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}

}