package com.prinova.messagepoint.controller.metadata;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointMetadataFormEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TouchpointMetadataFormEditController.class);
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		Map<String, Object> referenceData = new HashMap<>();

		referenceData.put("dateFormat", DateUtil.DATE_FORMAT);
		
		// EDITOR INIT
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected TouchpointMetadataFormEditWrapper formBackingObject(HttpServletRequest request) throws Exception {

		long documentId					= ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_DOCUMENTID, -1);
		
		Document document = Document.findById(documentId);
		
		TouchpointMetadataFormEditWrapper wrapper = null;
		if ( document.getMetadataForm() != null )
			wrapper = new TouchpointMetadataFormEditWrapper( document.getMetadataForm() );
		else
			wrapper = new TouchpointMetadataFormEditWrapper( document.getTouchpointMetadataFormDefinition() );
		
		wrapper.setDocument(document);

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		TouchpointMetadataFormEditWrapper wrapper = (TouchpointMetadataFormEditWrapper) commandObj;

    	ServiceExecutionContext context = CreateOrUpdateMetadataFormService.createContextForUpdateTouchpointMetadataForm(wrapper.getDocument(), wrapper.getFormWrapper());	
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateMetadataFormService.SERVICE_NAME, CreateOrUpdateMetadataFormService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {
			// Set TPContentChanged to true
			Document document = wrapper.getDocument();
			if(document != null && !document.isTpContentChanged()){
				document.setTpContentChanged(true);
				document.save();
			}
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateMetadataFormService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Metadata Form was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}

	}


}