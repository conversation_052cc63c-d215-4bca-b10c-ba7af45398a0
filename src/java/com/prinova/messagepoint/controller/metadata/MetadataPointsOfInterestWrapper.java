package com.prinova.messagepoint.controller.metadata;

import com.prinova.messagepoint.model.metadata.MetadataPointsOfInterest;
import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.prinova.messagepoint.controller.metadata.MetadataPointsOfInterestMapper.convertAllToDtos;

public class MetadataPointsOfInterestWrapper implements Serializable {

    private List<MetadataPointsOfInterestDto> metadataPointsOfInterestDtoList;

    public MetadataPointsOfInterestWrapper(List<MetadataPointsOfInterest> metadataPointsOfInterest, boolean lazyDecorate, long appId) {
        if (lazyDecorate) {
            this.metadataPointsOfInterestDtoList = LazyList.decorate(new ArrayList<MetadataPointsOfInterest>(), FactoryUtils.instantiateFactory(MetadataPointsOfInterest.class));
        } else {
            this.metadataPointsOfInterestDtoList = new ArrayList<>();
        }
        this.metadataPointsOfInterestDtoList.addAll(convertAllToDtos(metadataPointsOfInterest));
    }

    public List<MetadataPointsOfInterestDto> getMetadataPointsOfInterestDtoList() {
        return metadataPointsOfInterestDtoList;
    }

    public void setMetadataPointsOfInterestDtoList(List<MetadataPointsOfInterestDto> metadataPointsOfInterestDtoList) {
        this.metadataPointsOfInterestDtoList = metadataPointsOfInterestDtoList;
    }
}