package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.util.HibernateUtil;

public class MetadataFormDefinitionListWrapper  implements Serializable {

	private static final long serialVersionUID = 5826298241998379017L;

	private List<Long>				selectedIds;
	private String 					actionValue;
	
	public MetadataFormDefinitionListWrapper(){
		super();
	}	
	
	public MetadataFormDefinitionListWrapper(List<Zone> zones){
		super();
		this.selectedIds 	= new ArrayList<>();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public List<MetadataFormDefinition> getSelectedList(){
		List<MetadataFormDefinition> selectedList = new ArrayList<>();
		for ( Long selectedId : this.selectedIds )
			selectedList.add(HibernateUtil.getManager().getObject(MetadataFormDefinition.class, selectedId));
		return selectedList;
	}	

	public static class ZonesVO{
		private boolean 				selectedForAction;		
		private MetadataFormDefinition	formDefinition;	
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}
		public MetadataFormDefinition getFormDefinition() {
			return formDefinition;
		}
		public void setFormDefinition(MetadataFormDefinition formDefinition) {
			this.formDefinition = formDefinition;
		}				
	}

}
