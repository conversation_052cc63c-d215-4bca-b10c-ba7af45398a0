package com.prinova.messagepoint.controller.metadata;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataPointsOfInterest;
import com.prinova.messagepoint.model.metadata.MetadataPointsOfInterestComparatorType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataPointsOfInterestService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MetadataPointsOfInterestController extends MessagepointController {
    private static final Log log = LogUtil.getLog(MetadataPointsOfInterestController.class);
    private final Pattern sortPattern = Pattern.compile("^\\d+");
    private static final String REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM = "rationalizerApplicationId";
    public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        long appId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
        RationalizerApplication application = RationalizerApplication.findById(appId);

        final MetadataFormDefinition parsedDocumentFormDefinition = application.getParsedDocumentFormDefinition();
        Set<MetadataFormItemDefinition> availableDocumentsMetadataItemDefs = parsedDocumentFormDefinition == null ? new LinkedHashSet() : parsedDocumentFormDefinition.getFormItemDefinitions();
        final MetadataFormDefinition parsedContentFormDefinition = application.getParsedContentFormDefinition();
        Set<MetadataFormItemDefinition> availableContentMetadataItemDefs = parsedContentFormDefinition == null ? new LinkedHashSet() : parsedContentFormDefinition.getFormItemDefinitions();
        List<MetadataFormItemDefinition> allAvailableMetadataItemDefs = new ArrayList<>();
        allAvailableMetadataItemDefs.addAll(availableDocumentsMetadataItemDefs);
        allAvailableMetadataItemDefs.addAll(availableContentMetadataItemDefs);
        Collections.sort(allAvailableMetadataItemDefs, new MetadataFormItemDefinitionComparator());

        Map<String, Object> referenceData = new HashMap<>();
        referenceData.put("metadataComparatorList", MetadataPointsOfInterestComparatorType.listAll());
        referenceData.put("availableMetadataItemDefs", allAvailableMetadataItemDefs);
        referenceData.put("newItemId", Long.MAX_VALUE);
        referenceData.put("removedItemId", Long.MIN_VALUE);

        return referenceData;
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
        binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    @Override
    protected MetadataPointsOfInterestWrapper formBackingObject(HttpServletRequest request) throws Exception {
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
        List<MetadataPointsOfInterest> metadataPointsOfInterests = RationalizerApplication.findMetadataPointsOfInterest(rationalizerApplicationId);

        return new MetadataPointsOfInterestWrapper(metadataPointsOfInterests, isFormSubmission(request), rationalizerApplicationId);
    }

    @Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
                                    BindException errors) throws Exception {

        MetadataPointsOfInterestWrapper wrapper = (MetadataPointsOfInterestWrapper) commandObj;
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_RATIONALIZER_APPLICATION_ID_PARAM, -1);
        RationalizerApplication rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);

        ServiceExecutionContext context = CreateOrUpdateMetadataPointsOfInterestService.createContext(wrapper, rationalizerApplication);
        Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateMetadataPointsOfInterestService.SERVICE_NAME, CreateOrUpdateMetadataPointsOfInterestService.class);
        service.execute(context);
        ServiceResponse serviceResponse = context.getResponse();
        if (serviceResponse.isSuccessful()) {
            Map<String, Object> parms = new HashMap<>();
            parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
            return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);
        } else {
            StringBuilder sb = new StringBuilder();
            sb.append(CreateOrUpdateMetadataPointsOfInterestService.SERVICE_NAME);
            sb.append(" service call is not successful ");
            sb.append(" in ").append(this.getClass().getName());
            sb.append(" Metadata Point Of Interest was not saved. ");
            log.error(sb.toString());
            ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
            return super.showForm(request, response, errors);
        }
    }

    @Override
    protected boolean isFormSubmission(HttpServletRequest request) {
        String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
        return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
    }

    class MetadataFormItemDefinitionComparator implements Comparator<MetadataFormItemDefinition> {

        @Override
        public int compare(MetadataFormItemDefinition o1, MetadataFormItemDefinition o2) {
            if (o1 == null || o2 == null) {
                return 0;
            }

            String firstItem = o1.getPrimaryConnector();
            String secondItem = o2.getPrimaryConnector();

            if (firstItem == null || secondItem == null) {
                return 0;
            }

            Matcher m = sortPattern.matcher(firstItem);
            Integer number1;
            if (!m.find()) {
                return firstItem.compareTo(secondItem);
            } else {
                int number2;
                number1 = Integer.parseInt(m.group());
                m = sortPattern.matcher(secondItem);
                if (!m.find()) {
                    return firstItem.compareTo(secondItem);
                } else {
                    number2 = Integer.parseInt(m.group());
                    int comparison = number1.compareTo(number2);
                    if (comparison != 0) {
                        return comparison;
                    } else {
                        return firstItem.compareTo(secondItem);
                    }
                }
            }
        }
    }

}