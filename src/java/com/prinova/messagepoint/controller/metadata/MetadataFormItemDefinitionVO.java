package com.prinova.messagepoint.controller.metadata;

import java.io.Serializable;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.communication.CommunicationDataPrivacyType;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.communication.UploadedFileType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class MetadataFormItemDefinitionVO implements Serializable {

	private static final long serialVersionUID = -6006679012580879759L;

	private static final Log log = LogUtil.getLog(MetadataFormItemDefinitionVO.class);

	private long 							id;
	private int								action;
	private String							name;
	private String							description;
	private int								order;
	private boolean							isPrimaryDriverEntry;
	private boolean							isIndicatorEntry;
	private boolean							isMandatory;
	private boolean							isLockedForEdit;
	private String							displayCriteria;
	private Integer							parentOrder;

	private int								dataElementTypeId;

	private String							dataElementInputTypeFormat;
	private int								dataPrivacyTypeId;

	private int								uploadedFileTypeId				= UploadedFileType.ID_PRIMARY_DRIVER;
	private DataSource						uploadedFileDataSource;

	private boolean							autoGenerateOnImport			= false;

	private AbstractDataElement				dataElement;

	private DataElementVariable             dataElementVariable;
	private MetadataFormItemType			type;

	private List<String>					menuValueItems					= new ArrayList<>();

	private int								webServiceRefreshTypeId;
	private String							primaryConnector;

	private List<String>					connectorNames;
	private List<AbstractDataElement>		connectorDataElements;
	private List<DataElementVariable>		connectorDataElementVariables;

	private int								fieldSizeTypeId;
	private Integer							minLength;
	private Integer							maxLength;
	private int								fieldValidationTypeId;
	private String							textDefaultValue;
	private int								singleSelectDefaultValue		= -1;
	private List<Integer>					multiSelectDefaultValues		= new ArrayList<>();
	private String							dateDefaultValue;
	private String							monthDefaultValue;
	private boolean 						defaultDateValueToTodaysDate	= false;
	private boolean							uniqueValue						= false;
	private String                          guid;
	private String                          regexValidation;
	private String							criteriaOperator;
	private Integer							repeatingDataTypeId		= RepeatingDataType.ID_NONE;
	private Set<TouchpointSelection>		applicableVariants				= new HashSet<>();

	private List<CriteriaItemVO> criteriaItems = new ArrayList<>();

	public MetadataFormItemDefinitionVO() {
		super();
	}

	public MetadataFormItemDefinitionVO(CommunicationOrderEntryItemDefinition itemDefinition) throws Exception {

		this.id						= itemDefinition.getId();
		this.name					= itemDefinition.getName();
		this.description			= itemDefinition.getDescription();
		this.order					= itemDefinition.getOrder();
		this.isPrimaryDriverEntry	= itemDefinition.getIsPrimaryDriverEntry();
		this.isIndicatorEntry		= itemDefinition.getIsIndicatorEntry();
		this.isMandatory			= itemDefinition.getIsManadatory();
		this.isLockedForEdit		= itemDefinition.getIsLockedForEdit();
		this.displayCriteria		= itemDefinition.getDisplayTriggerValues();
		this.parentOrder			= itemDefinition.getParentItemOrder();
		this.primaryConnector		= itemDefinition.getPrimaryConnector();
		this.dataPrivacyTypeId      = itemDefinition.getDataPrivacyTypeId();
		this.uploadedFileTypeId		= itemDefinition.getUploadedFileTypeId();
		this.uploadedFileDataSource = itemDefinition.getUploadedFileDataSource();
		this.fieldSizeTypeId		= itemDefinition.getFieldSizeTypeId();
		this.minLength				= itemDefinition.getFieldMinLength();
		this.maxLength				= itemDefinition.getFieldMaxLength();
		this.fieldValidationTypeId  = itemDefinition.getInputValidationTypeId();
		this.defaultDateValueToTodaysDate = itemDefinition.isDefaultDateValueToTodaysDate();
		this.uniqueValue			= itemDefinition.isUniqueValue();
		this.guid                   = itemDefinition.getGuid();
		this.regexValidation        = itemDefinition.getRegexValidation();
		this.criteriaOperator       = itemDefinition.getCriteriaOperator();
		this.repeatingDataTypeId    = itemDefinition.getRepeatingDataTypeId();

		this.applicableVariants		= itemDefinition.getApplicableSelections();

		this.dataElementVariable	= itemDefinition.getDataElementVariable();
		this.type			= new MetadataFormItemType(itemDefinition.getTypeId());

		this.menuValueItems = itemDefinition.getMenuValueItemsList();

		this.webServiceRefreshTypeId= itemDefinition.getWebServiceRefreshTypeId();

		this.connectorNames 		= new ArrayList<>();
		this.connectorDataElementVariables 	= new ArrayList<>();
		if ( !itemDefinition.getConnectorVariableMap().isEmpty() ) {
			for ( String currentConnector: itemDefinition.getConnectorVariableMap().keySet() ) {
				this.connectorNames.add( currentConnector );
				this.connectorDataElementVariables.add( itemDefinition.getConnectorVariableMap().get(currentConnector) );
			}
		}
		if (itemDefinition.getCriteriaTriggerValuesJson() != null || (itemDefinition.getCriteriaTriggerValuesJson() != null && !itemDefinition.getCriteriaTriggerValuesJson().isEmpty())) {
			ObjectMapper objectMapper = new ObjectMapper();
			this.criteriaItems = List.of(objectMapper.readValue(itemDefinition.getCriteriaTriggerValuesJson(), CriteriaItemVO[].class));
		}

		// Default input value
		if(itemDefinition.getDefaultInputValue() != null && !itemDefinition.getDefaultInputValue().isEmpty()){
			if(this.type.getId() == MetadataFormItemType.ID_SELECT_MENU){
				this.singleSelectDefaultValue = Integer.valueOf(itemDefinition.getDefaultInputValue());
			}else if(this.type.getId() == MetadataFormItemType.ID_MULTISELECT_MENU){
				String[] selectedIdxSplt = itemDefinition.getDefaultInputValue().split(",");
				for(String selectedIdx : selectedIdxSplt){
					this.multiSelectDefaultValues.add(Integer.valueOf(selectedIdx));
				}
			}else if(this.type.getId() == MetadataFormItemType.ID_TEXT || this.type.getId() == MetadataFormItemType.ID_TEXTAREA){
				this.textDefaultValue = itemDefinition.getDefaultInputValue();
			}else if(this.type.getId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR){
				if(!itemDefinition.isDefaultDateValueToTodaysDate()){
					try {
						SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
						Date date= persistedDF.parse(itemDefinition.getDefaultInputValue());

						Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
						SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
						this.dateDefaultValue = viewDF.format(date);
					} catch (ParseException e) {
						log.error("ERROR: Unable to parse metadata persisted date: " + e);
					}
				}
			}else if(this.type.getId() == MetadataFormItemType.ID_DATE_MONTH_YEAR){
				try {
					SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
					Date date= persistedDF.parse(itemDefinition.getDefaultInputValue());

					Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
					SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
					this.monthDefaultValue = viewDF.format(date);
				} catch (ParseException e) {
					log.error("ERROR: Unable to parse metadata persisted date: " + e);
				}
			}
		}
	}
	public MetadataFormItemDefinitionVO(MetadataFormItemDefinition itemDefinition) throws Exception{

		this.id						= itemDefinition.getId();
		this.name					= itemDefinition.getName();
		this.description			= itemDefinition.getDescription();
		this.order					= itemDefinition.getOrder();
		this.isPrimaryDriverEntry	= false;
		this.isIndicatorEntry		= false;
		this.isMandatory			= itemDefinition.getIsManadatory();
		this.isLockedForEdit		= itemDefinition.getIsLockedForEdit();
		this.displayCriteria		= itemDefinition.getDisplayTriggerValues();
		this.parentOrder			= itemDefinition.getParentItemOrder();
		this.autoGenerateOnImport	= itemDefinition.getAutoGenerateOnImport();
		this.primaryConnector		= itemDefinition.getPrimaryConnector();
		this.dataPrivacyTypeId      = CommunicationDataPrivacyType.ID_UNRESTRICTED;
		this.fieldSizeTypeId		= itemDefinition.getFieldSizeTypeId();
		this.minLength				= itemDefinition.getFieldMinLength();
		this.maxLength				= itemDefinition.getFieldMaxLength();
		this.fieldValidationTypeId  = itemDefinition.getInputValidationTypeId();
		this.defaultDateValueToTodaysDate = itemDefinition.isDefaultDateValueToTodaysDate();
		this.uniqueValue			= itemDefinition.isUniqueValue();
		this.guid                   = itemDefinition.getGuid();
		this.criteriaOperator       = itemDefinition.getCriteriaOperator();

		this.dataElement	= itemDefinition.getDataElement();
		this.type			= new MetadataFormItemType(itemDefinition.getTypeId());

		this.menuValueItems = itemDefinition.getMenuValueItemsList();

		this.webServiceRefreshTypeId= itemDefinition.getWebServiceRefreshTypeId();

		ObjectMapper objectMapper = new ObjectMapper();
		if (itemDefinition.getCriteriaTriggerValuesJson() != null) {
			this.criteriaItems = List.of(objectMapper.readValue(itemDefinition.getCriteriaTriggerValuesJson(), CriteriaItemVO[].class));
		} else {
			this.criteriaItems = new ArrayList<>();
		}

		this.connectorNames 		= new ArrayList<>();
		this.connectorDataElements 	= new ArrayList<>();
		if ( !itemDefinition.getConnectorElementMap().isEmpty() ) {
			for ( String currentConnector: itemDefinition.getConnectorElementMap().keySet() ) {
				this.connectorNames.add( currentConnector );
				this.connectorDataElements.add( itemDefinition.getConnectorElementMap().get(currentConnector) );
			}
		}

		// Default input value
		if(itemDefinition.getDefaultInputValue() != null && !itemDefinition.getDefaultInputValue().isEmpty()){
			if(this.type.getId() == MetadataFormItemType.ID_SELECT_MENU){
				this.singleSelectDefaultValue = Integer.valueOf(itemDefinition.getDefaultInputValue());
			}else if(this.type.getId() == MetadataFormItemType.ID_MULTISELECT_MENU){
				String[] selectedIdxSplt = itemDefinition.getDefaultInputValue().split(",");
				for(String selectedIdx : selectedIdxSplt){
					this.multiSelectDefaultValues.add(Integer.valueOf(selectedIdx));
				}
			}else if(this.type.getId() == MetadataFormItemType.ID_TEXT || this.type.getId() == MetadataFormItemType.ID_TEXTAREA){
				this.textDefaultValue = itemDefinition.getDefaultInputValue();
			}else if(this.type.getId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR){
				if(!itemDefinition.isDefaultDateValueToTodaysDate()){
					try {
						SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
						Date date= persistedDF.parse(itemDefinition.getDefaultInputValue());

						Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
						SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
						this.dateDefaultValue = viewDF.format(date);
					} catch (ParseException e) {
						log.error("ERROR: Unable to parse metadata persisted date: " + e);
					}
				}
			}else if(this.type.getId() == MetadataFormItemType.ID_DATE_MONTH_YEAR){
				try {
					SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
					Date date= persistedDF.parse(itemDefinition.getDefaultInputValue());

					Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
					SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths( new DateFormatSymbols(locale) ));
					this.monthDefaultValue = viewDF.format(date);
				} catch (ParseException e) {
					log.error("ERROR: Unable to parse metadata persisted date: " + e);
				}
			}
		}
	}

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}

	public int getAction() {
		return action;
	}
	public void setAction(int action) {
		this.action = action;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}

	public boolean getIsPrimaryDriverEntry() {
		return isPrimaryDriverEntry;
	}
	public void setIsPrimaryDriverEntry(boolean isPrimaryDriverEntry) {
		this.isPrimaryDriverEntry = isPrimaryDriverEntry;
	}

	public boolean getIsIndicatorEntry() {
		return isIndicatorEntry;
	}
	public void setIsIndicatorEntry(boolean isIndicatorEntry) {
		this.isIndicatorEntry = isIndicatorEntry;
	}

	public boolean getIsMandatory() {
		return isMandatory;
	}
	public void setIsMandatory(boolean isMandatory) {
		this.isMandatory = isMandatory;
	}

	public boolean getIsLockedForEdit() {
		return isLockedForEdit;
	}
	public void setIsLockedForEdit(boolean isLockedForEdit) {
		this.isLockedForEdit = isLockedForEdit;
	}

	public String getDisplayCriteria() {
		return displayCriteria;
	}
	public void setDisplayCriteria(String displayCriteria) {
		this.displayCriteria = displayCriteria;
	}

	public Integer getParentOrder() {
		return parentOrder;
	}
	public void setParentOrder(Integer parentOrder) {
		this.parentOrder = parentOrder;
	}

	public int getDataPrivacyTypeId() {
		return dataPrivacyTypeId;
	}
	public void setDataPrivacyTypeId(int dataPrivacyTypeId) {
		this.dataPrivacyTypeId = dataPrivacyTypeId;
	}

	public int getUploadedFileTypeId() {
		return uploadedFileTypeId;
	}
	public void setUploadedFileTypeId(int uploadedFileTypeId) {
		this.uploadedFileTypeId = uploadedFileTypeId;
	}

	public DataSource getUploadedFileDataSource() {
		return uploadedFileDataSource;
	}
	public void setUploadedFileDataSource(DataSource uploadedFileDataSource) {
		this.uploadedFileDataSource = uploadedFileDataSource;
	}

	public boolean isAutoGenerateOnImport() {
		return autoGenerateOnImport;
	}
	public void setAutoGenerateOnImport(boolean autoGenerateOnImport) {
		this.autoGenerateOnImport = autoGenerateOnImport;
	}

	public AbstractDataElement getDataElement() {
		return dataElement;
	}
	public void setDataElement(AbstractDataElement dataElement) {
		this.dataElement = dataElement;
	}

	public DataElementVariable getDataElementVariable() {
		return dataElementVariable;
	}
	public void setDataElementVariable(DataElementVariable dataElementVariable) {
		this.dataElementVariable = dataElementVariable;
	}

	public MetadataFormItemType getType() {
		return type;
	}
	public void setType(MetadataFormItemType type) {
		this.type = type;
	}

	public List<String> getMenuValueItems() {
		return menuValueItems;
	}
	public void setMenuValueItems(List<String> menuValueItems) {
		this.menuValueItems = menuValueItems;
	}

	public int getWebServiceRefreshTypeId() {
		return webServiceRefreshTypeId;
	}
	public void setWebServiceRefreshTypeId(int webServiceRefreshTypeId) {
		this.webServiceRefreshTypeId = webServiceRefreshTypeId;
	}

	public String getPrimaryConnector() {
		return primaryConnector;
	}
	public void setPrimaryConnector(String primaryConnector) {
		this.primaryConnector = primaryConnector;
	}

	public List<String> getConnectorNames() {
		return connectorNames;
	}
	public void setConnectorNames(List<String> connectorNames) {
		this.connectorNames = connectorNames;
	}

	public List<AbstractDataElement> getConnectorDataElements() {
		return connectorDataElements;
	}
	public void setConnectorDataElements(
			List<AbstractDataElement> connectorDataElements) {
		this.connectorDataElements = connectorDataElements;
	}

	public List<DataElementVariable> getConnectorDataElementVariables() {
		return connectorDataElementVariables;
	}
	public void setConnectorDataElementVariables(
			List<DataElementVariable> connectorDataElementVariables) {
		this.connectorDataElementVariables = connectorDataElementVariables;
	}
	public String getGuid() {
		return guid;
	}
	public void setGuid(String guid) {
		this.guid = guid;
	}

	public int getFieldSizeTypeId() {
		return fieldSizeTypeId;
	}

	public void setFieldSizeTypeId(int fieldSizeTypeId) {
		this.fieldSizeTypeId = fieldSizeTypeId;
	}

	public Integer getMaxLength() {
		return maxLength;
	}

	public void setMaxLength(Integer maxLength) {
		this.maxLength = maxLength;
	}
	public Integer getMinLength() {
		return minLength;
	}

	public void setMinLength(Integer minLength) {
		this.minLength = minLength;
	}

	public int getFieldValidationTypeId() {
		return fieldValidationTypeId;
	}

	public void setFieldValidationTypeId(int fieldValidationTypeId) {
		this.fieldValidationTypeId = fieldValidationTypeId;
	}

	public String getTextDefaultValue() {
		return textDefaultValue;
	}

	public void setTextDefaultValue(String textDefaultValue) {
		this.textDefaultValue = textDefaultValue;
	}

	public int getSingleSelectDefaultValue() {
		return singleSelectDefaultValue;
	}

	public void setSingleSelectDefaultValue(int singleSelectDefaultValue) {
		this.singleSelectDefaultValue = singleSelectDefaultValue;
	}

	public List<Integer> getMultiSelectDefaultValues() {
		return multiSelectDefaultValues;
	}

	public void setMultiSelectDefaultValues(List<Integer> multiSelectDefaultValues) {
		this.multiSelectDefaultValues = multiSelectDefaultValues;
	}

	public String getDateDefaultValue() {
		return dateDefaultValue;
	}

	public void setDateDefaultValue(String dateDefaultValue) {
		this.dateDefaultValue = dateDefaultValue;
	}

	public String getMonthDefaultValue() {
		return monthDefaultValue;
	}

	public void setMonthDefaultValue(String monthDefaultValue) {
		this.monthDefaultValue = monthDefaultValue;
	}

	public boolean isDefaultDateValueToTodaysDate() {
		return defaultDateValueToTodaysDate;
	}

	public void setDefaultDateValueToTodaysDate(boolean defaultDateValueToTodaysDate) {
		this.defaultDateValueToTodaysDate = defaultDateValueToTodaysDate;
	}

	public boolean isUniqueValue() {
		return uniqueValue;
	}

	public void setUniqueValue(boolean uniqueValue) {
		this.uniqueValue = uniqueValue;
	}

	public Set<TouchpointSelection> getApplicableVariants() {
		return applicableVariants;
	}

	public void setApplicableVariants(Set<TouchpointSelection> applicableVariants) {
		this.applicableVariants = applicableVariants;
	}

	public List<CriteriaItemVO> getCriteriaItems() {
		return criteriaItems;
	}
	public void setCriteriaItems(List<CriteriaItemVO> criteriaItems) {
		this.criteriaItems = criteriaItems;
	}

	public String getRegexValidation() {
		return regexValidation;
	}

	public void setRegexValidation(String regexValidation) {
		this.regexValidation = regexValidation;
	}

	public String getCriteriaOperator() {
		return criteriaOperator;
	}

	public void setCriteriaOperator(String criteriaOperator) {
		this.criteriaOperator = criteriaOperator;
	}

	public int getDataElementTypeId() {
		return dataElementTypeId;
	}

	public void setDataElementTypeId(int dataElementTypeId) {
		this.dataElementTypeId = dataElementTypeId;
	}

	public String getDataElementInputTypeFormat() {
		return dataElementInputTypeFormat;
	}

	public void setDataElementInputTypeFormat(String dataElementInputTypeFormat) {
		this.dataElementInputTypeFormat = dataElementInputTypeFormat;
	}
	public Integer getRepeatingDataTypeId() {
		return repeatingDataTypeId;
	}

	public void setRepeatingDataTypeId(Integer repeatingDataTypeId) {
		this.repeatingDataTypeId = repeatingDataTypeId;
	}



	public MetadataFormItemDefinitionVO getParentItem(MetadataFormItemDefinitionVO[] items) {
		if (this.getParentOrder() != null) {
			List<MetadataFormItemDefinitionVO> coeiList = new ArrayList<>();
					coeiList = Arrays.stream(items)
							.filter(c -> c.getOrder() == this.getParentOrder())
							.collect(Collectors.toList());
			if (!coeiList.isEmpty())
				return coeiList.iterator().next();

			}

			return null;
	}
	
}