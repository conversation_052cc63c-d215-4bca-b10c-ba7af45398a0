package com.prinova.messagepoint.controller.metadata;

import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;

import java.util.List;

public class MetadataPointsOfInterestValidator extends MessagepointInputValidator {

    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        MetadataPointsOfInterestWrapper wrapper = (MetadataPointsOfInterestWrapper) commandObj;
        boolean labelError = false;
        boolean valueError = false;

        List<MetadataPointsOfInterestDto> metadataPointsOfInterestDtoList = wrapper.getMetadataPointsOfInterestDtoList();

        for (MetadataPointsOfInterestDto crtMetadataPointsOfInterestDto : metadataPointsOfInterestDtoList) {
            if (!crtMetadataPointsOfInterestDto.isMarkedAsDeleted()) {
                if (StringUtils.isEmpty(crtMetadataPointsOfInterestDto.getComparisionValue())) {
                    valueError = true;
                }
                if (StringUtils.isEmpty(crtMetadataPointsOfInterestDto.getLabel())) {
                    labelError = true;
                }
            }
        }

        if (labelError) {
            errors.reject("error.metadata.point.of.interest.label");
        }
        if (valueError) {
            errors.reject("error.metadata.point.of.interest.value");
        }
    }
}
