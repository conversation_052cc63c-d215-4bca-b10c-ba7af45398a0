package com.prinova.messagepoint.controller.metadata;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.util.InterviewValuesVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public  class MetadataFormItemDefinitionUtil {

    public static final String OR_OPERATOR = "OR";
    public static final String AND_OPERATOR = "AND";
    public static final String DOCUMENT_ID = "documentId";

    private MetadataFormItemDefinitionUtil() {

    }
    public static boolean isAppliedItemForParents(MetadataFormItemDefinitionVO[] items, Communication communication, MetadataFormItemDefinitionVO item, InterviewValuesVO[] interviewValues) {
        boolean isAppliedItem = true;
        if (item.getCriteriaOperator() != null && item.getCriteriaOperator().equals(OR_OPERATOR)) {
            isAppliedItem = false;
        }
        if(item.getCriteriaItems() != null && !item.getCriteriaItems().isEmpty()){
            for ( CriteriaItemVO criteriaItemVO : item.getCriteriaItems() ) {
                MetadataFormItemDefinitionVO parentOrder = getParentItemByParentOrder(items, criteriaItemVO.getParentOrder());
                if (item.getCriteriaOperator() != null && item.getCriteriaOperator().equals(OR_OPERATOR)){
                    isAppliedItem = isAppliedItem || isAppliedItem(items, communication, item, interviewValues, parentOrder);
                }
                if (item.getCriteriaOperator() != null && item.getCriteriaOperator().equals(AND_OPERATOR)){
                    isAppliedItem = isAppliedItem && isAppliedItem(items, communication, item, interviewValues, parentOrder);
                } else if(item.getCriteriaOperator() == null){
                    isAppliedItem = isAppliedItem && isAppliedItem(items, communication, item, interviewValues, parentOrder);
                }

            }
        } else if (item.getParentOrder() != null && item.getParentOrder() > 0 &&  item.getDisplayCriteria() != null && !item.getDisplayCriteria().isEmpty()){
            MetadataFormItemDefinitionVO parentOrder = getParentItemByParentOrder(items, item.getParentOrder());
            isAppliedItem = isAppliedItem(items, communication, item, interviewValues, parentOrder);
        }
        return  isAppliedItem;
    }


    private static MetadataFormItemDefinitionVO getParentItemByParentOrder(MetadataFormItemDefinitionVO[] items, long parentOrder) {
        if (parentOrder >= 1) {
            List<MetadataFormItemDefinitionVO> coeiList = new ArrayList<>();
            coeiList = Arrays.stream(items)
                    .filter(c -> c.getOrder() == parentOrder)
                    .collect(Collectors.toList());
            if (!coeiList.isEmpty())
                return coeiList.iterator().next();

        }

        return null;
    }

    private static boolean isAppliedItem(MetadataFormItemDefinitionVO[] items, Communication communication, MetadataFormItemDefinitionVO item, InterviewValuesVO[] interviewValues, MetadataFormItemDefinitionVO parentItem) {
        MetadataFormItemDefinitionVO currentItem = item;
        boolean	appliesVariantSelection = communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() > 0;
        TouchpointSelection appliedVariant = appliesVariantSelection ? TouchpointSelection.findById(communication.getTouchpointSelectionId()) : null;
        while ( parentItem != null ) {
            boolean isCurrentItemApplied = false;
            if(parentItem.getType().getId() == MetadataFormItemType.ID_DIVIDER || parentItem.getType().getId() == MetadataFormItemType.ID_HEADER){
                isCurrentItemApplied = true;
            }
            if(currentItem.getType().getId() == MetadataFormItemType.ID_DIVIDER || currentItem.getType().getId() == MetadataFormItemType.ID_HEADER){
                isCurrentItemApplied = true;
            }
            InterviewValuesVO interviewValue = getInterviewItemValue(interviewValues, parentItem.getGuid());
            List<String> parentValuesList 	= getValuesArray(interviewValue);

            List<String> triggerValues = new ArrayList<>();

            if(currentItem.getCriteriaItems() != null && !currentItem.getCriteriaItems().isEmpty()){
                for ( CriteriaItemVO criteriaItemVO : currentItem.getCriteriaItems() ) {
                    if(criteriaItemVO.getParentOrder() == parentItem.getOrder()) {
                        List<String> criteriaItemsList = Arrays.stream(criteriaItemVO.getDisplayCriteria().split(",")).toList();
                        triggerValues.addAll(criteriaItemsList);
                    }
                }
            } else if (currentItem.getDisplayCriteria() != null && !currentItem.getDisplayCriteria().isEmpty()){
                triggerValues = Arrays.stream(currentItem.getDisplayCriteria().split(",")).toList();
            }
            for ( String currentValue : parentValuesList ) {
                for ( int i = 0; i < triggerValues.size(); i++ ) {
                    if (triggerValues.get(i).equalsIgnoreCase("*not_empty_criteria") && currentValue!= null && !currentValue.isEmpty()) {
                        isCurrentItemApplied = true;
                    }
                    if (currentValue!= null && currentValue.isEmpty() && triggerValues.get(i).equals("*empty_criteria")){
                        isCurrentItemApplied = true;
                    }

                    if (currentValue != null && currentValue.trim().equals(triggerValues.get(i).trim()))
                        isCurrentItemApplied = true;
                    if (currentValue != null && currentValue.trim().equals("[\"" + triggerValues.get(i).trim() + "\"]"))
                        // for new connected, value is stored as ["value"]
                        isCurrentItemApplied = true;
                    if (isCurrentItemApplied) {
                        break;
                    }
                }
            }

            boolean isCurrentItemVariableApplicable = appliedVariant != null ?
                    !(currentItem.getApplicableVariants() != null && !currentItem.getApplicableVariants().isEmpty() &&
                            !currentItem.getApplicableVariants().contains(appliedVariant)) : true;
            boolean isParentItemVariableApplicable = appliedVariant != null ?
                    !(parentItem.getApplicableVariants() != null && !parentItem.getApplicableVariants().isEmpty() &&
                            !parentItem.getApplicableVariants().contains(appliedVariant)) : true;

            if ( !isCurrentItemVariableApplicable || !isParentItemVariableApplicable )
                isCurrentItemApplied = false;

            if ( isCurrentItemApplied ) {
                currentItem = parentItem;
                boolean foundedParent = false;
                for ( CriteriaItemVO criteriaItemVO : currentItem.getCriteriaItems() ) {
                    foundedParent = true;
                    parentItem = getParentItemByParentOrder(items, criteriaItemVO.getParentOrder());

                }

                if(currentItem.getCriteriaItems() == null || (currentItem.getCriteriaItems() != null && currentItem.getCriteriaItems().isEmpty())) {
                    foundedParent = true;
                    parentItem = parentItem.getParentItem(items);
                }

                if(!foundedParent){
                    parentItem = null;
                }

            } else {
                return false;
            }

            if ( appliesVariantSelection ) {
                communication.getTouchpointSelectionId();
            }
        }


        if ( currentItem.getApplicableVariants() != null && !currentItem.getApplicableVariants().isEmpty() &&
                !currentItem.getApplicableVariants().contains(appliedVariant) )
            return false;
        else
            return true; // No parent: Always applied

    }

    public static InterviewValuesVO getInterviewItemValue(InterviewValuesVO[] items, String guid) {
        for (InterviewValuesVO item : items) {
            if (item.getGuid().equalsIgnoreCase(guid)) {
                return item;
            }
        }
        return null;
    }


    private static List<String> getValuesArray(InterviewValuesVO item) {

        if (item == null) return new ArrayList<>();

        if(item.getValue() == null)
            return new ArrayList<>();

        Object value = item.getValue();
        if ( value == null )
            return new ArrayList<>();

        List<String> valuesList = new ArrayList<>();
        if (value != null && !value.toString().isEmpty() && value instanceof ArrayList) {
            ArrayList<String> arrayValue = (ArrayList<String>) value;
            for ( int i = 0; i < arrayValue.size(); i++ )
                valuesList.add(arrayValue.get(i).trim());
        } else {
            String[] valuesArray = value.toString().split(",");
            for (int i = 0; i < valuesArray.length; i++)
                valuesList.add(valuesArray[i].trim());
        }
        return valuesList;
    }
}
