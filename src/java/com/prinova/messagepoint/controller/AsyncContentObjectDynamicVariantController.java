package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncContentObjectDynamicVariantController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentObjectDynamicVariantController.class);

	public static final String PARAM_DOCUMENT_ID						= "documentId";
	public static final String PARAM_TYPE 								= "type";
	public static final String PARAM_SELECTED_PARM_INSTANCE 		    = "paramInstId";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseJSON(request).getBytes());
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
		}
		
		return null;
	}

	private String getResponseJSON (HttpServletRequest request) {
		ParameterGroupTreeNode p = null;
		JSONObject returnObj = new JSONObject();

		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		long pgTreeNodeId = ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_PARM_INSTANCE, -1);

		if (pgTreeNodeId > 0) {
			p = ParameterGroupTreeNode.findById(pgTreeNodeId);
		}

		if (contentObject != null) {
			try {
				returnObj.put("embeddedContent_id", contentObject.getId());
				returnObj.put("name", contentObject.getName());
				returnObj.put("paramInstId", p.getId());
				returnObj.put("variant_name", p.getName());

			} catch (JSONException e) {
				log.error("Error: Unable to retrieve content object info: " + e);
			}
		}

		return returnObj.toString();

	}
}