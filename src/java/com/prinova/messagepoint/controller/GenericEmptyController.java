package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpServletRequest;
import java.io.Serial;
import java.io.Serializable;

public class GenericEmptyController extends MessagepointController {

    @SuppressWarnings("NullableProblems")
    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return new GenericEmptyCommand();
    }

    private static class GenericEmptyCommand implements Serializable {
        @Serial
        private static final long serialVersionUID = -1L;
    }
}
