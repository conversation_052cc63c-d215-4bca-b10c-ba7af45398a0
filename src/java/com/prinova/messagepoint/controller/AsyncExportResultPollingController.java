package com.prinova.messagepoint.controller;

import java.io.File;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.export.ExportUtil;
import com.prinova.messagepoint.platform.services.export.ExportUtil.ExportType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncExportResultPollingController implements Controller {

	public static final String REQ_PARAM_EXPORT_ID 			= "exportId";
	public static final String REQ_PARAM_TYPE 				= "type";
	public static final String REQ_PARAM_EXPORT_NAME		= "exportName";
	public static final String REQ_EXT_TYPE					= "extType";
	
	public static final String TYPE_DYNAMICIMAGE 			= "dynamicimage";
	public static final String TYPE_TOUCHPOINT 				= "touchpoint";
	public static final String TYPE_DYNAMICTEXT 			= "dynamictext";
	public static final String TYPE_DYNAMICSMARTCANVAS		= "dynamicsmartcanvas";
	public static final String TYPE_DYNAMICMESSAGE			= "dynamicmessage";
	public static final String TYPE_TPVARIATNS				= "tpvariants";
	public static final String TYPE_TPMESSSAGES				= "tpmessages";
	public static final String TYPE_RLDOCUMENTS				= "rldocuments";
	public static final String TYPE_JOBPERFORMANCEREPORT	= "jobperformancereport";
	

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		
		ServletOutputStream out = response.getOutputStream();

		String exportName			= getExportName(request);
		String type 				= getType(request);
		String exportId 			= getExportId(request);
		String exportFilerootPath 	= ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir );
		User user 					= UserUtil.getPrincipalUser();
		ExportType exportIdentifier = null;
		int extType				= getExtType(request);
		
		JSONObject obj = new JSONObject();
		if ( type.equals(TYPE_TOUCHPOINT) )
			exportIdentifier = ExportUtil.ExportType.DOCUMENT;
		if ( type.equals(TYPE_DYNAMICIMAGE) )
			exportIdentifier = ExportUtil.ExportType.DYNAMICIMAGE;
		if ( type.equals(TYPE_DYNAMICTEXT) )
			exportIdentifier = ExportUtil.ExportType.DYNAMICSMARTTEXT;
		if ( type.equals(TYPE_DYNAMICSMARTCANVAS))
			exportIdentifier = ExportType.DYNAMICSMARTCANVAS;
		if ( type.equals(TYPE_DYNAMICMESSAGE) )
			exportIdentifier = ExportUtil.ExportType.DYNAMICMESSAGE;
		if ( type.equals(TYPE_TPVARIATNS) )
			exportIdentifier = ExportUtil.ExportType.TPVARIANTS;
		if ( type.equals(TYPE_TPMESSSAGES) )
			exportIdentifier = ExportUtil.ExportType.TPMESSAGES;
		if ( type.equals(TYPE_RLDOCUMENTS))
			exportIdentifier = ExportUtil.ExportType.RATIONALIZERDOCUMENT;
		if ( type.equals(TYPE_JOBPERFORMANCEREPORT))
			exportIdentifier = ExportUtil.ExportType.JOBPERFORMANCEREPORT;
		
		String exportFilename = null;
		if (extType == ExportUtil.EXTENSION_XML_TYPE)
			exportFilename = ExportUtil.getMessagepointObjectExportFileName(user, exportIdentifier, exportId, exportName, ExportUtil.EXTENSION_XML_TYPE);
		else
			exportFilename = ExportUtil.getMessagepointObjectExportFileName(user, exportIdentifier, exportId, exportName);
		
		File reportFile = new File(exportFilerootPath + File.separator + exportFilename);
		if (reportFile.exists()) {
			obj.put("result", 			"COMPLETE");
			obj.put("exportXMLpath", 	exportFilerootPath + exportFilename);
			obj.put("extType", 			extType);
		} else
			obj.put("result", 			"PROCESSING");

		out.write(obj.toString().getBytes());
		out.flush();

		return null;
	}

	private String getExportId(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_EXPORT_ID, null);
	}

	private String getType(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, null);
	}

	private String getExportName(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_EXPORT_NAME, null);
	}
	
	private int getExtType(HttpServletRequest request) {
		return ServletRequestUtils.getIntParameter(request, REQ_EXT_TYPE, 0);
	}

}