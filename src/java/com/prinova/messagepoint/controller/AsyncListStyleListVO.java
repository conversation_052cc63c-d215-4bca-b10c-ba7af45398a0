package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.wrapper.AsyncListVO;
import com.prinova.messagepoint.model.wrapper.AsyncListVOIFrameData;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncListStyleListVO extends AsyncListVO{

	private ListStyle 					listStyle;
	private String						name;
	private String 						connectorName;
	private String						listSpacing;
	private String						bulletSpacing;
	private AsyncListVOIFrameData		iFrameData;
	private ListStyleListVOFlags		flags;

	public void setListStyle(ListStyle listStyle) {
		this.listStyle = listStyle;
	}

	public String getName() {
		String editURL = ApplicationUtil.getWebRoot() + "content/list_style_edit.form";
		String nameHTML = "<a id=\"actionLabel_"+ this.listStyle.getId() +"\" itemName=\"ITEM_NAME\" href=\"" + editURL + "?listStyleId=" + this.listStyle.getId() + "\" class=\"d-inline-block w-100 text-truncate\">" + name + "</a>";
		nameHTML = nameHTML.replace("ITEM_NAME",name);
		return "<div class=\"position-relative mt-n2\"><div class=\"text-truncate-wrapper\">" + nameHTML + "</div></div>";
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getConnectorName() {
		return connectorName;
	}

	public void setConnectorName(String connectorName) {
		this.connectorName = connectorName;
	}

	public String getListSpacing() {
		return listSpacing;
	}

	public void setListSpacing(String listSpacing) {
		this.listSpacing = listSpacing;
	}

	public String getBulletSpacing() {
		return bulletSpacing;
	}

	public void setBulletSpacing(String bulletSpacing) {
		this.bulletSpacing = bulletSpacing;
	}

	public AsyncListVOIFrameData getiFrameData() {
		return iFrameData;
	}

	public void setiFrameData(AsyncListVOIFrameData iFrameData) {
		this.iFrameData = iFrameData;
	}
	
	public String getBinding() {
		return "<input id='listItemCheck_"+this.listStyle.getId()+"' type='checkbox' value='"+this.listStyle.getId()+
				"' style='display : none;' name='selectedIds'><input type='hidden' value='on' name='_selectedIds'>";
	}

	public ListStyleListVOFlags getFlags() {
		return flags;
	}

	public void setFlags(ListStyleListVOFlags flags) {
		this.flags = flags;
	}

	public static class ListStyleListVOFlags{
		private boolean	canUpdate;
		private boolean canDelete;
		private boolean canClone;

		public boolean isCanUpdate() {
			return canUpdate;
		}
		public void setCanUpdate(boolean canUpdate) {
			this.canUpdate = canUpdate;
		}
		public boolean isCanDelete() {
			return canDelete;
		}
		public void setCanDelete(boolean canDelete) {
			this.canDelete = canDelete;
		}
		public boolean isCanClone() {
			return canClone;
		}
		public void setCanClone(boolean canClone) {
			this.canClone = canClone;
		}
	}	
}
