package com.prinova.messagepoint.controller;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;

import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninService;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;

/**
 * 
 * <AUTHOR>
 * 
 */
public class MessagepointAuthenticationFailureHandler extends SimpleUrlAuthenticationFailureHandler {

	private String authenticationFailureUrl;

	public String getAuthenticationFailureUrl() {
		return authenticationFailureUrl;
	}

	public void setAuthenticationFailureUrl(String authenticationFailureUrl) {
		this.authenticationFailureUrl = authenticationFailureUrl;
	}

	@Override
	public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
		String failureUrl = authenticationFailureUrl;

		String errorMsgKey = processInvalidSigninAttempt(request);
		String username = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
		String mpUsername = (String) request.getAttribute("mp_username");

		if (errorMsgKey != null){
			User user = User.findByUsername((mpUsername!=null)?mpUsername:username, false);
			if(user != null && user.checkIsAccountLocked()){
				failureUrl += MessagepointAuthenticationProcessingFilter.AUTHENTICATION_FAIL_ACCOUNTLOCKED_KEY;
			} else if(user != null && !user.isEnabled()){
				failureUrl += MessagepointAuthenticationProcessingFilter.AUTHENTICATION_FAIL_ACCESS_DENIED_KEY;
			}else{
				failureUrl += MessagepointAuthenticationProcessingFilter.AUTHENTICATION_FAIL_DEFAULTMSG_KEY;
			}
		}

		// added node GUID into signin.jsp request
		//
		String requestedNodeGUID = request.getParameter("gd");
		if (requestedNodeGUID != null && !requestedNodeGUID.isEmpty())
			failureUrl += "&gd=" + requestedNodeGUID;
		else
		{
			DefaultSavedRequest savedRequest = (DefaultSavedRequest)request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST");
			if (savedRequest != null)
			{
				String[] requestedNodeGUIDValue = savedRequest.getParameterValues("gd");
				if ( requestedNodeGUIDValue != null && requestedNodeGUIDValue.length > 0 )
					failureUrl += "&gd=" + requestedNodeGUIDValue[0];
			}
		}
		
		logUnsuccessfulAuthentication((mpUsername!=null)?mpUsername:username, errorMsgKey);

		if (failureUrl == null) {
			logger.debug("No failure URL set, sending 401 Unauthorized error");
			HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_UNAUTHORIZED, "Authentication Failed: " + exception.getMessage(), request, response);
		} else {
			saveException(request, exception);

			if (isUseForward()) {
				logger.debug("Forwarding to " + failureUrl);

				request.getRequestDispatcher(failureUrl).forward(request, response);
			} else {
				logger.debug("Redirecting to " + failureUrl);
				getRedirectStrategy().sendRedirect(request, response, failureUrl);
			}
		}
	}

	private void logUnsuccessfulAuthentication(String userName, String errorMsgKey) {
		if (logger.isInfoEnabled()) {
			String errorMsg = getUnsuccessfulAuthenticationByKey(errorMsgKey);
			logger.info(errorMsg + " Username: " + userName);
		}
		// Audit (Authentication failure)
		AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, userName, null, AuditActionType.ID_AUTHENTICATION_FAILURE, null);		
	}

	private String getUnsuccessfulAuthenticationByKey(String errorMsgKey) {
		String errorMsg = "Authentication failed;";
		if (errorMsgKey != null) {
			String msg = ApplicationUtil.getMessage(errorMsgKey);
			if (msg != null && !msg.trim().isEmpty())
				errorMsg = msg;
		}
		return errorMsg;
	}

	private String processInvalidSigninAttempt(HttpServletRequest request) {
		String springUsername = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
		String mpUsername = (String) request.getAttribute("mp_username");

		ServiceExecutionContext context = ProcessInvalidSigninService.createContext((mpUsername!=null)?mpUsername:springUsername);

		Service invalidSigninService = MessagepointServiceFactory.getInstance().lookupService(ProcessInvalidSigninService.SERVICE_NAME, ProcessInvalidSigninService.class);
		invalidSigninService.execute(context);
		if (context.getResponse().isSuccessful()) {
			ProcessInvalidSigninServiceResponse response = (ProcessInvalidSigninServiceResponse) context.getResponse();
			return response.getFailMsgKey();
		} else {
			return null;
		}

	}
}
