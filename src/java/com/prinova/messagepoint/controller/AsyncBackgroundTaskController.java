package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.DeliverableMessagePointModel;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class AsyncBackgroundTaskController implements Controller {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(AsyncBackgroundTaskController.class);

	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_TASK_ID 		= "task_id";
	public static final String REQ_PARAM_MYTASK         = "my_task";
	
	public static final String ACTION_TASK_DATA			= "task_data";
	public static final String ACTION_REMOVE_TASK		= "remove";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		String action 	= ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);
		long taskId 	= ServletRequestUtils.getLongParameter(request, REQ_PARM_TASK_ID, -1L);
		String myTask   = ServletRequestUtils.getStringParameter(request, REQ_PARAM_MYTASK, "true");
		
		JSONObject returnObj = new JSONObject();

		if ( action.equalsIgnoreCase(ACTION_TASK_DATA) ) {
			boolean showOnlyMyTask = myTask.equalsIgnoreCase("true");
			
			JSONArray taskArray = new JSONArray();

			try {
				
				User principalUser = UserUtil.getPrincipalUser();
				
				List<StatusPollingBackgroundTask> backgroundTasks = null;
				boolean hasTask = StatusPollingBackgroundTask.hasAnyTask();
				returnObj.put("has_task", hasTask);

				if(showOnlyMyTask) { 
					backgroundTasks = StatusPollingBackgroundTask.findAllVisibleBackgroundTaskCreatedByUser(principalUser);
				} else {
					backgroundTasks = StatusPollingBackgroundTask.findAllBackgroundTask();
				}

				backgroundTasks.sort(Comparator.comparing(DeliverableMessagePointModel::getRequestDate));
				Collections.reverse(backgroundTasks);
				
				if (!backgroundTasks.isEmpty()) {
					
					long now = new Date().getTime();
					
					for (StatusPollingBackgroundTask backgroundTask : backgroundTasks.stream().limit(250).collect(Collectors.toList())) {
						
						JSONObject taskObj = new JSONObject();
						boolean removeTask = false;

						Calendar midnight = Calendar.getInstance();
						long current_ms = midnight.getTimeInMillis();
						midnight.set(Calendar.HOUR_OF_DAY, 0);
						midnight.set(Calendar.MINUTE, 0);
						midnight.set(Calendar.SECOND, 0);
						midnight.set(Calendar.MILLISECOND, 0);
						long msSinceMidnight = current_ms - midnight.getTimeInMillis();
						Long backgroundTaskId = backgroundTask.getId();
						Long backgroundThreadId = backgroundTask.getBackgroundThreadId();
                        String logFilePath = backgroundTask.getOutputPath();

						taskObj.put("progress"		, backgroundTask.getProgressInPercentInThread());
						taskObj.put("description"	, backgroundTask.getDescription() != null ? backgroundTask.getDescription() : "");
						taskObj.put("task_name"		, backgroundTask.getDisplayName());
						taskObj.put("request_date"	, (now - backgroundTask.getRequestDate().getTime() < msSinceMidnight) ? 
														DateUtil.formatTime(backgroundTask.getRequestDate()) :
														DateUtil.formatDate(backgroundTask.getRequestDate()) );
						taskObj.put("id"			, backgroundTaskId);
						taskObj.put("thread_id"		, backgroundThreadId);
						taskObj.put("is_new"		, (now - backgroundTask.getRequestDate().getTime()) < 7000 );
						taskObj.put("execution_time", DateUtil.formatElapsedTime(backgroundTask.getActiveTimeInMilliseconds()));
						taskObj.put("target_obj_id", backgroundTask.getTargetObjectId());
						taskObj.put("task_type", backgroundTask.getType());
						
						taskObj.put("my_task", backgroundTask.getCreatedBy().longValue() == principalUser.getId());
						
						if(backgroundTask.isRefreshPage() != null && backgroundTask.isRefreshPage()  == true){
							taskObj.put("refresh_page",  true);
							backgroundTask.setRefreshPage(false);
							backgroundTask.save();
						}
						
						if ( backgroundTask.isActive() ) {
							// Status: In progress
							taskObj.put("status", "in_process");
							if (AsyncBackgroundTaskDetailUtil.getTaskDetailObject(backgroundThreadId) != null) {
								taskObj.put("hasDetailInfo", "true");
							}
						} else {
							if ( backgroundTask.isComplete() || backgroundTask.isError() ) {
								String logFileName = logFilePath;
								if (logFileName != null) {
									File logFile = new File(logFileName);
									if (logFile.exists() && logFile.length() > 0) {
										taskObj.put("logFilePath", HttpRequestUtil.getFileResourceToken(logFileName));
										taskObj.put("logFileTitle", logFile.getName());
									}
								}
							}
							
							if (backgroundTask.isComplete() && !backgroundTask.isError()) {
								// Status: Complete
								taskObj.put("status", "complete");

								if (backgroundTask.getOutputPath() != null) {
									// Task with some output file 
									File file = new File(backgroundTask.getOutputPath());
									if (file.exists()) {
										taskObj.put("resource", backgroundTask.getResourceToken());
										taskObj.put("downloadType", FileUtil.getFileExtension(backgroundTask.getOutputFilename()));
										taskObj.put("fileTitle", backgroundTask.getOutputFilename());
									} else {
										// Hide completed task without the existing output file
										// for example it can be an old export task
										backgroundTask.setVisible(false);
										backgroundTask.save();
										removeTask = true;
									}
								}
							} else {
								// Status: Error
								taskObj.put("status", "error");		
								// Task with error output file
								String path = backgroundTask.getOutputPath();
								if (path != null && !path.isEmpty()) {
									File file = new File(path);
									if (file.exists()) {
										taskObj.put("resource", backgroundTask.getResourceToken());
										taskObj.put("fileTitle", backgroundTask.getOutputFilename());
									}
								}
							} 
						}
						
						if ( !removeTask )
							taskArray.put(taskObj);
						
					} // END FOR TASK LOOP
					
				} // END IF TASKS EXIST

			} catch (Exception e) {
				
				returnObj.put("error"	, true);
				returnObj.put("message"	, e.getMessage());
				
			}
			
			returnObj.put("tasks", taskArray);
			
			// END: ACTION TASK DATA

		} else if ( action.equalsIgnoreCase(ACTION_REMOVE_TASK) ) {

			StatusPollingBackgroundTask task = StatusPollingBackgroundTask.findById(taskId);
			
			if ( task != null ) {
				Long backgroundThreadId = task.getBackgroundThreadId();
				String logFileName = task.getOutputPath();
				if (logFileName != null) {
					File logFile = new File(logFileName);
					if( logFile.exists() ) {
						logFile.delete();
					}
				}
				
				task.setVisible(false);
				task.save();
				returnObj.put("status", "success");
			} else {
				returnObj.put("error", true);
				returnObj.put("message", "Task remove failed: task not found!");
			}
			
		} else {
			
			returnObj.put("error", true);
			returnObj.put("message", "Async Background Task: Invalid action type");
		}
		
		return returnObj.toString();
	}

}