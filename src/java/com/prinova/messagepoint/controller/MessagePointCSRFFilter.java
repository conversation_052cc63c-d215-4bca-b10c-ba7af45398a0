package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.web.filter.OncePerRequestFilter;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;

public class MessagePointCSRFFilter extends OncePerRequestFilter {

	private static final Log log = LogUtil.getLog(MessagePointCSRFFilter.class);
	public static boolean enabled = true;

	public static final String METHOD_GET = "GET";
	private static boolean INIT = false;
	
	private Set<String> entryPoints = new HashSet<>();
	private Set<String> wildEntryPoints = new HashSet<>();
	
	private static final int VALID = 1;
	private static final int NOT_VALID = 2;
	private static final int NOT_KNOWN = 3;

	public void setEntryPoints(String entryPoints) {
		String values[] = entryPoints.split(",");
		for (String value : values) {
			if(value.indexOf("*") == -1)
				this.entryPoints.add(value.trim());
			else 
				this.wildEntryPoints.add(value.trim());
		}
	}

	public int validToken(HttpServletRequest request, HttpServletResponse response, String tokenFromSession, HttpSession session) throws Exception {
		int flag = validByHeader(request, tokenFromSession);

		if (flag == NOT_KNOWN)
			return validByRequest(request, response, tokenFromSession, session);

		if (flag == VALID)
			return 1;

		return 0;
	}

	private int validByHeader(HttpServletRequest request, String tokenFromSession) {
		// test code
		// Enumeration names = request.getHeaderNames();
		// while (names.hasMoreElements()) {
		// String key = (String) names.nextElement();
		// System.out.println(key+"="+request.getHeader(key));
		// }
		// System.out.println("=========================================================");

		String tokenFromHeader = request.getHeader(WebAppSecurity.CSRF_TOKEN_KEY);
		if (tokenFromHeader == null) {
			log.debug("token is missing from the request's header");
			return NOT_KNOWN;
		} else if (tokenFromSession == null || !tokenFromHeader.equals(tokenFromSession)) {
			log.debug(attackLogInfo(request));
			return NOT_VALID;
		}
		return VALID;
	}

	private int validByRequest(HttpServletRequest request, HttpServletResponse response, String tokenFromSession, HttpSession session) throws Exception{
		String tokenFromRequest = request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY);
		if (tokenFromRequest == null) {
			log.debug(attackLogInfo(request));
			return 0;
		}
		if(tokenFromRequest.equals(tokenFromSession)){
			return 1;
		}

		if (isPreviousToken(request, response, tokenFromRequest)) {
			return 2;
		}

		SecurityContext context = (SecurityContext) session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
		if(context != null){
			// get the authentication object from acegi context...
			Authentication auth = context.getAuthentication();
			User principal = null;
			if (auth != null) {
				Object authorizedUser = auth.getPrincipal();
				if (authorizedUser instanceof User) {
					principal = (User) authorizedUser;
					User dbUser = User.findById(principal.getId());
					
					if(!dbUser.isSuperuser() && !dbUser.isDomainAdminUser() && dbUser.checkIsPasswordExpired()){
						String targetUrl = ApplicationUtil.addToken(MessagepointAuthenticationSuccessHandler.passwordChangeRedirect, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
						response.sendRedirect(response.encodeRedirectURL(request.getContextPath() + targetUrl));
						return 2;
					}
					
					String tokenFromUser = dbUser.getEmailToken();
					if(tokenFromUser != null && !tokenFromUser.isEmpty() && (tokenFromUser.equals(tokenFromRequest) || tokenFromUser.equals(tokenFromSession) )){
						String fromUrl = request.getRequestURI();
						String toUrl = fromUrl + "?" + request.getQueryString().replace(WebAppSecurity.CSRF_TOKEN_KEY + "=" + tokenFromUser, WebAppSecurity.CSRF_TOKEN_KEY + "=" + tokenFromSession);
						response.sendRedirect(response.encodeRedirectURL(toUrl));
						return 2;
					}
					
					String groupIdParam = request.getParameter("groupId");
					String ruleIdParam = request.getParameter("ruleId");
					if((groupIdParam != null && !groupIdParam.isEmpty()) || (ruleIdParam != null && !ruleIdParam.isEmpty())) {
						String fromUrl = request.getRequestURI();
						String toUrl = fromUrl + "?" + request.getQueryString().replace(WebAppSecurity.CSRF_TOKEN_KEY + "=" + tokenFromRequest, WebAppSecurity.CSRF_TOKEN_KEY + "=" + tokenFromSession);
						response.sendRedirect(response.encodeRedirectURL(toUrl));
						return 2;
					}
				}
			}
		}
		if (!tokenFromSession.equals(tokenFromRequest)) {
			log.debug(attackLogInfo(request));
			return 0;
		}
		return 1;
	}

	@SuppressWarnings("unchecked")
	private boolean isPreviousToken(HttpServletRequest request, HttpServletResponse response, String tokenFromRequest) throws IOException {

		Object history = request.getSession().getAttribute(WebAppSecurity.CSRF_SESSION_HISTORY_KEY);

		if (history != null) {

			HashMap<String, String> tokenHistoryMap = ((HashMap<String, String>) history);

			if (tokenHistoryMap.containsKey(tokenFromRequest)) {

				String currentCsrfToken = (String) request.getSession().getAttribute(WebAppSecurity.CSRF_SESSION_KEY);

				response.sendRedirect(MessageFormat.format(request.getContextPath() + "/switch_to_node.form?tk={0}&gd={1}", currentCsrfToken, tokenHistoryMap.get(tokenFromRequest)));
				return true;
			}
		}

		return false;
	}

	public void destroy() {
		// TODO Auto-generated method stub
	}

	public static boolean isCSRFFilterEnabled() {
		return enabled;
	}

	public static void setEnabled(boolean enabled) {
		MessagePointCSRFFilter.enabled = enabled;
	}

	private String attackLogInfo(HttpServletRequest request) {
		StringBuilder sb = new StringBuilder();
		sb.append("Invalid security token found. Requested link is: ");
		sb.append(request.getRequestURL());
		sb.append(request.getQueryString() != null ? "?"+request.getQueryString() :"");
		sb.append(" from ");
		sb.append(HttpRequestUtil.getIpFromRequest(request));
		sb.append(":");
		sb.append(request.getRemotePort());
		sb.append(" at ");
		sb.append(DateUtil.now()).append(".");
//		sb.append(", the victim is: ");
//		sb.append(UserUtil.getPrincipalUser() != null ? UserUtil.getPrincipalUser() : "");

		return sb.toString();
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
		if (!INIT) {
			if (getFilterConfig().getInitParameter("prevention") != null) {
				if ("false".equals(getFilterConfig().getInitParameter("prevention")))
					setEnabled(false);
			}
			if(isCSRFFilterEnabled())
				setEntryPoints(getFilterConfig().getInitParameter("entryPoints"));
			
			INIT = true;
		}

		if (request.getHeader(HttpHeaders.AUTHORIZATION) != null) {
			// TODO: validate jwt
			filterChain.doFilter(request, response);
			return;
		}

		if (isCSRFFilterEnabled()) {
			String path = request.getServletPath();
			if (request.getPathInfo() != null) {
				path = path + request.getPathInfo();
			}
			
			//to resolve websphere issue 6657
			if(path != null && ("/".equals(path) || path.isEmpty())) {
				String contextRoot = request.getContextPath();
				String url = contextRoot + "/index.jsp";
				log.debug("inside csrf filter: hack code is working in websphere, now the request is redirecting to: " + url);
				response.sendRedirect(response.encodeRedirectURL(url));
				return;
			}
			
			if (HttpRequestUtil.isStaticResourcesRequest(request) || isExcludedURL(path)) {// those static resources though GET
				try {
					filterChain.doFilter(request, response);
				} catch (Throwable e) {
					Throwable rootCause = e;
					while ( rootCause.getCause() != null ) rootCause = rootCause.getCause();
					
					if (rootCause.getMessage() != null && rootCause.getMessage().equals("Broken pipe")) {
						log.error("Got Broken Pipe...");
					} else if (e instanceof RequestRejectedException) {
						log.error(e);
						log.error("URL: " + request.getRequestURL() + (request.getQueryString() != null ? ("?" + request.getQueryString()) : ""));
					}
					else
					{
						log.error("Throwable e rootCause message=" + rootCause.getMessage() + " rootCause = " + rootCause, rootCause);
						log.error("Throwable e (Throwable e) message=" + e.getMessage() + " e=" + e, e);
					}
				}
			} else {
				HttpSession session = request.getSession(true);
				if(session.isNew()) {
//					if(request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY)!=null){
//						String fromUrl = request.getRequestURI();
//						String toUrl = fromUrl + "?" + request.getQueryString();
//						session.setAttribute("reqestUrlAtNewSession", toUrl);
//						session.setAttribute("tkUrlApplied", false);
//					}
					try {
						filterChain.doFilter(request, response);
					} catch (Throwable e) {
						Throwable rootCause = e;
						while (rootCause.getCause() != null) rootCause = rootCause.getCause();

						log.error("Throwable e rootCause message=" + rootCause.getMessage() + " rootCause = " + rootCause, rootCause);
						log.error("Throwable e (Throwable e) message=" + e.getMessage() + " e=" + e, e);
					}
				} else{
					try {
						int tokenStatus = validToken(request, response, (String) session.getAttribute(WebAppSecurity.CSRF_SESSION_KEY), session);
						if (tokenStatus == 2)
							return;
						
						if (tokenStatus > 0) {
							filterChain.doFilter(request, response);
						}
						else { 
							String contextRoot = request.getContextPath();
							String requestedUrl = request.getRequestURI() + (request.getQueryString() != null ? ("?" + request.getQueryString()) : "");
							requestedUrl = requestedUrl.replace(contextRoot, "");

							String acceptHeader = request.getHeader("Accepts");
							boolean isGetRequest = request.getMethod().equals("GET");
							if (isGetRequest && acceptHeader != null && (acceptHeader.toLowerCase().contains("/html") || acceptHeader.toLowerCase().contains("/xhtml"))) {
								request.getSession().setAttribute("MESSAGEPOINT_SAVED_URL_REQUEST", requestedUrl);
							}

							String url = contextRoot + "/signin.jsp?msgkey=code.text.session.timeout";
							
							String requestedNodeGUID = request.getParameter("gd");
							if (requestedNodeGUID != null)
								url += "&gd=" + requestedNodeGUID;
							String requestedToken = request.getParameter("tk");
							if (requestedNodeGUID != null && requestedToken != null){
								Node node = Node.findByGuid(requestedNodeGUID);
								if(node != null){
									User tempUser = UserUtil.getNodeUserByEmailToken(requestedToken, node.getSchemaName());
									if(tempUser != null){
										if(tempUser.isSSOUser()){
											Branch homeBranch = Branch.findBySSOIdPId(tempUser.getIdPIdentifier(), null);
											if(homeBranch != null && !node.getBranch().getName().equals(homeBranch.getName())){
												url += "&ssohome=" + homeBranch.getName();
											}
										}else if(tempUser.isExternalDomainUser()){
											Node homeNode = Node.findByGuid(tempUser.getIdPIdentifier());
											if(homeNode != null){
												url += "&home=" + homeNode.getBranch().getName();
											}
										}
									}
								}
							}
							
							response.sendRedirect(response.encodeRedirectURL(url));
							// code below is replaced with the one above because of work flow email links 
							// response.sendError(HttpServletResponse.SC_FORBIDDEN, "You are not signed in with required username for this session or your session has timed out.");
							return;
						}
					} catch (Exception e) {
						log.error("Exception message = " + e.getMessage() + " exception = " + e, e);
						if (e.getCause() != null)
						{
							Throwable rootCause = e.getCause();
							while ( rootCause.getCause() != null ) rootCause = rootCause.getCause();
							log.error("Root Cause Message: " + rootCause.getMessage());
							log.error("Root Cause: ", rootCause);
						}
						
						if (!response.isCommitted())
						{
							request.getSession().removeAttribute("MESSAGEPOINT_SAVED_URL_REQUEST");
							String contextRoot = request.getContextPath();
							String msgKey = request.getParameter("msgkey");
							String errorKey = request.getParameter("errorkey");
							String queryStr = "";
							queryStr += (msgKey == null || msgKey.trim().isEmpty()) ? "" : "?msgkey=" + msgKey;
							queryStr += (errorKey == null || errorKey.trim().isEmpty()) ? "" : (queryStr.isEmpty() ? "?" : "&") + "errorkey=" + errorKey;
							String url = contextRoot + "/signin.jsp" + queryStr;
							response.sendRedirect(response.encodeRedirectURL(url));
						}
						return;
					}
				}
			}
		} else {
			filterChain.doFilter(request, response);
		}
	}
	
	private void handlWebshpereIssue(HttpServletResponse response, Throwable e) throws ServletException, IOException {
		//totally hack code here for a webshpere issue 6423
		Object jndiFactory = null;
		try {
			InitialContext context = new InitialContext();
			jndiFactory = context.getEnvironment().get(Context.INITIAL_CONTEXT_FACTORY);
			if(jndiFactory != null && jndiFactory.toString().indexOf("WsnInitialContextFactory") > -1) {
				log.debug("it's webshpere!");
				jndiFactory = null;
			} else {
				jndiFactory = null;
			}
		} catch (NamingException e1) {
			jndiFactory = null;
		}
		if(jndiFactory == null) {
			if(e instanceof ServletException)
				throw (ServletException)e;
			if(e instanceof IOException)
				throw (IOException)e;
		}
	}
	
	private boolean isExcludedURL(String path) {
		if(entryPoints.contains(path))
			return true;
		else {//need to check the *
			for (String pattern : wildEntryPoints) {
				if(wildcardMatch(pattern, path, "*"))
					return true;
			}
		}
		return false;
	}

	private boolean wildcardMatch(String pattern, String str, String wildcard) {
		if (StringUtils.isEmpty(pattern) || StringUtils.isEmpty(str)) {
			return false;
		}
		
		final boolean startWith = pattern.startsWith(wildcard);
		final boolean endWith = pattern.endsWith(wildcard);
		String[] array = StringUtils.split(pattern, wildcard);
		int currentIndex = -1;
		int lastIndex = -1;
		switch (array.length) {
		case 0:
			return true;
		case 1:
			currentIndex = str.indexOf(array[0]);
			if (startWith && endWith) {
				return currentIndex >= 0;
			}
			if (startWith) {
				return currentIndex + array[0].length() == str.length();
			}
			if (endWith) {
				return currentIndex == 0;
			}
			return str.equals(pattern);
		default:
			for (String part : array) {
				currentIndex = str.indexOf(part);
				if (currentIndex > lastIndex) {
					lastIndex = currentIndex;
					continue;
				}
				return false;
			}
			return true;
		}
	}

	protected static class CsrfResponseWrapper extends HttpServletResponseWrapper {
		private String tokenString;

		public CsrfResponseWrapper(HttpServletResponse response, String tokenString) {
			super(response);
			this.tokenString = tokenString;
		}

		@Override
		@Deprecated
		public String encodeRedirectUrl(String url) {
			if (url.indexOf(this.tokenString) > -1)
				url = StringUtils.remove(url, tokenString);
			return encodeRedirectURL(url);
		}

		@Override
		public String encodeRedirectURL(String url) {
			if (url.indexOf(this.tokenString) > -1)
				url = StringUtils.remove(url, tokenString);
			return super.encodeRedirectURL(url);
		}

		@Override
		@Deprecated
		public String encodeUrl(String url) {
			if (url.indexOf(this.tokenString) > -1)
				url = StringUtils.remove(url, tokenString);
			return encodeURL(url);
		}

		@Override
		public String encodeURL(String url) {
			if (url.indexOf(this.tokenString) > -1)
				url = StringUtils.remove(url, tokenString);
			return super.encodeURL(url);
		}
	}
}
