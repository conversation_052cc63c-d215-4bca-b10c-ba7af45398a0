package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.ContentTargeting;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.model.targeting.*;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncTargetingController implements Controller {

	public static final String REQ_PARAM_TARGET_GROUP_ID 				= "targetGroupId";
	public static final String REQ_PARAM_TYPE 							= "type";
	public static final String REQ_PARAM_OBJECT_DNA                     = "objectDna";
	public static final String REQ_PARAM_CONTENT_TARGETING_ID			= "contentTargetingId";
	public static final String REQ_PARAM_TAG_ID							= "tagId";
	public static final String REQ_PARAM_INSERT_ID						= "insertId";
	public static final String REQ_PARAM_ATTACHMENT_ID					= "attachmentId";
	public static final String REQ_PARAM_STATUS_FILTER					= "statusFilterId";
	
	public static final String REQ_PARAM_VALUE_TARGETABLE 	= "summary";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		String returnXML = "";
		if ( ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, null).equals(REQ_PARAM_VALUE_TARGETABLE) )
			returnXML = getTargetableSummary(request);
		else
			returnXML = getTargetGroupTemplateDef(request);

		out.write(returnXML.getBytes());
		out.flush();

		return null;
	}
	
	private String getTargetableSummary(HttpServletRequest request) {
		String objectDna				= ServletRequestUtils.getStringParameter(request, REQ_PARAM_OBJECT_DNA, "");
		long contentObjectId 			= ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, 0L);
		long contentTargetingId 		= ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_TARGETING_ID, 0L);
		long tagId 						= ServletRequestUtils.getLongParameter(request, REQ_PARAM_TAG_ID, 0L);
		long insertId 					= ServletRequestUtils.getLongParameter(request, REQ_PARAM_INSERT_ID, 0L);
		long attachmentId 				= ServletRequestUtils.getLongParameter(request, REQ_PARAM_ATTACHMENT_ID, 0L);
		int msgListStatusFilterId		= ServletRequestUtils.getIntParameter(request, REQ_PARAM_STATUS_FILTER, 0);
		
		ContentObject contentObject 					= null;
		Targetable object 								= null;
		ContentTargeting contentTargeting				= null;

		if (contentObjectId > 0 || !objectDna.isEmpty()) {
			contentObject = ContentObject.findByHttpServletRequest(request);
			object = contentObject;
		} else if (contentTargetingId > 0) {
			contentTargeting = ContentTargeting.findById(contentTargetingId);
			object = contentTargeting;
		} else if (tagId > 0) {
			object = Tag.findById(tagId);
		} else if (insertId > 0) {
			object = Insert.findById(insertId);
		} else if (attachmentId > 0) {
			object = Attachment.findById(attachmentId);
		}
		
		StringBuilder targetingHtml = new StringBuilder();
		
		targetingHtml.append( "<?xml version='1.0'?>" );

		if (object != null) {

			targetingHtml.append( "<content><![CDATA[" );

			if (object instanceof ContentObject) {
				ContentObjectData contentObjectData = null;

				if (msgListStatusFilterId == ContentObject.DATA_TYPE_WORKING || msgListStatusFilterId == ContentObject.DATA_TYPE_ACTIVE || msgListStatusFilterId == ContentObject.DATA_TYPE_ARCHIVED) {
					contentObjectData = ((ContentObject)object).getContentObjectData(msgListStatusFilterId);
				} else {
					contentObjectData = ((ContentObject)object).getContentObjectData();
				}

				targetingHtml.append(TargetingSummaryUtil.generateSummaryHtml(contentObjectData, contentTargeting));
			}
			else
			{
				targetingHtml.append(TargetingSummaryUtil.generateSummaryHtml(object, contentTargeting));
			}
			
			targetingHtml.append( "]]></content>" );


		} else {
			
			targetingHtml.append( "<error>Invalid Targetable Object ID</error>" );
			
		}
		
		return targetingHtml.toString();
	}
	
	private String getTargetGroupTemplateDef(HttpServletRequest request) {
		String returnXML = "<?xml version='1.0'?>";

		TargetGroup targetGroup = TargetGroup.findById(getTargetGroupId(request));

		if (targetGroup == null) {
			returnXML += "<error>Invalid Target Group ID</error>";
		} else {
			TargetGroupWrapper targetGroupWrapper = new TargetGroupWrapper(targetGroup.getId());
			StringBuilder content = new StringBuilder();
			content.append("<div class=\"targetGroupDefinition\" title=\"").append(targetGroup.getName()).append("\" targetGroupId=\"").append(targetGroup.getId()).append("\">").append("<input name=\"targetGroupsMap[MAP_INDEX][TARGET_GROUP_INDEX].targetGroupId\" class=\"targetGroupIdInput\" style=\"display:none;\" value=\"").append(targetGroup.getId()).append("\" />").append("<input name=\"targetGroupsMap[MAP_INDEX][TARGET_GROUP_INDEX].instanceId\" class=\"instanceIdInput\" style=\"display:none;\" value=\"").append(targetGroup.getInstance().getId()).append("\" />");

			for (ConditionItem conditionItem : targetGroup.getInstance().getConditionItemsSorted()) { // RULES
				ConditionElement element = conditionItem.getConditionElement();
				content.append("<div class=\"ruleDefinition\" title=\"").append(element.getName()).append("\" ruleType=\"").append(element.getDisplayType()).append("\">");

				for (ConditionSubelement subElement: element.getSubElements()) { // CONDITIONS
					if (targetGroupWrapper.getConditionSubelementMap().get(element.getId()).contains(subElement.getId())) {
						String conditionType = targetGroupWrapper.getConditionSubelementValueTypes().get(subElement.getId());
						content.append("<div class=\"conditionDefinition\" title=\"").append(subElement.getName()).append("\" operator=\"").append(subElement.getConditionRelationship()).append("\">");
						
						if (subElement.isParameterized()) {
							String value = targetGroup.isParameterized() ? 
												"" : 
												StringEscapeUtils.escapeXml( targetGroupWrapper.getConditionSubelementValues().get(subElement.getId()) );
							
							content.append("<div class=\"parameterDefinition\" conditionType=\"").append(conditionType).append("\" isTextDisplay=\"").append(!targetGroup.isParameterized()).append("\" id=\"TABLE_INDICATOR_").append(targetGroup.getId()).append("_").append(element.getId()).append("_").append(subElement.getId()).append("\">").append("<input class=\"parameterValueBinding\" id=\"conditionValueString_TABLE_INDICATOR_").append(targetGroup.getId()).append("_").append(element.getId()).append("_").append(subElement.getId()).append("\" style=\"display: none;\" ").append("name=\"targetGroupsMap[MAP_INDEX][TARGET_GROUP_INDEX].conditionSubelementValues[").append(subElement.getId()).append("]\"").append("value=\"").append(value).append("\" />").append("</div>");
						}

						content.append(	"</div>" );
					}
				}
				
				content.append(		"</div>" );
			}

			content.append(		"</div>");
			returnXML += "<content><![CDATA[" + content + "]]></content>";
		}
		
		return returnXML;
	}

	private long getTargetGroupId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_TARGET_GROUP_ID, -1);
	}

}