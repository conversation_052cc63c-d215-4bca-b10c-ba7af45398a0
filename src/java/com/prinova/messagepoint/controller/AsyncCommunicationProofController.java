package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.communication.CreateCommunicationProofService;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public class AsyncCommunicationProofController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncCommunicationProofController.class);

	public static final String PARAM_PROOF_ID			= "proofId";
	public static final String PARAM_COMMUNICATION_ID	= "communicationId";
	public static final String PARAM_CUSTOMER_ID		= "recip_id";
	public static final String PARAM_REINIT_PROOF		= "rerun_on_error";
	public static final String PARAM_SOURCE				= "source";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getCommunicationProofResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for communication proof data: " + e);
		}

		return null;
	}

	private String getCommunicationProofResponseJSON (HttpServletRequest request) {

		long proofId 			= ServletRequestUtils.getLongParameter(request, PARAM_PROOF_ID, -1);
		String customerId		= ServletRequestUtils.getStringParameter(request, PARAM_CUSTOMER_ID, null);
		boolean reInitProof		= ServletRequestUtils.getBooleanParameter(request, PARAM_REINIT_PROOF, false);
		long communicationId 	= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
		String source			= ServletRequestUtils.getStringParameter(request, PARAM_SOURCE, "");
	
		JSONObject returnObj = new JSONObject();
		
		try {
			
			Document contextDocument = UserUtil.getCurrentTouchpointContext();
			Communication communication = null;
			if ( communicationId > 0 )
				communication = Communication.findById(communicationId);
			
			if ( communication != null && communication.isDebugOrder() ) {
				
				// DEBUG ORDER
				try {
					returnObj.put("status" 				, "complete");
					returnObj.put("output_path" 		, "DEBUG");
					returnObj.put("resource" 			, HttpRequestUtil.buildResourceToken().add("file", "DEBUG").getValue());
					returnObj.put("item_class" 			, CommunicationProof.class.getName());			

					returnObj.put("item_id"				, -999);
					returnObj.put("is_pre_proof"		, true);
					returnObj.put("generated_date"		, DateUtil.formatDate(new Date()));

					returnObj.put("pre_proof_id"		, -999 );
					returnObj.put("job_id"				, -999 );
					returnObj.put("is_debug"			, true );
					returnObj.put("communication_id"	, communication.getId() );
		
					boolean isDigitalTouchpoint = communication.getIsEmailContentDelivery() || communication.getIsWebDelivery();
					returnObj.put("is_print_touchpoint"	, !isDigitalTouchpoint);
					if ( isDigitalTouchpoint ) {
						returnObj.put("template_width"	, communication.getDocument().getEmailSection().getWidth());	
						returnObj.put("template_height"	, communication.getDocument().getEmailSection().getHeight());	
					}
				} catch (JSONException e) {
					log.error("Error: Unable to retrieve connected proof attributes JSON: " + e );
				}
				
			} else if ( proofId > 0 ) {
				
				CommunicationProof proof = CommunicationProof.findById(proofId);
				// RE-INIT: Previously here (deprecated)
				returnObj = setProofReturnAttr(proof, returnObj, customerId != null ? StringEscapeUtils.unescapeHtml(customerId) : null);
			} else if ( customerId != null ) {

				if ( customerId != null )
					customerId = StringEscapeUtils.unescapeHtml(customerId);

				Long proofItemId = null;

				List<CommunicationProof> preProofs = CommunicationProof.findPreProofsByCommunicationId(communicationId);
				if (preProofs.isEmpty() || (reInitProof && preProofs.get(0).isError() && preProofs.get(0).getIsPreProof() ) ||
						(reInitProof && !preProofs.get(0).isError() && preProofs.get(0).getOutputPath() == null && !preProofs.get(0).getDocument().isCommunicationWebServiceCompositionResultsEnabled() && preProofs.get(0).getIsPreProof() ) ||
						preProofs.get(0).getDocument().getId() != contextDocument.getId() ) {
					ServiceExecutionContext context = CreateCommunicationProofService.createContextForPreProof(customerId, communication, UserUtil.getCurrentTouchpointContext(), UserUtil.getPrincipalUser(),"Async: Identifier: Re-init");
					Service service = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
					service.execute(context);
					if ( context.getResponse().isSuccessful() )
						proofItemId = (Long)context.getResponse().getResultValueBean();
					else
						log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
				} else {
					proofItemId = preProofs.get(0).getId();
				}

				CommunicationProof proof = CommunicationProof.findById(proofItemId);

				returnObj = setProofReturnAttr(proof, returnObj, customerId);
			} else {
				
				returnObj.put("error", "Invalid communication proof");
				
			}

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve connected proof status JSON: " + e );
		}

		return returnObj.toString();
	}
	
	private JSONObject setProofReturnAttr(CommunicationProof proof, JSONObject returnObj, String customerId) {
		
		try {
			
			if ( proof == null || proof.getDocument() == null ) {
				
				returnObj.put("status" 				, "error");
				returnObj.put("delivery_event_id" 	, -1);
				returnObj.put("item_class" 			, "CommunicationProof");
				
			} else {
		
				boolean remoteProof = proof.getDocument().isCommunicationWebServiceCompositionResultsEnabled();
				if ( !proof.isComplete() && !proof.isError() && (remoteProof || (!remoteProof && proof.getOutputPath() == null)) ) {
					returnObj.put("status" , "in_process");
				} else if ( proof.isNoMatchingRecipientError() ) {
					returnObj.put("status" 				, "no_matching_recipient_error");
					returnObj.put("delivery_event_id" 	, proof.getDeliveryEvent() != null ? proof.getDeliveryEvent().getId() : -1);
					returnObj.put("item_class" 			, proof.getClass().getName());
				} else if ( proof.isNoProductionContentError() ) {
					returnObj.put("status" 				, "no_production_content_error");
					returnObj.put("delivery_event_id" 	, proof.getDeliveryEvent() != null ? proof.getDeliveryEvent().getId() : -1);
					returnObj.put("item_class" 			, proof.getClass().getName());
				} else if ( !proof.isError() && proof.getOutputPath() == null && !remoteProof && !proof.getIsEmailOrWebPreview() ) {
					returnObj.put("status" 				, "log");
					returnObj.put("delivery_event_id" 	, proof.getDeliveryEvent() != null ? proof.getDeliveryEvent().getId() : -1);
					returnObj.put("item_class" 			, proof.getClass().getName());
				} else if ( !proof.isError() ) {
					returnObj.put("status" 				, "complete");
					if ( remoteProof ) {
						returnObj.put("is_remote_result", true);
					}
					else {
						returnObj.put("output_path", proof.getOutputPath());
						returnObj.put("resource", HttpRequestUtil.buildResourceToken().add("file", proof.getOutputPath()).getValue());
					}
					returnObj.put("item_class" 			, proof.getClass().getName());			
				} else if ( proof.isError() ) {
					Communication communication = proof.getCommunication();
					if (communication != null && communication.hasProofErrors()) {
						returnObj.put("status" 				, "proof_error");
						returnObj.put("communication_id"	, communication.getId() );
					} else {
						returnObj.put("status" 				, "error");
						returnObj.put("delivery_event_id" 	, proof.getDeliveryEvent() != null ? proof.getDeliveryEvent().getId() : -1);
					}
					returnObj.put("item_class" 			, proof.getClass().getName());
				}
				
				returnObj.put("item_id"				, proof.getId());
				returnObj.put("is_pre_proof"		, proof.getIsPreProof());
				returnObj.put("generated_date"		, DateUtil.formatDate(proof.getRequestDate()));
				
				List<CommunicationProof> preProofs = CommunicationProof.findPreProofsByCommunicationId(proof.getCommunication().getId());
				returnObj.put("pre_proof_id"		, !preProofs.isEmpty() ? preProofs.get(0).getId() : -1 );
				returnObj.put("job_id"				, proof.getDeliveryEvent() != null && proof.getDeliveryEvent().getJob() != null ? proof.getDeliveryEvent().getJob().getId() : -1);
				returnObj.put("communication_id"	, (proof.getCommunication() != null ? proof.getCommunication().getId() : -1L));
				
				boolean isDigitalTouchpoint = proof.getIsEmailOrWebPreview();
				returnObj.put("is_print_touchpoint"	, !isDigitalTouchpoint);
				if ( isDigitalTouchpoint ) {
					returnObj.put("template_width"	, proof.getEmailTemplateWidth());	
					returnObj.put("template_height"	, proof.getEmailTemplateHeight());	
				}
			}
		
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve connected proof attributes JSON: " + e );
		}
		
		return returnObj;
	}
}