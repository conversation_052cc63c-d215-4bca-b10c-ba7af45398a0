package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.insert.ExportInsertSchedulesToCSVService;
import com.prinova.messagepoint.platform.services.insert.ExportInsertSchedulesToCSVServiceResponse;

public class AsyncInsertScheduleExportController implements Controller {
	
	public static final String REQ_PARAM_INSERT_SCHED_IDS = "insertSchedIds";
	public static final String REQ_PARAM_INCLUDE_HEADERS = "includeHeaders";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		ServiceExecutionContext context = ExportInsertSchedulesToCSVService.createContext(getInsertSchedIds(request), getIncludeHeadersFlag(request));
		Service exportInsertSchedulesToCSVService = MessagepointServiceFactory.getInstance().lookupService(ExportInsertSchedulesToCSVService.SERVICE_NAME, ExportInsertSchedulesToCSVService.class);
		exportInsertSchedulesToCSVService.execute(context);
		ExportInsertSchedulesToCSVServiceResponse serviceResponse = (ExportInsertSchedulesToCSVServiceResponse)context.getResponse();
		
		String returnXML = "<?xml version='1.0'?>";
		if(!serviceResponse.isSuccessful()){
			returnXML += "<content><error>Export Failed</error></content>";
		} else {
			returnXML += "<content><file>" + serviceResponse.getFilePath() + "</file></content>";
		}

		out.write(returnXML.getBytes());
		out.flush();
		
		return null;
	}
	
	private String getInsertSchedIds(HttpServletRequest request){
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_INSERT_SCHED_IDS, null);
	}
	private Boolean getIncludeHeadersFlag(HttpServletRequest request){
		return ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_INCLUDE_HEADERS, false);
	}

}
