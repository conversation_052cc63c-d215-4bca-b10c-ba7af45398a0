package com.prinova.messagepoint.controller;

import java.io.PrintWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.proxylogin.PodRole;
import com.prinova.messagepoint.model.navigation.DropDownMenu;
import com.prinova.messagepoint.model.navigation.DropDownMenuItem;
import com.prinova.messagepoint.model.navigation.NavigationTab;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncCategoryMenuItemsController implements Controller {
	
	private static final Log log = LogUtil.getLog(AsyncCategoryMenuItemsController.class);

	public static final String REQ_PARAM_CATEGORY_ID 	= "categoryId";
	public static final String PARAM_VIEWID 			= "viewid";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		
		try {
			PrintWriter out = response.getWriter();
			
			JSONArray menus = new JSONArray();
			long tabId = getCategoryId(request);
			NavigationTab tab = NavigationTab.findById(tabId);
	
			if ( (tab != null) && (tab.getMenus() != null) ) {
				for( DropDownMenu dropDownMenu : tab.getMenus() ) {
					boolean hasRequired = dropDownMenu.checkRequiredParameter( request );
					if( dropDownMenu.isAuthorized() && hasRequired && dropDownMenu.hasChildren( request ) ) {
						JSONObject menu = new JSONObject();
						menu.put("id",dropDownMenu.getId());
						menu.put("name", ApplicationUtil.getMessage(dropDownMenu.getName()));
	
						JSONArray menuItems = new JSONArray();
						for( DropDownMenuItem item : dropDownMenu.getItems() ) {
							// Hide Unified Login menu item if this feature is disabled
							if (item.getId() == DropDownMenuItem.MASTER_ADMIN_UNIFIED_LOGIN_MENU_ITEM_ID) {
								SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
								SystemPropertyManager spm = SystemPropertyManager.getInstance();
						        Integer proxySSOPodRole = spm.getIntegerSystemProperty(SystemPropertyKeys.ProxySSO.KEY_ProxySSOPodRole, PodRole.POD_ROLE_SLAVE);
						        HibernateUtil.getManager().restoreSession(mainSessionHolder);
								if (proxySSOPodRole != PodRole.POD_ROLE_MASTER)
									continue;
								
								// It seems that this "if" is not needed in case that there is no propagation of authorization to sub domains from dcs 
								if (!UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_ADMIN)) { 
									if (UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_USER) && Node.getCurrentBranch().getDcsSchemaName() != null) {
										continue;
									}
								}
							}
							
							// Hide Licences 
							if (item.getId() == DropDownMenuItem.MASTER_ADMIN_LICENCES_MENU_ITEM_ID) {
								if (!UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_ADMIN)) { 
									if (UserUtil.isPermissionGranted(Permission.ID_ROLE_ADMIN_MASTER_USER) && Node.getCurrentBranch().getDcsSchemaName() != null) {
										continue;
									}
								}
							}
							
							boolean hasRequiredItem = item.checkRequiredParameter( request );
							if( item.isAuthorized() && hasRequiredItem ) {	
								JSONObject menuItem = new JSONObject();
								if( DropDownMenuItem.TYPE_ID_SEPARATOR.equals(item.getIcon()) ) {
									menuItem.put("type","SEPARATOR");
								} else {
									String link = "";
									if ((item.getUrl()).contains("javascript:")) {
										link = item.getUrl();
									} else {
										link = addParameter( ApplicationUtil.getWebRoot() + item.getUrl(), item.getQueryString(request) );
										if( item.getTree() != null ) link = addParameter( link, PARAM_VIEWID + "=" + item.getId() );
										link = "javascript:javascriptHref('"+link+"')";
									}
									menuItem.put("type", "MENU_ITEM");
									menuItem.put("name", ApplicationUtil.getMessage(item.getName()));
									menuItem.put("link", link);
								}
								menuItems.put(menuItem);
							}
						}
						menu.put("menuItems",menuItems);
						menus.put(menu);
					}
				}
			}
	
			out.write(menus.toString());
			out.flush();
		} catch (Exception e) {
			log.error("Error: Generating category menu: " + e.getMessage() );
		}

		return null;
	}
	
	private String addParameter( String url, String parameter ) {
		if( url == null ) return null;
		if( parameter == null || parameter.isEmpty()) return url;
		
		if( url.indexOf('?') != -1  ) return url + '&' + parameter;
		return url + '?' + parameter;
	}

	private long getCategoryId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_CATEGORY_ID, -1);
	}

}