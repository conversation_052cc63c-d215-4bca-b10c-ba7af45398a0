package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;

import com.prinova.messagepoint.util.LogUtil;

public class CacheResponseFiter implements Filter {
	private String cacheAge = null;
	
	private static final Log log = LogUtil.getLog( CacheResponseFiter.class );
	private static final SimpleDateFormat df = httpResponseHeaderDateFormatter();
	private static final String CACHE_EXPIRES = "cacheExpires";
	
	public void init(FilterConfig filterConfig) throws ServletException {
		cacheAge = "max-age=" + filterConfig.getInitParameter("cache-for");
	}

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest req = (HttpServletRequest)request;
		
		if( log.isDebugEnabled() ){
			log.debug( req.getRequestURI() );
		}

		HttpServletResponse r = (HttpServletResponse)response;
		String cacheExpires = getCacheExpire(req);
		chain.doFilter(request, response);
		
		//write out the header
		r.setHeader("Cache-Control",  cacheAge);
		r.setHeader("expires"      ,  cacheExpires);
		
	}
	
	
	public void destroy() {}

	
	private static SimpleDateFormat httpResponseHeaderDateFormatter(){
		SimpleDateFormat df = new SimpleDateFormat("EEE, d MMM yyyy kk:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("GMT"));
		
		return df;
	}
	
	private String getCacheExpire(HttpServletRequest request){
		
		String cacheExpires = (String) request.getSession().getAttribute(CACHE_EXPIRES);
		
		if( cacheExpires == null ){
			Calendar c = GregorianCalendar.getInstance();
			c.setTime(new Date());
			
			c.set(Calendar.HOUR_OF_DAY, 23);
			c.set(Calendar.MINUTE, 59);
			c.set(Calendar.SECOND, 59);
			c.set(Calendar.MILLISECOND, 00);
			
			cacheExpires = df.format(c.getTime());
			request.getSession().setAttribute(CACHE_EXPIRES, cacheExpires);
		}
		
		return cacheExpires;
		
	}
	
	
	
}

