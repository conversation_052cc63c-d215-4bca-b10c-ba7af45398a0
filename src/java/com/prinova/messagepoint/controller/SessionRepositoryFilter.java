package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagepointSpringProfileManager;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.PropertyUtils;

import javax.servlet.*;
import java.io.IOException;
import java.util.Properties;

public class SessionRepositoryFilter extends org.springframework.web.filter.DelegatingFilterProxy implements Filter {

    private static Boolean hasRedisSessionConfiguration;

    public SessionRepositoryFilter() {
        super();
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        if (getHasRedisSessionConfiguration()) {
            super.doFilter(request, response, filterChain);
        } else {
            filterChain.doFilter(request, response);
        }

    }

    @Override
    protected void initFilterBean() throws ServletException {
        if (getHasRedisSessionConfiguration()) {
            super.initFilterBean();
        }
    }

    private Boolean getHasRedisSessionConfiguration() {

        if (hasRedisSessionConfiguration == null) {

            try {
                Properties props = PropertyUtils.loadApplicationContextCommonProperties();

                hasRedisSessionConfiguration = props.getProperty("session.messagepoint.redis.host") != null;

                if (hasRedisSessionConfiguration) {
                    MessagepointSpringProfileManager messagepointSpringProfileManager = (MessagepointSpringProfileManager)ApplicationUtil.getBean("mpSpringProfileManager");
                    hasRedisSessionConfiguration = messagepointSpringProfileManager.isSpringSessionProfileActive();
                }
            } catch (Exception e) {
                LogUtil.getLog(SessionRepositoryFilter.class).error(e);
                hasRedisSessionConfiguration = false;
            }

        }

        return hasRedisSessionConfiguration;
    }
}
