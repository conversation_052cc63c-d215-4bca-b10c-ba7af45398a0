package com.prinova.messagepoint.controller;

import java.util.Comparator;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient.SftpRepoFolder;
import com.prinova.messagepoint.platform.sftprepo.SftpRepoClient.SftpRepoImage;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncSftpRepoDataController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncSftpRepoDataController.class);

	public static final String REQ_PARM_TYPE 			= "type";
	public static final String REQ_PARM_FOLDER			= "folder";
	
	public static final String TYPE_FOLDERS_LIST		= "folders_list";
	public static final String TYPE_IMAGES				= "images";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();
		
		String type = ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, null);

		try {
			if ( type.equalsIgnoreCase(TYPE_FOLDERS_LIST) )
				out.write(getFoldersListResponseJSON(request,response).getBytes());
			else if ( type.equalsIgnoreCase(TYPE_IMAGES) )
				out.write(getImageListResponseJSON(request,response).getBytes());
			else
				out.write( getErrorJSON("Invalid service request parameter.").getBytes() );
		} catch (Exception e) {
			out.write( getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.request.fail")).getBytes() );
			log.error("Error - Unable to retrieve SFTP Repository data: " + e.getMessage(), e);
		}
		
		out.flush();

		return null;
	}

	@SuppressWarnings("unused")
	private String getFoldersListResponseJSON (HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		String folder = ServletRequestUtils.getStringParameter(request, REQ_PARM_FOLDER, null);
		
		if ( folder == null || folder.equals("undefined") )
			return getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.invalid.folder"));
		
		JSONObject returnObj = new JSONObject();
		
		SftpRepoClient sftp = new SftpRepoClient();
		if ( !sftp.isValid() )
			return getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.invalid.settings"));
		sftp.setFolder(folder);
		List<SftpRepoFolder> folders = sftp.listFolder();
		JSONArray foldersListJSON = new JSONArray();
		if ( folders != null ) {
			folders.sort(new Comparator<>() {
                @Override
                public int compare(SftpRepoFolder o1, SftpRepoFolder o2) {
                    return o1.getName().compareTo(o2.getName());
                }
            });
			
			for (SftpRepoFolder f : folders) {
				JSONObject folderObj = new JSONObject();
				folderObj.put("id", f.getId());
				folderObj.put("name", f.getName());
				foldersListJSON.put(folderObj);
			}
		} else {
			return getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.listing.folder"));
		}
		
		returnObj.put("folders", foldersListJSON);

		return returnObj.toString();
	}
	
	private String getImageListResponseJSON (HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		String folder = ServletRequestUtils.getStringParameter(request, REQ_PARM_FOLDER, null);
		
		if ( folder == null )
			return getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.invalid.folder"));
		
		JSONObject returnObj = new JSONObject();
		JSONArray imageListJSON = new JSONArray();
		
		SftpRepoClient sftp = new SftpRepoClient();
		if ( !sftp.isValid() )
			return getErrorJSON(ApplicationUtil.getMessage("sftprepo.async.service.error.invalid.settings"));
		sftp.setFolder(folder);
		folder = sftp.getFolder();
		List<SftpRepoImage> files = sftp.listImages(true);

		if( files != null ) {
			int count = 0;
			for (SftpRepoImage f : files) {
				count++;
				JSONObject imageObj = new JSONObject();
				
				imageObj.put("assetId"			, f.getId());
				imageObj.put("name"				, f.getName());
				imageObj.put("exists_in_system"	, f.getAlreadyUploaded());
				String imgURL = ApplicationUtil.getHTMLImageURL(f.getFilePath(), BinaryStreamController.TYPE_SFTPREPO_FILE, false);
				imageObj.put("url"				, imgURL);
				imageObj.put("filename"			, f.getFilename());
							
				imageListJSON.put(imageObj);
				if ( count > 30 ) {
					JSONObject no_preview = new JSONObject();
					no_preview.put("filename", ApplicationUtil.getMessage("sftprepo.async.service.error.too.much.image"));
					no_preview.put("exists_in_system", true);
					imageListJSON.put(no_preview);
					break;
				}
			}
		}
		
		returnObj.put("images", imageListJSON);
		returnObj.put("folder", folder);

		return returnObj.toString();
	}
	
	private String getErrorJSON (String message) {
		JSONObject returnObj = new JSONObject();
		
		try {
			returnObj.put("error", true);
			returnObj.put("message", message);
		} catch (JSONException e) {
			log.error("Error - Unable to retrieve SFTP Repository data: " + e.getMessage(), e);
		}
		
		return returnObj.toString();
	}
	
}