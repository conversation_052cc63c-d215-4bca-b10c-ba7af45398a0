package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.RedisUtil;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.util.DateUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.GZIPOutputStream;

public class WebAssetPackageController implements Controller {

    public final static String ASSET_ID_PARAM = "asset";
    public final static String CONTENT_TYPE_PARAM = "content";
    public final static String JSPROFILE_MODERN = "jsprofile-modern";
    public final static String JSPROFILE_NOMODULE = "jsprofile-nomodule";
    public final static String REAL_USER_METRICS_LEGACY = "real-user-metrics-legacy";
    public final static String REAL_USER_METRICS_MODERN = "real-user-metrics-modern";
    public final static String SCRIPT_PACKAGE_CSRFGUARD = "csrf-guard-package";
    public static final String FEATURE_FLAG = "feature-flag";
    public static final String SCRIPT_PACKAGE_PINCCONFIG = "pinc-config";

    private static HashMap<String, String> dynamicContentLookup = null;
    private static ConcurrentHashMap<String, WebAssetPackage> packageAssets = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, byte[]> assetPackages = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, String> packageNameToIdMap = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, String> packageIdToNameMap = new ConcurrentHashMap<>();

    public static void init() {
        try {
            WebAssetServiceUtil.init();
        } catch (Exception e) {
            LogUtil.getLog(WebAssetPackageController.class).error("Error (init)", e);
        }
    }

    @Override
    public ModelAndView handleRequest(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response) throws Exception {

        String assetId = ServletRequestUtils.getStringParameter(request, ASSET_ID_PARAM, StringUtils.EMPTY);
        String contentTypeValue = ServletRequestUtils.getStringParameter(request, CONTENT_TYPE_PARAM, StringUtils.EMPTY);
        String ifNoneMatch = request.getHeader("If-None-Match");

        if (request.getParameterMap().containsKey("cache_debug")) {
            byte[] result = getCacheDebugResponse().getBytes();
            response.setContentLength(result.length);
            response.getOutputStream().write(result);
            return null;
        }

        WebAssetContentType contentType = WebAssetContentType.resolveContentTypeByValue(contentTypeValue);

        if (ifNoneMatch != null && hasAssetPackage(assetId)) {
            response.setStatus(HttpStatus.SC_NOT_MODIFIED);
            return null;
        }

        if (!assetId.isEmpty()) {
            byte[] result = getAssetPackage(assetId, request.getServletContext());

            if (result != null && result.length != 0) {
                response.setStatus(200);
                response.setContentType(contentType.getHtmlContentType());
                response.setCharacterEncoding("utf-8");

                String headerAcceptEncoding = request.getHeader("accept-encoding");

                response.setHeader("Cache-Control", "public");
                response.setHeader("Expires", DateUtil.formatDate(getCacheExpiry(), DateUtil.PATTERN_RFC1123));
                response.setHeader("ETag", assetId);

                if (headerAcceptEncoding != null && headerAcceptEncoding.contains("gzip")) {
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    GZIPOutputStream zipStream = new GZIPOutputStream(byteArrayOutputStream);
                    zipStream.write(result);
                    zipStream.finish();
                    zipStream.flush();
                    response.setHeader("Content-Encoding", "gzip");
                    byte[] gzipBytes = byteArrayOutputStream.toByteArray();
                    response.setContentLength(gzipBytes.length);
                    response.getOutputStream().write(gzipBytes);
                } else {
                    response.setContentLength(result.length);
                    response.getOutputStream().write(result);
                }

                CompletableFuture.runAsync(() ->
                        WebAssetServiceUtil.uploadPrimaryAssetToCdn(assetId, contentType, result)
                );
            } else {
                response.setStatus(HttpStatus.SC_NOT_FOUND);
                return null;
            }
        } else {
            response.setStatus(HttpStatus.SC_BAD_REQUEST);
            return null;
        }

        return null;
    }

    private String getCacheDebugResponse() {
        ArrayList<JSONObject> cachePackages = new ArrayList<>();

        for (String key : assetPackages.keySet()) {
            JSONObject value = new JSONObject();

            String packageName = packageIdToNameMap.get(key);

            value.put("key", key);
            value.put("digest", DigestUtils.sha256Hex(assetPackages.get(key)));
            value.put("name", packageName);
            
            if (packageAssets.containsKey(packageName)) {
                value.put("files", packageAssets.get(packageName).getReferencePaths());
            } else {
                value.put("files", "empty");
            }

            cachePackages.add(value);
        }

        JSONObject result = new JSONObject();

        result.put("named-packages", new JSONArray());
        result.put("default-packages", new JSONArray());

        for (JSONObject value : cachePackages) {

            if (value.get("name").equals(getReferencesHash(packageAssets.get(value.getString("name")).getReferencePaths()))) {
                result.getJSONArray("default-packages").put(value);
            } else {
                result.getJSONArray("named-packages").put(value);
            }
        }

        return result.toString(1);
    }

    private Date getCacheExpiry() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.YEAR, 1);
        return cal.getTime();
    }

    public static String getPackageId(String packageName, ServletContext context) {
        String id;

        if (packageNameToIdMap.containsKey(packageName)) {
            id = packageNameToIdMap.get(packageName);
        } else {
            id = createAssetPackage(packageName, context);
            packageNameToIdMap.put(packageName, id);
            packageIdToNameMap.put(id, packageName);


            if (RedisUtil.hasRedisConfiguration()) {
                String cacheKey = getRedisAssetPackageKey(id);
                RedisUtil.getRedisContext().putValue(cacheKey, assetPackages.get(id));
            }

        }

        return id;
    }

    private static String getRedisAssetPackageKey(String id) {
        return MessageFormat.format("{0}:assetPackage:{1}", WebAssetPackageController.class.getName(), id);
    }

    public static void addPackageReference(String packageName, String path) {
        WebAssetPackage assetPackage = packageAssets.computeIfAbsent(packageName, k -> new WebAssetPackage());
        assetPackage.addReference(path);
    }

    public static String createAssetPackage(List<String> references) {
        String packageId = null;

        try {
            packageId = getReferencesHash(references);

            for (String reference : references) {
                addPackageReference(packageId, reference);
            }
        } catch (Exception ex) {
            LogUtil.getLog(WebAssetPackageController.class).error("Error (createAssetPackage)", ex);
        }

        return packageId;
    }

    private static String getReferencesHash(List<String> references) {
        StringBuilder refString = new StringBuilder();

        for (String reference : references) {
            refString.append(reference);
        }

        return DigestUtils.sha256Hex(refString.toString());
    }

    private boolean hasAssetPackage(String id) {
        return assetPackages.containsKey(id);
    }

    private byte[] getAssetPackage(String id, ServletContext context) {
        if (!assetPackages.containsKey(id)) {
            if (packageIdToNameMap.containsKey(id)) {
                String packageName = packageIdToNameMap.get(id);
                createAssetPackage(packageName, context);
            } else {
                if (RedisUtil.hasRedisConfiguration()) {
                    byte[] assetPackage = RedisUtil.getRedisContext().getBytesValue(getRedisAssetPackageKey(id));
                    assetPackages.put(id, assetPackage);
                }
            }
        }

        return assetPackages.get(id);
    }

    private static String createAssetPackage(String packageName, ServletContext context) {

        String packageId = null;
        ByteArrayOutputStream packageContent = new ByteArrayOutputStream();
        HashMap<String, String> filePaths = getDynamicScriptLookup(context);

        try {

            for (String referencePath : packageAssets.get(packageName).referencePaths) {
                InputStream fileStream;

                if (filePaths.containsKey(referencePath)) {
                    fileStream = context.getResourceAsStream(MessageFormat.format("/dynamiccontent/pkg{0}", filePaths.get(referencePath)));
                } else {

                    String webRoot = ApplicationUtil.getWebRoot();

                    if (referencePath.startsWith(webRoot)) {
                        referencePath = referencePath.substring(webRoot.length());
                    }

                    fileStream = context.getResourceAsStream(MessageFormat.format("/{0}", referencePath));
                }

                if (fileStream != null) {

                    byte[] scriptContent = IOUtils.toByteArray(fileStream);

                    if (referencePath.endsWith("js")) {

                        packageContent.write(";".getBytes(StandardCharsets.UTF_8));
                    }

                    packageContent.write(scriptContent);
                    packageContent.write(System.lineSeparator().getBytes(StandardCharsets.UTF_8));

                } else {
                    LogUtil.getLog(WebAssetPackageController.class).error("Unable to load file: " + referencePath);
                }
            }

            packageId = DigestUtils.sha256Hex(packageContent.toString());
            assetPackages.put(packageId, packageContent.toByteArray());
        } catch (Exception e) {
            LogUtil.getLog(WebAssetPackageController.class).error("Error (createAssetPackage)", e);
        }

        return packageId;
    }

    public static HashMap<String, String> getDynamicScriptLookup(ServletContext context) {

        try {
            if (dynamicContentLookup == null) {

                HashMap<String, String> result = new HashMap<>();

                JSONArray values;

                InputStream stream = context.getResourceAsStream("/dynamiccontent/pkg/scriptAssetMap.json");

                if (stream != null) {
                    String fileContent = IOUtils.toString(stream, StandardCharsets.UTF_8);
                    values = new JSONArray(fileContent);
                } else {
                    values = new JSONArray();
                }

                for (int x = 0; x < values.length(); x++) {
                    JSONObject value = values.getJSONObject(x);
                    result.put(value.getString("path").substring(1), value.getString("dynamicPath"));
                }

                stream = context.getResourceAsStream("/dynamiccontent/pkg/cssAssetMap.json");

                if (stream != null) {
                    String fileContent = IOUtils.toString(stream, StandardCharsets.UTF_8);
                    values = new JSONArray(fileContent);
                } else {
                    values = new JSONArray();
                }


                for (int x = 0; x < values.length(); x++) {
                    JSONObject value = values.getJSONObject(x);
                    result.put(value.getString("path").substring(1), value.getString("dynamicPath"));
                }


                dynamicContentLookup = result;
            }
        } catch (Exception e) {
            LogUtil.getLog(WebAssetPackageController.class).error("Error (getDynamicScriptLookup)", e);
        }

        return dynamicContentLookup;
    }

    private static class WebAssetPackage {

        private ArrayList<String> referencePaths = new ArrayList<>();
        private HashSet<String> referenceSet = new HashSet<>();

        private void addReference(String path) {
            synchronized (this) {
                if (!this.referenceSet.contains(path)) {
                    this.referencePaths.add(path);
                    this.referenceSet.add(path);
                }
            }
        }

        public ArrayList<String> getReferencePaths() {
            return referencePaths;
        }
    }

    /*
    private static class WebAssetSourceMapParser {

        static boolean hasSourceMap(String script) {
            String[] lines = script.split("\\r?\\n");

            for (String line : lines) {
                if (line.toLowerCase().startsWith("//# sourcemappingurl")) {
                    return true;
                }
            }

            return false;
        }

        static String getScriptWithoutSourceMap(String script) {
            StringBuilder result = new StringBuilder();
            String[] lines = script.split("\\r?\\n");

            for (String line : lines) {
                if (!line.toLowerCase().startsWith("//# sourcemappingurl")) {
                    result.append(line).append(System.lineSeparator());
                }
            }

            return result.toString();
        }

        static JSONObject getDecodedInlineSourceMap(String script) {
            String[] lines = script.split("\\r?\\n");
            JSONObject result = null;

            try {
                for (String line : lines) {
                    if (line.toLowerCase().startsWith("//# sourcemappingurl")) {
                        String[] sourceMapParts = line.split(",");

                        if (sourceMapParts.length > 0) {
                            String base64Encoded = sourceMapParts[1];
                            String base64Decoded = new String(Base64.getDecoder().decode(base64Encoded));
                            result = new JSONObject(base64Decoded);
                        }
                    }
                }
            } catch (JSONException e) {
                LogUtil.getLog(WebAssetPackageController.class).error("Error:", e);
            }

            return result;
        }

        static String getInlineSourceMap(JSONObject map) {
            String base64EncodedMap = new String(Base64.getEncoder().encode(map.toString().getBytes()));
            return MessageFormat.format("//# sourceMappingURL=data:application/json;charset=utf-8;base64,{0}", base64EncodedMap);
        }
    }
    */
}
