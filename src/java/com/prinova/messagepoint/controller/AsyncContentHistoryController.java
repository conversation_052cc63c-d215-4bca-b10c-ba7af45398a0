package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.ContentCompareUtils;
import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.ZonePart;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionHistory;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowActionStateType;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

public class AsyncContentHistoryController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentHistoryController.class);

	public static final String PARAM_HCA_1_ID					= "hca1Id";
	public static final String PARAM_HCA_2_ID					= "hca2Id";
	public static final String PARAM_TIMESTAMP_1				= "timestamp1";
	public static final String PARAM_TIMESTAMP_2				= "timestamp2";
	public static final String PARAM_IS_LATEST_1				= "isLatest1";
	public static final String PARAM_IS_LATEST_2				= "isLatest2";
	public static final String PARAM_MODEL_ID					= "modelId";
	public static final String PARAM_DATA_TYPE_ID 				= "dataType";
	public static final String PARAM_VARIANT_ID 				= "variantId";
	public static final String PARAM_ZONE_PART_ID				= "zonePartId";
	public static final String PARAM_LOCALE_ID					= "localeId";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_UPDATED_BY					= "updatedBy";
	public static final String PARAM_ACTION_TYPE				= "actionType";
	public static final String PARAM_MARKER_TYPE				= "markerType";

	public static final String TYPE_HISTORY_LIST				= "histList";
	public static final String TYPE_HISTORY_SUMMARY				= "histSumm";
	public static final String TYPE_HISTORY_COMPARE				= "histCompare";
	public static final String TYPE_ALT_TEXT_COMPARE			= "altTextCompare";
	public static final String TYPE_TRANSLATION_COMPARE			= "translationCompare";

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
		
		if ( type.equalsIgnoreCase(TYPE_HISTORY_LIST) ) {
			try {
				out.write(getHistoryListResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for history list data: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(TYPE_HISTORY_SUMMARY) ) {
			try {
				out.write(getHistorySummaryResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for history summary data: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(TYPE_HISTORY_COMPARE)) {
			try {
				out.write(getHistoryCompareResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for history compare data: "+e.getMessage(),e);
			}			
		} else if ( type.equalsIgnoreCase(TYPE_TRANSLATION_COMPARE)) {
			try {
				out.write(getTranslationCompareResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for transaltion compare data: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(TYPE_ALT_TEXT_COMPARE)) {
			try {
				out.write(getAltTextCompareResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for translation alt text compare data: "+e.getMessage(),e);
			}
		}

		return null;
	}
	
	private String getHistoryListResponseJSON(HttpServletRequest request) {
		long modelId 			= ServletRequestUtils.getLongParameter(request, PARAM_MODEL_ID, -1);
		long zonePartId			= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_PART_ID, -1);
		long variantId			= ServletRequestUtils.getLongParameter(request, PARAM_VARIANT_ID, -1);
		long localeId			= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, -1);
		JSONObject returnObj = new JSONObject();
		
		try {
			ContentObject contentObject = ContentObject.findByIdWorkingDataFocusCentric(modelId);

			// In case the hist version is empty or just working copy (might come from migration empty), recreate the history from the current content_object_data
			List<HistoricalContentObjectData> histVersionList = HistoricalContentObjectData.rebuildHistVersions(HistoricalContentObjectData.findAllByContentObjectId(modelId), modelId);

			JSONArray historyListArray = new JSONArray();
			ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantId);
			List<Long> usedHcaIdList = new ArrayList<>();

			
			returnObj.put("localeId", 	localeId);

			if(contentObject.isMessage() || contentObject.isTouchpointLocal()){

				List<HistoricalContentObjectAssociation> historyList = retrieveContentObjectHistoryCAList(contentObject, localeId, variantId, zonePartId);
				
				// Inject the version markers
				// If it is structured messages not in the master level, use the variant(TouchpointSelection) versions for the maker injection
				// Else(regular messages, dynamic messages or structured messages in the master level) use the message versions for the marker injection
				if(contentObject.isStructuredContentEnabled() && pgtn.getParentNode() != null){
					TouchpointSelection ts = TouchpointSelection.findByPGTN(pgtn.getId());
					List<ConfigurableWorkflowActionHistory> wfhl = ConfigurableWorkflowActionHistory.findByTPSelectionId(ts.getId());
					// If there is no working copy in the list, create a dummy one
					boolean foundWCWfh = false;
					for(ConfigurableWorkflowActionHistory wfh : wfhl){
						if(wfh.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_WORKING_COPY){
							foundWCWfh = true;
							break;
						}
					}
					if(!foundWCWfh){
						ConfigurableWorkflowActionHistory wfh = new ConfigurableWorkflowActionHistory();
						wfh.setActionDate(ts.getCreated());
						wfh.setStateCurrent(ConfigurableWorkflowActionStateType.ID_WORKING_COPY);
						wfhl.add(wfh);
					}
					// Sort the workflow histories by created date (Reverse chronological order)
					Collections.sort(wfhl, new Comparator<>() {
                        @Override
                        public int compare(ConfigurableWorkflowActionHistory o1, ConfigurableWorkflowActionHistory o2) {
                            return o2.getActionDate().compareTo(o1.getActionDate());
                        }
                    });
					Date versionCreated = null;
					Date versionActivated = null;
					Long versionCreatedBy = null;
					Long versionActivatedBy = null;
					// Three cases: 1. WC; 2. AC, WC; 3. WC, AC, WC, so when encountering the WC, injecting the current version with given versionCreated and versionActivated,
					// and reset the versionCreated
					List<Date> usedActivatedDates = new ArrayList<>();
					
					for(int i=0; i<wfhl.size(); i++){
						ConfigurableWorkflowActionHistory wfh = wfhl.get(i);
						if(wfh.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_WORKING_COPY){
							versionCreated = wfh.getActionDate();
							versionCreatedBy = wfh.getUserId();
						}
						
						if(versionCreated != null){
							if(usedActivatedDates.contains(versionActivated)){
								// It may happen that the WC appears many times in a row, so that the activated date needs to be set to last action date if being used before
								versionActivated = null;
								versionActivatedBy = null;
							}else{
								usedActivatedDates.add(versionActivated);
							}
							
							this.injectVersionMakers(historyList, usedHcaIdList, historyListArray, versionCreated, versionActivated, versionCreatedBy, versionActivatedBy, HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT, HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT);
							versionCreated = null;
							versionCreatedBy = null;
						}
						
						if(wfh.getStateCurrent() == ConfigurableWorkflowActionStateType.ID_FINAL_APPROVAL){
							versionActivated = wfh.getActionDate();
							versionActivatedBy = wfh.getUserId();
						}
					}
				}else{
					for(int i=0; i<histVersionList.size(); i++){
						HistoricalContentObjectData histInstance = histVersionList.get(i);
						this.injectVersionMakers(historyList, usedHcaIdList, historyListArray, histInstance.getVersionCreated(), histInstance.getVersionActivated(), histInstance.getVersionCreatedBy(), histInstance.getVersionActivatedBy(), histInstance.getVersionCreatedActionTypeId(), histInstance.getVersionActivatedActionTypeId());
					}
				}
				this.addRemainingHcas(historyList, usedHcaIdList, historyListArray);
			}else if(contentObject.isGlobalSmartText() || contentObject.isGlobalImage()){
				List<HistoricalContentObjectAssociation> historyList = retrieveContentObjectHistoryCAList(contentObject, localeId, variantId, zonePartId);

				for(int i=0; i<histVersionList.size(); i++){
					HistoricalContentObjectData histInstance = histVersionList.get(i);
					this.injectVersionMakers(historyList, usedHcaIdList, historyListArray, histInstance.getVersionCreated(), histInstance.getVersionActivated(), histInstance.getVersionCreatedBy(), histInstance.getVersionActivatedBy(), histInstance.getVersionCreatedActionTypeId(), histInstance.getVersionActivatedActionTypeId());
				}
				this.addRemainingHcas(historyList, usedHcaIdList, historyListArray);
			}

			this.postInitHistoryList(historyListArray);
			returnObj.put("hcas", historyListArray);
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve history list: " + e );
		}
		
		return returnObj.toString();
	}

	private List<HistoricalContentObjectAssociation> retrieveContentObjectHistoryCAList(ContentObject model,
																						long localeId,
																						long variantId,
																						long zonePartId){

        // Retrieve the history list based on the message types
		MessagepointLocale locale = MessagepointLocale.findById(localeId);
		ParameterGroupTreeNode tpPGTN = ParameterGroupTreeNode.findById(variantId);
		long coPGTNId = -1, tpPGTNId = -1;

		if (model.isMessage() || model.isLocalContentObject()) {
			if (model.isStructuredContentEnabled() && !tpPGTN.isTopLevel()) {
				tpPGTNId = variantId;
			}
			if (model.isDynamicVariantEnabled() && variantId != ParameterGroupTreeNode.MASTER_VARIANCE_ID) {
				coPGTNId = variantId;
			}
		} else {
			if (model.isDynamicVariantEnabled() && variantId > 0)
				coPGTNId = variantId;
		}

        List<HistoricalContentObjectAssociation> historyList = new ArrayList<>(HistoricalContentObjectAssociation.findAllByModelAndTreeNode(model.getId(), coPGTNId, tpPGTNId, locale.getId(), zonePartId));

		if(historyList.isEmpty() && model.isStructuredContentEnabled()) {
			ContentObjectAssociation currentCOA = ContentObjectAssociation.findByContentObjectAndParameters(model, model.getFocusOnDataType(), tpPGTN, locale, ZonePart.findById(zonePartId));
			if (currentCOA == null ) {		// CA does not exists, means it references its parent
				ParameterGroupTreeNode parentNode = tpPGTN.getParentNode();
				while (parentNode != null) {
					List<HistoricalContentObjectAssociation> parentHCAs = HistoricalContentObjectAssociation.findAllByModelAndTreeNode(model.getId(), -1, !parentNode.isTopLevel() ? parentNode.getId() : -1, locale.getId(), zonePartId);
					if(!parentHCAs.isEmpty()) {
						HistoricalContentObjectAssociation parentHCA = parentHCAs.get(0);
						parentHCA.setCreated(DateUtil.now());
						historyList.add(parentHCA);
					}
					parentNode = parentNode.getParentNode();
				}
			}
		}


		// Remove invalid entries
		Set<String> invalidGuids = HistoricalContentObjectData.findInvalidVersionGuidsByContentObjectId(model.getId());
		return historyList.stream().filter(o-> !invalidGuids.contains(o.getContentObjectDataGuid())).collect(Collectors.toList());
	}
	
	private void injectVersionMakers(List<HistoricalContentObjectAssociation> historyList,
									 List<Long> usedHcaIdList,
									 JSONArray historyListArray,
									 Date versionCreated,
									 Date versionActivated,
									 Long versionCreatedBy,
									 Long versionActivatedBy,
									 int versionCreatedByActionType,
									 int versionActivatedByActionType){
		// Sort the histories by the date type and then the id (Reverse chronological order)
		historyList.sort(new Comparator<>() {
            @Override
            public int compare(HistoricalContentObjectAssociation o1, HistoricalContentObjectAssociation o2) {
                //int dataTypeComparison = Integer.compare(o1.getDataType(), o2.getDataType());
                //if (dataTypeComparison != 0) {
                //    return dataTypeComparison;
                //}
                return Long.compare(o2.getId(), o1.getId());
            }
        });
		
		try {
			boolean acMarkAdded = false;
            long closestWcHcaId = -1;
			for(int i=0; i<historyList.size(); i++){
				IdentifiableMessagePointModel hca = historyList.get(i);
				Date historyCreated = hca.getCreated();
				if(versionActivated != null && !acMarkAdded){
					// If the different is less or equals to 1 second, we assume that means the same version
					if(historyCreated.compareTo(versionActivated) <= 0 || Math.abs(historyCreated.getTime() - versionActivated.getTime()) <= 1000){
						// Add the active copy mark
						JSONObject acMarkObj = new JSONObject();
						acMarkObj.put("id", hca.getId());
						acMarkObj.put("is_synced", versionCreatedByActionType==HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC);
						acMarkObj.put("activated"	, DateUtil.formatDateTimeWithSecond(versionActivated));
						acMarkObj.put("timestamp", versionActivated.getTime());
						acMarkObj.put("is_ac_mark", true);
						acMarkObj.put("updated_by", versionActivatedBy!=null?versionActivatedBy:"");
						acMarkObj.put("action_type", versionActivatedByActionType);
						acMarkAdded = true;
						historyListArray.put(acMarkObj);
					}
				}

				// If the different is less or equals to 1 second, we assume that means the same version
				if(historyCreated.compareTo(versionCreated) > 0 || Math.abs(historyCreated.getTime() - versionCreated.getTime()) <= 1000){
					if(!usedHcaIdList.contains(hca.getId())){
						HistoricalContentObjectAssociation hcaObj = (HistoricalContentObjectAssociation) hca;
						boolean isSynced = hcaObj.getActionTypeId()==HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC;
						JSONObject histObj = new JSONObject();
						histObj.put("id", hca.getId());
						histObj.put("is_synced", isSynced);
						histObj.put("action_type", hcaObj.getActionTypeId());
						histObj.put("data_type", hcaObj.getDataType());
						histObj.put("created"	, DateUtil.formatDateTimeWithSecond(hca.getCreated()));
						histObj.put("timestamp", hca.getCreated().getTime());
						if(hcaObj.getContent() != null){
							histObj.put("action_type", hcaObj.getContent().getActionTypeId());
						}else{
							histObj.put("action_type", hcaObj.getActionTypeId());
						}
						historyListArray.put(histObj);
						usedHcaIdList.add(hca.getId());
                        closestWcHcaId = hca.getId();
					}
				}else{
					break;
				}
			}
			
			if(closestWcHcaId < 0 && !historyList.isEmpty()){
				for(HistoricalContentObjectAssociation hca : historyList){
					if(!usedHcaIdList.contains(hca.getId())){
						closestWcHcaId = hca.getId();
						break;
					}
				}
				if(closestWcHcaId < 0){
					closestWcHcaId = historyList.get(historyList.size()-1).getId();
				}
			}

			// Add the working copy mark
			JSONObject wcMarkObj = new JSONObject();
			wcMarkObj.put("id", closestWcHcaId);
			wcMarkObj.put("is_synced", versionCreatedByActionType==HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC);
			wcMarkObj.put("created"	, DateUtil.formatDateTimeWithSecond(versionCreated));
			wcMarkObj.put("timestamp", versionCreated.getTime());
			wcMarkObj.put("is_wc_mark", true);
			wcMarkObj.put("updated_by", versionCreatedBy!=null?versionCreatedBy:"");
			wcMarkObj.put("action_type", versionCreatedByActionType);
			historyListArray.put(wcMarkObj);
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve history list: " + e );
		}		
	}

	private void addRemainingHcas(List<HistoricalContentObjectAssociation> historyList, List<Long> usedHcaIdList, JSONArray historyListArray){
		for(int i=0; i<historyList.size(); i++) {
			IdentifiableMessagePointModel hca = historyList.get(i);
			if(!usedHcaIdList.contains(hca.getId())){
				JSONObject histObj = new JSONObject();
				histObj.put("id", hca.getId());
				histObj.put("created"	, DateUtil.formatDateTimeWithSecond(hca.getCreated()));
				histObj.put("timestamp", hca.getCreated().getTime());
				historyListArray.put(histObj);
				usedHcaIdList.add(hca.getId());
			}
		}
	}

	private void postInitHistoryList(JSONArray historyListArray){
		List<Long> usedHcaId = new ArrayList<>();
		List<Integer> idxsToRemove = new ArrayList<>();

		long wcId = -1;
		for(int i=historyListArray.length()-1; i>=0; i--){
			JSONObject historyJsonObj = (JSONObject) historyListArray.get(i);
			long id = (long) historyJsonObj.get("id");
			boolean isWcMark = historyJsonObj.has("is_wc_mark");
			boolean isHistory = historyJsonObj.has("data_type");

			if(id <= 0 || usedHcaId.contains(id)){
				historyJsonObj.put("action_type", HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT);
			}else{
				HistoricalContentObjectAssociation hcaObj = HistoricalContentObjectAssociation.findById(id);
				historyJsonObj.put("action_type", hcaObj.getActionTypeId());
				if(isWcMark) {
					historyJsonObj.put("from_archive", hcaObj.getActionTypeId() == HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_FROM_ARCHIVE);
				}
				usedHcaId.add(id);
			}

			// Remove first history if it is duplicate with the wc mark
			if(isWcMark){
				wcId = id;
			}else if(isHistory){
				if(wcId >0 && wcId == id){	// wcId is set and it is a history entry, remove this entry
					idxsToRemove.add(i);
				}
			}
		}

		for(int idxToRemove : idxsToRemove) {
			historyListArray.remove(idxToRemove);
		}
	}

	private String getHistorySummaryResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try{
			long hcaId 			= ServletRequestUtils.getLongParameter(request, PARAM_HCA_1_ID, -1);
			boolean isLatest	= ServletRequestUtils.getBooleanParameter(request, PARAM_IS_LATEST_1, false);
			long timestamp 		= ServletRequestUtils.getLongParameter(request, PARAM_TIMESTAMP_1, -1);
			long updateById		= ServletRequestUtils.getLongParameter(request, PARAM_UPDATED_BY, -1);
			int actionType		= ServletRequestUtils.getIntParameter(request, PARAM_ACTION_TYPE, -1);
			int markerType		= ServletRequestUtils.getIntParameter(request, PARAM_MARKER_TYPE, -1);
			boolean isSynced	= ServletRequestUtils.getBooleanParameter(request, "isSynced", false);
			HistoricalContentObjectAssociation hca = HistoricalContentObjectAssociation.findById(hcaId);
			
			returnObj.put("name"						, "Summary" );
			
			// Text and Paragraph styles
			ContentObject model = hca.getContentObject();
			if(model.isMessage() || model.isTouchpointLocal() || model.isGlobalSmartText() || model.isGlobalSmartCanvas()) {
				returnObj.put("text_style_css_path", model.getCSSFilename());
				returnObj.put("paragraph_style_css_path", model.getParagraphCSSFilename());
				returnObj.put("list_style_css_path", model.getListCSSFilename());
				returnObj.put("default_style_css_path", model.getDefaultEditorCSSFilePath());
				returnObj.put("text_style_data", model.getTextStyleData());
				returnObj.put("paragraph_style_data", model.getParagraphStyleData());
			}

			if(hca != null){
				JSONArray contentArray = new JSONArray();
				JSONObject contentObj = new JSONObject();
				
				String actionBy = "";
				if(markerType < 0 && hca.getActionTypeId()==HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_SYNC) {
					if (hca.getDataType() == ContentObject.DATA_TYPE_ACTIVE) {
						actionBy = "(" + ApplicationUtil.getMessage("page.label.attribute.active.copy") + ") ";
					} else if (hca.getDataType() == ContentObject.DATA_TYPE_ARCHIVED) {
						actionBy = "(" + ApplicationUtil.getMessage("page.label.attribute.archived.copy") + ") ";
					}
				}
				while(hca.getTypeId() == ContentAssociationType.ID_REFERENCES || hca.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
					hca = hca.getReferencedHCA(timestamp, isLatest);
				}
				if(hca.getContent() != null){
					if(hca.getContent().getContent(hca.getDocument()) != null){
						contentObj.put("text_content"		, StringEscapeUtils.escapeXml(hca.getContent().getContent(hca.getDocument())));
					}else{
						contentObj.put("text_content"		, "");
					}
					contentObj.put("graphic_path"			, hca != null ? hca.getContent().getImageLocation() : "");
				}else{
					contentObj.put("text_content"			, "");

					if(hca.getReferencingImageLibrary() != null) {
						List<HistoricalContentObjectAssociation> imageHistCas = HistoricalContentObjectAssociation.findAllByModelAndTreeNode(hca.getReferencingImageLibrary().getId(), -1, -1, hca.getMessagepointLocale().getId(), hca.getZonePart() != null ? hca.getZonePart().getId() : -1);
						hca = !imageHistCas.isEmpty() ? imageHistCas.iterator().next() : null;
						contentObj.put("graphic_path"			, hca.getContent().getImageLocation());
					}
				}

				if(actionType == HistoricalContentActionType.ID_CONTENT_ACTION_TYPE_DEFAULT){
					if(isSynced){
						actionBy += ApplicationUtil.getMessage("page.label.content.action.type.sync");
					}
					else{
						switch (markerType){
							case 1: // WC Marker
								actionBy += ApplicationUtil.getMessage("page.label.created.by");
								break;
							case 2:	// AC Marker
								actionBy += ApplicationUtil.getMessage("page.label.activated.by");
								break;
							default:
								actionBy += new HistoricalContentActionType(actionType).getDisplayText();
						}
					}
				}else {
					actionBy += new HistoricalContentActionType(actionType).getDisplayText();
				}

				User updatedBy = null;
				
				if(updateById > 0){
					updatedBy = User.findById(updateById);
				}else{
					updatedBy = User.findById(hca.getUpdatedBy());
				}

				contentObj.put("updated_by"					, updatedBy!=null?updatedBy.getFullName():"");
				contentObj.put("action_by"					, actionBy);
				contentObj.put("is_suppressed"				, hca.getTypeId()==ContentAssociationType.ID_SUPPRESSES);
				contentObj.put("canvas_size"				, getCanvasWidth(hca) );			
				contentArray.put(contentObj);
				returnObj.put("contents"					, contentArray);
			}
		} catch (JSONException e) {
			e.printStackTrace();
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

	private String getHistoryCompareResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try{
			long hca1Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_1_ID, -1);
			long hca2Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_2_ID, -1);
			long timestamp1 	= ServletRequestUtils.getLongParameter(request, PARAM_TIMESTAMP_1, -1);
			long timestamp2 	= ServletRequestUtils.getLongParameter(request, PARAM_TIMESTAMP_2, -1);
			boolean isLatest1	= ServletRequestUtils.getBooleanParameter(request, PARAM_IS_LATEST_1, false);
			boolean isLatest2	= ServletRequestUtils.getBooleanParameter(request, PARAM_IS_LATEST_2, false);

			HistoricalContentObjectAssociation hca1 = HistoricalContentObjectAssociation.findById(hca1Id);
			HistoricalContentObjectAssociation hca2 = HistoricalContentObjectAssociation.findById(hca2Id);
			
			if(hca1 != null){
				while(hca1.getTypeId() == ContentAssociationType.ID_REFERENCES || hca1.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
					hca1 = hca1.getReferencedHCA(timestamp1, isLatest1);
				}

				String content1 = hca1.getContent()!=null?hca1.getContent().getContent(hca1.getDocument()):"";
				String imagePath1 = "", imagePath2 = "";
				String imageName1 = "", imageName2 = "";
				boolean isGraphicContent = false;

				if(hca1.getReferencingImageLibrary() != null) {
					List<HistoricalContentObjectAssociation> imageHistCas = HistoricalContentObjectAssociation.findAllByModelAndTreeNode(hca1.getReferencingImageLibrary().getId(), -1, -1, hca1.getMessagepointLocale().getId(), hca1.getZonePart() != null ? hca1.getZonePart().getId() : -1);
					hca1 = !imageHistCas.isEmpty() ? imageHistCas.iterator().next() : null;
				}
				if(hca1.getContent()!=null && hca1.getContent().getImageLocation() != null) {
					imagePath1 = hca1.getContent().getImageLocation();
					imageName1 = hca1.getContent().getImageName();
				}
				
				returnObj.put("graphic_path_1", imagePath1.isEmpty() ? imagePath1 : HttpRequestUtil.getFileResourceToken(imagePath1));
				returnObj.put("graphic_name_1", imageName1);
				
				isGraphicContent = !imagePath1.isEmpty();
				
				if(hca2 != null){
					while(hca2.getTypeId() == ContentAssociationType.ID_REFERENCES || hca2.getTypeId() == ContentAssociationType.ID_SAME_AS_DEFAULT){
						hca2 = hca2.getReferencedHCA(timestamp2, isLatest2);
					}

					String content2 = hca2.getContent()!=null?hca2.getContent().getContent(hca2.getDocument()):"";
					if(content1 != null && content2 != null){
						String result = StringsDiffUtils.diff(content1, content2);
						returnObj.put("compared_content", result);
					}else{
						returnObj.put("compared_content", "");
					}

					if(hca2.getReferencingImageLibrary() != null) {
						List<HistoricalContentObjectAssociation> imageHistCas = HistoricalContentObjectAssociation.findAllByModelAndTreeNode(hca2.getReferencingImageLibrary().getId(), -1, -1, hca2.getMessagepointLocale().getId(), hca2.getZonePart() != null ? hca2.getZonePart().getId() : -1);
						hca2 = !imageHistCas.isEmpty() ? imageHistCas.iterator().next() : null;
					}
					if(hca2.getContent()!=null && hca2.getContent().getImageLocation() != null) {
						imagePath2 = hca2.getContent().getImageLocation();				
						imageName2 = hca2.getContent().getImageName();
					}
					
					returnObj.put("graphic_path_2", imagePath2.isEmpty() ? imagePath2 : HttpRequestUtil.getFileResourceToken(imagePath2));
					returnObj.put("graphic_name_2", imageName2);
					
					if(!imagePath2.isEmpty()){
						returnObj.put("is_graphic_content", true);
					}
				}else{
					returnObj.put("graphic_path_2", "");
					returnObj.put("graphic_name_2", "");
					returnObj.put("compared_content", content1);
				}
				
				if(isGraphicContent){
					returnObj.put("is_graphic_content", true);
				}
			}else{
				returnObj.put("compared_content", "");
			}
		} catch (Exception e) {
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

	private String getTranslationCompareResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try{
			long hca1Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_1_ID, -1);
			long hca2Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_2_ID, -1);
			HistoricalContentObjectAssociation hca1 = HistoricalContentObjectAssociation.findById(hca1Id);
			HistoricalContentObjectAssociation hca2 = HistoricalContentObjectAssociation.findById(hca2Id);

			if(hca1 != null){
				ContentObject contentObject = null;
				if(hca2 != null){
					contentObject = hca2.getContentObject();
				}else{
					contentObject = hca1.getContentObject();
				}

				ParameterGroupTreeNode pgTreeNode = null;
				if(contentObject.isStructuredContentEnabled()){
					pgTreeNode = hca1.getTouchpointPGTreeNode();
				}else if(contentObject.isDynamicVariantEnabled()){
					pgTreeNode = hca1.getContentObjectPGTreeNode();
				}

				ContentObjectAssociation ca1 = ContentCompareUtils.findLastestCOA(contentObject, ContentObject.DATA_TYPE_WORKING, pgTreeNode,
						hca1.getZonePart(), hca1.getMessagepointLocale());

				String content1 = ca1.getContent()!=null?ca1.getContent().getContent(ca1.getDocument()):"";
				String imagePath1 = "", imagePath2 = "";
				String imageName1 = "", imageName2 = "";
				boolean isGraphicContent = false;

				if(ca1.getContent()!=null && ca1.getContent().getImageLocation() != null) {
					imagePath1 = ca1.getContent().getImageLocation();
					imageName1 = ca1.getContent().getImageName();
				}

				returnObj.put("graphic_path_1", imagePath1.isEmpty() ? imagePath1 : HttpRequestUtil.getFileResourceToken(imagePath1));
				returnObj.put("graphic_name_1", imageName1);

				isGraphicContent = !imagePath1.isEmpty();

				// Find the last active copy
				ContentObjectAssociation ca2 = ContentCompareUtils.findLastestCOA(contentObject, ContentObject.DATA_TYPE_ACTIVE, pgTreeNode,
						hca1.getZonePart(), hca1.getMessagepointLocale());

				String content2 = ca2.getContent()!=null?ca2.getContent().getContent(ca2.getDocument()):"";
				if(content1 != null && content2 != null){
					String result = StringsDiffUtils.diff(content2, content1);
					returnObj.put("compared_content", result);
				}else{
					returnObj.put("compared_content", "");
				}

				if(ca2.getContent()!=null && ca2.getContent().getImageLocation() != null) {
					imagePath2 = ca2.getContent().getImageLocation();
					imageName2 = ca2.getContent().getImageName();
				}

				returnObj.put("graphic_path_2", imagePath2.isEmpty() ? imagePath2 : HttpRequestUtil.getFileResourceToken(imagePath2));
				returnObj.put("graphic_name_2", imageName2);

				if(!imagePath2.isEmpty()){
					returnObj.put("is_graphic_content", true);
				}

				if(isGraphicContent){
					returnObj.put("is_graphic_content", true);
				}
			}else{
				returnObj.put("compared_content", "");
			}
		} catch (Exception e) {
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

	private String getAltTextCompareResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try{
			long hca1Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_1_ID, -1);
			long hca2Id 		= ServletRequestUtils.getLongParameter(request, PARAM_HCA_2_ID, -1);
			HistoricalContentObjectAssociation hca1 = HistoricalContentObjectAssociation.findById(hca1Id);
			HistoricalContentObjectAssociation hca2 = HistoricalContentObjectAssociation.findById(hca2Id);

			if(hca1 != null){
				ContentObject contentObject = null;
				if(hca2 != null){
					contentObject = hca2.getContentObject();
				}else{
					contentObject = hca1.getContentObject();
				}

				ParameterGroupTreeNode pgTreeNode = null;
				if(contentObject.isStructuredContentEnabled()){
					pgTreeNode = hca1.getTouchpointPGTreeNode();
				}else if(contentObject.isDynamicVariantEnabled()){
					pgTreeNode = hca1.getContentObjectPGTreeNode();
				}

				ContentObjectAssociation ca1 = ContentCompareUtils.findLastestCOA(contentObject, ContentObject.DATA_TYPE_WORKING, pgTreeNode,
						hca1.getZonePart(), hca1.getMessagepointLocale());
				String content1 = "";
				if(ca1.getContent()!=null&&
						ca1.getContent().getImageAltText()!=null&&
						ca1.getContent().getImageAltText().getEncodedValue()!=null){
					content1 = ca1.getContent().getImageAltText().getEncodedValue();
				}

				// Find the last active copy
				ContentObjectAssociation ca2 = ContentCompareUtils.findLastestCOA(contentObject, ContentObject.DATA_TYPE_ACTIVE, pgTreeNode,
						hca1.getZonePart(), hca1.getMessagepointLocale());

				String content2 = "";
				if(ca2.getContent()!=null&&
						ca2.getContent().getImageAltText()!=null&&
						ca2.getContent().getImageAltText().getEncodedValue()!=null){
					content2 = ca2.getContent().getImageAltText().getEncodedValue();
				}
				if(content1 != null && content2 != null){
					String result = StringsDiffUtils.diff(content2, content1);
					returnObj.put("compared_content", result);
				}else{
					returnObj.put("compared_content", "");
				}

				returnObj.put("is_graphic_content", false);
			}else{
				returnObj.put("compared_content", "");
			}
		} catch (Exception e) {
			log.error("Error: Unable to get content: " + e );
		}
		return returnObj.toString();
	}

	private String getCanvasWidth(HistoricalContentObjectAssociation hca) {
		if ( hca != null && hca.getZonePart() != null )
			return hca.getZonePart().getCanvasDimensions();
		else if (  hca != null && hca.getContentObject() != null )
			return hca.getContentObject().getCanvasDimensions();
		return null;
	}

}
