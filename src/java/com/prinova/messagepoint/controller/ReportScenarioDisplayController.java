package com.prinova.messagepoint.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.scenario.OperationsReportScenario;
import com.prinova.messagepoint.model.scenario.TpDeliveryReportScenario;
import com.prinova.messagepoint.util.FileUtil;

public class ReportScenarioDisplayController implements Controller {
	
	public static final String REQ_PARAM_REPORT = "report";
	
	public static final String REQ_PARAM_OPER_SCEN_ID = "operationScenId";
	public static final String REQ_PARAM_OPER_REPORT_ID = "batchReportId";
	public static final String REQ_PARAM_TP_DELI_SCEN_ID = "tpDeliveryScenId";
	public static final String REQ_PARAM_TP_DELI_REPORT_ID = "tpDeliveryReportId";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		Map<String, Object> dataMap = new HashMap<>();
		long operationScenId = getIdFromRequest(request, REQ_PARAM_OPER_SCEN_ID);	
		long batchReportId = getIdFromRequest(request, REQ_PARAM_OPER_REPORT_ID);
		if(operationScenId != -1){
			OperationsReportScenario operationsReport = OperationsReportScenario.findById(operationScenId);
			dataMap.put(REQ_PARAM_REPORT, operationsReport);			
			String htmlReportContent;
			try{
				String reportPath;
				if(batchReportId == -1)
					reportPath = operationsReport.getReportPath() +  "/BatchReportJobLevel.html";		
				else
					reportPath = operationsReport.getReportPath() +  "/" + batchReportId + ".html";
				operationsReport.setReportFile(reportPath);
				htmlReportContent = FileUtil.fileToString( reportPath );
				if(htmlReportContent != null){
					dataMap.put("htmlReportContent", htmlReportContent);
				}
			}catch(Exception e){
			}			
		}
		long tpDeliveryScenId = getIdFromRequest(request, REQ_PARAM_TP_DELI_SCEN_ID);	
		long tpDeliveryReportId = getIdFromRequest(request, REQ_PARAM_TP_DELI_REPORT_ID);
		if(tpDeliveryScenId != -1){
			TpDeliveryReportScenario tpDeliveryReport = TpDeliveryReportScenario.findById(tpDeliveryScenId);
			dataMap.put(REQ_PARAM_REPORT, tpDeliveryReport);			
			String htmlReportContent;
			try{
				String reportPath;
				if( tpDeliveryReportId == -1)
					reportPath = tpDeliveryReport.getReportPath()  +  "/TPDeliveryReport.html";
				else
					reportPath = tpDeliveryReport.getReportPath() +  "/" + tpDeliveryReportId + ".html";
				tpDeliveryReport.setReportFile(reportPath);
				htmlReportContent = FileUtil.fileToString( reportPath );
				if(htmlReportContent != null){
					dataMap.put("htmlReportContent", htmlReportContent);
				}
			}catch(Exception e){
			}			
		}		
		return new ModelAndView(getFormView(), dataMap);
	}
	
	private long getIdFromRequest(HttpServletRequest request, String reportId){
		if(request.getParameterMap().containsKey(reportId)){
			return ServletRequestUtils.getLongParameter(request, reportId, -1);
		}
		return -1L;
	}
}