package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.util.JSONUtils;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.jstree.CheckboxTreeNavigationJsonSerializer;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncCheckboxTreeController implements Controller {

    @Override
    public ModelAndView handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        String requestId = ServletRequestUtils.getStringParameter(httpServletRequest, "requestId", null);
        String ifNoneMatch = httpServletRequest.getHeader("If-None-Match");
        AsyncCheckboxTreeController.AsyncCheckboxTreeRequestContext requestContext = new AsyncCheckboxTreeController.AsyncCheckboxTreeRequestContext();
        try {
            try {
                Object binding = httpServletRequest.getSession().getAttribute("Binding-" + requestId);
                requestContext.setDataBinding(binding);
                Object params = httpServletRequest.getSession().getAttribute("Parameters-" + requestId);
                JSONObject jsonParams = new JSONObject(String.valueOf(params));
                requestContext.setExpanded(JSONUtils.getValueOrDefault(jsonParams, "expanded", false));
                requestContext.setMasterNodeId(JSONUtils.getValueOrDefault(jsonParams, "masterNodeId", -1));
                requestContext.setSelectedNodesList(JSONUtils.getValueOrDefault(jsonParams, "selectedNodesList", StringUtils.EMPTY));
            } catch (Exception e) {
                LogUtil.getLog(AsyncCheckboxTreeController.class).error("Error:", e);
            }

            JSONObject values = new JSONObject();
            values.put("tree", requestContext.generateTreeJsonNodes());
            values.put("masterVariantId", requestContext.getMasterNodeId());
            String respValue = values.toString();
            String etag = DigestUtils.md5Hex(respValue);
            if (ifNoneMatch != null && ifNoneMatch.equals(etag)) {
                httpServletResponse.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
            } else {
                httpServletResponse.setHeader("ETag", etag);
                httpServletResponse.setContentType("application/json");
                httpServletResponse.getOutputStream().write(respValue.getBytes());
            }
        } finally {
            httpServletRequest.getSession().removeAttribute("Parameters-" + requestId);
            httpServletRequest.getSession().removeAttribute("Binding-" + requestId);
        }

        return null;
    }

    public static class AsyncCheckboxTreeRequestContext {

        private Boolean expanded;
        private long masterNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
        private Object dataBinding;
        private String cssClass;
        private String id;
        private String onChange;
        private String onResize;
        private String selectedNodesList;
        private String style;

        private JSONArray generateTreeJsonNodes() throws Exception {

            RationalizerApplication rationalizerApp = (RationalizerApplication) getDataBinding();
            CheckboxTreeNavigationJsonSerializer serializer = new CheckboxTreeNavigationJsonSerializer(rationalizerApp);

            if (StringUtils.isNotEmpty(selectedNodesList)) {
                serializer.setSelectedNodesPaths(selectedNodesList);
            } else {
                serializer.setSelectedNodesPaths("-9");
            }
            return serializer.getTreeStructureJson();
        }


        public Boolean getExpanded() {
            return expanded;
        }

        public void setExpanded(Boolean expanded) {
            this.expanded = expanded;
        }

        public long getMasterNodeId() {
            return masterNodeId;
        }

        public void setMasterNodeId(long masterNodeId) {
            this.masterNodeId = masterNodeId;
        }

        public Object getDataBinding() {
            return dataBinding;
        }

        public void setDataBinding(Object dataBinding) {
            this.dataBinding = dataBinding;
        }

        public String getCssClass() {
            return cssClass;
        }

        public void setCssClass(String cssClass) {
            this.cssClass = cssClass;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOnChange() {
            return onChange;
        }

        public void setOnChange(String onChange) {
            this.onChange = onChange;
        }

        public String getOnResize() {
            return onResize;
        }

        public void setOnResize(String onResize) {
            this.onResize = onResize;
        }

        public String getStyle() {
            return style;
        }

        public void setStyle(String style) {
            this.style = style;
        }

        public String getSelectedNodesList() {
            return selectedNodesList;
        }

        public void setSelectedNodesList(String selectedNodesList) {
            this.selectedNodesList = selectedNodesList;
        }
    }

    public static class AsyncCheckboxTreeRequestVO {

        private boolean expanded;
        private long masterNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
        private String cssClass;
        private String id;
        private String onChange;
        private String onResize;
        private String selectedNodesList;
        private String style;


        public boolean isExpanded() {
            return expanded;
        }

        public void setExpanded(boolean expanded) {
            this.expanded = expanded;
        }

        public long getMasterNodeId() {
            return masterNodeId;
        }

        public void setMasterNodeId(long masterNodeId) {
            this.masterNodeId = masterNodeId;
        }

        public String getCssClass() {
            return cssClass;
        }

        public void setCssClass(String cssClass) {
            this.cssClass = cssClass;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOnChange() {
            return onChange;
        }

        public void setOnChange(String onChange) {
            this.onChange = onChange;
        }

        public String getOnResize() {
            return onResize;
        }

        public void setOnResize(String onResize) {
            this.onResize = onResize;
        }

        public String getSelectedNodesList() {
            return selectedNodesList;
        }

        public void setSelectedNodesList(String selectedNodesList) {
            this.selectedNodesList = selectedNodesList;
        }

        public String getStyle() {
            return style;
        }

        public void setStyle(String style) {
            this.style = style;
        }

    }
}
