package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.QueryItem;
import com.prinova.messagepoint.util.TouchpointHealthCheck;

public class AsyncHealthCheckController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncHealthCheckController.class);
	
	public static final String PARAM_QUERY_ID 			= "queryId";
	public static final String PARAM_DOCUMENT_ID		= "documentId";
    public static final String PARAM_CHECKSTATUS			= "checkStatus";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getHealthCheckResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for health check: "+e.getMessage(),e);
		}
		
		return null;
	}

	private String getHealthCheckResponseJSON (HttpServletRequest request) {
		int queryId = ServletRequestUtils.getIntParameter(request, PARAM_QUERY_ID, 0);
		long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1);
        boolean checkStatus = ServletRequestUtils.getBooleanParameter(request, PARAM_CHECKSTATUS, false);

		JSONObject returnObj = new JSONObject();
		try {
			if(queryId > 0){
				QueryItem queryItem = TouchpointHealthCheck.executeQueryById(queryId, documentId, checkStatus);
				returnObj.put("status", queryItem.getStatus());
				returnObj.put("hasDrillDown", queryItem.isHasDrillDown());
				returnObj.put("failedMsg", queryItem.getFailedMsg());
                returnObj.put("canQueryTaskStatus", queryItem.isCanQueryTaskStatus());
			}else{
				returnObj.put("error", "Invalid query Id");
			}
		} catch (JSONException e) {
			log.error("Error: Unable to run health check: " + e );
		}
		return returnObj.toString();
	}
}