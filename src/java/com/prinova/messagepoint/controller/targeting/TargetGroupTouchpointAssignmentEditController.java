package com.prinova.messagepoint.controller.targeting;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.targeting.UpdateTargetGroupTouchpointAssignmentService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.NestedPageUtils;

public class TargetGroupTouchpointAssignmentEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(TargetGroupTouchpointAssignmentEditController.class);

	public static final String REQ_PARAM_SELECTED_IDS_PARAM = "selectedIds";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		List<Document> availableTouchpoints = Document.findAllDocumentsAndProjectsVisible(true);
		referenceData.put("availableTouchpoints", availableTouchpoints);
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {

		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");

		TargetGroupTouchpointAssignmentWrapper command;
		command = new TargetGroupTouchpointAssignmentWrapper(getSelectedIds(selectedIdsS));

		return command;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		String selectedIdsS = ServletRequestUtils.getStringParameter(request, REQ_PARAM_SELECTED_IDS_PARAM, "");
		TargetGroupTouchpointAssignmentWrapper command = (TargetGroupTouchpointAssignmentWrapper)commandObj;
		ServiceExecutionContext context = UpdateTargetGroupTouchpointAssignmentService.createContext(getSelectedIds(selectedIdsS),command.getTouchpointAssignments());

		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTargetGroupTouchpointAssignmentService.SERVICE_NAME, UpdateTargetGroupTouchpointAssignmentService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(UpdateTargetGroupTouchpointAssignmentService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" selected Users were not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		} else {
			for(Document document : command.getTouchpointAssignments()){
				if(!document.isTpContentChanged()){
					document.setTpContentChanged(true);
					document.save();
				}
			}
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView(new RedirectView(getSuccessView()), parms);
		}
	}
	
	/**
	 * Convert selected id string to set
     */
	private Set<Long> getSelectedIds(String selectedIdsS){		
		Set<Long> selectionIds = new HashSet<>();
		if(selectedIdsS != null && !selectedIdsS.isEmpty()){
			for(String idS : selectedIdsS.split("_")){
				selectionIds.add(Long.valueOf(idS));
			}
		}
		return selectionIds;
	}
}