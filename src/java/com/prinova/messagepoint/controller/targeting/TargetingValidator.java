package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.targeting.*;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import com.prinova.messagepoint.util.DataModelUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.util.LogUtil;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.HashSet;
import java.util.List;

public class TargetingValidator implements Validator {
	private static final Log log = LogUtil.getLog(TargetingValidator.class);

	public boolean supports(Class clazz) {
        return clazz.equals(TargetingWrapper.class);
    }

    public void validate(Object command, Errors errors) {
    	
    	TargetingWrapper wrapper = (TargetingWrapper)command;
    	
    	Targetable targetableModel = (Targetable)wrapper.getModel();
    	
    	List<TargetGroupWrapper> includedList = wrapper.getIncludedList();
    	if (includedList != null) {
        	for (TargetGroupWrapper targetGroupWrapper :includedList) {
        		if (targetGroupWrapper == null) {
        			continue;
        		}
        		TargetGroup targetGroup = targetGroupWrapper.getTargetGroup();
        		if (targetGroup == null) {
        			continue;
        		}
        		loadTargetGroupInfo(targetGroup, targetGroupWrapper);
        		if (targetGroup.isParameterized()) {
            		TargetGroupValidator.validateConditionElementSelected(targetGroupWrapper, errors, true);
        		}
        	}
    	}
    	
    	List<TargetGroupWrapper> excludedList = wrapper.getExcludedList();
    	if (excludedList != null) {
        	for (TargetGroupWrapper targetGroupWrapper :excludedList) {
        		if (targetGroupWrapper == null) {
        			continue;
        		}
        		TargetGroup targetGroup = targetGroupWrapper.getTargetGroup();
        		if (targetGroup == null) {
        			continue;
        		}
        		loadTargetGroupInfo(targetGroup, targetGroupWrapper);
        		if (targetGroup.isParameterized()) {
            		TargetGroupValidator.validateConditionElementSelected(targetGroupWrapper, errors, true);
        		}
        	}
    	}
    	
    	List<TargetGroupWrapper> extendedList = wrapper.getExtendedList();
    	if (extendedList != null) {
        	for (TargetGroupWrapper targetGroupWrapper :extendedList) {
        		if (targetGroupWrapper == null) {
        			continue;
        		}
        		TargetGroup targetGroup = targetGroupWrapper.getTargetGroup();
        		if (targetGroup == null) {
        			continue;
        		}
        		loadTargetGroupInfo(targetGroup, targetGroupWrapper);
        		if (targetGroup.isParameterized()) {
            		TargetGroupValidator.validateConditionElementSelected(targetGroupWrapper, errors, true);
        		}
        	}
    	}

    	
    	if (targetableModel instanceof ContentObjectData) {
    		ContentObjectData contentObjectData = (ContentObjectData)targetableModel;
			ContentObject contentObject = ContentObjectData.findByGuid(contentObjectData.getGuid()).getContentObject();
    		List<TargetGroup> allTargetGroups = wrapper.getAllTargetGroupsList();
        	if (allTargetGroups.size() > 1 && !DataModelUtil.targetGroupsDataModelCompatible(allTargetGroups)) {
        		errors.reject("error.message.target.groups.not.data.model.compatible");
        	} else if (!allTargetGroups.isEmpty() && !contentObject.getIsTouchpointLocal() ) {
        		int result = DataModelUtil.targetGroupsAndZonesDataModelCompatible(allTargetGroups, contentObject.getZone());
        		if (result < 1) {
	        		if (result == 0) 
	        		{
	            		errors.reject("error.message.message.zone.and.target.groups.not.data.model.compatible");
	        		}
	        		else
	        		{
	        			errors.reject("error.message.message.zone.has.empty.datagroup");
	        		}
        		}
        	}
    	}

    	log.debug("Done inside TargetingValidator.validate() with errors.hasErrors() returning '" + errors.hasErrors() + "'.");
    }
    
    private void loadTargetGroupInfo(TargetGroup targetGroup, TargetGroupWrapper targetGroupWrapper) {
		for (ConditionItem conditionItem : targetGroup.getInstance().getConditionItems()) {
			ConditionElement conditionElement = conditionItem.getConditionElement(); 
			if (targetGroupWrapper.getConditionElements() == null) {
				targetGroupWrapper.setConditionElements(new HashSet<>());
			}
			targetGroupWrapper.getConditionElements().add(conditionElement);
			for (ConditionItemValue conditionItemValue : conditionItem.getConditionItemValues()) {
				ConditionSubelement conditionSubelement = conditionItemValue.getConditionSubelement();
				if (targetGroupWrapper.getConditionSubelementMap().get(conditionElement.getId()) == null) {
					targetGroupWrapper.getConditionSubelementMap().put(conditionElement.getId(), new HashSet<>());
				}
				targetGroupWrapper.getConditionSubelementMap().get(conditionElement.getId()).add(conditionSubelement.getId());
			}
		}
    }
}