package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.admin.VariableUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteTargetGroupsService;
import com.prinova.messagepoint.platform.services.whereused.CreateWhereUsedReportService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TargetGroupListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(TargetGroupListController.class);

	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_DOCUMENTID 		= "documentId";

	public static final int ACTION_DELETE 						= 2;
	public static final int ACTION_WHERE_USED 			= 3;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		referenceData.put("contextParms", VariableUtils.getContextURLParms(request));

		boolean displaySegmentationAnalysis = false;
		if(!UserUtil.getCurrentGlobalContext() && UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW)){
			Document document = UserUtil.getCurrentTouchpointContext();
			if (document != null)
				displaySegmentationAnalysis = document.isSegmentationAnalysisEnabled();
		}
		referenceData.put("displaySegmentationAnalysis", displaySegmentationAnalysis);

		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );

		// Task metadata form definition
		referenceData.put("hasTaskMetadataFormDef", !MetadataFormDefinition.findByTypeStrict(MetadataFormDefinitionType.ID_TASK).isEmpty());
		referenceData.put("taskMetadataFormDefinitions", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_TASK));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	@Override
	protected Object formBackingObject(HttpServletRequest request){
		return new TargetGroupListWrapper();
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		TargetGroupListWrapper command = (TargetGroupListWrapper) commandObj;

		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());

		switch(action){
			case ACTION_DELETE:{
				// Delete the target group(s)
				List<TargetGroup> list = command.getSelectedList();
				List<Document> docList = new ArrayList<>();
				for(TargetGroup targetGroup: list){
					if(targetGroup != null)
						for(Document document : targetGroup.getDocuments()){
							if(!docList.contains(document)){
								docList.add(document);
							}
						}
				}
				ServiceExecutionContext context = BulkDeleteTargetGroupsService.createContext(list);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteTargetGroupsService.SERVICE_NAME,
						BulkDeleteTargetGroupsService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteTargetGroupsService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(TargetGroup targetGroup: list){
						sb.append(" id ").append(targetGroup.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  working copies not aborted. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					// Set TPContentChanged to true
					for(Document document : docList){
						if(!document.isTpContentChanged()){
							document.setTpContentChanged(true);
							document.save();
						}
					}
					for(TargetGroup targetGroup: list){
						if(targetGroup != null){
							Set<Document> documents = new HashSet<>();
							TargetGroupInstance tgInstance = targetGroup.getInstance();
							if (tgInstance != null) {
								Set<ConditionElement> conditionElementSet = tgInstance.getConditionElements();
								for (ConditionElement ce: conditionElementSet) {
									documents.addAll(ce.getDocumentsByMappedVariablesUsed());
								}
							}
							AuditEventUtil.push(requestor, AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_TARGET_GROUP, targetGroup.getName(), targetGroup.getId(), AuditActionType.ID_CHANGE_DELETED, null, documents);
						}
					}
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
			case ACTION_WHERE_USED:{
				long reportId 					= Long.valueOf(command.getWhereUsedReportId());
				TargetGroup targetGroup 	= command.getSelectedList().get(0);

				ServiceExecutionContext context = CreateWhereUsedReportService.createContext(reportId, targetGroup.getId(), TargetGroup.class, false, new String[]{}, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateWhereUsedReportService.SERVICE_NAME, CreateWhereUsedReportService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateWhereUsedReportService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" Where used report could not be generated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
		}
		return null;
	}

	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> parms = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		if (documentId != -1L) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			parms.put(REQ_PARM_DOCUMENTID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
		}

		return parms;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {

		setOriginForm(request, null);

		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry

			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;

		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), false);
		}
		// ******* End User context persist/recall **************

		// Audit (Page access: Target groups list)
		AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_NONE, null, null, AuditActionType.ID_TARGET_GROUPS_LIST, null);
		return super.showForm(request, response, errors);
	}
}
