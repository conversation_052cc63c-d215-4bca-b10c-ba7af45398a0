package com.prinova.messagepoint.controller.targeting;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class TargetGroupListDetailController extends MessagepointController {
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long targetgroupid = ServletRequestUtils.getLongParameter(request, TargetGroupController.TARGET_GROUP_ID_PARM, -1L);
		
		TargetGroup targetGroup = HibernateUtil.getManager().getObject(TargetGroup.class, targetgroupid);
		params.put("targetGroup", targetGroup);

		return new ModelAndView(getFormView(), params);
	}
}
