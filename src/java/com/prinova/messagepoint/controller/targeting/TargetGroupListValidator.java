package com.prinova.messagepoint.controller.targeting;

import java.util.List;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TargetGroupListValidator extends MessagepointInputValidator  {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TargetGroupListWrapper wrapper = (TargetGroupListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		
		if ( action == TargetGroupListController.ACTION_DELETE ) {
			List<TargetGroup> selectedtgs = wrapper.getSelectedList();
			StringBuilder referencedTgNames = new StringBuilder();
			boolean referenced = false;
			
			for (TargetGroup currentTg: selectedtgs)
				if ( currentTg.isReferenced() ) {
					referenced = true;
					referencedTgNames.append(currentTg.getName()).append(" ");
				}

			if (referenced)
				errors.reject(	"error.message.cannot.delete.referenced.targetgroups", new String[] {referencedTgNames.toString()}, null);
		}
	}
}
