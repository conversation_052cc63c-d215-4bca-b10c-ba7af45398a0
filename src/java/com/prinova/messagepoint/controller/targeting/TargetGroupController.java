package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SegmentationAnalysis;
import com.prinova.messagepoint.model.SegmentationAnalyzable;
import com.prinova.messagepoint.model.admin.DateDataValue;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionItem;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.wrapper.*;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetGroupService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetGroupServiceRequest;
import com.prinova.messagepoint.platform.services.tpadmin.CreateSegmentationAnalysisService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class TargetGroupController extends MessagepointController {
	// private static final Log log = LogUtil.getLog(TargetGroupController.class);

    public static final String TARGET_GROUP_ID_PARM 			= "targetgroupid";
	
    private static final String CONTENT_TARGETING_ID_PARM 		= "contentTargetingId";
    private static final String NEW_TARGET_EDIT_PARAM 			= "newtgedit";
    
    private String insertSuccessView;
    private String tagSuccessView;
    private String attachmentSuccessView;
    private String touchpointTargetingSuccessView;
    
    public String getInsertSuccessView() {
		return insertSuccessView;
	}
	public void setInsertSuccessView(String insertSuccessView) {
		this.insertSuccessView = insertSuccessView;
	}

	public String getTagSuccessView() {
		return tagSuccessView;
	}
	public void setTagSuccessView(String tagSuccessView) {
		this.tagSuccessView = tagSuccessView;
	}

	public String getAttachmentSuccessView() {
		return attachmentSuccessView;
	}
	public void setAttachmentSuccessView(String attachmentSuccessView) {
		this.attachmentSuccessView = attachmentSuccessView;
	}

	public String getTouchpointTargetingSuccessView() {
		return touchpointTargetingSuccessView;
	}
	public void setTouchpointTargetingSuccessView(
			String touchpointTargetingSuccessView) {
		this.touchpointTargetingSuccessView = touchpointTargetingSuccessView;
	}

	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(ConditionElement.class, new IdCustomEditor<>(ConditionElement.class));
    	binder.registerCustomEditor(ConditionSubelement.class, new IdCustomEditor<>(ConditionSubelement.class));
    	binder.registerCustomEditor(ConditionItem.class, new IdCustomEditor<>(ConditionItem.class));
    	binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
    	binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

    protected Object formBackingObject(HttpServletRequest request) {
		long tGID = ServletRequestUtils.getLongParameter(request, TargetGroupController.TARGET_GROUP_ID_PARM, -1);
    	TargetGroupWrapper targetGroupWrapper = new TargetGroupWrapper(tGID);
		return targetGroupWrapper;
    }

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();
    	
    	referenceData.put("dateValueTypesJSON", DateDataValue.getDateValueTypesJSON());
		long tGID = ServletRequestUtils.getLongParameter(request, TargetGroupController.TARGET_GROUP_ID_PARM, -1);
    	long contentObjectId = ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -1);
    	long documentId = -1;
		TargetGroup tg = TargetGroup.findById(tGID);
    	if ( !UserUtil.getCurrentGlobalContext() || contentObjectId != -1 )
    		documentId = UserUtil.getCurrentTouchpointContext() != null ? UserUtil.getCurrentTouchpointContext().getId() : -1;
    	referenceData.put("documentId", documentId);

		List<Document> availableTouchpoints = Document.findAllDocumentsAndProjectsVisible(true);
		referenceData.put("availableTouchpoints", availableTouchpoints);
		
		boolean displaySegmentationAnalysis = false;
		if(!UserUtil.getCurrentGlobalContext() && UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW)){
			Document document = UserUtil.getCurrentTouchpointContext();
			displaySegmentationAnalysis = document.isSegmentationAnalysisEnabled();
			
			if(displaySegmentationAnalysis){
				if(tg != null)
					referenceData.put("segmentationInProcess", SegmentationAnalysis.isInProcess(tg.getSegmentationData(), UserUtil.getCurrentTouchpointContext().getId()));
			}
		}
		referenceData.put("displaySegmentationAnalysis", displaySegmentationAnalysis);
		
		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );

		boolean canUpdate = (tg == null);
		if(tg != null){
			AsyncTargetGroupListVO vo = new AsyncTargetGroupListVO();
			vo.setTargetGroup(tg);
			AsyncTargetGroupListVO.TargetGroupListVOFlags flags = new AsyncTargetGroupListVO.TargetGroupListVOFlags();
			AsyncTargetGroupListWrapper.setActionFlags(tg, flags);
			canUpdate = flags.isCanUpdate();
		}
		referenceData.put("canUpdate", canUpdate);
    	return referenceData;
    }

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {

		String submitType = ServletRequestUtils.getStringParameter(request, SUBMIT_PARAM_SUBMIT_TYPE, "");

		if (submitType.equals(SUBMIT_PARAM_CANCEL) || submitType.equals(SUBMIT_PARAM_CANCEL_AND_EXIT))
			return true;

		return super.suppressValidation(request, command);
	}

	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object command,
            BindException errors) throws Exception {
    	if (!(command instanceof TargetGroupWrapper)) {
    		return new ModelAndView(new RedirectView(getFormView()));
    	} else {

			String submitType = ServletRequestUtils.getStringParameter(request, SUBMIT_PARAM_SUBMIT_TYPE, "");

			if(submitType.equals(MessagepointController.SUBMIT_PARAM_CANCEL_AND_EXIT)) {

				String [] originForm = getOriginForm(request, true);
				String origin = originForm[0];

				if (!originForm[1].isEmpty())
					origin += "?" + originForm[1];

				return new ModelAndView(new RedirectView(origin));

			}

			TargetGroupWrapper targetGroupWrapper = (TargetGroupWrapper)command;
    		ServiceExecutionContext context = UpdateTargetGroupService.createContext();
    		UpdateTargetGroupServiceRequest serviceRequest = (UpdateTargetGroupServiceRequest)context.getRequest();
    		serviceRequest.setTargetGroupWrapper(targetGroupWrapper);
    		boolean isNewTg = targetGroupWrapper.getTargetGroupId() < 0;
       		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTargetGroupService.SERVICE_NAME, UpdateTargetGroupService.class);
    		service.execute(context);
    		if(context.getResponse().isSuccessful()){
    			String tgIdStr = (String)context.getResponse().getResultValueBean();
    			TargetGroup tg = TargetGroup.findById(Long.valueOf(tgIdStr));
    			
    			if(UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_LICENCED_SIMULATION_VIEW)){
	    			List<Document> documents = new ArrayList<>();
	    			if(UserUtil.getCurrentGlobalContext())
	    				documents.addAll(tg.getDocuments());
	    			else
	    				documents.add(UserUtil.getCurrentTouchpointContext());
	    			for(Document document : documents){
	    				if(document.isSegmentationAnalysisEnabled()){
			    			// Update segmentation data
			    			Service sgService = MessagepointServiceFactory.getInstance().lookupService(CreateSegmentationAnalysisService.SERVICE_NAME, CreateSegmentationAnalysisService.class);
			    			Set<SegmentationAnalyzable> sas = new HashSet<>();
			    			sas.add(tg);
			    			ServiceExecutionContext sgContext = CreateSegmentationAnalysisService.createContext(document.getId(), sas, UserUtil.getPrincipalUser());
			    			if ( sgService != null && sgContext != null )
			    				sgService.execute(sgContext);
	    				}
	    				// Set TPContentChanged to true
	    				if(!document.isTpContentChanged()){
	    					document.setTpContentChanged(true);
	    					document.save();
	    				}
	    			}
    			}

				String current = request.getRequestURL().toString();

    			switch (submitType) {

					case MessagepointController.SUBMIT_PARAM_SAVE_AND_STAY : {

						Map<String, Object> params = new HashMap<>();
						params.put(TargetGroupController.TARGET_GROUP_ID_PARM, tgIdStr);
						params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getStringParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, null));
						params.put(NEW_TARGET_EDIT_PARAM, ServletRequestUtils.getStringParameter(request, NEW_TARGET_EDIT_PARAM, null));
						params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

						return new ModelAndView(new RedirectView(current), params);

					}

					case MessagepointController.SUBMIT_PARAM_SAVE_AND_ADD : {

						Map<String, Object> params = new HashMap<>();
						params.put(REQUEST_PARAM_CUSTOM_FORM_SOURCE, ServletRequestUtils.getStringParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, null));
						params.put(NEW_TARGET_EDIT_PARAM, ServletRequestUtils.getStringParameter(request, NEW_TARGET_EDIT_PARAM, null));
						params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

						return new ModelAndView(new RedirectView(current), params);

					}

					case MessagepointController.SUBMIT_PARAM_SAVE_AND_GOBACK : {

						String [] originForm = getOriginForm(request, true);
						String origin = originForm[0];

						if (!originForm[1].isEmpty())
							origin += "?" + originForm[1];

						Map<String, Object> params = new HashMap<>();
						params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

						return new ModelAndView(new RedirectView(origin), params);

					}

					case MessagepointController.SUBMIT_PARAM_SAVE_AND_GOTOLIST : {

						Map<String, Object> params = new HashMap<>();
						params.put(REQUEST_PARAM_SAVE_SUCCESS, true);

						return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + "dataadmin/targetgroups.form"), params);

					}

					default:
						// send the user back to the form page so they can see the changes they just made
						if (request.getParameter(ContentObject.REQ_PARM_CONTENT_OBJECT_ID) != null) {
							return new ModelAndView(new RedirectView(getSuccessView()), ContentObject.REQ_PARM_CONTENT_OBJECT_ID, request.getParameter(ContentObject.REQ_PARM_CONTENT_OBJECT_ID));
						} else if (request.getParameter(CONTENT_TARGETING_ID_PARM) != null) {
							Map<String, Object> params = new HashMap<>();
							params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
							params.put(CONTENT_TARGETING_ID_PARM, request.getParameter(CONTENT_TARGETING_ID_PARM));
							return new ModelAndView(new RedirectView("../frameClose.jsp"), params);
						} else {
							return new ModelAndView(new RedirectView( "target_group_edit.jsp?targetgroupid="+targetGroupWrapper.getTargetGroupId()+"&"+NestedPageUtils.RESPONSE_SAVE_SUCCESS+"=true" )) ;
						}

				}
        	
    		} else {
    			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
    			return super.showForm(request, response, errors);
    		}
    	}	
    }
}