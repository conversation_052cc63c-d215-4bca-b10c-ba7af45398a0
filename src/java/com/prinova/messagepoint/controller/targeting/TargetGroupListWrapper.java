package com.prinova.messagepoint.controller.targeting;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class TargetGroupListWrapper  implements Serializable {
	private static final long serialVersionUID = -1064952439841799755L;
	
	private List<Long>				selectedIds;
	private String 					actionValue;
	private String					whereUsedReportId 	= DateUtil.timeStamp();
	
	public TargetGroupListWrapper(){
		super();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}	
	
	public List<TargetGroup> getSelectedList(){
		List<TargetGroup> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(TargetGroup.class, selectedId));
		}
		return selectedList;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public String getWhereUsedReportId() {
		return whereUsedReportId;
	}

	public void setWhereUsedReportId(String whereUsedReportId) {
		this.whereUsedReportId = whereUsedReportId;
	}

	public static class TargetGroupVO{
		private boolean 		selectedForAction;		
		private TargetGroup		targetGroup;	
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}		
		
		public TargetGroup getTargetGroup(){
			return this.targetGroup;
		}
		
		public void setTargetGroup(TargetGroup targetGroup){
			this.targetGroup = targetGroup;
		}				
	}

}
