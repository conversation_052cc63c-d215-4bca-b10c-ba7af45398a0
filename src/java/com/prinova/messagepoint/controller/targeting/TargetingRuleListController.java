package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.admin.VariableUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteTargetingRulesService;
import com.prinova.messagepoint.platform.services.whereused.CreateWhereUsedReportService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TargetingRuleListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(TargetingRuleListController.class);
	
	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_DOCUMENTID 		= "documentId";
	
	public static final int ACTION_DELETE 				= 2;
	public static final int ACTION_WHERE_USED 			= 3;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		referenceData.put("contextParms", VariableUtils.getContextURLParms(request));
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request){
		
		return new TargetingRuleListWrapper();
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		TargetingRuleListWrapper command = (TargetingRuleListWrapper) commandObj;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
			case ACTION_DELETE:{
				// Delete the targeting rule(s)
				List<ConditionElement> list = command.getSelectedList(); 
				List<Document> docList = new ArrayList<>();
				String names = "";
				for(ConditionElement element: list){
					if(element != null) {
						names = names.concat(list.indexOf(element)==0?element.getName():", " +element.getName());
						for(Document document : element.getDocumentsWithVisibility()){
		    				if(!docList.contains(document)){
		    					docList.add(document);
		    				}
		    			}
					}
				}
				ServiceExecutionContext context = BulkDeleteTargetingRulesService.createContext(list);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteTargetingRulesService.SERVICE_NAME,
						BulkDeleteTargetingRulesService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteTargetingRulesService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(ConditionElement rule: list){
						sb.append(" id ").append(rule.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  working copies not aborted. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					Set<Document> documents = new HashSet<>();
					for(ConditionElement element: list){
						if(element != null) {
							documents.addAll(element.getDocumentsByMappedVariablesUsed());
						}
					}

					// Audit (Audit rule deletion)
					AuditEventUtil.push(requestor, AuditEventType.ID_ASSET_CHANGE, AuditObjectType.ID_TARGET_RULE, names.substring(0, names.length()>15?15:names.length()), null, AuditActionType.ID_CHANGE_DELETED, names, documents);
					
					// Set TPContentChanged to true
					for(Document document : docList){
	    				if(!document.isTpContentChanged()){
	    					document.setTpContentChanged(true);
	    					document.save();
	    				}
	    			}
					return new ModelAndView(new RedirectView(getSuccessView()));
				}					
			}
			case ACTION_WHERE_USED:{
				long reportId 					= Long.valueOf(command.getWhereUsedReportId());
				ConditionElement targetingRule 	= command.getSelectedList().get(0);

				ServiceExecutionContext context = CreateWhereUsedReportService.createContext(reportId, targetingRule.getId(), ConditionElement.class, false, new String[]{}, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateWhereUsedReportService.SERVICE_NAME, CreateWhereUsedReportService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateWhereUsedReportService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" Where used report could not be generated. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
		}
		return null;
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) {
		Map<String, Object> parms = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);
		if (documentId != -1L) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			parms.put(REQ_PARM_DOCUMENTID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
		}

		return parms;
	}
	
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_DOCUMENT_ID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;

		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), false);
		}
		// ******* End User context persist/recall **************

		// Audit (Page access: Target rules list)
		AuditEventUtil.push(AuditEventType.ID_PAGE_ACCESS, AuditObjectType.ID_NONE, null, null, AuditActionType.ID_TARGET_RULES_LIST, null);		
		return super.showForm(request, response, errors);
	}	
}
