package com.prinova.messagepoint.controller.targeting;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.targeting.TargetGroup;

public class TargetGroupTouchpointAssignmentWrapper implements Serializable{

	private static final long serialVersionUID = -7803111526523517665L;

	private Set<Long>				selectedTargetGroupIds = new HashSet<>();
	private List<Document>			touchpointAssignments = new ArrayList<>();

	public TargetGroupTouchpointAssignmentWrapper(){
		super();
	}
	
	public TargetGroupTouchpointAssignmentWrapper(Set<Long> selectedTargetGroupIds){
		super();
		this.selectedTargetGroupIds = selectedTargetGroupIds;
		
		// Single action
		if(this.selectedTargetGroupIds.size()==1){	
			TargetGroup targetGroup = TargetGroup.findById(this.selectedTargetGroupIds.iterator().next());
			this.touchpointAssignments.addAll(targetGroup.getDocuments());
		}
	}

	public Set<Long> getSelectedTargetGroupIds() {
		return selectedTargetGroupIds;
	}

	public void setSelectedTargetGroupIds(Set<Long> selectedTargetGroupIds) {
		this.selectedTargetGroupIds = selectedTargetGroupIds;
	}

	public List<Document> getTouchpointAssignments() {
		return touchpointAssignments;
	}
	public void setTouchpointAssignments(List<Document> touchpointAssignments) {
		this.touchpointAssignments = touchpointAssignments;
	}

}
