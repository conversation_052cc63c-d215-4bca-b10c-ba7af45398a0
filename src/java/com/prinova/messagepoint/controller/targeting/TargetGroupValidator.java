package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.DataComparison;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.DataSubtype;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.apache.commons.logging.Log;
import org.springframework.validation.Errors;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TargetGroupValidator extends MessagepointInputValidator {
	private static final Log log = LogUtil.getLog(TargetGroupValidator.class);

    public void validateNotGenericInputs(Object command, Errors errors) {
    	TargetGroupWrapper targetGroupWrapper = (TargetGroupWrapper)command;
    	
		if(targetGroupWrapper.getTouchpointAssignments() == null || targetGroupWrapper.getTouchpointAssignments().isEmpty()){
			errors.reject("error.message.must.select.at.least.one.touchpoint");
			return;
		}		
		
    	List<TargetGroup> existingTargetGroups = TargetGroup.findAll();
    	for (TargetGroup currentTargetGroup: existingTargetGroups)
    		if ( currentTargetGroup.getName().equals(targetGroupWrapper.getNameText().trim()) &&
    			 currentTargetGroup.getId() != targetGroupWrapper.getTargetGroupId()) {
    			errors.rejectValue("nameText", "error.message.namemustbeunique");
    			break;
    		}
    	    	
		validateConditionElementSelected( targetGroupWrapper, errors, false);

		if(targetGroupWrapper.getTargetGroup()!=null) {
			// New touchpoint assignments must contain the visibility of the current target group
			Set<Document> touchpointsBeingUsedIn = targetGroupWrapper.getTargetGroup().getTouchpointsBeingUsedIn();
			if (!new HashSet<>(targetGroupWrapper.getTouchpointAssignments()).containsAll(touchpointsBeingUsedIn)) {
				errors.reject("error.message.target.group.visibility.does.not.match");
				return;
			}
		}

		// All assigned touchpoints match data model for applied rules/target groups
		Set<Document> errorTouchpoints = new HashSet<>();
		for(Document doc : targetGroupWrapper.getTouchpointAssignments()){
			for(ConditionElement rule : targetGroupWrapper.getConditionElements()){
				boolean checkPrimaryDataSource = false;
				for (DataSource ds : rule.getDataSources())
				{
					if (ds.isReferenceDataSource())
					{
						List<DataSource> referenceDataSources = doc.getReferenceDataSources();
						referenceDataSources.retainAll(rule.getDataSources());
						if (referenceDataSources.isEmpty())
						{
							errorTouchpoints.add(doc);
						}
					}
					else
					{
						checkPrimaryDataSource = true;
					}
				}
				if(checkPrimaryDataSource && doc.getPrimaryDataSource()!= null && !rule.getDataSources().contains(doc.getPrimaryDataSource())){
					errorTouchpoints.add(doc);
				}
			}
		}
		if (!errorTouchpoints.isEmpty())
			errors.reject("error.message.target.group.touchpoints.not.associated",
					new Object[]{getTouchpointNames(errorTouchpoints)}, "error.message.target.group.touchpoints.not.associated");
		log.debug("Done inside TargetGroupValidator.validate() with errors.hasErrors() returning '" + errors.hasErrors() + "'.");
    }
    
    @SuppressWarnings("unchecked")
    public static void validateConditionElementSelected( TargetGroupWrapper targetGroupWrapper, Errors errors, boolean isObjectTargetingUsed) {
    	
    	if ( (targetGroupWrapper.getConditionElements() == null) || (targetGroupWrapper.getConditionElements().isEmpty()) || (!targetGroupWrapper.isContainsConditionElements() && !isObjectTargetingUsed)) {
	    	log.debug("Rejecting command object 'messageTargetingWrapper' due to null or empty elementValues Map.");
    		errors.reject("error.message.mustselectrule");
    		return;
    	}
   	
		for( ConditionElement conditionElement : targetGroupWrapper.getConditionElements() ) {
			boolean subElementSelected = false;
			boolean requiresValue = !targetGroupWrapper.isRuleValueUserSpecified(conditionElement.getId());
			for( ConditionSubelement conditionSubelement : conditionElement.getSubElements()) {
				if( conditionSubelement == null ) continue;
				Set<Long> selectedSubelements = targetGroupWrapper.getConditionSubelementMap().get(conditionElement.getId() );
    			if( selectedSubelements != null && !selectedSubelements.isEmpty() ) {
    				if( selectedSubelements.contains( ""+conditionSubelement.getId() ) || selectedSubelements.contains( conditionSubelement.getId() ) ) { 
    					subElementSelected |= true;
    					
    					if( conditionSubelement.isParameterized() && requiresValue ) {
							String label = conditionElement.getName()+ " : "+ conditionSubelement.getName()+ " value";
    						// Validate that there is a value
    						Object value = targetGroupWrapper.getConditionSubelementValues().get( conditionSubelement.getId() );
    						if( value instanceof String ) {
    		            		TargetGroupValidator.validate( label, ConditionElementUtil.parseDataElementValueString(value.toString()), 
																conditionSubelement.getDataElementComparisonId(), conditionSubelement.getDataElementVariable(), conditionSubelement.getDataFilePath(), -1L, errors);
    							
    						} else if (value instanceof Map) {
    							Map<String, String> map = (Map<String, String>)value;
    							validate(label, map, conditionSubelement.getDataElementComparisonId(), conditionSubelement.getDataElementVariable(), conditionSubelement.getDataFilePath(), -1L, errors );  
    						}
    					}
    				}
    			}
			}
			if( !subElementSelected ) {
	    		errors.reject("error.message.must.select.condition.for.rule", new String[] {conditionElement.getName() != null && !conditionElement.getName().isEmpty() ? conditionElement.getName() : ApplicationUtil.getMessage("page.label.new.condition")}, null);
			}
		}

		if (!DataModelUtil.consditionElementsDataModelCompatible(targetGroupWrapper.getConditionElements())) {
			errors.reject("error.message.condition.elements..not.data.model.compatible");
		}

    }
    
	public static void validate( String label, Map<String, String> map, long dataElementComparisonId, DataElementVariable dataElementVariable, String dataFilePath, Long sandboxFileId, Errors errors ) {
		String stringRestrictedCharsList = ApplicationUtil.getMessage("page.text.validator.alphanum.symbols.space");
		String stringRestrictedCharsRegex = StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\p{Sc}\\s_\\-'\\*!\\.\\$\\+=\\?,/:\\\\;@#\\[\\]\\(\\)<>%/{}~^&|`\\\"]*+");
		String numericRestrictedCharsList = ApplicationUtil.getMessage("page.label.numeric");
		String numericRestrictedCharsRegex = "^[0-9]*+";
		
		DataComparison dataComparison = HibernateUtil.getManager().getObject( DataComparison.class, dataElementComparisonId );
		if( dataComparison.getScheme() == null || dataComparison.getScheme().isEmpty()) {
			// Comparator doesn't require any field values.
			return;
		}

		if( map == null ) {
			MessagepointInputValidationUtil.validateStringValue(label, "", true, 1, 255, stringRestrictedCharsList, stringRestrictedCharsRegex, errors);
			return;
		}
		
		if ( dataElementVariable == null ) {
			errors.reject("error.variable.selection.required", new String[] {label}, label + " requires a variable selection.");
			return;
		}
		
		if ( ((map.get("dataElementValue") != null && map.get("dataElementValue").indexOf(ConditionElementUtil.VARIABLE_REFERENCE_DATA_VALUE) != -1) && 
			  (map.get(ConditionElementUtil.VARIABLE_ID) != null && map.get(ConditionElementUtil.VARIABLE_ID).equals("0"))) ||
			 dataComparison.getScheme().equals( "Advanced" ) && 
			 (map.get("dataElementValue2") != null && map.get("dataElementValue2").indexOf(ConditionElementUtil.VARIABLE_REFERENCE_DATA_VALUE) != -1) && 
			 (map.get(ConditionElementUtil.VARIABLE_ID2) != null && map.get(ConditionElementUtil.VARIABLE_ID2).equals("0"))) {
			errors.reject("error.condition.variable.not.selected", new String[] {label}, label + " has not selected a variable for comparison.");
		} else if ( (dataComparison.getId() == DataComparison.COMPARISON_ISONEOF || dataComparison.getId() == DataComparison.COMPARISON_ISNOTONEOF) 
				&& map.get("dataElementValue").indexOf(ConditionElementUtil.DATA_FILE_MARKER) != -1 ) {
			// VALIDATE: DATA FILE
			if ( (dataFilePath == null || dataFilePath.trim().isEmpty()) && (sandboxFileId == null || !(sandboxFileId > 0)) )
				errors.reject("error.upload.file.is.empty", new String[] {label}, label + " requires a data file to be uploaded.");
		} else if ( dataElementVariable != null && dataElementVariable.getDataSubtypeId() != DataSubtype.DATA_SUBTYPE_DATE ) {
			if( dataComparison.getScheme().equals( "Simple" ) 
					|| dataComparison.getScheme().equals( "Advanced" ) ) {
				MessagepointInputValidationUtil.validateStringValue(label, map.get("dataElementValue"), true, 1, 4000, stringRestrictedCharsList, stringRestrictedCharsRegex, errors);
			}
			if( dataComparison.getScheme().equals( "Advanced" ) ) {
				MessagepointInputValidationUtil.validateStringValue(label, map.get("dataElementValue2"), true, 1, 4000, stringRestrictedCharsList, stringRestrictedCharsRegex, errors);
			}
		} else {
			if( dataComparison.getScheme().equals( "Simple" ) || dataComparison.getScheme().equals( "Advanced" ) ) {
				if( map.get("dateDataValue") == null || map.get("dateDataValue").trim().isEmpty()) {
					errors.reject("error.input.mandatory", new String[] {label}, label + " is mandatory");
				} else {
					String dateDataValue = map.get("dateDataValue");	
					if( dateDataValue.equals("SpecificDate") ) {
						String startDate = map.get("specificStartDate");
						if( startDate == null || startDate.trim().isEmpty()) {
							errors.reject("error.input.mandatory", new String[] {label}, label + " is mandatory");
						}
					} else {
						if (map.containsKey("operator")) {
							MessagepointInputValidationUtil.validateStringValue(label, map.get("number"), true, 1, 5, numericRestrictedCharsList, numericRestrictedCharsRegex, errors);	
						}
					}
				}
			}
			if( dataComparison.getScheme().equals( "Advanced" ) ) {
				if( map.get("dateDataValue2") == null || map.get("dateDataValue2").isEmpty()) {
					errors.reject("error.input.mandatory", new String[] {label}, label + " is mandatory");
				} else {
					String dateDataValue = map.get("dateDataValue2").trim();	
					if( dateDataValue.equals("SpecificDate") ) {
						String endDate = map.get("specificEndDate");
						if( endDate == null || endDate.trim().isEmpty()) {
							errors.reject("error.input.mandatory", new String[] {label}, label + " is mandatory");
						}
					} else {
						if (map.containsKey("operator2")) {
							MessagepointInputValidationUtil.validateStringValue(label, map.get("number2"), true, 1, 5, numericRestrictedCharsList, numericRestrictedCharsRegex, errors);
						}
					}
				}
			}
		}
	}

	private String getTouchpointNames(Set<Document> docs) {
		StringBuilder touchpointNames = new StringBuilder();
		int count = 0;
		for (Document doc : docs) {
			touchpointNames.append(doc.getName());
			if (count < docs.size() - 1) {
				touchpointNames.append(", ");
			}
			count++;
		}
		return touchpointNames.toString();
	}
}
