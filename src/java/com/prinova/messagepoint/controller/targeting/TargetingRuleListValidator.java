package com.prinova.messagepoint.controller.targeting;

import java.util.List;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TargetingRuleListValidator extends MessagepointInputValidator  {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TargetingRuleListWrapper wrapper = (TargetingRuleListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		if ( action == TargetingRuleListController.ACTION_DELETE ) {
			List<ConditionElement> selectedrules = wrapper.getSelectedList();
			StringBuilder referencedTgNames = new StringBuilder();
			boolean referenced = false;
			
			for (ConditionElement currentRule: selectedrules)
				if ( currentRule.isReferenced() ) {
					referenced = true;
					referencedTgNames.append(currentRule.getName()).append(" ");
				}

			if (referenced)
				errors.reject(	"error.message.cannot.delete.referenced.targetingrules", 
								new String[] {referencedTgNames.toString()},
								"The following targeting rule(s) are referenced and cannot be deleted: " + referencedTgNames);
		}
	}
}
