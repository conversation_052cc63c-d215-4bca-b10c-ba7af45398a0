package com.prinova.messagepoint.controller.targeting;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.HibernateUtil;

public class TargetingRuleListWrapper  implements Serializable {
	private static final long serialVersionUID = 4268721552273822570L;
	
	private List<Long>				selectedIds;
	private String 					actionValue;
	private String					whereUsedReportId 	= DateUtil.timeStamp();
	
	public TargetingRuleListWrapper(){
		super();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}	
	
	public List<ConditionElement> getSelectedList(){
		List<ConditionElement> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(ConditionElement.class, selectedId));
		}
		return selectedList;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public String getWhereUsedReportId() {
		return whereUsedReportId;
	}
	public void setWhereUsedReportId(String whereUsedReportId) {
		this.whereUsedReportId = whereUsedReportId;
	}

	public static class TargetingRuleVO{
		private boolean 			selectedForAction;		
		private ConditionElement	targetingRule;	
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}		
		
		public ConditionElement getTargetingRule(){
			return this.targetingRule;
		}
		
		public void setTargetingRule(ConditionElement targetingRule){
			this.targetingRule = targetingRule;
		}				
	}

}
