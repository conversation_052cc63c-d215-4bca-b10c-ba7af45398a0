package com.prinova.messagepoint.controller.targeting;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;

import com.prinova.messagepoint.controller.admin.ConditionElementEditController;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class TargetingRuleListDetailController extends MessagepointController {

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long elementid = ServletRequestUtils.getLongParameter(request, ConditionElementEditController.PARAMETER_ELEMENT_ID, -1L);
		
		ConditionElement rule = HibernateUtil.getManager().getObject(ConditionElement.class, elementid);
		params.put("rule", rule);

		return new ModelAndView(getFormView(), params);
	}	
}
