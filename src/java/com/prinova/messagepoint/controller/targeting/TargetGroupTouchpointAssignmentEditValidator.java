package com.prinova.messagepoint.controller.targeting;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.util.DataModelUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TargetGroupTouchpointAssignmentEditValidator extends MessagepointInputValidator {

	public boolean supports(Class clazz) {
		return clazz.equals(TargetGroupTouchpointAssignmentWrapper.class);
	}
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		TargetGroupTouchpointAssignmentWrapper wrapper = (TargetGroupTouchpointAssignmentWrapper) commandObj;

		List<TargetGroup> targetGroups = new ArrayList<>();
		for( Long currentId : wrapper.getSelectedTargetGroupIds() ) {
			TargetGroup currentTargetGroup = TargetGroup.findById(currentId);
			if (currentTargetGroup != null) {
				targetGroups.add(currentTargetGroup);
			}
		}
		
		// Bulk selected target groups must be in the same data model
		if (targetGroups.size() > 1 && !DataModelUtil.targetGroupsDataModelCompatible(targetGroups)) {
    		errors.reject("error.message.target.groups.not.data.model.compatible");
    		return;
    	}
		
		// At least one touchpoint has been assigned
		if(wrapper.getTouchpointAssignments() == null || wrapper.getTouchpointAssignments().isEmpty()){
			errors.reject("error.message.no.touchpoints.selected");
			return;
		}

		SystemPropertyManager spm = SystemPropertyManager.getInstance();
		boolean bypassTouchpointAssociationValidation = Boolean.parseBoolean(spm.getSystemProperty(SystemPropertyKeys.Data.KEY_BypassTouchpointAssociationValidation));

		if (!bypassTouchpointAssociationValidation) {
			for (TargetGroup tg : targetGroups) {
				// New touchpoint assignments must contain the visibility of the current target group
				Set<Document> touchpointsBeingUsedIn = tg.getTouchpointsBeingUsedIn();
				if (!touchpointsBeingUsedIn.isEmpty() && !new HashSet<>(wrapper.getTouchpointAssignments()).containsAll(touchpointsBeingUsedIn)) {
					errors.reject("error.message.target.group.visibility.does.not.match");
					return;
				}

				// All assigned touchpoints match data model for applied rules/target groups
				Set<DataSource> dataSources = tg.getDataSources();
				Set<Document> errorTouchpoints = new HashSet<>();
				for (Document doc : wrapper.getTouchpointAssignments()) {
					boolean checkPrimaryDataSource = false;
					for (DataSource ds : dataSources) {
						if (ds.isReferenceDataSource()) {
							List<DataSource> referenceDataSources = doc.getReferenceDataSources();
							referenceDataSources.retainAll(dataSources);
							if (referenceDataSources.isEmpty()) {
								errorTouchpoints.add(doc);
							}
						} else {
							checkPrimaryDataSource = true;
						}
					}
					if (checkPrimaryDataSource && doc.getPrimaryDataSource() != null && !dataSources.contains(doc.getPrimaryDataSource())) {
						errorTouchpoints.add(doc);
					}
				}
				if (!errorTouchpoints.isEmpty())
					errors.reject("error.message.target.group.touchpoints.not.associated",
							new Object[]{getTouchpointNames(errorTouchpoints)}, "error.message.target.group.touchpoints.not.associated");
			}
		}
	}

	private String getTouchpointNames(Set<Document> docs) {
		StringBuilder touchpointNames = new StringBuilder();
		int count = 0;
		for (Document doc : docs) {
			touchpointNames.append(doc.getName());
			if (count < docs.size() - 1) {
				touchpointNames.append(", ");
			}
			count++;
		}
		return touchpointNames.toString();
	}
}
