package com.prinova.messagepoint.controller.targeting;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.Factory;
import org.apache.commons.collections.list.LazyList;

import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import com.prinova.messagepoint.util.ApplicationUtil;

public class TargetingWrapper implements Serializable {

	private static final long serialVersionUID = -964284603512017157L;

	public static final String INCLUDED_TARGETING_MAP_KEY = "1";
	public static final String EXTENDED_TARGETING_MAP_KEY = "2";
	public static final String EXCLUDED_TARGETING_MAP_KEY = "3";

	private String[] mapIndex = {INCLUDED_TARGETING_MAP_KEY, EXTENDED_TARGETING_MAP_KEY, EXCLUDED_TARGETING_MAP_KEY};
	
	private Targetable model;
	private Map<String, List<TargetGroupWrapper>> targetGroupsMap = new HashMap<>();
	private Set<TargetGroup> selectedTargetGroups = new HashSet<>();
	private Map<String, Boolean> targetGroupRelationship = new HashMap<>();
	
	// Audit
	private Map<String, List<TargetGroupWrapper>> orgMap = new HashMap<>();
	private Map<String, Boolean> orgTargetGroupRelationship = new HashMap<>();
	
	
	@SuppressWarnings("unchecked")
	public TargetingWrapper(Targetable targetableModel, boolean lazyList, boolean referenceCheck) {
		this.model = targetableModel;

		List<TargetGroupWrapper> includedList = null;
		List<TargetGroupWrapper> excludedList = null;
		List<TargetGroupWrapper> extendedList = null;
		
		List<TargetGroupWrapper> orgIncludedList = null;
		List<TargetGroupWrapper> orgExcludedList = null;
		List<TargetGroupWrapper> orgExtendedList = null;
		
		if (lazyList) {
	    	includedList = LazyList.decorate(new ArrayList<TargetGroupWrapper>(), new Factory() {
	            public Object create() {
	                return new TargetGroupWrapper();
	            }
	        });
	    	excludedList = LazyList.decorate(new ArrayList<TargetGroupWrapper>(), new Factory() {
	            public Object create() {
	                return new TargetGroupWrapper();
	            }
	        });
	    	extendedList = LazyList.decorate(new ArrayList<TargetGroupWrapper>(), new Factory() {
	            public Object create() {
	                return new TargetGroupWrapper();
	            }
	        });
	    	orgIncludedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getIncludedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper wrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					orgIncludedList.add(wrapper);
				}
			}
			orgExcludedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getExcludedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper orgWrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					orgExcludedList.add(orgWrapper);
				}
			}
			
			orgExtendedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getExtendedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper orgWrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					orgExtendedList.add(orgWrapper);
				}
			}
		} else {
			includedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getIncludedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper wrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					includedList.add(wrapper);
					selectedTargetGroups.add(targetGroup);
				}
			}
			excludedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getExcludedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper wrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					excludedList.add(wrapper);
					selectedTargetGroups.add(targetGroup);
				}
			}
			extendedList = new ArrayList<>();
			for (TargetGroup targetGroup : targetableModel.getExtendedTargetGroups()) {
				if (targetGroup != null)
				{
					TargetGroupWrapper wrapper = new TargetGroupWrapper(targetGroup.getId(), targetableModel, referenceCheck);
					extendedList.add(wrapper);
					selectedTargetGroups.add(targetGroup);
				}
			}
		}
		
		targetGroupsMap = new HashMap<>();
		targetGroupsMap.put(INCLUDED_TARGETING_MAP_KEY, includedList);
		targetGroupsMap.put(EXCLUDED_TARGETING_MAP_KEY, excludedList);
		targetGroupsMap.put(EXTENDED_TARGETING_MAP_KEY, extendedList);
		
		orgMap = new HashMap<>();
		orgMap.put(INCLUDED_TARGETING_MAP_KEY, orgIncludedList);
		orgMap.put(EXCLUDED_TARGETING_MAP_KEY, orgExcludedList);
		orgMap.put(EXTENDED_TARGETING_MAP_KEY, orgExtendedList);
		
		targetGroupRelationship = new HashMap<>();
		targetGroupRelationship.put(INCLUDED_TARGETING_MAP_KEY, targetableModel.getIncludedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
		targetGroupRelationship.put(EXCLUDED_TARGETING_MAP_KEY, targetableModel.getExcludedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
		targetGroupRelationship.put(EXTENDED_TARGETING_MAP_KEY, targetableModel.getExtendedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
		
		orgTargetGroupRelationship = new HashMap<>();
		orgTargetGroupRelationship.put(INCLUDED_TARGETING_MAP_KEY, targetableModel.getIncludedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
		orgTargetGroupRelationship.put(EXCLUDED_TARGETING_MAP_KEY, targetableModel.getExcludedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
		orgTargetGroupRelationship.put(EXTENDED_TARGETING_MAP_KEY, targetableModel.getExtendedTargetGroupRelationship() == Targetable.RELATION_ALLOF ? true: false);
	}
	
	public Map<String,String> getTableLabel() {
		Map<String,String> tableLabelMap = new HashMap<>();
		tableLabelMap.put(INCLUDED_TARGETING_MAP_KEY, ApplicationUtil.getMessage("page.label.include"));
		tableLabelMap.put(EXTENDED_TARGETING_MAP_KEY, ApplicationUtil.getMessage("page.label.and.include"));
		tableLabelMap.put(EXCLUDED_TARGETING_MAP_KEY, ApplicationUtil.getMessage("page.label.exclude"));
		return tableLabelMap;
	}
	
	public Map<String,String> getTableIndicator() {
		Map<String,String> tableIndMap = new HashMap<>();
		tableIndMap.put(INCLUDED_TARGETING_MAP_KEY, "incl");
		tableIndMap.put(EXTENDED_TARGETING_MAP_KEY, "ext");
		tableIndMap.put(EXCLUDED_TARGETING_MAP_KEY, "excl");
		return tableIndMap;
	}
	
	public Targetable getModel() {
		return model;
	}

	public void setModel(Targetable model) {
		this.model = model;
	}

	public List<TargetGroupWrapper> getIncludedList() {
		return this.getTargetGroupsMap().get(TargetingWrapper.INCLUDED_TARGETING_MAP_KEY);
	}
	
	public List<TargetGroupWrapper> getExcludedList() {
		return this.getTargetGroupsMap().get(TargetingWrapper.EXCLUDED_TARGETING_MAP_KEY);
	}
	
	public List<TargetGroupWrapper> getExtendedList() {
		return this.getTargetGroupsMap().get(TargetingWrapper.EXTENDED_TARGETING_MAP_KEY);
	}
	
	public List<TargetGroup> getAllTargetGroupsList() {
		List<TargetGroupWrapper> allTargetGroupWrappersList = new ArrayList<>();
		allTargetGroupWrappersList.addAll(this.getIncludedList());
		allTargetGroupWrappersList.addAll(this.getExcludedList());
		allTargetGroupWrappersList.addAll(this.getExtendedList());

		List<TargetGroup> allTargetGroupsList = new ArrayList<>();
		for (TargetGroupWrapper targetGroupWrapper : allTargetGroupWrappersList) {
			if (targetGroupWrapper != null && targetGroupWrapper.getTargetGroup() != null) {
				allTargetGroupsList.add(targetGroupWrapper.getTargetGroup());
			}
		}
		return allTargetGroupsList;
	}

	public TargetGroupWrapper getIncludedTargetGroupWrapper(TargetGroup targetGroup) {
		if (targetGroup != null)
		{
			List<TargetGroupWrapper> includedList = this.getTargetGroupsMap().get(INCLUDED_TARGETING_MAP_KEY);
			if (includedList != null) {
				for (TargetGroupWrapper includedTargetGroupWrapper : includedList) {
					if (includedTargetGroupWrapper != null && includedTargetGroupWrapper.getTargetGroupId() == targetGroup.getId()) {
						return includedTargetGroupWrapper;
					}
				}
			}
		}
		return null;
	}
	
	public TargetGroupWrapper getExcludedTargetGroupWrapper(TargetGroup targetGroup) {
		if (targetGroup != null)
		{
			List<TargetGroupWrapper> excludedList = this.getTargetGroupsMap().get(EXCLUDED_TARGETING_MAP_KEY);
			if (excludedList != null) {
				for (TargetGroupWrapper excludedTargetGroupWrapper : excludedList) {
					if (excludedTargetGroupWrapper != null && excludedTargetGroupWrapper.getTargetGroupId() == targetGroup.getId()) {
						return excludedTargetGroupWrapper;
					}
				}
			}
		}
		return null;
	}
	
	public TargetGroupWrapper getExtendedTargetGroupWrapper(TargetGroup targetGroup) {
		if (targetGroup != null)
		{
			List<TargetGroupWrapper> extendedList = this.getTargetGroupsMap().get(EXTENDED_TARGETING_MAP_KEY);
			if (extendedList != null) {
				for (TargetGroupWrapper extendedTargetGroupWrapper : extendedList) {
					if (extendedTargetGroupWrapper != null && extendedTargetGroupWrapper.getTargetGroupId() == targetGroup.getId()) {
						return extendedTargetGroupWrapper;
					}
				}
			}
		}
		return null;
	}

	public Set<TargetGroup> getSelectedTargetGroups() {
		return selectedTargetGroups;
	}
	public void setSelectedTargetGroups(Set<TargetGroup> selectedTargetGroups) {
		this.selectedTargetGroups = selectedTargetGroups;
	}

	public Map<String, List<TargetGroupWrapper>> getTargetGroupsMap() {
		return targetGroupsMap;
	}
	public void setTargetGroupsMap(Map<String, List<TargetGroupWrapper>> targetGroupsMap) {
		this.targetGroupsMap = targetGroupsMap;
	}

	public String[] getMapIndex() {
		return mapIndex;
	}
	public void setMapIndex(String[] mapIndex) {
		this.mapIndex = mapIndex;
	}

	public Map<String, Boolean> getTargetGroupRelationship() {
		return targetGroupRelationship;
	}

	public void setTargetGroupRelationship(Map<String, Boolean> targetGroupRelationship) {
		this.targetGroupRelationship = targetGroupRelationship;
	}
	
	public boolean getIncludedTargetGroupRelationship(){
		return this.targetGroupRelationship.get(INCLUDED_TARGETING_MAP_KEY);
	}
	public boolean getExcludedTargetGroupRelationship(){
		return this.targetGroupRelationship.get(EXCLUDED_TARGETING_MAP_KEY);
	}
	public boolean getExtendedTargetGroupRelationship(){
		return this.targetGroupRelationship.get(EXTENDED_TARGETING_MAP_KEY);
	}

	public Map<String, List<TargetGroupWrapper>> getOrgMap() {
		return orgMap;
	}

	public void setOrgMap(Map<String, List<TargetGroupWrapper>> orgMap) {
		this.orgMap = orgMap;
	}

	public Map<String, Boolean> getOrgTargetGroupRelationship() {
		return orgTargetGroupRelationship;
	}

	public void setOrgTargetGroupRelationship(Map<String, Boolean> orgTargetGroupRelationship) {
		this.orgTargetGroupRelationship = orgTargetGroupRelationship;
	}
}