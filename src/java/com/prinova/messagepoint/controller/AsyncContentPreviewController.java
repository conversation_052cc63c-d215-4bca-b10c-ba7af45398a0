 package com.prinova.messagepoint.controller;

 import com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewController;
 import com.prinova.messagepoint.controller.content.ContentVO;
 import com.prinova.messagepoint.controller.touchpoints.SelectionStatusFilterType;
 import com.prinova.messagepoint.model.*;
 import com.prinova.messagepoint.model.admin.MessagepointLocale;
 import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
 import com.prinova.messagepoint.model.attachment.Attachment;
 import com.prinova.messagepoint.model.communication.Communication;
 import com.prinova.messagepoint.model.communication.CommunicationProof;
 import com.prinova.messagepoint.model.content.ContentObject;
 import com.prinova.messagepoint.model.content.ContentObjectAssociation;
 import com.prinova.messagepoint.model.font.DecimalValueUtil;
 import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
 import com.prinova.messagepoint.model.task.Task;
 import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
 import com.prinova.messagepoint.platform.services.content.ContentObjectContentSelectionVO;
 import com.prinova.messagepoint.tag.layout.TxtFmtTag;
 import com.prinova.messagepoint.util.ApplicationLanguageUtils;
 import com.prinova.messagepoint.util.ApplicationUtil;
 import com.prinova.messagepoint.util.HibernateUtil;
 import com.prinova.messagepoint.util.LogUtil;
 import com.prinova.messagepoint.util.RedisUtil;
 import com.prinova.messagepoint.util.UserUtil;
 import org.apache.commons.lang.StringEscapeUtils;
 import org.apache.commons.logging.Log;
 import org.json.JSONArray;
 import org.json.JSONException;
 import org.json.JSONObject;
 import org.jsoup.Jsoup;
 import org.springframework.web.bind.ServletRequestUtils;
 import org.springframework.web.servlet.ModelAndView;
 import org.springframework.web.servlet.mvc.Controller;

 import javax.servlet.ServletOutputStream;
 import javax.servlet.http.HttpServletRequest;
 import javax.servlet.http.HttpServletResponse;
 import java.text.ParseException;
 import java.util.*;

 /**
  *
  * Prinova Inc. 1998-2011
  * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
  * All rights reserved.
  *
  * AsyncContentPreviewController
  *
  * @since 4.0
  * <AUTHOR> Team
  */
public class AsyncContentPreviewController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentPreviewController.class);
	
	public static final String REQ_PARAM_CONTENT_TYPE 								= "contentType";
	public static final String REQ_PARAM_CONTENT_ITEM_ID 							= "contentItemId";
    public static final String REQ_PARAM_OBJECT_DNA                                 = "objectDna";
    public static final String REQ_PARAM_LOCALE_ID		 							= "localeId";
	private static final String REQ_PARAM_VARIANT_ITEM_ID 							= "variantId";
	private static final String REQ_PARAM_PREPROOF_ID	 							= "preProofId";
	private static final String REQ_PARAM_COMMUNICATION_ID	 						= "communicationId";
	public static final String REQ_PARAM_STATUS_VIEW_ID								= "statusViewId";
    public static final String REQ_PARAM_STATUS_FILTER_ID							= "statusFilterId";
	public static final String REQ_PARAM_CONTEXT									= "context";
	public static final String REQ_PARAM_VIEW_TYPE									= "viewType";
    public static final String REQ_PARAM_GET_STYLES									= "getStyles";

	public static final String CONTENT_TYPE_EMBEDDED_TEXT							= "embeddedText";
	public static final String CONTENT_TYPE_EMBEDDED_TEXT_RENDERING					= "embeddedTextRendering";
	public static final String CONTENT_TYPE_LOCAL_CONTENT_RENDERING					= "localContentRendering";
	public static final String CONTENT_TYPE_COMMUNICATION_EMBEDDED_TEXT_RENDERING	= "communicationEmbeddedTextRendering";
	public static final String CONTENT_TYPE_COMMUNICATION_TEMPLATE					= "communicationEmbeddedTextTemplate";
	public static final String CONTENT_TYPE_CONTENT_LIBRARY							= "contentLibrary";
	public static final String CONTENT_TYPE_LOCAL_CONTENT_LIBRARY					= "localContentLibrary";
    public static final String CONTENT_TYPE_CONTENT_OBJECT						    = "contentObject";
	public static final String CONTENT_TYPE_PLACEHOLDER								= "placeholder";
    public static final String CONTENT_TYPE_TASK_EDIT_CONTENT_INFO					= "taskEditContentInfo";
    public static final String CONTENT_TYPE_TASK_LIST_CONTENT_INFO					= "taskListContentInfo";
    public static final String CONTENT_TYPE_ATTACHMENT_MGT_DETAILS				    = "attachmentMgtDetails";
	public static final String VIEW_TYPE_DEFAULT									= "default";
	public static final String VIEW_TYPE_LIST										= "list";


	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		if (getContentType(request) != null) {
			
			// JSON Content Response
			response.setContentType("application/json");
			ServletOutputStream out = response.getOutputStream();
	
			try {
				out.write(getResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for content preview for content type: " + getContentType(request) + ": " + e.getMessage(), e);
			}
			
		}
		return null;
	}
	
	private String getResponseJSON (HttpServletRequest request) {
        long contentItemId 			= getContentItemId(request);
        String objectDna            = getObjectDna(request);
        long variantItemId 			= getVariantItemId(request);
        long documentId 			= getDocumentIdParam(request);
        long selectionStatusId 		= getSelectionStatusIdParam(request);
        long touchpointSelectionId 	= getTouchpointSelectionIdParam(request);
        long zoneId 				= getZoneIdParam(request);
        boolean isActiveStatusView 	= getIsActiveView(request);
        int statusFilterId          = getStatusFilterId(request);
        boolean isTestContext		= getIsTestContext(request);
        String viewType				= getViewType(request);
        boolean isGetStyles         = getIsGetStyles(request);

        String returnStr = null;
        try {

            JSONObject refData = getReferenceData(request);
            if ( refData != null ) {
                refData.put("is_test_context", isTestContext);
                RedisUtil.getRedisContext().putValue("connected_reference_data_" + UserUtil.getPrincipalUserId(), refData.toString());
            }
            long localeId = getLocaleId(request);
            String contentType = getContentType(request);
            MessagepointLocale locale = MessagepointLocale.findById(localeId);

            returnStr = getEmbeddedContentJSON(contentItemId, objectDna, variantItemId, documentId, selectionStatusId, touchpointSelectionId, zoneId, refData, locale, contentType, isActiveStatusView, statusFilterId, isTestContext, viewType, isGetStyles);

        } catch (JSONException e) {
            log.error("Error - Unable to set text context in connected refernce data: " + e.getMessage(), e);
        }

        return returnStr;
	}

    public static String getEmbeddedContentJSON(long contentItemId, String objectDna, long variantId, MessagepointLocale locale, String contentType) {
        return getEmbeddedContentJSON(contentItemId, objectDna, variantId, -1L, SelectionStatusFilterType.ID_ACTIVE, -1L, -1L, null, locale, contentType, true, 2, false, VIEW_TYPE_DEFAULT, true);
    }

    public static String getEmbeddedContentJSON(long contentItemId, String objectDna, long variantItemId, long documentId, long selectionStatusId, long touchpointSelectionId, long zoneId,
    											JSONObject refData, MessagepointLocale locale, String contentType, Boolean isActiveStatusView, int statusFilterId, Boolean isTestContext, String viewType, Boolean isGetStyles) {

        JSONObject returnObj = new JSONObject();
        if (contentType.equals(CONTENT_TYPE_TASK_EDIT_CONTENT_INFO) || contentType.equals(CONTENT_TYPE_TASK_LIST_CONTENT_INFO)) {
            Task task = Task.findById(contentItemId);
            try {
                IdentifiableMessagePointModel model = task.getItem();
                if (model instanceof RationalizerContent) {
                    returnObj.put( "name"						, task.getItemName() );

                    JSONArray contentArray = new JSONArray();
                    JSONObject contentObj = new JSONObject();
                    returnObj.put( "content_item_id",((RationalizerContent) model).getId()  );
                    contentObj.put( "is_rationalizer_content"		, true );
                    String markupContent = ((RationalizerContent) model).getMarkupContent();
                    contentObj.put( "text_content"				, markupContent != null  ?
                            markupContent : ApplicationUtil.getMessage("page.label.none") );
                    if(contentType.equals(CONTENT_TYPE_TASK_LIST_CONTENT_INFO)) {
                        contentObj.put("requirement"	, task.getRequirement() != null && task.getRequirement().length != 0 ?
                                                new String(task.getRequirement(), "UTF-8").trim() :
                                                ApplicationUtil.getMessage("page.label.none") );
                    }
                    contentArray.put(contentObj);
                    returnObj.put("contents", contentArray);

                    return returnObj.toString();
                }
            } catch (Exception e) {
                returnObj.put("error"	, true);
                returnObj.put("message"	, e.getMessage());

            }
        }
        if (contentType.equals(CONTENT_TYPE_EMBEDDED_TEXT) ||
                contentType.equals(CONTENT_TYPE_EMBEDDED_TEXT_RENDERING) ||
                contentType.equals(CONTENT_TYPE_COMMUNICATION_EMBEDDED_TEXT_RENDERING) ||
                contentType.equals(CONTENT_TYPE_COMMUNICATION_TEMPLATE) ||
                contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_RENDERING) ) {

            // START: Type: EMBEDDED TEXT
            ContentObject contentObject         = null;
            if ( contentItemId > 0 ) {
                if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                    contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                    contentObject = ContentObject.findByIdArchivedDataFocusCentric(contentItemId);
                else {
                    if (selectionStatusId != 2)
                        contentObject = ContentObject.findByIdWorkingDataFocusCentric(contentItemId);
                    else
                        contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                }
                objectDna = contentObject.getDna();
            } else if ( !objectDna.isEmpty()) {
                Document touchpointContext = null;
                if(contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_RENDERING)){
                    touchpointContext = UserUtil.getCurrentTouchpointContext();
                }
                if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                    contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna, touchpointContext);
                else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                    contentObject = ContentObject.findByDnaArchivedDataFocusCentric(objectDna, touchpointContext);
                else {
                    if (selectionStatusId != 2)
                        contentObject = ContentObject.findByDnaWorkingDataFocusCentric(objectDna, touchpointContext);
                    else
                        contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna, touchpointContext);
                }
            } else {
                return "{error: 'Content Object could not be found'}";
            }

            if ( contentObject == null ) {
                return "{error: 'Content Object could not be found'}";
            }

            Map<Long, ContentVO> masterContent = new HashMap<>();
           
            boolean sameAsParent = false;
            boolean hasProductionContent = true;
            long principalUserId = UserUtil.getPrincipalUserId();

            try {

            	if ( contentObject.isGlobalSmartText() || contentObject.isLocalSmartText() ) {
                    if (contentObject.hasActiveData()) {
                        contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
                    } else {
                        contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                    }
            		if ( contentObject.isStructuredContentEnabled() ) {
            			TouchpointSelection selection = null;
            			if ( variantItemId > 0 || variantItemId == -9 ) {
            				if ( variantItemId == -9 )
            					selection = contentObject.getFirstDocumentDelivery().getMasterTouchpointSelection();
            				else
            					selection = TouchpointSelection.findById( variantItemId) ;
            			} else {
            				selection = UserUtil.getCurrentSelectionContext(contentObject);
            			}
                        masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), -1L, true, selection.getParameterGroupTreeNode());
            		} else if ( variantItemId > 0 && contentObject.isDynamicVariantEnabled() ) {
	                	ContentObjectContentSelectionVO viewingContent;
                        ParameterGroupTreeNode p = HibernateUtil.getManager().getObject(ParameterGroupTreeNode.class, variantItemId);
	                    masterContent = ContentObjectDynamicVariantViewController.getMasterContent(contentObject);

                        if (contentObject.isStructuredContentEnabled() && selectionStatusId > 0 && statusFilterId != ContentObject.DATA_TYPE_ARCHIVED && (!contentObject.isVariantType() || contentObject.getOwningTouchpointSelection().getId() != touchpointSelectionId)) {
                            if (selectionStatusId == SelectionStatusFilterType.ID_ACTIVE) {
                                contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
                            } else {
                                contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                            }
                        }

                        if(p != null) {
                            viewingContent = ContentObjectContentSelectionVO.mapRegularSelectionContent(contentObject, p, principalUserId, masterContent);
                        }else{
                            viewingContent = ContentObjectContentSelectionVO.mapFromMasterContent(contentObject, masterContent);
                        }
                        masterContent = viewingContent.getLangContentMap();

                        sameAsParent = viewingContent.isSameAsParent();
	                    returnObj.put("has_active_content", contentObject.hasActiveData());
	                    hasProductionContent = contentObject.hasActiveData();
	                } else {
	                    masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType());
	                }
            	}

                if (masterContent != null && !masterContent.isEmpty()) {
                    ContentVO currentContent = masterContent.get(locale.getId()); // get the content for the current locale
                    if ( currentContent != null ) {

                        returnObj.put( "name"						, StringEscapeUtils.escapeXml( contentObject.getName() ) );

                        if ( isGetStyles ) {
                            returnObj.put("text_style_css_path", contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getCSSFilename() : null) : contentObject.getCSSFilename());
                            returnObj.put("text_style_data", contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getTextStyleData() : null) : contentObject.getTextStyleData());
                            returnObj.put("paragraph_style_css_path", contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getParagraphCSSFilename() : null) : contentObject.getParagraphCSSFilename());
                            returnObj.put("paragraph_style_data", contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getParagraphStyleData() : null) : contentObject.getParagraphStyleData());
                            returnObj.put("list_style_css_path", contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getListCSSFilename() : null) : contentObject.getListCSSFilename());
                        }

                        returnObj.put( "default_style_css_path"		, contentObject.isLocalSmartText() ? (!viewType.equals(VIEW_TYPE_LIST) ? contentObject.getDefaultEditorCSSFilePath() : null) : contentObject.getDefaultViewCSSFilePath() );
                        returnObj.put( "content_type"				, contentType );
                        returnObj.put( "dna"			            , objectDna );
                        returnObj.put( "locale_id"				    , currentContent.getLocaleId());
                        returnObj.put( "is_compound_smart_text"		, contentObject.isCompoundContentObject() );
                        returnObj.put( "is_block_content"			, contentObject.isInsertAsBlockContent() );
                        returnObj.put( "object_type"                , contentObject.getObjectType() );

                        JSONArray contentArray = new JSONArray();
                        JSONObject contentObj = new JSONObject();

                        if (currentContent.getEncodedContent() == null) {
                            currentContent.setEncodedContent(ContentObjectContentUtil.translateContentForPersistance(currentContent.getContent()));
                        }

                        if (contentType.equals(CONTENT_TYPE_COMMUNICATION_TEMPLATE) || contentType.equals(CONTENT_TYPE_COMMUNICATION_EMBEDDED_TEXT_RENDERING) || refData != null ) {
                            String contextualContent = currentContent.getContent();
                            // contextualContent = ContentObjectContentUtil.translateContentForPersistance(contextualContent);
                            // contextualContent = ContentObjectContentUtil.translateContentForView(contextualContent, UserUtil.getCurrentTouchpointContext(), MessagepointLocale.getDefaultLanguageLocale(langCode), refData, msgInst);
                            contextualContent = (currentContent.getEncodedContent() != null ? StringEscapeUtils.escapeXml(  ContentObjectContentUtil.translateCommunicationsContentForView(contextualContent, contentObject) ) : "");

                            contentObj.put( "text_content"				, contextualContent );
                            contentObj.put( "content_id"                , currentContent.getContentId() );
                        } else if (!contentType.equals(CONTENT_TYPE_EMBEDDED_TEXT_RENDERING) && !contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_RENDERING) ) {
                            contentObj.put( "text_content"				, currentContent.getEncodedContent() != null ? StringEscapeUtils.escapeXml(currentContent.getContent()) : "");
                            contentObj.put( "content_id"                , currentContent.getContentId() );
                        } else {
                            String textContent = (currentContent.getEncodedContent() != null ?
                                    ContentObjectContentUtil.cleanDOMForView(
                                            ContentObjectContentUtil.translateEmbeddedContentForView(Jsoup.parse(currentContent.getContent() != null ? currentContent.getContent() : ""), contentObject.isVariableContentEnabled() ? contentObject.getDocument() : null, contentObject, MessagepointLocale.findById(currentContent.getLocaleId())) )
                                    : "");
                            if ( currentContent.getEncodedContent() == null && contentObject.isDynamicVariantEnabled() )
                                textContent = contentObject.getName() + " (" + ApplicationUtil.getMessage("page.text.no.default.content") + ")";

                            textContent = 	ContentObjectContentUtil.applyPresentationTags(textContent, contentObject);

                            contentObj.put( "text_content"				, StringEscapeUtils.escapeXml(textContent) );
                            contentObj.put( "content_id"                , currentContent.getContentId() );

                            if ( sameAsParent )
                                contentObj.put("parent_text_content", "parent content");

                        }
                        
                        // Instance ID
                        long instId = 0L;
                        float canvasTrimWidth = 0f;
                        float canvasTrimHeight = 0f;
                		try {
	                        if ( contentObject != null ) {
	                        	instId = contentObject.getId();
	                        	if ( contentObject.getIsSharedFreeform() ) {
									canvasTrimWidth = Float.valueOf( DecimalValueUtil.dehydrate(contentObject.getCanvasTrimWidth()) );
									canvasTrimHeight = Float.valueOf( DecimalValueUtil.dehydrate(contentObject.getCanvasTrimHeight()) );
	                        	}
	                        }
						} catch (ParseException e) {
							e.printStackTrace();
						}

                        contentObj.put( "is_same_as_system_default"	    , contentObject.isGlobalContentObject() && currentContent.isSameAsDefault() );
                        contentObj.put( "is_same_as_touchpoint_default" , !contentObject.isGlobalContentObject() && currentContent.isSameAsDefault() );
                        contentObj.put( "is_empty_content"			    , currentContent.getContent() == null || currentContent.getContent().trim().isEmpty());
                        contentObj.put( "is_same_as_parent"			    , sameAsParent);
                        contentObj.put( "is_suppressed"				    , currentContent.isSuppress());
                        contentObj.put( "has_production_content"	    , hasProductionContent);
                        contentObj.put( "inst_id"					    , instId);
                        contentObj.put( "canvas_trim_width"			    , canvasTrimWidth);
                        contentObj.put( "canvas_trim_height"		    , canvasTrimHeight);

                        contentArray.put(contentObj);
                        returnObj.put("contents", contentArray);

                    }
                }

            } catch (JSONException e) {
                log.error("Error - Smart text content retrieval failed: "+e.getMessage(),e);
                return "{error: 'Smart Text content could not be retrieved'}";
            }

            return returnObj.toString();
            // START: Type: EMBEDDED TEXT

        } else if (contentType.equals(CONTENT_TYPE_CONTENT_LIBRARY) || contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_LIBRARY) ) {
            // START: Type: LOCAL CONTENT LIBRARY
            ContentObject contentObject = null;
            if ( contentItemId > 0 ) {
                if (contentType.equals(CONTENT_TYPE_CONTENT_LIBRARY) || contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_LIBRARY)) {
                    if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                        contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                    else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                        contentObject = ContentObject.findByIdArchivedDataFocusCentric(contentItemId);
                    else {
                        if (selectionStatusId != 2)
                            contentObject = ContentObject.findByIdWorkingDataFocusCentric(contentItemId);
                        else
                            contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                    }
                }
            } else if ( !objectDna.isEmpty()) {
                if (contentType.equals(CONTENT_TYPE_CONTENT_LIBRARY) || contentType.equals(CONTENT_TYPE_LOCAL_CONTENT_LIBRARY)) {
                    if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                        contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna);
                    else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                        contentObject = ContentObject.findByDnaArchivedDataFocusCentric(objectDna);
                    else {
                        if (selectionStatusId != 2)
                            contentObject = ContentObject.findByDnaWorkingDataFocusCentric(objectDna);
                        else
                            contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna);
                    }
                }
            } else {
                return "{error: 'Local Image Library could not be found'}";
            }

            Map<Long, ContentVO> masterContent = new HashMap<>();
            if ( contentObject.isStructuredContentEnabled() ) {
    			TouchpointSelection selection = UserUtil.getCurrentSelectionContext(contentObject);
                masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), -1L, true, selection.getParameterGroupTreeNode());
    		} else if ( variantItemId > 0 ) {
                ContentObjectContentSelectionVO viewingContent;
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantItemId);
                masterContent = ContentObjectDynamicVariantViewController.getMasterContent(contentObject);

                if (contentObject.isStructuredContentEnabled() && selectionStatusId > 0 && statusFilterId != ContentObject.DATA_TYPE_ARCHIVED && (!contentObject.isVariantType() || contentObject.getOwningTouchpointSelection().getId() != touchpointSelectionId)) {
                    if (selectionStatusId == SelectionStatusFilterType.ID_ACTIVE) {
                        contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
                    } else {
                        contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                    }
                }

                viewingContent = ContentObjectContentSelectionVO.mapRegularSelectionContent(contentObject, pgtn, UserUtil.getPrincipalUserId(), masterContent);
                masterContent = viewingContent.getLangContentMap();
            } else {
                masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType());
            }

            if (masterContent != null && !masterContent.isEmpty()) {
                ContentVO currentContent = masterContent.get(locale.getId()); // get the content for the current locale
                if ( currentContent != null ) {
                    try {
                        returnObj.put( "name"						, StringEscapeUtils.escapeXml( contentObject.getName() ) );
                        returnObj.put( "content_type"				, contentType );
                        returnObj.put( "dna"			            , objectDna );
                        returnObj.put( "locale_id"				    , currentContent.getLocaleId() );
                        returnObj.put( "object_type"                , contentObject.getObjectType() );

                        JSONArray contentArray = new JSONArray();
                        JSONObject contentObj = new JSONObject();
                        contentObj.put( "graphic_path"				    , currentContent.getImageLocation() );
                        contentObj.put( "is_same_as_system_default"	    , contentObject.isGlobalContentObject() && currentContent.isSameAsDefault() );
                        contentObj.put( "is_same_as_touchpoint_default" , !contentObject.isGlobalContentObject() && currentContent.isSameAsDefault() );
                        contentObj.put( "is_empty_content"			    , currentContent.getImageLocation() == null );
                        contentObj.put( "is_image_library_reference"    , currentContent.isUseImageLibrary() );
                        if ( currentContent.isUseImageLibrary() )
                            contentObj.put( "image_library_name"	    , StringEscapeUtils.escapeXml( TxtFmtTag.maxTxtLengh(currentContent.getImageLibraryName(), 30, false) ) );
                        contentArray.put(contentObj);
                        returnObj.put("contents", contentArray);
                    } catch (JSONException e) {
                        log.error("Error - Image Library content retrieval failed: "+e.getMessage(),e);
                        return "{error: 'Image Library content could not be retrieved'}";
                    }
                }
            }

            return returnObj.toString();
            // END: Type: LOCAL CONTENT LIBRARY	
 
        } else if ( contentType.equals(CONTENT_TYPE_CONTENT_OBJECT) ) {

            // START: Type: CONTENT OBJECT
            ContentObject contentObject = null;
            if ( contentItemId > 0 ) {
                if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                    contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                    contentObject = ContentObject.findByIdArchivedDataFocusCentric(contentItemId);
                else {
                    if (selectionStatusId != 2)
                        contentObject = ContentObject.findByIdWorkingDataFocusCentric(contentItemId);
                    else
                        contentObject = ContentObject.findByIdActiveDataFocusCentric(contentItemId);
                }
            } else if ( !objectDna.isEmpty()) {
                if (statusFilterId == ContentObject.DATA_TYPE_ACTIVE)
                    contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna);
                else if (statusFilterId == ContentObject.DATA_TYPE_ARCHIVED)
                    contentObject = ContentObject.findByDnaArchivedDataFocusCentric(objectDna);
                else {
                    if (selectionStatusId != 2)
                        contentObject = ContentObject.findByDnaWorkingDataFocusCentric(objectDna);
                    else
                        contentObject = ContentObject.findByDnaActiveDataFocusCentric(objectDna);
                }
            } else {
                return "{error: 'Content Object could not be found'}";
            }

            if ( contentObject == null ) {
                return "{error: 'Content Object could not be found'}";
            }

            Document document = Document.findById(documentId);
            if (document == null)
                document = UserUtil.getCurrentTouchpointContext();

            TouchpointSelection selection = TouchpointSelection.findById(touchpointSelectionId);

            Zone targetZone = null;
            if ( contentObject != null && contentObject.getIsTouchpointLocal() && !contentObject.isDeliveredToPlaceholder() ) {
                document = contentObject.getDocument();
            } else {
                // Resolve first matching delivery for Touchpoint
                targetZone = Zone.findById(zoneId);
                if (targetZone == null) {
                    Zone currentZone = contentObject.getZone();
                    if (currentZone != null && currentZone.getDocument().getId() == document.getId())
                        targetZone = currentZone;
                }

                // Message doesn't match document context: Apply first delivery
                if (targetZone == null && contentObject.getZone() != null) {
                    targetZone = contentObject.getZone();
                    document = targetZone.getDocument();
                }
            }

            if (contentObject.isStructuredContentEnabled() && selectionStatusId > 0 && statusFilterId != ContentObject.DATA_TYPE_ARCHIVED && (!contentObject.isVariantType() || contentObject.getOwningTouchpointSelection().getId() != touchpointSelectionId)) {
                if (selectionStatusId == SelectionStatusFilterType.ID_ACTIVE) {
                    contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_ACTIVE);
                } else {
                    contentObject.setFocusOnDataType(ContentObject.DATA_TYPE_WORKING);
                }
            }

            List<ContentVO> currentContents = ContentObjectAssociation.getMasterOrTPVariantContent(contentObject, contentObject.getFocusOnDataType(), -1L, true, selection != null && !selection.isMaster() ? selection.getParameterGroupTreeNode() : null, locale);

            try {
                returnObj.put( "name"						, StringEscapeUtils.escapeXml( contentObject.getName() ) );

                if ( isGetStyles ) {
                    returnObj.put("text_style_css_path", contentObject.getCSSFilename());
                    returnObj.put("paragraph_style_css_path", contentObject.getParagraphCSSFilename());
                    returnObj.put("list_style_css_path", contentObject.getListCSSFilename());
                    returnObj.put("text_style_data", contentObject.getTextStyleData());
                    returnObj.put("paragraph_style_data", contentObject.getParagraphStyleData());
                }

                returnObj.put("default_style_css_path", contentObject.getDefaultViewCSSFilePath());
                returnObj.put( "channel"					, contentObject.getDocument() != null ? contentObject.getDocument().getConnectorConfiguration().getConnector().getChannel().getName() : "Unknown" );
                returnObj.put( "connector"					, contentObject.getDocument() != null ? contentObject.getDocument().getConnectorConfiguration().getConnector().getName() : "Unknown" );
                returnObj.put( "content_type"				, contentType );
                returnObj.put( "dna"			            , objectDna );
                returnObj.put( "locale_id"				    , locale.getId() );
                returnObj.put( "is_block_content"			, contentObject.isInsertAsBlockContent() );
                returnObj.put( "object_type"                , contentObject.getObjectType() );

                JSONArray contentArray = new JSONArray();
                for (ContentVO contentVO: currentContents ) {

                    if ( contentVO == null )
                        continue;

                    JSONObject contentObj = new JSONObject();
                    contentObj.put( "content_name"						, StringEscapeUtils.escapeXml( TxtFmtTag.maxTxtLengh(contentVO.getZonePartName(), 40, false) ) );
                    contentObj.put( "sequence"                          , contentVO.getZonePartId() > 0 ? ZonePart.findById(contentVO.getZonePartId()).getSequence() : 0);
                    contentObj.put( "text_content"						, (contentVO.getContent() != null ? StringEscapeUtils.escapeXml(contentVO.getContent()) : "") );
                    contentObj.put( "content_id"                        , contentVO.getContentId() );
                    contentObj.put( "graphic_path"						, contentVO.getImageLocation() );
                    contentObj.put( "is_same_as_system_default"	        , contentObject.isGlobalContentObject() && contentVO.isSameAsDefault() );
                    contentObj.put( "is_same_as_touchpoint_default"     , !contentObject.isGlobalContentObject() && contentVO.isSameAsDefault() );
                    contentObj.put( "is_empty_content"					, (contentVO.getContent() == null || contentVO.getContent().trim().isEmpty()) && contentVO.getImageLocation() == null );
                    contentObj.put( "is_suppressed"						, contentVO.isSuppress() );
                    contentObj.put( "is_reference"						, contentVO.isReference() );
                    contentObj.put( "is_image_library_reference"		, contentVO.isUseImageLibrary() );
                    contentObj.put( "background_color"					, contentVO.getBackgroundColor() );
                    if ( contentVO.isReference() )
                        contentObj.put( "reference_node_name"			, StringEscapeUtils.escapeXml( TxtFmtTag.maxTxtLengh(contentVO.getReferenceNodeName(), 30, false) ) );
                    if ( contentVO.isUseImageLibrary() )
                        contentObj.put( "image_library_name"			, StringEscapeUtils.escapeXml( TxtFmtTag.maxTxtLengh(contentVO.getImageLibraryName(), 30, false) ) );
                    contentObj.put("canvas_size"						, contentVO.getCanvasWidth() );
                    contentArray.put(contentObj);
                }
                if (currentContents.isEmpty()) {
                    JSONObject contentObj = new JSONObject();
                    contentObj.put( "is_empty_content"			, true );
                    contentArray.put(contentObj);
                }

                returnObj.put("contents", intKeySortOnJSONArray(contentArray,"sequence"));
            } catch (JSONException e) {
                log.error("Error - Message content retrieval failed: "+e.getMessage(),e);
                return "{error: 'Message content could not be retrieved'}";
            }

            return returnObj.toString();
            // END: Type: CONTENT OBJECT

        } else if ( contentType.equals(CONTENT_TYPE_ATTACHMENT_MGT_DETAILS) ) {

            // START: Type: Attachment Management Details
            Attachment attachment = Attachment.findById(contentItemId);
            if (attachment == null) {
                return "{error: 'Attachment could not be found'}";
            } else {
                try {
                    returnObj.put( "name"						, attachment.getName());
                    returnObj.put( "attachment_id"				, attachment.getId() );
                    returnObj.put( "content_type"				, contentType );
                    returnObj.put( "locale_id"				    , locale.getId() );

                    JSONArray contentArray = new JSONArray();

                    ComplexValue recipAttachmentName = attachment.getRecipientAttachmentName();
                    if (recipAttachmentName != null) {
                        JSONObject attachmentObj = new JSONObject();
                        attachmentObj.put( "content_id"			, recipAttachmentName.getId() );
                        attachmentObj.put( "content_name"		, ApplicationUtil.getMessage("page.label.recipient.attachment.name") );
                        attachmentObj.put( "text_content"       , recipAttachmentName.getEncodedValue() );
                        contentArray.put(attachmentObj);
                    }

                    ComplexValue recipAttachmentLocation = attachment.getRecipientAttachmentLocation();
                    if (recipAttachmentLocation != null ) {
                        JSONObject attachmentObj = new JSONObject();
                        attachmentObj.put( "content_id"			, recipAttachmentLocation.getId() );
                        attachmentObj.put( "content_name"		, ApplicationUtil.getMessage("page.label.recipient.attachment.location") );
                        attachmentObj.put( "text_content"       , recipAttachmentLocation.getEncodedValue() );
                        contentArray.put(attachmentObj);
                    }

                    returnObj.put("contents", contentArray);
                } catch (JSONException e) {
                    log.error("Error - Attachment content retrieval failed: "+e.getMessage(),e);
                    return "{error: 'Attachment content could not be retrieved'}";
                }
            }

            return returnObj.toString();
            // END: Type: Attachment Management Details

        } else if ( contentType.equals(CONTENT_TYPE_PLACEHOLDER) ) {
            try {
                JSONArray contentArray = new JSONArray();
                JSONObject contentObj = new JSONObject();

                Zone placeholderZone = Zone.findById(contentItemId);
                returnObj.put( "dna"			            , placeholderZone.getDna() );
                contentObj.put( "text_content"				, StringEscapeUtils.escapeXml( ContentObjectContentUtil.translatePlaceholderForView(placeholderZone, locale) ) );
                
                contentArray.put(contentObj);
            	
				returnObj.put("contents", contentArray);
				
				return returnObj.toString();
			} catch (JSONException e) {
                log.error("Error - Placeholder content retrieval failed: "+e.getMessage(),e);
                return "{error: 'Placeholder content could not be retrieved'}";
			}
            
        }

        return "{error: 'Content type not recognized'}";
    }

    private static JSONArray intKeySortOnJSONArray(JSONArray unsortedArray, String intKey) {
        List<JSONObject> jsonValues = new ArrayList<>();
        for (int i = 0; i < unsortedArray.length(); i++) {
            jsonValues.add(unsortedArray.getJSONObject(i));
        }
        Collections.sort( jsonValues, new Comparator<>() {
            @Override
            public int compare(JSONObject a, JSONObject b) {
                if (a.getInt(intKey) == b.getInt(intKey))
                    return 0;
                return a.getInt(intKey) < b.getInt(intKey) ? -1 : 1;
            }
        });
        JSONArray sortedArray = new JSONArray();
        for (int i = 0; i < unsortedArray.length(); i++) {
            sortedArray.put(jsonValues.get(i));
        }
        return sortedArray;
    }

    private String getContentType (HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTENT_TYPE, null);
	}
    private String getViewType (HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_VIEW_TYPE, VIEW_TYPE_DEFAULT);
	}

    private long getLocaleId (HttpServletRequest request) {
        long localeId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID, -1);
        if (localeId == 0)
            localeId = UserUtil.getCurrentLanguageLocaleContext().getId();
        else if (localeId < 0)
            localeId = MessagepointLocale.getDefaultSystemLanguageLocale().getId();
        return localeId;
    }
	
	private Long getContentItemId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_ITEM_ID, -1L);
	}

     private String getObjectDna(HttpServletRequest request) {
         return ServletRequestUtils.getStringParameter(request, REQ_PARAM_OBJECT_DNA, "");
     }

	private Long getVariantItemId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_VARIANT_ITEM_ID, -1L);
	}
	
	private Long getZoneIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, "zoneId", -1L);
	}
	
	private Long getDocumentIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, "documentId", -1L);
	}
	
	private Long getTouchpointSelectionIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);
	}
	
	private Long getSelectionStatusIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, AsyncSectionImageThumbnailController.REQ_PARM_SELECTION_STATUS_ID, -1L);
	}
	
	private JSONObject getReferenceData( HttpServletRequest request ) {
		
		long preProofId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_PREPROOF_ID, -1);
		long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
		Communication communication = Communication.findById(communicationId);
		
		if ( communication != null && communication.isDebugOrder() ) {
			// DEBUG ORDER
			if ( communication.getDebugReferenceData() != null ) {
				try {
					return new JSONObject(communication.getDebugReferenceData());
				} catch (JSONException e) {
					log.error("Async Content Preview: Unable to parse debug reference data parameter value.");
				}
			}
		} else if ( preProofId > 0 ) {
			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			
			if ( preProof.getPreProofReferenceData() != null ) {
				try {
					return new JSONObject(preProof.getPreProofReferenceData());
				} catch (JSONException e) {
					log.error("Async Content Preview: Unable to parse reference data parameter value.");
				}
			}
		}
		return null;
	}
	
	private Boolean getIsActiveView( HttpServletRequest request ) {
		int statusViewId = ServletRequestUtils.getIntParameter(request, REQ_PARAM_STATUS_VIEW_ID, 2);
		return statusViewId == 2;	
	}

    private int getStatusFilterId( HttpServletRequest request ) {
        int statusFilterId = ServletRequestUtils.getIntParameter(request, REQ_PARAM_STATUS_FILTER_ID, ContentObject.DATA_TYPE_WORKING);
        return statusFilterId;
    }

    private Boolean getIsGetStyles( HttpServletRequest request ) {
        return ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_GET_STYLES, true);
    }
	
	private Boolean getIsTestContext( HttpServletRequest request ) {
		long preProofId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_PREPROOF_ID, -1);
		long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
		Communication communication = Communication.findById(communicationId);
		
		if ( communication != null ) {
			return communication.isTestOrder();
		} else if ( preProofId > 0 ) {
			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			return preProof.getCommunication().isTestOrder();
		}
		String context = ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default");
		return context.equalsIgnoreCase("test");	
	}

}