package com.prinova.messagepoint.controller;

import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

public class ServerNamespacePropertyInitializer implements Filter {

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
			ServletException {
		/**
		 * Add system name space properties to the system property table if not already done so.
		 */
		// FB 27373 - disable automatic update for system.server.* properties
		/*if (request instanceof HttpServletRequest) {
			Service service = MessagepointServiceFactory.getInstance()
					.lookupService(ServerNamespacePropertyUpdateService.SERVICE_NAME,
							ServerNamespacePropertyUpdateService.class);
			ServiceExecutionContext context = ServerNamespacePropertyUpdateService.createContext(request.getServerName(),
					((HttpServletRequest) request).getContextPath(),
					request.getServerPort(),
					request.getScheme());
			service.execute(context);
		}*/

		chain.doFilter(request, response);
	}

	public void destroy() {
	}

	public void init(FilterConfig arg0) throws ServletException {
	}

}
