package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.security.SecureFileUpload;
import com.prinova.messagepoint.security.antivirus.AllowedFileExtensionException;
import com.prinova.messagepoint.security.antivirus.AntiVirusUtil;
import com.prinova.messagepoint.security.antivirus.AntiVirusViolationException;
import com.prinova.messagepoint.security.antivirus.DisallowedFileExtensionException;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class FileUploadSecurityFilter implements Filter {
    public void destroy() {
    }

    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws ServletException, IOException {

        boolean doNextFilter = true;

        String allowExtension = SystemPropertyManager.getInstance().getDcsSystemProperty(SystemPropertyKeys.UserInterface.KEY_AllowUploadFileTypes);
        String filterExtension = SystemPropertyManager.getInstance().getDcsSystemProperty(SystemPropertyKeys.UserInterface.KEY_DisallowUploadFileTypes);

        List<String> allowExtensions = null;
        List<String> filterExtensions = null;

        if (allowExtension != null && !allowExtension.isEmpty()) {
            allowExtensions = Arrays.stream(allowExtension.split(",")).map(x -> ".".concat(x.toLowerCase().trim())).collect(Collectors.toList());
        }

        if (filterExtension != null) {
            filterExtensions = Arrays.stream(filterExtension.split(",")).map(x -> ".".concat(x.toLowerCase().trim())).collect(Collectors.toList());
        }

        MessagepointHttpServletRequestWrapper request = (MessagepointHttpServletRequestWrapper) req;

        CommonsMultipartResolver resolver = new CommonsMultipartResolver();

        try {
            if (resolver.isMultipart(request)) {
                MultipartHttpServletRequest multipartRequest = resolver.resolveMultipart(request);

                if (!multipartRequest.getFileMap().isEmpty()) {
                    for (String key : multipartRequest.getFileMap().keySet()) {
                        MultipartFile file = multipartRequest.getFile(key);

                        if (file == null) {
                            continue;
                        }

                        String fileName = file.getResource().getFilename();

                        if (allowExtensions != null && !StringUtils.isEmpty(fileName)) {
                            boolean isAllowed = false;

                            for (String extension : allowExtensions) {
                                if (fileName.toLowerCase().endsWith(extension)) {
                                    isAllowed = true;
                                    break;
                                }
                            }

                            if (!isAllowed) {
                                throw new AllowedFileExtensionException(fileName);
                            }
                        }

                        if (filterExtensions != null && !StringUtils.isEmpty(fileName)) {
                            for (String extension : filterExtensions) {
                                if (fileName.toLowerCase().endsWith(extension)) {
                                    throw new DisallowedFileExtensionException(fileName, extension);
                                }
                            }
                        }
                        if(!SecureFileUpload.canUploadFile(file)){
                            throw new DisallowedFileExtensionException(fileName, "");
                        }

                        if (AntiVirusUtil.isAntiVirusEnabled()) {
                            AntiVirusUtil.scanFileBytes(fileName, file.getBytes());
                        }
                    }
                }

                if (multipartRequest.getRequestURI().contains("source_editor.form") && multipartRequest.getMethod().equals("POST")) {
                    String resourceToken = multipartRequest.getParameter("resource");
                    // Resource token pass from source editor might be id of the database file, skip it in this case
                    boolean isResourceTokenValid = !StringUtils.isNumeric(resourceToken);
                    String updatedSource = multipartRequest.getParameter("source");
                    String path = "";

                    String filename = null;

                    if (resourceToken != null && isResourceTokenValid) {
                        path = HttpRequestUtil.getPathFromFileResourceToken(resourceToken);

                        if (path != null) {
                            filename = Paths.get(path).getFileName().toString();
                        }
                    }

                    if (path == null && StringUtils.isNumeric(resourceToken)) {
                        DatabaseFile databaseFile = DatabaseFile.findById(Long.parseLong(resourceToken));

                        if (databaseFile != null) {
                            filename = databaseFile.getFileName();
                        }
                    }

                    if (AntiVirusUtil.isAntiVirusEnabled() && isResourceTokenValid) {
                        AntiVirusUtil.scanFileBytes(filename != null ? filename : "source_editor", updatedSource.getBytes());
                    }
                }
            }

        } catch (AntiVirusViolationException ex) {
            String errorMessage = MessageFormat.format("Uploaded file: ''{0}'' was rejected due to an anti-virus policy violation.", ex.getFileName());
            String detailsMessage = MessageFormat.format("File tested positive for: ''{0}''", ex.getVirusName());
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorMessage", errorMessage);
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorDetails", detailsMessage);

            ((HttpServletResponse) resp).setStatus(HttpStatus.SC_UNPROCESSABLE_ENTITY);
            RequestDispatcher dispatcher = req.getRequestDispatcher("/unprocessable_entity_error.jsp");
            dispatcher.include(request, resp);
            doNextFilter = false;
        } catch (AllowedFileExtensionException ex) {
            String errorMessage = MessageFormat.format("Uploaded file: ''{0}'' was rejected due to file type policy violation.", ex.getFileName());
            String detailsMessage = MessageFormat.format("File type not allowed: ''{0}''", ex.getFileExtension());
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorMessage", errorMessage);
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorDetails", detailsMessage);

            ((HttpServletResponse) resp).setStatus(HttpStatus.SC_UNPROCESSABLE_ENTITY);
            RequestDispatcher dispatcher = req.getRequestDispatcher("/unprocessable_entity_error.jsp");
            dispatcher.include(request, resp);
            doNextFilter = false;
        } catch (DisallowedFileExtensionException ex) {
            String errorMessage = MessageFormat.format("Uploaded file: ''{0}'' was rejected due to file type policy violation.", ex.getFileName());
            String detailsMessage = StringUtils.isNotBlank(ex.getExtension()) ? MessageFormat.format("File type not allowed: ''{0}''", ex.getExtension()) : "File type not allowed";
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorMessage", errorMessage);
            request.setAttribute("com.prinova.messagepoint.controller.FileUploadSecurityFilter.errorDetails", detailsMessage);

            ((HttpServletResponse) resp).setStatus(HttpStatus.SC_UNPROCESSABLE_ENTITY);
            RequestDispatcher dispatcher = req.getRequestDispatcher("/unprocessable_entity_error.jsp");
            dispatcher.include(request, resp);
            doNextFilter = false;
        }



        if (doNextFilter) {
            chain.doFilter(req, resp);
        }
    }

    public void init(FilterConfig config) throws ServletException {
        try {
            if (AntiVirusUtil.isAntiVirusEnabled()) {
                long start = System.nanoTime();
                AntiVirusUtil.initialize();
                long initMs = (System.nanoTime() - start) / 1000000;
                LogUtil.getLog(FileUploadSecurityFilter.class).info("Initialize AV engine in: " + initMs + "ms");
            }
        } catch (Exception e) {
            LogUtil.getLog(FileUploadSecurityFilter.class).error(e);
        }
    }

}
