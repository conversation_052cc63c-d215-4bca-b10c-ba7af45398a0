package com.prinova.messagepoint.controller;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.platform.services.ServiceMessage;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.util.ApplicationUtil;


/**
 * Utility Class
 */
public class ServiceResponseConverter {

	/**
	 * Converts the servoce error messages and populates the spring {@link Errors}
	 * object so that tha errors can be displayed back to the user.  
	 *
     */
	public static void convertToSpringErrors(ServiceResponse response, Errors errors){
		if(response!=null && errors!=null){
			if(response.hasMessages()){
				for (ServiceMessage sMsg : response.getMessages()) {
					if(sMsg.getErrorArgs()!=null)
						errors.reject(sMsg.getErrCode(), sMsg.getErrorArgs(), sMsg.getDescription());
					else
						errors.reject(sMsg.getErrCode(),  sMsg.getDescription());
				}
			}
		}
	}
	
	/**
	 * Since our services store translated messages in description, 
	 * this will return a string which gets the first message and its description
	 *
     */
	public static String getFirstErrorDescription(ServiceResponse response){
		String msg = "";
		if(response!=null){
			if(response.hasMessages()){
				ServiceMessage sMsg = response.getMessages().iterator().next();
				if(sMsg.getDescription() != null)
					return sMsg.getDescription();
				if(sMsg.getErrorArgs()!=null)
					msg = ApplicationUtil.getMessage(sMsg.getErrCode(), sMsg.getErrorArgs());
				else
					msg = ApplicationUtil.getMessage(sMsg.getErrCode());
			}
		}
		return msg;
	}
}
