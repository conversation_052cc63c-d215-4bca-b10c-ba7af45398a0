package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagepointDeferrable;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.types.SystemEvents;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Map;
import java.util.stream.Collectors;

public class AnalyticsEventEndFilter implements Filter {

    private static HashSet<String> ignoredPaths;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        chain.doFilter(req, res);

        if (!res.isCommitted()) {
            res.flushBuffer();
        }

        MessagepointDeferrable.runAll();

        @SuppressWarnings("unchecked")
        AnalyticsEvent<SystemEvents> analytics = (AnalyticsEvent<SystemEvents>)req.getAttribute(MessagepointStartFilter.ANALYTICS_REQUEST_ATTR);

        if (analytics == null) {
            return;
        }

        String path = UriComponentsBuilder.fromUriString(((HttpServletRequest) req).getRequestURI()).build(false).getPath();

        if (path != null && (!path.toLowerCase().endsWith(".form") || path.toLowerCase().endsWith(".jsp"))) {
            return;
        }

        if (!getIgnoredPaths().contains(path)) {
            analytics.add(SystemEvents.Properties.HttpRequestPath, () -> path);

            String requestParameters = getRequestParameters(req.getParameterMap());

            if (requestParameters != null) {
                analytics.add(SystemEvents.Properties.HttpRequestParameters, () -> requestParameters);
            }

            analytics.send();
        }
    }

    @Override
    public void destroy() {

    }


    private String getRequestParameters(Map<String, String[]> parameters) {
        try {
            StringBuilder requestParams = new StringBuilder();

            boolean appendSeparator = false;

            for (String key : parameters.keySet().stream().filter(x -> !x.equals("tk")).collect(Collectors.toList())) {
                if (appendSeparator) {
                    requestParams.append("&");
                } else {
                    appendSeparator = true;
                }

                requestParams.append(key).append("=").append(String.join(",", parameters.get(key)));
            }

            return requestParams.toString();

        } catch (Exception e) {

        }

        return null;
    }

    private static HashSet<String> getIgnoredPaths() {

        if (ignoredPaths == null) {
            ignoredPaths = new HashSet<>();

            String context = ApplicationUtil.getWebRoot();

            String[] paths = new String[] {
                    "/healthcheck.form",
                    "/download/font.form",
                    "/download/image.form",
                    "/getBackgroundTask.form",
                    "/getJobStatusList.form",
                    "/getListTable.form",
                    "/getObjectInfo.form",
                    "/getProjectTask.form",
                    "/getSectionImageThumbnail.form",
                    "/getSessionKeepAlive.form",
                    "/getTestResultStatus.form",
                    "/getUserLocales.form",
                    "/security/owasp.csrfguard.form",
            };

            for (String path : paths) {
                ignoredPaths.add(Paths.get(context, path).toString());
            }

        }

        return ignoredPaths;
    }
}
