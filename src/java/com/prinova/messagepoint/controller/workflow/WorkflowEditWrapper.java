package com.prinova.messagepoint.controller.workflow;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;

public class WorkflowEditWrapper {

	private User owner;
	private int modelType;
	private int usageType;
	private long documentId;
	private ConfigurableWorkflow workflow;
	private List<WorkflowEditStepVO> approvals = new ArrayList<>();
	private List<Document> documents = new ArrayList<>();
	
	public WorkflowEditWrapper() {
		super();
	}

	public List<WorkflowEditStepVO> getApprovals() {
		return approvals;
	}
	public List<WorkflowEditStepVO> getApprovalsOrdered() {
		List<WorkflowEditStepVO> approvalsOrdered = new ArrayList<>(getApprovals());
		Collections.sort(approvalsOrdered, new WorkflowEditStepVOComparator());
		return approvalsOrdered;
	}
	public void setApprovals(List<WorkflowEditStepVO> approvals) {
		this.approvals = approvals;
	}

	public User getOwner() {
		return owner;
	}

	public void setOwner(User owner) {
		this.owner = owner;
	}
	
	public int getModelType() {
		return modelType;
	}
	public void setModelType(int modelType) {
		this.modelType = modelType;
	}

	public int getUsageType() {
		return usageType;
	}
	
	public String getUsageTypeStr(){
		if(this.usageType > 0){
			return new WorkflowUsageType(this.usageType).getName();
		}else{
			return "";
		}
	}

	public void setUsageType(int usageType) {
		this.usageType = usageType;
	}

	public long getDocumentId() {
		return documentId;
	}

	public void setDocumentId(long documentId) {
		this.documentId = documentId;
	}

	public ConfigurableWorkflow getWorkflow() {
		return workflow;
	}

	public void setWorkflow(ConfigurableWorkflow workflow) {
		this.workflow = workflow;
	}

	public List<Document> getDocuments() {
		return documents;
	}

	public void setDocuments(List<Document> documents) {
		this.documents = documents;
	}
}
