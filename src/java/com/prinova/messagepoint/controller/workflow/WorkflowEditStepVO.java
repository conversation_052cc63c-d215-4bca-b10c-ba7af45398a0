package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.model.admin.FrequencyType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.*;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class WorkflowEditStepVO implements Serializable {
	private static final long serialVersionUID = 4849340869648446014L;
	
	private long order;
	private long stepId;
	private String stepName;
	private List<User> users;
	private boolean dueByChecked;
	private WorkflowDueByType dueByType = new WorkflowDueByType(WorkflowDueByType.ID_DUE_BY_TYPE_NOTIFY);
	private ApprovalType approveType = new ApprovalType(ApprovalType.ID_APPROVAL_TYPE_ANY_OF);
	private Date startDate;
	private Date endDate;
	private FrequencyType frequencyType;
	private boolean enabled;
	private WorkflowStepType stepType = new WorkflowStepType(WorkflowStepType.ID_DEFAULT);
	private TranslatorType translatorType = new TranslatorType(TranslatorType.ID_USER);
	private Set<MessagepointLocale> languages;

	private Set<MessagepointLocale> translationApprovalLanguages;
	private List<User> translators;
	private boolean allowEditDefaultLanguage;
	private boolean isOrderEditable;
	private Set<ConfigurableWorkflow> subworkflows = new HashSet<>();
	private TextStyleTransformationProfile styleTransformProfile;

	private String translationServiceGuid;

	public Long getOrder() {
		return order;
	}
	public void setOrder(Long order) {
		this.order = order;
	}
	public long getStepId() {
		return stepId;
	}
	public void setStepId(long stepId) {
		this.stepId = stepId;
	}
	public String getStepName() {
		return stepName;
	}
	public void setStepName(String stepName) {
		this.stepName = stepName;
	}
	public List<User> getUsers() {
		return users;
	}
	public void setUsers(List<User> users) {
		this.users = users;
	}
	public WorkflowDueByType getDueByType() {
		return dueByType;
	}
	public void setDueByType(WorkflowDueByType dueByType) {
		this.dueByType = dueByType;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public FrequencyType getFrequencyType() {
		return frequencyType;
	}
	public void setFrequencyType(FrequencyType frequencyType) {
		this.frequencyType = frequencyType;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public ApprovalType getApproveType() {
		return approveType;
	}
	public void setApproveType(ApprovalType approveType) {
		this.approveType = approveType;
	}
	public void setOrder(long order) {
		this.order = order;
	}
	public boolean isEnabled() {
		return enabled;
	}
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
	public boolean isDueByChecked() {
		return dueByChecked;
	}
	public void setDueByChecked(boolean dueByChecked) {
		this.dueByChecked = dueByChecked;
	}
	public WorkflowStepType getStepType() {
		return stepType;
	}
	public void setStepType(WorkflowStepType stepType) {
		this.stepType = stepType;
	}
	public Set<MessagepointLocale> getLanguages() {
		return languages;
	}
	public void setLanguages(Set<MessagepointLocale> languages) {
		this.languages = languages;
	}
	public List<User> getTranslators() {
		return translators;
	}
	public void setTranslators(List<User> translators) {
		this.translators = translators;
	}

	public boolean isAllowEditDefaultLanguage() {
		return allowEditDefaultLanguage;
	}
	public void setAllowEditDefaultLanguage(boolean allowEditDefaultLanguage) {
		this.allowEditDefaultLanguage = allowEditDefaultLanguage;
	}
	public boolean getIsOrderEditable() {
		return isOrderEditable;
	}
	public void setIsOrderEditable(boolean isOrderEditable) {
		this.isOrderEditable = isOrderEditable;
	}
	public Set<ConfigurableWorkflow> getSubworkflows() {
		return subworkflows;
	}
	public void setSubworkflows(Set<ConfigurableWorkflow> subworkflows) {
		this.subworkflows = subworkflows;
	}
	public TranslatorType getTranslatorType() {
		return translatorType;
	}
	public void setTranslatorType(TranslatorType translatorType) {
		this.translatorType = translatorType;
	}

	public TextStyleTransformationProfile getStyleTransformProfile() {
		return styleTransformProfile;
	}
	public void setStyleTransformProfile(TextStyleTransformationProfile styleTransformProfile) {
		this.styleTransformProfile = styleTransformProfile;
	}

	public String getTranslationServiceGuid() {
		return translationServiceGuid;
	}

	public void setTranslationServiceGuid(String translationServiceGuid) {
		this.translationServiceGuid = translationServiceGuid;
	}

	public Set<MessagepointLocale> getTranslationApprovalLanguages() {
		return translationApprovalLanguages;
	}

	public void setTranslationApprovalLanguages(Set<MessagepointLocale> translationApprovalLanguages) {
		this.translationApprovalLanguages = translationApprovalLanguages;
	}
}
