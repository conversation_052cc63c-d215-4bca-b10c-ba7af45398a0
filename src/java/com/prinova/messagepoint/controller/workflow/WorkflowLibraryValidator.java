package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.controller.projects.ProjectTaskWorkflowListController;
import com.prinova.messagepoint.controller.tasks.TaskListController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.wrapper.AsyncWorkflowLibraryListVO;
import com.prinova.messagepoint.model.wrapper.AsyncWorkflowLibraryListWrapper;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.List;
import java.util.function.Predicate;

public class WorkflowLibraryValidator extends MessagepointInputValidator {

	public boolean supports(Class clazz) {
		return clazz.equals(WorkflowLibraryWrapper.class);
	}
	
    public void validateNotGenericInputs(Object commandObj, Errors errors) {
    	WorkflowLibraryWrapper command = (WorkflowLibraryWrapper) commandObj;
    	
    	int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.parseInt(command.getActionValue());
		}

		switch (action) {
			case TaskListController.ACTION_EDIT:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags::isCanUpdate);
				break;
			case TaskListController.ACTION_DELETE:
				validateActionPermission(command.getSelectedList(), errors, "error.message.action.not.permitted", AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags::isCanRemove);
				break;
			default:
				break;
		}

		
		if ( action == ProjectTaskWorkflowListController.ACTION_DELETE) {
			List<ConfigurableWorkflow> selecteds = command.getSelectedList();
			StringBuilder referencedWorkflowNames = new StringBuilder();
			boolean referenced = false;
			
			for (ConfigurableWorkflow selected: selecteds)
				if ( selected.isReferenced() ) {
					referenced = true;
					referencedWorkflowNames.append(selected.getName()).append(" ");
				}

			if (referenced)
				errors.reject(	"error.message.cannot.delete.referenced.workflows", 
								new String[] {referencedWorkflowNames.toString()},
								"The following workflow(s) are referenced and cannot be deleted: " + referencedWorkflowNames);
		}
    }

	private void validateActionPermission(List<ConfigurableWorkflow> configurableWorkflowList, Errors errors, String errorMessage, Predicate<AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( configurableWorkflowList.isEmpty() )
			errors.reject(errorMessage);

		for (ConfigurableWorkflow configurableWorkflow : configurableWorkflowList) {
			AsyncWorkflowLibraryListVO vo = new AsyncWorkflowLibraryListVO();
			vo.setWorkflow(configurableWorkflow);

			AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags flags = new AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags();
			AsyncWorkflowLibraryListWrapper.setActionFlags(flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}
}
