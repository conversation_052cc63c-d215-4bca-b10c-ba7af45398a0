package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.workflow.BulkDeleteWorkflowService;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WorkflowLibraryController extends MessagepointController {

	private static final Log log = LogUtil.getLog(WorkflowLibraryController.class);
	
	public static final String REQ_PARM_WORKFLOW_ID				= "workflowId";
	public static final String REQ_PARM_ACTION 					= "action";
	public static final String REQ_PARM_DOCUMENTID 				= "documentId";
	
	public static final int ACTION_EDIT							= 1;
	public static final int ACTION_DELETE		 				= 2;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request){
		Map<String, Object> referenceData = new HashMap<>();

		boolean existsRationalizerApp = !RationalizerApplication.findAll().isEmpty();
		boolean noVisibleTouchpoints = Document.getVisibleDocuments().isEmpty();
		List<WorkflowUsageType> wfUsageTypes = new ArrayList<>();
		if(existsRationalizerApp && noVisibleTouchpoints){
			wfUsageTypes.add(new WorkflowUsageType(WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER));
		}else{
			wfUsageTypes.addAll(WorkflowUsageType.listAll());
		}
		referenceData.put("allWorkflowLibraryUsageTypes", wfUsageTypes);
		referenceData.put("noVisibleTouchpoints", noVisibleTouchpoints);
		referenceData.put("existsRationalizerApp", existsRationalizerApp);
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		return new WorkflowLibraryWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		WorkflowLibraryWrapper c = (WorkflowLibraryWrapper)commandObj;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
		case (ACTION_DELETE):{
			// ACTION_DELETE: - delete the workflow(s)
			List<ConfigurableWorkflow> list = c.getSelectedList(); 
			ServiceExecutionContext context = BulkDeleteWorkflowService.createContext(list, requestor);
			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteWorkflowService.SERVICE_NAME,
					BulkDeleteWorkflowService.class);				
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(BulkDeleteWorkflowService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for(ConfigurableWorkflow workflow: list){
					sb.append(" id ").append(workflow.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				return new ModelAndView(new RedirectView(getSuccessView()));
			}
		}
		default:
			break;
		}
		return null;
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) throws Exception {
		Map<String, Object> parms = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);

		Document tp = Document.findById(documentId);

		if (documentId != -1L && tp != null && tp.getParent() == null) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			Document document = null;

			if (!visibleDocuments.isEmpty()) {
				document  = visibleDocuments.iterator().next();

				while (document.getParent() != null) {
					document = document.getParent();
				}
			}

			parms.put(REQ_PARM_DOCUMENTID, document != null ? document.getId() : -2);
		}

		return parms;
	}
	
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), false);
			if ( contextMismatchView != null )
				return contextMismatchView;
			
		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), true);
		}
		// ******* End User context persist/recall **************
		
		return super.showForm(request, response, errors);
	}
}
