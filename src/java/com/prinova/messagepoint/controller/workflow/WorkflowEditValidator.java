package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.model.workflow.TranslatorType;
import com.prinova.messagepoint.model.workflow.WorkflowManager;
import com.prinova.messagepoint.model.workflow.WorkflowStepType;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;
import com.prinova.messagepoint.model.wrapper.AsyncWorkflowLibraryListVO;
import com.prinova.messagepoint.model.wrapper.AsyncWorkflowLibraryListWrapper;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class WorkflowEditValidator extends MessagepointInputValidator {

	public boolean supports(Class clazz) {
		return clazz.equals(WorkflowEditWrapper.class);
	}
	
    public void validateNotGenericInputs(Object commandObj, Errors errors) {
    	WorkflowEditWrapper command = (WorkflowEditWrapper) commandObj;

		//Check for edit permission
		validateActionPermission(command.getWorkflow(), errors, "error.message.action.not.permitted", AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags::isCanUpdate);

    	User owner = command.getOwner();
    	boolean hasStep = false;
		for (WorkflowEditStepVO approvalVO : command.getApprovals()) {
			int stepType = approvalVO.getStepType().getId();
			if(approvalVO.isEnabled()){
				hasStep = true;
				if(approvalVO.getStepName() == null || approvalVO.getStepName().isEmpty()){
					errors.reject("error.workflow.approvals.step.name.cannot.be.empty");
					break;
				}
				if(stepType == WorkflowStepType.ID_DEFAULT) {	// Approval step
					if (!validateApprovalUsers(approvalVO, owner, errors)) {
						break;
					}
				}else if(stepType == WorkflowStepType.ID_TRANSLATION_APPROVAL){	// Translation Approval step
					// Validate the approval users
					if (!validateApprovalUsers(approvalVO, owner, errors)) {
						break;
					}

					// Validate the locale selections
					if (!validateLanguages(approvalVO, approvalVO.getTranslationApprovalLanguages(), command, errors)) {
						return;
					}
				}
				else if(stepType == WorkflowStepType.ID_TRANSLATION){	// Translation step
					// Validate the locale selections
					if (!validateLanguages(approvalVO, approvalVO.getLanguages(), command, errors)) {
						return;
					}

					// User translation step
					if(approvalVO.getTranslatorType().getId() == TranslatorType.ID_USER){
						if(approvalVO.getTranslators() == null || approvalVO.getTranslators().isEmpty()){
							errors.reject("error.workflow.translator.needed.for.each.workflow.step");
							break;
						}

						if(owner != null && approvalVO.getTranslators().contains(owner)){
							errors.reject("error.workflow.approvals.user.cannot.be.both.owner.and.translator");
							break;
						}
					}else{
						// Match text style transform profile's language with the selected language (Should be only one)
						if(approvalVO.getStyleTransformProfile() != null){
							TextStyleTransformationProfile transformationProfile = approvalVO.getStyleTransformProfile();
							Set<MessagepointLocale> profileLocales = transformationProfile.getLanguages();
							if(!profileLocales.contains(approvalVO.getLanguages().iterator().next())){
								errors.reject("error.workflow.style.transform.profile.language.must.match.selected.language");
								return;
							}
						}
					}
				}else if(stepType == WorkflowStepType.ID_SUBWORKFLOW){	// Sub-workflow step
					if(approvalVO.getSubworkflows().isEmpty()){
						errors.reject("error.workflow.subworkflow.needed.for.each.workflow.step");
						return;
					}
					// Check the cyclical reference
					StringBuilder cyclicalSubworkflowNames = new StringBuilder();
					for(ConfigurableWorkflow subworkflow : approvalVO.getSubworkflows()){
						if(isSubworkflowCyclical(command.getWorkflow(), subworkflow)){
							cyclicalSubworkflowNames.append(subworkflow.getName()).append(" ");
							break;
						}

						ConfigurableWorkflowInstance activeInstance = subworkflow.findActiveInstance();
						if(activeInstance!=null && activeInstance.getWorkflowSteps().isEmpty()){
							errors.reject("error.workflow.subworkflow.need.to.contain.at.lease.one.step");
							return;
						}
					}
					if(!cyclicalSubworkflowNames.isEmpty()){
						errors.reject("error.workflow.subworkflow.cannot.be.cyclical", new String[] {cyclicalSubworkflowNames.toString()}, null);
						return;
					}
				}

				// Validate the permissions associated with the owner or step approvers
				int workflowType = command.getModelType();
				int usageType = command.getUsageType();

				List<User> availUsers = WorkflowManager.findAllAvailApprovers(workflowType, usageType, command.getDocumentId());
				
				if(owner != null && !availUsers.contains(owner)){
					errors.reject("error.workflow.approvals.owner.is.not.authorized");
					break;
				}
				boolean approverAuthorized = true;
				for(User approver : approvalVO.getUsers()){
					if(!availUsers.contains(approver)){
						errors.reject("error.workflow.approvals.approver.is.not.authorized");
						approverAuthorized = false;
						break;						
					}
				}
				if(!approverAuthorized){
					break;
				}
				
				if(approvalVO.isDueByChecked()){
					Date startDate = approvalVO.getStartDate();
					Date endDate = approvalVO.getEndDate();
					if(startDate == null){
						errors.reject( "error.message.mustselectstartdate");
					}

					if ( (startDate != null) && (endDate != null) && endDate.before(startDate) ) {
						errors.reject("error.message.enddatebeforestart");
					}
					if ((startDate != null) && (!DateUtil.isInSQLDateRange(startDate))) {
						errors.reject("error.message.dateoutofrange");
						approvalVO.setStartDate(null);
					}
					if ((endDate != null) && (!DateUtil.isInSQLDateRange(endDate))) {
						errors.reject("error.message.dateoutofrange");
						approvalVO.setEndDate(null);
					}
				}
			}
		}
		
    	if(command.getModelType() == ConfigurableWorkflow.PROJECT_TASK_WORKFLOW){
    		if(command.getWorkflow().getName().isEmpty()){
    			errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
				return;
    		}
    		
    		// Project workflow needs to contain at least one step
    		if(!hasStep){
    			errors.reject("error.workflow.approvals.at.least.one.approval.step.for.project.workflow");
				return;
    		}
    	}
    	
    	if(command.getModelType() == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW){
    		if(command.getWorkflow().getName().isEmpty()){
    			errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
				return;
    		}else{
    			// Unique name
    			List<ConfigurableWorkflow> workflows = ConfigurableWorkflow.findByNameAndModelType(command.getWorkflow().getName(), ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW);
    			for(ConfigurableWorkflow workflow : workflows){
    				if(workflow.getId() != command.getWorkflow().getId()){
    					errors.reject("error.workflow.name.must.be.unique", new String[]{ApplicationUtil.getMessage("page.label.name")}, "");
    					return;
    				}
    			}
    		}
			if(command.getUsageType() != WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER && command.getUsageType() != WorkflowUsageType.ID_USAGE_TYPE_ALL){
				if (command.getDocuments() == null || command.getDocuments().isEmpty()) {
					errors.reject("error.message.touchpoint.must.be.selected");
					return;
				}
			}

			// Validate the touchpoint visibility
			if(command.getUsageType() != WorkflowUsageType.ID_USAGE_TYPE_ALL) {
                List<Document> preAssignedTouchpoints = new ArrayList<>(command.getWorkflow().findAllDocumentsAreReferencing());
				preAssignedTouchpoints.removeAll(command.getDocuments());
				if (!preAssignedTouchpoints.isEmpty()) {
					String tpNames = preAssignedTouchpoints.stream().map(d -> d.getName()).collect(Collectors.joining(", "));
					errors.reject("error.workflow.touchpoints.cannot.be.removed", new String[]{tpNames}, "");
					return;
				}
			}
    	}
		
		// Workflow owner is mandatory for workflow which contains at least one step
		if(hasStep && owner == null){
			errors.reject("error.workflow.approvals.owner.is.mandatory.for.workflow.has.step");
		}
    }

	private boolean validateApprovalUsers(WorkflowEditStepVO approvalVO, User owner, Errors errors){
		if (approvalVO.getUsers() == null || approvalVO.getUsers().isEmpty()) {
			errors.reject("error.workflow.approvals.at.least.one.user.needed.for.each.approval.step");
			return false;
		}

		if(owner != null && approvalVO.getUsers().contains(owner)){
			errors.reject("error.workflow.approvals.user.cannot.be.both.owner.and.approver");
			return false;
		}

		return true;
	}

	private boolean validateLanguages(WorkflowEditStepVO approvalVO, Set<MessagepointLocale> locales, WorkflowEditWrapper command, Errors errors){
		// Validate the locale selections
		if ( locales == null || locales.isEmpty()) {
			errors.reject(approvalVO.getStepType().isTranslation() ? "error.message.language.must.be.selected.for.translation.step" : "error.message.language.must.be.selected.for.translation.approval.step",
					new String[]{approvalVO.getStepName()}, "");
			return false;
		}

		if(command.getUsageType() == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE) {
			List<Long> tpLocaleIds = new ArrayList<>();
			if (command.getDocuments() != null && !command.getDocuments().isEmpty()) {
				for (Document tp : command.getDocuments()) {
					List<MessagepointLocale> tpLocales = tp.getTouchpointLanguagesAsLocales();
					if (tpLocaleIds.isEmpty()) {
						tpLocaleIds.addAll(MessagepointLocale.getLocaleIds(tpLocales));
					} else {
						tpLocaleIds.retainAll(MessagepointLocale.getLocaleIds(tpLocales));
					}
				}
			}

			for (MessagepointLocale locale : locales) {
				if (!tpLocaleIds.isEmpty() && !tpLocaleIds.contains(locale.getId())) {
					errors.reject("error.message.language.must.be.used.in.all.touchponts");
					return false;
				}
			}
		}

		return true;
	}

	private boolean isSubworkflowCyclical(ConfigurableWorkflow currentWorkflow, ConfigurableWorkflow subworkflow){
		for(ConfigurableWorkflowStep step : subworkflow.findActiveInstance().getWorkflowSteps()){
			for(ConfigurableWorkflow subworkflow2 : step.getSubworkflows()){
				if(subworkflow2.getId()==currentWorkflow.getId()){
					return true;
				}
				if(isSubworkflowCyclical(currentWorkflow, subworkflow2)){
					return true;
				}
			}
		}
		return false;
	}

	private void validateActionPermission(ConfigurableWorkflow configurableWorkflow, Errors errors, String errorMessage, Predicate<AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		AsyncWorkflowLibraryListVO vo = new AsyncWorkflowLibraryListVO();
		vo.setWorkflow(configurableWorkflow);

		AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags flags = new AsyncWorkflowLibraryListVO.WorkflowLibraryListVOFlags();
		AsyncWorkflowLibraryListWrapper.setActionFlags(flags);
		if (!flagChecker.test(flags)) {
			errors.reject(errorMessage);
		}
	}

}
