package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.controller.*;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.admin.FrequencyType;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.webservice.Services;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.workflow.CreateOrUpdateWorkflowService;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

public class WorkflowEditController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(WorkflowEditController.class);
	
	public static final String REQ_PARAM_DOCUMENT_ID 						= "documentId";
	public static final String REQ_PARAM_WORKFLOW_ID 						= "workflowId";
	public static final String REQ_PARAM_WORKFLOW_TYPE						= "type";
	public static final String REQ_PARAM_WORKFLOW_USAGE_TYPE				= "usageType";
	public static final String REQ_PARAM_SAVE_SUCCESS 						= "saveSuccess";
	public static final String REQ_PARAM_ACTION 							= "action";
	
	public static final long MAX_NUMBER_OF_APPROVALS 						= 20;
	public static final long UNUSED_APPROVAL_ID 							= -1;
	public static final long UNUSED_APPROVAL_ORDER	 						= -1;

	public static final int ACTION_OVERRIDE_SAVE		 					= 2;

	//private static String SUCCESS_VIEW_MESSAGE_LIST									= "touchpoints/touchpoint_content_object_list.form";
	//private static String SUCCESS_VIEW_SHARED_CONTENT 						= "content/mp_content_list.form";
	private static final String SUCCESS_VIEW_EMBEDDED_CONTENT 					= "content/global_content_list.form?localContentType=1";
	private static final String SUCCESS_VIEW_CONTENT_LIBRARY 					= "content/global_content_list.form?localContentType=2";
	private static final String SUCCESS_VIEW_LOOKUP_TABLE 						= "dataadmin/lookup_table_list.form";
	private static final String SUCCESS_VIEW_PROJECT		 					= "projects/project_task_workflow_list.form";
	private static final String SUCCESS_VIEW_WORKFLOW_LIBRARY					= "workflow/workflow_library.form";
	//private static String SUCCESS_VIEW_COMMUNICATIONS 					= "touchpoints/touchpoint_communications_list.form";
	
	protected Map<String, Object> referenceData(HttpServletRequest request) {
		int workflowType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_TYPE, -1);
		int usageType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_USAGE_TYPE, -1);
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		long workflowId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_WORKFLOW_ID, -1);
		Map<String, Object> referenceData = new HashMap<>();
	
		referenceData.put("availUsers", WorkflowManager.findAllAvailApprovers(workflowType, usageType, documentId));
		
		referenceData.put("document", Document.findById(documentId));

		referenceData.put("isSharedContentWorkflow", workflowType==ConfigurableWorkflow.SHARED_CONTENT_WORKFLOW);
		referenceData.put("isEmbeddedContentWorkflow", workflowType==ConfigurableWorkflow.GLOBAL_SMART_TEXT_WORKFLOW);
		referenceData.put("isContentLibraryWorkflow", workflowType==ConfigurableWorkflow.GLOBAL_IMAGE_WORKFLOW);
		referenceData.put("isLookupTableWorkflow", workflowType==ConfigurableWorkflow.LOOKUP_TABLE_WORKFLOW);
		referenceData.put("isProjectTaskWorkflow", workflowType==ConfigurableWorkflow.PROJECT_TASK_WORKFLOW);
		referenceData.put("isWorkflowLibraryWorkflow", workflowType==ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW);
		referenceData.put("isCommunicationsWorkflow", (workflowType==ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW 
				&& usageType == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED));
		referenceData.put("isRationalizerWorkflow", (workflowType==ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW 
				&& usageType == WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER));
		referenceData.put("isAllTypeWorkflow", (workflowType==ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW
				&& usageType == WorkflowUsageType.ID_USAGE_TYPE_ALL));
		
		if (usageType == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE){
			referenceData.put("isMessageWorkflow", true);
			referenceData.put("returnFromSetupPagePath", UserUtil.getReturnFromSetupPageContextPath());
		}else if (usageType == WorkflowUsageType.ID_USAGE_TYPE_VARIANT){
			referenceData.put("isVariantWorkflow", true);
			referenceData.put("returnFromSetupPagePath", UserUtil.getReturnFromSetupPageContextPath());
		}

		referenceData.put("usageTypes", WorkflowUsageType.listAll());

		List<WorkflowStepType> stepTypes = WorkflowStepType.listAll();
		// Limit the sub-workflow type to only message or global smart text
		if(usageType != WorkflowUsageType.ID_USAGE_TYPE_MESSAGE && workflowType != ConfigurableWorkflow.GLOBAL_SMART_TEXT_WORKFLOW){
			stepTypes.remove(new WorkflowStepType(WorkflowStepType.ID_SUBWORKFLOW));
		}
		referenceData.put("stepTypes", stepTypes);

		List<TranslatorType> translatorTypes = TranslatorType.listAll();
		List<Services> translationServices = Services.findAllIncludingParents(ApplicationUtil.getMessage("page.label.services.translation"))
				.stream().filter(s -> s.isServiceStatusEnabled() && s.isServiceStatusActivated())
				.sorted(Comparator.comparing(IdentifiableMessagePointModel::getName))
				.collect(Collectors.toList());

		boolean isTranslationServiceAvail = !translationServices.isEmpty();
		if(!isTranslationServiceAvail){
			translatorTypes.remove(new TranslatorType(TranslatorType.ID_SERVICE));
		}
		referenceData.put("translatorTypes", translatorTypes);
		referenceData.put("translationServices", translationServices);
		
		List<Document> availableTouchpoints = new ArrayList<>();
		for(Document availableTouchpoint : Document.findAllDocumentsAndProjectsVisible(true)){
			if(usageType == WorkflowUsageType.ID_USAGE_TYPE_VARIANT){
				if(availableTouchpoint.isEnabledForVariation()){
					availableTouchpoints.add(availableTouchpoint);
				}
			}else if(usageType == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED){
				if(availableTouchpoint.isConnectedEnabled()){
					availableTouchpoints.add(availableTouchpoint);
				}
			}else if(usageType == WorkflowUsageType.ID_USAGE_TYPE_MESSAGE){
				availableTouchpoints.add(availableTouchpoint);
			}
		}
		referenceData.put("availableTouchpoints", availableTouchpoints);

		referenceData.put("availableLanguages", MessagepointLocale.getAllLocales());
		
		referenceData.put("isGlobalContext", UserUtil.getCurrentGlobalContext());

		List<ConfigurableWorkflow> availWorkflows = ConfigurableWorkflow.findByDocumentModelAndUsageTypes(-1, ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_ALL)
				.stream().filter(w -> w.getId() != workflowId).collect(Collectors.toList());
		referenceData.put("availWorkflows", availWorkflows);

		referenceData.put("frequencyTypes", HibernateUtil.getManager().getObjects(FrequencyType.class, MessagepointOrder.asc("id")));

		referenceData.put("styleTransformProfiles", TextStyleTransformationProfile.findAllByType(TextStyleTransformationProfile.TYPE_TRANSLATION));

		referenceData.put("isCurrentlyUsedByAssets", ConfigurableWorkflow.isCurrentlyUsedByAssets(workflowId));

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(ApprovalType.class, new StaticTypeIdCustomEditor<>(ApprovalType.class));
		binder.registerCustomEditor(WorkflowDueByType.class, new StaticTypeIdCustomEditor<>(WorkflowDueByType.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(MessagepointLocale.class, new IdCustomEditor<>(MessagepointLocale.class));
		binder.registerCustomEditor(WorkflowStepType.class, new StaticTypeIdCustomEditor<>(WorkflowStepType.class));
		binder.registerCustomEditor(TranslatorType.class, new StaticTypeIdCustomEditor<>(TranslatorType.class));
		binder.registerCustomEditor(ConfigurableWorkflow.class, new IdCustomEditor<>(ConfigurableWorkflow.class));
		binder.registerCustomEditor(FrequencyType.class, new IdCustomEditorInt<>(FrequencyType.class));
		binder.registerCustomEditor(TextStyleTransformationProfile.class, new IdCustomEditor<>(TextStyleTransformationProfile.class));
		binder.registerCustomEditor(Date.class, "approvals.startDate", DateUtil.getCustomDateTimeEditor());
		binder.registerCustomEditor(Date.class, "approvals.endDate", DateUtil.getCustomDateEditor());
	}

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		WorkflowEditWrapper command = new WorkflowEditWrapper();
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		long workflowId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_WORKFLOW_ID, -1);
		int modelType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_TYPE, -1);
		int usageType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_USAGE_TYPE, -1);
		
		Document document = Document.findById(documentId);
		List<WorkflowEditStepVO> stepVOList = new ArrayList<>();
		long order = 0;
		
		ConfigurableWorkflow workflow = null;
		if(modelType == ConfigurableWorkflow.PROJECT_TASK_WORKFLOW || modelType == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW){
			if(workflowId != -1){
				workflow = ConfigurableWorkflow.findById(workflowId);
			}
		}else{
			workflow = ConfigurableWorkflow.findByDocumentAndModelType(documentId, modelType);
		}
		if(workflow == null){	// No workflow
			// Create new workflow
			workflow = new ConfigurableWorkflow();
			workflow.setModelType(modelType);
			workflow.setUsageType(usageType);
			workflow.setDocument(document);
		}
		
		if(modelType == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW){
			List<Document> defaultTouchpoints = new ArrayList<>();
			if(workflow.getId() <= 0){
				if ( UserUtil.getCurrentGlobalContext() ) {
					defaultTouchpoints.addAll(Document.findAllDocumentsAndProjectsVisible());
				} else {
					if ( UserUtil.getCurrentTouchpointContext() != null )
						defaultTouchpoints.add(UserUtil.getCurrentTouchpointContext());
				}
			}else{
				defaultTouchpoints.addAll(workflow.getWorkflowLibraryDocuments());
				command.setUsageType(workflow.getUsageType());
			}
			
			Set<Document> documentNotMatchingType = new HashSet<>();
			for ( Document currentDocument: defaultTouchpoints ) {
				if ( usageType == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED && !currentDocument.isConnectedEnabled() ) 
					documentNotMatchingType.add(currentDocument);
				else if ( usageType == WorkflowUsageType.ID_USAGE_TYPE_VARIANT && !currentDocument.isEnabledForVariation() )
					documentNotMatchingType.add(currentDocument);
			}
			defaultTouchpoints.removeAll(documentNotMatchingType);
			
			command.setDocuments(defaultTouchpoints);
		}

		command.setWorkflow(workflow);
		ConfigurableWorkflowInstance workflowInst = workflow.findActiveInstance();
		if(workflowInst != null){
			command.setOwner(workflowInst.getOwnerId() != null ? User.findById(workflowInst.getOwnerId()) : null);
			List<ConfigurableWorkflowStep> approvalStepsOrdered = workflowInst.getWorkflowStepsOrdered();
			for (ConfigurableWorkflowStep approvalStep : approvalStepsOrdered) {
				order++;
				WorkflowEditStepVO approvalVO = new WorkflowEditStepVO();
				approvalVO.setOrder(order);
				approvalVO.setStepId(approvalStep.getId());
				approvalVO.setStepName(approvalStep.getState());
				approvalVO.setIsOrderEditable(approvalStep.getIsOrderEditable());
				approvalVO.setStepType(new WorkflowStepType(approvalStep.getWorkflowStepType()));
				approvalVO.setTranslationServiceGuid(StringUtils.isNoneBlank(approvalStep.getServiceGuid()) ? approvalStep.getServiceGuid() : "0");
				if(approvalStep.getWorkflowStepType()==WorkflowStepType.ID_TRANSLATION) {
					approvalVO.setTranslatorType(new TranslatorType(approvalStep.getTranslatorType()));
					approvalVO.setStyleTransformProfile(approvalStep.getTextStyleTransformProfile());
					if(!approvalStep.getApprovalUsers().isEmpty()){
						approvalVO.setTranslators(approvalStep.getApprovalUsersSorted());
					}
					approvalVO.setLanguages(approvalStep.getLanguages());
					approvalVO.setAllowEditDefaultLanguage(approvalStep.isAllowEditDefaultLanguage());
				}else if(approvalStep.getWorkflowStepType()==WorkflowStepType.ID_SUBWORKFLOW) {
					approvalVO.setSubworkflows(approvalStep.getSubworkflows());
				}else if (approvalStep.getWorkflowStepType()==WorkflowStepType.ID_DEFAULT ||
						approvalStep.getWorkflowStepType()==WorkflowStepType.ID_TRANSLATION_APPROVAL) {
					if(!approvalStep.getApprovalUsersSorted().isEmpty())
						approvalVO.setUsers(approvalStep.getApprovalUsersSorted());
					approvalVO.setAllowEditDefaultLanguage(true);
					
					if(approvalStep.getLanguages() != null && !approvalStep.getLanguages().isEmpty()) {
						approvalVO.setTranslationApprovalLanguages(approvalStep.getLanguages());
					}
				}
				approvalVO.setEnabled(true);
				approvalVO.setApproveType( new ApprovalType(approvalStep.getApproveType()) );
				if(approvalStep.isDueBy()){
					approvalVO.setDueByChecked(true);
					approvalVO.setDueByType( new WorkflowDueByType(approvalStep.getDueByType()) );
					approvalVO.setFrequencyType(approvalStep.getFrequencyType());
					approvalVO.setStartDate(approvalStep.getStartDate());
					approvalVO.setEndDate(approvalStep.getEndDate());
				}else{
					approvalVO.setDueByChecked(false);
				}
				stepVOList.add(approvalVO);
			}
		}
		
		while (order < MAX_NUMBER_OF_APPROVALS) {
			order++;
			WorkflowEditStepVO approvalVO = new WorkflowEditStepVO();
			approvalVO.setOrder(UNUSED_APPROVAL_ORDER);
			approvalVO.setStepId(UNUSED_APPROVAL_ID);
			approvalVO.setOrder(order);
			stepVOList.add(approvalVO);
		}		
		command.setModelType(modelType);
		command.setUsageType(usageType);
		command.setDocumentId(documentId);
		command.setApprovals(stepVOList);
		
		return command;
	}	
	
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		WorkflowEditWrapper command = (WorkflowEditWrapper)commandObj;
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		int modelType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_TYPE, -1);
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARAM_ACTION, -1);
		User principal = UserUtil.getPrincipalUser();
		
		ServiceExecutionContext context = CreateOrUpdateWorkflowService.createContext(
				documentId,
				modelType,
				command.getUsageType(),
				command.getWorkflow(),
				command.getOwner(),
				command.getApprovalsOrdered(),
				command.getDocuments(),
				action == ACTION_OVERRIDE_SAVE);

		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateWorkflowService.SERVICE_NAME, CreateOrUpdateWorkflowService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateWorkflowService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Workflow Approvals were not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		} else {
			
			Map<String, Object> params = new HashMap<>();
			
			if( modelType == ConfigurableWorkflow.GLOBAL_SMART_TEXT_WORKFLOW && principal.isPermitted(Permission.ROLE_EMBEDDED_CONTENT_VIEW) ) {
				return new ModelAndView( new RedirectView(ApplicationUtil.getWebRoot() + SUCCESS_VIEW_EMBEDDED_CONTENT) );
			} else if( modelType == ConfigurableWorkflow.GLOBAL_IMAGE_WORKFLOW && principal.isPermitted(Permission.ROLE_CONTENT_LIBRARY_VIEW) ) {
				return new ModelAndView( new RedirectView(ApplicationUtil.getWebRoot() + SUCCESS_VIEW_CONTENT_LIBRARY) );
			} else if( modelType == ConfigurableWorkflow.LOOKUP_TABLE_WORKFLOW && (principal.isPermitted(Permission.ROLE_LOOKUP_TABLE_VIEW) || principal.isPermitted(Permission.ROLE_LICENCED_LOOKUP_TABLE_VIEW)) ) {
				return new ModelAndView( new RedirectView(ApplicationUtil.getWebRoot() + SUCCESS_VIEW_LOOKUP_TABLE) );
			} else if( modelType == ConfigurableWorkflow.PROJECT_TASK_WORKFLOW && principal.isPermitted(Permission.ROLE_PROJECT_VIEW) ) {
				return new ModelAndView( new RedirectView(ApplicationUtil.getWebRoot() + SUCCESS_VIEW_PROJECT) );
			} else if( modelType == ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW && principal.isPermitted(Permission.ROLE_WORKFLOW_ADMIN_EDIT) ) {
				return new ModelAndView( new RedirectView(ApplicationUtil.getWebRoot() + SUCCESS_VIEW_WORKFLOW_LIBRARY) );
			} else {
//				for(WorkflowEditStepVO step: command.getApprovalsOrdered()){
//					if(step.isEnabled())
//						for(User subUser:step.getUsers()){
//							NotificationEventUtil.unsubscribeNotification(NotificationActionType.ID_MESSAGE_ACTIVATED, subUser, documentId);
//							NotificationEventUtil.unsubscribeNotification(NotificationActionType.ID_MESSAGE_WIP_CREATED, subUser, documentId);
//						}
//				}
				params.put(REQ_PARAM_SAVE_SUCCESS, true);
				params.put(REQ_PARAM_WORKFLOW_TYPE, modelType);
				if ( documentId == -1 ) {
					if(modelType != ConfigurableWorkflow.PROJECT_TASK_WORKFLOW && modelType != ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW 
							&& command.getWorkflow() != null && command.getWorkflow().getDocument() != null && !command.getWorkflow().getDocument().isTpContentChanged()){
						command.getWorkflow().getDocument().setTpContentChanged(true);
						command.getWorkflow().getDocument().save();
					}
					return new ModelAndView(new RedirectView("workflow_edit.form"), params);
				} else {
					Document document = Document.findById(documentId);
					if(document != null && !document.isTpContentChanged()){
						document.setTpContentChanged(true);
						document.save();
					}
						
					params.put(REQ_PARAM_DOCUMENT_ID, documentId);
					return new ModelAndView(new RedirectView("workflow_edit.form"), params);
				}
			}
		}
	}
}
