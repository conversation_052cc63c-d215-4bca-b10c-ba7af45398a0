package com.prinova.messagepoint.controller.workflow;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.project.ProjectTask;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.version.Approvable;
import com.prinova.messagepoint.model.version.ApprovableWithWfActions;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class WorkflowHistoryController implements Controller {

	public static final String REQ_PARAM_WORKFLOW_TYPE				= "workflowType";
	public static final String REQ_PARAM_MODELID					= "modelId";
	public static final String REQ_PARAM_WORKFLOW_USAGE_TYPE		= "usageType";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}

	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> params = new HashMap<>();
		
		int workflowType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_TYPE, -1);
		int usageType = ServletRequestUtils.getIntParameter(request, REQ_PARAM_WORKFLOW_USAGE_TYPE, -1);

		long modelId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_MODELID, -1);
		String activeState = ApplicationUtil.getMessage("page.label.active");
		String initialState = ApplicationUtil.getMessage("page.label.in.process");
		String stepAwaitingState = "";
		boolean holdingForApproval = false;
		
		boolean applyActionsHistory = false;
		List<ConfigurableWorkflowActionHistory> workflowActions = new ArrayList<>();
		
		switch(workflowType){
			case ConfigurableWorkflow.LOOKUP_TABLE_WORKFLOW:{
				LookupTableInstance ltInt = HibernateUtil.getManager().getObject(LookupTableInstance.class, modelId);
				params.put("model", ltInt);
				
				params.put("wfActionMap", this.getActionMap(ltInt));
				params.put("wfStepMap", this.getStepMap(ltInt));
				params.put("currentWfActionMap", this.getCurrentActionMap(ltInt));
				break;
			}
			case ConfigurableWorkflow.PROJECT_TASK_WORKFLOW:{
				Task task = HibernateUtil.getManager().getObject(Task.class, modelId);
				params.put("model", task);
				
				List<ConfigurableWorkflowAction> actionList = new ArrayList<>();
				List<ConfigurableWorkflowStep> stepList = new ArrayList<>();
				
				Project project = task.getReferencedProject();
				if(project != null){
					ProjectTask projectTask = ProjectTask.findByProjectAndTask(project.getId(), task.getId());
					
					// Add the workflow action list
					ConfigurableWorkflowAction currentAction =  task.getWorkflowAction();
					if(currentAction == null){	// WC or being rejected before
						currentAction = ConfigurableWorkflowAction.findLatestActionByModel(task);
						initialState = ApplicationUtil.getMessage("page.label.pending.initial.task.complete");
					}
					
					if(!task.isComplete()){
						stepAwaitingState = ApplicationUtil.getMessage("page.label.pending.task.complete");
					}
					
					while(currentAction != null){
						if(currentAction.getCreated().after(projectTask.getCreated())){
							actionList.add(0, currentAction);
						}
						currentAction = currentAction.getPreviousAction();
					}
					
					// Add the workflow step list if the acion is not the last
					ConfigurableWorkflowStep nextStep = null;
					if(task.getWorkflowAction() == null){	// Task in wc
						nextStep = project.getFirstWorkflowStep(task);
					}else{	// Task in workflow step
						nextStep = project.getNextWorkflowStep(task, task.getWorkflowAction().getConfigurableWorkflowStep());
					}
					
					while(nextStep != null){
						stepList.add(nextStep);
						nextStep = project.getNextWorkflowStep(task, nextStep);
					}
				}
				
				params.put("wfActionMap", actionList);
				params.put("wfStepMap", stepList);
				params.put("currentWfActionMap", this.getCurrentActionMap(task));
				activeState = ApplicationUtil.getMessage("page.label.complete");
				
				if(!task.isComplete()){
					holdingForApproval = true;
				}
				break;
			}
			case ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW:{
				if(usageType == WorkflowUsageType.ID_USAGE_TYPE_VARIANT){
					TouchpointSelection tpSelection = HibernateUtil.getManager().getObject(TouchpointSelection.class, modelId);
					params.put("model", tpSelection);
					
					params.put("wfActionMap", this.getActionMap(tpSelection));
					params.put("wfStepMap", this.getStepMap(tpSelection));
					params.put("currentWfActionMap", this.getCurrentActionMap(tpSelection));
					
					applyActionsHistory = true;
					workflowActions = ConfigurableWorkflowActionHistory.findByTouchpointSelection(tpSelection);
					break;
				}else if(usageType == WorkflowUsageType.ID_USAGE_TYPE_CONNECTED){
					Communication communication = HibernateUtil.getManager().getObject(Communication.class, modelId);
					params.put("model", communication);
					
					params.put("wfActionMap", this.getActionMap(communication));
					params.put("wfStepMap", this.getStepMap(communication));
					params.put("currentWfActionMap", this.getCurrentActionMap(communication));
					break;
				}else if(usageType == WorkflowUsageType.ID_USAGE_TYPE_RATIONALIZER){
					RationalizerDocumentContent docContent = HibernateUtil.getManager().getObject(RationalizerDocumentContent.class, modelId);
					params.put("model", docContent);
					
					params.put("wfActionMap", this.getActionMap(docContent));
					params.put("wfStepMap", this.getStepMap(docContent));
					params.put("currentWfActionMap", this.getCurrentActionMap(docContent));
					break;
				}
			}
			default:{
				ContentObject contentObject = ContentObject.findById(modelId);
				ContentObjectData contentObjectData = contentObject.getContentObjectDataWorkingCentric();
				params.put("model", contentObject);

				params.put("wfActionMap", this.getActionMap(contentObject));
				params.put("wfStepMap", this.getStepMap(contentObject));
				params.put("currentWfActionMap", this.getCurrentActionMap(contentObject));

				applyActionsHistory = true;
				workflowActions = ConfigurableWorkflowActionHistory.findByContentObjectData(contentObjectData);
			}
		}
		params.put("holdingForApproval", holdingForApproval);
		params.put("initialState", initialState);
		params.put("stepAwaitingState", stepAwaitingState);
		params.put("activeState", activeState);
		
		params.put("applyActionsHistory", applyActionsHistory);
		params.put("historyActions", workflowActions);
		
		return new ModelAndView(getFormView(), params);
	}

	private Map<ConfigurableWorkflow, ConfigurableWorkflowAction> getCurrentActionMap(Approvable model){
		Map<ConfigurableWorkflow, ConfigurableWorkflowAction> actionMap = new HashMap<>();
		List<ConfigurableWorkflowAction> currentActionList = this.getCurrentActionList(model);

		for(ConfigurableWorkflowAction currentAction : currentActionList){
			ConfigurableWorkflow currentWorkflow = currentAction.getConfigurableWorkflow();
			actionMap.put(currentWorkflow, currentAction);
		}

		return actionMap;
	}
	 
	private Map<ConfigurableWorkflow, List<ConfigurableWorkflowAction>> getActionMap(Approvable model){
		Map<ConfigurableWorkflow, List<ConfigurableWorkflowAction>> actionMap = new LinkedHashMap<>();
		List<ConfigurableWorkflowAction> currentActionList = this.getCurrentActionList(model);

		for(ConfigurableWorkflowAction currentAction : currentActionList){
			ConfigurableWorkflow currentWorkflow = currentAction.getConfigurableWorkflow();
			List<ConfigurableWorkflowAction> actionList = new ArrayList<>();

			while(currentAction != null){
				actionList.add(0, currentAction);
				currentAction = currentAction.getPreviousAction();
			}
			actionMap.put(currentWorkflow, actionList);
		}

		return actionMap;
	}
	
	private Map<ConfigurableWorkflow, List<ConfigurableWorkflowStep>> getStepMap(Approvable model){
		Map<ConfigurableWorkflow, List<ConfigurableWorkflowStep>> stepMap = new LinkedHashMap<>();
		List<ConfigurableWorkflowAction> currentActionList = this.getCurrentActionList(model);

		for(ConfigurableWorkflowAction currentAction : currentActionList) {
			ConfigurableWorkflow currentWorkflow = currentAction.getConfigurableWorkflow();
			List<ConfigurableWorkflowStep> stepList = new ArrayList<>();

			if (!currentAction.isActive()) {
				ConfigurableWorkflowStep nextStep = currentAction.getConfigurableWorkflowStep().getNextStep();
				while (nextStep != null) {
					stepList.add(nextStep);
					nextStep = nextStep.getNextStep();
				}
			}
			stepMap.put(currentWorkflow, stepList);
		}
		return stepMap;
	}

	private List<ConfigurableWorkflowAction> getCurrentActionList(Approvable model){
		List<ConfigurableWorkflowAction> currentActionList = new ArrayList<>();
		if(model instanceof ApprovableWithWfActions model2){
			if(model2.getWorkflowActions() != null && !model2.getWorkflowActions().isEmpty()){
				currentActionList.addAll(model2.getWorkflowActions());
			}
		}else {
			if(model.getWorkflowAction() != null){
				currentActionList.add(model.getWorkflowAction());
			}
		}

		if(currentActionList.isEmpty()){ // WC or being rejected before
			if(model instanceof ContentObject) {
				// Only restore the workflow action if it is being rejected
				ConfigurableWorkflowActionHistory latestHistory = ConfigurableWorkflowActionHistory.findLatestHistoryByModel(model);
				if (latestHistory != null && (latestHistory.getAction() == ConfigurableWorkflowActionType.ID_REJECTED || latestHistory.getAction() == ConfigurableWorkflowActionType.ID_ABORTED)) {
					ConfigurableWorkflowAction latestAction = ConfigurableWorkflowAction.findLatestActionByModel(model);
					currentActionList.add(latestAction);
				}
			}else{
				currentActionList.add(ConfigurableWorkflowAction.findLatestActionByModel(model));
			}
		}

		// If actions link to sub-workflow, get those last actions in each sub-workflow
		List<ConfigurableWorkflowAction> subworkflowActions = new ArrayList<>();
		for(ConfigurableWorkflowAction currentAction : currentActionList){
			ConfigurableWorkflowAction currentAction2 = currentAction;
			do {
				subworkflowActions.addAll(currentAction2.findSubworkflowActions());
				currentAction2 = currentAction2.getPreviousAction();
			}while (currentAction2 != null);
		}
		currentActionList.addAll(subworkflowActions);

		return currentActionList;
	}
}
