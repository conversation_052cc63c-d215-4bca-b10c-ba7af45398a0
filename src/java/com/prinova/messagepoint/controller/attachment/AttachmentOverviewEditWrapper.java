package com.prinova.messagepoint.controller.attachment;

import java.io.Serializable;

import com.prinova.messagepoint.model.attachment.AttachmentDeliveryType;

public class AttachmentOverviewEditWrapper implements Serializable {

	private static final long serialVersionUID = 7089892738716302540L;
	
	private long 					id;
	private String 					name;
	private AttachmentDeliveryType 	deliveryType;
	private String 					recipientAttachmentName;
	private String 					recipientAttachmentLocation;
	

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public AttachmentDeliveryType getDeliveryType() {
		return deliveryType;
	}
	public void setDeliveryType(AttachmentDeliveryType deliveryType) {
		this.deliveryType = deliveryType;
	}
	
	public String getRecipientAttachmentName() {
		return recipientAttachmentName;
	}
	public void setRecipientAttachmentName(String recipientAttachmentName) {
		this.recipientAttachmentName = recipientAttachmentName;
	}
	
	public String getRecipientAttachmentLocation() {
		return recipientAttachmentLocation;
	}
	public void setRecipientAttachmentLocation(String recipientAttachmentLocation) {
		this.recipientAttachmentLocation = recipientAttachmentLocation;
	}

}