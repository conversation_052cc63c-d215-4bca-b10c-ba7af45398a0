package com.prinova.messagepoint.controller.attachment;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;

import com.prinova.messagepoint.controller.FlowController;
import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.targeting.TargetingWrapper;
import com.prinova.messagepoint.model.admin.DateDataValue;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.model.wrapper.TargetGroupWrapper;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.targeting.UpdateTargetingService;
import com.prinova.messagepoint.util.ConditionElementUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.TargetGroupUtil;

public class AttachmentTargetingEditController extends FlowController<Attachment, TargetingWrapper> {

	private static final String PARAM_SUBMIT_TYPE 	= "submittype";
	private static final String FORM_SAVE 			= "save";
	private static final String FORM_SAVE_AND_BACK 	= "saveandback";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> referenceData = new HashMap<>();
		
    	referenceData.put("dateValueTypesJSON", DateDataValue.getDateValueTypesJSON());
		
		referenceData.put("paramSimple", ConditionElementUtil.TYPE_SIMPLE);
		referenceData.put("paramSimpleRange", ConditionElementUtil.TYPE_SIMPLE_RANGE);
		referenceData.put("paramDate", ConditionElementUtil.TYPE_DATE);
		referenceData.put("paramDateRange", ConditionElementUtil.TYPE_DATE_RANGE);	
		referenceData.put("dateDataValues", HibernateUtil.getManager().getObjects(DateDataValue.class, MessagepointOrder.asc("id")));
		
		List<TargetGroup> allTargetGroups = TargetGroup.findAll();
		referenceData.put("targetGroups", allTargetGroups);
			
		Attachment attachment = getBackingObject(request);
		Set<ConditionElement> conditionElements = new HashSet<>();
		Map<Long,Set<Long>> conditionItemValueMap = new HashMap<>();
		Map<Long, String> conditionSubelementValueTypes = new HashMap<>();

		Map<String, List<TargetGroupWrapper>> targetGroupsMap = ((TargetingWrapper)command).getTargetGroupsMap();
		if (targetGroupsMap != null)
		{
			for (String key : targetGroupsMap.keySet())
			{
				for (TargetGroupWrapper tgw : targetGroupsMap.get(key))
				{
					TargetGroup targetGroup = tgw.getTargetGroup();
					if (targetGroup != null)
					{
						TargetGroupInstance tgInstance = tgw.getInstance();
						TargetGroupUtil.loadInstanceValues(tgInstance, conditionElements, conditionItemValueMap, conditionSubelementValueTypes);
					}
				}
			}
		}
		
    	referenceData.put("conditionElements", conditionElements);
		referenceData.put("conditionItemValueMap", conditionItemValueMap);
    	referenceData.put("conditionSubelementValueTypes", conditionSubelementValueTypes);

    	referenceData.put("attachment", attachment);
    	
    	referenceData.put("targetingPermitted", attachment.isOptionalDelivery());
    	
    	referenceData.put("documentId", attachment.getDocuments().iterator().next().getId());
    	
		return referenceData;
	}

	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(TargetGroup.class, new IdCustomEditor<>(TargetGroup.class));
	}
	
	@Override
	protected TargetingWrapper formBackingObjectInternal(HttpServletRequest request) {
		TargetingWrapper command = null;
    	
    	if (!isFormSubmission(request)) {
    		command = new TargetingWrapper(getBackingObject(request), false, false);
    	} else {
    		command = new TargetingWrapper(getBackingObject(request), true, false);
    	}
    	
    	return command;
    }
    
	@Override
	public void doSave(HttpServletRequest request, TargetingWrapper command, BindException errors) throws Exception {
		ServiceExecutionContext context = UpdateTargetingService.createContext(command);
		Service updateTargetingService = MessagepointServiceFactory.getInstance().lookupService(UpdateTargetingService.SERVICE_NAME, UpdateTargetingService.class);
		updateTargetingService.execute(context);
	}
		
	@Override
	public Attachment getObject(TargetingWrapper command) {
		return (Attachment)command.getModel();
	}
	
	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, PARAM_SUBMIT_TYPE, "");
		if (FORM_SAVE.equals(submitType) || FORM_SAVE_AND_BACK.equals(submitType)) {
			return true;
		}
		return false;
	}
}