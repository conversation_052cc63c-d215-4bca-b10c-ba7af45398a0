package com.prinova.messagepoint.controller.attachment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.FlowController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.attachment.AttachmentDeliveryType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.attachment.CreateOrUpdateAttachmentService;
import com.prinova.messagepoint.platform.services.insert.ReleaseForApprovalService;
import com.prinova.messagepoint.tag.view.WorkflowTabTag;
import com.prinova.messagepoint.util.UserUtil;

public class AttachmentOverviewEditController extends FlowController<Attachment, AttachmentOverviewEditWrapper> {
	
	private static final Log log = LogUtil.getLog(AttachmentOverviewEditController.class);

	public static final String REQ_PARAM_DOCUMENT_ID = "documentId";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> referenceData = new HashMap<>();

		List<AttachmentDeliveryType> deliveryTypes = AttachmentDeliveryType.listAll();
		referenceData.put("deliveryTypes", deliveryTypes);
		
		long documentId;
		Attachment attachment = getBackingObject(request);
		if (attachment != null)
			documentId = attachment.getDocuments().iterator().next().getId();
		else
			documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1L);
		referenceData.put("documentId",documentId);
		
		referenceData.put("userContextLangCode", UserUtil.getAppLangCodeForPrincipal());
		
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

		return referenceData;
	}

	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(AttachmentDeliveryType.class, new StaticTypeIdCustomEditor<>(AttachmentDeliveryType.class));
		return;
	}
	
	@Override
	protected AttachmentOverviewEditWrapper formBackingObjectInternal(HttpServletRequest request) {
    	Attachment attachment = getBackingObject(request);
    	AttachmentOverviewEditWrapper command = new AttachmentOverviewEditWrapper();
    	if (attachment != null) {
    		command.setId(attachment.getId());
    		command.setName(attachment.getName());
    		command.setDeliveryType(new AttachmentDeliveryType(attachment.getDeliveryTypeId()));
    		command.setRecipientAttachmentName(attachment.getRecipientAttachmentName().getEncodedValue());
    		command.setRecipientAttachmentLocation(attachment.getRecipientAttachmentLocation().getEncodedValue());
    	}
    	return command;
    }
    
	@Override
	protected ModelAndView handleConfirm(HttpServletRequest request, HttpServletResponse response,AttachmentOverviewEditWrapper command, BindException errors) throws Exception {
		String submitType = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_TYPE_PARAMETER, "");
		if (!WorkflowTabTag.SUBMIT_TYPE_CANCEL.equals(submitType)) {
			doSave(request, command, errors);
			if (errors.hasErrors()) {
				return showForm(request, response, errors);
			}			
		}
		return new ModelAndView(new RedirectView(getSuccessView(request, command)), getParameter(), command.getId());
	}

	@Override
	public void doSave(HttpServletRequest request, AttachmentOverviewEditWrapper command, BindException errors) throws Exception {
    	ServiceExecutionContext context = null;
    	User principalUser = UserUtil.getPrincipalUser();
    	
    	boolean isNew = command.getId() <= 0;
    	if (isNew) {
    		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1L);
        	Document document = Document.findById(documentId);
    		
    		context = CreateOrUpdateAttachmentService.createContextForNew(command.getName(), 
    																	document,
															 	   		command.getRecipientAttachmentName(),
															 	   		command.getRecipientAttachmentLocation(),
															 	   		command.getDeliveryType(),
															 	   		principalUser);
    	} else {
        	context = CreateOrUpdateAttachmentService.createContextForUpdate(
        																command.getId(),
        																command.getName(), 
																		command.getRecipientAttachmentName(),
																		command.getRecipientAttachmentLocation(),
																		command.getDeliveryType(),
																		principalUser);      	
    	}
    	
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAttachmentService.SERVICE_NAME, CreateOrUpdateAttachmentService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(ReleaseForApprovalService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" requestor = ").append(principalUser.getUsername());
				sb.append(" Attachment was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
		}
		if (isNew && serviceResponse.isSuccessful()) {
			command.setId(((Attachment)context.getResponse().getResultValueBean()).getId());
		}
	}
		
	@Override
	public Attachment getObject(AttachmentOverviewEditWrapper command) {
		if (command != null) {
			return Attachment.findById(command.getId());	
		} else {
			return null;
		}	
	}
}