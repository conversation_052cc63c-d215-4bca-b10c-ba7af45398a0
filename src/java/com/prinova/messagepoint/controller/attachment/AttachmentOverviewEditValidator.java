package com.prinova.messagepoint.controller.attachment;

import com.prinova.messagepoint.util.StringUtil;
import org.jsoup.Jsoup;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.attachment.Attachment;
import com.prinova.messagepoint.model.attachment.AttachmentDeliveryType;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class AttachmentOverviewEditValidator extends MessagepointInputValidator {
	
	public static final String RESTRICTED_FILE_PATH_CHARS = "#%&*<>?{|}~";
	public static final String RESTRICTED_FILE_NAME_CHARS = "#%&*:<>?{|}~\\/";
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		AttachmentOverviewEditWrapper wrapper = (AttachmentOverviewEditWrapper) commandObj;
		
		String recipientAttachmentName 		= ContentObjectContentUtil.getUnformattedTextContent( Jsoup.parse(wrapper.getRecipientAttachmentName() != null ? wrapper.getRecipientAttachmentName() : "") ).replaceAll("MPVAR[0-9]*#", "");
		String recipientAttachmentLocation 	= ContentObjectContentUtil.getUnformattedTextContent( Jsoup.parse(wrapper.getRecipientAttachmentName() != null ? wrapper.getRecipientAttachmentLocation() : "") ).replaceAll("MPVAR[0-9]*#", "");
		
		char[] restictedCharArray = RESTRICTED_FILE_NAME_CHARS.toCharArray();
		for (char currentChar: restictedCharArray)
			if (recipientAttachmentName.indexOf(currentChar) != -1) {
				errors.reject("error.message.recipient.name.contains.restricted.characters", new String[] { StringUtil.separateCharArray(restictedCharArray, " ") }, "");
				break;
			}
		
		restictedCharArray = RESTRICTED_FILE_PATH_CHARS.toCharArray();
		for (char currentChar: restictedCharArray)
			if (recipientAttachmentLocation.indexOf(currentChar) != -1) {
				errors.reject("error.message.recipient.location.contains.restricted.characters", new String[] { StringUtil.separateCharArray(restictedCharArray, " ") }, "");
				break;
			}
		
		if (wrapper.getRecipientAttachmentName().isEmpty())
			errors.reject("error.message.recipient.attachment.name.is.mandatory");
		
		if (wrapper.getRecipientAttachmentLocation().isEmpty())
			errors.reject("error.message.recipient.attachment.location.is.mandatory");
		
		Attachment attachment = Attachment.findById(wrapper.getId());
		if (attachment != null && attachment.getHasTarget()) 
			if (wrapper.getDeliveryType().getId() == AttachmentDeliveryType.ID_MANDATORY)
				errors.reject("error.message.mandatory.attachments.cannot.have.targeting");
		
	}
}