package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;
import com.prinova.messagepoint.model.IdentifiableMessagePointModelHasGuid;
import com.prinova.messagepoint.util.HibernateUtil;

public class GuidCustomEditor<T extends IdentifiableMessagePointModelHasGuid> extends PropertyEditorSupport {
		private Class<T> clazz;

		public GuidCustomEditor(Class<T> clazz) {
			this.clazz = clazz;
		}

		public void setAsText(String text) throws IllegalArgumentException {
			String guid = text;
			if (guid != null && !guid.trim().isEmpty()) {
				Object obj = HibernateUtil.getManager().getObjectByGuid(clazz,guid);
				setValue(obj);
			} else {
				setValue(null);
			}
		}

		public String getAsText() {
			IdentifiableMessagePointModelHasGuid object = (IdentifiableMessagePointModelHasGuid)getValue();
			if (object == null) return "0";
			return "" + object.getGuid();
		}
	}

