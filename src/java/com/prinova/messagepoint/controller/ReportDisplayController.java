package com.prinova.messagepoint.controller;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;

public class ReportDisplayController implements Controller {

	public static final String REQ_PARAM_REPORT_ID = "reportId";
	public static final String REQ_PARAM_REPORT = "report";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();
		
		long reportId = getReportIdFromRequest(request);
		
		AuditReport auditReport = AuditReport.findById(reportId);
		dataMap.put(REQ_PARAM_REPORT, auditReport);
		boolean reportExist = true;
		// Load the html report content
		int reportType = auditReport.getTypeId();
		if(reportType == AuditReportType.ID_TOUCHPOINT_AUDIT_REPORT 
				|| reportType == AuditReportType.ID_PROJECT_AUDIT_REPORT
				|| reportType == AuditReportType.ID_SMART_TEXT_AUDIT_REPORT
				|| reportType == AuditReportType.ID_CONNECTED_REPORT
				|| reportType == AuditReportType.ID_IMAGE_LIBRARY_AUDIT_REPORT
				|| reportType == AuditReportType.ID_MESSAGE_AUDIT_REPORT){
			String htmlReportContent;
			try{
				htmlReportContent = FileUtil.fileToString(auditReport.getReportPath());
				if(htmlReportContent != null){
					dataMap.put("htmlReportContent", htmlReportContent);
				}
			}catch(Exception e){
			}
		}else{
			try{
				String filePath = ApplicationUtil.getRootPath().concat(auditReport.getReportPath());
				File file = new File(filePath);
				if (!file.exists()) {
					reportExist = false;
				}else{
					String reportHTML = FileUtil.getFileAsString(filePath);
					dataMap.put("reportHTML", reportHTML);
				}
			}catch(Exception e){
				reportExist = false;
			}
		}
		dataMap.put("reportExist", reportExist);
		return new ModelAndView(getFormView(), dataMap);
	}
	
	private long getReportIdFromRequest(HttpServletRequest request){
		if(request.getParameterMap().containsKey(REQ_PARAM_REPORT_ID)){
			return ServletRequestUtils.getLongParameter(request, REQ_PARAM_REPORT_ID, -1);
		}
		return -1L;
	}
}