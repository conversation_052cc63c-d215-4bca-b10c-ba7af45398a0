package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.tag.TagUtils;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class AsyncVariablesController implements Controller {


	private static final Log log = LogUtil.getLog(AsyncVariablesController.class);

	public static final String PARAM_SELECTED_VARIABLE_ID		= "selectedVariableId";
	public static final String PARAM_SELECTED_ELEMENT_ID		= "selectedElementId";
	public static final String PARAM_DATA_SUBTYPE_ID			= "dataSubtypeId";
	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_VARIABLE_ID				= "variableId";
	public static final String PARAM_OBJECT_DNA					= "objectDna";
	public static final String PARAM_LOCALE_ID				    = "localeId";
	public static final String PARAM_NAME_SEARCH				= "sSearch";
	public static final String PARAM_NUM_CAP					= "numCap";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_TARGETING_FILTERED			= "targetingFiltered";
	public static final String PARAM_COMPARE_VARIABLES			= "compareVariables";
	public static final String PARAM_ZONE_ID					= "zoneId";
	public static final String PARAM_DATA_GROUP_ID				= "dataGroupId";
	public static final String PARAM_APPLY_OPTION_NONE			= "applyOptionNone";
	public static final String PARAM_DATA_SOURCE_ID 			= "dataSourceId";
	
	public static final String TYPE_VARIABLES_LIST				= "variablesList";
	public static final String TYPE_VARIABLE_INFO				= "variableInfo";
	public static final String TYPE_COMPATIBILITY				= "compatibility";
	public static final String TYPE_DATAELEMENTS				= "primaryDataSourceElements";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE);
		
		if ( type.equalsIgnoreCase(TYPE_VARIABLES_LIST) ) {
		
			response.setContentType("text/xml");
			ServletOutputStream out = response.getOutputStream();
	
			try {
				out.write(getVariablesListResponseXML(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
			}

		
		} else if ( type.equalsIgnoreCase(TYPE_VARIABLE_INFO) ) {
			
			response.setContentType("application/json");
			ServletOutputStream out = response.getOutputStream();
	
			try {
				out.write(getVariablesInfoResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for variable info: "+e.getMessage(),e);
			}
			
		} else if ( type.equalsIgnoreCase(TYPE_COMPATIBILITY) ) {
			
			response.setContentType("application/json");
			ServletOutputStream out = response.getOutputStream();
			
			try {
				out.write(getCompatibilityResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for variable compatibility: "+e.getMessage(),e);
			}
			
		} else if ( type.equalsIgnoreCase(TYPE_DATAELEMENTS)) {
			response.setContentType("text/xml");
			ServletOutputStream out = response.getOutputStream();

			try {
				out.write(getDataSourceElements(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception ex) {
				log.error("Error - Unable to resolve request for data elements: " + ex.getMessage());
			}
		}

		return null;
	}
	
	public static JSONObject getDefaultVariableInfoJSON(JSONObject obj, AbstractLocale locale) {
		
		try {
			obj.put("default_date_format"			, locale.getDateFormat() != null ? locale.getDateFormat() : ApplicationUtil.getProperty(SystemPropertyKeys.DataFormat.KEY_DATE_DefaultFormat) );

			String defaultCurrencySymbol = locale.getCurrencySymbolValidValue();
			obj.put("default_currency_symbol"		, defaultCurrencySymbol != null ? StringEscapeUtils.escapeXml( defaultCurrencySymbol ) : "" );
	
			obj.put("default_decimal_places"		, locale.getNumberOfDecimalsValidValue() );
			
			obj.put("drop_decimals_for_round_numbers"	, locale.getDropDecimalForWholeNum() );
	
			String defaultThousandsSeparator = locale.getThousandsSeparatorValidValue();
			obj.put("default_thousands_separator"	, defaultThousandsSeparator != null ? StringEscapeUtils.escapeXml( defaultThousandsSeparator ) : "" );
		
		} catch (JSONException e) {
			
			log.error("Error: Unable to retrieve variable info: " + e );
			
		}
		
		return obj;
	}
	
	public static JSONObject getVariablesInfoJSON(DataElementVariable variable, long localeId, Document document) {
		
		JSONObject returnObj = new JSONObject();

		try {

			if ( variable != null) {
				returnObj.put( "variable_id"				, variable.getId() );
				returnObj.put( "name"						, variable.getFriendlyName() );
				
				returnObj.put( "is_system_variable"			, variable.getIsSystemVariable() );
				returnObj.put( "system_type_id"				, variable.getSystemVariableTypeId() );

				returnObj.put( "data_subtype_id"			, variable.getSubtypeId() );
				returnObj.put( "data_subtype_name"			, variable.getSubTypeLabel() );

				returnObj.put("is_compound_variable"		, variable.isCompoundVariable() );
				returnObj.put("is_expression_variable"		, variable.isExpressionVariable() );
	
				if ( variable.isRepeating() ) {
					Long dataSourceId = document.getPrimaryDataSource() != null ? document.getPrimaryDataSource().getId() : -1L;
					VariableDataElementMap varMap = variable.getDataElementMap().get( dataSourceId );
					AggregationOperator agOp = null;
					if ( varMap == null ) {
						for (Long dsId : variable.getDataElementMap().keySet() )
							if ( variable.getDataElementMap().get( dsId ) != null )
								agOp = variable.getDataElementMap().get( dsId ).getAggOperator();
					} else {
						agOp = varMap.getAggOperator();
					}

					// Also check for non-driver aggregation operator (used by script/expression variables)
					if (agOp == null && (variable.isScriptVariable() || variable.isExpressionVariable()) && variable.getNonDriverAggOperator() != null ) {
						agOp = variable.getNonDriverAggOperator();
					}

					returnObj.put("aggregation_operator"	, agOp != null ? agOp.getId() : 0 );
				}

				MessagepointLocale currentLocale = MessagepointLocale.findById(localeId);
				String languageCode = currentLocale != null ? currentLocale.getLanguageCode() : MessagepointLocale.getDefaultSystemLanguageLocale().getLanguageCode();
				AbstractLocale locale = TouchpointLanguage.getTouchpointLanguageAsLocale(document, languageCode);

				returnObj = getDefaultVariableInfoJSON(returnObj, locale);
			}

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve variable info: " + e );
		}

		return returnObj;

	}
	
	private String getVariablesInfoResponseJSON (HttpServletRequest request) {
		
		long variableId 	= ServletRequestUtils.getLongParameter(request, PARAM_VARIABLE_ID, 0);
		String dna			= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_DNA, "");
		long localeId		= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, 1);

		DataElementVariable variable = null;
		if(variableId>0) {
			variable = DataElementVariable.findById(variableId);
		}else if(!dna.isEmpty()){
			variable = DataElementVariable.findByDna(dna);
		}
		
		Document document = UserUtil.getCurrentTouchpointContext();
		
		JSONObject returnObj = getVariablesInfoJSON(variable, localeId, document);
		
		return returnObj.toString();

	}
	
	private String getCompatibilityResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		String variableDNA 				= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_DNA, null);
		DataElementVariable variable 	= DataElementVariable.findByDna(variableDNA);
		
		String[] matchVariableDNAs 		= ServletRequestUtils.getStringParameter(request, PARAM_COMPARE_VARIABLES, "").split(",");
		long zoneId						= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_ID, 0);
		long dataGroupId				= ServletRequestUtils.getLongParameter(request, PARAM_DATA_GROUP_ID, 0);
		
		try {
			
			Boolean zoneCompatible 		= true;
			Boolean matchCompatible 	= true;
			String incompatibleVarDNA 	= null;
			JSONArray errorMsgs 		= new JSONArray();
			Boolean isRepeating			= variable.isRepeating();
			
			Document document 				= UserUtil.getCurrentTouchpointContext();
			Long dataSourceId 				= -1L;
			VariableDataElementMap varMap 	= null;
			if ( document != null && document.getPrimaryDataSource() != null ) {
				dataSourceId =  document.getPrimaryDataSource().getId();
				varMap = variable.getDataElementMap().get( dataSourceId );
				boolean isAggregatedToSingleValue = varMap != null && varMap.getAggOperator() != null && 
						varMap.getAggOperator().getId() != AggregationOperator.AGG_OPERATOR_NA;
				if ( isAggregatedToSingleValue )
					isRepeating = false;
			}

			if ( isRepeating ) {
				AggregationOperator agOp = null;
				if ( varMap == null ) {
					for (Long dsId : variable.getDataElementMap().keySet() )
						if ( variable.getDataElementMap().get( dsId ) != null )
							agOp = variable.getDataElementMap().get( dsId ).getAggOperator();
				} else {
					agOp = varMap.getAggOperator();
				}

				if ((agOp != null && agOp.getId() != AggregationOperator.AGG_OPERATOR_NA) ||
					(variable.getNonDriverAggOperator() != null && variable.getNonDriverAggOperator().getId() != AggregationOperator.AGG_OPERATOR_NA)) {
					isRepeating = false;
				}
			}
			
			if ( isRepeating && varMap != null && !variable.isExpressionVariable() && !variable.isScriptVariable() ) {

				DataGroup appliedDataGroup = null;
				if ( zoneId > 0 ) {
					Zone zone = Zone.findById(zoneId);
					appliedDataGroup = zone.getDataGroup();
				}
				// OVERRIDE DATA GROUP: Repeating tables
				if ( dataGroupId > 0 )
					appliedDataGroup = DataGroup.findById(dataGroupId);

				if ( appliedDataGroup == null ) {
					zoneCompatible = false;
					errorMsgs.put(ApplicationUtil.getMessage("error.no.data.group.set.for.zone"));
				} else if ( varMap.getLevel() == null && !appliedDataGroup.isRecepientLevel() ) {
					zoneCompatible = false;
					errorMsgs.put(ApplicationUtil.getMessage("error.level.not.set.for.variable"));
				} else if ( appliedDataGroup.getLevel() < (varMap.getLevel() == null ? 1 : varMap.getLevel().getValue()) ) {
					zoneCompatible = false;
					errorMsgs.put(ApplicationUtil.getMessage("error.level.of.variable.greater.than.zone.level"));
				}
				
				DataGroup variableDataGroup = variable.getDataGroup(document);
				
				if ( variableDataGroup == null ) {
					matchCompatible = false;
					incompatibleVarDNA = variableDNA;
					errorMsgs.put(ApplicationUtil.getMessage("error.no.data.group.set.for.variable"));
				}

				for ( int i=0; i < matchVariableDNAs.length; i++ ) {
					DataElementVariable matchVariable 	= DataElementVariable.findByDna(matchVariableDNAs[i]);
					if ( matchVariable != null ) {
						if ( matchVariable.isRepeating() ) {
							
							VariableDataElementMap matchVarMap = matchVariable.getDataElementMap().get( dataSourceId );

							if ( matchVarMap.getLevel() == null && !appliedDataGroup.isRecepientLevel() ) {
								incompatibleVarDNA = matchVariable.getDna();
								matchCompatible = false;
								errorMsgs.put(MessageFormat.format(ApplicationUtil.getMessage("error.level.not.set.for.row.variable"), matchVariable.getDisplayName() ));
							} else if ( (varMap.getLevel() != null && varMap.getLevel() != matchVarMap.getLevel()) || 
									    (varMap.getLevel() == null && matchVarMap.getLevel() != null) ) {
								incompatibleVarDNA = matchVariable.getDna();
								matchCompatible = false;
								errorMsgs.put(ApplicationUtil.getMessage("error.aggregation.level.must.match.for.repeating.variables"));
							}
							
							DataGroup matchVariableDataGroup = matchVariable.getDataGroup(document);
							if ( matchVariableDataGroup == null ) {
								incompatibleVarDNA = matchVariable.getDna();
								matchCompatible = false;
								errorMsgs.put(MessageFormat.format(ApplicationUtil.getMessage("error.no.data.group.set.for.variable"), matchVariable.getDisplayName() ));
							} else if ( variableDataGroup != null && variableDataGroup != matchVariableDataGroup ) {
								incompatibleVarDNA = matchVariable.getDna();
								matchCompatible = false;
								errorMsgs.put(ApplicationUtil.getMessage("error.data.groups.must.match.for.repeating.variables"));
							}
							
						}
					}
				}
			}

			returnObj.put( "variable_dna"				, variableDNA );
			returnObj.put( "zone_compatible"			, zoneCompatible );
			returnObj.put( "variable_compatible"		, matchCompatible );
			returnObj.put( "incompatible_dna"			, incompatibleVarDNA );
			returnObj.put( "is_repeating"				, isRepeating );
			returnObj.put( "errors"						, errorMsgs );

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve variable compatibility: " + e );
		}

		return returnObj.toString();

	}

	private String getDataSourceElements(HttpServletRequest request) {
		String returnData = "";

		long dataSourceId = ServletRequestUtils.getLongParameter(request, PARAM_DATA_SOURCE_ID, -1);
		boolean applyOptionNone		 = ServletRequestUtils.getBooleanParameter(request, PARAM_APPLY_OPTION_NONE, false);
		long selectedElementId			= ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_ELEMENT_ID, 0);
		int numCap 					= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
		String nameSearch 			= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);


		StringBuilder varOptions = new StringBuilder();

		try {
			AbstractDataElement selectedElement = AbstractDataElement.findById(selectedElementId);
			//Find primary data elements
			List<AbstractDataElement> allDataElements = AbstractDataElement.findAllByDataSource(nameSearch, dataSourceId, SourceType.TYPE_PRIMARY, numCap);
			long fullListCount = AbstractDataElement.getCountByDataSourceAndSourceType(dataSourceId, SourceType.TYPE_PRIMARY);
			int elementCount = allDataElements.size();

			if (allDataElements.isEmpty()) {
				return 	"<?xml version='1.0'?><content>" +
						"<option id=\"0\" value=\"0\">" +
						TagUtils.formatLabel("page.text.no.variables") +
						"</option>" +
						"</content>";
			}

			if ( applyOptionNone )
				varOptions.append("<option id=\"0\" value=\"0\" " + "isRepeating=\"false\" " + "dataSubtype=\"0\" class=\"fixedOption\">").append(ApplicationUtil.getMessage("page.label.none")).append("</option>");

			if (allDataElements.contains(selectedElement)) {
				allDataElements.remove(selectedElement);
			}

			if (selectedElement == null) {
				varOptions.append("<option value=\"-1\" selected=\"selected\">-- ").append(ApplicationUtil.getMessage("page.label.no.selection")).append(" --</option>");
			} else {
				varOptions.append("<option id=\"").append(selectedElement.getId()).append("\" value=\"").append(selectedElement.getId()).append("\" ").append("dataSubtype=\"").append(selectedElement.getDataSubtypeId()).append("\" class=\"fixedOption\">").append(selectedElement.getName()).append("</option>");
			}

			for (AbstractDataElement dataElement: allDataElements) {
				varOptions.append("<option id=\"").append(dataElement.getId()).append("\" value=\"").append(dataElement.getId()).append("\">").append(dataElement.getName()).append("</option>");
			}

			returnData = "<options selectedOptionId=\"" + selectedElementId + "\" fullListCount=\"" + fullListCount + "\" displayListCount=\"" + elementCount + "\">" + StringEscapeUtils.escapeXml(varOptions.toString()) + "</options>";

		} catch (Exception ex) {
			log.error("Error:", ex);
		}

		return "<?xml version='1.0'?><content>" + returnData + "</content>";
	}

	private String getVariablesListResponseXML (HttpServletRequest request) {
		String returnData = "";
		
		StringBuilder varOptions= new StringBuilder();
		
		List<DataElementVariable> varList = null;

		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		long dataSourceId			= ServletRequestUtils.getLongParameter(request, PARAM_DATA_SOURCE_ID, 0);
		String nameSearch 			= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);
		int numCap 					= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
		long selectedVarId			= ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_VARIABLE_ID, 0);
		int dataSubtypeId			= ServletRequestUtils.getIntParameter(request, PARAM_DATA_SUBTYPE_ID, 0);
		Boolean isTargetingFiltered = ServletRequestUtils.getBooleanParameter(request, PARAM_TARGETING_FILTERED, false);
		boolean applyOptionNone		 = ServletRequestUtils.getBooleanParameter(request, PARAM_APPLY_OPTION_NONE, false);

		List<Document> documents = new ArrayList<>();
		int fullListCount = 0;
		if ( documentId > 0 ) {
			documents.add(Document.findById(documentId));
			varList = DataElementVariable.findAllForDocumentFiltered(documents, null, dataSubtypeId, nameSearch, isTargetingFiltered, false, false, numCap);
			fullListCount = DataElementVariable.getAllForDocumentFilteredCount(documents, null, dataSubtypeId, nameSearch, isTargetingFiltered, false, false, numCap);
		} else if (dataSourceId > 0) {
			DataSource ds = DataSource.findById(dataSourceId);

			varList = StringUtils.isEmpty(nameSearch) ?
					DataElementVariable.findAllForDataSource(ds, numCap) :
					DataElementVariable.findAllForDataSource(ds, nameSearch, numCap);

			fullListCount = (StringUtils.isEmpty(nameSearch) ?
					DataElementVariable.findAllForDataSource(ds, -1) :
					DataElementVariable.findAllForDataSource(ds, nameSearch, -1)).size();


		} else {
			List<Document> allVisibleDocuments = Document.findAllDocumentsAndProjectsVisible();
			varList = DataElementVariable.findAllForDocumentFiltered(allVisibleDocuments, null, dataSubtypeId, nameSearch, isTargetingFiltered, false, false, numCap);
			fullListCount = DataElementVariable.getAllForDocumentFilteredCount(allVisibleDocuments, null, dataSubtypeId, nameSearch, isTargetingFiltered, false, false, numCap);
		}
		
		int varListCount = varList != null && !varList.isEmpty() ? varList.size() : 0;
		if (varList != null && !varList.isEmpty()) {


			DataElementVariable selectedVariable = DataElementVariable.findById(selectedVarId);

			// -99 is a special case, compound key in data source association edit
			if (selectedVarId != -99) {
				if (selectedVariable == null) {
					selectedVariable = varList.get(0);
				} else if (dataSubtypeId > 0 && selectedVariable.getDataSubtypeId() != dataSubtypeId) {
					if (varList.isEmpty())
						return 	"<?xml version='1.0'?><content>" +
								"<option id=\"0\" value=\"0\">" +
								TagUtils.formatLabel("page.text.no.variables") +
								"</option>" +
								"</content>";
					else
						selectedVariable = varList.get(0);
				}

				if (varList.contains(selectedVariable))
					varList.remove(selectedVariable);
			}

			if ( applyOptionNone )
				varOptions.append("<option id=\"0\" value=\"0\" " + "isRepeating=\"false\" " + "dataSubtype=\"0\" class=\"fixedOption\">").append(ApplicationUtil.getMessage("page.label.none")).append("</option>");

			if (selectedVariable != null) {
				varOptions.append("<option id=\"").append(selectedVariable.getId()).append("\" value=\"").append(selectedVariable.getId()).append("\" ").append("isRepeating=\"").append(selectedVariable.isRepeating()).append("\" ").append("dataSubtype=\"").append(selectedVariable.getDataSubtypeId()).append("\" class=\"fixedOption\">").append(selectedVariable.getName()).append("</option>");
			}

			for (DataElementVariable currentVariable: varList) {
				varOptions.append("<option id=\"").append(currentVariable.getId()).append("\" value=\"").append(currentVariable.getId()).append("\" ").append("isRepeating=\"").append(currentVariable.isRepeating()).append("\" ").append("dataSubtype=\"").append(currentVariable.getDataSubtypeId()).append("\">").append(currentVariable.getName()).append(currentVariable.isRepeating() ? "*" : "").append("</option>");
			}
		} else {
			varOptions.append("<option id=\"0\" value=\"0\">").append(TagUtils.formatLabel("page.text.no.variables")).append("</option>");
		}
		returnData = "<options selectedOptionId=\"" + selectedVarId + "\" fullListCount=\"" + fullListCount + "\" displayListCount=\"" + varListCount + "\">" + StringEscapeUtils.escapeXml(varOptions.toString()) + "</options>";

		return "<?xml version='1.0'?><content>" + returnData + "</content>";
	}
}