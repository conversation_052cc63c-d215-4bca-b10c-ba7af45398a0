package com.prinova.messagepoint.controller.projects;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.project.ProjectTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowAction;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class ProjectWorkflowManagementController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(ProjectWorkflowManagementController.class);

	public static final String REQ_PARM_PROJECT_ID				= "projectId";
	public static final String REQ_PARM_WORKFLOW_ID				= "workflowId";
	public static final String REQ_PARM_ACTION					= "submittype";

	public static final int ACTION_APPLY						= 1;
	
	public static final int TASK_ACTION_APPROVE					= 1;
	public static final int TASK_ACTION_REJECT		 			= 2;
	public static final int TASK_ACTION_REJECT_BACK_ONE			= 3;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors){
		Map<String, Object> referenceData = new HashMap<>();
		referenceData.put("availUsers", User.findAllActiveUsersSorted());
		
		long projectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_PROJECT_ID, -1);
		Project project = Project.findById(projectId);
		User principalUser = UserUtil.getPrincipalUser();
		Map<Long, Boolean> taskCanApproveMap = new HashMap<>();
		ConfigurableWorkflowInstance wfInst = project.getWorkflowInstance();
		boolean isWorkflowOwner	= wfInst != null && wfInst.getOwnerId() != null && wfInst.getOwnerId().longValue() == principalUser.getId();
		for(ProjectTask projectTask : project.getProjectTasksOrdered()){
			Task task = projectTask.getTask();
			if(isWorkflowOwner){
				taskCanApproveMap.put(task.getId(), true);
				continue;
			}
			
			ConfigurableWorkflowAction wfAction = task.getWorkflowAction();
			boolean isUserInApprovalList = wfAction != null && wfAction.getActionApprovers().stream().anyMatch(u->u.getId()==principalUser.getId());
			taskCanApproveMap.put(task.getId(), isUserInApprovalList);
		}
		referenceData.put("taskCanApproveMap", taskCanApproveMap);
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception{
		binder.registerCustomEditor(Task.class, new IdCustomEditor<>(Task.class));
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		long projectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_PROJECT_ID, -1);
		Project project = Project.findById(projectId);
		return new ProjectWorkflowManagementWrapper(project);
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		ProjectWorkflowManagementWrapper c = (ProjectWorkflowManagementWrapper)command;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
		case (ACTION_APPLY):{
			List<Task> list = c.getSelectedList(); 
			List<Task> tasksToApprove = new ArrayList<>();
			List<Task> tasksToReject = new ArrayList<>();
			List<Task> tasksToRejectBackOne = new ArrayList<>();
			for(Task task : list){
				switch(c.getTaskActions().get(task)){
					case TASK_ACTION_APPROVE:{
						tasksToApprove.add(task);
						break;
					}
					case TASK_ACTION_REJECT:{
						tasksToReject.add(task);
						break;
					}
					case TASK_ACTION_REJECT_BACK_ONE:{
						tasksToRejectBackOne.add(task);
						break;
					}
					default:
						break;
				}
			}
			
			if(!tasksToApprove.isEmpty()){
				ServiceExecutionContext context = WorkflowApproveService.createContext(requestor, "", false, tasksToApprove.toArray(new Task[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowApproveService.SERVICE_NAME, WorkflowApproveService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowApproveService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (Task task : tasksToApprove) {
						sb.append(" id ").append(task.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  tasks are not approved. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				}
			}
			if(!tasksToReject.isEmpty()){
				Map<Long, String> rejectionNote = new HashMap<>();
				for(Task task : tasksToReject){
					rejectionNote.put(task.getId(), c.getTaskRejectionNotes().get(task));
				}
				ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, null, rejectionNote, false, tasksToReject.toArray(new Task[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowRejectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (Task task : tasksToReject) {
						sb.append(" id ").append(task.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  tasks are not rejected. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				}
			}
			if(!tasksToRejectBackOne.isEmpty()){
				Map<Long, String> rejectionNote = new HashMap<>();
				for(Task task : tasksToRejectBackOne){
					rejectionNote.put(task.getId(), c.getTaskRejectionNotes().get(task));
				}
				ServiceExecutionContext context = WorkflowRejectService.createContext(requestor, null, rejectionNote, true, tasksToRejectBackOne.toArray(new Task[]{}));
				Service service = MessagepointServiceFactory.getInstance()
						.lookupService(WorkflowRejectService.SERVICE_NAME, WorkflowRejectService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(WorkflowRejectService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for (Task task : tasksToRejectBackOne) {
						sb.append(" id ").append(task.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					sb.append("  tasks are not rejected back one step. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				}
			}
		}		
		default:
			break;
		}
		
		Map<String, Object> parms = new HashMap<>();
		parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
	}
}
