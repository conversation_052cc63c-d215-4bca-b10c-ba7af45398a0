package com.prinova.messagepoint.controller.projects;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.projects.CreateOrUpdateProjectService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class ProjectEditController extends MessagepointController {
	private static final Log log = LogUtil.getLog(ProjectEditController.class);
	
	public static final String REQ_PARM_PROJECT_ID				= "projectId";
	public static final String REQ_PARAM_METADATA_ID 			= "metadataId";
	public static final String REQ_PARAM_WORKFLOW_ID 			= "workflowId";
	public static final String REQ_PARAM_SAVE_SUCCESS			= "saveSuccess";

	public static final String FORM_SUBMIT_TYPE_SUBMIT 			= "submit";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		long projectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_PROJECT_ID, -1);
    	referenceData.put("newProjectTaskId", Long.MAX_VALUE);
    	referenceData.put("removedProjectTaskId", Long.MIN_VALUE);
		referenceData.put("availUsers", User.findAllActiveUsersSorted());
		referenceData.put("ownerUsers", User.findEnabledUsersByPermission(Permission.ROLE_PROJECT_EDIT));
		referenceData.put("availTasks", Task.findNotInUseForProject(projectId));
		
		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception{
		binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(ConfigurableWorkflowStep.class, new IdCustomEditor<>(ConfigurableWorkflowStep.class));
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		long projectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_PROJECT_ID, -1);
		long metadataId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_METADATA_ID, -1);
		long workflowId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_WORKFLOW_ID, -1);
		Project project = Project.findById(projectId);
		ProjectEditWrapper wrapper = null;
		if ( project != null ){	// Existing project
			wrapper = new ProjectEditWrapper(project, isFormSubmission(request));
		}else{	// New project
			wrapper = new ProjectEditWrapper(MetadataFormDefinition.findById(metadataId), ConfigurableWorkflow.findById(workflowId), isFormSubmission(request));
		}
		return wrapper;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		ProjectEditWrapper wrapper = (ProjectEditWrapper) command;
		User requestor = UserUtil.getPrincipalUser();
		Project project = wrapper.getProject();
		project.setOwnerId(wrapper.getOwner().getId());
		
		ServiceExecutionContext context = CreateOrUpdateProjectService.createContextForCreateOrUpdate(project, wrapper.getProjectTasks(), wrapper.getFormWrapper(), wrapper.getStartDate(), wrapper.getDueDate(), requestor);	
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateProjectService.SERVICE_NAME, CreateOrUpdateProjectService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {	
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateProjectService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Project was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}
	
 	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
	}
}
