package com.prinova.messagepoint.controller.projects;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;

public class ProjectListWrapper implements Serializable{
	private static final long serialVersionUID = 1426782966943475175L;

	private List<Long>			selectedIds;
	private String 				actionValue;
	private User				changeOwnerToUser;
	private Boolean				includeAllProjects = false;
	
	public ProjectListWrapper() {
		super();
		this.selectedIds = new ArrayList<>();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public User getChangeOwnerToUser() {
		return changeOwnerToUser;
	}
	public void setChangeOwnerToUser(User changeOwnerToUser) {
		this.changeOwnerToUser = changeOwnerToUser;
	}

	public List<Project> getSelectedList(){
		List<Project> selectedList = new ArrayList<>();
		if(this.selectedIds == null)
			return selectedList;
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(Project.class, selectedId));
		}
		return selectedList;
	}

	public Boolean isIncludeAllProjects() {
		return includeAllProjects;
	}

	public void setIncludeAllProjects(Boolean includeAllProjects) {
		this.includeAllProjects = includeAllProjects;
	}
}
