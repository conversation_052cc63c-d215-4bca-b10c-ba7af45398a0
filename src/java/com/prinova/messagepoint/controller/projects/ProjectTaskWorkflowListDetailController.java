package com.prinova.messagepoint.controller.projects;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.util.HibernateUtil;

public class ProjectTaskWorkflowListDetailController implements Controller {

	private String successView;
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long workflowId = ServletRequestUtils.getLongParameter(request, ProjectTaskWorkflowListController.REQ_PARM_WORKFLOW_ID, -1L);
		
		ConfigurableWorkflow workflow = HibernateUtil.getManager().getObject(ConfigurableWorkflow.class, workflowId);
		params.put("workflow", workflow);

		return new ModelAndView(getFormView(), params);
	}
	
	public String getSuccessView() {
		return successView;
	}
	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
