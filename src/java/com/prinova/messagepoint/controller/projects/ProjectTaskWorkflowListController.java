package com.prinova.messagepoint.controller.projects;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.tasks.BulkDeleteTaskService;
import com.prinova.messagepoint.platform.services.workflow.BulkDeleteWorkflowService;
import com.prinova.messagepoint.util.UserUtil;

public class ProjectTaskWorkflowListController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(ProjectTaskWorkflowListController.class);

	public static final String REQ_PARM_WORKFLOW_ID				= "workflowId";
	public static final String REQ_PARM_ACTION					= "action";

	public static final int ACTION_EDIT							= 1;
	public static final int ACTION_DELETE		 				= 2;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors){
		Map<String, Object> referenceData = new HashMap<>();
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception{
		
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		return new ProjectTaskWorkflowListWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		ProjectTaskWorkflowListWrapper c = (ProjectTaskWorkflowListWrapper)command;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
		case (ACTION_DELETE):{
			// ACTION_DELETE: - delete the workflow(s)
			List<ConfigurableWorkflow> list = c.getSelectedList(); 
			ServiceExecutionContext context = BulkDeleteWorkflowService.createContext(list, requestor);
			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteWorkflowService.SERVICE_NAME,
					BulkDeleteWorkflowService.class);				
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(BulkDeleteTaskService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for(ConfigurableWorkflow workflow: list){
					sb.append(" id ").append(workflow.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				return new ModelAndView(new RedirectView(getSuccessView()));
			}
		}
		default:
			break;
		}
		return null;
	}
}
