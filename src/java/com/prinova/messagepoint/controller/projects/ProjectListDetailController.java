package com.prinova.messagepoint.controller.projects;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.util.HibernateUtil;

public class ProjectListDetailController implements Controller{

	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long projectId = ServletRequestUtils.getLongParameter(request, ProjectListController.REQ_PARM_PROJECT_ID, -1L);
		
		Project project = HibernateUtil.getManager().getObject(Project.class, projectId);
		params.put("project", project);

		return new ModelAndView(getFormView(), params);
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
	
}
