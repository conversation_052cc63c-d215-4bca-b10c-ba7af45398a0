package com.prinova.messagepoint.controller.projects;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditValidator;
import com.prinova.messagepoint.model.project.ProjectTask;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ProjectEditValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		ProjectEditWrapper command = (ProjectEditWrapper)commandObj;
		
		if(command.getFormWrapper() != null){
			MetadataFormEditValidator.validateMandatoryInputs(command.getFormWrapper(), errors);
		}
		
		// End date can't be earlier than start date
		if(command.getStartDate() != null && command.getDueDate() != null){
			if (command.getStartDate().after(command.getDueDate())) {
				errors.reject("error.message.end.date.before.start.date");
			}
		}
			
		for(ProjectTask projectTask : command.getProjectTasks()){
			if(projectTask.getAppliedWorkflowSteps() == null || projectTask.getAppliedWorkflowSteps().isEmpty()){
				errors.reject("error.project.task.at.least.one.step");
				return;
			}
		}
	}
}
