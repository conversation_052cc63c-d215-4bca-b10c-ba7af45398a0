package com.prinova.messagepoint.controller.projects;

import java.util.List;
import java.util.function.Predicate;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.wrapper.AsyncProjectWorkflowsListVO;
import com.prinova.messagepoint.model.wrapper.AsyncProjectWorkflowsListWrapper;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ProjectTaskWorkflowListValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		ProjectTaskWorkflowListWrapper wrapper = (ProjectTaskWorkflowListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue());
		}

		switch (action) {
			case ProjectTaskWorkflowListController.ACTION_EDIT:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncProjectWorkflowsListVO.ProjectWorkflowsListVOFlags::isCanUpdate);
				break;
			case ProjectTaskWorkflowListController.ACTION_DELETE:
				validateActionPermission(wrapper.getSelectedList(), errors, "error.message.action.not.permitted", AsyncProjectWorkflowsListVO.ProjectWorkflowsListVOFlags::isCanRemove);
				break;
			default:
				break;
		}
		
		if ( action == ProjectTaskWorkflowListController.ACTION_DELETE) {
			List<ConfigurableWorkflow> selecteds = wrapper.getSelectedList();
			StringBuilder referencedWorkflowNames = new StringBuilder();
			boolean referenced = false;
			
			for (ConfigurableWorkflow selected: selecteds)
				if ( selected.isReferenced() ) {
					referenced = true;
					referencedWorkflowNames.append(selected.getName()).append(" ");
				}

			if (referenced)
				errors.reject(	"error.message.cannot.delete.referenced.workflows", 
								new String[] {referencedWorkflowNames.toString()},
								"The following workflow(s) are referenced and cannot be deleted: " + referencedWorkflowNames);
		}

	}
	private void validateActionPermission(List<ConfigurableWorkflow> configurableWorkflowList, Errors errors, String errorMessage, Predicate<AsyncProjectWorkflowsListVO.ProjectWorkflowsListVOFlags> flagChecker) {

		// Admin bypass - Feature flag hidden toggle
		if ( !Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) )
			return;

		if ( configurableWorkflowList.isEmpty() )
			errors.reject(errorMessage);

		for (ConfigurableWorkflow configurableWorkflow : configurableWorkflowList) {
			AsyncProjectWorkflowsListVO vo = new AsyncProjectWorkflowsListVO();
			vo.setWorkflow(configurableWorkflow);

			AsyncProjectWorkflowsListVO.ProjectWorkflowsListVOFlags flags = new AsyncProjectWorkflowsListVO.ProjectWorkflowsListVOFlags();
			AsyncProjectWorkflowsListWrapper.setActionFlags(flags);
			if (!flagChecker.test(flags)) {
				errors.reject(errorMessage);
				break;
			}
		}
	}
}
