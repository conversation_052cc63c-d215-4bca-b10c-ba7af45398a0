package com.prinova.messagepoint.controller.projects;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;

import com.prinova.messagepoint.controller.metadata.MetadataFormEditWrapper;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.project.ProjectTask;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowInstance;
import com.prinova.messagepoint.util.DateUtil;

@SuppressWarnings("unchecked")
public class ProjectEditWrapper implements Serializable{
	private static final long serialVersionUID = -2273242146077616505L;
	
	private Project 						project;
	private Date 							startDate;
	private Date 							dueDate;
	private MetadataFormEditWrapper 		formWrapper;
	private List<ProjectTask>				projectTasks;
	private User 							owner;
	
	public ProjectEditWrapper(){
		super();
	}
	
	public ProjectEditWrapper(MetadataFormDefinition metadataFormDefinition, ConfigurableWorkflow workflow, boolean lazyDecorate){
		this.project = new Project();
		this.project.setStatusId((int) TaskStatus.ID_ACTIVE);
		this.setStartDate(DateUtil.today());
		if(metadataFormDefinition != null){
			this.formWrapper = new MetadataFormEditWrapper(metadataFormDefinition);
		}
		if(workflow != null){
			ConfigurableWorkflowInstance workflowInstance = workflow.findActiveInstance();
			this.project.setWorkflowInstance(workflowInstance);
		}
		
		if ( lazyDecorate ) {
			projectTasks = LazyList.decorate(new ArrayList<ProjectTask>(), FactoryUtils.instantiateFactory(ProjectTask.class));
		} else {
			projectTasks = new ArrayList<>();
		}
	}
	
	public ProjectEditWrapper(Project project, boolean lazyDecorate){
		this.project = project;
		this.setStartDate(project.getStartDate());
		this.setDueDate(project.getDueDate());
		if(project.getOwnerId() != null){
			this.setOwner(User.findById(project.getOwnerId()));
		}
		if(this.project.getMetadataForm() != null){
			this.formWrapper = new MetadataFormEditWrapper(this.project.getMetadataForm());
		}
		
		if ( lazyDecorate ) {
			projectTasks = LazyList.decorate(new ArrayList<ProjectTask>(), FactoryUtils.instantiateFactory(ProjectTask.class));
		} else {
			projectTasks = new ArrayList<>();
		}
		
		projectTasks.addAll(this.project.getProjectTasksOrdered());
	}
	
	public Project getProject() {
		return project;
	}

	public void setProject(Project project) {
		this.project = project;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getDueDate() {
		return dueDate;
	}

	public void setDueDate(Date dueDate) {
		this.dueDate = dueDate;
	}

	public MetadataFormEditWrapper getFormWrapper() {
		return formWrapper;
	}

	public void setFormWrapper(MetadataFormEditWrapper formWrapper) {
		this.formWrapper = formWrapper;
	}

	public List<ProjectTask> getProjectTasks() {
		return projectTasks;
	}

	public void setProjectTasks(List<ProjectTask> projectTasks) {
		this.projectTasks = projectTasks;
	}

	public User getOwner() {
		return owner;
	}

	public void setOwner(User owner) {
		this.owner = owner;
	}
}
