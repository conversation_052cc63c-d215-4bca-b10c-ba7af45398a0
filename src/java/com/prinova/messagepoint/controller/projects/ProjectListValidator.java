package com.prinova.messagepoint.controller.projects;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ProjectListValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		
		ProjectListWrapper command = (ProjectListWrapper)commandObj;
					
	}
}
