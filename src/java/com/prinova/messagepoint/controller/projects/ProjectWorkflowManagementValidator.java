package com.prinova.messagepoint.controller.projects;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ProjectWorkflowManagementValidator extends MessagepointInputValidator {
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		ProjectWorkflowManagementWrapper wrapper = (ProjectWorkflowManagementWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}

	}
}
