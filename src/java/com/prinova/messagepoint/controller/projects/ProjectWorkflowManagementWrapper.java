package com.prinova.messagepoint.controller.projects;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.task.Task;

public class ProjectWorkflowManagementWrapper implements Serializable{

	private static final long serialVersionUID = 2704439145523634678L;

	private Project 			project;
	private String 				actionValue;
	private Map<Task, Integer>	taskActions;
	private Map<Task, String>	taskRejectionNotes;
	
	public ProjectWorkflowManagementWrapper(Project project) {
		super();
		this.project = project;
	}
	
	public Project getProject() {
		return project;
	}

	public void setProject(Project project) {
		this.project = project;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public Map<Task, Integer> getTaskActions() {
		return taskActions;
	}

	public void setTaskActions(Map<Task, Integer> taskActions) {
		this.taskActions = taskActions;
	}
	
	public Map<Task, String> getTaskRejectionNotes() {
		return taskRejectionNotes;
	}

	public void setTaskRejectionNotes(Map<Task, String> taskRejectionNotes) {
		this.taskRejectionNotes = taskRejectionNotes;
	}

	public List<Task> getSelectedList(){
		List<Task> selectedList = new ArrayList<>();
		for(Task task : this.taskActions.keySet()){
			if(this.taskActions.get(task) >= ProjectWorkflowManagementController.TASK_ACTION_APPROVE){
				selectedList.add(task);
			}
		}
		return selectedList;
	}
}
