package com.prinova.messagepoint.controller.projects;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.AuditReportEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.touchpoints.AssignmentFilterType;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.project.Project;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflowStep;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateProjectAuditReportService;
import com.prinova.messagepoint.platform.services.projects.BulkDeleteProjectService;
import com.prinova.messagepoint.platform.services.projects.CreateOrUpdateProjectService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class ProjectListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(ProjectListController.class);
	
	public static final String REQ_PARM_PROJECT_ID				= "projectId";
	public static final String REQ_PARM_ACTION					= "action";
	public static final String REQ_PARM_DOCUMENTID 				= "documentId";
	
	public static final int ACTION_EDIT							= 1;
	public static final int ACTION_DELETE		 				= 2;
	public static final int ACTION_CHANGE_OWNER	 				= 3;
	public static final int ACTION_EXPORT						= 16;

	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		
		referenceData.put("projectMetadataForms", MetadataFormDefinition.findByType(MetadataFormDefinitionType.ID_PROJECT));
		referenceData.put("projectWorkflows", ConfigurableWorkflow.findByModelType(ConfigurableWorkflow.PROJECT_TASK_WORKFLOW));
		
		// Filters
		List<AssignmentFilterType> primaryFilterTypes = AssignmentFilterType.listAllStandard();
		if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL)){
			primaryFilterTypes.remove(new AssignmentFilterType(AssignmentFilterType.ID_ALL));
			
			if(!UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY)){
				primaryFilterTypes.remove(new AssignmentFilterType(AssignmentFilterType.ID_MY));
			}			
		}
		referenceData.put("primaryFilterTypes", primaryFilterTypes);
		
		List<TaskStatus> projectStatusFilterTypes = TaskStatus.listAll();
		projectStatusFilterTypes.remove(new TaskStatus(TaskStatus.ID_NEAR_TERM));
		referenceData.put("projectStatusFilterTypes", projectStatusFilterTypes);
		AuditReport latestAuditReport = AuditReport.findLatestByUserId(UserUtil.getPrincipalUser().getId(), AuditReportType.ID_PROJECT_AUDIT_REPORT);
		if (latestAuditReport != null) {
			referenceData.put("auditReport", latestAuditReport);
		}
		
		List<User> reassignmentUsers = User.findEnabledUsersByPermission(Permission.ROLE_PROJECT_EDIT);
		referenceData.put("reassignmentUsers", reassignmentUsers);
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception{
		binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
		binder.registerCustomEditor(ConfigurableWorkflowStep.class, new IdCustomEditor<>(ConfigurableWorkflowStep.class));
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception{
		return new ProjectListWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception{
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		ProjectListWrapper c = (ProjectListWrapper)command;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
		case (ACTION_DELETE):{
			// ACTION_DELETE: - delete the project(s)
			List<Project> list = c.getSelectedList(); 
			ServiceExecutionContext context = BulkDeleteProjectService.createContext(list);
			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteProjectService.SERVICE_NAME,
					BulkDeleteProjectService.class);				
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(BulkDeleteProjectService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for(Project project: list){
					sb.append(" id ").append(project.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), this.getSuccessViewParms(request));
			}
		}
		case (ACTION_CHANGE_OWNER): {

			List<Project> list = c.getSelectedList();
			
			ServiceExecutionContext context = CreateOrUpdateProjectService.createContextForReassign(list.get(0), c.getChangeOwnerToUser(), requestor);	
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateProjectService.SERVICE_NAME, CreateOrUpdateProjectService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (serviceResponse.isSuccessful()) {	
				return new ModelAndView(new RedirectView(getSuccessView()), this.getSuccessViewParms(request));
			} else {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateProjectService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" Project was not reassigned. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			}
			
		}
		case (ACTION_EXPORT): {
			AnalyticsEvent<AuditReportEvents> auditEvent = AnalyticsUtil.requestFor(AuditReportEvents.Project);
			auditEvent.setAction(Actions.GenerateReport);
			try {
				List<Long> projectList = c.getSelectedIds();
				boolean includeAllProjects = false;
				if ((projectList == null || projectList.isEmpty()) || (!projectList.isEmpty() && c.isIncludeAllProjects())) {
					includeAllProjects = true;
				}
				if (includeAllProjects) {
					for (Project proj : Project.findAllActive()) {
						if (!projectList.contains(proj.getId()))
							projectList.add(proj.getId());
					}
				}
				int auditReportType = AuditReportType.ID_PROJECT_AUDIT_REPORT;

				ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForCreate(principal.getId(), auditReportType);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateAuditReportService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" requestor = ").append(requestor.getUsername());
					sb.append(" AuditReport object could not be created. ");
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					long auditReportId = (Long) serviceResponse.getResultValueBean();
					AuditReportType type = new AuditReportType(auditReportType);
					String typeName = type.getName();

					ServiceExecutionContext reportContext = GenerateProjectAuditReportService.createContext(auditReportId, projectList, includeAllProjects, requestor);
					Service reportService = MessagepointServiceFactory.getInstance().lookupService(GenerateProjectAuditReportService.SERVICE_NAME, GenerateProjectAuditReportService.class);
					reportService.execute(reportContext);
					serviceResponse = reportContext.getResponse();
					if (!serviceResponse.isSuccessful()) {

						//					context = CreateOrUpdateAuditReportService.createContextForError(auditReportId);
						//					service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
						//					service.execute(context);

						// Audit (Audit Report Failed)
						AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, typeName, auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_FAIL, null);

						StringBuilder sb = new StringBuilder();
						sb.append(GenerateProjectAuditReportService.SERVICE_NAME);
						sb.append(" service call is not successful ");
						sb.append(" in ").append(this.getClass().getName());
						sb.append(" requestor = ").append(requestor.getUsername());
						sb.append(" AuditReport object could not be generated. ");
						log.error(sb.toString());
						ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
						return showForm(request, response, errors);
					} else {
						// Audit (Audit Report Success)
						AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, typeName, auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_SUCCEED, null);

						return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParms(request));
					}
				}
			}finally {
				auditEvent.send();
			}
		}
		default:
			break;
		}
		return null;
	}
	
	private Map<String, Object> getSuccessViewParms(HttpServletRequest request) throws Exception {
		Map<String, Object> parms = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);

		if (documentId != -1L) {
			parms.put(REQ_PARM_DOCUMENTID, documentId);
		} else {
			List<Document> visibleDocuments = Document.getVisibleDocuments();
			parms.put(REQ_PARM_DOCUMENTID, !visibleDocuments.isEmpty() ? visibleDocuments.iterator().next().getId() : -2);
		}

		return parms;
	}
	
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		// ******* Start User context persist/recall ************
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENTID, -1L);

		if (documentId == -2) {
			return super.showForm(request, response, errors);
		} else if (documentId != -1) { // Secondary access: Navigating post entry
			
			// Context validation
			ModelAndView contextMismatchView = UserUtil.getViewForTouchpointContextMismatch(documentId, getSuccessView(), getSuccessViewParms(request), true);
			if ( contextMismatchView != null )
				return contextMismatchView;
			
		} else { // First access: Direct traffic
			return UserUtil.getViewForContextTouchpoint(getSuccessView(), getSuccessViewParms(request), true);
		}
		// ******* End User context persist/recall **************
		
		return super.showForm(request, response, errors);
	}
}
