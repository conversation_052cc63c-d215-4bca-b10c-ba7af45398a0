package com.prinova.messagepoint.controller.projects;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.util.HibernateUtil;

public class ProjectTaskWorkflowListWrapper implements Serializable{

	private static final long serialVersionUID = 2704439145523634678L;

	private List<Long>			selectedIds;
	private String 				actionValue;
	
	public ProjectTaskWorkflowListWrapper() {
		super();
		this.selectedIds = new ArrayList<>();
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public List<ConfigurableWorkflow> getSelectedList(){
		List<ConfigurableWorkflow> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(ConfigurableWorkflow.class, selectedId));
		}
		return selectedList;
	}
}
