package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.model.testing.TestSuite;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;

import java.util.Map;

public class AsyncTestResultController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncTestResultController.class);

	public static final String PARAM_TEST_ID					= "testId";
	public static final String PARAM_TEST_SUITE_ID		= "testSuiteId";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getTestResultResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for test scenario data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getTestResultResponseJSON (HttpServletRequest request) {

		long testId = ServletRequestUtils.getLongParameter(request, PARAM_TEST_ID, 0);
		long testSuiteId = ServletRequestUtils.getLongParameter(request, PARAM_TEST_SUITE_ID, 0);
		boolean invalidTest = true;
			
		JSONObject returnObj = new JSONObject();
		
		try {
			
			if (testId != 0) {
				TestScenario test = TestScenario.findById(testId);
				if (test != null) {
					invalidTest = false;
					Boolean complete = false;
					DeliveryEvent deliveryEvent = test.getDeliveryEvent();

					if (test.isComplete()) {  // Complete jobs
						complete = true;
						returnObj.put("delivery_event_id", deliveryEvent.getId());
						returnObj.put("item_class", test.getClass().getName());
					}

					if (test.getOutputLink() != null && !test.getOutputLink().isEmpty()) {
						String resource = HttpRequestUtil.buildResourceToken()
								.add("file", test.getOutputLink())
								.getValue();

						if (test.getDocument() != null) { // Target is "Touchpoint"
							if (!(test.getDocument().isEmailTouchpoint() || test.getDocument().isSmsTouchpoint())) {  // TPs other than the Email and SMS
								returnObj.put("output_path", test.getOutputLink());
								returnObj.put("resource", resource);
							}
						} else {  // Target is "Collection"
							returnObj.put("output_path", test.getOutputLink());
							returnObj.put("resource", resource);
						}
					}

					returnObj.put("completed", complete);
					returnObj.put("test_id", testId);

					if (deliveryEvent != null && deliveryEvent.getJob() != null) {
						returnObj.put("job_id", deliveryEvent.getJob().getId());
					}
					returnObj.put("status", test.getStatus());
				}
			} else if (testSuiteId != 0) {
				TestSuite testSuite = TestSuite.findById(testSuiteId);
				if (testSuite != null) {
					invalidTest = false;
					Boolean complete = false;
					DeliveryEvent deliveryEvent = testSuite.getDeliveryEvent();

					if (testSuite.isComplete()) {  // Complete jobs
						complete = true;
						returnObj.put("delivery_event_id", deliveryEvent.getId());
						returnObj.put("item_class", testSuite.getClass().getName());
					}

					returnObj.put("completed", complete);
					returnObj.put("test_suite_id", testSuiteId);

					if (deliveryEvent != null) {
						if (testSuite.isError() || deliveryEvent.isError()) {
							returnObj.put("has_error", true);
						}
						if (deliveryEvent.getJob() != null) {
							returnObj.put("job_id", deliveryEvent.getJob().getId());
						}
					}

					returnObj.put("status", getStatus(testSuite, deliveryEvent));
				}
			}

			if (invalidTest) {
				returnObj.put("error", ApplicationUtil.getMessage("error.message.invalid.test.param"));
			}

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve test status: " + e );
		}

		return returnObj.toString();
	}

	private String getStatus(TestSuite testSuite, DeliveryEvent deliveryEvent) {
		if (deliveryEvent != null) {
			Map<String, Integer> jobStatusMap = testSuite.getJobStatusMap();

			// if there is no status available yet, try again later
			if ((jobStatusMap == null || jobStatusMap.isEmpty()) && !(testSuite.isError() || testSuite.isComplete())) {
				return "<div id=\"testStatusPollingContainer_" + testSuite.getId() + "\">" + deliveryEvent.getStatusDisplayString() + "</div>";
			}

			// check if all Jobs are completed
			if (jobStatusMap.containsKey(ApplicationUtil.getMessage("page.label.state.completed"))) {
				if ((testSuite.getModels() != null) && (testSuite.getModels().size() == jobStatusMap.get(ApplicationUtil.getMessage("page.label.state.completed")))) {
					if (testSuite.isComplete()) {
						return deliveryEvent.getStatusDisplayString();
					}
				}
			}

			// check if there was any Job error
			if (testSuite.isError() || deliveryEvent.isError()) {
				return deliveryEvent.getStatusDisplayString() + "<a class=\"text-danger ml-2\" href=\"javascript:popItUpWithDimensions('../includes/delivery_event_logs.jsp?id=" + testSuite.getId() + "&deliveryid=" + deliveryEvent.getId() + "&class=" + testSuite.getClass().getName() + "', 720, 480);\">"
						+ "<i class=\"fas fa-times-circle mr-1\" aria-hidden=\"true\"></i>" + "(" + ApplicationUtil.getMessage("client_messages.text.view_logs") + ")</a>" ;
			}

			StringBuilder message = new StringBuilder();
			boolean first = true;
			for (String statusLabel: jobStatusMap.keySet()) {
				// not using message.length() == 0 to avoid synchronization
				if (!first) {
					message.append("<br>");
				}
				message.append(statusLabel);
				message.append(" (");
				message.append(jobStatusMap.get(statusLabel));
				message.append(")");
				first = false;
			}

			return "<div id=\"testStatusPollingContainer_" + testSuite.getId() + "\">" + message + "</div>";
		} else {
			return "<div id=\"testStatusPollingContainer_" + testSuite.getId() + "\">" + testSuite.getStagedBundleStatus() + "</div>";
		}
	}
}