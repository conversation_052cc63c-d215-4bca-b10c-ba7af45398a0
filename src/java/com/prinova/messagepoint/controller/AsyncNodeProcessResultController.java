package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public class AsyncNodeProcessResultController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncNodeProcessResultController.class);

	public static final String PARAM_NODE_ID				= "nodeId";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getNodeProcessResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for node processing data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getNodeProcessResponseJSON (HttpServletRequest request) {

		long nodeId = ServletRequestUtils.getLongParameter(request, PARAM_NODE_ID, 0);
		Node node 	= Node.findById(nodeId);
			
		JSONObject returnObj = new JSONObject();
		
		try {
			
			if (node != null) {
				if(node.getStatus() == Node.NODE_STATUS_IN_INITIALIZING_PROCESS || node.getStatus() == Node.NODE_STATUS_IN_MIGRATING_PROCESS){
					returnObj.put("status" , "in_process");
				}else if(node.getStatus() == Node.NODE_STATUS_OFFLINE){
					returnObj.put("status" , "offline");
				}				
				returnObj.put("node_id"	, nodeId);
			} else {
				returnObj.put("error", "Invalid node");
			}

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve node processing data: " + e );
		}

		return returnObj.toString();
	}
}