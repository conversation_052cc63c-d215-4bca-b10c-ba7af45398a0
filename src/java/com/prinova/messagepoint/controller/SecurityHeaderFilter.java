package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.tag.layout.MessagepointHeader;
import com.prinova.messagepoint.util.FeatureFlag;
import org.apache.commons.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;

public class SecurityHeaderFilter implements Filter {
    public void destroy() {
    }

    public void doFilter(ServletRequest req, ServletResponse resp, Filter<PERSON>hain chain) throws ServletException, IOException {

        HttpServletResponse response = (HttpServletResponse) resp;

        if (response != null) {

            String webSpellCheckerURL = "https://webspellchecker-pvi.messagepoint.com";

            String pincServer = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.PINC.KEY_PINCServer);
            pincServer = !StringUtils.isEmpty(pincServer) && pincServer.startsWith("https://") ? pincServer : "";
            if (pincServer != null && !pincServer.isEmpty()) {
                URL pincServerUrl = new URL(pincServer);
                pincServer = pincServerUrl.getProtocol() + "://" + pincServerUrl.getHost();
            }

            boolean allowScreenRecording = FeatureFlag.isEnabled(FeatureFlag.Features.AllowScreenRecording, (HttpServletRequest) req);

            ContentSecurityPolicy policy = new ContentSecurityPolicy();
            policy.setPincServerUrl(pincServer);
            policy.setWebSpellCheckerURL(webSpellCheckerURL);
            policy.setApplyStripoDirectives(((HttpServletRequest) req).getRequestURI().endsWith("stripo_editor.form"));

            response.setHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
            response.setHeader("X-Permitted-Cross-Domain-Policies", "none");
            response.setHeader("Content-Security-Policy", policy.toString());

            if (!allowScreenRecording) {
                response.setHeader("Permissions-Policy", "microphone=(), camera=()");
            }

            response.setHeader("Referrer-Policy", "same-origin");
            response.setHeader("X-Content-Type-Options", "nosniff");
            response.setHeader("X-Frame-Options", "sameorigin");
            response.setHeader("X-XSS-Protection", "1; mode=block");

            if (req.getParameter("cacheStamp") != null) {
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // HTTP 1.1
                response.setHeader("Pragma", "no-cache");                                   // HTTP 1.0
                response.setDateHeader("Expires", 0);            // Prevents caching at the proxy server
            }

            req.setAttribute(MessagepointHeader.RESPONSE_CACHEABLE_ATTR_KEY, true);
        }



        chain.doFilter(req, resp);

        if (response != null && req.getAttribute(MessagepointHeader.RESPONSE_CACHEABLE_ATTR_KEY) != null) {
            Boolean cacheable = (Boolean) req.getAttribute(MessagepointHeader.RESPONSE_CACHEABLE_ATTR_KEY);

            /* Pragma is the HTTP/1.0 implementation and cache-control is the HTTP/1.1 implementation of the same concept. They both are meant to prevent the client from caching the response. Older clients may not support HTTP/1.1 which is why that header is still in use.
             * IE before version 10 uses only no-cache for Cache-Control header, so we need both no-cache and no-store in “cache-control” for it working on both old IE and other browsers.
             * Expires should prevent caching at the proxy server.
             */

            if (!cacheable) {
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate"); // HTTP 1.1
                response.setHeader("Pragma", "no-cache");                                   // HTTP 1.0
                response.setDateHeader("Expires", 0);            // Prevents caching at the proxy server
            }
        }

    }

    public void init(FilterConfig config) throws ServletException {

    }

    public static class ContentSecurityPolicy {

        private String webSpellCheckerURL = null;

        private String pincServerUrl = null;
        private boolean applyStripoDirectives = false;

        private SystemPropertyManager spm = SystemPropertyManager.getInstance();

        public String getDefaultSrc() {
            return "default-src " + spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPDefaultSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPDefaultSrc);
        }

        public String getObjectSrc() {
            return "object-src " + spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPObjectSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPObjectSrc);
        }

        private String getScriptSrc() {
            StringBuilder scriptSrc = new StringBuilder("script-src ");

            scriptSrc.append(spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPScriptSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPScriptSrc));

            if (webSpellCheckerURL != null) {
                scriptSrc.append(" ").append(getWebSpellCheckerURL());
            }

            if (applyStripoDirectives) {
                scriptSrc.append(" https://plugins.stripo.email");
            }

            return scriptSrc.toString();
        }

        @Override
        public String toString() {

            StringBuilder builder = new StringBuilder();

            builder.append(getDefaultSrc()).append("; ");
            builder.append(getObjectSrc()).append("; ");
            builder.append(getScriptSrc()).append("; ");
            builder.append(getStyleSrc()).append("; ");
            builder.append(getImgSrc()).append("; ");
            builder.append(getConnectSrc()).append("; ");
            builder.append(getFontSrc()).append("; ");
            builder.append(getFrameSrc()).append("; ");

            return builder.toString();
        }

        private String getFontSrc() {
            StringBuilder builder = new StringBuilder();

            builder.append("font-src ");

            builder.append(spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPFontSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPFontSrc));

            if (!StringUtils.isEmpty(getWebSpellCheckerURL())) {
                builder.append(" ").append(getWebSpellCheckerURL());
            }

            if (isApplyStripoDirectives()) {
                builder.append(" https://plugins.stripo.email");
                builder.append(" https://fonts.gstatic.com");
            }

            return builder.toString();
        }

        private String getImgSrc() {
            return "img-src " + spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPImgSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPImgSrc);
        }

        private String getStyleSrc() {
            return "style-src " + spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPStyleSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPStyleSrc);
        }

        private String getFrameSrc() {
            StringBuilder builder = new StringBuilder("frame-src ");

            builder.append(spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPFrameSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPFrameSrc));
            builder.append(" https://www.messagepoint.com");

            return builder.toString();
        }

        private String getConnectSrc() {
            StringBuilder builder = new StringBuilder("connect-src ");

            builder.append(spm.getPodMasterSystemProperty(SystemPropertyKeys.ContentSecurityPolicy.KEY_CSPConnectSrc, SystemPropertyKeys.ContentSecurityPolicy.VALUE_DefaultCSPConnectSrc));

            if (webSpellCheckerURL != null && !webSpellCheckerURL.isEmpty()) {
                builder.append(" ").append(webSpellCheckerURL).append(" ").append(webSpellCheckerURL).append(":2880");
            }

            if (fullStoryEnabled()) {
                builder.append(" https://rs.fullstory.com");
            }

            if (isApplyStripoDirectives()) {
                builder.append(" https://plugins.stripo.email");
            }

            if (pincServerUrl != null && !pincServerUrl.isEmpty()) {
                builder.append(" ").append(pincServerUrl);
            }

            // Chat Agent Websocket
            builder.append(" wss://gwqkbo8te1.execute-api.us-east-1.amazonaws.com");
            builder.append(" wss://7zd6jl66ze.execute-api.us-east-1.amazonaws.com");

            // Temp for local testing
            builder.append(" https://tt252anx.messagepoint.com");
            builder.append(" https://tt242anx.messagepoint.com");
            builder.append(" https://messagepoint-bucket.s3.us-east-2.amazonaws.com");
            builder.append(" https://messagepoint-bucket.s3.amazonaws.com");

            return builder.toString();
        }

        private boolean fullStoryEnabled() {
            return SystemPropertyManager.getInstance().getDcsBooleanSystemProperty(SystemPropertyKeys.Analytics.KEY_AnalyticsFullStoryEnabled, false);
        }

        public String getWebSpellCheckerURL() {
            return webSpellCheckerURL;
        }

        public void setWebSpellCheckerURL(String webSpellCheckerURL) {
            this.webSpellCheckerURL = webSpellCheckerURL;
        }

        public boolean isApplyStripoDirectives() {
            return applyStripoDirectives;
        }

        public void setApplyStripoDirectives(boolean applyStripoDirectives) {
            this.applyStripoDirectives = applyStripoDirectives;
        }

        public String getPincServerUrl() {
            return pincServerUrl;
        }

        public void setPincServerUrl(String pincServerUrl) {
            this.pincServerUrl = pincServerUrl;
        }
    }

}
