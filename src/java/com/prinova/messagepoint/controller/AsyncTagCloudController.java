package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

public class AsyncTagCloudController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncTargetGroupsController.class);

	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_RATIONALIZER_APP_ID		= "rationalizerApplicationId";
	public static final String PARAM_ZONE_ID					= "zoneId";
	public static final String PARAM_TAG_CLOUD_TYPE				= "tagCloudType";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getCloudTagsResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getCloudTagsResponseJSON (HttpServletRequest request) {

		long documentId 				= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		long rationalizerApplicationId 	= ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APP_ID, -1); // 0 to retrieve Application Level
		long zoneId 					= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_ID, 0);
		int tagCloudType 				= ServletRequestUtils.getIntParameter(request, PARAM_TAG_CLOUD_TYPE, 0);
		
		JSONObject returnObj = new JSONObject();
		
		try {

			if ( zoneId > 0)
				documentId = Zone.findById(zoneId).getDocument().getId();

			returnObj.put("documentId"	, documentId);
			returnObj.put("tagCloudType", tagCloudType);
			returnObj.put("cloudTypeLabel", new TagCloudType(tagCloudType).getName());

			JSONArray cloudTagListArray = new JSONArray();
			
			List<TagCloud> tagCloudList = new ArrayList<>();
			if ( tagCloudType == TagCloudType.ID_RATIONALIZER_COMBINED ) {
				tagCloudList.addAll( TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_CONTENT, documentId, rationalizerApplicationId) );
				tagCloudList.addAll( TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_DOCUMENT, documentId, rationalizerApplicationId) );
			} else if ( tagCloudType == TagCloudType.ID_RATIONALIZER_SHARED_CONTENT ) {
				tagCloudList.addAll( TagCloud.getTagCloudList(TagCloudType.ID_RATIONALIZER_SHARED_CONTENT, documentId, rationalizerApplicationId) );
			} else {
				tagCloudList = TagCloud.getTagCloudList(tagCloudType, documentId, rationalizerApplicationId);
			}
			int idx = 1;
			for(TagCloud tagCloud : tagCloudList){
				JSONObject ctObj = new JSONObject();
				
				String name = tagCloud.getName();
				if ( tagCloudType == TagCloudType.ID_RATIONALIZER_COMBINED )
					name = tagCloud.getName() + ( tagCloud.getTypeId() == TagCloudType.ID_RATIONALIZER_CONTENT ? "(C)" : "(D)");
				
				ctObj.put("name", name);
				ctObj.put("id"	, Integer.valueOf(idx).toString());
				cloudTagListArray.put(ctObj);
				idx++;
			}
			
			returnObj.put("cloudTags", cloudTagListArray);

			
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve cloud tag list: " + e );
		}

		return returnObj.toString();
	}
}