package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.simulation.Simulation;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public class AsyncSimulationResultController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncSimulationResultController.class);

	public static final String PARAM_SIM_ID				= "simId";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getTestResultResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for simulation data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getTestResultResponseJSON (HttpServletRequest request) {

		long simId 				= ServletRequestUtils.getLongParameter(request, PARAM_SIM_ID, 0);
		Simulation simulation 	= Simulation.findById(simId);
			
		JSONObject returnObj = new JSONObject();
		
		try {
			
			if (simulation != null) {
				if(simulation.isComplete()){
					returnObj.put("delivery_event_id" 	, simulation.getDeliveryEvent().getId());
					returnObj.put("item_class" 			, simulation.getClass().getName());					
				}else if(simulation.isError()){
					returnObj.put("delivery_event_id" 	, simulation.getDeliveryEvent().getId());
					returnObj.put("item_class" 			, simulation.getClass().getName());
				}

				returnObj.put("status" , simulation.getState());
				returnObj.put("status_display" , simulation.getDeliveryEvent().getStatusDisplayString());
				
				returnObj.put("sim_id"	, simId);
				returnObj.put("can_delete"	, simulation.isDeletable());
				returnObj.put("can_cancel"	, simulation.isCancelable());
				if(simulation.getDeliveryEvent().getJob() != null){
					returnObj.put("job_id"	, simulation.getDeliveryEvent().getJob().getId());
				}
			} else {
				returnObj.put("error", "Invalid simulation");				
			}

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve simulation list: " + e );
		}

		return returnObj.toString();
	}
}