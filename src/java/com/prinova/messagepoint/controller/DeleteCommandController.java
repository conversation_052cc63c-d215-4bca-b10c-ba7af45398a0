package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagePointDeletable;
import com.prinova.messagepoint.MessagePointDeletable.DeleteResponse;
import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.model.admin.DataElement;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.DataRecord;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataElement;
import com.prinova.messagepoint.model.dataadmin.xmllayout.XmlDataTagDefinition;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.common.DeleteModelService;
import com.prinova.messagepoint.platform.services.content.DeleteTextStyleFontService;
import com.prinova.messagepoint.platform.services.dataadmin.DeleteDataGroupService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class DeleteCommandController extends MessagepointController {
	private static final String DEFAULT_ID_STRING = "id";
	protected static final String CONFIRM_PARAMETER = "confirm";

	private static final Log log = LogUtil.getLog(DeleteCommandController.class);

	private String className;

	protected boolean delete(HttpServletRequest request, HttpServletResponse response, Object command,
			BindException errors) throws Exception {
		return true;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		DeleteCommand backingObject = new DeleteCommand();

		// set the class name
		backingObject.setClassName(className);

		// set the parameter
		String parameter = request.getParameter("parameter");
		if (parameter == null || parameter.isEmpty())
			parameter = DEFAULT_ID_STRING;

		// set the cancel view
		String cancelViewName = request.getParameter("cancelView");
		if (!StringUtils.isBlank(cancelViewName)) {
			backingObject.setCancelViewName(cancelViewName);
		}

		// set the id
		String idStr = request.getParameter(parameter);
		backingObject.setId(Long.valueOf(idStr).longValue());
		backingObject.setCancelAction(!isSubmitAction(request));

		String successParameter = request.getParameter("successParameter");
		if (!StringUtils.isBlank(successParameter)) {
			backingObject.setSuccessParameter(successParameter);
		}

		return backingObject;
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command,
			BindException errors) throws Exception {
		DeleteCommand c = (DeleteCommand) command;

		if (isSubmitAction(request)) {
			long modelIdToDelete = getModelId(request, response, (DeleteCommand) command, errors);

			Validator validator = new Validator();
			validator.validate(command, errors);
			
			if(errors.hasErrors()){
				return super.showForm(request, response, errors);				
			}
			
			String className = getClassName();
			Object modelToDelete = HibernateUtil.getManager().getObject(Class.forName(className), modelIdToDelete);

			ServiceResponse serviceResponse = null;
			
			if (modelToDelete instanceof DataGroup) {
				ServiceExecutionContext context = DeleteDataGroupService.createContext(modelIdToDelete);
				Service deleteDataGroupService = MessagepointServiceFactory.getInstance().lookupService(DeleteDataGroupService.SERVICE_NAME, DeleteDataGroupService.class);
				deleteDataGroupService.execute(context);
				serviceResponse = context.getResponse();
			} else if (modelToDelete instanceof TextStyleFont){
				ServiceExecutionContext context = DeleteTextStyleFontService.createContext(modelIdToDelete);
				Service fontDeleteService = MessagepointServiceFactory.getInstance().lookupService(DeleteTextStyleFontService.SERVICE_NAME, DeleteTextStyleFontService.class);
				fontDeleteService.execute(context);

				try {
					HibernateUtil.getManager().getSession().flush();
					CacheDataRepository cacheDataRepository = ApplicationUtil.getBean("cacheDataRepository", CacheDataRepository.class);

					cacheDataRepository.updateStylesData();
				} catch (Exception ex) {
					log.error(ex.getMessage(), ex);
				}

				serviceResponse = context.getResponse();				
			} else {
				String auditObjName = "";
				int auditObjectType = 0;
				if (modelToDelete instanceof DataSource) {
					DataSource ds = (DataSource)modelToDelete;
					// Audit (Audit DataSource Delete)
					auditObjectType = AuditObjectType.ID_DATA_SOURCE;
					auditObjName = ds.getName();
				}
				if (modelToDelete instanceof DataRecord) {
					DataRecord dr = (DataRecord)modelToDelete;
					// Audit (Audit DataRecord Delete)
					auditObjectType = AuditObjectType.ID_DATA_RECORD;
					auditObjName = dr.getRecordIndicator();
				}
				if (modelToDelete instanceof DataElement) {
					DataElement de = (DataElement)modelToDelete;
					// Audit (Audit DataElement Delete)
					auditObjectType = AuditObjectType.ID_DATA_ELEMENT;
					auditObjName = de.getName();
				}
				if (modelToDelete instanceof XmlDataTagDefinition) {
					XmlDataTagDefinition dt = (XmlDataTagDefinition)modelToDelete;
					// Audit (Audit Data Tag Delete)
					auditObjectType = AuditObjectType.ID_XML_DATA_TAG;
					auditObjName = dt.getName();
				}
				if (modelToDelete instanceof XmlDataElement) {
					XmlDataElement dt = (XmlDataElement)modelToDelete;
					// Audit (Audit Data Tag Delete)
					auditObjectType = AuditObjectType.ID_XML_DATA_ELEMENT;
					auditObjName = dt.getName();
				}
				if (modelToDelete instanceof JSONDataDefinition) {
					JSONDataDefinition definition = (JSONDataDefinition)modelToDelete;
					// Audit (Audit Data Definition Delete)
					auditObjectType = definition.getDefinitionType()==JSONDefinitionType.ID_KEY?AuditObjectType.ID_JSON_DATA_KEY:AuditObjectType.ID_JSON_DATA_DEFINITION;
					auditObjName = definition.getName();
				}
				if (modelToDelete instanceof JSONDataElement) {
					JSONDataElement de = (JSONDataElement)modelToDelete;
					// Audit (Audit Data Element Delete)
					auditObjectType = AuditObjectType.ID_JSON_DATA_ELEMENT;
					auditObjName = de.getName();
				}
				ServiceExecutionContext context = DeleteModelService.createContext(modelIdToDelete, getClassName());
				Service deleteModelService = MessagepointServiceFactory.getInstance().lookupService(DeleteModelService.SERVICE_NAME, DeleteModelService.class);
				deleteModelService.execute(context);
				serviceResponse = context.getResponse();
				if(serviceResponse.isSuccessful()){
					AuditEventUtil.push(AuditEventType.ID_ASSET_UPDATE, auditObjectType, auditObjName, null, AuditActionType.ID_DELETE, null);
				}
			}
			if (!serviceResponse.isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return super.showForm(request, response, errors);
			} else {
				
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessParametersMap(request, c));
			}
		}

		if (!StringUtils.isBlank(c.getCancelViewName())) {
			return new ModelAndView(new RedirectView(c.getCancelViewName()), c.getParameter(), c.getId());

		} else {
			return new ModelAndView(new RedirectView(getSuccessView()), c.getParameter(), c.getId());
		}
	}

	protected Map<String, Object> getSuccessParametersMap(HttpServletRequest request, DeleteCommand command) {
		Map<String, Object> map = new HashMap<>();

		String successParam = command.getSuccessParameter();
		String successParamValue = null;
		if (StringUtils.isBlank(successParam)
				|| (StringUtils.isBlank((successParamValue = request.getParameter(successParam))))) {
			return new HashMap<>();
		}

		map.put(successParam, successParamValue);
		map.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);

		return map;
	}

	protected boolean isSubmitAction(HttpServletRequest request) {
		if (request.getParameter(CONFIRM_PARAMETER) != null) {
			return request.getParameter(CONFIRM_PARAMETER).equals("true");
		}

		return false;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	protected long getModelId(HttpServletRequest request, HttpServletResponse response, DeleteCommand deleteCommand,
			BindException errors) throws Exception {
		String parameter = deleteCommand.getParameter();
		if (parameter == null || parameter.isEmpty())
			parameter = DEFAULT_ID_STRING;
		String idStr = request.getParameter(parameter);
		return Long.valueOf(idStr).longValue();
	}

	public static class Validator implements org.springframework.validation.Validator {
		public boolean supports(Class arg0) {
			return true;
		}

		public void validate(Object object, Errors errors) {
			DeleteCommand command = (DeleteCommand) object;
			
			try {
				Object modelToDelete = HibernateUtil.getManager().getObject(Class.forName(command.getClassName()), command.getId());			
				if(modelToDelete instanceof TargetGroup){
					TargetGroup targetGroup = (TargetGroup)modelToDelete;
					if(targetGroup.isReferenced()){
						errors.reject(null, "This target group is referenced and cannot be deleted.");
						return;
					}
				}else if(modelToDelete instanceof ConditionElement){
					ConditionElement rule = (ConditionElement)modelToDelete;
					if(rule.isReferenced()){
						errors.reject(null, "This targeting rule is referenced and cannot be deleted.");
						return;
					}				
				}else if(modelToDelete instanceof TextStyleFont){
					TextStyleFont font = (TextStyleFont)modelToDelete;
					if(font.isReferenced()){
						errors.reject(null, "This font is referenced and cannot be deleted.");
						return;
					}				
				}
			} catch (ClassNotFoundException e) {
				return;
			}
			
			if (command.isCancelAction()) {
				return;
			}

			Class<?> type = null;
			try {
				type = Class.forName(command.getClassName());
			} catch (ClassNotFoundException e) {
				return;
			}

			if (!MessagePointDeletable.class.isAssignableFrom(type)) {
				return;
			}

			// It is a MessagePointDeletable object, ask the object if we can delete it.
			MessagePointDeletable deletable = (MessagePointDeletable) HibernateUtil.getManager().getObject(type,
					command.getId());

			DeleteResponse deleteResponse = deletable.canDelete();
			if (!deleteResponse.canDelete()) {
				command.setDeleteable(false);

				StringBuilder sb = new StringBuilder("The entity could not be deleted: ");

				for (String message : deleteResponse.getMessages()) {
					sb.append(message);
				}

				errors.reject(null, sb.toString());
			}
		}
	}
}