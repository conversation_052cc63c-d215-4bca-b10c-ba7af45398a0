package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.insert.InsertScheduleControllerUtil;
import com.prinova.messagepoint.controller.touchpoints.TouchpointContentObjectListController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.DataGroup;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.content.ContentObjectZonePriority;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import com.prinova.messagepoint.model.util.ContentStyleUtils;
import com.prinova.messagepoint.model.util.InsertUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.insert.GetInsertListService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateZoneService;
import com.prinova.messagepoint.tag.view.DocumentTag;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.SocketException;
import java.util.*;

public class AsyncSectionImageThumbnailController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncSectionImageThumbnailController.class);
	
	public static final String REQ_PARM_TYPE 						= "type";
	public static final String REQ_PARM_DATE 						= "date";
	public static final String REQ_PARM_DOMAIN_ID                   = "domainId";
	public static final String REQ_PARM_INSTANCE_ID                 = "instanceId";
	public static final String REQ_PARM_VISIBILITY 					= "visibility";
	public static final String REQ_PARM_SECTION_ID 					= "sectionId";
	public static final String REQ_PARM_ZONE_ID 					= "zoneId";
	public static final String REQ_PARM_COLLECTION_ID 				= "collectionId";
	public static final String REQ_PARM_PRIORITY_ACTION				= "priorityAction";
	public static final String REQ_PARM_SELECTION_STATUS_ID			= "selectionStatusId";
	public static final String REQ_PARM_ZONE_FILTER					= "zoneFilter";
	public static final String REQ_PARM_SIZE						= "size";
	public static final String REQ_PARM_NEW_PART_ATTR				= "newPartAttr";
	public static final String REQ_PARM_NEW_ZONE_ATTR				= "newZoneAttr";
	public static final String REQ_PARM_REMOVED_PARTS				= "removedParts";
	public static final String REQ_PARM_SECTION_NAME				= "name";
	public static final String REQ_PARM_LAYOUT_TYPE_ID				= "layoutType";
	public static final String REQ_PARM_SECTION_TYPE_ID				= "sectionType";
	public static final String REQ_PARM_DIMENSIONS					= "dim";
	public static final String REQ_PARM_MAX_WIDTH					= "maxWidth";
	public static final String REQ_PARM_RENDER_CONTENT				= "renderContent";
	public static final String REQ_PARM_CHANNEL_CONTEXT_ID			= "channelContextId";
	public static final String TYPE_INSERT 							= "insert";
	public static final String TYPE_INSERT_SCHED		 			= "insertSched";
	public static final String TYPE_DOCUMENT_ONLY 					= "documentOnly";
	public static final String TYPE_MESSAGE 						= "message";
	public static final String TYPE_TAG 							= "tag";
	public static final String TYPE_TOUCHPOINT_CONTEXT 				= "touchpointContext";
	public static final String TYPE_TOUCHPOINT_NAVIGATION			= "touchpointNavigation";
	public static final String TYPE_ADD_SECTION						= "addSection";
	public static final String TYPE_ADD_ZONE_OR_PARTS				= "addZoneOrParts";
	public static final String TYPE_ZONE_INFO						= "zoneInfo";
	public static final String TYPE_TOUCHPOINT_FULL					= "touchpointFull";
	public static final String TYPE_SECTION_PREVIEW					= "sectionPreview";
	public static final String TYPE_UPDATE_CONTENT					= "updateContent";
	public static final String VISIBILITY_FULL 						= "full";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		if ( getTypeParam(request).equalsIgnoreCase(TYPE_TOUCHPOINT_CONTEXT) ||
			 getTypeParam(request).equalsIgnoreCase(TYPE_TOUCHPOINT_NAVIGATION) ||
			 getTypeParam(request).equalsIgnoreCase(TYPE_TOUCHPOINT_FULL) ||
			 getTypeParam(request).equalsIgnoreCase(TYPE_SECTION_PREVIEW) ||
			 getTypeParam(request).equalsIgnoreCase(TYPE_UPDATE_CONTENT))
			response.setContentType("application/json");
		else
			response.setContentType("text/xml");
		
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseXML(request,response).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (SocketException se) {
        	log.error("Touchpoint Navigation: Client have moved on");
 		    out.println("Touchpoint Navigation: Client have moved on");
			out.flush();
		} catch (Exception e) {
        	log.error(e.getMessage(),e);
 		    out.println("Exception: Unable to complete section image thumbnail request.");
			out.flush();
		}
		return null;
	}
	
	private String getResponseXML (HttpServletRequest request, HttpServletResponse response) throws Exception {

        SessionHolder mainSessionHolder = null;

        try {
        	
    		String requestedType = getTypeParam(request);
    		
    		Long documentId 		= getDocumentIdParam(request);

            long channelContextId	= ServletRequestUtils.getLongParameter(request, REQ_PARM_CHANNEL_CONTEXT_ID, -1L);
            Document contextDoc 	= Document.findById(documentId);
            if ( channelContextId > 0 && channelContextId != contextDoc.getConnectorConfiguration().getChannel().getId() )
            	documentId = contextDoc.getChannelAlternateByType( channelContextId ).get(0).getId();
    		
    		long domainId           = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOMAIN_ID, -1L);
    		long instanceId         = ServletRequestUtils.getLongParameter(request, REQ_PARM_INSTANCE_ID, -1L);
        	        	
    		if (instanceId > 0)
    		{
                Node instance = Node.findById(instanceId);
                if(instance.isExchangeNode()) {
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(instance.getSchemaName());
                }
    		}
    		
    		if (mainSessionHolder == null && domainId > 0) 
    		{
                Branch domain = Branch.findById(domainId);
                if(domain.getExchangeNode() != null) {
                    mainSessionHolder = HibernateUtil.getManager().openTemporarySession(domain.getExchangeNode().getSchemaName());
                }
            }

            Document document 		= requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_CONTEXT) ? null : Document.findById(documentId);
            boolean renderContent 	= ServletRequestUtils.getBooleanParameter(request, REQ_PARM_RENDER_CONTENT, true);

            if (requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_NAVIGATION) ||
                requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_FULL) ||
                requestedType.equalsIgnoreCase(TYPE_SECTION_PREVIEW) ) {

                String sectionThumbnailHTML = "";

                Long sectionId		= getSectionIdParam(request);
                String zoneFilter 	= getZoneFilterParam(request);

                DocumentTag docTag = new DocumentTag();
                if ( getVisibilityParam(request).equalsIgnoreCase(VISIBILITY_FULL) )
                    docTag.setType(DocumentTag.INTERFACE_TYPE_ADMIN);
                docTag.setZoneFilter(zoneFilter);

                // Type: TOUCHPOINT NAVIGATION
                JSONObject returnObj = new JSONObject();
                if ( requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_NAVIGATION) )
                    docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_NAVIGATION);
                else
                    docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_FULLSCREEN);

                DocumentSection section = sectionId > 0 ? DocumentSection.findById(sectionId) : document.getDocumentSectionsByOrder().get(0);

                // Section Size: CUSTOM AND DEFAULT MANAGEMENT
                String size = "110%";
                if ( section.getLayoutType() == DocumentSectionLayoutType.ID_LANDSCAPE && document.isPrintTouchpoint() )
                    size = "140%";
                if ( requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_FULL) )
                    size = "250%";

                String sizeOverride = ServletRequestUtils.getStringParameter(request, REQ_PARM_SIZE, null);
                if ( sizeOverride != null )
                    size = sizeOverride + "%";

                int maxWidth = ServletRequestUtils.getIntParameter(request, REQ_PARM_MAX_WIDTH, -1);
                if ( maxWidth == -1 )
                    maxWidth = DocumentTag.getFullScaleSectionWidth(section);

                sectionThumbnailHTML = docTag.getSectionImageHTML(documentId, sectionId , -1L, size, maxWidth, request);
                returnObj.put( "name", document.getName() );
                returnObj.put( "id", documentId );
                returnObj.put( "section_name", section.getName() );
                returnObj.put( "section_id", section.getId() );
                returnObj.put( "section_html", StringEscapeUtils.escapeXml(sectionThumbnailHTML) );

                if ( !zoneFilter.equals(DocumentTag.ZONE_FILTER_COMMUNICATIONS) && !requestedType.equalsIgnoreCase(TYPE_SECTION_PREVIEW) && renderContent ) {
                	
                	List<Zone> sectionZones 		= section.getVisibleZonesListOfUser();
                	TouchpointSelection selection 	= TouchpointSelection.findById(getTouchpointSelectionIdParam(request));

                    double sizeMultiplier = 1.0;
                    if ( requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_FULL) )
                        sizeMultiplier = 2.27;

                    Double fontScale = 0.25 * sizeMultiplier;
                    if ( maxWidth > -1 )
                        fontScale = (double)Math.min(maxWidth, DocumentTag.getFullScaleSectionWidth(section)) / DocumentTag.getFullScaleSectionWidth(section);
                    if ( document.isSmsTouchpoint() )
                        fontScale = 1.0 * sizeMultiplier;
                    returnObj.put("fontScale", fontScale);

                    MessagepointLocale locale = UserUtil.getCurrentLanguageLocaleContext();

                    JSONArray contentsArray = new JSONArray();
                    Document primaryDocument = document;
                    if ( document.isAlternate() )
                        primaryDocument = document.getParent();
                    else if ( document.isChannelAlternate() )
                    	primaryDocument = document.getChannelParent();

                    for ( Zone currentZone: sectionZones ) {
                        ContentObject currentContentObject = null;

                        Zone primaryZone = currentZone;
                        if ( currentZone.isAlternate() )
                            primaryZone = currentZone.getParent();

                        long messageInstanceOverrideId = ServletRequestUtils.getLongParameter(request, "zone_"+currentZone.getId(), -1L);
                        if (messageInstanceOverrideId != -1) {
							currentContentObject = ContentObject.findById(messageInstanceOverrideId);
                        } else {

							currentContentObject = ContentObjectZonePriority.findFirstPriorityMessageByZone(primaryZone.getId(), (primaryDocument.isEnabledForVariation() && selection != null && !selection.isMaster() ? selection : null) );
                        }

                        if (currentContentObject != null) {

                            List<ContentVO> currentContents = ContentObjectAssociation.getMasterOrTPVariantContent(currentContentObject, currentContentObject.getFocusOnDataType(), -1L, true,
									selection != null && !selection.isMaster() && (currentContentObject.isStructuredContentEnabled() || currentContentObject.isDynamicVariantEnabled() || currentContentObject.isVariantType()) ?
									selection.getParameterGroupTreeNode() : null,
									locale);

                            for (ContentVO currentContent: currentContents) {

								if ( currentContent==null )
									continue;

                                JSONObject currentContentObj = new JSONObject();
                                currentContentObj.put( "zone_id", 		currentZone.getId() );
                                currentContentObj.put( "zone_type", 	currentZone.getZoneTypeId() );
                                currentContentObj.put( "zone_rotation", currentZone.getRotationAngle() );
                                currentContentObj.put( "part_id", 		currentZone.isAlternate() && currentContent.getZonePartId() > 0 ?
                                                                            currentZone.findPartByParent(ZonePart.findById(currentContent.getZonePartId())).getId() :
                                                                            currentContent.getZonePartId() );
                                String content = (currentZone.getContentType().getId() == ContentType.VIDEO && !currentContent.isContainsVariables()) ? currentContent.getContent() : StringEscapeUtils.escapeXml(currentContent.getContent());
                                currentContentObj.put( "content",		content );
                                currentContentObj.put( "img_resource", 		HttpRequestUtil.getFileResourceToken(currentContent.getImageLocation()) );
                                currentContentObj.put( "img_name", 		currentContent.getImageName());
                                currentContentObj.put( "message_id", 	currentContentObject.getId());
                                currentContentObj.put( "content_type", 	currentZone.getContentTypeId() );
                                currentContentObj.put( "content_state", (currentContent.getContent() == null || currentContent.getContent().trim().isEmpty()) && currentContent.getImageLocation() == null ? "NO_CONTENT" : "CONTENT" );
                                contentsArray.put(currentContentObj);
                            }

                            if (currentContents.isEmpty()) {
                                if (currentZone.isMultipart()) {
                                    for (ZonePart currentPart: currentZone.getParts()) {
                                        JSONObject currentContentObj = new JSONObject();
                                        currentContentObj.put( "zone_id", 		currentZone.getId() );
                                        currentContentObj.put( "part_id", 		currentPart.getId() );
                                        currentContentObj.put( "message_id", 	currentContentObject.getId());
                                        currentContentObj.put( "content_type", 	currentZone.getContentTypeId() );
                                        currentContentObj.put( "content_state", "NO_CONTENT" );
                                        contentsArray.put(currentContentObj);
                                    }
                                } else {
                                    JSONObject currentContentObj = new JSONObject();
                                    currentContentObj.put( "zone_id", 		currentZone.getId() );
                                    currentContentObj.put( "message_id", 	currentContentObject.getId());
                                    currentContentObj.put( "content_type", 	currentZone.getContentTypeId() );
                                    currentContentObj.put( "content_state", "NO_CONTENT" );
                                    contentsArray.put(currentContentObj);
                                }
                            }

                        } else {

                            JSONObject currentContentObj = new JSONObject();
                            currentContentObj.put( "zone_id", 			currentZone.getId() );
                            currentContentObj.put( "zone_type", 		currentZone.getZoneTypeId() );
                            currentContentObj.put( "zone_rotation", 	currentZone.getRotationAngle() );
                            currentContentObj.put( "part_id", 			0 );
                            currentContentObj.put( "content_type", 		currentZone.getContentTypeId() );
                            currentContentObj.put( "content_state", 	"NO_MESSAGES" );
                            contentsArray.put(currentContentObj);

                        }
                    }
                    returnObj.put("contents", contentsArray);

                    StringBuilder contentStyleCSS = new StringBuilder();
                    for ( Zone currentZone: sectionZones ) {
						try {
							contentStyleCSS.append(ContentStyleUtils.getDefaultCSSforZone(currentZone));
						} catch (IllegalArgumentException | NullPointerException e) {
							log.warn("Document " + documentId + " has a null Zone. Skipping this record");
							log.error(e);
						}
					}
                    contentStyleCSS.append( ContentStyleUtils.getTextStyleCSSforZones(sectionZones) );
                    contentStyleCSS.append( ContentStyleUtils.getParagraphStyleCSSforZones(sectionZones) );
                    returnObj.put("css", contentStyleCSS.toString());

                }

                return returnObj.toString();

            } else if ( requestedType.equalsIgnoreCase(TYPE_ADD_SECTION) ) {
                return addSectionAndGeneratePreview(document, request);
            } else if ( requestedType.equalsIgnoreCase(TYPE_ADD_ZONE_OR_PARTS) ) {
                return addZoneOrParts(document, request);
            } else if ( requestedType.equalsIgnoreCase(TYPE_ZONE_INFO) ) {
                return getZoneInfo(document, request);
            } else if ( requestedType.equalsIgnoreCase(TYPE_UPDATE_CONTENT) ) {
                return generateContentUpdate(document, request);
            } else if ( requestedType.equalsIgnoreCase(TYPE_TOUCHPOINT_CONTEXT) ) {
                return generateTouchpointContext(request);
            } else {
                return generateSummaryDetails(document, request);
            }
        } finally {
            if (mainSessionHolder != null) {
                HibernateUtil.getManager().restoreSession(mainSessionHolder);
            }
        }
	}

	@SuppressWarnings("unchecked")
	private String addSectionAndGeneratePreview(Document document, HttpServletRequest request) throws Exception {
		
		int layoutType 		= ServletRequestUtils.getIntParameter(request, REQ_PARM_LAYOUT_TYPE_ID, DocumentSectionLayoutType.ID_PORTRAIT);
		int sectionType 	= ServletRequestUtils.getIntParameter(request, REQ_PARM_SECTION_TYPE_ID, DocumentSection.SECTION_TYPE_DEFAULT);
		String sectionName 	= ServletRequestUtils.getStringParameter(request, REQ_PARM_SECTION_NAME, null);
		String dim			=  ServletRequestUtils.getStringParameter(request, REQ_PARM_DIMENSIONS, null);

		// VALIDATIONS
		String errorMsg = null;
		
		String[] dimensions;
		String width = null;
		String height = null;
		if ( dim != null ) {
			dimensions = dim.split(":");
			if (dimensions[0].isEmpty() || Double.valueOf(dimensions[0]) <= 1 || dimensions[1].isEmpty() || Double.valueOf(dimensions[1]) <= 1 ) {
				errorMsg = ApplicationUtil.getMessage("error.section.dimensions.invalid");
			} else {
				width = dimensions[0];
				height = dimensions[1];
			}
		}
		
		List<String> sectionNames = new ArrayList<>();
		for (DocumentSection currentSection: document.getDocumentSections()) {
			sectionNames.add(currentSection.getName());
			if(document.isMPHCSCompositionTouchpoint() || document.isSefasCompositionTouchpoint()) {
				if (layoutType == DocumentSectionLayoutType.ID_CUSTOM) {
					if (currentSection.getLayoutType() == DocumentSectionLayoutType.ID_CUSTOM) {
						if (!((Float.compare(Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getHeight())), Float.valueOf(height)) == 0 && Float.compare(Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getWidth())), Float.valueOf(width)) == 0) ||
								(Float.compare(Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getHeight())), Float.valueOf(width)) == 0 && Float.compare(Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getWidth())), Float.valueOf(height)) == 0))) {
							errorMsg = ApplicationUtil.getMessage("error.section.size.different");
						}
					} else {
						DocumentSectionLayoutType currentDocumentLayoutType = new DocumentSectionLayoutType(currentSection.getLayoutType());
						if (!((currentDocumentLayoutType.getHeight() == Float.valueOf(height) && currentDocumentLayoutType.getWidth() == Float.valueOf(width)) || (currentDocumentLayoutType.getHeight() == Float.valueOf(width) && currentDocumentLayoutType.getWidth() == Float.valueOf(height)))) {
							errorMsg = ApplicationUtil.getMessage("error.section.size.different");
						}
					}
				} else {
					if (currentSection.getLayoutType() != DocumentSectionLayoutType.ID_CUSTOM) {
						if (!(currentSection.getLayoutType() == layoutType || layoutType == getLayoutEquivalentOrientation(currentSection.getLayoutType()))) {
							errorMsg = ApplicationUtil.getMessage("error.section.size.different");
						}
					} else {
						DocumentSectionLayoutType documentSectionLayoutType = new DocumentSectionLayoutType(layoutType);
						if (!((Float.compare(documentSectionLayoutType.getHeight(), Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getHeight()))) == 0 && Float.compare(documentSectionLayoutType.getWidth(), Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getWidth()))) == 0)
								|| (Float.compare(documentSectionLayoutType.getHeight(), Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getWidth()))) == 0 && Float.compare(documentSectionLayoutType.getWidth(), Float.valueOf(DecimalValueUtil.dehydrate(currentSection.getHeight()))) == 0))) {
							errorMsg = ApplicationUtil.getMessage("error.section.size.different");
						}
					}
				}
			}
		}

		if (sectionName.trim().isEmpty()) {
			errorMsg = ApplicationUtil.getMessage("error.section.name.required");
		} else if(sectionName.length() > 96){
			errorMsg = ApplicationUtil.getMessage("error.section.name.too.long");
		} else if (sectionNames.contains(sectionName.trim())) {
			errorMsg = ApplicationUtil.getMessage("error.section.name.must.be.unique");
		}
		if (errorMsg != null) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", errorMsg);
			return returnObj.toString();
		}


		DocumentTag docTag = new DocumentTag();
		docTag.setType(DocumentTag.INTERFACE_TYPE_ADMIN);
		
		// PERSIST NEW SECTION
		ServiceExecutionContext context = UpdateDocumentService.createContextForSectionAdd(document, sectionName, 1, document.getDocumentSections().size(), layoutType, width, height, sectionType, UserUtil.getPrincipalUserId());
		Service updateDocService = MessagepointServiceFactory.getInstance().lookupService(UpdateDocumentService.SERVICE_NAME, UpdateDocumentService.class);
		updateDocService.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		
		if ( !serviceResponse.isSuccessful() ) {
			
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", "Failed to add new section");
			return returnObj.toString();
			
		} else {
			
			List<DocumentSection> newSections = (List<DocumentSection>)context.getResponse().getResultValueBean();
			long sectionId = newSections.iterator().next().getId();

			// Type: TOUCHPOINT NAVIGATION
			JSONObject returnObj = new JSONObject();
			docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_FULLSCREEN);
	
			DocumentSection section = DocumentSection.findById(sectionId);
			
			// Section Size: CUSTOM AND DEFAULT MANAGEMENT
	
			String sizeOverride = ServletRequestUtils.getStringParameter(request, REQ_PARM_SIZE, "110");
			String size = sizeOverride + "%";
			
			Integer maxWidth = ServletRequestUtils.getIntParameter(request, REQ_PARM_MAX_WIDTH, -1);
			
			returnObj.put( "name", document.getName() );
			returnObj.put( "id", document.getId() );
			returnObj.put( "section_name", section.getName() );
			returnObj.put( "section_id", section.getId() );
			returnObj.put( "section_layout", section.getLayoutType() );
			if ( section.getLayoutType() == DocumentSectionLayoutType.ID_CUSTOM ) {
				returnObj.put( "section_width", DecimalValueUtil.dehydrate(section.getWidth()) );
				returnObj.put( "section_height", DecimalValueUtil.dehydrate(section.getHeight()) );
			}
			returnObj.put( "section_html", StringEscapeUtils.escapeXml(docTag.getSectionImageHTML(document.getId(), sectionId , -1L, size, maxWidth, request)) );
			returnObj.put("page_numbering_type", section.getPageNumberingType());
			returnObj.put("section_type", section.getSectionType());
			
			if ( section.getSectionType() == DocumentSection.SECTION_TYPE_FREEFORM && section.getZones().size() == 1 ) {
				Zone freeformZone = section.getZones().iterator().next();
				
				JSONObject zoneObj = new JSONObject();
				zoneObj.put("zone_id"			, freeformZone.getId());
				zoneObj.put("zone_name"			, freeformZone.getFriendlyName());
				zoneObj.put("zone_type"			, freeformZone.getZoneTypeId());
				zoneObj.put("section_id"		, freeformZone.getSection().getId() );
				zoneObj.put("width"			, DecimalValueUtil.dehydrate(freeformZone.getWidth()) );
				zoneObj.put("height"			, DecimalValueUtil.dehydrate(freeformZone.getHeight()) );
				zoneObj.put("left"				, DecimalValueUtil.dehydrate(freeformZone.getTopX()) );
				zoneObj.put("top"				, DecimalValueUtil.dehydrate(freeformZone.getTopY()) );
				zoneObj.put("zone_content_type", freeformZone.getContentTypeId() );

				returnObj.put("zone", zoneObj);
			} else if ( section.getSectionType() == DocumentSection.SECTION_TYPE_PDF && section.getZones().size() == 1 ) {
				Zone pdfZone = section.getZones().iterator().next();

				JSONObject zoneObj = new JSONObject();
				zoneObj.put("zone_id"			, pdfZone.getId());
				zoneObj.put("zone_name"			, pdfZone.getFriendlyName());
				zoneObj.put("zone_type"			, pdfZone.getZoneTypeId());
				zoneObj.put("section_id"		, pdfZone.getSection().getId() );
				zoneObj.put("width"				, DecimalValueUtil.dehydrate(pdfZone.getWidth()) );
				zoneObj.put("height"			, DecimalValueUtil.dehydrate(pdfZone.getHeight()) );
				zoneObj.put("left"				, DecimalValueUtil.dehydrate(pdfZone.getTopX()) );
				zoneObj.put("top"				, DecimalValueUtil.dehydrate(pdfZone.getTopY()) );
				zoneObj.put("zone_content_type"	, pdfZone.getContentTypeId() );

				returnObj.put("zone", zoneObj);
			}
			
			return returnObj.toString();
		
		}
	}
	
	@SuppressWarnings("unchecked")
	private String addZoneOrParts(Document document, HttpServletRequest request) throws Exception {

		long sectionId 	= ServletRequestUtils.getLongParameter(request, REQ_PARM_SECTION_ID, -1);
		Long zoneId 	= ServletRequestUtils.getLongParameter(request, REQ_PARM_ZONE_ID, -1);
		DocumentSection section = DocumentSection.findById(sectionId);
		if(section.isAlternate()){
			section = section.getParent();
		}
		if(document.isAlternate()){
			document = document.getParent();
		}
		String zoneAttr 	= ServletRequestUtils.getStringParameter(request, REQ_PARM_NEW_ZONE_ATTR, null);
		String partAttr 	= ServletRequestUtils.getStringParameter(request, REQ_PARM_NEW_PART_ATTR, null);
		String removedParts = ServletRequestUtils.getStringParameter(request, REQ_PARM_REMOVED_PARTS, null);

		Zone zone = null;
		String zoneTemplateId = null;
		if (zoneAttr != null) {
			String[] zoneAttrArray = zoneAttr.split(":");

			zoneTemplateId = zoneAttrArray[0];

			zone = new Zone();		
			zone.setTopX( DecimalValueUtil.hydrate(zoneAttrArray[3]) );
			zone.setTopY( DecimalValueUtil.hydrate(zoneAttrArray[4]) );
			zone.setWidth( DecimalValueUtil.hydrate(zoneAttrArray[1]) );
			zone.setHeight( DecimalValueUtil.hydrate(zoneAttrArray[2]) );
			zone.setSection(section);
			zone.setPage( section.getSectionOrder() );
			zone.setContentTypeId( ContentType.TEXT );

			if(document.isGMCTouchpoint()){
				zone.setSupportsTables(false);
			}
			
			DataGroup firstLevelDataGroup = DataGroup.getFirstLevelDataGroup( section.getDocument().getPrimaryDataSource() );
			if ( firstLevelDataGroup != null)
				zone.setDataGroup( firstLevelDataGroup );
			
			if ( document.isNativeCompositionTouchpoint() )
				zone.setImageDimensionsTypeId( ZoneImageDimensionsType.ID_IMAGE_DIMENSION_TYPE_UNALTERED );
	
			String zoneType = zoneAttrArray[5];
			if ( zoneType.equals("header") ) {
				zone.setZoneTypeId(ZoneType.ID_HEADER_ZONE);
				zone.setName( ApplicationUtil.getMessage("page.label.header") );
				zone.setFriendlyName( ApplicationUtil.getMessage("page.label.header") );
			} else if ( zoneType.equals("footer") ) {
				zone.setZoneTypeId(ZoneType.ID_FOOTER_ZONE);
				zone.setName( ApplicationUtil.getMessage("page.label.footer") );
				zone.setFriendlyName( ApplicationUtil.getMessage("page.label.footer") );
			} else if ( zoneType.equals("regionleft") ) {
				zone.setZoneTypeId(ZoneType.ID_REGION_LEFT_ZONE);
				zone.setName( ApplicationUtil.getMessage("page.label.region.left") );
				zone.setFriendlyName( ApplicationUtil.getMessage("page.label.region.left") );
			} else if ( zoneType.equals("regionright") ) {
				zone.setZoneTypeId(ZoneType.ID_REGION_RIGHT_ZONE);
				zone.setName( ApplicationUtil.getMessage("page.label.region.right") );
				zone.setFriendlyName( ApplicationUtil.getMessage("page.label.region.right") );
			} else {
				zone.setZoneTypeId(ZoneType.ID_DOCUMENT_ZONE);
				zone.setName( ApplicationUtil.getMessage("page.text.new.zone") );
				zone.setFriendlyName( ApplicationUtil.getMessage("page.text.new.zone") );
			}
			
			zone.setBackgroundColor(Zone.ZONE_BACKGROUND_DEFAULT);
			zone.setUpdatedBy( UserUtil.getPrincipalUserId() );
			
			Set<Zone> zones = document.getZones();
			zones.add(zone);
			document.setZones(zones);
			zone.associateDocument( document );
		} else if ( zoneId != null ) {
			zone = Zone.findById( zoneId );
		}

		JSONArray partsReturnObjArray = new JSONArray();
		Set<Long> removedPartIds = new HashSet<>();
		Set<Zone> channelMasterAlternateZones = new HashSet<>();
		if ( partAttr != null ) {
			
			zone.setContentTypeId( ContentType.MULTIPART );
			zone.setSubContentTypeId( 0 );
			
			String[] newPartsArray = partAttr.split(";");
			Set<ZonePart> newPartsList = zone.getParts();
			
			int numberOfRemovedParts = 0;
			if ( removedParts != null ) {
				String[] removedPartsArray = removedParts.split(";");
				numberOfRemovedParts = removedPartsArray.length;
				
				for ( int j=0; j < removedPartsArray.length; j++ )
					removedPartIds.add( Long.valueOf(removedPartsArray[j]) );
			}

			int currentPartSequence = zone.getParts().size() - numberOfRemovedParts;
			for ( int i=0; i < newPartsArray.length; i++ ) {
				if (!newPartsArray[i].isEmpty()) {
					String[] partAttrArray = newPartsArray[i].split(":");
					
					ZonePart newPart = new ZonePart();
					
					newPart.setContentType( ContentType.getTextContentType() );
					newPart.setUpdatedBy( UserUtil.getPrincipalUserId() );
					newPart.setWidth( DecimalValueUtil.hydrate(partAttrArray[1]) );
					newPart.setHeight( DecimalValueUtil.hydrate(partAttrArray[2]) );
					newPart.setTopX( DecimalValueUtil.hydrate(partAttrArray[3]) );
					newPart.setTopY( DecimalValueUtil.hydrate(partAttrArray[4]) );
					newPart.setName( ApplicationUtil.getMessage("page.text.new.part") );
					
					currentPartSequence++;
					newPart.setSequence( currentPartSequence );
					
					JSONObject partObj = new JSONObject();
					partObj.put("sequence", currentPartSequence);
					partObj.put("part_template_id", partAttrArray[0]);
					
					newPart.setZone(zone);
					
					partsReturnObjArray.put(partObj);
					newPartsList.add(newPart);
				}
			}
			
			if ( document.getIsOmniChannel() && !newPartsList.isEmpty())
				channelMasterAlternateZones = applyPartsToChannelAlternates(document,zone,newPartsList);


			zone.setParts(newPartsList);
			
		}

		Set<Zone> zoneSet = new HashSet<>();
		zoneSet.add(zone);
		ServiceExecutionContext context = UpdateZoneService.createContextForZoneAndPartUpdate(zoneSet, removedPartIds);
		Service updateZoneService = MessagepointServiceFactory.getInstance().lookupService(UpdateZoneService.SERVICE_NAME, UpdateZoneService.class);
		
		updateZoneService.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		
		JSONObject returnObj = new JSONObject();
		
		if ( zoneTemplateId != null )	
			returnObj.put("zone_template_id", zoneTemplateId);
			
		if ( !serviceResponse.isSuccessful() ) {

			returnObj.put("error", true);
			returnObj.put("message", "Failed to add new zone");

		} else {
			
			Set<Zone> newZones = (Set<Zone>)context.getResponse().getResultValueBean();
			long newZoneId = newZones.iterator().next().getId();

			Zone returnZone = Zone.findById(newZoneId);

			returnObj.put( "zone_name", returnZone.getFriendlyName() );
			returnObj.put( "zone_id", returnZone.getId() );
			returnObj.put( "section_id", returnZone.getSection() != null ? returnZone.getSection().getId() : -1 );
			returnObj.put( "width", DecimalValueUtil.dehydrate(returnZone.getWidth()) );
			returnObj.put( "height", DecimalValueUtil.dehydrate(returnZone.getHeight()) );
			returnObj.put( "left", DecimalValueUtil.dehydrate(returnZone.getTopX()) );
			returnObj.put( "top", DecimalValueUtil.dehydrate(returnZone.getTopY()) );
			returnObj.put( "zone_content_type", returnZone.getContentTypeId() );
			returnObj.put( "zone_type", returnZone.getZoneTypeId() );
			
			for (int j=0; j < partsReturnObjArray.length(); j++) {
				for ( ZonePart currentPart: returnZone.getParts() ) {
					if ( currentPart.getSequence() == partsReturnObjArray.getJSONObject(j).getInt("sequence") ) {
						JSONObject currentReturnPart = partsReturnObjArray.getJSONObject(j);
						currentReturnPart.put("part_id", currentPart.getId());
						currentReturnPart.put("part_name", currentPart.getName());
						currentReturnPart.put( "width", DecimalValueUtil.dehydrate(currentPart.getWidth()) );
						currentReturnPart.put( "height", DecimalValueUtil.dehydrate(currentPart.getHeight()) );
						currentReturnPart.put( "left", DecimalValueUtil.dehydrate(currentPart.getTopX()) );
						currentReturnPart.put( "top", DecimalValueUtil.dehydrate(currentPart.getTopY()) );
						currentReturnPart.put( "part_type", currentPart.getContentType().getId() );
					}
				}
			}
			
			returnObj.put("parts",partsReturnObjArray);

			// Update Alternate Layouts: Create corresponding zone/parts
			Set<Zone> alternateZones = new HashSet<>();
			Set<Long> alternateRemovedPartIds = new HashSet<>();
			for (Document currentLayout: document.getAlternateLayouts() ) {
				
				Zone alternateZone = currentLayout.findZoneByParent(returnZone);
				
				if ( alternateZone == null || alternateZone.getId() == returnZone.getId()) {
				
					alternateZone = new Zone();
					
					alternateZone.setParentObject(returnZone);
					alternateZone.setDocument(currentLayout);
					alternateZone.setSection(currentLayout.findSectionByParent(returnZone.getSection()));
					
					Set<Zone> layoutZones = currentLayout.getZones();
					layoutZones.add(alternateZone);
					currentLayout.setZones(layoutZones);
					alternateZone.associateDocument( currentLayout );
				
				}
				
				Set<ZonePart> alternateZoneParts = alternateZone.getParts();
				for (ZonePart primaryPart: returnZone.getParts()) {
					ZonePart alternatePart = alternateZone.findPartByParent(primaryPart);
					if ( alternatePart == null ) {
						alternatePart = new ZonePart();
						
						alternatePart.setParentObject(primaryPart);
						alternatePart.setZone(alternateZone);
						
						alternatePart.setSequence(0); //TEMP
						alternatePart.setContentType(ContentType.getTextContentType()); //TEMP
						
						alternateZoneParts.add(alternatePart);
					}
				}
				
				alternateZone.setParts(alternateZoneParts);
				alternateZones.add(alternateZone);
				
				if ( document.getIsOmniChannel() && !alternateZoneParts.isEmpty())
					alternateZones.addAll( applyPartsToChannelAlternates(currentLayout,alternateZone,alternateZoneParts) );
			}
			
			
			for ( Long removedPartId : removedPartIds) {
				List<ZonePart> removedAlternateParts = ZonePart.findByParent(removedPartId);
				for (ZonePart removedAlternatePart: removedAlternateParts) {
					alternateRemovedPartIds.add(removedAlternatePart.getId());
					
					List<ZonePart> removedChannelAlternateParts = ZonePart.findByParent(removedAlternatePart.getId());
					for (ZonePart removedChannelAlternatePart: removedChannelAlternateParts)
						alternateRemovedPartIds.add(removedChannelAlternatePart.getId());
				}
			}

			alternateZones.addAll( channelMasterAlternateZones );
			ServiceExecutionContext alternateZoneContext = UpdateZoneService.createContextForZoneAndPartUpdate(alternateZones, alternateRemovedPartIds);
			Service alternateUpdateZoneService = MessagepointServiceFactory.getInstance().lookupService(UpdateZoneService.SERVICE_NAME, UpdateZoneService.class);
			
			alternateUpdateZoneService.execute(alternateZoneContext);
			ServiceResponse alternateServiceResponse = alternateZoneContext.getResponse();
			
			if ( !alternateServiceResponse.isSuccessful() ) {

				returnObj.put("error", true);
				returnObj.put("message", "Failed to generate alter zones");

			} 
		}
		
		return returnObj.toString();
	}
	
	private Set<Zone> applyPartsToChannelAlternates(Document document, Zone zone, Set<ZonePart> parts) {
		Set<Zone> channelAlternateZones = new HashSet<>();
		for ( Document channelAlternateLayout: document.getChannelAlternateDocuments() ) {
			Zone channelAlternateZone = channelAlternateLayout.findZoneByParent(zone);
			if ( channelAlternateZone != null ) {
				
				Set<ZonePart> channelAlternateZoneParts = channelAlternateZone.getParts();
				for (ZonePart masterPart: parts) {
					ZonePart channelAlternatePart = channelAlternateZone.findPartByParent(masterPart);
					if ( channelAlternatePart == null ) {
						channelAlternatePart = new ZonePart();
						
						channelAlternatePart.setParentObject(masterPart);
						channelAlternatePart.setZone(zone);
						
						channelAlternatePart.setSequence(0); //TEMP
						channelAlternatePart.setContentType(ContentType.getTextContentType()); //TEMP
						
						channelAlternateZoneParts.add(channelAlternatePart);
					}
				}
				
				channelAlternateZone.setParts(channelAlternateZoneParts);
				
				if (!channelAlternateZone.getParts().isEmpty())
					channelAlternateZone.setContentTypeId(ContentType.MULTIPART);
				
				channelAlternateZones.add(channelAlternateZone);
			}
		}
		
		return channelAlternateZones;
	}
	
	private String getZoneInfo(Document document, HttpServletRequest request) throws Exception {
		JSONObject returnObj = new JSONObject();
		
		JSONArray zonesReturnObjArray = new JSONArray();
		JSONArray partsReturnObjArray = new JSONArray();
		for ( Zone currentZone: document.getZones() ) {
			
			JSONObject zoneObj = new JSONObject();
			zoneObj.put("id", currentZone.getId());
			zoneObj.put("name", currentZone.getFriendlyName());
			zoneObj.put("type", currentZone.getContentTypeId());
			zonesReturnObjArray.put(zoneObj);
			
			if ( currentZone.isMultipart() )
				for ( ZonePart currentPart: currentZone.getParts() ) {
					JSONObject partObj = new JSONObject();
					partObj.put("id", currentPart.getId());
					partObj.put("name", currentPart.getName());
					partObj.put("sequence", currentPart.getSequence());
					partObj.put("type", currentPart.getContentType().getId());
					partsReturnObjArray.put(partObj);
				}
			
		}
		
		returnObj.put("zones", zonesReturnObjArray);
		returnObj.put("parts", partsReturnObjArray);
		
		return returnObj.toString();
	}
	
	private String generateTouchpointContext(HttpServletRequest request) throws Exception {
		
		String sectionThumbnailHTML = "";
		
		long documentId 	= getDocumentIdParam(request);
		long collectionId	= getCollectionIdParam(request);
		String zoneFilter 	= getZoneFilterParam(request);
		
		TouchpointCollection touchpointCollection = null;
		List<Document> collectionDocuments = new ArrayList<>();
		if ( collectionId != -1 ) {
			touchpointCollection = TouchpointCollection.findById(collectionId);
			if ( touchpointCollection != null ) {
				collectionDocuments = touchpointCollection.getDocumentsSortedOnOrder();
				documentId = collectionDocuments.get(0).getId();	
			}
		}
		
		Document document = Document.findById(documentId);
		
		DocumentTag docTag = new DocumentTag();
		if ( getVisibilityParam(request).equalsIgnoreCase(VISIBILITY_FULL) )
			docTag.setType(DocumentTag.INTERFACE_TYPE_ADMIN);
		docTag.setZoneFilter(zoneFilter);
		
		// Type: TOUCHPOINT CONTEXT
		JSONObject obj = new JSONObject();

		if (document != null || touchpointCollection != null)
		{
			docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_THUMBNAIL_DOCUMENT);
			sectionThumbnailHTML = docTag.getSectionImageHTML(documentId, -1L , -1L, "50%", 160, request);
	
			obj.put("name"			, touchpointCollection != null ? touchpointCollection.getName() : document.getName());
			obj.put("id"			, touchpointCollection != null ? collectionId : documentId);
			if ( touchpointCollection != null )
				obj.put("touchpoint_count", collectionDocuments.size());
			obj.put("section_html"	, StringEscapeUtils.escapeXml(sectionThumbnailHTML));
		}
		else
		{
			obj.put("name"			, "No published touchpoint");
			obj.put("id"			, -1);
			obj.put("section_html"	, "");
		}
		
		return obj.toString();
		
	}
	
	private String generateContentUpdate(Document document, HttpServletRequest request) throws Exception {
		// Type: UPDATE CONTENT
		JSONObject returnObj = new JSONObject();

		TouchpointSelection selection = TouchpointSelection.findById(getTouchpointSelectionIdParam(request));
		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		MessagepointLocale locale = UserUtil.getCurrentLanguageLocaleContext();

		Document primaryDocument = document;
		if ( document != null && (document.isAlternate() || document.isChannelAlternate()) )
			primaryDocument = document.getRootDocument();
		
		// Resolve first matching delivery for Touchpoint
		Zone targetZone = null;
		if (contentObject.getZone().getDocument().getId() == primaryDocument.getId())
			targetZone = contentObject.getZone();
		
		TouchpointSelection selectionContext = UserUtil.getCurrentSelectionContext(targetZone);
		if ( (document != null && document.isChannelAlternate()) || (selectionContext != null && selectionContext.getAlternateLayout() != null) )
			targetZone = document.findZoneByParent(targetZone);

		Double fontScale = 1.0;
		int maxWidth = ServletRequestUtils.getIntParameter(request, REQ_PARM_MAX_WIDTH, -1);
		if ( maxWidth == -1 && targetZone.getSection() != null )
			maxWidth = DocumentTag.getFullScaleSectionWidth(targetZone.getSection());
		if ( maxWidth > -1 && targetZone.getSection() != null )
			fontScale = (double)Math.min(maxWidth, DocumentTag.getFullScaleSectionWidth(targetZone.getSection())) / DocumentTag.getFullScaleSectionWidth(targetZone.getSection());
		if ( document.isSmsTouchpoint() )
			fontScale = 1.0;
		returnObj.put("fontScale", fontScale);

		returnObj.put( "section_id",  targetZone.getSection() != null ? targetZone.getSection().getId() : -1 );
		
		JSONArray contentsArray = new JSONArray();
		
		List<ContentVO> currentContents = ContentObjectAssociation.getMasterOrTPVariantContent(contentObject, contentObject.getFocusOnDataType(), -1L, true,
				selection != null && !selection.isMaster() && (contentObject.isStructuredContentEnabled() || contentObject.isDynamicVariantEnabled() || contentObject.isVariantType()) ?
				selection.getParameterGroupTreeNode() : null,
				locale);

		for (ContentVO currentContent: currentContents) {
			JSONObject currentContentObj = new JSONObject();
			currentContentObj.put( "zone_id", 		targetZone.getId() );
			currentContentObj.put( "zone_rotation", targetZone.getRotationAngle() );
			currentContentObj.put( "zone_type", 	targetZone.getZoneTypeId() );
			currentContentObj.put( "part_id", 		targetZone.isAlternate() && currentContent.getZonePartId() > 0 ? 
														targetZone.findPartByParent(ZonePart.findById(currentContent.getZonePartId())).getId() : 
														currentContent.getZonePartId() );
			String content = (targetZone.getContentType().getId() == ContentType.VIDEO && !currentContent.isContainsVariables()) ? currentContent.getContent() : StringEscapeUtils.escapeXml(currentContent.getContent());
			currentContentObj.put( "content",		content );
			currentContentObj.put( "img_resource", 		HttpRequestUtil.getFileResourceToken(currentContent.getImageLocation()) );
			currentContentObj.put( "img_name", 		currentContent.getImageName());
			currentContentObj.put( "message_id", 	contentObject.getId());
			currentContentObj.put( "content_type", 	targetZone.getContentTypeId() );
			currentContentObj.put( "content_state", (currentContent.getContent() == null || currentContent.getContent().trim().isEmpty()) && currentContent.getImageLocation() == null ? "NO_CONTENT" : "CONTENT" );
			contentsArray.put(currentContentObj);
		}
		
		if (currentContents.isEmpty()) {
			if (targetZone.isMultipart()) {
				for (ZonePart currentPart: targetZone.getParts()) {
					JSONObject currentContentObj = new JSONObject();
					currentContentObj.put( "zone_id", 		targetZone.getId() );
					currentContentObj.put( "zone_type", 	targetZone.getZoneTypeId() );
					currentContentObj.put( "part_id", 		currentPart.getId() );
					currentContentObj.put( "message_id", 	contentObject.getId());
					currentContentObj.put( "content_type", 	targetZone.getContentTypeId() );
					currentContentObj.put( "content_state", "NO_CONTENT" );
					contentsArray.put(currentContentObj);
				}
			} else {
				JSONObject currentContentObj = new JSONObject();
				currentContentObj.put( "zone_id", 		targetZone.getId() );
				currentContentObj.put( "zone_type", 	targetZone.getZoneTypeId() );
				currentContentObj.put( "message_id", 	contentObject.getId());
				currentContentObj.put( "content_type", 	targetZone.getContentTypeId() );
				currentContentObj.put( "content_state", "NO_CONTENT" );
				contentsArray.put(currentContentObj);
			}
		}

		returnObj.put("contents", contentsArray);

		return returnObj.toString();
	}

	private int getLayoutEquivalentOrientation(int layoutType){
		if(layoutType == DocumentSectionLayoutType.ID_PORTRAIT) return DocumentSectionLayoutType.ID_LANDSCAPE;
		if(layoutType == DocumentSectionLayoutType.ID_LANDSCAPE) return  DocumentSectionLayoutType.ID_PORTRAIT;
		if(layoutType == DocumentSectionLayoutType.ID_LEGAL_PORTRAIT) return DocumentSectionLayoutType.ID_LEGAL_LANDSCAPE;
		if(layoutType == DocumentSectionLayoutType.ID_LEGAL_LANDSCAPE) return DocumentSectionLayoutType.ID_LEGAL_PORTRAIT;
		if(layoutType == DocumentSectionLayoutType.ID_A4_PORTRAIT) return DocumentSectionLayoutType.ID_A4_LANDSCAPE;
		if(layoutType == DocumentSectionLayoutType.ID_A4_LANDSCAPE) return DocumentSectionLayoutType.ID_A4_PORTRAIT;
		if(layoutType == DocumentSectionLayoutType.ID_A3_PORTRAIT) return DocumentSectionLayoutType.ID_A3_LANDSCAPE;
		if(layoutType == DocumentSectionLayoutType.ID_A3_LANDSCAPE) return DocumentSectionLayoutType.ID_A3_PORTRAIT;
		return -1;
	}

	@SuppressWarnings("deprecation")
	private String generateSummaryDetails(Document document, HttpServletRequest request) throws Exception {
		
		String sectionThumbnailHTML = "";
		
		long documentId 	= getDocumentIdParam(request);
		Long sectionId		= getSectionIdParam(request);
		Long zoneId			= getZoneIdParam(request);
		String zoneFilter 	= getZoneFilterParam(request);
		
		DocumentTag docTag = new DocumentTag();
		if ( getVisibilityParam(request).equalsIgnoreCase(VISIBILITY_FULL) )
			docTag.setType(DocumentTag.INTERFACE_TYPE_ADMIN);
		docTag.setZoneFilter(zoneFilter);
		
		String requestedType = getTypeParam(request);
		
		// Type: SUMMARY POPUPS
		// Touchpoint summary popup types
		if ( getDocumentIdParam(request) != -1 ) {
			docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_THUMBNAIL_DOCUMENT);
			sectionThumbnailHTML = docTag.getSectionImageHTML(documentId, -1L , -1L, "62.5%", null, request);
		} else if ( getSectionIdParam(request) != -1 ) {
			docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_THUMBNAIL_SECTION);
			sectionThumbnailHTML = docTag.getSectionImageHTML(-1L, sectionId, -1L, "62.5%", null, request);
		} else if ( getZoneIdParam(request) != -1 ) {
			docTag.setPresentationType(DocumentTag.PRESENTATION_TYPE_THUMBNAIL_ZONE);
			sectionThumbnailHTML = docTag.getSectionImageHTML(-1L, -1L, zoneId, "62.5%", null, request);
		}

		String listItemCountHTML = "";
		if ( requestedType.equalsIgnoreCase(TYPE_INSERT) )
			listItemCountHTML = InsertUtil.getInsertCountHTML(getInsertListByDocumentId(documentId));
		else if ( requestedType.equalsIgnoreCase(TYPE_INSERT_SCHED) ) {
			String dateParam = ServletRequestUtils.getStringParameter(request, REQ_PARM_DATE, "");
			Date filterDate = new Date(dateParam);
			listItemCountHTML = InsertScheduleControllerUtil.getInsertSchedCountHTML(getInsertSchedListByDocumentId(documentId,filterDate),filterDate);
		} else if ( requestedType.equalsIgnoreCase(TYPE_MESSAGE) && documentId != -1 )
			listItemCountHTML = ContentObjectUtil.getMessageCountHTML(getMessageListByDocumentId(documentId));
		else if ( requestedType.equalsIgnoreCase(TYPE_MESSAGE) && zoneId != -1 )
			listItemCountHTML = ContentObjectUtil.getMessageCountHTML(getMessageListByZoneId(zoneId));
		
		return "<?xml version='1.0'?><sectionImageHTML>"+StringEscapeUtils.escapeXml(sectionThumbnailHTML+listItemCountHTML)+"</sectionImageHTML>";
	}
	
	private List<ContentObject> getMessageListByZoneId(long zoneId) {
		return ContentObjectUtil.getMessageListFromService(ContentObjectUtil.LIST_BY_ZONE, zoneId);
	}
	
	private List<ContentObject> getMessageListByDocumentId(long documentId) {
		return ContentObjectUtil.getMessageListFromService(ContentObjectUtil.LIST_BY_TOUCHPOINT, documentId);
	}
	
	private List<Insert> getInsertListByDocumentId(long documentId) {
		if (documentId != -1)
			return InsertUtil.getInsertListFromService(GetInsertListService.LIST_BY_TOUCHPOINT, documentId);
		else
			return InsertUtil.getInsertListFromService(GetInsertListService.LIST_BY_NO_TOUCHPOINT, GetInsertListService.LIST_BY_ALL);
	}
	
	private List<InsertSchedule> getInsertSchedListByDocumentId(long documentId, Date filterDate) {
		return InsertScheduleControllerUtil.getInsertSchedListFromService(documentId, filterDate);
	}
	
	private Long getDocumentIdParam( HttpServletRequest request ) {
		long documentid = ServletRequestUtils.getLongParameter(request, "documentid", -1L);
		long documentId = ServletRequestUtils.getLongParameter(request, "documentId", -1L);
		if (documentid != -1)
			return documentid;
		else if (documentId != -1) {
			return documentId;
		} else {
			return ServletRequestUtils.getLongParameter(request, "docid", -1L);
		}
	}
	
	private Long getTouchpointSelectionIdParam( HttpServletRequest request ) {
		long touchpointSelectionid = ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_SELECTION_ID, -1L);
		Long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, AsyncListTableController.PARAM_SELECTION_ID, -1L);
		if (touchpointSelectionid != -1)
			return touchpointSelectionid;
		else
			return touchpointSelectionId;
	}
	
	private Long getSectionIdParam( HttpServletRequest request ) {
		long sectionid = ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_SECTIONID, -1L);
		Long sectionId = ServletRequestUtils.getLongParameter(request, REQ_PARM_SECTION_ID, -1L);
		if (sectionid != -1)
			return sectionid;
		else
			return sectionId;
	}
	
	private Long getZoneIdParam( HttpServletRequest request ) {
		long zoneid = ServletRequestUtils.getLongParameter(request, TouchpointContentObjectListController.REQ_PARM_ZONEID, -1L);
		Long zoneId = ServletRequestUtils.getLongParameter(request, REQ_PARM_ZONE_ID, -1L);
		if (zoneid != -1)
			return zoneid;
		else
			return zoneId;
	}

	private Long getSelectionStatusIdParam( HttpServletRequest request ) {
		long statusId = ServletRequestUtils.getLongParameter(request, REQ_PARM_SELECTION_STATUS_ID, -1L);
		if (statusId == -1)
			statusId = Long.parseLong(String.valueOf(UserUtil.getCurrentSelectionStatusContext().getId()));
		return statusId;
	}

	private String getTypeParam( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, "null");
	}
	
	private String getZoneFilterParam( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARM_ZONE_FILTER, DocumentTag.ZONE_FILTER_DEFAULT);
	}
	
	private String getVisibilityParam( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARM_VISIBILITY, "null");
	}
	
	private Long getCollectionIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARM_COLLECTION_ID, -1L);
	}

}