package com.prinova.messagepoint.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.connected.data.CacheDataRepository;
import com.prinova.messagepoint.model.file.DatabaseFile;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.font.TextStyleFont;
import com.prinova.messagepoint.model.font.TextStyleFontJsonVO;
import com.prinova.messagepoint.model.font.TextStyleFontCharactersJsonVO;
import com.prinova.messagepoint.model.font.TextStyleJsonVO;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceMessage;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.TextStyleService;
import com.prinova.messagepoint.platform.services.whereused.CreateWhereUsedReportService;
import com.prinova.messagepoint.query.IPage;
import com.prinova.messagepoint.query.service.HibernatePaginationService;
import com.prinova.messagepoint.query.service.PaginationServiceResponse;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.beanvalidation.MessageSourceResourceBundleLocator;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.awt.Font;
import java.awt.FontFormatException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.util.ApplicationUtil.getBean;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Objects.nonNull;

@RestController
public class TextStyleController {

    private static final Log log = LogUtil.getLog(TextStyleController.class);

    private static final String FILTER = "filter";
    private static final String PAGE_INDEX = "pageIndex";
    private static final String PAGE_SIZE = "pageSize";
    private static final String SORT_DIRECTION = "sortDirection";
    private static final String SORT_KEY = "sortKey";
    private static final String WHERE_USED_TEXT_STYLE_ID = "whereUsedTextStyleId";
    private static final String WHERE_USED_REPORT_ID = "whereUsedReportId";
    private static final String TEXT_STYLE_FONT_ID = "textStyleFontId";
    private static final String TEXT_STYLE_ID = "textStyleId";

    @Autowired
    private CacheDataRepository cacheDataRepository;

    @RequestMapping(value = "/textstyle.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getTextStyles(
            @RequestParam(name = PAGE_INDEX, defaultValue = "1") int pageIndex,
            @RequestParam(name = PAGE_SIZE, defaultValue = "0") int pageSize,
            @RequestParam(name = FILTER, required = false) String filter,
            @RequestParam(name = SORT_KEY, required = false) String sortKey,
            @RequestParam(name = SORT_DIRECTION, required = false) String sortDirection
    ) throws JSONException, IOException {

        JSONObject result = new JSONObject();

        IPage page = getPage(pageIndex, pageSize, filter, sortKey, sortDirection);
        List<TextStyle> textStyles = getTextStyles(page.getList());

        JSONArray data = new JSONArray();
        ObjectMapper objectMapper = new ObjectMapper();

        for (TextStyle textStyle : textStyles) {
            String jsonString = objectMapper.writeValueAsString(new TextStyleJsonVO(textStyle));
            data.put(new JSONObject(jsonString));
        }

        result.put(PAGE_INDEX, page.getPageIndex());
        result.put(PAGE_SIZE, page.getPageSize());
        result.put("rows", page.getRowCount());
        result.put("data", data);

        return result.toString();
    }

    @RequestMapping(value = "/textstyle_fonts.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getTextStyleFonts() throws JSONException {

        ObjectMapper objectMapper = new ObjectMapper();

        List<Object> voList = TextStyleFont.findAll().stream().map(font -> {

            String jsonString = null;

            try {
                jsonString = objectMapper.writeValueAsString(new TextStyleFontJsonVO(font));
            } catch (IOException e) {
                LogUtil.getLog(TextStyleController.class).error("Error:", e);
            }

            return new JSONObject(jsonString);
        }).collect(Collectors.toList());

        return new JSONArray(voList).toString();

    }

    @RequestMapping(value = "/textstyle_where_used.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getTextStyleWhereUsed(
            @RequestParam(name = WHERE_USED_TEXT_STYLE_ID, defaultValue = "-1") String whereUsedTextStyleId,
            @RequestParam(name = WHERE_USED_REPORT_ID, defaultValue = "-1") String whereUsedReportId
    ) throws JSONException {

        JSONObject result = new JSONObject();

        long reportId = Long.valueOf(whereUsedReportId);
        long textStyleId = Long.valueOf(whereUsedTextStyleId);

        ServiceExecutionContext context = CreateWhereUsedReportService.createContext(reportId, textStyleId, TextStyle.class, false, new String[]{}, UserUtil.getPrincipalUser());
        Service service = MessagepointServiceFactory.getInstance().lookupService(CreateWhereUsedReportService.SERVICE_NAME, CreateWhereUsedReportService.class);
        service.execute(context);
        ServiceResponse serviceResponse = context.getResponse();
        if (!serviceResponse.isSuccessful()) {
            String sb = CreateWhereUsedReportService.SERVICE_NAME +
                    " service call is not successful " +
                    " in " + this.getClass().getName() +
                    " Where used report could not be generated. ";
            log.error(sb);
        }

        result.put("success", serviceResponse.isSuccessful());

        return result.toString();

    }

    @RequestMapping(value = "/textstyle_details.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getTextStyleById(
            @RequestParam(name = TEXT_STYLE_ID, defaultValue = "-1") String textStyleId
    ) throws JSONException {

        TextStyle textStyle = TextStyle.findById(Long.valueOf(textStyleId));

        ObjectMapper objectMapper = new ObjectMapper();

        String jsonString = null;

        try {
            jsonString = objectMapper.writeValueAsString(new TextStyleJsonVO(textStyle));
        } catch (IOException e) {
            LogUtil.getLog(TextStyleController.class).error("Error:", e);
        }

        return new JSONObject(jsonString).toString();
    }

    @RequestMapping(value = "/textstyle_font_characters.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getTextStyleFontCharacters(
            @RequestParam(name = TEXT_STYLE_FONT_ID) Long textStyleFontId
    ) throws JSONException, InterruptedException {

        ObjectMapper objectMapper = new ObjectMapper();

        try {
            TextStyleFont font = TextStyleFont.findById(textStyleFontId);
            DatabaseFile fontFile = font.getTtfFile() != null ? font.getTtfFile() : font.getEotFile();

            // OTF files are not supported by Font.createFont method, so we need to use TTF files
            if (fontFile == null || (fontFile != null && fontFile.getFile().getOriginalFilename().endsWith(".otf"))) {
                fontFile = TextStyleFont.findAll().stream()
                        .map(TextStyleFont::getTtfFile)
                        .filter(ttfFile -> ttfFile.getFile().getOriginalFilename().endsWith(".ttf"))
                        .findFirst()
                        .orElse(null);
            }

            if (fontFile != null) {
                InputStream inputStream = fontFile.getFile().getInputStream();
                Font newFont = Font.createFont(Font.TRUETYPE_FONT, inputStream);

                List<String> unicodeCharacters = getAvailableUnicodeCharacters(newFont);

                TextStyleFontCharactersJsonVO jsonVO = new TextStyleFontCharactersJsonVO(font, unicodeCharacters);
                return new JSONObject(objectMapper.writeValueAsString(jsonVO)).toString();
            }
        } catch (IOException | FontFormatException e) {
            LogUtil.getLog(TextStyleController.class).error("Error:", e);
        }

        return null;

    }

    private List<String> getAvailableUnicodeCharacters(Font font) {
        // Calculate the maximum possible Unicode characters (16 bits)
        int maxPossibleCharacters = 0xFFFF;
        List<String> unicodeCharacters = new ArrayList<>();

        for (int i = 0; i < maxPossibleCharacters; i++) {
            char characterToCheck = (char) i;
            if (font.canDisplay(characterToCheck)) {
                unicodeCharacters.add(Integer.toHexString(i));
            }
        }

        return unicodeCharacters;
    }

    /**
     * @param body request body in JSON format:
     *             {
     *             "data":
     *             [
     *             {text_style},
     *             {text_style},
     *             ...
     *             ],
     *             "delete": [0000, 0000, 0000, ...]
     *             }
     * @throws IOException   IOException
     * @throws JSONException JSONException
     */
    @RequestMapping(
            value = "/textstyle.form",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> saveTextStyles(@RequestBody String body) throws IOException, JSONException {

        JSONObject request = new JSONObject(body);
        JSONArray data = request.optJSONArray("data");

        List<TextStyleJsonVO> updateTextStyles = new ArrayList<>();
        Set<Long> deleteIds = getDeleteIds(request);

        List<JSONObject> failedItems = new ArrayList<>();

        if (!data.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();
            Validator validator = getValidator();
            for (int i = 0; i < data.length(); i++) {
                TextStyleJsonVO textStyleJsonVO = objectMapper.readValue(data.get(i).toString(), TextStyleJsonVO.class);
                Set<ConstraintViolation<TextStyleJsonVO>> violations = validator.validate(textStyleJsonVO);
                if (violations.isEmpty()) {
                    updateTextStyles.add(textStyleJsonVO);
                } else {
                    failedItems.add(buildFailedItem(textStyleJsonVO, violations));
                }
            }
        }

        if (failedItems.isEmpty()) {
            ServiceExecutionContext context = TextStyleService.createContext(updateTextStyles, deleteIds);
            Service service = MessagepointServiceFactory.getInstance().lookupService(TextStyleService.SERVICE_NAME, TextStyleService.class);
            service.execute(context);

            ServiceResponse response = context.getResponse();
            if (!response.isSuccessful() && response.hasMessages()) {
                for (ServiceMessage errorMessage : response.getMessages()) {
                    log.error(errorMessage.getDescription());
                    failedItems.add(buildFailedItem(errorMessage));
                }
            }
        }

        JSONObject response = new JSONObject();
        if (failedItems.isEmpty()) {
            cacheDataRepository.updateStylesData();

            response.put("success", true);
        } else {
            response.put("success", false);
            response.put("errors", failedItems);
        }

        return new ResponseEntity<>(response.toString(), HttpStatus.OK);
    }

    private Set<Long> getDeleteIds(final JSONObject request) throws JSONException {
        Set<Long> result = new HashSet<>();
        JSONArray delete = request.optJSONArray("delete");
        if (nonNull(delete) && !delete.isEmpty()) {
            for (int i = 0; i < delete.length(); i++) {
                result.add(delete.getLong(i));
            }
        }
        return result;
    }

    private JSONObject buildFailedItem(final ServiceMessage errorMessage) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("id", errorMessage.getKey());
        JSONArray errors = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("field", errorMessage.getErrCode());
        item.put("message", errorMessage.getDescription());
        errors.put(item);
        result.put("errors", errors);
        return result;
    }

    private JSONObject buildFailedItem(final TextStyleJsonVO vo, final Set<ConstraintViolation<TextStyleJsonVO>> violations) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("id", vo.getId());
        result.put("name", vo.getName());
        JSONArray errors = new JSONArray();
        for (ConstraintViolation<TextStyleJsonVO> violation : violations) {
            JSONObject item = new JSONObject();
            item.put("field", violation.getPropertyPath().toString());
            item.put("message", violation.getMessage());
            errors.put(item);
        }
        result.put("errors", errors);
        return result;
    }

    private IPage getPage(final int pageIndex, final int pageSize, final String sSearch, final String sortKey, String sortDirection) {
        List<MessagepointCriterion> firstLevelCriterionList = getCriteria(sSearch);
        ServiceExecutionContext context = HibernatePaginationService.createContext(TextStyle.class, firstLevelCriterionList, emptyList(), emptyMap(), emptyMap(), emptyMap(), pageIndex, pageSize, getOrderByList(sortKey, sortDirection));
        Service paginationService = MessagepointServiceFactory.getInstance().lookupService(HibernatePaginationService.SERVICE_NAME, HibernatePaginationService.class);
        paginationService.execute(context);
        PaginationServiceResponse serviceResponse = (PaginationServiceResponse) context.getResponse();

        IPage page = serviceResponse.getPage();

        // If filter is applied and new result set has less pages than the current index then go to the last page
        if (!StringUtils.isEmpty(sSearch) && page.getList().isEmpty() && page.getRowCount() > 0 && pageIndex > 0) {
            // There is no Math.ceilDiv, so negate the dividend of floorDiv and then negate the result to get to equivalent of what Math.ceilDiv would do
            int nextIndex = -Math.floorDiv(-page.getRowCount(), page.getPageSize() != 0 ? page.getPageSize() : 1);
            page = getPage(nextIndex, pageSize, sSearch, sortKey, sortDirection);
        }

        return page;
    }

    private List<MessagepointOrder> getOrderByList(String sortKey, String sortDirection) {
        if (sortKey == null || sortKey.isEmpty()) {
            return new ArrayList<>();
        }

        ArrayList<MessagepointOrder> result = new ArrayList<>();
        result.add(sortDirection.equals("asc") ? MessagepointOrder.asc(HibernateObjectManager.MODEL_ALIAS_PREFIX + "." + sortKey) : MessagepointOrder.desc(HibernateObjectManager.MODEL_ALIAS_PREFIX + "." + sortKey));

        return result;
    }

    private List<MessagepointCriterion> getCriteria(final String sSearch) {
        List<MessagepointCriterion> criterionList;
        if (nonNull(sSearch) && !sSearch.isEmpty()) {
            criterionList = new ArrayList<>();
            String searchValue = "%" + sSearch + "%";
            criterionList.add(MessagepointRestrictions.or(
                    MessagepointRestrictions.ilike("name", searchValue),
                    MessagepointRestrictions.ilike("fontName", searchValue),
                    MessagepointRestrictions.ilike("connectorName", searchValue)
            ));
        } else {
            criterionList = emptyList();
        }
        return criterionList;
    }

    private List<TextStyle> getTextStyles(final List<?> list) {
        return list.stream().filter(o -> o instanceof TextStyle).map(o -> (TextStyle) o).collect(Collectors.toList());
    }

    private static Validator getValidator() {
        ReloadableResourceBundleMessageSource resources = (ReloadableResourceBundleMessageSource) getBean("messageSource");
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .messageInterpolator(
                        new ResourceBundleMessageInterpolator(
                                new MessageSourceResourceBundleLocator(resources)
                        )
                )
                .buildValidatorFactory();
        return validatorFactory.getValidator();
    }
}
