package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.backgroundtask.SyncTouchpointMessagePriorityBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.ExportSyncDataBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.GetSyncListBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncStarterBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncStarterBackgroundTaskStatus;
import com.prinova.messagepoint.platform.services.backgroundtask.sync.utils.SyncListUtil;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class AsyncTouchpointSyncListController {

    private static final Log log = LogUtil.getLog(AsyncTouchpointSyncListController.class);

    private static final String PARAM_DOCUMENT_ID = "documentId";
    public static final Integer SYNC_DIRECTION_UPDATE = 1;
    public static final Integer SYNC_DIRECTION_COMMIT = 2;
    private static final String PARAM_OBJECT_ID = "objectId";
    private static final String PARAM_LANGUAGES = "languages";
    private static final String PARAM_FORCE_SYNC = "forceSync";
    private static final String PARAM_ACTIVE_ONLY = "activeOnly";
    private static final String PARAM_SYNC_ACTIVE_ONLY = "syncActiveOnly";
    private static final String PARAM_SYNC_MESSAGE_PRIORITY = "syncMessagePriority";
    private static final String PARAM_INCLUDE_DIFFERENCES_IN_LOG = "includeDifferencesInLog";
    private static final String PARAM_INCLUDE_NOT_VISIBLE_TARGET_RULES = "includeNotVisibleTargetRules";
    private static final String PARAM_INCLUDE_REJECTED_CHANGES = "includeRejectedChanges";
    private static final String PARAM_NEW_WC_AFTER_AC = "newWorkingAfterActive";
    private static final String PARAM_MAINTAIN_TARGET_RELATIVE_PRIORITY = "maintainTargetRelativePriority";

    private static final String PARAM_SOURCE_DOCUMENT_ID = "sourceDocumentId";
    private static final String PARAM_SOURCE_INSTANCE_ID = "sourceInstanceId";

    private static final String PARAM_SOURCE_OBJECT_ID = "sourceObjectId";
    private static final String PARAM_TARGET_DOCUMENT_ID = "targetDocumentId";
    private static final String PARAM_TARGET_INSTANCE_ID = "targetInstanceId";

    private static final String PARAM_TARGET_OBJECT_ID = "targetObjectId";
    private static final String PARAM_LOCALE_ID = "mpLocaleID";
    private static final String PARAM_PAGE_INDEX = "pageIndex";
    private static final String PARAM_PAGE_SIZE = "pageSize";
    private static final String PARAM_GUID_SYNC = "guidSync";
    private static final String PARAM_INCLUDE_RAW_CONTENTS = "includeRawContents";
    private static final String PARAM_TASK_GUID = "taskGuid";
    private static final String PARAM_LIST_KEY = "listKey";
    private static final String PARAM_ACCEPT_ALL = "acceptAll";
    private static final String PARAM_OBJECT_IDS = "objectIds";

    private static final String PARAM_REJECT_IDS = "rejectIds";
    private static final String PARAM_TASK_ID = "taskId";
    private static final String PARAM_INSTANCE_ID = "instanceId";
    private static final String PARAM_OBJECT_TYPE = "objectType";

    private static final String PARAM_DATA_TYPE = "dataType"; // 0 = N/A, 1 = Working copy, 2 = Active copy, 4 = Archived copy
    private static final String PARAM_SORT_SORT_KEY = "sortKey";
    private static final String PARAM_SORT_DIRECTION = "sortDirection";

    private static final String PARAM_FILTER_TEXT = "filterText";
    private static final String PARAM_FILTER_STATUS_TYPE = "statusType";
    private static final String PARAM_FILTER_OBJECT_TYPE = "objectType";
    private static final String PARAM_FILTER_PROJECT_NAME = "projectName";

    private static final String PARAM_HIDE_UNTIL_NEXT_CHANGES = "hideUntilNextChanges";
    private static final String PARAM_NOT_SYNC_LAYOUT_CHANGES = "notSyncLayoutChanges";

    private static final Long DEFAULT_MIN_PAGE_SIZE = 10L;
    private static final Long DEFAULT_MAX_PAGE_SIZE = 50L;

    private static final String SYNC_DATA_CACHE_KEY = "sync2.0:";
    private static final String SYNC_FILTERED_DATA_CACHE_KEY = "sync2.0-filtered:";

    @RequestMapping(value = "getDocumentSyncData.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getDocumentSyncData(@RequestParam(name = PARAM_DOCUMENT_ID) String documentIdParamString) throws JSONException, IOException {

        JSONObject result = new JSONObject();

        Long documentId = Long.parseLong(documentIdParamString);
        Document document = Document.findById(documentId);

        List<MessagepointLocale> sourceDocumentLanguages = new ArrayList<>();
        List<MessagepointLocale> targetDocumentLanguages = new ArrayList<>();

        // When update
        // The current document is the target document
        targetDocumentLanguages = document.getSystemDefaultAndTouchpointLanguagesAsLocales();//getTouchpointLanguagesAsLocales();
        // targetTouchpointTreeData = null;

        // Source document will be selected from source touchpoint tree
        JSONObject sourceTouchpointTreeData = getTouchpointTree(document, SYNC_DIRECTION_UPDATE);
        JSONObject update = new JSONObject();

        update.put("targetLanguages", getLanguagesJSONArray(targetDocumentLanguages));
        update.put("data", sourceTouchpointTreeData);

        // When commit
        // The current document is the source document
        sourceDocumentLanguages = document.getSystemDefaultAndTouchpointLanguagesAsLocales(); //getTouchpointLanguagesAsLocales();
        // sourceTouchpointTreeData = null;

        // Target document will be selected from target touchpoint tree
        // The tree will contains only document in the current instance
        JSONObject targetTouchpointTreeData = getTouchpointTree(document, SYNC_DIRECTION_COMMIT);
        JSONObject commit = new JSONObject();

        commit.put("sourceLanguages", getLanguagesJSONArray(sourceDocumentLanguages));
        commit.put("data", targetTouchpointTreeData);

        result.put("syncContent", true);
        result.put("currentBranchName", Node.getCurrentBranchName());
        result.put("currentInstanceName", Node.getCurrentNodeName());
        result.put("currentDocumentId", documentId);
        result.put("currentDocumentName", document.getName());
        result.put("currentInstanceId", Node.getCurrentNode().getId());
        result.put("currentAcceptOnlyActiveObjects", document.isAcceptOnlyActiveObjects());
        result.put("update", update);
        result.put("commit", commit);

        return result.toString();
    }

    private JSONObject getTouchpointTree(Document document, Integer direction) {
        Node currentNode = Node.getCurrentNode();
        long currentNodeId = currentNode.getId();
        String currentNodeGuid = currentNode.getGuid();
        Branch currentBranch = Node.getCurrentBranch();
        Long parentBranchId = currentBranch.getParentBranch() == null ? null : currentBranch.getParentBranch().getId();
        Branch parentBranch = parentBranchId == null ? null : Branch.findById(parentBranchId);
        Node dcsNode = currentBranch.getDcsNode();
        String dcsNodeGuid = dcsNode.getGuid();
        Document originDocument = (Document) document.getOriginObject();
        Long originDocumentId = originDocument == null ? null : originDocument.getId();
        boolean targetOfSync = document.isTargetOfSync();
        boolean isSubscribed = false; // Is the current document subscribed from exchange ?
        boolean canSyncUpdateFromParent = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromParent();
        boolean canSyncUpdateFromChild = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromChild();
        String documentExchangeTouchpointGiud = document.getExchangeTouchpointGuid();
        String documentExchangeInstanceGuid = document.getExchangeInstanceGuid();
        User currentUser = UserUtil.getPrincipalUser();

        List<Document> allFamilyDocsInCurrentNode = filterEnabledDocument(Document.findFamilyDocuments(document));

        Map<Node, List<Document>> nodeDocumentsMap = new LinkedHashMap<>();

        if(SYNC_DIRECTION_UPDATE.equals(direction)) {
            if(targetOfSync) {
                nodeDocumentsMap.put(currentNode, allFamilyDocsInCurrentNode);
            }
        } else if(SYNC_DIRECTION_COMMIT.equals(direction)) {
            nodeDocumentsMap.put(currentNode, allFamilyDocsInCurrentNode.stream().filter(d->d.isTargetOfSync()).collect(Collectors.toList()));
        }

        Map<Node, Map<Long, JSONArray>> nodeDocumentLanguages = new HashMap<>(); // For update

        if (SYNC_DIRECTION_UPDATE.equals(direction)) {
            if(targetOfSync) {
                if (!currentNode.isExchangeNode()) {
                    if (document.getExchangeTouchpointGuid() != null || document.getExchangeInstanceGuid() != null
                        || (document.getExchangeInstanceGuid() != null && !document.getExchangeInstanceGuid().equals(currentNode.getGuid())))
                        isSubscribed = true;
                }

                List<Branch> accessibleExchangeDomains = currentBranch.getExchangeDomains();
                List<Node> nodesInCurrentBranch = currentBranch.getAllAccessibleNodesForTES(false, true);
                List<Branch> childBranches = canSyncUpdateFromChild ? currentBranch.getAllEnabledChildren() : null;
                List<Node> nodes = new ArrayList<>(nodesInCurrentBranch); // All nodes in current branch are be accessible
                if (canSyncUpdateFromParent && parentBranch != null) {
                    List<Node> nodesInParentBranch = parentBranch.getAllAccessibleNodes(false)
                        .stream()
                        .filter(n -> n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isSandboxNode() || n.isTestingNode()))
                        .collect(Collectors.toList());
                    nodes.addAll(nodesInParentBranch);
                }

                if (canSyncUpdateFromChild && childBranches != null) {
                    for(Branch childBranch : childBranches) {
                        List<Node> nodesInChildBranch = childBranch.getAllAccessibleNodes(false)
                            .stream()
                            .filter(n -> n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isSandboxNode() || n.isTestingNode()))
                            .collect(Collectors.toList());
                        nodes.addAll(nodesInChildBranch);
                    }
                }

                Collections.reverse(accessibleExchangeDomains);
                // Looking for exchange nodes in higher levels
                for (Branch exchangeDomainBranch : accessibleExchangeDomains) {
                    if (!exchangeDomainBranch.isEnabled()) continue;
                    if (!exchangeDomainBranch.isOnline()) continue;
                    if (exchangeDomainBranch.getId() == currentBranch.getId())
                        continue; // Nodes in current branch are already handled
                    List<Node> nodesInAncestorExchangeDomainBranch = exchangeDomainBranch.getAllAccessibleNodesForTES(false, true);
                    for (Node node : nodesInAncestorExchangeDomainBranch) {
                        if (node.isExchangeNode()) {
                            nodes.add(node);
                        }
                    }
                }

                {
                    nodeDocumentLanguages.put(currentNode,
                        allFamilyDocsInCurrentNode.stream().collect(Collectors.toMap(Document::getId, doc -> {
                            List<MessagepointLocale> documentMessagepointLocales = doc.getTouchpointLanguagesAsLocales();
                            return getLanguagesJSONArray(documentMessagepointLocales);
                        })));
                }

                for (Node node : nodes) {
                    if (!node.isEnabled()) continue;
                    if (!node.isOnline()) continue;
                    if (node.getId() == currentNode.getId()) continue;

                    User nodeUser = currentUser.getNodeUser(node);
                    boolean nodeUserIsInvalid = (nodeUser == null || (!nodeUser.isAccountActive()) || (!nodeUser.isAccountEnabled()) || (!nodeUser.isAccountNonExpired()) || (!nodeUser.isAccountNonLocked()));
                    if(! node.isExchangeNode()) {
                        if (nodeUserIsInvalid) {
                            continue;
                        }
                    }

                    Branch nodeBranch = node.getBranch();
                    List<Document> allFamilyDocs = CloneHelper.queryInSchema(node.getSchemaName(), () -> filterEnabledDocument(Document.findFamilyDocuments(document)));
                    if (node.isExchangeNode() /* || nodeBranch.getId() != currentBranch.getId() */) {
                        List<Document> allFamilyDocsFiltered = new ArrayList<>();
                        Map<Long, JSONArray> enabledLanguages = new HashMap<>();
                        for (Document doc : allFamilyDocs) {
                            TPInstanceVisibilityMap domainTPInstanceVisibilityMap = CloneHelper.queryInSchema(node.getSchemaName(), () -> TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), dcsNodeGuid));
                            TPInstanceVisibilityMap nodeTPInstanceVisibilityMap = domainTPInstanceVisibilityMap != null ? null : CloneHelper.queryInSchema(node.getSchemaName(), () -> TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), currentNodeGuid));
                            if (domainTPInstanceVisibilityMap != null || nodeTPInstanceVisibilityMap != null) {
                                if (document != null && doc != null && (nodeBranch.getId() == currentBranch.getId() || (document.getExchangeTouchpointGuid() != null && document.getExchangeTouchpointGuid().equals(doc.getGuid())))) {
                                    allFamilyDocsFiltered.add(doc);
                                    enabledLanguages.put(doc.getId(), CloneHelper.queryInSchema(node.getSchemaName(),
                                        () -> {
                                            Set<MessagepointLocale> enabledMessagepointLocales = domainTPInstanceVisibilityMap != null ?
                                                domainTPInstanceVisibilityMap.getMessagepointLocales() :
                                                nodeTPInstanceVisibilityMap.getMessagepointLocales();
                                            Set<Long> enabledMessagepointLocaleIDs = enabledMessagepointLocales.stream().map(MessagepointLocale::getId).collect(Collectors.toSet());
                                            List<MessagepointLocale> documentMessagepointLocales = doc.getTouchpointLanguagesAsLocales()
                                                .stream()
                                                .sequential()
                                                .filter(ml -> enabledMessagepointLocaleIDs.contains(ml.getId()))
                                                .collect(Collectors.toList());
                                            return getLanguagesJSONArray(documentMessagepointLocales);
                                        }));
                                }
                            }
                        }

                        if (allFamilyDocsFiltered != null && !allFamilyDocsFiltered.isEmpty()) {
                            nodeDocumentsMap.put(node, allFamilyDocsFiltered);
                        }

                        nodeDocumentLanguages.put(node, enabledLanguages);
                    }

                    if(! nodeUserIsInvalid) {
                        if (nodeBranch.getId() == currentBranch.getId()) {
                            if (allFamilyDocs != null && !allFamilyDocs.isEmpty()) {
                                nodeDocumentsMap.put(node, allFamilyDocs);
                                nodeDocumentLanguages.put(node,
                                    CloneHelper.queryInSchema(node.getSchemaName(),
                                        () -> allFamilyDocs.stream().collect(Collectors.toMap(
                                            Document::getId,
                                            doc -> {
                                                List<MessagepointLocale> documentMessagepointLocales = doc.getTouchpointLanguagesAsLocales();
                                                return getLanguagesJSONArray(documentMessagepointLocales);
                                            },
                                            (p1, p2) -> {
                                                return p1;
                                            }
                                        )))
                                );
                            }
                        } else if (canSyncUpdateFromParent && parentBranch != null && nodeBranch.getId() == parentBranchId) {
                            if (allFamilyDocs != null && !allFamilyDocs.isEmpty()) {
                                nodeDocumentsMap.put(node, allFamilyDocs);
                                nodeDocumentLanguages.put(node,
                                    CloneHelper.queryInSchema(node.getSchemaName(),
                                        () -> allFamilyDocs.stream().collect(Collectors.toMap(
                                            Document::getId,
                                            doc -> {
                                                List<MessagepointLocale> documentMessagepointLocales = doc.getTouchpointLanguagesAsLocales();
                                                return getLanguagesJSONArray(documentMessagepointLocales);
                                            },
                                            (p1, p2) -> {
                                                return p1;
                                            }
                                        )))
                                );
                            }
                        } else if(canSyncUpdateFromChild && nodeBranch.getParentBranch() != null && nodeBranch.getParentBranch().getId() == currentBranch.getId()) {
                            nodeDocumentsMap.put(node, allFamilyDocs);
                            nodeDocumentLanguages.put(node,
                                CloneHelper.queryInSchema(node.getSchemaName(),
                                    () -> allFamilyDocs.stream().collect(Collectors.toMap(
                                        Document::getId,
                                        doc -> {
                                            List<MessagepointLocale> documentMessagepointLocales = doc.getTouchpointLanguagesAsLocales();
                                            return getLanguagesJSONArray(documentMessagepointLocales);
                                        },
                                        (p1, p2) -> {
                                            return p1;
                                        }
                                    )))
                            );
                        }
                    }
                }
            }
        } else if (SYNC_DIRECTION_COMMIT.equals(direction)) {
            List<MessagepointLocale> documentMessagepointLocales = document.getTouchpointLanguagesAsLocales();

            nodeDocumentLanguages.put(currentNode,
                    allFamilyDocsInCurrentNode.stream().collect(Collectors.toMap(Document::getId, doc -> {
                        return getLanguagesJSONArray(documentMessagepointLocales);
                    })));

            List<Node> nodes = currentBranch.getAllAccessibleNodesForTES(false, true);
            for (Node node : nodes) {
                if (!node.isEnabled()) continue;
                if (!node.isOnline()) continue;
                if (node.getId() == currentNode.getId()) continue;
                User nodeUser = currentUser.getNodeUser(node);
                if (nodeUser == null || (!nodeUser.isAccountActive()) || (!nodeUser.isAccountEnabled()) || (!nodeUser.isAccountNonExpired()) || (!nodeUser.isAccountNonLocked()))
                    continue;
                List<Document> allFamilyDocs = CloneHelper.queryInSchema(node.getSchemaName(), () -> filterEnabledDocument(Document.findFamilyDocuments(document)).stream().filter(d->d.isTargetOfSync()).collect(Collectors.toList()));
                nodeDocumentsMap.put(node, allFamilyDocs);
                nodeDocumentLanguages.put(node,
                        allFamilyDocs.stream().collect(Collectors.toMap(Document::getId, doc -> {
                            return getLanguagesJSONArray(documentMessagepointLocales);
                        })));
            }
        }

        boolean isCurrentTPSubscribed = isSubscribed;

        JSONObject tree = new JSONObject();

        class Selected {
            String selectedBranchName = null;
            Long selectedInstanceId = null;
            String selectedInstanceName = null;
            Long selectedSiblingId = null;
            String selectedSiblingName = null;
            Boolean selectedAcceptOnlyActiveObjects = false;
        }

        final Selected selected = new Selected();
        final Selected firstInSameNode = new Selected();
        final Selected first = new Selected();

        final boolean isCurrentDocumentEnabledForVariantWorkflow = document.isEnabledForVariantWorkflow();

        {
            JSONObject currentDocumentTree = new JSONObject();
            MessagepointLocale systemDefaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();

            currentDocumentTree.put("id", "Node_" + currentNode.getId() + "_Document_" + document.getId());
            currentDocumentTree.put("name", document.getName());
            currentDocumentTree.put("instanceName", currentNode.getName());
            currentDocumentTree.put("branchName", currentNode.getBranch().getName());
            currentDocumentTree.put("enabled", false);
            currentDocumentTree.put("enabledForVariantWorkflow", document.isEnabledForVariantWorkflow());
            currentDocumentTree.put("acceptOnlyActiveObjects", document.isAcceptOnlyActiveObjects());

            currentDocumentTree.put("systemDefaultLocale", systemDefaultLocale.getCode());

            if (originDocument != null) {
                currentDocumentTree.put("parent", "Node_" + currentNode.getId() + "_Document_" + originDocument.getId());
            }

            JSONArray messagepointLocales = getLanguagesJSONArray(document.getTouchpointLanguagesAsLocales());
            addSystemDefaultLocale(messagepointLocales, systemDefaultLocale);

            currentDocumentTree.put("languages", messagepointLocales);
            tree.put("Node_" + currentNode.getId() + "_Document_" + document.getId(), currentDocumentTree);
        }

        for (Node node : nodeDocumentsMap.keySet()) {
            List<Document> documentsOnNode = nodeDocumentsMap.get(node);
            if (documentsOnNode == null) continue;
            String nodeSchema = node.getSchemaName();
            Map<Long, JSONArray> documentMessagepointLocalesMap = nodeDocumentLanguages.get(node);

            CloneHelper.execInSchema(nodeSchema, () -> {
                MessagepointLocale systemDefaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();

                for (Document nodeDocument : documentsOnNode) {
                    boolean isNodeDocumentEnabledForVariantWorkflow = nodeDocument.isEnabledForVariantWorkflow();

                    JSONObject nodeDocumentTree = new JSONObject();
                    JSONArray messagepointLocales = documentMessagepointLocalesMap == null ? null : documentMessagepointLocalesMap.get(nodeDocument.getId());
                    if (messagepointLocales == null) messagepointLocales = new JSONArray();
                    addSystemDefaultLocale(messagepointLocales, systemDefaultLocale);

                    nodeDocumentTree.put("id", "Node_" + node.getId() + "_Document_" + nodeDocument.getId());
                    nodeDocumentTree.put("name", nodeDocument.getName());
                    nodeDocumentTree.put("instanceName", node.getName());
                    nodeDocumentTree.put("branchName", node.getBranch().getName());
                    nodeDocumentTree.put("systemDefaultLocale", systemDefaultLocale.getCode());
                    nodeDocumentTree.put("enabledForVariantWorkflow", nodeDocument.isEnabledForVariantWorkflow());
                    nodeDocumentTree.put("acceptOnlyActiveObjects", nodeDocument.isAcceptOnlyActiveObjects());

                    Selected possibleDefault = new Selected();

                    possibleDefault.selectedBranchName = node.getBranch().getName();
                    possibleDefault.selectedSiblingId = nodeDocument.getId();
                    possibleDefault.selectedSiblingName = nodeDocument.getName();
                    possibleDefault.selectedInstanceId = node.getId();
                    possibleDefault.selectedInstanceName = node.getName();
                    possibleDefault.selectedAcceptOnlyActiveObjects = nodeDocument.isAcceptOnlyActiveObjects();

                    if(isCurrentDocumentEnabledForVariantWorkflow == isNodeDocumentEnabledForVariantWorkflow) {
                        if (!isCurrentTPSubscribed) {
                            if (node.getId() == currentNodeId && originDocumentId != null && nodeDocument.getId() == originDocumentId) {
                                selected.selectedBranchName = node.getBranch().getName();
                                selected.selectedSiblingId = nodeDocument.getId();
                                selected.selectedSiblingName = nodeDocument.getName();
                                selected.selectedInstanceId = node.getId();
                                selected.selectedInstanceName = node.getName();
                                possibleDefault.selectedAcceptOnlyActiveObjects = nodeDocument.isAcceptOnlyActiveObjects();
                            } else {
                                if (node.getId() == currentNodeId) {
                                    if (firstInSameNode.selectedInstanceId == null) {
                                        firstInSameNode.selectedBranchName = node.getBranch().getName();
                                        firstInSameNode.selectedSiblingId = possibleDefault.selectedSiblingId;
                                        firstInSameNode.selectedSiblingName = possibleDefault.selectedSiblingName;
                                        firstInSameNode.selectedInstanceId = possibleDefault.selectedInstanceId;
                                        firstInSameNode.selectedInstanceName = possibleDefault.selectedInstanceName;
                                        firstInSameNode.selectedAcceptOnlyActiveObjects = possibleDefault.selectedAcceptOnlyActiveObjects;
                                    }
                                }
                                if (first.selectedSiblingId == null) {
                                    first.selectedBranchName = node.getBranch().getName();
                                    first.selectedSiblingId = possibleDefault.selectedSiblingId;
                                    first.selectedSiblingName = possibleDefault.selectedSiblingName;
                                    first.selectedInstanceId = possibleDefault.selectedInstanceId;
                                    first.selectedInstanceName = possibleDefault.selectedInstanceName;
                                    first.selectedAcceptOnlyActiveObjects = possibleDefault.selectedAcceptOnlyActiveObjects;
                                }
                            }
                        } else {
                            if (node.getGuid().equals(documentExchangeInstanceGuid) && nodeDocument.getGuid().equals(documentExchangeTouchpointGiud)) {
                                selected.selectedBranchName = node.getBranch().getName();
                                selected.selectedSiblingId = nodeDocument.getId();
                                selected.selectedSiblingName = nodeDocument.getName();
                                selected.selectedInstanceId = node.getId();
                                selected.selectedInstanceName = node.getName();
                                selected.selectedAcceptOnlyActiveObjects = nodeDocument.isAcceptOnlyActiveObjects();
                            } else {
                                if (node.getId() == currentNodeId) {
                                    if (firstInSameNode.selectedInstanceId == null) {
                                        firstInSameNode.selectedBranchName = node.getBranch().getName();
                                        firstInSameNode.selectedSiblingId = possibleDefault.selectedSiblingId;
                                        firstInSameNode.selectedSiblingName = possibleDefault.selectedSiblingName;
                                        firstInSameNode.selectedInstanceId = possibleDefault.selectedInstanceId;
                                        firstInSameNode.selectedInstanceName = possibleDefault.selectedInstanceName;
                                        firstInSameNode.selectedAcceptOnlyActiveObjects = possibleDefault.selectedAcceptOnlyActiveObjects;
                                    }
                                }

                                if (first.selectedSiblingId == null) {
                                    first.selectedBranchName = node.getBranch().getName();
                                    first.selectedSiblingId = possibleDefault.selectedSiblingId;
                                    first.selectedSiblingName = possibleDefault.selectedSiblingName;
                                    first.selectedInstanceId = possibleDefault.selectedInstanceId;
                                    first.selectedInstanceName = possibleDefault.selectedInstanceName;
                                    first.selectedAcceptOnlyActiveObjects = possibleDefault.selectedAcceptOnlyActiveObjects;
                                }
                            }
                        }
                    }

                    nodeDocumentTree.put("enabled", true);

                    Document originNodeDocument = (Document) nodeDocument.getOriginObject();
                    if (originNodeDocument != null) {
                        nodeDocumentTree.put("parent", "Node_" + node.getId() + "_Document_" + originNodeDocument.getId());
                    }

                    nodeDocumentTree.put("languages", messagepointLocales);

                    tree.put("Node_" + node.getId() + "_Document_" + nodeDocument.getId(), nodeDocumentTree);
                }
            });
        }

        if (selected.selectedSiblingId == null) {
            if (firstInSameNode.selectedSiblingId != null) {
                selected.selectedBranchName = firstInSameNode.selectedBranchName;
                selected.selectedSiblingId = firstInSameNode.selectedSiblingId;
                selected.selectedSiblingName = firstInSameNode.selectedSiblingName;
                selected.selectedInstanceId = firstInSameNode.selectedInstanceId;
                selected.selectedInstanceName = firstInSameNode.selectedInstanceName;
                selected.selectedAcceptOnlyActiveObjects = firstInSameNode.selectedAcceptOnlyActiveObjects;
            }

            if (first.selectedSiblingId != null) {
                selected.selectedBranchName = first.selectedBranchName;
                selected.selectedSiblingId = first.selectedSiblingId;
                selected.selectedSiblingName = first.selectedSiblingName;
                selected.selectedInstanceId = first.selectedInstanceId;
                selected.selectedInstanceName = first.selectedInstanceName;
                selected.selectedAcceptOnlyActiveObjects = first.selectedAcceptOnlyActiveObjects;
            }
        }

        JSONObject result = new JSONObject();

        result.put("tree", tree);
        result.put("selectedBranchName", selected.selectedBranchName);
        result.put("selectedInstanceId", selected.selectedInstanceId); //SelectedInstanceId
        result.put("selectedInstanceName", selected.selectedInstanceName);
        result.put("selectedSiblingId", selected.selectedSiblingId); //SelectedDocumentId
        result.put("selectedSiblingName", selected.selectedSiblingName); //
        result.put("selectedAcceptOnlyActiveObjects", selected.selectedAcceptOnlyActiveObjects); //

        result.put("canSync", selected.selectedSiblingId != null);

        return result;
    }

    private void addSystemDefaultLocale(JSONArray messagepointLocales, MessagepointLocale systemDefaultLocale){
        // Add system default locale if it is not in the list
        if(messagepointLocales.toList().stream().map(o -> ((HashMap<?, ?>) o).get("isSystemDefaultLocale")).noneMatch(Boolean.TRUE::equals)){
            messagepointLocales.put(getLanguageJSONObject(systemDefaultLocale, systemDefaultLocale));
        }
    }

    protected List<Document> filterEnabledDocument(List<Document> documents) {
        return documents.stream().sequential().filter(this::isDocumentEnabled).collect(Collectors.toList());
    }

    protected boolean isDocumentEnabled(Document document) {
        if (document.isRemoved()) return false;
        if (!document.isEnabled()) return false;
        if (document.getOriginObject() == null) {
            return true;
        }
        Document originDocument = (Document) document.getOriginObject();
        return isDocumentEnabled(originDocument);
    }

    protected JSONArray getLanguagesJSONArray(List<MessagepointLocale> locales) {

        JSONArray languages = new JSONArray();
        MessagepointLocale systemDefaultLocale = MessagepointLocale.getDefaultSystemLanguageLocale();

        if (locales.isEmpty())
            return languages;

        for (MessagepointLocale locale : locales) {
            languages.put(getLanguageJSONObject(locale, systemDefaultLocale));
        }

        return languages;

    }

    private JSONObject getLanguageJSONObject(MessagepointLocale locale, MessagepointLocale systemDefaultLocale){
        JSONObject language = new JSONObject();

        language.put("id", locale.getId());
        language.put("name", locale.getName());
        language.put("displayName", ApplicationUtil.getMessage(locale.getDisplayCode()));
        language.put("code", locale.getCode());
        language.put("isFavourite", locale.isFavourite());
        language.put("isDefaultLocale", locale.isDefaultLocale());
        language.put("isSystemDefaultLocale", locale.getCode().equals(systemDefaultLocale.getCode()));
        language.put("languageCode", locale.getLanguageCode());

        return language;
    }

    @RequestMapping(value = "startGetSyncList.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String startGetSyncList(@RequestParam(name = PARAM_SOURCE_DOCUMENT_ID) Long sourceDocumentId,
                                   @RequestParam(name = PARAM_SOURCE_INSTANCE_ID) Long sourceInstanceId,
                                   @RequestParam(name = PARAM_TARGET_DOCUMENT_ID) Long targetDocumentId,
                                   @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId,
                                   @RequestParam(name = PARAM_LANGUAGES) List<Long> languages,           // Messagepoint locale Ids selected to sync.
                                   @RequestParam(name = PARAM_FORCE_SYNC) Boolean forceSync,
                                   @RequestParam(name = PARAM_ACTIVE_ONLY) Boolean activeOnly,
                                   @RequestParam(name = PARAM_SYNC_ACTIVE_ONLY) Boolean syncActiveOnly,
                                   @RequestParam(name = PARAM_INCLUDE_DIFFERENCES_IN_LOG) Boolean includeDifferencesInLog,
                                   @RequestParam(name = PARAM_INCLUDE_NOT_VISIBLE_TARGET_RULES) Boolean includeNotVisibleTargetRules,
                                   @RequestParam(name = PARAM_INCLUDE_REJECTED_CHANGES) Boolean includeRejectedChanges,
                                   @RequestParam(name = PARAM_PAGE_INDEX) Long pageIndex,
                                   @RequestParam(name = PARAM_PAGE_SIZE) Long pageSize,
                                   @RequestParam(name = PARAM_SORT_SORT_KEY) String sortKey,
                                   @RequestParam(name = PARAM_SORT_DIRECTION) String sortDirection,
                                   @RequestParam(name = PARAM_FILTER_TEXT, required = false) String filterText,
                                   @RequestParam(name = PARAM_FILTER_STATUS_TYPE, required = false) String filterStatusType,
                                   @RequestParam(name = PARAM_FILTER_OBJECT_TYPE, required = false) String filterObjectType,
                                   @RequestParam(name = PARAM_FILTER_PROJECT_NAME, required = false) String filterProjectName,
                                   @RequestParam(name = PARAM_GUID_SYNC) String guid,
                                   HttpServletRequest request,
                                   HttpServletResponse response
                                   ) throws JSONException, IOException {
        boolean syncVariantEnabled = SyncTouchpointUtil.checkSyncVariantEnabledFeatureFlag(request, sourceInstanceId, sourceDocumentId);

        JSONObject data = startGenerateSyncData(
                sourceDocumentId,
                sourceInstanceId,
                targetDocumentId,
                targetInstanceId,
                languages,
                activeOnly,
                syncActiveOnly,
                includeNotVisibleTargetRules,
                includeRejectedChanges,
                syncVariantEnabled
            );
        return data.toString();
    }

    private JSONObject startGenerateSyncData(Long sourceDocumentId,
                                             Long sourceInstanceId,
                                             Long targetDocumentId,
                                             Long targetInstanceId,
                                             List<Long> languages,
                                             Boolean activeOnly,
                                             Boolean syncActiveOnly,
                                             Boolean includeNotVisibleTargetRules,
                                             Boolean includeRejectedChanges,
                                             boolean syncVariantEnabled) {
        GetSyncListBackgroundTask task = new GetSyncListBackgroundTask(
                sourceDocumentId,
                sourceInstanceId,
                targetDocumentId,
                targetInstanceId,
                languages,
                activeOnly,
                syncActiveOnly,
                UserUtil.getPrincipalUser(),
                includeNotVisibleTargetRules,
                includeRejectedChanges,
                syncVariantEnabled
            );
        JSONObject status = task.getStatus();
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
        return status;
    }

    @RequestMapping(value = "getSyncListStatus.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSyncListStatus(@RequestParam(name = PARAM_LIST_KEY) String listKey) throws JSONException, IOException {
        JSONObject status = GetSyncListBackgroundTask.getStatus(listKey);
        if(status == null) return null;
        return status.toString();
    }

    private List<String> getIdsFromRequestBody(String body, String name) {
        List<String> objectIds = null;

        if (body != null) {
            JSONObject request = new JSONObject(body);
            if(request.has(name)){
                JSONArray data = request.getJSONArray(name);
                objectIds = new ArrayList<>();
                for (int i = 0; i < data.length(); i++) {
                    objectIds.add(data.getString(i));
                }
            }
        }

        return objectIds;
    }

    @RequestMapping(value = "getSyncList.form", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSyncList(@RequestParam(name = PARAM_SOURCE_DOCUMENT_ID) Long sourceDocumentId,
                              @RequestParam(name = PARAM_SOURCE_INSTANCE_ID) Long sourceInstanceId,
                              @RequestParam(name = PARAM_TARGET_DOCUMENT_ID) Long targetDocumentId,
                              @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId,
                              @RequestParam(name = PARAM_LANGUAGES) List<Long> languages,           // Messagepoint locale Ids selected to sync.
                              @RequestParam(name = PARAM_FORCE_SYNC) Boolean forceSync,
                              @RequestParam(name = PARAM_ACTIVE_ONLY) Boolean activeOnly,
                              @RequestParam(name = PARAM_SYNC_ACTIVE_ONLY) Boolean syncActiveOnly,
                              @RequestParam(name = PARAM_ACCEPT_ALL, required = false) Boolean acceptAll,
                              @RequestParam(name = PARAM_INCLUDE_DIFFERENCES_IN_LOG) Boolean includeDifferencesInLog,
                              @RequestParam(name = PARAM_INCLUDE_NOT_VISIBLE_TARGET_RULES) Boolean includeNotVisibleTargetRules,
                              @RequestParam(name = PARAM_INCLUDE_REJECTED_CHANGES) Boolean includeRejectedChanges,
                              @RequestParam(name = PARAM_PAGE_INDEX) Long pageIndex,
                              @RequestParam(name = PARAM_PAGE_SIZE) Long pageSize,
                              @RequestParam(name = PARAM_SORT_SORT_KEY) String sortKey,
                              @RequestParam(name = PARAM_SORT_DIRECTION) String sortDirection,
                              @RequestParam(name = PARAM_FILTER_TEXT, required = false) String filterText,
                              @RequestParam(name = PARAM_FILTER_STATUS_TYPE, required = false) String filterStatusType,
                              @RequestParam(name = PARAM_FILTER_OBJECT_TYPE, required = false) String filterObjectType,
                              @RequestParam(name = PARAM_FILTER_PROJECT_NAME, required = false) String filterProjectName,
                              @RequestParam(name = PARAM_GUID_SYNC) String guid,
                              @RequestParam(name = PARAM_LIST_KEY, required = false) String listKey,
                              @RequestBody(required = false) String body,
                              HttpServletRequest request,
                              HttpServletResponse response
    ) throws JSONException, IOException {
        boolean syncVariantEnabled = SyncTouchpointUtil.checkSyncVariantEnabledFeatureFlag(request, sourceInstanceId, sourceDocumentId);

        List<String> objectIds = getIdsFromRequestBody(body, PARAM_OBJECT_IDS);

        // Read data from session
        JSONArray data = getSyncData(
                sourceDocumentId,
                sourceInstanceId,
                targetDocumentId,
                targetInstanceId,
                languages,
                activeOnly,
                syncActiveOnly,
                filterObjectType,
                filterStatusType,
                filterText,
                acceptAll,
                objectIds,
                guid,
                listKey,
                includeNotVisibleTargetRules,
                includeRejectedChanges
            );

        data = sortSyncListData(data, sortKey, sortDirection);

        pageSize = validatePageSize(pageSize);
        pageIndex = validatePageIndex(pageIndex, pageSize, data);

        JSONObject result = new JSONObject();
        result.put(PARAM_PAGE_INDEX, pageIndex);
        result.put(PARAM_PAGE_SIZE, pageSize);
        result.put("rows", data.length());
        result.put("data", paginate(data, pageIndex, pageSize));

        return result.toString();
    }

    private JSONArray getSyncData(Long sourceDocumentId,
                                  Long sourceInstanceId,
                                  Long targetDocumentId,
                                  Long targetInstanceId,
                                  List<Long> languages,
                                  Boolean activeOnly,
                                  Boolean syncActiveOnly,
                                  String filterObjectType,
                                  String filterStatusType,
                                  String filterText,
                                  Boolean acceptAll,
                                  List<String> objectIds,
                                  String guid,
                                  String listKey,
                                  Boolean includeNotVisibleTargetRules,
                                  Boolean includeRejectedChanges) {

        JSONArray data = GetSyncListBackgroundTask.getGeneratedData(listKey);
        if (data == null) {
            JSONObject status = GetSyncListBackgroundTask.getSyncList(sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId, languages, activeOnly, syncActiveOnly, UserUtil.getPrincipalUser(), includeNotVisibleTargetRules, includeRejectedChanges);
            data = GetSyncListBackgroundTask.getGeneratedData(status);
        }

        // Filter sync list by object type
        data = new AsyncTouchpointSyncListFilter(filterStatusType, filterObjectType, filterText, objectIds, acceptAll).filter(data);
        cacheSyncData(data, SYNC_FILTERED_DATA_CACHE_KEY, guid);

        return data;
    }

    private void cacheSyncData(JSONArray data, String cacheKey, String guid) {
        try {
            if (StringUtils.isNotEmpty(cacheKey) && StringUtils.isNotEmpty(guid)) {
                SyncCacheUtil.putValue(cacheKey + guid, data.toString());
            }
        } catch (Exception ex) {
            log.warn("Failed to save data to Redis cache with key " + cacheKey);
        }
    }

    private JSONArray sortSyncListData(JSONArray data, String sortKey, String sortDirection) {
        if (!data.isEmpty()) {
            Comparator<Object> comparator = new AsyncTouchpointSyncListComparator(sortKey, sortDirection);
            try{
                List<Object> dataList = data.toList().stream().sorted(comparator).collect(Collectors.toList());
                return new JSONArray(dataList);
            }
            catch(Exception ex){
                log.error("Failed to sort sync list due to an error:", ex);
            }
        }

        return data;
    }

    private Long validatePageSize(Long pageSize) {
        Long validatedPageSize = pageSize < DEFAULT_MIN_PAGE_SIZE ? DEFAULT_MIN_PAGE_SIZE : pageSize;
        validatedPageSize = validatedPageSize > DEFAULT_MAX_PAGE_SIZE ? DEFAULT_MAX_PAGE_SIZE : validatedPageSize;

        return validatedPageSize;
    }

    private Long validatePageIndex(Long pageIndex, Long pageSize, JSONArray data) {

        long validatedPageIndex = (pageIndex - 1) * pageSize > data.length() ? Double.valueOf(Math.ceil(data.length() / pageSize)).longValue() : pageIndex;
        validatedPageIndex = validatedPageIndex <= 0 ? 1 : validatedPageIndex;

        return validatedPageIndex;
    }

    private JSONArray paginate(JSONArray data, Long pageIndex, Long pageSize) {
        List list = data.toList();

        int fromIndex = Math.max(Double.valueOf((pageIndex - 1) * pageSize).intValue(), 0);
        int toIndex = Math.min(fromIndex + pageSize.intValue(), list.size());

        return new JSONArray(list.subList(fromIndex, toIndex));
    }

    private JSONArray getSyncDataFromCache(String cacheKey, String guid) {
        JSONArray data = null;
        if (StringUtils.isNotEmpty(cacheKey) && StringUtils.isNotEmpty(guid)) {
            String cachedData = SyncCacheUtil.getValue(cacheKey + guid);
            if (cachedData != null) {
                data = new JSONArray(cachedData);
            }
        }
        return data;
    }

    @RequestMapping(value = "initSyncProcess.form", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public String initSyncProcess(@RequestParam(name = PARAM_SOURCE_DOCUMENT_ID) Long sourceDocumentId,
                                  @RequestParam(name = PARAM_SOURCE_INSTANCE_ID) Long sourceInstanceId,
                                  @RequestParam(name = PARAM_TARGET_DOCUMENT_ID) Long targetDocumentId,
                                  @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId,
                                  @RequestParam(name = PARAM_LANGUAGES) List<Long> languages,
                                  @RequestParam(name = PARAM_ACCEPT_ALL) Boolean acceptAll,
                                  @RequestParam(name = PARAM_FORCE_SYNC) Boolean forceSync,
                                  @RequestParam(name = PARAM_ACTIVE_ONLY) Boolean activeOnly,
                                  @RequestParam(name = PARAM_SYNC_ACTIVE_ONLY) Boolean syncActiveOnly,
                                  @RequestParam(name = PARAM_FILTER_OBJECT_TYPE, required = false) String filterObjectType,
                                  @RequestParam(name = PARAM_FILTER_TEXT, required = false) String filterText,
                                  @RequestParam(name = PARAM_FILTER_STATUS_TYPE, required = false) String filterStatusType,
                                  @RequestParam(name = PARAM_SYNC_MESSAGE_PRIORITY) Boolean syncMessagePriority,
                                  @RequestParam(name = PARAM_INCLUDE_DIFFERENCES_IN_LOG) Boolean includeDifferencesInLog,
                                  @RequestParam(name = PARAM_INCLUDE_NOT_VISIBLE_TARGET_RULES) Boolean includeNotVisibleTargetRules,
                                  @RequestParam(name = PARAM_INCLUDE_REJECTED_CHANGES) Boolean includeRejectedChanges,  // Include changes rejected before
                                  @RequestParam(name = PARAM_HIDE_UNTIL_NEXT_CHANGES) Boolean hideUntilNextChanges,     // Reject this time
                                  @RequestParam(name = PARAM_NOT_SYNC_LAYOUT_CHANGES) Boolean notSyncLayoutChanges,     // Dont sync layouts

                                  @RequestParam(name = PARAM_GUID_SYNC) String guid,
                                  @RequestParam(name = PARAM_NEW_WC_AFTER_AC, required = false, defaultValue = "false") Boolean newWorkingCopyAfterActiveCopy,
                                  @RequestParam(name = PARAM_MAINTAIN_TARGET_RELATIVE_PRIORITY, required = false, defaultValue = "false") Boolean maintainTargetRelativePriority,
                                  @RequestBody(required = false) String body) throws JSONException, IOException {

        if(SyncTouchpointUtil.hasActiveBackgroundTasks(sourceInstanceId, targetInstanceId)) {
            JSONObject status = new JSONObject();
            status.put("status", "error");
            status.put("errorMessage", ApplicationUtil.getMessage("error.message.cannot.run.task"));
            return status.toString();
        }

        List<String> objectIds = getIdsFromRequestBody(body, PARAM_OBJECT_IDS);
        List<String> rejectIds = getIdsFromRequestBody(body, PARAM_REJECT_IDS);

        List<String> objectIdsForSync = new ArrayList<>(objectIds);

        JSONArray data = getSyncDataFromCache(SYNC_FILTERED_DATA_CACHE_KEY, guid);
        if (data == null) {
            data = getSyncData(
                    sourceDocumentId,
                    sourceInstanceId,
                    targetDocumentId,
                    targetInstanceId,
                    languages,
                    activeOnly,
                    syncActiveOnly,
                    filterObjectType,
                    filterStatusType,
                    filterText,
                    acceptAll,
                    objectIds,
                    guid,
                    null,
                    includeNotVisibleTargetRules,
                    includeRejectedChanges
                );
        }

        if (acceptAll) {
            // Get the data from cache
            objectIdsForSync = data.toList().stream().filter(o -> {
                if (objectIds == null || objectIds.isEmpty()) {
                    return true;
                }

                HashMap map = (HashMap) o;
                Long id = Long.parseLong(map.get("id").toString());
                Integer type = (Integer) map.get("objectType");
                Integer status = (Integer) map.get("statusCode");

                String objectId = type + "_" + id + "_" + status;

                return !objectIds.contains(objectId);
            }).map(o -> {
                HashMap map = (HashMap) o;
                Long id = Long.parseLong(map.get("id").toString());
                Integer type = (Integer) map.get("objectType");
                Integer status = (Integer) map.get("statusCode");

                return type + "_" + id + "_" + status;
            }).collect(Collectors.toList());
        }

        SyncStarterBackgroundTask   syncContentTask = null,
                                    rejectSyncTask = null;

        SyncTouchpointMessagePriorityBackgroundTask syncPrioritiesTask = null;

        if (!objectIdsForSync.isEmpty()) {
            Map<Long, Long> objectStatusMap = new HashMap<>();
            Map<Long, Long> objectTypeMap = new HashMap<>();
            Map<Long, List<Long>> sourceMustExistsMap = new HashMap<>();
            Map<Long, List<Long>> sourceSyncWhenChanged = new HashMap<>();
            LinkedList<Long> orderedIDsList = new LinkedList<>();
            SyncTouchpointUtil.parseObjectIDsForSync(data, objectIdsForSync, objectStatusMap, objectTypeMap, sourceMustExistsMap, sourceSyncWhenChanged, orderedIDsList);
            syncContentTask = new SyncStarterBackgroundTask(
                    sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId,
                    activeOnly,
                    syncActiveOnly,
                    forceSync,
                    includeDifferencesInLog, false,
                    includeNotVisibleTargetRules, includeRejectedChanges,
                    notSyncLayoutChanges,
                    objectIdsForSync,
                    languages, UserUtil.getPrincipalUser(),
                    objectStatusMap, objectTypeMap, sourceMustExistsMap, sourceSyncWhenChanged, orderedIDsList);
        }

        if(rejectIds != null && !rejectIds.isEmpty()){
            Map<Long, Long> objectStatusMap = new HashMap<>();
            Map<Long, Long> objectTypeMap = new HashMap<>();
            Map<Long, List<Long>> sourceMustExistsMap = new HashMap<>();
            Map<Long, List<Long>> sourceSyncWhenChanged = new HashMap<>();
            LinkedList<Long> orderedIDsList = new LinkedList<>();
            SyncTouchpointUtil.parseObjectIDsForSync(data, rejectIds, objectStatusMap, objectTypeMap, sourceMustExistsMap, sourceSyncWhenChanged, orderedIDsList);
            rejectSyncTask = new SyncStarterBackgroundTask(
                    sourceDocumentId, sourceInstanceId, targetDocumentId, targetInstanceId,
                    activeOnly,
                    syncActiveOnly,
                    forceSync,
                    includeDifferencesInLog, true,
                    includeNotVisibleTargetRules, includeRejectedChanges,
                    notSyncLayoutChanges,
                    rejectIds,
                    languages, UserUtil.getPrincipalUser(),
                    objectStatusMap, objectTypeMap, sourceMustExistsMap, sourceSyncWhenChanged, orderedIDsList);

        }

        if(syncMessagePriority){
            syncPrioritiesTask = buildSyncPrioritiesTask(sourceDocumentId,
                    sourceInstanceId,
                    targetDocumentId,
                    targetInstanceId,
                    newWorkingCopyAfterActiveCopy,
                    maintainTargetRelativePriority
            );
        }

        // Chain sync content process after reject
        SyncStarterBackgroundTask firstSyncProcess = chainSyncProcesses(syncContentTask, rejectSyncTask, syncPrioritiesTask);
        firstSyncProcess.chainNextProcessStatus();

        log.info("Initiating SyncStarterBackgroundTask with background task GUID=" + firstSyncProcess.getBackgroundTaskGuid());
        MessagePointRunnableUtil.startThread(firstSyncProcess, Thread.MAX_PRIORITY).getId();

        JSONObject status = new JSONObject();
        if(firstSyncProcess != null && StringUtils.isNotBlank(firstSyncProcess.getBackgroundTaskGuid())){
            status = SyncStarterBackgroundTaskStatus.getStatusOfAllProcesses(firstSyncProcess.getBackgroundTaskGuid());
        }

        if(syncPrioritiesTask != null && StringUtils.isNotBlank(syncPrioritiesTask.getBackgroundTaskGuid())){
            status.put("syncPrioritiesTaskId", syncPrioritiesTask.getBackgroundTaskGuid());
        }

        return status.toString();
    }

    private SyncStarterBackgroundTask chainSyncProcesses(SyncStarterBackgroundTask syncContentTask, SyncStarterBackgroundTask rejectSyncTask, SyncTouchpointMessagePriorityBackgroundTask syncPrioritiesTask){
        // Chain content after reject
        if(rejectSyncTask != null && syncContentTask != null){
            log.info("Sync content task " + syncContentTask.getBackgroundTaskGuid() + " chained after reject sync process " + rejectSyncTask.getBackgroundTaskGuid());
            rejectSyncTask.setNextSyncTask(syncContentTask);
        }

        // Chain priorities after either sync content or reject sync
        if(syncPrioritiesTask != null){
            if(syncContentTask != null){
                log.info("Sync priorities task " + syncPrioritiesTask.getBackgroundTaskGuid() + " chained after sync content process " + syncContentTask.getBackgroundTaskGuid());
                syncContentTask.setNextSyncTask(syncPrioritiesTask);
            }
            else if(rejectSyncTask != null){
                log.info("Sync priorities task " + syncPrioritiesTask.getBackgroundTaskGuid() + " chained after reject sync process " + rejectSyncTask.getBackgroundTaskGuid());
                rejectSyncTask.setNextSyncTask(syncPrioritiesTask);
            }
        }

        return rejectSyncTask != null ? rejectSyncTask : syncContentTask;
    }

    @RequestMapping(value = "getSyncProcessStatus.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSyncProcessStatus(@RequestParam(name = PARAM_TASK_ID) String taskGuid,
                                       @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId) throws JSONException, IOException {

        JSONObject statusJsonObject = SyncStarterBackgroundTaskStatus.getStatusOfAllProcesses(taskGuid);
        return statusJsonObject.toString();
    }

    @RequestMapping(value = "initSyncPriority.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String initSyncPriority(@RequestParam(name = PARAM_SOURCE_DOCUMENT_ID) Long sourceDocumentId,
                                   @RequestParam(name = PARAM_SOURCE_INSTANCE_ID) Long sourceInstanceId,
                                   @RequestParam(name = PARAM_TARGET_DOCUMENT_ID) Long targetDocumentId,
                                   @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId,
                                   @RequestParam(name = PARAM_NEW_WC_AFTER_AC) Boolean newWorkingCopyAfterActiveCopy,
                                   @RequestParam(name = PARAM_MAINTAIN_TARGET_RELATIVE_PRIORITY) Boolean maintainTargetRelativePriority,
                                   @RequestParam(name = PARAM_GUID_SYNC) String guid) throws JSONException, IOException {


        if(SyncTouchpointUtil.hasActiveBackgroundTasks(sourceInstanceId, targetInstanceId)) {
            JSONObject status = new JSONObject();
            status.put("status", "error");
            status.put("errorMessage", ApplicationUtil.getMessage("error.message.cannot.run.task"));
            return status.toString();
        }

        String targetSchema = Node.findById(targetInstanceId).getSchemaName();
        String backgroundTaskGuid = "";

        backgroundTaskGuid = CloneHelper.execInSchema(targetSchema, () -> {
            SyncTouchpointMessagePriorityBackgroundTask task = buildSyncPrioritiesTask(
                    sourceDocumentId,
                    sourceInstanceId,
                    targetDocumentId,
                    targetInstanceId,
                    newWorkingCopyAfterActiveCopy,
                    maintainTargetRelativePriority
                );
            MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);

            return task.getBackgroundTaskGuid();
        });

        JSONObject result = new JSONObject();
        result.put("status", backgroundTaskGuid.isEmpty() ? "error" : "success"); // If success or not
        result.put("errorMessage", ""); // In case of error, a message
        result.put("taskGuid", backgroundTaskGuid); // If success, the task GUID

        return result.toString();
    }

    private SyncTouchpointMessagePriorityBackgroundTask buildSyncPrioritiesTask(Long sourceDocumentId,
                                                                                Long sourceInstanceId,
                                                                                Long targetDocumentId,
                                                                                Long targetInstanceId,
                                                                                boolean newWorkingCopyAfterActiveCopy,
                                                                                boolean maintainTargetRelativePriority){
        Node targetNode = targetInstanceId == null ? Node.getCurrentNode() : Node.findById(targetInstanceId);
        User requestor = UserUtil.getPrincipalUser();
        User userInTargetNode = requestor == null ? null : requestor.getNodeUser(targetNode);
        return CloneHelper.queryInSchema(targetNode.getSchemaName(), ()->{
            return  new SyncTouchpointMessagePriorityBackgroundTask(
                targetDocumentId,
                true,
                newWorkingCopyAfterActiveCopy,
                maintainTargetRelativePriority,
                sourceDocumentId,
                sourceInstanceId,
                userInTargetNode);
        });
    }

    @RequestMapping(value = "getSyncPriorityStatus.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSyncPriorityStatus(@RequestParam(name = PARAM_TASK_ID) String taskGuid,
                                        @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId) throws JSONException, IOException {

        Node targetNode = Node.findById(targetInstanceId);
        StatusPollingBackgroundTask pollingBackgroundTask = CloneHelper.queryInSchema(targetNode.getSchemaName(), () -> StatusPollingBackgroundTask.findByGuid(taskGuid));

        Integer percentage = 0;

        if (pollingBackgroundTask != null) {
            percentage = pollingBackgroundTask.getProgressInPercentInThread();
        }

        JSONObject result = new JSONObject();
        result.put("percentage", percentage); //  0 - 100

        return result.toString();

    }

    @RequestMapping(value = "initExportSyncDataProcess.form", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public String initExportSyncDataProcess(
        @RequestParam(name = PARAM_GUID_SYNC) String guid,
        @RequestParam(name = PARAM_SOURCE_DOCUMENT_ID) Long sourceDocumentId,
        @RequestParam(name = PARAM_SOURCE_INSTANCE_ID) Long sourceInstanceId,
        @RequestParam(name = PARAM_TARGET_DOCUMENT_ID) Long targetDocumentId,
        @RequestParam(name = PARAM_TARGET_INSTANCE_ID) Long targetInstanceId,
        @RequestParam(name = PARAM_INCLUDE_RAW_CONTENTS, required = false) Boolean includeRawContent,
        @RequestBody(required = false) String body) {
        List<String> objectIds = getIdsFromRequestBody(body, PARAM_OBJECT_IDS);
        List<String> rejectIds = getIdsFromRequestBody(body, PARAM_REJECT_IDS);

        ExportSyncDataBackgroundTask task = new ExportSyncDataBackgroundTask(
            SYNC_FILTERED_DATA_CACHE_KEY + guid,
            includeRawContent,
            sourceInstanceId,
            sourceDocumentId,
            targetInstanceId,
            targetDocumentId,
            objectIds,
            rejectIds,
            UserUtil.getPrincipalUser());
        MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY).getId();

        JSONObject result = new JSONObject();
        result.put("status", "success");
        result.put("taskGuid", task.getBackgroundTaskGuid());

        return result.toString();
    }

    @RequestMapping(value = "getExportSyncDataProcessStatus.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getExportSyncDataProcessStatus(@RequestParam(name = PARAM_TASK_GUID) String taskGuid){

        StatusPollingBackgroundTask pollingBackgroundTask = StatusPollingBackgroundTask.findByGuid(taskGuid);
        JSONObject result = new JSONObject();

        if(pollingBackgroundTask != null){
            result.put("taskGuid", taskGuid);
            if(StringUtils.isNotEmpty(pollingBackgroundTask.getOutputPath())){
                result.put("fileName", pollingBackgroundTask.getOutputFilename());
                result.put("resource", pollingBackgroundTask.getResourceToken());
            }
            if(pollingBackgroundTask.isActive()){
                result.put("status", "inProgress");
            }
            else{
                result.put("status", pollingBackgroundTask.isError() ? "error" : "success");
            }
        }

        return result.toString();
    }

    @RequestMapping(value = "getSyncObjectDetailView.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSyncObjectDetailView(
        @RequestParam(name = PARAM_OBJECT_TYPE)                         Integer objectType,
        @RequestParam(name = PARAM_DATA_TYPE)                           Integer dataType,
        @RequestParam(name = PARAM_SOURCE_INSTANCE_ID, required=false)  Long sourceInstanceID,
        @RequestParam(name = PARAM_SOURCE_DOCUMENT_ID)                  Long sourceDocumentID,
        @RequestParam(name = PARAM_SOURCE_OBJECT_ID)                    Long sourceObjectID,
        @RequestParam(name = PARAM_TARGET_INSTANCE_ID, required=false)  Long targetInstanceID,
        @RequestParam(name = PARAM_TARGET_DOCUMENT_ID)                  Long targetDocumentID,
        @RequestParam(name = PARAM_TARGET_OBJECT_ID)                    Long targetObjectID,
        @RequestParam(name = PARAM_LOCALE_ID, required=false)           String localeIDs
    ) {
        return SyncListUtil.getSyncObjectDetailView(objectType, dataType, sourceInstanceID, sourceDocumentID, sourceObjectID, targetInstanceID, targetDocumentID, targetObjectID, localeIDs).toString();
    }
}