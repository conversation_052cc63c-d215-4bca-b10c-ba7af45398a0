package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.ContentObjectContentWrapper;
import com.prinova.messagepoint.controller.targeting.TargetingWrapper;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.navigation.WorkflowTab;
import com.prinova.messagepoint.model.targeting.Targetable;
import com.prinova.messagepoint.model.workflow.Workflow;
import com.prinova.messagepoint.model.workflow.WorkflowPosition;
import com.prinova.messagepoint.model.wrapper.ContentObjectOverviewWrapper;
import com.prinova.messagepoint.tag.view.WorkflowTabTag;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public abstract class FlowController<T, V> extends ConfirmationCommandController<V> {

	public static final String REQ_PARAM_STATUS_VIEW_ID 			= "statusViewId";
	public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID 	= "touchpointSelectionId";
    public static final String REQ_PARM_CONTINUE_ID            	= "continueInstId";
	
	protected Class<?> persistedClass;
	private String persistedClassName;
	private String parameter;

	@Override
	protected Object formBackingObject(HttpServletRequest request) {
		Object command = super.formBackingObject(request);

		if (command instanceof IdentifiableMessagePointModel && command.getClass().equals(persistedClass)) {
			IdentifiableMessagePointModel model = (IdentifiableMessagePointModel) command;
			String parameterValue = request.getParameter(getParameter());
			if (parameterValue != null && !parameterValue.isEmpty()) {
				long id = Long.valueOf(parameterValue).longValue();
				if (model.getId() != id)
					command = HibernateUtil.getManager().getObject(persistedClass, id);
			} else {
				command = formBackingObjectInternal(request);
			}
			if (command instanceof ContentObject) {
				ContentObject contentObject = (ContentObject) command;
				boolean isContentObjectInProduction = false;
				try {
					isContentObjectInProduction = contentObject.hasActiveData();
				} catch (Exception e) {
				}
				contentObject.setConfirmationNeeded(isContentObjectInProduction);
			}
		}
		
		return command;
	}

	@SuppressWarnings("unchecked")
	@Override
	protected V formBackingObjectInternal(HttpServletRequest request) {
		return (V) getBackingObject(request);
	}

	@SuppressWarnings("unchecked")
	protected T getBackingObject(HttpServletRequest request) {
		long id = ServletRequestUtils.getLongParameter(request, getParameter(), -1);
		T object = (T) HibernateUtil.getManager().getObject(persistedClass, id);
		return object;
	}

	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {

		String submitType = ServletRequestUtils.getStringParameter(request, SUBMIT_PARAM_SUBMIT_TYPE, "");

		if (submitType.equals(SUBMIT_PARAM_CANCEL) || submitType.equals(SUBMIT_PARAM_CANCEL_AND_EXIT))
			return true;

		return super.suppressValidation(request, command);
	}

	@Override
	protected ModelAndView handleConfirm(HttpServletRequest request, HttpServletResponse response, V command, BindException errors) throws Exception {
		ModelAndView result = null;

        String submitType = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_TYPE_PARAMETER, "");
		if (!WorkflowTabTag.SUBMIT_TYPE_CANCEL.equals(submitType) && !WorkflowTabTag.SUBMIT_TYPE_CANCEL_AND_EXIT.equals(submitType)) {
			doSave(request, command, errors);
			if (errors.hasErrors()) {
				return showForm(request, response, errors);
			}
		}

        if (ServletRequestUtils.getIntParameter(request, REQ_PARM_CONTINUE_ID, -1) != -1) {
            return getContinueSuccessView(request);
        }

        if (result == null) {
            result = new ModelAndView(new RedirectView(getSuccessView(request, command)), getParameter(), request.getParameter(getParameter()));
        }

        return result;
	}

	@Override
	protected ModelAndView handleDiscard(HttpServletRequest request, V command) {
		Workflow workflow = Workflow.getWorkflow(persistedClass);
		WorkflowPosition current = workflow.getSelected(((HttpServletRequest) request).getRequestURL().toString());
		if (current == null) {
			String requestURL = ((HttpServletRequest) request).getRequestURL().toString();
			requestURL = requestURL.replace(getConfirmationView(), getFormView());
			// current is null because we are on a confirmation view.
			current = workflow.getSelected(requestURL);
		}
		return new ModelAndView(new RedirectView(ApplicationUtil.getWebRoot() + current.getTab().getEditUrl()), getParameter(), request.getParameter(getParameter()));
	}

	@Override
	protected boolean needConfirmation(HttpServletRequest request, V command) {
		// Override this method if you need confirmation support.
		return false;
	}

	public abstract void doSave(HttpServletRequest request, V command, BindException errors) throws Exception;

	public abstract T getObject(V command);

    protected ModelAndView getContinueSuccessView(HttpServletRequest request) {
        return null;
    }

	protected String getSuccessView(HttpServletRequest request, V command) {
        String successView = "";
		Workflow workflow = Workflow.getWorkflow(persistedClass);
		WorkflowPosition current = workflow.getSelected(((HttpServletRequest) request).getRequestURL().toString());

		if (current == null) {
			// current is null because we are on a confirmation view.
			String requestURL = ((HttpServletRequest) request).getRequestURL().toString();
			if (getConfirmationView() != null && getFormView() != null)
				requestURL = requestURL.replace(getConfirmationView(), getFormView());
			current = workflow.getSelected(requestURL);
		}
		String submitType = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_TYPE_PARAMETER, "");
		String cfSource = ServletRequestUtils.getStringParameter(request, REQUEST_PARAM_CUSTOM_FORM_SOURCE, "");
		String flowParameter = ServletRequestUtils.getStringParameter(request, WorkflowTabTag.SUBMIT_FLOW_PARAMETER, "");

		ContentObject contentObject = null;
		boolean isGlobalContent = false;
		boolean isFreeform = false;
		boolean isGlobalImage = false;
		boolean isGlobalSmartText = false;
		if(command instanceof ContentObjectOverviewWrapper){
			contentObject = ((ContentObjectOverviewWrapper) command).getContentObject();
		}else if(command instanceof TargetingWrapper){
			Targetable targetable = ((TargetingWrapper) command).getModel();
			contentObject = (targetable instanceof ContentObject)?(ContentObject)targetable:null;
		}else if(command instanceof ContentObjectContentWrapper){
			contentObject = ((ContentObjectContentWrapper) command).getContentObject();
		}

		if(contentObject != null) {
			isGlobalContent = contentObject.getIsGlobalContentObject();
			isFreeform = contentObject.getIsFreeform();
			isGlobalImage = contentObject.getIsGlobalImage();
			isGlobalSmartText = contentObject.getIsGlobalSmartText();
		}

   		if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_BACK.equals(submitType)) {
			WorkflowTab workflowTab = workflow.getPrevWorkflowPositionWithTab(current).getTab();
			String view = ApplicationUtil.getWebRoot()+ workflowTab.getEditUrl() +
					(flowParameter.isEmpty() ? "" : "?" + WorkflowTabTag.SUBMIT_FLOW_PARAMETER + "=" + flowParameter);
			return  view + (cfSource.isEmpty() ? "" : (view.contains("?") ? "&" : "?" + REQUEST_PARAM_CUSTOM_FORM_SOURCE + "=" + cfSource));
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE.equals(submitType)) {
			if (flowParameter.equals("newTpContSelMsg"))
				return  ApplicationUtil.getWebRoot() + "tpadmin/touchpoint_content_selections_list.form?" +
						HttpRequestUtil.getBackToListURL(request,HttpRequestUtil.HTTP_ATTRIBUTE_BACKURL_TP_CONTENT_SELECTION);
			else
				return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_STAY.equals(submitType)) {
			if (current != null)
			return ApplicationUtil.getWebRoot() + current.getTab().getEditUrl() +
					"?saveSuccess=true" +
					(cfSource.isEmpty() ? "" : "&" + REQUEST_PARAM_CUSTOM_FORM_SOURCE + "=" + cfSource);
			else {

				String string = ApplicationUtil.getWebRoot() + "content/content_object_edit.form" +
						"?saveSuccess=true" +
						(cfSource.isEmpty() ? "" : "&" + REQUEST_PARAM_CUSTOM_FORM_SOURCE + "=" + cfSource);
				return string;
			}
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_VIEW.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl() +
					"?saveSuccess=true&statusViewId=1" +
					(cfSource.isEmpty() ? "" : "&" + REQUEST_PARAM_CUSTOM_FORM_SOURCE + "=" + cfSource);
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_GOTOLIST.equals(submitType)) {
			String listUrl = current.getTab().getListUrl();

			if (listUrl != null && !listUrl.trim().isEmpty()) {

				String url = ApplicationUtil.getWebRoot() + listUrl + "?";

				if (isFreeform) {

					if (isGlobalContent)
						url = ApplicationUtil.getWebRoot() + "content/global_content_list.form?isFreeform=true&";

					else url = ApplicationUtil.getWebRoot() + "touchpoints/local_content_list.form?isFreeform=true&";
				}

				else if (isGlobalSmartText)
					url = ApplicationUtil.getWebRoot() + "content/global_content_list.form?";

				else if (isGlobalImage)
					url = ApplicationUtil.getWebRoot() + "content/global_content_list.form?localContentType=" + ContentObject.OBJECT_TYPE_LOCAL_IMAGE + "&";

				return url + "saveSuccess=true";

			} else return ApplicationUtil.getWebRoot() + current.getTab().getEditUrl() + "&saveSuccess=true";
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_GOBACK.equals(submitType)) {
			String[] origin = getOriginForm(request, true);
			if (origin[0].contains("content/content_object_edit_targeting.form"))
				origin = getOriginForm(request, true);
			return origin[0] + "?saveSuccess=true";
		} else if (WorkflowTabTag.SUBMIT_TYPE_CANCEL_AND_EXIT.equals(submitType)) {
			String[] origin = getOriginForm(request, true);
			if (origin[0].contains("content/content_object_edit_targeting.form"))
				origin = getOriginForm(request, true);

			if (isFreeform)
				return origin[0] + "?isFreeform=true";

			else if (isGlobalImage)
				return origin[0] + "?localContentType=" + ContentObject.OBJECT_TYPE_LOCAL_IMAGE;

			return origin[0];
		} else if (WorkflowTabTag.SUBMIT_TYPE_PREVIEW_VIEW.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_PREVIEW_EDIT.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getEditUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_CANCEL.equals(submitType)) {
			return ApplicationUtil.getWebRoot() + current.getTab().getViewUrl();
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_FORWARD.equals(submitType)) {
			WorkflowTab workflowTab = workflow.getNextWorkflowPositionWithTab(current).getTab();
			String view =  ApplicationUtil.getWebRoot() + workflowTab.getEditUrl() +
					(flowParameter.isEmpty() ? "" : "?" + WorkflowTabTag.SUBMIT_FLOW_PARAMETER + "=" + flowParameter);
			return  view + (cfSource.isEmpty() ? "" : (view.contains("?") ? "&" : "?" + REQUEST_PARAM_CUSTOM_FORM_SOURCE + "=" + cfSource));
		} else if (WorkflowTabTag.SUBMIT_TYPE_SAVE_AND_CLOSE.equals(submitType)) {
			return "../frameClose.jsp?nprSaveSuccess=true";
		}

        successView = getSuccessView();

		return successView == null ? "" : successView;
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}

	public String getPersistedClassName() {
		return persistedClassName;
	}

	public void setPersistedClassName(String persistedClassName) {
		this.persistedClassName = persistedClassName;
		try {
			this.persistedClass = Class.forName(persistedClassName);
		} catch (ClassNotFoundException e) {
			throw new RuntimeException("CLass not found in FlowController setPersistedClassName. Must be a COnfiguration error.",
					e);
		}
	}
	
	public int getStatusViewIdParam(HttpServletRequest request) {
		return UserUtil.getCurrentSelectionStatusContext().getId();
	}
	
	public long getCurrentTouchpointSelectionId(HttpServletRequest request, ContentObject contentObject) {
		if (contentObject.isVariantType())
			return HttpRequestUtil.getCurrentTouchpointSelectionId(request, contentObject);
		else
			return -1;
	}
}
