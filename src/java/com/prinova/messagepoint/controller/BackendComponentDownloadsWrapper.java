package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.BackendComponentMetadata;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import org.apache.commons.io.FileUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class BackendComponentDownloadsWrapper {

    private static final Log log = LogUtil.getLog(BackendComponentDownloadsWrapper.class);

    private File[] DEDigital;
    private File[] DEServer;
    private File[] MPComposer;
    private File[] MPComp;
    private File[] productionManager;

    private static final String PREFIX_FILE_VALUE_DEDIGITAL = "DEDigital_";
    private static final String PREFIX_FILE_VALUE_DESERVER = "DEServer_";
    private static final String PREFIX_FILE_VALUE_MPCOMPOSER = "MPComposer_";
    private static final String PREFIX_FILE_VALUE_MPCOMP = "MPComp_";
    private static final String PREFIX_FILE_VALUE_PMGR = "pmgr-";


    public void setDEDigital(File[] DEDigital) {
        this.DEDigital = DEDigital;
    }

    public void setDEServer(File[] DEServer) {
        this.DEServer = DEServer;
    }

    public List<BackendComponentMetadata> getDEDigitalDownloads() {
        return getDownloadFiles(this.DEDigital, PREFIX_FILE_VALUE_DEDIGITAL);
    }

    public List<BackendComponentMetadata> getDEServerDownloads() {
        return getDownloadFiles(this.DEServer, PREFIX_FILE_VALUE_DESERVER);
    }

    public List<BackendComponentMetadata> getMPComposerDownloads() {
        return getDownloadFiles(this.MPComposer, PREFIX_FILE_VALUE_MPCOMPOSER);
    }

    public List<BackendComponentMetadata> getMPCompDownloads() {
        return getDownloadFiles(this.MPComp, PREFIX_FILE_VALUE_MPCOMP);
    }

    public List<BackendComponentMetadata> getPMGRDownloads() {
        return getDownloadFiles(this.productionManager, PREFIX_FILE_VALUE_PMGR);
    }

    private List<BackendComponentMetadata> getDownloadFiles(File[] files, String prefix) {
        ArrayList<BackendComponentMetadata> result = new ArrayList<>();

        try {
            for (File file : files) {
                if (!file.isFile() || !file.getName().startsWith(prefix)) {
                    continue;
                }

                BackendComponentMetadata value = new BackendComponentMetadata();

                String filename = file.getName();
                value.setFilename(filename);
                int versionIndexStart = filename.indexOf(prefix) + prefix.length();
                int versionIndexEnd = filename.indexOf("_", prefix.length()  + 1);

                if (versionIndexEnd > 0 && versionIndexEnd > versionIndexStart) {
                    value.setVersion(filename.substring(versionIndexStart, versionIndexEnd));
                }

                value.setFilesize(FileUtils.byteCountToDisplaySize(file.length()));
                value.setFileDate( new SimpleDateFormat(DateUtil.DATE_FORMAT).format(file.lastModified()));

                result.add(value);
            }
        } catch (Exception e) {
            log.error("Error:", e);
        }

        return result;
    }

    public boolean isOpenTextTenant() {
        return ApplicationUtil.getProperty(SystemPropertyKeys.UserInterface.KEY_SigninTheme).equals("1");
    }

    public void setMPComposer(File[] MPComposer) {
        this.MPComposer = MPComposer;
    }

    public void setMPComp(File[] MPComp) {
        this.MPComp = MPComp;
    }

    public void setProductionManager(File[] productionManager) {
        this.productionManager = productionManager;
    }

    public boolean getIsPincEnabled() {
        return MessagepointLicenceManager.getInstance().isLicencedForPINC();
    }

}
