package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.SegmentationAnalysis;
import com.prinova.messagepoint.model.SegmentationAnalyzable;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;

public class AsyncSegmentationResultController implements Controller{

	private static final Log log = LogUtil.getLog(AsyncSegmentationResultController.class);
	
	public static final int MODEL_TYPE_MESSAGE 				= 1;
	public static final int MODEL_TYPE_SELECTION			= 2;
	public static final int MODEL_TYPE_TARGETGROUP			= 3;
	
	public static final String PARAM_MODEL_TYPE				= "modelType";
	public static final String PARAM_MODEL_ID				= "modelId";
	public static final String PARAM_DOCUMENT_ID			= "documentId";

	@Override
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getSegmentationResultResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for segmentation result data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getSegmentationResultResponseJSON(HttpServletRequest request) {
		JSONObject returnObj = new JSONObject();
		try{
			int modelType			= ServletRequestUtils.getIntParameter(request, PARAM_MODEL_TYPE, 0);
			long modelId 			= ServletRequestUtils.getLongParameter(request, PARAM_MODEL_ID, 0);
			long documentId 		= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
			String segEntries 		= "";
			SegmentationAnalyzable sa = null;
			switch(modelType){
				case MODEL_TYPE_MESSAGE:{
					sa = ContentObject.findById(modelId);
					break;
				}
				case MODEL_TYPE_SELECTION:{
					sa = TouchpointSelection.findById(modelId);
					break;
				}
				case MODEL_TYPE_TARGETGROUP:{
					sa = TargetGroup.findById(modelId);
					break;
				}
			}
			if(sa != null){
				segEntries = sa.getSegmentationData();
			}
			boolean isInProcess = SegmentationAnalysis.isInProcess(segEntries, documentId); 
			if(isInProcess){
				returnObj.put("status" 				, "in_process");
			}else{
				String reach = SegmentationAnalysis.getRecentPercentageOfRecipientsInStr(segEntries, documentId);
				returnObj.put("status" 				, "complete");
				returnObj.put("reach" 				, reach);
			}
			returnObj.put("item_id" 				, modelId);
		} catch (JSONException e) {
			log.error("Error: Unable to get segmentation result: " + e );
		}
		return returnObj.toString();
	}

}
