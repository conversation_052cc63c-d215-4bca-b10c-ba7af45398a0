package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.model.wrapper.AsyncTestListVO;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * Generates a JSON response with Tests data to be displayed.
 * Can return an empty JSON, an "items" field as a JSON Array or an "error" field with a String message
 * <AUTHOR>
 * @since 2020-05-20
 */
public class AsyncTestingListResultsController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncTestingListResultsController.class);

    public static final String PARAM_TEST_ID = "id";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            out.write(getTestResultResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error - Unable to resolve request for test scenario data: "+e.getMessage(),e);
        }

        return null;
    }

    private String getTestResultResponseJSON (HttpServletRequest request) {
        JSONObject returnObj = new JSONObject();
        List<JSONObject> responseItems = new ArrayList<>();

        String[] testIds = request.getParameterValues(PARAM_TEST_ID);

        try {
            if (testIds != null && testIds.length > 0) {

                List<Long> ids = new ArrayList<>();
                for (String id: testIds) {
                    if (id != null && !id.trim().isEmpty()) {
                        try {
                            ids.add(Long.parseLong(id));
                        } catch (NumberFormatException e) {
                            log.error(e);
                        }
                    }
                }

                if (!ids.isEmpty()) {
                    List<TestScenario> list = HibernateUtil.getManager().getObjectsAdvanced(TestScenario.class, MessagepointRestrictions.in(TestScenario.FIELD_ID, ids));

                    for (TestScenario test: list) {
                        AsyncTestListVO testListVO = new AsyncTestListVO();
                        testListVO.setTest(test);

                        JSONObject testJson = new JSONObject();

                        testJson.put("id", test.getId());
                        testJson.put("job", testListVO.getJob());
                        testJson.put("date", testListVO.getDate());
                        testJson.put("user", testListVO.getUser());
                        testJson.put("status", testListVO.getStatus());
                        testJson.put("results", testListVO.getResults());
                        testJson.put("executionTimes", testListVO.getExecutionTimes());

                        responseItems.add(testJson);
                    }

                    returnObj.put("items", new JSONArray(responseItems));
                }
            } else {
                returnObj.put("error", ApplicationUtil.getMessage("error.message.invalid.test.param"));
            }
        } catch (JSONException e) {
            log.error("Error: Unable to retrieve test status: " + e );
        }

        return returnObj.toString();
    }

}
