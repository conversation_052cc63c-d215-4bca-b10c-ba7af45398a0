package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemProperty;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.communication.LibraryItemUsageType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.font.DecimalValueUtil;
import com.prinova.messagepoint.model.font.ListStyleVO;
import com.prinova.messagepoint.model.font.ParagraphStyleVO;
import com.prinova.messagepoint.model.font.TextStyleVO;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.ContentUpdateService;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

public class AsyncEditorDataController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncLayoutController.class);

    public static final String REQ_PARM_ACTION 				= "action";
    public static final String REQ_PARM_CONTENT		        = "content";
    public static final String REQ_PARM_CONTENT_ID	        = "contentId";
    public static final String REQ_PARM_CONTENT_OBJECT_ID	= "contentObjectId";
    public static final String REQ_PARM_LOCALE_ID		    = "localeId";
    public static final String REQ_TOUCHPOINT_SELECTION_ID  = "touchpointSelectionId";

    public static final String ACTION_GET_EDITOR_DATA       = "editor_data";
    public static final String ACTION_GET_CONTENT_OBJECT_DATA = "object_data";
    public static final String ACTION_SAVE_CONTENT          = "save_content";


    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);

            if ( action.equalsIgnoreCase(ACTION_GET_EDITOR_DATA) ) {
                out.write(getEditorDataJSON(request, response).toString().getBytes());
            } else if ( action.equalsIgnoreCase(ACTION_SAVE_CONTENT) ) {
                out.write(saveContent(request, response).toString().getBytes());
            } else if ( action.equalsIgnoreCase(ACTION_GET_CONTENT_OBJECT_DATA) ) {
                out.write(getContentDataJSON(request, response).toString().getBytes());
            } else {
                JSONObject returnObj = new JSONObject();
                returnObj.put("error", true);
                returnObj.put("message", "Metatags edit: Invalid action type");
                out.write(returnObj.toString().getBytes());
            }

        } catch (JSONException e) {
            JSONObject returnObj = new JSONObject();
            returnObj.put("error", true);
            returnObj.put("message", e.getMessage());
            out.write(returnObj.toString().getBytes());
        }

        out.flush();

        return null;
    }

    private JSONObject getContentDataJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();

        Long contentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_ID, -1L);
        Long contentObjectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_OBJECT_ID, -1L);

        if ( contentId > 0 && contentObjectId > 0 ) {

            returnObj.put("has_power_edit_permission", UserUtil.isPermissionGranted(Permission.ROLE_POWER_EDIT) );

            ContentObject co = ContentObject.findById(contentObjectId);
            returnObj.put("name", co.getName());
            returnObj.put("content_id", contentId);
            returnObj.put("content_object_id", contentObjectId);

            Boolean isGlobalObject = co.isGlobalContentObject();
            Boolean isStructuredContent = co.isStructuredContentEnabled();
            boolean isTouchpointStructured = isGlobalObject ? false: co.getDocument().isEnabledForVariation();
            TouchpointSelection masterSelection = isTouchpointStructured ? co.getDocument().getMasterTouchpointSelection() : null;
            returnObj.put("is_structured_content", isStructuredContent);
            returnObj.put("is_global_object", isGlobalObject);

            if ( isTouchpointStructured ) {
                returnObj.put("master_id", masterSelection.getId() );
                returnObj.put("is_variant_workflow", co.getDocument().isEnabledForVariantWorkflow() );
            }

            if ( masterSelection != null ) {
                returnObj.put("owning_selection_id", co.getOwningTouchpointSelection() != null ?
                        co.getOwningTouchpointSelection().getId() : masterSelection.getId());
                returnObj.put("owning_selection_name", co.getOwningTouchpointSelection() != null ?
                        co.getOwningTouchpointSelection().getName() : masterSelection.getName());
            }

            List<ContentObjectAssociation> coal = ContentObjectAssociation.findByContent(contentId);
            if ( coal.size() == 1 ) {

                ContentObjectAssociation coa = coal.get(0);
                Boolean isDynamic = coa.getContentObjectWithDataTypeFocus().isDynamicVariantEnabled();

                if ( coa.getZonePart() != null )
                    returnObj.put("group_name", coa.getZonePart().getName() );

                Boolean isLanguageReference = getLocaleId(request) != coa.getMessagepointLocale().getId();
                returnObj.put("is_language_reference", isLanguageReference);
                if ( isLanguageReference )
                    returnObj.put("referencing_language", coa.getMessagepointLocale().getDisplayName());

                returnObj.put("is_dynamic", isDynamic);
                int typeId = coa.getTypeId();
                returnObj.put("type_id", typeId);
                ParameterGroupTreeNode pgtn = coa.getPGTreeNode();
                TouchpointSelection editSelection = null;
                if ( pgtn != null) {
                    editSelection = TouchpointSelection.findByPgTreeNodeId(pgtn.getId());
                } else if ( isStructuredContent ) {
                    if (  co.getOwningTouchpointSelection() != null )
                        editSelection = co.getOwningTouchpointSelection();
                    else
                        editSelection = masterSelection;
                } else if ( isDynamic ) {
                    returnObj.put("edit_selection_name", "Default" );
                }

                boolean isRootObjectSelection = editSelection != null &&
                        ((editSelection == co.getOwningTouchpointSelection()) || (editSelection == masterSelection));
                if ( editSelection != null ) {

                    returnObj.put("edit_selection_name", editSelection.getName());
                    returnObj.put("edit_selection_id", editSelection.getId());

                    if ( !isGlobalObject && co.getDocument().isEnabledForVariantWorkflow() ) {
                        returnObj.put("can_update_edit_selection", editSelection.isMaster() ?
                                masterSelection.canUpdate(UserUtil.getPrincipalUser()) :
                                editSelection.hasWorkingCopy() && editSelection.canUpdate(UserUtil.getPrincipalUser()));

                        // Variant Workflow: Assess translation
                        boolean isPendingSelectionApproval = editSelection.getWorkflowAction() != null;
                        if ( isPendingSelectionApproval && ! isRootObjectSelection) {
                            Boolean isSelectionTranslationStep = editSelection.getCurrentWorkflowStep().isTranslationStep();
                            returnObj.put("is_translation_edit", isSelectionTranslationStep);
                            if ( isSelectionTranslationStep ) {
                                Set<MessagepointLocale> translationLocales = editSelection.getCurrentWorkflowStep().getLanguages();
                                if ( editSelection.getCurrentWorkflowStep().isAllowEditDefaultLanguage() )
                                    translationLocales.add(co.getDocument().getDefaultTouchpointLanguageLocale());
                                returnObj.put("can_edit_translation",translationLocales.contains( coa.getMessagepointLocale() ));
                                JSONArray translationLocaleNames = new JSONArray();
                                for ( MessagepointLocale currentLocale: translationLocales )
                                    translationLocaleNames.put( currentLocale.getDisplayName() );
                                returnObj.put("editable_translation_langs", translationLocaleNames);
                            }
                        }
                    }

                }

                // Standard Object Workflow (non Variant Workflow): Assess translation
                if ( editSelection == null || isRootObjectSelection || co.isGlobalContentObject() || !co.getDocument().isEnabledForVariantWorkflow() ) {
                    boolean isPendingApproval = co.isPendingApproval();
                    Boolean isTranslationStep = isPendingApproval && co.getCurrentWorkflowStep().isTranslationStep();
                    if (isPendingApproval) {
                        returnObj.put("is_translation_step", isTranslationStep);
                        if (isTranslationStep) {
                            Set<MessagepointLocale> translationLocales = co.getCurrentWorkflowStep().getLanguages();
                            if (co.getCurrentWorkflowStep().isAllowEditDefaultLanguage() && !co.isGlobalContentObject())
                                translationLocales.add(co.getDocument().getDefaultTouchpointLanguageLocale());
                            else if (co.getCurrentWorkflowStep().isAllowEditDefaultLanguage() && co.isGlobalContentObject())
                                translationLocales.add(MessagepointLocale.getDefaultSystemLanguageLocale());
                            returnObj.put("can_edit_translation", translationLocales.contains(coa.getMessagepointLocale()));
                            JSONArray translationLocaleNames = new JSONArray();
                            for (MessagepointLocale currentLocale : translationLocales)
                                translationLocaleNames.put(currentLocale.getDisplayName());
                            returnObj.put("editable_translation_langs", translationLocaleNames);
                        }
                    }
                }

            }
        }

        return returnObj;
    }

    private JSONObject saveContent(HttpServletRequest request, HttpServletResponse response) throws JSONException {
        JSONObject returnObj = new JSONObject();

        Long contentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_ID, -1L);
        long contentObjectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_OBJECT_ID, -1L);
        String content = ServletRequestUtils.getStringParameter(request, REQ_PARM_CONTENT, null);

        if ( contentId > 0 && content != null && contentObjectId > 0 ) {

            ServiceExecutionContext context = ContentUpdateService.createContext(contentId, content, contentObjectId);

            Service service = MessagepointServiceFactory.getInstance().lookupService(ContentUpdateService.SERVICE_NAME, ContentUpdateService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            if ( !serviceResponse.isSuccessful() ) {
                returnObj.put("error", true);
                returnObj.put("message", serviceResponse.getMessagesToString());
            } else {
                returnObj.put("success", true);
                returnObj.put("content_id", contentId);
            }

        }

        return returnObj;
    }

    private JSONObject getEditorDataJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {

        boolean debugMode = true;

        long startTime = 0;
        if (debugMode)
            startTime = new Date().getTime();

        long contentObjectId = ServletRequestUtils.getLongParameter(request, REQ_PARM_CONTENT_OBJECT_ID, -1L);
        ContentObject co = ContentObject.findById(contentObjectId);

        JSONObject returnObj = new JSONObject();

        if (co != null ) {

            try {

                JSONObject editorData = new JSONObject();

                long timeStamp = new Date().getTime();

                List<TextStyleVO> textStyles = co.getStyles();
                List<ParagraphStyleVO> paragraphStyles = co.getParagraphStyles();
                List<ListStyleVO> listStyles = co.getListStyles();
                Document touchpointContext 	= co.getFirstDocumentDelivery();
                long touchpointSelectionId = ServletRequestUtils.getLongParameter(request, REQ_TOUCHPOINT_SELECTION_ID, -1L);
                TouchpointSelection touchpointSelection = TouchpointSelection.findById(touchpointSelectionId);
                Zone sharedAssetZoneContext = co.getIsTouchpointLocal() && !co.isDeliveredToPlaceholder() ? null : co.getZone();
                Boolean isCommunicationContent = co.getUsageTypeId() == LibraryItemUsageType.ID_COMMUNICATION ||
                                                 co.getUsageTypeId() == LibraryItemUsageType.ID_UNRESTRICTED;

                String contentCSS = "";
                contentCSS += co.getDefaultEditorCSSFilePath() + "?cacheStamp=" + timeStamp;
                if ( !textStyles.isEmpty() )
                    contentCSS += "," + co.getCSSFilename() + "?cacheStamp=" + timeStamp;
                editorData.put("content_css", contentCSS);

                editorData.put("text_data", !textStyles.isEmpty() ? co.getTextStyleData() : null);

                JSONObject objectReferencesType = new JSONObject();
                if ( co.isGlobalImage() )
                    objectReferencesType.put("type", "document");
                else
                    objectReferencesType.put("type", co.isGlobalContentObject() ? "embedded_content" : "message");
                editorData.put("async_variables", objectReferencesType);
                editorData.put("async_embedded_content", objectReferencesType);

                // SYSTEM VARIABLES
                List<DataElementVariable> systemVariables = new ArrayList<>();
                boolean isRestricted = true;
                if (touchpointContext != null &&
                        ( touchpointContext.isNativeCompositionTouchpoint() || touchpointContext.isMPHCSCompositionTouchpoint() || touchpointContext.isSefasCompositionTouchpoint()) ) {
                    isRestricted = false;
                }
                systemVariables = DataElementVariable.findAllSystemVariablesEnabledForContent(isRestricted);
                editorData.put("markers_list", DataElementVariable.getListAsJSON(systemVariables));

                // SMART CANVAS
                List<ContentObject> smartCanvas = new ArrayList<>();
                if (co.getIsFreeform() && !co.getIsSharedFreeform() && touchpointContext.isNativeCompositionTouchpoint()) {
                    String[] zoneDims = sharedAssetZoneContext.getCanvasDimensions().split(":");
                    smartCanvas = ContentObject.findAllCanvasWithActiveInstanceByDocumentAndZone(touchpointContext, sharedAssetZoneContext, ContentType.SHARED_FREEFORM,
                            sharedAssetZoneContext.isSupportsTables(), sharedAssetZoneContext.isSupportsForms(), sharedAssetZoneContext.isSupportsBarcodes(), false, DecimalValueUtil.hydrate(zoneDims[0]), DecimalValueUtil.hydrate(zoneDims[1]));
                }
                editorData.put("smart_canvas_list", ContentObject.getListAsJSON(smartCanvas));

                // LOCAL SMART CANVAS
                List<ContentObject> localSmartCanvas = new ArrayList<>();
                if ( co.getIsFreeform() && !co.getIsSharedFreeform() && touchpointContext.isNativeCompositionTouchpoint() ) {
                    String[] zoneDims = sharedAssetZoneContext.getCanvasDimensions().split(":");
                    localSmartCanvas = ContentObject.findAllLocalCanvasByDocumentAndSelection(touchpointContext, UserUtil.getCurrentSelectionContext(co), sharedAssetZoneContext.isSupportsTables(), sharedAssetZoneContext.isSupportsForms(), DecimalValueUtil.hydrate(zoneDims[0]), DecimalValueUtil.hydrate(zoneDims[1]));
                }
                editorData.put("local_smart_canvas_list", ContentObject.getListAsJSON(localSmartCanvas));

                if ( !co.isGlobalContentObject() ) {
                    JSONObject messageType = new JSONObject();
                    messageType.put("type","message");
                    editorData.put("async_local_embedded_content", messageType);
                }

                // PLACEHOLDERS
                List<Zone> placeholders = new ArrayList<>();
                if ( !co.getIsTouchpointLocal() )
                    placeholders = Zone.findPlaceholdersByDocumentId(touchpointContext);
                editorData.put("placeholders_list", Zone.getListAsJSON(placeholders));

                editorData.put("applies_text_styles", !textStyles.isEmpty());
                editorData.put("applies_paragraph_styles", !paragraphStyles.isEmpty());
                editorData.put("applies_list_styles", !listStyles.isEmpty());

                editorData.put("paragraph_style_css", !paragraphStyles.isEmpty() ? co.getParagraphCSSFilename() + "?cacheStamp=" + timeStamp : "");
                editorData.put("paragraph_style_data", !paragraphStyles.isEmpty() ? co.getParagraphStyleData() : null);

                editorData.put("list_style_css", !listStyles.isEmpty() ? co.getListCSSFilename() + "?cacheStamp=" + timeStamp : "");
                editorData.put("list_style_data", !listStyles.isEmpty() ? co.getListStyleData() : null);

                editorData.put("apply_rotation", touchpointContext != null ? co.getIsFreeform() && touchpointContext.isNativeCompositionTouchpoint() : false);

                editorData.put("applies_templates", isCommunicationContent);
                editorData.put("applies_freeform", co.getIsFreeform());
                editorData.put("applies_forms",co.getAppliesForms());
                editorData.put("applies_barcodes",co.getAppliesBarcodes());
                editorData.put("applies_images", co.getAppliesImages());
                editorData.put("applies_tables", co.getAppliesTables());

                JSONObject contentMenuProperties = new JSONObject();
                contentMenuProperties.put("applied", (co.isTouchpointLocal() && co.getAppliesContentMenus()) || !co.isTouchpointLocal());
                contentMenuProperties.put("mode", co.isTouchpointLocal() && co.getAppliesContentMenus() ? "edit" : "content");
                editorData.put("content_menu", contentMenuProperties);

                editorData.put("support_unicode", co.getSupportsUnicode());
                editorData.put("canvas_dimensions", co.getCanvasDimensions());

                editorData.put("zone_rotation", co.getZoneRotation());

                editorData.put("zone_id", co.isMessage() ? co.getZone().getId() : 0);
                editorData.put("channel", co.isTouchpointLocal() ? co.getDocument().getConnectorConfiguration().getChannel().getId() : co.getAppliedChannels());
                editorData.put("connector", co.isTouchpointLocal() ? co.getDocument().getConnectorConfiguration().getConnector().getId(): co.getAppliedConnectors());

                editorData.put("is_exstream_html", co.getIsExstreamHtml());
                editorData.put("is_exstream_dxf", co.getIsExstreamDxf());
                editorData.put("is_exstream_runtime_dxf", co.getIsExstreamRunTimeDxf());

                editorData.put("can_edit_source", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

                JSONObject marcieFlags = SystemProperty.getMarcieKeyValues();
                editorData.put("marcie_flags", marcieFlags);

                editorData.put("is_dynamic_canvas", co.getIsSharedFreeform() || co.isSupportsTables());
                editorData.put("is_translation_compare", co.isInTranslationStep());
                editorData.put("apply_super_sub", co.supportsSuperSubScript());
                editorData.put("applies_connected_authoring", co.getAppliesConnectedAuthoring());
                editorData.put("is_global_context", co.getIsGlobalContentObject());
                editorData.put("content_object_id", co.getId());

                if (touchpointSelection != null )
                    editorData.put("variant_id", touchpointSelection.getParameterGroupTreeNode().getId());
                else if (co.isDynamicVariantEnabled())
                    editorData.put("variant_id", -1);

                JSONObject canvasBindings = new JSONObject();
                canvasBindings.put("max_width","#canvasMaxWidth_"+contentObjectId);
                canvasBindings.put("max_height","#canvasMaxHeight_"+contentObjectId);
                canvasBindings.put("trim_width","#canvasTrimWidth_"+contentObjectId);
                canvasBindings.put("trim_height","#canvasTrimHeight_"+contentObjectId);
                editorData.put("dynamic_canvas_bindings", canvasBindings);

                editorData.put("enforce_max_height", false);

                returnObj.put("editor_data", editorData);
                returnObj.put("editor_context",co.getIsEmailContentDelivery() || co.getIsWebDelivery() ? "digital": "print");

                returnObj.put("canvas_max_width", DecimalValueUtil.dehydrate(co.getCanvasMaxWidth(ContentObject.DATA_TYPE_WORKING)) );
                returnObj.put("canvas_max_height", DecimalValueUtil.dehydrate(co.getCanvasMaxHeight(ContentObject.DATA_TYPE_WORKING)) );
                returnObj.put("canvas_trim_width", DecimalValueUtil.dehydrate(co.getCanvasTrimWidth(ContentObject.DATA_TYPE_WORKING)) );
                returnObj.put("canvas_trim_height", DecimalValueUtil.dehydrate(co.getCanvasTrimHeight(ContentObject.DATA_TYPE_WORKING)) );
                returnObj.put("content_object_id", co.getId());

            } catch (Exception e) {
                returnObj.put("error", true);
                returnObj.put("message", e.getMessage());
            }

        }

        if (debugMode)
            log.error("AsyncEditorDataController: get editor data JSON: " + (new Date().getTime() - startTime));

        return returnObj;
    }

    private long getLocaleId (HttpServletRequest request) {
        long localeId = ServletRequestUtils.getLongParameter(request, REQ_PARM_LOCALE_ID, -1);
        if (localeId == 0)
            localeId = UserUtil.getCurrentLanguageLocaleContext().getId();
        else if (localeId < 0)
            localeId = MessagepointLocale.getDefaultSystemLanguageLocale().getId();
        return localeId;
    }

}
