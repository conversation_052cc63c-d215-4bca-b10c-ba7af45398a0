package com.prinova.messagepoint.controller;

import java.util.ArrayList;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractFormController;

public class MessagepointController extends AbstractFormController {
    final protected String REQUEST_PARAM_CUSTOM_FORM_SOURCE         = "cfSource";
    final protected String REQUEST_PARAM_SAVE_SUCCESS               = "saveSuccess";

    final static protected String SUBMIT_PARAM_SUBMIT_TYPE          = "submittype";
    final static protected String SUBMIT_PARAM_CANCEL 				= "cancel";
    final static protected String SUBMIT_PARAM_CANCEL_AND_EXIT 	    = "cancelandexit";
    final static protected String SUBMIT_PARAM_SAVE 				= "save";
    final static protected String SUBMIT_PARAM_SAVE_AND_STAY 		= "saveandstay";
    final static protected String SUBMIT_PARAM_SAVE_AND_ADD 		= "saveandadd";
    final static protected String SUBMIT_PARAM_SAVE_AND_VIEW 	    = "saveandview";
    final static protected String SUBMIT_PARAM_SAVE_AND_GOBACK 	    = "saveandgoback";
    final static protected String SUBMIT_PARAM_SAVE_AND_GOTOLIST	= "saveandgotolist";

    final private String SESSION_PARAM_ORIGIN_FORM              = "originForm";

    private String formView;
    private String successView;

    public MessagepointController() {
    }

    public final void setFormView(String formView) {
        this.formView = formView;
    }

    public final String getFormView() {
        return this.formView;
    }

    public final void setSuccessView(String successView) {
        this.successView = successView;
    }

    public final String getSuccessView() {
        return this.successView;
    }

    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        return this.showForm(request, response, errors, (Map)null);
    }

    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors, Map controlModel) throws Exception {
        return this.showForm(request, errors, this.getFormView(), controlModel);
    }

    protected void setOriginForm(HttpServletRequest request, String [] parametersToRemove) {

        Object sessionOriginFormObj = request.getSession().getAttribute(SESSION_PARAM_ORIGIN_FORM);
        String currentForm = request.getRequestURL().toString();
        ArrayList<String []> originForm = new ArrayList<>();

        if (sessionOriginFormObj != null) {

            originForm = (ArrayList)sessionOriginFormObj;

            int currentFormIndex = 0;
            int currentIndex = 0;

            for (int k = 0; k < originForm.size(); k++) {

                currentFormIndex = k;
                currentIndex = originForm.get(k)[0].indexOf(currentForm);

                if (currentIndex >= 0)
                    break;

            }

            if(currentIndex >= 0 && !originForm.isEmpty())
                originForm.remove(currentFormIndex);

        }

        String params = removeParameter(request.getQueryString(), SystemPropertyKeys.WebAppSecurity.CSRF_TOKEN_KEY, request.getParameter(SystemPropertyKeys.WebAppSecurity.CSRF_TOKEN_KEY));

        if (parametersToRemove != null) {

            for (int k = 0; k < parametersToRemove.length; k++) {

                params = removeParameter(params, parametersToRemove[0], request.getParameter(parametersToRemove[0]));

            }

        }

        String[] current = {currentForm, params};
        originForm.add(current);

        request.getSession().setAttribute(SESSION_PARAM_ORIGIN_FORM, originForm);

    }

    protected String[] getOriginForm(HttpServletRequest request, boolean removeLatest) {

        String [] currentForm = {request.getRequestURL().toString(), removeParameter(request.getQueryString(), SystemPropertyKeys.WebAppSecurity.CSRF_TOKEN_KEY, request.getParameter(SystemPropertyKeys.WebAppSecurity.CSRF_TOKEN_KEY))};
        Object sessionOriginFormObj = request.getSession().getAttribute(SESSION_PARAM_ORIGIN_FORM);
        String [] latestOriginForm = currentForm;
        int sessionSize;

        if (sessionOriginFormObj != null) {

            ArrayList<String []> sessionOriginForm = (ArrayList)sessionOriginFormObj;
            sessionSize = sessionOriginForm.size();

            if (sessionSize > 0) {

                if(sessionOriginForm.get(sessionSize - 1)[0].equals(currentForm[0])) {

                    if(sessionSize > 1)
                        sessionOriginForm.remove(sessionSize - 1);

                }

                latestOriginForm = sessionOriginForm.get(sessionOriginForm.size() - 1);

                if (removeLatest)
                    sessionOriginForm.remove(sessionOriginForm.size() - 1);

                request.getSession().setAttribute(SESSION_PARAM_ORIGIN_FORM, sessionOriginForm);

            }

        }

        return latestOriginForm;

    }

    protected Map referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        return this.referenceData(request);
    }

    protected Map referenceData(HttpServletRequest request) throws Exception {
        return null;
    }

    protected ModelAndView processFormSubmission(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
        if (errors.hasErrors()) {
            if (this.logger.isDebugEnabled()) {
                this.logger.debug("Data binding errors: " + errors.getErrorCount());
            }

            return this.showForm(request, response, errors);
        } else if (this.isFormChangeRequest(request, command)) {
            this.logger.debug("Detected form change request -> routing request to onFormChange");
            this.onFormChange(request, response, command, errors);
            return this.showForm(request, response, errors);
        } else {
            this.logger.debug("No errors -> processing submit");
            return this.onSubmit(request, response, command, errors);
        }
    }

    protected boolean suppressValidation(HttpServletRequest request, Object command) {
        return this.isFormChangeRequest(request, command);
    }

    private boolean isFormChangeRequest(HttpServletRequest request, Object command) {
        return this.isFormChangeRequest(request);
    }

    private boolean isFormChangeRequest(HttpServletRequest request) {
        return false;
    }

    private void onFormChange(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
        this.onFormChange(request, response, command);
    }

    private void onFormChange(HttpServletRequest request, HttpServletResponse response, Object command) throws Exception {
    }

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
        return this.onSubmit(command, errors);
    }

    protected ModelAndView onSubmit(Object command, BindException errors) throws Exception {
        ModelAndView mv = this.onSubmit(command);
        if (mv != null) {
            return mv;
        } else if (this.getSuccessView() == null) {
            throw new ServletException("successView isn't set");
        } else {
            return new ModelAndView(this.getSuccessView(), errors.getModel());
        }
    }

    protected ModelAndView onSubmit(Object command) throws Exception {
        this.doSubmitAction(command);
        return null;
    }

    protected void doSubmitAction(Object command) throws Exception {
    }

    private String removeParameter(String query, String parameter, String value) {

        String tkParam = parameter + "=" + value;
        String params = query.replace(tkParam + "&", "");
        params = params.replace(tkParam, "");

        int length = params.length();

        if(length > 0 && params.substring(length - 1, length).equals("&"))
            params = params.substring(0, length - 1);

        return params;

    }
}
