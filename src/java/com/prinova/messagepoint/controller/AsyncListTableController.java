package com.prinova.messagepoint.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.DataTableEvents;
import com.prinova.messagepoint.controller.contentintelligence.AsyncContentCompareListWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardBrandDetailsWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardBrandWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardDuplicatesDetailsWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardDuplicatesWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardReadingDetailsWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardSentimentDetailsWrapper;
import com.prinova.messagepoint.controller.dashboards.AsyncGlobalDashboardTranslationAccuracyDetailsWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.DocumentSection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.metadata.MetadataFormItemDefinition;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.rationalizer.RationalizerQuery;
import com.prinova.messagepoint.model.wrapper.*;
import com.prinova.messagepoint.model.wrapper.elastic.AsyncRationalizerQueryWrapper;
import com.prinova.messagepoint.model.wrapper.elastic.dto.ExpandSharedContentsDto;
import com.prinova.messagepoint.model.wrapper.elastic.factory.AsyncRationalizerQueryWrapperFactory;
import com.prinova.messagepoint.model.wrapper.elastic.factory.AsyncRationalizerQueryWrapperParameters;
import com.prinova.messagepoint.platform.mprest.common.RestResourceType;
import com.prinova.messagepoint.util.FeatureFlag;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

public class AsyncListTableController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncListTableController.class);

    public static final int LIST_TABLE_TYPE_CONTENT_OBJECTS = 101;
    public static final int LIST_TABLE_TYPE_MESSAGES = 1;
    public static final int LIST_TABLE_TYPE_SELECTIONS = 2;
    public static final int LIST_TABLE_TYPE_EMBEDDED_CONTENT = 4;
    public static final int LIST_TABLE_TYPE_VARIABLE = 5;
    public static final int LIST_TABLE_TYPE_CONTENT_LIBRARY = 6;
    public static final int LIST_TABLE_TYPE_TARGET_GROUP = 7;
    public static final int LIST_TABLE_TYPE_TARGETING_RULE = 8;
    public static final int LIST_TABLE_TYPE_PARAGRAPH_STYLE = 9;
    public static final int LIST_TABLE_TYPE_COMMUNICATIONS = 10;
    public static final int LIST_TABLE_TYPE_ZONES = 11;
    public static final int LIST_TABLE_TYPE_TESTS = 12;
    public static final int LIST_TABLE_TYPE_REPORTS = 13;
    public static final int LIST_TABLE_TYPE_SIMULATIONS = 14;
    public static final int LIST_TABLE_TYPE_BRANCH = 15;
    public static final int LIST_TABLE_TYPE_USER = 16;
    public static final int LIST_TABLE_TYPE_POD = 17;
    public static final int LIST_TABLE_TYPE_AUDITING = 19;
    public static final int LIST_TABLE_TYPE_DATA_FILES = 20;
    public static final int LIST_TABLE_TYPE_DATA_RESOURCES = 21;
    public static final int LIST_TABLE_TYPE_PROJECT_SYNC_OBJECTS = 22;
    public static final int LIST_TABLE_TYPE_LOOKUP_TABLES = 23;
    public static final int LIST_TABLE_TYPE_METADATA_FORMS = 24;
    public static final int LIST_TABLE_TYPE_TASKS = 25;
    public static final int LIST_TABLE_TYPE_PROJECTS = 27;
    public static final int LIST_TABLE_TYPE_PROJECT_WORKFLOWS = 28;
    public static final int LIST_TABLE_TYPE_INSERTS = 29;
    public static final int LIST_TABLE_TYPE_INSERT_SCHEDS = 30;
    public static final int LIST_TABLE_TYPE_RATE_SHEETS = 31;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_DOCUMENTS = 32;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_QUERY = 33;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_QUERY_RESULT = 34;
    public static final int LIST_TABLE_TYPE_PRINT_STREAM_TAGS = 35;
    public static final int LIST_TABLE_TYPE_JOB_CENTER = 36;
    public static final int LIST_TABLE_TYPE_WORKFLOW_LIBRARY = 37;
    public static final int LIST_TABLE_TYPE_LIST_STYLE = 38;
    public static final int LIST_TABLE_TYPE_TEST_SUITE = 39;
    public static final int LIST_TABLE_TYPE_DESERVER = 40;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_CONTENT = 41;
    public static final int LIST_TABLE_TYPE_DICTIONARIES = 42;
    public static final int LIST_TABLE_CONTENT_COMPARE = 43;
    private static final int LIST_TABLE_TYPE_DELIVERY_EVENT = 44;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_DUPLCATES = 45;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_SIMILARITIES = 46;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_METADATA = 47;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_DUPLCATES_DETAILS = 48;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_SIMILARITIES_DETAILS = 49;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_METADATA_DETAILS = 50;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_SENTIMENT_DETAILS = 51;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_READING_DETAILS = 52;
    public static final int LIST_TABLE_TYPE_BRAND_PROFILE = 53;
    public static final int LIST_TABLE_TYPE_GLOBAL_SEARCH = 54;
    public static final int LIST_TABLE_TYPE_GLOBAL_DUPLCATES = 55;
    public static final int LIST_TABLE_TYPE_GLOBAL_DUPLCATES_DETAILS = 56;
    public static final int LIST_TABLE_TYPE_GLOBAL_SIMILARITIES = 57;
    public static final int LIST_TABLE_TYPE_GLOBAL_SIMILARITIES_DETAILS = 58;
    public static final int LIST_TABLE_TYPE_GLOBAL_SENTIMENT_DETAILS = 59;
    public static final int LIST_TABLE_TYPE_GLOBAL_READING_DETAILS = 60;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_BRAND = 61;
    public static final int LIST_TABLE_TYPE_RATIONALIZER_BRAND_DETAILS = 62;
    public static final int LIST_TABLE_TYPE_GLOBAL_BRAND = 63;
    public static final int LIST_TABLE_TYPE_GLOBAL_BRAND_DETAILS = 64;
    public static final int LIST_TABLE_RATIONALIZER_SHARED_CONTENT = 65;
    public static final int LIST_TABLE_RATIONALIZER_CONSOLIDATE = 66;
    public static final int LIST_TABLE_TYPE_CHANGES = 67;
    public static final int LIST_TABLE_TYPE_TAG_CLOUD = 68;
    public static final int LIST_TABLE_TYPE_SERVICES = 69;
    public static final int LIST_TABLE_TYPE_TEXT_TRANSFORMATION_PROFILE = 70;
    public static final int LIST_TABLE_TYPE_CONTENT_ASSISTANT = 71;
    public static final int LIST_TABLE_TYPE_GLOBAL_TRANSLATION_ACCURACY_DETAILS = 72;
    public static final int LIST_TABLE_TYPE_ENTITIES = 73;

    public static final String PARAM_SELECTION_ID = "touchpointSelectionId";
    public static final String PARAM_DOCUMENT_ID = "documentId";
    public static final String PARAM_OTHER_DOCUMENT_ID = "otherDocumentId";
    public static final String PARAM_COLLECTION_ID = "collectionId";
    public static final String PARAM_USER_ID = "userId";
    public static final String PARAM_INSTANCE_ID = "instanceId";
    public static final String PARAM_TP_IDS = "tpIds";
    public static final String PARAM_TARGET_IDS = "targetIds";
    public static final String PARAM_TARGET_STATUS_IDS = "statusIds";
    public static final String PARAM_GLOBAL_SEARCH_TYPE = "globalSearchType";
    public static final String PARAM_IS_EXACT_MATCH = "isExactMatch";
    public static final String PARAM_IS_NEW_SEARCH = "isNewSearch";

    public static final String PARAM_PARENT_ID = "parentId";
    public static final String PARAM_PARENT_INSTANCE_ID = "parentInstanceId";
    public static final String PARAM_OTHER_PARENT_ID = "otherParentId";
    public static final String PARAM_OTHER_PARENT_INSTANCE_ID = "otherParentInstanceId";

    public static final String PARAM_DOCSECTION_ID = "docSectionId";
    public static final String PARAM_DOCZONE_ID = "docZoneId";
    public static final String PARAM_ASSIGNEDTO_ID = "assignedToId";
    public static final String PARAM_MESSAGES_STATUS_FILTER_ID = "messagesStatusFilterId";
    public static final String PARAM_MESSAGES_ASSIGNMENT_FILTER_ID = "messagesAssignmentFilterId";
    public static final String PARAM_CURRENT_SELECTION_STATUS_FILTER_ID = "currentSelectionStatusFilterId";
    public static final String PARAM_SELECTIONS_STATUS_FILTER_ID = "selectionsStatusFilterId";
    public static final String PARAM_SELECTIONS_ASSIGNMENT_FILTER_ID = "selectionsAssignmentFilterId";
    public static final String PARAM_CONTENT_TYPE_FILTER_ID = "contentTypeFilterId";
    public static final String PARAM_EMBEDDED_CONTENT_STATUS_FILTER_ID = "embeddedContentsStatusFilterId";
    public static final String PARAM_EMBEDDED_CONTENT_ASSIGNMENT_FILTER_ID = "embeddedContentsAssignmentFilterId";
    public static final String PARAM_CONTENT_LIBRARY_STATUS_FILTER_ID = "contentLibrarysStatusFilterId";
    public static final String PARAM_CONTENT_LIBRARY_ASSIGNMENT_FILTER_ID = "contentLibrarysAssignmentFilterId";
    public static final String PARAM_COMMUNICATIONS_STATUS_FILTER_ID = "communicationsStatusFilterId";
    public static final String PARAM_COMMUNICATIONS_ASSIGNMENT_FILTER_ID = "communicationsAssignmentFilterId";
    public static final String PARAM_ZONES_CONTENT_TYPE_FILTER_ID = "zonesContentTypeFilterId";
    public static final String PARAM_ZONES_ENABLED_FILTER_ID = "zonesEnabledFilterId";
    public static final String PARAM_TESTS_ASSIGNMENT_FILTER_ID = "testsAssignmentFilterId";
    public static final String PARAM_TESTS_STATUS_FILTER_ID = "testsStatusFilterId";
    public static final String PARAM_USERS_STATUS_FILTER = "usersStatusFilter";
    public static final String PARAM_SERVICES_STATUS_FILTER = "servicesStatusFilter";
    public static final String PARAM_REPORTS_ASSIGNMENT_FILTER_ID = "reportsAssignmentFilterId";
    public static final String PARAM_REPORTS_TYPE_FILTER_ID = "reportsTypeFilterId";
    public static final String PARAM_REPORTS_STATUS_FILTER_ID = "reportsStatusFilterId";
    public static final String PARAM_SIMULATIONS_ASSIGNMENT_FILTER_ID = "simulationsAssignmentFilterId";
    public static final String PARAM_SIMULATIONS_STATUS_FILTER_ID = "simulationsStatusFilterId";
    public static final String PARAM_DISPLAY_MODE = "displayMode";
    public static final String PARAM_GLOBAL_CONTEXT = "globalContext";
    public static final String PARAM_BRANCH_ID = "branchId";
    public static final String PARAM_NODE_ID = "nodeId";
    public static final String PARAM_POD_ID = "podId";
    public static final String PARAM_DESERVER_ID = "deServerId";
    public static final String PARAM_AUDITING_EVENT_TYPE_ID = "auditingEventTypeId";
    public static final String PARAM_AUDITING_ACTION_TYPE_ID = "auditingActionTypeId";
    public static final String PARAM_AUDITING_DATE_SELECT_TYPE_ID = "auditingDateSelectTypeId";
    public static final String PARAM_AUDITING_DATE_FROM = "auditingDateFrom";
    public static final String PARAM_AUDITING_DATE_TO = "auditingDateTo";
    public static final String PARAM_SYNC_OBJECT_FILTER_ID = "syncObjectFilterId";
    public static final String PARAM_LOOKUP_TABLE_STATUS_FILTER_ID = "lookupTableStatusFilterId";
    public static final String PARAM_LOOKUP_TABLE_ASSIGNMENT_FILTER_ID = "lookupTableAssignmentFilterId";
    public static final String PARAM_LOCAL_CONTEXT = "localContext";
    public static final String PARAM_TASKS_TYPE_FILTER_ID = "tasksTypeFilterId";
    public static final String PARAM_TASKS_STATUS_FILTER_ID = "tasksStatusFilterId";
    public static final String PARAM_TASKS_REPORTER_FILTER_IDS = "tasksReporterFilterIds";
    public static final String PARAM_TASKS_OBJECT_ASSIGNEE_FILTER_IDS = "tasksObjectAssigneeFilterIds";
    public static final String PARAM_TASKS_ASSIGNMENT_FILTER_ID = "tasksAssignmentFilterId";
    public static final String PARAM_TASKS_ITEM_FILTER_IDS = "tasksItemFilterIds";
    public static final String PARAM_TASKS_LOCALE_FILTER_IDS = "tasksLocaleFilterIds";
    public static final String PARAM_TASKS_CREATED_DATE_SELECT_TYPE_ID = "tasksCreatedDateSelectTypeId";
    public static final String PARAM_TASKS_CREATED_FROM_DATE = "tasksCreatedFromDate";
    public static final String PARAM_TASKS_CREATED_TO_DATE = "tasksCreatedToDate";
    public static final String PARAM_TAG_CLOUD_TOUCHPOINT_FILTER_IDS = "tagCloudTouchpointFilterIds";
    public static final String PARAM_TAG_CLOUD_OBJECT_TYPE_FILTER_IDS = "tagCloudObjectTypeFilterIds";
    public static final String PARAM_CHANGES_STATUS_FILTER_ID = "changesStatusFilterId";
    public static final String PARAM_CHANGES_USER_FILTER_IDS = "changesUserFilterIds";
    public static final String PARAM_CHANGES_TOUCHPOINT_FILTER_IDS = "changesTouchpointFilterIds";
    public static final String PARAM_CHANGES_ASSIGNMENT_FILTER_ID = "changesAssignmentFilterId";
    public static final String PARAM_CHANGES_ITEM_FILTER_IDS = "changesItemFilterIds";

    public static final String PARAM_CHANGES_TYPE_FILTER_IDS = "changesTypeFilterIds";
    public static final String PARAM_CHANGES_CREATED_DATE_SELECT_TYPE_ID = "changesCreatedDateSelectTypeId";
    public static final String PARAM_CHANGES_CREATED_FROM_DATE = "changesCreatedFromDate";
    public static final String PARAM_CHANGES_CREATED_TO_DATE = "changesCreatedToDate";
    public static final String PARAM_PROJECTS_ASSIGNMENT_FILTER_ID = "projectsAssignmentFilterId";
    public static final String PARAM_PROJECTS_STATUS_FILTER_ID = "projectsStatusFilterId";
    public static final String PARAM_PROJECT_ID = "projectId";
    public static final String PARAM_INSERTS_STATUS_FILTER_ID = "insertsStatusFilterId";
    public static final String PARAM_INSERTS_ASSIGNMENT_FILTER_ID = "insertsAssignmentFilterId";
    public static final String PARAM_INSERT_SCHEDS_STATUS_FILTER_ID = "insertSchedsStatusFilterId";
    public static final String PARAM_INSERT_SCHEDS_ASSIGNMENT_FILTER_ID = "insertSchedsAssignmentFilterId";
    public static final String PARAM_INSERT_SCHEDS_DATE_TYPE_FILTER_ID = "insertSchedsDateTypeFilterId";
    public static final String PARAM_RATE_SHEETS_DATE_TYPE_FILTER_ID = "rateSheetsDateTypeFilterId";
    public static final String PARAM_IS_SMART_CONTENT_LIST = "listSmartContent";
    public static final String PARAM_IS_TOUCHPOINT_LOCAL = "isTouchpointLocal";
    public static final String PARAM_IS_SHARED_CANVAS = "isSharedCanvas";
    public static final String PARAM_DATE_SELECT = "dateSelect";
    public static final String PARAM_RATIONALIZER_APP_ID = "rationalizerApplicationId";
    public static final String PARAM_RATIONALIZER_QUERY_ID = "rationalizerQueryId";
    public static final String PARAM_RATIONALIZER_COMPARISON_CONTENT_ID = "rationalizerComparisonContentId";
    public static final String PARAM_RATIONALIZER_DOC_FORM_ITEM_ID = "rationalizerDocFormItemId";
    public static final String PARAM_RATIONALIZER_CONTENT_LIST_ASSIGNMENT_FILTER_ID = "rationalizerContentListAssignmentFilterId";
    public static final String PARAM_RATIONALIZER_CONTENT_LIST_CONTENT_STATUS_ID = "rationalizerContentListContentStatusFilterId";
    public static final String PARAM_RATIONALIZER_CONTENT_LIST_STATE_ID = "rationalizerContentListStateFilterId";
    public static final String PARAM_PRINT_STREAM_TAG_STATUS_FILTER_ID = "printStreamTagsStatusFilterId";
    public static final String PARAM_PRINT_STREAM_TAG_DATE_TYPE_FILTER_ID = "printStreamTagsDateTypeFilterId";
    public static final String PARAM_DICTIONARY_ID = "dictionaryId";
    public static final String PARAM_DICTIONARY_TYPE_FILTER_ID = "dictionaryTypeFilterId";
    public static final String PARAM_DICTIONARY_STATUS_FILTER_ID = "dictionaryStatusFilterId";
    public static final String PARAM_JOB_EVENT_TYPE_FILTER_ID = "jobEventTypeId";
    public static final String PARAM_RATIONALIZER_FILTER_PREFIX = "queryFilter_";
    public static final String PARAM_CONTEXT = "context";
    public static final String PARAM_RANGE_START = "rangeStart";
    public static final String PARAM_RANGE_END = "rangeEnd";
    public static final String PARAM_TEST_SUITE_ID = "testSuiteId";
    public static final String PARAM_TEST_SUITE_ASSIGNMENT_FILTER_ID = "testSuiteAssignmentFilterId";
    public static final String PARAM_PLACEHOLDER = "placeholder";
    public static final String PARAM_ACCESS_TYPE = "listAccessType";
    public static final String PARAM_IS_SNAP_OFF = "isSnapOff";
    public static final String PARAM_LOWER_THRESHOLD = "lowerThreshold";
    public static final String METADATA_POINT_OF_INTEREST_ID = "metadataPointOfInterestId";
    public static final String SELECTED_SENTIMENT_ID = "selectedSentimentId";
    public static final String SELECTED_TRANSLATION_ACCURACY_ID = "selectedTranslationId";
    public static final String SELECTED_LANGUAGE_CODE = "selectedLangId";
    public static final String SELECTED_STATUS_CODE = "selectedStatusId";
    public static final String SELECTED_CONTENT_CODE = "selectedContentTypeId";
    public static final String SELECTED_READING_ID = "selectedReadingId";
    public static final String SELECTED_ITEM_ID = "selectedItemId";
    public static final String SELECTED_BRAND_ITEM_ID = "selectedBrandItemId";
    public static final String PARAM_SHOW_MARKUP = "showMarkup";
    public static final String PARAM_FILTER_SELECTION = "filterSelection";
    public static final String PARAM_SHARED_CONTENT_SORT_ID = "sharedContentSortId";
    public static final String PARAM_RATIONALIZER_CONTENT_ID = "rationalizerContentId";
    public static final String PARAM_RATIONALIZER_CONSOLIDATION_TAB_ID = "rationalizerConsolidationTabId";
    public static final String SOURCE_DASHBOARD_COMPARE_SELECTION = "sourceDashboardCompareSelection";
    public static final String TARGET_DASHBOARD_COMPARE_SELECTION = "targetDashboardCompareSelection";
    public static final String PARAM_CONTENT_OBJECT_ID = "contentObjectId";

    public static final String PARAM_LANGUAGESSELECTED = "languagesSelected";

    //public static final String PARAM_SEARCH_NAME			= "searchName";
    public static final String PARAM_LIST_TABLE_TYPE = "listTableType";
    public static final String PARAM_LIST_TABLE_SUB_TYPE = "listTableSubType";
    public static final String PARAM_ECHO = "sEcho";
    public static final String PARAM_SEARCH_NAME = "sSearch";
    public static final String PARAM_SEARCH_META = "searchMeta";
    public static final String PARAM_SORT_BY = "sortBy";
    public static final String PARAM_SORT_TYPE = "sortType";
    public static final String PARAM_PAGE_SIZE = "iDisplayLength";
    public static final String PARAM_DISPLAY_START = "iDisplayStart";
    public static final String PARAM_LIST_DISPLAY_FMT = "listDisplayFormat";
    public static final String PARAM_ADVANCED = "advanced";
    public static final String PARAM_SYNC_FROM_ORIGIN = "syncFromOrigin";
    public static final String PARAM_SYNC_MULTI_WAY = "syncMultiWay";
    public static final String PARAM_SYNC_COMPARE_ACTIVE_COPY_ONLY = "compareActiveCopyOnly";
    public static final String PARAM_CARD_SORT_TYPE = "cardSort";
    public static final String PARAM_CONTENT_ID = "contentId";

    public static final String DISPLAY_MODE_FULL = "full";
    public static final String DISPLAY_MODE_LIMITED = "limited";

    public static final String LIST_DISPLAY_FORMAT_NAME = "name";
    public static final String LIST_DISPLAY_FORMAT_ICON = "icon";

    public static final String PARAM_OBJECTS_RESOURCE_TYPE_CODE = "resourceTypeCode";
    public static final String PARAM_OBJECTS_RESOURCE_GUIDS = "resourceGUIDs";
    public static final String PARAM_OBJECTS_RESOURCE_IDS = "resourceIds";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();
        out.write(getListTableBytes(request));
        out.flush();
        out.close();
        return null;
    }

    private byte[] getListTableBytes(HttpServletRequest request) throws Exception {

        String echo = ServletRequestUtils.getStringParameter(request, PARAM_ECHO);
        int listType = ServletRequestUtils.getIntParameter(request, PARAM_LIST_TABLE_TYPE, LIST_TABLE_TYPE_CONTENT_OBJECTS);
        String listSubType = ServletRequestUtils.getStringParameter(request, PARAM_LIST_TABLE_SUB_TYPE, null);
        long selectionId = ServletRequestUtils.getLongParameter(request, PARAM_SELECTION_ID, -1L);
        long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1L);
        long otherDocumentId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_DOCUMENT_ID, -1L);
        long collectionId = ServletRequestUtils.getLongParameter(request, PARAM_COLLECTION_ID, -1L);
        long docSectionId = ServletRequestUtils.getLongParameter(request, PARAM_DOCSECTION_ID, -1L);
        long docZoneId = ServletRequestUtils.getLongParameter(request, PARAM_DOCZONE_ID, -1L);
        long branchId = ServletRequestUtils.getLongParameter(request, PARAM_BRANCH_ID, -1L);
        long nodeId = ServletRequestUtils.getLongParameter(request, PARAM_NODE_ID, -1L);
        long podId = ServletRequestUtils.getLongParameter(request, PARAM_POD_ID, -1L);
        long userId = ServletRequestUtils.getLongParameter(request, PARAM_USER_ID, -1L);
        long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_APP_ID, -1L);
        long rationalizerQueryId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_QUERY_ID, -1L);
        String rationalizerComparisonContentGuid = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_COMPARISON_CONTENT_ID, "");
        String rationalizerSelectionBranchFormItemIds = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_DOC_FORM_ITEM_ID, "-9");
        long testSuiteId = ServletRequestUtils.getLongParameter(request, PARAM_TEST_SUITE_ID, -1L);
        long deServerId = ServletRequestUtils.getLongParameter(request, PARAM_DESERVER_ID, -1L);
        long instanceId = ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -1L);
        long metadataPointOfInterestId = ServletRequestUtils.getLongParameter(request, METADATA_POINT_OF_INTEREST_ID, -1L);
        long selectedSentimentId = ServletRequestUtils.getLongParameter(request, SELECTED_SENTIMENT_ID, -1L);
        long selectedTranslationAccuracyId = ServletRequestUtils.getLongParameter(request, SELECTED_TRANSLATION_ACCURACY_ID, -1L);
        String selectedLanguageCode = ServletRequestUtils.getStringParameter(request, SELECTED_LANGUAGE_CODE, "en");
        long selectedStatusCode = ServletRequestUtils.getLongParameter(request, SELECTED_STATUS_CODE, 0L);
        String selectedContentTypleFilterValue = ServletRequestUtils.getStringParameter(request, SELECTED_CONTENT_CODE, "");
        long selectedReadingId = ServletRequestUtils.getLongParameter(request, SELECTED_READING_ID, -1L);
        long selectedItemId = ServletRequestUtils.getLongParameter(request, SELECTED_ITEM_ID, -1L);
        long selectedBrandItemId = ServletRequestUtils.getLongParameter(request, SELECTED_BRAND_ITEM_ID, -1L);
        String rationalizerContentGuid = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_CONTENT_ID, "");
        long rationalizerConsolidationTabId = ServletRequestUtils.getLongParameter(request, PARAM_RATIONALIZER_CONSOLIDATION_TAB_ID, -1L);

        int messagesStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_MESSAGES_STATUS_FILTER_ID, -1);
        int messagesAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_MESSAGES_ASSIGNMENT_FILTER_ID, -1);
        int testSuiteAssignmentFilterId = ServletRequestUtils.getIntParameter(request, PARAM_TEST_SUITE_ASSIGNMENT_FILTER_ID, -1);
        int currentSelectionStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CURRENT_SELECTION_STATUS_FILTER_ID, -1);
        int selectionsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_SELECTIONS_STATUS_FILTER_ID, -1);
        int selectionsAssignmentFilterId = ServletRequestUtils.getIntParameter(request, PARAM_SELECTIONS_ASSIGNMENT_FILTER_ID, -1);
        int contentTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CONTENT_TYPE_FILTER_ID, -1);
        int embeddedContentsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_EMBEDDED_CONTENT_STATUS_FILTER_ID, -1);
        int embeddedContentsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_EMBEDDED_CONTENT_ASSIGNMENT_FILTER_ID, -1);
        int contentLibrarysStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CONTENT_LIBRARY_STATUS_FILTER_ID, -1);
        int contentLibrarysAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CONTENT_LIBRARY_ASSIGNMENT_FILTER_ID, -1);
        int communicationsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_COMMUNICATIONS_STATUS_FILTER_ID, -1);
        int communicationsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_COMMUNICATIONS_ASSIGNMENT_FILTER_ID, -1);
        int zonesContentTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_ZONES_CONTENT_TYPE_FILTER_ID, -1);
        int zonesEnabledFilterId = ServletRequestUtils.getIntParameter(request, PARAM_ZONES_ENABLED_FILTER_ID, -1);
        int testsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_TESTS_STATUS_FILTER_ID, -1);
        String usersStatusFilter = ServletRequestUtils.getStringParameter(request, PARAM_USERS_STATUS_FILTER, "");
        String servicesStatusFilter = ServletRequestUtils.getStringParameter(request, PARAM_SERVICES_STATUS_FILTER, "");
        int testsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_TESTS_ASSIGNMENT_FILTER_ID, -1);
        int reportsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_REPORTS_STATUS_FILTER_ID, -1);
        int reportsAssignmentFilterId = ServletRequestUtils.getIntParameter(request, PARAM_REPORTS_ASSIGNMENT_FILTER_ID, -1);
        int reportsTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_REPORTS_TYPE_FILTER_ID, -1);
        int simulationsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_SIMULATIONS_ASSIGNMENT_FILTER_ID, -1);
        int simulationsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_SIMULATIONS_STATUS_FILTER_ID, -1);
        int auditingEventTypeId = ServletRequestUtils.getIntParameter(request, PARAM_AUDITING_EVENT_TYPE_ID, -1);
        int auditingActionTypeId = ServletRequestUtils.getIntParameter(request, PARAM_AUDITING_ACTION_TYPE_ID, -1);
        int auditingDateSelectTypeId = ServletRequestUtils.getIntParameter(request, PARAM_AUDITING_DATE_SELECT_TYPE_ID, -1);
        int lookupTableStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_LOOKUP_TABLE_STATUS_FILTER_ID, -1);
        int lookupTableAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_LOOKUP_TABLE_ASSIGNMENT_FILTER_ID, -1);
        int tasksTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_TASKS_TYPE_FILTER_ID, -1);
        long tasksStatusFilterId = ServletRequestUtils.getLongParameter(request, PARAM_TASKS_STATUS_FILTER_ID, -1);
        int tasksAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_TASKS_ASSIGNMENT_FILTER_ID, -1);
        String tasksReporterFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_REPORTER_FILTER_IDS, "");
        String tasksObjectAssigneeFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_OBJECT_ASSIGNEE_FILTER_IDS, "");
        String tasksItemFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_ITEM_FILTER_IDS, "");
        String tasksLocaleFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_LOCALE_FILTER_IDS, "");
        int tasksCreatedDateSelectTypeId = ServletRequestUtils.getIntParameter(request, PARAM_TASKS_CREATED_DATE_SELECT_TYPE_ID, -1);
        String tasksCreatedFromDate = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_CREATED_FROM_DATE);
        String tasksCreatedToDate = ServletRequestUtils.getStringParameter(request, PARAM_TASKS_CREATED_TO_DATE);
        String tagCloudTouchpointFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TAG_CLOUD_TOUCHPOINT_FILTER_IDS);
        String tagCloudObjectTypeFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_TAG_CLOUD_OBJECT_TYPE_FILTER_IDS);
        int changesStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CHANGES_STATUS_FILTER_ID, -1);
        int changesAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_CHANGES_ASSIGNMENT_FILTER_ID, -1);
        String changesUserFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_USER_FILTER_IDS, "");
        String changesTouchpointFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_TOUCHPOINT_FILTER_IDS, "");
        String changesItemFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_ITEM_FILTER_IDS, "");
        String changesTypeFilterIds = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_TYPE_FILTER_IDS, "");
        int changesCreatedDateSelectTypeId = ServletRequestUtils.getIntParameter(request, PARAM_CHANGES_CREATED_DATE_SELECT_TYPE_ID, -1);
        String changesCreatedFromDate = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_CREATED_FROM_DATE);
        String changesCreatedToDate = ServletRequestUtils.getStringParameter(request, PARAM_CHANGES_CREATED_TO_DATE);
        int projectsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_PROJECTS_ASSIGNMENT_FILTER_ID, -1);
        int projectsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_PROJECTS_STATUS_FILTER_ID, -1);
        int projectId = ServletRequestUtils.getIntParameter(request, PARAM_PROJECT_ID, -1);
        int insertsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_INSERTS_STATUS_FILTER_ID, -1);
        int insertsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_INSERTS_ASSIGNMENT_FILTER_ID, -1);
        int insertSchedsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_INSERT_SCHEDS_STATUS_FILTER_ID, -1);
        int insertSchedsAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_INSERT_SCHEDS_ASSIGNMENT_FILTER_ID, -1);
        int insertSchedsDateTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_INSERT_SCHEDS_DATE_TYPE_FILTER_ID, -1);
        int rateSheetsDateTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_RATE_SHEETS_DATE_TYPE_FILTER_ID, -1);
        int printStreamTagsStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_PRINT_STREAM_TAG_STATUS_FILTER_ID, -1);
        int printStreamTagsDateTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_PRINT_STREAM_TAG_DATE_TYPE_FILTER_ID, -1);
        int rationalizerContentListAssignementFilterId = ServletRequestUtils.getIntParameter(request, PARAM_RATIONALIZER_CONTENT_LIST_ASSIGNMENT_FILTER_ID, -1);
        int rationalizerContentListContentStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_RATIONALIZER_CONTENT_LIST_CONTENT_STATUS_ID, -1);
        int rationalizerContentListStateFilterId = ServletRequestUtils.getIntParameter(request, PARAM_RATIONALIZER_CONTENT_LIST_STATE_ID, -1);
        int dictionaryId = ServletRequestUtils.getIntParameter(request, PARAM_DICTIONARY_ID, -1);
        int dictionaryTypeFilterId = ServletRequestUtils.getIntParameter(request, PARAM_DICTIONARY_TYPE_FILTER_ID, -1);
        int dictionaryStatusFilterId = ServletRequestUtils.getIntParameter(request, PARAM_DICTIONARY_STATUS_FILTER_ID, -1);
        String auditingDateFrom = ServletRequestUtils.getStringParameter(request, PARAM_AUDITING_DATE_FROM);
        String auditingDateTo = ServletRequestUtils.getStringParameter(request, PARAM_AUDITING_DATE_TO);
        int pageSize = ServletRequestUtils.getIntParameter(request, PARAM_PAGE_SIZE, 0);
        int displayStart = ServletRequestUtils.getIntParameter(request, PARAM_DISPLAY_START, 0);
        int pageIndex = pageSize == 0 ? 1 : Math.round(displayStart / pageSize) + 1;
        String sSearch = ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_NAME);
        String displayMode = ServletRequestUtils.getStringParameter(request, PARAM_DISPLAY_MODE, "limited");
        String listDisplayFormat = ServletRequestUtils.getStringParameter(request, PARAM_LIST_DISPLAY_FMT, LIST_DISPLAY_FORMAT_NAME);
        boolean isGlobalContext = ServletRequestUtils.getBooleanParameter(request, PARAM_GLOBAL_CONTEXT, false);
        boolean isAdvanced = ServletRequestUtils.getBooleanParameter(request, PARAM_ADVANCED, false);
        boolean isSyncFromOrigin = ServletRequestUtils.getBooleanParameter(request, PARAM_SYNC_FROM_ORIGIN, true);
        boolean isSyncMultiWay = ServletRequestUtils.getBooleanParameter(request, PARAM_SYNC_MULTI_WAY, false);
        boolean isSnapOff = ServletRequestUtils.getBooleanParameter(request, PARAM_IS_SNAP_OFF, false);
        Boolean showMarkup = ServletRequestUtils.getBooleanParameter(request, PARAM_SHOW_MARKUP, false);
        String filterSelection = ServletRequestUtils.getStringParameter(request, PARAM_FILTER_SELECTION, "");

        boolean isCompareActiveCopyOnly = ServletRequestUtils.getBooleanParameter(request, PARAM_SYNC_COMPARE_ACTIVE_COPY_ONLY, false);

        String tpIds = ServletRequestUtils.getStringParameter(request, PARAM_TP_IDS, "");
        String targetIds = ServletRequestUtils.getStringParameter(request, PARAM_TARGET_IDS, "");
        String statusIds = ServletRequestUtils.getStringParameter(request, PARAM_TARGET_STATUS_IDS, "");
        String globalSearchType = ServletRequestUtils.getStringParameter(request, PARAM_GLOBAL_SEARCH_TYPE, "Name");
        boolean isExactMatch = ServletRequestUtils.getBooleanParameter(request, PARAM_IS_EXACT_MATCH, true);
        boolean isNewSearch = ServletRequestUtils.getBooleanParameter(request, PARAM_IS_NEW_SEARCH, false);

        long parentId = ServletRequestUtils.getLongParameter(request, PARAM_PARENT_ID, -1L);
        long parentInstanceId = ServletRequestUtils.getLongParameter(request, PARAM_PARENT_INSTANCE_ID, -1L);
        long otherParentId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_PARENT_ID, -1L);
        long otherParentInstanceId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_PARENT_INSTANCE_ID, -1L);
        int syncObjectFilterId = ServletRequestUtils.getIntParameter(request, PARAM_SYNC_OBJECT_FILTER_ID, -1);
        Document document = Document.findById(documentId);
        int localContext = ServletRequestUtils.getIntParameter(request, PARAM_LOCAL_CONTEXT, 0);
        boolean isSharedCanvas = ServletRequestUtils.getBooleanParameter(request, PARAM_IS_SHARED_CANVAS, false);
        String dateSelect = ServletRequestUtils.getStringParameter(request, PARAM_DATE_SELECT);
        long jobEventTypeId = ServletRequestUtils.getLongParameter(request, PARAM_JOB_EVENT_TYPE_FILTER_ID, Long.MIN_VALUE);
        boolean filterUserJobs = ServletRequestUtils.getIntParameter(request, PARAM_ASSIGNEDTO_ID, -1) != -1;
        String context = ServletRequestUtils.getStringParameter(request, PARAM_CONTEXT, null);
        String placeholder = ServletRequestUtils.getStringParameter(request, PARAM_PLACEHOLDER, null);
        String listAccessType = ServletRequestUtils.getStringParameter(request, PARAM_ACCESS_TYPE, "default");
        int lowerThreshold = ServletRequestUtils.getIntParameter(request, PARAM_LOWER_THRESHOLD, 60);
        int contentId = ServletRequestUtils.getIntParameter(request, PARAM_CONTENT_ID, -1);
        int sharedContentSortId = ServletRequestUtils.getIntParameter(request, PARAM_SHARED_CONTENT_SORT_ID, -1);
        long contentObjectId = ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_OBJECT_ID, -1);
        int resourceTypeCode = ServletRequestUtils.getIntParameter(request, PARAM_OBJECTS_RESOURCE_TYPE_CODE, -1);
        String resourceGuids = ServletRequestUtils.getStringParameter(request, PARAM_OBJECTS_RESOURCE_GUIDS, "[]");
        String resourceIds = ServletRequestUtils.getStringParameter(request, PARAM_OBJECTS_RESOURCE_IDS, "[]");

        ExpandSharedContentsDto expandSharedContentsDto = ExpandSharedContentsDto.buildFromRationalizerQueryId(rationalizerQueryId);

        Boolean isMandatorySort = listType != LIST_TABLE_TYPE_RATIONALIZER_QUERY_RESULT;

        // Custom sort overrides when no sort is declared
        String defaultSort = null;
        String defaultSortOrder = null;
        boolean secondarySort = false;
        switch ( listType) {
            case LIST_TABLE_TYPE_TESTS: {
                defaultSort = "date";
                defaultSortOrder = "desc";
                break;
            }
            case LIST_TABLE_TYPE_TEST_SUITE: {
                defaultSort = "date";
                defaultSortOrder = "desc";
                break;
            }
            case LIST_TABLE_TYPE_CHANGES: {
                defaultSort = "datetime";
                defaultSortOrder = "asc";
                break;
            }
            case LIST_TABLE_TYPE_COMMUNICATIONS: {
                defaultSort = "created";
                defaultSortOrder = "desc";
                secondarySort = true;
                break;
            }
        }
        Map<String, String> orderByMap = getOrderedByMap(request, isMandatorySort, defaultSort, defaultSortOrder, secondarySort);
        // Retrieve the default selection
        if (selectionId == -1 && document != null) {
            if (document.isEnabledForVariation() && document.getMasterTouchpointSelection() != null) {
                selectionId = document.getMasterTouchpointSelection().getId();
            }
        }


        AnalyticsEvent<DataTableEvents> analyticsEvent = AnalyticsUtil.requestFor(DataTableEvents.AsyncListRequest)
                .add(DataTableEvents.Properties.PageIndex, () -> pageIndex)
                .add(DataTableEvents.Properties.PageSize, () -> pageSize)
                .add(DataTableEvents.Properties.SearchValue, () -> sSearch);

        try {
            AsyncAbstractListWrapper wrapper = null;
            switch (listType) {
                case LIST_TABLE_TYPE_CONTENT_OBJECTS: {

                    if (document != null && document.isAlternate()) {
                        documentId = document.getParent().getId();
                    }
                    if (docSectionId > 0) {
                        DocumentSection section = DocumentSection.findById(docSectionId);
                        if (section != null) {
                            if (section.isAlternate()) {
                                docSectionId = section.getParent().getId();
                            }
                        } else {
                            docSectionId = 0;
                        }
                    }
                    if (docZoneId > 0) {
                        Zone zone = Zone.findById(docZoneId);
                        if (zone != null) {
                            if (zone.isAlternate()) {
                                docZoneId = zone.getParent().getId();
                            }
                        } else {
                            docZoneId = 0;
                        }
                    }

                    wrapper = new AsyncContentObjectListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, selectionId, docSectionId, docZoneId, currentSelectionStatusFilterId, messagesStatusFilterId,
                            messagesAssignementFilterId, contentTypeFilterId, localContext, isSharedCanvas, isGlobalContext, placeholder, true,
                            listDisplayFormat, displayMode, LIST_TABLE_TYPE_MESSAGES, FeatureFlag.isEnabled(FeatureFlag.Features.MessageListHQLQuery, request), FeatureFlag.isEnabled(FeatureFlag.Features.AbortWorkflowOverride, request));
                    // analyticsEvent.setAction(DataTableEvents.List.Messages);
                    break;
                }
                case LIST_TABLE_TYPE_SELECTIONS: {
                    wrapper = new AsyncSelectionListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, selectionId, docSectionId, docZoneId, selectionsAssignmentFilterId, selectionsStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Selections);

                    break;
                }
                case LIST_TABLE_TYPE_EMBEDDED_CONTENT: {
                    wrapper = new AsyncContentObjectListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, selectionId, docSectionId, docZoneId, currentSelectionStatusFilterId, embeddedContentsStatusFilterId,
                            embeddedContentsAssignementFilterId, contentTypeFilterId, 4, isSharedCanvas, isGlobalContext, placeholder, true,
                            listDisplayFormat, displayMode, LIST_TABLE_TYPE_EMBEDDED_CONTENT, false, FeatureFlag.isEnabled(FeatureFlag.Features.AbortWorkflowOverride, request));
                    analyticsEvent.setAction(DataTableEvents.List.EmbeddedContent);
                    break;
                }
                case LIST_TABLE_TYPE_CONTENT_LIBRARY: {
                    wrapper = new AsyncContentObjectListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, selectionId, docSectionId, docZoneId, currentSelectionStatusFilterId,
                            contentLibrarysStatusFilterId, contentLibrarysAssignementFilterId, contentTypeFilterId, 8, isSharedCanvas, isGlobalContext, placeholder, true,
                            listDisplayFormat, displayMode, LIST_TABLE_TYPE_CONTENT_LIBRARY, false, FeatureFlag.isEnabled(FeatureFlag.Features.AbortWorkflowOverride, request));
                    analyticsEvent.setAction(DataTableEvents.List.ContentLibrary);
                    break;
                }
                case LIST_TABLE_TYPE_VARIABLE: {
                    wrapper = new AsyncVariableListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext);
                    analyticsEvent.setAction(DataTableEvents.List.Variable);
                    break;
                }
                case LIST_TABLE_TYPE_TARGET_GROUP: {
                    wrapper = new AsyncTargetGroupListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, isAdvanced);
                    analyticsEvent.setAction(DataTableEvents.List.TargetGroup);
                    break;
                }
                case LIST_TABLE_TYPE_TARGETING_RULE: {
                    wrapper = new AsyncTargetingRuleListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, isAdvanced);
                    analyticsEvent.setAction(DataTableEvents.List.TargetingRule);
                    break;
                }
                case LIST_TABLE_TYPE_PARAGRAPH_STYLE: {
                    wrapper = new AsyncParagraphStyleListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.ParagraphStyle);
                    break;
                }
                case LIST_TABLE_TYPE_COMMUNICATIONS: {
                    wrapper = new AsyncCommunicationsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, isAdvanced, communicationsStatusFilterId, communicationsAssignementFilterId, context);
                    analyticsEvent.setAction(DataTableEvents.List.Messages);
                    break;
                }
                case LIST_TABLE_TYPE_ZONES: {
                    wrapper = new AsyncZoneListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, docSectionId, docZoneId, zonesContentTypeFilterId, zonesEnabledFilterId, listSubType);
                    analyticsEvent.setAction(DataTableEvents.List.Zones);
                    break;
                }
                case LIST_TABLE_TYPE_TESTS: {
                    wrapper = new AsyncTestListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, collectionId, testSuiteId, testsAssignementFilterId, testsStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Tests);
                    break;
                }
                case LIST_TABLE_TYPE_REPORTS: {
                    wrapper = new AsyncReportListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, reportsAssignmentFilterId, reportsTypeFilterId, reportsStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Reports);
                    break;
                }
                case LIST_TABLE_TYPE_SIMULATIONS: {
                    wrapper = new AsyncSimulationListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, simulationsAssignementFilterId, simulationsStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Simulations);
                    break;
                }
                case LIST_TABLE_TYPE_BRANCH: {
                    wrapper = new AsyncBranchListWrapper(sSearch, orderByMap, pageSize, pageIndex, branchId);
                    analyticsEvent.setAction(DataTableEvents.List.Branch);
                    break;
                }
                case LIST_TABLE_TYPE_USER: {
                    wrapper = new AsyncUserListWrapper(sSearch, orderByMap, pageSize, pageIndex, usersStatusFilter);
                    analyticsEvent.setAction(DataTableEvents.List.User);
                    break;
                }
                case LIST_TABLE_TYPE_POD: {
                    wrapper = new AsyncPodListWrapper(sSearch, orderByMap, pageSize, pageIndex, podId, isAdvanced);
                    analyticsEvent.setAction(DataTableEvents.List.Pod);
                    break;
                }
                case LIST_TABLE_TYPE_AUDITING: {
                    wrapper = new AsyncAuditingListWrapper(sSearch, orderByMap, pageSize, pageIndex, auditingEventTypeId, auditingActionTypeId, branchId, nodeId, userId,
                            auditingDateSelectTypeId, auditingDateFrom, auditingDateTo, context);
                    analyticsEvent.setAction(DataTableEvents.List.Auditing);
                    break;
                }
                case LIST_TABLE_TYPE_DATA_FILES: {
                    wrapper = new AsyncDataFileListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, isGlobalContext);
                    analyticsEvent.setAction(DataTableEvents.List.DataFiles);
                    break;
                }
                case LIST_TABLE_TYPE_DATA_RESOURCES: {
                    wrapper = new AsyncDataResourcesListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, isGlobalContext);
                    analyticsEvent.setAction(DataTableEvents.List.DataResources);
                    break;
                }
                case LIST_TABLE_TYPE_PROJECT_SYNC_OBJECTS: {
                    if (document != null && document.isAlternate()) {
                        documentId = document.getParent().getId();
                    }
                    long documentIdFinal = documentId;
                    String languagesSelectedParam = ServletRequestUtils.getStringParameter(request, PARAM_LANGUAGESSELECTED, "");
                    Set<Long> selectedLanguages =
                            Arrays.stream(languagesSelectedParam.split(",")).filter(id->(id!=null) && (!id.isEmpty()) && (!id.equals("-1"))).map(id->MessagepointLocale.findById(Long.parseLong(id))).filter(ml->ml != null).map(MessagepointLocale::getId).collect(Collectors.toSet());
                    wrapper = AsyncLongOperationResultWrapper.handleLongOperationRequest(request,
                            () -> {
                                AsyncProjectSyncObjectListWrapper innerWrapper = new AsyncProjectSyncObjectListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentIdFinal, otherDocumentId, instanceId, isSyncFromOrigin, isSyncMultiWay, isCompareActiveCopyOnly, parentId, parentInstanceId, otherParentId, otherParentInstanceId, syncObjectFilterId, selectedLanguages, context);
                                innerWrapper.setsEcho(echo);
                                innerWrapper.setDisplayMode(displayMode);
                                innerWrapper.setListDisplayFormat(listDisplayFormat);
                                innerWrapper.init();
                                return innerWrapper;
                            });

                    ObjectMapper mapper = new ObjectMapper();
                    byte[] rs = mapper.writeValueAsBytes(wrapper);

                    analyticsEvent.setAction(DataTableEvents.List.ProjectSyncObjects);
                    return rs;
                }
                case LIST_TABLE_TYPE_LOOKUP_TABLES: {
                    wrapper = new AsyncLookupTablesListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, isGlobalContext, lookupTableStatusFilterId, lookupTableAssignementFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.LookupTables);
                    break;
                }
                case LIST_TABLE_TYPE_METADATA_FORMS: {
                    wrapper = new AsyncMetadataFormsListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.MetadataForms);
                    break;
                }
                case LIST_TABLE_TYPE_TASKS: {
                    wrapper = new AsyncTasksListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, rationalizerApplicationId, tasksTypeFilterId, tasksStatusFilterId, tasksReporterFilterIds, tasksObjectAssigneeFilterIds, tasksItemFilterIds, tasksLocaleFilterIds, projectId, tasksCreatedDateSelectTypeId, tasksCreatedFromDate, tasksCreatedToDate, isSnapOff);
                    analyticsEvent.setAction(DataTableEvents.List.Tasks);
                    break;
                }
                case LIST_TABLE_TYPE_CHANGES: {
                    wrapper = new AsyncChangesListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, rationalizerApplicationId, changesStatusFilterId, changesUserFilterIds, changesTouchpointFilterIds, changesItemFilterIds, changesTypeFilterIds, projectId, changesCreatedDateSelectTypeId, changesCreatedFromDate, changesCreatedToDate, isSnapOff);
                    analyticsEvent.setAction(DataTableEvents.List.Changes);
                    break;
                }
                case LIST_TABLE_TYPE_PROJECTS: {
                    wrapper = new AsyncProjectsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, projectsStatusFilterId, projectsAssignementFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Projects);
                    break;
                }
                case LIST_TABLE_TYPE_PROJECT_WORKFLOWS: {
                    wrapper = new AsyncProjectWorkflowsListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.ProjectWorkflows);
                    break;
                }
                case LIST_TABLE_TYPE_INSERTS: {
                    wrapper = new AsyncInsertsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, insertsStatusFilterId, insertsAssignementFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Inserts);
                    break;
                }
                case LIST_TABLE_TYPE_INSERT_SCHEDS: {
                    wrapper = new AsyncInsertSchedsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, insertSchedsStatusFilterId, insertSchedsAssignementFilterId, insertSchedsDateTypeFilterId, dateSelect);
                    analyticsEvent.setAction(DataTableEvents.List.InsertSchedules);
                    break;
                }
                case LIST_TABLE_TYPE_RATE_SHEETS: {
                    wrapper = new AsyncRateSheetsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, rateSheetsDateTypeFilterId, dateSelect);
                    analyticsEvent.setAction(DataTableEvents.List.RateSheets);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_DOCUMENTS: {
                    wrapper = new AsyncRationalizerDocumentsListWrapper(sSearch, orderByMap, pageSize, pageIndex, rationalizerApplicationId, rationalizerSelectionBranchFormItemIds);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerDocuments);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_CONTENT: {
                    wrapper = new AsyncRationalizerContentListWrapper(sSearch, orderByMap, pageSize, pageIndex, rationalizerApplicationId, rationalizerSelectionBranchFormItemIds, rationalizerContentListAssignementFilterId, rationalizerContentListContentStatusFilterId, rationalizerContentListStateFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerContent);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_QUERY: {
                    wrapper = new AsyncRationalizerQueryListWrapper(sSearch, orderByMap, pageSize, pageIndex, rationalizerApplicationId);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerQuery);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_QUERY_RESULT: {

                    int rangeStart = ServletRequestUtils.getIntParameter(request, PARAM_RANGE_START, -1);
                    int rangeEnd = ServletRequestUtils.getIntParameter(request, PARAM_RANGE_END, -1);

                    RationalizerQuery query = RationalizerQuery.findById(rationalizerQueryId);
                    final RationalizerContent comparisionContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(rationalizerComparisonContentGuid);

                    RationalizerApplication app = query != null ? query.getRationalizerApplication() :
                            (comparisionContent != null ? comparisionContent.computeRationalizerApplication() : null );
                    Map<Long, String> filterValuesMap = new HashMap<>();
                    if (app != null) {
                        for (MetadataFormItemDefinition currentItem : app.getQueryFilterFormItemDefinitions()) {
                            String currentValue = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_FILTER_PREFIX + currentItem.getId(), null);
                            if (currentValue != null && !currentValue.equalsIgnoreCase("-1")) {
                                filterValuesMap.put(currentItem.getId(), currentValue);
                            }
                        }
                    }
                    String duplicatesCountFilter = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_FILTER_PREFIX + "duplicatesCount", null);
                    String similaritiesCountFilter = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_FILTER_PREFIX + "similaritiesCount", null);

                    AsyncRationalizerQueryWrapperParameters queryWrapperParams = AsyncRationalizerQueryWrapperParameters.builder()
                            .withsSearch(sSearch)
                            .withOrderByMap(orderByMap)
                            .withPageSize(pageSize)
                            .withPageIndex(pageIndex)
                            .withRationalizerQueryId(rationalizerQueryId)
                            .withComparisonContentGuid(rationalizerComparisonContentGuid)
                            .withFilterValuesMap(filterValuesMap)
                            .withDuplicatesCountFilter(duplicatesCountFilter)
                            .withSimilaritiesCountFilter(similaritiesCountFilter)
                            .withRangeStart(rangeStart)
                            .withRangeEnd(rangeEnd)
                            .withListSubType(listSubType)
                            .withListAccessType(listAccessType)
                            .withQuery(query)
                            .withShowMarkup(showMarkup)
                            .withExpandSharedContentsDto(expandSharedContentsDto)
                            .build();
                    wrapper = new AsyncRationalizerQueryWrapperFactory().createQueryWrapper(queryWrapperParams);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerQueryResult);
                    break;
                }
                case LIST_TABLE_TYPE_PRINT_STREAM_TAGS: {
                    wrapper = new AsyncPrintStreamTagsListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, isGlobalContext, printStreamTagsStatusFilterId, printStreamTagsDateTypeFilterId, dateSelect);
                    analyticsEvent.setAction(DataTableEvents.List.PrintStreamTags);
                    break;
                }
                case LIST_TABLE_TYPE_JOB_CENTER: {
                    wrapper = new AsyncJobCenterListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, isGlobalContext, jobEventTypeId, filterUserJobs);
                    analyticsEvent.setAction(DataTableEvents.List.JobCenter);
                    break;
                }
                case LIST_TABLE_TYPE_WORKFLOW_LIBRARY: {
                    wrapper = new AsyncWorkflowLibraryListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext);
                    analyticsEvent.setAction(DataTableEvents.List.WorkflowLibrary);
                    break;
                }
                case LIST_TABLE_TYPE_LIST_STYLE: {
                    wrapper = new AsyncListStyleListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.ListStyle);
                    break;
                }
                case LIST_TABLE_TYPE_TEST_SUITE: {
                    wrapper = new AsyncTestSuiteListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, isGlobalContext, collectionId, testSuiteAssignmentFilterId, testsStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.TestSuite);
                    break;
                }
                case LIST_TABLE_TYPE_DESERVER: {
                    wrapper = new AsyncDEServerListWrapper(sSearch, orderByMap, pageSize, pageIndex, deServerId, isAdvanced, branchId);
                    analyticsEvent.setAction(DataTableEvents.List.DEServer);
                    break;
                }
                case LIST_TABLE_TYPE_DICTIONARIES: {
                    wrapper = new AsyncDictionaryListWrapper(sSearch, orderByMap, pageSize, pageIndex, dictionaryTypeFilterId, dictionaryStatusFilterId);
                    analyticsEvent.setAction(DataTableEvents.List.Dictionaries);
                    break;
                }
                case LIST_TABLE_CONTENT_COMPARE: {
                    wrapper = new AsyncContentCompareListWrapper(sSearch, orderByMap, pageSize, pageIndex, listSubType, isSnapOff, lowerThreshold, contentObjectId);
                    analyticsEvent.setAction(DataTableEvents.List.ContentCompare);
                    break;
                }
                case LIST_TABLE_TYPE_DELIVERY_EVENT: {
                    int deliveryEventTypeId = ServletRequestUtils.getIntParameter(request, "deliveryEventTypeFilterId", 0);
                    int enabled = ServletRequestUtils.getIntParameter(request, "enabledFilter", 0);

                    wrapper = new AsyncDeliveryEventListWrapper(sSearch, orderByMap, pageSize, pageIndex, documentId, collectionId, deliveryEventTypeId, enabled);
                    analyticsEvent.setAction(DataTableEvents.List.TargetGroup);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_DUPLCATES: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    String targetDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerDuplicatesWrapper(pageSize, pageIndex, rationalizerApplicationId, sourceDashboardCompareSelection, targetDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerDuplicates);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_SIMILARITIES: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    String targetDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerSimilaritiesWrapper(pageSize, pageIndex, rationalizerApplicationId, sourceDashboardCompareSelection, targetDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerSimilarities);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_METADATA: {
                    wrapper = new AsyncRationalizerMetadataWrapper(pageSize, pageIndex, rationalizerApplicationId, rationalizerSelectionBranchFormItemIds);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerMetadata);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_DUPLCATES_DETAILS: {
                    String targetDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerDuplicatesDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, rationalizerComparisonContentGuid, targetDashboardCompareSelection, sSearch, showMarkup);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerDuplicatesDetails);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_SIMILARITIES_DETAILS: {
                    String targetDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, TARGET_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerSimilaritiesDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, rationalizerComparisonContentGuid, showMarkup, sSearch, targetDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerSimilaritiesDetails);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_METADATA_DETAILS: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerMetadataDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, metadataPointOfInterestId, sourceDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerMetadataDetails);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_SENTIMENT_DETAILS: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerSentimentDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, selectedSentimentId, sourceDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerSentimentDetails);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_READING_DETAILS: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerReadingDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, selectedReadingId, sourceDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerReadingDetails);
                    break;
                }
                case LIST_TABLE_TYPE_BRAND_PROFILE: {
                    wrapper = new AsyncBrandProfileListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.BrandProfile);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_SEARCH: {
                    wrapper = new AsyncGlobalSearchListWrapper(
                            request.getSession(),
                            sSearch,
                            orderByMap,
                            pageSize,
                            pageIndex,
                            globalSearchType,
                            tpIds,
                            targetIds,
                            statusIds,
                            isExactMatch,
                            isNewSearch,
                            UserUtil.getPrincipalUser()
                    );
                    //analyticsEvent.setAction(DataTableEvents.List.BrandProfile);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_DUPLCATES: {
                    wrapper = new AsyncGlobalDashboardDuplicatesWrapper(pageSize, pageIndex, selectedItemId);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalDuplicates);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_DUPLCATES_DETAILS: {
                    wrapper = new AsyncGlobalDashboardDuplicatesDetailsWrapper(pageSize, pageIndex, contentId, selectedItemId);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalDuplicatesDetails);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_SENTIMENT_DETAILS: {
                    wrapper = new AsyncGlobalDashboardSentimentDetailsWrapper(pageSize, pageIndex, selectedSentimentId, selectedItemId);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalSentimentDetails);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_TRANSLATION_ACCURACY_DETAILS: {
                    String selectedTotalCountString = ServletRequestUtils.getStringParameter(request, "selectedTotal", "0");
                    int selectedTotalCount = Integer.valueOf(selectedTotalCountString.replaceAll(",", ""));
                    wrapper = new AsyncGlobalDashboardTranslationAccuracyDetailsWrapper(pageSize, pageIndex, selectedTranslationAccuracyId, selectedItemId, selectedLanguageCode, selectedStatusCode, selectedTotalCount, selectedContentTypleFilterValue);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalSentimentDetails);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_READING_DETAILS: {
                    wrapper = new AsyncGlobalDashboardReadingDetailsWrapper(pageSize, pageIndex, selectedReadingId, selectedItemId);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalReadingDetails);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_BRAND: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerBrandWrapper(pageSize, pageIndex, rationalizerApplicationId, sourceDashboardCompareSelection);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerBrand);
                    break;
                }
                case LIST_TABLE_TYPE_RATIONALIZER_BRAND_DETAILS: {
                    String sourceDashboardCompareSelection = ServletRequestUtils.getStringParameter(request, SOURCE_DASHBOARD_COMPARE_SELECTION, "-9");
                    wrapper = new AsyncRationalizerBrandDetailsWrapper(pageSize, pageIndex, rationalizerApplicationId, selectedBrandItemId, sourceDashboardCompareSelection, showMarkup, filterSelection, sSearch);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerBrandDetails);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_BRAND: {
                    wrapper = new AsyncGlobalDashboardBrandWrapper(pageSize, pageIndex, selectedItemId);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalBrand);
                    break;
                }
                case LIST_TABLE_TYPE_GLOBAL_BRAND_DETAILS : {
                    wrapper = new AsyncGlobalDashboardBrandDetailsWrapper(pageSize, pageIndex, selectedItemId, selectedBrandItemId, showMarkup, filterSelection);
                    analyticsEvent.setAction(DataTableEvents.List.GlobalBrandDetails);
                    break;
                }
                case LIST_TABLE_RATIONALIZER_SHARED_CONTENT: {
                    wrapper = new AsyncRationalizerSharedContentListWrapper(sSearch, sharedContentSortId, pageSize, pageIndex, rationalizerApplicationId);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerSharedContent);
                    break;
                }
                case LIST_TABLE_RATIONALIZER_CONSOLIDATE: {
                    wrapper = new AsyncRationalizerConsolidateWrapper(pageSize, pageIndex, rationalizerContentGuid, rationalizerConsolidationTabId);
                    analyticsEvent.setAction(DataTableEvents.List.RationalizerConsolidate);
                    break;
                }
                case LIST_TABLE_TYPE_TAG_CLOUD: {
                    //For filtering, 0 disables filter on tagCloudTouchpointFilterIds, tagCloudObjectTypeFilterIds
                    //        if (!tagCloudObjectTypeFilterIds.matches("[0-9,]+")) {
//            throw Exception("Invalid tag cloud filter ids");
//        }
//
                    wrapper = new AsyncTagCloudListWrapper(sSearch, orderByMap, pageSize, pageIndex, displayMode,
                            tagCloudTouchpointFilterIds, tagCloudObjectTypeFilterIds);
//                    analyticsEvent.setAction(DataTableEvents.List.RationalizerConsolidate);
                    break;
                }
                case LIST_TABLE_TYPE_SERVICES: {
                    wrapper = new AsyncServiceListWrapper(sSearch, orderByMap, pageSize, pageIndex, servicesStatusFilter);
                    // analyticsEvent.setAction(DataTableEvents.List.Services);
                    break;
                }
                case LIST_TABLE_TYPE_TEXT_TRANSFORMATION_PROFILE: {
                    wrapper = new AsyncTextStyleTransformationListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.TextStyleTransformationProfile);
                    break;
                }
                case LIST_TABLE_TYPE_CONTENT_ASSISTANT: {
                    wrapper = new AsyncContentAssistantListWrapper(sSearch, orderByMap, pageSize, pageIndex);
                    analyticsEvent.setAction(DataTableEvents.List.ContentAssistant);
                    break;
                }
                case LIST_TABLE_TYPE_ENTITIES: {
                    RestResourceType restResourceType = RestResourceType.findByCode(resourceTypeCode);
                    ObjectMapper objectMapper = new ObjectMapper();
                    String[] selectedGuids = objectMapper.readValue(resourceGuids, String[].class);
                    String[] selectedIds = objectMapper.readValue(resourceIds, String[].class);

                    wrapper = new AsyncEntitiesListWrapper(sSearch, orderByMap, restResourceType, selectedGuids, selectedIds);
                    analyticsEvent.setAction(DataTableEvents.List.Entity);
                    break;
                }

            }

            wrapper.setsEcho(echo);
            wrapper.setDisplayMode(displayMode);
            wrapper.setListDisplayFormat(listDisplayFormat);
            wrapper.init();

            AsyncAbstractListWrapper wrapperToReturn = wrapper;
            if (wrapper instanceof AsyncRationalizerQueryWrapper) {
                wrapperToReturn = ((AsyncRationalizerQueryWrapper) wrapper).buildUIWrapper();
            }

            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsBytes(wrapperToReturn);
        } finally {
            analyticsEvent.send();
        }
    }

    /**
     * Build the ordered by map
     * key: column name
     * value: order string (asc, desc)
     *
     */
    private Map<String, String> getOrderedByMap(HttpServletRequest request, Boolean mandatorySort) {
        return getOrderedByMap( request, mandatorySort, null, null, false);
    }
    private Map<String, String> getOrderedByMap(HttpServletRequest request, Boolean mandatorySort, String defaultSort, String defaultSortOrder, boolean secondarySort) {
        Map<String, String> orderedByMap = new LinkedHashMap<>();

        String cardSortType = ServletRequestUtils.getStringParameter(request, PARAM_CARD_SORT_TYPE, null);

        if (cardSortType != null) {

            // Card sorting
            String[] sortArr = cardSortType.split("_");
            orderedByMap.put(sortArr[0].toLowerCase(), sortArr[1].equalsIgnoreCase("asc") ? "asc" : "desc");

        } else {

            // Table column sorting
            int numColumns = ServletRequestUtils.getIntParameter(request, "iColumns", 0);
            for (int i = 0; i < numColumns; i++) {
                int sortColumnIdx = ServletRequestUtils.getIntParameter(request, "iSortCol_" + i, -1);
                if (sortColumnIdx > -1) {
                    // Search for the column property name
                    String dataProp = ServletRequestUtils.getStringParameter(request, "mDataProp_" + sortColumnIdx, "name");
                    String sortDir = ServletRequestUtils.getStringParameter(request, "sSortDir_" + i, "asc");
                    // Add to the map
                    if (sortDir.equals("asc")) {
                        orderedByMap.put(dataProp.toLowerCase(), "asc");
                    } else {
                        orderedByMap.put(dataProp.toLowerCase(), "desc");
                    }
                }
            }
        }

        secondarySort = secondarySort && !orderedByMap.containsKey(defaultSort);
        if (secondarySort || (orderedByMap.isEmpty() && mandatorySort)) {
            if ( defaultSort != null && defaultSortOrder != null )
                orderedByMap.put(defaultSort, defaultSortOrder);
            else
                orderedByMap.put("name", "asc");
        }

        return orderedByMap;
    }

    /**
     * Retrieve the zone list with given document, section and zone id
     *
     */
    public static List<Long> getZoneList(long documentId, long docSectionId, long docZoneId, boolean visibilityFiltered) {
        // Retrieve section and zone
        List<Long> zoneIdList = new ArrayList<>();
        if (docZoneId <= 0) {    // All zones
            List<Zone> selectedZones = new ArrayList<>();
            if (docSectionId <= 0) { // All sections
                Document document = HibernateUtil.getManager().getObject(Document.class, documentId);
                if (document != null) {
                    selectedZones.addAll(visibilityFiltered ? document.getVisibleZonesOfUser() : document.getZones());
                }
            } else {    // Selected section
                DocumentSection docSection = HibernateUtil.getManager().getObject(DocumentSection.class, docSectionId);
                if (docSection != null) {
                    selectedZones.addAll(visibilityFiltered ? docSection.getVisibleZonesOfUser() : docSection.getZones());
                }
            }
            for (Zone zone : selectedZones) {
                if (zone.isEnabled()) {
                    zoneIdList.add(zone.getId());
                }
            }
        } else {    // Selected zone
            zoneIdList.add(docZoneId);
        }
        return zoneIdList;
    }
}