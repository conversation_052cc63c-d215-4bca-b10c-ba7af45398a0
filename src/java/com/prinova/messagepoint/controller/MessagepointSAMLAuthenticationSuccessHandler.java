package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.util.StringUtils;

import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

/**
 * 
 * <AUTHOR>
 *
 */
public class MessagepointSAMLAuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {
    
	protected final Log logger = LogFactory.getLog(this.getClass());
    private String unmatchingUserIdUrl;
    private RequestCache requestCache = new HttpSessionRequestCache();

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
            Authentication authentication) throws ServletException, IOException {
        SavedRequest savedRequest = requestCache.getRequest(request, response);

        if (savedRequest == null) {
            super.onAuthenticationSuccess(request, response, authentication);

            return;
        }

        if (isAlwaysUseDefaultTargetUrl() || StringUtils.hasText(request.getParameter(getTargetUrlParameter()))) {
            requestCache.removeRequest(request, response);
            super.onAuthenticationSuccess(request, response, authentication);

            return;
        }

        clearAuthenticationAttributes(request);

        // Use the DefaultSavedRequest URL
        String targetUrl = savedRequest.getRedirectUrl();
		if (ApplicationUtil.isCSRFPreventionEnabled()) {
			targetUrl = replacePreviousToken(targetUrl, request);
		}
        logger.debug("Redirecting to DefaultSavedRequest Url: " + targetUrl);

        if(authentication.getPrincipal() != null) {
        	User principalUser = (User) authentication.getPrincipal();
        	principalUser.setLastLogin(DateUtil.now());
        	principalUser.save();
    		// Audit (Authentication success)
    		AuditEventUtil.push(AuditEventType.ID_SIGNIN, AuditObjectType.ID_USER, principalUser.getUsername(), principalUser.getId(), AuditActionType.ID_AUTHENTICATION_SUCCESS, null);
        }
		
        getRedirectStrategy().sendRedirect(request, response, targetUrl);
    }

    public void setRequestCache(RequestCache requestCache) {
        this.requestCache = requestCache;
    }

	public RequestCache getRequestCache() {
		return requestCache;
	}

	@Override
	protected String determineTargetUrl(HttpServletRequest request, HttpServletResponse response) {

		String targetUrl = super.determineTargetUrl(request, response);
		if (ApplicationUtil.isCSRFPreventionEnabled()) {
			targetUrl = replacePreviousToken(targetUrl, request);
		}

		//
		// Check userid and passwords against system settings to ensure
		// compliance
		// If no longer compliant, add special permission and redirect to
		// special pages.
		//
		User principalUser = null;

		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		if(auth != null && auth.getPrincipal() != null) {
			principalUser = (User) auth.getPrincipal();
		}
		
		if (UserUtil.isValidUsername(principalUser)) 
		{
			return targetUrl;
		}
		else
		{
			return getTargetUrlWhenSecuritySettingsViolated(request, MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID);
		} 
		
	}

	private String replacePreviousToken(String url, HttpServletRequest request) {
		if (url.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "=") != -1 && request.getSession(false) != null) {
			String newToken = (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY);
			String tokenURL = url.substring(url.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "="));
			String[] kvs = tokenURL.split("&");
			for (String pairs : kvs) {
				if (pairs.indexOf(WebAppSecurity.CSRF_TOKEN_KEY + "=") != -1) {
					String[] tks = pairs.split("=");
					if (tks != null && tks.length > 1 && tks[1].trim().length() == WebAppSecurity.TOKEN_LENGTH) {
						return url.replaceAll(tks[1], newToken);
					}
				}
			}
		}
		return url;
	}
	
	protected String getTargetUrlWhenSecuritySettingsViolated(HttpServletRequest request, String failureReason) {

		List<GrantedAuthority> newAuthorities = new ArrayList<>();
		if (failureReason.equals(MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID)) {
			newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_USERID));
		} else if (failureReason.equals(MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_PASSWORD)) {
			newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_PASSWORD));
		} else {
			newAuthorities.add(new SimpleGrantedAuthority(Permission.ROLE_INVALID_USERID_PASSWORD));
		}

		Authentication auth = SecurityContextHolder.getContext().getAuthentication();
		((User) auth.getPrincipal()).setAuthorities(newAuthorities);
		UsernamePasswordAuthenticationToken newAuth = new UsernamePasswordAuthenticationToken(auth.getPrincipal(), auth.getCredentials(), newAuthorities);
		newAuth.setDetails(auth.getDetails());
		SecurityContextHolder.getContext().setAuthentication(newAuth);

		String url = "";
		if (failureReason.equals(MessagepointAuthenticationProcessingFilter.LOGIN_FAILURE_REASON_USERID)) {
			url = getUnmatchingUserIdUrl();
		}

		return ApplicationUtil.addToken(url, (String) request.getSession(false).getAttribute(WebAppSecurity.CSRF_SESSION_KEY));
	}
	
	public String getUnmatchingUserIdUrl() {
		return unmatchingUserIdUrl;
	}

	public void setUnmatchingUserIdUrl(String unmatchingUserIdUrl) {
		this.unmatchingUserIdUrl = unmatchingUserIdUrl;
	}
}
