package com.prinova.messagepoint.controller;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.platform.services.backgroundtask.CloneTouchpointBackgroundTask;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.MessagePointRunnableUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncProjectController implements Controller {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(AsyncProjectController.class);

	public static final String REQ_PARM_ACTION 				= "action";
	public static final String REQ_PARM_NAME 				= "name";
	public static final String REQ_PARM_DOCUMENT_ID			= "documentId";
    public static final String REQ_PARM_LANGUAGEIDS			= "languageIds";

	public static final String ACTION_ADD_PROJECT			= "add_project";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);
		Long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARM_DOCUMENT_ID, -1);
		
		JSONObject returnObj = new JSONObject();
		returnObj.put("target_id", documentId);
		
		if ( action.equalsIgnoreCase(ACTION_ADD_PROJECT) ) {
			
			String name = ServletRequestUtils.getStringParameter(request, REQ_PARM_NAME, null);
			if ( !validateUnique(Document.class, "name", name) ) {
				
				returnObj.put("error", true);
				returnObj.put("message", ApplicationUtil.getMessage("error.message.duplicate.project.name"));
				
			}
            else if(! SyncTouchpointUtil.findAllActiveUpdatingDatabaseTasks().isEmpty()) {
                returnObj.put("error", true);
                returnObj.put("message", ApplicationUtil.getMessage("error.message.cannot.run.task"));
            }
            else {
/*
                long[] languageForSyncIDs = ServletRequestUtils.getLongParameters(request, REQ_PARM_LANGUAGEIDS);

                Document doc = Document.findById(documentId);
                Set<Long> touchpointLanguageLocales = doc.getTouchpointLanguagesAsLocales()
                        .stream()
                        .map(MessagepointLocale::getId)
                        .collect(Collectors.toSet());

                Set<Long> languagesForSync = new HashSet<>();
                for(long id : languageForSyncIDs) {
                    MessagepointLocale messagepointLocale = MessagepointLocale.findById(id);
                    if(messagepointLocale != null) {
                        Long messagepointLocaleId = messagepointLocale.getId();
                        if(touchpointLanguageLocales.contains(messagepointLocaleId)) {
                            languagesForSync.add(messagepointLocaleId);
                        }
                    }
                }
*/
                Document doc = Document.findById(documentId);
                Set<Long> languagesForSync = doc.getTouchpointLanguagesAsLocales()
                        .stream()
                        .map(MessagepointLocale::getId)
                        .collect(Collectors.toSet());

                Set<Integer> dontOverrideObjectTypeIds = new HashSet<>();

                try {
										
					CloneTouchpointBackgroundTask task = new CloneTouchpointBackgroundTask(documentId, name, true, false, false, true, UserUtil.getPrincipalUser(), null, 0, languagesForSync, dontOverrideObjectTypeIds);
					Thread thread = MessagePointRunnableUtil.startThread(task, Thread.MAX_PRIORITY);
					
					returnObj.put("status_polling_id", StatusPollingBackgroundTask.findByThreadId(thread.getId()));
					
				} catch (Exception e) {
					
					returnObj.put("error", true);
					returnObj.put("message", e.getMessage());
					
				}
				
			}
			
		} else {
			returnObj.put("error", true);
			returnObj.put("message", "Async Project: Invalid action type");
		}
		
		return returnObj.toString();
	}
	
    // lookup an object of a particular class and see if it is unique according
    // to the field with value (ie, is T's name (blah) unique across all T's)
    private <T extends Serializable> boolean validateUnique(
    		Class<T> clazz, 
    		String field,
    		String value)
    {
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.eq(field, value));
		critList.add(MessagepointRestrictions.eq("removed", false));

		List<T> items = HibernateUtil.getManager().getObjectsAdvanced(clazz, critList);
    	
		return items == null || items.isEmpty();
    }

}