package com.prinova.messagepoint.controller;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.filter.GenericFilterBean;

import com.prinova.messagepoint.controller.communication.CommunicationPortalGatewayController;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.util.ApplicationUtil;

/**
 * 
 * <AUTHOR>
 *
 */
public class MessagepointSSORedirectFilter extends GenericFilterBean {

	private static final Log log = LogFactory.getLog(MessagepointSSORedirectFilter.class);
	private String redirectUrl = "/index.jsp";
	private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {		
		String hsrURI = ((HttpServletRequest) request).getRequestURI();
		
		if (hsrURI.toLowerCase().contains("signin"))
		{
			redirectUrl = "/index.jsp";
		}
		else if (hsrURI.toLowerCase().contains("signout"))
		{
			if (ApplicationUtil.isSSOAuthenticationMode())
			{
				redirectUrl = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.SSO.KEY_SSOUrlRedirect);
				
				// CONNECTED EMBEDDED: Add order guid to signout URL
				String orderGUID 		= ServletRequestUtils.getStringParameter(request, CommunicationPortalGatewayController.REQ_PARAM_ORDER_GUID, null);
				if ( orderGUID != null )
					redirectUrl += (redirectUrl.indexOf("?") == -1 ? "?" : "&") + CommunicationPortalGatewayController.REQ_PARAM_ORDER_GUID + "=" + orderGUID;
				
		        SecurityContextHolder.clearContext();
			}
		}
		
		FilterInvocation fi = new FilterInvocation(request, response, chain);
		logger.debug(fi.getRequest().getRequestURI() + " will be redirected to !" + redirectUrl);
		getRedirectStrategy().sendRedirect(fi.getRequest(), fi.getResponse(), redirectUrl);
	}
	
    public void setRedirectStrategy(RedirectStrategy redirectStrategy) {
        this.redirectStrategy = redirectStrategy;
    }

    protected RedirectStrategy getRedirectStrategy() {
        return redirectStrategy;
    }

	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	
}
