package com.prinova.messagepoint.controller.externalevent;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.util.HttpRequestUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.externalevent.ExternalEvent;
import com.prinova.messagepoint.model.externalevent.ExternalEventType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.externalevent.BulkDeleteExternalEventsService;
import com.prinova.messagepoint.platform.services.externalevent.CreateExternalEventService;
import com.prinova.messagepoint.platform.services.externalevent.GetExternalEventService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;

public class ExternalEventListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(ExternalEventListController.class);
	
	public String extEventEditRedirect;
	
	public static final String REQ_PARM_ACTION 			= "action";
	public static final String REQ_PARM_EVENTID 		= "eventId";
	public static final String REQ_PARM_PAGE 			= "page";
	
	public static final int ACTION_ADD_EXT_EVENT 		= 1;
	public static final int ACTION_DELETE_EXT_EVENT		= 2;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception{
		Map<String, Object> referenceData = new HashMap<>();
		referenceData.put("eventTypes", ExternalEventType.getAllExtEvents());
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@SuppressWarnings("unchecked")
	protected Object formBackingObject(HttpServletRequest request){
		ServiceExecutionContext context = GetExternalEventService.createContext();
		Service service = MessagepointServiceFactory.getInstance().lookupService(GetExternalEventService.SERVICE_NAME, 
				GetExternalEventService.class);
		service.execute(context);
		if(context.getResponse().isSuccessful()){
			List<ExternalEvent> list = (List<ExternalEvent>) context.getResponse().getResultValueBean();
			return new ExternalEventListWrapper(list);
		}else{
			return new ExternalEventListWrapper();
		}		
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		ExternalEventListWrapper command = (ExternalEventListWrapper) commandObj;
		Map<String, Object> parms = new HashMap<>();
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());	
		
		switch (action) {
		case(ACTION_ADD_EXT_EVENT) : {

			if (!UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_EXTERNAL_EVENT_EDIT)) {
				HttpRequestUtil.sendMessagepointError(HttpStatus.SC_FORBIDDEN, StringUtils.EMPTY, request, response);
				return null;
			}

			int extEventType = command.getEventType();
			String extEventName = command.getExtEventName();
			
			ServiceExecutionContext context = CreateExternalEventService.createContext(extEventName, extEventType);
			Service createDocumentService = MessagepointServiceFactory.getInstance().lookupService(CreateExternalEventService.SERVICE_NAME, CreateExternalEventService.class);
			createDocumentService.execute(context);

			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return super.showForm(request, response, errors);
			} else {							
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}			
		}
		case(ACTION_DELETE_EXT_EVENT) : {
			List<ExternalEvent> list = command.getSelectedList();
			ServiceExecutionContext context = BulkDeleteExternalEventsService.createContext(list);
			Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteExternalEventsService.SERVICE_NAME,
					BulkDeleteExternalEventsService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				StringBuilder sb = new StringBuilder();
				sb.append(BulkDeleteExternalEventsService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				for(ExternalEvent extEvent: list){
					sb.append(" id ").append(extEvent.getId());
				}
				sb.append(" requestor=").append(requestor.getUsername());
				sb.append("  working copies not aborted. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
				return showForm(request, response, errors);
			} else {
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}			
		}
		}
		return null;
	}
}
