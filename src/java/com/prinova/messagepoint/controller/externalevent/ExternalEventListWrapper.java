package com.prinova.messagepoint.controller.externalevent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.prinova.messagepoint.model.externalevent.ExternalEvent;

public class ExternalEventListWrapper {
	private List<ExternalEventListVO> 			extEventList = new ArrayList<>();
	private Map<Long, ExternalEventListVO>		extEventMap = new HashMap<>();
	private String 								actionValue;
	private String								extEventName;
	private int									eventType;
	
	public ExternalEventListWrapper(){
		super();
	}
	
	public ExternalEventListWrapper(List<ExternalEvent> events){
		super();
		configureEventsVO(events);		
	}
	
	private void configureEventsVO(List<ExternalEvent> events){
		for(ExternalEvent event: events){
			ExternalEventListVO eventVO = new ExternalEventListVO();
			eventVO.setExternalEvent(event);
			this.extEventList.add(eventVO);
			this.extEventMap.put(event.getId(), eventVO);
		}
	}		
	
	public List<ExternalEventListVO> getExtEventList() {
		return extEventList;
	}

	public void setExtEventList(List<ExternalEventListVO> extEventList) {
		this.extEventList = extEventList;
	}
	
	public Map<Long, ExternalEventListVO> getExtEventMap() {
		return extEventMap;
	}
	
	public void setExtEventMap(Map<Long, ExternalEventListVO> extEventMap) {
		this.extEventMap = extEventMap;
	}	
	
	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}		
	
	public String getExtEventName() {
		return extEventName;
	}
	public void setExtEventName(String extEventName) {
		this.extEventName = extEventName;
	}	
	
	public int getEventType() {
		return eventType;
	}
	public void setEventType(int eventType) {
		this.eventType = eventType;
	}		
	
	public List<ExternalEvent> getSelectedList(){
		List<ExternalEvent> selected = new ArrayList<>();
		for(ExternalEventListVO item: getExtEventList()){
			if(item.isSelectedForAction()){
				selected.add(item.getExternalEvent());
			}
		}
		return selected;
	}	
	
	public static class ExternalEventListVO{
		private boolean 			selectedForAction;
		private ExternalEvent		externalEvent;
		
		public boolean isSelectedForAction() {
			return selectedForAction;
		}
		public void setSelectedForAction(boolean selectedForAction) {
			this.selectedForAction = selectedForAction;
		}		
		public ExternalEvent getExternalEvent() {
			return externalEvent;
		}
		public void setExternalEvent(ExternalEvent externalEvent) {
			this.externalEvent = externalEvent;
		}		
	}
}
