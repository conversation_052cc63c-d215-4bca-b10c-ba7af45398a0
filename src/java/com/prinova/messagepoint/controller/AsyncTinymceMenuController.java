package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.communication.LibraryItemUsageType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AsyncTinymceMenuController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncTinymceMenuController.class);

	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_ACTION 					= "action";
	public static final String PARAM_OBJECT_TYPE				= "objectType";
	public static final String PARAM_OBJECT_ID					= "objectId";
	public static final String PARAM_SEARCH_VALUE				= "sSearch";
	public static final String PARAM_FILTER_TYPE				= "filterType";
	public static final String PARAM_PAGE_SIZE					= "pagesize";
	public static final String PARAM_SNIPPETS_ONLY				= "snippetsOnly";
	public static final String PARAM_ZONE_ID					= "zoneId";

	public static final String OBJECT_TYPE_CONTENT_OBJECT	 	= "contentObject";
	public static final String OBJECT_TYPE_DOCUMENT			 	= "document";
	public static final String OBJECT_TYPE_COMMUNICATION		= "communication";
	
	public static final String ACTION_LIST			 			= "list";
	public static final String ACTION_COUNT			 			= "count";
	
	public static final String TYPE_VARIABLES_LIST 				= "insertvariable";
	public static final String TYPE_EMBEDDED_CONTENT_LIST 		= "insertembeddedcontent";
	public static final String TYPE_LOCAL_EMBEDDED_CONTENT_LIST = "insertlocalcontent";

	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE);

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			
			if ( type.equalsIgnoreCase(TYPE_VARIABLES_LIST) ) {
				out.write(getVariablesResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_LIST) ) {
				out.write(getEmbeddedContentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( type.equalsIgnoreCase(TYPE_LOCAL_EMBEDDED_CONTENT_LIST) ) {
				out.write(getLocalEmbeddedContentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			}
			
		} catch (Exception e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", "Error - Unable to retrieve variable listing: " + e.getMessage());
			log.error("Error: Unable to retrieve variable info: " + e.getMessage() );
			out.write(returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
		}

		return null;
	}
	
	private String getVariablesResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		List<DataElementVariable> variables = new ArrayList<>();
		
		String action 		= ServletRequestUtils.getStringParameter(request, PARAM_ACTION, null);
		String objectType 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
		String objectId 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_ID, null);
		String search 		= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		String filterType 	= ServletRequestUtils.getStringParameter(request, PARAM_FILTER_TYPE, "content");
		int zoneId			= ServletRequestUtils.getIntParameter(request, PARAM_ZONE_ID, -1);
		int pagesize		= ServletRequestUtils.getIntParameter(request, PARAM_PAGE_SIZE, 100);
		int fullListCount 	= 0;

		try {
			
			if ( objectId == null ) {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Invalid object ID for objectType: " + objectType);
				return returnObj.toString();
			}
			
			if ( objectType.equalsIgnoreCase(OBJECT_TYPE_CONTENT_OBJECT) ) {
				
				ContentObject contentObj = ContentObject.findById(Long.parseLong(objectId));
				if ( contentObj == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Content object could not be found for ID: " + objectId);
					return returnObj.toString();
				}

				List<Document> documents = new ArrayList<>();
				if(contentObj.isVariableContentEnabled()) {
					if (contentObj.isGlobalSmartText()) {
						documents.addAll(contentObj.getDocuments());
					} else {
						if (contentObj.getFirstDocumentDelivery() != null) {
							documents.add(contentObj.getFirstDocumentDelivery());
						}
					}

					if (action.equals(ACTION_LIST)) {
						variables = DataElementVariable.findAllForDocumentFilteredNative(documents, null, 0, search, false, filterType.equalsIgnoreCase("content"), false, pagesize);
					} else if (action.equals(ACTION_COUNT)) {
						fullListCount = DataElementVariable.findAllForDocumentFilteredCountNative(documents, null, 0, search, false, filterType.equalsIgnoreCase("content"), false, pagesize);
					}
				} else {
					fullListCount = 0;
				}
			} else if ( objectType.equalsIgnoreCase(OBJECT_TYPE_DOCUMENT) ) {
				
				List<Document> documents = new ArrayList<>();
				String[] documentIds = objectId != null ? objectId.split(",") : null;
				if ( documentIds != null )
					for (int i = 0; i < documentIds.length; i ++ ) {
						Document currentDocument = Document.findById( Long.valueOf(documentIds[i]) );
						if ( currentDocument != null )
							documents.add(currentDocument);
				}
				
				if ( action.equals(ACTION_LIST) )
					variables = DataElementVariable.findAllForDocumentFiltered(documents, null, 0, search, false, false, false, pagesize);
				else if ( action.equals(ACTION_COUNT) )
					fullListCount = DataElementVariable.getAllForDocumentFilteredCount(documents, null, 0, search, false, false, false, pagesize);
				
			} else if ( objectType.equalsIgnoreCase(OBJECT_TYPE_COMMUNICATION) ) {
				
				Zone zone = Zone.findById(zoneId);
				if ( zone == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Connected zone could not be found ID: " + zoneId);
					return returnObj.toString();
				}
				
				List<Document> documents = new ArrayList<>();
				documents.add( zone.getDocument().getRootDocument() );

				if ( action.equals(ACTION_LIST) )
					variables = DataElementVariable.findAllForDocumentFiltered(documents, null, 0, search, false, false, true, pagesize);
				else if ( action.equals(ACTION_COUNT) )
					fullListCount = DataElementVariable.getAllForDocumentFilteredCount(documents, null, 0, search, false, false, true, pagesize);
				
			} else {
				
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unknown objectType: " + objectType);
				return returnObj.toString();
				
			}

			if ( action.equals(ACTION_COUNT) )
				returnObj.put("total_list_count", fullListCount);
			
			if ( action.equals(ACTION_LIST) ) {
				JSONArray variablesArray = new JSONArray();
				for ( DataElementVariable currentVariable: variables ) {
					JSONObject currentVarObj = new JSONObject();
					currentVarObj.put("name", currentVariable.getDisplayName());
					currentVarObj.put("id", currentVariable.getId());
					variablesArray.put(currentVarObj);
				}
				returnObj.put("items", variablesArray);
			}

		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve variable listing: " + e.getMessage());
				log.error("Error: Unable to retrieve variable info: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error: Unable to retrieve variable info: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
	private String getEmbeddedContentResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		List<ContentObject> embeddedContents = new ArrayList<>();
		
		String action 		= ServletRequestUtils.getStringParameter(request, PARAM_ACTION, null);
		String objectType 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
		String objectId 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_ID, null);
		String search 		= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		String filterType 	= ServletRequestUtils.getStringParameter(request, PARAM_FILTER_TYPE, "content");
		int zoneId			= ServletRequestUtils.getIntParameter(request, PARAM_ZONE_ID, -1);
		int pagesize		= ServletRequestUtils.getIntParameter(request, PARAM_PAGE_SIZE, 100);
		int fullListCount 	= 0;
		Boolean snippetsOnly= ServletRequestUtils.getBooleanParameter(request, PARAM_SNIPPETS_ONLY, false);

		try {
			
			if ( objectId == null ) {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Invalid object ID for objectType: " + objectType);
				return returnObj.toString();
			}

			if ( objectType.equalsIgnoreCase(OBJECT_TYPE_CONTENT_OBJECT) ) {
				
				ContentObject contentObj = ContentObject.findById(Long.valueOf(objectId));
				if ( contentObj == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Content object could not be found for ID: " + objectId);
					return returnObj.toString();
				}

				Zone sharedAssetZoneContext			= contentObj.getIsTouchpointLocal() && !contentObj.isDeliveredToPlaceholder() ? null : contentObj.getZone();
				long sharedAssetContentContext		= contentObj.getIsMarkup() ? ContentType.MARKUP : ContentType.TEXT;

				Set<Document> documents = new HashSet<>();
				Boolean supportBarcodes = null;
				Boolean supportsForms = null;
				Boolean supportsTables = null;
				boolean filterForCommunications = false;
				if ( contentObj.isGlobalContentObject() ) {
					supportBarcodes = contentObj.isSupportsBarcodes();
					supportsForms = contentObj.isSupportsForms();
					supportsTables = contentObj.isSupportsTables();
					filterForCommunications = contentObj.getUsageTypeId() == LibraryItemUsageType.ID_COMMUNICATION;
					documents.addAll( contentObj.getVisibleDocuments() );
				} else if ( contentObj.getDocument() != null ) {
					documents.add( contentObj.getDocument() );
				}

				if ( action.equals(ACTION_LIST) ) {
					embeddedContents = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documents, sharedAssetZoneContext, null, !contentObj.isVariableContentEnabled(),
							filterForCommunications, sharedAssetContentContext, true, false,
							search, pagesize, supportsTables, supportsForms, supportBarcodes, (snippetsOnly ? false : null), 0, 0, snippetsOnly);

				} else if ( action.equals(ACTION_COUNT) ) {
					embeddedContents = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documents, sharedAssetZoneContext, null, !contentObj.isVariableContentEnabled(),
							filterForCommunications, sharedAssetContentContext, true, false,
							search, 0, null, null, null, (snippetsOnly ? false : null), 0, 0, snippetsOnly);
					fullListCount = embeddedContents.size();
				}
				
			} else if ( objectType.equalsIgnoreCase(OBJECT_TYPE_DOCUMENT) ) {

				Set<Document> documents = new HashSet<>();
				String[] documentIds = objectId != null ? objectId.split(",") : null;
				if ( documentIds != null )
					for (int i = 0; i < documentIds.length; i ++ ) {
						Document currentDocument = Document.findById( Long.valueOf(documentIds[i]) );
						if ( currentDocument != null )
							documents.add(currentDocument);
				}
				
				if ( action.equals(ACTION_LIST) ) {
					embeddedContents = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documents, null, null, false,
							false, ContentType.TEXT, true, false,
							search, pagesize, null, null, null, false, 0, 0, snippetsOnly);

				} else if ( action.equals(ACTION_COUNT) ) {
					embeddedContents = ContentObject.findAllGlobalSmartTextByAdvancedQuery(documents, null, null, false,
							false, ContentType.TEXT, true, false,
							search, 0, null, null, null, false, 0, 0, snippetsOnly);
					fullListCount = embeddedContents.size();
				}
				
			} else if ( objectType.equalsIgnoreCase(OBJECT_TYPE_COMMUNICATION) ) {
				
				Communication communication = Communication.findById(Long.valueOf(objectId));
				if ( communication == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Communication object could not be found for ID: " + objectId);
					return returnObj.toString();
				}
				
				Zone zone = Zone.findById(zoneId);
				if ( zone == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Connected zone could not be found ID: " + zoneId);
					return returnObj.toString();
				}

				List<CommunicationProof> preProofs = CommunicationProof.findPreProofsByCommunicationId(communication.getId());
				CommunicationProof preProof = null;
				if (!preProofs.isEmpty())
					preProof = preProofs.get(0);
				
				Document targetDoc = AsyncConnectedInteractiveController.getTargetDocument(preProof, communication);
				if ( targetDoc.isAlternate() )
					zone = targetDoc.findZoneByParent(zone);

				JSONObject refData 	= communication != null && communication.isDebugOrder() ? 
										new JSONObject( communication.getDebugReferenceData() != null ? communication.getDebugReferenceData() : "{}" ) :
										new JSONObject( preProof.getPreProofReferenceData() != null ? preProof.getPreProofReferenceData() : "{}" );

				if ( action.equals(ACTION_LIST) ) {
					embeddedContents = communication.getSmartText(zone.getDocument(), zone, refData, communication.isTestOrder(), search, pagesize);
				} else if ( action.equals(ACTION_COUNT) ) {
					embeddedContents = communication.getSmartText(zone.getDocument(), zone, refData, communication.isTestOrder(), search, 0);
					fullListCount = embeddedContents.size();
				}

			} else {
				
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unknown objectType: " + objectType);
				return returnObj.toString();
				
			}

			if ( action.equals(ACTION_COUNT) )
				returnObj.put("total_list_count", fullListCount);
			
			if ( action.equals(ACTION_LIST) ) {
				JSONArray embeddedContentArray = ContentObject.getListAsJSON(embeddedContents);
				returnObj.put("items", embeddedContentArray);
			}

		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve smart text listing: " + e.getMessage());
				log.error("Error - Unable to retrieve smart text listing: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve smart text listing: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
	private String getLocalEmbeddedContentResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		List<ContentObject> localContentObjects = new ArrayList<>();
		
		String action 		= ServletRequestUtils.getStringParameter(request, PARAM_ACTION, null);
		String objectType 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_TYPE, null);
		String objectId 	= ServletRequestUtils.getStringParameter(request, PARAM_OBJECT_ID, null);
		String search 		= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		String filterType 	= ServletRequestUtils.getStringParameter(request, PARAM_FILTER_TYPE, "content");
		int zoneId			= ServletRequestUtils.getIntParameter(request, PARAM_ZONE_ID, -1);
		int pagesize		= ServletRequestUtils.getIntParameter(request, PARAM_PAGE_SIZE, 100);
		int fullListCount 	= 0;
		Boolean snippetsOnly= ServletRequestUtils.getBooleanParameter(request, PARAM_SNIPPETS_ONLY, false);

		try {
			
			if ( objectId == null ) {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Invalid object ID for objectType: " + objectType);
				return returnObj.toString();
			}

			if ( objectType.equalsIgnoreCase(OBJECT_TYPE_CONTENT_OBJECT) ) {
				
				ContentObject contentObject = ContentObject.findById(Long.valueOf(objectId));
				if ( contentObject == null ) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - Content object could not be found for ID: " + objectId);
					return returnObj.toString();
				}

				Document contentObjTouchpointContext 	= contentObject.getDocument();
				
				if ( action.equals(ACTION_LIST) ) {
					localContentObjects = ContentObject.findAllLocalAssetByAdvancedQuery(contentObjTouchpointContext, UserUtil.getCurrentSelectionContext(), false, ContentType.TEXT,
							search, pagesize, null, null, 0, 0, false);
					
					// Remove current local embedded content for list: Can not apply embedded content to itself
					localContentObjects.remove(contentObject);

				} else if ( action.equals(ACTION_COUNT) ) {
					localContentObjects = ContentObject.findAllLocalAssetByAdvancedQuery(contentObjTouchpointContext, UserUtil.getCurrentSelectionContext(), false, ContentType.TEXT,
							search, 0, null, null, 0, 0, false);
					
					// Remove current local embedded content for list: Can not apply embedded content to itself
					localContentObjects.remove(contentObject);
					
					fullListCount = localContentObjects.size();
				}

			} else {
				
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unknown objectType: " + objectType);
				return returnObj.toString();
				
			}

			if ( action.equals(ACTION_COUNT) )
				returnObj.put("total_list_count", fullListCount);
			
			if ( action.equals(ACTION_LIST) ) {
				JSONArray localEmbeddedContentArray = ContentObject.getListAsJSON(localContentObjects);
				returnObj.put("items", localEmbeddedContentArray);
			}

		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve local smart text listing: " + e.getMessage());
				log.error("Error - Unable to retrieve local smart text listing: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve local smart text listing: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
}