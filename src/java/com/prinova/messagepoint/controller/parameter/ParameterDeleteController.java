package com.prinova.messagepoint.controller.parameter;

import java.io.Serializable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.parameter.DeleteParameterService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class ParameterDeleteController extends MessagepointController {
	
	public static final String REQ_PARAM = "paramid";
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		Command command = new Command();
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
		if (id >0){
			Parameter parameter = HibernateUtil.getManager().getObject(Parameter.class, id);
			command.setParameter(parameter);
		}
		return command;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		Command command = (Command) commandObj;
		ServiceExecutionContext context = DeleteParameterService.createContext(command.getParameter().getId());

		Service deleteParameterService = MessagepointServiceFactory.getInstance().lookupService(DeleteParameterService.SERVICE_NAME, DeleteParameterService.class);
		deleteParameterService.execute(context);
		SimpleServiceResponse serviceResponse = (SimpleServiceResponse) context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
			return super.showForm(request, response, errors);
		} else {
			return new ModelAndView(new RedirectView(getSuccessView(), true));
		}
	}
	
	public static class Command implements Serializable {
		
		private static final long serialVersionUID = 5903506143007306158L;

		private Parameter parameter;

		public Parameter getParameter() {
			return parameter;
		}
		public void setParameter(Parameter parameter) {
			this.parameter = parameter;
		}
	}
}