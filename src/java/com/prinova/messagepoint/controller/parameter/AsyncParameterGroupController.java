package com.prinova.messagepoint.controller.parameter;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.util.ApplicationUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.prinova.messagepoint.controller.content.ContentObjectDetailsEditController.REF_NO_SELECTION_PARM_ID;

@RestController
public class AsyncParameterGroupController {

    @RequestMapping(value = "parametergroups.form", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getParameterGroups(@RequestParam("documentId") long documentId) {
        Document document = Document.findById(documentId);
        List<JSONObject> values = new ArrayList<>();

        JSONObject noSelector = new JSONObject();
        noSelector.put("id", REF_NO_SELECTION_PARM_ID);
        noSelector.put("name", "== " + ApplicationUtil.getMessage("page.text.no.content.selector")+ " ==");
        values.add(0,noSelector);


        List<ParameterGroup> parameterGroups;
        if (document == null) {
            parameterGroups = ParameterGroup.findAllIncludingParameters();
        } else {
            parameterGroups = ParameterGroup.getVisibleToDocumentParameterGroups(document);
        }

        List<JSONObject> parameterGroupsJson = parameterGroups.stream()
                .map(x -> {
                    JSONObject val = new JSONObject();
                    val.put("id", x.getId());
                    val.put("name", x.getName());
                    return val;
                })
                .collect(Collectors.toList());

        values.addAll(parameterGroupsJson);

        return new JSONArray(values).toString();
    }

}
