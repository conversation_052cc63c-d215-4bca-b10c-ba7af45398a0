package com.prinova.messagepoint.controller.parameter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.parameter.CreateOrUpdateParameterService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;

public class ParameterEditController extends MessagepointController implements Serializable {

	private static final long serialVersionUID = -545840398382803257L;

	public static final String REQ_PARAM = "paramid";
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
    	binder.registerCustomEditor(DataElementVariable.class, new IdCustomEditor<>(DataElementVariable.class));
    	binder.registerCustomEditor(String.class, new StringXSSEditor());
    }
	
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
    	Map<String, Object> referenceData = new HashMap<>();

    	List<DataElementVariable> variables = DataElementVariable.findAllEnabledForRuleSortByName();
    	referenceData.put("dataElementVariables", variables);

    	if (variables.isEmpty()) {
    		errors.reject("", "There are no data element variables available. Please create new variables first.");
    	}
    	
    	Parameter parameter = getParameterObject(request);
    	referenceData.put("parameterIsReferenced", (parameter.isApplied() && parameter.getDataElementVariable() != null));
    	    	
    	return referenceData;
    }

    protected Object formBackingObject(HttpServletRequest request) {
    	Parameter parameter = getParameterObject(request);
		
    	Command command = new Command();
    	if ( parameter.getId() > 0L ) {
    		command.setInEditMode(true);
	    	command.setId(parameter.getId());
	    	command.setName(parameter.getName());
	    	command.setDescription(parameter.getDescription());
	    	command.setDataElementVariable(parameter.getDataElementVariable());
    	}
    	
		return command;
    }
	
    protected Parameter getParameterObject(HttpServletRequest request) {
    	Parameter parameter = null;
    	long parameterId = ServletRequestUtils.getLongParameter(request, REQ_PARAM, -1);
    	if(  parameterId == -1 ) {
    		parameter = new Parameter();
    	} else {
    		parameter = HibernateUtil.getManager().getObject(Parameter.class, parameterId);
    	}
    	return parameter;
    }

	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object commandObj,
            BindException errors) throws Exception {

    	Command command = (Command)commandObj;

    	ServiceExecutionContext context = CreateOrUpdateParameterService.createContext(	command.getId(), 
    																				 	command.getName(), 
    																				 	command.getDescription(), 
    																				 	command.isRestrictedVisibility(),
    																				 	command.getDataElementVariable()); 
    	
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateParameterService.SERVICE_NAME, CreateOrUpdateParameterService.class);
		service.execute(context);
		if (context.getResponse().isSuccessful()) {
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
 			return new ModelAndView(new RedirectView("../frameClose.jsp"), parms);	
		} else {
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
    }

	public static class Command {
		private boolean inEditMode = false;
		private long id;
		private String name;
		private String description;
		private boolean restrictedVisibility = false; 
		private DataElementVariable dataElementVariable;
		
		public boolean isInEditMode() {
			return inEditMode;
		}
		public void setInEditMode(boolean inEditMode) {
			this.inEditMode = inEditMode;
		}
		public long getId() {
			return id;
		}
		public void setId(long id) {
			this.id = id;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public String getDescription() {
			return description;
		}
		public void setDescription(String description) {
			this.description = description;
		}
		public boolean isRestrictedVisibility() {
			return restrictedVisibility;
		}
		public void setRestrictedVisibility(boolean restrictedVisibility) {
			this.restrictedVisibility = restrictedVisibility;
		}
		public DataElementVariable getDataElementVariable() {
			return dataElementVariable;
		}
		public void setDataElementVariable(DataElementVariable dataElementVariable) {
			this.dataElementVariable = dataElementVariable;
		}
	}
}