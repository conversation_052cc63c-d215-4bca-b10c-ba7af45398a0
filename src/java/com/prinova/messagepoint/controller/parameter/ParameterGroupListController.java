package com.prinova.messagepoint.controller.parameter;

import com.prinova.messagepoint.model.content.ParameterGroupListDao;
import com.prinova.messagepoint.model.content.ParameterGroupListDaoImpl;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.wrapper.ParameterGroupListObjectVO;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ParameterGroupListController implements Controller{

	public static final String PARAM_GROUPS_REQ_PARAM = "paramgroups";
	public static final String USER_HAS_EDIT_PERMISSION_REQ_PARAM = "userHasEditPermission";
	
	private String formView;
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();

		ParameterGroupListDao parameterGroupListDao = new ParameterGroupListDaoImpl();
		List<ParameterGroupListObjectVO> paramGroupList = parameterGroupListDao.getParameterGroupList();		//Selector Groups

		dataMap.put(PARAM_GROUPS_REQ_PARAM, paramGroupList);
//		dataMap.put(PARAM_GROUPS_REQ_PARAM, ParameterGroup.findAllExcludingParameters());
		dataMap.put(USER_HAS_EDIT_PERMISSION_REQ_PARAM, UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT));

		return new ModelAndView(getFormView(), dataMap);
	}

	

}
