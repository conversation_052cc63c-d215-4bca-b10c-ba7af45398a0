package com.prinova.messagepoint.controller.parameter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.admin.ParameterGroupItem;
import com.prinova.messagepoint.model.wrapper.ParameterGroupItemVO;
import com.prinova.messagepoint.model.wrapper.ParameterGroupItemVOComparator;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.parameter.CreateParameterGroupService;
import com.prinova.messagepoint.platform.services.parameter.UpdateParameterGroupService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;

public class ParameterGroupEditController extends MessagepointController implements Serializable{

	private static final long serialVersionUID = -6821847575940427341L;
	
	public static final String PARAM_GROUP_ID = "paramgrpid";
	private static final String PARAM_SUBMIT_TYPE="submittype";
	private static final String FORM_ADD_PARMINS="addparam";
	private static final String FORM_REMOVE_PARMINS="removeparam";
	public static final String FORM_SUBMIT="submit";
	
	@Override
    protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
    	referenceData.put("parameters", HibernateUtil.getManager().getObjects(Parameter.class, MessagepointOrder.asc("name")));

    	long paramGroupId = ServletRequestUtils.getLongParameter(request, PARAM_GROUP_ID, -1);
    	boolean isReferenced = false;
    	if (paramGroupId != -1) {
    		isReferenced = ParameterGroup.findById(paramGroupId).isReferenced();
    	}
    	referenceData.put("pgIsReferenced", isReferenced);

    	return referenceData;
    }
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

    protected Object formBackingObject(HttpServletRequest request) {    			
    	Command command =  null;
    	long paramGroupId = ServletRequestUtils.getLongParameter(request, PARAM_GROUP_ID, -1);
    	if (isFormAddParameter(request)) {
    		command = getCommandObject(request);
    		addParamterToCommand(command);
    	} else if(isFormRemoveParameter(request)) {
    		command = getCommandObject(request);
    		removeParamterFromCommand(command);
    	} else if (paramGroupId != -1) {
    		command = loadParameterGroup(paramGroupId);
    	} else {
    		command = new Command();
    	}
		return command;
    }

    private Command getCommandObject(HttpServletRequest request) {
    	Command command = new Command(); 
    	command = bind(request);
		return command;
    }

	private Command bind( HttpServletRequest request) {
		Command command = new Command();
		try {
			ServletRequestDataBinder binder = createBinder(request, command );
			binder.bind( request );
		} catch ( Exception e ) {
			throw new RuntimeException("Could not bind the Command", e);
		}
		return command;
	}

	private Command loadParameterGroup(long paramGroupId){
		ParameterGroup pg = ParameterGroup.findById(paramGroupId);
		if(pg!=null){
			Command cmd = new Command();
			cmd.setInEditMode(true);
			cmd.setName(pg.getName());
			cmd.setDescription(pg.getDescription());
			cmd.setId(paramGroupId);
			
			for (Iterator<ParameterGroupItem> iterator = pg.getParameterGroupItems().iterator(); iterator.hasNext();) {
				ParameterGroupItem pgi = (ParameterGroupItem) iterator.next();
				if(pgi!=null){
					ParameterGroupItemVO pgiVO = new ParameterGroupItemVO();
					pgiVO.setId(pgi.getId());
					pgiVO.setGuid(pgi.getGuid());
					pgiVO.setItemOrder(pgi.getItemOrder());
					pgiVO.setParameterGroupId(paramGroupId);
					if(pgi.getParameter()!=null){
						pgiVO.setParameterId(pgi.getParameter().getId());
						pgiVO.setParameterName(pgi.getParameter().getName());
					}
					cmd.getItemList().add(pgiVO);
				}
			}
			return cmd;
		}
		return null;
	}
	
	/**
	 * add parameterGroupItemVo to tail of list of command, orderItem set to max+1 
     */
    private void addParamterToCommand(Command cmd){    	
    	long paraId = cmd.getSelectedParameterId();
    	if(!isInParameterList(paraId,cmd.getItemList())){
    		Parameter parameter = Parameter.findById(paraId);
        	if(parameter!=null){
        		ParameterGroupItemVO pgiVO = new ParameterGroupItemVO();
        		int maxOrderItem= getMaxItemOrder(cmd.getItemList());
        		pgiVO.setItemOrder(maxOrderItem+1);
        		pgiVO.setParameterId(parameter.getId());
        		pgiVO.setParameterName(parameter.getName());
        		cmd.getItemList().add(pgiVO);
        	}
    	}   	
		
    }
    
    private int getMaxItemOrder(List<ParameterGroupItemVO> paramList){
    	int order =0;
    	for (Iterator<ParameterGroupItemVO> iterator = paramList.iterator(); iterator.hasNext();) {
			ParameterGroupItemVO pgiVO = (ParameterGroupItemVO) iterator.next();
			if(pgiVO!=null){
				if(pgiVO.getItemOrder()>order)
					order=pgiVO.getItemOrder();
			}
		}
    	return order;
    }
    
    private void removeParamterFromCommand(Command cmd){    	
    	long paraId = cmd.getSelectedParameterId();
    	int deleteOrder = 0;
    	for (int i = 0; i < cmd.getItemList().size(); i++) {
    		ParameterGroupItemVO pgivo = (ParameterGroupItemVO) cmd.getItemList().get(i);
    		if(pgivo!= null && pgivo.getParameterId()==paraId){
    			deleteOrder = pgivo.getItemOrder();
				cmd.getItemList().remove(i);
				break;
			}
		}
    	rearrangeOrderOfList(deleteOrder,cmd.getItemList());
    }

    /**
     * //rearrange order of the list whose is bigger than deleteOrder
     */
    private void rearrangeOrderOfList(int order,List<ParameterGroupItemVO> paramList){
    	for (Iterator<ParameterGroupItemVO> iterator = paramList.iterator(); iterator.hasNext();) {
			ParameterGroupItemVO pgiVO = (ParameterGroupItemVO) iterator.next();
			if(pgiVO!=null && pgiVO.getItemOrder()>order){
				pgiVO.setItemOrder(pgiVO.getItemOrder()-1);
			}
		}
    }
    
    private boolean isInParameterList(long paramId, List<ParameterGroupItemVO> paramList){
    	boolean result =false;
    	if(paramList!=null){
    		for (Iterator<ParameterGroupItemVO> iterator = paramList.iterator(); iterator.hasNext();) {
				ParameterGroupItemVO pvo = (ParameterGroupItemVO) iterator.next();
				if(pvo!=null && pvo.getParameterId()==paramId){
					result = true;
					break;
				}
			}
    	}
    	return result;
    }
    
	protected boolean isFormAddParameter(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, PARAM_SUBMIT_TYPE, "");
		if (FORM_ADD_PARMINS.equals(submitType)) {
			return true;
		}
		return false;
	}
	
	protected boolean isFormRemoveParameter(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, PARAM_SUBMIT_TYPE, "");
		if (FORM_REMOVE_PARMINS.equals(submitType)) {
			return true;
		}
		return false;
	}
	
	protected ModelAndView onSubmit(
            HttpServletRequest request,
            HttpServletResponse response,
            Object commandObj,
            BindException errors) throws Exception {

    	Command command = (Command)commandObj;
    	SimpleServiceResponse serviceResponse = null;
    	if (isFormSubmission(request)) {
    		if (command.isInEditMode()) {
    			long parameteGroupId = command.getId();
        		command = getCommandObject(request);
    			ServiceExecutionContext context = UpdateParameterGroupService.createContext(parameteGroupId,
        			 																		command.getName(), 
        			 																		command.getDescription(), 
        			 																		command.getItemList()); 

        		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateParameterGroupService.SERVICE_NAME, UpdateParameterGroupService.class);
        		service.execute(context);
        		serviceResponse = (SimpleServiceResponse) context.getResponse();
    		} else {
    			ServiceExecutionContext context = CreateParameterGroupService.createContext(
        			 	command.getName(), 
        			 	command.getDescription(), 
        			 	command.getItemList()); 

        		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateParameterGroupService.SERVICE_NAME, CreateParameterGroupService.class);
        		service.execute(context);
        		serviceResponse = (SimpleServiceResponse) context.getResponse();
    		}
    		
    		if (serviceResponse.isSuccessful()) {
    			Map<String, Object> parms = new HashMap<>();
    			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
    			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
    		} else {
    			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
    			return super.showForm(request, response, errors);
    		}
    	} else {
    		
    		return new ModelAndView(new RedirectView(request.getRequestURI(), true));
    	}
    }
	
	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, PARAM_SUBMIT_TYPE, "");
		if (FORM_SUBMIT.equals(submitType)) {
			return true;
		}
		return false;
	}

	@SuppressWarnings("unchecked")
	public static class Command {
		private boolean inEditMode = false;
		private long id;
		private String name;
		private String description;
		private long selectedParameterId;
		
		private List<ParameterGroupItemVO> itemList =  LazyList.decorate(
				new ArrayList<ParameterGroupItemVO>(),
			      FactoryUtils.instantiateFactory(ParameterGroupItemVO.class));
		
		public boolean isInEditMode() {
			return inEditMode;
		}
		public void setInEditMode(boolean inEditMode) {
			this.inEditMode = inEditMode;
		}
		public long getId() {
			return id;
		}
		public void setId(long id) {
			this.id = id;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public String getDescription() {
			return description;
		}
		public void setDescription(String description) {
			this.description = description;
		}
		public List<ParameterGroupItemVO> getItemList() {
			return itemList;
		}
	
		public List<ParameterGroupItemVO> getSortedListByOrder(){
			List<ParameterGroupItemVO> sorted =LazyList.decorate(	new ArrayList<ParameterGroupItemVO>(), FactoryUtils.instantiateFactory(ParameterGroupItemVO.class));
		
			sorted.addAll(itemList);
			Collections.sort(sorted, new ParameterGroupItemVOComparator());
			return Collections.unmodifiableList(sorted);
		}
		public long getSelectedParameterId() {
			return selectedParameterId;
		}
		public void setSelectedParameterId(long selectedParameterId) {
			this.selectedParameterId = selectedParameterId;
		}
	
		
	}


}
