package com.prinova.messagepoint.controller.parameter;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.util.UserUtil;

public class ParameterListController implements Controller {

	public static final String PARAMETERS_REQ_PARAM = "parameters";
	public static final String USER_HAS_EDIT_PERMISSION_REQ_PARAM = "userHasEditPermission";
	
	private String formView;
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();
		
		dataMap.put(PARAMETERS_REQ_PARAM, Parameter.findAll());
		dataMap.put(USER_HAS_EDIT_PERMISSION_REQ_PARAM, UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ID_ROLE_TOUCHPOINT_DATA_EDIT));
		
		return new ModelAndView(getFormView(), dataMap);
	}
	
}