package com.prinova.messagepoint.controller.parameter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.parameter.ParameterGroupEditController.Command;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.wrapper.ParameterGroupItemVO;
import com.prinova.messagepoint.util.DataModelUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class ParameterGroupEditValidator extends MessagepointInputValidator {

	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		Command command = (Command) commandObj;
		if (command != null){
			validateName(command, errors);
			validateParameters(command, errors);
			validateDateParameters(command, errors);
		}
	}
	protected void validateName(Command command, Errors errors){
		String parameterGroupName = command.getName();

		boolean exists = HibernateUtil.getManager().exists(ParameterGroup.class, MessagepointRestrictions.eq("name", parameterGroupName), MessagepointRestrictions.ne("id", command.getId()));
		if (exists) {
			errors.rejectValue("name", "error.message.parameter.group.name.already.in.use");
		}
	}

	protected void validateParameters(Command command, Errors errors){
		if (command.getItemList().size() < 2) {
			errors.rejectValue("itemList", "error.message.parameter.group.at.least.two.parameters");
		} else {
			Set<Parameter> parameters = new HashSet<>();
			for (ParameterGroupItemVO itemVO : command.getItemList()) {
				Parameter parameter = Parameter.findById(itemVO.getParameterId());
				if ( parameter.getDataElementVariable() != null && !parameter.getDataElementVariable().getIsSystemVariable() && !parameter.getDataElementVariable().isScriptVariable() )
					parameters.add(parameter);
			}
			if (!DataModelUtil.parametersDataModelCompatible(parameters)) {
				errors.reject("error.message.parameters.not.data.model.compatible");
			}
		}
	}
	
	protected void validateDateParameters(Command command, Errors errors){
		List<ParameterGroupItemVO> orderedParameters = command.getSortedListByOrder();
		for ( int i=0; i < orderedParameters.size(); i++ ) {
			Parameter parameter = Parameter.findById( orderedParameters.get(i).getParameterId() );
			if ( parameter != null && parameter.getDataElementVariable() != null && 
				 parameter.getIsDateParameter() && 
				 i != orderedParameters.size() -1 )
				errors.reject("error.message.date.parameter.must.be.lowest.level.in.group");
		}
	}
}
