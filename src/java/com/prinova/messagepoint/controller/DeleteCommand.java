package com.prinova.messagepoint.controller;

public class DeleteCommand {
	private String parameter;
	private String className;
	private long id;
	private boolean deleteable = true;
	private boolean cancelAction;
	private String cancelViewName;
	private String successParameter;
	
	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getParameter() {
		return parameter;
	}

	public void setParameter(String parameter) {
		this.parameter = parameter;
	}

	public boolean isDeleteable() {
		return deleteable;
	}

	public void setDeleteable(boolean deleteable) {
		this.deleteable = deleteable;
	}

	public boolean isCancelAction() {
		return cancelAction;
	}

	public void setCancelAction(boolean cancelAction) {
		this.cancelAction = cancelAction;
	}

	public String getSuccessParameter() {
		return successParameter;
	}

	public void setSuccessParameter(String successParameter) {
		this.successParameter = successParameter;
	}

	public String getCancelViewName() {
		return cancelViewName;
	}

	public void setCancelViewName(String cancelViewName) {
		this.cancelViewName = cancelViewName;
	}

}