package com.prinova.messagepoint.controller;

import java.io.UnsupportedEncodingException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.ExternalProofValidation;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinition;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.platform.ws.client.WsClientCustomer;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.AuxiliaryDataPointListType;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.AuxiliaryDataPointType;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.OrderEntryItemDataPointListType;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.OrderEntryItemDataPointType;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.OrderEntryItemListType;
import com.prinova.messagepoint.platform.ws.client.mpcustomer.api.schemas.OrderEntryItemType;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.FeatureFlag;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncMetadataController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncMetadataController.class);

	public static final String PARAM_DATA_REQ_TYPE				= "dataType";

	public static final String PARAM_EXT_VALIDATION_ID			= "validationId";
	
	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_COMMUNICATION_ID			= "communicationId";
	public static final String PARAM_METADATA_FORM_DEF_ID		= "metadataFormDefinitionId";
	
	public static final String PARAM_DATA_KEY					= "dataKey";
	public static final String PARAM_DATA_VALUE					= "dataValue";
	
	public static final String VALUE_DATA_TYPE_METADATA_VALUES	= "metadata_values";
	public static final String VALUE_DATA_TYPE_PROD_STATUS		= "prod_status";
	public static final String VALUE_DATA_TYPE_EXT_VALIDATION	= "ext_validation";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			String requestedDataType	= ServletRequestUtils.getStringParameter(request, PARAM_DATA_REQ_TYPE, null);
			
			if ( requestedDataType == null ) {
				JSONObject returnObj = new JSONObject();
				returnObj.put("error", true);
				returnObj.put("message", "Error - unspecified request data type");
				out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
			} else {
				
				if ( requestedDataType.equalsIgnoreCase(VALUE_DATA_TYPE_METADATA_VALUES) )
					out.write( getMetadataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				else if ( requestedDataType.equalsIgnoreCase(VALUE_DATA_TYPE_PROD_STATUS) )
					out.write( getProductionStatusResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				else if ( requestedDataType.equalsIgnoreCase(VALUE_DATA_TYPE_EXT_VALIDATION) )
					out.write( getExternalValidationDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				else {
					JSONObject returnObj = new JSONObject();
					returnObj.put("error", true);
					returnObj.put("message", "Error - invalid request data type");
					out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				}
			}
			
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for communication proof data: "+e.getMessage(),e);
		}

		return null;
	}

	private String getMetadataResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();

		try {
			long documentId = ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1);
			long metadataFormDefId = ServletRequestUtils.getLongParameter(request, PARAM_METADATA_FORM_DEF_ID, -1);
			
			WebServiceConfiguration webServiceConfig = null;
			if ( documentId > 0 ) {
				Document document = Document.findById(documentId);
				webServiceConfig = document.getCommunicationDataFeedWebService();
			} else {
				MetadataFormDefinition metadataFormDef = MetadataFormDefinition.findById(metadataFormDefId);
				webServiceConfig = metadataFormDef.getWebServiceConfiguration();
			}
			
			if (webServiceConfig != null) {
				String url = webServiceConfig.getUrl();
				String username = webServiceConfig.getUsername();
				String password = webServiceConfig.getPassword();
				
				WsClientCustomer client = null;
				boolean useSSL = false; // No SSL supported yet
				log.info("Target customer URL is: " + url);
				client = new WsClientCustomer(url, username, password);
				
				// Prepare request
				String dataKey		= ServletRequestUtils.getStringParameter(request, PARAM_DATA_KEY, null);
				String dataValue	= ServletRequestUtils.getStringParameter(request, PARAM_DATA_VALUE, null);
				
				String[] connectors = dataKey.split("!-!", -1);
				String[] values 	= dataValue.split("!-!", -1);
				OrderEntryItemListType inputList = new OrderEntryItemListType();
				inputList.setCount(connectors.length);
				for ( int i = 0; i < connectors.length; i++ ) {
					OrderEntryItemType orderEntryItem = new OrderEntryItemType();
					orderEntryItem.setConnector(connectors[i]);
					String[] dataValues = values[i].split(",", -1);
					OrderEntryItemDataPointListType orderEntryItemDataPointList = new OrderEntryItemDataPointListType();
					orderEntryItemDataPointList.setCount(dataValues.length);
					
					for ( int j = 0; j < dataValues.length; j++ ) {
						OrderEntryItemDataPointType orderEntryItemDataPoint = new OrderEntryItemDataPointType();
						orderEntryItemDataPoint.setDataValue(dataValues[j]);
						orderEntryItemDataPointList.getOrderEntryItemDataPoint().add(orderEntryItemDataPoint);
					}
					
					orderEntryItem.setOrderEntryItemDataPointList(orderEntryItemDataPointList);
					inputList.getOrderEntryItem().add(orderEntryItem);
				}
				
				Document document = UserUtil.getCurrentTouchpointContext();
				String documentConnectorName = document.getConnectorName() != null ? document.getConnectorName() : "";
				
				if ( FeatureFlag.isEnabled(FeatureFlag.Features.ConnectedDebug, request) )
					log.info("CustomerAPI input: documentConnectorName - " + documentConnectorName + "; inputList - dataKey (" + (dataKey != null && !dataKey.isEmpty() ? dataKey : "") + 
						"), dataValue (" + (dataValue != null && !dataValue.isEmpty() ? dataValue : "") + ")");
				
				OrderEntryItemListType response = client.orderEntryDataRequest(documentConnectorName, inputList);
				if (response == null) {
					returnObj.put("error", true);
					returnObj.put("message", "Error - web service request failed");
					return returnObj.toString();
				}
			
				// Prepare response
				JSONArray metadataItemArray = new JSONArray();
				for (OrderEntryItemType orderEntryItem : response.getOrderEntryItem()) {
					String connector = orderEntryItem.getConnector();
					OrderEntryItemDataPointListType orderEntryItemDataPointList = orderEntryItem.getOrderEntryItemDataPointList();

						JSONObject wsm = new JSONObject();
						JSONArray wsmValues = new JSONArray();
						if (orderEntryItemDataPointList != null) {
							for ( OrderEntryItemDataPointType orderEntryItemDataPoint : orderEntryItemDataPointList.getOrderEntryItemDataPoint()) {
								JSONObject item = new JSONObject();
								item.put("value", orderEntryItemDataPoint.getDataValue());
								JSONArray auxValuesArray = new JSONArray();
								AuxiliaryDataPointListType auxiliaryDataPointList = orderEntryItemDataPoint.getAuxiliaryDataPointList();
								if (auxiliaryDataPointList != null) {
									for ( AuxiliaryDataPointType auxiliaryDataPoint : auxiliaryDataPointList.getAuxiliaryDataPoint()) {
										JSONObject auxItem = new JSONObject();
										auxItem.put("value", auxiliaryDataPoint.getDataValue() != null ? auxiliaryDataPoint.getDataValue() : "");
										auxItem.put("connector", auxiliaryDataPoint.getConnector() != null ? auxiliaryDataPoint.getConnector() : "") ;
										auxValuesArray.put(auxItem);
									}
								}
								item.put("aux_values", auxValuesArray);
								wsmValues.put(item);
							}
						}
						
						wsm.put("connector", connector);
						wsm.put("values", wsmValues);
						metadataItemArray.put(wsm);

				}
				if (metadataItemArray != null && !metadataItemArray.isEmpty()) {
					log.info("Received " + metadataItemArray.length() + " metadata items" + (FeatureFlag.isEnabled(FeatureFlag.Features.ConnectedDebug, request) ? metadataItemArray.toString() : "") );
				} else {
					log.info("Received 0 metadata items");
				}
				returnObj.put("metadataItems", metadataItemArray);
				
				
			} else {
				log.error("Missing web service configuration");
			}
	
		} catch (JSONException e) {
			log.error("Error: Unable to build aux connected data response: ", e );
		} 

		return returnObj.toString();
	}
	
	private String getProductionStatusResponseJSON( HttpServletRequest request ) {
		JSONObject returnObj = new JSONObject();

		try {
			
			long communicationId	= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
			Communication communication = Communication.findById(communicationId);
			
			if ( communication != null ) {
				returnObj.put("communication_id", communication.getId() );
				returnObj.put("tracking_complete", !communication.isPollingProductionStatusResults() );
				returnObj.put("status", communication.getLatestProductionStatusResult() );
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build aux connected data response: ", e );
		}

		return returnObj.toString();
	}
	
	private String getExternalValidationDataResponseJSON( HttpServletRequest request ) {
		JSONObject returnObj = new JSONObject();

		try {
			
			long validationId	= ServletRequestUtils.getLongParameter(request, PARAM_EXT_VALIDATION_ID, -1);
			ExternalProofValidation proofValidation = ExternalProofValidation.findById(validationId);
			
			if ( proofValidation != null ) {
				returnObj.put("communication_id", 	proofValidation.getCommunication().getId() );
				returnObj.put("status_id", 			proofValidation.getStatusId() );
				if ( proofValidation.getDateResponseReceived() != null )
					returnObj.put("response_date", 	DateUtil.formatDateTime(proofValidation.getDateResponseReceived()) );
				returnObj.put("email", 				proofValidation.getExternalValidationEmail() );
				if ( proofValidation.getValidationFeedback() != null && proofValidation.getValidationFeedback().length > 0 ) {
					try {
						returnObj.put("feedback", 		new String(proofValidation.getValidationFeedback(), ApplicationLanguageUtils.FILE_ENCODING) );
					} catch (UnsupportedEncodingException e) {
						e.printStackTrace();
					}
				}
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to build aux connected data response: ", e );
		}

		return returnObj.toString();
	}
}