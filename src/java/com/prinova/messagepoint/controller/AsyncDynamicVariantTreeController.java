package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.MessageEvents;
import com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewValidator;
import com.prinova.messagepoint.controller.content.DynamicVariantPopupContentController;
import com.prinova.messagepoint.controller.tpadmin.FormattingSelectionsListValidator;
import com.prinova.messagepoint.controller.tpadmin.FormattingSelectionsListWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.FormattingSelection;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.font.TextStyleTransformationProfile;
import com.prinova.messagepoint.model.wrapper.ContentObjectContentSelectionViewWrapper;
import com.prinova.messagepoint.model.wrapper.DataValueTreeChangesVO;
import com.prinova.messagepoint.model.wrapper.NewParameterGroupTreeNodeVO;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.content.CreateContentObjectDynamicVariantTreeNodeService;
import com.prinova.messagepoint.platform.services.content.DeleteContentObjectDynamicVariantTreeNodeService;
import com.prinova.messagepoint.platform.services.parameter.RenameParameterGroupTreeNodeService;
import com.prinova.messagepoint.platform.services.parameter.UpdateParameterGroupInstanceCollectionService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateFormattingSelectionsService;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

public class AsyncDynamicVariantTreeController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncDynamicVariantTreeController.class);

    public static final String PARAM_NODE_NAME                  = "addParamInstanceName";
    public static final String PARAM_NEW_COLLECTION_FIRST_VALUE = "newCollectionFirstValue[]";
    public static final String PARAM_SHARE_FLAG                 = "shared";
    public static final String PARAM_ACTION                     = "action";
    public static final String PARAM_OBJECT_TYPE                = "objectType";

    public static final String PARAM_TYPE                       = "type";
    public static final String PARAM_SELECTED_PARM_INSTANCE     = "paramInstId";
    public static final String PARAM_DATA_TYPE                  = "dataType";
    public static final String PARAM_NEW_NODE_NAME              = "newNodeName";
    public static final String PARAM_DOCUMENT_ID                = "documentId";

    public static final String PARAM_DATA_VALUE_TO_ADD          = "dataValueToAdd[]";
    public static final String PARAM_FULL_VALUES_TO_REMOVE      = "fullValuesToRemove";
    public static final String VALUE_SIMILAR_VARIANT            = "similar_variant";

    public static final String PARAM_TS_TRANSFORM_PROFILE       = "tsTransformProfile";
    public static final String PARAM_ALTERNATE_LAYOUT           = "alternateLayout";

    public static final String REQ_PARM_ACTION                  = "action";
    public static final String UI_ITEMS_TO_REMOVE_TOKENIZER 	= ",";

    public static final int ACTION_UPDATE 					= 1;
    public static final int ACTION_CREATE_TREE_NODE 		= 4;
    public static final int ACTION_ADD_TO_TREE_NODE 		= 8;
    public static final int ACTION_REMOVE_FROM_TREE_NODE 	= 9;
    public static final int ACTION_RENAME_TREE_NODE 		= 12;
    public static final int ACTION_REMOVE_TREE_NODE 		= 13;
    public static final int ACTION_MANAGE_FROM_TREE_NODE 	= 14;
    public static final int ACTION_EDIT_FORMATTING_SELECTION = 15;

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            String type	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);

            if (type == null) {
                out.write(getResponseJSON(request).getBytes());
                out.flush();
            } else {
                if (type.equalsIgnoreCase(VALUE_SIMILAR_VARIANT)) {
                    out.write(getSimilarVariantJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
                } else {
                    JSONObject returnObj = new JSONObject();
                    returnObj.put("error", true);
                    returnObj.put("message", "Error - invalid request data type");
                    out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
                }
            }

        } catch (Exception e) {
            JSONObject returnObj = new JSONObject();
            returnObj.put("result", "error");
            returnObj.put("message", "Exception: Unable to complete async variant request.");
            log.error("Error - Unable to resolve request to manage context: " + e.getMessage(), e);
            out.write(returnObj.toString().getBytes());
            out.flush();
        }

        return null;
    }

    private void setNewTreeNode(ParameterGroup parameterGroup, NewParameterGroupTreeNodeVO newTreeNode) {
        List<ParameterGroupItem> sortedItemList = parameterGroup.getParameterGroupItemsSorted();
        List<Parameter> sortedParameters = new ArrayList<>();
        for (ParameterGroupItem item : sortedItemList)
            sortedParameters.add(item.getParameter());

        if (parameterGroup.isParameter()) {
            List<Parameter> firstParameter = new ArrayList<>();
            firstParameter.add(sortedParameters.get(0));
            newTreeNode.setParameters(firstParameter);
            newTreeNode.setNewCollectionFirstValue(new String[1]);
        } else {
            newTreeNode.setParameters(sortedParameters);
            newTreeNode.setNewCollectionFirstValue(new String[sortedParameters.size()]);
        }
    }

    private FormattingSelectionsListWrapper wrapperInitForFormattingSelection (HttpServletRequest request) {
        String nodeName = ServletRequestUtils.getStringParameter(request, PARAM_NODE_NAME, null);
        String[] newCollectionFirstValue = ServletRequestUtils.getStringParameters(request, PARAM_NEW_COLLECTION_FIRST_VALUE);
        boolean shareFlag = ServletRequestUtils.getBooleanParameter(request, PARAM_SHARE_FLAG, false);
        Integer actionValue = ServletRequestUtils.getIntParameter(request, PARAM_ACTION, -1);
        String newNodeName = ServletRequestUtils.getStringParameter(request, PARAM_NEW_NODE_NAME, null);
        long selectionId = ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_PARM_INSTANCE, ParameterGroupTreeNode.MASTER_VARIANCE_ID);
        String[] dataValueToAdd = ServletRequestUtils.getStringParameters(request, PARAM_DATA_VALUE_TO_ADD);
        String fullValuesToRemove = ServletRequestUtils.getStringParameter(request, PARAM_FULL_VALUES_TO_REMOVE, null);
        Document document = Document.findById(ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1));
        TextStyleTransformationProfile tsTransformProfile = TextStyleTransformationProfile.findById(ServletRequestUtils.getLongParameter(request, PARAM_TS_TRANSFORM_PROFILE, -1));
        Document alternateLayout = Document.findById(ServletRequestUtils.getLongParameter(request, PARAM_ALTERNATE_LAYOUT, -1));

        FormattingSelectionsListWrapper wrapper = new FormattingSelectionsListWrapper(document);
        FormattingSelection formattingSelection = FormattingSelection.findById(selectionId);
        wrapper.setFormattingSelection(formattingSelection);
        wrapper.setTsTransformProfile(tsTransformProfile);
        wrapper.setAlternateLayout(alternateLayout);

        long pgTreeNodeId = formattingSelection.getParameterGroupTreeNode().getId();
        ParameterGroupTreeNode node = ParameterGroupTreeNode.findById(pgTreeNodeId);

        wrapper.setContentTreeNode(node);
        NewParameterGroupTreeNodeVO newNode = new NewParameterGroupTreeNodeVO();
        newNode.setName(nodeName);
        setNewTreeNode(document.getFormattingParameterGroup(), newNode);
        newNode.setNewCollectionFirstValue(newCollectionFirstValue);
        newNode.setShared(shareFlag);
        newNode.setCreatingNewCollection(true);
        wrapper.setNewTreeNode(newNode);

        DataValueTreeChangesVO dataValueChanges= new DataValueTreeChangesVO();
        dataValueChanges.setDataValueToAdd(dataValueToAdd);
        dataValueChanges.setFullValuesToRemove(fullValuesToRemove);
        wrapper.setDataValueChanges(dataValueChanges);

        wrapper.setNewNodeName(newNodeName);
        wrapper.setActionValue(String.valueOf(actionValue));
        return wrapper;
    }

    private ContentObjectContentSelectionViewWrapper wrapperInitForContentObject(HttpServletRequest request) {
        String nodeName = ServletRequestUtils.getStringParameter(request, PARAM_NODE_NAME, null);
        String[] newCollectionFirstValue = ServletRequestUtils.getStringParameters(request, PARAM_NEW_COLLECTION_FIRST_VALUE);
        boolean shareFlag = ServletRequestUtils.getBooleanParameter(request, PARAM_SHARE_FLAG, false);
        Integer actionValue = ServletRequestUtils.getIntParameter(request, PARAM_ACTION, -1);
        String newNodeName = ServletRequestUtils.getStringParameter(request, PARAM_NEW_NODE_NAME, null);
        long pgTreeNodeId = ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_PARM_INSTANCE, ParameterGroupTreeNode.MASTER_VARIANCE_ID);
        String[] dataValueToAdd = ServletRequestUtils.getStringParameters(request, PARAM_DATA_VALUE_TO_ADD);
        String fullValuesToRemove = ServletRequestUtils.getStringParameter(request, PARAM_FULL_VALUES_TO_REMOVE, null);

        ContentObjectContentSelectionViewWrapper wrapper = new ContentObjectContentSelectionViewWrapper();
        ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
        wrapper.setContentObject(contentObject);

        ParameterGroupTreeNode node = ParameterGroupTreeNode.findById(pgTreeNodeId);
        if (pgTreeNodeId == ParameterGroupTreeNode.MASTER_VARIANCE_ID) {
            node = ParameterGroupTreeNode.createMasterParam(wrapper.getContentObject().getParameterGroup());
        }
        wrapper.setContentTreeNode(node);
        NewParameterGroupTreeNodeVO newNode = new NewParameterGroupTreeNodeVO();
        newNode.setName(nodeName);
        setNewTreeNode(contentObject.getParameterGroup(), newNode);
        newNode.setNewCollectionFirstValue(newCollectionFirstValue);
        newNode.setShared(shareFlag);
        newNode.setCreatingNewCollection(true);
        wrapper.setNewTreeNode(newNode);

        DataValueTreeChangesVO dataValueChanges= new DataValueTreeChangesVO();
        dataValueChanges.setDataValueToAdd(dataValueToAdd);
        dataValueChanges.setFullValuesToRemove(fullValuesToRemove);
        wrapper.setDataValueChanges(dataValueChanges);

        wrapper.setNewNodeName(newNodeName);
        wrapper.setActionValue(String.valueOf(actionValue));
        return wrapper;
    }

    public String getResponseJSON(HttpServletRequest request) {
        AnalyticsEvent<MessageEvents> analyticsEvent = AnalyticsUtil.requestFor(MessageEvents.View);

        int objectType = ServletRequestUtils.getIntParameter(request, PARAM_OBJECT_TYPE, DynamicVariantPopupContentController.OBJECT_TYPE_CONTENT_OBJECT);
        ParameterGroup parameterGroup = null;
        ParameterGroupTreeNode contentTreeNode = null;
        DataValueTreeChangesVO dataValueChanges = null;
        NewParameterGroupTreeNodeVO newNode = null;
        String newNodeName = null;
        String actionValue = null;
        Object objectWrapper = null;
        Errors errors = null;
        if (objectType == DynamicVariantPopupContentController.OBJECT_TYPE_CONTENT_OBJECT) {
            ContentObjectContentSelectionViewWrapper wrapper = wrapperInitForContentObject(request);
            errors = new BeanPropertyBindingResult(wrapper, "");
            ContentObjectDynamicVariantViewValidator validator = new ContentObjectDynamicVariantViewValidator();
            validator.validateNotGenericInputs(wrapper, errors);
            parameterGroup = wrapper.getContentObject().getParameterGroup();
            contentTreeNode = wrapper.getContentTreeNode();
            dataValueChanges = wrapper.getDataValueChanges();
            newNode = wrapper.getNewTreeNode();
            newNodeName = wrapper.getNewNodeName();
            actionValue = wrapper.getActionValue();
            objectWrapper = wrapper;
        } else {
            FormattingSelectionsListWrapper wrapper = wrapperInitForFormattingSelection(request);
            errors = new BeanPropertyBindingResult(wrapper, "");
            FormattingSelectionsListValidator validator = new FormattingSelectionsListValidator();
            validator.validateNotGenericInputs(wrapper, errors);
            parameterGroup = wrapper.getDocument().getFormattingParameterGroup();
            contentTreeNode = wrapper.getContentTreeNode();
            dataValueChanges = wrapper.getDataValueChanges();
            newNode = wrapper.getNewTreeNode();
            newNodeName = wrapper.getNewNodeName();
            actionValue = wrapper.getActionValue();
            objectWrapper = wrapper;
        }

        long principalUserId = UserUtil.getPrincipalUserId();
        JSONObject returnObj = new JSONObject();
        int action = -1;

        if (errors.hasErrors()) {
            ObjectError error = errors.getAllErrors().get(0);
            returnObj.put("result", "error");
            returnObj.put("message", MessageFormat.format(ApplicationUtil.getMessage(error.getCode()), error.getArguments()));
        } else {
            if (actionValue!=null && !actionValue.trim().isEmpty()) {
                action = Integer.parseInt(actionValue);
            }
        }

        ServiceExecutionContext context = null;
        switch (action) {
            case (ACTION_CREATE_TREE_NODE): {
                analyticsEvent.setAction(Actions.CreateTreeNode);

                long parentNodeId = contentTreeNode != null ? contentTreeNode.getId() : ParameterGroupTreeNode.MASTER_VARIANCE_ID;
                if (parameterGroup.isParameter()) {
                    parentNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
                }

                if (objectType == DynamicVariantPopupContentController.OBJECT_TYPE_CONTENT_OBJECT) {
                    ContentObjectContentSelectionViewWrapper wrapper = (ContentObjectContentSelectionViewWrapper) objectWrapper;
                    context = CreateContentObjectDynamicVariantTreeNodeService.createContextForNewCreate(wrapper.getContentObject().getId(),
                            newNode.getName(),
                            parentNodeId,
                            principalUserId,
                            newNode.getNewCollectionFirstValue(),
                            newNode.isShared());

                    Service service = MessagepointServiceFactory.getInstance().lookupService(CreateContentObjectDynamicVariantTreeNodeService.SERVICE_NAME,
                            CreateContentObjectDynamicVariantTreeNodeService.class);
                    service.execute(context);

                    if (context.getResponse().isSuccessful()) {
                        ParameterGroupTreeNode result = (ParameterGroupTreeNode) context.getResponse().getResultValueBean();
                        returnObj.put("message", result.getId());
                    }
                }else{
                    FormattingSelectionsListWrapper wrapper = (FormattingSelectionsListWrapper) objectWrapper;
                    context = UpdateFormattingSelectionsService.createContextForNewCreate(wrapper,
                            newNode.getName(),
                            null,
                            UserUtil.getPrincipalUser(),
                            newNode.getNewCollectionFirstValue(),
                            newNode.isShared());

                    Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateFormattingSelectionsService.SERVICE_NAME,
                            UpdateFormattingSelectionsService.class);
                    service.execute(context);
                }

                break;
            }
            case (ACTION_RENAME_TREE_NODE): {
                analyticsEvent.setAction(Actions.RenameTreeNode);

                context = RenameParameterGroupTreeNodeService.createContext(contentTreeNode.getId(),
                        newNodeName,
                        principalUserId);
                Service service = MessagepointServiceFactory.getInstance()
                        .lookupService(RenameParameterGroupTreeNodeService.SERVICE_NAME,
                                RenameParameterGroupTreeNodeService.class);
                service.execute(context);

                break;
            }
            case (ACTION_REMOVE_TREE_NODE): {
                analyticsEvent.setAction(Actions.DeleteTreeNode);

                if (objectType == DynamicVariantPopupContentController.OBJECT_TYPE_CONTENT_OBJECT) {
                    ContentObjectContentSelectionViewWrapper wrapper = (ContentObjectContentSelectionViewWrapper) objectWrapper;
                    context = DeleteContentObjectDynamicVariantTreeNodeService.createContext(wrapper.getContentObject().getId(),
                            contentTreeNode.getId(),
                            true,
                            principalUserId);
                    Service service = MessagepointServiceFactory.getInstance().lookupService(DeleteContentObjectDynamicVariantTreeNodeService.SERVICE_NAME,
                            DeleteContentObjectDynamicVariantTreeNodeService.class);
                    service.execute(context);
                } else {
                    FormattingSelectionsListWrapper wrapper = (FormattingSelectionsListWrapper) objectWrapper;
                    context = UpdateFormattingSelectionsService.createContextForDeleteNode(wrapper,
                            UserUtil.getPrincipalUser());
                    Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateFormattingSelectionsService.SERVICE_NAME,
                            UpdateFormattingSelectionsService.class);
                    service.execute(context);
                }

                break;
            }
            case (ACTION_ADD_TO_TREE_NODE): {
                analyticsEvent.setAction(Actions.AddToTreeNode);

                String[] itemToAdd = dataValueChanges.getDataValueToAdd();
                long pgInstCollectionId = contentTreeNode.getParameterGroupInstanceCollection().getId();
                long pgId = contentTreeNode.getParameterGroup().getId();
                context = UpdateParameterGroupInstanceCollectionService.createContextToAddItem(pgId, pgInstCollectionId,itemToAdd, true, contentTreeNode.isTopLevel());

                Service service = MessagepointServiceFactory.getInstance()
                        .lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
                                UpdateParameterGroupInstanceCollectionService.class);
                service.execute(context);

                break;
            }
            case (ACTION_REMOVE_FROM_TREE_NODE): {
                analyticsEvent.setAction(Actions.RemoveFromTreeNode);

                List<String> fullValuesToRemove = new ArrayList<>();
                StringTokenizer st = new StringTokenizer(dataValueChanges.getFullValuesToRemove(),
                        UI_ITEMS_TO_REMOVE_TOKENIZER);
                while (st.hasMoreTokens()) {
                    fullValuesToRemove.add(st.nextToken().trim());
                }
                boolean isTopLevel = contentTreeNode.isTopLevel();
                ParameterGroupTreeNode topLevelTreeNode = null;
                if (isTopLevel) {
                    topLevelTreeNode = contentTreeNode;
                }

                context = UpdateParameterGroupInstanceCollectionService.createContextToRemoveItems(contentTreeNode
                                .getParameterGroupInstanceCollection()
                                .getId(),
                        fullValuesToRemove, true, isTopLevel, topLevelTreeNode);

                Service service = MessagepointServiceFactory.getInstance()
                        .lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
                                UpdateParameterGroupInstanceCollectionService.class);
                service.execute(context);

                break;
            }
            case (ACTION_MANAGE_FROM_TREE_NODE): {

                String[] itemToAdd = dataValueChanges.getDataValueToAdd();
                if (itemToAdd.length > 0) {
                    analyticsEvent.setAction(Actions.AddToTreeNode);
                    long pgInstCollectionId = contentTreeNode.getParameterGroupInstanceCollection().getId();
                    long pgId = contentTreeNode.getParameterGroup().getId();
                    context = UpdateParameterGroupInstanceCollectionService.createContextToAddItem(pgId, pgInstCollectionId,itemToAdd, true, contentTreeNode.isTopLevel());

                    Service addService = MessagepointServiceFactory.getInstance()
                            .lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
                                    UpdateParameterGroupInstanceCollectionService.class);
                    addService.execute(context);
                }

                if (dataValueChanges.getFullValuesToRemove() != null) {
                    List<String> fullValuesToRemove = new ArrayList<>();
                    StringTokenizer st = new StringTokenizer(dataValueChanges.getFullValuesToRemove(),
                            UI_ITEMS_TO_REMOVE_TOKENIZER);
                    while (st.hasMoreTokens()) {
                        fullValuesToRemove.add(st.nextToken().trim());
                    }
                    analyticsEvent.setAction(Actions.RemoveFromTreeNode);
                    boolean isTopLevel = contentTreeNode.isTopLevel();
                    ParameterGroupTreeNode topLevelTreeNode = isTopLevel ? contentTreeNode : null;

                    context = UpdateParameterGroupInstanceCollectionService.createContextToRemoveItems(contentTreeNode
                                    .getParameterGroupInstanceCollection()
                                    .getId(),
                            fullValuesToRemove, true, isTopLevel, topLevelTreeNode);

                    Service deleteService = MessagepointServiceFactory.getInstance()
                            .lookupService(UpdateParameterGroupInstanceCollectionService.SERVICE_NAME,
                                    UpdateParameterGroupInstanceCollectionService.class);
                    deleteService.execute(context);
                }
                break;
            }
            case (ACTION_EDIT_FORMATTING_SELECTION): {
                FormattingSelectionsListWrapper wrapper = (FormattingSelectionsListWrapper) objectWrapper;
                // Edit the formatting selection
                context = UpdateFormattingSelectionsService.createContextForEdit(wrapper, UserUtil.getPrincipalUser());
                Service updateFormattingSelectionsService = MessagepointServiceFactory.getInstance().lookupService(UpdateFormattingSelectionsService.SERVICE_NAME, UpdateFormattingSelectionsService.class);

                updateFormattingSelectionsService.execute(context);
                context.getResponse();

                break;
            }
        }
        if (context != null) {
            if (!context.getResponse().isSuccessful()) {
                String error = "unexpected exception when invoking DynamicVariantTreeService execute method";
                log.error(error);
                returnObj.put("result", "error");
                returnObj.put("message", context.getResponse().getMessages());
            } else {
                if (objectType == DynamicVariantPopupContentController.OBJECT_TYPE_CONTENT_OBJECT) {
                    ContentObjectContentSelectionViewWrapper wrapper = (ContentObjectContentSelectionViewWrapper) objectWrapper;
                    wrapper.getContentObject().save();
                }
                returnObj.put("result", "success");
            }
        }

        return returnObj.toString();
    }

    public String getSimilarVariantJSON(HttpServletRequest request) {

        long pgTreeNodeId = ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_PARM_INSTANCE, -1);
        int dataType = ServletRequestUtils.getIntParameter(request, PARAM_DATA_TYPE, -1);

        JSONObject returnObj = new JSONObject();

        ParameterGroupTreeNode p = null;

        if (pgTreeNodeId > 0 && dataType > 0) {
            p = ParameterGroupTreeNode.findById(pgTreeNodeId);
            ContentObject contentObject = ContentObject.findByHttpServletRequest(request);

            // If current statusViewId is Working Copy we need to switch to Active, if Active we need to switch to Working Copy
            dataType = dataType == 1 ? 2 : 1;
            List<ParameterGroupTreeNode> parameterGroupTreeNodes = ParameterGroupTreeNode.findAllNodesForDynamicAsset(contentObject, dataType);
            ParameterGroupTreeNode finalP = p;
            ParameterGroupTreeNode pgtn = parameterGroupTreeNodes.stream().filter(e -> e.getName().equals(finalP.getName())).findAny().orElse(null);

            if (pgtn != null) {
                returnObj.put(PARAM_SELECTED_PARM_INSTANCE, pgtn.getId());
                returnObj.put("result", "success");
            } else {
                returnObj.put("result", "not_found");
            }

        } else {
            returnObj.put("error", true);
            returnObj.put("message", "Error - Parameters missing");
        }

        return returnObj.toString();
    }

}
