package com.prinova.messagepoint.controller;

import ai.mpr.marcie.content.rationalizer.SearchContext;
import ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils;
import ai.mpr.marcie.content.rationalizer.steps.translateaccuracy.TranslateAccuracyProcessor;
import ai.mpr.marcie.content.rationalizer.steps.translateaccuracy.TranslateValidationStep;
import ai.mpr.marcie.text.SentenceSpliter;
import ai.mpr.marcie.text.readability.com.ipeirotis.readability.engine.MetricType;
import ai.mpr.marcie.text.readability.com.ipeirotis.readability.engine.Readability;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.ContentIntelligenceEvents;
import com.prinova.messagepoint.analytics.types.RationalizerEvents;
import com.prinova.messagepoint.controller.content.StringsDiffUtils;
import com.prinova.messagepoint.marcie.MarcieUtils;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditMetadataBuilder;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.contentintelligence.FleschReadabilityLevelType;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.RationalizerContent;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.semantex.HttpClientSemantexHandler;
import com.prinova.messagepoint.platform.services.semantex.SemantexMessagepointClient;
import com.prinova.messagepoint.platform.services.semantex.SemantexUtils;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.safety.Safelist;
import org.jsoup.select.Elements;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static ai.mpr.marcie.content.rationalizer.misc.ReadabilityUtil.getScore;
import static ai.mpr.marcie.reindex.common.elasticsearch.ElasticsearchUtil.preProcessHtmlForTranslateAccuracyCall;
import static com.prinova.messagepoint.mapper.BrandProfileToJsonMapper.mapBrandProfileToJSONObject;
import static com.prinova.messagepoint.model.wrapper.elastic.util.ElasticSearchQueryUtil.extractRawContentSourceList;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.constructMarkupForTranslateViewHtml;
import static com.prinova.messagepoint.platform.services.elasticsearch.FilterUtil.createSimilarityContext;
import static com.prinova.messagepoint.util.ApplicationUtil.getMessage;

public class AsyncContentIntelligenceController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentIntelligenceController.class);

	public static final String PARAM_ACTION 			= "action";
	public static final String PARAM_CONTENT			= "content";
	public static final String PARAM_VARIABLES			= "variables";
	public static final String PARAM_DATA				= "data";
	public static final String PARAM_VERBOSE			= "verbose";
	public static final String PARAM_CONTENT_OBJECT_ID 	= "contentObjectId";
	public static final String PARAM_COMMUNICATON_ID 	= "communicationId";
	public static final String PARAM_MODEL				= "model";

	public static final String ACTION_READABILITY		= "readability";
	public static final String ACTION_SENTIMENT			= "sentiment";
	public static final String ACTION_CONTENT_DIFF		= "content_diff";
	public static final String ACTION_HAS_SIM_DUP		= "sim_dup";
	public static final String ACTION_BRAND_CHECK		= "brand_check";
	public static final String ACTION_REWRITE_READABILITY = "rewrite_readability";
	public static final String ACTION_REWRITE_SENTIMENT	= "rewrite_sentiment";
	public static final String ACTION_SUMMARIZE	= "summarize";
	public static final String ACTION_REWRITE_SUMMARIZE	= "rewrite_summarize";
	public static final String ACTION_REWRITE_ACCEPT	= "rewrite_accept";
	public static final String ACTION_PLAIN_LANGUAGE	= "plain";
	public static final String ACTION_REWRITE_PLAIN_LANGUAGE	= "rewrite_plain";
	public static final String ACTION_TRANSLATE_INFO	= "translate";
	public static final String ACTION_REWRITE_TRANSLATE	= "rewrite_translate";
	public static final String ACTION_TRANSLATION_ACCURACY	= "translation_accuracy";

	private final HttpClientSemantexHandler semantexHandler= new HttpClientSemantexHandler();

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String action = ServletRequestUtils.getStringParameter(request, PARAM_ACTION);

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			if ( action.equalsIgnoreCase(ACTION_READABILITY) ) {
				out.write(getReadabilityResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_SENTIMENT) ) {
				out.write(getSentimentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_CONTENT_DIFF) ) {
				out.write(getContentDiffResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_HAS_SIM_DUP) ) {
				out.write(getHasSimOrDupResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_BRAND_CHECK) ) {
				out.write(getBrandCheckResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_REWRITE_READABILITY)  ) {
				out.write(getRewriteReadabilityResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_REWRITE_SENTIMENT) ) {
				out.write(getRewriteSentimentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_SUMMARIZE) ) {
				out.write(getSummarizeInfoResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_REWRITE_SUMMARIZE) ) {
				out.write(getRewriteSummarizeResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_PLAIN_LANGUAGE) ) {
			    out.write(getPlainLanguageInfoResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

	    	} else if ( action.equalsIgnoreCase(ACTION_REWRITE_PLAIN_LANGUAGE) ) {
			   out.write(getRewritePlainLanguageResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_TRANSLATE_INFO) ) {
				out.write(getTranslateInfoResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));

			} else if ( action.equalsIgnoreCase(ACTION_REWRITE_TRANSLATE) ) {
				out.write(getRewriteTranslateResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( action.equalsIgnoreCase(ACTION_TRANSLATION_ACCURACY) ) {
				out.write(getTranslationAccuracyResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( action.equalsIgnoreCase(ACTION_REWRITE_ACCEPT) ) {
				out.write(getRewriteAcceptResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			}
		} catch (Exception e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", "Error - Unable to retrieve content intelligence response");
			log.error("Error: Unable to retrieve content intelligence response: ", e );
			out.write(returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
		}

		return null;
	}

	private String getReadabilityResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, false);

		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, "rationalizerApplicationId", -1L);
		if(verbose && rationalizerApplicationId != -1L) {
			// Rationalizer event
			AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.AssistedAuthoring);
			analyticsEvent.setAction(RationalizerEvents.AssistedAuthoringActions.ReadabilityCheck);
			analyticsEvent.send();
		} else if (verbose && rationalizerApplicationId == -1L) {
			// Messagepoint event
			AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.AssistedAuthoring);
			analyticsEvent.setAction(ContentIntelligenceEvents.AssistedAuthoringActions.ReadabilityCheck);
			analyticsEvent.send();
		}

		try {
			JSONObject contentJson = new JSONObject(content);
			JSONArray txtJsonArray = contentJson.getJSONArray("txt_array");
			List<List<String>> varsLists = constructVariablesNamesList(request);

			LinkedList<Integer> paragraphIndexes = calculateSentencesNbInParagraphs(txtJsonArray);
			StringBuffer allText = constructAllText(txtJsonArray);
			JSONArray txtArrayOut = constructAllTextsArray(txtJsonArray, allText);

			int readabilityTargetId = Integer.valueOf(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ContentIntelligence.KEY_TargetFleschReadabilityLevel));
			FleschReadabilityLevelType readabilityTargetLevel = new FleschReadabilityLevelType(readabilityTargetId);

			computeReadabilityResponse(returnObj, "", verbose, txtArrayOut, paragraphIndexes, readabilityTargetLevel, varsLists);
			returnObj.put("hasAIRewriteLicence" , MessagepointLicenceManager.getInstance().isLicencedForAIRewrites());

		} catch (JSONException e) {
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve readability response: " + e.getMessage());
				log.error("Error - Unable to retrieve readability response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve readability response: " + e.getMessage() );
			}
		}

		return returnObj.toString();
	}

	private List<List<String>> constructVariablesNamesList(HttpServletRequest request) {
		List<List<String>> varsLists = new ArrayList<>();
		String variables = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");
		JSONObject variablesJson = new JSONObject(variables);
		JSONArray variablesJsonArray = variablesJson.getJSONArray("variables_array");
		final ObjectMapper objectMapper = new ObjectMapper();

		try {
			varsLists = objectMapper.readValue(variablesJsonArray.toString(), new TypeReference<>(){
			});
		} catch(JsonProcessingException e) {
			e.printStackTrace();
		}
		return varsLists;
	}

	private void computeReadabilityResponse(JSONObject returnObj, String rewriteHTML, Boolean verbose, JSONArray txtArrayOut,
											List<Integer> paragraphIndexes,  FleschReadabilityLevelType readabilityTargetLevel) {
		computeReadabilityResponse(returnObj, rewriteHTML, verbose, txtArrayOut, paragraphIndexes,  readabilityTargetLevel, new ArrayList<>());
	}

	private void computeReadabilityResponse(JSONObject returnObj, String rewriteHTML, Boolean verbose, JSONArray txtArrayOut,
											List<Integer> paragraphIndexes,  FleschReadabilityLevelType readabilityTargetLevel, List<List<String>> varsLists) {
		JSONArray resultsListArray = new JSONArray();

		for (int i = 0; i < txtArrayOut.length(); i++) {
			JSONObject readabilityJsonObject = computeReadabilityInternal(txtArrayOut.get(i).toString().replaceAll("\\$\\$var\\$\\$", "var"), false);
			resultsListArray.put(readabilityJsonObject);
		}

		if (verbose) {

			JSONArray returnArray = new JSONArray();
			StringBuilder currentParagraph = new StringBuilder();
			int currentParaIndex = 0;
			int currentParaSentenceCount = paragraphIndexes.get(currentParaIndex);
			int currentSentenceCount = 1;
			List<String> varsListForParagraph = CollectionUtils.isEmpty(varsLists) ? new ArrayList<>() : varsLists.get(currentParaIndex);
			for (int i = 1; i < txtArrayOut.length(); i++) {
				JSONObject currentSentenceResult = resultsListArray.getJSONObject(i);

				float fleschKincaid = currentSentenceResult.getFloat("flesch_kincaid");
				//double fleschReadingEase = currentSentenceResult.getDouble("flesch_reading_ease");

				String highlight = "transparent";
				if (fleschKincaid < readabilityTargetLevel.getTopGradeLevelThreshold()) {
					highlight = "#e4fdc8";
				} else {
					highlight = "#ffe7e7";
				}

				String popupStr = getMessage("page.label.flesch_kincaid.grade.level") + ": " + (Math.round(fleschKincaid * 10 ) / 10);

				String textWithVarName = constructTextWithVarName(txtArrayOut.getString(i),varsListForParagraph);

				currentParagraph.append("<span class=\"mceSentence\" style=\"background-color: ").append(highlight).append("; padding: 4px 4px 4px 0px; cursor: default; line-height: 1.5;\" data-toggle=\"tooltip\" data-html=\"true\" title=\"").append(popupStr).append("\">").append(textWithVarName).append("</span>");

				if (currentSentenceCount >= currentParaSentenceCount) {
					returnArray.put(currentParagraph.toString());
					if (currentParaIndex + 1 < paragraphIndexes.size()) {
						currentParaIndex++;
						currentSentenceCount = 0;
						currentParaSentenceCount = paragraphIndexes.get(currentParaIndex);
						currentParagraph = new StringBuilder();
						if(CollectionUtils.isNotEmpty(varsLists)) {
							varsListForParagraph = varsLists.get(currentParaIndex);
						}
					}
				}

				currentSentenceCount++;
			}

			returnObj.put("block_results", returnArray);
		}

		Boolean hasAtLeastOneDifficultSentence = false;
		for (int i = 1; i < txtArrayOut.length(); i++) {
			JSONObject currentSentenceResult = resultsListArray.getJSONObject(i);
			float fleschKincaid = currentSentenceResult.getFloat("flesch_kincaid");
			if (fleschKincaid >= readabilityTargetLevel.getTopGradeLevelThreshold()) {
				hasAtLeastOneDifficultSentence = true;
				break;
			}
		}

		returnObj.put("flesch_levels", FleschReadabilityLevelType.getAllLevelsAsJSON());
		returnObj.put("target_readability", readabilityTargetLevel.getLevelAsJSON());
		returnObj.put("combined_result", resultsListArray.get(0));
		returnObj.put("has_difficult_sentence", hasAtLeastOneDifficultSentence);
		returnObj.put("rewrite_html", rewriteHTML);

		returnObj.put("result", "success");
	}

	private String constructTextWithVarName(String textWithoutVarName, List<String> varsListForParagraph) {
		String textWithVarName = textWithoutVarName;
		while(!CollectionUtils.isEmpty(varsListForParagraph) ) {
			if(textWithVarName.contains("$$var$$")) {
				textWithVarName = textWithVarName.replaceFirst("\\$\\$var\\$\\$", varsListForParagraph.get(0));
				varsListForParagraph.remove(0);
			} else {
				varsListForParagraph = new ArrayList<>();
			}
		}
		return textWithVarName;
	}


	private static JSONObject computeReadabilityInternal(String text, boolean debugEnabled) {
		Readability r = new Readability(text);

		JSONObject jo = new JSONObject();

		if (debugEnabled) {
			jo.put("source", text);
		}

		jo.put("flesch_reading_ease", getScore(r, MetricType.FLESCH_READING));
		jo.put("flesch_kincaid"     , getScore(r, MetricType.FLESCH_KINCAID));

		return jo;
	}

	private JSONObject computeSummarizePropertiesInternal(String rewriteHTML) {
		String cleanHtml = cleanupHtml(rewriteHTML);
		String text =extractTextFromHtml(cleanHtml);
		Readability r = new Readability(text);

		JSONObject jo = new JSONObject();
		jo.put("source", text);
		jo.put("rewrite_html", rewriteHTML);
		jo.put("sentences", r.getMetric(MetricType.SENTENCES));
		jo.put("words", /*r.getMetric(MetricType.WORDS)*/ SemantexUtils.TEMP.getNumberOfWords(text));
		jo.put("characters", /*r.getMetric(MetricType.CHARACTERS)*/ SemantexUtils.TEMP.getNumberOfCharacters(text));

        String readingTimeString = getStringDuration((int)r.getMetric(MetricType.READIND_TIME_IN_SECONDS));
		jo.put("averageReadTime", readingTimeString);

		return jo;
	}

	private static String getStringDuration(int seconds) {

		int hours = seconds / 3600;
		int secondsLeft = seconds - hours * 3600;
		int minutes = secondsLeft / 60;
		int secs = secondsLeft - minutes * 60;
		StringBuilder buf = new StringBuilder(24);

		if (hours != 0) {
			buf.append(hours).append("  hours ");
		}
		if (minutes != 0) {
			buf.append(minutes).append(" mins ");
		}
		if (secs != 0 ) {
			buf.append(secs).append(" seconds");
		}

		return buf.toString();
	}

	private JSONArray requestSentimentResults(JSONArray txtArrayOut, JSONObject returnObj) {

		JSONArray resultsArray = new JSONArray();
		JsonArray response = MarcieUtils.getTextSentiment(txtArrayOut);

		if (response == null) {
			returnObj.put("message", "MARCIE: Sentiment lambda is invalid (empty or null)");
			returnObj.put("error", "true");

			return resultsArray;
		}
		for (JsonElement je : response) {
			JsonObject result = je.getAsJsonObject();
			JsonArray enrichments = result.getAsJsonArray("enrichments");

			JsonObject sentimentEnrichment = null;
			for (JsonElement enrichment : enrichments) {
				boolean isSentimentEnrichment = "sentiment".equals(enrichment.getAsJsonObject().get("type").getAsString());
				if (isSentimentEnrichment) {
					sentimentEnrichment = enrichment.getAsJsonObject().get("value").getAsJsonObject();
					break;
				}
			}
			if (sentimentEnrichment == null) {
				continue;
			}

			resultsArray.put(new JSONObject(sentimentEnrichment.toString()));
		}

		return resultsArray;
	}

	private String getContentDiffResponseJSON(HttpServletRequest request) {

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		JSONObject returnObj = new JSONObject();
		JSONArray returnArray = new JSONArray();

		try {

			JSONObject contentJson = new JSONObject(content);
			JSONArray txtJsonArray = contentJson.getJSONArray("content_array");

			String compareText = contentJson.getString("compare_text");
			compareText = compareText.replace("%uFEFF", "").replace("%20", " ");
			compareText = StringEscapeUtils.unescapeXml( compareText );

			for ( int i = 0; i < txtJsonArray.length(); i++ ) {
				JSONObject currentObj = txtJsonArray.getJSONObject(i);
				String currentId = currentObj.getString("id");
				String currentContent = currentObj.getString("content");
				currentContent = URLDecoder.decode(currentContent.replace("%uFEFF", ""), StandardCharsets.UTF_8);
				currentContent = StringEscapeUtils.unescapeXml( currentContent );

				JSONObject currentDiffObj = new JSONObject();
				currentDiffObj.put("id",currentId);
				currentDiffObj.put("diff_content", StringsDiffUtils.diff(compareText, currentContent) );
				returnArray.put( currentDiffObj );
			}

			returnObj.put("diff_array", returnArray);

		} catch (UnsupportedEncodingException e) {
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve content diff response: " + e.getMessage());
				log.error("Error - Unable to retrieve content diff response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve content difference response: " + e.getMessage() );
			}
		} catch (Exception e) {
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve content diff response: " + e.getMessage());
				log.error("Error - Unable to retrieve content diff response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve content difference response: " + e.getMessage() );
			}
		}

		return returnObj.toString();
	}

	private String getSentimentResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, true);

		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, "rationalizerApplicationId", -1L);
		if(verbose && rationalizerApplicationId != -1L) {
			// Rationalizer event
			AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.AssistedAuthoring);
			analyticsEvent.setAction(RationalizerEvents.AssistedAuthoringActions.SentimentCheck);
			analyticsEvent.send();
		} else if (verbose && rationalizerApplicationId == -1L) {
			// Messagepoint event
			AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.AssistedAuthoring);
			analyticsEvent.setAction(ContentIntelligenceEvents.AssistedAuthoringActions.SentimentCheck);
			analyticsEvent.send();
		}

		try {
			JSONObject contentJson = new JSONObject(content);
			JSONArray txtJsonArray = contentJson.getJSONArray("txt_array");
			LinkedList<Integer> paragraphIndexes = calculateSentencesNbInParagraphs(txtJsonArray);

			List<List<String>> varsLists = constructVariablesNamesList(request);

			StringBuffer allText = constructAllText(txtJsonArray);

			JSONArray txtArrayOut = constructAllTextsArray(txtJsonArray, allText);
			computeSentimentResponse(returnObj, "", true, txtArrayOut, paragraphIndexes, varsLists);
			returnObj.put("hasAIRewriteLicence" , MessagepointLicenceManager.getInstance().isLicencedForAIRewrites());

		} catch (JSONException e) {
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve content difference response: " + e.getMessage());
				log.error("Error - Unable to retrieve content difference response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve content difference response: " + e.getMessage() );
			}
		}

		return returnObj.toString();

	}

	private void computeSentimentResponse(JSONObject returnObj, String rewriteHTML, Boolean verbose, JSONArray txtJsonArray,  List<Integer> paragraphIndexes) {
		computeSentimentResponse(returnObj, rewriteHTML, verbose, txtJsonArray,  paragraphIndexes,new ArrayList<>());
	}

	private void computeSentimentResponse(JSONObject returnObj, String rewriteHTML, Boolean verbose, JSONArray txtJsonArray,  List<Integer> paragraphIndexes,List<List<String>> varsLists ) {
		JSONArray resultsListArray;
		if(CollectionUtils.isNotEmpty(varsLists)) {
			JSONArray cleanListArray = new JSONArray();
			for(int i = 0; i < txtJsonArray.length(); i++) {
				String txt = txtJsonArray.get(i).toString().replaceAll("\\$\\$var\\$\\$", "var");
				cleanListArray.put(txt);
			}
			resultsListArray = requestSentimentResults(cleanListArray, returnObj);
		} else {
			resultsListArray = requestSentimentResults(txtJsonArray, returnObj);
		}

		if (verbose) {

			JSONArray returnArray 			= new JSONArray();
			StringBuilder currentParagraph 	= new StringBuilder();
			int currentParaIndex 			= 0;
			int currentParaSentenceCount 	= paragraphIndexes.get(currentParaIndex);
			int currentSentenceCount 		= 1;
			List<String> varsListForParagraph = CollectionUtils.isEmpty(varsLists) ? new ArrayList<>() : varsLists.get(currentParaIndex);
			for (int i = 1; i < txtJsonArray.length(); i++ ) {
				JSONObject currentSentenceResult = resultsListArray.getJSONObject(i);

				if ( currentSentenceResult.has("label") && currentSentenceResult.has("conf") ) {

					String highlight = "transparent";
					if (currentSentenceResult.getString("label").equalsIgnoreCase("negative"))
						highlight = "#ffe7e7";
					else if (currentSentenceResult.getString("label").equalsIgnoreCase("positive"))
						highlight = "#e4fdc8";

					JSONObject currentSentimentScores = (JSONObject) currentSentenceResult.get("conf");

					double neg = Math.round(currentSentimentScores.getDouble("negative") * 1000d) / 10d;
					double pos = Math.round(currentSentimentScores.getDouble("positive") * 1000d) / 10d;
					double neut = Math.round(currentSentimentScores.getDouble("neutral") * 1000d) / 10d;
					double mix = Math.round(currentSentimentScores.getDouble("mixed") * 1000d) / 10d;
					String popupStr = getMessage("page.label.positive") + " " + pos + "% | " +
							getMessage("page.label.negative") + " " + neg + "% | " +
							getMessage("page.label.neutral") + " " + neut + "% | " +
							getMessage("page.label.mixed") + " " + mix + "%";

					String textWithVarName = constructTextWithVarName(txtJsonArray.getString(i), varsListForParagraph);
					currentParagraph.append("<span class=\"mceSentence\" style=\"background-color: ").append(highlight).append("; padding: 4px 4px 4px 0px; cursor: default; line-height: 1.5;\" data-toggle=\"tooltip\" data-html=\"true\" title=\"").append(popupStr).append("\">").append(textWithVarName).append("</span>");

				} else {
					currentParagraph.append("<span class=\"mceSentence\" style=\"color: red; padding: 4px 4px 4px 0px; cursor: default; line-height: 1.5;\" data-toggle=\"tooltip\" data-html=\"true\" title=\"ERROR\">ERROR</span>");
				}

				if ( currentSentenceCount >= currentParaSentenceCount  ) {
					returnArray.put( currentParagraph.toString() );
					if ( currentParaIndex + 1 < paragraphIndexes.size() ) {
						currentParaIndex++;
						currentSentenceCount = 0;
						currentParaSentenceCount = paragraphIndexes.get(currentParaIndex);
						currentParagraph = new StringBuilder();
						if(CollectionUtils.isNotEmpty(varsLists)) {
							varsListForParagraph = varsLists.get(currentParaIndex);
						}
					}
				}

				currentSentenceCount++;
			}

			returnObj.put("block_results", returnArray);
		}

		returnObj.put("combined_result", resultsListArray.get(0));
		returnObj.put("rewrite_html", rewriteHTML);
		returnObj.put("result", "success");
	}

	private JSONArray parseParagraphSentences(JSONArray txtJsonArray, LinkedList<Integer> paragraphIndexes) {
		JSONArray sentences = new JSONArray();
		try {
			for (int i = 0; i < txtJsonArray.length(); i++) {
				String itemAsString = txtJsonArray.getString(i);

				String[] outputSentencesArray = SentenceSpliter.DEFAULT.split(itemAsString);
				for (String outputSentence : outputSentencesArray) {
					sentences.put(outputSentence);
				}
				paragraphIndexes.add(outputSentencesArray.length);
			}

		} catch (JSONException e) {
			log.error("Error - Unable to retrieve paragraph parsing response: " + e.getMessage() );
		}

		return sentences;
	}

	private LinkedList<Integer>  calculateSentencesNbInParagraphs(JSONArray txtJsonArray) {
		LinkedList<Integer> paragraphIndexes = new LinkedList<>();

		for(int i = 0; i < txtJsonArray.length(); i++) {
			String itemAsString = txtJsonArray.getString(i);
			String[] outputSentencesArray = SentenceSpliter.DEFAULT.split(itemAsString);
			paragraphIndexes.add(outputSentencesArray.length);
		}
		return paragraphIndexes;
	}

	private int containsCountOfObjId (JsonArray array, String objId) {
		if ( array == null )
			return 0;

		int count = 0;
		for ( int i = 0; i < array.size(); i++ ) {
			JsonElement currentObj = array.get(i);
			JsonObject resultJsonObj = currentObj.getAsJsonObject();
			String currentId = resultJsonObj.get("id").getAsString();
			if ( currentId.indexOf(objId) != -1 )
				count++;
		}
		return count;
	}

	private String getHasSimOrDupResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		long variantId = ServletRequestUtils.getLongParameter(request, "variantId", -1L);

		ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
		ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantId);
		String selfId = null;
		if ( contentObject != null && !contentObject.isGlobalSmartText() )
			selfId = "M_" + contentObject.getGuid();
		else
			selfId = "ST_" + contentObject.getGuid();
		if ( pgtn != null)
			selfId += "_" + pgtn.getGuid();

		try {

			JSONObject contentJson = new JSONObject(content);
			String rawContent = contentJson.getString("text");

			String comparisonContent = "";

			// %uFEFF: No width space: comes from cursor placement (can't be decoded)
			comparisonContent = URLDecoder.decode(rawContent.replace("%uFEFF", "").replace("%20", " "), StandardCharsets.UTF_8);
			comparisonContent = StringEscapeUtils.unescapeXml( comparisonContent );
			comparisonContent = ContentObjectContentUtil.translateContentForPersistance( comparisonContent );

			org.jsoup.nodes.Document document = Jsoup.parse(comparisonContent != null ? comparisonContent : "");

			comparisonContent = ContentObjectContentUtil.getUnformattedTextContentForMarcie(document);


			boolean hasSimOrDup = false;
			int dupCount = -1, simCount = -1;

			String applicationGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();
			MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
			MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(applicationGuid);

			String contentHash = ElasticsearchContentUtils.calculateContentHash(comparisonContent);

			JsonArray duplicateArr = messagepointElasticSearchHandler.searchDuplicatesByHash(contentHash);
			duplicateArr = extractRawContentSourceList(duplicateArr);

			int selfDupCount = selfId != null ? containsCountOfObjId(duplicateArr, selfId) : 0;

			if (duplicateArr == null || (duplicateArr.size() - selfDupCount) <= 0) {
				dupCount = 0;
				float minRange = 95f / 100f;
				float maxRange = 1.0f;
				SearchContext<JsonObject> ctx = createSimilarityContext(minRange, maxRange, contentHash);
				JsonArray similarArr = messagepointElasticSearchHandler.searchSimilaritiesByText(comparisonContent, ctx);

				int selfSimCount = selfId != null ? containsCountOfObjId(similarArr, selfId) : 0;

				if (similarArr != null && (similarArr.size() - selfSimCount) > 0) {
					simCount = similarArr.size() - selfSimCount;
					hasSimOrDup = true;
				}

			} else {
				dupCount = duplicateArr.size();
				hasSimOrDup = true;
			}



			returnObj.put("has_sim_or_dup", hasSimOrDup);
			returnObj.put("sim_count", simCount);
			returnObj.put("dup_count", dupCount);

			returnObj.put("result", "success");

		} catch (JSONException e) {

			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve has similarities/duplicates response: " + e.getMessage());
				log.error("Error - Unable to retrieve has similarities/duplicates response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve has similarities/duplicates response: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}

	private String getBrandCheckResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		Boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, false);

		long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, "rationalizerApplicationId", -1L);
		if(verbose && rationalizerApplicationId != -1L) {
			// Rationalizer event
			AnalyticsEvent<RationalizerEvents> analyticsEvent = AnalyticsUtil.requestFor(RationalizerEvents.AssistedAuthoring);
			analyticsEvent.setAction(RationalizerEvents.AssistedAuthoringActions.BrandCheck);
			analyticsEvent.send();
		} else if (verbose && rationalizerApplicationId == -1L) {
			// Messagepoint event
			AnalyticsEvent<ContentIntelligenceEvents> analyticsEvent = AnalyticsUtil.requestFor(ContentIntelligenceEvents.AssistedAuthoring);
			analyticsEvent.setAction(ContentIntelligenceEvents.AssistedAuthoringActions.BrandCheck);
			analyticsEvent.send();
		}

		try {
			JSONObject contentJson = new JSONObject(content);
			JSONArray txtJsonArray = contentJson.getJSONArray("txt_array");

			LinkedList<Integer> paragraphIndexes = new LinkedList<>();
			JSONArray txtArrayOut = new JSONArray();
			JSONArray sentencesArray = parseParagraphSentences( txtJsonArray, paragraphIndexes );
			for ( int i = 0; i < sentencesArray.length(); i++ )
				txtArrayOut.put( sentencesArray.get(i) );

			JSONArray returnArray 			= new JSONArray();
			StringBuilder currentParagraph 	= new StringBuilder();
			int currentParaIndex 			= 0;
			int currentParaSentenceCount 	= paragraphIndexes.get(currentParaIndex);
			int currentSentenceCount 		= 1;
			int brandViolations				= 0;

			BrandProfile activeProfile = findBrandActiveProfile(request, rationalizerApplicationId);
			if (activeProfile == null) {
				returnObj.put("error", true);
				returnObj.put("message", "Error - No brand profile defined");

				return returnObj.toString();
			}

			if ( verbose ) {
				JSONObject brandProfileData = mapBrandProfileToJSONObject(activeProfile);
				returnObj.put("brand_profile", brandProfileData);
			}

			for (int i = 0; i < txtArrayOut.length(); i++) {
				String brandValidatedSentence =  StringEscapeUtils.unescapeXml(txtArrayOut.getString(i));
				brandValidatedSentence = brandValidatedSentence.replaceAll("<", "&lt;").replaceAll(">", "&gt;");

				// Lead compound spaces
				String matchStr = brandValidatedSentence;
				Pattern pattern =   Pattern.compile("^S_S[\\d]+S_S");
				Matcher matcher =   pattern.matcher(brandValidatedSentence);
				while (matcher.find())
				{
					StringBuilder swap = new StringBuilder(brandValidatedSentence);
					int spaceCount = Integer.parseInt(matcher.group().replace("S", "").replace("_", ""));
					if ( activeProfile.getApplyLeadCompoundSpaceDetection() ) {
						swap.replace(matcher.start(), matcher.end(),
								"<span class=\"brandViolationHighlight\" type=\"leadspace\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.compound_spaces_lead") + "\">" +
										StringUtils.repeat("&nbsp;", spaceCount) +
										"</span>");
					} else {
						swap.replace(matcher.start(), matcher.end(), StringUtils.repeat("&nbsp;", spaceCount));
					}
					brandValidatedSentence = swap.toString();
				}

				// Between compound spaces
				pattern =   Pattern.compile("\\sS_S[\\d]+S_S");
				matcher =   pattern.matcher(brandValidatedSentence);
				Set<Integer> spaceCounts = new HashSet<>();
				while (matcher.find())
				{
					String match = matcher.group();
					Integer spaceCount = Integer.parseInt(match.replace(" ", "").replace("S", "").replace("_", ""));
					spaceCounts.add(spaceCount);
				}

				for ( Integer count: spaceCounts ) {
					if ( activeProfile.getApplyCompoundSpaceDetection() ) {
						brandValidatedSentence = brandValidatedSentence.replaceAll("S_S" + count + "S_S","<span class=\"brandViolationHighlight\" type=\"spaces\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.compound_spaces_between") + "\">" +
								StringUtils.repeat("&nbsp;", count) +
								"</span>");
					} else {
						brandValidatedSentence = matcher.replaceAll( StringUtils.repeat("&nbsp;", count) );
					}
				}

				// Single bullet list items
				if ( brandValidatedSentence.indexOf("UL_1_UL") != -1 ) {
					int unorderedCount = StringUtils.countMatches( brandValidatedSentence, "UL_1_UL");
					brandValidatedSentence = brandValidatedSentence.replaceAll("UL_1_UL", "");
					if ( activeProfile.getApplySingleBulletListDetection() )
						brandViolations += unorderedCount;
				}

				// Single number list items
				if ( brandValidatedSentence.indexOf("OL_1_OL") != -1 ) {
					int orderedCount = StringUtils.countMatches( brandValidatedSentence, "OL_1_OL");
					brandValidatedSentence = brandValidatedSentence.replaceAll("OL_1_OL", "");
					if ( activeProfile.getApplySingleNumberListDetection() )
						brandViolations += orderedCount;
				}

				// Superscripting
				if ( brandValidatedSentence.indexOf("SUP_OPEN_SUP") != -1 || brandValidatedSentence.indexOf("SUP_CLOSE_SUP") != -1 ) {
					brandValidatedSentence = brandValidatedSentence.replaceAll("SUP_OPEN_SUP", "<sup>").replaceAll("SUP_CLOSE_SUP", "</sup>");
				}
				matchStr = brandValidatedSentence;

				pattern = Pattern.compile("[a-zA-Z0-9]{3,}(TM|SM|\\(c\\)|\\(C\\)|\\(R\\))([\\s;:,.?!$%]|$)");
				brandValidatedSentence = addLegalMarkViolations(activeProfile, brandValidatedSentence, pattern);

				// Restricted contractions
				if ( activeProfile.getApplyRestrictedContractions() ) {
					JSONArray terms = activeProfile.getEnabledRestrictedContractions();
					for ( int j = 0; j < terms.length(); j++ ) {
						if ( containsTarget( terms.getJSONObject(j).getString("target"), brandValidatedSentence ) ) {

							Map<String,String> targetReplacementMap = getTargetReplacementMap(terms.getJSONObject(j).getString("target"), terms.getJSONObject(j).getString("replacement"));
							for ( String currentTarget: targetReplacementMap.keySet() ) {
								String regex = "(?<![\\w\\d])" + currentTarget + "(?![\\w\\d])";
								brandValidatedSentence = brandValidatedSentence.replaceAll(regex,
										"<span class=\"brandViolationHighlight\" type=\"restrictedcontractions\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.restricted_contraction") + "\">" +
												"<span class=\"brandViolationReplacement\">" + targetReplacementMap.get(currentTarget) + "</span>" +
												currentTarget +
												"</span>");
							}
						}
					}
				}

				// Preferred contractions
				if ( activeProfile.getApplyPreferredContractions() ) {
					JSONArray terms = activeProfile.getEnabledPreferredContractions();
					for ( int j = 0; j < terms.length(); j++ ) {
						if ( containsTarget( terms.getJSONObject(j).getString("target"), brandValidatedSentence ) ) {

							Map<String,String> targetReplacementMap = getTargetReplacementMap(terms.getJSONObject(j).getString("target"), terms.getJSONObject(j).getString("replacement"));
							for ( String currentTarget: targetReplacementMap.keySet() ) {
								String regex = "(?<![\\w\\d])" + currentTarget + "(?![\\w\\d])";
								brandValidatedSentence = brandValidatedSentence.replaceAll(regex,
										"<span class=\"brandViolationHighlight\" type=\"preferredcontractions\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.preferred_contraction") + "\">" +
												"<span class=\"brandViolationReplacement\">" + targetReplacementMap.get(currentTarget) + "</span>" +
												currentTarget +
												"</span>");
							}

						}
					}
				}

				// Restricted terms
				if ( activeProfile.getApplyRestrictedTerms() ) {
					JSONArray terms = activeProfile.getEnabledRestrictedTerms();
					for ( int j = 0; j < terms.length(); j++ ) {
						if ( containsTarget( terms.getJSONObject(j).getString("target"), brandValidatedSentence ) ) {

							String replacement = terms.getJSONObject(j).getString("replacement");
							Map<String,String> targetReplacementMap = getTargetReplacementMap(terms.getJSONObject(j).getString("target"), replacement);
							boolean replacementIsMixedCase = !Objects.equals(replacement, replacement.toLowerCase());
							for ( String currentTarget: targetReplacementMap.keySet() ) {
								String regex = "(?<![\\w\\d])" + currentTarget + "(?![\\w\\d])";
								brandValidatedSentence = brandValidatedSentence.replaceAll(regex,
										"<span class=\"brandViolationHighlight\" type=\"restrictedterms\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.restricted_term") + "\">" +
												"<span class=\"brandViolationReplacement\">" + ( replacementIsMixedCase ? replacement : targetReplacementMap.get(currentTarget) ) + "</span>" +
												currentTarget +
												"</span>");
							}

						}
					}
				}

				// Fixed content
				if ( verbose && brandValidatedSentence.indexOf("FIXEDCONTENT_OPEN") != -1 && brandValidatedSentence.indexOf("FIXEDCONTENT_CLOSE") != -1 )
					brandValidatedSentence = brandValidatedSentence
							.replaceAll("FIXEDCONTENT_OPEN", "<span class=\"ignoredContentItem\">")
							.replaceAll("FIXEDCONTENT_CLOSE", "</span>");

				// Sentence max length
				String[] tokens = txtArrayOut.getString(i).split(" ");
				int currentLength = 0;
				for ( int j = 0 ; j < tokens.length; j++ )
					if ( tokens[j].indexOf("FIXEDCONTENT_OPEN") == -1 && tokens[j].indexOf("FIXEDCONTENT_CLOSE") == -1 && !tokens[j].trim().isEmpty())
						currentLength++;
				Boolean maxLengthViolation = currentLength > activeProfile.getMaxSentenceLength();
				if ( activeProfile.getApplyMaxSentenceLength() && maxLengthViolation ) {
					currentParagraph.append("<span class=\"mceSentence brandViolationHighlight\" type=\"maxlength\" data-toggle=\"tooltip\" data-html=\"true\" title=\"").append(getMessage("client_messages.text.max_sentence_length")).append("\" style=\"padding: 4px 4px 4px 0px; cursor: default; line-height: 1.5;\">").append(brandValidatedSentence).append("</span>");
				} else {
					currentParagraph.append("<span class=\"mceSentence\" style=\"padding: 4px 4px 4px 0px; cursor: default; line-height: 1.5;\">").append(brandValidatedSentence).append("</span>");
				}

				if (currentSentenceCount >= currentParaSentenceCount) {

					returnArray.put(currentParagraph.toString());

					if (currentParaIndex + 1 < paragraphIndexes.size()) {
						currentParaIndex++;
						currentSentenceCount = 0;
						currentParaSentenceCount = paragraphIndexes.get(currentParaIndex);
						currentParagraph = new StringBuilder();
					}
				}

				currentSentenceCount++;
			}

			for ( int i = 0 ; i < returnArray.length(); i++ ) {
				org.jsoup.nodes.Document currentSentenceDOM = Jsoup.parse(returnArray.getString(i));
				brandViolations += currentSentenceDOM.select(".brandViolationHighlight").size();
			}

			returnObj.put("block_results", returnArray);
			returnObj.put("violations", brandViolations);
			returnObj.put("verbose", verbose);

			returnObj.put("result", "success");


		} catch (JSONException e) {
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve brand check response: " + e.getMessage());
				log.error("Error - Unable to retrieve brand check response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve brand check response: " + e.getMessage() );
			}

		} catch (UnsupportedEncodingException e) {

			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve brand check response - encoding exception: " + e.getMessage());
				log.error("Error - Unable to retrieve brand check response - encoding exception: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve brand check response: " + e.getMessage() );
			}

		}

		return returnObj.toString();
	}

	private String getRewriteReadabilityResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		Boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, false);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, SemantexMessagepointClient.DEFAULT_MODEL);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("html");
		String input = isHTML ? contentJson.getString("html") : contentJson.getString("markup").replaceAll("\"", "\\\\\"");
		String targetSuggestedReadability = contentJson.getString("targetSuggestedReadability");

		JsonObject rewriteReadabilityJsonResponse = semantexHandler.rewriteReadability(input, targetSuggestedReadability, isHTML, model);
		System.out.println("rewriteReadabilityJsonResponse " + rewriteReadabilityJsonResponse);

		if(hasError(rewriteReadabilityJsonResponse)) {
			return rewriteReadabilityJsonResponse.toString();
		}

		String rewriteHTML = rewriteReadabilityJsonResponse.getAsJsonObject("result").getAsJsonObject().get("text").getAsString();
		if(isHTML) {
			rewriteHTML = cleanupHtml(rewriteHTML);
		}
		String text = isHTML ? extractTextFromHtml(rewriteHTML) : rewriteHTML;
		text = text.replaceAll("\\n{2,}", "\n");
		text = text.replace("\\.\n", ". \n");

		generateAuditForAction(contentJson, variablesJsonString, rewriteHTML, "Readability");

		JSONObject returnObj = constructRewriteReadabilityResponse(isHTML, targetSuggestedReadability, rewriteHTML, text);

		return returnObj.toString();
	}

	private JSONObject constructRewriteReadabilityResponse(Boolean isHTML, String targetSuggestedReadability, String rewriteHTML, String text) {
		JSONObject returnObj = new JSONObject();
		LinkedList<Integer> paragraphIndexes;
		JSONArray txtArrayAll = new JSONArray();
		if(isHTML) {
			JSONArray txtJsonArray = constructSentencesArrayFromHTML(rewriteHTML);
			paragraphIndexes = calculateSentencesNbInParagraphs(txtJsonArray);
			StringBuffer allText = constructAllText(txtJsonArray);
			txtArrayAll = constructAllTextsArray(txtJsonArray, allText);
		} else {
			JSONArray txtArrayOut = constructSentencesArray(text);
			paragraphIndexes = calculateSentencesNbInParagraphs(txtArrayOut);
			txtArrayAll.put(text);
			for(int i = 0; i < txtArrayOut.length(); i++) {
				txtArrayAll.put(txtArrayOut.get(i));
			}
		}

		FleschReadabilityLevelType readabilityTargetLevel = FleschReadabilityLevelType.getBySemnatexValue(targetSuggestedReadability);
		computeReadabilityResponse(returnObj, rewriteHTML, true,  txtArrayAll, paragraphIndexes, readabilityTargetLevel);

		System.out.println("rewrite returnObj " + returnObj);
		return returnObj;
	}

	private void generateAuditForAction(JSONObject requestContentJson, String variablesJsonString, String rewriteHTML, String targetName) {
		Boolean isFirstSuggest = requestContentJson.getBoolean("isFirstSuggest");
		boolean isRationalizerModel = requestContentJson.getBoolean("isRationalizerModel");
		String modelName = isRationalizerModel ? "Rationalizer" : "Messagepoint";

		String rewriteWithVars = constructRewriteHTMLResponseWithVars(variablesJsonString, rewriteHTML);
		String textWithVars =  extractTextFromHtml(rewriteWithVars);
		auditAction(textWithVars,  targetName, isFirstSuggest, modelName);
	}

	private void generateAuditForAction(JSONObject requestContentJson, String text, String targetName) {
		Boolean isFirstSuggest = requestContentJson.getBoolean("isFirstSuggest");
		boolean isRationalizerModel = requestContentJson.getBoolean("isRationalizerModel");
		String modelName = isRationalizerModel ? "Rationalizer" : "Messagepoint";
		auditAction(text,  targetName, isFirstSuggest, modelName);
	}


	private String constructRewriteHTMLResponseWithVars(String variablesJsonString, String rewriteHTML) {
		List<String> varsLists = new ArrayList<>();
		if(StringUtils.isEmpty(variablesJsonString)) {
			return rewriteHTML;
		}
		JSONObject variablesJson = new JSONObject(variablesJsonString);
		if(!variablesJson.has("variables_array")) {
			return rewriteHTML;
		}
		JSONArray variablesJsonArray = variablesJson.getJSONArray("variables_array");
		final ObjectMapper objectMapper = new ObjectMapper();

		try {
			varsLists = objectMapper.readValue(variablesJsonArray.toString(), new TypeReference<>(){
			});
		} catch(JsonProcessingException e) {
			e.printStackTrace();
		}
		String rewriteWithVars = rewriteHTML;
		Pattern pattern =   Pattern.compile("__\\s*VAR\\s*:\\s*(\\d+)\\s*__");
		Matcher matcher =   pattern.matcher(rewriteWithVars);
		while (matcher.find())
		{
			String groupText = matcher.group();
			int varNo = Integer.parseInt(groupText.replaceAll("__VAR\\s*:\\s*", "").replaceAll("\\s*__", ""));
			rewriteWithVars = rewriteWithVars.replace(groupText, varsLists.get(varNo));
		}

		return rewriteWithVars;
	}

	private void auditAction(String text,  String targetName, Boolean isFirstSuggest, String modelName) {
		if(StringUtils.isEmpty(text)) {
			return;
		}

		int suggestActionId = isFirstSuggest ? AuditActionType.ID_AI_FIRST_SUGGEST_ACTION : AuditActionType.ID_AI_SUGGEST_ACTION;
		int noOfWords = SemantexUtils.TEMP.getNumberOfWords(text);
		AuditEventUtil.push(AuditEventType.ID_AI_REWRITE, AuditObjectType.ID_AI_REWRITE, targetName, null,suggestActionId, AuditMetadataBuilder.forAIRewrite(modelName, noOfWords));
	}

	private String getRewriteSentimentResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		Boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, false);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, SemantexMessagepointClient.DEFAULT_MODEL);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("html");
		String input = isHTML ? contentJson.getString("html") : contentJson.getString("markup");

		JsonObject rewriteSentimentJsonResponse = semantexHandler.rewriteSentiment(input, isHTML, model);

		System.out.println("rewriteSentimentJsonResponse " + rewriteSentimentJsonResponse);

		if(hasError(rewriteSentimentJsonResponse)) {
			return rewriteSentimentJsonResponse.toString();
		}

		String rewriteHTML = rewriteSentimentJsonResponse.getAsJsonObject("result").getAsJsonObject().get("text").getAsString();
		if(isHTML) {
			rewriteHTML = cleanupHtml(rewriteHTML);
		}
		String text = isHTML ? extractTextFromHtml(rewriteHTML) : rewriteHTML;
		text = text.replaceAll("\\n{2,}", "\n");
		text = text.replace("\\.\n", ". \n");

		generateAuditForAction(contentJson, variablesJsonString, rewriteHTML, "Sentiment");

		JSONObject returnObj = constructRewriteSentimentResponse(verbose, isHTML, rewriteHTML, text);

		return returnObj.toString();
	}

	private JSONObject constructRewriteSentimentResponse(Boolean verbose, Boolean isHTML, String rewriteHTML, String text) {
		JSONArray txtArrayOut = new JSONArray();
		JSONArray txtArray = isHTML ? constructSentencesArrayFromHTML(rewriteHTML) : constructSentencesArray(text);
		LinkedList<Integer> paragraphIndexes = calculateSentencesNbInParagraphs(txtArray);
		// API accepts a max byte array length of 5000
		if(isHTML) {
			StringBuffer allText = constructAllText(txtArray);
			txtArrayOut = constructAllTextsArray(txtArray, allText);
		} else {
			int allTxtLength = text.getBytes().length;
			txtArrayOut.put(allTxtLength >= 5000 ? new String(Arrays.copyOfRange(text.getBytes(), 0, 4999), StandardCharsets.UTF_8) : text);
			for(int i = 0; i < txtArray.length(); i++) {
				txtArrayOut.put(txtArray.get(i));
			}
		}

		JSONObject returnObj = new JSONObject();
		computeSentimentResponse(returnObj, rewriteHTML, verbose, txtArrayOut, paragraphIndexes);
		return returnObj;
	}


	private String getSummarizeInfoResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		JSONObject contentJson = new JSONObject(content);
		String markup = contentJson.getString("text").replaceAll("\"", "\\\\\"");

		JSONObject summaryJsonObject = computeSummarizePropertiesInternal(markup);
		JSONObject returnObj = new JSONObject();
		returnObj.put("summarize", summaryJsonObject);
		returnObj.put("result", "success");
		returnObj.put("hasAIRewriteLicence" , MessagepointLicenceManager.getInstance().isLicencedForAIRewrites());

		return returnObj.toString();
	}
	private String getPlainLanguageInfoResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		JSONObject contentJson = new JSONObject(content);
		String markup = contentJson.getString("text").replaceAll("\"", "\\\\\"");

		JSONObject summaryJsonObject = computeSummarizePropertiesInternal(markup);
		JSONObject returnObj = new JSONObject();
		returnObj.put("plain", summaryJsonObject);
		returnObj.put("result", "success");
		returnObj.put("hasAIRewriteLicence" , MessagepointLicenceManager.getInstance().isLicencedForAIRewrites());

		return returnObj.toString();
	}

	private String getTranslateInfoResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		JSONObject contentJson = new JSONObject(content);

		String markup = contentJson.getString("text");
		String cleanHtml = cleanupHtml( contentJson.getString("text"));
		String text =extractTextFromHtml(cleanHtml);
		JsonObject detectLangResponse = semantexHandler.detectLanguage(text);

		if(hasError(detectLangResponse)) {
			return detectLangResponse.toString();
		}

		String detectedLanguageCode = detectLangResponse.getAsJsonObject("result").getAsJsonObject().get("label").getAsString();
		String langDisplay = "Not detected";
		String translateLanguageCode = "Not detected";

		String idsForDetectedCode = "";
		if(StringUtils.isNotEmpty(detectedLanguageCode) && !detectedLanguageCode.equalsIgnoreCase("none")) {
			List<Long> collectIdsList = MessagepointLocale.getLanguageLocales(detectedLanguageCode).stream().map(MessagepointLocale::getId).collect(Collectors.toList());
			idsForDetectedCode = StringUtils.join(collectIdsList, ',');
			String langDisplayCode = MessagepointLocale.getLanguageLocales(detectedLanguageCode).get(0).getLanguageDisplayCode();
			 langDisplay = ApplicationUtil.getMessage(langDisplayCode);

			String translateLocaleId = contentJson.getString("translateLocaleId");
			MessagepointLocale translateLocale = MessagepointLocale.findById(Long.parseLong(translateLocaleId));
			 translateLanguageCode = translateLocale.getLanguageCode();
		}
		markup = constructMarkupForTranslateViewHtml(markup);

		JSONObject jo = new JSONObject();
		jo.put("language", langDisplay);
		jo.put("crt_lang_code", detectedLanguageCode);
		jo.put("crt_ids_list", idsForDetectedCode);
		jo.put("translate_lang_code", translateLanguageCode);
		jo.put("source", text);
		jo.put("rewrite_html", markup);
		JSONObject returnObj = new JSONObject();
		returnObj.put("translate", jo);
		returnObj.put("result", "success");
		returnObj.put("hasAITranslateLicence" , MessagepointLicenceManager.getInstance().isLicencedForAITranslate());

		return returnObj.toString();
	}

	private String getRewriteSummarizeResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, SemantexMessagepointClient.DEFAULT_MODEL);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");
		
		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("html");
		String input = isHTML ? contentJson.getString("html") : contentJson.getString("markup").replaceAll("\"", "\\\\\"");
		String summarizeLimit = contentJson.getString("summarizeLimit").replaceAll("\"", "\\\\\"");

		JsonObject summarizeJsonResponse = semantexHandler.summarize(input, Integer.parseInt(summarizeLimit), isHTML, model);

		System.out.println("summarize Response " + summarizeJsonResponse);

		if(hasError(summarizeJsonResponse)) {
			return summarizeJsonResponse.toString();
		}

		String rewriteHTMLResponse = summarizeJsonResponse.getAsJsonObject("result").getAsJsonObject().get("summary").getAsString();

		String rewriteResponseWithVars = constructRewriteHTMLResponseWithVars(variablesJsonString, rewriteHTMLResponse);
		String textWithVars =  extractTextFromHtml(rewriteResponseWithVars);
		generateAuditForAction(contentJson, textWithVars, "Summarize");

		JSONObject summaryJsonObject = computeSummarizePropertiesInternal(rewriteResponseWithVars);
		JSONObject returnObj = new JSONObject();
		returnObj.put("summarize", summaryJsonObject);
		returnObj.put("result", "success");

		return returnObj.toString();
	}

	private String getRewritePlainLanguageResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, SemantexMessagepointClient.DEFAULT_MODEL);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("html");
		String input = isHTML ? contentJson.getString("html") : contentJson.getString("markup").replaceAll("\"", "\\\\\"");

		JsonObject plainLangResponse = semantexHandler.rewritePlainLanguage(input, isHTML, model);

		System.out.println("Plain Language Response " + plainLangResponse);

		if(hasError(plainLangResponse)) {
			return plainLangResponse.toString();
		}

		String rewriteHTMLResponse = plainLangResponse.getAsJsonObject("result").getAsJsonObject().get("text").getAsString();

		String rewriteResponseWithVars = constructRewriteHTMLResponseWithVars(variablesJsonString, rewriteHTMLResponse);
		String textWithVars =  extractTextFromHtml(rewriteResponseWithVars);
		generateAuditForAction(contentJson, textWithVars, "Plain Language");

		JSONObject plainJsonResult = computeSummarizePropertiesInternal(rewriteResponseWithVars);
		JSONObject returnObj = new JSONObject();
		returnObj.put("plain", plainJsonResult);
		returnObj.put("result", "success");

		return returnObj.toString();
	}
	private String getRewriteTranslateResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, SemantexMessagepointClient.DEFAULT_MODEL);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("html");
		String input = isHTML ? contentJson.getString("default_html") : contentJson.getString("markup").replaceAll("\"", "\\\\\"");

		String targetlocaleId = contentJson.getString("targetLocaleId");
		MessagepointLocale targetLocale = MessagepointLocale.findById(Long.parseLong(targetlocaleId));
		String localeLanguageCode = targetLocale.getLanguageDisplayCode();
		String targetLangDisplay = ApplicationUtil.getMessage(localeLanguageCode);
		String targetLangCode = targetLocale.getLanguageCode();

		String sourceLocaleId = contentJson.getString("sourceLocaleId");
		MessagepointLocale sourceLocale = MessagepointLocale.findById(Long.parseLong(sourceLocaleId));
		String sourceLangCode = sourceLocale.getLanguageCode();

		JsonObject translateResponse = semantexHandler.translate(input, isHTML, targetLangCode, model,  sourceLangCode);

		System.out.println("Translate Response " + translateResponse);

		if(hasError(translateResponse)) {
			translateResponse.addProperty("translation_accuracy", "f");
			translateResponse.addProperty("reason", "Translation accuracy not calculated due to translation error.");
			return translateResponse.toString();
		}

		String rewriteHTMLResponse = translateResponse.getAsJsonObject("result").getAsJsonObject().get("translation").getAsString();

		String rewriteResponseWithVarsPlaseholders = constructRewriteHTMLResponseWithVars(variablesJsonString, rewriteHTMLResponse);

		//String cleanupResponseWithVars = constructMarkupForTranslateViewHtml(rewriteResponseWithVarsPlaseholders);
		//add space before var tag if not exist
		String cleanupResponseWithVars = rewriteResponseWithVarsPlaseholders.replaceAll("(?<=\\w)(<var)", " $1");

		rewriteResponseWithVarsPlaseholders = cleanupHtml(rewriteHTMLResponse);
		rewriteResponseWithVarsPlaseholders = rewriteResponseWithVarsPlaseholders.replaceAll("(?<=\\w)(__VAR)", " $1");
		String textWithVars =  extractTextFromHtml(rewriteResponseWithVarsPlaseholders);
		generateAuditForAction(contentJson, textWithVars, "Translation");


		JSONObject jo = new JSONObject();
		jo.put("language", targetLangDisplay);
		jo.put("localeId", targetlocaleId);
		jo.put("source", input);
		jo.put("rewrite_html", rewriteResponseWithVarsPlaseholders);
		jo.put("rewrite_html_with_vars", cleanupResponseWithVars);
		JSONObject returnObj = new JSONObject();
		returnObj.put("translate", jo);
		returnObj.put("result", "success");

		return returnObj.toString();
	}

	private void callTranslateAccuracy(String translated, JSONObject returnObj, String defaultMarkup, String modelAcc, Boolean verbose, String defaultHtmlLang, String localeHtmlLang, String domain, String targetContentWithVars, String referenceContentWithVars) {
		defaultMarkup = defaultMarkup.replace(">\n<", "><");
		translated = translated.replace(">\n<", "><");
		if(isEmpty(defaultMarkup)) {
			if(isEmpty(translated)) {
				returnObj.put("translation_accuracy", "t");
				returnObj.put("reason", "Translation is accurate");
			} else {
				returnObj.put("translation_accuracy", "f");
				returnObj.put("reason", "The default language text is empty while the translated text is not empty.");
			}
			return;
		} else{
			if(isEmpty(translated)) {
				returnObj.put("translation_accuracy", "f");
				returnObj.put("reason", "The translated language text is empty while the translated text is not empty.");
				return;
			} else {
				String defaultContentHtml = constructMarkupForTranslateViewHtml(referenceContentWithVars);
				String translatedContentHtml = constructMarkupForTranslateViewHtml(targetContentWithVars);
				if(containsOnlyVarsAndNonAlphaChars(defaultContentHtml) && containsOnlyVarsAndNonAlphaChars(translatedContentHtml)) {
					String accuracyResponse = "t";
					String reason = "";
					if(getCleanTextWithWhitespaces(defaultContentHtml).equals(getCleanTextWithWhitespaces(translatedContentHtml))){
						reason = "Translation is accurate.";
					} else {
						String defaultWithoutWhitespace = getCleanTextWithWhitespaces(defaultContentHtml).replaceAll("\\s+","");
						String trWithoutWhitespace = getCleanTextWithWhitespaces(translatedContentHtml).replaceAll("\\s+","");
						if(!defaultWithoutWhitespace.equals(trWithoutWhitespace)) {
							accuracyResponse = "f";
							reason = "Translation is considered inaccurate. There are differences between source and translation.";

						} else{
							accuracyResponse = "f";
							reason = "Translation is considered inaccurate. There are whitespace differences between source and translation.";
						}
					}
					TranslateValidationStep.TranslateValidationResult response = new TranslateValidationStep().apply(null, TranslateValidationStep.Request.builder()
							.defaultContentHtml(defaultContentHtml)
							.translatedContentHtml(translatedContentHtml)
							.build()).result();

					constructResponse(returnObj, accuracyResponse, reason, response, translatedContentHtml, referenceContentWithVars);
				} else {
					JsonObject translationAccuracyResponse = semantexHandler.translationAccuracy(translated, defaultMarkup, modelAcc, verbose, defaultHtmlLang, localeHtmlLang, domain);

					if(hasError(translationAccuracyResponse)) {
						returnObj.put("translation_accuracy", "f");
						returnObj.put("reason", "Translation accuracy calculation error.");
						return;
					}
					String accuracyResponse = "";
					String reason = "";
					JsonObject result = translationAccuracyResponse.getAsJsonObject("result");
					if(result != null) {
						if(result.getAsJsonObject().get("label") != null) {
							accuracyResponse = result.getAsJsonObject().get("label").getAsString();
						}
						if(result.getAsJsonObject().get("explanation") != null) {
							reason = result.getAsJsonObject().get("explanation").getAsString();
						}
						if("f".equalsIgnoreCase(accuracyResponse) && (StringUtils.isNotEmpty(reason) && reason.contains("No translation has been performed."))) {
							accuracyResponse = "no";
						}
						if(StringUtils.isEmpty(reason)){
							if(accuracyResponse.equalsIgnoreCase("t")) {
								reason = "Translation is accurate.";
							}  else{
								reason = "Translation is inaccurate.";
							}
						}
					}

					TranslateValidationStep.TranslateValidationResult response = new TranslateValidationStep().apply(null, TranslateValidationStep.Request.builder()
							.defaultContentHtml(defaultContentHtml)
							.translatedContentHtml(translatedContentHtml)
							.build()).result();

					constructResponse(returnObj, accuracyResponse, reason, response, targetContentWithVars, referenceContentWithVars);
				}
			}
		}
	}

	private boolean isEmpty(String markup) {
		if(StringUtils.isEmpty(markup)) {
			return true;
		}
		org.jsoup.nodes.Document document = Jsoup.parse(markup);
		document.select(".mce-ico-txt").remove();
		String cleanupHtml = preProcessHtmlForTranslateAccuracyCall(document.html());

		String text = Jsoup.parse(cleanupHtml).body().text();
		return  StringUtils.isEmpty(text);
	}

	private static void constructResponse(JSONObject returnObj, String accuracyResponse, String reason, TranslateValidationStep.TranslateValidationResult response, String defaultHtml, String trHtml) {
		JsonObject validationVarsST = response.translateAccuracyVariablesJson();
		String accErrors = Optional.ofNullable(validationVarsST.get("message").getAsString()).orElse("");

		int varStErrorsNo = Optional.ofNullable(validationVarsST.get("count").getAsInt()).orElse(0);
		if("t".equals(accuracyResponse)  && varStErrorsNo == 0) {
			accuracyResponse ="t";
		} else if("f".equals(accuracyResponse) || varStErrorsNo > 0){
			accuracyResponse ="f";
		}
		if(StringUtils.isNotEmpty(accErrors)) {
			reason =  ApplicationUtil.getMessage("client_messages.content_editor.content_errors_prefix");
		}
		returnObj.put("translation_accuracy", accuracyResponse);
		returnObj.put("reason", reason);
		returnObj.put("contentErrors",  constructErrorDetailsHtml(accErrors, defaultHtml, trHtml) );
	}

	private boolean containsOnlyVarsAndNonAlphaChars(String html) {
		org.jsoup.nodes.Document document = Jsoup.parse(html);
		document.select(".mce-ico-txt").remove();
		String cleanupHtml = preProcessHtmlForTranslateAccuracyCall(document.html());
		String text = Jsoup.parse(cleanupHtml).body().text();
		text = text.replaceAll("__VAR__", "");

		if(text.matches(".*[a-zA-Z]+.*")) {
			return false;
		}
		text = text.replaceAll("&nbsp;", "");
		text = text.replaceAll("&ensp;", "");
		text = text.replaceAll("&emsp;", "");
		text = text.replaceAll("&thinsp;", "");
		text = text.replaceAll("\\s*", "");

		return !text.matches(".*[a-zA-Z]+.*");
	}

	private boolean isOnlyWhitespaceDifference(String sourceHtml, String translatedHtml) {
		String sourceCleanupHtml = preProcessHtmlForTranslateAccuracyCall(sourceHtml);
		String trCleanupHtml = preProcessHtmlForTranslateAccuracyCall(translatedHtml);
		String sourceText = getCleanTextWithWhitespaces(sourceCleanupHtml);

		String trText = getCleanTextWithWhitespaces(trCleanupHtml);
		if(sourceText.matches(".*[a-zA-Z]+.*") || trText.matches(".*[a-zA-Z]+.*")) {
			return false;
		}
		return !sourceText.equals(trText);
	}

	private static String getCleanTextWithWhitespaces(String trCleanupHtml) {
		String trText = Jsoup.clean(trCleanupHtml, "", Safelist.none(), new org.jsoup.nodes.Document.OutputSettings().prettyPrint(false));
		trText = trText.replaceAll("__VAR__", "");
		trText = trText.replace("&nbsp;", " ");
		trText = trText.replaceAll("&ensp;", " ");
		trText = trText.replaceAll("&emsp;", " ");
		trText = trText.replaceAll("&thinsp;", " ");
		return trText;
	}

	private static String constructErrorDetailsHtml(String accError, String defaultHtml, String trHtml) {

        if(StringUtils.isEmpty(accError)){
			return accError;
		}
		String resultHtml = constructMarkupForTranslateViewHtml(accError);

		if(StringUtils.isNotEmpty(accError)) {
			resultHtml =  ApplicationUtil.getMessage("client_messages.content_editor.content_errors") +  resultHtml;
		}
		if(resultHtml.contains( ApplicationUtil.getMessage("client_messages.content_editor.content_errors"))) {
			String[] split = resultHtml.split(ApplicationUtil.getMessage("client_messages.content_editor.content_errors"));
			String errvars = split[1].replace(ApplicationUtil.getMessage("client_messages.content_editor.content_errors"), "");
			String[] varsList = errvars.split("<br/>");
				org.jsoup.nodes.Document document = Jsoup.parse(constructMarkupForTranslateViewHtml(defaultHtml) + constructMarkupForTranslateViewHtml(trHtml));
			for(String var : varsList) {
				if(org.apache.commons.lang3.StringUtils.isEmpty(var)){
					continue;
				}
				if(var.contains("=")) {
					String[] attList = var.split("=");
					Elements elementsByAttributeValue = document.body().getElementsByAttributeValue(attList[0], attList[1]);
					if(elementsByAttributeValue != null && !elementsByAttributeValue.isEmpty()) {
						resultHtml = resultHtml.replace(var, elementsByAttributeValue.get(0).toString());
					}
				} else {
					if(TranslateAccuracyProcessor.INLINE_TARGETING_ERROR.equals(var)) {
						resultHtml = resultHtml.replace(var, ApplicationUtil.getMessage("client_messages.content_editor.inline_targeting_errors"));
					} else if(TranslateAccuracyProcessor.IMAGE_ERROR.equals(var)) {
						resultHtml = resultHtml.replace(var, ApplicationUtil.getMessage("client_messages.content_editor.image_errors"));
					}
				}
			}
		}
		return resultHtml;
	}
	private String getTranslationAccuracyResponseJSON(HttpServletRequest request) {

		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		String model = ServletRequestUtils.getStringParameter(request, PARAM_MODEL, "gpt4-o");
		Boolean verbose = ServletRequestUtils.getBooleanParameter(request, PARAM_VERBOSE, true);
		String variablesJsonString = ServletRequestUtils.getStringParameter(request, PARAM_VARIABLES, "");

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		String pattern = "__VAR:\\d+__";

		JSONObject contentJson = new JSONObject(content);
		boolean isHTML = contentJson.has("locale_html");
		String input = isHTML ? contentJson.getString("locale_html") : contentJson.getString("markup").replaceAll("\"", "\\\\\"");
		String referenceContentWithVars = constructRewriteHTMLResponseWithVars(variablesJsonString, input);
		String targetContentWithVars = constructRewriteHTMLResponseWithVars(variablesJsonString,  contentJson.getString("default_html"));
		input = input.replaceAll(pattern, "__VAR:X__");

		String defaultMarkup = contentJson.getString("default_html");
		defaultMarkup = defaultMarkup.replaceAll(pattern, "__VAR:X__");

		String targetlocaleId = contentJson.getString("targetLocaleId");
		MessagepointLocale targetLocale = MessagepointLocale.findById(Long.parseLong(targetlocaleId));
		String targetLangCode = targetLocale.getLanguageCode();

		String sourceLocaleId = contentJson.getString("sourceLocaleId");
		MessagepointLocale sourceLocale = MessagepointLocale.findById(Long.parseLong(sourceLocaleId));
		String sourceLangCode = sourceLocale.getLanguageCode();

		String domain ="general";
		JSONObject returnObj = new JSONObject();
		callTranslateAccuracy(input,  returnObj,  defaultMarkup, model,  verbose, sourceLangCode, targetLangCode, domain, targetContentWithVars, referenceContentWithVars);

		returnObj.put("result", "success");

		return returnObj.toString();
	}

	private String getRewriteAcceptResponseJSON(HttpServletRequest request) {
		String content = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);

		if(StringUtils.isEmpty(content)) {
			JsonObject returnObj = new JsonObject();
			returnObj.addProperty("error", true);
			returnObj.addProperty("message", getMessage("error.semantex.empty.content"));
			return returnObj.toString();
		}

		JSONObject contentJson = new JSONObject(content);
		String markup = contentJson.getString("markup");
		markup = cleanupHtml(markup);
		String text = extractTextFromHtml(markup);

		boolean isRationalizerModel = contentJson.getBoolean("isRationalizerModel");
		String modelName = isRationalizerModel ? "Rationalizer" : "Messagepoint";
		String targetName = contentJson.getString("type");
		int noOfWords = SemantexUtils.TEMP.getNumberOfWords(text);

		AuditEventUtil.push(AuditEventType.ID_AI_REWRITE, AuditObjectType.ID_AI_REWRITE, targetName, null, AuditActionType.ID_AI_ACCEPT_ACTION, AuditMetadataBuilder.forAIRewrite(modelName, noOfWords));

		JSONObject returnObj = new JSONObject();
		returnObj.put("result", "success");

		return returnObj.toString();
	}

	private boolean hasError(JsonObject jsonResponse) {
		Boolean hasError = ApplicationUtils.getValueForJsonPath(jsonResponse, "error", Boolean.class);
		return hasError != null && hasError;
	}

	private BrandProfile findBrandActiveProfile(HttpServletRequest request, Long rationalizerApplicationId) {
		long communicationId = ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATON_ID, -1);
		String contentObjectId = ServletRequestUtils.getStringParameter(request, PARAM_CONTENT_OBJECT_ID, "");
		boolean contentObjectIdIsLongValue = false;
		try {
			Long.parseLong(contentObjectId);

			contentObjectIdIsLongValue = true;
		} catch (NumberFormatException ex) {
			// Stay silent.
		}

		Document document = null;
		RationalizerApplication rationalizerApplication = null;
		if (contentObjectIdIsLongValue) {
			ContentObject contentObject = ContentObject.findByHttpServletRequest(request);
			if (contentObject != null) {
				document = contentObject.getDocument();
			}
		} else if ( communicationId > 0 ) {
			Communication communication = Communication.findById(communicationId);
			document = communication.getDocument();
		} else {
			RationalizerContent rationalizerContent = RationalizerContent.findRationalizerContentByElasticSearchGuid(contentObjectId);
			if (rationalizerContent != null) {
				rationalizerApplication = rationalizerContent.computeRationalizerApplication();
			}
		}

		if ( rationalizerApplication == null && rationalizerApplicationId != -1L ) {
			rationalizerApplication = RationalizerApplication.findById(rationalizerApplicationId);
		}

		BrandProfile activeProfile = BrandProfile.getAppliedProfile(document, rationalizerApplication);
		return activeProfile;
	}

	private String addLegalMarkViolations(BrandProfile activeProfile, String brandValidatedSentence, Pattern pattern) {
		Matcher matcher;
		matcher =   pattern.matcher(brandValidatedSentence);
		while (matcher.find()) {
			if (activeProfile.getEnforceLegalMarkSuperscripting()) {
				String match = matcher.group();
				int matchedLegalMarkLength = match.length() > 3 ? 3 : match.length();
				StringBuilder swap = new StringBuilder(brandValidatedSentence);
				swap.replace(matcher.end() - matchedLegalMarkLength, matcher.end(),
						"<span class=\"brandViolationHighlight\" type=\"superscripting\" data-toggle=\"tooltip\" data-html=\"true\" title=\"" + getMessage("client_messages.text.legal_mark_superscripting") + "\">" +
								match.substring(match.length() - matchedLegalMarkLength) +
								"</span>");
				brandValidatedSentence = swap.toString();
			}
			matcher = pattern.matcher(brandValidatedSentence);
		}

		return brandValidatedSentence;
	}

	private Boolean containsTarget(String targets, String sentence) throws UnsupportedEncodingException {
		String[] targetTerms = targets.split(";");
		String compareStr =  StringEscapeUtils.unescapeXml(sentence);
		for ( int i = 0; i < targetTerms.length; i++ ) {
			if (compareStr.toLowerCase().indexOf(targetTerms[i].trim().toLowerCase()) != -1)
				return true;
			else if (compareStr.replaceAll("’", "'").toLowerCase().indexOf(targetTerms[i].replaceAll("’", "'").trim().toLowerCase()) != -1)
				return true;
			else if (compareStr.replaceAll("'", "’").toLowerCase().indexOf(targetTerms[i].replaceAll("'", "’").trim().toLowerCase()) != -1)
				return true;
		}
		return false;
	}

	private Map<String,String> getTargetReplacementMap(String targets, String replacement) {
		Map<String,String> targetReplacementMap = new HashMap<>();

		String[] targetTerms = targets.split(";");
		for ( int i = 0; i < targetTerms.length; i++ ) {
			// Lower, capitalize, caps: for [’]
			targetReplacementMap.put(targetTerms[i].trim().toLowerCase().replaceAll("'","’"),replacement.trim().toLowerCase().replaceAll("'","’"));
			targetReplacementMap.put(targetTerms[i].trim().toUpperCase().replaceAll("'","’"),replacement.trim().toUpperCase().replaceAll("'","’"));
			targetReplacementMap.put(targetTerms[i].trim().substring(0, 1).toUpperCase() + targetTerms[i].trim().substring(1).replaceAll("'","’"), replacement.trim().substring(0, 1).toUpperCase() + replacement.trim().substring(1).replaceAll("'","’"));
			// Lower, capitalize, caps: for [']
			targetReplacementMap.put(targetTerms[i].trim().toLowerCase().replaceAll("’","'"),replacement.trim().toLowerCase().replaceAll("’","'"));
			targetReplacementMap.put(targetTerms[i].trim().toUpperCase().replaceAll("’","'"),replacement.trim().toUpperCase().replaceAll("’","'"));
			targetReplacementMap.put(targetTerms[i].trim().substring(0, 1).toUpperCase() + targetTerms[i].trim().substring(1).replaceAll("’","'"), replacement.trim().substring(0, 1).toUpperCase() + replacement.trim().substring(1).replaceAll("’","'"));
		}

		return targetReplacementMap;
	}

	private JSONArray constructAllTextsArray(JSONArray txtJsonArray, StringBuffer allText) {
		JSONArray txtSplitArray = new JSONArray();
		JSONArray txtArrayOut = new JSONArray();
		txtArrayOut.put(allText.toString());
		for (int i = 0; i < txtJsonArray.length(); i++ ) {
			JSONArray splitted = constructSentencesArray(txtJsonArray.get(i).toString());
			for ( int j = 0; j < splitted.length(); j++ ) {
				txtSplitArray.put(splitted.get(j));
			}
		}
		for ( int i = 0; i < txtSplitArray.length(); i++ ) {
			txtArrayOut.put(txtSplitArray.get(i));
		}
		return txtArrayOut;
	}

	private StringBuffer constructAllText(JSONArray txtJsonArray) {
		StringBuffer allText = new StringBuffer();

		for(int i = 0; i < txtJsonArray.length(); i++) {
			String crt = txtJsonArray.get(i).toString();
			if(!crt.trim().isEmpty()) {
				if (crt.matches(".*\\p{Punct}")){
					allText.append(crt).append(" ");
				} else {
					allText.append(crt);
				}
			} else{
				allText.append(txtJsonArray.get(i));
			}
		}
		return allText;
	}

	private JSONArray constructSentencesArray(String text) {
		String[] splitText = SentenceSpliter.DEFAULT.split(text);
		JSONArray txtArrayOut = new JSONArray();
		for (int i = 0; i < splitText.length; i++) {
			if(i < splitText.length -1) {
				String crt = splitText[i];
				if(crt.matches(".*\\p{Punct}")) {
					txtArrayOut.put(crt + " ");
				} else {
					txtArrayOut.put(crt);
				}
			} else {
				txtArrayOut.put(splitText[i]);
			}
		}
		return txtArrayOut;
	}

	private String cleanupHtml(String html) {
		html=html.replaceAll(">\\s*<", "><");
		html=html.replaceAll("\\n", "<br/>");
		try {
			org.jsoup.nodes.Document jsoupDoc = Jsoup.parse(html);
			org.jsoup.nodes.Document.OutputSettings outputSettings = new org.jsoup.nodes.Document.OutputSettings();
			outputSettings.prettyPrint(false);
			jsoupDoc.outputSettings(outputSettings);
			for (Element element : jsoupDoc.select("*")) {
				if ((!element.hasText() && element.isBlock()) || (!element.hasText() && element.childNodes().isEmpty()))  {
					if(!element.tagName().equals("br")) {
						element.remove();
					}
				}
				if (element.hasText() && element.hasClass("renderedLabelContainer")) {
					element.remove();
				}
			}

			return jsoupDoc.body().html();
		} catch (Exception e) {
			log.error("AsyncContentIntelligenceController:extractTextFromHtml: Error extracting text from html: " + html);
			e.printStackTrace();
		}
		return html;
	}


	private String extractTextFromHtml(String html) {
		String text = "";
		try {
			org.jsoup.nodes.Document jsoupDoc = Jsoup.parse(html);
			org.jsoup.nodes.Document.OutputSettings outputSettings = new org.jsoup.nodes.Document.OutputSettings();
			outputSettings.prettyPrint(false);
			jsoupDoc.outputSettings(outputSettings);
			jsoupDoc.select("p, li, th, div, br").append("\n");
			String str = jsoupDoc.html().replaceAll("\\\\n", "\n");
			text = Jsoup.clean(str, "", Safelist.none(), outputSettings);
		} catch (Exception e) {
			log.error("AsyncContentIntelligenceController:extractTextFromHtml: Error extracting text from html: " + html);
			e.printStackTrace();
		}
		return text;
	}

	private JSONArray constructSentencesArrayFromHTML(String html) {
		JSONArray txtArrayOut = new JSONArray();
		org.jsoup.nodes.Document jsoupDoc = Jsoup.parse(html);
		org.jsoup.nodes.Document.OutputSettings outputSettings = new org.jsoup.nodes.Document.OutputSettings();
		outputSettings.prettyPrint(false);
		jsoupDoc.outputSettings(outputSettings);
		jsoupDoc.select("br").before("\\n");
		if(jsoupDoc.select("p, li, td, th, div").isEmpty()) {
			return constructSentencesArray(html);
		}
		jsoupDoc.select("p, li, td, th, div").forEach(element -> {
					String crt = element.text();
					if(element.tagName().equals("td") || element.tagName().equals("th")) {
						crt = element.ownText();
					}

					if(!StringUtils.isEmpty(crt)) {
						if(crt.matches(".*\\p{Punct}")) {
							txtArrayOut.put(crt + " ");
						} else {
							txtArrayOut.put(crt);
						}
					}
				}
		);

		return txtArrayOut;
	}
}