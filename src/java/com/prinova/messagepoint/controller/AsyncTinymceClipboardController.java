package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.content.ClipboardContent;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.content.CreateOrUpdateClipboardContentService;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringEscapeUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class AsyncTinymceClipboardController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncTinymceClipboardController.class);

	public static final String PARAM_ACTION 					= "action";
	public static final String PARAM_CONTENT					= "content";
	public static final String PARAM_OBJECT_ID					= "objectId";
	public static final String PARAM_SEARCH_VALUE				= "sSearch";
	public static final String PARAM_SHARED						= "shared";
	public static final String PARAM_TAGS						= "tags";
	public static final String PARAM_LOCALE_ID					= "localeId";
	
	public static final String ACTION_LIST			 			= "list";
	public static final String ACTION_ADD_CONTENT			 	= "add_content";
	public static final String ACTION_REMOVE_CONTENT			= "remove_content";

	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		String action = ServletRequestUtils.getStringParameter(request, PARAM_ACTION);

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			
			if ( action.equalsIgnoreCase(ACTION_LIST) ) {
				out.write(getListResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( action.equalsIgnoreCase(ACTION_ADD_CONTENT) ) {
				out.write(getAddContentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			} else if ( action.equalsIgnoreCase(ACTION_REMOVE_CONTENT) ) {
				out.write(getRemoveContentResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			}
			
		} catch (Exception e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", "Error - Unable to retrieve clipboard response: " + e.getMessage());
			log.error("Error: Unable to retrieve clipboard response: " + e.getMessage() );
			out.write(returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
		}

		return null;
	}
	
	private String getListResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();

		String search 		= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		long localeId 		= ServletRequestUtils.getLongParameter(request, PARAM_LOCALE_ID, 1);
		
		User principal = UserUtil.getPrincipalUser();
		User user = User.findById(principal.getId());

		try {
			
			MessagepointLocale locale = MessagepointLocale.findById(localeId);

			JSONArray clipboardContentArray = new JSONArray();
			List<ClipboardContent> clipboardContent = ClipboardContent.list(user.getId(), user.getWorkgroup() != null ? user.getWorkgroupId() : -1, search);
			for (ClipboardContent currentItem: clipboardContent) {

				JSONObject contentObj = new JSONObject();
				contentObj.put("metatags", currentItem.getMetatags());
				contentObj.put("is_mine", !currentItem.getIsShared());
				contentObj.put("content", currentItem.getContent() != null && currentItem.getContent().getEncodedValue() != null ?
						StringEscapeUtils.escapeXml( ContentObjectContentUtil.translateContentForView( currentItem.getContent().getEncodedValue(), UserUtil.getCurrentTouchpointContext(), locale, null) ) : "ERROR");
				contentObj.put("id", currentItem.getId());
				
				clipboardContentArray.put( contentObj );

			}
			returnObj.put("contents", clipboardContentArray);
			
			returnObj.put("result", "success");
			
		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve clipboard listing: " + e.getMessage());
				log.error("Error: Unable to retrieve clipboard listing: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error: Unable to retrieve clipboard listing: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
	private String getAddContentResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		Boolean isShared 	= ServletRequestUtils.getBooleanParameter(request, PARAM_SHARED, false);
		String content 		= ServletRequestUtils.getStringParameter(request, PARAM_CONTENT, null);
		String metatags		= ServletRequestUtils.getStringParameter(request, PARAM_TAGS, null);
		
		User principal = UserUtil.getPrincipalUser();
		User user = User.findById(principal.getId());

		try {
			
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateClipboardContentService.SERVICE_NAME, CreateOrUpdateClipboardContentService.class);
			ServiceExecutionContext context = CreateOrUpdateClipboardContentService.createContextForNew(metatags, content, user, isShared);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				returnObj.put("error", true);
				returnObj.put("message", "Create clipboard content service failed: " + ServiceResponseConverter.getFirstErrorDescription(serviceResponse) );
			} else {
				returnObj.put("result", "success");
				returnObj.put("id", ((ClipboardContent)serviceResponse.getResultValueBean()).getId());
			}

		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve add content response: " + e.getMessage());
				log.error("Error - Unable to retrieve add content response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve add content response: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
	private String getRemoveContentResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		long clipboardContentId = ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, -1L);

		try {
			
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateClipboardContentService.SERVICE_NAME, CreateOrUpdateClipboardContentService.class);
			ServiceExecutionContext context = CreateOrUpdateClipboardContentService.createContextForRemove(clipboardContentId);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				returnObj.put("error", true);
				returnObj.put("message", "Create clipboard content service failed: " + ServiceResponseConverter.getFirstErrorDescription(serviceResponse) );
			} else {
				returnObj.put("result", "success");
			}

		} catch (JSONException e) {
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Error - Unable to retrieve remove content response: " + e.getMessage());
				log.error("Error - Unable to retrieve remove content response: " + e.getMessage() );
			} catch (JSONException e2) {
				log.error("Error - Unable to retrieve remove content response: " + e.getMessage() );
			}

		}

		return returnObj.toString();

	}
	
}