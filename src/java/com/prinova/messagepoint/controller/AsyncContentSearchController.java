package com.prinova.messagepoint.controller;

import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.*;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.wrapper.SearchResultVO;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncContentSearchController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentSearchController.class);

	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_OBJECT_ID					= "objId";
	public static final String PARAM_SEARCH_VALUE				= "sSearch";
	public static final String PARAM_SEARCH_TYPE 				= "searchType";
	public static final String PARAM_QUERY_TYPE 				= "queryType";
	public static final String PARAM_ADVANCED 					= "advanced";

	public static final String QUERY_TYPE_PRIMARY_QUERY			= "primary";
	public static final String QUERY_TYPE_VARIANT_QUERY			= "variant";
	
	public static final String SEARCH_TYPE_MESSAGE				= "message";
	public static final String SEARCH_TYPE_EMBEDDED_TEXT		= "embeddedText";
	public static final String SEARCH_TYPE_SMART_CONTENT		= "smartContent";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_QUERY_TYPE, null);
		
		if ( type.equalsIgnoreCase(QUERY_TYPE_PRIMARY_QUERY) ) {
			try {
				out.write(getContentSearchResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for content search results: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(QUERY_TYPE_VARIANT_QUERY) ) {
			try {
				out.write(getVariantContentSearchResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for content search results: "+e.getMessage(),e);
			}
		}
		
		return null;
	}
	
	private String getContentSearchResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		String searchValue 			= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		String searchType 			= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_TYPE, null);
		boolean isAdvancedSearch 	= ServletRequestUtils.getBooleanParameter(request, PARAM_ADVANCED, false);
		
		try {

			List<SearchResultVO> results = ContentObject.searchContent(searchValue, isAdvancedSearch, documentId, searchType);

			returnObj.put("documentId"		, documentId);
			returnObj.put("searchType"		, searchType);
			returnObj.put("searchValue"		, searchValue);
			
			JSONArray resultsArray = new JSONArray();
			
			Document document = Document.findById(documentId);
			User principalUser = UserUtil.getPrincipalUser();
			User requestor = User.findById(principalUser.getId());
			if ( searchType.equalsIgnoreCase(SEARCH_TYPE_MESSAGE) ) {
				TouchpointSelection masterTouchpointSelection = document.getMasterTouchpointSelection();
				boolean primaryAccessRestricted = true;
				if ( ( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) ) &&
					 ( masterTouchpointSelection == null || masterTouchpointSelection.isVisible(requestor) || masterTouchpointSelection.isFullyVisible() ) )
					primaryAccessRestricted = false;
				returnObj.put("primaryAccessRestricted"	, primaryAccessRestricted );
			} else if ( searchType.equalsIgnoreCase(SEARCH_TYPE_SMART_CONTENT) ) {
				TouchpointSelection masterTouchpointSelection = document.getMasterTouchpointSelection();
				boolean primaryAccessRestricted = true;
				if ( UserUtil.isPermissionGranted(Permission.ROLE_TOUCHPOINT_CONTENT_VIEW) &&
					 ( masterTouchpointSelection == null || masterTouchpointSelection.isVisible(requestor) || masterTouchpointSelection.isFullyVisible() ) )
					primaryAccessRestricted = false;
				returnObj.put("primaryAccessRestricted"	, primaryAccessRestricted );
			} else if ( searchType.equalsIgnoreCase(SEARCH_TYPE_EMBEDDED_TEXT) ) {
				returnObj.put("primaryAccessRestricted"	, false );
			}
			
			for (SearchResultVO currentResult: results) {
				
				JSONObject searchResult = new JSONObject();

				searchResult.put("name"							, currentResult.getDisplayObject().getName() );
				searchResult.put("objId"						, currentResult.getDisplayObject().getId() );
				searchResult.put("isLocalRef"					, currentResult.getDisplayObjectIsLocalReference() );
				searchResult.put("objLanguageCodes"				, currentResult.getDisplayObjectLanguagesDisplay() );
				if ( searchType.equalsIgnoreCase(SEARCH_TYPE_MESSAGE) || searchType.equalsIgnoreCase(SEARCH_TYPE_SMART_CONTENT) ) {
					TouchpointSelection owningTouchpointSelection = ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().getOwningTouchpointSelection();
					searchResult.put("isDynamic"				, ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled());
					searchResult.put("isStructured"				, ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().isStructuredContentEnabled() );
					searchResult.put("touchpointSelectionId"	, owningTouchpointSelection != null ? owningTouchpointSelection.getId() : -1 );
					searchResult.put("objAccessRestricted"		, !(owningTouchpointSelection == null || (owningTouchpointSelection != null && (owningTouchpointSelection.isFullyVisible() || owningTouchpointSelection.isVisible(requestor)))) );
					searchResult.put("isFreeform"				, ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().getIsFreeform() );
				} else if ( searchType.equalsIgnoreCase(SEARCH_TYPE_EMBEDDED_TEXT) ) {
					searchResult.put("isDynamic"				, ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled() );
					searchResult.put("isStructured"				, false );
					searchResult.put("touchpointSelectionId"	, -1 );
					searchResult.put("objAccessRestricted"		, false );
					searchResult.put("isFreeform"				, ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().getIsSharedFreeform() );
				}
				
				Boolean hasActive = currentResult.getActiveCopyContentObject() != null;
				searchResult.put("hasActiveObj"					, hasActive );
				if ( hasActive ) {
					searchResult.put("activeObjId"				, currentResult.getActiveCopyContentObject().getId() );
					searchResult.put("isLocalActiveRef"			, currentResult.getActiveCopyLocalReference() );
					searchResult.put("activeLanguageCodes"		, currentResult.getActiveCopyLanguagesDisplay() );
				}
				
				Boolean hasWorkingCopy = currentResult.getWorkingCopyContentObject() != null;
				searchResult.put("hasWorkingCopyObj"			, hasWorkingCopy );
				if ( hasWorkingCopy ) {
					searchResult.put("workingCopyObjId"			, currentResult.getWorkingCopyContentObject().getId() );
					searchResult.put("isLocalWorkingCopyRef"	, currentResult.getWorkingCopyLocalReference() );
					searchResult.put("workingCopyLanguageCodes"	, currentResult.getWorkingCopyLanguagesDisplay() );
				}

				resultsArray.put(searchResult);
				
			}
			
			returnObj.put("matches", resultsArray);

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve content search: " + e );
			return "{ error: 'Search query failed'}";
		}

		return returnObj.toString();
	}

	private String getVariantContentSearchResponseJSON (HttpServletRequest request) {

		JSONObject returnObj = new JSONObject();
		
		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		long parentObjId 			= ServletRequestUtils.getLongParameter(request, PARAM_OBJECT_ID, 0);
		String searchValue 			= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, null);
		String searchType 			= ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_TYPE, null);
		boolean isAdvancedSearch 	= ServletRequestUtils.getBooleanParameter(request, PARAM_ADVANCED, false);
		
		try {

			returnObj.put("documentId"		, documentId);
			returnObj.put("searchType"		, searchType);
			returnObj.put("searchValue"		, searchValue);
			returnObj.put("parentObjId"		, parentObjId);

			List<SearchResultVO> results = ContentObject.searchContent(searchValue, isAdvancedSearch, documentId, parentObjId, searchType);

			JSONArray resultsArray = new JSONArray();
			
			Document document = Document.findById(documentId);
			User principalUser = UserUtil.getPrincipalUser();
			User requestor = User.findById(principalUser.getId());
			if ( searchType.equalsIgnoreCase(SEARCH_TYPE_MESSAGE) || searchType.equalsIgnoreCase(SEARCH_TYPE_SMART_CONTENT)  ) {
				boolean primaryAccessRestricted = false;
				if ( document.isEnabledForVariation() && !( UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_MESSAGE_VIEW_MY) ) )
					primaryAccessRestricted = true;
				returnObj.put("primaryAccessRestricted"	, primaryAccessRestricted );
			} else if ( searchType.equalsIgnoreCase(SEARCH_TYPE_EMBEDDED_TEXT) ) {
				returnObj.put("primaryAccessRestricted"	, false );
			}
			
			for (SearchResultVO currentResult: results) {
				
				JSONObject searchResult = new JSONObject();

				searchResult.put("name"								, currentResult.getDisplaySelectionObjectName() );
				searchResult.put("objId"							, currentResult.getDisplayObject().getId() );
				searchResult.put("objVariantId"						, currentResult.getDisplaySelectionObjectId() );
				searchResult.put("objLanguageCodes"					, currentResult.getDisplayObjectLanguagesDisplay() );
				if ( searchType.equalsIgnoreCase(SEARCH_TYPE_MESSAGE) || searchType.equalsIgnoreCase(SEARCH_TYPE_SMART_CONTENT)  ) {

					TouchpointSelection touchpointSelection = null;
					
					if ( ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().isStructuredContentEnabled() ) {
						
						touchpointSelection = currentResult.getDisplayTouchpointSelection();
						
						searchResult.put("touchpointSelectionId"		, touchpointSelection.getId() );
						
						boolean hasActive = currentResult.getActiveCopyPGTNObject() != null;
						searchResult.put("hasActiveObj"					, hasActive );
						if (hasActive) {
							searchResult.put("activeObjId"				, currentResult.getActiveCopyContentObject().getId() );
							searchResult.put("activeCopyVariantId"		, currentResult.getActiveCopyPGTNObject().getId() );
							searchResult.put("activeLanguageCodes"		, currentResult.getActiveCopyLanguagesDisplay() );
						}
						
						boolean hasWorkingCopy = currentResult.getWorkingCopyPGTNObject() != null;
						searchResult.put("hasWorkingCopyObj"			, hasWorkingCopy );
						if (hasWorkingCopy) {
							searchResult.put("workingCopyObjId"			, currentResult.getWorkingCopyContentObject().getId() );
							searchResult.put("workingCopyVariantId"		, currentResult.getWorkingCopyPGTNObject().getId() );
							searchResult.put("workingCopyLanguageCodes"	, currentResult.getWorkingCopyLanguagesDisplay() );
						}
						
					} else if ( ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled() ) {
						searchResult.put("touchpointSelectionId"		, -1 );
						
						boolean hasActive = currentResult.getActiveCopyPGTNObject() != null;
						searchResult.put("hasActiveObj"					, hasActive );
						if (hasActive) {
							searchResult.put("activeObjId"				, currentResult.getActiveCopyContentObject().getId() );
							searchResult.put("activeCopyVariantId"		, currentResult.getActiveCopyPGTNObject().getId() );
							searchResult.put("activeLanguageCodes"		, currentResult.getActiveCopyLanguagesDisplay() );
						}
						
						boolean hasWorkingCopy = currentResult.getWorkingCopyPGTNObject() != null;
						searchResult.put("hasWorkingCopyObj"			, hasWorkingCopy );
						if (hasWorkingCopy) {
							searchResult.put("workingCopyObjId"			, currentResult.getWorkingCopyContentObject().getId() );
							searchResult.put("workingCopyVariantId"		, currentResult.getWorkingCopyPGTNObject().getId() );
							searchResult.put("workingCopyLanguageCodes"	, currentResult.getWorkingCopyLanguagesDisplay() );
						}

					}
					boolean isStructured = ((ContentObjectData)currentResult.getDisplayObject()).getContentObject().isStructuredContentEnabled();
					searchResult.put("isDynamic"					, ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled());
					searchResult.put("isStructured"					, isStructured );
					searchResult.put("objAccessRestricted"			, !(!isStructured || (isStructured && touchpointSelection != null && (touchpointSelection.isFullyVisible() || touchpointSelection.isVisible(requestor)))) );
				} else if ( searchType.equalsIgnoreCase(SEARCH_TYPE_EMBEDDED_TEXT) ) {
					if ( ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled() ) {
						searchResult.put("touchpointSelectionId"	, -1 );
						
						boolean hasActive = currentResult.getActiveCopyPGTNObject() != null;
						searchResult.put("hasActiveObj"					, hasActive );
						if (hasActive) {
							searchResult.put("activeObjId"				, currentResult.getActiveCopyContentObject().getId() );
							searchResult.put("activeCopyVariantId"		, currentResult.getActiveCopyPGTNObject().getId() );
							searchResult.put("activeLanguageCodes"		, currentResult.getActiveCopyLanguagesDisplay() );
						}
						
						boolean hasWorkingCopy = currentResult.getWorkingCopyPGTNObject() != null;
						searchResult.put("hasWorkingCopyObj"			, hasWorkingCopy );
						if (hasWorkingCopy) {
							searchResult.put("workingCopyObjId"			, currentResult.getWorkingCopyContentObject().getId() );
							searchResult.put("workingCopyVariantId"		, currentResult.getWorkingCopyPGTNObject().getId() );
							searchResult.put("workingCopyLanguageCodes"	, currentResult.getWorkingCopyLanguagesDisplay() );
						}
					}
					searchResult.put("isDynamic"					, ((ContentObjectData)currentResult.getDisplayObject()).isDynamicVariantEnabled() );
					searchResult.put("isStructured"					, false );
					searchResult.put("objAccessRestricted"			, false );
				}

				resultsArray.put(searchResult);
				
			}
			
			returnObj.put("matches", resultsArray);

		} catch (JSONException e) {
			log.error("Error: Unable to retrieve content search: " + e );
		}

		return returnObj.toString();
	}
}