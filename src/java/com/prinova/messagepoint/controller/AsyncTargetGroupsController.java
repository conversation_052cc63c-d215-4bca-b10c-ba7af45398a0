package com.prinova.messagepoint.controller;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.targeting.ConditionElement;
import com.prinova.messagepoint.model.targeting.ConditionItem;
import com.prinova.messagepoint.model.targeting.ConditionItemValue;
import com.prinova.messagepoint.model.targeting.ConditionSubelement;
import com.prinova.messagepoint.model.targeting.TargetGroup;
import com.prinova.messagepoint.model.targeting.TargetGroupInstance;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.ConditionElementUtil;
import com.prinova.messagepoint.util.TargetGroupUtil;

public class AsyncTargetGroupsController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncTargetGroupsController.class);

	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_TARGET_GROUP_ID			= "targetGroupId";
	public static final String PARAM_NAME_SEARCH				= "sSearch";
	public static final String PARAM_NUM_CAP					= "numCap";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_ADVANCED 					= "advanced";
	
	public static final String TYPE_TARGET_GROUP_LIST			= "targetGroupsList";
	public static final String TYPE_TARGET_GROUP_DEFINITION		= "targetGroupDefinition";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
		
		if ( type.equalsIgnoreCase(TYPE_TARGET_GROUP_LIST) ) {
			try {
				out.write(getTargetGroupListResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
			}
		} else if ( type.equalsIgnoreCase(TYPE_TARGET_GROUP_DEFINITION) ) {
			try {
				out.write(getTargetGroupDefinitionResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request for rule definition data: "+e.getMessage(),e);
			}
		}
		
		return null;
	}
	
	private String getTargetGroupDefinitionResponseJSON (HttpServletRequest request) {
		
		JSONObject returnObj = new JSONObject();
		
		long targetGroupId 					= ServletRequestUtils.getLongParameter(request, PARAM_TARGET_GROUP_ID, 0);
		TargetGroup targetGroup 			= TargetGroup.findById(targetGroupId);
		
		try {

			Set<ConditionElement> conditionElements = new HashSet<>();
			Map<Long,Set<Long>> conditionItemValueMap = new HashMap<>();
			Map<Long, String> conditionSubelementValueTypes = new HashMap<>();

			TargetGroupInstance tgInstance = targetGroup.getInstance();
			TargetGroupUtil.loadInstanceValues(tgInstance, conditionElements, conditionItemValueMap, conditionSubelementValueTypes);

			returnObj.put("name"			, targetGroup.getName());
			returnObj.put("id"				, targetGroup.getId());
			returnObj.put("instance_id"		, targetGroup.getInstance().getId());
			returnObj.put("relationship" 	, targetGroup.getConditionRelationship() == 2 ? ApplicationUtil.getMessage("page.label.and")  : "or");
			returnObj.put("metatags"		, targetGroup.getMetatags() != null ? targetGroup.getMetatags() :  ApplicationUtil.getMessage("page.label.none") );
			
			JSONArray rulesArray = new JSONArray();
			
			for (ConditionItem currentItem: targetGroup.getInstance().getConditionItemsSorted()) {
				
				ConditionElement conditionElement = currentItem.getConditionElement();
				
				JSONObject ruleObj = new JSONObject();

				ruleObj.put("name"				, conditionElement.getName());
				ruleObj.put("id"				, conditionElement.getId());
				ruleObj.put("metatags"			, conditionElement.getMetatags() != null ? conditionElement.getMetatags() :  ApplicationUtil.getMessage("page.label.none") );
				
				Boolean ruleIsParameterized = targetGroup.getInstance().getConditionParamMap().get(currentItem) == null || targetGroup.getInstance().getConditionParamMap().get(currentItem) == false ? false : true;
				ruleObj.put("is_parameterized"	, ruleIsParameterized );
				
				JSONArray conditionArray = new JSONArray();
	
				for ( ConditionItemValue currentItemValue: currentItem.getConditionItemValues() ) {
					ConditionSubelement currentCondition = currentItemValue.getConditionSubelement();

					if ( conditionItemValueMap.get(currentItem.getId()).contains(currentItemValue.getId()) ) {
						JSONObject conditionObj = new JSONObject();
		
						conditionObj.put("name"				, currentCondition.getName() );
						conditionObj.put("id"				, currentCondition.getId() );
						conditionObj.put("variable_name"	, currentCondition.getDataElementVariable() != null ? currentCondition.getDataElementVariable().getDisplayName() : ApplicationUtil.getMessage("page.text.no.variable.brackets")  );
						conditionObj.put("is_parameterized"	, currentCondition.isParameterized() );
						conditionObj.put("comparator"		, ApplicationUtil.getMessage(currentCondition.getDataComparison().getName()) );
						conditionObj.put("comparator_id"	, currentCondition.getDataComparison().getId() );
						conditionObj.put("relationship"		, currentCondition.getConditionType().getId() == 2 ? ApplicationUtil.getMessage("page.label.and") : ApplicationUtil.getMessage("page.label.or")  );
						
						String conditionSubelementType = ConditionElementUtil.getDataElementValueType(	currentCondition.getDataElementComparisonId(), 
								currentCondition.getDataElementVariable() != null ? currentCondition.getDataElementVariable().getDataSubtypeId() : 0);
						conditionObj.put("is_date_value"	, ConditionElementUtil.TYPE_DATE.equalsIgnoreCase(conditionSubelementType) || ConditionElementUtil.TYPE_DATE_RANGE.equalsIgnoreCase(conditionSubelementType) );
						conditionObj.put("is_data_file_value", currentCondition.getDataFilePath() != null);
						
						if ( currentCondition.isParameterized() && ruleIsParameterized )
							conditionObj.put("is_range_value"		, ConditionElementUtil.TYPE_SIMPLE_RANGE.equalsIgnoreCase(conditionSubelementType) || ConditionElementUtil.TYPE_DATE_RANGE.equalsIgnoreCase(conditionSubelementType) );
						else
							conditionObj.put("display_value"		, currentCondition.isParameterized() ? currentItemValue.getDataElementDisplayValue() : currentCondition.getDataElementDisplayValue() );
						
						if ( currentCondition.getFilterCondition() != null )
							conditionObj.put("filter_display_value"	,  currentCondition.getFilterCondition().getDisplayValue() );
						
						conditionArray.put(conditionObj);
					}
		
				}
				
				ruleObj.put("conditions", conditionArray);
				
				rulesArray.put(ruleObj);
			
			}
			
			returnObj.put("rules", rulesArray);
			
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve rule definition: " + e );
		}

		return returnObj.toString();
	}

	private String getTargetGroupListResponseJSON (HttpServletRequest request) {

		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		String nameSearch 			= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);
		int numCap 					= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
		boolean isAdvancedSearch 	= ServletRequestUtils.getBooleanParameter(request, PARAM_ADVANCED, false);
		
		JSONObject returnObj = new JSONObject();
		
		try {

			returnObj.put("documentId", documentId);
			returnObj.put("search", 	nameSearch);
			returnObj.put("maxResults", numCap);

			List<TargetGroup> tgList = null;

			tgList = TargetGroup.findAllByDocument(Document.findById(documentId), nameSearch, isAdvancedSearch, numCap, true);

			JSONArray tgListArray = new JSONArray();
			
			for (int i=0; i < tgList.size(); i++) {
				JSONObject tgObj = new JSONObject();
				tgObj.put("name", tgList.get(i).getName());
				tgObj.put("id"	, tgList.get(i).getId());
				
				tgListArray.put(tgObj);
			}
			
			returnObj.put("targetGroups", tgListArray);

			
		} catch (JSONException e) {
			log.error("Error: Unable to retrieve target group list: " + e );
		}

		return returnObj.toString();
	}
}