package com.prinova.messagepoint.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObject;
import org.apache.commons.lang.StringEscapeUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.tag.TagUtils;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncContentObjectController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContentObjectController.class);

	public static final String PARAM_CONTENT_OBJECT_ID					= "contentObjectId";
	public static final String PARAM_CONTENT_LIBRARY_ID					= "imageLibraryId";
	public static final String PARAM_SELECTED_CONTENT_LIBRARY_ID		= "selectedContentLibraryId";
	public static final String PARAM_SELECTED_LOCAL_CONTENT_LIBRARY_ID	= "selectedLocalContentLibraryId";
	public static final String PARAM_DOCUMENT_ID						= "documentId";
	public static final String PARAM_COMMUNICATION_ID					= "communicationId";
	public static final String PARAM_ZONE_ID							= "zoneId";
	public static final String PARAM_NAME_SEARCH						= "sSearch";
	public static final String PARAM_EXT_TYPE							= "extType";
	public static final String PARAM_NUM_CAP							= "numCap";
	public static final String PARAM_TYPE 								= "type";
	public static final String PARAM_CONTEXT							= "context";

	public static final String TYPE_CON_LIB_LIST						= "conLibList";
	public static final String TYPE_LOCAL_CON_LIB_LIST					= "localConLibList";
	public static final String TYPE_COMM_CON_LIB_LIST					= "communicationConLibList";
	public static final String TYPE_EMBEDDED_CONTENT_INFO				= "embeddedContentInfo";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		try {
			String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE);
			out.write(getResponseXML(request, type).getBytes());
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
		}
		
		return null;
	}

	private String getResponseXML (HttpServletRequest request, String type) {
		String returnData = "";
		JSONObject returnObj = new JSONObject();
		
		StringBuilder clOptions = new StringBuilder();

		if ( type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_INFO) ) {

			ContentObject contentObject = ContentObject.findByHttpServletRequest(request);

			if (contentObject != null) {

				try {

					returnObj.put("embeddedContent_id", contentObject.getId());
					returnObj.put("name", contentObject.getName());
					returnObj.put("insert_as_paragraph", contentObject.isInsertAsBlockContent());
					returnObj.put("is_removed", contentObject.isRemoved());
					returnObj.put("has_working_data", contentObject.hasWorkingData());
					returnObj.put("has_active_data", contentObject.hasActiveData());
					returnObj.put("has_archived_data", contentObject.hasArchivedData());
					returnObj.put("focus_on_data_type", contentObject.getFocusOnDataType());


				} catch (JSONException e) {
					log.error("Error: Unable to retrieve content object info: " + e);
				}
			}

			return returnObj.toString();

		} else if ( type.equalsIgnoreCase(TYPE_CON_LIB_LIST) || type.equalsIgnoreCase(TYPE_COMM_CON_LIB_LIST) ) {
			
			List<ContentObject> clList = new ArrayList<>();

			long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
			long communicationId 		= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, 0);
			long zoneId 				= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_ID, 0);
			String nameSearch 			= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);
			String extType 				= ServletRequestUtils.getStringParameter(request, PARAM_EXT_TYPE, null);
			int numCap 					= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
			long contentLibId			= ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_LIBRARY_ID, 0);
			long contentObjectId		= ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_OBJECT_ID, 0);
			long selectedContentLibId	= ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_CONTENT_LIBRARY_ID, 0);
			boolean isTestContext		= ServletRequestUtils.getStringParameter(request, PARAM_CONTEXT, "default").equalsIgnoreCase("test");

			if ( contentLibId == 0)
				contentLibId = contentObjectId;

			ContentObject targetInstance 	= ContentObject.findByIdActiveDataFocusCentric(contentLibId);
			ContentObject selectedItem		= ContentObject.findByIdActiveDataFocusCentric(selectedContentLibId);

			Document document = null;
			if ( targetInstance != null && !targetInstance.isGlobalImage() )
				document = Document.findById(documentId);
			
			if ( zoneId > 0 ) {
				
				Zone zone = Zone.findById(zoneId);
				document = zone.getDocument();
				
				// Connected: Apply alternate library definition
				if ( communicationId > 0 ) {
					Communication communication = Communication.findById(communicationId);
					List<CommunicationProof> preProofs = CommunicationProof.findPreProofsByCommunicationId(communicationId);
					if (!preProofs.isEmpty()) {
						Document contextDocument = AsyncConnectedInteractiveController.getTargetDocument(preProofs.get(0), communication);
						if ( contextDocument.isAlternate() )
							zone = contextDocument.findZoneByParent(zone);
					}
				}
				
				if ( zone.isRestrictSharedAssets() ) {
					clList.addAll( zone.getImageAssets() );
				} else {
					clList = ContentObject.findAllGlobalImagesFiltered(document, selectedItem, targetInstance, nameSearch, extType, numCap, !isTestContext, type.equalsIgnoreCase(TYPE_COMM_CON_LIB_LIST));
				}
			} else {
				clList = ContentObject.findAllGlobalImagesFiltered(document, selectedItem, targetInstance, nameSearch, extType, numCap, !isTestContext, type.equalsIgnoreCase(TYPE_COMM_CON_LIB_LIST));
			}

			Long fullListCount = 0L;
			int displayCount = 0;
			if ( clList != null && !clList.isEmpty()) {

				clOptions.append("<option id=\"empty_").append(zoneId).append("\" value=\"0\" class=\"fixedOption\">").append(TagUtils.formatLabel("page.text.select.content.library")).append("</option>");
				
				for ( ContentObject contentLibrary: clList )
					clOptions.append("<option id=\"").append(contentLibrary.getId()).append("\" value=\"").append(contentLibrary.getId()).append("\">").append(contentLibrary.getName()).append("</option>");
				
				fullListCount 	= ContentObject.findAllFilteredCount(document, selectedItem, targetInstance, nameSearch, extType, numCap, !isTestContext, type.equalsIgnoreCase(TYPE_COMM_CON_LIB_LIST));
				fullListCount 	= clList.size() > fullListCount ? clList.size() : fullListCount;
				displayCount 	= clList.size();
			} else {

				clOptions.append("<option id=\"0\" value=\"0\">").append(TagUtils.formatLabel("page.text.no.content.libraries.qualified.for.link")).append("</option>");

			}
			
			returnData = 	"<options " + (selectedContentLibId > 0 ? "selectedOptionId=\"" + selectedContentLibId + "\"" : "") + " fullListCount=\"" + fullListCount + "\" displayListCount=\"" + displayCount + "\">" + 
								StringEscapeUtils.escapeXml(clOptions.toString()) + 
							"</options>";
			
		} else if ( type.equalsIgnoreCase(TYPE_LOCAL_CON_LIB_LIST) ) {
			
			List<ContentObject> clList = new ArrayList<>();

			long documentId 				= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
			String nameSearch 				= ServletRequestUtils.getStringParameter(request, PARAM_NAME_SEARCH, null);
			String extType 					= ServletRequestUtils.getStringParameter(request, PARAM_EXT_TYPE, null);
			int numCap 						= ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);
			long contentObjectId			= ServletRequestUtils.getLongParameter(request, PARAM_CONTENT_OBJECT_ID, 0);
			long selectedLocalContentLibId	= ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_LOCAL_CONTENT_LIBRARY_ID, 0);
			TouchpointSelection selection	= UserUtil.getCurrentSelectionContext();

			ContentObject targetInstance 	= ContentObject.findByIdActiveDataFocusCentric(contentObjectId);
			ContentObject selectedItem		= ContentObject.findByIdActiveDataFocusCentric(selectedLocalContentLibId);
			Document document = Document.findById(documentId);
			
			clList = ContentObject.findAllLocalImageLibraryFiltered(document, selection, selectedItem, targetInstance, nameSearch, extType, numCap);
			
			Long fullListCount = 0L;
			int displayCount = 0;
			if ( clList != null && !clList.isEmpty()) {
				
				clOptions.append("<option id=\"0\" value=\"0\" class=\"fixedOption\">").append(TagUtils.formatLabel("page.text.select.local.content.library")).append("</option>");
				
				for ( ContentObject localContentLibrary: clList )
					clOptions.append("<option id=\"").append(localContentLibrary.getId()).append("\" value=\"").append(localContentLibrary.getId()).append("\">").append(localContentLibrary.getName()).append("</option>");
				
				fullListCount 	= ContentObject.findAllLocalImagesFilteredCount(document, selection, selectedItem, targetInstance, nameSearch, extType, numCap);
				fullListCount 	= clList.size() > fullListCount ? clList.size() : fullListCount;
				displayCount 	= clList.size();
			} else {

				clOptions.append("<option id=\"0\" value=\"0\">").append(TagUtils.formatLabel("page.text.no.local.content.libraries.qualified.for.link")).append("</option>");

			}
			
			returnData = 	"<options " + (selectedLocalContentLibId > 0 ? "selectedOptionId=\"" + selectedLocalContentLibId + "\"" : "") + " fullListCount=\"" + fullListCount + "\" displayListCount=\"" + displayCount + "\">" + 
								StringEscapeUtils.escapeXml(clOptions.toString()) + 
							"</options>";
			
		}

		return "<?xml version='1.0'?><content>" + returnData + "</content>";
	}
}