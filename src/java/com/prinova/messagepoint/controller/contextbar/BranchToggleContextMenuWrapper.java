package com.prinova.messagepoint.controller.contextbar;

import java.util.List;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.BranchUtil;
import com.prinova.messagepoint.util.UserUtil;

public class BranchToggleContextMenuWrapper {

	private String[] 					searchValuesByBestFit;
	private String[] 					searchValuesByFamily;
	private String						searchByBranchName;
	private List<Branch>				searchByFamilyBranches;
	private List<Branch>				searchByNameBranches;
	private String 						actionValue;
	
	private boolean						showSearchResult;
	private Branch currentBranch;

	public BranchToggleContextMenuWrapper(Branch branch, User requestor) {
		super();
		this.setSearchValuesByBestFit(new String[BranchUtil.getAllBranches().size()]);
		this.currentBranch = branch;
	}

	public String[] getSearchValuesByBestFit() {
		return searchValuesByBestFit;
	}

	public void setSearchValuesByBestFit(String[] searchValuesByBestFit) {
		this.searchValuesByBestFit = searchValuesByBestFit;
	}

	public String[] getSearchValuesByFamily() {
		return searchValuesByFamily;
	}

	public void setSearchValuesByFamily(String[] searchValuesByFamily) {
		this.searchValuesByFamily = searchValuesByFamily;
	}	

	public String getSearchByBranchName() {
		return searchByBranchName;
	}

	public void setSearchByBranchName(String searchByBranchName) {
		this.searchByBranchName = searchByBranchName;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<Branch> getSearchByFamilyBranches() {
		return searchByFamilyBranches;
	}

	public void setSearchByFamilyBranches(
			List<Branch> searchByFamilyBranches) {
		this.searchByFamilyBranches = searchByFamilyBranches;
	}	

	public List<Branch> getSearchByNameBranches() {
		return searchByNameBranches;
	}

	public void setSearchByNameBranches(
			List<Branch> searchByNameBranches) {
		this.searchByNameBranches = searchByNameBranches;
	}

	public boolean isShowSearchResult() {
		return showSearchResult;
	}

	public void setShowSearchResult(boolean showSearchResult) {
		this.showSearchResult = showSearchResult;
	}

    public Branch getCurrentBranch() {
        return currentBranch;
    }

    public void setCurrentBranch(Branch currentBranch) {
        this.currentBranch = currentBranch;
    }

    public Branch getMasterBranch() {
        return Node.findBySchema(UserUtil.getPrincipalUser().getSchemaOfThisUser()).getBranch();
    }
}
