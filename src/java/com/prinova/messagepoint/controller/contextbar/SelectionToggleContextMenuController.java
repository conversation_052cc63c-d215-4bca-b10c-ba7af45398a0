package com.prinova.messagepoint.controller.contextbar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.ParameterGroupItem;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.TouchpointSelectionUtil;
import com.prinova.messagepoint.util.UserUtil;

public class SelectionToggleContextMenuController extends MessagepointController {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(SelectionToggleContextMenuController.class);

	public static final String REQ_PARM_ACTION = "submittype";
	public static final String REQ_PARM_WINDOW = "windowType";
	
	public static final String WINDOW_TYPE_STAND_ALONE 	= "stand_alone";
	public static final String WINDOW_TYPE_IN_PAGE 		= "in_page";
	
	public static final int ACTION_SEARCH_BY_BEST_FIT 	= 1;
	public static final int ACTION_SEARCH_BY_FAMILY 	= 2;
	public static final int ACTION_SEARCH_BY_NAME 		= 3;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long selectionId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_SELECTION_ID_PARAM, -1L);
		TouchpointSelection selection = TouchpointSelection.findById(selectionId);
		Document document = selection.getDocument();
		List<ParameterGroupItem> pgItemList = selection.getDocument().getSelectionParameterGroup().getParameterGroupItemsSorted();
		referenceData.put("searchParameters",  pgItemList);
		referenceData.put("document", selection.getDocument());
		referenceData.put("touchpointSelection", document);
		referenceData.put("masterTouchpointSelection", document.getMasterTouchpointSelection());

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	SelectionToggleContextMenuWrapper wrapper = (SelectionToggleContextMenuWrapper) commandObj;
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		long selectionId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_SELECTION_ID_PARAM, -1L);
		long localContext		= ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_LOCAL_CONTEXT_PARAM, -1L);
		TouchpointSelection selection = TouchpointSelection.findById(selectionId);
		User principalUser = UserUtil.getPrincipalUser();
		User requestor = User.findById(principalUser.getId());
		Map<String, Object> params = new HashMap<>();
		
		switch (action) {
			case (ACTION_SEARCH_BY_BEST_FIT): {
				TouchpointSelection matchingSelection = null;
                List<String> searchValues = new ArrayList<>(Arrays.asList(wrapper.getSearchValuesByBestFit()));
				while (matchingSelection == null) {
					matchingSelection = TouchpointSelectionUtil.findBestFitByDataValues(selection.getDocument(), searchValues);
					if (matchingSelection != null && !matchingSelection.isDeleted()) {
						break;
					} else if (searchValues.size() - 1 == 0 && matchingSelection == null) {
						matchingSelection = selection.getDocument().getMasterTouchpointSelection();
						break;
					} else {
						searchValues.remove(searchValues.size()-1);
					}
				}
				
				params.put(AsyncListTableController.PARAM_SELECTION_ID, matchingSelection.getId());
				if(localContext > 0L){
					params.put(ContextBarTag.REQ_PARAM_LOCAL_CONTEXT_PARAM, localContext);
				}
				// User needs to have full visibility or selected visibility of this selection to view the child selection
				if(!matchingSelection.isVisible(requestor)){
					params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, false);
					errors.reject("error.touchpointselection.matching.variant.no.visibility");
					return super.showForm(request, response, errors);
				}else{
					params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}			
			}
			case (ACTION_SEARCH_BY_FAMILY): {
                List<String> searchValues = new ArrayList<>(Arrays.asList(wrapper.getSearchValuesByFamily()));
				List<TouchpointSelection> matchingSelections = TouchpointSelectionUtil.findFamilyByDataValues(selection.getDocument(), searchValues);
				List<TouchpointSelection> visibleMatchingSelections = new ArrayList<>();
				// User needs to have full visibility or selected visibility of this selection to view the selections
				for(TouchpointSelection matchingSelection : matchingSelections){
					if(matchingSelection.isVisible(requestor) && !matchingSelection.isDeleted()){
						visibleMatchingSelections.add(matchingSelection);
					}
				}
				wrapper.setSearchByFamilySelections(visibleMatchingSelections);
				wrapper.setShowSearchResult(true);

				if(selectionId > 0L){
					params.put(ContextBarTag.REQ_PARAM_SELECTION_ID_PARAM, selectionId);
				}
				if(localContext > 0L){
					params.put(ContextBarTag.REQ_PARAM_LOCAL_CONTEXT_PARAM, localContext);
				}
				return super.showForm(request, response, errors);
			}
			case (ACTION_SEARCH_BY_NAME): {
				String searchName = wrapper.getSearchBySelectionName();
				List<TouchpointSelection> matchingSelections = TouchpointSelectionUtil.findBySelectionName(selection.getDocument(), searchName);
				List<TouchpointSelection> visibleMatchingSelections = new ArrayList<>();
				// User needs to have full visibility or selected visibility of this selection to view the selections
				for(TouchpointSelection matchingSelection : matchingSelections){
					if(matchingSelection.isVisible(requestor) && !matchingSelection.isDeleted()){
						visibleMatchingSelections.add(matchingSelection);
					}
				}
				wrapper.setSearchByNameSelections(visibleMatchingSelections);
				wrapper.setShowSearchResult(true);

				if(selectionId > 0L){
					params.put(ContextBarTag.REQ_PARAM_SELECTION_ID_PARAM, selectionId);
				}
				if(localContext > 0L){
					params.put(ContextBarTag.REQ_PARAM_LOCAL_CONTEXT_PARAM, localContext);
				}
				return super.showForm(request, response, errors);
			}
			default:
				break;
		}
		
		params.put(AsyncListTableController.PARAM_SELECTION_ID, selection.getId());
		
		String windowType = ServletRequestUtils.getStringParameter(request, REQ_PARM_WINDOW, WINDOW_TYPE_IN_PAGE);
		if (windowType.equals(WINDOW_TYPE_STAND_ALONE))
			params.put(REQ_PARM_WINDOW, windowType);

    	return new ModelAndView(new RedirectView(getSuccessView()), params);
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {

		long selectionId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_SELECTION_ID_PARAM, -1L);
		TouchpointSelection selection = TouchpointSelection.findById(selectionId);
		User requestor = UserUtil.getPrincipalUser();

		return new SelectionToggleContextMenuWrapper(selection, requestor);
	}

}