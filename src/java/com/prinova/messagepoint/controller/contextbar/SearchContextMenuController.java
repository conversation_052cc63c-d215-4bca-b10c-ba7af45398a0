package com.prinova.messagepoint.controller.contextbar;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.UserUtil;

public class SearchContextMenuController implements Controller {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(SearchContextMenuController.class);
	
	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> referenceData = new HashMap<>();
		
		referenceData.put("document", UserUtil.getCurrentTouchpointContext());

		return new ModelAndView(getFormView(), referenceData);

	}
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

}