package com.prinova.messagepoint.controller.contextbar;

import java.util.List;

import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.security.User;

public class SelectionToggleContextMenuWrapper {

	private String[] 					searchValuesByBestFit;
	private String[] 					searchValuesByFamily;
	private String						searchBySelectionName;
	private List<TouchpointSelection>	searchByFamilySelections;
	private List<TouchpointSelection>	searchByNameSelections;
	private String 						actionValue;
	
	private boolean						showSearchResult;
	
	public SelectionToggleContextMenuWrapper(TouchpointSelection selection, User requestor) {
		super();
		this.setSearchValuesByBestFit(new String[selection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted().size()]);
		this.setSearchValuesByFamily(new String[selection.getParameterGroupTreeNode().getParameterGroup().getParameterGroupItemsSorted().size()]);
	}

	public String[] getSearchValuesByBestFit() {
		return searchValuesByBestFit;
	}

	public void setSearchValuesByBestFit(String[] searchValuesByBestFit) {
		this.searchValuesByBestFit = searchValuesByBestFit;
	}

	public String[] getSearchValuesByFamily() {
		return searchValuesByFamily;
	}

	public void setSearchValuesByFamily(String[] searchValuesByFamily) {
		this.searchValuesByFamily = searchValuesByFamily;
	}	

	public String getSearchBySelectionName() {
		return searchBySelectionName;
	}

	public void setSearchBySelectionName(String searchBySelectionName) {
		this.searchBySelectionName = searchBySelectionName;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<TouchpointSelection> getSearchByFamilySelections() {
		return searchByFamilySelections;
	}

	public void setSearchByFamilySelections(
			List<TouchpointSelection> searchByFamilySelections) {
		this.searchByFamilySelections = searchByFamilySelections;
	}	

	public List<TouchpointSelection> getSearchByNameSelections() {
		return searchByNameSelections;
	}

	public void setSearchByNameSelections(
			List<TouchpointSelection> searchByNameSelections) {
		this.searchByNameSelections = searchByNameSelections;
	}

	public boolean isShowSearchResult() {
		return showSearchResult;
	}

	public void setShowSearchResult(boolean showSearchResult) {
		this.showSearchResult = showSearchResult;
	}
}
