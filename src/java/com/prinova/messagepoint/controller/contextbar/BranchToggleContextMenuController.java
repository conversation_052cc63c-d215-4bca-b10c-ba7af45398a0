package com.prinova.messagepoint.controller.contextbar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.AsyncListTableController;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;
import com.prinova.messagepoint.util.BranchUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class BranchToggleContextMenuController extends MessagepointController {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(BranchToggleContextMenuController.class);

	public static final String REQ_PARM_ACTION = "submittype";
	public static final String REQ_PARM_WINDOW = "windowType";
	
	public static final String WINDOW_TYPE_STAND_ALONE 	= "stand_alone";
	public static final String WINDOW_TYPE_IN_PAGE 		= "in_page";
	
	public static final int ACTION_SEARCH_BY_BEST_FIT 	= 1;
	public static final int ACTION_SEARCH_BY_FAMILY 	= 2;
	public static final int ACTION_SEARCH_BY_NAME 		= 3;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		List<Branch> branchItemList = new ArrayList<>();
		if(UserUtil.getPrincipalUser().getSchemaOfThisUser() != null){
			Node userNode = Node.findBySchema(UserUtil.getPrincipalUser().getSchemaOfThisUser());
        	Branch branch = userNode.getBranch();
        	branchItemList.add(branch);
        	branchItemList.addAll(BranchUtil.getAllChildren(branch));
		}else{
			branchItemList = BranchUtil.getAllBranches();
		}
        	
		referenceData.put("searchBranches",  branchItemList);

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	BranchToggleContextMenuWrapper wrapper = (BranchToggleContextMenuWrapper) commandObj;
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		long branchId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_BRANCH_ID_PARAM, -1L);
		Branch branch = Branch.findById(branchId);
		User principalUser = UserUtil.getPrincipalUser();
		User requestor = User.findById(principalUser.getId());
		Map<String, Object> params = new HashMap<>();
		
		switch (action) {
			case (ACTION_SEARCH_BY_BEST_FIT): {
				Branch matchingBranch = null;
                List<String> searchValues = new ArrayList<>(Arrays.asList(wrapper.getSearchValuesByBestFit()));
				while (matchingBranch == null) {
					matchingBranch = BranchUtil.findByBranchName(searchValues.iterator().next().toString());
					if (matchingBranch != null) {
						break;
					} else if (searchValues.size() - 1 == 0 && matchingBranch == null) {
						matchingBranch = BranchUtil.findMasterBranch();
						break;
					} else {
						searchValues.remove(searchValues.size()-1);
					}
				}
				
				params.put(AsyncListTableController.PARAM_BRANCH_ID, matchingBranch.getId());
				params.put("level", matchingBranch.isMaster() ? "master" : "1");
				
				// User needs to have full visibility or selected visibility of this branch to view the child branch
				if(!matchingBranch.isVisible(requestor)){
					params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, false);
					errors.reject("error.touchpointselection.matching.variant.no.visibility");
					return super.showForm(request, response, errors);
				}else{
					params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
					return new ModelAndView(new RedirectView(getSuccessView()), params);
				}			
			}
			case (ACTION_SEARCH_BY_NAME): {
				String searchName = wrapper.getSearchByBranchName();
				Branch loggedBranch = Branch.findById(Node.getCurrentNode().getBranch().getId());
				Set<String> possibleBranchSet = BranchUtil.getPossibleBranchNames(loggedBranch);
				List<Branch> matchingBranches = new ArrayList<>();
				if(!searchName.isEmpty() && !searchName.isEmpty()){
					matchingBranches = BranchUtil.findAllByBranchName(searchName);
				}
				List<Branch> visibleMatchingBranches = new ArrayList<>();
				// User needs to have full visibility or selected visibility of this branch to view the branchs
				for(Branch matchingBranch : matchingBranches){
					if(matchingBranch.isVisible(requestor) && possibleBranchSet.contains(matchingBranch.getName())){
						visibleMatchingBranches.add(matchingBranch);
					}
				}
				wrapper.setSearchByNameBranches(visibleMatchingBranches);
				wrapper.setShowSearchResult(true);						
				return super.showForm(request, response, errors);
			}
			default:
				break;
		}
		
		params.put(AsyncListTableController.PARAM_BRANCH_ID, branch.getId());
		
		String windowType = ServletRequestUtils.getStringParameter(request, REQ_PARM_WINDOW, WINDOW_TYPE_IN_PAGE);
		if (windowType.equals(WINDOW_TYPE_STAND_ALONE))
			params.put(REQ_PARM_WINDOW, windowType);

    	return new ModelAndView(new RedirectView(getSuccessView()), params);
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {

		long branchId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_BRANCH_ID_PARAM, -1L);
		Branch branch = Branch.findById(branchId);
		User requestor = UserUtil.getPrincipalUser();

		return new BranchToggleContextMenuWrapper(branch, requestor);
	}

}