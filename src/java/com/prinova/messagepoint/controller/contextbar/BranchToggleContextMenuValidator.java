package com.prinova.messagepoint.controller.contextbar;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class BranchToggleContextMenuValidator extends MessagepointInputValidator{
	
	public void validateNotGenericInputs(Object command, Errors errors) {
		BranchToggleContextMenuWrapper wrapper = (BranchToggleContextMenuWrapper)command;
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		if (action==BranchToggleContextMenuController.ACTION_SEARCH_BY_FAMILY) {
			String[] searchDataValues = wrapper.getSearchValuesByFamily();
			StringBuilder occPattern = new StringBuilder();	// Occurrence pattern: "1" means occurrence, " " means none
			for(int i=0; i<searchDataValues.length; i++){
				if(searchDataValues[i] == null || searchDataValues[i].trim().isEmpty()){
					occPattern.append(" ");
				}else{
					occPattern.append("1");
				}
			}
			if(occPattern.toString().trim().contains(" ")){
				errors.reject("error.message.datavalues.must.be.consecutive");
			}
		}		
	}
}
