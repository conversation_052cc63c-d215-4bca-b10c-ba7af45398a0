package com.prinova.messagepoint.controller.contextbar;

import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LanguageContextMenuController extends MessagepointController {

	// private static final Log log = LogUtil.getLog(LanguageContextMenuController.class);

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		Document currentDocument = UserUtil.getCurrentTouchpointContext();

		List<MessagepointLocale> langLocales;
		if (currentDocument != null)
			langLocales = currentDocument.getTouchpointLanguagesAsLocales();
		else
			langLocales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

		referenceData.put("locales", langLocales);
		referenceData.put("selectedLocaleId", UserUtil.getCurrentLanguageLocaleContext().getId() );

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	return new ModelAndView(new RedirectView(getSuccessView()));
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}

}