package com.prinova.messagepoint.controller.contextbar;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointCollection;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;
import com.prinova.messagepoint.util.UserUtil;

public class TouchpointContextMenuController extends MessagepointController {

	// private static final Log log = LogUtil.getLog(TouchpointContextMenuController.class);

	public static final String REQ_PARAM_ADMIN_VIEW			= "adminView";
	public static final String REQ_PARAM_COLLECTION_CONTEXT	= "collectionContext";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		// CURRENT CONTEXT
		long selectedTouchpointOrProjectId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_DOCUMENT_ID_PARAM, -1L);
		long selectedCollectionId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_COLLECTION_ID_PARAM, -1L);
		referenceData.put("selectedCollectionId", selectedCollectionId );
		
		Document selectedTouchpointOrProject = null;
		Document currentOrigin = null;
		if ( selectedCollectionId == -1 ) {
			if ( selectedTouchpointOrProjectId > 0 ) {
				selectedTouchpointOrProject = Document.findById(selectedTouchpointOrProjectId);
			} else {
				selectedTouchpointOrProject = UserUtil.getCurrentTouchpointContext();
				selectedTouchpointOrProjectId = selectedTouchpointOrProject.getId();
			}
			
			currentOrigin = selectedTouchpointOrProject;
			while ( currentOrigin.getOriginObject() != null && currentOrigin.getCheckoutTimestamp() != null )
				currentOrigin = (Document)currentOrigin.getOriginObject();
		}

		long selectedTouchpointId = currentOrigin != null ? currentOrigin.getId() : -1;
		referenceData.put("selectedTouchpointId", selectedTouchpointId );
		referenceData.put("selectedTouchpointOrProjectId", selectedTouchpointOrProjectId );
		
		// TOUCHPOINT LIST
		List<Document> availableTouchpoints 	= new ArrayList<>();
		List<Document> touchpointsAndProjects 	= new ArrayList<>();
		if ( ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_ADMIN_VIEW, false) ) {
            Set<Long> beingClonedDocumentIds = StatusPollingBackgroundTask.findAllActiveBackgroundTasksOfType(Arrays.asList(StatusPollingBackgroundTask.TYPE_TP_CLONE, StatusPollingBackgroundTask.TYPE_OBJECT_IMPORT))
                .stream()
                .filter(task->task.getTargetObjectId() != null)
                .map(StatusPollingBackgroundTask::getTargetObjectId)
                .collect(Collectors.toSet());
			availableTouchpoints = Document.findAll().stream().sequential().filter(d->!beingClonedDocumentIds.contains(d.getId())).collect(Collectors.toList());
			touchpointsAndProjects = Document.findAllDocumentsAndProjects().stream().sequential().filter(d->!beingClonedDocumentIds.contains(d.getRootDocument().getId())).collect(Collectors.toList());
		} else {
			availableTouchpoints.addAll(Document.findAllVisible(false));
			touchpointsAndProjects.addAll(Document.findAllDocumentsAndProjectsVisible());
		}
		
		boolean hasProjects = touchpointsAndProjects.size() != availableTouchpoints.size();
		referenceData.put("hasProjects", hasProjects);
		
		Document trashTp = Document.findTrashTp();
		if(trashTp != null){
			if(Document.hasAssetsInTrashTp()){
				availableTouchpoints.add(trashTp);
				touchpointsAndProjects.add(trashTp);
			}
		}

		referenceData.put("touchpointsAndProjects", touchpointsAndProjects);
		referenceData.put("touchpoints", availableTouchpoints);
	
		if ( hasProjects ) {
			JSONArray touchpointArray = new JSONArray();
			List<String> openNodeIds = new ArrayList<>();
			List<Document> disabledTouchpointParents = new ArrayList<>();
			for ( Document currentTouchpoint: touchpointsAndProjects ) {

				JSONObject touchpointObj = getTouchpointData(currentTouchpoint);
				
				// DISABLED PARENT: Mark for inclusion 
				Document parentDisabledTouchpoint = 
						currentTouchpoint.getOriginObject() != null && currentTouchpoint.getCheckoutTimestamp() != null && 
						!touchpointsAndProjects.contains( currentTouchpoint.getOriginObject() ) ? 
						(Document)currentTouchpoint.getOriginObject() : null;
				while ( parentDisabledTouchpoint != null ) {
					disabledTouchpointParents.add( parentDisabledTouchpoint );
					parentDisabledTouchpoint = 
						parentDisabledTouchpoint.getOriginObject() != null && parentDisabledTouchpoint.getCheckoutTimestamp() != null && 
						!touchpointsAndProjects.contains( parentDisabledTouchpoint.getOriginObject() ) ? 
						(Document)parentDisabledTouchpoint.getOriginObject() : null;
				}

				if ( currentTouchpoint.getId() == selectedTouchpointOrProjectId ) {
					Document iterDocument = currentTouchpoint;
					while ( iterDocument != null ) {
						openNodeIds.add("tp_"+iterDocument.getId());
						iterDocument = (Document)iterDocument.getOriginObject();
					}
				}

				touchpointArray.put(touchpointObj);
			}
			// DISABLED PARENTS: Add as pass through nodes
			for ( Document currentTouchpoint: disabledTouchpointParents ) {
				JSONObject touchpointObj = getTouchpointData(currentTouchpoint);
				JSONObject stateObj = new JSONObject();
				stateObj.put("disabled", true);
				touchpointObj.put("state", stateObj);
				touchpointArray.put(touchpointObj);
			}
			
			for ( int i=0; i < touchpointArray.length(); i++ )
				if ( openNodeIds.contains( touchpointArray.getJSONObject(i).get("id") ) ) {
					
					JSONObject stateObj = new JSONObject();
					if ( touchpointArray.getJSONObject(i).has("state") )
						stateObj = touchpointArray.getJSONObject(i).getJSONObject("state");
					
					if ( touchpointArray.getJSONObject(i).get("id").toString().replace("tp_", "").equals(Long.toString(selectedTouchpointOrProjectId)) ) {
						stateObj.put("selected", true);
					} else {
						stateObj.put("opened", true);
					}
					touchpointArray.getJSONObject(i).put("state", stateObj);
				}
			
			referenceData.put("touchpointTreeData", touchpointArray);
		}

		List<TouchpointCollection> availableCollections = TouchpointCollection.findAllWithAtLeastOneVisibleDocument(false);
		referenceData.put("collections", availableCollections);
		
		Map<Long,String> collectionTouchpointMap = new HashMap<>();
		for ( TouchpointCollection currentCollection: availableCollections ) {
			JSONArray documentIdArray = new JSONArray();
			for ( Document currentDocument: currentCollection.getDocumentsSortedOnOrder() ){
				if (currentDocument != null) {
					documentIdArray.put(currentDocument.getId());
				}
			}
			if (!documentIdArray.isEmpty()) {
				collectionTouchpointMap.put(currentCollection.getId(), documentIdArray.toString());
			}
		}
		referenceData.put("collectionTouchpointMap", collectionTouchpointMap);
		
		boolean appliesCollectionContext = ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_COLLECTION_CONTEXT, false);
		referenceData.put("contextCollections", appliesCollectionContext ? TouchpointCollection.findAllWithAllDocumentsVisible(true) : null);

		boolean isCollectionContext = UserUtil.getCurrentCollectionContext() != null && selectedCollectionId > 0;
		referenceData.put("isCollectionContext", isCollectionContext );

		String primaryLabel = isCollectionContext ?
				TouchpointCollection.findById(selectedCollectionId).getName() :
				Document.findById(selectedTouchpointId).getName();
		String secondaryLabel = null;
		if ( !isCollectionContext && selectedTouchpointOrProject.getId() != selectedTouchpointId ) {
			primaryLabel = selectedTouchpointOrProject.getName();
			secondaryLabel = Document.findById(selectedTouchpointId).getName();
		}
		referenceData.put("primaryLabel", primaryLabel);
		referenceData.put("secondaryLabel", secondaryLabel);
		
		String contextDescription = null;
		if ( isCollectionContext ) {
			TouchpointCollection collection = TouchpointCollection.findById(selectedCollectionId);
			if ( collection != null )
				contextDescription = collection.getDescription();	
		} else if ( selectedTouchpointOrProject != null ) {
			contextDescription = selectedTouchpointOrProject.getDescription(); 
		}
		contextDescription = contextDescription != null && !contextDescription.trim().isEmpty() && !contextDescription.trim().equals("<p></p>") ?
				contextDescription : null;
		referenceData.put("contextDescription", contextDescription);
		
		boolean hasTaskViewPermission = UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_ALL) || UserUtil.isPermissionGranted(Permission.ID_ROLE_TASK_VIEW_MY);
		referenceData.put("hasTaskViewPermission", hasTaskViewPermission );
		
		return referenceData;
	}
	
	protected JSONObject getTouchpointData(Document touchpoint) throws Exception {
		JSONObject touchpointObj = new JSONObject();
		
		touchpointObj.put("id"		, "tp_"+touchpoint.getId());
		touchpointObj.put("text"	, touchpoint.getName());
		touchpointObj.put("parent"	, (touchpoint.getOriginObject() == null || touchpoint.getCheckoutTimestamp() == null) ? 
										"#" : "tp_"+touchpoint.getOriginObject().getId());
		
		String icon = "fa mp-list-icon ";
	 	if ( touchpoint.getIsOmniChannel() )
	 		icon = "fas mp-list-icon fa-circle-notch  fa-rotate-180";
	 	else if ( touchpoint.isPrintTouchpoint() )
	 		icon = "far mp-list-icon fa-file";
	 	else if ( touchpoint.isWebTouchpoint() )
	 		icon += "fa-globe fa-14px";
	 	else if ( touchpoint.isEmailTouchpoint() )
	 		icon = "far mp-list-icon fa-at fa-14px";
	 	else if ( touchpoint.isSmsTouchpoint() )
	 		icon += "fa-mobile fa-20px";
	 	else if ( touchpoint.getTrashTouchpoint() )
	 		icon += "fa-trash fa-20px";
		touchpointObj.put("icon"	,  icon);
		
		return touchpointObj;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	return new ModelAndView(new RedirectView(getSuccessView()));
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}

}