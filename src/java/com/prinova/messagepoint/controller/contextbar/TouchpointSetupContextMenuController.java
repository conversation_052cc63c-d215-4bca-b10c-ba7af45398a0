package com.prinova.messagepoint.controller.contextbar;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.util.*;

import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.MessagepointLicenceManager;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.TPInstanceVisibilityMap;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.ClearMetadataService;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.tag.layout.ContextBarTag;

public class TouchpointSetupContextMenuController extends MessagepointController {
    private static final Log log = LogUtil.getLog(TouchpointSetupContextMenuController.class);

	public static final String REQ_PARAM_DOCUMENTID 				= "documentId";
	public static final String REQ_PARAM_VIEWID 							= "viewId";
	public static final String REQ_PARM_ACTION 							= "action";

	public static final int ACTION_CLEAR_METADATA						= 17;

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long selectedTouchpointId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_DOCUMENT_ID_PARAM, -1L);

		Document selectedTouchpoint = null;
		if ( selectedTouchpointId > 0 )
			selectedTouchpoint = Document.findById(selectedTouchpointId);
		else
			selectedTouchpoint = UserUtil.getCurrentTouchpointContext();

		if(selectedTouchpoint != null){
			if(selectedTouchpoint.getTrashTouchpoint()){
				selectedTouchpoint = null;
			}
		}

        boolean targetOfSync = selectedTouchpoint == null ? false : selectedTouchpoint.isTargetOfSync();

        boolean syncUpdateFromParent = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromParent();
        boolean syncUpdateFromChild = MessagepointLicenceManager.getInstance().isLicencedForSyncUpdateFromChild();

		boolean hasFamilyDocuments = false;

        Node currentNode = Node.getCurrentNode();
        User currentUser = UserUtil.getPrincipalUser();

//        if(! currentNode.isExchangeNode())
        {
            Branch currentBranch = Node.getCurrentBranch();
            Long parentBranchId = syncUpdateFromParent ? (currentBranch.getParentBranch() == null ? null : currentBranch.getParentBranch().getId()) : null;
            Branch parentBranch = parentBranchId == null ? null : Branch.findById(parentBranchId);
            List<Branch> childBranches = syncUpdateFromChild ? currentBranch.getAllEnabledChildren() : null;
            List<Node> nodesInCurrentBranch = currentBranch.getAllAccessibleNodesForTES(false, true);
            Set<Node> nodes = new HashSet<>(nodesInCurrentBranch);
            if(syncUpdateFromParent) {
                if(parentBranch != null) {
                    List<Node> nodesInParentBranch = parentBranch.getAllAccessibleNodes(false)
                        .stream()
                        .filter(n->n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isSandboxNode() || n.isTestingNode()) )
                        .collect(Collectors.toList());
                    nodes.addAll(nodesInParentBranch);
                }
            }
            if(syncUpdateFromChild) {
                if(childBranches != null) {
                    for(Branch childBranch : childBranches) {
                        List<Node> nodeInChildBranch = childBranch.getAllAccessibleNodes(false)
                            .stream()
                            .filter(n->n.isEnabled() && n.isOnline() && (n.isProductionNode() || n.isSandboxNode() || n.isTestingNode()) )
                            .collect(Collectors.toList());
                        nodes.addAll(nodeInChildBranch);
                    }
                }
            }
            List<Branch> accessibleExchangeDomains = currentBranch.getExchangeDomains();
            for(Branch exchangeDomainBranch : accessibleExchangeDomains) {
                if(! exchangeDomainBranch.isEnabled()) continue;
                if(! exchangeDomainBranch.isOnline()) continue;
                if(exchangeDomainBranch.getId() == currentBranch.getId()) continue;
                List<Node> nodesInAncestorExchangeDomainBranch = exchangeDomainBranch.getAllAccessibleNodesForTES(false, true);
                for(Node node : nodesInAncestorExchangeDomainBranch) {
                    if(node.isExchangeNode()) {
                        nodes.add(node);
                    }
                }
            }

            Document selectedTouchpointFinal = selectedTouchpoint;

			boolean syncVariantEnabled = SyncTouchpointUtil.checkSyncVariantEnabledFeatureFlag(request, currentNode.getId(), selectedTouchpointId);

            String layoutsDnaDesc = selectedTouchpointFinal == null ? null : Document.getLayoutsDnaDescription(selectedTouchpointFinal, syncVariantEnabled);

            log.info(" layoutsDnaDesc: " + layoutsDnaDesc);

findFamilyDocsInAllAccessibleNodes:
            for(Node node : nodes) {
    		    if(! node.isEnabled()) continue;
    		    if(! node.isOnline()) continue;
                User nodeUser = currentUser.getNodeUser(node);
                boolean nodeUserIsInvalid = (nodeUser == null || (!nodeUser.isAccountActive()) || (!nodeUser.isAccountEnabled()) || (!nodeUser.isAccountNonExpired()) || (!nodeUser.isAccountNonLocked()));
                if(! node.isExchangeNode()) {
                    if (nodeUserIsInvalid) {
                        continue;
                    }
                }
                Branch nodeBranch = node.getBranch();
                Branch parentOfNodeBranch = nodeBranch.getParentBranch();
    		    if(nodeBranch.getId() != currentBranch.getId()) {
    		        if(node.isExchangeNode()) {
                        if (selectedTouchpointFinal.getExchangeTouchpointGuid() == null || selectedTouchpointFinal.getExchangeInstanceGuid() == null) continue;
                        if (!selectedTouchpointFinal.getExchangeInstanceGuid().equals(node.getGuid())) continue;
                    }
                    else {
                        boolean shouldIncludeNode = false;
                        if(syncUpdateFromParent) {
                            if(parentBranch != null && nodeBranch.getId() == parentBranch.getId()) {
                                shouldIncludeNode = true;
                            }
                        }
                        if(syncUpdateFromChild) {
                            if(parentOfNodeBranch != null && parentOfNodeBranch.getId() == currentBranch.getId()) {
                                shouldIncludeNode = true;
                            }
                        }
                        if(! shouldIncludeNode) {
                            continue;
                        }
                    }
    		    }

                List<Document> allFamilyDocs = CloneHelper.queryInSchema(node.getSchemaName(), ()->Document.findFamilyDocumentsWithSameVariantWorkflowSetting(selectedTouchpointFinal, layoutsDnaDesc, syncVariantEnabled));

                if(node.isExchangeNode()) {
                    for(Document doc : allFamilyDocs) {
                        if(CloneHelper.queryInSchema(node.getSchemaName(),
                                ()->TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), currentBranch.getDcsNode().getGuid()) != null
                                 || TPInstanceVisibilityMap.findByTouchpointAndInstance(doc.getGuid(), currentNode.getGuid()) != null)
                        ) {
                            if(selectedTouchpointFinal.getExchangeTouchpointGuid() != null && selectedTouchpointFinal.getExchangeTouchpointGuid().equals(doc.getGuid())) {
                                if(targetOfSync /*|| doc.isTargetOfSync()*/) {
                                    hasFamilyDocuments = true;
                                    break findFamilyDocsInAllAccessibleNodes;
                                }
                            }
                        }
                    }
                }

                if(! nodeUserIsInvalid) {
                    if (nodeBranch.getId() == currentBranch.getId()) {
                        if (allFamilyDocs != null && !allFamilyDocs.isEmpty()) {
                            if (targetOfSync || allFamilyDocs.stream().anyMatch(d -> d.isTargetOfSync())) {
                                hasFamilyDocuments = true;
                                break findFamilyDocsInAllAccessibleNodes;
                            }
                        }
                    } else if (syncUpdateFromParent && parentBranch != null && nodeBranch.getId() == parentBranch.getId()) {
                        if (allFamilyDocs != null && !allFamilyDocs.isEmpty()) {
                            if (targetOfSync /*|| allFamilyDocs.stream().anyMatch(d -> d.isTargetOfSync())*/) {
                                hasFamilyDocuments = true;
                                break findFamilyDocsInAllAccessibleNodes;
                            }
                        }
                    } else if (syncUpdateFromChild && nodeBranch.getParentBranch() != null && nodeBranch.getParentBranch().getId() == currentBranch.getId()) {
                        if (allFamilyDocs != null && !allFamilyDocs.isEmpty()) {
                            if (targetOfSync /*|| allFamilyDocs.stream().anyMatch(d -> d.isTargetOfSync())*/) {
                                hasFamilyDocuments = true;
                                break findFamilyDocsInAllAccessibleNodes;
                            }
                        }
                    }
                }
            }
        }

		boolean isNoTouchpointsContext = false;
		if ( selectedTouchpoint != null ) {
			referenceData.put("touchpoint", selectedTouchpoint);
            referenceData.put("hasFamilyDocuments", hasFamilyDocuments);
            referenceData.put("targetOfSync", selectedTouchpoint.isTargetOfSync());

			referenceData.put("isVariantTouchpoint", selectedTouchpoint != null ? selectedTouchpoint.isEnabledForVariation() : false);

			boolean licencedForMessagepointInteractive = MessagepointLicenceManager.getInstance().isLicencedForMessagepointInteractive();
			referenceData.put("licencedForMessagepointInteractive", licencedForMessagepointInteractive);

			boolean licencedForVariantManagement = MessagepointLicenceManager.getInstance().isLicencedForVariantManagement();
			referenceData.put("licencedForVariantManagement", licencedForVariantManagement);

			referenceData.put("isOrderEntryTouchpoint", selectedTouchpoint.isConnectedEnabled() && selectedTouchpoint.isCommunicationOrderEntryEnabled() );

			referenceData.put("selectedTouchpointId", selectedTouchpoint.getId() );

			referenceData.put("healthChecksAvailable", !TouchpointHealthCheck.getAllQuery(selectedTouchpoint).isEmpty());
		} else {

			referenceData.put("selectedTouchpointId", -1 );
			isNoTouchpointsContext = true;
		}

		referenceData.put("isNoTouchpointsContext", isNoTouchpointsContext);
		referenceData.put("contextPath", request.getContextPath());
		referenceData.put("cacheStamp", DateUtil.timeStamp());

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    		TouchpointSetupContextMenuWrapper coomand = (TouchpointSetupContextMenuWrapper)commandObj;
    		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
    		Long selectedTouchpointId = ServletRequestUtils.getLongParameter(request, ContextBarTag.REQ_PARAM_DOCUMENT_ID_PARAM, -1L);

		long principalUserId = UserUtil.getPrincipalUserId();
		User requestor = User.findById(principalUserId);

		switch (action) {
			case (ACTION_CLEAR_METADATA): {
				ServiceExecutionContext context = ClearMetadataService.createContext(requestor, selectedTouchpointId, ClearMetadataService.OBJECT_TYPE_TOUCHPOINT);
				Service extendDeliveryService = MessagepointServiceFactory.getInstance().lookupService(ClearMetadataService.SERVICE_NAME, ClearMetadataService.class);
				extendDeliveryService.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					return showForm(request, response, errors);
				} else {
					return new ModelAndView( new RedirectView(getSuccessView()), getSuccessViewParams(request));
				}
			}
		}
    		return null;
    }

	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new TouchpointSetupContextMenuWrapper();
	}

	private Map<String, Object> getSuccessViewParams(HttpServletRequest request) {
		Map<String, Object> params = new HashMap<>();
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENTID, -1L);
		long viewid = ServletRequestUtils.getLongParameter(request, REQ_PARAM_VIEWID, -1L);

		if(viewid != -1L){
			params.put(REQ_PARAM_VIEWID, viewid);
		}
		if(documentId != -1L){
			params.put(REQ_PARAM_DOCUMENTID, documentId);
		} else {
			params.put(REQ_PARAM_DOCUMENTID, UserUtil.getCurrentTouchpointContext().getId());
		}

		if(isContextMenuAction(request)){
			params.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		}

		return params;
	}

	private boolean isContextMenuAction(HttpServletRequest request){
		String url = request.getRequestURI();
		if(url.contains("touchpoint_setup_context_menu.form")){
			return true;
		}else{
			return false;
		}
	}

}