package com.prinova.messagepoint.controller.contextbar;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointLanguage;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.UserUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class AsyncLanguageContextMenu implements Controller {
    @Override
    public ModelAndView handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {

        JSONArray result = new JSONArray();

        Document currentDocument = UserUtil.getCurrentTouchpointContext();

        List<MessagepointLocale> langLocales;
        if (currentDocument != null)
            langLocales = currentDocument.getTouchpointLanguagesAsLocales();
        else
            langLocales = TouchpointLanguage.getSystemDefaultAndAllTouchpointLanguagesAsLocales();

        String userLangCode = User.findById(UserUtil.getPrincipalUser().getId()).getAppLocaleLangCode();

        for (MessagepointLocale locale : langLocales) {
            JSONObject jsonLocale = new JSONObject();
            jsonLocale.put("id", locale.getId());
            jsonLocale.put("uppercaseLanguageCode", locale.getFormatedLocaleToString());
            jsonLocale.put("displayName", locale.getDisplayName());
            jsonLocale.put("selected", userLangCode.equals(locale.getLanguageCode()));

            result.put(jsonLocale);
        }

        ServletOutputStream out = httpServletResponse.getOutputStream();

        httpServletResponse.setHeader("content-type", "application/json");

        out.print(result.toString());

        return null;
    }
}
