package com.prinova.messagepoint.controller;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.homepage.HomepageWidget;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncContextController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncContextController.class);

	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_COLLECTION_ID				= "collectionId";
	public static final String PARAM_WIDGET_ID					= "widgetId";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_CONTEXT 					= "context";

	public static final String TYPE_SET_TOUCHPOINT_CONTEXT		= "setTouchpointContext";
	public static final String TYPE_ADD_WIDGET_CONTEXT			= "addWidgetContext";
	public static final String TYPE_REMOVE_WIDGET_CONTEXT		= "removeWidgetContext";
	public static final String TYPE_SET_CHANNEL_CONTEXT			= "setChannelContext";
	
	private ReentrantLock	lock								= new ReentrantLock();
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		String type 	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
		
		if ( type.equalsIgnoreCase(TYPE_SET_TOUCHPOINT_CONTEXT) ) {
			try {
				out.write(setTouchpointContext(request).getBytes());
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request to manage context: "+e.getMessage(),e);
			}
		}else if(type.equalsIgnoreCase(TYPE_ADD_WIDGET_CONTEXT)){
			try {
				out.write(addWidgetContext(request).getBytes());
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request to manage context: "+e.getMessage(),e);
			}
		}else if(type.equalsIgnoreCase(TYPE_REMOVE_WIDGET_CONTEXT)){
			try {
				out.write(removeWidgetContext(request).getBytes());
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request to manage context: "+e.getMessage(),e);
			}
		}else if(type.equalsIgnoreCase(TYPE_SET_CHANNEL_CONTEXT)){
			try {
				out.write(setChannelContext(request).getBytes());
				out.flush();
			} catch (Exception e) {
				log.error("Error - Unable to resolve request to manage context: "+e.getMessage(),e);
			}
		}
		
		return null;
	}

	private String setTouchpointContext (HttpServletRequest request) {

		long documentId 			= ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, 0);
		long collectionId 			= ServletRequestUtils.getLongParameter(request, PARAM_COLLECTION_ID, 0);
		
		JSONObject returnObj = new JSONObject();
		
		try {
			
			if (documentId > 0) {
				HashMap<String,String> contextAttr = new HashMap<>();
				contextAttr = UserUtil.updateContextAttrForDocumentToggle(Document.findById(documentId), contextAttr);
				UserUtil.updateUserContextAttributes(contextAttr);
				
				returnObj.put("result", "success");
			} else if (collectionId > 0) {
				HashMap<String,String> contextAttr = new HashMap<>();
				contextAttr.put(UserUtil.CONTEXT_KEY_COLLECTION_CONTEXT, String.valueOf(collectionId));
				UserUtil.updateUserContextAttributes(contextAttr);
				
				returnObj.put("result", "success");
			} else {
				returnObj.put("result", "error");
				returnObj.put("message", "Invalid documentId/collectionId");
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to set context: " + e );
		}

		return returnObj.toString();
	}
	
	private String addWidgetContext (HttpServletRequest request) {
		this.lock.lock();
		try{
			int widgetId 				= ServletRequestUtils.getIntParameter(request, PARAM_WIDGET_ID, 0);
			
			JSONObject returnObj = new JSONObject();
			
			try {
				
				if (widgetId > 0) {
					HashMap<String,String> contextAttr = new HashMap<>();
					List<HomepageWidget> widgetTypes = UserUtil.getEnabledWidgetTypesContext();
					widgetTypes.add(new HomepageWidget(widgetId));
					UserUtil.updateContextAttrForWidgets(widgetTypes, contextAttr);
					UserUtil.updateUserContextAttributes(contextAttr);
					
					returnObj.put("result", "success");
				} else {
					returnObj.put("result", "error");
					returnObj.put("message", "Invalid widgetId");
				}
				
			} catch (JSONException e) {
				log.error("Error: Unable to set context: " + e );
			}
	
			return returnObj.toString();
		}finally{
			this.lock.unlock();
		}
	}
	
	private String removeWidgetContext (HttpServletRequest request) {
		this.lock.lock();
		try{
			int widgetId 				= ServletRequestUtils.getIntParameter(request, PARAM_WIDGET_ID, 0);
			
			JSONObject returnObj = new JSONObject();
			
			try {
				
				if (widgetId > 0) {
					HashMap<String,String> contextAttr = new HashMap<>();
					List<HomepageWidget> widgetTypes = UserUtil.getEnabledWidgetTypesContext();
					widgetTypes.remove(new HomepageWidget(widgetId));
					UserUtil.updateContextAttrForWidgets(widgetTypes, contextAttr);
					UserUtil.updateUserContextAttributes(contextAttr);
					
					returnObj.put("result", "success");
				} else {
					returnObj.put("result", "error");
					returnObj.put("message", "Invalid widgetId");
				}
				
			} catch (JSONException e) {
				log.error("Error: Unable to set context: " + e );
			}
	
			return returnObj.toString();
		}finally{
			this.lock.unlock();
		}
	}
	
	private String setChannelContext (HttpServletRequest request) {

		String context 			= ServletRequestUtils.getStringParameter(request, PARAM_CONTEXT, null);
		
		JSONObject returnObj = new JSONObject();
		
		try {
			
			if (context != null) {
				HashMap<String,String> contextAttr = new HashMap<>();
				
				long channelId = -1L;
				if ( context.equalsIgnoreCase(Document.CHANNEL_ALTERNATE_KEY_PRINT) )
					channelId = Channel.CHANNEL_COMPOSITION;
				else if ( context.equalsIgnoreCase(Document.CHANNEL_ALTERNATE_KEY_EMAIL) )
					channelId = Channel.CHANNEL_EMAIL_ID;
				else if ( context.equalsIgnoreCase(Document.CHANNEL_ALTERNATE_KEY_WEB) )
					channelId = Channel.CHANNEL_WEB_ID;
				
				contextAttr.put(UserUtil.CONTEXT_KEY_CHANNEL_CONTEXT, String.valueOf(channelId));
				UserUtil.updateUserContextAttributes(contextAttr);
				
				returnObj.put("result", "success");
			} else {
				returnObj.put("result", "error");
				returnObj.put("message", "Invalid channel context");
			}
			
		} catch (JSONException e) {
			log.error("Error: Unable to set context: " + e );
		}

		return returnObj.toString();
	}
}