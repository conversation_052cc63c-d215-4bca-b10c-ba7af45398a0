package com.prinova.messagepoint.controller.workgroup;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.util.ApplicationUtil;

public class WorkgroupViewController implements Controller {

	public static final String WORKGROUP_ID_REQ_PARAM = "wgid";

	public static final String NAME_PARAM 							= "name";
	public static final String DESCRIPTION_PARAM 					= "description";
	public static final String FULL_VISIBILITY_PARAM 				= "fullVisibilityGranted";
	public static final String FULL_VISIBILITY_GRANTED_LABEL_PARAM 	= "fullVisibilityGrantedLabel";
	public static final String USER_NAMES_PARAM 					= "userNames";
	public static final String USER_IDS_PARAM 						= "userIds";
	public static final String TOUCHPOINT_NAMES_PARAM 				= "touchpointNames";
	public static final String ZONE_ASSOCIATIONS_PARAM 				= "touchpointZones";
	public static final String IS_DELETABLE_PARAM 					= "isDeletable";
	public static final String PARAMETERS 							= "parameters";
	public static final String VISIBLE_VALUES_MAP 					= "visibleValuesMap";
	public static final String HAS_ZONE_ASSOCIATIONS_PARAM 			= "hasZoneAssociations";

	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();
		
		long workgroupId = getParameterFromRequest(request, WORKGROUP_ID_REQ_PARAM);
		
		if (workgroupId != -1) {
			Workgroup workgroup = Workgroup.findById(workgroupId);
			
			String workgroupName = workgroup.getName();
			if (workgroup.isDefaultWorkgroup()) {
				workgroupName = workgroupName + " (" + ApplicationUtil.getMessage("page.label.defaultworkgroup") + ")";
			}
			dataMap.put(NAME_PARAM, workgroupName);
			dataMap.put(DESCRIPTION_PARAM, workgroup.getDescription());
			
			Set<String> workgroupUserNames = new HashSet<>();
			Map<String, Long> workgroupUserIds = new HashMap<>();
			for (User user : workgroup.getUsers()) {
				if(!user.isHiddenSupervisor()){
					String displayName = user.getFirstName().trim() + " " + user.getLastName().trim();
					workgroupUserNames.add(displayName);
					workgroupUserIds.put(displayName, user.getId());
				}
			}
			dataMap.put(USER_NAMES_PARAM, workgroupUserNames);
			dataMap.put(USER_IDS_PARAM, workgroupUserIds);
			
			//Preparing the Zone Associations table data. 
			Map<String, String[]> zoneAssociations = new HashMap<>();
			Set<String> touchpointNames = new HashSet<>();
			boolean hasZoneAssociations = false;
			
			Set<String> selectedDocumentZones = new HashSet<>();
			List<Document> documents = Document.findAll(true);
			for ( Document document : documents ) {
				for ( Zone zone : document.getZones() ) {
					if ( workgroup.getZones().contains(zone) )
						selectedDocumentZones.add( zone.getFriendlyName() );
				}
				if (!selectedDocumentZones.isEmpty()) {
					
			    	String[] selectedDocumentZonesArray = new String[0];
			    	selectedDocumentZonesArray = selectedDocumentZones.toArray(selectedDocumentZonesArray);
			    	Arrays.sort(selectedDocumentZonesArray);
					
					touchpointNames.add(document.getName());
					zoneAssociations.put(document.getName(), selectedDocumentZonesArray);
					
					hasZoneAssociations = true;
				}
				selectedDocumentZones.clear();
			}
			dataMap.put(TOUCHPOINT_NAMES_PARAM, touchpointNames);
			dataMap.put(ZONE_ASSOCIATIONS_PARAM, zoneAssociations);
			dataMap.put(HAS_ZONE_ASSOCIATIONS_PARAM, hasZoneAssociations);
		}
		
		return new ModelAndView(getFormView(), dataMap);
	}
	
	private long getParameterFromRequest(HttpServletRequest request, String parameterName){
		if(request.getParameterMap().containsKey(parameterName)){
			return ServletRequestUtils.getLongParameter(request, parameterName, -1);
		}
		return -1L;
	}		
}