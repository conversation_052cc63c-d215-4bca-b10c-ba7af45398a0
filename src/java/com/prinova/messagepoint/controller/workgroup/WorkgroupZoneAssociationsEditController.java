package com.prinova.messagepoint.controller.workgroup;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.workgroup.UpdateWorkgroupZoneAssociationService;
import com.prinova.messagepoint.security.StringXSSEditor;

public class WorkgroupZoneAssociationsEditController extends MessagepointController {

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {

		long touchpointId = getParameterFromRequest(request, WorkgroupZoneAssociationsViewController.TOUCHPOINT_ID_REQ_PARAM);

		Document document = Document.findById(touchpointId);

		List<Zone> zones = Zone.findByDocumentIdOrderByName(touchpointId);
		List<Workgroup> workgroups = Workgroup.findOrderByName();
		
		WorkgroupZoneAssociationsDataStructure workgroupZoneAssociationsDataStructure = WorkgroupUtil.getWorkgroupZoneAssociationsDataStructure(zones, workgroups); 
		
		Command command = new Command();

		command.setTouchpointName(document.getName());
		command.setWorkgroupZones(workgroupZoneAssociationsDataStructure.getWorkgroupZones());
		command.setZoneNames(workgroupZoneAssociationsDataStructure.getZoneNames());
		command.setZoneIds(workgroupZoneAssociationsDataStructure.getZoneIds());
		command.setWorkgroupNames(workgroupZoneAssociationsDataStructure.getWorkgroupNames());
		command.setWorkgroupIds(workgroupZoneAssociationsDataStructure.getWorkgroupIds());
		command.setAllZones(workgroupZoneAssociationsDataStructure.getAllZones());
		
		return command;
	}

	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException error) throws Exception {

		long touchpointId = getParameterFromRequest(request, WorkgroupZoneAssociationsViewController.TOUCHPOINT_ID_REQ_PARAM);
		
		Command command = (Command) commandObj;
		
		Map<Workgroup,Set<Zone>> workgroupZoneMap = new HashMap<>();
		
		for (int i=0 ; i<command.getWorkgroupIds().length ; i++) {
			Workgroup workgroup = Workgroup.findById(command.getWorkgroupIds()[i]);
			Set<Zone> selectedWorkgroupZones = new HashSet<>();
			for (int j=0 ; j<command.getZoneIds().length ; j++) {
				if (command.getWorkgroupZones()[i][j]) {
					Zone selectedZone = Zone.findById(command.getZoneIds()[j]);
					selectedWorkgroupZones.add(selectedZone);
				}
			}
			workgroupZoneMap.put(workgroup, selectedWorkgroupZones);
		}
		
		ServiceExecutionContext context = UpdateWorkgroupZoneAssociationService.createContext(touchpointId, workgroupZoneMap);

		Service updateWorkgroupZoneAssociationService = MessagepointServiceFactory.getInstance().lookupService(UpdateWorkgroupZoneAssociationService.SERVICE_NAME, UpdateWorkgroupZoneAssociationService.class);
		updateWorkgroupZoneAssociationService.execute(context);
		SimpleServiceResponse serviceResponse = (SimpleServiceResponse) context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, error);
			return super.showForm(request, response, error);
		} else {
			return new ModelAndView(new RedirectView(getSuccessView(), true));
		}
	}

	private long getParameterFromRequest(HttpServletRequest request, String parameterName){
		if(request.getParameterMap().containsKey(parameterName)){
			return ServletRequestUtils.getLongParameter(request, parameterName, -1);
		}
		return -1L;
	}	

	public static class Command extends WorkgroupZoneAssociationsDataStructure{
		private static final long serialVersionUID = 5369183238385280991L;

		private String touchpointName;

		public String getTouchpointName() {
			return touchpointName;
		}
		public void setTouchpointName(String touchpointName) {
			this.touchpointName = touchpointName;
		}
	}
}
