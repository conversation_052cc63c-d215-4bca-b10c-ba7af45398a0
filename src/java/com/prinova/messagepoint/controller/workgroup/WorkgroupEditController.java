package com.prinova.messagepoint.controller.workgroup;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.Parameter;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.workgroup.CreateWorkgroupService;
import com.prinova.messagepoint.platform.services.workgroup.UpdateWorkgroupService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class WorkgroupEditController extends MessagepointController {

	public static final String VALUE_SEPARATOR = "~@~";
	
	public static final String WORKGROUP_ID_REQ_PARAM = "wgid";
	public static final String FORM_SUBMIT_TYPE_SUBMIT = "submit";

	public static final String TOUCHPOINTS = "touchpoints";
	public static final String PARAMETERS = "parameters";
	public static final String PARAMETER_VALUES_MAP = "parameterValuesMap";
	
	@Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(Zone.class, new IdCustomEditor<>(Zone.class));
		binder.registerCustomEditor(Parameter.class, new IdCustomEditor<>(Parameter.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
    }

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		HashMap<String, Object> map = new HashMap<>();

		List<Document> documents = Document.findAll(true);
		map.put(TOUCHPOINTS, documents);
		
		return map;
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
	
    	Command command = getCommandObject(request);
    	command = bind(request, command);
		
		return command;
	}

    private Command bind( HttpServletRequest request, Command command ) throws Exception {
    	ServletRequestDataBinder binder = createBinder(request, command);
    	binder.bind( request );
		return command;
    }

    protected Command getCommandObject(HttpServletRequest request) throws Exception {
		long workgroupId = getParameterFromRequest(request, WORKGROUP_ID_REQ_PARAM);
		
		Command command = new Command();
		
		if ( workgroupId != -1 ) {
			Workgroup workgroup = Workgroup.findById(workgroupId);
			
			command.setInEditMode(true);
			command.setWorkgroupId(workgroupId);
			command.setName(workgroup.getName());
			command.setDescription(workgroup.getDescription());
			command.setDefaultWorkgroup(workgroup.isDefaultWorkgroup());
			command.setZones(workgroup.getZones());

			//Populating the (Select All)'s for Zones
			Map<Long, Boolean> allZonesMap = new HashMap<>();
			List<Document> documents = Document.findAll(true);
			for ( Document document : documents ) {
				boolean allZonesValue = true;
				for ( Zone zone : document.getZones() ) {
					if (!workgroup.getZones().contains(zone)) {
						allZonesValue = false;
						break;
					}
				}

				allZonesMap.put(document.getId(), allZonesValue);
			}
			command.setAllZones(allZonesMap);
		}
		
		return command;
    }
    
 	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		if( FORM_SUBMIT_TYPE_SUBMIT.equals(submitType) ){
			return true;
		}
		return false;
	}
	
	@Override
	protected boolean suppressValidation(HttpServletRequest request, Object command) {
		return !isFormSubmission(request);
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		
		Command command = (Command)commandObj;

		long workgroupId = command.getWorkgroupId();
		SimpleServiceResponse serviceResponse = null;
		if ( command.isInEditMode() )  {
			Workgroup workgroup = Workgroup.findById(workgroupId);
			// Set the allzones to false if there is a zone is not selected
			List<Document> documents = Document.findAll(true);
			for ( Document document: documents ) {
				long documentId = document.getId();
				Map<Long, Boolean> map = (Map<Long, Boolean>) command.getAllZones();
				List<Zone> zones = Zone.findByDocumentIdOrderByName(documentId);
				Set<Zone> selectedZones = command.getZones();
				if ( selectedZones != null ) {
					for ( Zone zone: zones ) {
						if ( !selectedZones.contains(zone) ) {
							map.remove(documentId);
							map.put(documentId, false);
							break;
						}
					}
				}

				// Restore the alternative layouts
				List<Document> alternativeLayouts = document.getAlternateLayouts();
				for(Document alternativeLayout : alternativeLayouts){
					Set<Zone> alZones = workgroup.getZones();
					alZones.retainAll(alternativeLayout.getZones());
					command.getZones().addAll(alZones);
				}
			}
			ServiceExecutionContext context = UpdateWorkgroupService.createContext(command.getName(), 
																	               command.getDescription(), 
																	               command.getZones(),
																	               UserUtil.getPrincipalUserId(),
																	               command.getWorkgroupId() );
			Service updateWorkgroupService = MessagepointServiceFactory.getInstance().lookupService(UpdateWorkgroupService.SERVICE_NAME, UpdateWorkgroupService.class);
			updateWorkgroupService.execute(context);
			serviceResponse = (SimpleServiceResponse) context.getResponse();
		} else {
			ServiceExecutionContext context = CreateWorkgroupService.createContext(command.getName(), command.getDescription(), command.getZones(), command.isDefaultWorkgroup());
			Service createWorkgroupService = MessagepointServiceFactory.getInstance().lookupService(CreateWorkgroupService.SERVICE_NAME, CreateWorkgroupService.class);
			createWorkgroupService.execute(context);
			serviceResponse = (SimpleServiceResponse) context.getResponse();
		}

		if (!serviceResponse.isSuccessful()) {
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
			return super.showForm(request, response, errors);
		} else {
			if (!command.isInEditMode())  {
				workgroupId = ((Long)serviceResponse.getResultValueBean()).longValue();
			}
			return new ModelAndView(new RedirectView(getSuccessView(), true), WORKGROUP_ID_REQ_PARAM, workgroupId);
		}
	}
	
	private long getParameterFromRequest(HttpServletRequest request, String parameterName){
		if(request.getParameterMap().containsKey(parameterName)){
			return ServletRequestUtils.getLongParameter(request, parameterName, -1);
		}
		return -1L;
	}	

	public static class Command implements Serializable {

		private static final long serialVersionUID = 4916220303439472264L;

		private boolean inEditMode = false;
		
		private long 				workgroupId;
		private String 				name;
		private String 				description;
		private boolean 			defaultWorkgroup 			= false;
		private Document 			selectedTouchpoint;
		private Set<Zone> 			zones;
		private Map<Long, Boolean> 	allZones 					= new HashMap<>();
		
		public boolean isInEditMode() {
			return inEditMode;
		}
		public void setInEditMode(boolean inEditMode) {
			this.inEditMode = inEditMode;
		}
		public long getWorkgroupId() {
			return workgroupId;
		}
		public void setWorkgroupId(long workgroupId) {
			this.workgroupId = workgroupId;
		}
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public String getDescription() {
			return description;
		}
		public void setDescription(String description) {
			this.description = description;
		}
		public boolean isDefaultWorkgroup() {
			return defaultWorkgroup;
		}
		public void setDefaultWorkgroup(boolean defaultWorkgroup) {
			this.defaultWorkgroup = defaultWorkgroup;
		}
		public Document getSelectedTouchpoint() {
			return selectedTouchpoint;
		}
		public void setSelectedTouchpoint(Document selectedTouchpoint) {
			this.selectedTouchpoint = selectedTouchpoint;
		}
		public Set<Zone> getZones() {
			return zones;
		}
		public void setZones(Set<Zone> zones) {
			this.zones = zones;
		}
		public Map<Long, Boolean> getAllZones() {
			return allZones;
		}
		public void setAllZones(Map<Long, Boolean> allZones) {
			this.allZones = allZones;
		}

	}
}