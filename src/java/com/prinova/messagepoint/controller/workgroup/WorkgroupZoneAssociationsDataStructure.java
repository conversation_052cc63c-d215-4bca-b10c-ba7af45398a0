package com.prinova.messagepoint.controller.workgroup;

import java.io.Serializable;

public class WorkgroupZoneAssociationsDataStructure implements Serializable {

	private static final long serialVersionUID = 2397160080787202987L;

	private boolean[][] workgroupZones;
	private boolean[] allZones;
	private String[] zoneNames;
	private String[] workgroupNames;
	private long[] workgroupIds;
	private long[] zoneIds;
	
	public boolean[][] getWorkgroupZones() {
		return workgroupZones;
	}
	public void setWorkgroupZones(boolean[][] workgroupZones) {
		this.workgroupZones = workgroupZones;
	}
	public boolean[] getAllZones() {
		return allZones;
	}
	public void setAllZones(boolean[] allZones) {
		this.allZones = allZones;
	}
	public String[] getZoneNames() {
		return zoneNames;
	}
	public void setZoneNames(String[] zoneNames) {
		this.zoneNames = zoneNames;
	}
	public String[] getWorkgroupNames() {
		return workgroupNames;
	}
	public void setWorkgroupNames(String[] workgroupNames) {
		this.workgroupNames = workgroupNames;
	}
	public long[] getWorkgroupIds() {
		return workgroupIds;
	}
	public void setWorkgroupIds(long[] workgroupIds) {
		this.workgroupIds = workgroupIds;
	}
	public long[] getZoneIds() {
		return zoneIds;
	}
	public void setZoneIds(long[] zoneIds) {
		this.zoneIds = zoneIds;
	}
}
