package com.prinova.messagepoint.controller.workgroup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.ZoneComparatorOnFrienlyName;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.util.UserUtil;

public class WorkgroupUtil {
	
	public static WorkgroupZoneAssociationsDataStructure getWorkgroupZoneAssociationsDataStructure(List<Zone> zones, List<Workgroup> workgroups) {

		boolean[][] workgroupZones = new boolean[workgroups.size()][zones.size()];
		String[] zoneNames = new String[zones.size()];
		long[] zoneIds = new long[zones.size()];
		String[] workgroupNames = new String[workgroups.size()];
		long[] workgroupIds = new long[workgroups.size()];
		boolean[] allZones = new boolean[workgroups.size()];
		boolean allZonesValue = true;

		for (int i = 0 ; i < workgroups.size() ; i++) {
			workgroupNames[i] = workgroups.get(i).getName();
			if (workgroupNames[i].length() > 25) {
				workgroupNames[i] = workgroupNames[i].substring(0, 24) + "...";
			}
			workgroupIds[i] = workgroups.get(i).getId();
			allZonesValue = true;
			for (int j = 0 ; j < zones.size() ; j++) {
				zoneNames[j] = zones.get(j).getFriendlyName();
				if (zoneNames[j].length() > 16) {
					zoneNames[j] = zoneNames[j].substring(0, 15) + "...";
				}
				zoneIds[j] = zones.get(j).getId();
				if (workgroups.get(i).getZones().contains(zones.get(j))) {
					workgroupZones[i][j] = true; 
				} else {
					workgroupZones[i][j] = false;
					allZonesValue = false;
				}
			}
			allZones[i] = allZonesValue;
		}
		
		WorkgroupZoneAssociationsDataStructure workgroupZoneAssociations = new WorkgroupZoneAssociationsDataStructure();
		
		workgroupZoneAssociations.setWorkgroupZones(workgroupZones);
		workgroupZoneAssociations.setZoneNames(zoneNames);
		workgroupZoneAssociations.setZoneIds(zoneIds);
		workgroupZoneAssociations.setWorkgroupNames(workgroupNames);
		workgroupZoneAssociations.setWorkgroupIds(workgroupIds);
		workgroupZoneAssociations.setAllZones(allZones);
		
		return workgroupZoneAssociations;

	}

	public static Zone[] getCurrentWorkgroupZonesActive(Document document) {
		Set<Zone> unsorted = getCurrentWorkgroupZoneActiveMessageMap(document).keySet();
		Zone[] sortedArray = new Zone[unsorted.size()];
		// Sort the zones
		Arrays.sort(unsorted.toArray(sortedArray), new ZoneComparatorOnFrienlyName());
		return sortedArray;
	}
	
	/**
	 * Returns a map having each of the zones of the Workgroup corresponding to workgroupId parameter 
	 * passed to the method as the key and a List of Messages that that Zone is delivered to as the value.
	 * 
	 * @param document -
     */
	public static Map<Zone, List<ContentObject>> getCurrentWorkgroupZoneActiveMessageMap(Document document) {
		Map<Zone, List<ContentObject>> map = new HashMap<>();
		Workgroup workgroup = Workgroup.findById(UserUtil.getPrincipalUser().getWorkgroupId());
		Set<Zone> zones = new HashSet<>();
		if(document != null){	// Touchpoint centric
			zones.addAll(document.findZonesFilteredByWorkgroup(workgroup.getId()));
		}else{	// Global
			zones.addAll(workgroup.getZones());
		}
		for (Zone zone : zones) {
			List<ContentObject> zoneMessages = new ArrayList<>();
			List<ContentObject> zoneMessageInstances = ContentObjectUtil.convertToSortedContentObjectList(zone.getContentObjects());
			for (ContentObject contentObject : zoneMessageInstances) {
				if (! zoneMessages.contains(contentObject) && !contentObject.isRemoved() && contentObject.hasActiveData() ) {
					zoneMessages.add(contentObject);
				}
			}
			if (!zoneMessages.isEmpty()) {
				map.put(zone, zoneMessages);				
			}
		}
		return map;
	}
}
