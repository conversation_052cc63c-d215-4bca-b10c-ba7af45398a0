package com.prinova.messagepoint.controller.workgroup;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.workgroup.Workgroup;

public class WorkgroupZoneAssociationsViewController implements Controller {
	
	public static final String TOUCHPOINT_ID_REQ_PARAM = "tpid";
	
	public static final String TOUCHPOINT_ID = "touchpointid";
	public static final String TOUCHPOINTS = "touchpoints";
	public static final String WORKGROUP_ZONES = "workgroupZones";
	public static final String ALL_ZONES = "allZones";
	public static final String ZONE_NAMES = "zoneNames";
	public static final String WORKGROUP_NAMES = "workgroupNames";
	public static final String WORKGROUP_IDS = "workgroupIds";

	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception  {
		
		Map<String, Object> dataMap = new HashMap<>();

		long touchpointId = getParameterFromRequest(request, TOUCHPOINT_ID_REQ_PARAM);

		List<Document> documents = Document.findAll(true);

		if (touchpointId == -1) {
			if (!documents.isEmpty()) {
				touchpointId = documents.get(0).getId();
			}
		}

		List<Zone> zones = Zone.findByDocumentIdOrderByName(touchpointId);
		List<Workgroup> workgroups = Workgroup.findOrderByName();

		WorkgroupZoneAssociationsDataStructure workgroupZoneAssociationsDataStructure = WorkgroupUtil.getWorkgroupZoneAssociationsDataStructure(zones, workgroups); 

		dataMap.put(TOUCHPOINTS, documents);
		dataMap.put(WORKGROUP_ZONES, workgroupZoneAssociationsDataStructure.getWorkgroupZones());
		dataMap.put(ALL_ZONES, workgroupZoneAssociationsDataStructure.getAllZones());
		dataMap.put(ZONE_NAMES, workgroupZoneAssociationsDataStructure.getZoneNames());
		dataMap.put(WORKGROUP_NAMES, workgroupZoneAssociationsDataStructure.getWorkgroupNames());
		dataMap.put(WORKGROUP_IDS, workgroupZoneAssociationsDataStructure.getWorkgroupIds());
		dataMap.put(TOUCHPOINT_ID, touchpointId);
		
		return new ModelAndView(getFormView(), dataMap);
	}
	
	private long getParameterFromRequest(HttpServletRequest request, String parameterName){
		if(request.getParameterMap().containsKey(parameterName)){
			return ServletRequestUtils.getLongParameter(request, parameterName, -1);
		}
		return -1L;
	}	
}