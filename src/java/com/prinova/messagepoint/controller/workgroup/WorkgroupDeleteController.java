package com.prinova.messagepoint.controller.workgroup;

import java.io.Serializable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.SimpleServiceResponse;
import com.prinova.messagepoint.platform.services.workgroup.DeleteWorkgroupService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.HibernateUtil;

public class WorkgroupDeleteController extends MessagepointController {
    
	public static final String REQ_PARAM = "wgid";
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		Command command = new Command();
		long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM);
		if (id >0){
			Workgroup workgroup = HibernateUtil.getManager().getObject(Workgroup.class, id);
			command.setWorkgroup(workgroup);
		}
		return command;
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		Command command = (Command) commandObj;
		ServiceExecutionContext context = DeleteWorkgroupService.createContext(command.getWorkgroup().getId());

		Service deleteWorkgroupService = MessagepointServiceFactory.getInstance().lookupService(DeleteWorkgroupService.SERVICE_NAME, DeleteWorkgroupService.class);
		deleteWorkgroupService.execute(context);
		SimpleServiceResponse serviceResponse = (SimpleServiceResponse) context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
			return super.showForm(request, response, errors);
		} else {
			return new ModelAndView(new RedirectView(getSuccessView(), true));
		}
	}
	
	public static class Command implements Serializable {
		
		private static final long serialVersionUID = 7168937931925492613L;

		private Workgroup workgroup;

		public Workgroup getWorkgroup() {
			return workgroup;
		}

		public void setWorkgroup(Workgroup workgroup) {
			this.workgroup = workgroup;
		}
	}
}
