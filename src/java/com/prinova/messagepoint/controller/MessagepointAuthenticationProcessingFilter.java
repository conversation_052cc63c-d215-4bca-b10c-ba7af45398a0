package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.platform.auth.PINCOpenIdConnectUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.UpdateUserService;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninService;
import com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninServiceRequest;

public class MessagepointAuthenticationProcessingFilter extends UsernamePasswordAuthenticationFilter {
	
	private static final Log log = LogUtil.getLog(MessagepointAuthenticationProcessingFilter.class);

	public static final String INVALID_SIGNIN_ATTEMPS = "invalidSigninAttempts";
	public static final String AUTHENTICATION_FAIL_DEFAULTMSG_KEY="?msgkey=code.text.authenticationfailed";
	public static final String AUTHENTICATION_FAIL_ACCOUNTLOCKED_KEY="?msgkey=code.text.accountlocked";
	public static final String AUTHENTICATION_FAIL_SOFT_DEACTIVATED = "?msgkey=code.text.soft.deactivated.error";
	public static final String AUTHENTICATION_FAIL_ACCESS_DENIED_KEY="?msgkey=code.text.accessdenied";
	public static final String AUTHENTICATION_FAIL_SECURITY_SETTINGS_KEY = "&msgkey=code.text.security.settings.username.password.invalid";

	public static final String LOGIN_FAILURE_REASON_USERID = "1";
	public static final String LOGIN_FAILURE_REASON_PASSWORD = "2";
	public static final String LOGIN_FAILURE_REASON_USERID_PASSWORD = "3";
	public static final String LOGIN_FAILURE_REASON_SOFT_DEACTIVATED = "4";
	public static final String SESSION_ATTRIBUTE_FAILURE_URL = "FAILED_URL";
	
	public static final String 	BRANCH_COOKIE_NAME = "branch";
	public static final int 	BRANCH_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
	public static final String 	IDP_BRANCH_COOKIE_NAME = "idp-branch";
	public static final int 	IDP_BRANCH_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
	public static final String 	NODE_COOKIE_NAME = "node";
	public static final int 	NODE_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
	public static final String 	USER_COOKIE_NAME = "username";
	public static final int 	USER_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)
	public static final String 	IDP_USER_COOKIE_NAME = "idp-username";
	public static final int 	IDP_USER_COOKIE_AGE = 60*60*24*7;		//lasts one week (60 seconds per minute, 60 mins per hour, 24 hours per day 7 days per week)

	private String unmatchingUserIdUrl;
	private String unmatchingPasswordUrl;
	private String unmatchingUserIdAndPasswordUrl;
	
	public String getUnmatchingUserIdUrl() {
		return unmatchingUserIdUrl;
	}

	public void setUnmatchingUserIdUrl(String unmatchingUserIdUrl) {
		this.unmatchingUserIdUrl = unmatchingUserIdUrl;
	}
	
    public String getUnmatchingPasswordUrl() {
		return unmatchingPasswordUrl;
	}

	public void setUnmatchingPasswordUrl(String unmatchingPasswordUrl) {
		this.unmatchingPasswordUrl = unmatchingPasswordUrl;
	}

	public String getUnmatchingUserIdAndPasswordUrl() {
		return unmatchingUserIdAndPasswordUrl;
	}

	public void setUnmatchingUserIdAndPasswordUrl(
			String unmatchingUserIdAndPasswordUrl) {
		this.unmatchingUserIdAndPasswordUrl = unmatchingUserIdAndPasswordUrl;
	}

	public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
		//
		// STEP 1: Use the super class to authenticate the username and password against Users table
		//

		String mpUsername = (String) request.getAttribute("mp_username");
		
		Authentication auth = null;
		if (mpUsername != null && !mpUsername.isEmpty())
		{
	        if (!request.getMethod().equals("POST")) {
	            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
	        }

	        String password = obtainPassword(request);

	        if (password == null) {
	            password = "";
	        }

	        mpUsername = mpUsername.trim();

	        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(mpUsername, password);

	        // Allow subclasses to set the "details" property
	        setDetails(request, authRequest);

	        auth = this.getAuthenticationManager().authenticate(authRequest);
			
		}
		else
		{
			boolean isOIDCAuth = request.getSession().getAttribute(PINCOpenIdConnectUtil.REQ_PARAM_OIDC_AUTH) != null;
			boolean isIDPSignIn = isOIDCAuth && request.getRequestURI().contains("j_spring_security_check");
			if (isIDPSignIn && request.getParameterMap().size() == 3) {
				String branchName = request.getParameter("j_branch");
				String signin_username = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_USERNAME_KEY);
				String signin_pwd = request.getParameter(UsernamePasswordAuthenticationFilter.SPRING_SECURITY_FORM_PASSWORD_KEY);
				if (branchName != null && signin_username != null && signin_pwd != null) {
					UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(signin_username + "#idp#" + branchName, signin_pwd);
					setDetails(request, authRequest);
					auth = this.getAuthenticationManager().authenticate(authRequest);
				}
				else {
					throw new AuthenticationServiceException("Invalid request parameters for IDP sign-in");
				}
			}
			else {
				auth = super.attemptAuthentication(request, response);
			}
		}
			
		User principalUser = (User) auth.getPrincipal();
		if (principalUser == null) {
			return auth;
		}

		return auth;
	}

	protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain, Authentication authResult) throws IOException, ServletException{
		User user = null;
		if (authResult != null)
		{
			Object authorizedUser = authResult.getPrincipal();
			if (authorizedUser instanceof User) {
				user = (User) authorizedUser;
				setWebDAVToken(user);
			}
		}
		processCookies(request, response, user);
		super.successfulAuthentication(request, response, chain, authResult);
	}
	
	private void setWebDAVToken(User user) {
		ServiceExecutionContext context = UpdateUserService.createContextForNewWebDAVToken(user.getId());
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateUserService.SERVICE_NAME, UpdateUserService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (!serviceResponse.isSuccessful()) {
			StringBuilder sb = new StringBuilder();
			sb.append(UpdateUserService.SERVICE_NAME);
			sb.append(" service call to set webDAV token failed");
			log.error(sb.toString());
		}
	}
    
	private void processCookies(HttpServletRequest request, HttpServletResponse response, User user) {

		if (logger.isInfoEnabled()) {
			String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
    		if (currentSchema == null)
    			currentSchema = "POD MASTER (" + MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName() + ")";

    		logger.info("Authentication success: Username: " + obtainUsername(request) + "; Schema: " + currentSchema);
		}
		String username = obtainUsername(request);
		resetUserInvalidSigninAttempts(username);

		String branch = request.getParameter("j_branch");
		if (branch == null) {
			branch = "";
		}
		String node = request.getParameter("j_node");
		if (node == null) {
			node = "";
		}

		try {
			
			Cookie branchCookie = new Cookie(BRANCH_COOKIE_NAME, URLEncoder.encode(branch, "UTF-8"));
			Cookie nodeCookie = new Cookie(NODE_COOKIE_NAME, URLEncoder.encode(node, "UTF-8"));
			Cookie userCookie = new Cookie(USER_COOKIE_NAME, URLEncoder.encode(username, "UTF-8"));
			
			String remember = request.getParameter("remembermeCheckbox");
			if (remember != null && remember.equals("true")) 
			{
				branchCookie.setMaxAge(BRANCH_COOKIE_AGE);
				nodeCookie.setMaxAge(NODE_COOKIE_AGE);
				userCookie.setMaxAge(USER_COOKIE_AGE);
			}
			else 
			{ 	
				branchCookie.setMaxAge(0);
				nodeCookie.setMaxAge(0);
				userCookie.setMaxAge(0);
			}
			
			response.addCookie(branchCookie);
			response.addCookie(nodeCookie);
			response.addCookie(userCookie);
			
		} catch (UnsupportedEncodingException e) {
			LogUtil.getLog(MessagepointAuthenticationProcessingFilter.class).error("Error:", e);
		}
	}

	private void resetUserInvalidSigninAttempts(String userName) {
		ServiceExecutionContext context = ProcessInvalidSigninService.createContext(userName);
		ProcessInvalidSigninServiceRequest request = (ProcessInvalidSigninServiceRequest) context.getRequest();
		request.setResetFlag(true);
		Service invalidSigninService = MessagepointServiceFactory.getInstance().lookupService(ProcessInvalidSigninService.SERVICE_NAME, ProcessInvalidSigninService.class);
		invalidSigninService.execute(context);

	}

}