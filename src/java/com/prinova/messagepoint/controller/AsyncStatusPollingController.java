package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.DocumentPreview;
import com.prinova.messagepoint.model.StatusPollingBackgroundTask;
import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.proof.Proof;
import com.prinova.messagepoint.model.testing.TestScenario;
import com.prinova.messagepoint.model.testing.TestSuite;
import com.prinova.messagepoint.util.HibernateUtil;

public class AsyncStatusPollingController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncStatusPollingController.class);
	
	public static final String REQ_PARAM_TYPE 	= "type";
	public static final String REQ_PARAM_ID 	= "itemId";
	public static final String REQ_PARAM_DATA	= "data";
	
	public static final String TYPE_PREVIEW 		= "preview";
	public static final String TYPE_PROOF			= "proof";
	public static final String TYPE_AUDIT_REPORT	= "auditreport";
	public static final String TYPE_SYNC_REPORT		= "syncreport";
	public static final String TYPE_TEST			= "testscenario";
	public static final String TYPE_TEST_BATCH		= "testscenarios";
	public static final String TYPE_SIMILARITY_REPORT	= "similarityreport";
	public static final String TYPE_RTN_DOCUMENT_REPORT	= "rationalizerdocumentreport";
	public static final String TYPE_RTN_METADATA_REPORT	= "rationalizermetadatareport";
	public static final String TYPE_TEST_SUITE			= "testsuite";
	
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseXML(request).getBytes());
			out.flush();
		} catch (Exception e) {
			try {
				out.write(getErrorXML().getBytes());
				log.error("Status polling failed: "+e.getMessage(),e);
				out.flush();
			} catch (RuntimeException e1) {
				log.error("Status polling failed: Runtime error: "+e.getMessage(),e1);
			}
		}
		
		return null;
	}
	
	private String getErrorXML(){
		return "<?xml version='1.0'?><error>Status polling failed</error>";
	}
	
	private String getResponseXML (HttpServletRequest request) {
		String type = ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, null);
		String returnContent = "";

		if ( (TYPE_PREVIEW).equals(type.toLowerCase()) ) {
			DocumentPreview preview = getPreviewInstance(request);
			if (preview != null) {
				if (!preview.isComplete() && !preview.isError())
					returnContent += "<status value='inProcess'>"+preview.getId()+"</status>";
				else if (!preview.isError())
					returnContent += "<status value='complete' type='save'>"+preview.getResourceToken()+"</status>";
				else if (preview.isError())
					returnContent += 	"<status value='error' deliveryId='"+preview.getDeliveryEvent().getId()+"' itemClass='"+preview.getClass().getName()+"'>" +
											"PREVIEW ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Preview:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ( (TYPE_PROOF).equals(type.toLowerCase()) ) {
			Proof proof = getProofInstance(request);
			if (proof != null) {
				if (!proof.isComplete() && !proof.isError())
					returnContent += "<status value='inProcess'>"+proof.getId()+"</status>";
				else if (proof.isHasDataError())
					returnContent += 	"<status value='invalidData' deliveryId='"+proof.getDeliveryEvent().getId()+"' itemClass='"+proof.getClass().getName()+"'>" +
											"INVALID PROOF DATA" +
										"</status>";
				else if ( !proof.isError() && proof.getOutputPath() == null )
					returnContent += 	"<status value='log' deliveryId='"+proof.getDeliveryEvent().getId()+"' itemClass='"+proof.getClass().getName()+"'>" +
											"PROOF LOG" +
										"</status>";
				else if (!proof.isError())
					returnContent += "<status value='complete' type='save'>"+proof.getResourceToken()+"</status>";
				else if (proof.isError())
					returnContent += 	"<status value='error' deliveryId='"+proof.getDeliveryEvent().getId()+"' itemClass='"+proof.getClass().getName()+"'>" +
											"PROOF ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Proof:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ( (TYPE_AUDIT_REPORT).equals(type.toLowerCase()) || (TYPE_SYNC_REPORT).equals(type.toLowerCase())) {
			AuditReport auditReport = getAuditReportInstance(request);
			if (auditReport != null) {
				if (!auditReport.isComplete() && !auditReport.isError())
					returnContent += "<status value='inProcess' type='auditReport'>"+auditReport.getId()+"</status>";
				else if (!auditReport.isError())
					returnContent += "<status value='complete' type='popup'>"+auditReport.getReportPath()+"</status>";
				else if (auditReport.isError())
					returnContent += 	"<status value='error' deliveryId='-1' itemClass='null' type='auditReport'>" +
											"AUDIT REPORT ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Audit Report:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ((TYPE_TEST).equals(type.toLowerCase())){
			TestScenario testScenario = getTestScenario(request);
			if(testScenario != null){
				if(testScenario.isInProcess()){
					returnContent += "<status value='inProcess'>"+testScenario.getId()+"</status>";
				}else{
					returnContent += "<status value='complete' >"+testScenario.getId()+"</status>";
				}
			}else{
				return "<?xml version='1.0'?><status value='error'>InvalidItemID Test Scenario:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ((TYPE_TEST_BATCH).equals(type.toLowerCase())){
			if(!this.hasTestComplete(request)){
				returnContent += "<status value='inProcess' />";
			}else{
				returnContent += "<status value='complete' />";
			}
		} else if ( (TYPE_SIMILARITY_REPORT).equals(type.toLowerCase()) ) {
			StatusPollingBackgroundTask task = getStatusPollingBackgroundTask(request);
			if (task != null) {
				if (!task.isComplete() && !task.isError())
					returnContent += "<status value='inProcess'>"+task.getId()+"</status>";
				else if (!task.isError()) {
					String reportTypeValue = FileUtil.getFileExtension(task.getOutputFilename());
					returnContent += "<status value='complete' type='rationalizerReportDownload' reportType='" + reportTypeValue + "'>" + task.getResourceToken() + "</status>";
				}
				else if (task.isError())
					returnContent += 	"<status value='error' deliveryId='-1' itemClass='null'>" +
											"REPORT ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Report:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ( (TYPE_RTN_DOCUMENT_REPORT).equals(type.toLowerCase()) ) {
			StatusPollingBackgroundTask task = getStatusPollingBackgroundTask(request);
			if (task != null) {
				if (!task.isComplete() && !task.isError())
					returnContent += "<status value='inProcess'>"+task.getId()+"</status>";
				else if (!task.isError()) {
					String reportTypeValue = FileUtil.getFileExtension(task.getOutputFilename());
					returnContent += "<status value='complete' type='rationalizerExportDownload' reportType='" + reportTypeValue + "'>" + task.getResourceToken() + "</status>";
				}
				else if (task.isError())
					returnContent += 	"<status value='error' deliveryId='-1' itemClass='null'>" +
											"REPORT ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Report:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ( (TYPE_RTN_METADATA_REPORT).equals(type.toLowerCase()) ) {
			StatusPollingBackgroundTask task = getStatusPollingBackgroundTask(request);
			if (task != null) {
				if (!task.isComplete() && !task.isError())
					returnContent += "<status value='inProcess'>"+task.getId()+"</status>";
				else if (!task.isError()) {
					String reportTypeValue = FileUtil.getFileExtension(task.getOutputFilename());
					returnContent += "<status value='complete' type='rationalizerReportDownload' reportType='" + reportTypeValue + "'>" + task.getResourceToken() + "</status>";
				}
				else if (task.isError())
					returnContent += 	"<status value='error' deliveryId='-1' itemClass='null'>" +
											"REPORT ERROR" +
										"</status>";
			} else {
				return 	"<?xml version='1.0'?><status value='error'>InvalidItemID Report:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		} else if ((TYPE_TEST_SUITE).equals(type.toLowerCase())){
			TestSuite testSuite = getTestSuite(request);
			if(testSuite != null){
				if(testSuite.isInProcess()){
					returnContent += "<status value='inProcess'>"+testSuite.getId()+"</status>";
				}else{
					returnContent += "<status value='complete' >"+testSuite.getId()+"</status>";
				}
			}else{
				return "<?xml version='1.0'?><status value='error'>InvalidItemID Test Suite:"+ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1)+"</status>";
			}
		}

		return "<?xml version='1.0'?>"+returnContent;	
	}
	
	private DocumentPreview getPreviewInstance(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(DocumentPreview.class, id);
	}
	
	private Proof getProofInstance(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(Proof.class, id);
	}
	
	private AuditReport getAuditReportInstance(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(AuditReport.class, id);
	}
	
	private StatusPollingBackgroundTask getStatusPollingBackgroundTask(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(StatusPollingBackgroundTask.class, id);
	}

	private TestScenario getTestScenario(HttpServletRequest request){
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(TestScenario.class, id);
	}
	
	private TestSuite getTestSuite(HttpServletRequest request){
		Long id = ServletRequestUtils.getLongParameter(request, REQ_PARAM_ID, -1);
		return HibernateUtil.getManager().getObject(TestSuite.class, id);
	}
	
	private boolean hasTestComplete(HttpServletRequest request){
		String data = ServletRequestUtils.getStringParameter(request, REQ_PARAM_DATA, "");
		String[] testIdStrArr = data.split("-");
		for(String testIdStr : testIdStrArr){
			TestScenario testScenario = TestScenario.findById(Long.valueOf(testIdStr));
			if(!testScenario.isInProcess()){
				return true;
			}
		}
		return false;
	}

}