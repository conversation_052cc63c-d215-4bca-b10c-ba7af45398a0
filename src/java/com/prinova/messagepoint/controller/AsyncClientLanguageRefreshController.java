package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.ApplicationUtil;

public class AsyncClientLanguageRefreshController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncClientLanguageRefreshController.class);
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getClientLanguageRefreshResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for client language refresh: "+e.getMessage(),e);
		}

		return null;
	}

	private String getClientLanguageRefreshResponseJSON (HttpServletRequest request) {
			
		JSONObject returnObj = new JSONObject();
		
		try {
			ApplicationUtil.refreshMessageSource();
			ApplicationLanguageUtils.generateApplicationClientLanguageFiles();

			returnObj.put("success", true);

		} catch (JSONException e) {
			log.error("Error: Unable to process request for client language refresh: " + e );
		}

		return returnObj.toString();
	}
}