package com.prinova.messagepoint.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.web.bind.ServletRequestUtils;

import com.prinova.messagepoint.controller.communication.CommunicationPortalGatewayController;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class MessagepointLogoutFilter extends LogoutFilter {

	private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
	private List<LogoutHandler> handlers;
	
	public MessagepointLogoutFilter(String logoutSuccessUrl, LogoutHandler... handlers) {
		super(logoutSuccessUrl, handlers);
		this.handlers = Arrays.asList(handlers);
	}
	
	@Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;

        if (requiresLogout(request, response)) {
    		Cookie langCookie = new Cookie("lang", UserUtil.getAppLangCodeForPrincipal());
    		langCookie.setMaxAge(60); // lasts one minute (60 seconds per minute)
    		response.addCookie(langCookie); 
    		
        	String ssoUrl = determineSSOTargetUrl(request, response);
        	
        	if (ssoUrl != null && !ssoUrl.isEmpty())
        	{
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();

                if (logger.isDebugEnabled()) {
                    logger.debug("Logging out user '" + auth + "' and transferring to logout destination");
                }

                for (LogoutHandler handler : handlers) {
                    handler.logout(request, response, auth);
                }

                redirectStrategy.sendRedirect(request, response, ssoUrl);

                return;				
        	}
        	else
        	{
        		super.doFilter(request, response, chain);
        	}
		} 
        else {
        	chain.doFilter(request, response);
		}
	}

    private String determineSSOTargetUrl(HttpServletRequest request, HttpServletResponse response) {
    	String targetUrl = null;
    	
    	User user = UserUtil.getPrincipalUser();
    	
    	String returnUrlOverride	= CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
		if ( returnUrlOverride != null && returnUrlOverride.equalsIgnoreCase("null") )
			returnUrlOverride = null;
    	if ( returnUrlOverride != null  ) {
	    	try {
				returnUrlOverride	= URLDecoder.decode(returnUrlOverride, "UTF-8");
			} catch (UnsupportedEncodingException e) {
				logger.debug("Failed to decode return_url");
			}
    	}
    	
    	if (user != null && user.isSSOSession())
    	{
    		if (user.getSSOSessionType() == User.SSO_TYPE_PROXY_PRODUCTION || user.getSSOSessionType() == User.SSO_TYPE_PROXY_TRANSITION)
    		{
    			SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
	        	SystemPropertyManager spm = SystemPropertyManager.getInstance();
        		targetUrl = spm.getSystemProperty(SystemPropertyKeys.ProxySSO.KEY_ProxySSOMasterPageUrl);
    			HibernateUtil.getManager().restoreSession(mainSessionHolder);
				if (targetUrl != null && !targetUrl.isEmpty())
				{
					targetUrl += "/displaymessage.form?msgkey=page.text.signoutsuccess";
					if (user.getSSORequestToken() != null)
						targetUrl += "&reqt=" + user.getSSORequestToken();
				}
    		}
    		else
    		{
	    		Branch branch = Node.getCurrentBranch();
	    		if (branch != null)
	    		{
	    			targetUrl = returnUrlOverride != null ? returnUrlOverride : branch.getSsoLogoutPageUrl();
	    		}
				if (targetUrl != null && !targetUrl.isEmpty())
				{
					if (user.getSSORequestToken() != null)
						targetUrl += "?reqt=" + user.getSSORequestToken();
				}
				else
				{
		    		if (user.getSSOSessionType() == User.SSO_TYPE_PING_PROXY_PRODUCTION || user.getSSOSessionType() == User.SSO_TYPE_PING_PROXY_TRANSITION)
		    		{
		    			SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
			        	SystemPropertyManager spm = SystemPropertyManager.getInstance();
		        		targetUrl = spm.getSystemProperty(SystemPropertyKeys.ProxySSO.KEY_ProxySSOMasterPageUrl);
		    			HibernateUtil.getManager().restoreSession(mainSessionHolder);
						if (targetUrl != null && !targetUrl.isEmpty())
						{
							targetUrl += "/displaymessage.form?msgkey=page.text.signoutsuccess";
							if (user.getSSORequestToken() != null)
								targetUrl += "&reqt=" + user.getSSORequestToken();
						}
		    		}
				}
    		}
    	}
    	
		// CONNECTED EMBEDDED: Add order guid to signout URL
		String orderGUID 		= ServletRequestUtils.getStringParameter(request, CommunicationPortalGatewayController.REQ_PARAM_ORDER_GUID, null);
		if ( targetUrl != null && orderGUID != null )
			targetUrl += (targetUrl.indexOf("?") == -1 ? "?" : "&") + CommunicationPortalGatewayController.REQ_PARAM_ORDER_GUID + "=" + orderGUID;
    	
        return targetUrl;
    }

	public List<LogoutHandler> getHandlers() {
		return handlers;
	}

	public RedirectStrategy getRedirectStrategy() {
		return redirectStrategy;
	}
}
