package com.prinova.messagepoint.controller;

import com.idrsolutions.image.jpeg.JpegEncoder;

import com.prinova.messagepoint.email.EmailTemplateUtils;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ContentType;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.audit.*;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.communication.CommunicationsUtil;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.util.*;
import com.prinova.messagepoint.util.PDFUtilities.PageSizeType;
import com.prinova.messagepoint.util.PDFUtilities.PageUnits;

import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;

import org.apache.commons.logging.Log;
import org.jpedal.examples.images.ConvertPagesToImages;
import org.jpedal.examples.text.FindTextInRectangle;
import org.jpedal.fonts.FontMappings;
import org.jpedal.grouping.SearchType;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;
import org.springframework.ws.soap.client.SoapFaultClientException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;

public class AsyncConnectedInteractiveController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncConnectedInteractiveController.class);

	public static final String PARAM_TYPE						= "type";
	public static final String PARAM_PREPROOF_ID				= "preProofId";
	public static final String PARAM_PAGE						= "page";
	public static final String PARAM_ZONE_ID					= "zoneId";
	public static final String PARAM_COMMUNICATION_ID			= "communicationId";
	public static final String PARAM_DEBUG_DATA					= "debug_data";
	
	public static final String VALUE_GENERAL_DATA 				= "general_data";
	public static final String VALUE_EDITOR_DATA				= "editor_data";
	public static final String VALUE_VARIABLE_DATA				= "variable_data";
	public static final String VALUE_AUDIT_LOAD_COMPLETE		= "audit_load_complete";
	public static final String VALUE_CONVERSION					= "conversion";
	
	private boolean debugMode = false;
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			String type	= ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
			
			if ( type == null ) {
				JSONObject returnObj = new JSONObject();
				returnObj.put("error", true);
				returnObj.put("message", "Error - unspecified request data type");
				out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
			} else {
				
				if ( type.equalsIgnoreCase(VALUE_GENERAL_DATA) ) {
					out.write( getGeneralDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_EDITOR_DATA) ) {
					out.write( getEditorDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_CONVERSION) ) {
					out.write( getTouchpointConversionResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_VARIABLE_DATA) ) {
					out.write( getVariableDataResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else if ( type.equalsIgnoreCase(VALUE_AUDIT_LOAD_COMPLETE) ) {
					out.write( getAuditLoadCompleteResponseJSON(request).getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				} else {
					JSONObject returnObj = new JSONObject();
					returnObj.put("error", true);
					returnObj.put("message", "Error - invalide request data type");
					out.write( returnObj.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING) );
				}
			}
			
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for communication data: ", e);
		}

		return null;
	}
	
	private JSONObject getZoneJSON(Zone zone) {
		JSONObject zoneObj = new JSONObject();
		
		// ZONE ID: Identified by master document zone ids
		long zoneId = zone.getId();
		if ( zone.isAlternate() )
			zoneId = zone.getParent().getId();

		try {
			zoneObj.put("rotation"		, zone.getRotationAngle());
			zoneObj.put("id"			, zoneId);
			zoneObj.put("name"			, zone.getFriendlyName());
			zoneObj.put("connector"		, zone.getName());
			zoneObj.put("content_type"	, zone.getContentTypeId() == ContentType.GRAPHIC ? "graphic" : "text");
			zoneObj.put("enabled"		, zone.isEnabled());
		} catch (JSONException e) {
			log.error("Error: Unable to build pre-proof connected data response: ", e );
			
			try {
				zoneObj.put("error", true);
				zoneObj.put("message", "Caught JSONException: Error - Unable to build pre-proof connected data response");
				zoneObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}
		
		return zoneObj;
	}
	
	private String getVariableDataResponseJSON( HttpServletRequest request ) {
		
		long startTime = 0;
		if (debugMode)
			startTime = new Date().getTime();
		
		JSONObject varDataObj = new JSONObject();
		JSONObject debugObj = new JSONObject();
		
		try {

			long preProofId 			= ServletRequestUtils.getLongParameter(request, PARAM_PREPROOF_ID, -1);
			long communicationId		= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
			
			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			Communication communication = null;
			if ( preProof != null )
				communication = preProof.getCommunication();
			else
				communication = Communication.findById(communicationId);
			
			JSONArray variableValuesArray = new JSONArray();
			
			if ( communication.getDocument().isCommunicationResolveVariableValues() && communication.getDocument().getCommunicationCompositionResultsWebService() != null ) {
			
				// WS: Get Variable Values
				long wsVarValuesSartTime = new Date().getTime();

				Map<String, String> variableValueMap = CommunicationWSClient.retrieveVariableResolutionData (
						communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(), 
						communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(), 
						communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
						communication.getGuid(), communication.getPmgrOrderUUID()
				);
				// Map<String,String> variableValueMap = new HashMap<String,String>();
				
				debugObj.put("ws_get_variable_values", (new Date().getTime() - wsVarValuesSartTime) );
	
				// Build response
				long buildVarValuesSartTime = new Date().getTime();


                List<String> dnaList = new ArrayList<>(variableValueMap.keySet());

				// FOR TESTING
//				List<DataElementVariable> testVariables = DataElementVariable.findAll();
//				for ( DataElementVariable testVar: testVariables ) {
//					dnaList.add(testVar.getDNA());
//					
//					if ( testVar.getId() == 1273 ) 
//						variableValueMap.put(testVar.getDNA(), "2500");
//					else if ( testVar.getId() == 74373 ) 
//						variableValueMap.put(testVar.getDNA(), "$1000.99");
//					else if ( testVar.getId() == 6923 ) 
//						variableValueMap.put(testVar.getDNA(), "new york");
//					else if ( testVar.getId() == 400855 ) 
//						variableValueMap.put(testVar.getDNA(), "plan A#@#plan B#@#plan A#@##@#plan C");
//					else if ( testVar.getId() == 74374 ) 
//						variableValueMap.put(testVar.getDNA(), "2012/02/05");
//					else
//						variableValueMap.put(testVar.getDNA(), "ABC_"+testVar.getId());
//				}
				// END FOR TESTING
				
				List<DataElementVariable> variables = DataElementVariable.findByDNA(dnaList);
				for (DataElementVariable currentVariable: variables ) {
					JSONObject variableValueObj = new JSONObject();
					if ( variableValueMap.containsKey(currentVariable.getDna()) ) {
						variableValueObj.put( "id", String.valueOf(currentVariable.getId()) );
						variableValueObj.put( "dna", currentVariable.getDna() );
						variableValueObj.put( "value", variableValueMap.get(currentVariable.getDna()) );
						variableValueObj.put( "sub_type_id", currentVariable.getDefaultDataElement() != null ? currentVariable.getDefaultDataElement().getDataSubtypeId() :-1);
						variableValuesArray.put(variableValueObj);
					}
				}
				
				debugObj.put("ws_build_variable_values", (new Date().getTime() - buildVarValuesSartTime) );
				
				varDataObj.put("debug_timing", debugObj);
			
			} else {
				
				varDataObj.put("debug_timing", "NOT_APPLICABLE");
				
			}

			varDataObj.put("variable_values", variableValuesArray);
			
			JSONObject defaultsObj = new JSONObject();
			varDataObj.put("defaults", AsyncVariablesController.getDefaultVariableInfoJSON(defaultsObj, communication.getLocale()));
			

			if (debugMode)
				log.error("Connected: Variable Data: End " + (new Date().getTime() - startTime) ); 
		
		} catch (JSONException e) {
			log.error("Error: Unable to build pre-proof connected data response: ", e );
			
			try {
				varDataObj.put("error", true);
				varDataObj.put("message", "Caught JSONException: Error - Unable to build connected variables data response");
				varDataObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}
		
		return varDataObj.toString();
		
	}

	private String getEditorDataResponseJSON( HttpServletRequest request ) {
		
		long startTime = 0;
		if (debugMode)
			startTime = new Date().getTime();
		
		JSONObject zoneInitDataObj = new JSONObject();

		try {
			
			Long zoneId 				= ServletRequestUtils.getLongParameter(request, PARAM_ZONE_ID, -1);
			long preProofId 			= ServletRequestUtils.getLongParameter(request, PARAM_PREPROOF_ID, -1);
			long communicationId		= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
			
			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			Communication communication = null;
			if ( preProof != null )
				communication = preProof.getCommunication();
			else
				communication = Communication.findById(communicationId);

			Document targetDoc 			= getTargetDocument(preProof,communication);
			
			Zone zone 					= Zone.findById( zoneId );
			if ( targetDoc.isAlternate() )
				zone = targetDoc.findZoneByParent(zone);

			if (debugMode)
				log.error("Connected: Editor Data: Start " + (new Date().getTime() - startTime) ); 
			
			// ZONE ID: Identified by master document zone ids
			if ( zone.isAlternate() )
				zoneId = zone.getParent().getId();

			if ( zone.getContentTypeId() == ContentType.TEXT ) {
				// TEXT ZONE PROPERTIES
				
				zoneInitDataObj.put("canvas_dimensions"	, Communication.getCanvasDimensions(zone));
				
				// STYLE CSS
				String contentCSSpaths = communication.getDefaultEditorCSSFilePath(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp();
				if ( !communication.getAllStyles(targetDoc).isEmpty() )
					contentCSSpaths += "," + communication.getCSSFilename(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp();
				zoneInitDataObj.put("content_css"			, contentCSSpaths);
				
				JSONArray textStyleData = communication.getTextStyleData(zone);
				zoneInitDataObj.put("text_data", textStyleData);
				zoneInitDataObj.put("applies_text_styles"	, !textStyleData.isEmpty());
				
				// PARAGRAPH STYLES
				JSONArray paragraphStyleData = communication.getParagraphStyleData(zone);
				zoneInitDataObj.put("paragraph_style_data", paragraphStyleData);
				zoneInitDataObj.put("applies_paragraph_styles", !paragraphStyleData.isEmpty());
				String paragraphCSSpath = "";
				if ( !communication.getAllParagraphStyles(targetDoc).isEmpty() )
					paragraphCSSpath += communication.getParagraphCSSFilename(targetDoc,true) + "?cacheStamp=" + DateUtil.timeStamp();
				zoneInitDataObj.put("paragraph_style_css"	, paragraphCSSpath);
				
				// LIST STYLES
				JSONArray listStyleData = communication.getListStyleData(zone);
				zoneInitDataObj.put("list_style_data", listStyleData);
				zoneInitDataObj.put("applies_list_styles", !listStyleData.isEmpty());
				String listCSSpath = "";
				if ( !communication.getAllListStyles(targetDoc).isEmpty() )
					listCSSpath += communication.getListCSSFilename(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp();
				zoneInitDataObj.put("list_style_css"	, listCSSpath);

				zoneInitDataObj.put("apply_reset"		, zone.getDefaultCommunicationTemplateSmartText() != null);

				// TABLES
				zoneInitDataObj.put("applies_tables", zone.getAreTablesEnabled());
				
			} else {
				zoneInitDataObj.put("content_css"				, "");
				zoneInitDataObj.put("text_data"					, new JSONArray());
				zoneInitDataObj.put("applies_text_styles"		, false);
				zoneInitDataObj.put("paragraph_style_data"		, new JSONArray());
				zoneInitDataObj.put("applies_paragraph_styles"	, false);
				zoneInitDataObj.put("paragraph_style_css"		, "");
				zoneInitDataObj.put("list_style_data"			, new JSONArray());
				zoneInitDataObj.put("applies_list_styles"		, false);
				zoneInitDataObj.put("list_style_css"			, "");
				zoneInitDataObj.put("applies_tables"			, false);
				zoneInitDataObj.put("apply_reset"				, zone.getDefaultCommunicationTemplateImage() != null);
			}

			zoneInitDataObj.put("context_lang"			, communication.getLocale() != null ?
															communication.getLocale().getLanguageCode() : UserUtil.getCurrentTouchpointContext().getDefaultTouchpointLanguageCode() );
			zoneInitDataObj.put("zone_id"				, zoneId);
			
			zoneInitDataObj.put("channel"				, zone.getChannel());
			zoneInitDataObj.put("connector"				, zone.getConnector());
			
			zoneInitDataObj.put("can_edit_source"		, UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));

			if (debugMode)
				log.error("Connected: Editor Data: End " + (new Date().getTime() - startTime) ); 
		
		} catch (JSONException e) {
			log.error("Error: Unable to build pre-proof connected data response: ", e );
			
			try {
				zoneInitDataObj.put("error", true);
				zoneInitDataObj.put("message", "Caught JSONException: Error - Unable to build pre-proof connected data response");
				zoneInitDataObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}
		
		return zoneInitDataObj.toString();
		
	}

	private String getGeneralDataResponseJSON(HttpServletRequest request ) {

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.InteractivePreProofData);
		analyticsEvent.setAction(Actions.AsyncData);

		JSONObject returnObj = new JSONObject();
		JSONObject debugObj = new JSONObject();

		long startTime = new Date().getTime();

		long preProofId 		= ServletRequestUtils.getLongParameter(request, PARAM_PREPROOF_ID, -1);
		long communicationId 	= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);

		CommunicationProof preProof = CommunicationProof.findById(preProofId);
		CommunicationProof proof 	= preProof != null && preProof.getCommunication() != null && preProof.getCommunication().getLatestProof() != null ?
				preProof.getCommunication().getLatestProof() : null;
		Long proofId = (proof != null && proof.getDeliveryEvent() != null && proof.getDeliveryEvent().getJob() != null) ? proof.getDeliveryEvent().getJob().getId() : -1;

		Communication communication = null;
		if ( communicationId > 0 )
			communication = Communication.findById(communicationId);
		else
			communication = preProof.getCommunication();

		boolean isDebugOrder = false;
		if ( communication != null )
			isDebugOrder = communication.isDebugOrder();

		try {

			long genEditorTime = new Date().getTime();
			returnObj.put("general_editor_data", getGeneralEditorData(preProof, communication));
			debugObj.put("general_editor_data", (Float.valueOf((new Date().getTime()) - genEditorTime)/1000f)+"s");
			analyticsEvent.add(ConnectedEvents.Properties.GeneralEditorDataLoad, () -> (Float.valueOf((new Date().getTime()) - genEditorTime)/1000f)+"s");

			long refDebugTime = new Date().getTime();
			returnObj.put("reference_data", isDebugOrder ?
					new JSONObject(communication.getDebugReferenceData() != null ? communication.getDebugReferenceData() : "{}") :
					new JSONObject(preProof.getPreProofReferenceData() != null ? preProof.getPreProofReferenceData() : "{}"));
			debugObj.put("reference_data", (Float.valueOf((new Date().getTime()) - refDebugTime)/1000f)+"s");
			analyticsEvent.add(ConnectedEvents.Properties.ReferenceDataLoad, () -> (Float.valueOf((new Date().getTime()) - refDebugTime)/1000f)+"s");

			TouchpointSelection appliedSelection = getTargetSelection(preProof, communication);
			String variantName = appliedSelection != null ? appliedSelection.getName() : "NULL";
			String alternateName = appliedSelection != null && appliedSelection.getAlternateLayout() != null ? appliedSelection.getAlternateLayout().getName() : "DEFAULT";

			returnObj.put("variant_name", variantName);
			returnObj.put("alternate_name", alternateName);

			returnObj.put("debug_timing", debugObj);

		} catch (JSONException e) {
			log.error("Error: Unable to build general data connected data response: ", e );

			try {
				returnObj.put("error", true);
				returnObj.put("message", "Caught JSONException: Error - web service request failed");
				returnObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}

		} catch (Exception e) {
			log.error("Error: General interactive data request failed: ", e );

			try {
				returnObj.put("error", true);
				returnObj.put("message", "Caught Exception: General interactive data request failed");
				returnObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}

		if (debugMode)
			log.error("Connected: Interactive Data: Return " + (new Date().getTime() - startTime) );

		analyticsEvent.send();

		return returnObj.toString();
	}
	
	private String getTouchpointConversionResponseJSON(HttpServletRequest request ) {

		long startTime = new Date().getTime();

		JSONObject returnObj = new JSONObject();
		JSONObject debugObj = new JSONObject();

		System.setProperty("sun.java2d.cmm", "sun.java2d.cmm.kcms.KcmsServiceProvider");

		try {

			debugObj.put("server_side_conversion_start_time", startTime);

			AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.InteractivePreProofConversion);
			analyticsEvent.setAction(Actions.AsyncData);

			Long preProofId 		= ServletRequestUtils.getLongParameter(request, PARAM_PREPROOF_ID, -1);
			long communicationId 	= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
			
			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			CommunicationProof proof 	= preProof != null && preProof.getCommunication() != null && preProof.getCommunication().getLatestProof() != null ? 
											preProof.getCommunication().getLatestProof() : null;
			Long proofId = (proof != null && proof.getDeliveryEvent() != null && proof.getDeliveryEvent().getJob() != null) ? proof.getDeliveryEvent().getJob().getId() : -1;
			
			Communication communication = null;
			if ( communicationId > 0 )
				communication = Communication.findById(communicationId);
			else
				communication = preProof.getCommunication();
			
			boolean isDebugOrder = false;
			if ( communication != null )
				isDebugOrder = communication.isDebugOrder();

			// DEBUG DATA
			returnObj.put("preproof_job_id", (preProof != null ? preProof.getDeliveryEvent().getJob().getId() : -999) );
			returnObj.put("proof_job_id", proofId);
			returnObj.put("pre_proof_id", preProofId);

			boolean isDigitalPreview = preProof != null ? preProof.getIsEmailOrWebPreview() : communication.isDigitalOrder();

			if ( isDigitalPreview ) {

				returnObj.put("is_digital", true);
				returnObj.put("page_count", 1);

				if ( isDebugOrder ) {
					
					Document contextDocument = communication.getDocument();
					if ( contextDocument.getIsOmniChannel() )
						contextDocument = communication.getDocument().getChannelAlternateByType(communication.getChannelContextId()).get(0);
				    String currentTemplate = EmailTemplateUtils.getTemplateDebug( contextDocument );
				    returnObj.put("display_html", currentTemplate);

				} else if ( preProof != null ) {

					analyticsEvent.add(ConnectedEvents.Properties.JobId, () -> preProof.getDeliveryEvent().getJob().getId());
					
					if ( communication.getDocument().isCommunicationWebServiceCompositionResultsEnabled() ) {
						
							try {

								long getWSDigitalOuptutTime = new Date().getTime();
								SandboxFile sandboxFile = CommunicationWSClient.getPdfFile(communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(), 
										communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(), 
										communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
										preProof.getDeliveryEvent());
								debugObj.put("ws_get_digital_output", (Float.valueOf((new Date().getTime()) - getWSDigitalOuptutTime)/1000f)+"s");
								analyticsEvent.add(ConnectedEvents.Properties.WS_GetDigitalOutput, () -> (Float.valueOf((new Date().getTime()) - getWSDigitalOuptutTime)/1000f)+"s");

								String dynamicContentPath = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir) + "/"
										+ preProof.getDeliveryEvent().getJob().getId() + "/1/";

								if (sandboxFile != null && sandboxFile.getFileContent() != null) {
									byte[]  bProofHTML = sandboxFile.getFileContent();

									long postProcessDigitalOutputTime = new Date().getTime();
									String previewHTML = EmailTemplateUtils.postProcessEmailPreviewContent(
											new String(bProofHTML, "UTF-8"), 
											dynamicContentPath, 
											communication.getDocument().getId(), 
											communication.getLocale().getLanguageCode());
									debugObj.put("post_process_digital_output", (Float.valueOf((new Date().getTime()) - postProcessDigitalOutputTime)/1000f)+"s");
									analyticsEvent.add(ConnectedEvents.Properties.PostProcessDigitalOutput, () -> (Float.valueOf((new Date().getTime()) - postProcessDigitalOutputTime)/1000f)+"s");
									
									returnObj.put("display_html", previewHTML);
								} else {
									returnObj.put("error", true);
									returnObj.put("message", "Digital pre-proof could not be retrieved through WS");
								}
							
							} catch (SoapFaultClientException e) {
								
								try {
									returnObj.put("error", true);
									returnObj.put("message", "Caught SoapFaultClientException: Error - unable to retrieve pre-proof through WS ");
									returnObj.put("stack", e.getMessage());
								} catch (JSONException e1) {
									// NULL
								}
								
							}

						} else {
					
							File file = new File(preProof.getOutputPath());
			
							boolean isDirectory = file.isDirectory();
							boolean canReadFile = file.canRead();
							boolean isFileAccessAllowed = isFileAccessAllowed(file);
							
							if (isDirectory || !canReadFile || !isFileAccessAllowed) {
								returnObj.put("error", true);
								returnObj.put("message", "Digital pre-proof could not be retrieved through fileroot");
							} else {
								String proofHTML = FileUtil.getFileAsString(preProof.getOutputPath());
								returnObj.put("display_html", proofHTML);
							}
							
						}

				}

				JSONArray pageObjArray = new JSONArray();

				long zoneDataTime = new Date().getTime();
				Document targetDoc = getTargetDocument(preProof,communication);
				DocumentSection targetSection = targetDoc.getDocumentSections().iterator().next();
				List<Zone> zones = Zone.findCommunicationZonesByDocument(targetDoc.getId());

				JSONObject pageObj = new JSONObject();
				
				pageObj.put("x"			, -1);
				pageObj.put("y"			, -1);
				pageObj.put("width"		, targetSection.getWidth());
				pageObj.put("height"	, targetSection.getHeight());
				pageObj.put("rotation"	, -1);
				pageObj.put("order"		, 1);

				JSONArray zonesArray = new JSONArray();
				for ( Zone currentZone: zones ) {
					JSONObject zoneObj = getZoneJSON(currentZone);

					zoneObj.put("x1"			, -1);
					zoneObj.put("y1"			, -1);
					zoneObj.put("x2"			, Float.valueOf(currentZone.getCanvasDimensions().split(":")[0]));
					zoneObj.put("y2"			, Float.valueOf(currentZone.getCanvasDimensions().split(":")[1]));

					zonesArray.put(zoneObj);
				}
				pageObj.put("zones", zonesArray);
				
				pageObjArray.put(pageObj);

				returnObj.put("pages", pageObjArray);

				debugObj.put("zone_data", (Float.valueOf((new Date().getTime()) - zoneDataTime)/1000f)+"s");
				analyticsEvent.add(ConnectedEvents.Properties.ZoneDataLoad, () -> (Float.valueOf((new Date().getTime()) - zoneDataTime)/1000f)+"s");

			} else {
				
				returnObj.put("is_print", true);
			
				ConvertPagesToImages convertPagesToImages = null;
				FontMappings.setFontReplacements();
				FontMappings.addFontFile("ZapfDingbats.ttf", ApplicationUtil.getRootPath() + "includes/fonts/pdf-embedded/");

				PDFUtilities pdfUtils = null;
				FindTextInRectangle extract = null;
				long PDFmanipulationTime = new Date().getTime();
				float PDFimageTransformTime = 0;
				if ( !isDebugOrder ) {
					if ( communication.getDocument().isCommunicationWebServiceCompositionResultsEnabled() ) {
						
						try {

							long getWSPrintOuptutTime = new Date().getTime();
							SandboxFile sandboxFile = CommunicationWSClient.getPdfFile(communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(), 
									communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(), 
									communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
									preProof.getDeliveryEvent());
							debugObj.put("ws_get_PDF_output", (Float.valueOf((new Date().getTime()) - getWSPrintOuptutTime)/1000f)+"s");
							analyticsEvent.add(ConnectedEvents.Properties.WS_GetPDFOutput, () -> (Float.valueOf((new Date().getTime()) - getWSPrintOuptutTime)/1000f)+"s");
							
							/**
							CustomerAPICaller.getPdfFile(preProof.getDocument().getCommunicationCompositionResultsWebService().getUrl(), 
									preProof.getDocument().getCommunicationCompositionResultsWebService().getUsername(), 
									preProof.getDocument().getCommunicationCompositionResultsWebService().getPassword(), 
									preProof.getCommunication(), 
									preProof.getPreProofCustomerIdentifier(),
									CustomerAPICaller.STATE_PRE_PROOF);
									**/
			
							if (sandboxFile != null)
							{
								PDFmanipulationTime = new Date().getTime();
								pdfUtils = new PDFUtilities(sandboxFile.getFileContent());
								extract = new FindTextInRectangle(sandboxFile.getFileContent());
								convertPagesToImages = new ConvertPagesToImages(sandboxFile.getFileContent());
							}
						
						} catch (SoapFaultClientException e) {
							
							try {
								returnObj.put("error", true);
								returnObj.put("message", "Caught SoapFaultClientException: Error - unable to retrieve pre-proof through WS ");
								returnObj.put("stack", e.getMessage());
							} catch (JSONException e1) {
								// NULL
							}
							
						}
		
					} else {

						PDFmanipulationTime = new Date().getTime();
						pdfUtils = new PDFUtilities(preProof.getOutputPath());
						extract = new FindTextInRectangle(preProof.getOutputPath());
						convertPagesToImages = new ConvertPagesToImages(preProof.getOutputPath());

					}
				}
	
				if ( isDebugOrder || (pdfUtils.openPDFFile() && extract.openPDFFile() && convertPagesToImages.openPDFFile()) ) {

					Document targetDoc = getTargetDocument(preProof,communication);
					
					int pageCount = isDebugOrder ? targetDoc.getDocumentSections().size() : pdfUtils.getPageCount();
					returnObj.put("page_count", pageCount);
					JSONArray pageObjArray = new JSONArray();
					
					
					List<Zone> zones = Zone.findCommunicationZonesByDocument(targetDoc.getId());
					Set<Zone> matchedZones = new HashSet<>();
					JSONArray unmatchedZonesArray = new JSONArray();
					for ( int i=1; i < pageCount+1; i++ ) {
						JSONObject pageObj = new JSONObject();
						
						float[] pageDimensions = new float[5];
						if ( isDebugOrder ) {
							pageDimensions[0] = 0f;
							pageDimensions[1] = 0f;
							pageDimensions[2] = Float.valueOf(targetDoc.getDocumentSectionsByOrder().get(i-1).getWidthInInches());
							pageDimensions[3] = Float.valueOf(targetDoc.getDocumentSectionsByOrder().get(i-1).getHeightInInches());
							pageDimensions[4] = 0f;
						} else {
							pageDimensions = pdfUtils.getPageDimensions(i, PageUnits.Inches, PageSizeType.CropBox);	
						}
						
						pageObj.put("x"			, pageDimensions[0]);
						pageObj.put("y"			, pageDimensions[1]);
						pageObj.put("width"		, pageDimensions[2]);
						pageObj.put("height"	, pageDimensions[3]);
						pageObj.put("rotation"	, pageDimensions[4]);
						pageObj.put("order"		, i);

						if ( isDebugOrder ) {
							pageObj.put("background_image", targetDoc.getDocumentSectionsByOrder().get(i-1).getImageLocation() != null ?
									ApplicationUtil.getWebRoot() + "download/image.form?resource=" + targetDoc.getDocumentSectionsByOrder().get(i-1).getImageFileResourceToken() :
									HttpRequestUtil.getFileResourceToken("../includes/themes/commonimages/dot.gif"));
						} else {

							long currentTransformTime = new Date().getTime();
					    	BufferedImage img = convertPagesToImages.getPageAsImage(i);

							JpegEncoder jpgEncoder = new JpegEncoder();
							jpgEncoder.setQuality( targetDoc != null && targetDoc.getCommunicationPDFConversionQuality() != null ? targetDoc.getCommunicationPDFConversionQuality() : 70);
							ByteArrayOutputStream baos = new ByteArrayOutputStream();
							jpgEncoder.write(img, baos);

					    	String pdfBase64String =
					    			org.apache.commons.codec.binary.StringUtils.newStringUtf8(org.apache.
					    			commons.codec.binary.Base64.encodeBase64(baos.toByteArray()));
					    	pageObj.put("background_image", pdfBase64String);

							PDFimageTransformTime += Float.valueOf((new Date().getTime()) - currentTransformTime);
						}

						JSONArray zonesArray = new JSONArray();
						for ( Zone currentZone: zones ) {
							String zoneIndicator = null;
							
							long zoneId = currentZone.getId();
							if ( currentZone.isAlternate() )
								zoneId = currentZone.getParent().getId();
							
							try {
								float[] coords = new float[5];
								if ( !isDebugOrder ) {
									if ( currentZone.getDocument().isNativeCompositionTouchpoint() )
										zoneIndicator = "##Z" + zoneId + "##";
									else
										zoneIndicator = "##" + currentZone.getName().replace("MP_ZON_","Z") + "##";
									coords = extract.findTextOnPage(i, zoneIndicator, SearchType.FIND_FIRST_OCCURANCE_ONLY ) ;
								}

								boolean isParsedFromPage = !isDebugOrder && (coords.length > 0 || !currentZone.isEnabled()) && !matchedZones.contains(currentZone);
								boolean isDebugZoneFromPage = isDebugOrder && (targetDoc.getDocumentSectionsByOrder().get(i-1).getZones().contains(currentZone) || currentZone.isEnabled()) && !matchedZones.contains(currentZone);
								if ( isParsedFromPage || isDebugZoneFromPage ) {
									JSONObject zoneObj = getZoneJSON(currentZone);
									
									zoneObj.put("zone_id"		, zoneId);
									if ( !isDebugOrder ) {
										zoneObj.put("x1"			, coords.length > 0 ? coords[0]/72f : -1);
										zoneObj.put("y1"			, coords.length > 0 ? pageDimensions[3] - coords[1]/72f : -1);
										zoneObj.put("x2"			, coords.length > 0 ? coords[2]/72f : -1);
										zoneObj.put("y2"			, coords.length > 0 ? pageDimensions[3] - coords[3]/72f : -1);
									} else {
										zoneObj.put("x1"			, currentZone.getTopXInInches());
										zoneObj.put("y1"			, currentZone.getTopYInInches());
										zoneObj.put("x2"			, currentZone.getTopXInInches() + currentZone.getWidthInInches());
										zoneObj.put("y2"			, currentZone.getTopYInInches() + currentZone.getHeightInInches());
									}
									
									zonesArray.put(zoneObj);
									matchedZones.add(currentZone);
								}

							} catch (Exception e) {
								
							}
						}
						pageObj.put("zones", zonesArray);
						
						pageObjArray.put(pageObj);
					}
					returnObj.put("pages", pageObjArray);

					if ( convertPagesToImages != null )
						convertPagesToImages.closePDFfile();

					debugObj.put("pdf_manipulation_total_and_transform", (Float.valueOf((new Date().getTime()) - PDFmanipulationTime)/1000f)+"s" + ":" + PDFimageTransformTime/1000f + "s");
					long finalPDFmanipulationTime = PDFmanipulationTime;
					analyticsEvent.add(ConnectedEvents.Properties.PDF_ManipulationTotal, () -> (Float.valueOf((new Date().getTime()) - finalPDFmanipulationTime)/1000f)+"s");
					float finalPDFimageTransformTime = PDFimageTransformTime;
					analyticsEvent.add(ConnectedEvents.Properties.PDF_ManipulationSub_Transform, () -> finalPDFimageTransformTime /1000f + "s");
					
					for ( Zone currentZone: zones ) 
						if ( !matchedZones.contains(currentZone) ) {
							JSONObject zoneObj = getZoneJSON(currentZone);
							
							zoneObj.put("x1"			, -1);
							zoneObj.put("y1"			, -1);
							zoneObj.put("x2"			, -1);
							zoneObj.put("y2"			, -1);
							
							unmatchedZonesArray.put(zoneObj);
						}
					returnObj.put("unmatched_zones", unmatchedZonesArray);
				}

				debugObj.put("all_pdf_transform_data", (Float.valueOf((new Date().getTime()) - startTime)/1000f)+"s");
				analyticsEvent.add(ConnectedEvents.Properties.AllData, () -> (Float.valueOf((new Date().getTime()) - startTime)/1000f)+"s");

				if ( extract != null )
					extract.closePDFfile();
				if ( pdfUtils != null )
					pdfUtils.closePDFfile();
			
			}

			long analyticsStartTime = new Date().getTime();

			// AUDIT: Update debug info
			CommunicationsUtil.addPdfDebugInfoToAudit(communication, debugObj, preProof);
			List<Communication> commList = new ArrayList<>();
			commList.add(communication);
			ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForAuditDataUpdate(commList);
			Service externalValidationService = MessagepointServiceFactory.getInstance()
					.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
			externalValidationService.execute(context);
			if (!context.getResponse().isSuccessful()) {
				log.error("Error: Unable set persist audit debug data");
			}

			debugObj.put("umh_processing", CommunicationsUtil.getUMHProcessingData(preProof));
			debugObj.put("dews", CommunicationsUtil.getDEWSProcessingData(preProof));
			debugObj.put("umh_finalizing", CommunicationsUtil.getUMHFinalizingData(preProof));

			debugObj.put("conversion_analytics_time", (Float.valueOf((new Date().getTime()) - analyticsStartTime)/1000f)+"s");

			debugObj.put("server_side_conversion_total_time", (Float.valueOf((new Date().getTime()) - startTime)/1000f)+"s");

			analyticsEvent.send();

			returnObj.put("debug_timing", debugObj);
			
		} catch (JSONException e) {
			log.error("Error: Unable to build pre-proof connected data response: ", e );
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Caught JSONException: Error - web service request failed");
				returnObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
			
		} catch (Exception e) {
			log.error("Error: Aux data web service request failed: ", e );
			
			try {
				returnObj.put("error", true);
				returnObj.put("message", "Caught Exception: Error - web service request failed");
				returnObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}
		
		if (debugMode)
			log.error("Connected: Interactive Data: Return " + (new Date().getTime() - startTime) );

		return returnObj.toString();
	}

	private String getAuditLoadCompleteResponseJSON( HttpServletRequest request ) {

		JSONObject returnDataObj = new JSONObject();

		AnalyticsEvent<ConnectedEvents> endToEndEvent = AnalyticsUtil.requestFor(ConnectedEvents.ConnectedRoundTripData);
		endToEndEvent.setAction(Actions.TimeTracking);

		try {

			long preProofId 			= ServletRequestUtils.getLongParameter(request, PARAM_PREPROOF_ID, -1);
			long communicationId		= ServletRequestUtils.getLongParameter(request, PARAM_COMMUNICATION_ID, -1);
			String debugDataStr			= ServletRequestUtils.getStringParameter(request, PARAM_DEBUG_DATA, null);

			JSONObject debugDataObj = new JSONObject();
			if ( debugDataStr != null ) {
				String[] debugDataArray = debugDataStr.split(":");
				for ( int i = 0 ; i < debugDataArray.length; i++ )
					debugDataObj.put(debugDataArray[i].split(",")[0],debugDataArray[i].split(",")[1]);
			}

			CommunicationProof preProof = CommunicationProof.findById(preProofId);
			Communication communication = null;
			if ( preProof != null )
				communication = preProof.getCommunication();
			else
				communication = Communication.findById(communicationId);

			String jobId = preProof != null && preProof.getDeliveryEvent() != null && preProof.getDeliveryEvent().getJob() != null ? String.valueOf(preProof.getDeliveryEvent().getJob().getId()) : "DEBUG";
			debugDataObj.put("job_id", jobId);

			// AUDIT: Round trip complete
			Float endToEndTime = CommunicationsUtil.addRoundTripCompleteToAudit(communication, debugDataObj);
			List<Communication> commList = new ArrayList<>();
			commList.add(communication);
			ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForAuditDataUpdate(commList);
			Service externalValidationService = MessagepointServiceFactory.getInstance()
					.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
			externalValidationService.execute(context);
			if (!context.getResponse().isSuccessful()) {
				log.error("Error: Unable set persist round trip complete");
			}

			JSONObject latestRoundTripData = CommunicationsUtil.getLatestRoundTripData(communication);
			if ( endToEndTime > 0 ) {
				endToEndEvent.add(ConnectedEvents.Properties.EndToEndComplete, () -> (endToEndTime +"s"));
				endToEndEvent.send();

				JSONObject jsonData = new JSONObject();
				jsonData.put("json_data", latestRoundTripData);
				AuditEventUtil.push(AuditEventType.ID_CONNECTED, AuditObjectType.ID_CONNECTED,
						communication.getCustomerIdentifierDisplayValue() + " (" + endToEndTime + "s) [Job Id:" + jobId + "]", communication.getId(),
						AuditActionType.ID_ORDER_ROUND_TRIP, jsonData.toString());
			}

			returnDataObj.put("end_to_end_time", endToEndTime);
			returnDataObj.put("all_audit_data", latestRoundTripData);

		} catch (JSONException e) {
			log.error("Error: Unable to mark round trip complete: ", e );

			try {
				returnDataObj.put("error", true);
				returnDataObj.put("message", "Caught JSONException: Error - Unable to mark round trip complete");
				returnDataObj.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}

		return returnDataObj.toString();

	}
	
	public static Document getTargetDocument(CommunicationProof preProof, Communication communication) {
		
		long targetDocId = -1L;
		if (preProof != null)
			targetDocId = preProof.getDocumentId();
		else
			targetDocId = communication.getDocument().getId();
		
		JSONObject refData;
		
		try {
			refData = communication != null && communication.isDebugOrder() ?
					new JSONObject( communication.getDebugReferenceData() != null ? communication.getDebugReferenceData() : "{}" ) :
					new JSONObject( preProof.getPreProofReferenceData() != null ? preProof.getPreProofReferenceData() : "{}" );

			if ( refData.has("documents") ) {
				JSONObject docObject = (JSONObject)refData.get("documents");
				long selectionId = -1L;
				if ( docObject.has( String.valueOf(communication.getDocument().getId()) )   && !docObject.getString(String.valueOf(communication.getDocument().getId())).isEmpty())
					selectionId = docObject.getLong( String.valueOf(communication.getDocument().getId()) );
				
				if ( selectionId > 0 ) {
					TouchpointSelection selection = TouchpointSelection.findById(selectionId);
					if ( selection != null && selection.getAlternateLayout() != null )
						targetDocId = selection.getAlternateLayout().getId();
				}
			} else {
				// Master variant selected: Resolve alternate if applied
				Document masterDoc = Document.findById(targetDocId);
				if ( !masterDoc.isAlternate() && masterDoc.getMasterTouchpointSelection() != null && masterDoc.getMasterTouchpointSelection().getAlternateLayout() != null )
					targetDocId = masterDoc.getMasterTouchpointSelection().getAlternateLayout().getId();
			}
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		Document targetDoc = Document.findById(targetDocId);

		return targetDoc;
	}
	
	public static TouchpointSelection getTargetSelection(CommunicationProof preProof, Communication communication) {
		
		TouchpointSelection selection = null;
		
		JSONObject refData;
		
		try {
			refData = communication != null && communication.isDebugOrder() ? 
					new JSONObject( communication.getDebugReferenceData() != null ? communication.getDebugReferenceData() : "{}" ) :
					new JSONObject( preProof.getPreProofReferenceData() != null ? preProof.getPreProofReferenceData() : "{}" );

			if ( refData.has("documents") ) {
				JSONObject docObject = (JSONObject)refData.get("documents");
				long selectionId = -1L;
				if ( docObject.has( String.valueOf(communication.getDocument().getId()) )   && !docObject.getString(String.valueOf(communication.getDocument().getId())).isEmpty())
					selectionId = docObject.getLong( String.valueOf(communication.getDocument().getId()) );
				if ( selectionId > 0 )
					selection = TouchpointSelection.findById(selectionId);
			} else {
				// Master variant selected
				Document masterDoc = Document.findById( communication.getDocument().getId() );
				selection = masterDoc.getMasterTouchpointSelection();
			}
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return selection;
	}
	
	private JSONObject getGeneralEditorData(CommunicationProof preProof, Communication communication) {
		
		JSONObject editorData = new JSONObject();
		
		try {
			
			long startTime = 0;
			if (debugMode)
				startTime = new Date().getTime();
			JSONObject debugData = new JSONObject();

			JSONObject refData = new JSONObject( communication != null && communication.isDebugOrder() ? 
					communication.getDebugReferenceData() != null ? communication.getDebugReferenceData() : "{}" :
					preProof.getPreProofReferenceData() != null ? preProof.getPreProofReferenceData() : "{}"	);
			Document targetDoc = getTargetDocument(preProof,communication);

			editorData.put("content_ref_data"				, refData);
			
			long zoneDataTime = new Date().getTime();
			editorData.put("zone_data"						, communication.getZoneData(targetDoc) );
			debugData.put("zone", (Float.valueOf((new Date().getTime()) - zoneDataTime)/1000f)+"s");
			
			editorData.put("applies_default_templates"		, (communication.getZoneContentAssociations() == null || communication.getZoneContentAssociations().isEmpty()) );
			
			long styleDataTime = new Date().getTime();
			editorData.put("default_content_view_css", 				communication.getDefaultViewCSSFilePath(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp());
			if ( !communication.getAllStyles(targetDoc).isEmpty() )
				editorData.put("extended_text_styles_view_css", 	communication.getCSSFilename(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp());
			if ( !communication.getAllParagraphStyles(targetDoc).isEmpty() )
				editorData.put("extended_paragraph_styles_view_css", communication.getParagraphCSSFilename(targetDoc,false) + "?cacheStamp=" + DateUtil.timeStamp());
			if ( !communication.getAllListStyles(targetDoc).isEmpty() )
				editorData.put("extended_list_styles_view_css", communication.getListCSSFilename(targetDoc) + "?cacheStamp=" + DateUtil.timeStamp());

			debugData.put("style", (Float.valueOf((new Date().getTime()) - styleDataTime)/1000f)+"s");

			editorData.put("ref_debug_timing", debugData);

		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			
			try {
				editorData.put("error", true);
				editorData.put("message", "Caught JSONException: Error - Unable to generate editor data: ");
				editorData.put("stack", e.getMessage());
			} catch (JSONException e1) {
				// NULL
			}
		}

		return editorData;
	}
	
	private boolean isFileAccessAllowed(File f) {
		String[] allowedFolders = {
			new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir)).getAbsolutePath(),
			new File(ApplicationUtil.getRootPath()).getAbsolutePath() 
		};

		String parentPath = f.getParentFile().getAbsolutePath();

		boolean allowed = false;
		for (String folder : allowedFolders) {
			allowed = allowed || parentPath.startsWith(folder);
		}

		return allowed;
	}

}