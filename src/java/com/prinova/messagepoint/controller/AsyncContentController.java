package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.ContentObjectDynamicVariantViewController;
import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.message.content.SelectableContentUtil;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.content.ContentObjectContentSelectionVO;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class AsyncContentController implements Controller {

	public static final String REQ_PARAM_TOUCHPOINT_SELECTION_ID 	= "touchpointSelectionId";
	public static final String REQ_PARAM_VARIANT_ITEM_ID 			= "variantId";
	public static final String REQ_PARAM_CONTENT_OBJECT_ID 			= "contentObjectId";
	public static final String REQ_PARAM_PART_ID 					= "partId";
	public static final String REQ_PARAM_LOCALE_ID 					= "localeId";
	public static final String REQ_PARAM_IS_LOCAL_CONTENT			= "localContent";
	public static final String REQ_PARAM_STATUS_VIEW_ID				= "statusViewId";
	public static final String REQ_PARAM_CONTEXT					= "context";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		StringBuilder returnXML = new StringBuilder("<?xml version='1.0'?>");

		long contentObjectId = getContentObjectId(request);
		ContentObject contextContentObject = ContentObject.findById( getContentObjectId(request) );
		Boolean isTestContext = getIsTestContext(request);
		if ( contextContentObject.isGlobalImage() ) {
			
			// START: Type: CONTENT LIBRARY
			long variantItemId 	= getVariantIdParam(request);
			long localeId 	= getLocaleId(request);
			
        	ContentObject contentObject = null;
        	if ( isTestContext )
				contentObject = ContentObject.findByIdWorkingDataFocusCentric(contentObjectId);
        	else
				contentObject = ContentObject.findByIdActiveDataFocusCentric(contentObjectId);

			Map<Long, ContentVO> contentVOs;
			if (variantItemId > 0) {
				ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantItemId);
				contentVOs = ContentObjectDynamicVariantViewController.getMasterContent(contentObject);
				ContentObjectContentSelectionVO clInstVO = ContentObjectContentSelectionVO.mapRegularSelectionContent(contentObject, pgtn, UserUtil.getPrincipalUserId(), contentVOs);
				contentVOs = clInstVO.getLangContentMap();
			} else {
				contentVOs = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType());
			}

			for (Long currentLocaleId: contentVOs.keySet()) {
				ContentVO currentContent = contentVOs.get(currentLocaleId);
				if ( currentContent.getLocaleId() == localeId ) {
					
					returnXML.append("<contentItems>");
					returnXML.append("<content id=\"").append(localeId).append("\" ").append("localeId=\"").append(localeId).append("\" ").append("imageDna=\"").append(contentObject.getDna()).append("\" ").append("isLocal=\"false\" ").append("name=\"").append(contentObject.getName() != null ? contentObject.getName() : "").append("\" ").append("uploadDate=\"").append(currentContent.getImageUploadedDate() != null ? DateUtil.formatDateTime(currentContent.getImageUploadedDate()) : "").append("\" ").append("appliedImageName=\"").append(currentContent.getAppliedImageFilename() != null ? currentContent.getAppliedImageFilename() : "").append("\" ").append("cmsLastUpdateDate=\"").append(currentContent.getAssetLastUpdate() != null ? currentContent.getAssetLastUpdate() : "").append("\" ").append("cmsLastSyncDate=\"").append(currentContent.getAssetLastSync() != null ? currentContent.getAssetLastSync() : "").append("\" ").append("imageLink=\"").append(currentContent.getImageLink() != null && currentContent.getImageLink().getEncodedValue() != null ? currentContent.getImageLinkforView() : "").append("\" ").append("imageAltText=\"").append(currentContent.getImageAltText() != null && currentContent.getImageAltText().getEncodedValue() != null ? currentContent.getImageAltTextforView() : "").append("\" ").append("imageExtLink=\"").append(currentContent.getImageExtLink() != null && currentContent.getImageExtLink().getEncodedValue() != null ? currentContent.getImageExtLinkforView() : "").append("\" ").append("imageExtPath=\"").append(currentContent.getImageExtPath() != null && currentContent.getImageExtPath().getEncodedValue() != null ? currentContent.getImageExtPathforView() : "").append("\" >").append("<![CDATA[").append(currentContent.getImageLocation() != null ? currentContent.getImageLocation() : "").append("]]>").append("</content>");
					returnXML.append("<tags><![CDATA[").append(ContentObjectContentUtil.getTagIcons(contentObject)).append("]]></tags>");
					returnXML.append("</contentItems>");

				}
			}
			// END: Type: CONTENT LIBRARY
			
		} else if ( contextContentObject.isLocalImage() ) {
			
			// START: Type: LOCAL CONTENT LIBRARY
			long tpSelId = getTouchpointSelectionId(request);
			long variantItemId 	= getVariantIdParam(request);
			Long localeId 	= getLocaleId(request);
			
			ContentObject contentObject = null;
			if ( getIsActiveView(request) )
				contentObject = ContentObject.findByIdActiveDataFocusCentric(contentObjectId);
			else
				contentObject = ContentObject.findByIdWorkingDataFocusCentric(contentObjectId);

			// local image doesn't deliver to a specific zone, thus it cannot be multipart. For this reason Map<locale, ContentVO> is fine to use here
            Map<Long, ContentVO> masterContent = new HashMap<>();
            if ( contentObject.isStructuredContentEnabled() ) {
    			TouchpointSelection selection = tpSelId > 0 ? TouchpointSelection.findById(tpSelId) : UserUtil.getCurrentSelectionContext(contentObject);
				masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType(), -1L, true, selection.getParameterGroupTreeNode());
    		} else if ( variantItemId > 0 ) {
				ContentObjectContentSelectionVO viewingContent;
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(variantItemId);
                masterContent = ContentObjectDynamicVariantViewController.getMasterContent(contentObject);

				if (getIsActiveView(request)) {
					contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_ACTIVE);
				}
				else {
					contentObject.setFocusOnDataTypeCheckAndAdjust(ContentObject.DATA_TYPE_WORKING);
				}

				viewingContent = ContentObjectContentSelectionVO.mapRegularSelectionContent(contentObject, pgtn, UserUtil.getPrincipalUserId(), masterContent);
				masterContent = viewingContent.getLangContentMap();
            } else {
                masterContent = ContentObjectAssociation.getMasterOrTPVariantContents(contentObject, contentObject.getFocusOnDataType());
            }

			String idPrefix = "document" + contextContentObject.getDocument().getId();

			returnXML.append("<contentItems>");
			for (Long currentLocaleId: masterContent.keySet()) {
				ContentVO currentContent = masterContent.get(currentLocaleId);
				if ( currentContent.getLocaleId() == localeId || localeId.equals(0L)) {
					returnXML.append("<content id=\"").append(currentLocaleId).append("\" ").append("groupId=\"").append(idPrefix).append("\" ").append("localeId=\"").append(currentLocaleId).append("\" ").append("imageDna=\"").append(contentObject.getDna()).append("\" ").append("isLocal=\"true\" ").append("name=\"").append(contentObject.getName() != null ? contentObject.getName() : "").append("\" ").append("uploadDate=\"").append(currentContent.getImageUploadedDate() != null ? DateUtil.formatDateTime(currentContent.getImageUploadedDate()) : "").append("\" ").append("appliedImageName=\"").append(currentContent.getAppliedImageFilename() != null ? currentContent.getAppliedImageFilename() : "").append("\" ").append("cmsLastUpdateDate=\"").append(currentContent.getAssetLastUpdate() != null ? currentContent.getAssetLastUpdate() : "").append("\" ").append("cmsLastSyncDate=\"").append(currentContent.getAssetLastSync() != null ? currentContent.getAssetLastSync() : "").append("\" ").append("imageLink=\"").append(currentContent.getImageLink() != null && currentContent.getImageLink().getEncodedValue() != null ? currentContent.getImageLinkforView() : "").append("\" ").append("imageAltText=\"").append(currentContent.getImageAltText() != null && currentContent.getImageAltText().getEncodedValue() != null ? currentContent.getImageAltTextforView() : "").append("\" ").append("imageExtLink=\"").append(currentContent.getImageExtLink() != null && currentContent.getImageExtLink().getEncodedValue() != null ? currentContent.getImageExtLinkforView() : "").append("\" ").append("imageExtPath=\"").append(currentContent.getImageExtPath() != null && currentContent.getImageExtPath().getEncodedValue() != null ? currentContent.getImageExtPathforView() : "").append("\" >").append("<![CDATA[").append(currentContent.getImageLocation() != null ? currentContent.getImageLocation() : "").append("]]>").append("</content>");
					returnXML.append("<tags><![CDATA[").append(ContentObjectContentUtil.getTagIcons(contentObject)).append("]]></tags>");
				}
			}
			returnXML.append("</contentItems>");
            // END: Type: LOCAL CONTENT LIBRARY	
			
		} else {
		
			long tpSelId = getTouchpointSelectionId(request);
			Long localeId = getLocaleId(request);
			long partId = getPartId(request);
			TouchpointSelection tpSelection = TouchpointSelection.findById(tpSelId);
			if (contextContentObject == null || tpSelection == null || localeId < 0) {
				if (tpSelection == null)
					returnXML.append("<error>Invalid Touchpoint Selection ID</error>");
				if (contextContentObject == null)
					returnXML.append("<error>Invalid Content Object ID</error>");
				if (localeId < 0)
					returnXML.append("<error>Invalid locale designation</error>");
			} else {
				// TODO for Dynamic Content Object
				int dataType = contextContentObject.getFocusOnDataType();
				if(tpSelection.getDocument().isEnabledForVariantWorkflow()){
					if(tpSelection.hasWorkingCopy()) {
						dataType = ContentObject.DATA_TYPE_WORKING;
					}else if(tpSelection.getHasActiveCopy()){
						dataType = ContentObject.DATA_TYPE_ACTIVE;
					}else{
						dataType = ContentObject.DATA_TYPE_ARCHIVED;
					}
				}

				ContentObjectAssociation ca = ContentObjectAssociation.findByContentObjectTouchpointVariantAndLanguageWithReferenceResolution(contextContentObject, tpSelection.getParameterGroupTreeNode(), null, null, dataType);
				if (ca == null) {
					returnXML.append("<error>Cannot find the Touchpoint Content Selection</error>");
				} else {

					returnXML.append("<contentItems>");

					if (contextContentObject.isMultipartType()) {
						Map<Long, Map<Long, String>> contentMap 					= SelectableContentUtil.getTPContentSelectionMPContentMap(tpSelId, contentObjectId);
						Map<Long, Map<Long, String>> graphicNameMap 				= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_NAME);
						Map<Long, Map<Long, String>> graphicUploadDateMap 			= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_UPLOAD_DATE);
						Map<Long, Map<Long, String>> graphicAppliedImageNameMap 	= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_APPLIED_IMAGE_NAME);
						Map<Long, Map<Long, String>> graphicImageLinkMap 			= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_LINK);
						Map<Long, Map<Long, String>> graphicImageAltTextMap 		= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_ALT_TEXT);
						Map<Long, Map<Long, String>> graphicImageExtLinkMap 		= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_EXT_LINK);
						Map<Long, Map<Long, String>> graphicImageExtPathMap 		= SelectableContentUtil.getTPContentSelectionMPGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_EXT_PATH);

						for (long currentPartId : contentMap.keySet())
							for (Long currentLocaleId : contentMap.get(currentPartId).keySet())
								if ((currentLocaleId.equals(localeId) && (partId == currentPartId || partId < 0)) || localeId.equals(0L)) {
									String id = "zone" + contextContentObject.getZone().getId() + "_part" + currentPartId + "_lang" + currentLocaleId;
									String content = contentMap.get(currentPartId).get(currentLocaleId);
									String name 			= graphicNameMap.get(currentPartId).get(currentLocaleId);
									String uploadDate 		= graphicUploadDateMap.get(currentPartId).get(currentLocaleId);
									String appliedImageName = graphicAppliedImageNameMap.get(currentPartId).get(currentLocaleId);
									String imageLink		= graphicImageLinkMap.get(currentPartId).get(currentLocaleId);
									String imageAltText		= graphicImageAltTextMap.get(currentPartId).get(currentLocaleId);
									String imageExtLink		= graphicImageExtLinkMap.get(currentPartId).get(currentLocaleId);
									String imageExtPath		= graphicImageExtPathMap.get(currentPartId).get(currentLocaleId);
									returnXML.append("<content id=\"").append(id).append("\" ").append("groupId=\"part").append(currentPartId).append("\" ").append("localeId=\"").append(currentLocaleId).append("\" ").append("name=\"").append(name != null ? name : "").append("\" ").append("uploadDate=\"").append(uploadDate != null ? uploadDate : "").append("\" ").append("appliedImageName=\"").append(appliedImageName != null ? appliedImageName : "").append("\" ").append("imageLink=\"").append(imageLink).append("\" ").append("imageAltText=\"").append(imageAltText).append("\" ").append("imageExtLink=\"").append(imageExtLink).append("\" ").append("imageExtPath=\"").append(imageExtPath).append("\" >").append("<![CDATA[").append(content == null ? "" : content).append("]]>").append("</content>");
								}
					} else {
						Map<Long, String> contentMap 					= SelectableContentUtil.getTPContentSelectionContentMap(tpSelId, contentObjectId, dataType);
						Map<Long, String> graphicNameMap 				= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_NAME);
						Map<Long, String> graphicUploadDateMap 			= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_UPLOAD_DATE);
						Map<Long, String> graphicAppliedImageNameMap 	= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_APPLIED_IMAGE_NAME);
						Map<Long, String> graphicImageLinkMap 			= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_LINK);
						Map<Long, String> graphicImageAltTextMap 		= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_ALT_TEXT);
						Map<Long, String> graphicImageExtLinkMap 		= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_EXT_LINK);
						Map<Long, String> graphicImageExtPathMap 		= SelectableContentUtil.getTPContentSelectionGraphicDataMap(tpSelId, contentObjectId, SelectableContentUtil.GRAPHIC_IMAGE_EXT_PATH);

						String idPrefix = null;
						if ( contextContentObject.getIsTouchpointLocal() ) {
							idPrefix = "document" + contextContentObject.getDocument().getId();
						} else {
							idPrefix = "zone" + contextContentObject.getZone().getId();
						}

						for (Long currentLocaleId : contentMap.keySet()) {
							if (currentLocaleId.equals(localeId) || localeId.equals(0L)) {
								returnXML.append("<content id=\"").append(currentLocaleId).append("\" ").append("groupId=\"").append(idPrefix).append("\" ").append("localeId=\"").append(currentLocaleId).append("\" ");
								if (contextContentObject.isGraphicContent()) {
									returnXML.append("name=\"").append(graphicNameMap.get(currentLocaleId) != null ? graphicNameMap.get(currentLocaleId) : "").append("\" ").append("uploadDate=\"").append(graphicUploadDateMap.get(currentLocaleId) != null ? graphicUploadDateMap.get(currentLocaleId) : "").append("\" ").append("appliedImageName=\"").append(graphicAppliedImageNameMap.get(currentLocaleId) != null ? graphicAppliedImageNameMap.get(currentLocaleId) : "").append("\" ").append("imageLink=\"").append(graphicImageLinkMap.get(currentLocaleId)).append("\" ").append("imageAltText=\"").append(graphicImageAltTextMap.get(currentLocaleId)).append("\" ").append("imageExtLink=\"").append(graphicImageExtLinkMap.get(currentLocaleId)).append("\" ").append("imageExtPath=\"").append(graphicImageExtPathMap.get(currentLocaleId)).append("\" ");
								}
								returnXML.append(">" + "<![CDATA[").append(contentMap.get(currentLocaleId) == null ? "" : contentMap.get(currentLocaleId)).append("]]>").append("</content>");
							}
						}
					}

					returnXML.append("</contentItems>");
				}
			}
			
		}

		out.write(returnXML.toString().getBytes());
		out.flush();

		return null;
	}

	private long getTouchpointSelectionId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_TOUCHPOINT_SELECTION_ID, -1);
	}

	private long getPartId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_PART_ID, -1);
	}
	
	private long getContentObjectId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_OBJECT_ID, -1);
	}

	private Long getLocaleId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_LOCALE_ID, -1);
	}
	
	private Long getVariantIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_VARIANT_ITEM_ID, -1L);
	}
	
	private Boolean getIsLocalContentParam( HttpServletRequest request ) {
		return ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_IS_LOCAL_CONTENT, false);
	}
	
	private Boolean getIsActiveView( HttpServletRequest request ) {
		int statusViewId = ServletRequestUtils.getIntParameter(request, REQ_PARAM_STATUS_VIEW_ID, 2);
		return statusViewId == 2;	
	}
	
	private Boolean getIsTestContext( HttpServletRequest request ) {
		String context = ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default");
		return context.equalsIgnoreCase("test");	
	}

}