package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.tasks.AsyncDataForTaskObjectSelectMenuController;
import com.prinova.messagepoint.controller.tasks.TaskEditController;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataResource;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.DataSourceAssociation;
import com.prinova.messagepoint.model.admin.VariableDataElementMap;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.LookupTableInstance;
import com.prinova.messagepoint.model.file.CompositionFileSet;
import com.prinova.messagepoint.model.font.ListStyle;
import com.prinova.messagepoint.model.font.ParagraphStyle;
import com.prinova.messagepoint.model.font.TextStyle;
import com.prinova.messagepoint.model.insert.Insert;
import com.prinova.messagepoint.model.insert.InsertSchedule;
import com.prinova.messagepoint.model.rationalizer.RationalizerDocumentContent;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.tag.Tag;
import com.prinova.messagepoint.model.task.Task;
import com.prinova.messagepoint.model.task.TaskStatus;
import com.prinova.messagepoint.model.task.TaskType;
import com.prinova.messagepoint.model.util.ContentObjectUtil;
import com.prinova.messagepoint.model.util.DataResourceUtil;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowStepType;
import com.prinova.messagepoint.tag.TagUtils;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.TouchpointSelectionUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

public class AsyncDataForSelectMenuController implements Controller {

	private static final Log log = LogUtil.getLog(AsyncDataForSelectMenuController.class);
	
	public static final String PARAM_INSERT_ID 					= "insertId";
	public static final String PARAM_INSERT_SCHED_ID 			= "insertSchedId";
	public static final String PARAM_TOUCHPOINT_SELECTION_ID 	= "touchpointSelectionId";
	public static final String PARAM_TAG						= "tagId";
	public static final String PARAM_DOCSECTION_ID				= "docSectionId";
	public static final String PARAM_CONTENT_LIBRARY_ID			= "imageLibraryId";
	public static final String PARAM_DOCUMENT_ID				= "documentId";
	public static final String PARAM_COMMUNICATION_ID			= "communicationId";
	public static final String PARAM_LOOKUP_TABLE_ID			= "lookupTableId";
	public static final String PARAM_TYPE 						= "type";
	public static final String PARAM_ZONE_ID					= "zoneId";
	public static final String PARAM_TOUCHPOINT_COLLECTION_ID		= "touchpointCollectionId";
	public static final String PARAM_DOMAIN_ID 						= "domainId";
	public static final String PARAM_INSTANCE_ID					= "instanceId";
	public static final String PARAM_TASK_ID						= "taskId";
	public static final String PARAM_WORKFLOW_ID					= "workflowId";
	public static final String PARAM_SELECTED_ITEM_ID				= "selectedItemId";
	public static final String PARAM_IS_FIXED_DEFINITION			= "isFixedDefinition";
	public static final String PARAM_IS_FIXED_DEFAULT_DEFINITION	= "isFixedOrDefaultDefinition";
	public static final String PARAM_DATA_SOURCE_ID					= "dataSourceId";
	public static final String PARAM_IS_PRIMARY						= "isPrimary";
	public static final String PARAM_DATA_ELEMENT_ID				= "dataElementId";
	public static final String PARAM_VARIABLE_ID					= "variableId";
	public static final String PARAM_RATIONALIZER_CONTENT_ID		= "rationalizerContentId";
	public static final String PARAM_SEARCH_VALUE					= "sSearch";
	public static final String PARAM_IS_NEW							= "isNew";
	
	public static final String TYPE_NEXT_STAGE_USERS 							= "nextStageUsers";
	public static final String TYPE_CURRENT_STAGE_USERS 						= "currentStageUsers";
	public static final String TYPE_INSERT_NEXT_STAGE_USERS 					= "insert_nextStageUsers";
	public static final String TYPE_INSERT_CURRENT_STAGE_USERS 					= "insert_currentStageUsers";
	public static final String TYPE_INSERT_EDIT_OBJECT_USERS 					= "insert_editObjectUsers";
	public static final String TYPE_INSERT_SCHEDULE_NEXT_STAGE_USERS 			= "insertSched_nextStageUsers";
	public static final String TYPE_INSERT_SCHEDULE_CURRENT_STAGE_USERS 		= "insertSched_currentStageUsers";
	public static final String TYPE_INSERT_SCHEDULE_EDIT_OBJECT_USERS 			= "insertSched_editObjectUsers";
	public static final String TYPE_TOUCHPOINT_SELECTION_CURRENT_STAGE_USERS 	= "touchpointSelections_currentStageUsers";
	public static final String TYPE_TOUCHPOINT_SELECTION_REJECT_TO_USERS 		= "touchpointSelections_rejectToUsers";
	public static final String TYPE_MESSAGE_REJECT_TO_USERS 					= "message_rejectToUsers";
	public static final String TYPE_MESSAGES_RELEASE_FROM_TRANS_WORKFLOWS 		= "messages_releaseFromTransWorkflows";
	public static final String TYPE_MESSAGES_APPROVE_WORKFLOWS 					= "messages_approveWorkflows";
	public static final String TYPE_MESSAGES_REJECT_WORKFLOWS 					= "messages_rejectWorkflows";
	public static final String TYPE_MESSAGES_RETRY_WORKFLOWS 					= "messages_retryWorkflows";
	public static final String TYPE_TAG_EDIT_OBJECT_USERS						= "tag_editObjectUsers";
	public static final String TYPE_ZONES_FOR_SECTION							= "zonesForSection";
	public static final String TYPE_EMBEDDED_CONTENT_CURRENT_STAGE_USERS 		= "embeddedContent_currentStageUsers";
	public static final String TYPE_EMBEDDED_CONTENT_REJECT_TO_USERS 			= "embeddedContent_rejectToUsers";
	public static final String TYPE_CONTENT_LIBRARY_CURRENT_STAGE_USERS 		= "contentLibrary_currentStageUsers";
	public static final String TYPE_CONTENT_LIBRARY_REJECT_TO_USERS 			= "contentLibrary_rejectToUsers";
	public static final String TYPE_TOUCHPOINT_COMPOSITION_FILE_SET 			= "touchpoint_compositionFileSet";
	public static final String TYPE_TP_COLLECTION_COMPOSITION_FILE_SET 			= "tp_collection_compositionFileSet";
	public static final String TYPE_COMMUNICATIONS_CURRENT_STAGE_USERS 			= "communications_currentStageUsers";
	public static final String TYPE_COMMUNICATIONS_REJECT_TO_USERS 				= "communications_rejectToUsers";
	public static final String TYPE_COMMUNICATIONS_DATA_ELEMENTS 				= "communications_dataElements";
	public static final String TYPE_COMMUNICATIONS_TEXT_TEMPLATES 				= "communication_template_text";
	public static final String TYPE_COMMUNICATIONS_IMAGE_TEMPLATES 				= "communication_template_image";
	public static final String TYPE_LOOKUP_TABLE_CURRENT_STAGE_USERS 			= "lookupTable_currentStageUsers";
	public static final String TYPE_LOOKUP_TABLE_REJECT_TO_USERS 				= "lookupTable_rejectToUsers";
	public static final String TYPE_TASK_CURRENT_STAGE_USERS 					= "task_currentStageUsers";
	public static final String TYPE_ZONES_DEFAULT_TEXTSTYLES 					= "zones_defaultTextStyles";
	public static final String TYPE_ZONES_PARASTYLES 							= "zones_paraStyles";
	public static final String TYPE_ZONES_LISTSTYLES 							= "zones_listStyles";
	public static final String TYPE_COPY_FROM_INSTANCE 							= "copyFromInstance";
	public static final String TYPE_CLONE_FROM_INSTANCE 						= "cloneFromInstance";
	public static final String TYPE_AUDITING_DOMAIN_INSTANCES					= "domain_instances";
	public static final String TYPE_AUDITING_INSTANCE_USERS						= "instance_users";
	public static final String TYPE_TEXT_STYLES									= "text_styles";
	public static final String TYPE_PARAGRAPH_STYLES							= "paragraph_styles";
	public static final String TYPE_LIST_STYLES									= "list_styles";
	public static final String TYPE_UNBRIDGED_DATASOURCES						= "unbridged_datasources";
	public static final String TYPE_DATA_ELEMENTS								= "data_elements";
	public static final String TYPE_RATIONALIZER_CURRENT_STAGE_USERS			= "rationalizer_currentStageUsers";
	public static final String TYPE_RATIONALIZER_REJECT_TO_USERS 				= "rationalizer_rejectToUsers";
	public static final String TYPE_CONTENT_PROOF_ZONES 						= "content_proof_zones";
	public static final String TYPE_CURRENT_TRANSLATORS							= "currentTranslators";
	public static final String TYPE_LINKED_TO_TASKS 							= "linkedToTasks";
	public static final String TYPE_DATA_RESOURCE								= "dataResource";
	public static final String PARAM_NUM_CAP									= "numCap";
	public static final String TYPE_VARIANTS_FOR_CONTENT_OBJECT 				= "variantsForContentObject";

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("text/xml");
		ServletOutputStream out = response.getOutputStream();

		try {
			String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE);
			out.write(getResponseXML(request, type).getBytes());
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for select menu data: "+e.getMessage(),e);
		}
		
		return null;
	}

	private String getResponseXML (HttpServletRequest request, String type) {
		String returnData = "";

		String searchValue = ServletRequestUtils.getStringParameter(request, PARAM_SEARCH_VALUE, "");
		int numCap = ServletRequestUtils.getIntParameter(request, PARAM_NUM_CAP, 0);

		if (type.equalsIgnoreCase(TYPE_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_NEXT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_MESSAGE_REJECT_TO_USERS) || type.equalsIgnoreCase(TYPE_LINKED_TO_TASKS)) {
			if (getContentObjectId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Content Object ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_MESSAGES_RELEASE_FROM_TRANS_WORKFLOWS) || type.equalsIgnoreCase(TYPE_MESSAGES_APPROVE_WORKFLOWS) || type.equalsIgnoreCase(TYPE_MESSAGES_REJECT_WORKFLOWS)
				|| type.equalsIgnoreCase(TYPE_MESSAGES_RETRY_WORKFLOWS) || type.equalsIgnoreCase(TYPE_CURRENT_TRANSLATORS)) {
			if (getContentObjectIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Content Object IDs</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_NEXT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_EDIT_OBJECT_USERS)) {
			if (getInsertSchedule(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Insert Schedule ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_INSERT_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_INSERT_NEXT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_INSERT_EDIT_OBJECT_USERS)) {
			if (getInsert(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Insert ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_TOUCHPOINT_SELECTION_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_TOUCHPOINT_SELECTION_REJECT_TO_USERS)) {
			if (getTouchpointSelectionIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Touchpoint Selection ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_TAG_EDIT_OBJECT_USERS)) {
			if (getTag(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Tag ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_ZONES_FOR_SECTION)) {
			if (getDocumentSection(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Document Section ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_REJECT_TO_USERS)) {
			if (getContentObject(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Embedded Content ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_CONTENT_LIBRARY_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_CONTENT_LIBRARY_REJECT_TO_USERS)) {
			if (getContentObject(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Image Library ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_TOUCHPOINT_COMPOSITION_FILE_SET)) {
			if (getTouchpointId(request) == -1L) {
				return "<?xml version='1.0'?><content><error>Invalid Touchpoint ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_TP_COLLECTION_COMPOSITION_FILE_SET)) {
			if (getTouchpointCollectionId(request) == -1L) {
				return "<?xml version='1.0'?><content><error>Invalid Touchpoint Collection ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_COMMUNICATIONS_CURRENT_STAGE_USERS)) {
			if (getCommunicationIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Communication ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_COMMUNICATIONS_REJECT_TO_USERS)) {
			if (getCommunicationIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Communication ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_COMMUNICATIONS_DATA_ELEMENTS)) {
			if (getTouchpointId(request) == -1L) {
				return "<?xml version='1.0'?><content><error>Invalid Document ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_LOOKUP_TABLE_CURRENT_STAGE_USERS) || type.equalsIgnoreCase(TYPE_LOOKUP_TABLE_REJECT_TO_USERS)) {
			if (getLookupTableInstance(request) == null) {
				return "<?xml version='1.0'?><content><error>Invalid Lookup Table ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_TASK_CURRENT_STAGE_USERS)) {
			if (getTaskIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Task ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_ZONES_DEFAULT_TEXTSTYLES) || type.equalsIgnoreCase(TYPE_ZONES_PARASTYLES) || type.equalsIgnoreCase(TYPE_ZONES_LISTSTYLES)) {
			if (getZoneIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Zone ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_COPY_FROM_INSTANCE)) {
			if (getNodeId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Target Instance ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_CLONE_FROM_INSTANCE)) {
			if (getNodeId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Target Instance ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_AUDITING_DOMAIN_INSTANCES)) {
			if (getDomainId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Domain ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_AUDITING_INSTANCE_USERS)) {
			if (getInstanceId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Instance ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_UNBRIDGED_DATASOURCES)) {
//			if (getVariableId(request) == -9){ return "<?xml version='1.0'?><content><error>Invalid Variable ID</error></content>"; }
		} else if (type.equalsIgnoreCase(TYPE_DATA_ELEMENTS)) {
			if (getDataSourceId(request) == -9) {
				return "<?xml version='1.0'?><content><error>Invalid Data Source ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_CURRENT_STAGE_USERS)) {
			if (getRationalizerIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Rationalizer ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_RATIONALIZER_REJECT_TO_USERS)) {
			if (getRationalizerIds(request).isEmpty()) {
				return "<?xml version='1.0'?><content><error>Invalid Rationalizer ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_CONTENT_PROOF_ZONES)) {
			if (getTouchpointId(request) == -1L) {
				return "<?xml version='1.0'?><content><error>Invalid Document ID</error></content>";
			}
		} else if (type.equalsIgnoreCase(TYPE_DATA_RESOURCE)){
			//If document is -1, it can be a touchpoint collection
		} else if (type.equalsIgnoreCase(TYPE_VARIANTS_FOR_CONTENT_OBJECT)) {
			if (getContentObjectId(request) == -1) {
				return "<?xml version='1.0'?><content><error>Invalid Content Object ID</error></content>";
			}
		} else if (!type.equalsIgnoreCase(TYPE_TEXT_STYLES) && !type.equalsIgnoreCase(TYPE_PARAGRAPH_STYLES) && !type.equalsIgnoreCase(TYPE_LIST_STYLES) &&
				!type.equalsIgnoreCase(TYPE_COMMUNICATIONS_TEXT_TEMPLATES) && !type.equalsIgnoreCase(TYPE_COMMUNICATIONS_IMAGE_TEMPLATES)) {
			return "<?xml version='1.0'?><content><error>Invalid select menu data request type</error></content>";
		}

		String messageOptions = "";
		StringBuilder userOptions = new StringBuilder();
		StringBuilder zoneOptions = new StringBuilder();
		StringBuilder textStyleOptions = new StringBuilder();
		StringBuilder paraStyleOptions = new StringBuilder();
		StringBuilder listStyleOptions = new StringBuilder();
		StringBuilder compPackOptions = new StringBuilder();
		StringBuilder nodeOptions = new StringBuilder();
		StringBuilder dataSourceOptions = new StringBuilder();
		StringBuilder dataElementOptions = new StringBuilder();
		StringBuilder smartTextOptions = new StringBuilder();
		StringBuilder imageOptions = new StringBuilder();
		StringBuilder workflowOptions = new StringBuilder();
		StringBuilder taskOptions = new StringBuilder();
		StringBuilder dataResourceOptions = new StringBuilder();
		StringBuilder variantOptions = new StringBuilder();

		List<ContentObjectData> msgInsList = null;
		List<User> userList = null;
		List<Zone> zoneList = null;
		List<CompositionFileSet> compositionFileSetList = null;
		List<TextStyle> textStyleList = null;
		List<ParagraphStyle> paraStyleList = null;
		List<ListStyle> listStyleList = null;
		List<Node> nodeList = null;
		List<AbstractDataElement> dataElementList = null;
		List<DataSource> dataSourceList = null;
		List<ContentObject> smartTextList = null;
		List<ContentObject> imageList = null;
		List<DataSourceAssociation> dataCollectionList = null;
		List<ConfigurableWorkflow> workflowList = null;
		List<Task> taskList = null;
		List<DataResource> dataResourceList = null;
		List<TaskEditController.VariantSelectOption> variantList = null;

		Document document = Document.findById(getTouchpointId(request));

		Long[] permissions = {};


		//UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_COMMUNICATIONS_VIEW)
		if (type.equalsIgnoreCase(TYPE_CURRENT_STAGE_USERS)) {
			userList = ContentObjectUtil.getAssignableUsers(getContentObjectId(request));
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_EDIT, Permission.ROLE_CONTENT_LIBRARY_EDIT,
					Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if (type.equalsIgnoreCase(TYPE_NEXT_STAGE_USERS)) {
			userList = ContentObjectUtil.getNextStepUsers(getContentObjectId(request));
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_EDIT, Permission.ROLE_CONTENT_LIBRARY_EDIT,
					Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		}else if ( type.equalsIgnoreCase(TYPE_MESSAGE_REJECT_TO_USERS)) {
			userList = ContentObjectUtil.getRejectToUsers(getContentObjectId(request));
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_EDIT, Permission.ROLE_CONTENT_LIBRARY_EDIT,
					Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_MESSAGES_RELEASE_FROM_TRANS_WORKFLOWS)) {
			List<Long> contentObjectIds = getContentObjectIds(request);
			workflowList = ContentObjectUtil.getActionToWorkflows(contentObjectIds, WorkflowStepType.ID_TRANSLATION, false, false);
		}else if ( type.equalsIgnoreCase(TYPE_MESSAGES_APPROVE_WORKFLOWS)) {
			List<Long> contentObjectIds = getContentObjectIds(request);
			workflowList = ContentObjectUtil.getActionToWorkflows(contentObjectIds, new int[]{WorkflowStepType.ID_DEFAULT, WorkflowStepType.ID_TRANSLATION_APPROVAL}, false, false);
		}else if ( type.equalsIgnoreCase(TYPE_MESSAGES_REJECT_WORKFLOWS)) {
			List<Long> contentObjectIds = getContentObjectIds(request);
			workflowList = ContentObjectUtil.getActionToWorkflows(contentObjectIds, 0, true, false);
		}else if ( type.equalsIgnoreCase(TYPE_MESSAGES_RETRY_WORKFLOWS)) {
			List<Long> contentObjectIds = getContentObjectIds(request);
			workflowList = ContentObjectUtil.getActionToWorkflows(contentObjectIds, WorkflowStepType.ID_TRANSLATION, false, true);
		}else if ( type.equalsIgnoreCase(TYPE_INSERT_CURRENT_STAGE_USERS) ) {
			userList = getInsert(request).getCurrentStateUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_EDIT, Permission.ROLE_INSERT_APPROVE, Permission.ROLE_INSERT_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_INSERT_NEXT_STAGE_USERS) ) {
			userList = getInsert(request).getNextStateUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_EDIT, Permission.ROLE_INSERT_APPROVE, Permission.ROLE_INSERT_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_INSERT_EDIT_OBJECT_USERS) ) {
			userList = getInsert(request).getEditObjectUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_EDIT, Permission.ROLE_INSERT_APPROVE, Permission.ROLE_INSERT_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_CURRENT_STAGE_USERS) ) {
			userList = getInsertSchedule(request).getCurrentStateUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_SCHEDULE_EDIT, Permission.ROLE_INSERT_SCHEDULE_APPROVE, Permission.ROLE_INSERT_SCHEDULE_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_NEXT_STAGE_USERS) ) {
			userList = getInsertSchedule(request).getNextStateUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_SCHEDULE_EDIT, Permission.ROLE_INSERT_SCHEDULE_APPROVE, Permission.ROLE_INSERT_SCHEDULE_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_INSERT_SCHEDULE_EDIT_OBJECT_USERS) ) {
			userList = getInsertSchedule(request).getEditObjectUsers();
			permissions = new Long[]{Permission.ROLE_INSERT_SCHEDULE_EDIT, Permission.ROLE_INSERT_SCHEDULE_APPROVE, Permission.ROLE_INSERT_SCHEDULE_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_TOUCHPOINT_SELECTION_CURRENT_STAGE_USERS) ) {
			userList = TouchpointSelectionUtil.getCommonCurrentStateUsers(getTouchpointSelectionIds(request));
			permissions = new Long[]{Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT, Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE};

		} else if ( type.equalsIgnoreCase(TYPE_TOUCHPOINT_SELECTION_REJECT_TO_USERS) ) {
			userList = TouchpointSelectionUtil.getRejectToUsers(getTouchpointSelectionIds(request));
			permissions = new Long[]{Permission.ROLE_TOUCHPOINT_SELECTIONS_EDIT, Permission.ROLE_TOUCHPOINT_SELECTIONS_APPROVE};

		} else if ( type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_CURRENT_STAGE_USERS) ) {
			userList = getContentObject(request).getAssignableUsers();
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_EMBEDDED_CONTENT_REJECT_TO_USERS) ) {
			userList = getContentObject(request).getRejectToUsers();
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_CONTENT_LIBRARY_CURRENT_STAGE_USERS) ) {
			userList = getContentObject(request).getAssignableUsers();
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_CONTENT_LIBRARY_REJECT_TO_USERS) ) {
			userList = getContentObject(request).getRejectToUsers();
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_LINKED_TO_TASKS) )
			taskList = Task.findAllTasks(getContentObject(request), new int[]{TaskType.ID_CHANGE, TaskType.ID_TRANSLATE}, TaskStatus.ID_TO_DO, -1);
		else if ( type.equalsIgnoreCase(TYPE_TAG_EDIT_OBJECT_USERS) ) {
			userList = getTag(request).getEditObjectUsers()
			    .stream()
					.sorted(Comparator.comparing(User::getFirstName)
							.thenComparing(User::getLastName))
					.collect(Collectors.toList());
			permissions = new Long[]{Permission.ROLE_TAG_EDIT};

		} else if ( type.equalsIgnoreCase(TYPE_ZONES_FOR_SECTION))
			zoneList = getDocumentSection(request).getZonesList();
		else if ( type.equalsIgnoreCase(TYPE_CONTENT_PROOF_ZONES))
			zoneList = document.getTextZonesList();
		else if ( type.equalsIgnoreCase(TYPE_TOUCHPOINT_COMPOSITION_FILE_SET))
			compositionFileSetList = CompositionFileSet.findByDocumentId(getTouchpointId(request));
		else if ( type.equalsIgnoreCase(TYPE_TP_COLLECTION_COMPOSITION_FILE_SET))
			compositionFileSetList = CompositionFileSet.findByTpCollectionId(getTouchpointCollectionId(request));
		else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_CURRENT_STAGE_USERS) ) {
			List<Long> communicationIds = getCommunicationIds(request);
			if(!communicationIds.isEmpty() && document == null){
				document = Communication.findById(communicationIds.get(0)).getDocument();
			}
			userList = Communication.getAssignableUsers(getCommunicationIds(request), document);
			permissions = new Long[]{Permission.ROLE_COMMUNICATIONS_EDIT, Permission.ROLE_COMMUNICATIONS_APPROVE, Permission.ROLE_COMMUNICATIONS_REASSIGN};

		}
		else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_REJECT_TO_USERS) ) {
			userList = Communication.getRejectToUsers(getCommunicationIds(request));
			permissions = new Long[]{Permission.ROLE_COMMUNICATIONS_EDIT, Permission.ROLE_COMMUNICATIONS_APPROVE, Permission.ROLE_COMMUNICATIONS_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_DATA_ELEMENTS) ) {
			dataElementList = new ArrayList<>(AbstractDataElement.findAllForConnectedReferenceByDocument(document));
		} else if ( type.equalsIgnoreCase(TYPE_LOOKUP_TABLE_CURRENT_STAGE_USERS) ) {
			userList = getLookupTableInstance(request).getAssignableUsers();
			permissions = new Long[]{Permission.ROLE_LOOKUP_TABLE_EDIT, Permission.ROLE_LOOKUP_TABLE_APPROVE, Permission.ROLE_LOOKUP_TABLE_SETUP};

		} else if ( type.equalsIgnoreCase(TYPE_TASK_CURRENT_STAGE_USERS) ) {
			userList = Task.getAssignableUsers(getTaskIds(request));
			permissions = new Long[]{Permission.ID_ROLE_TASK_EDIT, Permission.ID_ROLE_TASK_REASSIGN, Permission.ID_ROLE_TASK_SETUP};

		} else if ( type.equalsIgnoreCase(TYPE_LOOKUP_TABLE_REJECT_TO_USERS) ) {
			userList = getLookupTableInstance(request).getRejectToUsers();
			permissions = new Long[]{Permission.ROLE_LOOKUP_TABLE_EDIT, Permission.ROLE_LOOKUP_TABLE_APPROVE, Permission.ROLE_LOOKUP_TABLE_SETUP};

		} else if ( type.equalsIgnoreCase(TYPE_ZONES_DEFAULT_TEXTSTYLES))
			textStyleList = TextStyle.findAllFixedDefinition();
		else if ( type.equalsIgnoreCase(TYPE_ZONES_PARASTYLES) )
			paraStyleList = ParagraphStyle.findAll();
		else if ( type.equalsIgnoreCase(TYPE_ZONES_LISTSTYLES) )
			listStyleList = ListStyle.findAll();
		else if ( type.equalsIgnoreCase(TYPE_COPY_FROM_INSTANCE) ) {
			nodeList = Node.getCopyFromNodes(getNodeId(request), getDomainId(request));
			permissions = new Long[]{Permission.ROLE_AUDITING_EDIT, Permission.ID_ROLE_ADMIN_MASTER_ADMIN, Permission.ID_ROLE_ADMIN_MASTER_USER};

		} else if ( type.equalsIgnoreCase(TYPE_CLONE_FROM_INSTANCE) )
			dataCollectionList = DataSourceAssociation.findAll(getNodeId(request), document);
		else if ( type.equalsIgnoreCase(TYPE_AUDITING_DOMAIN_INSTANCES) ) {
			nodeList = getDomainInstances(getDomainId(request));
			permissions = new Long[]{Permission.ROLE_AUDITING_EDIT, Permission.ID_ROLE_ADMIN_MASTER_ADMIN, Permission.ID_ROLE_ADMIN_MASTER_USER};

		} else if ( type.equalsIgnoreCase(TYPE_AUDITING_INSTANCE_USERS) ) {
			userList = getInstanceUsers(getDomainId(request), getInstanceId(request));
			permissions = new Long[]{Permission.ROLE_AUDITING_EDIT, Permission.ID_ROLE_ADMIN_MASTER_ADMIN, Permission.ID_ROLE_ADMIN_MASTER_USER};

		}else if ( type.equalsIgnoreCase(TYPE_TEXT_STYLES)) {
			if ( getIsFixedDefinition(request) )
				textStyleList = TextStyle.findAllFixedDefinition();
			else if (getIsFixedOrDefaultDefinition(request))
				textStyleList = TextStyle.findAllFixedOrDefaultDefinition();
			else
				textStyleList = TextStyle.findAll();

			if (!StringUtils.isEmpty(searchValue)) {
				textStyleList = textStyleList.stream().filter(x -> x.getName().toLowerCase().contains(searchValue.toLowerCase())).collect(Collectors.toList());
			}
		}
		else if ( type.equalsIgnoreCase(TYPE_PARAGRAPH_STYLES)) {
			if ( getIsFixedDefinition(request) )
				paraStyleList = ParagraphStyle.findAllFixedDefinition();
			else
				paraStyleList = ParagraphStyle.findAll();

			if (!StringUtils.isEmpty(searchValue)) {
				paraStyleList = paraStyleList.stream().filter(x -> x.getName().toLowerCase().contains(searchValue.toLowerCase())).collect(Collectors.toList());
			}
		}
		else if ( type.equalsIgnoreCase(TYPE_LIST_STYLES)) {
			listStyleList = ListStyle.findAll();

			if (!StringUtils.isEmpty(searchValue)) {
				listStyleList = listStyleList.stream().filter(x -> x.getName().toLowerCase().contains(searchValue.toLowerCase())).collect(Collectors.toList());
			}

		}
		else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_TEXT_TEMPLATES) )
			smartTextList = ContentObject.findGlobalSmartTextsVisibleForDocument(document, true, true);
		else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_IMAGE_TEMPLATES) )
			imageList = ContentObject.findGlobalImagesVisibleForDocument(document,true, true, true);
		else if ( type.equalsIgnoreCase(TYPE_UNBRIDGED_DATASOURCES) ) {
			if (isPrimarySourceType(request)) {
				dataSourceList = DataSource.findAllPrimary();
			} else {
				dataSourceList = DataSource.findAllReferenceAndRefenceConnected();
			}
		} else if ( type.equalsIgnoreCase(TYPE_DATA_ELEMENTS) ) {
			//No source type filter
			dataElementList = AbstractDataElement.findAllByDataSource(searchValue, getDataSourceId(request), 0, numCap);
		} else if ( type.equalsIgnoreCase(TYPE_RATIONALIZER_CURRENT_STAGE_USERS) ){
			userList = RationalizerDocumentContent.getAssignableUsers(getRationalizerIds(request));
			permissions = new Long[]{Permission.ROLE_ECATALOG_EDIT, Permission.ROLE_ECATALOG_APPROVE, Permission.ROLE_ECATALOG_ADMIN};

		} else if ( type.equalsIgnoreCase(TYPE_RATIONALIZER_REJECT_TO_USERS) ){
			userList = RationalizerDocumentContent.getRejectToUsers(getRationalizerIds(request));
			permissions = new Long[]{Permission.ROLE_ECATALOG_EDIT, Permission.ROLE_ECATALOG_APPROVE, Permission.ROLE_ECATALOG_ADMIN};

		} else if ( type.equalsIgnoreCase(TYPE_CURRENT_TRANSLATORS)){
			List<Long> contentObjectIds = getContentObjectIds(request);
			long workflowId = getWorkflowId(request);
			userList = ContentObjectUtil.getAssignableTranslators(contentObjectIds, workflowId);
			permissions = new Long[]{Permission.ID_ROLE_MESSAGE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_EDIT, Permission.ROLE_CONTENT_LIBRARY_EDIT,
					Permission.ID_ROLE_MESSAGE_APPROVE_EDIT, Permission.ROLE_EMBEDDED_CONTENT_APPROVE, Permission.ROLE_CONTENT_LIBRARY_APPROVE,
					Permission.ID_ROLE_MESSAGE_REASSIGNMENT, Permission.ROLE_EMBEDDED_CONTENT_REASSIGN, Permission.ROLE_CONTENT_LIBRARY_REASSIGN};

		} else if ( type.equalsIgnoreCase(TYPE_DATA_RESOURCE)) {
			dataResourceList = DataResourceUtil.findDataResourcesForDropdown(getTouchpointCollectionId(request), getTouchpointId(request), numCap, searchValue);
		} else if (type.equalsIgnoreCase(TYPE_VARIANTS_FOR_CONTENT_OBJECT)){
			variantList = AsyncDataForTaskObjectSelectMenuController.getVariantList(getContentObjectId(request), getTouchpointId(request));
		}

		// PERMISSION CHECK
		if ( Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.WebAppSecurity.SERVER_SIDE_ACTION_VALIDATION)) ) {
			if (permissions.length > 0 && Arrays.stream(permissions).noneMatch(UserUtil::isPermissionGranted)) {
				return "<?xml version='1.0'?><content><userOptions><option id=\"0\" value=\"0\">REQUEST DENIED</option></userOptions></content>";
			}
		}

        if ( type.equalsIgnoreCase(TYPE_UNBRIDGED_DATASOURCES) ) {
			long variableId = getVariableId(request);
			if (variableId == 0){
				if (isPrimarySourceType(request)) {
					dataSourceList = DataSource.findAllPrimary();
				} else {
					dataSourceList = DataSource.findAllReferenceAndRefenceConnected();
				}
			} else {
				DataElementVariable variable = DataElementVariable.findById(getVariableId(request));
				Map<Long, VariableDataElementMap> dataElementMap = variable.getDataElementMap();

				dataSourceList = dataSourceList.stream()
						.filter(ds -> !dataElementMap.containsKey(ds.getId()))
						.collect(Collectors.toList());
			}

			if (StringUtils.isEmpty(searchValue)) {
				dataSourceOptions.append("<option id=\"dataSourceOption_0\" value=\"0\">").append(TagUtils.formatLabel("page.label.none")).append("</option>");
			} else {
				dataSourceList = dataSourceList.stream().filter(x -> x.getName().toLowerCase().contains(searchValue.toLowerCase())).collect(Collectors.toList());
				if (dataSourceList.isEmpty()) {
					dataSourceOptions.append("<option id=\"dataSourceOption_0\" value=\"0\">").append(TagUtils.formatLabel("page.label.none")).append("</option>");
				}
			}


			for (DataSource ds : dataSourceList) {
				dataSourceOptions.append("<option id=\"dataSourceOption_").append(ds.getId()).append("\" value=\"").append(ds.getId()).append("\">").append(ds.getName() != null ? ds.getName() : "NO NAME").append("</option>");
			}
			returnData = "<options selectedOptionId=\"dataSourceOption_" + getSelectedItemId(request) + "\" fullListCount=\"" + dataSourceList.size() +
					"\" displayListCount=\"" + dataSourceList.size() + "\">"+StringEscapeUtils.escapeXml(dataSourceOptions.toString())+"</options>";
		}
		else if ( type.equalsIgnoreCase(TYPE_DATA_ELEMENTS) ) {
			dataElementOptions = new StringBuilder();
			long fullListCount = AbstractDataElement.getCountByDataSourceAndSourceType(getDataSourceId(request), 0);

			long selectedItemId = getSelectedItemId(request);
			AbstractDataElement selectedItem = AbstractDataElement.findById(selectedItemId);
			boolean listHasSelectedItemId = dataElementList.stream().anyMatch(o -> o.getId() == selectedItemId);

			if (selectedItem !=null && !listHasSelectedItemId) {
				dataElementOptions.append("<option id=\"dataElementOption_").append(selectedItem.getId()).append("\" value=\"").append(selectedItem.getId()).append("\">").append(selectedItem.getName() != null ? selectedItem.getName() : "NO NAME").append("</option>");
			}

			if (!isNew(request)) {
				dataElementOptions.append("<option id=\"dataElementOption_-1\" value=\"-1\">").append(TagUtils.formatLabel("page.label.unset")).append("</option>");
			}

			dataElementOptions.append("<option id=\"dataElementOption_0\" value=\"0\">").append(TagUtils.formatLabel("page.text.Empty")).append("</option>");

			if (dataElementList != null && !dataElementList.isEmpty()) {
				for (AbstractDataElement currentElement: dataElementList) {
					dataElementOptions.append("<option id=\"dataElementOption_").append(currentElement.getId()).append("\" value=\"").append(currentElement.getId()).append("\">").append(currentElement.getName() != null ? currentElement.getName() : "NO NAME").append("</option>");
				}
			}
			returnData = "<options selectedOptionId=\"dataElementOption_" + getSelectedItemId(request) + "\" fullListCount=\"" + fullListCount +
					"\" displayListCount=\"" + dataElementList.size() + "\">"+StringEscapeUtils.escapeXml(dataElementOptions.toString())+"</options>";
		} else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_TEXT_TEMPLATES) ) {
			smartTextOptions = new StringBuilder("<option id=\"smartTextOption_0\" value=\"0\">" + TagUtils.formatLabel("page.label.none") + "</option>");
			if (smartTextList != null && !smartTextList.isEmpty()) {
				for (ContentObject currentElement: smartTextList)
					smartTextOptions.append("<option id=\"smartTextOption_").append(currentElement.getId()).append("\" value=\"").append(currentElement.getId()).append("\">").append(currentElement.getName()).append("</option>");
			}
			returnData = "<options selectedOptionId=\"smartTextOption_" + getSelectedItemId(request) + "\">"+StringEscapeUtils.escapeXml(smartTextOptions.toString())+"</options>";
		} else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_IMAGE_TEMPLATES) ) {
			imageOptions = new StringBuilder("<option id=\"imageOption_0\" value=\"0\">" + TagUtils.formatLabel("page.label.none") + "</option>");
			if (imageList != null && !imageList.isEmpty()) {
				for (ContentObject currentElement: imageList)
					imageOptions.append("<option id=\"imageOption_").append(currentElement.getId()).append("\" value=\"").append(currentElement.getId()).append("\">").append(currentElement.getName()).append("</option>");
			}
			returnData = "<options selectedOptionId=\"imageOption_" + getSelectedItemId(request) + "\">"+StringEscapeUtils.escapeXml(imageOptions.toString())+"</options>";
		} else if ( type.equalsIgnoreCase(TYPE_COMMUNICATIONS_DATA_ELEMENTS) ) {
			if (dataElementList != null && !dataElementList.isEmpty()) {
				for (AbstractDataElement currentElement: dataElementList) {
					dataElementOptions.append("<option id=\"dataElementOption_").append(currentElement.getId()).append("\" value=\"").append(currentElement.getId()).append("\">").append(currentElement.getName()).append("</option>");
				}
			} else {
				dataElementOptions = new StringBuilder("<option id=\"dataElementOption_0\" value=\"0\">" + TagUtils.formatLabel("page.text.no.available.connected.data.elements") + "</option>");
			}
			returnData = "<options selectedOptionId=\"dataElementOption_" + getSelectedItemId(request) + "\">"+StringEscapeUtils.escapeXml(dataElementOptions.toString())+"</options>";
		} else if (type.equalsIgnoreCase(TYPE_COPY_FROM_INSTANCE)) {
			if (nodeList != null && !nodeList.isEmpty()) {
				nodeOptions = new StringBuilder("<option id=\"0\" value=\"0\">" + TagUtils.formatLabel("page.text.select.instance") + "</option>");
				for (Node currentNode: nodeList) {
					nodeOptions.append("<option id=\"").append(currentNode.getId()).append("\" value=\"").append(currentNode.getId()).append("\">").append(currentNode.getName()).append("</option>");
				}
			} else {
				nodeOptions = new StringBuilder("<option id=\"0\" value=\"0\">" + TagUtils.formatLabel("page.text.no.available.instance.to.copy.from") + "</option>");
			}
			returnData = "<userOptions>"+StringEscapeUtils.escapeXml(nodeOptions.toString())+"</userOptions>";
		} else if (type.equalsIgnoreCase(TYPE_CLONE_FROM_INSTANCE)) {
			if (dataCollectionList != null && !dataCollectionList.isEmpty()) {
				nodeOptions = new StringBuilder();
				for (DataSourceAssociation currentDataCollection: dataCollectionList) {
					if (currentDataCollection != null)
					{
						if (document != null)
						{
							nodeOptions.append("<option id=\"0\" value=\"0\" selected='selected' >Source: ").append(currentDataCollection.getName()).append("</option>");
							document = null;
						}
						else
						{
							nodeOptions.append("<option id=\"").append(currentDataCollection.getId()).append("\" value=\"").append(currentDataCollection.getId()).append("\">Target: ").append(currentDataCollection.getName()).append("</option>");
						}
					}
				}
				nodeOptions.append("<option id=\"-1\" value=\"-1\">").append(TagUtils.formatLabel("page.text.none")).append("</option>");
			} else {
				nodeOptions = new StringBuilder("<option id=\"-1\" value=\"-1\">" + TagUtils.formatLabel("page.text.none") + "</option>");
			}
			returnData = "<userOptions>"+StringEscapeUtils.escapeXml(nodeOptions.toString())+"</userOptions>";
		}  else if(type.equalsIgnoreCase(TYPE_ZONES_FOR_SECTION)) {
			if(zoneList != null && !zoneList.isEmpty()){
				zoneOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.zone") + "</option>");
				for (Zone zone: zoneList) {
					zoneOptions.append("<option value=\"").append(zone.getId()).append("\">").append(zone.getFriendlyName()).append("</option>");
				}				
			}else{
				zoneOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.zone") + "</option>");
			}
			returnData = "<zoneOptions>"+StringEscapeUtils.escapeXml(zoneOptions.toString())+"</zoneOptions>";
		} else if(type.equalsIgnoreCase(TYPE_CONTENT_PROOF_ZONES)) {
			if(zoneList != null && !zoneList.isEmpty()){
				zoneOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.zone") + "</option>");
				for (Zone zone: zoneList) {
					zoneOptions.append("<option value=\"").append(zone.getId()).append("\">").append(zone.getFriendlyName()).append("</option>");
				}
			}else{
				zoneOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.zone") + "</option>");
			}
			returnData = "<zoneOptions>"+StringEscapeUtils.escapeXml(zoneOptions.toString())+"</zoneOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_TOUCHPOINT_COMPOSITION_FILE_SET) ) {
			boolean isFilePackage = document.isTemplateControlled() && (document.isSefasCompositionTouchpoint() || document.isMPHCSCompositionTouchpoint());
			if(compositionFileSetList != null && !compositionFileSetList.isEmpty()){
				compPackOptions = new StringBuilder("<option value=\"0\">" +
                        (isFilePackage ? TagUtils.formatLabel("page.text.select.file.package") : TagUtils.formatLabel("page.text.select.composition.package")) +
                        "</option>");
				for (CompositionFileSet compFileSet: compositionFileSetList) {
					boolean isDefaultOption = ( document.getCompositionFileSet() != null && compFileSet.getId() == document.getCompositionFileSet().getId() );
					compPackOptions.append("<option value=\"").append(compFileSet.getId()).append("\" ").append(isDefaultOption ? "selected=\"selected\"" : "").append(" packageName=\"").append(compFileSet.getName()).append("\" ").append(" templateFileName=\"").append(compFileSet.getTemplateFileName()).append("\" ").append(" configurationFileName=\"").append(compFileSet.getCompositionConfigurationFileName()).append("\" >").append(compFileSet.getName()).append("</option>");
				}				
			} else {
				compPackOptions = new StringBuilder("<option value=\"0\">" +
                        (isFilePackage ? TagUtils.formatLabel("page.text.no.file.packages.for.touchpoint") : TagUtils.formatLabel("page.text.no.composition.packages.for.touchpoint")) +
                        "</option>");
			}
			returnData = "<packageOptions>"+StringEscapeUtils.escapeXml(compPackOptions.toString())+"</packageOptions>";
		}  else if ( type.equalsIgnoreCase(TYPE_TP_COLLECTION_COMPOSITION_FILE_SET) ) {
			if(compositionFileSetList != null && !compositionFileSetList.isEmpty()){
				TouchpointCollection tpCollection = TouchpointCollection.findById(getTouchpointCollectionId(request));

				compPackOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.composition.package") + "</option>");
				for (CompositionFileSet compFileSet: compositionFileSetList) {
					boolean isDefaultOption = ( tpCollection.getDefaultCompositionFileSet() != null && compFileSet.getId() == tpCollection.getDefaultCompositionFileSet().getId() );
					compPackOptions.append("<option value=\"").append(compFileSet.getId()).append("\" ").append(isDefaultOption ? "selected=\"selected\"" : "").append(" packageName=\"").append(compFileSet.getName()).append("\" ").append(" templateFileName=\"").append(compFileSet.getTemplateFileName()).append("\" ").append(" configurationFileName=\"").append(compFileSet.getCompositionConfigurationFileName()).append("\" >").append(compFileSet.getName()).append("</option>");
				}				
			} else {
				compPackOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.composition.packages.for.touchpoint.collection") + "</option>");
			}
			returnData = "<packageOptions>"+StringEscapeUtils.escapeXml(compPackOptions.toString())+"</packageOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_ZONES_DEFAULT_TEXTSTYLES)){ 
			if(textStyleList != null && !textStyleList.isEmpty()){
				boolean singleZoneSelected = getZoneIds(request).size() == 1;
				Zone firstZone = Zone.findById(getZoneIds(request).get(0));

				textStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (TextStyle textStyle: textStyleList) {
					boolean isDefaultOption = ( singleZoneSelected && firstZone.getDefaultTextStyle() != null && firstZone.getDefaultTextStyle().getId() == textStyle.getId());
					textStyleOptions.append("<option id=\"textStyleOption_").append(textStyle.getId()).append("\" value=\"").append(textStyle.getId()).append("\" ").append(isDefaultOption ? "selected=\"selected\"" : "").append(">").append(textStyle.getName()).append("</option>");
				}
			} else {
				textStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.text.styles") + "</option>");
			}
			returnData = "<styleOptions>"+StringEscapeUtils.escapeXml(textStyleOptions.toString())+"</styleOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_ZONES_PARASTYLES)){ 
			if(paraStyleList != null && !paraStyleList.isEmpty()){
				boolean singleZoneSelected = getZoneIds(request).size() == 1;
				Zone firstZone = Zone.findById(getZoneIds(request).get(0));

				paraStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (ParagraphStyle paraStyle: paraStyleList) {
					boolean isDefaultOption = ( singleZoneSelected && firstZone.getDefaultParagraphStyle() != null && firstZone.getDefaultParagraphStyle().getId() == paraStyle.getId());
					paraStyleOptions.append("<option id=\"paragraphStyleOption_").append(paraStyle.getId()).append("\" value=\"").append(paraStyle.getId()).append("\" ").append(isDefaultOption ? "selected=\"selected\"" : "").append(">").append(paraStyle.getName()).append("</option>");
				}
			} else {
				paraStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.para.styles") + "</option>");
			}
			returnData = "<styleOptions>"+StringEscapeUtils.escapeXml(paraStyleOptions.toString())+"</styleOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_ZONES_LISTSTYLES)){ 
			if(listStyleList != null && !listStyleList.isEmpty()){
				boolean singleZoneSelected = getZoneIds(request).size() == 1;
				Zone firstZone = Zone.findById(getZoneIds(request).get(0));

				listStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (ListStyle listStyle: listStyleList) {
					boolean isDefaultOption = ( singleZoneSelected && firstZone.getDefaultListStyle() != null && firstZone.getDefaultListStyle().getId() == listStyle.getId());
					listStyleOptions.append("<option id=\"listStyleOption_").append(listStyle.getId()).append("\" value=\"").append(listStyle.getId()).append("\" ").append(isDefaultOption ? "selected=\"selected\"" : "").append(">").append(listStyle.getName()).append("</option>");
				}
			} else {
				listStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.para.styles") + "</option>");
			}
			returnData = "<styleOptions>"+StringEscapeUtils.escapeXml(listStyleOptions.toString())+"</styleOptions>";
		}else if ( type.equalsIgnoreCase(TYPE_AUDITING_DOMAIN_INSTANCES)){
			User requestUser = UserUtil.getPrincipalUser();
			
			nodeOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.label.auditing.all.instances.for") + "</option>");
			if(nodeList != null && !nodeList.isEmpty()){
				for (Node instance: nodeList) {
					if(requestUser != null && !instance.hasUserAccess(requestUser))
						continue;
					nodeOptions.append("<option value=\"").append(instance.getId()).append("\" >").append(instance.getBranch().getName()).append(" - ").append(instance.getName()).append("</option>");
				}
			}
			returnData = "<instanceOptions>"+StringEscapeUtils.escapeXml(nodeOptions.toString())+"</instanceOptions>";
		}else if ( type.equalsIgnoreCase(TYPE_AUDITING_INSTANCE_USERS)){ 
			if(userList != null && !userList.isEmpty()){
				userOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.label.auditing.any.user") + "</option>");
				for (User user: userList) {
					userOptions.append("<option value=\"").append(user.getId()).append("\" >").append(user.getName()).append(" (").append(user.getIdPTypeDisplayName()).append(")").append("</option>");
				}
			}else{
				userOptions = new StringBuilder("<option id=\"-1\" value=\"-1\">" + TagUtils.formatLabel("page.text.no.available.users") + "</option>");
			}
			returnData = "<userOptions>"+StringEscapeUtils.escapeXml(userOptions.toString())+"</userOptions>";
		}else if ( type.equalsIgnoreCase(TYPE_TEXT_STYLES)) {
			if(textStyleList != null && !textStyleList.isEmpty()){
				textStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (TextStyle textStyle: textStyleList) {
					textStyleOptions.append("<option id=\"textStyleOption_").append(textStyle.getId()).append("\" value=\"").append(textStyle.getId()).append("\" >").append(textStyle.getName()).append("</option>");
				}
			} else {
				textStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.text.styles") + "</option>");
			}
			returnData = "<options selectedOptionId=\"textStyleOption_" + getSelectedItemId(request) + "\">"+StringEscapeUtils.escapeXml(textStyleOptions.toString())+"</options>";
		}else if ( type.equalsIgnoreCase(TYPE_PARAGRAPH_STYLES)) {
			if(paraStyleList != null && !paraStyleList.isEmpty()){
				paraStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (ParagraphStyle paraStyle: paraStyleList) {
					paraStyleOptions.append("<option id=\"paragraphStyleOption_").append(paraStyle.getId()).append("\" value=\"").append(paraStyle.getId()).append("\" >").append(paraStyle.getName()).append("</option>");
				}
			} else {
				paraStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.para.styles") + "</option>");
			}
			returnData = "<options selectedOptionId=\"paragraphStyleOption_" + getSelectedItemId(request) + "\">"+StringEscapeUtils.escapeXml(paraStyleOptions.toString())+"</options>";
		}else if ( type.equalsIgnoreCase(TYPE_LIST_STYLES)) {
			if (listStyleList != null && !listStyleList.isEmpty()) {
				listStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.no.default.style") + "</option>");
				for (ListStyle listStyle : listStyleList) {
					listStyleOptions.append("<option id=\"listStyleOption_").append(listStyle.getId()).append("\" value=\"").append(listStyle.getId()).append("\" >").append(listStyle.getName()).append("</option>");
				}
			} else {
				listStyleOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.no.list.styles") + "</option>");
			}
			returnData = "<options selectedOptionId=\"listStyleOption_" + getSelectedItemId(request) + "\">" + StringEscapeUtils.escapeXml(listStyleOptions.toString()) + "</options>";
		}else if(type.equalsIgnoreCase(TYPE_MESSAGES_RELEASE_FROM_TRANS_WORKFLOWS) || type.equalsIgnoreCase(TYPE_MESSAGES_APPROVE_WORKFLOWS) || type.equalsIgnoreCase(TYPE_MESSAGES_REJECT_WORKFLOWS) || type.equalsIgnoreCase(TYPE_MESSAGES_RETRY_WORKFLOWS)){
			if(workflowList != null && !workflowList.isEmpty()){
				workflowOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.workflow") + "</option>");
				for (ConfigurableWorkflow workflow: workflowList) {
					workflowOptions.append("<option value=\"").append(workflow.getId()).append("\">").append(workflow.getName()).append("</option>");
				}
			}else{
				workflowOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.text.select.workflow") + "</option>");
			}
			returnData = "<workflowOptions>"+StringEscapeUtils.escapeXml(workflowOptions.toString())+"</workflowOptions>";
		}else if ( type.equalsIgnoreCase(TYPE_LINKED_TO_TASKS)) {
			if (taskList != null && !taskList.isEmpty()) {
				taskOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.label.create.new.task") + "</option>");
				for (Task task : taskList) {
					String taskDescription = task.getRequirementStr();
					taskOptions.append("<option id=\"taskOption_").append(task.getId()).append("\" value=\"").append(task.getId()).append("\" >").append(task.getItemName()).append(taskDescription == null || taskDescription.isEmpty() ? "" : " (" + taskDescription + ")").append("</option>");
				}
			} else {
				taskOptions = new StringBuilder("<option value=\"0\">" + TagUtils.formatLabel("page.label.create.new.task") + "</option>");
			}
			returnData = "<taskOptions selectedOptionId=\"taskOption_" + getSelectedItemId(request) + "\">" + StringEscapeUtils.escapeXml(taskOptions.toString()) + "</taskOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_VARIANTS_FOR_CONTENT_OBJECT)) {
			if (variantList != null && !variantList.isEmpty()) {
				variantOptions = new StringBuilder("<option id=\"varOption_0\" value=\"0\">"+TagUtils.formatLabel("page.label.no.selected.items")+"</option>");
				for (TaskEditController.VariantSelectOption currentElement : variantList) {
					variantOptions.append("<option id=\"varOption_").append(currentElement.getId()).append("\" value=\"").append(currentElement.getType()).append("_").append(currentElement.getId()).append("\" data-clean=\"").append(currentElement.getName().replaceAll("&nbsp;", "")).append("\">").append(currentElement.getName()).append("</option>");
				}
			} else {
				variantOptions = new StringBuilder("<option id=\"varOption_0\" value=\"0\">"+TagUtils.formatLabel("page.label.no.selected.items")+"</option>");
			}
			returnData = "<varOptions selectedOptionId=\"varOption_" + getSelectedItemId(request) + "\">" + StringEscapeUtils.escapeXml(variantOptions.toString()) + "</varOptions>";
		} else if ( type.equalsIgnoreCase(TYPE_DATA_RESOURCE) ) {
			dataResourceOptions = new StringBuilder();
			int fullListCount = DataResourceUtil.findDataResourcesForDropdownCount(getTouchpointCollectionId(request), getTouchpointId(request), searchValue);
			DataResource selectedDataResource = DataResource.findById(getSelectedItemId(request));
			int dataResourceListCount = 0;

            if (dataResourceList != null && !dataResourceList.isEmpty()) {
				long selectedItemId = getSelectedItemId(request);
				boolean listHasSelectedItemId = selectedDataResource!=null && dataResourceList.stream().anyMatch(o -> o.getId() == selectedItemId);
				dataResourceListCount = dataResourceList.size();

				if (selectedItemId > 0  && !listHasSelectedItemId) {
					dataResourceList.add(selectedDataResource);
				}

				for (DataResource dataResource : dataResourceList) {
					String metatags = dataResource.getMetatags() != null ? dataResource.getMetatags().toLowerCase() : "";
					dataResourceOptions.append("<option id=\"dataResourceOption_").append(dataResource.getId()).append("\" value=\"").append(dataResource.getId()).append("\"").append(" data-filtervalue=\"").append(metatags).append("\" ").append(dataResource.getId() == selectedItemId ? "selected=\"selected\"" : "").append(" >").append(dataResource.getName() != null ? dataResource.getName() : "NO NAME").append("</option>");
				}
			} else {
				dataResourceOptions.append("<option id=\"dataResourceOption_-1\" value=\"-1\" selected=\"selected\" >").append(TagUtils.formatLabel("client_messages.text.no_items")).append("</option>");
			}
			returnData = "<options selectedOptionId=\"dataResourceOption_" + getSelectedItemId(request) + "\" fullListCount=\"" + fullListCount +
					"\" displayListCount=\"" + dataResourceListCount + "\">" + StringEscapeUtils.escapeXml(dataResourceOptions.toString()) + "</options>";
		} else {
			if (userList != null && !userList.isEmpty()) {
				userOptions = new StringBuilder("<option id=\"0\" value=\"0\">" + TagUtils.formatLabel("page.text.select.user") + "</option>");
				for (User currentUser: userList) {
					userOptions.append("<option id=\"").append(currentUser.getId()).append("\" value=\"").append(currentUser.getId()).append("\">").append(currentUser.getName()).append("</option>");
				}
			} else {
				userOptions = new StringBuilder("<option id=\"0\" value=\"0\">" + TagUtils.formatLabel("page.text.no.assignable.user") + "</option>");
			}
			returnData = "<userOptions>"+StringEscapeUtils.escapeXml(userOptions.toString())+"</userOptions>";
		}
		return "<?xml version='1.0'?><content>"+returnData+"</content>";
	}
	
	private Long getContentObjectId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -9);
	}

	private List<Long> getContentObjectIds(HttpServletRequest request) {
		String idStr = ServletRequestUtils.getStringParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, null);
		List<Long> ids = new ArrayList<>();
		for (String currentId: idStr.split(","))
			ids.add(Long.valueOf(currentId));
		return ids;
	}

	private ContentObject getContentObject(HttpServletRequest request) {
		return ContentObject.findByHttpServletRequest(request);
	}

	private Insert getInsert(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, PARAM_INSERT_ID, -9);
		return HibernateUtil.getManager().getObject(Insert.class, id);
	}
	
	private InsertSchedule getInsertSchedule(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, PARAM_INSERT_SCHED_ID, -9);
		return HibernateUtil.getManager().getObject(InsertSchedule.class, id);
	}
	
	private Tag getTag(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, PARAM_TAG, -9);
		return HibernateUtil.getManager().getObject(Tag.class, id);
	}
	
	private LookupTableInstance getLookupTableInstance(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, PARAM_LOOKUP_TABLE_ID, -9);
		return HibernateUtil.getManager().getObject(LookupTableInstance.class, id);
	}	
	
	private DocumentSection getDocumentSection(HttpServletRequest request) {
		Long id = ServletRequestUtils.getLongParameter(request, PARAM_DOCSECTION_ID, -9);
		return HibernateUtil.getManager().getObject(DocumentSection.class, id);
	}		
	
	private List<Long> getTouchpointSelectionIds(HttpServletRequest request) {
		String idStr = ServletRequestUtils.getStringParameter(request, PARAM_TOUCHPOINT_SELECTION_ID, null);
		List<Long> ids = new ArrayList<>();
		for (String currentId: idStr.split(","))
			ids.add(Long.valueOf(currentId));
		return ids;
	}
	
	private List<Long> getCommunicationIds(HttpServletRequest request) {
		String idStr = ServletRequestUtils.getStringParameter(request, PARAM_COMMUNICATION_ID, null);
		List<Long> ids = new ArrayList<>();
		for (String currentId: idStr.split(","))
			ids.add(Long.valueOf(currentId));
		return ids;
	}

	private List<Long> getRationalizerIds(HttpServletRequest request) {
		String contentGuid = ServletRequestUtils.getStringParameter(request, PARAM_RATIONALIZER_CONTENT_ID, null);
		RationalizerDocumentContent rdc = RationalizerDocumentContent.findByGuid(contentGuid);

		List<Long> ids = new ArrayList<>();
		ids.add(rdc.getId());
		return ids;
	}
	
	private List<Long> getZoneIds(HttpServletRequest request) {
		String idStr = ServletRequestUtils.getStringParameter(request, PARAM_ZONE_ID, null);
		List<Long> ids = new ArrayList<>();
		for (String currentId: idStr.split(","))
			ids.add(Long.valueOf(currentId));
		return ids;
	}
	
	private List<Long> getTaskIds(HttpServletRequest request) {
		String idStr = ServletRequestUtils.getStringParameter(request, PARAM_TASK_ID, null);
		List<Long> ids = new ArrayList<>();
		for (String currentId: idStr.split(","))
			ids.add(Long.valueOf(currentId));
		return ids;
	}	
	
	private List<Node> getDomainInstances(long domainId){
		List<Node> instances = new ArrayList<>();
		if(domainId == 0){	// All domains
			for(Branch domain : Branch.findAllVisible()){
				instances.addAll(domain.getAllAccessibleNodes(true));
			}
		}else{
			Branch domain = Branch.findById(domainId);
			instances.addAll(domain.getAllAccessibleNodes(true));
		}
		return instances;
	}
	
	private List<User> getInstanceUsers(long domainId, long instanceId){
		List<User> users = new ArrayList<>();
		if(instanceId == 0){	// All instances
			if(domainId == 0){	// All domains
				for(Branch domain : Branch.findAllVisible()){
					for(Node instace : domain.getAllAccessibleNodes(true)){
						SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(instace.getSchemaName());
						List<User> instanceUsers = User.findAllActiveUsers();
						instanceUsers.removeAll(users);
						users.addAll(instanceUsers);
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
					}
				}
			}else{
				Branch domain = Branch.findById(domainId);
				for(Node instace : domain.getAllAccessibleNodes(true)){
					SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(instace.getSchemaName());
					List<User> instanceUsers = User.findAllActiveUsers();
					instanceUsers.removeAll(users);
					users.addAll(instanceUsers);
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}				
			}

		}else{
			Node instance = Node.findById(instanceId);
			SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(instance.getSchemaName());
			users.addAll(User.findAllActiveUsers());
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}
		return users;
	}	
	
	private Long getTouchpointId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -9);
	}

	private Long getTouchpointCollectionId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_TOUCHPOINT_COLLECTION_ID, -9);
	}	

	private Long getNodeId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, ContentObject.REQ_PARM_CONTENT_OBJECT_ID, -9);
	}

	private Long getInstanceId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_INSTANCE_ID, -9);
	}
	
	private Long getDomainId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_DOMAIN_ID, -9);
	}
	
	private Boolean getIsFixedDefinition (HttpServletRequest request) {
		return ServletRequestUtils.getBooleanParameter(request, PARAM_IS_FIXED_DEFINITION, false);
	}

	private Boolean getIsFixedOrDefaultDefinition (HttpServletRequest request) {
		return ServletRequestUtils.getBooleanParameter(request, PARAM_IS_FIXED_DEFAULT_DEFINITION, false);
	}
	
	private Long getSelectedItemId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_SELECTED_ITEM_ID, -1L);
	}
	
	private Long getDataSourceId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_DATA_SOURCE_ID, -9);
	}

	private Long getVariableId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_VARIABLE_ID, -1L);
	}

	private Long getWorkflowId (HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, PARAM_WORKFLOW_ID, -1L);
	}

	private Boolean isPrimarySourceType (HttpServletRequest request) {
		return ServletRequestUtils.getBooleanParameter(request, PARAM_IS_PRIMARY, true);
	}

	private Boolean isNew (HttpServletRequest request) {
		return ServletRequestUtils.getBooleanParameter(request, PARAM_IS_NEW, false);
	}
}