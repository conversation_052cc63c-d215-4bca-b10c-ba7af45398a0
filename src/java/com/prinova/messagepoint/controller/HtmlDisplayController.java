package com.prinova.messagepoint.controller;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.HttpRequestUtil;

public class HtmlDisplayController implements Controller {

	private static final Log log = LogUtil.getLog(HtmlDisplayController.class);
	
	public static final String REQ_PARM_FILE	= "file";
	public static final String REQ_PARM_TYPE	= "type";
	
	public static final String HTML_FILE_TYPE_PREVIEW	= "preview";
	
	private String formView;
	
	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		String filePath = ServletRequestUtils.getStringParameter(request, REQ_PARM_FILE, null);
		String fileType = ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, null);
		
		File file = new File(filePath);

		boolean isDirectory = file.isDirectory();
		boolean canReadFile = file.canRead();
		boolean isFileAccessAllowed = isFileAccessAllowed(file);

		Map<String, Object> dataMap = new HashMap<>();
		
		dataMap.put("fileType", fileType);
		
		if (isDirectory || !canReadFile || !isFileAccessAllowed) {
			HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_FOUND, "The file " + file.getName() + " is not available", request, response);
			log.error("Error: The file " + file.getName() + " is not available");
		} else {

			if ( fileType.equalsIgnoreCase(HTML_FILE_TYPE_PREVIEW) ) {
				String previewHTML = FileUtil.getFileAsString(filePath);
				dataMap.put("displayHTML", previewHTML);
			} else {
				HttpRequestUtil.sendMessagepointError(HttpServletResponse.SC_NOT_ACCEPTABLE, "Invalid 'type' definition", request, response);
				log.error("Error: Invalid 'type' definition");
			}
		
		}
		
		return new ModelAndView(getFormView(), dataMap);
	}

	private boolean isFileAccessAllowed(File f) {
		String[] allowedFolders = {
			new File(ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_OutputDir)).getAbsolutePath(),
			new File(ApplicationUtil.getRootPath()).getAbsolutePath() 
		};

		String parentPath = f.getParentFile().getAbsolutePath();

		boolean allowed = false;
		for (String folder : allowedFolders) {
			allowed = allowed || parentPath.startsWith(folder);
		}

		return allowed;
	}
}