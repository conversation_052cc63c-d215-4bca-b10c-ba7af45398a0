package com.prinova.messagepoint.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.audit.AuditEventType;
import com.prinova.messagepoint.model.audit.AuditEventUtil;
import com.prinova.messagepoint.model.audit.AuditObjectType;
import com.prinova.messagepoint.model.audit.AuditReport;
import com.prinova.messagepoint.model.audit.AuditReportType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService;
import com.prinova.messagepoint.platform.services.export.GenerateCompareReportService;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;


public class AsyncSyncReportResultController implements Controller {
	
	private static final Log log = LogUtil.getLog(AsyncSyncReportResultController.class);

	public static final String REQ_PARAM_REPORT_ID 			= "reportId";
	public static final String REQ_PARAM_TYPE 				= "type";
	
	public static final String TYPE_SYNC_REPORT 			= "syncreport";
	
	public static final String PARAM_DOC_ID					= "documentId";
	public static final String PARAM_OTHER_DOC_ID			= "otherDocumentId";
	public static final String PARAM_VARIANT_ID 			= "variantId";
	public static final String PARAM_ZONE_PART_ID			= "zonePartId";
	public static final String PARAM_LANG_CODE_ID			= "langCodeId";
	public static final String PARAM_COMPARE_TO_ID			= "compareToId";
	public static final String PARAM_COMPARE_TO_NODE_ID		= "syncInstanceId";
	public static final String PARAM_COMPARE_STATE			= "compareState";
    public static final String PARAM_DIRECTION              = "direction";
    public static final String PARAM_SELECTED_OBJECTS       = "selectedObjects";
    public static final String PARAM_SYNC_MULTYWAY       	= "syncMultiWay";
    public static final String PARM_CONTENT_OBJECT_FILTER	= "filterId";
	public static final String PARM_SIBLING_PARENT_INSTANCE_ID	= "siblingParentInstanceId";
	public static final String PARM_SIBLING_PARENT_ID		= "siblingParentId";
	public static final String PARM_PARENT_INSTANCE_ID		= "parentInstanceId";
	public static final String PARM_PARENT_ID				= "parentId";
	

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		
		ServletOutputStream out = response.getOutputStream();

		String type = getType(request);
		long docId = ServletRequestUtils.getLongParameter(request, PARAM_DOC_ID, -1);
		long otherDocId = ServletRequestUtils.getLongParameter(request, PARAM_OTHER_DOC_ID, -1);
		//long reportId = getReportId(request);
		long compareToNodeId = ServletRequestUtils.getLongParameter(request, PARAM_COMPARE_TO_NODE_ID, -1);
		String direction = ServletRequestUtils.getStringParameter(request, PARAM_DIRECTION, null);
		String[] selected = request.getParameterValues("selected[]");
		int contentObjectFilter = ServletRequestUtils.getIntParameter(request, PARM_CONTENT_OBJECT_FILTER, 0);
		List<String> selectedObjects = new ArrayList<>();
		if(selected!=null)
			selectedObjects.addAll(Arrays.asList(selected));
		boolean syncMultiWay = ServletRequestUtils.getStringParameter(request, PARAM_SYNC_MULTYWAY, null).equals("true")?true:false;
		long auditReportId = 0;
		JSONObject obj = new JSONObject();
		String result = "PROCESSING";
		
		if(type.equals(TYPE_SYNC_REPORT)){
			User requestor = UserUtil.getPrincipalUser();
			long userId = requestor.getId();
			int auditReportType = AuditReportType.ID_TOUCHPOINT_COMPARE_REPORT;
			Document document = Document.findById(docId);
			TouchpointSelection touchpointSelection = document.getMasterTouchpointSelection();
			ServiceExecutionContext context = CreateOrUpdateAuditReportService.createContextForCreate(requestor.getId(), auditReportType);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if (!serviceResponse.isSuccessful()) {
				result = "ERROR";
			}else{
				auditReportId = (Long)serviceResponse.getResultValueBean();
				AuditReportType reportType = new AuditReportType(auditReportType);
				long siblingParentInstanceId = ServletRequestUtils.getLongParameter(request, PARM_SIBLING_PARENT_INSTANCE_ID, -1);
				long siblingParentId = ServletRequestUtils.getLongParameter(request, PARM_SIBLING_PARENT_ID, -1);
				long parentInstanceId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_INSTANCE_ID, -1);
				long parentId = ServletRequestUtils.getLongParameter(request, PARM_PARENT_ID, -1);
				context = GenerateCompareReportService.createContext(auditReportId, touchpointSelection, requestor, 
						document, otherDocId, compareToNodeId, (direction.equalsIgnoreCase("commit")?false:true), false, 
						contentObjectFilter, Node.getCurrentNode().getId(), syncMultiWay,
						siblingParentInstanceId, siblingParentId, parentInstanceId, parentId, selectedObjects);
				
				
				service = MessagepointServiceFactory.getInstance().lookupService(GenerateCompareReportService.SERVICE_NAME, GenerateCompareReportService.class);
				service.execute(context);
				serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					result = "ERROR";
					context = CreateOrUpdateAuditReportService.createContextForError(auditReportId);
					service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateAuditReportService.SERVICE_NAME, CreateOrUpdateAuditReportService.class);
					service.execute(context);
					// Audit (Audit Report Failed)
					AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, reportType.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_FAIL, null);
				} else {

					result = "COMPLETE";
					// Audit (Audit Report Success)
					AuditEventUtil.push(AuditEventType.ID_AUDIT_REPORT, AuditObjectType.ID_AUDIT_REPORT, reportType.getName(), auditReportId, AuditActionType.ID_AUDIT_REPORT_REQUESTED_SUCCEED, null);
				}
			}
			if(auditReportId > 0){
				AuditReport report = AuditReport.findById(auditReportId);
				if(report != null){
					obj.put("requestTime", DateUtil.formatDateTime(report.getRequestDate()));
					if(report.getReportPath()!=null){
						File reportFile = new File(report.getReportPath());
						if (reportFile.exists()) {
							obj.put("reportPath", report.getReportPath());
							result = "COMPLETE";
						}
					}
				}
			}
			obj.put("result", result);		
			obj.put("reportId", auditReportId);
		}
		
		out.write(obj.toString().getBytes());
		out.flush();

		return null;
	}
	
	
	private long getReportId(HttpServletRequest request) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARAM_REPORT_ID, -1);
	}

	private String getType(HttpServletRequest request) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARAM_TYPE, null);
	}

}