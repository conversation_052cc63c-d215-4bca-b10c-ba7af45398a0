package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.dataadmin.MetatagsEditController;
import com.prinova.messagepoint.model.tagcloud.TagCloud;
import com.prinova.messagepoint.model.tagcloud.TagCloudType;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AsyncMetatagsEditController implements Controller {

    private static final Log log = LogUtil.getLog(AsyncLayoutController.class);

    public static final String REQ_PARM_ACTION 				= "action";
    public static final String REQ_PARM_OBJECT_TYPES		= "obj_type";

    public static final String ACTION_GET_TAGS			= "get_tags";


    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        response.setContentType("application/json");
        ServletOutputStream out = response.getOutputStream();

        try {
            String action = ServletRequestUtils.getStringParameter(request, REQ_PARM_ACTION, null);

            if ( action.equalsIgnoreCase(ACTION_GET_TAGS) ) {
                out.write(getGetTagsResponseJSON(request, response).toString().getBytes());
            } else {
                JSONObject returnObj = new JSONObject();
                returnObj.put("error", true);
                returnObj.put("message", "Metatags edit: Invalid action type");
                out.write(returnObj.toString().getBytes());
            }

        } catch (JSONException e) {
            JSONObject returnObj = new JSONObject();
            returnObj.put("error", true);
            returnObj.put("message", e.getMessage());
            out.write(returnObj.toString().getBytes());
        }

        out.flush();

        return null;
    }

    private JSONObject getGetTagsResponseJSON(HttpServletRequest request, HttpServletResponse response) throws JSONException {

        boolean debugMode = true;

        long startTime = 0;
        if (debugMode)
            startTime = new Date().getTime();

        String types = ServletRequestUtils.getStringParameter(request, REQ_PARM_OBJECT_TYPES, null);
        Long rationalizerApplicationId = ServletRequestUtils.getLongParameter(request, MetatagsEditController.PARAM_RATIONALIZER_APP_ID, -1L);

        JSONObject returnObj = new JSONObject();
        JSONArray tagsByTypeArray = new JSONArray();

        if (types != null && !types.isEmpty()) {

            String[] typesStrList = types.split(",");
            List<Long> typesList = new ArrayList<>();
            for ( int i = 0; i < typesStrList.length; i++ )
                typesList.add(Long.valueOf(typesStrList[i]));

            try {

                List<TagCloud> tags = TagCloud.findAllByType(typesList, rationalizerApplicationId);
                long currentType = -1L;
                JSONArray currentTagsArray = new JSONArray();
                int index = 0;
                for ( TagCloud currentTag: tags ) {

                    // When results list contains multiple types
                    if ( currentTag.getTypeId() != currentType && currentType != -1L ) {
                        addTagTypeToArray(currentType, currentTagsArray, tagsByTypeArray);
                        currentTagsArray = new JSONArray();
                    }
                    currentType = currentTag.getTypeId();

                    JSONObject tagObj = new JSONObject();
                    tagObj.put("n",currentTag.getName());
                    tagObj.put("i",currentTag.getId());
                    if ( currentTag.getDocument() != null )
                        tagObj.put("d", currentTag.getDocument().getId());
                    else if ( currentTag.getRationalizerApplication() != null )
                        tagObj.put("r", currentTag.getRationalizerApplication().getId());

                    currentTagsArray.put( tagObj );
                    index++;

                    // When last/only type
                    if ( index == tags.size() )
                        addTagTypeToArray(currentType, currentTagsArray, tagsByTypeArray);

                }

            } catch (Exception e) {
                returnObj.put("error", true);
                returnObj.put("message", e.getMessage());
            }

        }

        returnObj.put("tags_by_type", tagsByTypeArray);

        if (debugMode)
            log.error("Tags admin: get tags by type: " + (new Date().getTime() - startTime));

        return returnObj;
    }

    private static void addTagTypeToArray(Long tagType, JSONArray tagsArray, JSONArray tagsByTypeArray) {
        JSONObject tagTypeObj = new JSONObject();
        TagCloudType tagCloudType = new TagCloudType( Integer.valueOf( String.valueOf(tagType) ) );
        tagTypeObj.put("type", tagType);
        tagTypeObj.put("label", tagCloudType.getName());
        tagTypeObj.put("tags",tagsArray);
        tagsByTypeArray.put( tagTypeObj );
    }

}
