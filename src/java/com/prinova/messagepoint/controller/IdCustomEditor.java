package com.prinova.messagepoint.controller;

import java.beans.PropertyEditorSupport;

import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.HibernateUtil;

public class IdCustomEditor<T extends IdentifiableMessagePointModel> extends PropertyEditorSupport {
	
	private Class<T> clazz;
	
	public IdCustomEditor( Class<T> clazz ) {
		this.clazz = clazz;
	}

	public void setAsText(String text) throws IllegalArgumentException {
		long id = Long.parseLong( text );
		
		if ( id > 0 ) {
			if (clazz.equals(Node.class))
			{
				setValue(Node.findById(id));
			}
			else if (clazz.equals(Branch.class))
			{
				setValue(Branch.findById(id));
			}
			else 
			{
				Object obj = HibernateUtil.getManager().getObject(clazz,id);
				setValue(obj);
			}
		} else {
			setValue(null);
		}
	}

	public String getAsText() {
		IdentifiableMessagePointModel object = (IdentifiableMessagePointModel)getValue();
		if( object == null ) return "0";
		return "" + object.getId();
	}
}   