package com.prinova.messagepoint.controller.communication;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.interview.InterviewFieldValues;
import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.controller.communication.connected.AbstractDataElementDeserializer;
import com.prinova.messagepoint.controller.communication.connected.AbstractDataSourceDeserializer;
import com.prinova.messagepoint.controller.communication.connected.TouchpointCommunicationsListDetailItem;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionUtil;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.util.InterviewValuesVO;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.communication.Communication;

public class TouchpointCommunicationsListDetailController implements Controller {

	public static String REQ_PRAM_COMMUNICATION_ID = "communicationId";
	
	private String successView;
	private String formView;

	public String getSuccessView() {
		return successView;
	}

	public void setSuccessView(String successView) {
		this.successView = successView;
	}

	public String getFormView() {
		return formView;
	}

	public void setFormView(String formView) {
		this.formView = formView;
	}
	private CacheDataService dataService = null;
	public void setDataService (CacheDataService dataService) {
		this.dataService = dataService;
	}

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PRAM_COMMUNICATION_ID, -1L);
		Communication communication = Communication.findById(communicationId);
		CommunicationOrderEntryEditWrapper wrapper;
		if (communication.getDocument().isCommunicationUseBeta()) {

			SandboxFile refDataFile = CommunicationWSClient.retrieveRefDataFile(
					communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
					communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
					communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
					communication.getGuid(), communication.getPmgrOrderUUID()
			);
			List<TouchpointCommunicationsListDetailItem> overviewCommunicationItemDetails = new ArrayList<>();

			if(refDataFile!= null) {
				JSONObject interviewValues = new JSONObject(new String(refDataFile.getFileContent()));
				String communicationDefinition = dataService.getInterviewDefinitionFile(communication.getDocument());

				JSONObject communicationJsonBackend = null;
				boolean processCommunicationDefinition = ( communicationDefinition != null );
				if (processCommunicationDefinition) {
					communicationJsonBackend = new JSONObject(communicationDefinition);

					if (communicationJsonBackend == null || !communicationJsonBackend.has(Constants.Key_MetadataFormItemDefinitionVOs)) {
						processCommunicationDefinition = false;
					}
				}

				if (processCommunicationDefinition) {
					Gson gson = new GsonBuilder()
							.registerTypeAdapter(AbstractDataElement.class, new AbstractDataElementDeserializer())
							.registerTypeAdapter(DataSource.class, new AbstractDataSourceDeserializer())
							.create();
					String metadataFormItemDefinitions = communicationJsonBackend.get(Constants.Key_MetadataFormItemDefinitionVOs).toString();
					MetadataFormItemDefinitionVO[] items = gson.fromJson(metadataFormItemDefinitions, MetadataFormItemDefinitionVO[].class);
					ObjectMapper objectMapper = new ObjectMapper();
					InterviewValuesVO[] interviewValuesVOS = objectMapper.readValue(interviewValues.get(Constants.Key_InterviewValues).toString(), InterviewValuesVO[].class);

					for (MetadataFormItemDefinitionVO metadataFormItemDefinitionVO : items) {
						if (metadataFormItemDefinitionVO.getType().getId() != MetadataFormItemType.ID_HEADER && metadataFormItemDefinitionVO.getType().getId() != MetadataFormItemType.ID_DIVIDER) {
							if (MetadataFormItemDefinitionUtil.isAppliedItemForParents(items, communication, metadataFormItemDefinitionVO, interviewValuesVOS)) {
								TouchpointCommunicationsListDetailItem objItem = new TouchpointCommunicationsListDetailItem();
								objItem.setName(metadataFormItemDefinitionVO.getName());
								InterviewValuesVO interviewValuesVO = getInterviewItemValue(interviewValuesVOS, metadataFormItemDefinitionVO.getGuid());
								if (interviewValuesVO != null) {
									Object value = interviewValuesVO.getValue();
									if (metadataFormItemDefinitionVO.getType().getId() == MetadataFormItemType.ID_FILE) {
										if (value != null && !value.toString().isEmpty() && ((LinkedHashMap) value).get("name") != null) {
											objItem.setValue(((LinkedHashMap) value).get("name").toString());
										}
									} else {
										if(value!= null) {
											objItem.setValue(interviewValuesVO.getValue().toString());
										}
									}
								}
								overviewCommunicationItemDetails.add(objItem);
							}
						}
					}
					referenceData.put("communication", communication);
					referenceData.put("overviewCommunicationItemDetails", overviewCommunicationItemDetails);
				}
			} else {
				// WS: Get Interview Transient Fields
				//I don't think this makes sense, if refDataFile is null, then almost certainly this call also fails
				Map<Long, InterviewFieldValues> interviewFields = CommunicationWSClient.retrieveInterviewTransientFields(
						communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
						communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
						communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
						communication.getGuid(), communication.getPmgrOrderUUID()
				);
				wrapper = new CommunicationOrderEntryEditWrapper(communication, interviewFields);
				referenceData.put("communication", wrapper.getCommunication());
			}

		} else {
			wrapper = new CommunicationOrderEntryEditWrapper(communication);
			referenceData.put("communication", wrapper.getCommunication());
		}

		
		referenceData.put("orderEntryEnabled", communication.getDocument().isCommunicationOrderEntryEnabled());
		
		return new ModelAndView(getFormView(), referenceData);
	}

	private static InterviewValuesVO getInterviewItemValue(InterviewValuesVO[] items, String guid) {
		for (InterviewValuesVO item : items) {
			if (item.getGuid().equalsIgnoreCase(guid)) {
				return item;
			}
		}
		return null;
	}


}
