package com.prinova.messagepoint.controller.communication;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.*;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.brand.BrandProfile;
import com.prinova.messagepoint.model.communication.CommunicationsUtil;
import com.prinova.messagepoint.util.EmailUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationProof;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.communication.CreateCommunicationProofService;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.UserUtil;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 *
 * <AUTHOR> Team
 */
public class CommunicationContentEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(CommunicationContentEditController.class);

	public static final String REQ_PARAM_COMMUNICATION_ID 			= "communicationId";
	public static final String REQ_PARM_ACTION 						= "submittype";
	public static final String DEFAULT_DIGITAL_ORDER_EMAIL_ADDRESS	= "no_preview"; //default dummy email for digital orders (case #27818)

	public static final int 	ACTION_UPDATE = 		1;
	public static final int 	ACTION_CANCEL = 		2;
	public static final int 	ACTION_SAVE_AND_PREVIEW = 3;
	public static final int 	ACTION_PREVIOUS = 		4;
	public static final int 	ACTION_SUBMIT = 		5;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
		Communication communication = Communication.findById(communicationId);
		
		// DEBUG: Timing
		long startTime = new Date().getTime();
		
		Map<String, Object> referenceData = new HashMap<>();

		Document touchpointContext = UserUtil.getCurrentTouchpointContext();
		
		List<Zone> zones = Zone.findCommunicationZonesByDocument(touchpointContext.getId());
		Collections.sort(zones, new ZoneComparator());
		referenceData.put("zones", zones);
		referenceData.put("document", touchpointContext);
		
		referenceData.put("nodeGUID", Node.getCurrentNode().getGuid());
		referenceData.put("webDAVToken", UserUtil.getPrincipalUser().getWebDAVToken());

		boolean appliesOrderEntry = communication != null ? communication.appliesOrderEntry() : false;
		referenceData.put("orderEntryEnabled", appliesOrderEntry && touchpointContext.isCommunicationOrderEntryEnabled());
		referenceData.put("webServiceDriverEnabled", touchpointContext.isCommunicationWebServiceDriverEnabled() );
		
		CommunicationProof proof = null;

		if ( communication != null)
			proof =	communication.getLatestProof();
		referenceData.put("proof", proof);
		
		// PRE-PROOF: Favour latest proof if exists; otherwise, generate pre-proof or use existing
		long preProofId = -1L;
		List<CommunicationProof> preProofs = CommunicationProof.findPreProofsByCommunicationId(communicationId);
		if (!preProofs.isEmpty()) {
			preProofId = preProofs.get(0).getId();
			referenceData.put("preProof", preProofs.get(0));
		}
		referenceData.put("preProofId", preProofId);

		referenceData.put("isTestCenterContext", communication.isTestOrder());

		referenceData.put("debugTime_referenceData", (Float.valueOf(new Date().getTime() - startTime)/1000));
		
		referenceData.put("suppressNonEditableZones", touchpointContext.isCommunicationSuppressNonEditableZones());

		Boolean appliesVariantSelect =	(touchpointContext.isEnabledForVariation() && touchpointContext.isCommunicationAppliesTouchpointSelection()) ||
				(communication != null && communication.getTouchpointSelectionId() != null);
		referenceData.put("appliesVariantSelect", appliesVariantSelect);

		JSONObject marcieFlagsObj = SystemProperty.getMarcieKeyValues();
		marcieFlagsObj.put("content_compare_enabled", false);
		marcieFlagsObj.put("brand_check_enabled", BrandProfile.isBrandProfileConfigured(touchpointContext, null));
		referenceData.put("marcieFlags", marcieFlagsObj);
		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Zone.class, new IdCustomEditor<>(Zone.class));
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected CommunicationContentEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
		long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
		String recipientId 		= ServletRequestUtils.getStringParameter(request, TouchpointCommunicationsListController.REQ_PARAM_NEW_RECIPIENT_ID, null);
		Long localeId			= null;
		
		CommunicationContentEditWrapper wrapper = null;
		Communication communication = null;
		if ( communicationId != -1 ) {	
			communication = Communication.findById(communicationId);
			localeId = communication.getLocale() != null ?
						communication.getLocale().getId() : UserUtil.getCurrentTouchpointContext().getDefaultTouchpointLanguageLocale().getId();
			wrapper = new CommunicationContentEditWrapper(communication);
		} else if ( recipientId != null ) {
			localeId = UserUtil.getCurrentTouchpointContext().getDefaultTouchpointLanguageLocale().getId();
			wrapper = new CommunicationContentEditWrapper(recipientId, localeId);
		}

		wrapper.setLocaleId(localeId);

		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@SuppressWarnings("unchecked")
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		int action 				= ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
		boolean isEmbedded 		= ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
		boolean isReturnToList	= ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.ContentEdit);

		CommunicationContentEditWrapper wrapper = (CommunicationContentEditWrapper) commandObj;

		User requestor = UserUtil.getPrincipalUser();
		
		boolean isTestCenterContext = wrapper.getCommunication().isTestOrder();
		
		String listReturnPath = "/touchpoints/touchpoint_communications_list.form";
		if ( isTestCenterContext )
			listReturnPath = "/testing/test_center_communications_list.form";

		try {

			switch (action) {
			case (ACTION_UPDATE): {
				/**
				 * ACTION_UPDATE - Save and proof
				 */
				ServiceExecutionContext context = null;
				if (communicationId != -1)
					context = CreateOrUpdateCommunicationService.createContextForUpdateContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), wrapper.getCommunicationPDFConversionQuality(), requestor, null);
				else
					context = CreateOrUpdateCommunicationService.createContextForNewWithContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), requestor);

				Service updateContentService = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
				updateContentService.execute(context);

				analyticsEvent.setAction(Actions.Update);

				if (!context.getResponse().isSuccessful()) {
					log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return showForm(request, response, errors);
				} else {

					Communication communication = ((List<Communication>)context.getResponse().getResultValueBean()).get(0);

					if ( !communication.isDigitalOrder() || (communication.isDigitalOrder() && wrapper.getEmail() != null && EmailUtil.isValidEmailAddress(wrapper.getEmail())) ) {

						// PROOF
						String email = communication.isDigitalOrder() && wrapper.getEmail() != null && EmailUtil.isValidEmailAddress(wrapper.getEmail()) ? wrapper.getEmail() : null;
						ServiceExecutionContext proofContext = CreateCommunicationProofService.createContextForProof(communication, email, UserUtil.getPrincipalUser(), "Communication Content: Save");
						Service proofService = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
						proofService.execute(proofContext);
						if (!proofContext.getResponse().isSuccessful()) {
							log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
							ServiceResponseConverter.convertToSpringErrors(proofContext.getResponse(), errors);
							return showForm(request, response, errors);
						}
					}

					// RETURN: PIK exit or Order List
					if ( isEmbedded && !isReturnToList ) {
						Map<String, Object> params = new HashMap<>();
						params.put("order_guid", communication.getGuid());

						String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
						if ( returnUrl != null )
							params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);

						return new ModelAndView(new RedirectView(request.getContextPath() + "/signout"), params);
					} else {
						return new ModelAndView(new RedirectView(request.getContextPath() + listReturnPath), getSuccessViewParams(request,wrapper,true));
					}

				}
			}
			case (ACTION_PREVIOUS): {
				/**
				 * ACTION_PREVIOUS
				 */
				ServiceExecutionContext context = null;
				if (communicationId != -1)
					context = CreateOrUpdateCommunicationService.createContextForUpdateContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), wrapper.getCommunicationPDFConversionQuality(), requestor, null);
				else
					context = CreateOrUpdateCommunicationService.createContextForNewWithContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), requestor);

				Service updateContentService = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
				updateContentService.execute(context);

				analyticsEvent.setAction(Actions.Previous);

				if (!context.getResponse().isSuccessful()) {
					log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(request.getContextPath() + "/communication/communication_order_entry.form"), getSuccessViewParams(request,wrapper,false));
				}
			}
			case (ACTION_SAVE_AND_PREVIEW): {
				/**
				 * ACTION_SAVE_AND_PROOF
				 */
				ServiceExecutionContext saveContext = null;
				Communication communication = wrapper.getCommunication();
				CommunicationsUtil.addNewRoundTripAudit(communication, "interactive_proof");
				if (communicationId != -1)
					saveContext = CreateOrUpdateCommunicationService.createContextForUpdateContent(communication, wrapper.getZoneContentMap(), wrapper.getCommunicationPDFConversionQuality(), requestor, null);
				else
					saveContext = CreateOrUpdateCommunicationService.createContextForNewWithContent(communication, wrapper.getZoneContentMap(), requestor);

				Service updateContentService = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
				updateContentService.execute(saveContext);

				analyticsEvent.setAction(Actions.SaveAndProof);

				if (!saveContext.getResponse().isSuccessful()) {
					log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
					ServiceResponseConverter.convertToSpringErrors(saveContext.getResponse(), errors);
					return showForm(request, response, errors);
				} else {

					if( saveContext.getResponse().getResultValueBean() != null ) {
						List<Communication> communications = (List<Communication>)saveContext.getResponse().getResultValueBean();

						// PRE-PROOF: First
						ServiceExecutionContext preProofContext = CreateCommunicationProofService.createContextForPreProof(communications.get(0).getCustomerIdentifierDataValue(), communications.get(0), UserUtil.getCurrentTouchpointContext(), UserUtil.getPrincipalUser(), "CommunicationContent: Save and Proof");
						Service preProofService = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
						preProofService.execute(preProofContext);
						if ( !preProofContext.getResponse().isSuccessful() ) {
							log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
							ServiceResponseConverter.convertToSpringErrors(preProofContext.getResponse(), errors);
							return showForm(request, response, errors);
						} else {
							Map<String, Object> params = new HashMap<>();

							if (isEmbedded)
								params.put(CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, isEmbedded);
							String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
							if (returnUrl != null)
								params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);
							if ( isReturnToList )
								params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, isReturnToList);

							params.put(REQ_PARAM_COMMUNICATION_ID, communications.get(0).getId());
							return new ModelAndView(new RedirectView(request.getContextPath() + "/communication/communication_content_edit.form"), params);

						}
					}
				}
			}
			case (ACTION_CANCEL): {
				analyticsEvent.setAction(Actions.Cancel);
				return new ModelAndView(new RedirectView(getSuccessView()), getSuccessViewParams(request,wrapper,true));
			}
			case (ACTION_SUBMIT): {
				/**
				 * ACTION_SUBMIT - Save, release/activate, proof, return
				 */
				ServiceExecutionContext context = null;
				if (communicationId != -1)
					context = CreateOrUpdateCommunicationService.createContextForUpdateContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), wrapper.getCommunicationPDFConversionQuality(), requestor, null);
				else
					context = CreateOrUpdateCommunicationService.createContextForNewWithContent(wrapper.getCommunication(), wrapper.getZoneContentMap(), requestor);

				Service updateContentService = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
				updateContentService.execute(context);

				analyticsEvent.setAction(Actions.Submit);
				if (!context.getResponse().isSuccessful()) {

					log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return showForm(request, response, errors);

				} else {

					Communication communication = ((List<Communication>)context.getResponse().getResultValueBean()).get(0);
					ConfigurableWorkflow workflow = wrapper.getCommunication().getQualifiedConfigurableWorkflow();

					if ( workflow != null ) {

						// ACTIVATE/RELEASE FOR APPROVAL
						List<Communication> communications = new ArrayList<>();
						communications.add(communication);
						ServiceExecutionContext workflowContext = WorkflowReleaseForApprovalService.createContext(requestor, "Order submission", communications.toArray(new Communication[]{}));

						Service service = MessagepointServiceFactory.getInstance().lookupService(WorkflowReleaseForApprovalService.SERVICE_NAME, WorkflowReleaseForApprovalService.class);
						service.execute(workflowContext);
						ServiceResponse serviceResponse = workflowContext.getResponse();
						if (!serviceResponse.isSuccessful()) {
							log.error(" unexpected exception when invoking WorkflowReleaseForApprovalService execute method");
							ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
							return showForm(request, response, errors);
						}
					}

					// PROOF
					String email = null; //default for non-digital orders
					if(communication.isDigitalOrder()) {
						email = EmailUtil.isValidEmailAddress(wrapper.getEmail()) ? wrapper.getEmail() : DEFAULT_DIGITAL_ORDER_EMAIL_ADDRESS;
					}

					ServiceExecutionContext proofContext = CreateCommunicationProofService.createContextForProof(communication, email, UserUtil.getPrincipalUser(), "Communication Content: Save and Proof");
					Service proofService = MessagepointServiceFactory.getInstance().lookupService(CreateCommunicationProofService.SERVICE_NAME, CreateCommunicationProofService.class);
					proofService.execute(proofContext);
					if ( !proofContext.getResponse().isSuccessful() ) {
						log.error(" unexpected exception when invoking CreateCommunicationProofService execute method");
						ServiceResponseConverter.convertToSpringErrors(proofContext.getResponse(), errors);
						return showForm(request, response, errors);
					}

					// RETURN: PIK exit or Order List
					if ( isEmbedded && !isReturnToList ) {
						Map<String, Object> params = new HashMap<>();
						params.put("order_guid", communication.getGuid());

						String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
						if ( returnUrl != null )
							params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);

						return new ModelAndView(new RedirectView(request.getContextPath() + "/signout"), params);
					} else {
						return new ModelAndView(new RedirectView(request.getContextPath() + listReturnPath), getSuccessViewParams(request,wrapper,true));
					}

				} // END: ACTION_SUBMIT
			}
			default:
				break;
			}
		} finally {
			analyticsEvent.send();
		}

		return null;

	}
	
	private Map<String, Object> getSuccessViewParams(HttpServletRequest request, CommunicationContentEditWrapper wrapper, boolean returnToList) {
		Map<String, Object> params = new HashMap<>();
		if ( returnToList ) {
			params.put(TouchpointCommunicationsListController.REQ_PARAM_DOCUMENTID, UserUtil.getCurrentTouchpointContext().getId());
		} else {
			long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
			if (communicationId != -1L)
				params.put(REQ_PARAM_COMMUNICATION_ID, communicationId);
		}

		boolean isEmbedded = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
		if ( isEmbedded ) {
			params.put(CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, isEmbedded);
			String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
			if (returnUrl != null)
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);
			boolean isReturnToList = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);
			if (isReturnToList)
				params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, isReturnToList);
		}

		return params;
	}		

}