package com.prinova.messagepoint.controller.communication;

import java.io.Serializable;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.interview.InterviewFieldValues;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class CommunicationOrderEntryEditWrapper implements Serializable {

	private static final long serialVersionUID = -7357384022140528877L;
	
	private static final Log log = LogUtil.getLog(CommunicationOrderEntryEditWrapper.class);

	private Communication 						communication;
	private List<CommunicationOrderEntryItem>	orderEntryItemsList;
	private List<String>						orderEntryItemsDefIdList		= new ArrayList<>();
	private List<String>						orderEntryItemsIdList			= new ArrayList<>();
	private List<String>						orderEntryItemsConnectorList	= new ArrayList<>();
	private List<Boolean>						orderEntryItemsDisplayed		= new ArrayList<>();
	private List<String>						orderEntryItemsMenuItems		= new ArrayList<>();
	private List<String>						orderEntryUploadFilePathList	= new ArrayList<>();
	
	private String								wrapperInitDebugTime;
	private boolean useBetaRenderer;

	public CommunicationOrderEntryEditWrapper(Document document, CacheDataService cacheDataService) {
		super();
		initNewWrapper(document, null, cacheDataService);
	}

	public CommunicationOrderEntryEditWrapper(CacheDataService cacheDataService) {
		super();
		initNewWrapper(null, null, cacheDataService);
	}


	public CommunicationOrderEntryEditWrapper(Document document, Communication newCommunication, CacheDataService cacheDataService) {
		super();
		initNewWrapper(document, newCommunication, cacheDataService);
	}

	public CommunicationOrderEntryEditWrapper(long communicationId, CacheDataService cacheDataService) {
		super();
		Communication communication = Communication.findById(communicationId);
		initNewWrapper(null, communication, cacheDataService);
	}


	public void initNewWrapper(Document document, Communication newCommunication, CacheDataService cacheDataService) {
		
		// DEBUG: Timing
		long startTime = new Date().getTime();
		
		if ( newCommunication == null )
			newCommunication = new Communication();
		
		if ( document == null ) 
			document = UserUtil.getCurrentTouchpointContext();

		List<CommunicationOrderEntryItem> orderEntryItemsList = new ArrayList<>();

		if ( document.isCommunicationOrderEntryEnabled()) {
			if(newCommunication.getInterviewConfiguration() == null) {
				if(document.isCommunicationUseBeta()) {
					String interviewConfiguration = cacheDataService.getInterviewDefinitionFile(document);
					if (interviewConfiguration != null) {
						newCommunication.setInterviewConfiguration(interviewConfiguration);
					} else {
						log.error("ERROR: Unable to retrieve Interview definition from Bundle. Make sure you have created a delivery event");
					}
				} else {
					String interviewConfiguration = cacheDataService.getInterviewData(document.getGuid());
					if (interviewConfiguration != null) {
						newCommunication.setInterviewConfiguration(interviewConfiguration);
					}
                }
			}

			if(!document.isCommunicationUseBeta()) {
				List<CommunicationOrderEntryItemDefinition> docOrderEntryDefs = document.getCommunicationOrderEntryItemDefinitionsInOrder();
				Set<CommunicationOrderEntryItem> orderEntryItemsSet = new HashSet<>();
				for (CommunicationOrderEntryItemDefinition currentDef : docOrderEntryDefs) {
					if (currentDef.getTypeId() == MetadataFormItemType.ID_HEADER || currentDef.getTypeId() == MetadataFormItemType.ID_DIVIDER) {
						continue;
					}
					CommunicationOrderEntryItem newOrderEntryItem = new CommunicationOrderEntryItem();

					newOrderEntryItem.setMetadataDefinitionId(currentDef.getId());
					newOrderEntryItem.setCommunication(newCommunication);
					newOrderEntryItem.setDocument(document);

					newOrderEntryItem.setName(currentDef.getName());
					newOrderEntryItem.setDescription(currentDef.getDescription());
					newOrderEntryItem.setOrder(currentDef.getOrder());
					newOrderEntryItem.setIsPrimaryDriverEntry(currentDef.getIsPrimaryDriverEntry());
					newOrderEntryItem.setIsIndicatorEntry(currentDef.getIsIndicatorEntry());
					newOrderEntryItem.setDataElementVariable(currentDef.getDataElementVariable());
					newOrderEntryItem.setTypeId(currentDef.getTypeId());
					newOrderEntryItem.setDataPrivacyTypeId(currentDef.getDataPrivacyTypeId());
					newOrderEntryItem.setRepeatingDataTypeId(currentDef.getRepeatingDataTypeId());

					newOrderEntryItem.setIsManadatory(currentDef.getIsManadatory());
					newOrderEntryItem.setIsLockedForEdit(currentDef.getIsLockedForEdit());
					newOrderEntryItem.setDisplayTriggerValues(currentDef.getDisplayTriggerValues());
					newOrderEntryItem.setRegexValidation(currentDef.getRegexValidation());
					newOrderEntryItem.setParentItemOrder(currentDef.getParentItemOrder());

					newOrderEntryItem.setMenuValueItems(currentDef.getMenuValueItems());

					newOrderEntryItem.setWebServiceRefreshTypeId(currentDef.getWebServiceRefreshTypeId());
					newOrderEntryItem.setPrimaryConnector(currentDef.getPrimaryConnector());

					if (currentDef.getConnectorVariableMap() != null && !currentDef.getConnectorVariableMap().isEmpty())
						for (String currentConnector : currentDef.getConnectorVariableMap().keySet())
							newOrderEntryItem.getConnectorVariableMap().put(currentConnector, currentDef.getConnectorVariableMap().get(currentConnector));

					orderEntryItemsList.add(newOrderEntryItem);
					orderEntryItemsSet.add(newOrderEntryItem);

					orderEntryItemsDefIdList.add(String.valueOf(currentDef.getId()));
					orderEntryItemsIdList.add("0");
					orderEntryItemsConnectorList.add(currentDef.getPrimaryConnector());
					orderEntryItemsDisplayed.add(false);
					orderEntryItemsMenuItems.add(currentDef.getMenuValueItemsListAsString());

					newOrderEntryItem.setInputValidationTypeId(currentDef.getInputValidationTypeId());
					newOrderEntryItem.setFieldMaxLength(currentDef.getFieldMaxLength());
					newOrderEntryItem.setFieldSizeTypeId(currentDef.getFieldSizeTypeId());
					newOrderEntryItem.setDefaultDateValueToTodaysDate(currentDef.isDefaultDateValueToTodaysDate());
					newOrderEntryItem.setUniqueValue(currentDef.isUniqueValue());
					if (currentDef.getApplicableSelections() != null) {
						newOrderEntryItem.getApplicableSelections().clear();
						newOrderEntryItem.getApplicableSelections().addAll(currentDef.getApplicableSelections());
					}


					// Set the default value
					boolean isDefaultValueSet = currentDef.getDefaultInputValue() != null && !currentDef.getDefaultInputValue().isEmpty();
					if (isDefaultValueSet) {
						if (currentDef.getTypeId() == MetadataFormItemType.ID_TEXT || currentDef.getTypeId() == MetadataFormItemType.ID_TEXTAREA) {
							String textDefaultInput = currentDef.getDefaultInputValue();
							if (textDefaultInput != null && !textDefaultInput.isEmpty()) {
								// If TODAYS_DATE appears, replace it with today's date in format: ddMMMyyyy
								SimpleDateFormat persistedDF = new SimpleDateFormat("ddMMMyyyy");
								textDefaultInput = textDefaultInput.replaceAll("TODAYS_DATE", persistedDF.format(DateUtil.today()));
								if (currentDef.getTypeId() == MetadataFormItemType.ID_TEXT && currentDef.isUniqueValue()) {
									// Unique value for default value
									textDefaultInput = CommunicationOrderEntryItem.getDefaultValueForUniqueDefinitionItem(document, textDefaultInput, currentDef, newOrderEntryItem);
								}
							}
							newOrderEntryItem.setValue(textDefaultInput);
						} else if (currentDef.getTypeId() == MetadataFormItemType.ID_SELECT_MENU) {
							int defaultValueIdx = Integer.valueOf(currentDef.getDefaultInputValue());

							if (!currentDef.getMenuValueItemsList().isEmpty()) {
								if (currentDef.getMenuValueItemsList().size() > defaultValueIdx) {
									newOrderEntryItem.setValue(currentDef.getMenuValueItemsList().get(defaultValueIdx));
								} else {
									newOrderEntryItem.setValue(currentDef.getMenuValueItemsList().get(0));
								}
							}

						} else if (currentDef.getTypeId() == MetadataFormItemType.ID_MULTISELECT_MENU) {
							StringBuilder valueStr = new StringBuilder();
							for (String defaultValue : currentDef.getDefaultInputValue().split(",")) {
								int defaultValueIdx = Integer.valueOf(defaultValue);
								if (valueStr.length() > 0) {
									valueStr.append(",");
								}
								valueStr.append(currentDef.getMenuValueItemsList().get(defaultValueIdx));
							}
							newOrderEntryItem.setValue(valueStr.toString());
						}

						if (currentDef.getIsPrimaryDriverEntry())
							newCommunication.setCustomerIdentifier(newOrderEntryItem.getValue());
					}

					if (currentDef.getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR) {
						if (currentDef.isDefaultDateValueToTodaysDate()) {
							newOrderEntryItem.setDateStrInput(DateUtil.formatDate(DateUtil.today()));
						} else {
							try {
								if (currentDef.getDefaultInputValue() != null) {
									SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
									Date date = persistedDF.parse(currentDef.getDefaultInputValue());

									Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
									SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
									newOrderEntryItem.setDateStrInput(viewDF.format(date));
								}
							} catch (ParseException e) {
								log.error("ERROR: Unable to parse metadata persisted date: " + e);
							}
						}
					}

					if (currentDef.getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR) {
						try {
							if (currentDef.getDefaultInputValue() != null) {
								SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
								Date date = persistedDF.parse(currentDef.getDefaultInputValue());

								Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
								SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
								newOrderEntryItem.setDateStrInput(viewDF.format(date));
							}
						} catch (ParseException e) {
							log.error("ERROR: Unable to parse metadata persisted date: " + e);
						}
					}

					if (currentDef.getTypeId() == MetadataFormItemType.ID_FILE) {
						newOrderEntryItem.setUploadedFileTypeId(currentDef.getUploadedFileTypeId());
						newOrderEntryItem.setUploadedFileDataSource(currentDef.getUploadedFileDataSource());
						if (newOrderEntryItem.getUploadedFile() != null) {
							String uploadFilePath = String.valueOf(newOrderEntryItem.getId()) + "|" + newOrderEntryItem.getUploadedFile().getId() + "|" + newOrderEntryItem.getUploadedFile().getFileName();
							orderEntryUploadFilePathList.add(uploadFilePath);
						} else {
							orderEntryUploadFilePathList.add("");
						}
					} else {
						orderEntryUploadFilePathList.add("");
					}
				}
				newCommunication.setOrderEntryItems(orderEntryItemsSet);
			}

			newCommunication.setDocument( document );
			newCommunication.applyDefaultVariant();

		}

		this.communication 			= newCommunication;
		this.setOrderEntryItemsList(orderEntryItemsList);
		this.setUseBetaRenderer(document.isCommunicationUseBeta());

		wrapperInitDebugTime = String.valueOf((Float.valueOf(new Date().getTime() - startTime)/1000));
		
	}

	public CommunicationOrderEntryEditWrapper(Communication communication) {
		this(communication, null);
	}

	public CommunicationOrderEntryEditWrapper(Communication communication, Map<Long, InterviewFieldValues> interviewFields) {
		super();

		// DEBUG: Timing
		long startTime = new Date().getTime();

		this.communication = communication;
		this.setUseBetaRenderer(communication.getDocument().isCommunicationUseBeta());
		if (!communication.getDocument().isCommunicationUseBeta()) {
			this.setOrderEntryItemsList(communication.getOrderEntryItemsInOrder());
			for (CommunicationOrderEntryItem currentItem : this.orderEntryItemsList) {
				if (currentItem.getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR &&
						currentItem.getValue() != null && !currentItem.getValue().isEmpty()) {
					currentItem.setDateStrInput(currentItem.getValue());
					try {
						SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM-dd");
						Date date = persistedDF.parse(currentItem.getValue());

						Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
						SimpleDateFormat viewDF = new SimpleDateFormat(DateUtil.DATE_FORMAT, DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
						currentItem.setDateStrInput(viewDF.format(date));
					} catch (ParseException e) {
						log.error("ERROR: Unable to parse order entry persisted date: " + e);
					}
				} else if (currentItem.getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR &&
						currentItem.getValue() != null && !currentItem.getValue().isEmpty()) {
					currentItem.setDateStrInput(currentItem.getValue());
					try {
						SimpleDateFormat persistedDF = new SimpleDateFormat("yyyy-MM");
						Date date = persistedDF.parse(currentItem.getValue());

						Locale locale = new Locale(UserUtil.getAppLangCodeForPrincipal());
						SimpleDateFormat viewDF = new SimpleDateFormat("MMM yyyy", DateUtil.formatAbbricationMonths(new DateFormatSymbols(locale)));
						currentItem.setDateStrInput(viewDF.format(date));
					} catch (ParseException e) {
						log.error("ERROR: Unable to parse order entry persisted date: " + e);
					}
				} else if (currentItem.getTypeId() == MetadataFormItemType.ID_FILE) {
//				if(currentItem.getUploadedFile() != null){
//					String uploadFilePath = String.valueOf(currentItem.getId()) + "|" + currentItem.getUploadedFile().getId() + "|" + currentItem.getUploadedFile().getFileName() + "|" + currentItem.get;
//					orderEntryUploadFilePathList.add(uploadFilePath);
//				}else{
//					orderEntryUploadFilePathList.add("");
//				}
					String uploadFilePath = String.valueOf(currentItem.getFileName()) + "|" + currentItem.getFileContentType();
					orderEntryUploadFilePathList.add(uploadFilePath);
				} else {
					orderEntryUploadFilePathList.add("");
				}
			}
		}
		wrapperInitDebugTime = String.valueOf((Float.valueOf(new Date().getTime() - startTime) / 1000));


	}
	

	public Communication getCommunication() {
		return communication;
	}
	public void setCommunication(Communication communication) {
		this.communication = communication;
	}

	public List<CommunicationOrderEntryItem> getOrderEntryItemsList() {
		if (this.orderEntryItemsList == null) {
			this.orderEntryItemsList = new LinkedList<>();
		}

		return orderEntryItemsList;
	}

	public void setOrderEntryItemsList(List<CommunicationOrderEntryItem> orderEntryItemsList) {
		this.orderEntryItemsList = orderEntryItemsList;
		if (this.orderEntryItemsList == null) {
			this.orderEntryItemsList = new LinkedList<>();
		}
	}

	public List<String> getOrderEntryItemsDefIdList() {
		return orderEntryItemsDefIdList;
	}
	public void setOrderEntryItemsDefIdList(List<String> orderEntryItemsDefIdList) {
		this.orderEntryItemsDefIdList = orderEntryItemsDefIdList;
	}

	public List<String> getOrderEntryItemsIdList() {
		return orderEntryItemsIdList;
	}
	public void setOrderEntryItemsIdList(List<String> orderEntryItemsIdList) {
		this.orderEntryItemsIdList = orderEntryItemsIdList;
	}

	public List<String> getOrderEntryItemsConnectorList() {
		return orderEntryItemsConnectorList;
	}
	public void setOrderEntryItemsConnectorList(
			List<String> orderEntryItemsConnectorList) {
		this.orderEntryItemsConnectorList = orderEntryItemsConnectorList;
	}

	public List<Boolean> getOrderEntryItemsDisplayed() {
		return orderEntryItemsDisplayed;
	}
	public void setOrderEntryItemsDisplayed(List<Boolean> orderEntryItemsDisplayed) {
		this.orderEntryItemsDisplayed = orderEntryItemsDisplayed;
	}

	public List<String> getOrderEntryItemsMenuItems() {
		return orderEntryItemsMenuItems;
	}
	public void setOrderEntryItemsMenuItems(List<String> orderEntryItemsMenuItems) {
		this.orderEntryItemsMenuItems = orderEntryItemsMenuItems;
	}

	public List<String> getOrderEntryUploadFilePathList() {
		return orderEntryUploadFilePathList;
	}

	public void setOrderEntryUploadFilePathList(List<String> orderEntryUploadFilePathList) {
		this.orderEntryUploadFilePathList = orderEntryUploadFilePathList;
	}

	public String getWrapperInitDebugTime() {
		return wrapperInitDebugTime;
	}
	public void setWrapperInitDebugTime(String wrapperInitDebugTime) {
		this.wrapperInitDebugTime = wrapperInitDebugTime;
	}

	public boolean isUseBetaRenderer() {
		return useBetaRenderer;
	}

	public void setUseBetaRenderer(boolean useBetaRenderer) {
		this.useBetaRenderer = useBetaRenderer;
	}
}