package com.prinova.messagepoint.controller.communication;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.*;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.util.HttpRequestUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.communication.ExternalProofValidation;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.HibernateUtil;

/**
 * 
 * Prinova Inc. 1998-2011
 * 678 Belmont Ave West, Unit 201, KW, ON N2M 1N6
 * All rights reserved.
 *
 * <AUTHOR> Team
 */
public class CommunicationExternalValidationController extends MessagepointController {

	private static final Log log = LogUtil.getLog(CommunicationExternalValidationController.class);

	public static final String REQ_PARAM_INSTANCE_GUID				= "gd";
	public static final String REQ_PARAM_EXT_VALIDATION_ID			= "validationId";
	public static final String REQ_PARM_ACTION 						= "submittype";
	
	public static final int 	ACTION_APPROVE = 		1;
	public static final int 	ACTION_REJECT = 		2;
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		long extValidationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_EXT_VALIDATION_ID, -1);
		
		Map<String, Object> referenceData = new HashMap<>();
		
		ExternalProofValidation proofValidation = ExternalProofValidation.findById(extValidationId);
		
		if ( proofValidation.getDateResponseReceived() != null )
			referenceData.put("feedbackSubmitted", true);
		
		if ( proofValidation.getCommunication() == null )
			referenceData.put("orderDeleted", true);

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}

	protected Command formBackingObject(HttpServletRequest request) throws Exception {
		
		long extValidationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_EXT_VALIDATION_ID, -1);
		
		Node node = Node.findByGuid(ServletRequestUtils.getStringParameter(request, REQ_PARAM_INSTANCE_GUID, null));
		if (node != null)
			HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
		else
			HibernateUtil.getManager().openTemporarySession(null);
		
		ExternalProofValidation proofValidation = ExternalProofValidation.findById(extValidationId);

		Command command = new Command();
		if ( proofValidation != null && proofValidation.getValidationFeedback() != null )
			command.setFeedback(new String(proofValidation.getValidationFeedback(), ApplicationLanguageUtils.FILE_ENCODING));
		command.setProofValidation(proofValidation);

		return command;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		final int action 				= ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		
		int validationStatus	= 0;
		if ( action == ACTION_APPROVE )
			validationStatus = ACTION_APPROVE;
		else if ( action == ACTION_REJECT )
			validationStatus = ACTION_REJECT;

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.ExternalValidation);
		analyticsEvent.setAction(action == ACTION_APPROVE ? Actions.Approve : Actions.Reject);

		try {
			Command command = (Command) commandObj;

			if (command.getProofValidation() != null)
			{
				Node node = Node.findByGuid(ServletRequestUtils.getStringParameter(request, REQ_PARAM_INSTANCE_GUID, null));
				if (node != null)
					HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
				else
					HibernateUtil.getManager().openTemporarySession(null);

				command.getProofValidation().setStatusId(validationStatus);
				command.getProofValidation().setValidationFeedback(command.getFeedback().getBytes());

				ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForExternalValidation(command.getProofValidation());
				Service externalValidationService = MessagepointServiceFactory.getInstance()
						.lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
				externalValidationService.execute(context);

				if (!context.getResponse().isSuccessful()) {
					log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
					ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
					return showForm(request, response, errors);
				}
			}

			return new ModelAndView(new RedirectView(request.getContextPath() + "/communication_external_validation.form"), getSuccessViewParams(request,command));
		} finally {
			analyticsEvent.send();
		}
	}
	
	private Map<String, Object> getSuccessViewParams(HttpServletRequest request, Command command) {
		Map<String, Object> params = new HashMap<>();

		String gd = ServletRequestUtils.getStringParameter(request, REQ_PARAM_INSTANCE_GUID, null);
		if (gd != null)
			params.put(REQ_PARAM_INSTANCE_GUID, gd);
		
		long extValidationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_EXT_VALIDATION_ID, -1);
		if (extValidationId != -1L)
			params.put(REQ_PARAM_EXT_VALIDATION_ID, extValidationId);

		return params;
	}	
	
	public static class Command {
		
		private ExternalProofValidation proofValidation;
		private String feedback;

		public String getFeedback() {
			return feedback;
		}
		public void setFeedback(String feedback) {
			this.feedback = feedback;
		}

		public ExternalProofValidation getProofValidation() {
			return proofValidation;
		}
		public void setProofValidation(ExternalProofValidation proofValidation) {
			this.proofValidation = proofValidation;
		}

		public String getFileResource() {
			String outputPath = proofValidation.getProof().getOutputPath();
			return HttpRequestUtil.buildResourceToken()
					.add("file", outputPath)
					.getValue();
		}
	}

}