package com.prinova.messagepoint.controller.communication;

import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.common.StaticType;
import com.prinova.messagepoint.util.ApplicationUtil;

public class CommunicationStatusFilterType extends StaticType {

	private static final long serialVersionUID = 3386698222585097656L;

	public static final int ID_WORKING_COPIES 		= 1;
	public static final int ID_ACTIVE 				= 2;

	public static final String MESSAGE_CODE_WORKING_COPIES 	= "page.label.list.filter.type.working.copy";
	public static final String MESSAGE_CODE_ACTIVE 			= "page.label.list.filter.type.active";

	public CommunicationStatusFilterType(Integer id) {
		super();
		switch (id) {
		case ID_WORKING_COPIES:
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
			break;
		case ID_ACTIVE:
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
			break;
		default:
			break;
		}
	}

	public CommunicationStatusFilterType(String name) {
		super();
		if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES))) { 
			this.setId(ID_WORKING_COPIES);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_WORKING_COPIES));
			this.setDisplayMessageCode(MESSAGE_CODE_WORKING_COPIES);
		} else if (name.trim().equals(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE))) { 
			this.setId(ID_ACTIVE);
			this.setName(ApplicationUtil.getMessage(MESSAGE_CODE_ACTIVE));
			this.setDisplayMessageCode(MESSAGE_CODE_ACTIVE);
		}
	}
	
	public static List<CommunicationStatusFilterType> listAll() {
		List<CommunicationStatusFilterType> allListFilterTypes = new ArrayList<>();

		CommunicationStatusFilterType listFilterType = null;

		listFilterType = new CommunicationStatusFilterType(ID_WORKING_COPIES);
		allListFilterTypes.add(listFilterType);
		
		listFilterType = new CommunicationStatusFilterType(ID_ACTIVE);
		allListFilterTypes.add(listFilterType);

		return allListFilterTypes;
	}
}
