package com.prinova.messagepoint.controller.communication;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.*;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.workflow.ConfigurableWorkflow;
import com.prinova.messagepoint.model.workflow.WorkflowUsageType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.communication.UpdateConnectedWorkflowAssignmentService;
import com.prinova.messagepoint.util.NestedPageUtils;

public class ConnectedWorkflowAssignmentEditController extends MessagepointController {

	private static final Log log = LogUtil.getLog(ConnectedWorkflowAssignmentEditController.class);

	public static final String REQ_PARAM_DOCUMENT_ID_PARAM 	= "documentId";

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		
		referenceData.put("connectedWorkflows", ConfigurableWorkflow.findByDocumentModelAndUsageTypes(documentId, ConfigurableWorkflow.WORKFLOW_LIBRARY_WORKFLOW, WorkflowUsageType.ID_USAGE_TYPE_CONNECTED));
		
		referenceData.put("document", Document.findById(documentId));

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Document.class, new IdCustomEditor<>(Document.class));
		binder.registerCustomEditor(ConfigurableWorkflow.class, new IdCustomEditor<>(ConfigurableWorkflow.class));
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document document = Document.findById(documentId);
		return document;
    }

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID_PARAM, -1);
		Document command = (Document)commandObj;

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.WorkflowAssignmentEdit);

		try {
			ServiceExecutionContext context = UpdateConnectedWorkflowAssignmentService.createContext(command);
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateConnectedWorkflowAssignmentService.SERVICE_NAME, UpdateConnectedWorkflowAssignmentService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();

			analyticsEvent.setAction(Actions.Save);

			if ( !serviceResponse.isSuccessful() ) {
				StringBuilder sb = new StringBuilder();
				sb.append(UpdateConnectedWorkflowAssignmentService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" Connected workflow was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			} else {
				Map<String, Object> parms = new HashMap<>();
				parms.put(REQ_PARAM_DOCUMENT_ID_PARAM, documentId);
				parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
				return new ModelAndView(new RedirectView(getSuccessView()), parms);
			}
		} finally {
			analyticsEvent.send();
		}
	}
}