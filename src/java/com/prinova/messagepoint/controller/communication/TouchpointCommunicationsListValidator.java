package com.prinova.messagepoint.controller.communication;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.util.EmailUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class TouchpointCommunicationsListValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		TouchpointCommunicationsListWrapper wrapper = (TouchpointCommunicationsListWrapper) commandObj;
		
		int action = -1;
		if (wrapper.getActionValue() != null && !wrapper.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(wrapper.getActionValue()).intValue();
		}
		
		boolean approvalWithoutValidProof = false;
		boolean approvalWithoutValidConnectedWf = false;
		if ( (action == TouchpointCommunicationsListController.ACTION_APPROVE || 
			 	action == TouchpointCommunicationsListController.ACTION_ACTIVATE ||
				action == TouchpointCommunicationsListController.ACTION_REASSIGN_ACTIVATE) &&
			 (UserUtil.getCurrentTouchpointContext().getCommunicationProductionTypeId() == Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL ||
			  UserUtil.getCurrentTouchpointContext().getCommunicationProductionTypeId() == Document.COMMUNICATION_PRODUCTION_TYPE_ON_APPROVAL_MINI_BUNDLE) ) {
			for ( Communication currentCommunication: wrapper.getSelectedList() ){
				if(currentCommunication.getDocument().getConnectedWorkflow() == null){
					approvalWithoutValidConnectedWf = true;
				}
				if ( currentCommunication.getLatestProof() == null || currentCommunication.getLatestProof().isError() || !currentCommunication.getLatestProof().isComplete() ){
					approvalWithoutValidProof = true;
				}
			}
			if(approvalWithoutValidConnectedWf){
				errors.reject("error.communication.must.assign.valid.workflow");
			}
			if ( approvalWithoutValidProof &&
							(action == TouchpointCommunicationsListController.ACTION_ACTIVATE ||
							action == TouchpointCommunicationsListController.ACTION_REASSIGN_ACTIVATE))
				errors.reject("error.communication.activation.without.proof");
			else if ( approvalWithoutValidProof && action == TouchpointCommunicationsListController.ACTION_APPROVE )
				errors.reject("error.communication.approval.without.proof");
		}

		if ( action == TouchpointCommunicationsListController.ACTION_RUN_BATCH)
		{
			if ( UserUtil.getCurrentTouchpointContext().getCompositionFileSet() == null && 
							UserUtil.getCurrentTouchpointContext().isPrintTouchpoint() && 
							!UserUtil.getCurrentTouchpointContext().isNativeCompositionTouchpoint() )
				errors.reject("error.message.composition.package.must.be.uploaded.to.run.connected.bundle");
		}
		
		if ( action == TouchpointCommunicationsListController.ACTION_SOCIALIZE ) {
			if ( wrapper.getSocializeEmail() == null || wrapper.getSocializeEmail().trim().isEmpty() ||
				 !EmailUtil.isValidEmailAddress(wrapper.getSocializeEmail().trim()) )
				errors.reject("error.message.order.socialization.email.address.invalid");
		}
		
		if ( action == TouchpointCommunicationsListController.ACTION_REQUEST_PROOF ) {
			boolean isDigitalProofRequest = false;
			if ( wrapper.getSelectedList() != null)
				for ( Communication currentCommunication: wrapper.getSelectedList() )
					if ( currentCommunication.isDigitalOrder() )
						isDigitalProofRequest = true;
			
			if ( isDigitalProofRequest	) {
				if ( wrapper.getProofEmail() == null || wrapper.getProofEmail().trim().isEmpty() ||
						 !EmailUtil.isValidEmailAddress(wrapper.getProofEmail().trim()) )
						errors.reject("error.message.order.digital.proof.email");
			}
		}
			

	}
}
