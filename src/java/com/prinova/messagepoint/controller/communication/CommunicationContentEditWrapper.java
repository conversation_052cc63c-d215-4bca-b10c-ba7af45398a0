package com.prinova.messagepoint.controller.communication;

import java.io.Serializable;
import java.util.*;

import com.prinova.messagepoint.model.communication.CommunicationZoneContentAssociation;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.util.UserUtil;

public class CommunicationContentEditWrapper implements Serializable {

	private static final long serialVersionUID = -7357384022140528877L;
	private static final Log log = LogUtil.getLog(CommunicationContentEditWrapper.class);

	private Communication 			communication;
	
	private String 					actionValue;

	private Map<Long, ContentVO> 	zoneContentMap 	= new HashMap<>();

	private Long					localeId;
	private String					email;
	
	private String					wrapperInitDebugTime;

	private Integer					communicationPDFConversionQuality = 70;
	
	public CommunicationContentEditWrapper() {
		super();
	}
	
	public CommunicationContentEditWrapper(Communication communication) {
		super();
		
		// DEBUG: Timing
		long startTime = new Date().getTime();
		
		this.communication = communication;
		
		if ( communication.getLocale() == null )
			communication.setLocale( UserUtil.getCurrentTouchpointContext().getDefaultTouchpointLanguage().getMessagepointLocale() );
		
		Document documentContext = UserUtil.getCurrentTouchpointContext();
		List<Zone> communicationZones = Zone.findCommunicationZonesByDocument(documentContext.getId());
		
		// SYNC ZONE MAP: Add/remove zones based on current document state
		Set<CommunicationZoneContentAssociation> existingZoneContentAssociations = this.communication.getZoneContentAssociations();
		Set<Zone> existingZones = new HashSet<>();
		for (CommunicationZoneContentAssociation zoneContentAssociation : existingZoneContentAssociations)
		{
			Zone currentZone = zoneContentAssociation.getZone();
			if ( !communicationZones.contains(currentZone) )
				existingZoneContentAssociations.remove(zoneContentAssociation);
			else
				existingZones.add(currentZone);
		}
		for (Zone currentZone: communicationZones) {
			if ( !existingZones.contains(currentZone) ) {
				CommunicationZoneContentAssociation ca = new CommunicationZoneContentAssociation();
				ca.setCommunication(communication);
				ca.setZone(currentZone);

				existingZoneContentAssociations.add(ca);
			}
		}

		populateContentVO(communication, documentContext, existingZoneContentAssociations);

		this.communicationPDFConversionQuality = documentContext.getCommunicationPDFConversionQuality();
		
		wrapperInitDebugTime = String.valueOf((Float.valueOf(new Date().getTime() - startTime)/1000));
	}

	public CommunicationContentEditWrapper(String recipientId, long localeId) {
		super();
		
		Communication newCommunication = new Communication();
		newCommunication.setCustomerIdentifier(recipientId);
		
		Document documentContext = UserUtil.getCurrentTouchpointContext();
		List<Zone> communicationZones = Zone.findCommunicationZonesByDocument(documentContext.getId());
		
		// INIT ZONE MAP: Add zones based on current document state
		Set<CommunicationZoneContentAssociation> newZoneContentAssociations = new HashSet<>();
		for ( Zone currentZone: communicationZones ) {
			CommunicationZoneContentAssociation ca = new CommunicationZoneContentAssociation();
			ca.setCommunication(newCommunication);
			ca.setZone(currentZone);

			newZoneContentAssociations.add(ca);
		}
		newCommunication.setZoneContentAssociations(newZoneContentAssociations);
		newCommunication.setDocument(documentContext);
		
		populateContentVO(communication, documentContext, newZoneContentAssociations);

		this.communicationPDFConversionQuality = documentContext.getCommunicationPDFConversionQuality();

		this.communication = newCommunication;
	}
	
	private ContentVO initContentVO(CommunicationZoneContentAssociation zoneContentAssociation) {
		
		ContentVO contentVO = new ContentVO();
		
		if ( zoneContentAssociation.getReferencingImageLibrary() != null ) {
			contentVO = zoneContentAssociation.getReferencingImageLibrary().getDefaultProductionImageLibrary(null);
			contentVO.setUseImageLibrary( true );
			contentVO.setCustom( false );
			contentVO.setImageLibraryId( zoneContentAssociation.getReferencingImageLibrary().getId() );
			contentVO.setImageLibraryObjectType( zoneContentAssociation.getReferencingImageLibrary().getObjectType() );
			contentVO.setImageLibraryName( zoneContentAssociation.getReferencingImageLibrary().getName() );
		}

		if ( zoneContentAssociation.getContent() != null ) {
			Content content = zoneContentAssociation.getContent();
			contentVO.setContentId( content.getId() );
			contentVO.setContentSupplier(() -> content.getContent(zoneContentAssociation.getZone().getDocument(), zoneContentAssociation.getCommunication().getLocale()) );
			contentVO.setEncodedContent( content.getEncodedContent() );
			contentVO.setImageLocation( content.getImageLocation() );
			contentVO.setImageName( content.getImageName() );
			contentVO.setShared( false );
			contentVO.setCustom( true );
			contentVO.setAppliedImageFilename( content.getAppliedImageFilename() );
			contentVO.setImageUploadedDate( content.getImageUploadedDate() );
		} else {
			contentVO.setShared( false );
			contentVO.setCustom( true );
		}
		
		return contentVO;
	}

	public Communication getCommunication() {
		return communication;
	}
	public void setCommunication(Communication communication) {
		this.communication = communication;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public Map<Long, ContentVO> getZoneContentMap() {
		return zoneContentMap;
	}
	public void setZoneContentMap(Map<Long, ContentVO> zoneContentMap) {
		this.zoneContentMap = zoneContentMap;
	}

	public Long getLocaleId() { return localeId; }
	public void setLocaleId(Long localeId) { this.localeId = localeId; }

	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}

	public String getWrapperInitDebugTime() {
		return wrapperInitDebugTime;
	}
	public void setWrapperInitDebugTime(String wrapperInitDebugTime) {
		this.wrapperInitDebugTime = wrapperInitDebugTime;
	}

	public Integer getCommunicationPDFConversionQuality() { return communicationPDFConversionQuality; }
	public void setCommunicationPDFConversionQuality(Integer communicationPDFConversionQuality) { this.communicationPDFConversionQuality = communicationPDFConversionQuality; }

	private void populateContentVO(Communication communication, Document documentContext,
								   Set<CommunicationZoneContentAssociation> zoneContentAssociations) {
		// POPULATE VOs
		for (CommunicationZoneContentAssociation zoneContentAssociation : zoneContentAssociations) {
			ContentVO currentVO = initContentVO(zoneContentAssociation);
			zoneContentMap.put(zoneContentAssociation.getZone().getId(), currentVO);
		}
		// load zone content to map
		if ( documentContext.isCommunicationZoneContentTransient() ) {
			retrieveTransientZoneContent(communication);
		}
	}
	
	protected boolean retrieveTransientZoneContent(Communication communication) {
		Map<Long, String> transientZoneContent = null;
		if ( !communication.getDocument().isCommunicationWebServiceCompositionResultsEnabled() ) {
			log.error( "No WS server configured to retrieve transient zone content for communicationId:" + String.valueOf(communication.getId()) );
			return false;		// no destination server configured to send transient content
		}
		try {
			transientZoneContent = CommunicationWSClient.retrieveZoneContentData( communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(), 
					communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(), 
					communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
					communication.getGuid(),
					((communication.getDocument().isCommunicationUseBeta() && communication.getGuid() != null && !communication.getGuid().equalsIgnoreCase(communication.getPmgrOrderUUID())) ?
							communication.getPmgrOrderUUID() : null));
			if (transientZoneContent != null) {
				for ( Long key : transientZoneContent.keySet() ) {
					Map<Long, String> finalTransientZoneContent = transientZoneContent;
					zoneContentMap.get(key).setContentSupplier( () -> finalTransientZoneContent.get(key) );
				}
			}
		} catch (Exception e) {
			log.error("Download transient content zone exception: ", e);
			return false;
		}
		return true;
	}

}