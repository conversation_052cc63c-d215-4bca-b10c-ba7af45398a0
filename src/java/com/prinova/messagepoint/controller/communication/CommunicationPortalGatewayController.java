package com.prinova.messagepoint.controller.communication;

import java.net.URI;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.interview.InterviewFieldValues;
import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.controller.communication.connected.AbstractDataElementDeserializer;
import com.prinova.messagepoint.controller.communication.connected.AbstractDataSourceDeserializer;
import com.prinova.messagepoint.controller.communication.connected.CommunicationOrderControllerUtil;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.admin.SecuritySettings;
import com.prinova.messagepoint.model.communication.CommunicationsUtil;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.platform.services.communication.CommunicationUnlockModelService;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.security.StringXSSEditor;

import java.util.stream.Collectors;

public class CommunicationPortalGatewayController extends MessagepointController {
	
	private static final Log log = LogUtil.getLog(CommunicationPortalGatewayController.class);

	public static final String REQ_PARAM_TOUCHPOINT_GUID		= "touchpoint_guid";
	public static final String REQ_PARAM_ORDER_GUID				= "order_guid";
	public static final String REQ_PARAM_EMBEDDED				= "embedded";
	public static final String REQ_PARAM_TAG_CLOUD				= "tag_cloud";
	public static final String REQ_PARAM_RETURN_URL				= "return_url";
	public static final String REQ_PARAM_RETURN_TO_LIST			= "return_to_list";
	public static final String REQ_PARAM_REASSIGN				= "reassign";
	public static final String REQ_PARAM_DOCUMENT_ID	        = "documentId";

	private CacheDataService _cacheDataService;

	public CommunicationPortalGatewayController(CacheDataService cacheDataService)
	{
		_cacheDataService = cacheDataService;
	}

	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		Map<String, Object> referenceData = new HashMap<>();

		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
	}
	
	@SuppressWarnings("unchecked")
	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
		
		Map<String, Object> referenceData = new HashMap<>();

		String touchpointGUID 	= ServletRequestUtils.getStringParameter(request, REQ_PARAM_TOUCHPOINT_GUID, null);
		String orderGUID 		= ServletRequestUtils.getStringParameter(request, REQ_PARAM_ORDER_GUID, null);
		String tagCloud 		= ServletRequestUtils.getStringParameter(request, REQ_PARAM_TAG_CLOUD, null);
		String returnUrl 		= CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
		Boolean returnToList	= ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_RETURN_TO_LIST, false);
		boolean reassign		= ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_REASSIGN, false);
		
		boolean isMissingTouchpointGUID 	= false;
		boolean isUnknownTouchpoint			= false;
		Boolean hasUnknownConnectors		= false;
		boolean isUnknownUser				= false;
		boolean isTouchpointVisibleToUser	= false;
		Boolean hasReturnURL				= false;
		boolean isReassignPermRejected		= false;
		boolean isActiveOrderEdit			= false;
		boolean isReassignRejected			= false;
		boolean isAccessViolation			= false;
		boolean isOrderGuidLinkedToAnotherTP= false;
		boolean isOrderGUIDEmpty 			= false;
		boolean hasErrorOnSaveToBackend     = false;
		List<String> unknownConnectors 		= new ArrayList<>();

		Document document = null;
		if ( touchpointGUID == null ) {
			isMissingTouchpointGUID = true;
		} else {
			document = Document.findByGuid(touchpointGUID);
			if ( document == null )
				isUnknownTouchpoint = true;
			else
				referenceData.put("document", document);
		}
		
		User requestor = UserUtil.getPrincipalUser();
		if ( requestor == null ) {
			isUnknownUser = true;
		} else {

			SecuritySettings securitySettings = SecuritySettings.findForCurrentDomain();
			if (securitySettings != null && securitySettings.getSessionExpireMins() != null) {
				int sessionExpireMins = Integer.valueOf(securitySettings.getSessionExpireMins());
				if (sessionExpireMins > 0)
					request.getSession().setMaxInactiveInterval(sessionExpireMins * 60); //in seconds
			}

			if (document != null && document.isVisible())
				isTouchpointVisibleToUser = true;

			referenceData.put("user", requestor);

			if (tagCloud != null)
				tagCloud = tagCloud.replace(",", " ");
			referenceData.put("tags", tagCloud != null ? tagCloud : ApplicationUtil.getMessage("page.label.none"));

			Communication communication = null;
			if(orderGUID != null && orderGUID.isEmpty()){
				isOrderGUIDEmpty = true;
			}
			if (orderGUID == null) {
				orderGUID = RandomGUID.getGUID();
			}

			if (!isOrderGUIDEmpty && document != null && isTouchpointVisibleToUser) {

				Map<String, Object> params = new HashMap<>();

				HashMap<String, String> contextAttr = new HashMap<>();
				contextAttr.put(UserUtil.CONTEXT_KEY_TOUCHPOINT_CONTEXT, String.valueOf(document.getId()));
				UserUtil.updateUserContextAttributes(contextAttr);

				communication = Communication.findByGuid(orderGUID);

				if (communication != null) {

					if (communication.getDocument().getId() != document.getId()) {
						isOrderGuidLinkedToAnotherTP = true;
						referenceData.put("isOrderGuidLinkedToAnotherTP", isOrderGuidLinkedToAnotherTP);
						referenceData.put("isMissingTouchpointGUID", isMissingTouchpointGUID);
						referenceData.put("isUnknownTouchpoint", isUnknownTouchpoint);
						referenceData.put("isUnknownUser", isUnknownUser);
						referenceData.put("isTouchpointVisibleToUser", isTouchpointVisibleToUser);
						addInfoToReferenceData(requestor, referenceData);
						return new ModelAndView(getFormView(), referenceData);
					}

					referenceData.put("communication", communication);

					// EXISTING ORDER
					params.put(TouchpointCommunicationsListController.REQ_PARAM_COMMUNICATION_ID, communication.getId());

					log.info("Starting to update the communication with guid" + communication.getGuid() + " and document " + document.getName());

					// REQUESTOR VALUES: Fill in supplied connector/value pairs
					SandboxFile refDataFile = CommunicationWSClient.retrieveRefDataFile(
							communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
							communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
							communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
							communication.getGuid(), communication.getPmgrOrderUUID()
					);
					if (refDataFile != null) {

						log.info("Ref 6 file was found");

						JSONObject communicationJsonBackend = new JSONObject(new String(refDataFile.getFileContent()));
						communication.setInterviewValuesJson(communicationJsonBackend);
						ObjectMapper objectMapper = new ObjectMapper();
						InterviewFieldValues[] interviewFields = objectMapper.readValue(communicationJsonBackend.get(Constants.Key_InterviewValues).toString(), InterviewFieldValues[].class);
						Map<Long, InterviewFieldValues> interviewFieldsMap = null;
						if (interviewFields != null) {
							interviewFieldsMap = Arrays.stream(interviewFields).collect(
									Collectors.toMap(item -> (item.getId() > 0 ? item.getId() : UUID.randomUUID().getMostSignificantBits() & Long.MAX_VALUE), item -> item)
							);
						}

						CommunicationOrderEntryEditWrapper wrapper = new CommunicationOrderEntryEditWrapper(communication, interviewFieldsMap);
						if(communication.getInterviewConfiguration() != null) {
							setDataValues(request, communication, wrapper, hasUnknownConnectors, unknownConnectors, true, interviewFields);
						}
					}

					if (unknownConnectors.isEmpty()) {

						// UPDATE ORDER
						ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForUpdateOrderEntry(communication, false, requestor);
						Service updateContentService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
						updateContentService.execute(context);

						if (!context.getResponse().isSuccessful()) {
							if(context.getResponse().hasMessages()){
								if("SENDING_COMMUNICATION_DEFINITION_FAILED".equalsIgnoreCase(context.getResponse().getResultStatus().getReturnCode())){
									hasErrorOnSaveToBackend = true;
								}
							} else {
								log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
								ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
								return showForm(request, response, errors);
							}
						}

						// REASSIGN
						if (communication.isWIP() && communication.getAssigneeId() != null && communication.getAssigneeId() != requestor.getId()) {

							boolean reassignPermission = UserUtil.isPermissionGranted(Permission.ROLE_LICENCED_COMMUNICATIONS_REASSIGN);
							if (reassign && reassignPermission) {
								List<Communication> communications = new ArrayList<>();
								communications.add(communication);
								context = CommunicationUnlockModelService.createReassignContext(communications, requestor.getId(), requestor.getId(),
										"Connected Gateway: Reassign on access to order assigned to another user", false);
								Service reassignService = MessagepointServiceFactory.getInstance().lookupService(CommunicationUnlockModelService.SERVICE_NAME, CommunicationUnlockModelService.class);
								reassignService.execute(context);

								if (!context.getResponse().isSuccessful()) {
									log.error(" unexpected exception when invoking CommunicationUnlockModelService execute method");
									ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
									return showForm(request, response, errors);
								}
							} else if (reassign && !reassignPermission) {
								isReassignPermRejected = true;
								isAccessViolation = true;
							} else if (!reassign) {
								isReassignRejected = true;
								isAccessViolation = true;
							}

						} else if (!communication.isWIP()) {

							// INVALID ACCESS
							isActiveOrderEdit = true;
							isAccessViolation = true;

						}

					}

				} else {
					log.info("New communication order with guid" + orderGUID + " and document " + document.getName() + " was created ");
					// NEW ORDER: New GUID
					communication = new Communication();
					communication.setGuid(orderGUID);
					communication.setDocument(document);

					if (tagCloud != null)
						communication.setMetatags(tagCloud);

					// REQUESTOR VALUES: Fill in supplied connector/value pairs
					CommunicationOrderEntryEditWrapper wrapper = new CommunicationOrderEntryEditWrapper(document, communication, this._cacheDataService);
					if(communication.getInterviewConfiguration() != null) {
						setDataValues(request, communication, wrapper, hasUnknownConnectors, unknownConnectors, false, new InterviewFieldValues[]{});
					}

					if (unknownConnectors.isEmpty()) {

						// CREATE ORDER
						ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForNew(communication, requestor);
						Service updateContentService = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
						updateContentService.execute(context);

						if (!context.getResponse().isSuccessful()) {
							if(context.getResponse().hasMessages()){
								if("SENDING_COMMUNICATION_DEFINITION_FAILED".equalsIgnoreCase(context.getResponse().getResultStatus().getReturnCode())){
                                    hasErrorOnSaveToBackend = true;
								}
							} else {
								log.error(" unexpected exception when invoking CreateOrUpdateCommunicationService execute method");
								ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
								return showForm(request, response, errors);
							}
						} else {
							if (context.getResponse().getResultValueBean() != null) {
								communication = ((List<Communication>) context.getResponse().getResultValueBean()).get(0);
								params.put(TouchpointCommunicationsListController.REQ_PARAM_COMMUNICATION_ID, communication.getId());
							}
						}

					}

				} // END IF: New order

				if (!hasUnknownConnectors && !isAccessViolation && !hasErrorOnSaveToBackend) {
					// REDIRECT: To order entry or interactive
					boolean interactiveInitialized = communication.GetInteractiveInitialized();

					params.put(REQ_PARAM_EMBEDDED, true);
					if (returnUrl != null)
						params.put(REQ_PARAM_RETURN_URL, returnUrl);
					if (returnToList != false)
						params.put(REQ_PARAM_RETURN_TO_LIST, returnToList);
					if (document != null)
						params.put(REQ_PARAM_DOCUMENT_ID, document.getId());

					if (interactiveInitialized) {
						CommunicationsUtil.addNewRoundTripAudit(communication, "gateway_no_order_entry");
						Communication.initPreProof(communication);

						if (document.isCommunicationUseBeta())
							return new ModelAndView(new RedirectView(request.getContextPath() + "/connected/connected_touchpoint_rendering_manager.form"), params);

						return new ModelAndView(new RedirectView(request.getContextPath() + "/communication/communication_content_edit.form"), params);
					} else {
						return new ModelAndView(new RedirectView(request.getContextPath() + "/communication/communication_order_entry.form"), params);
					}
				}

			} // END IF: Unknown document
		} // END IF: Unknown user
		
		referenceData.put("isMissingTouchpointGUID"		, isMissingTouchpointGUID);
		referenceData.put("isUnknownTouchpoint"			, isUnknownTouchpoint);
		referenceData.put("hasUnknownConnectors"		, hasUnknownConnectors);
		referenceData.put("unknownConnectors"			, unknownConnectors);
		referenceData.put("isUnknownUser"				, isUnknownUser);
		referenceData.put("isTouchpointVisibleToUser"	, isTouchpointVisibleToUser);
		referenceData.put("hasReturnURL"				, hasReturnURL);
		referenceData.put("isReturnToList"				, returnToList);
		referenceData.put("isReassignPermRejected"		, isReassignPermRejected);
		referenceData.put("isReassignRejected"			, isReassignRejected);
		referenceData.put("isActiveOrderEdit"			, isActiveOrderEdit);
		referenceData.put("isAccessViolation"			, isAccessViolation);
		referenceData.put("isOrderGUIDEmpty"			, isOrderGUIDEmpty);
		referenceData.put("hasErrorOnSaveToBackend"		, hasErrorOnSaveToBackend);

		addInfoToReferenceData(requestor, referenceData);
		
		return new ModelAndView(getFormView(), referenceData);
	}

	private void addInfoToReferenceData(User requestor, Map<String, Object> referenceData){

		if ( requestor != null ) {
			Node userNode = Node.findBySchema(requestor.getSchemaOfThisUser());
			Branch userBranch = userNode.getBranch();
			String currentBranchName = userBranch.getName();
			referenceData.put("nodeName"				, userNode.getName());
			referenceData.put("branchName"				, currentBranchName);
		} else {
			referenceData.put("nodeName"				, "UNKNOWN");
			referenceData.put("branchName"				, "UNKNOWN");
		}
	}
	
	private void setDataValues(HttpServletRequest request, Communication communication, CommunicationOrderEntryEditWrapper wrapper, Boolean hasUnknownConnectors, List<String> unknownConnectors, Boolean isUpdate, InterviewFieldValues[] interviewFields ) {
		
		List<String> paramReservedValues = new ArrayList<>(Arrays.asList(REQ_PARAM_ORDER_GUID.toLowerCase(),
                REQ_PARAM_TOUCHPOINT_GUID.toLowerCase(), SystemPropertyKeys.WebAppSecurity.CSRF_TOKEN_KEY.toLowerCase(),
                REQ_PARAM_TAG_CLOUD.toLowerCase(), "company", "instance", REQ_PARAM_RETURN_URL.toLowerCase(),
                REQ_PARAM_RETURN_TO_LIST.toLowerCase(), REQ_PARAM_REASSIGN.toLowerCase()));
		
		// MATCH CONNECTORS: Verify URL parameters against requestor definition
		Map<String,String[]> paramMap = request.getParameterMap();
		MetadataFormItemDefinitionVO[] items = new MetadataFormItemDefinitionVO[0];
		if(communication.getDocument().isCommunicationUseBeta()){
			items = GetMetadataFormItemDefinitionVOS (communication.getInterviewConfiguration());
		}
		for ( String currentKey: paramMap.keySet() ) {
			if ( paramReservedValues.contains( currentKey.toLowerCase() ) ) {
				continue;
			} else {
				boolean matchedCurrentConnector = false;
				if(!communication.getDocument().isCommunicationUseBeta()) {
					for (CommunicationOrderEntryItem currentItem : wrapper.getOrderEntryItemsList()) {
						if (currentItem.getPrimaryConnector() != null && currentItem.getPrimaryConnector().equalsIgnoreCase(currentKey)) {
							matchedCurrentConnector = true;
							break;
						}
					}
				} else {
					if(interviewFields.length >0) {
						for (int i = 0; i < interviewFields.length; i++) {
							String connector = interviewFields[i].getConnector();
							if (connector != null) {
								if (connector.equalsIgnoreCase(currentKey)) {
									matchedCurrentConnector = true;
									break;
								}
							}
						}
					} else {
						for (MetadataFormItemDefinitionVO item : items) {
							if(item.getPrimaryConnector() != null && item.getPrimaryConnector().equalsIgnoreCase(currentKey)){
								matchedCurrentConnector = true;
								break;
							}
						}
					}

				}
				if ( !matchedCurrentConnector )
					unknownConnectors.add( currentKey );
			}
		}
		
		if (!unknownConnectors.isEmpty()) {
			hasUnknownConnectors = true;
		} else {
			
			if ( isUpdate ) {
				// UPDATE
				if(communication.getDocument().isCommunicationUseBeta()){
					for (int i = 0; i < interviewFields.length; i++) {
						String connector = interviewFields[i].getConnector();
						String currentConnectorValue = ServletRequestUtils.getStringParameter(request, connector, null);
						if (currentConnectorValue != null) {
							interviewFields[i].setValue(currentConnectorValue);
						}
					}
					for (MetadataFormItemDefinitionVO item : items) {
						if (item.getIsPrimaryDriverEntry()) {
							String currentConnectorValue = ServletRequestUtils.getStringParameter(request, item.getPrimaryConnector(), null);
							if(currentConnectorValue != null) {
								communication.setCustomerIdentifier(currentConnectorValue);
							}
						}
						if (item.getIsIndicatorEntry()) {
							String currentConnectorValue = ServletRequestUtils.getStringParameter(request, item.getPrimaryConnector(), null);
							if(currentConnectorValue != null) {
								communication.setIndicator(currentConnectorValue);
							}
						}
					}
					JSONObject jsDefinitionInterview = new JSONObject();
					jsDefinitionInterview.put(Constants.Key_MetadataFormItemDefinitionVOs, new JSONObject(communication.getInterviewConfiguration()).get(Constants.Key_MetadataFormItemDefinitionVOs));
					communication.setMetadataFormItemArray(jsDefinitionInterview);
					communication.getInterviewValues().remove(Constants.Key_InterviewValues);
					communication.getInterviewValues().put(Constants.Key_InterviewValues, new JSONArray(interviewFields));

				} else {
					for (CommunicationOrderEntryItem currentItem : communication.getOrderEntryItems()) {
						if (currentItem.getPrimaryConnector() != null) {
							String currentConnectorValue = ServletRequestUtils.getStringParameter(request, currentItem.getPrimaryConnector(), null);
							if (currentConnectorValue != null) {
								currentItem.setValue(currentConnectorValue);
								if (currentItem.getIsPrimaryDriverEntry())
									communication.setCustomerIdentifier(currentConnectorValue);
							}
						}
					}
				}
				
			} else {
				// NEW
				Set<CommunicationOrderEntryItem> orderEntryItemsSet = new HashSet<>();
				if(communication.getDocument().isCommunicationUseBeta()){
					List<InterviewFieldValues> newInterviewFields  = new ArrayList<>();
					for (MetadataFormItemDefinitionVO item : items) {
						if (item.getType().getId() == MetadataFormItemType.ID_DIVIDER || item.getType().getId() == MetadataFormItemType.ID_HEADER) {
							continue;
						}
						InterviewFieldValues interviewField = new InterviewFieldValues();
						interviewField.setConnector(item.getPrimaryConnector());
						interviewField.setGuid(item.getGuid());
						interviewField.setId(item.getId());
						if(item.getPrimaryConnector() != null) {
							String currentConnectorValue = ServletRequestUtils.getStringParameter(request, item.getPrimaryConnector(), null);
							if (currentConnectorValue != null) {
								interviewField.setValue(currentConnectorValue);
							}
							if (item.getIsPrimaryDriverEntry()) {
								communication.setCustomerIdentifier(currentConnectorValue);
							}
							if (item.getIsIndicatorEntry()) {
								communication.setIndicator(currentConnectorValue);
							}
						}
						newInterviewFields.add(interviewField);

					}
					JSONObject interviewValues = new JSONObject();
					interviewValues.put(Constants.Key_InterviewValues, newInterviewFields);
					interviewValues.put(Constants.Key_Settings, CommunicationOrderControllerUtil.GetSettingsJsonObject(communication));
					communication.setInterviewValuesJson(interviewValues);
					communication.setMetadataFormItemArray(new JSONObject(items));
				} else {
					for (CommunicationOrderEntryItem currentItem : wrapper.getOrderEntryItemsList()) {
						if (currentItem.getPrimaryConnector() != null) {
							String currentConnectorValue = ServletRequestUtils.getStringParameter(request, currentItem.getPrimaryConnector(), null);
							if (currentConnectorValue != null) {
								currentItem.setValue(currentConnectorValue);
								if (currentItem.getIsPrimaryDriverEntry())
									communication.setCustomerIdentifier(currentConnectorValue);
							}
						}
						orderEntryItemsSet.add(currentItem);
					}
					communication.setOrderEntryItems(orderEntryItemsSet);
				}
			
			}
		
		}
		
	}
	
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new Object();
	}

    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
    	return new ModelAndView(new RedirectView(getFormView()));
    }

    public static String getWhitelistedReturnUrl(HttpServletRequest request) {

		try {
			String returnUrl = ServletRequestUtils.getStringParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, StringUtils.EMPTY);
			List<String> validDomains = new ArrayList<>();
			if (!returnUrl.isEmpty()) {

				validDomains.add("force.com");
				validDomains.add("visualforce.com");
				validDomains.add("salesforce.com");
				validDomains.add("messagepoint.com");
				validDomains.add("messagepointlabs.com");

				String domainsString = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ConnectedSecurity.KEY_ConnectedPermittedDomains);
				if(!StringUtil.isEmptyOrNull(domainsString)) {
					List<String> domains = Arrays.stream(domainsString.split(",")).map(x -> x.trim()).collect(Collectors.toList());
					validDomains.addAll(domains);
				}

				URI uri = URI.create(returnUrl);

				if (uri.getHost() == null) {
					return null;
				}

				String returnHost = uri.getHost().toLowerCase();

				if (validDomains.stream().anyMatch(returnHost::endsWith)) {
					return returnUrl;
				}
			}
		} catch (Exception e) {
			LogUtil.getLog(CommunicationPortalGatewayController.class).error("Error:", e);
		}

		return null;

	}

	private MetadataFormItemDefinitionVO[] GetMetadataFormItemDefinitionVOS(String interviewConfiguration){
		Gson gson = new GsonBuilder()
				.registerTypeAdapter(AbstractDataElement.class, new AbstractDataElementDeserializer())
				.registerTypeAdapter(DataSource.class, new AbstractDataSourceDeserializer())
				.create();
		String metadataFormItemDefinitions = new JSONObject(interviewConfiguration).get(Constants.Key_MetadataFormItemDefinitionVOs).toString();
		MetadataFormItemDefinitionVO[] items = gson.fromJson(metadataFormItemDefinitions, MetadataFormItemDefinitionVO[].class);
		return  items;
	}
}
