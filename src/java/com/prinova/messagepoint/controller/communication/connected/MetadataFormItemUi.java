package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;

import java.util.ArrayList;
import java.util.List;

public class MetadataFormItemUi {

    private Integer index;
    private MetadataFormItemDefinitionVO item;
    private List<MetadataFormItemUi> innerChildren;

    public MetadataFormItemUi(MetadataFormItemDefinitionVO item) {
        this.item = item;
        this.innerChildren = new ArrayList<>();
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public MetadataFormItemDefinitionVO getItem() {
        return item;
    }

    public void setItem(MetadataFormItemDefinitionVO item) {
        this.item = item;
    }

    public List<MetadataFormItemUi> getInnerChildren() {
        return innerChildren;
    }

    public void setInnerChildren(List<MetadataFormItemUi> innerChildren) {
        this.innerChildren = innerChildren;
    }

    public void addInnerChild(MetadataFormItemUi child) {
        this.innerChildren.add(child);
    }
}
