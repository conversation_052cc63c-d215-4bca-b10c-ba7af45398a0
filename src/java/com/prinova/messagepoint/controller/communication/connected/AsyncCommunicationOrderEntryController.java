package com.prinova.messagepoint.controller.communication.connected;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.data.json.interview.InterviewFieldValues;
import com.prinova.messagepoint.connected.data.json.support.CommunicationLogType;
import com.prinova.messagepoint.connected.data.oracle.entity.CacheData;
import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.connected.util.SupportingDataUtil;
import com.prinova.messagepoint.controller.AsyncVariantTreeController;
import com.prinova.messagepoint.controller.communication.CommunicationOrderEntryEditWrapper;
import com.prinova.messagepoint.controller.communication.CommunicationPortalGatewayController;
import com.prinova.messagepoint.controller.communication.TouchpointCommunicationsListController;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.MigrateOnDemandCommunicationOrderUtil;
import com.prinova.messagepoint.platform.services.*;
import com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.util.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static com.prinova.messagepoint.connected.util.Constants.Base64_Identifier;

public class AsyncCommunicationOrderEntryController implements Controller {
    public static final String PARAM_TYPE = "type";
    public static final String LANGUAGE = "language";

    public static final String REQ_PARAM_COMMUNICATION_ID 			= "communicationId";
    public static final String REQ_PARM_ACTION 						= "submittype";
    public static final String REQ_PARAM_CONTEXT	 				= "context";
    private static final String REQ_PARAM_DEBUG_ORDER = "debug_order";
    private static final String REQ_PARAM_DEBUG_REFERENCE_DATA = "debug_reference_data";
    private static final String REQ_PARAM_DEBUG_PACKAGE = "debug_package";
    public static   final String REQ_PARAM_DOCUMENT_ID	= "documentId";

    public static final int 	ACTION_UPDATE 							= 1;
    public static final int 	ACTION_UPDATE_AND_CONTINUE 				= 3;
    public static final int 	ACTION_UPDATE_CLEAR_CONTENT_CONTINUE 	= 4;
    public static final int 	ACTION_UPDATE_AUTO_SAVE	= 5;
    public static final int     ACTION_DELETE = 6;

    public static final int REPETABLE_CONTAINER = 14;
    private static final Log LOGGER = LogUtil.getLog(CommunicationOrderEntryController.class);

    private CacheDataService _cacheDataService;
    private Document _currentLayout;
    private TouchpointSelection _touchpointSelection = null;
    private HashMap<String,  Object> _metadataFormItemDefinitionsMap = new HashMap<>();
    private String _requestId;
    private MigrateOnDemandCommunicationOrderUtil _migrateOnDemandCommunicationOrderUtil;

    public AsyncCommunicationOrderEntryController(CacheDataService cacheDataService)
    {
        _cacheDataService = cacheDataService;
        _migrateOnDemandCommunicationOrderUtil = new MigrateOnDemandCommunicationOrderUtil();
    }

    @Override
    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        long paramCommId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);

        String rndReqGUID = RandomGUID.getGUID();
        long cacheDataId = SupportingDataUtil.logCommunicationAction(-1, paramCommId, rndReqGUID, CommunicationLogType.INTERVIEW_SCREEN_ENTER, new Date());

        PrintWriter out = response.getWriter();
        String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
        long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
        Document document = null;
        if(documentId >0) {
            document = Document.findById(documentId);
        }
        try {
            switch (type) {
                case "GetInterviewData": {
                    long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
                    SupportingDataUtil.logCommunicationAction(cacheDataId, communicationId, rndReqGUID, CommunicationLogType.INTERVIEW_SCREEN_GET, new Date());
                    Communication communication = InitializeCommunication(request, communicationId, document);
                    JSONObject result =  GetInterviewResult(request, communicationId, communication, document);
                    out.write(result.toString());
                    SupportingDataUtil.logCommunicationAction(cacheDataId, communicationId, rndReqGUID, CommunicationLogType.INTERVIEW_SCREEN_GET_EXIT, new Date());
                    break;
                }
                case "Save": {
                    long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
                    SupportingDataUtil.logCommunicationAction(cacheDataId,communicationId, rndReqGUID, CommunicationLogType.INTERVIEW_SCREEN_SAVE, new Date());
                    JSONObject saveResult = SaveOrder(request);
                    out.write(saveResult.toString());
                    SupportingDataUtil.logCommunicationAction(cacheDataId, communicationId, rndReqGUID, CommunicationLogType.INTERVIEW_SCREEN_SAVE_EXIT, new Date());
                    break;
                }
                default:
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("Error - Unable to resolve request for interview: " + e.getMessage(), e);
        }

        out.flush();
        return null;
    }

    private JSONObject GetInterviewResult(HttpServletRequest request, long communicationId,  Communication communication, Document document) throws Exception {
        JSONObject result = new JSONObject();

        if (document == null) {
            document = communication.getDocument();
        }

        ValidateDataVariableElements(result, document);
        if(!document.isCommunicationUseBeta()){
            String interviewConfig = communication.getInterviewConfiguration();
            if(interviewConfig != null){
               JSONObject communicationJson =  new JSONObject(interviewConfig);
               communication.setMetadataFormItemArray(communicationJson);
            }
        }
        if(communication.getMetadataFormItemArray() == null) return result;
        JSONArray items = (JSONArray)communication.getMetadataFormItemArray().get(Constants.Key_MetadataFormItemDefinitionVOs);
        if(items == null) {
            return result;
        }

        if (!items.isEmpty()) {
            JSONObject metadataItemObj = (JSONObject) items.get(0);
            if (!metadataItemObj.has(Constants.Key_Guid)) {
                 _migrateOnDemandCommunicationOrderUtil.updateMetadataFormItemDefinitionWithGuid(items);
            } if(!metadataItemObj.has(MigrateOnDemandCommunicationOrderUtil.DATA_ELEMENT_VARIABLE)){
                _migrateOnDemandCommunicationOrderUtil.updateMetadataFormItemDefinitionWithDataElementVariable(items, communication.getId());
            }
        }

        if(!communication.getDocument().isCommunicationUseBeta()){
            items = GetInterviewItemsForOdlConnected(communication.getMetadataFormItemArray(), communication);
        } else {
            JSONArray newItems = new JSONArray();
            if (communication.getInterviewValues() != null && communication.getInterviewValues().has(Constants.Key_InterviewValues)) {
                ObjectMapper objectMapper = new ObjectMapper();
                InterviewFieldValues[] interviewValues = objectMapper.readValue(communication.getInterviewValues().get(Constants.Key_InterviewValues).toString(), new TypeReference<>() {
                });
                ;
                if (interviewValues != null) {
                    newItems = getItemsToRender(items, interviewValues);
                } else {
                    newItems = getItemsToRender(items, null);

                }
            } else {
                newItems = getItemsToRender(items, null);
            }
            items = newItems;
        }

        result.put("items", items);
        if(communicationId == -1){
            result.put(Constants.Key_Settings, GetExtraSettingsObject(request, communication, document));
        } else {
            if(communication.getMetadataFormItemArray().has(Constants.Key_Settings)) {
                JSONObject settings = (JSONObject) communication.getMetadataFormItemArray().get(Constants.Key_Settings);
                settings.put(Constants.Key_RequestId, _requestId);
                result.put(Constants.Key_Settings, settings);
            } else {
                result.put(Constants.Key_Settings, GetExtraSettingsObject(request, communication, document));
            }
        }

        UUID uuid = UUID.randomUUID();
        String uuidString = uuid.toString();
        result.put(Constants.Key_MetadataFormItemDefinitionKey, uuidString);

        _metadataFormItemDefinitionsMap.put(uuidString, communication.getMetadataFormItemArray().get(Constants.Key_MetadataFormItemDefinitionVOs));

        return result;
    }

    private JSONArray getItemsToRender(JSONArray items,  InterviewFieldValues[] interviewFields) throws  Exception{

        JSONArray newItems = new JSONArray();
        JSONArray lastGroupedItems = new JSONArray();
        JSONObject jsObjectGroupedItems = null;
        int groupIndex = 1;

        for (int i = 0; i < items.length(); i++) {
            JSONObject itemObject = (JSONObject) items.get(i);
            if (itemObject != null) {
                if (itemObject.has(Constants.Key_RepeatingDataTypeId)) {
                    Object repeatingDataTypeIdObj = itemObject.get(Constants.Key_RepeatingDataTypeId);
                    if  (repeatingDataTypeIdObj == null) repeatingDataTypeIdObj = 1;
                    int repeatingDataTypeId = (int) repeatingDataTypeIdObj;
                    if (repeatingDataTypeId == RepeatingDataType.ID_NONE) {
                        if (!lastGroupedItems.isEmpty() && jsObjectGroupedItems != null) {
                            List<JSONArray> groupedItems = createRepeatingGroupWithValues(interviewFields, lastGroupedItems, groupIndex);
                            jsObjectGroupedItems.put("values", groupedItems);
                            newItems.put(jsObjectGroupedItems);
                        }
                        if (interviewFields != null) {
                            UpdateItemObjectWithValue(itemObject, interviewFields);
                        } else {
                            UpdateItemWithEmptyValue(itemObject);
                        }
                        newItems.put(itemObject);
                        lastGroupedItems = new JSONArray();
                        jsObjectGroupedItems = null;
                    } else if (repeatingDataTypeId == RepeatingDataType.ID_REPEAT) {
                        if (!lastGroupedItems.isEmpty()) {
                            List<JSONArray> groupedItems = createRepeatingGroupWithValues(interviewFields, lastGroupedItems, groupIndex);
                            jsObjectGroupedItems.put("values", groupedItems);
                            newItems.put(jsObjectGroupedItems);
                        }
                        jsObjectGroupedItems = new JSONObject();
                        jsObjectGroupedItems.put("name", "GroupedItems");
                        jsObjectGroupedItems.put("type", new JSONObject(new MetadataFormItemType(MetadataFormItemType.ID_GROUPED_ITEM)));
                        jsObjectGroupedItems.put(Constants.Key_Guid, UUID.randomUUID());
                        groupIndex = groupIndex + 1;
                        lastGroupedItems = new JSONArray();
                        lastGroupedItems.put(itemObject);
                    } else if (repeatingDataTypeId == RepeatingDataType.ID_REPEAT_GROUP_WITH_ABOVE) {
                        lastGroupedItems.put(itemObject);
                    }
                }
            }
        };
        if(!lastGroupedItems.isEmpty() && jsObjectGroupedItems != null){
            List<JSONArray> groupedItems = createRepeatingGroupWithValues(interviewFields, lastGroupedItems, groupIndex);
            jsObjectGroupedItems.put("values", groupedItems);
            newItems.put(jsObjectGroupedItems);
        }
        return  newItems;
    }


    private List<JSONArray> createRepeatingGroupWithValues( InterviewFieldValues[]interviewFields, JSONArray items, int groupIndex) throws  Exception{
        Map<String, JSONObject> itemDefinitions = new HashMap<>();
        List<JSONArray> itemsWithEmptyValuesList = new ArrayList<>();
        JSONArray itemsWithEmptyValues = new JSONArray();
        for (int i = 0; i < items.length(); i++) {
            JSONObject item = items.getJSONObject(i);
            String guid = item.optString(Constants.Key_Guid);
            if (!guid.isEmpty()) {
                itemDefinitions.put(guid, item);
                item.put(Constants.Key_Guid, guid+"__"+groupIndex);
                UpdateItemWithEmptyValue(item);
                itemsWithEmptyValues.put(item);
            }
        }
        itemsWithEmptyValuesList.add(itemsWithEmptyValues);
        List<JSONArray>  newListGroup = new ArrayList<>();

        if(interviewFields != null) {
            int innerGroupIndex = 0;
            for (InterviewFieldValues field : interviewFields) {
                if (field.getValues() != null) {
                    for (List<InterviewFieldValues> valueGroup : field.getValues()) {
                        innerGroupIndex = innerGroupIndex +1;
                        JSONArray group = new JSONArray();
                        for (InterviewFieldValues valueItem : valueGroup) {
                            JSONObject originalItemDefinition = itemDefinitions.get(valueItem.getGuid());
                            if (originalItemDefinition != null) {
                                // Create a deep copy of the item definition
                                JSONObject itemDefinitionCopy = new JSONObject(originalItemDefinition.toString());
                                if (itemDefinitionCopy != null) {
                                    itemDefinitionCopy.put(Constants.Key_Guid, valueItem.getGuid() + "__" + innerGroupIndex);
                                    populateItemWithValue(itemDefinitionCopy, valueItem);
                                    group.put(itemDefinitionCopy);
                                }
                            }
                        }
                        if(!group.isEmpty()) {
                            newListGroup.add(group);
                        }
                    }

                }
            }
        }
        if(newListGroup.isEmpty()){
            return itemsWithEmptyValuesList;
        }
        return newListGroup;
    }



    private void UpdateItemsWithEmptyValue( JSONArray items){
        items.forEach(item -> {
            JSONObject itemObject = (JSONObject) item;
            itemObject.put(Constants.AUX_VALUE, JSONObject.NULL);
            itemObject.put(Constants.VALUE,  JSONObject.NULL);

        });
    }

    private void UpdateItemWithEmptyValue( JSONObject itemObject ) {
        itemObject.put(Constants.AUX_VALUE, JSONObject.NULL);
        itemObject.put(Constants.VALUE, JSONObject.NULL);
    }

    private JSONArray GetInterviewItemsForOdlConnected(JSONObject communicationJson, Communication communication) {

        JSONArray items = (JSONArray)communicationJson.get(Constants.Key_MetadataFormItemDefinitionVOs);

        if(items == null || communication.getOrderEntryItems() == null)
            return items;
        Set<CommunicationOrderEntryItem> orderEntryItems = communication.getOrderEntryItems();
        items.forEach(item -> {
            JSONObject itemObject = (JSONObject)item;
            if(itemObject != null) {
                int id = (int)(itemObject).get("id");
                CommunicationOrderEntryItem entryItem = GetCommunicationOrderItemByMetadataDefinitionId(orderEntryItems, id);
                CommunicationOrderEntryItemDefinition entryItemDef = CommunicationOrderEntryItemDefinition.findById(id);
                if(entryItemDef != null){
                    itemObject.put("guid",entryItemDef.getGuid());
                }
                if (entryItem != null && entryItem.getValue() != null) {
                    JSONObject type = (JSONObject)itemObject.get("type");
                    int typeId = (int)type.get("id");

                    itemObject.put("auxValues", entryItem.getAuxValueData());

                    // add the value to the result (for transient data, data will be fetched by the CommunicationOrderEntryEditWrapper)
                    String value = entryItem.getValue();
                    if(typeId == MetadataFormItemType.ID_FILE) {
                        JSONObject fileContentObject = new JSONObject();
                        fileContentObject.put("name", entryItem.getFileName());
                        String encodedValue = Constants.Data_Identifier + entryItem.getFileContentType() + Base64_Identifier + value;
                        fileContentObject.put("content", encodedValue);
                        itemObject.put("value", fileContentObject);
                    }
                    else if(typeId == MetadataFormItemType.ID_MULTISELECT_MENU) {
                        try {
                            JSONArray arrayValue = new JSONArray(value);
                            itemObject.put("value", arrayValue);
                        }
                        catch (JSONException ex) { }
                    }
                    else {
                        itemObject.put("value", value);
                    }
                    // update default values for text fields (it might be an auto-incremented one determined in CommunicationOrderEntryEditWrapper.initNewWrapper)
                    if(typeId == MetadataFormItemType.ID_TEXT || typeId == MetadataFormItemType.ID_TEXTAREA) {
                        //match items by order (as on the client-side)
                        ((JSONObject)item).put("textDefaultValue", entryItem.getValue());
                    }

                    // avoid storing the value in the DB
                    // improve transient data retrieval - do not set then on CommunicationOrderEntryItem instances in the first place
                    entryItem.setValue(null);
                }
            }
        });

        return items;
    }

    private void UpdateItemObjectWithValue(JSONObject itemObject,  InterviewFieldValues[]  interviewValues){
        int length = interviewValues.length;
        if (itemObject != null) {
            int id = (int) (itemObject).get("id");
            String guid = itemObject.getString("guid");
            for (int i = 0; i < length; i++) {
                InterviewFieldValues updatedItem = (InterviewFieldValues) Arrays.stream(interviewValues).toList().get(i);
                if (updatedItem.getId() == id || updatedItem.getGuid().equalsIgnoreCase(guid)) {
                    populateItemWithValue(itemObject, updatedItem);
                }
            }
        }
    }

    private void populateItemWithValue(JSONObject itemObject, InterviewFieldValues updatedItem) {
        String auxValue = updatedItem.getAux();
        itemObject.put(Constants.AUX_VALUE, auxValue);
        Object value = updatedItem.getValue();
        Object type = updatedItem.getType();
        if(type instanceof Integer ) {
            if ((int)type == MetadataFormItemType.ID_MULTISELECT_MENU) {
                try {
                    JSONArray arrayValue = new JSONArray(value.toString());
                    itemObject.put("value", arrayValue);
                } catch (JSONException ex) {
                }
            }else if ((int)type== MetadataFormItemType.ID_FILE) {
                if (updatedItem.getFileName()!= null) {
                    populateFileJson(updatedItem, itemObject, value);
                } else{
                    itemObject.put(Constants.VALUE, value);
                }
            } else {
                itemObject.put(Constants.VALUE, value);
            }
        } else {
            //this code is used for old orders that were migrated to Json
            JSONObject typeObj = (JSONObject) type;
            int typeId = (int) typeObj.get("id");
            if (typeId == MetadataFormItemType.ID_MULTISELECT_MENU) {
                try {
                    JSONArray arrayValue = new JSONArray(value.toString());
                    itemObject.put("value", arrayValue);
                } catch (JSONException ex) {
                }
            } else if (typeId == MetadataFormItemType.ID_FILE) {
                populateFileJson(updatedItem, itemObject, value);

            } else {
                itemObject.put(Constants.VALUE, value);
            }
        }
    }

    private void populateFileJson(InterviewFieldValues updatedItem, JSONObject itemObject, Object value){
        JSONObject fileContentObject = new JSONObject();
        if(updatedItem.getFileName() != null) {
            fileContentObject.put("name", updatedItem.getFileName());
            String encodedValue = Constants.Data_Identifier + updatedItem.getContentType()+  Constants.Base64_Identifier + value;
            fileContentObject.put("content", encodedValue);
            itemObject.put("value", fileContentObject);
        } else {
            itemObject.put(Constants.VALUE, value);
        }
    }

    private JSONObject GetExtraSettingsObject(HttpServletRequest request, Communication communication, Document document) {
        JSONObject extraSettingsNode = new JSONObject();

        extraSettingsNode.put(Constants.Key_CommunicationGuid, communication.getGuid());

        List<Long> layoutsWithoutConnectedZones = new ArrayList<>();
        List<Document> layouts = document.getAlternateLayouts();
        layouts.add(document);
        for ( Document currentLayout: layouts ) {
            boolean hasEnabledConnectedZone = false;
            List<Zone> zones = Zone.findCommunicationZonesByDocument(currentLayout.getId());
            for ( Zone currentLayoutZone: zones )
                if ( currentLayoutZone.isEnabled() ) {
                    hasEnabledConnectedZone = true;
                    break;
                }
            if ( !hasEnabledConnectedZone )
                layoutsWithoutConnectedZones.add(currentLayout.getId());
        }
        extraSettingsNode.put(Constants.Key_LayoutsWithoutConnectedZones, layoutsWithoutConnectedZones);
        extraSettingsNode.put(Constants.Key_ContentExists, communication != null && !communication.getZoneContentAssociations().isEmpty());

        String context 	= ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default");
        Boolean isTestCenterContext = context.equals("test") || (communication != null && communication.isTestOrder());
        extraSettingsNode.put(Constants.Key_IsTestCenterContext, isTestCenterContext);
        extraSettingsNode.put(Constants.Key_SkipInteractiveWhenNoZones,  document.isCommunicationSkipInteractiveWhenNoZones());
        extraSettingsNode.put(Constants.Key_AppliesVariantSelect, document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()
                && communication.getTouchpointSelectionId() != null);
        extraSettingsNode.put(Constants.Key_CurrentLayoutId, _currentLayout.getId());
        extraSettingsNode.put(Constants.Key_HasCommunicationZones, CommunicationOrderControllerUtil.HasCommunicationZones(document));

        extraSettingsNode.put(Constants.Key_CommunicationId, communication.getId());
        extraSettingsNode.put(Constants.Key_DocumentId, document.getId());
        extraSettingsNode.put(Constants.Key_DocumentName, document.getName());
        extraSettingsNode.put(Constants.Key_RequestId, _requestId);
        extraSettingsNode.put(Constants.Key_IsNewConnected, document.isCommunicationUseBeta());

        if (_touchpointSelection != null)
            extraSettingsNode.put(Constants.Key_SelectionId, _touchpointSelection.getId());

        extraSettingsNode.put(Constants.Key_DateFormat, DateUtil.DATE_FORMAT.toUpperCase());
        extraSettingsNode.put(Constants.Key_CommunicationDisplayTouchpointThumbnail, document.isCommunicationDisplayTouchpointThumbnail());

        List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
        List<TextValuePair> languageList = new ArrayList<>();
        for(MessagepointLocale locale: langLocales) {
            languageList.add(new TextValuePair(locale.getLanguageDisplayName(),  String.valueOf(locale.getId())));
        }
        extraSettingsNode.put(Constants.Key_Languages, languageList);
        extraSettingsNode.put(Constants.Key_SelectedLanguageId, communication.getLocale() != null ? String.valueOf(communication.getLocale().getId()):
                !languageList.isEmpty() ? languageList.get(0).getValue() : "");


        extraSettingsNode.put(Constants.Key_DebugOrder, communication.isDebugOrder());
        extraSettingsNode.put(Constants.Key_DebugReferenceData, communication.getDebugReferenceData());
        extraSettingsNode.put(Constants.Key_DebugPackage, communication.getDebugPackage() != null ? "<zip-file>" : "");

        Boolean isOmniChannel = document.getIsOmniChannel();
        if ( isOmniChannel ) {
            Long appliedChannelContextId = communication != null && communication.getChannelContextId() != null ? communication.getChannelContextId() : -1;
            List<Channel> appliedChannels = document.getAppliedChannels(false);
            List<TextValuePair> channelList = new ArrayList<>();
            for (Channel appliedChannel : appliedChannels) {
                channelList.add(new TextValuePair(appliedChannel.getName(), String.valueOf(appliedChannel.getId())));
            }
            extraSettingsNode.put(Constants.Key_AppliedChannels, channelList);
            extraSettingsNode.put(Constants.Key_AppliedChannelContextId, appliedChannelContextId > 0 ? String.valueOf(appliedChannelContextId) :
                    !channelList.isEmpty() ? channelList.get(0).getValue() : "");
        }

        extraSettingsNode.put(Constants.Key_CommunicationAppliesCopiesInput, document.isCommunicationAppliesCopiesInput());
        extraSettingsNode.put(Constants.Key_NumberOfCopies, communication.getNumberOfCopies());

        if(document.isCommunicationAppliesTagCloud() && RolesUtil.hasPermission(UserUtil.getPrincipalUser().getRoles(), "ROLE_METATAGS_VIEW")) {
            extraSettingsNode.put(Constants.Key_AppliesTagCloud, true);
            extraSettingsNode.put(Constants.Key_MetaTags, communication.getMetatags());
        }

        return extraSettingsNode;
    }

    private JSONObject SaveOrder(HttpServletRequest request) {
        Date dateStart = new Date();
        JSONObject result = new JSONObject();

        long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, 0);
        int submitType = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, 1);
        String metadataFormItemDefinitionKey = ServletRequestUtils.getStringParameter(request, Constants.Key_MetadataFormItemDefinitionKey, "");

        long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
        Document document = null;
        if (documentId > 0) {
            document = Document.findById(documentId);
        }
        CommunicationOrderEntryEditWrapper wrapper;
        Communication _communication = null;
        if (communicationId > 0) {
            _communication = Communication.findById(communicationId);
            document = _communication.getDocument();
            wrapper = new CommunicationOrderEntryEditWrapper(_communication);
        } else {
            wrapper = new CommunicationOrderEntryEditWrapper(document, this._cacheDataService);
            _communication = wrapper.getCommunication();
        }

        String updatedItemsJson = ServletRequestUtils.getStringParameter(request, "updatedItems", "");
        JSONArray updatedItems = new JSONArray(updatedItemsJson);
        User requestor = UserUtil.getPrincipalUser();

        if (submitType != ACTION_DELETE) {
            UpdateInterviewConfigurationJson(updatedItems, metadataFormItemDefinitionKey,_communication, request);
            if (!document.isCommunicationUseBeta()) {
                UpdateOrderEntryItems(updatedItems, _communication);
                JSONObject validationResult = ValidateOrder(_communication);

                if (!validationResult.getBoolean("success")) {
                    return validationResult;
                }
            }

            _communication.setCustomerIdentifier(ServletRequestUtils.getStringParameter(request, "identifier", ""));
            _communication.setChannelContextId(ServletRequestUtils.getLongParameter(request, "channelContextId", -1));
            _communication.setNumberOfCopies(ServletRequestUtils.getIntParameter(request, "numberOfCopies", 1));
            _communication.setMetatags(ServletRequestUtils.getStringParameter(request, "metaTags", ""));
            _communication.setTouchpointSelectionId(ServletRequestUtils.getLongParameter(request, "touchpointSelectionId", -1));

            _communication.setDebugOrder(ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_DEBUG_ORDER, false));
            _communication.setDebugReferenceData(ServletRequestUtils.getStringParameter(request, REQ_PARAM_DEBUG_REFERENCE_DATA, ""));

            _communication.setTestOrder(ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default").equalsIgnoreCase("test"));

            // TODO check if zip; otherwise return error.
            String base64Value = ServletRequestUtils.getStringParameter(request, REQ_PARAM_DEBUG_PACKAGE, "");
            int dataIndex = base64Value.indexOf(Constants.Data_Identifier);
            int base64Index = base64Value.indexOf(Constants.Base64_Identifier);
            if (dataIndex >= 0 && base64Index >= 0) {
                String fileType = base64Value.substring(dataIndex + 5, base64Index);
                _communication.setDebugPackage(base64Value.substring(base64Index + 8));
            }

            String selectedLanguageId = ServletRequestUtils.getStringParameter(request, "languageId", "");
            if (!selectedLanguageId.isEmpty()) {
                List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
                for (MessagepointLocale locale : langLocales) {
                    if (String.valueOf(locale.getId()).equals(selectedLanguageId)) {
                        _communication.setLocale(locale);
                        break;
                    }
                }
            }


            AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.OrderEntryEdit);
            analyticsEvent.setAction(submitType == ACTION_UPDATE ? Actions.Update : Actions.UpdateAndContinue);

            ServiceExecutionContext context;
            if (communicationId > 0)
                context = CreateOrUpdateCommunicationService.createContextForUpdateOrderEntry(wrapper.getCommunication(), submitType == ACTION_UPDATE_CLEAR_CONTENT_CONTINUE, requestor);
            else
                context = CreateOrUpdateCommunicationService.createContextForNew(wrapper.getCommunication(), requestor);

            Service updateContentService = MessagepointServiceFactory.getInstance()
                    .lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
            updateContentService.execute(context);


            if (!context.getResponse().isSuccessful()) {
                if(context.getResponse().hasMessages()){
                    if("SENDING_COMMUNICATION_DEFINITION_FAILED".equalsIgnoreCase(context.getResponse().getResultStatus().getReturnCode())){
                        String errorMessage = "The definition of communication and the values entered could not be sent to backend for communication guid " + _communication.getGuid() + " and document " + document.getName();
                        LOGGER.error(errorMessage);
                        result.put("success", false);
                        result.put("errorMessage", "Unable to save the values to backend");
                        return result;
                    }
                }
                String errorMessage = "Unexpected exception when invoking CreateOrUpdateCommunicationService execute method";
                LOGGER.error(errorMessage);
                result.put("success", false);

            } else {
                result.put("success", true);
                Communication communication;
                if (context.getResponse().getResultValueBean() != null) {
                    communication = ((List<Communication>) context.getResponse().getResultValueBean()).get(0);

                    switch (submitType) {
                        case ACTION_UPDATE:
                            Communication.initProofForNonInteractive(communication, null);

                            boolean isEmbedded = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
                            boolean isReturnToList = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);
                            if (isEmbedded && !isReturnToList) {
                                Map<String, String> params = new HashMap<>();
                                params.put("order_guid", communication != null ? communication.getGuid() : "-1");

                                String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
                                if (returnUrl != null)
                                    params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);

                                result.put("returnPath", BuildUrl(request.getContextPath() + "/signout", params));
                            } else {
                                boolean isTestCenterContext = wrapper.getCommunication().isTestOrder();

                                String returnPath = "/touchpoints/touchpoint_communications_list.form";
                                if (isTestCenterContext)
                                    returnPath = "/testing/test_center_communications_list.form";

                                Map<String, String> params = GetEmbeddedParams(request);
                                _metadataFormItemDefinitionsMap.remove(metadataFormItemDefinitionKey);
                                params.put(TouchpointCommunicationsListController.REQ_PARAM_DOCUMENTID, String.valueOf(document.getId()));
                                result.put("returnPath", BuildUrl(request.getContextPath() + returnPath, params));
                                result.put("communicationId", String.valueOf(communication.getId()));
                            }
                            break;
                        case ACTION_UPDATE_AND_CONTINUE:
                        case ACTION_UPDATE_CLEAR_CONTENT_CONTINUE:
                            long proofId = Communication.initPreProof(communication);
                            SupportingDataUtil.logCommPreProofCreated(communication, proofId, dateStart, new Date());

                            Map<String, String> params = GetEmbeddedParams(request);
                            params.put("communicationId", String.valueOf(communication.getId()));

                            String returnPath = "/communication/communication_content_edit.form";
                            if (wrapper.isUseBetaRenderer()) {
                                returnPath = "/connected/connected_touchpoint_rendering_manager.form";
                            }
                            _metadataFormItemDefinitionsMap.remove(metadataFormItemDefinitionKey);
                            result.put("returnPath", BuildUrl(request.getContextPath() + returnPath, params));
                            result.put("communicationId", String.valueOf(communication.getId()));
                            break;
                        case ACTION_UPDATE_AUTO_SAVE:
                            result.put("communicationId", String.valueOf(communication.getId()));
                            break;
                        default:
                            String errorMessage = "Unexpected 'submittype' parameter.";
                            LOGGER.error(errorMessage);
                            result.put("success", false);
                    }
                }
            }
            return result;
        } else {
            List<Communication> communications = new ArrayList<>();
            communications.add(wrapper.getCommunication());
            ServiceExecutionContext context = CreateOrUpdateCommunicationService.createContextForDelete(communications, requestor);

            Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateCommunicationService.SERVICE_NAME, CreateOrUpdateCommunicationService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            if (serviceResponse.isSuccessful()) {
                result.put("success", true);
            } else {
                result.put("success", false);
                LOGGER.error("Unable to delete the communication after the user press cancel -Autosave.");
            }
            return result;
        }
    }

    private void UpdateOrderEntryItems(JSONArray updatedItems, Communication communication) {
        int length = updatedItems.length();
        for (int i = 0; i < length; i++) {
            JSONObject updatedItem = (JSONObject)updatedItems.get(i);
            int updatedItemId = updatedItem.getInt("id");
            // update Communication's Order Entry items
            CommunicationOrderEntryItem orderEntryItem = GetCommunicationOrderItemByMetadataDefinitionId(communication.getOrderEntryItems(), updatedItemId);
            if(orderEntryItem != null) {
                String updatedValue = null;
                if (orderEntryItem.getTypeId() == MetadataFormItemType.ID_FILE) {
                    if(updatedItem.isNull("value") || updatedItem.get("value").toString().isEmpty()) {
                        orderEntryItem.setFileName(null);
                        orderEntryItem.setFileContentType(null);
                    }
                    else {
                        JSONObject fileUploadObject = updatedItem.getJSONObject("value");
                        orderEntryItem.setFileName(fileUploadObject.get("name").toString());
                        String base64Value = fileUploadObject.get("content").toString();
                        int dataIndex = base64Value.indexOf(Constants.Data_Identifier);
                        int base64Index = base64Value.indexOf(Constants.Base64_Identifier);
                        if(dataIndex >= 0 && base64Index >= 0) {
                            String fileType = base64Value.substring(dataIndex + 5, base64Index);
                            orderEntryItem.setFileContentType(fileType);
                            updatedValue = base64Value.substring(base64Index + 8);
                        }
                    }
                }
                else {
                    updatedValue = updatedItem.isNull("value") ? "" : updatedItem.get("value").toString();
                }

                orderEntryItem.setValue(updatedValue);
                String auxValue = updatedItem.isNull("auxValues") ? "" : updatedItem.get("auxValues").toString();
                orderEntryItem.setAuxValueData(auxValue);

                if (orderEntryItem.getIsPrimaryDriverEntry())
                    communication.setCustomerIdentifier(updatedValue);
            }
        }
    }

    private static CommunicationOrderEntryItem GetCommunicationOrderItemByMetadataDefinitionId(Set<CommunicationOrderEntryItem> orderEntryItems, int metadataDefinitionId) {
        for(CommunicationOrderEntryItem item : orderEntryItems) {
            if(item.getMetadataDefinitionId() == metadataDefinitionId) {
                return item;
            }
        }

        return null;
    }

    private void UpdateInterviewConfigurationJson(JSONArray updatedItems, String metadataFormItemDefinitionKey, Communication communication, HttpServletRequest request){
        LOGGER.warn("Interview with guid " + communication.getGuid() + " was updated  ");
        JSONArray items = (JSONArray) _metadataFormItemDefinitionsMap.get(metadataFormItemDefinitionKey);

        int updateditemsLength = updatedItems.length();
        if (items == null) {
            items = new JSONArray();
        }
        items.forEach(item -> {
            JSONObject itemObject = (JSONObject) item;
            LOGGER.warn("Interview with guid " + communication.getGuid() + " was updated  " + "first item updated " + item.toString());
            if (itemObject != null) {
                boolean isPrimaryDriveEntry = itemObject.getBoolean(Constants.Key_IsPrimaryDriverEntry);
                boolean isIndicatorEntry = false;
                if (itemObject.has(Constants.Key_IsIndicatorEntry)) {
                    isIndicatorEntry = itemObject.getBoolean(Constants.Key_IsIndicatorEntry);
                    if (isPrimaryDriveEntry || isIndicatorEntry) {
                        int id = (int) (itemObject).get("id");
                        for (int i = 0; i < updateditemsLength; i++) {
                            JSONObject updatedItem = (JSONObject) updatedItems.get(i);
                            if (updatedItem.has("id")) {
                                int updatedItemId = updatedItem.getInt("id");
                                if (updatedItemId == id) {
                                    Object value = updatedItem.get(Constants.VALUE);
                                    if (isPrimaryDriveEntry) {
                                        communication.setCustomerIdentifier(value.toString());
                                    }
                                    if (isIndicatorEntry) {
                                        communication.setIndicator(value.toString());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        JSONObject interviewValues = new JSONObject();
        String communicationSettings = ServletRequestUtils.getStringParameter(request, Constants.Key_Settings, "");
        interviewValues.put(Constants.Key_Settings, new JSONObject( communicationSettings));
        interviewValues.put(Constants.Key_InterviewValues, updatedItems);
        communication.setInterviewValuesJson(interviewValues);
        JSONObject jsDefinitionInterview = new JSONObject();
        jsDefinitionInterview.put(Constants.Key_MetadataFormItemDefinitionVOs, items);
        communication.setMetadataFormItemArray(jsDefinitionInterview);
    }

    private Map<String, String> GetEmbeddedParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();

        boolean isEmbedded = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, false);
        if ( isEmbedded ) {
            params.put(CommunicationPortalGatewayController.REQ_PARAM_EMBEDDED, String.valueOf(isEmbedded));
            String returnUrl = CommunicationPortalGatewayController.getWhitelistedReturnUrl(request);
            if ( returnUrl != null )
                params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_URL, returnUrl);
            boolean isReturnToList = ServletRequestUtils.getBooleanParameter(request, CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, false);
            if ( isReturnToList )
                params.put(CommunicationPortalGatewayController.REQ_PARAM_RETURN_TO_LIST, String.valueOf(isReturnToList));
        }
        Long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
        if(documentId >0){
            params.put(CommunicationPortalGatewayController.REQ_PARAM_DOCUMENT_ID, String.valueOf(documentId));
        }

        return params;
    }

    private String BuildUrl(String formUrl, Map<String, String> params) {
        StringBuilder result = new StringBuilder(formUrl);
        if(!params.isEmpty()) {
            result.append("?");
            params.forEach((k, v) -> {
                    try {
                        result.append("&").append(k).append("=").append(URLEncoder.encode(v, "UTF-8"));
                    } catch (UnsupportedEncodingException e) {
                        LOGGER.info("Unable to encode URL param '" + k + "=" + v + ": " + e.getMessage());
                        result.append("&").append(k).append("=").append(v);
                    }

            });
        }

        return  result.toString();
    }

    private Communication InitializeCommunication(HttpServletRequest request, long communicationId, Document document) throws Exception {
        String context 			= ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default");
        JSONObject communicationJson = null;
        Communication communication = null;

        CommunicationOrderEntryEditWrapper wrapper;
        if ( communicationId != -1 ) {
             communication = Communication.findById(communicationId);

            if ( communication.getDocument().isCommunicationUseBeta() ) {
                InterviewFieldValues[] interviewFields = new InterviewFieldValues[]{};

                if (communication.isDebugOrder()) {

                    String interviewJson = getInterviewFromDebugPackage(communication);
                    if (interviewJson != null) {
                        communicationJson = new JSONObject(interviewJson);

                        ObjectMapper objectMapper = new ObjectMapper();
                        interviewFields = objectMapper.readValue(communicationJson.get(Constants.Key_InterviewValues).toString(), objectMapper.getTypeFactory().constructCollectionType(List.class, InterviewFieldValues.class));
                    }

                } else {

                    Date reqInterviewDate = new Date();

                    SandboxFile refDataFile = CommunicationWSClient.retrieveRefDataFile(
                            communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
                            communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
                            communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
                            communication.getGuid(), communication.getPmgrOrderUUID()
                    );
                    SupportingDataUtil.logCommunicationAction(communication.getId(), CommunicationLogType.RETRIEVE_INTERVIEW_DATA, reqInterviewDate, new Date(), refDataFile == null);
                    if(refDataFile!= null) {
                       JSONObject interviewValues = new JSONObject(new String(refDataFile.getFileContent()));
                        String communicationJsonBackend = _cacheDataService.getInterviewDefinitionFile(communication.getDocument());
                        communicationJson = new JSONObject(communicationJsonBackend);
                        if (communicationJson != null && !communicationJson.has(Constants.Key_MetadataFormItemDefinitionVOs)) {
                            communicationJson = _migrateOnDemandCommunicationOrderUtil.migrateCommunicationInfoToBackend(communication, communicationJson);
                        }
                        ObjectMapper objectMapper = new ObjectMapper();
                        interviewFields = objectMapper.readValue(interviewValues.get(Constants.Key_InterviewValues).toString(), new TypeReference<>() {
                        });
                        communication.setInterviewValuesJson(interviewValues);
                    } else {
                        updateInterviewWithGuid(communication);
                    }
                }

                wrapper = new CommunicationOrderEntryEditWrapper(communication, Arrays.stream(interviewFields).collect(
                        Collectors.toMap(item -> (item.getId() > 0 ? item.getId() : UUID.randomUUID().getMostSignificantBits() & Long.MAX_VALUE), item -> item)
                ));
            } else {
                wrapper = new CommunicationOrderEntryEditWrapper( communication);
            }
        } else {
            wrapper = new CommunicationOrderEntryEditWrapper(  document, this._cacheDataService);
            if (wrapper.getCommunication().getDocument().isCommunicationUseBeta() ) {
                String communicationJsonString = wrapper.getCommunication().getInterviewConfiguration();
                JSONObject communicationJsonObj = new JSONObject(communicationJsonString);
                JSONArray items = (JSONArray)communicationJsonObj.get(Constants.Key_MetadataFormItemDefinitionVOs);
                if(items != null && !items.isEmpty()) {
                    JSONObject metadataItemObj = (JSONObject) items.get(0);
                    if (! metadataItemObj.has("guid") || !(metadataItemObj.has(MigrateOnDemandCommunicationOrderUtil.DATA_ELEMENT_VARIABLE)) ) {
                        if (!metadataItemObj.has("guid")){
                            _migrateOnDemandCommunicationOrderUtil.updateMetadataFormItemDefinitionWithGuid(items);
                        }
                        if (!metadataItemObj.has(MigrateOnDemandCommunicationOrderUtil.DATA_ELEMENT_VARIABLE)){
                            _migrateOnDemandCommunicationOrderUtil.updateMetadataFormItemDefinitionWithDataElementVariable(items, communicationId);
                        }
                        JSONObject newCommunicationJsonWithGuid = communicationJsonObj.put(Constants.Key_MetadataFormItemDefinitionVOs, items);
                        wrapper.getCommunication().setInterviewConfiguration(newCommunicationJsonWithGuid.toString());
                        communicationJson = newCommunicationJsonWithGuid;
                    } else {
                        communicationJson = communicationJsonObj;
                    }
                } else {
                    communicationJson = communicationJsonObj;
                }
            }
        }

        if ( context.equals("test") )
            wrapper.getCommunication().setTestOrder(true);

        communication = wrapper.getCommunication();
        communication.setMetadataFormItemArray(communicationJson);
        document = communication.getDocument();
        TouchpointSelection masterTouchpointSelection = document.getMasterTouchpointSelection();

        if(masterTouchpointSelection != null)
            SetTreeSessionData(request, masterTouchpointSelection.getId(), masterTouchpointSelection);

        // initialize layout and selection
        _currentLayout = document;

        boolean appliesVariantSelect =	(document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()) ||
                (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1);

        if (appliesVariantSelect) {
            if (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1) {
                _touchpointSelection = TouchpointSelection.findById(communication.getTouchpointSelectionId());
                long currentLayoutId = _touchpointSelection.getDocumentContext().getLong("document_context");
                _currentLayout = Document.findById(currentLayoutId);
            } else {
                _touchpointSelection = document.getMasterTouchpointSelection();
                if (_touchpointSelection != null) {
                    long currentLayoutId = _touchpointSelection.getDocumentContext().getLong("document_context");
                    _currentLayout = Document.findById(currentLayoutId);
                }
            }
        } else {
            _touchpointSelection = null;
        }
        return communication;
    }

    private JSONObject ValidateDataVariableElements(JSONObject result, Document document){

        String invalidDataItemsName = "";
        String variableWithoutDataElement = "";
        for (CommunicationOrderEntryItemDefinition item: document.getCommunicationOrderEntryItemDefinitions() ) {
            if (item.getTypeId() != MetadataFormItemType.ID_FILE && item.getTypeId() != MetadataFormItemType.ID_HEADER && item.getTypeId() != MetadataFormItemType.ID_DIVIDER && item.getDataElementVariable() == null) {
                invalidDataItemsName =	invalidDataItemsName.concat(item.getName() + "; ");
            }
            else if (item.getTypeId() != MetadataFormItemType.ID_FILE && item.getTypeId() != MetadataFormItemType.ID_HEADER && item.getTypeId() != MetadataFormItemType.ID_DIVIDER && item.getDataElementVariable() != null && AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document) == null) {
                variableWithoutDataElement = variableWithoutDataElement.concat(item.getDataElementVariable().getName() + "; ");
            }
        }

        if(!invalidDataItemsName.isEmpty() || !variableWithoutDataElement.isEmpty()) {
            result.put("success", false);
            if(!invalidDataItemsName.isEmpty()) {
                result.put("invalidDataItemsName", invalidDataItemsName);
            }
            if(!variableWithoutDataElement.isEmpty()) {
                result.put("variableWithoutDataElement", variableWithoutDataElement);
            }
        }

        return result;

    }

    private JSONObject ValidateOrder(Communication communication) {
        JSONObject result = new JSONObject();
        result.put("success", true);

        Set<CommunicationOrderEntryItem> orderEntryItems = communication.getOrderEntryItems();
        List<String> invalidFields = new ArrayList<>();
        for (CommunicationOrderEntryItem item : orderEntryItems) {
            if(item.isUniqueValue()) {
                String value = item.getValue();
                if(value != null && !value.isEmpty() && CommunicationOrderEntryItem.containsValue(item, value)) {
                    MessageFormat format = new MessageFormat(ApplicationUtil.getMessage("error.communication.order.value.is.used"));
                    invalidFields.add(format.format(new String[] { item.getName() }));
                }
            }
        }

        if(!invalidFields.isEmpty()) {
            result.put("success", false);
            result.put("invalidFields", invalidFields);
        }

        return result;
    }


    private void updateInterviewWithGuid(Communication communication) {
        LOGGER.info("Interview was updated with guid " + communication.getDocument().getName());
        CacheData cacheData = _cacheDataService.getCacheData(communication.getDocument().getGuid());
        String  jsonData = cacheData.getJsonData();
        if(jsonData != null) {
            JSONObject interviewObject = new JSONObject(jsonData);
            boolean interviewObjectWasUpdated = false;
            if (interviewObject != null) {
                JSONArray interviewItems = (JSONArray) interviewObject.get(Constants.Key_MetadataFormItemDefinitionVOs);
                int length = interviewItems.length();
                for (int i = 0; i < length; i++) {
                    if (!((JSONObject) interviewItems.get(i)).has(Constants.Key_Guid)) {
                        JSONObject updatedItem = (JSONObject) interviewItems.get(i);
                        long updatedItemId = updatedItem.getLong("id");
                        CommunicationOrderEntryItemDefinition orderItemDef = CommunicationOrderEntryItemDefinition.findById(updatedItemId);
                        updatedItem.put(Constants.Key_Guid, orderItemDef.getGuid());
                        interviewObjectWasUpdated = true;
                    } else {
                        break;
                    }
                }
                if (interviewObjectWasUpdated) {
                    interviewObject.put(Constants.Key_MetadataFormItemDefinitionVOs, interviewItems);
                    _cacheDataService.updateCacheDataJson(cacheData.getId(), interviewObject.toString());
                }
            }
        }
    }

    // END region migrate communication order on demand

    private void SetTreeSessionData(HttpServletRequest request, Long selectionId, TouchpointSelection dataBinding)
    {
        AsyncVariantTreeController.AsyncVariantTreeRequestVO params = new AsyncVariantTreeController.AsyncVariantTreeRequestVO();

        params.setApplyDocumentContext(true);
        params.setConnectedContext(true);
        params.setExpanded(true);
        params.setSelectedNodeId(selectionId == null ? "": selectionId.toString());
        params.setShowVersioning(false);

        JSONObject paramJson = new JSONObject(params);

        _requestId = DigestUtils.md5Hex(paramJson.toString() + dataBinding.hashCode() + this.hashCode());

        request.getSession().setAttribute("Parameters-" + _requestId, paramJson.toString());
        request.getSession().setAttribute("Binding-" + _requestId, dataBinding);
    }

    private static String getInterviewFromDebugPackage (Communication communication) {
        try {
            String base64DebPack = communication.getDebugPackage();
            byte[] debPack = Base64.decodeBase64(base64DebPack);

            File zipFile = null;
            String LOCATION = MessagepointMultiTenantConnectionProvider.getPodMasterCode() + "_debugPackage";
            File locationFile = new File(LOCATION);
            if (!locationFile.exists()) {
                locationFile.mkdirs();
            }

            zipFile = File.createTempFile("debPack", ".zip", locationFile);
            LOGGER.info("Unpacking debug package to " + LOCATION);
            FileOutputStream outputStream = new FileOutputStream(zipFile);
            outputStream.write(debPack);

            zipFile.deleteOnExit();
            String dirPath = FilenameUtils.removeExtension(zipFile.getPath());
            File destDir = new File(dirPath);
            if (!destDir.exists()) {
                destDir.mkdir();
            }

            ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFile));
            ZipEntry entry = zipIn.getNextEntry();
            // iterates over entries in the zip file
            while (entry != null) {
                String filePath = destDir.getPath() + File.separator + entry.getName();
                if (entry.isDirectory()) {
                    continue;
                } else {

                    File file = new File(filePath);
                    if (!file.exists()) {
                        file.getParentFile().mkdirs();
                        file.createNewFile();
                    }
                    BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath));
                    byte[] bytesIn = new byte[4096];
                    int read = 0;
                    while ((read = zipIn.read(bytesIn)) != -1) {
                        bos.write(bytesIn, 0, read);
                    }
                    bos.close();
                }
                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
            zipIn.close();
            File file = new File(dirPath, "interview.json");
            if (!file.exists() || !file.isFile()) {
                return null;
            }
            StringBuilder contentBuilder = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new FileReader(file))) {
                String sCurrentLine;
                while ((sCurrentLine = br.readLine()) != null) {
                    contentBuilder.append(sCurrentLine).append("\n");
                }
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage(), ex);
            }

            return contentBuilder.toString();
        } catch (Exception e) {
            LOGGER.error("Error while extracting interview.");
            return null;
        }

    }
}

