package com.prinova.messagepoint.controller.communication.connected;

import com.google.gson.*;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;

import java.lang.reflect.Type;

public class MetadataFormItemDefinitionSerializer implements JsonSerializer<MetadataFormItemDefinitionVO> {

    @Override
    public JsonElement serialize(MetadataFormItemDefinitionVO src, Type typeOfSrc, JsonSerializationContext context) {
        JsonObject result = new JsonObject();
        result.addProperty("primaryConnector", src.getPrimaryConnector());
        result.addProperty("maxLength", src.getMaxLength());
        result.addProperty("fieldValidationTypeId", src.getFieldValidationTypeId());
        result.addProperty("isPrimaryDriverEntry", src.getIsPrimaryDriverEntry());
        result.addProperty("guid", src.getGuid());

        JsonArray applicableVariants = new JsonArray();
        if(src.getApplicableVariants() != null) {
            src.getApplicableVariants().stream().forEach(variant -> {
                JsonObject obj = new JsonObject();
                obj.addProperty("id", variant.getId());
                applicableVariants.add(obj);
            });
        }
        result.add("applicableVariants", applicableVariants);

        if (src.getDataElementVariable() != null) {
            JsonObject newDataElement = new JsonObject();
            newDataElement.addProperty("id", src.getDataElementVariable().getId());
            newDataElement.addProperty("name", src.getDataElementVariable().getName());
            result.add("dataElementVariable", newDataElement);
        }

        JsonObject type = new JsonObject();
        if(src.getType() != null)
            type.addProperty("id", src.getType().getId());
        result.add("type", type);

        result.addProperty("id", src.getId());
        result.addProperty("name", src.getName());
        result.addProperty("parentOrder", src.getParentOrder());
        result.addProperty("description", src.getDescription());
        result.addProperty("order", src.getOrder());
        result.addProperty("displayCriteria", src.getDisplayCriteria());
        result.addProperty("isMandatory", src.getIsMandatory());
        result.addProperty("isLockedForEdit", src.getIsLockedForEdit());
        result.addProperty("textDefaultValue", src.getTextDefaultValue());
        result.addProperty("singleSelectDefaultValue", src.getSingleSelectDefaultValue());
        result.addProperty("regexValidation", src.getRegexValidation());
        result.addProperty("criteriaOperator", src.getCriteriaOperator());
        result.addProperty("repeatingDataTypeId", src.getRepeatingDataTypeId());

        JsonArray criteriaItems = new JsonArray();
        if(src.getCriteriaItems() != null)
            src.getCriteriaItems().stream().forEach(criteriaItemVO -> {
                JsonObject obj = new JsonObject();
                obj.addProperty("parentOrder", criteriaItemVO.getParentOrder());
                obj.addProperty("displayCriteria", criteriaItemVO.getDisplayCriteria());
                criteriaItems.add(obj);
            });
        result.add("criteriaItems", criteriaItems);

        JsonArray multiSelectDefaultValues = new JsonArray();
        if(src.getMultiSelectDefaultValues() != null)
            src.getMultiSelectDefaultValues().stream().forEach(i -> multiSelectDefaultValues.add(i));
        result.add("multiSelectDefaultValues", multiSelectDefaultValues);

        result.addProperty("dateDefaultValue", src.getDateDefaultValue());
        result.addProperty("defaultDateValueToTodaysDate", src.isDefaultDateValueToTodaysDate());
        result.addProperty("monthDefaultValue", src.getMonthDefaultValue());

        JsonArray menuValueItems = new JsonArray();
        if(src.getMenuValueItems() != null)
            src.getMenuValueItems().stream().forEach(i -> menuValueItems.add(i));
        result.add("menuValueItems", menuValueItems);

        result.addProperty("fieldSizeTypeId", src.getFieldSizeTypeId());
        result.addProperty("dataPrivacyTypeId", src.getDataPrivacyTypeId());
        result.addProperty("webServiceRefreshTypeId", src.getWebServiceRefreshTypeId());

        return result;
    }
}
