package com.prinova.messagepoint.controller.communication.connected;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.prinova.messagepoint.analytics.AnalyticsEvent;
import com.prinova.messagepoint.analytics.AnalyticsUtil;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.connected.data.CacheDataService;
import com.prinova.messagepoint.connected.util.ObjectMapperUtil;
import com.prinova.messagepoint.controller.communication.CommunicationOrderEntrySetupValidator;
import com.prinova.messagepoint.controller.metadata.CriteriaItemVO;
import com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionWrapper;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.communication.CommunicationDataPrivacyType;
import com.prinova.messagepoint.model.communication.CommunicationInputFormatDataElementType;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import com.prinova.messagepoint.model.communication.UploadedFileType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormFieldSizeType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.metadata.MetadataWebserviceValueRefreshType;
import com.prinova.messagepoint.model.webservice.WebServiceConfiguration;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormDefinitionService;
import com.prinova.messagepoint.query.criterion.MessagepointOrder;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class AsyncConnectedTouchpointInterviewController implements Controller {
    public static final String PARAM_TYPE = "type";
    public static final String LANGUAGE = "language";
    public static final String DOCUMENT_ID = "documentId";
    private static final String CONTENT = "content";
    private static final String METADATA_FORM_ITEMS = "metadataFormItemDefinitionVOs";
    private static final String DOCUMENT_INFORMATION_TYPE = "documentInformation";
    private static final String METADATA_FORM_ITEM_TYPE = "metadataFormItemTypes";
    private static final String METADATA_FORM_SIZE_TYPE = "metadataFormFieldSizeTypes";
    private static final String METADATA_FORM_FIELD_VALIDATION_TYPE = "metadataFormFieldValidationTypes";
    private static final String CONNECTED_DATA_PRIVACY_TYPE = "connectedDataPrivacyTypes";
    private static final String REPEATING_DATA_TYPE = "repeatingDataTypes";
    private static final String WEB_SERVICE_REFRESH_TYPES_TYPE = "webServiceRefreshTypes";
    private static final String UPLOADED_FILE_TYPES_TYPE = "uploadedFileTypes";
    private static final String CONNECTED_DATA_ELEMENTS_TYPE = "connectedDataElements";
    private static final String FILE_REFERENCE_DATE_SOURCE_TYPE = "referenceDataSource";
    private static final String DATA_FOR_MULTISELECT_MENU_TYPE = "getDataForMultiselectMenu";
    private static final String DATA_ELEMENT_TYPES = "dataElementTypes";
    private static final String DATA_SOURCE_ASSOCIATION_INFO = "dataSourceAssociationInfo";
    private static final String INPUT_FORMAT_FOR_STRING = "inputFormatString";
    private static final String INPUT_FORMAT_FOR_NUMERIC = "inputFormatNumeric";
    private static final String INPUT_FORMAT_FOR_BOOLEAN = "inputFormatBoolean";
    private static final String INPUT_FORMAT_FOR_DATE = "inputFormatDate";
    private static final String LOAD_MESSAGE_TRANSLATIONS = "loadTranslations";
    private static final String POST_SAVE = "saveUpdate";
    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss.SSS");
    private static final Log LOGGER = LogUtil.getLog(AsyncConnectedTouchpointInterviewController.class);

    private CacheDataService dataService;

    public AsyncConnectedTouchpointInterviewController(CacheDataService cacheDataService)
    {
        this.dataService = cacheDataService;
    }

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        String type = ServletRequestUtils.getStringParameter(request, PARAM_TYPE, null);
        String dataForMultiSelect = ServletRequestUtils.getStringParameter(request, DATA_FOR_MULTISELECT_MENU_TYPE, null);
        long documentId = ServletRequestUtils.getLongParameter(request, DOCUMENT_ID, -1);
        boolean fixit = false;
        if(FeatureFlag.isEnabled(FeatureFlag.Features.ConnectedDebug, request)) {
            fixit = true;
        }
        try {
            switch (type) {
                case METADATA_FORM_ITEMS: {
                    out.write(retrieveDocumentMetadataInfoByDocumentId(documentId, fixit).get());
                    break;
                }
                case DOCUMENT_INFORMATION_TYPE: {
                    out.write(retrieveDocumentBasicsInfoById(documentId).get());
                    break;
                }
                case METADATA_FORM_ITEM_TYPE: {
                    List<MetadataFormItemType> metadataFormItemTypes = MetadataFormItemType.listAll();
                    metadataFormItemTypes.remove(new MetadataFormItemType(MetadataFormItemType.ID_TEXT_EDITOR));
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(metadataFormItemTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case METADATA_FORM_SIZE_TYPE: {
                    List<MetadataFormFieldSizeType> metadataFormItemSizeTypes = MetadataFormFieldSizeType.listAll();
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(metadataFormItemSizeTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case METADATA_FORM_FIELD_VALIDATION_TYPE: {
                    List<MetadataFormInputValidationType> metadataFormValidationTypes = MetadataFormInputValidationType.listAll();
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(metadataFormValidationTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case CONNECTED_DATA_PRIVACY_TYPE: {
                    List<CommunicationDataPrivacyType> connectedDataPrivacyTypes = CommunicationDataPrivacyType.listAll();
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(connectedDataPrivacyTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case REPEATING_DATA_TYPE: {
                    List<RepeatingDataType> repeatingDataTypesTypes = RepeatingDataType.listAll();
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(repeatingDataTypesTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case INPUT_FORMAT_FOR_STRING: {
                    List<CommunicationInputFormatDataElementType> inputTypes = CommunicationInputFormatDataElementType.listAll(CommunicationInputFormatDataElementType.TYPE_STRING);
                    Optional<String> inputTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(inputTypes));
                    out.write(inputTypesResponse.get());
                    break;
                }
                case INPUT_FORMAT_FOR_BOOLEAN: {
                    List<CommunicationInputFormatDataElementType> inputTypes = CommunicationInputFormatDataElementType.listAll(CommunicationInputFormatDataElementType.TYPE_BOOLEAN);
                    Optional<String> inputTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(inputTypes));
                    out.write(inputTypesResponse.get());
                    break;
                }
                case INPUT_FORMAT_FOR_DATE: {
                    List<CommunicationInputFormatDataElementType> inputTypes = CommunicationInputFormatDataElementType.listAll(CommunicationInputFormatDataElementType.TYPE_DATE);
                    Optional<String> inputTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(inputTypes));
                    out.write(inputTypesResponse.get());
                    break;
                }
                case INPUT_FORMAT_FOR_NUMERIC: {
                    List<CommunicationInputFormatDataElementType> inputTypes = CommunicationInputFormatDataElementType.listAll(CommunicationInputFormatDataElementType.TYPE_NUMBER);
                    Optional<String> inputTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(inputTypes));
                    out.write(inputTypesResponse.get());
                    break;
                }
                case WEB_SERVICE_REFRESH_TYPES_TYPE: {
                    List<MetadataWebserviceValueRefreshType> webServiceRefreshTypes = MetadataWebserviceValueRefreshType.listAll();
                    Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                            .writeValueAsString(webServiceRefreshTypes));
                    out.write(allTypesResponse.get());
                    break;
                }
                case UPLOADED_FILE_TYPES_TYPE: {
                    out.write(getFileTypes().get());
                    break;
                }
                case CONNECTED_DATA_ELEMENTS_TYPE: {
                    out.write(getConnectedDataElementVariables(documentId).get());
                    break;
                }
                case DATA_FOR_MULTISELECT_MENU_TYPE: {
                    out.write(getDataForMultiSelectMenu(documentId, dataForMultiSelect).get());
                    break;
                }
                case FILE_REFERENCE_DATE_SOURCE_TYPE: {
                    out.write(getFileReferenceDataSources(documentId).get());
                    break;
                }
                case DATA_ELEMENT_TYPES:{
                    out.write(GetDataElementTypes().get());
                    break;
                }
                case DATA_SOURCE_ASSOCIATION_INFO:{
                    out.write(retrieveDataAssociationInfo(documentId).get());
                    break;
                }
                case POST_SAVE: {
                    String content = ServletRequestUtils.getStringParameter(request, CONTENT, null);
                    MetadataFormDefinitionWrapper wrapper = new MetadataFormDefinitionWrapper();
                    Gson gson = new GsonBuilder()
                            .registerTypeAdapter(DataSource.class, new AbstractDataSourceDeserializer())
                            .create();
                    wrapper = gson.fromJson(content, MetadataFormDefinitionWrapper.class);
                    WebServiceConfiguration config = gson.fromJson(new JSONObject(content).getJSONObject("document").getJSONObject("communicationDataFeedWebService").toString(), WebServiceConfiguration.class);
                    Document document = Document.findById(documentId);
                    if (config.getUrl() != null)
                        document.getCommunicationDataFeedWebService().setUrl(config.getUrl());
                    if (config.getUsername() != null)
                        document.getCommunicationDataFeedWebService().setUsername(config.getUsername());
                    if (config.getUpdatedPassword() != null)
                        document.getCommunicationDataFeedWebService().setHashedPassword(config.getUpdatedPassword());
                    document.setCommunicationAppliesTouchpointSelection(new JSONObject(content).getJSONObject("document").getBoolean("communicationAppliesTouchpointSelection"));
                    wrapper.setDocument(document);
                    out.write(updateMetadataFormDefinitionItem(wrapper, content, fixit).toString());
                    break;
                }
                case LOAD_MESSAGE_TRANSLATIONS: {
                    String language = ServletRequestUtils.getStringParameter(request, LANGUAGE, null);
                    List<String> list = Arrays.asList("page.label.save.complete", "page.label.save", "page.label.order.entry.setup", "page.text.touchpoint.does.not.apply.for.order.entry",
                            "page.label.url", "page.label.username", "page.label.password", "page.text.click.button.to.add.items", "page.label.indicator", "page.text.order.entry.item.is.primary.drive",
                            "page.label.primary", "page.label.add.menu.item", "page.label.remove.menu.item", "page.label.remove.connection", "page.label.add.connection",
                            "page.label.description", "page.label.type", "page.label.variable", "page.label.criteria","page.label.criteria.operator", "page.label.connector",
                            "page.label.mandatory", "page.label.items", "page.text.no.menu.value.items", "page.label.request", "page.label.file.type",
                            "page.label.reference.data.source", "page.label.auxiliary.data.connections", "page.label.applicable.variants",
                            "page.label.size", "page.label.max.Length", "page.text.unlimited", "page.label.default.value",
                            "page.label.default.value", "page.label.unique.value", "page.label.validation", "page.label.privacy", "page.label.indicator",
                            "page.label.criteria", "page.label.connector", "page.label.mandatory", "page.label.items", "page.text.no.menu.value.items",
                            "page.label.request", "page.label.file.type", "page.label.add.child", "page.label.auxiliary.data.connections", "page.label.applicable.variants",
                            "page.label.size", "page.text.unlimited", "page.text.select.no.default.style", "page.label.todays.date", "page.label.date.selection",
                            "page.text.select.date", "page.label.date.selection", "page.text.no.touchpoints.available.to.you",
                            "error.message.order.entry.unique.connector.values.required", "error.message.order.entry.unique.connector.values.required", "error.message.reference.source.required.for.reference.file.type",
                            "error.message.default.value.not.match.validation.type", "error.message.order.entry.menu.items.required", "error.message.order.entry.data.element.required", "error.message.order.entry.data.reference.required", "error.message.order.entry.data.reference.connector.parameter.required",
                            "error.message.order.entry.display.criteria.required", "error.cannot.change.primary.driver.once.communications.authored", "error.order.entry.primary.driver.must.be.selected", "page.label.add.item",
                            "page.label.enabled", "page.label.disabled", "page.text.order.entry.item.is.indicato", "page.label.remove.item.plus.children", "page.label.promote", "page.label.nest", "page.label.move.up", "page.label.move.down",
                            "page.label.yes", "page.label.no", "page.label.expand.all", "page.label.collapse.all",
                            "page.label.order.entry.type.date.day.month.year", "page.label.order.entry.type.date.month.year", "page.label.order.entry.type.file",
                            "page.label.order.entry.type.multiselect.menu", "page.label.order.entry.type.select.menu", "page.label.order.entry.type.text.editor", "page.label.order.entry.type.text",
                            "page.label.order.entry.type.textarea", "page.label.variant.selection", "page.label.order.entry.type.web.service.menu", "page.label.order.entry.type.web.service.multiselect.menu",
                            "page.label.order.entry.type.web.service.text", "client_messages.calendar.month_name_short.april", "client_messages.calendar.month_name_short.august",
                            "client_messages.calendar.month_name_short.december", "client_messages.calendar.month_name_short.february", "error.message.order.entry.primary.datasource.required",
                            "client_messages.calendar.month_name_short.january", "client_messages.calendar.month_name_short.july",
                            "client_messages.calendar.month_name_short.june", "client_messages.calendar.month_name_short.march",
                            "client_messages.calendar.month_name_short.may", "client_messages.calendar.month_name_short.november",
                            "client_messages.calendar.month_name_short.october", "client_messages.calendar.month_name_short.september",
                            "page.label.data.web.service", "page.label.attribute.type", "error.input.mandatory", "page.label.uploaded.file.pass.through", "page.label.input.formatting", "page.label.primary.data.variable", "page.label.reference.data.variable",
                            "page.label.empty.value", "page.label.any.value", "page.label.specific.value", "page.label.locked.for.editing", "error.order.entry.duplicate.order.number", "page.label.fix.duplicates", "error.message.order.entry.only.one.drive.file", "page.label.connected.parent", "page.label.datatype",
                            "page.label.connected.addCriteria", "error.message.order.entry.criteria.not.correct.defined", "error.message.order.entry.criteria.empty",  "error.message.duplicate.primary.order", "error.message.duplicate.indicator.order", "page.label.regex.validation", "error.message.order.entry.regex.validation", "page.label.save.in.progress", "error.message.order.entry.data.element.variable.required",
                            "error.order.entry.primary.variable.reference.variable.must.be.selected", "page.label.group.with.above", "page.label.group.repeating.data", "error.message.each.group.should.start.with.repeating");
                    out.write(loadTranslationsMessages(language, list).toString());
                }
                default: {
                    break;
                }
            }
        } catch (JsonProcessingException e) {
            LOGGER.error("Error - Unable to resolve request for interview: " + e.getMessage(), e);
        }
        out.flush();
        return null;
    }

    private boolean createConnectedDataSourceReference(Document document, List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId,  Long referenceDataVariableId ) {
        try {
            ConnectedReferenceDataSourceUtil connectedReferenceDataSourceUtil = new ConnectedReferenceDataSourceUtil();
            return connectedReferenceDataSourceUtil.CreateDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
        } catch (Exception ex) {
            return false;
        }
    }

    private JSONObject loadTranslationsMessages(final String language, final List<String> propertiesList) {
        JSONObject jsonTranslation = new JSONObject();
        String defaultTranslationFile = "WEB-INF/i18n/messages.properties";
        StringBuilder fileName = new StringBuilder();
        fileName.append("WEB-INF/i18n/messages");
        if (!language.equals("en")) {
            fileName.append("_");
            fileName.append(language);
        }
        fileName.append(".properties");
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName.toString());
        InputStream defaultInputStream = getClass().getClassLoader().getResourceAsStream(defaultTranslationFile);
        if (inputStream != null && defaultInputStream != null) {
            try {
                Properties propMessages = new Properties();
                propMessages.load(inputStream);
                Properties defaultPropMessages = new Properties();
                defaultPropMessages.load(defaultInputStream);
                propertiesList.forEach(property -> {
                    String translation = propMessages.getProperty(property);
                    if (translation == null || translation.isEmpty()) {
                        translation = defaultPropMessages.getProperty(property);
                    }
                    jsonTranslation.put(property, translation);
                });
            } catch (IOException e) {
                LOGGER.error("Error - Unable to load properties file: " + e.getMessage(), e);
            }
        } else {
            if (inputStream == null && defaultInputStream != null) {
                try {
                    Properties defaultPropMessages = new Properties();

                    defaultPropMessages.load(defaultInputStream);
                    propertiesList.forEach(property -> {
                        String translation = defaultPropMessages.getProperty(property);
                        jsonTranslation.put(property, translation);
                    });
                } catch (IOException e) {
                    LOGGER.error("Error - Unable to load from default properties file: " + e.getMessage(), e);
                }
            }
        }

        return jsonTranslation;
    }

    private Optional<String> getFileReferenceDataSources(final Long documentId) {
        Document document = Document.findById(documentId);
        if (document == null) {
            return Optional.empty();
        }

        List<DataSource> results = new ArrayList<>();
        document.getNonConnectedReferenceDataSources().forEach(dataSource -> {
            dataSource.setDataRecords(null);
            results.add(dataSource);
        });

        return Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(results));
    }

    private Optional<String> getConnectedDataElementVariables(final Long documentId) {
        Document document = Document.findById(documentId);
        if (document == null) {
            return Optional.empty();
        }

        List<DataElementVariable> connectedDataElements = DataElementVariable.findAllDataElementVariableForConnectedReference();

        return Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(connectedDataElements));
    }

    private Optional<String> GetDataElementTypes(){
        List<DataSubtype> dataSubtypesAll = HibernateUtil.getManager().getObjects(DataSubtype.class, MessagepointOrder.asc("id"));
        List<DataSubtype> dataSubtypes = dataSubtypesAll.stream().filter(data-> data.getId() != 998 && data.getId() != 999).toList();
        dataSubtypes.forEach(subType -> {
            subType.setName(ApplicationUtil.getMessage(subType.getName()));
        });
        return Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(dataSubtypes));
    }

    private Optional<String> getDataForMultiSelectMenu(final Long documentId, final String selectedIds) {
        List<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.in("id", getSelectedIds(selectedIds)));
        List<TouchpointSelection> selectedItems = HibernateUtil.getManager().getObjectsAdvanced(TouchpointSelection.class, critList);
        List<TouchpointSelection> items = TouchpointSelection.findAllByDocument(Document.findById(documentId));

        selectedItems.forEach(currentSelectedItem -> {
            if (!items.contains(currentSelectedItem))
                items.add(currentSelectedItem);
        });

        List<ApplicableVariantsUi> allApplicableVariants = new ArrayList<>();
        items.forEach(currentItem -> {
            allApplicableVariants.add(ApplicableVariantsUi.Builder.newInstance()
                    .withId(currentItem.getId())
                    .withName(currentItem.getNameWithContext())
                    .withSelection(selectedItems.contains(currentItem))
                    .build());
        });

        return Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(allApplicableVariants));
    }

    private ResponseEntity<String> updateMetadataFormDefinitionItem(final MetadataFormDefinitionWrapper wrapper, String jsonContent, boolean fixit) {
        CommunicationOrderEntrySetupValidator validator = new CommunicationOrderEntrySetupValidator();
        Errors errors = new BeanPropertyBindingResult(wrapper, "");
        validator.validateNotGenericInputs(wrapper, errors);
        if (errors.hasErrors()) {
            StringBuilder sb = new StringBuilder();
            sb.append(DATE_FORMAT.format(new Date()));
            sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
            sb.append(" service call has errors on save");
            sb.append(" in ").append(this.getClass().getName());
            LOGGER.error(sb.toString());
            return new ResponseEntity<>("{\"errors\": "  + ObjectMapperUtil.writeValueAsString(errors.getAllErrors()) + ", \"fixit\": " + fixit + "}",
                    HttpStatus.UNPROCESSABLE_ENTITY);
        }

        long primaryDataVariableId = wrapper.getPrimaryDataVariableId();
        long referenceDataVariableId = wrapper.getReferenceDataVariableId();

        boolean createDataSource = createConnectedDataSourceReference(wrapper.getDocument(), wrapper.getMetadataFormItemDefinitionVOs(), primaryDataVariableId, referenceDataVariableId);
        if(!createDataSource) {
            return new ResponseEntity<>("Unable to update/create connected reference data source", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.OrderEntrySetup);
        try {
            ServiceExecutionContext context = CreateOrUpdateMetadataFormDefinitionService
                    .createContextForCommunicationFormUpdate(wrapper.getDocument(), wrapper.getMetadataFormItemDefinitionVOs(), UserUtil.getPrincipalUser());
            Service service = MessagepointServiceFactory.getInstance()
                    .lookupService(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME, CreateOrUpdateMetadataFormDefinitionService.class);
            service.execute(context);
            ServiceResponse serviceResponse = context.getResponse();

            analyticsEvent.setAction(Actions.Setup);

            if (serviceResponse.isSuccessful()) {
                StringBuilder sb = new StringBuilder();
                sb.append(DATE_FORMAT.format(new Date()));
                sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
                sb.append(" service call successful save");
                sb.append(" in ").append(this.getClass().getName());
                LOGGER.debug(sb.toString());

                Optional<String> metadataFormDefinitionItems = retrieveDocumentMetadataInfoByDocumentId(wrapper.getDocument().getId(),false);
                if(metadataFormDefinitionItems.isPresent()){
                    JSONObject jsonContentObj = new JSONObject(jsonContent);
                    JSONObject metadataFormDefinitionItemsJsonObj = new JSONObject(metadataFormDefinitionItems.get());
                    jsonContentObj.remove(METADATA_FORM_ITEMS);
                    jsonContentObj.put(METADATA_FORM_ITEMS,metadataFormDefinitionItemsJsonObj.get("items") );
                    jsonContent = jsonContentObj.toString();
                }

                this.dataService.UpdateConnectedInterviewMetadataItems(wrapper.getDocument().getGuid(), jsonContent);

                return new ResponseEntity<>(HttpStatus.ACCEPTED);
            }
        } catch (Exception ex) {
            StringBuilder sb = new StringBuilder();
            sb.append(DATE_FORMAT.format(new Date()));
            sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
            sb.append(" service call is not successful ");
            sb.append(" in ").append(this.getClass().getName());
            sb.append(" Order Entry Setup was not saved. ");
            LOGGER.error(sb.toString());
            return new ResponseEntity<>(sb.toString(), HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            analyticsEvent.send();
        }
        StringBuilder sb = new StringBuilder();
        sb.append(DATE_FORMAT.format(new Date()));
        sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
        sb.append(" service call is not successful ");
        sb.append(" in ").append(this.getClass().getName());
        sb.append(" Order Entry Setup was not found. ");
        LOGGER.debug(sb.toString());
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    private List<Long> getSelectedIds(final String selectedIdsValue) {
        List<Long> ids = new ArrayList<>();

        if (selectedIdsValue != null) {
            String[] idArr = selectedIdsValue.split(",");
            for (String currentId : idArr) {
                ids.add(Long.valueOf(currentId));
            }
        }
        if (ids.isEmpty())
            ids.add(0L);

        return ids;
    }

    private Optional<String> retrieveDocumentMetadataInfoByDocumentId(final long documentId, boolean fixit)  {

            Document document = Document.findById(documentId);
            if (document == null) {
                return Optional.empty();
            }
            List orderList = new ArrayList<Integer>();
            List<MetadataFormItemDefinitionVO> metadataFormItemDefinitionVOs = new ArrayList<>();
            document.getCommunicationOrderEntryItemDefinitionsInOrder().forEach(currentItemDefinition -> {
                if (currentItemDefinition != null) {
                    MetadataFormItemDefinitionVO newObject = new MetadataFormItemDefinitionVO();
                    try {
                        newObject = new MetadataFormItemDefinitionVO(currentItemDefinition);
                    } catch (Exception ex){
                       throw new RuntimeException(ex);
                    }
                    DataElementVariable var = currentItemDefinition.getDataElementVariable();
                    if (var != null) {
                        DataElementVariable newDataElement = new DataElementVariable();
                        newDataElement.setId(var.getId());
                        newDataElement.setName(var.getName());
                        newDataElement.setGuid(var.getGuid());
                        newObject.setDataElementVariable(newDataElement);
                        AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(var.getId(), document);
                        if (ade != null){
                            newObject.setDataElementTypeId(ade.getDataSubtypeId());
                            newObject.setDataElementInputTypeFormat(ade.getExternalFormatText());
                        }
                    }
                    if (!currentItemDefinition.getConnectorVariableMap().isEmpty()) {
                        List<DataElementVariable> connectorDataElementVariables = new ArrayList<>();
                        currentItemDefinition.getConnectorVariableMap().keySet().forEach(key -> {
                            DataElementVariable elementData = currentItemDefinition.getConnectorVariableMap().get(key);
                            if (!connectorDataElementVariables.contains(elementData)) {
                                DataElementVariable newDataElement = new DataElementVariable();
                                newDataElement.setId(elementData.getId());
                                newDataElement.setName(elementData.getName().toString());
                                connectorDataElementVariables.add(newDataElement);
                            }
                        });
                        newObject.setConnectorDataElementVariables(connectorDataElementVariables);
                    }
                    if ( (currentItemDefinition.getCriteriaTriggerValuesJson()== null || currentItemDefinition.getCriteriaTriggerValuesJson().isEmpty() || currentItemDefinition.getCriteriaTriggerValuesJson().equals( "[]" )) && currentItemDefinition.getParentItemOrder() != null && currentItemDefinition.getParentItemOrder() >0 && currentItemDefinition.getDisplayTriggerValues() != null && !currentItemDefinition.getDisplayTriggerValues().isEmpty() ) {
                        List<CriteriaItemVO> criteriaItems = new ArrayList<>();
                        CriteriaItemVO criteriaItem = new CriteriaItemVO();
                        criteriaItem.setDisplayCriteria(currentItemDefinition.getDisplayTriggerValues());
                        criteriaItem.setParentOrder(currentItemDefinition.getParentItemOrder());
                        criteriaItems.add(criteriaItem);
                        newObject.setCriteriaItems(criteriaItems);
                    }

                    if (currentItemDefinition.getUploadedFileDataSource() != null) {
                        DataSource newDataSource = new DataSource();
                        newDataSource.setDataRecords(null);
                        newDataSource.setId(currentItemDefinition.getUploadedFileDataSource().getId());
                        newDataSource.setName(currentItemDefinition.getUploadedFileDataSource().getName());
                        newDataSource.setCreated(null);
                        newObject.setUploadedFileDataSource(newDataSource);
                    }

                    if (newObject.getApplicableVariants() != null) {
                        Set<TouchpointSelection> allApplicableVariants = new HashSet<>();
                        newObject.getApplicableVariants().forEach(applicableVariant -> {
                            allApplicableVariants.add(removeUnusedFieldsInApplicableVariants(applicableVariant));
                        });
                        newObject.setApplicableVariants(allApplicableVariants);
                    }
                    orderList.add(newObject.getOrder());
                    metadataFormItemDefinitionVOs.add(newObject);
                }
            });

            boolean hasDuplicateOrder = false;
            boolean hasOrphanItems = false;
            List duplicateList = new ArrayList<Integer>();
            for (MetadataFormItemDefinitionVO currentVO : metadataFormItemDefinitionVOs) {
                if (duplicateList.contains(currentVO.getOrder())) {
                    hasDuplicateOrder = true;
                }
                if (currentVO.getParentOrder() != null && !orderList.contains(currentVO.getParentOrder())) {
                    hasOrphanItems = true;
                }
                duplicateList.add(currentVO.getOrder());
            }
            // "[{"codes":["error.order.entry.duplicate.order.number"],"arguments":null,"defaultMessage":null,"objectName":""}]"
            if (hasDuplicateOrder || hasOrphanItems) {
                MetadataFormDefinitionWrapper wrapper = new MetadataFormDefinitionWrapper();
                Errors errors = new BeanPropertyBindingResult(wrapper, "");
                errors.reject("error.order.entry.duplicate.order.number");
                return Optional.ofNullable("{\"items\":" + ObjectMapperUtil
                        .writeValueAsString(metadataFormItemDefinitionVOs) + ", \"warn\": " + ObjectMapperUtil.writeValueAsString(errors.getAllErrors()) +
                        ", \"fixit\": " + fixit + "}");
            } else if(  document.getPrimaryDataSource() == null) {
                MetadataFormDefinitionWrapper wrapper = new MetadataFormDefinitionWrapper();
                Errors errors = new BeanPropertyBindingResult(wrapper, "");
                errors.reject("error.message.order.entry.primary.datasource.required");
                return Optional.ofNullable("{\"items\":" + ObjectMapperUtil
                        .writeValueAsString(metadataFormItemDefinitionVOs) + ", \"dataCollectionMissing\": " + ObjectMapperUtil.writeValueAsString(errors.getAllErrors()) +  "}");
            } else {
                return Optional.ofNullable("{\"items\":" + ObjectMapperUtil
                        .writeValueAsString(metadataFormItemDefinitionVOs) + "}");
            }
    }


    private Optional<String> retrieveDocumentBasicsInfoById(final long documentId) throws JsonProcessingException {
        Document document = Document.findById(documentId);
        if (document == null) {
            return Optional.empty();
        }

        WebServiceConfiguration webServiceConfiguration = document.getCommunicationDataFeedWebService();
        DocumentUi documentLazyObj = DocumentUi.Builder.newInstance()
                .withEnableForVariation(document.isEnabledForVariation())
                .withCommunicationDataFeedWebServiceUi(webServiceConfiguration.getUrl(),
                        webServiceConfiguration.getUsername(),
                        webServiceConfiguration.getHashedPassword())
                .withCommunicationAppliesTouchpointSelection(document.isCommunicationAppliesTouchpointSelection())
                .withCommunicationOrderEntryEnabled(document.isCommunicationOrderEntryEnabled())
                .build();
        return Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(documentLazyObj));
    }

    private Optional<String> retrieveDataAssociationInfo(final long documentId) throws JsonProcessingException {
        Document document = Document.findById(documentId);
        if (document == null) {
            return Optional.empty();
        }
        Long primaryDataVariableId = null;
        Long referenceDataVariableId = null;
        for (DataSource dataSource : document.getReferenceDataSources()) {
            if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                DataSource connectedDataSource = dataSource;
                for (ReferenceConnection referenceConnection : document.getDataSourceAssociation().getReferenceConnections()) {
                    if (referenceConnection.getReferenceDataSource().getName().equals(connectedDataSource.getName())) {
                        if (referenceConnection.getPrimaryVariable() != null) {
                            primaryDataVariableId = referenceConnection.getPrimaryVariable().getId();
                        }
                        if (referenceConnection.getReferenceVariable() != null) {
                            referenceDataVariableId = referenceConnection.getReferenceVariable().getId();
                        }
                    }

                }
            }
        }
        List<DataElementVariable> primaryDataElementVariables = DataElementVariable.findAllDataElementVariableForPrimaryDataSourceByDocument(document);


        return Optional.ofNullable("{\"primaryDataVariableId\":" + ObjectMapperUtil
                .writeValueAsString(primaryDataVariableId) + ", \"referenceDataVariableId\": " + ObjectMapperUtil.writeValueAsString(referenceDataVariableId)  +  ", \"primaryDataElementVariables\": " + ObjectMapperUtil.writeValueAsString(primaryDataElementVariables)  +"}");
    }

    private Optional<String> getFileTypes() {
        List<UploadedFileType> uploadedFileType = UploadedFileType.listAll();
        uploadedFileType.stream().forEach(fileType -> {
            if (fileType.getId() == 3) {
                fileType.setDisplayMessageCode("page.label.uploaded.file.pass.through");
                fileType.setName("Pass through");
            }
        });
        Optional<String> allTypesResponse = Optional.ofNullable(ObjectMapperUtil
                .writeValueAsString(uploadedFileType));
        return allTypesResponse;
    }

    private TouchpointSelection removeUnusedFieldsInApplicableVariants(TouchpointSelection applicableVariant){
        // update applicable variant
        applicableVariant.setDocument(null);
        applicableVariant.setAlternateLayout(null);
        applicableVariant.setWorkflowAction(null);
        applicableVariant.setConnectedWorkflow(null);
        applicableVariant.setMetadataForm(null);
        applicableVariant.setConnectedVisibleUsers(null);
        applicableVariant.setTemplateModifiers(null);
        applicableVariant.setUpdated(null);
        applicableVariant.setCheckoutTimestamp(null);
        applicableVariant.setCheckinTimestamp(null);
        applicableVariant.setLatestProductionDate(null);
        applicableVariant.setContentLastUpdated(null);
        applicableVariant.setCreated(null);
        ParameterGroupTreeNode node = new ParameterGroupTreeNode();
        applicableVariant.getParameterGroupTreeNode();
        node.setName(applicableVariant.getParameterGroupTreeNode().getName());
        node.setId(applicableVariant.getParameterGroupTreeNode().getId());
        node.setCreated(null);
        applicableVariant.setParameterGroupTreeNode(node);
        applicableVariant.setOriginObject(null);
        applicableVariant.setProofDefinitions(null);
        applicableVariant.setMetadataForm(null);
        applicableVariant.setLatestProductionDate(null);
        applicableVariant.setContentLastUpdated(null);
        return applicableVariant;
    }
}
