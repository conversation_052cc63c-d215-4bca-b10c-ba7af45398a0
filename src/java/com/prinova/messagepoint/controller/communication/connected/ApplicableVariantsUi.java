package com.prinova.messagepoint.controller.communication.connected;

import java.io.Serializable;

public class ApplicableVariantsUi implements Serializable {

    private boolean isSelected;

    private String name;

    private long id;

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selectedValue) {
        isSelected = selectedValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static class Builder {
        private ApplicableVariantsUi applicableVariantsUi;

        public static ApplicableVariantsUi.Builder newInstance() {
            return new ApplicableVariantsUi.Builder();
        }

        private Builder() {
            this.applicableVariantsUi = new ApplicableVariantsUi();
        }

        public ApplicableVariantsUi.Builder withSelection(boolean selectedValue) {
            this.applicableVariantsUi.setSelected(selectedValue);
            return this;
        }

        public ApplicableVariantsUi.Builder withName(String nameValue) {
            this.applicableVariantsUi.setName(nameValue);
            return this;
        }

        public ApplicableVariantsUi.Builder withId(long idValue) {
            this.applicableVariantsUi.setId(idValue);
            return this;
        }

        public ApplicableVariantsUi build() {
            return applicableVariantsUi;
        }
    }
}
