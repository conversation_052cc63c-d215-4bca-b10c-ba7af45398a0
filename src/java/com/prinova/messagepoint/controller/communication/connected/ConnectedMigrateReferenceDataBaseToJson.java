package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceAssociationService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceServiceRequest;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate5.SessionFactoryUtils;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.support.TransactionSynchronizationManager;


import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ConnectedMigrateReferenceDataBaseToJson {
    private final List<Node> allNodes;
    private static final Log log = LogUtil.getLog(ConnectedMigrateCommunicationJsonBackgroundTask.class);


    public ConnectedMigrateReferenceDataBaseToJson(List<Node> allNodes) {
        this.allNodes = allNodes;
    }

    public void performMainProcessing() {
        for (Node node : allNodes) {
            String schemaName = node.getSchemaName();
            if (StringUtils.isNotEmpty(schemaName)) {
                    migrateConnectedReferenceDataBaseToJson(schemaName);
            }
        }
    }

    private SessionHolder useSchemaName(String schemaName) {
        if (StringUtils.isEmpty(schemaName)) {
            return null;
        }
        SessionFactory sessionFactory = HibernateUtil.getManager().getSessionFactory();

        if (TransactionSynchronizationManager.hasResource(sessionFactory)) {
            SessionHolder sessionHolder = (SessionHolder) TransactionSynchronizationManager.unbindResource(sessionFactory);
            SessionFactoryUtils.closeSession(sessionHolder.getSession());
        }

        MessagepointCurrentTenantIdentifierResolver.setTenantIdentifier(schemaName);
        Session session = HibernateUtil.getManager().openSessionInView();
        SessionHolder sessionHolder = new SessionHolder(session);
        TransactionSynchronizationManager.bindResource(sessionFactory, new SessionHolder(session));
        return sessionHolder;
    }

    public void migrateConnectedReferenceDataBaseToJson(String schemaName) {
        SessionHolder sessionHolder = useSchemaName(schemaName);
        try {
            List<Document> documents = Document.findAll();
            if (!documents.isEmpty()) {
                sessionHolder.getSession().beginTransaction();
                LogUtil.getLog(MessagePointStartUp.class).info("Start migration for connected reference data Base:  " + schemaName);
                for (Document document : documents) {
                    DataSourceAssociation dsa = document.getDataSourceAssociation();
                    if (document.isCommunicationUseBeta() && !isCollectionLinkedToOldConnectedDocument(dsa, documents)) {
                        if (dsa != null) {
                            long referenceDataVariableId = -1;
                            long primaryDataVariableId = -1;
                            for (ReferenceConnection connection : dsa.getReferenceConnections()) {
                                DataSource dataSource = connection.getReferenceDataSource();
                                if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED && dataSource.isDelimited()) {
                                    DataElementVariable referenceDataVariable = connection.getReferenceVariable();
                                    DataElementVariable primaryDataVariable = connection.getPrimaryVariable();
                                    if(referenceDataVariable != null && primaryDataVariable != null) {
                                        dataSource.setName("DELETED_OLD_" + dataSource.getName());
                                        LogUtil.getLog(MessagePointStartUp.class).info("Begin migration for document :  " + document.getName());
                                        referenceDataVariableId = referenceDataVariable.getId();
                                        primaryDataVariableId = primaryDataVariable.getId();
                                        dsa.getReferenceConnections().remove(connection);
                                        dsa.save();
                                        ReferenceConnection.delete(connection.getId());
                                        document.getReferenceDataSources().remove(connection.getReferenceDataSource());
                                        document.save();
                                        sessionHolder.getSession().flush();
                                        createDataSource(document, dataSource, primaryDataVariableId, referenceDataVariableId);
                                    }
                                }
                            }
                        }
                    }
                }
                sessionHolder.getSession().getTransaction().commit();
            }

        } catch (Exception ex) {
            sessionHolder.getSession().getTransaction().rollback();
            LogUtil.getLog(MessagePointStartUp.class).error("cannot convert connected reference data source to json for data Base:  " + schemaName + ex.getMessage());
        }
    }

    public boolean isCollectionLinkedToOldConnectedDocument(DataSourceAssociation dsa,  List<Document> documents){
        for (Document document : documents) {
            if (!document.isCommunicationUseBeta() && document.getDataSourceAssociation() != null && dsa!=null && document.getDataSourceAssociation().getId() == dsa.getId() ) {
                return true;
            }
        }
        return false;
    }

    public void createDataSource(Document document, DataSource oldDataSource, Long primaryDataVariableId, Long referenceDataVariableId){
        DataSourceAssociation dsa = document.getDataSourceAssociation();
        if(dsa != null) {
            ServiceExecutionContext updateDataSourceServiceContext = UpdateDataSourceService.createContext();
            UpdateDataSourceServiceRequest updateDataSourceServiceRequest = (UpdateDataSourceServiceRequest) updateDataSourceServiceContext.getRequest();
            DataSource dataSource = new DataSource();
            String name = ConnectedReferenceDataSourceUtil.generateUniqueName(document.getName());
            dataSource.setName(name);
            dataSource.setEncodingType(EncodingType.findById(EncodingType.TYPE_ASCII));
            dataSource.setSourceType(SourceType.findById(SourceType.TYPE_REFERENCE_CONNECTED));
            dataSource.setLayoutType(LayoutType.findById(LayoutType.TYPE_JSON));
            updateDataSourceServiceRequest.setDataSource(dataSource);
            Service updateDataSourceService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceService.SERVICE_NAME, UpdateDataSourceService.class);
            updateDataSourceService.execute(updateDataSourceServiceContext);
            document.getReferenceDataSources().add(dataSource);
            document.save();
            JSONDataDefinition rootDefinition = ConnectedReferenceDataSourceUtil.createJsonRootDefinition(dataSource);

            Set<AbstractDataElement> oldDataElements = oldDataSource.getDataElements();

            for (AbstractDataElement item : oldDataElements) {
                createJsonData(rootDefinition, item, dataSource);
            }

            ReferenceConnection referenceConnection = new ReferenceConnection();
            referenceConnection.setReferenceDataSource(dataSource);
            referenceConnection.setConnectorParameter(ConnectedReferenceDataSourceUtil.CONNECTED_REFERENCE_CONNECTOR_PARAMETER);
            if (referenceDataVariableId > 0) {
                referenceConnection.setReferenceVariable(DataElementVariable.findById(referenceDataVariableId));
            }
            if (primaryDataVariableId > 0) {
                referenceConnection.setPrimaryVariable(DataElementVariable.findById(primaryDataVariableId));
            }
            referenceConnection.setPrimaryCompoundKey(null);
            referenceConnection.setReferenceCompoundKey(null);
            Set<ReferenceConnection> referenceConnections = new HashSet<>();
            referenceConnections.add(referenceConnection);
            ServiceExecutionContext ctx = UpdateDataSourceAssociationService.createContext(dsa.getId(),
                    dsa.getName(),
                    dsa.getPrimaryDataSource(),
                    referenceConnections,
                    dsa.getCustomerDataElementId());
            Service updateDataSourceAssociationService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceAssociationService.SERVICE_NAME, UpdateDataSourceAssociationService.class);
            updateDataSourceAssociationService.execute(ctx);
        }
    }

    public static void createJsonData(JSONDataDefinition parent, AbstractDataElement oldDataElement, DataSource  connectedDataSource) {
        List<DataElementVariable> dataElementVariables = DataElementVariable.findAllDataElementVariableByDataElementId(oldDataElement.getId());

        JSONDataDefinition dataDefinitionKey = new JSONDataDefinition();
        dataDefinitionKey.setDataSource(connectedDataSource);
        dataDefinitionKey.setDefinitionType(JSONDefinitionType.ID_KEY);
        dataDefinitionKey.setParentDefinition(parent);
        if(dataElementVariables != null && !dataElementVariables.isEmpty()) {
            dataDefinitionKey.setName(dataElementVariables.get(0).getGuid());
        } else {
            dataDefinitionKey.setName(oldDataElement.getName() + "_unused" );

        }
        HibernateUtil.getManager().saveObject(dataDefinitionKey);


        JSONDataDefinition dataDefinition = new JSONDataDefinition();
        dataDefinition.setDataSource(connectedDataSource);
        dataDefinition.setDefinitionType(JSONDefinitionType.ID_OBJECT);
        dataDefinition.setJsonDataElement(null);
        dataDefinition.setStartDataGroup(false);
        dataDefinition.setName(JSONDefinitionType.getObjectType().getName().toUpperCase());
        dataDefinition.setDataGroup(null);
        dataDefinition.setParentDefinition(dataDefinitionKey);
        HibernateUtil.getManager().saveObject(dataDefinition);

        JSONDataDefinition dataDefinitionValue = new JSONDataDefinition();
        dataDefinitionValue.setDataSource(connectedDataSource);
        dataDefinitionValue.setDefinitionType(JSONDefinitionType.ID_KEY);
        dataDefinitionValue.setParentDefinition(dataDefinition);
        dataDefinitionValue.setName("VALUE");
        JSONDataElement jsonDataElement = new JSONDataElement();
        ConnectedReferenceDataSourceUtil.setDataElement(jsonDataElement, oldDataElement);
        dataDefinitionValue.setJsonDataElement(jsonDataElement);
        jsonDataElement.save();
        HibernateUtil.getManager().saveObject(dataDefinitionValue);
        if (dataElementVariables != null && !dataElementVariables.isEmpty()) {
            for (DataElementVariable dataElementVariable : dataElementVariables) {
                VariableDataElementMap demap = new VariableDataElementMap();
                demap.setDataElement(jsonDataElement);
                demap.setAggOperator(null);
                demap.setLevel(null);

                dataElementVariable.getDataElementMap().remove(connectedDataSource.getId());
                dataElementVariable.getDataElementMap().put(connectedDataSource.getId(), demap);
            }
        }

    }



    private List<MetadataFormItemDefinitionVO> retrieveDocumentMetadataInfoByDocument(Document document )  {

        List<MetadataFormItemDefinitionVO> metadataFormItemDefinitionVOs = new ArrayList<>();
        document.getCommunicationOrderEntryItemDefinitionsInOrder().forEach(currentItemDefinition -> {
            if (currentItemDefinition != null) {
                MetadataFormItemDefinitionVO newObject = new MetadataFormItemDefinitionVO();
                try {
                    newObject = new MetadataFormItemDefinitionVO(currentItemDefinition);
                } catch (Exception ex){
                    throw new RuntimeException(ex);
                }
                DataElementVariable var = currentItemDefinition.getDataElementVariable();
                if (var != null) {
                    DataElementVariable newDataElement = new DataElementVariable();
                    newDataElement.setId(var.getId());
                    newDataElement.setName(var.getName());
                    newObject.setDataElementVariable(newDataElement);
                    AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(var.getId(), document);
                    if (ade != null){
                        newObject.setDataElementTypeId(ade.getDataSubtypeId());
                        newObject.setDataElementInputTypeFormat(ade.getExternalFormatText());
                    }
                }
                if (!currentItemDefinition.getConnectorVariableMap().isEmpty()) {
                    List<DataElementVariable> connectorDataElementVariables = new ArrayList<>();
                    currentItemDefinition.getConnectorVariableMap().keySet().forEach(key -> {
                        DataElementVariable elementData = currentItemDefinition.getConnectorVariableMap().get(key);
                        if (!connectorDataElementVariables.contains(elementData)) {
                            DataElementVariable newDataElement = new DataElementVariable();
                            newDataElement.setId(elementData.getId());
                            newDataElement.setName(elementData.getName());
                            connectorDataElementVariables.add(newDataElement);
                        }
                    });
                    newObject.setConnectorDataElementVariables(connectorDataElementVariables);
                }
                metadataFormItemDefinitionVOs.add(newObject);
            }
        });

        return metadataFormItemDefinitionVOs;
    }



}
