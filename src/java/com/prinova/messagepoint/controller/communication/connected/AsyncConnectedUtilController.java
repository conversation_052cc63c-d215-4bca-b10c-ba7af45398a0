package com.prinova.messagepoint.controller.communication.connected;

import ai.mpr.marcie.content.rationalizer.misc.JsonObjectBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.util.*;
import com.syncfusion.docio.FormatType;
import com.syncfusion.docio.WordDocument;
import com.syncfusion.licensing.SyncfusionLicenseProvider;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Base64;

public class AsyncConnectedUtilController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncConnectedUtilController.class);

    public static final String REQ_PARAM_ACTION = "action";

    private static final String ACTION_GENERATE_GUID = "generateGuid";
    private static final String ACTION_CONVERT_RTF_TO_HTML = "convertRtfToHtml";

    public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String action = ServletRequestUtils.getStringParameter(request, REQ_PARAM_ACTION, "");
        if (StringUtils.equals(action, ACTION_GENERATE_GUID)) {
            handleGenerateGuid(response);

            return null;
        } else if (StringUtils.equals(action, ACTION_CONVERT_RTF_TO_HTML)) {
            new ConvertRtfToHtmlHandler().execute(request, response);

            return null;
        }

        return null;
    }

    private void handleGenerateGuid(HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();

            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty("generatedGuid", RandomGUID.getGUID())
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error("Error: Unable to generate guid.", e);
        }
    }
}


class ConvertRtfToHtmlHandler {
    private static final Log log = LogUtil.getLog(ConvertRtfToHtmlHandler.class);

    ConvertRtfToHtmlHandler() {
    }

    public void execute(HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!StringUtils.equalsIgnoreCase("POST", request.getMethod())) {
                this.buildErrorResponse(response);
                return;
            }

            String content = ServletRequestUtils.getStringParameter(request, "content");
            if (StringUtils.isEmpty(content) || !StringUtils.startsWith(content, "{")) {
                this.buildErrorResponse(response);
                return;
            }

            JsonElement inputJsonElement = JsonParser.parseString(content);
            JsonElement inputRtfAsBase64JsonElement = inputJsonElement.getAsJsonObject().get("inputRtfAsBase64");
            if (inputRtfAsBase64JsonElement == null) {
                this.buildErrorResponse(response);
                return;
            }

            String inputRtfAsBase64String = inputRtfAsBase64JsonElement.getAsString();
            if (inputRtfAsBase64String == null) {
                this.buildErrorResponse(response);
                return;
            }

            byte[] inputRtfAsBase64Bytes = Base64.getDecoder().decode(inputRtfAsBase64String);
            if (ArrayUtils.isEmpty(inputRtfAsBase64Bytes)) {
                this.buildErrorResponse(response);
                return;
            }

            String inputRtfString = new String(inputRtfAsBase64Bytes, StandardCharsets.UTF_8);
            String htmlFromRtfString = this.computeHtmlFromRtfString(inputRtfString);

            String outputHtmlAsBase64 = new String(Base64.getEncoder().encode(htmlFromRtfString.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
            JsonObject outputJsonObject = new JsonObjectBuilder()
                    .addProperty("outputHtmlAsBase64", outputHtmlAsBase64)
                    .build();

            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();
            out.write(outputJsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            this.buildErrorResponse(response);
            log.error(e.getMessage(), e);
        }
    }

    private String computeHtmlFromRtfString(String inputRtfString) throws Exception {
        String temporaryDirectory = ApplicationUtil.getProperty( SystemPropertyKeys.Folder.KEY_MessagepointObjectExportDir );
        FileUtil.createDirectoryIfNeeded(temporaryDirectory);
        String tempOutputFilename =  temporaryDirectory
                + (temporaryDirectory.endsWith(File.separator) ? "" : File.separator )
                + "RTF_to_HTML_outputFile_" + RandomGUID.getGUID() + ".html";

        File tempOutputFile = new File(tempOutputFilename);

        SyncfusionLicenseProvider.registerLicense("Ngo9BigBOggjHTQxAR8/V1NNaF1cWWhOYVJyWmFZfVtgc19HZFZTTGYuP1ZhSXxWdkNiXX5fcHRQRWVfVU19XUs=");
        WordDocument document = new WordDocument(new ByteArrayInputStream(inputRtfString.getBytes(StandardCharsets.UTF_8)), FormatType.Rtf);
        document.save(tempOutputFile.getCanonicalPath(), FormatType.Html);
        String outputHtml = new String(Files.readAllBytes(tempOutputFile.toPath()), StandardCharsets.UTF_8);
        if (!tempOutputFile.delete()) {
            tempOutputFile.deleteOnExit();
        }

        return outputHtml;
    }

    private void buildErrorResponse(HttpServletResponse response) {
        try {
            response.setContentType("application/json");

            ServletOutputStream out = response.getOutputStream();

            JsonObject jsonObject = new JsonObjectBuilder()
                    .addProperty("errorMessage", "Failed to convert the provided RTF string into HTML.")
                    .build();

            out.write(jsonObject.toString().getBytes(ApplicationLanguageUtils.DISPLAY_ENCODING));
            out.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}