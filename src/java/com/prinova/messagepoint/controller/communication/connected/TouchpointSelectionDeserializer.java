package com.prinova.messagepoint.controller.communication.connected;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.prinova.messagepoint.model.TouchpointSelection;

import java.io.IOException;

public class TouchpointSelectionDeserializer extends StdSerializer<TouchpointSelection> {

    public TouchpointSelectionDeserializer() {
        this(null);
    }

    public TouchpointSelectionDeserializer(Class<TouchpointSelection> vc) {
        super(vc);
    }

    @Override
    public void serialize(TouchpointSelection value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("name", value.getNameWithContext());
        gen.writeNumberField("id", value.getId());
        gen.writeEndObject();
    }
}
