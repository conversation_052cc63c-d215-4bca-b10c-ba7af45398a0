package com.prinova.messagepoint.controller.communication.connected;

public class DocumentUi {

    private boolean isEnableForVariation;

    private boolean communicationAppliesTouchpointSelection;

    private boolean communicationOrderEntryEnabled;

    private CommunicationDataFeedWebServiceUi communicationDataFeedWebServiceUi;

    public CommunicationDataFeedWebServiceUi getCommunicationDataFeedWebServiceUi() {
        return communicationDataFeedWebServiceUi;
    }

    public void setCommunicationDataFeedWebServiceUi(String urlValue, String usernameValue, String passwordValue) {

        this.communicationDataFeedWebServiceUi = new CommunicationDataFeedWebServiceUi(urlValue, usernameValue, passwordValue);
    }

    public boolean isCommunicationAppliesTouchpointSelection() {
        return communicationAppliesTouchpointSelection;
    }

    public void setCommunicationAppliesTouchpointSelection(boolean communicationAppliesTouchpointSelection) {
        this.communicationAppliesTouchpointSelection = communicationAppliesTouchpointSelection;
    }

    public boolean isEnableForVariation() {
        return isEnableForVariation;
    }

    public void setEnableForVariation(boolean enableForVariationValue) {
        isEnableForVariation = enableForVariationValue;
    }

    public boolean isCommunicationOrderEntryEnabled() {
        return communicationOrderEntryEnabled;
    }

    public void setCommunicationOrderEntryEnabled(boolean communicationOrderEntryEnabled) {
        this.communicationOrderEntryEnabled = communicationOrderEntryEnabled;
    }

    public static class CommunicationDataFeedWebServiceUi {
        private String url;
        private String username;
        private String password;


        public CommunicationDataFeedWebServiceUi(String urlValue, String usernameValue, String passwordValue) {
            this.url = urlValue;
            this.username = usernameValue;
            this.password = passwordValue;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Builder {
        private DocumentUi documentUi;

        public static DocumentUi.Builder newInstance() {
            return new DocumentUi.Builder();
        }

        private Builder() {
            this.documentUi = new DocumentUi();
        }

        public DocumentUi.Builder withEnableForVariation(boolean enableForVariationValue) {
            this.documentUi.setEnableForVariation(enableForVariationValue);
            return this;
        }

        public DocumentUi.Builder withCommunicationAppliesTouchpointSelection(boolean communicationAppliesTouchpointSelectionValue) {
            this.documentUi.setCommunicationAppliesTouchpointSelection(communicationAppliesTouchpointSelectionValue);
            return this;
        }

        public DocumentUi.Builder withCommunicationDataFeedWebServiceUi(String url, String username, String password) {
            this.documentUi.setCommunicationDataFeedWebServiceUi(url, username, password);
            return this;
        }

        public DocumentUi.Builder withCommunicationOrderEntryEnabled(boolean communicationOrderEntryEnabled) {
            this.documentUi.setCommunicationOrderEntryEnabled(communicationOrderEntryEnabled);
            return this;
        }

        public DocumentUi build() {
            return documentUi;
        }
    }
}
