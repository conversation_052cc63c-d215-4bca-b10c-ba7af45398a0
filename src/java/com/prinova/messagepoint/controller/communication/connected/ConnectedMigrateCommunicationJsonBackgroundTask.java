package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.connected.data.oracle.entity.CacheData;
import com.prinova.messagepoint.connected.data.oracle.entity.CacheDataType;
import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItemDefinition;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.platform.ws.client.CommunicationWSClient;
import com.prinova.messagepoint.util.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.orm.hibernate5.SessionFactoryUtils;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.List;

public class ConnectedMigrateCommunicationJsonBackgroundTask extends MessagePointRunnable {

    private final List<Node> allNodes;
    private static final Log log = LogUtil.getLog(ConnectedMigrateCommunicationJsonBackgroundTask.class);

    public ConnectedMigrateCommunicationJsonBackgroundTask(List<Node> allNodes) {
        this.allNodes = allNodes;
    }
    @Override
    public void performMainProcessing() {
        for (Node node : allNodes) {
            String schemaName = node.getSchemaName();
            if (StringUtils.isNotEmpty(schemaName)) {
                migrateCommunicationJson(schemaName);
            }
        }
    }
    private void useSchemaName(String schemaName) {
        if (StringUtils.isEmpty(schemaName)) {
            return;
        }
        setTenantIdentifier(schemaName);

        SessionFactory sessionFactory = HibernateUtil.getManager().getSessionFactory();

        if (TransactionSynchronizationManager.hasResource(sessionFactory)) {
            SessionHolder sessionHolder = (SessionHolder) TransactionSynchronizationManager.unbindResource(sessionFactory);
            SessionFactoryUtils.closeSession(sessionHolder.getSession());
        }

        MessagepointCurrentTenantIdentifierResolver.setTenantIdentifier(schemaName);
        Session session = HibernateUtil.getManager().openSessionInView();
        TransactionSynchronizationManager.bindResource(sessionFactory, new SessionHolder(session));
    }

    private void migrateCommunicationJson(String schemaName) {
        try {
            log.info("Start communication migration for schema:  " + schemaName);
            useSchemaName(schemaName);
            updateInterviewWithGuid();
            /*List<Communication> communicationList = Communication.findAll();
            for (Communication communication : communicationList) {
                JSONObject supportingData = communication.getSupportingData() != null ? new JSONObject(communication.getSupportingData()) : new JSONObject();
                boolean migrateJson = true;
                if(supportingData.has(Constants.Key_CommunicationMigrated)){
                    migrateJson = false;
                }
                if (communication.getDocument().isCommunicationUseBeta() && migrateJson) {
                    JSONObject retrieveRefDataFileContent = retrieveRefDataFileContent(communication);
                    if (retrieveRefDataFileContent != null && !retrieveRefDataFileContent.has(Constants.Key_MetadataFormItemDefinitionVOs)) {
                        String interviewConfig = communication.getInterviewConfiguration();
                        if (interviewConfig != null) {
                            JSONObject configurationJson = new JSONObject();
                            JSONArray items = (JSONArray) new JSONObject(interviewConfig).get(Constants.Key_MetadataFormItemDefinitionVOs);
                            updateMetadataFormItemDefinitionWithGuid(items);
                            configurationJson.put(Constants.Key_MetadataFormItemDefinitionVOs, items);
                            configurationJson.put(Constants.Key_Settings, GetSettingsJsonObject(communication));
                            String interviewValues = retrieveInterviewValues(retrieveRefDataFileContent);
                            configurationJson.put(Constants.Key_InterviewValues, interviewValues);
                            communication.setCommunicationInterviewJson(configurationJson);

                            CommunicationWSClient.sendDataFile(communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
                                    communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
                                    communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
                                    communication.getGuid(),
                                    communication);

                            supportingData.put(Constants.Key_CommunicationMigrated, true);
                            communication.setSupportingData(supportingData.toString());
                            communication.save();


                        }
                    }
                }
            }*/
        } catch (Throwable th) {
            log.error("ConnectedMigrateCommunicationJsonBackgroundTask  - Failed to migrate JSON for schema " + schemaName, th);
        } finally {
            log.info("ConnectedMigrateCommunicationJsonBackgroundTask  - End migrate JSON for schema " + schemaName);
            closeSession();
        }
    }

    private void updateInterviewWithGuid() {
        //The match between a field and his value will be done based on guid
        ArrayList<MessagepointCriterion> critList = new ArrayList<>();
        critList.add(MessagepointRestrictions.eq("type", CacheDataType.INTERVIEW));

        List<CacheData> connectedDataList = HibernateUtil.getManager().getObjectsAdvanced(CacheData.class, critList);
        for (CacheData connectedData : connectedDataList) {
            String interviewString = connectedData.getJsonData();
            JSONObject interviewObject = new JSONObject(interviewString);
            boolean interviewObjectWasUpdated = false;
            if (interviewObject != null) {
                JSONArray interviewItems = (JSONArray) interviewObject.get(Constants.Key_MetadataFormItemDefinitionVOs);
                for (int i = 0; i < interviewItems.length(); i++) {
                    if (!((JSONObject) interviewItems.get(i)).has("guid")) {
                        JSONObject updatedItem = (JSONObject) interviewItems.get(i);
                        long updatedItemId = updatedItem.getLong("id");
                        CommunicationOrderEntryItemDefinition orderItemDef = CommunicationOrderEntryItemDefinition.findById(updatedItemId);
                        if(orderItemDef != null) {
                            updatedItem.put("guid", orderItemDef.getGuid());
                            interviewObjectWasUpdated = true;
                        }
                    } else {
                        break;
                    }
                }
                if(interviewObjectWasUpdated){
                    interviewObject.put(Constants.Key_MetadataFormItemDefinitionVOs, interviewItems);
                    connectedData.setJsonData(interviewObject.toString());
                    connectedData.save();
                }
            }
        }
    }

    private JSONObject GetSettingsJsonObject(Communication communication){
        JSONObject extraSettingsNode = new JSONObject();

        extraSettingsNode.put(Constants.Key_CommunicationGuid, communication.getGuid());

        List<Long> layoutsWithoutConnectedZones = new ArrayList<>();
        Document document = communication.getDocument();
        List<Document> layouts = document.getAlternateLayouts();
        layouts.add(communication.getDocument());
        for ( Document currentLayout: layouts ) {
            boolean hasEnabledConnectedZone = false;
            List<Zone> zones = Zone.findCommunicationZonesByDocument(currentLayout.getId());
            for ( Zone currentLayoutZone: zones )
                if ( currentLayoutZone.isEnabled() ) {
                    hasEnabledConnectedZone = true;
                    break;
                }
            if ( !hasEnabledConnectedZone )
                layoutsWithoutConnectedZones.add(currentLayout.getId());
        }
        extraSettingsNode.put(Constants.Key_LayoutsWithoutConnectedZones, layoutsWithoutConnectedZones);
        extraSettingsNode.put(Constants.Key_ContentExists, communication != null && !communication.getZoneContentAssociations().isEmpty());


        extraSettingsNode.put(Constants.Key_SkipInteractiveWhenNoZones,  document.isCommunicationSkipInteractiveWhenNoZones());
        extraSettingsNode.put(Constants.Key_AppliesVariantSelect, document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()
                && communication.getTouchpointSelectionId() != null);
        extraSettingsNode.put(Constants.Key_HasCommunicationZones, hasCommunicationZones(document));

        extraSettingsNode.put(Constants.Key_CommunicationId, communication.getId());
        extraSettingsNode.put(Constants.Key_DocumentId, document.getId());
        extraSettingsNode.put(Constants.Key_DocumentName, document.getName());
        extraSettingsNode.put(Constants.Key_IsNewConnected, document.isCommunicationUseBeta());

        populateCurrentLayoutAndTpSelection(document,communication, extraSettingsNode);

        extraSettingsNode.put(Constants.Key_DateFormat, DateUtil.DATE_FORMAT.toUpperCase());
        extraSettingsNode.put(Constants.Key_CommunicationDisplayTouchpointThumbnail, document.isCommunicationDisplayTouchpointThumbnail());

        List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
        List<TextValuePair> languageList = new ArrayList<>();
        for(MessagepointLocale locale: langLocales) {
            languageList.add(new TextValuePair(locale.getLanguageDisplayName(),  String.valueOf(locale.getId())));
        }
        extraSettingsNode.put(Constants.Key_Languages, languageList);
        extraSettingsNode.put(Constants.Key_SelectedLanguageId, communication.getLocale() != null ? String.valueOf(communication.getLocale().getId()):
                !languageList.isEmpty() ? languageList.get(0).getValue() : "");


        extraSettingsNode.put(Constants.Key_DebugOrder, communication.isDebugOrder());
        extraSettingsNode.put(Constants.Key_DebugReferenceData, communication.getDebugReferenceData());
        extraSettingsNode.put(Constants.Key_DebugPackage, communication.getDebugPackage() != null ? "<zip-file>" : "");

        Boolean isOmniChannel = document.getIsOmniChannel();
        if ( isOmniChannel ) {
            Long appliedChannelContextId = communication != null && communication.getChannelContextId() != null ? communication.getChannelContextId() : -1;
            List<Channel> appliedChannels = document.getAppliedChannels(false);
            List<TextValuePair> channelList = new ArrayList<>();
            for (Channel appliedChannel : appliedChannels) {
                channelList.add(new TextValuePair(appliedChannel.getName(), String.valueOf(appliedChannel.getId())));
            }
            extraSettingsNode.put(Constants.Key_AppliedChannels, channelList);
            extraSettingsNode.put(Constants.Key_AppliedChannelContextId, appliedChannelContextId > 0 ? String.valueOf(appliedChannelContextId) :
                    !channelList.isEmpty() ? channelList.get(0).getValue() : "");
        }

        extraSettingsNode.put(Constants.Key_CommunicationAppliesCopiesInput, document.isCommunicationAppliesCopiesInput());
        extraSettingsNode.put(Constants.Key_NumberOfCopies, communication.getNumberOfCopies());

        if(document.isCommunicationAppliesTagCloud() && RolesUtil.hasPermission(UserUtil.getPrincipalUser().getRoles(), "ROLE_METATAGS_VIEW")) {
            extraSettingsNode.put(Constants.Key_AppliesTagCloud, true);
            extraSettingsNode.put(Constants.Key_MetaTags, communication.getMetatags());
        }

        return extraSettingsNode;
    }

    private void populateCurrentLayoutAndTpSelection(Document document, Communication communication,  JSONObject extraSettingsNode) {
        boolean appliesVariantSelect = (document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()) ||
                (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1);

        if (appliesVariantSelect) {
            if (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1) {
                TouchpointSelection touchpointSelection = TouchpointSelection.findById(communication.getTouchpointSelectionId());
                if(touchpointSelection!= null) {
                    long currentLayoutId = touchpointSelection.getDocumentContext().getLong("document_context");
                    extraSettingsNode.put(Constants.Key_CurrentLayoutId, Document.findById(currentLayoutId).getId());
                    extraSettingsNode.put(Constants.Key_SelectionId, touchpointSelection.getId());
                }
            } else {
                TouchpointSelection touchpointSelection = document.getMasterTouchpointSelection();
                if (touchpointSelection != null) {
                    long currentLayoutId = touchpointSelection.getDocumentContext().getLong("document_context");
                    extraSettingsNode.put(Constants.Key_CurrentLayoutId,  Document.findById(currentLayoutId).getId());
                    extraSettingsNode.put(Constants.Key_SelectionId, touchpointSelection.getId());
                }
            }
        }
    }
    private boolean hasCommunicationZones(Document document)
    {
        List<Zone> communicationZones = Zone.findCommunicationZonesByDocument(document.getId());
        boolean hasEnabledCommunicationZones = false;
        for ( Zone currentZone: communicationZones ) {
            if (currentZone.isEnabled()) {
                hasEnabledCommunicationZones = true;
                break;
            }
        }

        return hasEnabledCommunicationZones;
    }

    private void closeSession() {
        SessionFactory sessionFactory = HibernateUtil.getManager().getSessionFactory();
        if (TransactionSynchronizationManager.hasResource(sessionFactory)) {
            SessionHolder sessionHolder = (SessionHolder) TransactionSynchronizationManager.unbindResource(sessionFactory);
            SessionFactoryUtils.closeSession(sessionHolder.getSession());
        }
    }

    private static JSONObject retrieveRefDataFileContent(Communication communication){
        try {
            SandboxFile refDataFile = CommunicationWSClient.retrieveRefDataFile(
                    communication.getDocument().getCommunicationCompositionResultsWebService().getUrl(),
                    communication.getDocument().getCommunicationCompositionResultsWebService().getUsername(),
                    communication.getDocument().getCommunicationCompositionResultsWebService().getPassword(),
                    communication.getGuid(), communication.getPmgrOrderUUID()
            );
            if (refDataFile != null) {
                JSONObject communicationJson = new JSONObject(new String(refDataFile.getFileContent()));
                return communicationJson;
            }
            return null;
        } catch (Exception ex){
            log.error("ConnectedMigrateCommunicationJsonBackgroundTask - error while trying to connect to backend server and retrieve interview_values " + ex);
            return  null;
        }
    }

    private static String retrieveInterviewValues( JSONObject  communicationJson) {
        String interviewValuesString = communicationJson.get(Constants.Key_InterviewValues).toString();
        JSONArray interviewValues = new JSONArray(interviewValuesString);
        for (int i = 0; i < interviewValues.length(); i++) {
            JSONObject updatedItem = (JSONObject) interviewValues.get(i);
            long updatedItemId = updatedItem.getLong("id");
            CommunicationOrderEntryItem commOrderEntryItem = CommunicationOrderEntryItem.findById(updatedItemId);
            if(commOrderEntryItem != null) {
                CommunicationOrderEntryItemDefinition orderItemDef = CommunicationOrderEntryItemDefinition.findById(commOrderEntryItem.getMetadataDefinitionId());
                updatedItem.put("guid", orderItemDef.getGuid());
            }
        }
        return interviewValues.toString();
    }

    private static void updateMetadataFormItemDefinitionWithGuid(JSONArray items) {
        for (int i = 0; i < items.length(); i++) {
            JSONObject updatedItem = (JSONObject) items.get(i);
            long updatedItemId = updatedItem.getLong("id");
            CommunicationOrderEntryItemDefinition orderItemDef = CommunicationOrderEntryItemDefinition.findById(updatedItemId);
            updatedItem.put("guid", orderItemDef.getGuid());
        }
    }

}
