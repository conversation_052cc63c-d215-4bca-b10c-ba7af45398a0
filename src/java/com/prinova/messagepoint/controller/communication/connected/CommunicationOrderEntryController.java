package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.MessagepointController;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.controller.communication.CommunicationOrderEntryEditWrapper;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CommunicationOrderEntryController extends MessagepointController {
    public static final String REQ_PARAM_COMMUNICATION_ID 			= "communicationId";
    public static final String REQ_PARAM_CONTEXT	 				= "context";
    public static final String REQ_PARAM_DEBUG                      = "debug";
    public static final String REQ_PARAM_VALUE_ONSCREEN             = "onscreen";

    private static final Log log = LogUtil.getLog(ConnectedTouchpointInterviewSetupController.class);

    @Override
    protected Map<String, Object> referenceData(HttpServletRequest request) throws Exception {
        Map<String, Object> referenceData = new HashMap<>();
        return referenceData;
    }

    @Override
    protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors) throws Exception {
        return super.showForm(request, response, errors);
    }

    @Override
    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        binder.setAutoGrowCollectionLimit(1024);
        binder.registerCustomEditor(String.class, new StringXSSEditor());
        binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
        binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
        binder.registerCustomEditor(AbstractDataElement.class, new IdCustomEditor<>(AbstractDataElement.class));
        binder.registerCustomEditor(MetadataFormItemType.class, new StaticTypeIdCustomEditor<>(MetadataFormItemType.class));
        binder.registerCustomEditor(TouchpointSelection.class, new IdCustomEditor<>(TouchpointSelection.class));
        binder.registerCustomEditor(DataSource.class, new IdCustomEditor<>(DataSource.class));
    }

    @Override
    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        long communicationId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1);
        String context 			= ServletRequestUtils.getStringParameter(request, REQ_PARAM_CONTEXT, "default");
        String debugOrder       = ServletRequestUtils.getStringParameter(request, REQ_PARAM_DEBUG);

        Communication communication = communicationId != -1 ? Communication.findById(communicationId) : new Communication();
        if(communication.getDocument() == null) {
            communication.setDocument(UserUtil.getCurrentTouchpointContext());
        }

        CommunicationOrderEntryEditWrapper wrapper = new CommunicationOrderEntryEditWrapper(communication);

        if (debugOrder != null && REQ_PARAM_VALUE_ONSCREEN.equals(debugOrder)) {
            wrapper.getCommunication().setDebugOrder(true);
        }

        if ( context.equals("test") )
            wrapper.getCommunication().setTestOrder(true);

        return wrapper;
    }
}
