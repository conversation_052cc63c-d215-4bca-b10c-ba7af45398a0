package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.connected.util.Constants;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.Zone;
import com.prinova.messagepoint.model.admin.Channel;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.RolesUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CommunicationOrderControllerUtil {
    public static JSONObject GetSettingsJsonObject(Communication communication){
        JSONObject extraSettingsNode = new JSONObject();

        extraSettingsNode.put(Constants.Key_CommunicationGuid, communication.getGuid());

        List<Long> layoutsWithoutConnectedZones = new ArrayList<>();
        Document document = communication.getDocument();
        List<Document> layouts = document.getAlternateLayouts();
        layouts.add(communication.getDocument());
        for ( Document currentLayout: layouts ) {
            boolean hasEnabledConnectedZone = false;
            List<Zone> zones = Zone.findCommunicationZonesByDocument(currentLayout.getId());
            for ( Zone currentLayoutZone: zones )
                if ( currentLayoutZone.isEnabled() ) {
                    hasEnabledConnectedZone = true;
                    break;
                }
            if ( !hasEnabledConnectedZone )
                layoutsWithoutConnectedZones.add(currentLayout.getId());
        }
        extraSettingsNode.put(Constants.Key_LayoutsWithoutConnectedZones, layoutsWithoutConnectedZones);
        extraSettingsNode.put(Constants.Key_ContentExists, communication != null && !communication.getZoneContentAssociations().isEmpty());


        extraSettingsNode.put(Constants.Key_SkipInteractiveWhenNoZones,  document.isCommunicationSkipInteractiveWhenNoZones());
        extraSettingsNode.put(Constants.Key_AppliesVariantSelect, document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()
                && communication.getTouchpointSelectionId() != null);
        extraSettingsNode.put(Constants.Key_HasCommunicationZones, HasCommunicationZones(document));

        extraSettingsNode.put(Constants.Key_CommunicationId, communication.getId());
        extraSettingsNode.put(Constants.Key_DocumentId, document.getId());
        extraSettingsNode.put(Constants.Key_DocumentName, document.getName());
        extraSettingsNode.put(Constants.Key_IsNewConnected, document.isCommunicationUseBeta());

        populateCurrentLayoutAndTpSelection(document,communication, extraSettingsNode);

        extraSettingsNode.put(Constants.Key_DateFormat, DateUtil.DATE_FORMAT.toUpperCase());
        extraSettingsNode.put(Constants.Key_CommunicationDisplayTouchpointThumbnail, document.isCommunicationDisplayTouchpointThumbnail());

        List<MessagepointLocale> langLocales = document.getTouchpointLanguagesAsLocales();
        List<TextValuePair> languageList = new ArrayList<>();
        for(MessagepointLocale locale: langLocales) {
            languageList.add(new TextValuePair(locale.getLanguageDisplayName(),  String.valueOf(locale.getId())));
        }
        extraSettingsNode.put(Constants.Key_Languages, languageList);
        extraSettingsNode.put(Constants.Key_SelectedLanguageId, communication.getLocale() != null ? String.valueOf(communication.getLocale().getId()):
                !languageList.isEmpty() ? languageList.get(0).getValue() : "");


        extraSettingsNode.put(Constants.Key_DebugOrder, communication.isDebugOrder());
        extraSettingsNode.put(Constants.Key_DebugReferenceData, communication.getDebugReferenceData());
        extraSettingsNode.put(Constants.Key_DebugPackage, communication.getDebugPackage() != null ? "<zip-file>" : "");

        Boolean isOmniChannel = document.getIsOmniChannel();
        if ( isOmniChannel ) {
            Long appliedChannelContextId = communication != null && communication.getChannelContextId() != null ? communication.getChannelContextId() : -1;
            List<Channel> appliedChannels = document.getAppliedChannels(false);
            List<TextValuePair> channelList = new ArrayList<>();
            for (Channel appliedChannel : appliedChannels) {
                channelList.add(new TextValuePair(appliedChannel.getName(), String.valueOf(appliedChannel.getId())));
            }
            extraSettingsNode.put(Constants.Key_AppliedChannels, channelList);
            extraSettingsNode.put(Constants.Key_AppliedChannelContextId, appliedChannelContextId > 0 ? String.valueOf(appliedChannelContextId) :
                    !channelList.isEmpty() ? channelList.get(0).getValue() : "");
        }

        extraSettingsNode.put(Constants.Key_CommunicationAppliesCopiesInput, document.isCommunicationAppliesCopiesInput());
        extraSettingsNode.put(Constants.Key_NumberOfCopies, communication.getNumberOfCopies());

        if(document.isCommunicationAppliesTagCloud() && RolesUtil.hasPermission(UserUtil.getPrincipalUser().getRoles(), "ROLE_METATAGS_VIEW")) {
            extraSettingsNode.put(Constants.Key_AppliesTagCloud, true);
            extraSettingsNode.put(Constants.Key_MetaTags, communication.getMetatags());
        }

        return extraSettingsNode;
    }

    private static void populateCurrentLayoutAndTpSelection(Document document, Communication communication,  JSONObject extraSettingsNode) {
        boolean appliesVariantSelect = (document.isEnabledForVariation() && document.isCommunicationAppliesTouchpointSelection()) ||
                (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1);

        if (appliesVariantSelect) {
            if (communication != null && communication.getTouchpointSelectionId() != null && communication.getTouchpointSelectionId() != -1) {
                TouchpointSelection touchpointSelection = TouchpointSelection.findById(communication.getTouchpointSelectionId());
                if(touchpointSelection!= null) {
                    long currentLayoutId = touchpointSelection.getDocumentContext().getLong("document_context");
                    extraSettingsNode.put(Constants.Key_CurrentLayoutId, Document.findById(currentLayoutId).getId());
                    extraSettingsNode.put(Constants.Key_SelectionId, touchpointSelection.getId());
                }
            } else {
                TouchpointSelection touchpointSelection = document.getMasterTouchpointSelection();
                if (touchpointSelection != null) {
                    long currentLayoutId = touchpointSelection.getDocumentContext().getLong("document_context");
                    extraSettingsNode.put(Constants.Key_CurrentLayoutId,  Document.findById(currentLayoutId).getId());
                    extraSettingsNode.put(Constants.Key_SelectionId, touchpointSelection.getId());
                }
            }
        }
    }

    public static boolean HasCommunicationZones(Document document)
    {
        List<Zone> communicationZones = Zone.findCommunicationZonesByDocument(document.getId());
        boolean hasEnabledCommunicationZones = false;
        for ( Zone currentZone: communicationZones ) {
            if (currentZone.isEnabled()) {
                hasEnabledCommunicationZones = true;
                break;
            }
        }

        return hasEnabledCommunicationZones;
    }
}
