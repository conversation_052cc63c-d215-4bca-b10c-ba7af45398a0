package com.prinova.messagepoint.controller.communication.connected;

import com.google.gson.*;
import com.prinova.messagepoint.model.admin.DataSource;

import java.lang.reflect.Type;

public class AbstractDataSourceDeserializer implements
        JsonSerializer<DataSource>, JsonDeserializer<DataSource> {

    private static final String DATA_SOURCE_PROPERTY_VALUE = "uploadedFileDataSource";

    @Override
    public DataSource deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
            throws JsonParseException {
        if (json == null || (json.isJsonArray() && ((JsonArray) json).isEmpty())) {
            return null;
        }
        JsonObject jsonObject = json.getAsJsonObject();
        jsonObject.remove("created");
        JsonElement element = jsonObject.get(DATA_SOURCE_PROPERTY_VALUE);
        DataSource dataElement = new DataSource();
        dataElement.setName(jsonObject.get("name").toString());
        dataElement.setId(jsonObject.get("id").getAsLong());

        return dataElement;
    }

    @Override
    public JsonElement serialize(DataSource src, Type typeOfSrc, JsonSerializationContext context) {
        JsonObject result = new JsonObject();
        result.add(DATA_SOURCE_PROPERTY_VALUE, context.serialize(src, src.getClass()));
        return result;
    }
}
