package com.prinova.messagepoint.controller.communication.connected;

import com.google.gson.*;

import com.prinova.messagepoint.model.admin.DataElement;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;

import java.lang.reflect.Type;

public class AbstractDataElementDeserializer implements
        JsonSerializer<AbstractDataElement>, JsonDeserializer<AbstractDataElement> {

    private static final String DATA_ELEMENT_PROPERTY_VALUE = "dataElement";

    @Override
    public AbstractDataElement deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
            throws JsonParseException {
        if (json == null) {
            return new DataElement();
        }
        JsonObject jsonObject = json.getAsJsonObject();
        jsonObject.remove("created");
        jsonObject.remove("checkoutTimestamp");
        jsonObject.remove("checkinTimestamp");
        JsonElement element = jsonObject.get(DATA_ELEMENT_PROPERTY_VALUE);
        AbstractDataElement dataElement = new DataElement();
        dataElement.setName(jsonObject.get("name").toString());
        dataElement.setId(jsonObject.get("id").getAsLong());

        return dataElement;
    }

    @Override
    public JsonElement serialize(AbstractDataElement src, Type typeOfSrc, JsonSerializationContext context) {
        JsonObject result = new JsonObject();
        result.add(DATA_ELEMENT_PROPERTY_VALUE, context.serialize(src, src.getClass()));
        return result;
    }
}
