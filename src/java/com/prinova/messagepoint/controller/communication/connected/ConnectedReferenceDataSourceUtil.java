package com.prinova.messagepoint.controller.communication.connected;

import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataDefinition;
import org.jetbrains.annotations.NotNull;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDataElement;
import com.prinova.messagepoint.model.dataadmin.jsonlayout.JSONDefinitionType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceAssociationService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceServiceRequest;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ConnectedReferenceDataSourceUtil {
    private static final String CONNECTED_REFERENCE = "CONNECTED_REFERENCE";
    private static final int ORDER_ENTRY_ACTION_DELETE = -9;
    public static  final String CONNECTED_REFERENCE_CONNECTOR_PARAMETER = "mp_ref_01";
    private static final Log LOGGER = LogUtil.getLog(ConnectedReferenceDataSourceUtil.class);

    public static boolean CreateDataSource(Document document, List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId, Long referenceDataVariableId ) {
        try {
            if (existConnectedDataSource(document)) {
                DataSource connectedDataSource = getConnectedDataSource(document);
                if (connectedDataSource.isJSON()) {
                    if (!isSharedCollection(document)) {
                        CreateJsonDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                    } else {
                        CreateCollection(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                    }
                } else {
                    if (isSharedCollection(document)) {
                        if(document.isCommunicationUseBeta()) {
                            CreateCollection(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                        } else {
                            CreateDelimitedDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                        }
                    } else{
                        if(document.isCommunicationUseBeta()){
                            ConvertDelimitedDataSourceToJson(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                        } else {
                            CreateDelimitedDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                        }
                    }
                }
            } else {
                if(document.isCommunicationUseBeta()) {
                    CreateJsonDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                } else {
                    CreateDelimitedDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
                }
            }
            return true;
        } catch (Exception ex){
            throw new RuntimeException("Unable to create/update connected reference data source" + ex.getMessage(), ex);
        }

    }

    public static void ConvertDelimitedDataSourceToJson(Document document,  List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId, Long referenceDataVariableId){
        DataSourceAssociation dsa = document.getDataSourceAssociation();
        for (ReferenceConnection connection : dsa.getReferenceConnections()) {
            DataSource dataSource = connection.getReferenceDataSource();
            if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED && dataSource.isDelimited()) {
                dataSource.setName("DELETED_OLD_" + dataSource.getName());

                // Find and remove any ConnectionResource that references this connection
                for (DataResource dr : document.getDataResources()) {
                    Set<ConnectionResource> resourcesToRemove = new HashSet<>();
                    for (ConnectionResource cr : dr.getConnectionResources()) {
                        if (cr.getReferenceConnection() != null &&
                                cr.getReferenceConnection().getId() == connection.getId()) {
                            resourcesToRemove.add(cr);
                        }
                    }

                    // Remove the identified connection resources
                    for (ConnectionResource cr : resourcesToRemove) {
                        dr.getConnectionResources().remove(cr);
                        cr.delete();
                    }

                    if (!resourcesToRemove.isEmpty()) {
                        dr.save();
                    }
                }

                dsa.getReferenceConnections().remove(connection);
                dsa.save();
                ReferenceConnection.delete(connection.getId());
                document.getReferenceDataSources().remove(connection.getReferenceDataSource());
                document.save();

                DataSource newDataSource = new DataSource();
                String name = generateUniqueName(document.getName());
                newDataSource.setName(name);
                newDataSource.setEncodingType(EncodingType.findById(EncodingType.TYPE_ASCII));
                newDataSource.setSourceType(SourceType.findById(SourceType.TYPE_REFERENCE_CONNECTED));
                newDataSource.setLayoutType(LayoutType.findById(LayoutType.TYPE_JSON));
                ServiceExecutionContext updateDataSourceServiceContext = UpdateDataSourceService.createContext();
                UpdateDataSourceServiceRequest updateDataSourceServiceRequest = (UpdateDataSourceServiceRequest) updateDataSourceServiceContext.getRequest();
                updateDataSourceServiceRequest.setDataSource(newDataSource);
                Service updateDataSourceService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceService.SERVICE_NAME, UpdateDataSourceService.class);
                updateDataSourceService.execute(updateDataSourceServiceContext);
                document.getReferenceDataSources().add(newDataSource);
                document.save();
                createReferenceConnection(newDataSource, primaryDataVariableId, referenceDataVariableId, dsa, true);
                createRecordsForDataSource(dataSource, newDataSource);
                CreateJsonDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
            }
        }
    }

    public static void CreateCollection(Document document,  List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId, Long referenceDataVariableId){
       if(document.getDataSourceAssociation() != null){
          DataSourceAssociation dataSourceAssociation = document.getDataSourceAssociation();
          DataSourceAssociation newDataSourceAssociation = (DataSourceAssociation) dataSourceAssociation.clone();
           for (DataSource dataSource : document.getReferenceDataSources()) {
               if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                    DataSource newDataSource =  dataSource.clone(false);
                    newDataSource.setLayoutType(LayoutType.findById(LayoutType.TYPE_JSON));
                    createReferenceConnection(newDataSource, primaryDataVariableId, referenceDataVariableId, newDataSourceAssociation, true);
                    createRecordsForDataSource(dataSource, newDataSource);
               }
           }

           document.setDataSourceAssociation(newDataSourceAssociation);
           document.save();
           CreateJsonDataSource(document, metaDataFormItemDefinitions, primaryDataVariableId, referenceDataVariableId);
       }

    }

    private static void createRecordsForDataSource (DataSource original, DataSource clone){
      JSONDataDefinition rootDefinition =  createJsonRootDefinition(clone);
      Set<AbstractDataElement> dataElements = original.getDataElements();
        for (AbstractDataElement dataElement : dataElements) {
            createJsonData(rootDefinition, dataElement, clone);
        }
    }

    public static void CreateDelimitedDataSource(Document document, List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId, Long referenceDataVariableId ) {

        try {
            if (existConnectedDataSource(document)) {
                DataSource connectedDataSource = getConnectedDataSource(document);
                List<DataElement> dataElements = DataElement.findAllElementsForConnectedReferenceByDocument(document);
                long startLocation = dataElements.get(0).getStartLocation();
                for (MetadataFormItemDefinitionVO item : metaDataFormItemDefinitions) {
                    if (item.getType().getId() == MetadataFormItemType.ID_DIVIDER || item.getType().getId() == MetadataFormItemType.ID_HEADER || item.getType().getId() == MetadataFormItemType.ID_FILE || item.getAction() == ORDER_ENTRY_ACTION_DELETE) {
                        continue;
                    }
                    if (item.getDataElementVariable() != null) {

                        AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document);
                        if (ade == null) {
                            startLocation = startLocation + 1;
                            CreateDataElement(startLocation, item, connectedDataSource);

                        } else {
                            DataElement dataElement = HibernateUtil.getManager().getObject(DataElement.class, ade.getId());
                            setDataElement(dataElement, item, connectedDataSource);
                            dataElement.save();
                        }
                    }
                }
                DataSourceAssociation dsa = document.getDataSourceAssociation();
                for(ReferenceConnection connection : dsa.getReferenceConnections()) {
                    if (connection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED){
                        if (referenceDataVariableId > 0) {
                            connection.setReferenceVariable(DataElementVariable.findById(referenceDataVariableId));
                        }
                        if (primaryDataVariableId > 0) {
                            connection.setPrimaryVariable(DataElementVariable.findById(primaryDataVariableId));
                        }
                        connection.save();
                    }
                }

            } else {
                DataSourceAssociation dsa = document.getDataSourceAssociation();
                if (dsa != null) {
                    ServiceExecutionContext updateDataSourceServiceContext = UpdateDataSourceService.createContext();
                    UpdateDataSourceServiceRequest updateDataSourceServiceRequest = (UpdateDataSourceServiceRequest) updateDataSourceServiceContext.getRequest();
                    DataSource dataSource = new DataSource();
                    String name = generateUniqueName(document.getName());
                    dataSource.setName(name);
                    dataSource.setEncodingType(EncodingType.findById(EncodingType.TYPE_ASCII));
                    dataSource.setSourceType(SourceType.findById(SourceType.TYPE_REFERENCE_CONNECTED));

                    dataSource.setDelimeter(",");
                    dataSource.setLayoutType(LayoutType.findById(LayoutType.TYPE_DELIMITED));
                    updateDataSourceServiceRequest.setDataSource(dataSource);
                    Service updateDataSourceService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceService.SERVICE_NAME, UpdateDataSourceService.class);
                    updateDataSourceService.execute(updateDataSourceServiceContext);
                    document.getReferenceDataSources().add(dataSource);
                    document.save();
                    long startLocation = 1;
                    for (MetadataFormItemDefinitionVO item : metaDataFormItemDefinitions) {
                        if (item.getType().getId() == MetadataFormItemType.ID_DIVIDER || item.getType().getId() == MetadataFormItemType.ID_HEADER || item.getType().getId() == MetadataFormItemType.ID_FILE || item.getAction() == ORDER_ENTRY_ACTION_DELETE) {
                            continue;
                        }
                        AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document);
                        if (ade == null) {
                            CreateDataElement(startLocation, item, dataSource);
                            startLocation = startLocation + 1;

                        } else {
                            DataElement dataElement = HibernateUtil.getManager().getObject(DataElement.class, ade.getId());
                            setDataElement(dataElement, item, dataSource);
                            dataElement.save();
                        }

                    }


                    createReferenceConnection(dataSource, primaryDataVariableId, referenceDataVariableId, dsa, true);

                }
            }

        } catch (Exception ex){
            LOGGER.error("Error - Unable to create or update connected reference data source: " + ex.getMessage(), ex);
            throw ex;
        }

    }

    public static String generateUniqueName(String documentName){
        String baseName = documentName + "_" + CONNECTED_REFERENCE;
        DataSource dataSource = DataSource.findByName(baseName);
        if(dataSource != null){
            int counter = 1;
           String name = baseName + "_" + counter;
            while (DataSource.findByName(name) != null) {
                counter++;
                name = baseName + "_" + counter;
            }
            return name;
        } else {
            return baseName;
        }

    }

    private static void CreateDataElement(Long startLocation, MetadataFormItemDefinitionVO item, DataSource  connectedDataSource){
        DataElement dataElement = new DataElement();
        dataElement.setStartLocation(startLocation);
        setDataElement(dataElement, item, connectedDataSource);
        dataElement.save();
        DataElementVariable dataElementVariable = DataElementVariable.findById(item.getDataElementVariable().getId());
        VariableDataElementMap demap = new VariableDataElementMap();
        demap.setDataElement(dataElement);
        demap.setAggOperator(null);
        demap.setLevel(null);
        dataElementVariable.getDataElementMap().remove(connectedDataSource.getId());
        dataElementVariable.getDataElementMap().put(connectedDataSource.getId(), demap);
    }

    private static boolean existConnectedDataSource(Document document) {
        for (DataSource dataSource : document.getReferenceDataSources()) {
            if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                return true;
            }
        }
        return false;

    }

    private static boolean isSharedCollection(Document document) {
        Set<Document> sharedDocuments = document.getDataSourceAssociation().getDocuments();
        return sharedDocuments != null && (long) sharedDocuments.size() > 1;
    }

    public static DataSource getConnectedDataSource(Document document) {

        for (DataSource dataSource : document.getReferenceDataSources()) {
            if (dataSource.getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED) {
                return dataSource;
            }
        }
        return null;

    }

    private static void setDataElement(AbstractDataElement dataElement, MetadataFormItemDefinitionVO item, DataSource connectedDataSource) {
        dataElement.setName(item.getDataElementVariable().getName());
        dataElement.setDataSubtypeId(item.getDataElementTypeId());
        long categoryId = item.getDataElementTypeId();
        if (categoryId == 100) {
            dataElement.setDataTypeId(DataType.DATA_TYPE_STRING);
        } else if (categoryId >= 200 && categoryId <= 499) {
            dataElement.setDataTypeId(DataType.DATA_TYPE_NUMERIC);
        } else if (categoryId == 600) {
            dataElement.setDataTypeId(DataType.DATA_TYPE_DATE);
        } else if (categoryId == 800) {
            dataElement.setDataTypeId(DataType.DATA_TYPE_BOOLEAN);
        } else {
            dataElement.setDataTypeId(DataType.DATA_TYPE_STRING);
        }

        dataElement.setExternalFormatText(item.getDataElementInputTypeFormat());
        if (dataElement.getClass() == DataElement.class) {
            DataRecord[] dataRecords = connectedDataSource.getDataRecordArray();
            if (dataRecords != null) {
                DataRecord dataRecord = dataRecords[0];
                dataElement.setDataRecord(dataRecord);
            }
        }
    }

    public static void CreateJsonDataSource(Document document, List<MetadataFormItemDefinitionVO> metaDataFormItemDefinitions, Long primaryDataVariableId, Long referenceDataVariableId ) {

        try {
            if (existConnectedDataSource(document)) {
                DataSource connectedDataSource = getConnectedDataSource(document);

                JSONDataDefinition rootDefinition = JSONDataDefinition.findRootDefinitionByDataSource(connectedDataSource.getId());
                if(rootDefinition == null){
                    rootDefinition = createJsonRootDefinition( connectedDataSource);
                }
                JSONDataDefinition rootDefinitionFirstChild = rootDefinition.getChildList().stream().findFirst().orElse(null);
                for (MetadataFormItemDefinitionVO item : metaDataFormItemDefinitions) {
                    if (item.getType().getId() == MetadataFormItemType.ID_DIVIDER || item.getType().getId() == MetadataFormItemType.ID_HEADER || item.getType().getId() == MetadataFormItemType.ID_FILE || item.getAction() == ORDER_ENTRY_ACTION_DELETE) {
                        continue;
                    }
                    if (item.getDataElementVariable() != null) {

                        AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document);
                        if (ade == null) {
                            if(rootDefinitionFirstChild == null){
                                createJsonData(rootDefinition, item, connectedDataSource);

                            } else {
                                createJsonData(rootDefinitionFirstChild, item, connectedDataSource);
                            }

                        } else {
                            setDataElement(ade, item, connectedDataSource);
                            ade.save();
                        }
                    }
                }
                DataSourceAssociation dsa = document.getDataSourceAssociation();
                for(ReferenceConnection connection : dsa.getReferenceConnections()) {
                    if (connection.getReferenceDataSource().getSourceType().getId() == SourceType.TYPE_REFERENCE_CONNECTED){
                        if (referenceDataVariableId > 0) {
                            connection.setReferenceVariable(DataElementVariable.findById(referenceDataVariableId));
                        }
                        if (primaryDataVariableId > 0) {
                            connection.setPrimaryVariable(DataElementVariable.findById(primaryDataVariableId));
                        }
                        connection.save();
                    }
                }

            } else {
                DataSourceAssociation dsa = document.getDataSourceAssociation();
                if (dsa != null) {
                    ServiceExecutionContext updateDataSourceServiceContext = UpdateDataSourceService.createContext();
                    UpdateDataSourceServiceRequest updateDataSourceServiceRequest = (UpdateDataSourceServiceRequest) updateDataSourceServiceContext.getRequest();
                    DataSource dataSource = new DataSource();
                    String name = generateUniqueName(document.getName());
                    dataSource.setName(name);
                    dataSource.setEncodingType(EncodingType.findById(EncodingType.TYPE_ASCII));
                    dataSource.setSourceType(SourceType.findById(SourceType.TYPE_REFERENCE_CONNECTED));
                    dataSource.setLayoutType(LayoutType.findById(LayoutType.TYPE_JSON));
                    updateDataSourceServiceRequest.setDataSource(dataSource);
                    Service updateDataSourceService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceService.SERVICE_NAME, UpdateDataSourceService.class);
                    updateDataSourceService.execute(updateDataSourceServiceContext);
                    document.getReferenceDataSources().add(dataSource);
                    document.save();
                    JSONDataDefinition rootDefinition = createJsonRootDefinition( dataSource);
                    for (MetadataFormItemDefinitionVO item : metaDataFormItemDefinitions) {
                        if (item.getType().getId() == MetadataFormItemType.ID_DIVIDER || item.getType().getId() == MetadataFormItemType.ID_HEADER || item.getType().getId() == MetadataFormItemType.ID_FILE || item.getAction() == ORDER_ENTRY_ACTION_DELETE) {
                            continue;
                        }
                        if(item.getDataElementVariable() == null){
                            continue;
                        }
                        AbstractDataElement ade = AbstractDataElement.findDataElementByVariableId(item.getDataElementVariable().getId(), document);
                        if (ade == null) {
                            createJsonData(rootDefinition, item, dataSource);

                        } else {
                            DataElement dataElement = HibernateUtil.getManager().getObject(DataElement.class, ade.getId());
                            if(dataElement != null) {
                                setDataElement(dataElement, item, dataSource);
                                dataElement.save();
                            }
                        }

                    }


                   createReferenceConnection(dataSource, primaryDataVariableId, referenceDataVariableId, dsa, true);

                }
            }

        } catch (Exception ex){
            LOGGER.error("Error - Unable to create or update connected reference data source: " + ex.getMessage(), ex);
            throw ex;
        }

    }

    private static void createReferenceConnection(DataSource dataSource, Long primaryDataVariableId, Long referenceDataVariableId, DataSourceAssociation dsa, Boolean isConnectedReference){
        ReferenceConnection referenceConnection = new ReferenceConnection();
        referenceConnection.setReferenceDataSource(dataSource);
        if(isConnectedReference) {
            referenceConnection.setConnectorParameter(CONNECTED_REFERENCE_CONNECTOR_PARAMETER);
        }
        if (referenceDataVariableId > 0) {
            referenceConnection.setReferenceVariable(DataElementVariable.findById(referenceDataVariableId));
        }
        if (primaryDataVariableId > 0) {
            referenceConnection.setPrimaryVariable(DataElementVariable.findById(primaryDataVariableId));
        }
        referenceConnection.setPrimaryCompoundKey(null);
        referenceConnection.setReferenceCompoundKey(null);
        Set<ReferenceConnection> referenceConnections = new HashSet<>();
        referenceConnections.add(referenceConnection);
        ServiceExecutionContext ctx = UpdateDataSourceAssociationService.createContext(dsa.getId(),
                dsa.getName(),
                dsa.getPrimaryDataSource(),
                referenceConnections,
                dsa.getCustomerDataElementId());
        Service updateDataSourceAssociationService = MessagepointServiceFactory.getInstance().lookupService(UpdateDataSourceAssociationService.SERVICE_NAME, UpdateDataSourceAssociationService.class);
        updateDataSourceAssociationService.execute(ctx);

    }


    public static JSONDataDefinition createJsonRootDefinition(DataSource  connectedDataSource){
        JSONDataDefinition dataDefinition = new JSONDataDefinition();
        dataDefinition.setDataSource(connectedDataSource);
        dataDefinition.setDefinitionType(JSONDefinitionType.ID_ARRAY);
        dataDefinition.setStartDataGroup(false);
        dataDefinition.setName(JSONDefinitionType.getArrayType().getName().toUpperCase());
        dataDefinition.setDataGroup(null);
        JSONDataDefinition dataDefinitionObjectIdentifier = new JSONDataDefinition();
        dataDefinitionObjectIdentifier.setJsonDataElement(null);
        dataDefinitionObjectIdentifier.setStartDataGroup(false);
        dataDefinitionObjectIdentifier.setDataSource(connectedDataSource);
        dataDefinitionObjectIdentifier.setDefinitionType(JSONDefinitionType.ID_OBJECT);
        dataDefinitionObjectIdentifier.setName(JSONDefinitionType.getObjectType().getName().toUpperCase());
        dataDefinitionObjectIdentifier.setDataGroup(null);
        dataDefinitionObjectIdentifier.setParentDefinition(dataDefinition);
        HibernateUtil.getManager().saveObject(dataDefinition);
        HibernateUtil.getManager().saveObject(dataDefinitionObjectIdentifier);
        return dataDefinitionObjectIdentifier;

    }

    public static void createJsonData(JSONDataDefinition parent, MetadataFormItemDefinitionVO item, DataSource  connectedDataSource) {
        DataElementVariable dataElementVariable = DataElementVariable.findById(item.getDataElementVariable().getId());
        JSONDataDefinition dataDefinitionKey = getJsonDataDefinitionKey(parent, connectedDataSource, dataElementVariable, item.getName());


        JSONDataDefinition dataDefinition = getJsonDataDefinitionObject(connectedDataSource, dataDefinitionKey);

        JSONDataDefinition dataDefinitionValue = new JSONDataDefinition();
        dataDefinitionValue.setDataSource(connectedDataSource);
        dataDefinitionValue.setDefinitionType(JSONDefinitionType.ID_KEY);
        dataDefinitionValue.setParentDefinition(dataDefinition);
        dataDefinitionValue.setName("VALUE");
        JSONDataElement jsonDataElement = new JSONDataElement();
        setDataElement(jsonDataElement, item, connectedDataSource);
        dataDefinitionValue.setJsonDataElement(jsonDataElement);
        jsonDataElement.save();
        HibernateUtil.getManager().saveObject(dataDefinitionValue);

        VariableDataElementMap demap = new VariableDataElementMap();
        demap.setDataElement(jsonDataElement);
        demap.setAggOperator(null);
        demap.setLevel(null);
        dataElementVariable.getDataElementMap().remove(connectedDataSource.getId());
        dataElementVariable.getDataElementMap().put(connectedDataSource.getId(), demap);


    }

    public static void createJsonData(JSONDataDefinition parent, AbstractDataElement abstractDataElement, DataSource  connectedDataSource) {
        DataElementVariable dataElementVariable = DataElementVariable.findDataElementVariableByDataElementId(abstractDataElement.getId());
        JSONDataDefinition dataDefinitionKey = getJsonDataDefinitionKey(parent, connectedDataSource, dataElementVariable, abstractDataElement.getName());


        JSONDataDefinition dataDefinition = getJsonDataDefinitionObject(connectedDataSource, dataDefinitionKey);

        JSONDataDefinition dataDefinitionValue = new JSONDataDefinition();
        dataDefinitionValue.setDataSource(connectedDataSource);
        dataDefinitionValue.setDefinitionType(JSONDefinitionType.ID_KEY);
        dataDefinitionValue.setParentDefinition(dataDefinition);
        dataDefinitionValue.setName("VALUE");
        JSONDataElement jsonDataElement = new JSONDataElement();
        setDataElement(jsonDataElement, abstractDataElement);
        dataDefinitionValue.setJsonDataElement(jsonDataElement);
        jsonDataElement.save();
        HibernateUtil.getManager().saveObject(dataDefinitionValue);

        if(dataElementVariable!= null) {
            VariableDataElementMap demap = new VariableDataElementMap();
            demap.setDataElement(jsonDataElement);
            demap.setAggOperator(null);
            demap.setLevel(null);
            dataElementVariable.getDataElementMap().remove(connectedDataSource.getId());
            dataElementVariable.getDataElementMap().put(connectedDataSource.getId(), demap);
        }


    }

    @NotNull
    private static JSONDataDefinition getJsonDataDefinitionObject(DataSource connectedDataSource, JSONDataDefinition dataDefinitionKey) {
        JSONDataDefinition dataDefinition = new JSONDataDefinition();
        dataDefinition.setDataSource(connectedDataSource);
        dataDefinition.setDefinitionType(JSONDefinitionType.ID_OBJECT);
        dataDefinition.setJsonDataElement(null);
        dataDefinition.setStartDataGroup(false);
        dataDefinition.setName(JSONDefinitionType.getObjectType().getName().toUpperCase());
        dataDefinition.setDataGroup(null);
        dataDefinition.setParentDefinition(dataDefinitionKey);
        HibernateUtil.getManager().saveObject(dataDefinition);
        return dataDefinition;
    }

    @NotNull
    private static JSONDataDefinition getJsonDataDefinitionKey(JSONDataDefinition parent, DataSource connectedDataSource, DataElementVariable dataElementVariable, String dataElementName) {
        JSONDataDefinition dataDefinitionKey = new JSONDataDefinition();
        dataDefinitionKey.setDataSource(connectedDataSource);
        dataDefinitionKey.setDefinitionType(JSONDefinitionType.ID_KEY);
        dataDefinitionKey.setParentDefinition(parent);
        if(dataElementVariable != null) {
            dataDefinitionKey.setName(dataElementVariable.getGuid());
        } else {
            dataDefinitionKey.setName(dataElementName + "_unused");
        }
        HibernateUtil.getManager().saveObject(dataDefinitionKey);
        return dataDefinitionKey;
    }

    public static void setDataElement(AbstractDataElement dataElement, AbstractDataElement oldDataElement) {
        dataElement.setName(oldDataElement.getName());
        dataElement.setDataSubtypeId(oldDataElement.getDataSubtypeId());
        dataElement.setDataTypeId(oldDataElement.getDataTypeId());
        dataElement.setExternalFormatText(oldDataElement.getExternalFormatText());

    }
}
