package com.prinova.messagepoint.controller.communication;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import com.prinova.messagepoint.controller.metadata.CriteriaItemVO;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionUtil;
import com.prinova.messagepoint.model.communication.RepeatingDataType;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionWrapper;
import com.prinova.messagepoint.controller.metadata.MetadataFormItemDefinitionVO;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.communication.CommunicationDataPrivacyType;
import com.prinova.messagepoint.model.communication.UploadedFileType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class CommunicationOrderEntrySetupValidator extends MessagepointInputValidator {

    @SuppressWarnings("unchecked")
    public void validateNotGenericInputs(Object commandObj, Errors errors) {
        MetadataFormDefinitionWrapper wrapper = (MetadataFormDefinitionWrapper) commandObj;

        if (wrapper.getPrimaryDataVariableId() <=0 || wrapper.getReferenceDataVariableId() <=0) {
            errors.reject("error.order.entry.primary.variable.reference.variable.must.be.selected");
        }

        boolean hasPrimaryDriverSet = false;
        for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs())
            if (currentVO.getAction() != -9 && currentVO.getIsPrimaryDriverEntry()) {
                hasPrimaryDriverSet = true;
                break;
            }

        if (!hasPrimaryDriverSet)
            errors.reject("error.order.entry.primary.driver.must.be.selected");

        boolean hasDuplicateOrder = false;
        boolean hasOrphanItems = false;
        List<Integer> orderList = new ArrayList<>();
        for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs()) {
            if (orderList.contains(currentVO.getOrder())) {
                hasDuplicateOrder = true;
            }
            orderList.add(currentVO.getOrder());
        }
        for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs()) {
            if (currentVO.getParentOrder() != null && !orderList.contains(currentVO.getParentOrder())) {
                hasOrphanItems = true;
                break;
            }
        }
        if (hasDuplicateOrder || hasOrphanItems) {
            errors.reject("error.order.entry.duplicate.order.number");
        }

        Document documentContext = UserUtil.getCurrentTouchpointContext();
        if (documentContext.getCommunicationDriverOrderEntryItem() != null) {
            for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs())
                if (currentVO.getAction() != -9 && currentVO.getIsPrimaryDriverEntry() && currentVO.getId() != documentContext.getCommunicationDriverOrderEntryItem().getId()) {

                    StringBuilder query = new StringBuilder("	SELECT 		c ");
                    query.append("						   	FROM 		Communication as c ");
                    query.append("							WHERE		c.document.id = :documentId ");

                    Map<String, Object> params = new HashMap<>();
                    params.put("documentId", documentContext.getId());

                    List<Communication> communicationList = new ArrayList<>((List<Communication>) HibernateUtil.getManager().getObjectsAdvanced(query.toString(), params, 1, 1, "ORDER BY c.id DESC"));

                    if (!communicationList.isEmpty())
                        errors.reject("error.cannot.change.primary.driver.once.communications.authored");
                }
        }

        boolean webServiceApplied = false;
        boolean dataPrivacyApplied = false;
        boolean hasAlreadyDriveFile = false;
        boolean hasAlreadyPrimary = false;
        boolean hasAlreadyIndicator = false;
        boolean lastRepeatingDataTypeWasRepeating = false;
        boolean lastRepeatingDataTypeEasGroupWithAbove = false;
        for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs()) {
            if (currentVO.getAction() != -9) {

                if (currentVO.getType().getId() == MetadataFormItemType.ID_WEB_SERVICE_MENU || currentVO.getType().getId() == MetadataFormItemType.ID_WEB_SERVICE_TEXT)
                    webServiceApplied = true;

                if (currentVO.getType().getId()  != MetadataFormItemType.ID_DIVIDER  && (currentVO.getName() == null || currentVO.getName().isEmpty() || currentVO.getName().trim().isEmpty())) {
                    String label = ApplicationUtil.getMessage("page.label.name");
                    MessagepointInputValidationUtil.validateStringValue(label, currentVO.getName() == null ? currentVO.getName() :
                            currentVO.getName().trim(), true, 1, 96, ApplicationUtil.getMessage("page.text.validator.alphanum.space.dash.underscore.apos"), StringUtil.injectI18Nchars("^[A-Za-z0-9\\p{L}\\s_\\-']*+"), errors);
                }
                if(currentVO.getType().getId()  == MetadataFormItemType.ID_FILE && currentVO.getUploadedFileTypeId() == UploadedFileType.ID_PRIMARY_DRIVER){
                    if(hasAlreadyDriveFile){
                        errors.reject("error.message.order.entry.only.one.drive.file");
                    }
                    hasAlreadyDriveFile=true;

                }
                if(currentVO.getType().getId() == MetadataFormItemType.ID_FILE && currentVO.getUploadedFileTypeId() == UploadedFileType.ID_REFERENCE && currentVO.getUploadedFileDataSource() == null){
                    errors.reject("error.message.order.entry.data.reference.required", new String[]{currentVO.getName()}, "" );
                }
                List<CriteriaItemVO> criteriaItems = currentVO.getCriteriaItems();
                if (criteriaItems != null) {
                    for (CriteriaItemVO criteriaItemVO : criteriaItems) {
                        if (criteriaItemVO.getParentOrder() > 0 && (criteriaItemVO.getDisplayCriteria() == null || criteriaItemVO.getDisplayCriteria().trim().isEmpty()))
                            errors.reject("error.message.order.entry.display.criteria.required");
                    }
                    for (int i = 0; i < criteriaItems.size() - 1; i++)
                        for (int k = i + 1; k < criteriaItems.size(); k++) {
                            if (criteriaItems.get(i).getParentOrder() == criteriaItems.get(k).getParentOrder() && !Objects.equals(criteriaItems.get(i).getDisplayCriteria(), criteriaItems.get(k).getDisplayCriteria()) && currentVO.getCriteriaOperator()!= null && currentVO.getCriteriaOperator().equalsIgnoreCase(MetadataFormItemDefinitionUtil.AND_OPERATOR))
                                errors.reject("error.message.order.entry.criteria.not.correct.defined", new String[]{currentVO.getName()}, "");
                        }
                }
                if ((criteriaItems == null || (criteriaItems.isEmpty())) && currentVO.getParentOrder() != null && currentVO.getParentOrder() > 0 && (currentVO.getDisplayCriteria() == null || currentVO.getDisplayCriteria().trim().isEmpty())) {
                    errors.reject("error.message.order.entry.criteria.empty", new String[]{currentVO.getName()}, "");
                }

                if (shouldHaveDataElement(currentVO.getType().getId() ) && currentVO.getDataElementVariable() == null)
                    errors.reject("error.message.order.entry.data.element.variable.required", new String[]{currentVO.getName()}, "" );
                if(currentVO.getRegexValidation() != null && !currentVO.getRegexValidation().isEmpty()){
                    try {
                        Pattern.compile(currentVO.getRegexValidation());
                    } catch (PatternSyntaxException ex) {
                        errors.reject("error.message.order.entry.regex.validation", new String[]{currentVO.getRegexValidation(), currentVO.getName()}, "" );
                    }
                }

                if (currentVO.getType().getId() == MetadataFormItemType.ID_SELECT_MENU || currentVO.getType().getId() == MetadataFormItemType.ID_MULTISELECT_MENU) {
                    if (currentVO.getMenuValueItems() == null || currentVO.getMenuValueItems().isEmpty())
                        errors.reject("error.message.order.entry.menu.items.required", new String[]{currentVO.getName()}, "" );
                } else if (currentVO.getType().getId() == MetadataFormItemType.ID_TEXT) {
                    if (currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_INTEGER) {
                        if(!StringUtils.isEmpty(currentVO.getTextDefaultValue())) {
                            try {
                                Integer.parseInt(currentVO.getTextDefaultValue());
                            } catch (Exception e) {
                                errors.reject("error.message.default.value.not.match.validation.type");
                            }
                        }
                    } else if (currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_DECIMAL) {
                        if (!StringUtils.isEmpty(currentVO.getTextDefaultValue()) && !currentVO.getTextDefaultValue().matches("[0-9.]*")) {
                            errors.reject("error.message.default.value.not.match.validation.type");
                        }
                    } else if(currentVO.getFieldValidationTypeId() == MetadataFormInputValidationType.ID_ALPHANUM){
                        if (!StringUtils.isEmpty(currentVO.getTextDefaultValue()) && !currentVO.getTextDefaultValue().matches("[A-Za-z0-9]*")) {
                            errors.reject("error.message.default.value.not.match.validation.type");
                        }
                    }
                }
                if(currentVO.getRepeatingDataTypeId() != null && currentVO.getRepeatingDataTypeId() == RepeatingDataType.ID_REPEAT){
                    lastRepeatingDataTypeWasRepeating  = true;
                } else  if (currentVO.getRepeatingDataTypeId() != null && currentVO.getRepeatingDataTypeId() == RepeatingDataType.ID_REPEAT_GROUP_WITH_ABOVE) {
                    if (!lastRepeatingDataTypeWasRepeating && !lastRepeatingDataTypeEasGroupWithAbove) {
                        errors.reject("error.message.each.group.should.start.with.repeating", new String[]{currentVO.getName()}, "");
                    }
                    lastRepeatingDataTypeEasGroupWithAbove = true;
                } else{
                    lastRepeatingDataTypeWasRepeating  = false;
                    lastRepeatingDataTypeEasGroupWithAbove = false;
                }

                if(currentVO.getIsPrimaryDriverEntry()){
                    if(hasAlreadyPrimary){
                        errors.reject("error.message.duplicate.primary.order");
                    }
                    hasAlreadyPrimary = true;
                }

                if(currentVO.getIsIndicatorEntry()){
                    if(hasAlreadyIndicator){
                        errors.reject("error.message.duplicate.indicator.order");
                    }
                    hasAlreadyIndicator = true;
                }

                if (currentVO.getDataPrivacyTypeId() == CommunicationDataPrivacyType.ID_NON_PERSISTED || currentVO.getDataPrivacyTypeId() == CommunicationDataPrivacyType.ID_EXTERNAL)
                    dataPrivacyApplied = true;

                if (currentVO.getType().getId() == MetadataFormItemType.ID_FILE && currentVO.getUploadedFileTypeId() == UploadedFileType.ID_REFERENCE &&
                        (!documentContext.getNonConnectedReferenceDataSources().isEmpty() && currentVO.getUploadedFileDataSource() == null)) {
                    errors.reject("error.message.reference.source.required.for.reference.file.type");
                }
            }
        }

        if (webServiceApplied || dataPrivacyApplied) {

            if (webServiceApplied) {
                if (wrapper.getDocument().getCommunicationDataFeedWebService() == null ||
                        StringUtil.isEmptyOrNull(wrapper.getDocument().getCommunicationDataFeedWebService().getUrl()) || wrapper.getDocument().getCommunicationDataFeedWebService().getUrl().trim().isEmpty() ||
                        wrapper.getDocument().getCommunicationDataFeedWebService().getUsername() == null || wrapper.getDocument().getCommunicationDataFeedWebService().getUsername().isEmpty() || wrapper.getDocument().getCommunicationDataFeedWebService().getUsername().trim().isEmpty() ||
                        wrapper.getDocument().getCommunicationDataFeedWebService().getPassword() == null || wrapper.getDocument().getCommunicationDataFeedWebService().getPassword().isEmpty() || wrapper.getDocument().getCommunicationDataFeedWebService().getPassword().trim().isEmpty()) {
                    return;
                    //errors.reject("error.message.order.entry.web.service.attributes.required");
                }
            }

            Map<String, List<String>> connectorToNamesMap = new HashMap<>();

            for (MetadataFormItemDefinitionVO currentVO : wrapper.getMetadataFormItemDefinitionVOs()) {
                if (currentVO.getAction() != -9 && currentVO.getPrimaryConnector() != null &&
                        !currentVO.getPrimaryConnector().trim().isEmpty()) {

                    String connector = currentVO.getPrimaryConnector().trim();
                    connectorToNamesMap.putIfAbsent(connector, new ArrayList<>());
                    connectorToNamesMap.get(connector).add(currentVO.getName());
                }
            }


            for (Map.Entry<String, List<String>> entry : connectorToNamesMap.entrySet()) {
                if (entry.getValue().size() > 1) {
                    List<String> duplicateNames = new ArrayList<>();
                    duplicateNames.addAll(entry.getValue());
                    String joinedNames = String.join(", ", duplicateNames);
                    errors.reject("error.message.order.entry.unique.connector.values.required", new String[]{joinedNames}, "");
                }
            }
        }

    }

    private boolean shouldHaveDataElement(int typeId){
       if (typeId == MetadataFormItemType.ID_FILE || typeId == MetadataFormItemType.ID_HEADER || typeId == MetadataFormItemType.ID_DIVIDER){
           return false;
       }
       return true;
    }
}
