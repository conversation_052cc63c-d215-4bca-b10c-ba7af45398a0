package com.prinova.messagepoint.controller.communication;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;

public class TouchpointCommunicationsListWrapper implements Serializable {

	private static final long serialVersionUID = -3126776441826997661L;

	private List<Long>			selectedIds;

	private String 				userNote;
	private User 				assignedToUser;
	private String				recipientIdentifier;
	private String 				actionValue;
	private String				langCode;
	private String 				proofEmail;
	private String 				socializeEmail;
	
	private Date 				batchWindowStartDate;
	private Date				batchWindowEndDate;
	
	private String				exportName	= "";
	private int 				extType 	= 0;

	private Boolean				cloneInterview = true;
	private Boolean				cloneInteractive = true;

	private Boolean				dontAskAgain = false;
	
	public TouchpointCommunicationsListWrapper() {
		super();
		this.selectedIds = new ArrayList<>();
	}

	public String getUserNote() {
		return userNote;
	}
	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}

	public User getAssignedToUser() {
		return assignedToUser;
	}
	public void setAssignedToUser(User assignedToUser) {
		this.assignedToUser = assignedToUser;
	}

	public String getRecipientIdentifier() {
		return recipientIdentifier;
	}
	public void setRecipientIdentifier(String recipientIdentifier) {
		this.recipientIdentifier = recipientIdentifier;
	}

	public String getActionValue() {
		return actionValue;
	}
	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}

	public List<Long> getSelectedIds() {
		return selectedIds;
	}
	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getLangCode() {
		return langCode;
	}
	public void setLangCode(String langCode) {
		this.langCode = langCode;
	}

	public String getProofEmail() {
		return proofEmail;
	}
	public void setProofEmail(String proofEmail) {
		this.proofEmail = proofEmail;
	}

	public String getSocializeEmail() { return socializeEmail; }
	public void setSocializeEmail(String socializeEmail) { this.socializeEmail = socializeEmail; }

	public Date getBatchWindowStartDate() {
		return batchWindowStartDate;
	}
	public void setBatchWindowStartDate(Date batchWindowStartDate) {
		this.batchWindowStartDate = batchWindowStartDate;
	}

	public Date getBatchWindowEndDate() {
		return batchWindowEndDate;
	}
	public void setBatchWindowEndDate(Date batchWindowEndDate) {
		this.batchWindowEndDate = batchWindowEndDate;
	}

	public List<Communication> getSelectedList(){
		List<Communication> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(Communication.class, selectedId));
		}
		return selectedList;
	}

	public String getExportName() {
		return exportName;
	}

	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public int getExtType() {
		return extType;
	}

	public void setExtType(int extType) {
		this.extType = extType;
	}

	public Boolean getCloneInterview() { return cloneInterview; }
	public void setCloneInterview(Boolean cloneInterview) { this.cloneInterview = cloneInterview; }

	public Boolean getCloneInteractive() { return cloneInteractive; }
	public void setCloneInteractive(Boolean cloneInteractive) { this.cloneInteractive = cloneInteractive; }

	public Boolean getDontAskAgain() {
		return dontAskAgain;
	}

	public void setDontAskAgain(Boolean dontAskAgain) {
		this.dontAskAgain = dontAskAgain;
	}
}