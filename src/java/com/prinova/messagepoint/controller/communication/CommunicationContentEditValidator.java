package com.prinova.messagepoint.controller.communication;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.util.EmailUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class CommunicationContentEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		CommunicationContentEditWrapper wrapper = (CommunicationContentEditWrapper) commandObj;

		for ( Long currentZoneId : wrapper.getZoneContentMap().keySet() ) {
			ContentVO contentVO = wrapper.getZoneContentMap().get(currentZoneId);
			
			// Reject text content if it contains forbidden characters
			if ( ContentObjectContentUtil.hasForbiddenCharacters(contentVO.getContent()) )
				errors.reject("error.message.invalid.text.content");
		}
		
		if ( (wrapper.getActionValue().equalsIgnoreCase(String.valueOf(CommunicationContentEditController.ACTION_SAVE_AND_PREVIEW)) ||
			  wrapper.getActionValue().equalsIgnoreCase(String.valueOf(CommunicationContentEditController.ACTION_SUBMIT)) ||
		  	  wrapper.getActionValue().equalsIgnoreCase(String.valueOf(CommunicationContentEditController.ACTION_UPDATE)) ) &&
			wrapper.getCommunication().isDigitalOrder()	) {
				if ( wrapper.getEmail() != null && !wrapper.getEmail().trim().isEmpty() &&
						 !EmailUtil.isValidEmailAddress(wrapper.getEmail().trim()) )
						errors.reject("error.message.order.digital.proof.email");
			}
		
	}
}
