package com.prinova.messagepoint.controller.communication;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.analytics.*;
import com.prinova.messagepoint.analytics.types.Actions;
import com.prinova.messagepoint.analytics.types.ConnectedEvents;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.controller.StaticTypeIdCustomEditor;
import com.prinova.messagepoint.controller.metadata.MetadataFormDefinitionWrapper;
import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.communication.CommunicationDataPrivacyType;
import com.prinova.messagepoint.model.communication.UploadedFileType;
import com.prinova.messagepoint.model.dataadmin.AbstractDataElement;
import com.prinova.messagepoint.model.metadata.MetadataFormDefinitionType;
import com.prinova.messagepoint.model.metadata.MetadataFormFieldSizeType;
import com.prinova.messagepoint.model.metadata.MetadataFormInputValidationType;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.model.metadata.MetadataWebserviceValueRefreshType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormDefinitionService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.UserUtil;

public class CommunicationOrderEntrySetupController extends MessagepointController {

	private static final Log log = LogUtil.getLog(CommunicationOrderEntrySetupController.class);
	
	public static final String 	REQ_PARAM_DOCUMENT_ID 	= "documentId";
	public static final String 	REQ_PARAM_SAVE_SUCCESS	= "saveSuccess";
	
	public static final String 	FORM_SUBMIT_TYPE_SUBMIT = "submit";
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {

		Map<String, Object> referenceData = new HashMap<>();
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		Document document = Document.findById(documentId);
		
		referenceData.put("document", document);

		List<MetadataFormItemType> metadataFormItemTypes = MetadataFormItemType.listAll();
		metadataFormItemTypes.remove( new MetadataFormItemType(MetadataFormItemType.ID_TEXT_EDITOR) );
		referenceData.put("metadataFormItemTypes", metadataFormItemTypes );
		
		referenceData.put("connectedDataPrivacyTypes", CommunicationDataPrivacyType.listAll());
		
		referenceData.put("metadataFormDefinitionTypes", MetadataFormDefinitionType.listAll() );
		
		referenceData.put("metadataFormFieldSizeTypes", MetadataFormFieldSizeType.listAll());
		
		referenceData.put("metadataFormFieldValidationTypes", MetadataFormInputValidationType.listAll());
		
		referenceData.put("appliesVariantSelectToggle", document.isEnabledForVariation() );

		referenceData.put("webServiceRefreshTypes", MetadataWebserviceValueRefreshType.listAll() );
		
		referenceData.put("uploadedFileTypes", UploadedFileType.listAll());
		referenceData.put("referenceDataSources", document.getNonConnectedReferenceDataSources());

		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.setAutoGrowCollectionLimit(1024);
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(Integer.class, new CustomNumberEditor(Integer.class, true));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(AbstractDataElement.class, new IdCustomEditor<>(AbstractDataElement.class));
		binder.registerCustomEditor(MetadataFormItemType.class, new StaticTypeIdCustomEditor<>(MetadataFormItemType.class));
		binder.registerCustomEditor(TouchpointSelection.class, new IdCustomEditor<>(TouchpointSelection.class) );
		binder.registerCustomEditor(DataSource.class, new IdCustomEditor<>(DataSource.class) );
	}

	protected MetadataFormDefinitionWrapper formBackingObject(HttpServletRequest request) throws Exception {
		
		long documentId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DOCUMENT_ID, -1);
		
		MetadataFormDefinitionWrapper wrapper = new MetadataFormDefinitionWrapper();
		Document document = Document.findById(documentId);
		if ( document != null )
			wrapper = new MetadataFormDefinitionWrapper(document, isFormSubmission(request));
		wrapper.setDocument(document);
		
		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj,
			BindException errors) throws Exception {
		
		MetadataFormDefinitionWrapper wrapper = (MetadataFormDefinitionWrapper) commandObj;

		AnalyticsEvent<ConnectedEvents> analyticsEvent = AnalyticsUtil.requestFor(ConnectedEvents.OrderEntrySetup);

		try {
			User requestor = UserUtil.getPrincipalUser();

			ServiceExecutionContext context = CreateOrUpdateMetadataFormDefinitionService.createContextForCommunicationFormUpdate(wrapper.getDocument(), wrapper.getMetadataFormItemDefinitionVOs(), requestor);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME, CreateOrUpdateMetadataFormDefinitionService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();

			analyticsEvent.setAction(Actions.Setup);

			if (serviceResponse.isSuccessful()) {
				Map<String, Object> params = new HashMap<>();
				params.put(REQ_PARAM_SAVE_SUCCESS, true);
				params.put(REQ_PARAM_DOCUMENT_ID, wrapper.getDocument() != null ? wrapper.getDocument().getId() :-1);
				return new ModelAndView( new RedirectView( getSuccessView() ), params );
			} else {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateMetadataFormDefinitionService.SERVICE_NAME);
				sb.append(" service call is not successful ");
				sb.append(" in ").append(this.getClass().getName());
				sb.append(" Order Entry Setup was not saved. ");
				log.error(sb.toString());
				ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
				return super.showForm(request, response, errors);
			}
		} finally {
			analyticsEvent.send();
		}

	}
	
 	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
	}

}