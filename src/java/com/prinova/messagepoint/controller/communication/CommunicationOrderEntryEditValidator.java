package com.prinova.messagepoint.controller.communication;

import com.prinova.messagepoint.model.communication.CommunicationOrderEntryItem;
import com.prinova.messagepoint.model.metadata.MetadataFormItemType;
import com.prinova.messagepoint.util.StringUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

public class CommunicationOrderEntryEditValidator extends MessagepointInputValidator {
	
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		CommunicationOrderEntryEditWrapper wrapper = (CommunicationOrderEntryEditWrapper) commandObj;

		if (wrapper.getCommunication().isDebugOrder()) {
			return;
		}
		
		if ( wrapper.getCommunication().getNumberOfCopies() == null || !(wrapper.getCommunication().getNumberOfCopies() > 0) )
			errors.reject("error.communication.must.specify.positive.integer.number.copies");
		
		List<String> mandatoryValuesList = new ArrayList<>();
		int index = 0;
		for ( CommunicationOrderEntryItem item: wrapper.getOrderEntryItemsList() ) {
			String value = item.getValue();
			if ( item.getIsPrimaryDriverEntry() )
				value = wrapper.getCommunication().getCustomerIdentifier();
			if ( value != null )
				value = value.trim();
			if ( item.getIsManadatory()) {
				boolean missingInput = false;
				
				if(item.getTypeId() == MetadataFormItemType.ID_SELECT_MENU || item.getTypeId() == MetadataFormItemType.ID_WEB_SERVICE_MENU){
					if(value == null || value.isEmpty() || value.equals("0")){
						missingInput = true;
					}
				}else if(item.getTypeId() == MetadataFormItemType.ID_DATE_DAY_MONTH_YEAR || item.getTypeId() == MetadataFormItemType.ID_DATE_MONTH_YEAR){
					if(item.getDateStrInput() == null || item.getDateStrInput().isEmpty()){
						missingInput = true;
					}
				}else if(item.getTypeId() == MetadataFormItemType.ID_FILE){
					if(value == null || value.isEmpty()){
						missingInput = true;
					}
				}else{
					if(value == null || value.isEmpty()){
						missingInput = true;
					}
				}
				
				if(missingInput){
					if ( item.getParentItemOrder() == null || 
						 (item.getParentItemOrder() != null && wrapper.getOrderEntryItemsDisplayed().get(index)) ) {
						mandatoryValuesList.add(item.getName());
					}
				}

			}
			
			if ( item.isUniqueValue() && value != null){
				if(CommunicationOrderEntryItem.containsValue(item, value)){
					errors.reject("error.communication.order.value.is.used", new String[]{item.getName()}, "");
				}
			}
			
			index++;
		}

		if (!mandatoryValuesList.isEmpty())
			errors.reject("error.communication.mandatory.values", new String[] { StringUtil.join(mandatoryValuesList,", ") }, "");

	}
}
