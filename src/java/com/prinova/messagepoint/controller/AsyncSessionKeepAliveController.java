package com.prinova.messagepoint.controller;

import java.util.Date;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.IdentifiableMessagePointModel;
import com.prinova.messagepoint.model.communication.Communication;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.common.UpdateEditManagerService;

public class AsyncSessionKeepAliveController implements Controller {

	@SuppressWarnings("unused")
	private static final Log log = LogUtil.getLog(AsyncSessionKeepAliveController.class);

	private static final String REQ_PARAM_COMMUNICATION_ID = "communicationId";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();
		
		IdentifiableMessagePointModel model = ContentObject.findByHttpServletRequest(request);;

		if (model == null) {
			long communicationId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_COMMUNICATION_ID, -1L);
			if (communicationId > 0) {
				model = Communication.findById(communicationId);
			}
		}

		if ( model != null ) {
			ServiceExecutionContext context = UpdateEditManagerService.createContext(model, new Date() );
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateEditManagerService.SERVICE_NAME, UpdateEditManagerService.class);
			service.execute(context);

			if ( !context.getResponse().isSuccessful() ) {
				JSONObject returnObj = new JSONObject();
				returnObj.put("error", true);
				returnObj.put("message", context.getResponse().getMessages());
				out.write(returnObj.toString().getBytes());
				out.flush();
				
				return null;
			}
		}

		try {
			out.write( getResponseJSON(request,response).getBytes() );
		} catch (JSONException e) {
			JSONObject returnObj = new JSONObject();
			returnObj.put("error", true);
			returnObj.put("message", e.getMessage());
			out.write(returnObj.toString().getBytes());
		}
		out.flush();

		return null;
	}

	private String getResponseJSON (HttpServletRequest request, HttpServletResponse response) throws JSONException {
		
		JSONObject returnObj = new JSONObject();
			
		returnObj.put("status", "alive");
		
		return returnObj.toString();
	}

}