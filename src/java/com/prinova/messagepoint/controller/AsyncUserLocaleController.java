package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.admin.ApplicationLocale;
import com.prinova.messagepoint.model.admin.MessagepointLocale;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.UpdateUserLocaleService;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class AsyncUserLocaleController implements Controller {

    // TODO: Merge/Refactor this with asyncLanguageContextMenu Controller

	private static final Log log = LogUtil.getLog(AsyncUserLocaleController.class);

	public static final String REQ_PARM_TYPE 				= "type";
	public static final String REQ_PARM_LOCALE_ID			= "localeId";
	public static final String TYPE_UPDATE 					= "update";
	public static final String TYPE_LIST					= "list";
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		response.setContentType("application/json");
		ServletOutputStream out = response.getOutputStream();

		try {
			out.write(getResponseJson(request,response).getBytes());
			out.flush();
		} catch (Exception e) {
			log.error("Error - Unable to resolve request for user locale: "+e.getMessage(),e);
		}

		return null;
	}

	private String getResponseJson(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		String requestedType 	= getTypeParam(request);
		Long userId 			= UserUtil.getPrincipalUserId();
		Long localeId 			= getLocaleIdParam(request);

		try {
            if ( requestedType.equalsIgnoreCase(TYPE_UPDATE) ) {
                return updateUserLocale(userId, localeId, request);
            }else if( requestedType.equalsIgnoreCase(TYPE_LIST)){
                return listUserLocales(userId, request);
            }
        } catch (Exception e) {
		    log.error("Error:", e);
        }

		return null;
	}
	
	private String listUserLocales(Long userId, HttpServletRequest request) throws JSONException {
		List<ApplicationLocale>	appLocaleList 	= ApplicationLocale.findAllLicenced();
		
        JSONArray appLocales = new JSONArray();
		
		if ( appLocaleList != null && !appLocaleList.isEmpty()) {
			User user = userId == null ? null : User.findById(userId);
			Long currentAppLocaleId = user == null ? null : user.getApplicationLocaleId();
			if ( currentAppLocaleId == null || currentAppLocaleId <= 0 ) {
				currentAppLocaleId = ApplicationLocale.resolveMissingUserApplicationLocale().getMessagepointLocale().getId();
			}
			for (ApplicationLocale locale: appLocaleList) {
				MessagepointLocale mpLocale = locale.getMessagepointLocale();

				JSONObject jsonLocale = new JSONObject();

                jsonLocale.put("id", mpLocale.getId());
                jsonLocale.put("uppercaseLanguageCode", mpLocale.getFormatedLocaleToString());
                jsonLocale.put("displayName", mpLocale.getDisplayName());
                jsonLocale.put("selected", currentAppLocaleId == mpLocale.getId());

				appLocales.put(jsonLocale);
			}

			if ( ApplicationUtil.isDebugMode() ) {

			    JSONObject jsonLocale = new JSONObject();

			    jsonLocale.put("id", 999);
			    jsonLocale.put("uppercaseLanguageCode", "XX");
			    jsonLocale.put("displayName", "Test: XxXx");
			    jsonLocale.put("selected", currentAppLocaleId == 999);

				appLocales.put(jsonLocale);

                jsonLocale = new JSONObject();

                jsonLocale.put("id", 998);
                jsonLocale.put("uppercaseLanguageCode", "E");
                jsonLocale.put("displayName", "Test: X[abc]X");
                jsonLocale.put("selected", currentAppLocaleId == 998);

                appLocales.put(jsonLocale);
			}
			
		}

		return appLocales.toString();
	}

	private String updateUserLocale(Long userId, Long localeId, HttpServletRequest request) throws Exception {
		ServiceExecutionContext context = UpdateUserLocaleService.createContext(userId, localeId);
		Service updateLocaleService = MessagepointServiceFactory.getInstance().lookupService(UpdateUserLocaleService.SERVICE_NAME, UpdateUserLocaleService.class);
		
		updateLocaleService.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		
		JSONObject returnObj = new JSONObject();
		
		if ( !serviceResponse.isSuccessful() ) {
			returnObj.put("error", true);
			returnObj.put("message", "Failed to update user locale");
		}else{
			// Update the user locale information to the session attribute
			String langCode = null;
			if ( localeId == 999 )
				langCode = "xx";
			else if ( localeId == 998 )
				langCode = "zz";
			else
				langCode = MessagepointLocale.findById(localeId).getLanguageCode();
			request.getSession().setAttribute(ApplicationLocale.SESSION_ATTR_NAME, langCode);
			
			returnObj.put("error", false);
		}
		return returnObj.toString();
	}

	private String getTypeParam( HttpServletRequest request ) {
		return ServletRequestUtils.getStringParameter(request, REQ_PARM_TYPE, "null");
	}

	private Long getLocaleIdParam( HttpServletRequest request ) {
		return ServletRequestUtils.getLongParameter(request, REQ_PARM_LOCALE_ID, -1L);
	}	
}