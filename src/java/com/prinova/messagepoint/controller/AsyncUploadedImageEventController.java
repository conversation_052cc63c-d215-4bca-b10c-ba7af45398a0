package com.prinova.messagepoint.controller;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.jpedal.PdfDecoderServer;
import org.jpedal.constants.JPedalSettings;
import org.jpedal.exception.PdfException;
import org.jpedal.fonts.FontMappings;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.file.SandboxFile;
import com.prinova.messagepoint.model.tenant.Tenant;
import com.prinova.messagepoint.model.util.PDFToImage;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.file.CreateOrUpdateDatabaseFileService;
import com.prinova.messagepoint.platform.services.file.CreateOrUpdateSandboxFileService;
import com.prinova.messagepoint.tag.view.DocumentTag;

public class AsyncUploadedImageEventController implements Controller {
	
	private static final Log log = LogUtil.getLog(AsyncUploadedImageEventController.class);
	
	private static String PDF_IMAGE_RAW_PREFIX = 			"pdfWorkingImage";
	private static String GRAPHIC_IMAGE_RAW_PREFIX = 		"graphicWorkingImage";
	
	private static String IMAGE_SCALED_PREFIX = 	"scaledImage";
	private static String IMAGE_SELETEC_PREFIX = 	"selectedImage_";
	
	public static final String IMAGE_EXT = 			PDFToImage.IMAGE_PNG;
	public static final String EXT_PDF = 			"pdf";
	
	public static final String WORK_DIR_SECTION_IMAGES =	"sectionImages";
	public static final String WORK_DIR_INSERT_IMAGES = 	"insertImages";
	
	private static String PARAM_ACTION = 			"action";
	private static String PARAM_TYPE = 				"type";
	private static String PARAM_FILE_EXTENSION = 	"fileExt";
	
	private static String TYPE_INSERT_IMAGE = 		"insert";
	private static String TYPE_SECTION_IMAGE = 		"section";
	private static String TYPE_NO_TYPE = 			"noType";
	
	private static String ACTION_LOAD = 			"load";
	private static String ACTION_RETRIEVE = 		"retrieve";
	private static String ACTION_SELECT = 			"select";
	private static String ACTION_LM_LOAD_EXISTING = "layout_manager_load_existing";
	private static String ACTION_LM_UPLOAD = 		"layout_manager_upload_images";
	private static String ACTION_LM_APPLY_IMAGE = 	"layout_manager_apply_image";
	private static String ACTION_LM_REMOVE_IMAGE = 	"layout_manager_remove_image";
	
	private static String PARAM_SECTION = 			"section";
	private static String PARAM_DOCUMENT_ID = 		"documentId";
	private static String PARAM_IMAGE_PATH = 		"image";
	private static String PARAM_RANGE_PAGE_START = 	"pageRangeStart";
	private static String PARAM_RANGE_PAGE_END = 	"pageRangeEnd";
	private static String FILE_LABEL = 				"uploadedFile";
	
	private static int IMAGE_DIMENSION_LONG_SECTIONS =	1000;
	private static int IMAGE_DIMENSION_SHORT_SECTIONS =	800;
	private static int IMAGE_DIMENSION_LONG_INSERTS =	500;
	private static int IMAGE_DIMENSION_SHORT_INSERTS =	400;
	
	HttpServletRequest 	request;
	Long 				documentId = null;
	
	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {

		setRequest(request);

		if ( 	getActionRequestParameter().equals(ACTION_LOAD) || 
				getActionRequestParameter().equals(ACTION_RETRIEVE) || 
				getActionRequestParameter().equals(ACTION_SELECT) ) {
			
			response.setContentType("text/xml");
			ServletOutputStream out = response.getOutputStream();
	
			File operatingPath = new File(getOperatingPath());
			if (!operatingPath.exists())
				operatingPath.mkdirs(); 
			
			try {
				if (getActionRequestParameter().equals(ACTION_LOAD)) {
					deleteWorkspaceImages();
					String fileExt = ServletRequestUtils.getStringParameter(request, PARAM_FILE_EXTENSION, null);
					if (fileExt.equalsIgnoreCase(EXT_PDF))
						out.write(XML_getImagesFromUploadedPDF().getBytes());
					else
						out.write(XML_getImageFromUploadedGraphic(fileExt).getBytes());
				} else if (getActionRequestParameter().equals(ACTION_RETRIEVE))
					out.write(XML_getExistingImages().getBytes());
				else if (getActionRequestParameter().equals(ACTION_SELECT))
					out.write(XML_selectImageForUse().getBytes());
				out.flush();
			} catch (Exception e) {
				try {
					log.error(e.getMessage(),e);
					out.flush();
				} catch (RuntimeException e1) {
					log.error(e1.getMessage(),e1);
				}
			}
			
		} else if ( getActionRequestParameter().equals(ACTION_LM_LOAD_EXISTING) || 
					getActionRequestParameter().equals(ACTION_LM_UPLOAD) || 
					getActionRequestParameter().equals(ACTION_LM_APPLY_IMAGE) ||
					getActionRequestParameter().equals(ACTION_LM_REMOVE_IMAGE) ) {
			
			response.setContentType("text/plain");
			ServletOutputStream out = response.getOutputStream();

			setDocumentId( ServletRequestUtils.getLongParameter(request, PARAM_DOCUMENT_ID, -1L) );
			File operatingPath = new File(getOperatingPath());
			if (!operatingPath.exists())
				operatingPath.mkdirs(); 
			
			try {
				if ( getActionRequestParameter().equals(ACTION_LM_UPLOAD) ) {
					MultipartHttpServletRequest multipartRequest  = (MultipartHttpServletRequest)request;
					String[] fileExt 	= multipartRequest.getParameterValues(PARAM_FILE_EXTENSION);
					
					if (fileExt[0].equalsIgnoreCase(EXT_PDF))
						out.write(JSON_getImagesFromUploadedPDF().getBytes());
					else
						out.write(JSON_getImageFromUploadedGraphic(fileExt[0]).getBytes());
				} else if ( getActionRequestParameter().equals(ACTION_LM_LOAD_EXISTING) )
					out.write(JSON_getExistingImages().getBytes());
				else if ( getActionRequestParameter().equals(ACTION_LM_APPLY_IMAGE) )
					out.write(JSON_selectImageForUse().getBytes());
				else if ( getActionRequestParameter().equals(ACTION_LM_REMOVE_IMAGE) )
					out.write(JSON_removeImage().getBytes());
				out.flush();
			} catch (Exception e) {
				try {
					log.error(e.getMessage(),e);
					out.flush();
				} catch (RuntimeException e1) {
					log.error(e1.getMessage(),e1);
				}
			}
			
		}
		return null;
	}

	private String XML_getImagesFromUploadedPDF () throws Exception {
		
		MultipartHttpServletRequest multipartRequest  = (MultipartHttpServletRequest)getRequest();
		MultipartFile pdfFile = multipartRequest.getFile(FILE_LABEL);
		int pageRangeStart = -1;
		int pageRangeEnd = -1; 

		// Define page range if applicable for type
		if (getTypeRequestParameter().equals(TYPE_NO_TYPE) || getTypeRequestParameter().equals(TYPE_SECTION_IMAGE)) {
			if (!multipartRequest.getParameter(PARAM_RANGE_PAGE_START).trim().isEmpty())
				pageRangeStart = Integer.parseInt(multipartRequest.getParameter(PARAM_RANGE_PAGE_START).trim());
			if (!multipartRequest.getParameter(PARAM_RANGE_PAGE_END).trim().isEmpty())
				pageRangeEnd = Integer.parseInt(multipartRequest.getParameter(PARAM_RANGE_PAGE_END).trim());
		}
		
		File operatingPDF = initOperatingFile(pdfFile);

        StringBuffer contentItems = new StringBuffer();
		String[] filenames = generatePDFimages(operatingPDF.getAbsolutePath(), pageRangeStart, pageRangeEnd);
		if (filenames.length > 0)
			for (String currentFilename: filenames) {
				Integer[] dimensions = getImageDisplayDimensions(currentFilename,getTypeRequestParameter());
				contentItems.append("<token width='").append(dimensions[0]).append("' height='").append(dimensions[1]).append("' timeStamp='").append(DateUtil.timeStamp()).append("'>").append(HttpRequestUtil.getFileResourceToken(currentFilename)).append("</token>");
			}
		else
			contentItems.append("<error>No PDF images were generated</error>");
		return generateXMLreturnString(contentItems);	
	}
	private String JSON_getImagesFromUploadedPDF() throws Exception {
		JSONObject returnObj = new JSONObject();
		
		
		MultipartHttpServletRequest multipartRequest  = (MultipartHttpServletRequest)request;
		Map<String,MultipartFile> fileMap = multipartRequest.getFileMap();
		
		String[] paramPageRangeStart 	= multipartRequest.getParameterValues(PARAM_RANGE_PAGE_START);
		String[] paramPageRangeEnd 		= multipartRequest.getParameterValues(PARAM_RANGE_PAGE_END);
		int pageRangeStart = -1;
		int pageRangeEnd = -1; 

		// Define page range if applicable for type
		if (getTypeRequestParameter().equals(TYPE_NO_TYPE) || getTypeRequestParameter().equals(TYPE_SECTION_IMAGE)) {
			if (paramPageRangeStart.length != 0 && !paramPageRangeStart[0].trim().isEmpty())
				pageRangeStart = Integer.parseInt(paramPageRangeStart[0].trim());
			if (paramPageRangeEnd.length != 0 && !paramPageRangeEnd[0].trim().isEmpty())
				pageRangeEnd = Integer.parseInt(paramPageRangeEnd[0].trim());
		}
		
		JSONArray imagesArray = new JSONArray();
		JSONArray errorsArray = new JSONArray();
		for ( String currentIndex: fileMap.keySet() ) {
			MultipartFile currentPdfFile = fileMap.get(currentIndex);

			File operatingPDF = initOperatingFile(currentPdfFile);
			String[] filenames = generatePDFimages(operatingPDF.getAbsolutePath(), pageRangeStart, pageRangeEnd);
			if (filenames.length > 0) {
				for (String currentFilename: filenames) {
					JSONObject currentImageObj = new JSONObject();
					Integer[] dimensions = getImageDisplayDimensions(currentFilename,getTypeRequestParameter());
					currentImageObj.put("width", dimensions[0]);
					currentImageObj.put("height", dimensions[1]);
					currentImageObj.put("path", HttpRequestUtil.getFileResourceToken(currentFilename));
					imagesArray.put(currentImageObj);
				}
				
			} else {
				JSONObject errorObj = new JSONObject();
				errorObj.put("error", true);
				errorObj.put("message", "No images were generated for uploaded PDF: " + currentPdfFile.getOriginalFilename());
				errorsArray.put(errorObj);
			}
			
		}
		returnObj.put("errors", errorsArray);
		returnObj.put("sandbox_images", imagesArray);
		
		return returnObj.toString();	
	}
	
	private String XML_getImageFromUploadedGraphic (String graphicExtension) throws Exception {
		MultipartHttpServletRequest multipartRequest  = (MultipartHttpServletRequest)getRequest();
		MultipartFile graphicFile = multipartRequest.getFile(FILE_LABEL);
		try {
			graphicFile.transferTo(new File(getOperatingPath()+File.separator+GRAPHIC_IMAGE_RAW_PREFIX+"."+graphicExtension));
		} catch (Exception e) {
			log.error("Unable to transfer uploaded graphic file - "+e.getMessage(),e);
		}
		
		File workingImageDir = new File(getOperatingPath()+File.separator);
		File[] imageFiles = workingImageDir.listFiles(new FileListFilter(GRAPHIC_IMAGE_RAW_PREFIX,graphicExtension));

		String[] filenames = scaleRawImages(imageFiles);
        StringBuffer contentItems = new StringBuffer();
		if (filenames.length > 0)
			for (String currentFilename: filenames) {
				Integer[] dimensions = getImageDisplayDimensions(currentFilename,getTypeRequestParameter());
				contentItems.append("<token width='").append(dimensions[0]).append("' height='").append(dimensions[1]).append("' timeStamp='").append(DateUtil.timeStamp()).append("'>").append(HttpRequestUtil.getFileResourceToken(currentFilename)).append("</token>");
			}
		else
			contentItems.append("<error>No graphic images were generated</error>");
		return generateXMLreturnString(contentItems);	
	}
	private String JSON_getImageFromUploadedGraphic (String graphicExtension) throws Exception {
		JSONObject returnObj = new JSONObject();
		
		
		MultipartHttpServletRequest multipartRequest  = (MultipartHttpServletRequest)request;
		Map<String,MultipartFile> fileMap = multipartRequest.getFileMap();
		
		JSONArray errorsArray = new JSONArray();
		List<Long> databaseFileIds = new ArrayList<>();
		for ( String currentIndex: fileMap.keySet() ) {
				
			MultipartFile currentGraphicFile = fileMap.get(currentIndex);
			
			ServiceExecutionContext context = CreateOrUpdateDatabaseFileService.createContextForCreate(currentGraphicFile, SandboxFile.TYPE_SECTION_BACKGROUND_IMG);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateDatabaseFileService.SERVICE_NAME, CreateOrUpdateDatabaseFileService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if ( !serviceResponse.isSuccessful() ) {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
				sb.append(" service call is not successful in AsyncUploadedImageEventController");
				log.error(sb.toString());
			} else {
				
				Long databaseFileId = (Long) serviceResponse.getResultValueBean();
				
				try {
					String ext = currentGraphicFile.getOriginalFilename().substring( currentGraphicFile.getOriginalFilename().lastIndexOf(".") + 1 );
					currentGraphicFile.transferTo(new File(getOperatingPath()+File.separator+GRAPHIC_IMAGE_RAW_PREFIX+"."+ext));
					databaseFileIds.add(databaseFileId);
				} catch (IOException e) {
					JSONObject errorObj = new JSONObject();
					errorObj.put("error", true);
					errorObj.put("message", "Unable to transfer uploaded graphic file: "+currentGraphicFile.getOriginalFilename());
					errorsArray.put(errorObj);
					log.error("Unable to transfer uploaded graphic file - "+e.getMessage(),e);
				}
			}

		}
		
		File workingImageDir = new File(getOperatingPath()+File.separator);
		File[] imageFiles = workingImageDir.listFiles(new FileListFilter(GRAPHIC_IMAGE_RAW_PREFIX,null));

		String[] filenames = scaleRawImages(imageFiles, databaseFileIds);
		if (filenames.length > 0) {
			JSONArray imagesArray = new JSONArray();
			for (String currentFilename: filenames) {
				JSONObject currentImageObj = new JSONObject();
				Integer[] dimensions = getImageDisplayDimensions(currentFilename,getTypeRequestParameter());
				currentImageObj.put("width", dimensions[0]);
				currentImageObj.put("height", dimensions[1]);
				currentImageObj.put("path", HttpRequestUtil.getFileResourceToken(currentFilename));
				imagesArray.put(currentImageObj);
			}
			returnObj.put("sandbox_images", imagesArray);
		} else {
			JSONObject errorObj = new JSONObject();
			errorObj.put("error", true);
			errorObj.put("message", "No images were generated");
			errorsArray.put(errorObj);
		}
		
		returnObj.put("errors", errorsArray);
		
		return returnObj.toString();	
	}

	private String XML_getExistingImages() throws Exception {
		// Get list of generated raw images
		File workingImageDir = new File(getOperatingPath());
		File[] imageFiles = workingImageDir.listFiles(new FileListFilter(IMAGE_SCALED_PREFIX,IMAGE_EXT));
		
		StringBuffer contentItems = new StringBuffer();
		if (imageFiles == null || imageFiles.length == 0) {
			contentItems.append("<error>Error retrieving existing images</error>");
		} else {
			for (File currentFile: imageFiles) {
		        Integer[] dimensions = getImageDisplayDimensions(currentFile.getAbsolutePath(),getTypeRequestParameter());
				contentItems.append("<token width='").append(dimensions[0]).append("' height='").append(dimensions[1]).append("' timeStamp='").append(DateUtil.timeStamp()).append("'>").append(HttpRequestUtil.getFileResourceToken(currentFile.getAbsolutePath())).append("</token>");
			}
		}

		return generateXMLreturnString(contentItems);
	}
	private String JSON_getExistingImages() throws Exception {
		// Get list of generated raw images
		File workingImageDir = new File(getOperatingPath());
		File[] imageFiles = workingImageDir.listFiles(new FileListFilter(IMAGE_SCALED_PREFIX,IMAGE_EXT));
		
		JSONObject returnObj = new JSONObject();
		JSONArray imagesArray = new JSONArray();
		if (imageFiles != null && imageFiles.length != 0) {
			for (File currentFile: imageFiles) {
				JSONObject currentImageObj = new JSONObject();
				Integer[] dimensions = getImageDisplayDimensions(currentFile.getAbsolutePath(),getTypeRequestParameter());
				currentImageObj.put("width", dimensions[0]);
				currentImageObj.put("height", dimensions[1]);
				currentImageObj.put("path", HttpRequestUtil.getFileResourceToken(currentFile.getAbsolutePath()));
				imagesArray.put(currentImageObj);
			}
		}
		returnObj.put("sandbox_images", imagesArray);

		return returnObj.toString();
	}

	private String XML_selectImageForUse() throws Exception {
		String imagePath = HttpRequestUtil.getPathFromFileResourceToken(getRequest());

		String sectionId = ServletRequestUtils.getStringParameter(getRequest(), PARAM_SECTION);
		
		File selectedImageFile = new File(imagePath);
		File flaggedImageFile = new File(getOperatingPath()+File.separator+IMAGE_SELETEC_PREFIX+sectionId+"."+IMAGE_EXT);
		copyFile(selectedImageFile,flaggedImageFile);

        StringBuffer contentItems = new StringBuffer();
        Integer[] dimensions = getImageDisplayDimensions(flaggedImageFile.getAbsolutePath(),getTypeRequestParameter());
        contentItems.append("<token width='").append(dimensions[0]).append("' height='").append(dimensions[1]).append("' sectionId='").append(sectionId).append("' timeStamp='").append(DateUtil.timeStamp()).append("'>").append(HttpRequestUtil.getFileResourceToken(flaggedImageFile.getAbsolutePath())).append("</token>");
		
		return generateXMLreturnString(contentItems);
	}
	private String JSON_selectImageForUse() throws Exception {
		String imagePath = HttpRequestUtil.getPathFromFileResourceToken(getRequest());
		String sectionId = ServletRequestUtils.getStringParameter(getRequest(), PARAM_SECTION);
		
		File selectedImageFile = new File(imagePath);
		String selectedFilename = selectedImageFile.getName();
		long databaseFileId = -1L;
		if ( selectedFilename.indexOf("_DBF") != -1 ) {
			String[] filenameParts = selectedFilename.split("_DBF");
			databaseFileId = Long.valueOf(filenameParts[1].split("[.]")[0]);
		}
		
		File flaggedImageFile = new File(getOperatingPath()+File.separator+IMAGE_SELETEC_PREFIX+sectionId+
										( databaseFileId > 0 ? "_DBF" + databaseFileId : "") +
										"."+IMAGE_EXT);
		copyFile(selectedImageFile,flaggedImageFile);
 
		JSONObject returnObj = new JSONObject();
		Integer[] dimensions = getImageDisplayDimensions(flaggedImageFile.getAbsolutePath(),getTypeRequestParameter());
		returnObj.put("width", dimensions[0]);
		returnObj.put("height", dimensions[1]);
		returnObj.put("sectionId", sectionId);
		returnObj.put("path", HttpRequestUtil.getFileResourceToken(flaggedImageFile.getAbsolutePath()));

		return returnObj.toString();
	}
	
	private String JSON_removeImage() throws Exception {
		String imagePath = HttpRequestUtil.getPathFromFileResourceToken(getRequest());

		File imageFile = new File(imagePath);
		
		JSONObject returnObj = new JSONObject();
		if ( imageFile.exists() ) {
			imageFile.delete();
			returnObj.put("success", true);
		} else {
			returnObj.put("error", true);
			returnObj.put("message", "Could not delete background image: File does not exist");
		}

		return returnObj.toString();
	}

	private String generateXMLreturnString(StringBuffer contentItems) {
		StringBuilder sb = new StringBuilder();
		sb.append("<?xml version='1.0'?><content>").append(contentItems).append("</content>");
		return sb.toString();
	}

	private String[] generatePDFimages(String pdfFile, int pageRangeStart, int pageRangeEnd) {

		String[] filenames 				= new String[0];

		try {
			
			Long databaseFileId				= null;

			File file = new File(pdfFile);
		    FileInputStream input = new FileInputStream(file);
		    MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "application/pdf", IOUtils.toByteArray(input));
			
			ServiceExecutionContext context = CreateOrUpdateDatabaseFileService.createContextForCreate(multipartFile, SandboxFile.TYPE_SECTION_BACKGROUND_IMG);
			Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateDatabaseFileService.SERVICE_NAME, CreateOrUpdateDatabaseFileService.class);
			service.execute(context);
			ServiceResponse serviceResponse = context.getResponse();
			if ( !serviceResponse.isSuccessful() ) {
				StringBuilder sb = new StringBuilder();
				sb.append(CreateOrUpdateSandboxFileService.SERVICE_NAME);
				sb.append(" service call is not successful in AsyncUploadedImageEventController");
				log.error(sb.toString());
			} else {

				databaseFileId = (Long) serviceResponse.getResultValueBean();
				
		        PdfDecoderServer decode_pdf = new PdfDecoderServer(true);
		        FontMappings.setFontReplacements();
		        FontMappings.addFontFile("ZapfDingbats.ttf", ApplicationUtil.getRootPath() + "includes/fonts/pdf-embedded/");
		        String outputFilePathPrefix = getOperatingPath() + File.separator;

		        try {
		            decode_pdf.openPdfFile(pdfFile); //file
		            decode_pdf.setExtractionMode(0, 1f); //do not save images

		            BufferedImage img = null;

		            Map<Integer,Object> mapValues = new HashMap<>();
		            mapValues.put(JPedalSettings.EXTRACT_AT_BEST_QUALITY_MAXSCALING, 2);
		            mapValues.put(JPedalSettings.EXTRACT_AT_PAGE_SIZE, new String[]{"2000","1600"});
		            mapValues.put(JPedalSettings.PAGE_SIZE_OVERRIDES_IMAGE, Boolean.TRUE);
		            PdfDecoderServer.modifyJPedalParameters(mapValues);

		    		if (pageRangeStart == -1)
		    			pageRangeStart = 1;
		    		if (pageRangeEnd == -1)
		    			pageRangeEnd = decode_pdf.getPageCount();
		    		
					for ( int i = pageRangeStart; i < pageRangeEnd + 1; i++ ) {
						img = decode_pdf.getPageAsHiRes(i);
						ImageIO.write(img, IMAGE_EXT, new File(outputFilePathPrefix+PDF_IMAGE_RAW_PREFIX+i+"."+IMAGE_EXT));
					}

		            /**close the pdf file*/
		            decode_pdf.closePdfFile();

		        } catch (IOException e) {
					log.error("Error reading PDF File (IO Exception) - "+e.getMessage(),e);
					decode_pdf.closePdfFile();
				} catch (PdfException e) {
		            log.error("Error: " + e.getMessage());
		            e.printStackTrace();
		        }
				
				// Get list of generated raw images
				File workingImageDir = new File(getOperatingPath()+File.separator);
				File[] imageFiles = workingImageDir.listFiles(new FileListFilter(PDF_IMAGE_RAW_PREFIX,IMAGE_EXT));
				Arrays.sort(imageFiles, new Comparator<>() {
                    public int compare(File f1, File f2) {
                        return Long.valueOf(f1.lastModified()).compareTo(f2.lastModified());
                    }
                });
				List<Long> databaseFileIds = new ArrayList<>();
				for ( int i=0; i < imageFiles.length; i++ )
					databaseFileIds.add(databaseFileId);
				
				filenames = scaleRawImages(imageFiles, databaseFileIds);

			}
			
		} catch (IOException e1) {
            log.error("Error: " + e1.getMessage());
            e1.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}


		return filenames;

	}	
	
	private String[] scaleRawImages(File[] imageFiles) throws Exception {
		return scaleRawImages(imageFiles, new ArrayList<>());
	}
	private String[] scaleRawImages(File[] imageFiles, List<Long> databaseFileIds) throws Exception {
		String[] fileArray = new String[imageFiles.length];
		String filename;
		
		// Scale raw image, save scaled images, delete raw images
		int fileIndex = 0;
		for (int i=0; i<imageFiles.length; i++) {
			File currentImage = imageFiles[i];
			
			boolean fileExists = false;
			do {
				File targetScaledFilename = new File(getOperatingPath() + File.separator + IMAGE_SCALED_PREFIX + (fileIndex+1) + 
														(!databaseFileIds.isEmpty() && databaseFileIds.get(i) > 0 ? "_DBF" + databaseFileIds.get(i) : "") +
														"." + IMAGE_EXT );
				if ( targetScaledFilename.exists() ) {
					fileIndex++;
					fileExists = true;
				} else {
					fileExists = false;
				}
			} while (fileExists);
			
			filename = IMAGE_SCALED_PREFIX + (fileIndex+1) + 
						(!databaseFileIds.isEmpty() && databaseFileIds.get(i) > 0 ? "_DBF" + databaseFileIds.get(i) : "") +
						"." + IMAGE_EXT;
			fileArray[i] = getOperatingPath() + File.separator + filename;
			CreateThumbnail currentThumb = new CreateThumbnail(currentImage.getAbsolutePath());
			currentThumb.getThumbnail(resolveScalingWidth(currentImage, getTypeRequestParameter()), CreateThumbnail.HORIZONTAL);
			currentThumb.saveThumbnail(new File(fileArray[i]), IMAGE_EXT);
			currentImage.delete();
		}
		
		return fileArray;
	}
	
	private static int resolveScalingWidth(File imageFile, String imageType) {
		boolean scaleImageForInserts = imageType.equals(TYPE_INSERT_IMAGE);
	
		// For Insert Images: Actual display size || For Section Images: Larger for future image uses
        CreateThumbnail image = new CreateThumbnail(imageFile.getAbsolutePath());
		int imgHeight = image.getImageHeight();
        int imgWidth = image.getImageWidth();
        
        int width;
        if (imgHeight > imgWidth)
        	width = scaleImageForInserts ? IMAGE_DIMENSION_SHORT_INSERTS : IMAGE_DIMENSION_SHORT_SECTIONS;
        else
        	width = scaleImageForInserts ? IMAGE_DIMENSION_LONG_INSERTS : IMAGE_DIMENSION_LONG_SECTIONS;

		return width;
	}
	
	private static Integer[] getImageDisplayDimensions (String imageFilePath, String imageType) {
		Integer[] widthHeightArray = new Integer[2];
		
		boolean scaleImageForInserts = imageType.equals(TYPE_INSERT_IMAGE);

        CreateThumbnail image = new CreateThumbnail(imageFilePath);
		int imgHeight = image.getImageHeight();
        int imgWidth = image.getImageWidth();

        if (imgHeight > imgWidth) { // Portrait
        	if (scaleImageForInserts) { // Inserts
        		widthHeightArray[0] = IMAGE_DIMENSION_SHORT_INSERTS;
        		widthHeightArray[1] = IMAGE_DIMENSION_LONG_INSERTS;
        	} else {					// Sections
        		widthHeightArray[0] = DocumentTag.SECTION_WIDTH_DEFAULT;
        		widthHeightArray[1] = DocumentTag.SECTION_HEIGHT_DEFAULT;
        	}
        } else { 					// Landscape
        	if (scaleImageForInserts) { // Inserts
        		widthHeightArray[0] = IMAGE_DIMENSION_LONG_INSERTS;
        		widthHeightArray[1] = IMAGE_DIMENSION_SHORT_INSERTS;
        	} else {					// Sections
        		widthHeightArray[0] = DocumentTag.SECTION_HEIGHT_DEFAULT;
        		widthHeightArray[1] = DocumentTag.SECTION_WIDTH_DEFAULT;
        	}
        }
		
		return widthHeightArray;
	}

	private File initOperatingFile(MultipartFile pdfFile) throws Exception {
		File operatingPDF = new File(getOperatingPath()+File.separator+"operatingPDF.pdf");
		try {
			if (!operatingPDF.exists())
				operatingPDF.createNewFile();
			pdfFile.transferTo(operatingPDF);
		} catch (IOException e) {
			log.error("Error - Unable to init PDF file for conversion: "+e.getMessage(),e);
		}
		return operatingPDF;
	}
	
	private void deleteWorkspaceImages() throws Exception {
		File operatingPath = new File(getOperatingPath());
		
		// Delete existing files
		File[] existingFiles = operatingPath.listFiles();
		for (File currentFile: existingFiles)
			if (currentFile.getAbsoluteFile().toString().indexOf(IMAGE_SCALED_PREFIX) != -1)
				currentFile.delete();
	}

	public String getSectionImagesWorkingDirectory() {
		if (UserUtil.getPrincipalUser()==null) {
			return null;
		}		
		String imageFileRoot = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir);
		return imageFileRoot + File.separator + WORK_DIR_SECTION_IMAGES + File.separator + Tenant.getPrimary().getId() + File.separator + (getDocumentId() != null && getDocumentId() > 0 ? getDocumentId() : "global" );
	}
	
	public static String getInsertImagesWorkingDirectory() {
		if (UserUtil.getPrincipalUser()==null) {
			return null;
		}
		long userId = UserUtil.getPrincipalUserId();
		String imageFileRoot = ApplicationUtil.getProperty(SystemPropertyKeys.Folder.KEY_ImagesDir);
		return imageFileRoot+File.separator+WORK_DIR_INSERT_IMAGES+File.separator + Tenant.getPrimary().getId() + File.separator+userId;
	}
	
	private String getOperatingPath() throws Exception {
		String operatingPath;

		if (UserUtil.getPrincipalUser() == null) {
			throw new Exception("User context is required com.prinova.messagepoint.controller.AsyncUploadedImageEventController.getOperatingPath, principal user context is null");
		}

		if (getTypeRequestParameter().equals(TYPE_INSERT_IMAGE))
			operatingPath = getInsertImagesWorkingDirectory();
		else
			operatingPath = getSectionImagesWorkingDirectory();

		return operatingPath;
	}

	private static void copyFile(File sourceFile, File destinationFile){
	    try{
	      InputStream in = new FileInputStream(sourceFile);

	      //For Overwrite the file.
	      OutputStream out = new FileOutputStream(destinationFile);

	      byte[] buf = new byte[1024];
	      int len;
	      while ((len = in.read(buf)) > 0){
	        out.write(buf, 0, len);
	      }
	      in.close();
	      out.close();
	    }
	    catch(FileNotFoundException ex){
	    	log.error(ex.getMessage() + " in the specified directory.",ex);
	    }
	    catch(IOException e){
	    	log.error(e.getMessage(),e);   
	    }
	}

	private String getTypeRequestParameter() {
		return ServletRequestUtils.getStringParameter(getRequest(), PARAM_TYPE, TYPE_NO_TYPE);
	}
	
	private String getActionRequestParameter() {
		return ServletRequestUtils.getStringParameter(getRequest(), PARAM_ACTION, null);
	}
	
	public HttpServletRequest getRequest() {
		return request;
	}
	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public Long getDocumentId() {
		return documentId;
	}
	public void setDocumentId(Long documentId) {
		this.documentId = documentId;
	}

}

class FileListFilter implements FilenameFilter {
	  private String name; 

	  private String extension; 

	  public FileListFilter(String name, String extension) {
	    this.name = name;
	    this.extension = extension;
	  }

	  public boolean accept(File directory, String filename) {
	    boolean fileOK = true;

	    if (name != null) {
	      fileOK &= filename.startsWith(name);
	    }

	    if (extension != null) {
	      fileOK &= filename.endsWith('.' + extension);
	    }
	    return fileOK;
	  }
	}
