package com.prinova.messagepoint.controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.Document;
import com.prinova.messagepoint.model.TouchpointSelection;
import com.prinova.messagepoint.model.message.content.SelectableContentUtil;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.TouchpointSelectionUtil;
import com.prinova.messagepoint.util.UserUtil;

public class AsyncSelectionTreeController implements Controller {
	
	public static final String REQ_PARAM_CONTENT_SELECTION_ID_PARAM 	= "contentSelectionId";
	public static final String REQ_PARAM_SELECTION_ID_PARAM 			= "selectionId";
	public static final String REQ_PARAM_PARENT_SELECTED_ID_PARAM 		= "parentSelectedId";	
	public static final String REQ_PARAM_VIEW_ID 						= "statusViewId";
	public static final String REQ_PARAM_PARTIAL 						= "partialReq";
	public static final String REQ_PARAM_GET_PARENTS 					= "getParents";
	
	public static final int VIEW_ACTIVE = 2;	

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		response.setContentType("application/json");
		
		ServletOutputStream out = response.getOutputStream();

		boolean isStatusViewActive 	= getStatusViewIdParam(request) == VIEW_ACTIVE;
		long contentSelectionId 	= ServletRequestUtils.getLongParameter(request, REQ_PARAM_CONTENT_SELECTION_ID_PARAM, -1);
		long selectionId 			= ServletRequestUtils.getLongParameter(request, REQ_PARAM_SELECTION_ID_PARAM, -1);
		long parentSelectedId 		= ServletRequestUtils.getLongParameter(request, REQ_PARAM_PARENT_SELECTED_ID_PARAM, -1);		
		boolean partialRequest 		= ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_PARTIAL, false);
		boolean getParents 			= ServletRequestUtils.getBooleanParameter(request, REQ_PARAM_GET_PARENTS, false);
		Document document;
		
		if (selectionId != -1) {
			TouchpointSelection selection = TouchpointSelection.findById(selectionId);
			document = selection.getDocument();
		} else {
			TouchpointContentObjectContentSelection contentSelection = TouchpointContentObjectContentSelection.findById(contentSelectionId);
			document = contentSelection.getTouchpointSelection().getDocument();
			if (document == null) {
				ContentObjectAssociation contentAssoc = ContentObjectAssociation.findById(contentSelectionId);
				document = TouchpointSelection.findByPgTreeNodeId(contentAssoc.getPGTreeNode().getId()).getDocument();
			}
		}
		
		User requestor = UserUtil.getPrincipalUser();
		
		JSONObject obj = new JSONObject();

		obj.put("result", "COMPLETE");
		if( !partialRequest ){
			if (selectionId != -1)
				obj.put("TPCSNavTree", TouchpointSelectionUtil.getTouchpointSelectionTreeAsHTML(selectionId, document, requestor));
			else
				obj.put("TPCSNavTree", SelectableContentUtil.getTouchpointSelectionTreeAsHTML(contentSelectionId, document, isStatusViewActive, requestor));
		} else {
			if( getParents ) {
				if (selectionId != -1)
					obj.put("TPCSNodeParents", TouchpointSelectionUtil.getTouchpointSelectionAnsestorPath(selectionId, document));
				else
					obj.put("TPCSNodeParents", SelectableContentUtil.getTouchpointSelectionAnsestorPath(contentSelectionId, document, isStatusViewActive));
			} else {
				if (selectionId != -1)
					obj.put("TPCSNavTreeNodes", TouchpointSelectionUtil.getTouchpointSelectionHTMLChildNodes(selectionId, parentSelectedId, document, requestor) );
				else
					obj.put("TPCSNavTreeNodes", SelectableContentUtil.getTouchpointSelectionHTMLChildNodes(contentSelectionId, parentSelectedId, document, isStatusViewActive, requestor) );
			}
		}
		out.write(obj.toString().getBytes());
		out.flush();

		return null;
	}
	
	private int getStatusViewIdParam(HttpServletRequest request) {
		int viewIdParam = ServletRequestUtils.getIntParameter(request, REQ_PARAM_VIEW_ID, -1);
		if ( viewIdParam > 0 )
			return viewIdParam;
		else
			return UserUtil.getCurrentSelectionStatusContext().getId();
	}
}