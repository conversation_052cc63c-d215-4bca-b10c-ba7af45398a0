package com.prinova.messagepoint.controller.dictionary;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.util.HibernateUtil;

public class DictionaryListDetailController implements Controller{

	private String formView;

	public ModelAndView handleRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
		
		Map<String, Object> params = new HashMap<>();
		long dictionaryId = ServletRequestUtils.getLongParameter(request, DictionaryListController.REQ_PARM_DICTIONARY_ID, -1L);
		
		Dictionary dictionary = HibernateUtil.getManager().getObject(Dictionary.class, dictionaryId);
		params.put("dictionary", dictionary);

		return new ModelAndView(getFormView(), params);
	}

	public String getFormView() {
		return formView;
	}
	public void setFormView(String formView) {
		this.formView = formView;
	}
}
