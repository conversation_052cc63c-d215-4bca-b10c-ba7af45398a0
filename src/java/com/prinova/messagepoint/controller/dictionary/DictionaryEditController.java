package com.prinova.messagepoint.controller.dictionary;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dictionary.CreateOrUpdateDictionaryService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.DateUtil;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class DictionaryEditController extends MessagepointController {
	private static final Log log = LogUtil.getLog(DictionaryEditController.class);
	
	public static final String REQ_PARAM_DICTIONARY_ID 		= "dictionaryId";
	public static final String REQ_PARAM_SAVE_SUCCESS		= "saveSuccess";
	
	public static final String FORM_SUBMIT_TYPE_SUBMIT 		= "submit";
	
	
	@Override
	protected Map<String, Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();

    		referenceData.put("sourceEditPerm", UserUtil.isAllowed(UserUtil.getPrincipalUser(), Permission.ROLE_CONTENT_SOURCE_EDIT));
    		referenceData.put("availableLanguages", Dictionary.getAvailableLanguages());
    		
		return referenceData;
	}

	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true));
		binder.registerCustomEditor(Date.class, DateUtil.getCustomDateEditor());
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
	}

	protected DictionaryEditWrapper formBackingObject(HttpServletRequest request) throws Exception {
		long dictionaryId = ServletRequestUtils.getLongParameter(request, REQ_PARAM_DICTIONARY_ID, -1);
		
		Dictionary dictionary = Dictionary.findById(dictionaryId);
		DictionaryEditWrapper wrapper = null;
		if ( dictionary != null ){	// Existing dictionary
			wrapper = new DictionaryEditWrapper(dictionary);
		}
		
		return wrapper;
	}

	@Override
	protected ModelAndView showForm(HttpServletRequest request, HttpServletResponse response, BindException errors)
			throws Exception {
		return super.showForm(request, response, errors);
	}

	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object commandObj, BindException errors) throws Exception {
		DictionaryEditWrapper wrapper = (DictionaryEditWrapper) commandObj;
		User requestor = UserUtil.getPrincipalUser();
		
		Dictionary dictionary = wrapper.getDictionary();

    		ServiceExecutionContext context = CreateOrUpdateDictionaryService.createContextForUpdateDictionary(dictionary, wrapper.getDictionaryName(), wrapper.getDictionaryLang(), wrapper.getDictionaryLocale(), requestor);
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateDictionaryService.SERVICE_NAME, CreateOrUpdateDictionaryService.class);
		service.execute(context);
		ServiceResponse serviceResponse = context.getResponse();
		if (serviceResponse.isSuccessful()) {	
			Map<String, Object> parms = new HashMap<>();
			parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
			return new ModelAndView( new RedirectView("../frameClose.jsp"), parms );
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(CreateOrUpdateDictionaryService.SERVICE_NAME);
			sb.append(" service call is not successful ");
			sb.append(" in ").append(this.getClass().getName());
			sb.append(" Dictionary was not saved. ");
			log.error(sb.toString());
			ServiceResponseConverter.convertToSpringErrors(context.getResponse(), errors);
			return super.showForm(request, response, errors);
		}
	}
	
 	protected boolean isFormSubmission(HttpServletRequest request) {
		String submitType = ServletRequestUtils.getStringParameter(request, "submittype", "");
		return FORM_SUBMIT_TYPE_SUBMIT.equals(submitType);
	}
}
