package com.prinova.messagepoint.controller.dictionary;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindException; 
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.file.SourceEditorController;
import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.util.NestedPageUtils;
import com.prinova.messagepoint.util.UserUtil;

public class DictionarySourceEditorController extends SourceEditorController {
	public static final String PARAM_DICTIONARY_ID 		= "dictionaryId";
	
	@Override
    protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors) throws Exception {
		super.onSubmit(request, response, command, errors);
		
		// Reload the SpellChecker if the dictionary is enabled
		long dictionaryId = ServletRequestUtils.getLongParameter(request, DictionaryEditController.REQ_PARAM_DICTIONARY_ID, -1);
		Dictionary dictionary = Dictionary.findById(dictionaryId);
		if(dictionary.getEnabled()){
			long userId = UserUtil.getPrincipalUserId();
			dictionary.reload(userId, null);
		}
		
		Map<String, Object> parms = new HashMap<>();
		parms.put(NestedPageUtils.RESPONSE_SAVE_SUCCESS, true);
		return new ModelAndView(new RedirectView("../frameClose.jsp"),parms);	
	}
}
