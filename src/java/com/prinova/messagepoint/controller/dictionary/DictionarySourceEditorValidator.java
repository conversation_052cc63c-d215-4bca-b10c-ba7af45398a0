package com.prinova.messagepoint.controller.dictionary;

import org.springframework.validation.Errors;

import com.prinova.messagepoint.controller.file.SourceEditorController.SourceCommand;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class DictionarySourceEditorValidator extends MessagepointInputValidator {
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		SourceCommand command = (SourceCommand)commandObj;
	
	}
}
