package com.prinova.messagepoint.controller.dictionary;

import java.io.Serializable;
import com.prinova.messagepoint.model.dictionary.Dictionary;

public class DictionaryEditWrapper implements Serializable{
	private static final long serialVersionUID = 5558761455291603577L;
	
	private Dictionary 					dictionary;
	private String							dictionaryName;
	private String							dictionaryLang;
	private String 							dictionaryLocale;
	
	public DictionaryEditWrapper(Dictionary dictionary){
		this.dictionary = dictionary;
		this.dictionaryName = dictionary.getName();
		this.dictionaryLang = dictionary.getLangCode();
		this.dictionaryLocale = dictionary.getLocaleCode();
	}

	public Dictionary getDictionary() {
		return dictionary;
	}

	public void setDictionary(Dictionary dictionary) {
		this.dictionary = dictionary;
	}

	public String getDictionaryName() {
		return dictionaryName;
	}

	public void setDictionaryName(String dictionaryName) {
		this.dictionaryName = dictionaryName;
	}

	public String getDictionaryLang() {
		return dictionaryLang;
	}

	public void setDictionaryLang(String dictionaryLang) {
		this.dictionaryLang = dictionaryLang;
	}

	public String getDictionaryLocale() {
		return dictionaryLocale;
	}

	public void setDictionaryLocale(String dictionaryLocale) {
		this.dictionaryLocale = dictionaryLocale;
	}
}
