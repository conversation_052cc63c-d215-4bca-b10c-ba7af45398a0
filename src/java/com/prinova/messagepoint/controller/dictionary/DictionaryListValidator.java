package com.prinova.messagepoint.controller.dictionary;

import org.apache.commons.lang3.LocaleUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class DictionaryListValidator extends MessagepointInputValidator{
	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		DictionaryListWrapper command = (DictionaryListWrapper)commandObj;
			
		int action = -1;
		if (command.getActionValue() != null && !command.getActionValue().trim().isEmpty()) {
			action = Integer.valueOf(command.getActionValue()).intValue();
		}
		
		if ( action == DictionaryListController.ACTION_DELETE_DICTIONARY ) {
			
		}else if( action == DictionaryListController.ACTION_ADD_DICTIONARY){
			String dicName = command.getnDictionaryName();
			String dicLanguage = command.getnDictionaryLang();
			String dicLocale = command.getnDictionaryLocale().toUpperCase();
			
			if(dicName == null || dicName.isEmpty()){
				errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.name")}, null);
				return;
			}else{
				boolean foundWithSameName = Dictionary.findAll().stream().anyMatch(d->d.getName().equals(dicName));
				if(foundWithSameName){
					errors.reject("error.message.namemustbeunique");
					return;
				}
			}
			if(dicLanguage == null || dicLanguage.isEmpty()){
				errors.reject("error.input.mandatory", new String[]{ApplicationUtil.getMessage("page.label.language")}, null);
				return;
			}
			if(dicLocale != null && !dicLocale.isEmpty()){
				String localeStr = dicLanguage + "_" + dicLocale;
				try{
					LocaleUtils.toLocale(localeStr);
				}catch(IllegalArgumentException iae){
					errors.reject("error.input.invalid.locale", new String[]{localeStr}, null);
					return;
				}
			}
		}
	}
}
