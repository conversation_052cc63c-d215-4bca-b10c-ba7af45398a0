package com.prinova.messagepoint.controller.dictionary;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import com.prinova.messagepoint.controller.MessagepointController;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.controller.IdCustomEditor;
import com.prinova.messagepoint.controller.ServiceResponseConverter;
import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.model.dictionary.DictionaryType;
import com.prinova.messagepoint.model.security.Permission;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.dictionary.BulkDeleteDictionaryService;
import com.prinova.messagepoint.platform.services.dictionary.BulkUpdateDictionaryService;
import com.prinova.messagepoint.platform.services.dictionary.CreateOrUpdateDictionaryService;
import com.prinova.messagepoint.security.StringXSSEditor;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;

public class DictionaryListController extends MessagepointController {
	private static final Log log = LogUtil.getLog(DictionaryListController.class);

	public static final String REQ_PARM_ACTION 					= "action";
	public static final String REQ_PARM_DICTIONARY_ID 		= "dictionaryId";
	
	public static final int ACTION_EDIT_DICTIONARY 				= 1;
	public static final int ACTION_DELETE_DICTIONARY		= 2;
	public static final int ACTION_ADD_DICTIONARY				= 3;
	public static final int ACTION_ENABLE_DICTIONARY		= 4;
	public static final int ACTION_DISABLE_DICTIONARY		= 5;
	public static final int ACTION_ADD_WORD							= 6;
	
	@Override
	protected Map<String,Object> referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
		Map<String, Object> referenceData = new HashMap<>();
		
		referenceData.put("dictionaryTypeFilterTypes", DictionaryType.listAll());
		
		List<DictionaryType> dictionaryTypesForCreation = DictionaryType.listAllForCreation();
		boolean dictionaryEditPermission				= UserUtil.isPermissionGranted(Permission.ROLE_DICTIONARY_EDIT);
		if(!dictionaryEditPermission){
			dictionaryTypesForCreation.remove(new DictionaryType(DictionaryType.ID_TYPE_USER));
		}
		referenceData.put("dictionaryTypesForCreation", dictionaryTypesForCreation);
		
		referenceData.put("availableLanguages", Dictionary.getAvailableLanguages());
		
		return referenceData;
	}
	
	@Override
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
		binder.registerCustomEditor(String.class, new StringXSSEditor());
		binder.registerCustomEditor(User.class, new IdCustomEditor<>(User.class));
	}
	
	@Override
	protected Object formBackingObject(HttpServletRequest request) throws Exception {
		return new DictionaryListWrapper();
	}
	
	@Override
	protected ModelAndView onSubmit(HttpServletRequest request, HttpServletResponse response, Object command, BindException errors)
			throws Exception {
		int action = ServletRequestUtils.getIntParameter(request, REQ_PARM_ACTION, -1);
		DictionaryListWrapper c = (DictionaryListWrapper)command;
		
		User principal = UserUtil.getPrincipalUser();
		User requestor = User.findById(principal.getId());
		
		switch(action){
			case ACTION_DELETE_DICTIONARY:{
				List<Dictionary> list = c.getSelectedList(); 
				ServiceExecutionContext context = BulkDeleteDictionaryService.createContext(list);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkDeleteDictionaryService.SERVICE_NAME,
						BulkDeleteDictionaryService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkDeleteDictionaryService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(Dictionary dictionary: list){
						sb.append(" id ").append(dictionary.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
			case ACTION_ADD_DICTIONARY:{
				ServiceExecutionContext context = CreateOrUpdateDictionaryService.createContextForCreateDictionary(c.getnDictionaryName(), c.getnDictionaryLang(), c.getnDictionaryLocale(), c.getnDictionaryType(), requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateDictionaryService.SERVICE_NAME,
						CreateOrUpdateDictionaryService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(CreateOrUpdateDictionaryService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
			case ACTION_ENABLE_DICTIONARY:{
				List<Dictionary> list = c.getSelectedList(); 
				ServiceExecutionContext context = BulkUpdateDictionaryService.createContextForEnable(list, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateDictionaryService.SERVICE_NAME,
						BulkUpdateDictionaryService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateDictionaryService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(Dictionary dictionary: list){
						sb.append(" id ").append(dictionary.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
			case ACTION_DISABLE_DICTIONARY:{
				List<Dictionary> list = c.getSelectedList(); 
				ServiceExecutionContext context = BulkUpdateDictionaryService.createContextForDisable(list, requestor);
				Service service = MessagepointServiceFactory.getInstance().lookupService(BulkUpdateDictionaryService.SERVICE_NAME,
						BulkUpdateDictionaryService.class);				
				service.execute(context);
				ServiceResponse serviceResponse = context.getResponse();
				if (!serviceResponse.isSuccessful()) {
					StringBuilder sb = new StringBuilder();
					sb.append(BulkUpdateDictionaryService.SERVICE_NAME);
					sb.append(" service call is not successful ");
					sb.append(" in ").append(this.getClass().getName());
					for(Dictionary dictionary: list){
						sb.append(" id ").append(dictionary.getId());
					}
					sb.append(" requestor=").append(requestor.getUsername());
					log.error(sb.toString());
					ServiceResponseConverter.convertToSpringErrors(serviceResponse, errors);
					return showForm(request, response, errors);
				} else {
					return new ModelAndView(new RedirectView(getSuccessView()));
				}
			}
		}
		return null;
	}
}
