package com.prinova.messagepoint.controller.dictionary;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.model.dictionary.Dictionary;
import com.prinova.messagepoint.util.HibernateUtil;

public class DictionaryListWrapper implements Serializable{
	private static final long serialVersionUID = 1749744863987128589L;
	
	private List<Long>				selectedIds;
	private String 						actionValue;
	private String 						userNote;
	
	// For new dictionary creation
	private String						nDictionaryName;
	private String 						nDictionaryLang;
	private String						nDictionaryLocale;
	private Integer					nDictionaryType;
	
	public DictionaryListWrapper() {
		super();
		this.selectedIds = new ArrayList<>();
	}
	
	public List<Long> getSelectedIds() {
		return selectedIds;
	}

	public void setSelectedIds(List<Long> selectedIds) {
		this.selectedIds = selectedIds;
	}

	public String getActionValue() {
		return actionValue;
	}

	public void setActionValue(String actionValue) {
		this.actionValue = actionValue;
	}
	
	public String getUserNote() {
		return userNote;
	}

	public void setUserNote(String userNote) {
		this.userNote = userNote;
	}
	
	public String getnDictionaryName() {
		return nDictionaryName;
	}

	public void setnDictionaryName(String nDictionaryName) {
		this.nDictionaryName = nDictionaryName;
	}

	public String getnDictionaryLang() {
		return nDictionaryLang;
	}

	public void setnDictionaryLang(String nDictioanryLang) {
		this.nDictionaryLang = nDictioanryLang;
	}

	public String getnDictionaryLocale() {
		return nDictionaryLocale;
	}

	public void setnDictionaryLocale(String nDictionaryLocale) {
		this.nDictionaryLocale = nDictionaryLocale;
	}

	public Integer getnDictionaryType() {
		return nDictionaryType;
	}

	public void setnDictionaryType(Integer nDictionaryType) {
		this.nDictionaryType = nDictionaryType;
	}

	public List<Dictionary> getSelectedList(){
		List<Dictionary> selectedList = new ArrayList<>();
		for(Long selectedId : this.selectedIds){
			selectedList.add(HibernateUtil.getManager().getObject(Dictionary.class, selectedId));
		}
		return selectedList;
	}	
}
