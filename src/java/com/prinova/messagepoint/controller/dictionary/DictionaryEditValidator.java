package com.prinova.messagepoint.controller.dictionary;

import org.apache.commons.lang3.LocaleUtils;
import org.springframework.validation.Errors;

import com.prinova.messagepoint.validator.MessagepointInputValidator;

public class DictionaryEditValidator extends MessagepointInputValidator{

	@Override
	public void validateNotGenericInputs(Object commandObj, Errors errors) {
		DictionaryEditWrapper wrapper = (DictionaryEditWrapper)commandObj;
		
		String dicLanguage = wrapper.getDictionaryLang();
		String dicLocale = wrapper.getDictionaryLocale().toUpperCase();
		
		if(dicLocale != null && !dicLocale.isEmpty()){
			String localeStr = dicLanguage + "_" + dicLocale;
			try{
				LocaleUtils.toLocale(localeStr);
			}catch(IllegalArgumentException iae){
				errors.reject("error.input.invalid.locale", new String[]{localeStr}, null);
				return;
			}
		}
	}
}
