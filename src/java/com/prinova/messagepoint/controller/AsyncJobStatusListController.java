package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.job.JobZipFile;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.model.admin.SystemState;
import com.prinova.messagepoint.model.deliveryevent.DeliveryEvent;
import com.prinova.messagepoint.model.util.JobPackerUtil;
import com.prinova.messagepoint.util.HibernateUtil;

import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import java.io.File;
import java.io.IOException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AsyncJobStatusListController implements Controller {
    private static final Log log = LogUtil.getLog(AsyncJobStatusListController.class);

    @Override
    public ModelAndView handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {

        String jobStatusList = ServletRequestUtils.getStringParameter(httpServletRequest, "jobs", StringUtils.EMPTY);

        JSONArray result = new JSONArray();

        if (!jobStatusList.isEmpty()) {
            JSONArray pollList = new JSONArray(jobStatusList);

            for (int x = 0; x < pollList.length(); x++) {
                Long jobId = Long.parseLong(((JSONObject)pollList.get(x)).get("jobid").toString());
                DeliveryEvent event = DeliveryEvent.findByJob(jobId);

                // avoid IllegalArgumentException when refreshing session with null
                if (event == null) {
                    continue;
                }

                HibernateUtil.getManager().getSession().refresh(event);
                long statusId = event.getCurrentStateId();

                JSONObject status = new JSONObject();
                status.put("jobId", jobId);
                status.put("statusId", statusId);
                status.put("status", event.getStatusDisplayString());
                status.put("clientIcon", getClientBundleIconClass(jobId, event));
                status.put("serverIcon", getServerBundleIconClass(jobId, event));
                status.put("diagnosticIcon", getDiagnosticBundleIconClass(jobId, event));

                result.put(status);
            }

            sendJsonResponse(httpServletResponse, result);
        }


        return null;
    }

    private void sendJsonResponse(HttpServletResponse response, Object result) {
        try {
            response.setContentType("application/json");
            ServletOutputStream out = response.getOutputStream();
            out.print(result.toString());
        } catch (IOException ex) {
            log.error("Error processing async request: " + ex.getMessage());
        }
    }

    public static String getClientBundleIconClass(long jobId, DeliveryEvent deliveryEvent) {

        if (deliveryEvent != null) {
            // If delivery event was packed successfully it will have state transition STATUS_TYPE_PACKED - display download icon
            if (deliveryEvent.isStateTransitionExist(SystemState.STATUS_TYPE_PACKED) || deliveryEvent.isCompletedStateExist()) {
                return "downloadBundle fa-cloud-download";
            }

            // If delivery event completed in error state - display warning icon
            if (deliveryEvent.isError()) {
                return getBundleErrorIconClass(jobId, JobZipFile.CLIENT_BUNDLE_ZIP_SUFIX);
            }
        }

        return  "fa-clock";
    }

    public static String getServerBundleIconClass(long jobId, DeliveryEvent deliveryEvent) {

        if (deliveryEvent != null) {
            // Job transitioned into STATUS_TYPE_WAITING_FOR_RESULT - the server bundle was uploaded successfully, display icon to download
            if (deliveryEvent.isStateTransitionExist(SystemState.STATUS_TYPE_WAITING_FOR_RESULT) || deliveryEvent.isCompletedStateExist()) {
                return "downloadBundle fa-cloud-download";
            }
            // If completed with error look if job server bundle exists
            if (deliveryEvent.isError()) {
                return getBundleErrorIconClass(jobId, JobZipFile.SERVER_BUNDLE_ZIP_SUFIX);
            }
        }

        return "fa-clock";
    }

    public static String getDiagnosticBundleIconClass(long jobId, DeliveryEvent deliveryEvent) {

        if (deliveryEvent != null) {
            // Job completed successfully
            if (deliveryEvent.isCompletedSuccessfully()) {
                return "downloadBundle fa-cloud-download";
            }
            // If completed with error or warning check if diagnostic bundle exists
            if (deliveryEvent.isError() || deliveryEvent.isCompletedStateExist()) {
                return getBundleErrorIconClass(jobId, JobZipFile.DIAGNOSTIC_BUNDLE_ZIP_SUFIX);
            }
        }

        // Job still processing, waiting
        return "fa-clock";
    }

    private static String getBundleErrorIconClass(long jobId, String bundleSuffix) {
        String incomingSuccessDir = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_IncomingSuccessDir);
        String branchId = Node.getCurrentBranchName();
        String nodeId = Node.getCurrentNodeName();
        String path = JobPackerUtil.lookForJob(jobId, incomingSuccessDir, branchId, nodeId, bundleSuffix);

        return path != null && new File(path).exists() ? "downloadBundle fa-cloud-download" : "fa-exclamation-triangle";
    }

}
