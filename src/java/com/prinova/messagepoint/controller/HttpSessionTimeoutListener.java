package com.prinova.messagepoint.controller;

import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.model.security.User;

public class HttpSessionTimeoutListener implements HttpSessionListener {

	private static final Log log = LogUtil.getLog(HttpSessionTimeoutListener.class);

	public void sessionCreated(HttpSessionEvent event) {
	}

	public void sessionDestroyed(HttpSessionEvent event) {
		// here need to get principal user
		HttpSession session = event.getSession();
		SecurityContext context = (SecurityContext) session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);

		LogUtil.getLog(HttpSessionTimeoutListener.class).info("sessionDestroyed:" + session.getId() + " csrf: " + session.getAttribute(WebAppSecurity.CSRF_SESSION_KEY));

		if (context != null) {
			// get the authentication object from acegi context...
			Authentication auth = context.getAuthentication();
			if (auth != null) {
				Object authorizedUser = auth.getPrincipal();
				if (authorizedUser instanceof User) {
					if (log.isInfoEnabled())
						log.info("Http Session timeout or User sign out; Username: " + ((UserDetails) authorizedUser).getUsername());
				}
			}
		}
	}


}
