package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.controller.content.ContentVO;
import com.prinova.messagepoint.controller.dashboards.GlobalDashboardNavigationWrapper;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.ParameterGroup;
import com.prinova.messagepoint.model.admin.ParameterGroupTreeNode;
import com.prinova.messagepoint.model.content.ContentAssociationType;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectAssociation;
import com.prinova.messagepoint.model.message.content.TouchpointContentObjectContentSelection;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.content.ContentObjectContentSelectionVO;
import com.prinova.messagepoint.platform.services.utils.CloneHelper;
import com.prinova.messagepoint.tag.VariantTreeTag;
import com.prinova.messagepoint.util.JSONUtils;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;
import com.prinova.messagepoint.util.jstree.*;
import com.prinova.messagepoint.util.jstree.serializers.TouchpointSelectionJsonSerializer;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class AsyncVariantTreeController implements Controller {

    @Override
    public ModelAndView handleRequest(HttpServletRequest httprequestervletRequest, HttpServletResponse httpServletResponse) throws Exception {

        String requestId = ServletRequestUtils.getStringParameter(httprequestervletRequest, "requestId", null);
        String ifNoneMatch = httprequestervletRequest.getHeader("If-None-Match");

        AsyncVariantTreeRequestContext requestContext = new AsyncVariantTreeRequestContext();

        try {
            Object binding = httprequestervletRequest.getSession().getAttribute("Binding-" + requestId);
            requestContext.setDataBinding(binding);

            Object params = httprequestervletRequest.getSession().getAttribute("Parameters-" + requestId);
            JSONObject jsonParams = new JSONObject(String.valueOf(params));

            requestContext.setApplyDocumentContext(JSONUtils.getValueOrDefault(jsonParams,"applyDocumentContext", false));
            requestContext.setTreesCompareContext(JSONUtils.getValueOrDefault(jsonParams, "treesCompareContext", false));
            requestContext.setContentCompareContext(JSONUtils.getValueOrDefault(jsonParams, "contentCompareContext", false));
            requestContext.setExpanded(JSONUtils.getValueOrDefault(jsonParams, "expanded", false));
            requestContext.setShowContentStatus(JSONUtils.getValueOrDefault(jsonParams, "showContentStatus", false));
            requestContext.setShowVersioning(JSONUtils.getValueOrDefault(jsonParams, "showVersioning", false));
            requestContext.setMasterNodeId(JSONUtils.getValueOrDefault(jsonParams, "masterNodeId", -1));
            requestContext.setCompareToObjectId(JSONUtils.getValueOrDefault(jsonParams, "compareToObjectId", StringUtils.EMPTY));
            requestContext.setCompareDataType(JSONUtils.getValueOrDefault(jsonParams, "compareDataType", StringUtils.EMPTY));
            requestContext.setSelectedNodeId(JSONUtils.getValueOrDefault(jsonParams, "selectedNodeId", StringUtils.EMPTY));
            requestContext.setCompareToNodeId(JSONUtils.getValueOrDefault(jsonParams, "compareToNodeId", StringUtils.EMPTY));
            requestContext.setStatusViewId(JSONUtils.getValueOrDefault(jsonParams, "statusViewId", 1));
            requestContext.setSyncCommit(JSONUtils.getValueOrDefault(jsonParams, "syncCommit", false));
        } catch (Exception e) {
            LogUtil.getLog(AsyncVariantTreeController.class).error("Error:", e);
        }

        JSONObject values = new JSONObject();

        values.put("tree", requestContext.generateTreeJsonNodes());
        values.put("masterVariantId", requestContext.getMasterNodeId());

        String respValue = values.toString();
        String etag = DigestUtils.md5Hex(respValue);

        if (ifNoneMatch != null && ifNoneMatch.equals(etag)) {
            httpServletResponse.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
        } else {
            httpServletResponse.setHeader("ETag", etag);
            httpServletResponse.setContentType("application/json");
            httpServletResponse.getOutputStream().write(respValue.getBytes());
        }

        return null;
    }

    public static class AsyncVariantTreeRequestContext {

        private Boolean applyDocumentContext	= false;
        private Boolean treesCompareContext     = false; // Compare two variant trees and show the changes
        private Boolean contentCompareContext   = false;
        private Boolean connectedContext		= false;
        private Boolean expanded;
        private Boolean showContentStatus       = false;
        private Boolean showVersioning			= true;
        private long masterNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
        private Object dataBinding;
        private String compareToObjectId;
        private String cssClass;
        private String id;
        private String onChange;
        private String onContextMenuSelect;
        private String onResize;
        private String selectedNodeId;
        private String style;
        private String compareToNodeId;
        private String compareDataType;
        private long statusViewId;
        private Boolean syncCommit = false;

        private void setStatusViewId(long statusViewId) {
            this.statusViewId = statusViewId;
        }

        private JSONArray generateTreeJsonNodes() throws Exception {

            JSTreeDataBinding serializer;
            ParameterGroup parameterGroup;

            // long startTime = System.currentTimeMillis();
            // long execTime;

            if (getDataBinding() instanceof Branch) {
                serializer = new BranchSelectionJsonSerializer((Branch)getDataBinding());
            } else if (getDataBinding() instanceof ContentObject) {
                ContentObject contentObject = (ContentObject) getDataBinding();
                int dataType = compareDataType == null || compareDataType.isEmpty() ? contentObject.getFocusOnDataType() : Integer.valueOf(compareDataType);
                contentObject.setFocusOnDataType(dataType);

                parameterGroup = ((ContentObject) getDataBinding()).getParameterGroup();

                ParameterGroupTreeWrapper instance;

                // Issue 19734 - Check for nulls to avoid issues with
                // Touchpoints without Data Collections
                if (parameterGroup == null) {
                    // EmptyJsonArraySerializer - New class introduced to fix issue
                    serializer = new EmptyJsonArraySerializer();
                } else {
                    instance = ParameterGroupTreeWrapper.createInstance(contentObject, dataType);
                    HashMap<String, Function<ParameterGroupTreeWrapper, Object>> extendedPropertiesMap = new HashMap<>();

                    /**
                     * Disabled this part because of performance issue JIRA CDD-2433
                     * This implementaion will need to be rewritten
                    if (getShowContentStatus()) {
                        contentObject.setFocusOnDataType(dataType);
                        Map<Long, ContentVO> masterContent = ContentObjectDynamicVariantViewController.getMasterContent((ContentObject) getDataBinding());
                        extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_A_ATTR,
                                pgTreeNodeWrapper -> getContentStatusClassForDynamicContent(((ContentObject) getDataBinding()), masterContent, pgTreeNodeWrapper.getId(), getStatusViewId()) );
                    }
                     **/

                    serializer = new JSTreeJsonSerializer<>(instance, instance.getChildren(), extendedPropertiesMap);
                }
            } else if (getDataBinding() instanceof TouchpointSelectionWrapper
                    || this.getDataBinding() instanceof TouchpointSelection
                    || this.getDataBinding() instanceof TouchpointSelectionVO
                    || this.getDataBinding() instanceof TouchpointContentObjectContentSelection) {
                serializer = getTouchpointSelectionBinding();
            } else if (getDataBinding() instanceof RationalizerApplication) {
                RationalizerApplication rationalizerApp = (RationalizerApplication)getDataBinding();
                serializer = new RationalizerNavigationJsonSerializer(rationalizerApp);
            } else if (getDataBinding() instanceof GlobalDashboardNavigationWrapper) {
                GlobalDashboardNavigationWrapper wrapper = (GlobalDashboardNavigationWrapper)getDataBinding();
                serializer = new GlobalDashboardNavigationJsonSerializer(wrapper);
            } else if (getDataBinding() instanceof FormattingSelection){
                FormattingSelection formattingSelection = (FormattingSelection)getDataBinding();
                serializer = new FormattingSelectionJsonSerializer(formattingSelection);
            } else {
                throw new Exception("Unable to databind VariantTreeTag to: " + this.getDataBinding().getClass().getName());
            }
            if (getDataBinding() instanceof RationalizerApplication) {
                if(StringUtils.isNotEmpty(selectedNodeId)) {
                    ((RationalizerNavigationJsonSerializer) serializer).setSelectedNodePath(selectedNodeId);
                } else {
                    ((RationalizerNavigationJsonSerializer) serializer).setSelectedNodePath("-9");
                }
            } else {
                if (selectedNodeId != null && !selectedNodeId.isEmpty()) {
                    serializer.setSelectedNodeId(Long.parseLong(selectedNodeId));
                }
            }

//            execTime = System.currentTimeMillis() - startTime;
//            LogUtil.getLog(AsyncVariantTreeController.class).error("AsyncVariantTreeController preparation TIME TAKEN: <" + (execTime) + ">");
//            startTime = System.currentTimeMillis();

            JSONArray result = serializer.getTreeStructureJson();

//            execTime = System.currentTimeMillis() - startTime;
//            LogUtil.getLog(AsyncVariantTreeController.class).error("AsyncVariantTreeController serialization TIME TAKEN: <" + (execTime) + ">");

            return result;
        }

        private long getStatusViewId() {
            return statusViewId;
        }

        private JSONObject getContentStatusClassForDynamicContent(Object modelInstance, Map<Long, ContentVO> masterContent, long pgTreeNodeId, long statusViewId) {

            JSONObject result = new JSONObject();

            try {
                ParameterGroupTreeNode pgtn = ParameterGroupTreeNode.findById(pgTreeNodeId);
                Map<Long, ContentVO> selVO;
                ContentObject contentObject = (ContentObject) modelInstance;

                selVO = ContentObjectContentSelectionVO.mapFromContentInstance(pgtn, contentObject, masterContent);

                for (Long localeId : selVO.keySet()) {

                    if (selVO.get(localeId).getAssociationType() == ContentAssociationType.ID_REFERENCES) {
                        result.put("data-contentStatus", "references");
                    }
                    if (pgtn != null && selVO.get(localeId).isReference()) {
                        if (result.isEmpty() || (!result.isEmpty() && !result.get("data-contentStatus").equals("default"))) {
                            result.put("data-contentStatus", "references");
                        }
                    } else {
                        result.put("data-contentStatus", "default");

                        if (pgtn == null) {
                            // Loading Association Type from master since ContentVO is not providing the data
                            List<ContentObjectAssociation> cas = ContentObjectAssociation.findAllByContentObjectAndParameters(
                                    contentObject,
                                    ContentObject.DATA_TYPE_WORKING,
                                    null,
                                    UserUtil.getCurrentLanguageLocaleContext()
                            );
                            if (!cas.isEmpty() && cas.get(0).getTypeId() == ContentAssociationType.ID_SUPPRESSES) {
                                result.put("data-contentStatus", "suppresses");
                            }
                        }
                    }


                    if(((ContentObject)modelInstance).isInTranslationStep()) {
                        if (selVO.get(localeId).isDirty() && selVO.get(localeId).getAssociationType() == ContentAssociationType.ID_OWNS) {
                            result.put("data-contentStatus", "dirty");
                        }
                    }
                }

                if (!result.has("data-contentStatus")) {
                    result.put("data-contentStatus", "default");
                }
            } catch (JSONException e) {
                LogUtil.getLog(VariantTreeTag.class).error(e);
            }

            return result;
        }

        private JSTreeDataBinding getTouchpointSelectionBinding() {
            TouchpointSelection touchpointSelection;
            Document otherDocument = null;
            TouchpointSelection rootSelection;
            TouchpointSelectionWrapper touchpointSelectionWrapper = null;
            boolean isEnabledForVariantWorkflow;
            ContentObject contentObject = null;
            boolean isActive = false;
            boolean includeArchived = false;

            HashMap<String, Function<TouchpointSelection, Object>> extendedPropertiesMap = new HashMap<>();
            Map<ParameterGroupTreeNode, Map<ParameterGroupTreeNode, Object>> compareToPGTNsMapping = new HashMap<>();

            if (dataBinding instanceof TouchpointSelectionWrapper) {
                touchpointSelectionWrapper = ((TouchpointSelectionWrapper) this.dataBinding);
                contentObject = ContentObject.findById(touchpointSelectionWrapper.getContentObjectId());
                int dataType = contentObject.getFocusOnDataType();
                if (getTreesCompareContext()) {
                    long compareFromObjectId = touchpointSelectionWrapper.getContentObjectId();
                    long compareToObjectId = Long.valueOf(getCompareToObjectId());

                    long nodeInstanceId = Long.valueOf(compareToNodeId);
                    Node node = Node.findById(nodeInstanceId);
                    ContentObject compareToMsg = CloneHelper.queryInSchema(node.getSchemaName(), ()->ContentObject.findById(compareToObjectId));

                    Map<ParameterGroupTreeNode, Map<ParameterGroupTreeNode, Object>> compareToPGTNsMappingL = contentObject.findPGTNOriginVersionMapping(compareToMsg, node.getSchemaName(), dataType);

                    touchpointSelectionWrapper.setCompareToPGTNsMapping(compareToPGTNsMappingL);
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_A_ATTR, tpSel ->  tpSel.getCompareState(compareToPGTNsMappingL, compareFromObjectId, compareToObjectId, node.getSchemaName()));
                    compareToPGTNsMapping = compareToPGTNsMappingL;
                    otherDocument = touchpointSelectionWrapper.getCompareToDocument();

                    includeArchived = true;
                }

                if (getShowContentStatus()) {
                    final boolean isCOInTranslationStep = contentObject.isInTranslationStep();
                    final TouchpointSelectionWrapper wrapper = touchpointSelectionWrapper;
                    final long messageId = ContentObject.findById(wrapper.getContentObjectId()).getId();
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_A_ATTR, tpSel -> ((TouchpointSelectionWrapper)dataBinding).getContentStatus(wrapper, messageId, tpSel, isCOInTranslationStep));
                }

                touchpointSelection = touchpointSelectionWrapper.getOwningTpSelection() != null ? touchpointSelectionWrapper.getOwningTpSelection() : touchpointSelectionWrapper.getMasterTpSelection();
                rootSelection = touchpointSelectionWrapper.getMasterTpSelection();
                isEnabledForVariantWorkflow = touchpointSelectionWrapper.isEnabledForVariantWorkflow();
                isActive = touchpointSelectionWrapper.isStatusViewActive();
                this.masterNodeId = ((TouchpointSelectionWrapper) dataBinding).getMasterTpSelection().getId();
            } else {

                if (dataBinding instanceof  TouchpointContentObjectContentSelection) {
                    touchpointSelection = ((TouchpointContentObjectContentSelection)dataBinding).getTouchpointSelection();
                    contentObject = ((TouchpointContentObjectContentSelection)dataBinding).getContentObject();
                } else {
                    touchpointSelection = this.dataBinding instanceof TouchpointSelectionVO ? ((TouchpointSelectionVO) this.dataBinding).getTouchpointSelection() : (TouchpointSelection) this.dataBinding;
                }

                rootSelection = touchpointSelection;

                isEnabledForVariantWorkflow = touchpointSelection.getDocument().isEnabledForVariantWorkflow();
                this.masterNodeId = touchpointSelection.getId();
            }

            User user = UserUtil.getPrincipalUser();

            if(contentObject != null){
                contentObject.setFocusOnDataType(isActive?ContentObject.DATA_TYPE_ACTIVE:ContentObject.DATA_TYPE_WORKING);
            }
            final ContentObject finalContentObject = contentObject;
            final boolean finalIsActive = isActive;

            if ( getApplyDocumentContext() ) {
                extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_LI_ATTR, TouchpointSelection::getDocumentContext);
            }

            if ( isEnabledForVariantWorkflow && !getConnectedContext() ) {
                if ( getShowVersioning() ) {
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_ICON, tpSel ->
                            (tpSel.getHasActiveCopy()
                                    ? " activeCopyIcon " : "") + (tpSel.getHasWorkingCopy() ? " workingCopyIcon " : ""));
                }

                if (getShowContentStatus()){
                    if (dataBinding instanceof  TouchpointSelection) {
                        extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_A_ATTR, TouchpointSelection::getContentStatus);
                    }
                }
            } else {
                extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_DISABLED, tpSel ->
                        (finalIsActive && !finalContentObject.isFocusOnActiveData())
                                || !(( (getConnectedContext() ? tpSel.isConnectedFullyVisible() : tpSel.isFullyVisible())
                                || (getConnectedContext() ? tpSel.getConnectedVisibleUsers().contains(user) : tpSel.isVisible(user)) )
                                && tpSel.isEnabledForZoneContext(finalContentObject)));
            }

            if (dataBinding instanceof TouchpointSelectionWrapper && isEnabledForVariantWorkflow && touchpointSelectionWrapper != null) {

                boolean filterOwnSelections = touchpointSelectionWrapper.isEditContext() && touchpointSelectionWrapper.isEnabledForVariantWorkflow();

                if (finalIsActive) {
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_DISABLED, tpSel ->
                            !(tpSel.getHasActiveCopy()
                                    && TouchpointSelectionWrapper.hasContentSelectionForMessage(tpSel, finalContentObject.getId(), tpSel.getParent())
                                    && ( (getConnectedContext() ? tpSel.isConnectedFullyVisible() : tpSel.isFullyVisible()) || (getConnectedContext() ? tpSel.getConnectedVisibleUsers().contains(user) : tpSel.isVisible(user))) && tpSel.isEnabledForZoneContext(finalContentObject))
                                    || (filterOwnSelections && !tpSel.getIsMine()));
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_DISABLED_MASTER, tpSel -> !finalContentObject.isFocusOnActiveData());
                } else {
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_DISABLED, tpSel ->
                            !(tpSel.getHasWorkingCopy()
                                    && ( (getConnectedContext() ? tpSel.isConnectedFullyVisible() : tpSel.isFullyVisible())
                                    || (getConnectedContext() ? tpSel.getConnectedVisibleUsers().contains(user) : tpSel.isVisible(user)))
                                    && tpSel.isEnabledForZoneContext(finalContentObject))
                                    || (filterOwnSelections && !tpSel.getIsMine()));
                    extendedPropertiesMap.put(JSTreeJsonSerializer.EXTENDED_PROPERTY_DISABLED_MASTER, tpSel -> !finalContentObject.isFocusOnWorkingData());
                }

            }

            TouchpointSelectionJsonSerializer binding = new TouchpointSelectionJsonSerializer(touchpointSelection, rootSelection, otherDocument);

            binding.setExtendedPropertiesMap(extendedPropertiesMap);
            binding.setCompareToPGNsMapping(compareToPGTNsMapping);
            binding.setIncludedArchived(includeArchived);

            return binding;
        }

        public Boolean getApplyDocumentContext() {
            return applyDocumentContext;
        }

        public void setApplyDocumentContext(Boolean applyDocumentContext) {
            this.applyDocumentContext = applyDocumentContext;
        }

        public Boolean getTreesCompareContext() {
            return treesCompareContext;
        }

        public void setTreesCompareContext(Boolean treesCompareContext) {
            this.treesCompareContext = treesCompareContext;
        }

        public Boolean getContentCompareContext() {
            return contentCompareContext;
        }

        public void setContentCompareContext(Boolean contentCompareContext) {
            this.contentCompareContext = contentCompareContext;
        }

        public Boolean getConnectedContext() {
            return connectedContext;
        }

        public void setConnectedContext(Boolean connectedContext) {
            this.connectedContext = connectedContext;
        }

        public Boolean getExpanded() {
            return expanded;
        }

        public void setExpanded(Boolean expanded) {
            this.expanded = expanded;
        }

        public Boolean getShowContentStatus() {
            return showContentStatus;
        }

        public void setShowContentStatus(Boolean showContentStatus) {
            this.showContentStatus = showContentStatus;
        }

        public Boolean getShowVersioning() {
            return showVersioning;
        }

        public void setShowVersioning(Boolean showVersioning) {
            this.showVersioning = showVersioning;
        }

        public long getMasterNodeId() {
            return masterNodeId;
        }

        public void setMasterNodeId(long masterNodeId) {
            this.masterNodeId = masterNodeId;
        }

        public Object getDataBinding() {
            return dataBinding;
        }

        public void setDataBinding(Object dataBinding) {
            this.dataBinding = dataBinding;
        }

        public String getCompareToObjectId() {
            return compareToObjectId;
        }

        public void setCompareToObjectId(String compareToObjectId) {
            this.compareToObjectId = compareToObjectId;
        }

        public String getCompareDataType() {
            return compareDataType;
        }

        public void setCompareDataType(String compareDataType) {
            this.compareDataType = compareDataType;
        }

        public String getCssClass() {
            return cssClass;
        }

        public void setCssClass(String cssClass) {
            this.cssClass = cssClass;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOnChange() {
            return onChange;
        }

        public void setOnChange(String onChange) {
            this.onChange = onChange;
        }

        public String getOnContextMenuSelect() {
            return onContextMenuSelect;
        }

        public void setOnContextMenuSelect(String onContextMenuSelect) {
            this.onContextMenuSelect = onContextMenuSelect;
        }

        public String getOnResize() {
            return onResize;
        }

        public void setOnResize(String onResize) {
            this.onResize = onResize;
        }

        public String getSelectedNodeId() {
            return selectedNodeId;
        }

        public void setSelectedNodeId(String selectedNodeId) {
            this.selectedNodeId = selectedNodeId;
        }

        public String getStyle() {
            return style;
        }

        public void setStyle(String style) {
            this.style = style;
        }

        public String getCompareToNodeId() {
            return compareToNodeId;
        }

        public void setCompareToNodeId(String compareToNodeId) {
            this.compareToNodeId = compareToNodeId;
        }

        public Boolean getSyncCommit() { return syncCommit; }

        public Boolean isSyncCommit() { return getSyncCommit(); }

        public void setSyncCommit(Boolean syncCommit) {
            this.syncCommit = syncCommit;
        }
    }

    public static class AsyncVariantTreeRequestVO {
        private boolean applyDocumentContext = false;
        private boolean connectedContext = false;
        private boolean contentCompareContext = false;
        private boolean expanded;
        private boolean showContentStatus = false;
        private boolean showVersioning = true;
        private boolean treesCompareContext = false; // Compare two variant trees and show the changes
        private long masterNodeId = ParameterGroupTreeNode.MASTER_VARIANCE_ID;
        private long statusViewId;
        private String compareToNodeId;
        private String compareToObjectId;
        private String compareDataType;
        private String cssClass;
        private String id;
        private String onChange;
        private String onContextMenuSelect;
        private String onResize;
        private String selectedNodeId;
        private String style;
        private boolean syncCommit = false;

        public boolean isApplyDocumentContext() {
            return applyDocumentContext;
        }

        public void setApplyDocumentContext(boolean applyDocumentContext) {
            this.applyDocumentContext = applyDocumentContext;
        }

        public boolean isConnectedContext() {
            return connectedContext;
        }

        public void setConnectedContext(boolean connectedContext) {
            this.connectedContext = connectedContext;
        }

        public boolean isContentCompareContext() {
            return contentCompareContext;
        }

        public void setContentCompareContext(boolean contentCompareContext) {
            this.contentCompareContext = contentCompareContext;
        }

        public boolean isExpanded() {
            return expanded;
        }

        public void setExpanded(boolean expanded) {
            this.expanded = expanded;
        }

        public boolean isShowContentStatus() {
            return showContentStatus;
        }

        public void setShowContentStatus(boolean showContentStatus) {
            this.showContentStatus = showContentStatus;
        }

        public boolean isShowVersioning() {
            return showVersioning;
        }

        public void setShowVersioning(boolean showVersioning) {
            this.showVersioning = showVersioning;
        }

        public boolean isTreesCompareContext() {
            return treesCompareContext;
        }

        public void setTreesCompareContext(boolean treesCompareContext) {
            this.treesCompareContext = treesCompareContext;
        }

        public long getMasterNodeId() {
            return masterNodeId;
        }

        public void setMasterNodeId(long masterNodeId) {
            this.masterNodeId = masterNodeId;
        }

        public long getStatusViewId() {
            return statusViewId;
        }

        public void setStatusViewId(long statusViewId) {
            this.statusViewId = statusViewId;
        }

        public String getCompareToNodeId() {
            return compareToNodeId;
        }

        public void setCompareToNodeId(String compareToNodeId) {
            this.compareToNodeId = compareToNodeId;
        }

        public String getCompareToObjectId() {
            return compareToObjectId;
        }

        public void setCompareToObjectId(String compareToObjectId) {
            this.compareToObjectId = compareToObjectId;
        }

        public String getCompareDataType() {
            return compareDataType;
        }

        public void setCompareDataType(String compareDataType) {
            this.compareDataType = compareDataType;
        }

        public String getCssClass() {
            return cssClass;
        }

        public void setCssClass(String cssClass) {
            this.cssClass = cssClass;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getOnChange() {
            return onChange;
        }

        public void setOnChange(String onChange) {
            this.onChange = onChange;
        }

        public String getOnContextMenuSelect() {
            return onContextMenuSelect;
        }

        public void setOnContextMenuSelect(String onContextMenuSelect) {
            this.onContextMenuSelect = onContextMenuSelect;
        }

        public String getOnResize() {
            return onResize;
        }

        public void setOnResize(String onResize) {
            this.onResize = onResize;
        }

        public String getSelectedNodeId() {
            return selectedNodeId;
        }

        public void setSelectedNodeId(String selectedNodeId) {
            this.selectedNodeId = selectedNodeId;
        }

        public String getStyle() {
            return style;
        }

        public void setStyle(String style) {
            this.style = style;
        }

        public boolean isSyncCommit() {
            return syncCommit;
        }

        public void setSyncCommit(boolean syncCommit) {
            this.syncCommit = syncCommit;
        }
    }
}
