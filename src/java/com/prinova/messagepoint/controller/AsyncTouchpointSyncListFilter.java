package com.prinova.messagepoint.controller;

import com.prinova.messagepoint.model.common.SyncObjectType;
import org.json.JSONArray;
import org.parboiled.common.StringUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class AsyncTouchpointSyncListFilter {
    private final String filterByStatus;
    private final String filterByObjectType;
    private final String searchString;
    private final List<String> objectIds;
    private final Boolean acceptAll;

    private static final String STATUS_ALL = "0";
    private static final String STATUS_NO_CONFLICT = "1";
    private static final String STATUS_CONFLICT = "2";
    private static final String STATUS_REJECTED = "3";
    private static final String STATUS_SELECTED = "4";
    private static final String STATUS_NOT_SELECTED = "5";

    public AsyncTouchpointSyncListFilter(String filterByStatus, String filterByObjectType, String searchString, List<String> objectIds, Boolean acceptAll) {
        this.filterByStatus = filterByStatus;
        this.filterByObjectType = filterByObjectType;
        this.searchString = searchString;
        this.acceptAll = acceptAll;
        this.objectIds = objectIds;
    }

    public JSONArray filter(JSONArray data){
        JSONArray filteredData = new JSONArray(data.toList());

        if(!filteredData.isEmpty()){
            filteredData = filterByObjectType(filteredData);
            filteredData = filterByStatus(filteredData);
            filteredData = filterBySeacrhString(filteredData);
        }

        return filteredData;
    }

    private JSONArray filterByObjectType(JSONArray data){
        // Check if need to filter by object type
        if(StringUtils.isEmpty(filterByObjectType) && data != null && !data.isEmpty()){
            return data;
        }

        try {
            // Validate provided object type to filter on
            Integer iFilterObjectType = Integer.parseInt(filterByObjectType);

            if (!data.isEmpty() && (new SyncObjectType(iFilterObjectType)).getId() > 0) {
                return new JSONArray(data.toList().stream().filter(o -> {
                    HashMap map = (HashMap) o;
                    return map.get("objectType").equals(iFilterObjectType);
                }).collect(Collectors.toList()));
            }
        } catch (NumberFormatException ex) {
            // do nothing, return unfiltered data
        }

        return data;
    }

    private JSONArray filterByStatus(JSONArray data){
        // Check if need to filter by object type
        if(StringUtils.isEmpty(filterByStatus) && data != null && !data.isEmpty()){
            return data;
        }

        return new JSONArray(data.toList().stream().filter(o -> {
            HashMap map = (HashMap) o;
            if(STATUS_CONFLICT.equals(filterByStatus)){
                return AsyncTouchpointSyncListComparator.isConflicted(map);
            }
            else if(STATUS_NO_CONFLICT.equals(filterByStatus)){
                return !AsyncTouchpointSyncListComparator.isConflicted(map);
            }
            else if(STATUS_SELECTED.equals(filterByStatus) || STATUS_REJECTED.equals(filterByStatus) || STATUS_NOT_SELECTED.equals(filterByStatus)){
                if(objectIds != null && !objectIds.isEmpty()){
                    List<String> selectedObjectIds = objectIds.stream().map(object -> {
                        String [] parts = object.split("_");
                        return parts[1];
                    }).collect(Collectors.toList());

                    boolean mapInTheObjectIds = selectedObjectIds.contains(map.get("id").toString());

                    if (STATUS_NOT_SELECTED.equals(filterByStatus))
                        return !mapInTheObjectIds;

                    return mapInTheObjectIds;
                }
                else {
                    return STATUS_NOT_SELECTED.equals(filterByStatus);
                }
            }

            return false;

        }).collect(Collectors.toList()));
    }

    private JSONArray filterBySeacrhString(JSONArray data){
        // Check if need to filter by object type
        if(!StringUtils.isEmpty(searchString) && data != null && !data.isEmpty()){
            return new JSONArray(data.toList().stream().filter(o -> {
                HashMap map = (HashMap) o;

                String name = (String)map.get("name");
                String targetObjectName = (String)map.get("targetObjectName");

                return name.toLowerCase().contains(searchString.toLowerCase()) ||
                        Optional.ofNullable(targetObjectName).orElse("").toLowerCase().contains(searchString.toLowerCase());

            }).collect(Collectors.toList()));
        }

        return data;
    }
}
