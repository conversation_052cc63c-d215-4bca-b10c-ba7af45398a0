package com.prinova.messagepoint;


/**
 * An {@link ExceptionHandler} handles exception processing.
 * 
 * Multiple exception handlers can be chained together for fine tune exception 
 * processing.  The chained handlers are sequenced based on the {@link #getPriority()}
 * value.
 * 
 */
public interface ExceptionHandler {

	/**
	 * Handles the given {@link Throwable} exception.
	 * 
	 * @return A handler specific return value/object
	 */
	public Object handle(ExceptionEvent event);
	
	
	/**
	 * Determines if the {@link ExceptionHandler} can handle this exception.
	 * 
	 * @return true if the exception can be handled, false otherwise.
	 */
	public boolean canHandle(ExceptionEvent event); 
	
	/**
	 * The priority helps determine the order in which the exception handlers should be
	 * chained together.
	 *
     */
	public int getPriority();
	
}
