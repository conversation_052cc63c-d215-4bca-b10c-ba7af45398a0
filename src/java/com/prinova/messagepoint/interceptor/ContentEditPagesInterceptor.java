package com.prinova.messagepoint.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

public class ContentEditPagesInterceptor extends HandlerInterceptorAdapter {

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response,
                           Object handler, ModelAndView modelAndView) throws Exception {


        if (modelAndView != null && modelAndView.hasView()) {
            String originalViewName = modelAndView.getViewName();
            if ("content/content_object_edit_content".equals(originalViewName) ||
                "content/content_object_edit_content_dynamic_variant".equals(originalViewName) ||
                    "content/content_object_edit_content_image_library".equals(originalViewName) ||
                    "content/content_object_edit_content_regular".equals(originalViewName) ||
                    "content/content_object_edit_content_smart".equals(originalViewName) ||
                    "content/content_object_edit_content_tp_variant".equals(originalViewName) ||
                    "tpadmin/touchpoint_content_selection_edit".equals(originalViewName)
            ) {
                boolean newEditor = Boolean.parseBoolean(SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.UserInterface.KEY_NewEditor));

                if (!newEditor) {
                    modelAndView.setViewName(originalViewName + "_OLD");
                }
            }
        }
    }
}

