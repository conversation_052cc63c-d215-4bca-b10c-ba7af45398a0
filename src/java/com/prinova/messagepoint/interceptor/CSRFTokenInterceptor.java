package com.prinova.messagepoint.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.mvc.Controller;
import org.springframework.web.servlet.view.RedirectView;

import com.prinova.messagepoint.model.SystemPropertyKeys.WebAppSecurity;
import com.prinova.messagepoint.util.ApplicationUtil;

public class CSRFTokenInterceptor extends Hand<PERSON>InterceptorAdapter {

	/**
	 * just add the token to the returned ModelAndView instance
	 * so that the token can be exposed to the page context
	 */
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
		if(ApplicationUtil.isCSRFPreventionEnabled() && modelAndView != null && handler instanceof Controller) {
			//for the page parameter retention in URL
//			if(request.getParameter("page") != null && request.getParameter("page").length() > 0)
//				modelAndView.addObject("page", request.getParameter("page"));
			
			if(modelAndView.getView() != null && modelAndView.getView() instanceof RedirectView) {
				String token = request.getParameter(WebAppSecurity.CSRF_TOKEN_KEY);
				RedirectView rv = (RedirectView) modelAndView.getView();
				String url = rv.getUrl();
				if(url != null && !url.isEmpty() && token != null && url.indexOf(token) == -1) {
					rv.setUrl(ApplicationUtil.addToken(url, token));
				}
			}
		}
	}

	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
		// TODO Auto-generated method stub
	}
}
