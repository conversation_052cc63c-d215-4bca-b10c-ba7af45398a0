package com.prinova.messagepoint.interceptor;

import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.mvc.Controller;

public class RequestParameterRetentionInterceptor extends HandlerInterceptorAdapter {

	private Set<String> retentionParameterSet;
	
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
		if(retentionParameterSet != null && modelAndView != null && handler instanceof Controller) {
			for (String param : retentionParameterSet) {
				if(request.getParameter(param) != null && !request.getParameter(param).isEmpty())
					modelAndView.addObject(param, request.getParameter(param));
			}
		}
	}

	public Set<String> getRetentionParameterSet() {
		return retentionParameterSet;
	}

	public void setRetentionParameterSet(Set<String> retentionParameterSet) {
		this.retentionParameterSet = retentionParameterSet;
	}
}
