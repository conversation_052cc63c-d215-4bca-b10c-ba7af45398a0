package com.prinova.messagepoint;



public class MessagepointException extends RuntimeException {
	
	private static final long serialVersionUID = 1068086416729105986L;
	private ExceptionEvent event;
	private ExceptionHandlingResult result;
	
	public MessagepointException() {
		super();
	}

	public MessagepointException(Throwable t){
		super(t);
	}

	public MessagepointException(String message) {
		super(message);
	}
	
	public MessagepointException(String message, Throwable t) {
		this(message, t, ExceptionEvent.createDefault(t), null);
	}
	
	public MessagepointException(String message, Throwable throwable, ExceptionEvent event, ExceptionHandlingResult result) {
		super(message, throwable);
		setEvent(event);
		setResult(result);
	}

	public ExceptionEvent getEvent() {
		return event;
	}

	public void setEvent(ExceptionEvent event) {
		this.event = event;
	}

	public ExceptionHandlingResult getResult() {
		return result;
	}

	public void setResult(ExceptionHandlingResult handlingResult) {
		this.result = handlingResult;
	}

	public boolean isProcessed(){
		return (result==null)? false: result.isProcessed();
	}
	
	
}
