package com.prinova.messagepoint.initialization;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

import org.springframework.beans.factory.InitializingBean;

import com.prinova.messagepoint.util.LogUtil;

public class InitializeClassTableNames implements InitializingBean {
	private String hibernateDialect;
	private String classTableMappings;
	private HashSet<String> classNames = null;
	private HashSet<String> tableNames = null;

	/* (non-Javadoc)
	 * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet()
	 */
	public void afterPropertiesSet() throws Exception {
		LogUtil.getLog(InitializeClassTableNames.class).debug("Inside InitializeClassTableNames.afterPropertiesSet()...");
	}

	public String getHibernateDialect() {
		return this.hibernateDialect;
	}
	public void setHibernateDialect(String hibernateDialect) {
		this.hibernateDialect = hibernateDialect;
	}
	
	public String getClassTableMappings() {
		return this.classTableMappings;
	}
	public void setClassTableMappings(String classTableMappings) {
		this.classTableMappings = classTableMappings;
	}

	public Set<String> getClassNames() {
		if (this.classNames == null) {
			refreshClassNames();
		}
		return this.classNames;
	}

	public Set<String> getTableNames() {
		if (this.tableNames == null) {
			refreshTableNames();
		}
		return this.tableNames;
	}

	private void refreshClassNames() {
		if (this.classTableMappings == null) {
			LogUtil.getLog(InitializationHibernateInterceptor.class).warn("(this.classTableMappings == null)! Cannot handle a SQLServer refresh!");
		} else {
			File webInfDir = InitializeDatabase.getWebInfDir();
			LogUtil.getLog(InitializeClassTableNames.class).info("WEB-INF='"+webInfDir+"'");
			File mappingFile = new File(webInfDir, "initialization" + File.separator + this.classTableMappings);
			this.classNames = new HashSet<>();
			try {
				Properties mappingProperties = new Properties();
				mappingProperties.load(new FileInputStream(mappingFile));
				Set<Object> keys = mappingProperties.keySet();
				for (Object key : keys) {
					this.classNames.add(key.toString());
				}
			} catch(IOException ioe) {
				LogUtil.getLog(InitializeClassTableNames.class).error("IOException caught on attempt to parse the Class Names from the mapping file found at '"+this.classTableMappings+"': '"+ioe.getMessage()+"'. Initializing the Set to a new empty Set and continuing!");
				this.classNames = new HashSet<>();
			}
		}
	}

	private void refreshTableNames() {
		if (this.classTableMappings == null) {
			LogUtil.getLog(InitializationHibernateInterceptor.class).warn("(this.classTableMappings == null)! Cannot handle a SQLServer refresh!");
		} else {
			File webInfDir = InitializeDatabase.getWebInfDir();
			LogUtil.getLog(InitializeClassTableNames.class).info("WEB-INF='"+webInfDir+"'");
			File mappingFile = new File(webInfDir, "initialization" + File.separator + this.classTableMappings);
			this.tableNames = new HashSet<>();
			try {
				Properties mappingProperties = new Properties();
				mappingProperties.load(new FileInputStream(mappingFile));
				Set<Object> keys = mappingProperties.keySet();
				for (Object key : keys) {
					String tableName = (String)mappingProperties.get(key);
					if ( (tableName != null) && (!tableName.isEmpty()) ) {
						this.tableNames.add(tableName);
					}
				}
			} catch(IOException ioe) {
				LogUtil.getLog(InitializeClassTableNames.class).error("IOException caught on attempt to parse the Table Names from the mapping file found at '"+this.classTableMappings+"': '"+ioe.getMessage()+"'. Initializing the Set to a new empty Set and continuing!");
				this.tableNames = new HashSet<>();
			}
		}
	}
}
