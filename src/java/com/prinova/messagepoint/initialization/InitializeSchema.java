package com.prinova.messagepoint.initialization;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.MessagePointStartUp;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.audit.AuditActionType;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.PropertyUtils;
import com.prinova.migrate.Migrate;
import org.apache.commons.io.FileUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.jdbc.Work;
import org.hibernate.query.NativeQuery;
import org.springframework.orm.hibernate5.SessionHolder;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class InitializeSchema extends MessagePointRunnable {
	
	private static final Log log = LogUtil.getLog(InitializeSchema.class);
	
	private String dbtask = "update";
	private String dbflavor = "postgres";
	private File webInfDir;
	private long activateNodeId = 0;
	private long activateBranchId = 0;
	private boolean isPodMaster = false;

	public void performMainProcessing() {

		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		log.info("TenantIdentifier from MessagepointCurrentTenantIdentifierResolver in InitializeSchema: " + currentSchema);

		isPodMaster = (currentSchema == null);

		if (dbtask.equalsIgnoreCase("upgrade"))
		{
			if (activateNodeId <= 0)
			{
				Node node = Node.findBySchema(currentSchema);
				if (node != null)
					activateNodeId = node.getId();
			}
			
			if (currentSchema == null)
			{
				if (activateNodeId > 0)
				{
					currentSchema = MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName();
				}
				else
				{
					currentSchema = (System.getProperty("db.messagepoint.user") == null ? "default" : System.getProperty("db.messagepoint.user") );
				}
			}			
			
			log.info("Request upgrade DB for Node ID = " + activateNodeId + " schema name = " + currentSchema);

			if (activateNodeId > 0)
			{
				Node.changeStatus(activateNodeId, Node.NODE_STATUS_IN_MIGRATING_PROCESS);
				Node.activateNode(activateNodeId, false);
			}
			
			if (activateBranchId > 0)
			{
				Branch.changeStatus(activateBranchId, Branch.DCS_STATUS_IN_MIGRATING_PROCESS);
				Branch.activateBranch(activateBranchId, false);
			}
			
			String[] args = new String[] {"-mprequest", "-developer", "-schema"};

			args[2] = "-schema=" + currentSchema;
			
			try {
				Migrate migrate = new Migrate();
				migrate.run(args);
			} catch (Exception e) {
				log.info("There was an error: " + org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(e));
			}

			Node node = Node.findById(activateNodeId);
			if (node != null)
			{
				MigrateSchema migrateTask = new MigrateSchema();

				SystemPropertyManager.getInstance().clearPropertyCache(null);
				SystemPropertyManager.getInstance().clearPropertyCache(currentSchema);

				String toPath = node.getFileroot().getAbsolutePath();
				
				String fromPath = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_ReportDir);
	
				if (fromPath != null)
					fromPath = fromPath.replace("/reports/", "");
				else
					fromPath = toPath;
	
				migrateTask.setFromPath(fromPath);

				if (!isPodMaster) // Set if it's not pod master schema
				{
					migrateTask.setToPath(toPath);
				}

				migrateTask.setMigrateNodeId(activateNodeId);
				migrateTask.setDcsNodeGuid(node.getBranch().getDcsNode().getGuid());

				if (!isPodMaster)
					migrateTask.setEnableNodeAfterMigration(false);
				
				migrateTask.performMainProcessing();
			}
		}

		SessionHolder mainSessionHolder = null;
		Session session = null;
		try {
			// Set following parameters in case it's ANT process
			String messagepointFileroot = (System.getProperty("messagepoint.fileroot") == null ? "/messagepoint/mp/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() + "/fileroot" : System.getProperty("messagepoint.fileroot"));
			String messagepointJobFileroot = (System.getProperty("messagepoint.job.fileroot") == null ? "/repository/jobs/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() : System.getProperty("messagepoint.job.fileroot"));
			String messagepointJobWorkingFileroot = (System.getProperty("messagepoint.job.working.fileroot") == null ? "/localFiles/jobs/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() : System.getProperty("messagepoint.job.working.fileroot"));
			String messagepointOutputFileroot = (System.getProperty("messagepoint.output.fileroot") == null ? "/repository/outputs/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() : System.getProperty("messagepoint.output.fileroot"));
			String messagepointQEFileroot = (System.getProperty("messagepointQE.fileroot") == null ? "/messagepoint/declient/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() : System.getProperty("messagepointQE.fileroot"));
			String compositionEnginesFileroot = (System.getProperty("compositionEngines.fileroot") == null ? "/messagepoint/compEngine" : System.getProperty("compositionEngines.fileroot"));
			String buildXml = (System.getProperty("umh.build.xml") == null ? "/messagepointUMH/build.xml" : System.getProperty("umh.build.xml"));
			String antHome = (System.getProperty("ant.home") == null ? "/ant" : System.getProperty("ant.home"));

			if (currentSchema == null) {
				currentSchema = (System.getProperty("db.messagepoint.user") == null ? "default" : System.getProperty("db.messagepoint.user"));

			}

			String currentSchemaLowerCase = currentSchema.toLowerCase();

			// Email settings

			String emailServer = "";
			String emailAccount = "";
			String emailPassword = "";
			String emailPort = "-1";
			String emailSecurity = "0";

			Map<Long, Map<String, String>> users = new HashMap<>();

			// JOB Folders

			String outgoingJobs = messagepointJobFileroot + "/" + currentSchemaLowerCase + "/outgoing/";
			String incomingJobs = messagepointJobFileroot + "/" + currentSchemaLowerCase + "/incoming/";
			String successfulJobs = messagepointJobFileroot + "/" + currentSchemaLowerCase + "/success/";
			String failedJobs = messagepointJobFileroot + "/" + currentSchemaLowerCase + "/failure/";
			String inProcessJobs = messagepointJobWorkingFileroot + "/" + currentSchemaLowerCase + "/temp/";
			String messageOutputs = messagepointOutputFileroot + "/" + currentSchemaLowerCase + "/dynamiccontent/output/";
			String rationalizerFilesDir = null;
			String transformFilesDir = null;
			String contentAssistantFilesDir = null;

			// Disable the node before initialization
			if (activateNodeId > 0) {

				SystemPropertyManager.getInstance().clearPropertyCache(null);
				SystemPropertyManager.getInstance().clearPropertyCache(currentSchema);

				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);

				if (!dbtask.equalsIgnoreCase("upgrade")) {
					Node.changeStatus(activateNodeId, Node.NODE_STATUS_IN_INITIALIZING_PROCESS);
					Node.activateNode(activateNodeId, false);

					if (activateBranchId > 0) {
						Branch.changeStatus(activateBranchId, Branch.DCS_STATUS_IN_INITIALIZING_PROCESS);
						Branch.activateBranch(activateBranchId, false);
					}
				}

				// This is initialization from running UMH UI
				// Determine following parameters from pod master node 
				//
				String temp;

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_Axis2RepositoryDir);
				if (temp != null) {
					temp = temp.replace("/repository", "");
					if (!isPodMaster || temp.equalsIgnoreCase(messagepointFileroot)) {
						messagepointFileroot = temp;
						isPodMaster = false;
					}
				}

				log.info("Pod Master repository path in initializeSchema class: " + temp + " Messagepoint Fileroot: " + messagepointFileroot + " isPodMaster = " + isPodMaster);

				if (!isPodMaster) {
					messagepointQEFileroot = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_MessagepointQEFileroot);
					log.info("Pod Master DE path in initializeSchema class: " + messagepointQEFileroot);
					if (messagepointQEFileroot != null && messagepointQEFileroot.endsWith("/"))
						messagepointQEFileroot = messagepointQEFileroot.substring(0, messagepointQEFileroot.length() - 1);

					compositionEnginesFileroot = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_CompositionEnginesFileroot);
					if (compositionEnginesFileroot != null && compositionEnginesFileroot.endsWith("/"))
						compositionEnginesFileroot = compositionEnginesFileroot.substring(0, compositionEnginesFileroot.length() - 1);

					buildXml = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ExactTargetSettings.KEY_ExactTargetBuildXMLPath);

					antHome = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ExactTargetSettings.KEY_ExactTargetAntPath);
					if (antHome != null)
						antHome = antHome.replace("/bin/ant", "");
				}

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerAddress);
				if (temp != null)
					emailServer = temp;

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerUserId);
				if (temp != null)
					emailAccount = temp;

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerUserPassword);
				if (temp != null)
					emailPassword = temp;

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerPort);
				if (temp != null)
					emailPort = temp;

				temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpConnectionSecurity);
				if (temp != null)
					emailSecurity = temp;

				// Switch to DCS node if the activate node is not DCS

				String searchPattern = "(?i)" + Pattern.quote(MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName().toLowerCase());

				Node node = Node.findById(activateNodeId);
				if (node != null) {
					if (!node.isDcsNode()) {
						node = node.getBranch().getDcsNode();
						if (node != null && node.getSchemaName() != null && node.isAccessible()) {
							HibernateUtil.getManager().restoreSession(mainSessionHolder);
							mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
							searchPattern = "(?i)" + Pattern.quote(node.getSchemaName().toLowerCase());
						}
					}
				}

				// Following parameters will be read from POD MASTER if the initialized node is DCS; otherwise it will be from the DCS node

				long[] userIDs = {900, 901, 999};

				for (int i = 0; i < 3; i++) {
					User user = User.findById(userIDs[i]);
					if (user != null) {
						Map<String, String> password = new HashMap<>();
						password.put(user.getPassword(), user.getSalt());
						users.put(userIDs[i], password);
					}
				}

				if (!isPodMaster) {
					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_OutgoingDir);
					if (temp != null)
						outgoingJobs = temp.replaceAll(searchPattern, currentSchemaLowerCase);

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_IncomingDir);
					if (temp != null)
						incomingJobs = temp.replaceAll(searchPattern, currentSchemaLowerCase);

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_IncomingSuccessDir);
					if (temp != null)
						successfulJobs = temp.replaceAll(searchPattern, currentSchemaLowerCase);

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_IncomingFailureDir);
					if (temp != null)
						failedJobs = temp.replaceAll(searchPattern, currentSchemaLowerCase);

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_Working);
					if (temp != null)
						inProcessJobs = temp.replaceAll(searchPattern, currentSchemaLowerCase);

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_RationalizerFilesDir);
					if (temp != null) {
						rationalizerFilesDir = temp.replaceAll(searchPattern, currentSchemaLowerCase);
					}

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_TransformReportDir);
					if (temp != null) {
						transformFilesDir = temp.replaceAll(searchPattern, currentSchemaLowerCase);
					}

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_ContentAssistantReportDir);
					if (temp != null) {
						contentAssistantFilesDir = temp.replaceAll(searchPattern, currentSchemaLowerCase);
					}
				}

				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

			boolean isStaticTableRebuildRequired = true;
			boolean isDbRebuildTask = dbtask.equalsIgnoreCase("rebuild");
			String podMasterSchemaName = MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName();

			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(podMasterSchemaName.equalsIgnoreCase(currentSchema) ? null : currentSchema);
				session = HibernateUtil.getManager().getSession();

				if (isDbRebuildTask) {
					log.info("Starting task 'rebuild' the schema: " + currentSchema + " identifier from session: " + session.getTenantIdentifier());
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-ddl.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_report-ddl.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_report-views.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-views.sql",false);

					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-testdata.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/schemaInfo.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/schemaInfoRp.sql",false);

					InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "initialization/database/postgres/messagepoint-fk-ddl.sql"));
					InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "initialization/database/postgres/messagepoint_report-fk-ddl.sql"));

					if (currentSchema.equalsIgnoreCase(MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName())) {
						runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-quartz.sql",false);
						InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "messagepoint-schedule.xml"));
					}

					File schemaFileroot = new File(messagepointFileroot + "/" + currentSchemaLowerCase);
					if (schemaFileroot != null && schemaFileroot.exists() && schemaFileroot.isDirectory()) {
						FileUtils.cleanDirectory(schemaFileroot);
					}

					User primaryUser = HibernateUtil.getManager().getObject(User.class, 1L);
					InitializeMetadataParser initializeMetadataParser =
							new InitializeMetadataParser(isRebuild(), session, currentSchema, messagepointFileroot,
									messagepointJobFileroot, messagepointJobWorkingFileroot, messagepointOutputFileroot,
									messagepointQEFileroot, compositionEnginesFileroot, buildXml, antHome);
					initializeMetadataParser.parseAllMetadata(primaryUser, webInfDir);
				} else {
					log.info("Starting task 'update' for schema: " + currentSchema);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-views.sql",false);
					runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_report-views.sql",false);

					isStaticTableRebuildRequired = StaticTableUtils.isStaticTableRebuildRequired(webInfDir, InitializeSchema::isSourceFileOutOfSync);

					if (isStaticTableRebuildRequired) {
						runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_upgrade-ddl.sql",false);
						runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_report_upgrade-ddl.sql",false);

						User primaryUser = HibernateUtil.getManager().getObject(User.class, 1L);
						InitializeMetadataParser initializeMetadataParser =
								new InitializeMetadataParser(isRebuild(), session, currentSchema, messagepointFileroot,
										messagepointJobFileroot, messagepointJobWorkingFileroot, messagepointOutputFileroot,
										messagepointQEFileroot, compositionEnginesFileroot, buildXml, antHome);
						initializeMetadataParser.parseAllMetadata(primaryUser, webInfDir);
						session.flush();
					}

					boolean isOnlyOneSchemaProcessed = this.getTenantIdentifier() == null || !this.getTenantIdentifier().startsWith("all");

					if (isOnlyOneSchemaProcessed && StaticTableUtils.isForeignKeyRebuildRequired(webInfDir, InitializeSchema::isSourceFileOutOfSync))
					{
						// Rebuild foreign keys only if one schema is processed; otherwise it will be checked and done in CheckForeignKeysScheduler
						//
						log.info("Rebuilding foreign keys for schema: " + currentSchema);
						StringBuilder deleteScript = new StringBuilder();
						deleteScript.append("DO $$ ");
						deleteScript.append("DECLARE r RECORD; ");
						deleteScript.append("BEGIN ");
						deleteScript.append("FOR r IN SELECT DISTINCT tc.table_schema, tc.table_name, tc.constraint_name ");
						deleteScript.append("FROM information_schema.table_constraints AS tc ");
						deleteScript.append("JOIN information_schema.constraint_column_usage AS ccu ");
						deleteScript.append("ON ccu.constraint_name = tc.constraint_name AND ccu.table_schema = tc.table_schema ");
						deleteScript.append("WHERE constraint_type = 'FOREIGN KEY' AND tc.table_schema = '").append(currentSchema).append("' ");
						deleteScript.append("LOOP ");
						deleteScript.append("EXECUTE 'ALTER TABLE ' || quote_ident(r.table_schema) || '.' || quote_ident(r.table_name) || ' DROP CONSTRAINT ' || quote_ident(r.constraint_name); ");
						deleteScript.append("END LOOP; ");
						deleteScript.append("END $$; ");

						runSQLScriptFromStringBuilder(session, deleteScript, "Delete All Foreign Keys");
						runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-cleanDataForFK.sql",true);

						if (!runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-fk-ddl.sql",false))
							InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "initialization/database/postgres/messagepoint-fk-ddl.sql"));

						if (!runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint_report-fk-ddl.sql",false))
							InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "initialization/database/postgres/messagepoint_report-fk-ddl.sql"));

						if (currentSchema.equalsIgnoreCase(MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName())) {
							if (!runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-quartz.sql", false))
								InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "messagepoint-schedule.xml"));
						}
					}
					else {
						if (currentSchema.equalsIgnoreCase(MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName()) && InitializeMetadataParser.isQuartzTableRebuildRequired(webInfDir)) {
							if (!runSQLScript(session, webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/messagepoint-quartz.sql", false))
								InitializeMetadataParser.updateDatabaseFileVersion(new File(webInfDir, "messagepoint-schedule.xml"));
						}
					}
				}

				// Post initialize the node information
				// Enable the node after initialization
				if (activateNodeId > 0) {
					Node.postInitNode(activateNodeId);

					// Email settings

					String query;
					NativeQuery sqlQuery;

					query = "UPDATE system_property SET prop_value = '" + emailServer + "' where prop_key = 'email.server.outgoing'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					query = "UPDATE system_property SET prop_value = '" + emailAccount + "' where prop_key = 'email.server.outgoing.user'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					query = "UPDATE system_property SET prop_value = '" + emailPassword + "' where prop_key = 'email.server.outgoing.password'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					query = "UPDATE system_property SET prop_value = '" + emailPort + "' where prop_key = 'email.server.outgoing.port'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					query = "UPDATE system_property SET prop_value = '" + emailSecurity + "' where prop_key = 'email.server.connection.security'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					query = "UPDATE system_property SET prop_value = 'true' where prop_key = 'global.sendemail.booleanflag'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					String taskLogFolder = messagepointFileroot + "/" + currentSchemaLowerCase + "/files/tasklogs/";
					query = "UPDATE system_property SET prop_value = '" + taskLogFolder + "' where prop_key = 'tasklog.fileroot.dir' and prop_value like '%@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@%'";
					log.info("Task Log Query:" + query);
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					String dictionaryFolder = messagepointFileroot + "/" + currentSchemaLowerCase + "/dictionary/";
					query = "UPDATE system_property SET prop_value = '" + dictionaryFolder + "' where prop_key = 'dictionary.fileroot.dir' and prop_value like '%@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@%'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					String bundleDeliveryPostProcessScriptsFolder = messagepointFileroot + "/" + currentSchemaLowerCase + "/bundleDeliveryPostProcessScripts/";
					query = "UPDATE system_property SET prop_value = '" + bundleDeliveryPostProcessScriptsFolder + "' where prop_key = 'bundle.delivery.post.process.scripts.folder' and prop_value like '%@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@%'";
					log.info("Bundle Script Folder: " + query);
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					String documentHistoryFolder = messagepointFileroot + "/" + currentSchemaLowerCase + "/files/document_history/";
					query = "UPDATE system_property SET prop_value = '" + taskLogFolder + "' where prop_key = 'document.history.dir' and prop_value like '%@@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@%'";
					log.info("Document History Query:" + query);
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					if (outgoingJobs != null) {
						query = "UPDATE system_property SET prop_value = '" + outgoingJobs + "' where prop_key = 'job.folder.outgoing'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (incomingJobs != null) {
						query = "UPDATE system_property SET prop_value = '" + incomingJobs + "' where prop_key = 'job.folder.incoming'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (successfulJobs != null) {
						query = "UPDATE system_property SET prop_value = '" + successfulJobs + "' where prop_key = 'job.folder.incoming.sucess'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (failedJobs != null) {
						query = "UPDATE system_property SET prop_value = '" + failedJobs + "' where prop_key = 'job.folder.incoming.failure'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (inProcessJobs != null) {
						query = "UPDATE system_property SET prop_value = '" + inProcessJobs + "' where prop_key = 'job.folder.working'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (messageOutputs != null) {
						query = "UPDATE system_property SET prop_value = '" + messageOutputs + "' where prop_key = 'message.folder.output'";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.executeUpdate();
					}

					if (rationalizerFilesDir != null) {
						query = "UPDATE system_property SET prop_value = :prop_value WHERE prop_key = :prop_key";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.setParameter("prop_value", rationalizerFilesDir);
						sqlQuery.setParameter("prop_key", SystemPropertyKeys.Folder.KEY_RationalizerFilesDir);
						sqlQuery.executeUpdate();
					}

					if (transformFilesDir != null) {
						query = "UPDATE system_property SET prop_value = :prop_value WHERE prop_key = :prop_key";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.setParameter("prop_value", transformFilesDir);
						sqlQuery.setParameter("prop_key", SystemPropertyKeys.Folder.KEY_TransformReportDir);
						sqlQuery.executeUpdate();
					}

					if (contentAssistantFilesDir != null) {
						query = "UPDATE system_property SET prop_value = :prop_value WHERE prop_key = :prop_key";
						sqlQuery = session.createNativeQuery(query);
						sqlQuery.setParameter("prop_value", contentAssistantFilesDir);
						sqlQuery.setParameter("prop_key", SystemPropertyKeys.Folder.KEY_ContentAssistantReportDir);
						sqlQuery.executeUpdate();
					}

					query = "UPDATE system_property SET prop_value = null where prop_key = 'de.versions'";
					sqlQuery = session.createNativeQuery(query);
					sqlQuery.executeUpdate();

					if (!users.isEmpty()) {
						for (Long userID : users.keySet()) {
							String password = users.get(userID).keySet().iterator().next();
							String salt = users.get(userID).get(password);

							query = "UPDATE users SET password = :passwordString, salt = :saltString where id = " + userID;
							sqlQuery = session.createNativeQuery(query);
							sqlQuery.setParameter("passwordString", password);
							sqlQuery.setParameter("saltString", salt);
							sqlQuery.executeUpdate();
						}
					}

					if (activateBranchId > 0) {
						Node node = Node.findById(activateNodeId);
						MessagePointStartUp.startupSchema(node);
						Node.changeStatus(activateNodeId, Node.NODE_STATUS_ONLINE);
						Node.activateNode(activateNodeId, true);
						Node.updateSystemProperties(activateNodeId);
						node.auditAction(AuditActionType.ID_ENABLING);

						Branch.changeStatus(activateBranchId, Branch.DCS_STATUS_ONLINE);
						Branch.activateBranch(activateBranchId, true);
					} else {
						Node.activateNode(activateNodeId, true);
						if (isPodMaster)
							Node.changeStatus(activateNodeId, Node.NODE_STATUS_ONLINE);
						else
							Node.changeStatus(activateNodeId, Node.NODE_STATUS_OFFLINE, true);
					}

				}

				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationServerNameSpace, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationServerWebContext, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationServerProtocol, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationServerPort, session);

				updateOrAddSystemProperty(SystemPropertyKeys.SSO.KEY_SSOPingAdapterId, session);

				updateOrAddSystemProperty(SystemPropertyKeys.BackendComponentRepository.KEY_S3Bucket, session);
				updateOrAddSystemProperty(SystemPropertyKeys.BackendComponentRepository.KEY_AccessKeyId, session);
				updateOrAddSystemProperty(SystemPropertyKeys.BackendComponentRepository.KEY_SecretAccessKey, session);

				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationCallbackProtocol, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationCallbackNamespace, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationCallbackPort, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationCallbackWebContext, session);
				updateOrAddSystemProperty(SystemPropertyKeys.ApplicationServer.KEY_ApplicationCallbackQueryParams, session);

				SystemPropertyManager.getInstance().clearPropertyCache(null);
				SystemPropertyManager.getInstance().clearPropertyCache(currentSchema);

				activateNodeId = 0;
				activateBranchId = 0;

				log.info("Ending task initialize the schema: " + currentSchema);
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

		} catch (Exception e) {
			if (activateNodeId > 0)
				Node.changeStatus(activateNodeId, Node.NODE_STATUS_INITIALIZATION_ERROR);

			if (activateBranchId > 0)
				Branch.changeStatus(activateBranchId, Branch.DCS_STATUS_INITIALIZATION_ERROR);

			activateNodeId = 0;
			activateBranchId = 0;

			try {
				log.error("Exception occur in InitializeDatabase(): ", e);
				if (session != null) {
					Transaction tx = session.getTransaction();
					if (tx != null && tx.isActive()) {
						tx.rollback();
					}
				}
			} catch (Exception ex) {
				log.error("Exception occur when trying to rollback the transaction ,  " + ex.getMessage());
			}
		}
	}

	private void updateOrAddSystemProperty(String propertyKey, Session session) {

		String runtimePropValue = PropertyUtils.getRuntimeProperty("app.".concat(propertyKey));

		if (runtimePropValue != null) {
			SystemProperty property = SystemProperty.findByKey(propertyKey);

			String query = null;

			if (property != null) {
				query = "UPDATE system_property SET prop_value = ? WHERE prop_key = ?";
			} else {
				query = "INSERT into system_property (prop_value, prop_key, readonly, id, prop_desc) VALUES (?, ?, ?, ?, '')";
			}

			NativeQuery nativeQuery = session.createNativeQuery(query);

			nativeQuery.setParameter(1, runtimePropValue);
			nativeQuery.setParameter(2, propertyKey);



			if (property == null) {

				long nextId = ((Number) session.createNativeQuery("SELECT MAX(id) + 1 as next_id from system_property").getSingleResult()).longValue();
				nativeQuery.setParameter(3, false);
				nativeQuery.setParameter(4, nextId);
			}

			nativeQuery.executeUpdate();


		}
	}

	private boolean runSQLScript(Session session, final String sqlFilePath, boolean enableLogSQL) throws Exception
	{
		final boolean[] error = {false};
		session.doWork(new Work() {
	        @Override
	        public void execute(Connection conn) {
				String sqlQuery = "";
		
				try
				{
					File file = new File(sqlFilePath);
					FileInputStream inputStream = new FileInputStream(file); 
					InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
					BufferedReader reader = new BufferedReader(inputStreamReader);
					
					log.info("runSQLScript from file: " + file.getAbsolutePath());

		            Statement sqlStatement = conn.createStatement();

					String line = reader.readLine();
					while (line != null)
					{
						sqlQuery = line.replace(";","").trim();
						if (!sqlQuery.isEmpty())
						{
							try
							{
								int r = sqlStatement.executeUpdate(sqlQuery);
								if (enableLogSQL && r > 0)
									log.error("runSQLScript sqlQuery return: " + r + " - " + sqlQuery);
							}
							catch (SQLException sqlException)
							{
								if (!sqlQuery.contains("drop ")) {
									error[0] = true;
									log.error("runSQLScript sqlQuery: " + sqlQuery + " - " + sqlException.getMessage());
								}
							}
						}
						line = reader.readLine(); ;
					}
					
					sqlStatement.close();
					
					reader.close();
					inputStreamReader.close();
					inputStream.close();
				}
				catch (Exception e)
				{
					log.error("runSQLScript exception sqlQuery: " + sqlQuery + " - " + e.getMessage());
				}
	        }
		});

		return error[0];
	}

	private void runSQLScriptFromStringBuilder(Session session, final StringBuilder sqlScript, final String sqlScriptDescription) throws Exception
	{
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) {
				try
				{
					log.info("runSQLScript: " + sqlScriptDescription);

					Statement sqlStatement = conn.createStatement();
					sqlStatement.executeUpdate(sqlScript.toString());
					sqlStatement.close();
				}
				catch (Exception e)
				{
					log.error("runSQLScriptFromScriptBuilder: " + sqlScript.toString() + " - " + e.getMessage());
				}
			}
		});
	}

	public static boolean isSourceFileOutOfSync(File file) {

		if (file != null) {

            Session session = HibernateUtil.getManager().getSession();
            NativeQuery query = session.createNativeQuery("SELECT version FROM data_migration_info_static WHERE filename = :filename");
            query.setParameter("filename", file.getName());
            query.addScalar("version");

            Object version = query.uniqueResult();
            if (version != null) {
				return !StaticTableUtils.getFileMD5Checksum(file).equals(version.toString());
            }
		}

		return true;
	}
	
	private boolean isRebuild() {
		return dbtask.equalsIgnoreCase("rebuild");
	}
	
	public String getDbTask()
	{
		return dbtask;
	}
	public void setDbTask(String task)
	{
		dbtask = task;
	}
	
	public String getDbFlavor()
	{
		return dbflavor;
	}
	public void setDbFlavor(String flavor)
	{
		dbflavor = flavor;
	}
   
	public File getWebInfDir()
	{
		return webInfDir;
	}
	public void setWebInfDir(File file)
	{
		webInfDir = file;
	}
   
	public long getActivateNode()
	{
		return activateNodeId;
	}
	public void setActivateNode(long value)
	{
		activateNodeId = value;
	}
   
	public long getActivateBranch()
	{
		return activateBranchId;
	}
	public void setActivateBranch(long value)
	{
		activateBranchId = value;
	}
}
