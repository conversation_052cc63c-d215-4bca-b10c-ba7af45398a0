package com.prinova.messagepoint.initialization;

import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.Role;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.workgroup.Workgroup;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.ActivateDeactivateUserProxy;
import com.prinova.messagepoint.platform.services.admin.CreateOrUpdateNodesService;
import com.prinova.messagepoint.platform.services.admin.NewUserProxy;
import com.prinova.messagepoint.platform.services.admin.UserCreateOrUpdateService;
import com.prinova.messagepoint.platform.services.admin.UserProxy;
import com.prinova.messagepoint.platform.services.common.CreateOrUpdatePasswordRecoveryService;
import com.prinova.messagepoint.platform.services.common.CreateOrUpdatePasswordRecoveryServiceRequest;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import com.prinova.messagepoint.util.UserUtil;

public class CreateSandboxCopyWithUser extends CopySchema
{
	private static final Log log = LogUtil.getLog(CreateSandboxCopyWithUser.class);

	private Node toNode;
	private final User user;
	private String roleName;
	
	private static final String ACTIVATE_MODE	= "1";
	
	/// Note that the toNode must be a complete object, containing parent node, branch, schema name, etc.
	public CreateSandboxCopyWithUser( Node fromNode, String fromSchemaName, Node toNode, User newUser, String roleName)
	{
		super(fromNode, fromSchemaName, toNode, false);
		
		this.toNode = toNode;
		user = newUser;
		this.roleName = roleName;
	}

	@Override
	public void performMainProcessing() 
	{
		SessionHolder mainSessionHolder = null;
		boolean switchSession = false; 
		try
		{
			createSchema();
			toNode = Node.findById(toNode.getId()); 
			long nodeId = toNode.getId();
			
			Node.changeStatus(nodeId, Node.NODE_STATUS_IN_INITIALIZING_PROCESS);
			Node.activateNode(nodeId, false);

			super.copySchema();
			super.createSyncHistory();
			super.copyFileroot();
			if ( user != null )
			{
				user.setDefaultNodeId(nodeId);
				user.setWorkgroupId(Workgroup.DEFAULT_WORKGROUP);
				user.setAccountEnabled(true);

				String url = ApplicationUtil.buildFullyQualifiedServerURLForEmail();

				createUser(url);

				User domainUser = User.findByUsername(user.getUsername());
				Branch currentBranch = Node.getCurrentNode().getBranch();
				
				domainUser.setAccountEnabled(false);
				
				Role domainRole = Role.findById(Role.RESTRICTED_ACCESS_ROLE);
				domainUser.getRoles().add(domainRole);
				domainUser.setLicensedType(domainUser.getLicenceTypeByRole());
				domainUser.save();
				
				domainRole.getUsers().add(domainUser);
				domainRole.save();

		        // switch DB session to requested schema
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(toNode.getSchemaName());
				switchSession = true;

				// create new user, set role to roleId
				User newUser = UserUtil.createNewUser(domainUser);
				String idPUserGuid = domainUser.getGuid();
				String idPIdentifier = currentBranch.getDcsNode().getGuid();
				
				newUser.setAccountEnabled(true);
				newUser.setIdPType(User.IDP_TYPE_MP_INSTANCE_USER);
				newUser.setIdPIdentifier(idPIdentifier);
				newUser.setIdPUserGuid(idPUserGuid);

				Role role = null;
				if (roleName != null && !roleName.isEmpty())
				{
					role = Role.findByName(roleName);
				}
				
				if (role == null)
				{
					role = Role.findById(Role.DEFAULT_ACCESS_ROLE);
				}
				newUser.getRoles().add(role);
				newUser.setLicensedType(newUser.getLicenceTypeByRole());
				newUser.setHiddenSupervisor(false);
				newUser.save();
				
				role.getUsers().add(newUser);
				role.save();

				HibernateUtil.getManager().restoreSession(mainSessionHolder);
				switchSession = false;
			}

			// bring node back online
			super.changeNodeState(toNode, CreateOrUpdateNodesService.ACTION_ACTIVATE);
		}
		catch( Exception ex )
		{
			// set DB schema back to the original one 
			if (switchSession)
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			Node.changeStatus(toNode.getId(), Node.NODE_STATUS_INITIALIZATION_ERROR);
			Node.activateNode(toNode.getId(), false);
			LogUtil.getLog(this.getClass()).error("Caught exception: ", ex);
		}
	}

	private void createSchema()
	{
		int nodeVersion = Node.getSchemaDBVersion(toNode.getSchemaName());
		
		if (nodeVersion == -2)
		{
			try {
				HibernateUtil.getManager().callCreateUser(toNode.getSchemaName(), MessagepointMultiTenantConnectionProvider.getPodMasterPassword());
				HibernateUtil.getManager().callFixUserSchemaPermissions(toNode.getSchemaName());
			}
			catch(Exception e) {
				log.error("Web service exception:", e);
			}
		}
		
		ServiceExecutionContext ctx = CreateOrUpdateNodesService.createContext(toNode, toNode.getParentNode().getId(), CreateOrUpdateNodesService.ACTION_CREATE, toNode.getSchemaName());
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateNodesService.SERVICE_NAME, CreateOrUpdateNodesService.class);
		service.execute(ctx);

		ServiceResponse serviceResponse = ctx.getResponse();
		if ( !serviceResponse.isSuccessful() )
			throw new RuntimeException("Instance creation error");
	}

	private void createUser(String url)
	{
		NewUserProxy proxy = new NewUserProxy(user);

		//execute the service
		ServiceExecutionContext ctx = UserCreateOrUpdateService.createContext(proxy, UserUtil.getPrincipalUserId());
		Service updateService = MessagepointServiceFactory.getInstance().lookupService(UserCreateOrUpdateService.SERVICE_NAME, UserCreateOrUpdateService.class);
		updateService.execute(ctx);

		ServiceResponse serviceResponse = ctx.getResponse();
		if (!serviceResponse.isSuccessful()) {
			throw new RuntimeException("Runtime exception in Web Service createUser \n");
		}

		User newUser = User.findByUsername(user.getUsername());
		
		UserProxy userProxy = new ActivateDeactivateUserProxy(newUser, true, ACTIVATE_MODE);
		
		ServiceExecutionContext context = CreateOrUpdatePasswordRecoveryService.createContext(newUser.getEmail(), CreateOrUpdatePasswordRecoveryServiceRequest.PR_REQUESTTYPE_ACTIVATION, url, newUser.getId(), 0, newUser.getSchemaOfThisUser());
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdatePasswordRecoveryService.SERVICE_NAME, CreateOrUpdatePasswordRecoveryService.class);
		service.execute(context);
		serviceResponse = context.getResponse();

		if (!serviceResponse.isSuccessful()) {
			throw new RuntimeException("Runtime exception in Web Service createUser \n");
		}

		//execute the service
		ctx = UserCreateOrUpdateService.createContext(userProxy, UserUtil.getPrincipalUserId(), true, null);
		updateService = MessagepointServiceFactory.getInstance().lookupService(UserCreateOrUpdateService.SERVICE_NAME, UserCreateOrUpdateService.class);
		updateService.execute(ctx);
		
		serviceResponse = ctx.getResponse();
		if(!serviceResponse.isSuccessful()){
			throw new RuntimeException("Runtime exception in Web Service createUser \n");
 		}
		
	}
}
