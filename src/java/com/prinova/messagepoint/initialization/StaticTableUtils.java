package com.prinova.messagepoint.initialization;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.function.Predicate;

public class StaticTableUtils {

    public static boolean isStaticTableRebuildRequired(File webInfDir, Predicate<File> isSourceFileOutOfSync) {
        List<File> files = new ArrayList<>();
        files.add(new File(webInfDir, "initialization/securitymetadata.xml"));
        files.add(new File(webInfDir, "initialization/generalmetadata.xml"));
        files.add(new File(webInfDir, "initialization/navworkflowmetadata.xml"));
        files.add(new File(webInfDir, "initialization/touchpointmetadata.xml"));

        return isRebuildRequired(files, isSourceFileOutOfSync);
    }

    public static boolean isForeignKeyRebuildRequired(File webInfDir, Predicate<File> isSourceFileOutOfSync) {
        List<File> files = new ArrayList<>();
        files.add(new File(webInfDir, "initialization/database/postgres/messagepoint-fk-ddl.sql"));
        files.add(new File(webInfDir, "initialization/database/postgres/messagepoint_report-fk-ddl.sql"));

        return isRebuildRequired(files, isSourceFileOutOfSync);
    }

    public static boolean isStaticTableRebuildRequired(HashMap<String, String> staticDataVersions) throws IOException {

        HashMap<String, String> versions = new HashMap<>();

        if (staticDataVersions.containsKey("messagepoint-schedule.xml")) {
            versions.put("messagepoint-schedule.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/WEB-INF/messagepoint-schedule.xml"))));
        }

        versions.put("securitymetadata.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/securitymetadata.xml"))));
        versions.put("generalmetadata.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/generalmetadata.xml"))));
        versions.put("navworkflowmetadata.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/navworkflowmetadata.xml"))));
        versions.put("touchpointmetadata.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/touchpointmetadata.xml"))));

        versions.put("messagepoint-fk-ddl.sql", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/database/postgres/messagepoint-fk-ddl.sql"))));
        versions.put("messagepoint_report-fk-ddl.sql.xml", DigestUtils.md5Hex(IOUtils.toString(StaticTableUtils.class.getResourceAsStream("/initialization/database/postgres/messagepoint_report-fk-ddl.sql"))));

        for (String file : versions.keySet()) {
            if (!staticDataVersions.containsKey(file) || !staticDataVersions.get(file).equals(versions.get(file))) {
                return true;
            }
        }

        return false;
    }

    private static boolean isRebuildRequired(List<File> files, Predicate<File> isSourceFileOutOfSync) {
        for (File file : files) {
            if (isSourceFileOutOfSync.test(file)) {
                return true;
            }
        }

        return false;
    }

    public static String getFileMD5Checksum(File file) {
        String md5 = StringUtils.EMPTY;

        try {
            if (file != null) {
                md5 = DigestUtils.md5Hex(FileUtils.readFileToString(file));
            }
        } catch (IOException ex) {
        }

        return md5;
    }
}
