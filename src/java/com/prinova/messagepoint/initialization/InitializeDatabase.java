package com.prinova.messagepoint.initialization;

import com.prinova.licence.common.Constants;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.ConfigUtils;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.hibernate.query.NativeQuery;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.mock.web.MockServletContext;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.context.support.XmlWebApplicationContext;

import java.io.File;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Loads the initial data to the messagepoint and reports database.
 * 
 * Access to the messagepoint web application is controlled via a system property {@link #KEY_SYS_PROP_DB_INIT_BASE_DIR}.
 * The default value is {@link #DEFAULT_DB_INIT_BASE_DIR}.
 * 
 * A different location can be specified by setting the {@link #KEY_SYS_PROP_DB_INIT_BASE_DIR} system property.
 * 
 */
public class InitializeDatabase {

	public static final String KEY_SYS_PROP_DB_INIT_BASE_DIR = "messagepoint.init.base.dir";
	private static final String DEFAULT_DB_INIT_BASE_DIR = "src" + File.separator + "web";
	private List<Long> branchIds = new ArrayList<>();

	private Options options;

	public static void main(String[] args) throws Exception {
		ConfigUtils.loadLog4JConfig();
		Options opt = new Options(args);
		new InitializeDatabase(opt);
	}

	public InitializeDatabase(Options opt) throws Exception {
		options = opt;
		File baseDir = getBaseDir();
		File webinfDir = getWebInfDir();
		LogUtil.getLog(InitializeDatabase.class).info("InitializeDatabase...");

		String[] conxtextXmlFiles =  {
				"file:" + new File(webinfDir, "messagepoint-servlet.xml").getAbsolutePath(),
				"file:" + new File(webinfDir, "applicationContext-security.xml").getAbsolutePath(),
//				"file:" + new File(webinfDir, "applicationContext-common-business.xml").getAbsolutePath(),
				"file:" + new File(webinfDir + File.separator + "initialization" + File.separator
								+ "applicationContext-initialization.xml").getAbsolutePath() };

		initContext(conxtextXmlFiles, baseDir.getAbsolutePath());

		// Use the initialization hibernate manager as the default persistence manager.
		// Thus we need to replace the original hibernate manager with ours in the ApplicationUtil
		
		HibernateObjectManager initManager = ApplicationUtil.getBean("initializationHibernateObjectManager", HibernateObjectManager.class);
		HibernateUtil.setManager(initManager);

		InitializeSchema task = new InitializeSchema();
		
		task.setDbTask(options.getDbTask());
		task.setDbFlavor("postgres");
		task.setWebInfDir(webinfDir);

		if (options.getDbSchema() != null && !options.getDbSchema().equalsIgnoreCase("${db.messagepoint.schema}") && !options.getDbSchema().isEmpty())
		{
			task.setTenantIdentifier(options.getDbSchema());
			LogUtil.getLog(InitializeDatabase.class).info("Initialize (" + options.getDbTask() + ") entered database shema = " + options.getDbSchema());
			task.run();
		}
		else
		{
			// the task will update default schema (db.messagepoint.user) and all its children nodes (schemas)
			// if it's rebuild task, we don't want to initialize the children nodes
			//
			if (options.getDbTask().equalsIgnoreCase("rebuild"))
			{
				// only default schema (db.messagepoint.user) will be initialized 
				//
				task.setTenantIdentifier(null);
				LogUtil.getLog(InitializeDatabase.class).info("Rebuild ONLY default (db.messagepoint.user) database shema.");
				task.run();
			}
			else
			{
				// update all enabled schemas (off-line, on-line and schema for migration)
				//
				task.setTenantIdentifier("allEnabled");
				LogUtil.getLog(InitializeDatabase.class).info("Update ALL enabled database shemas.");
				task.run();
				
				LogUtil.getLog(InitializeDatabase.class).info("Number of migrated database schemas: " + Node.getAllEnabledSchemas().size());
				
				if (Branch.isRequiredDcsMigration())
				{
					LogUtil.getLog(InitializeDatabase.class).info("Starting DCS migration.");
					Branch podMasterBranch = Branch.findPODMaster(); 
					MigrateDcsDatabases dcsTask = new MigrateDcsDatabases();
					dcsTask.setActivateBranch(podMasterBranch.getId());
					dcsTask.setDbTask("dcsmigrate");
					dcsTask.setDbFlavor("postgres");
					dcsTask.setWebInfDir(getWebInfDir());
					dcsTask.setTenantIdentifier(null);

					SessionHolder mainSessionHolder = null;
					Map<String, String> changeToGUIDsMap = new HashMap<>();
					try {
						mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);

						dcsTask.performMainProcessing();

						branchIds = dcsTask.getBranchIDs();

						dcsMigrateEachBranch(podMasterBranch.getId());

						LogUtil.getLog(InitializeDatabase.class).info("Updating GUIDs");

						List<MessagepointCriterion> critList = new ArrayList<>();
						critList.add(MessagepointRestrictions.isNotNull("parentBranch"));
						critList.add(MessagepointRestrictions.isNotNull("dcsSchemaName"));
						critList.add(MessagepointRestrictions.or(MessagepointRestrictions.eq("status", Branch.DCS_STATUS_ONLINE), MessagepointRestrictions.eq("status", Branch.DCS_STATUS_OFFLINE)));

						List<Branch> list = HibernateUtil.getManager().getObjectsAdvanced(Branch.class, critList);

						for (Branch branch : list) {
							Node defaultNode = branch.getDefaultNode();
							Node dcsNode = branch.getDcsNode();
							if (defaultNode != null && dcsNode != null)
								changeToGUIDsMap.put(defaultNode.getGuid(), dcsNode.getGuid());
						}
					} finally {
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
					}
					
					updateBranchNodesIdpIdentifiers(changeToGUIDsMap, podMasterBranch.getId());

					LogUtil.getLog(InitializeDatabase.class).info("Finished DCS migration.");
				}
			}
		}
		
		LogUtil.getLog(InitializeDatabase.class).info("Finished Migration / Initialization Database task...");
	}

	public static File getBaseDir() {
		return new File(System.getProperty(KEY_SYS_PROP_DB_INIT_BASE_DIR));
	}

	public static File getWebInfDir() {
		return new File(getBaseDir(), "WEB-INF");
	}

	/**
	 * Creates a simple WebApplicationContext object. We need an web application context since Our beans are
	 */
    public static XmlWebApplicationContext initContext(String[] conxtextXmlFiles, String baseDir) throws Exception {
		XmlWebApplicationContext context = new XmlWebApplicationContext();
		context.setClassLoader(InitializeDatabase.class.getClassLoader());
		context.setConfigLocations(conxtextXmlFiles);
		context.setServletContext(new MockServletContext(new ResourceLoader(baseDir)));
		context.refresh();
		return context;
	}

	/**
	 * A simple resource loader that loads resources relative to the {@link #baseFolder}
	 */
	static class ResourceLoader implements org.springframework.core.io.ResourceLoader {
		File baseFolder;

		public ResourceLoader(String base) {
			baseFolder = new File(base);
		}

		public ClassLoader getClassLoader() {
			throw new RuntimeException("method not permitted.");
		}

		public Resource getResource(String location) {
			return new FileSystemResource(new File(baseFolder, location));
		}
	}

	// static initialization
	static {
		if (!System.getProperties().containsKey(KEY_SYS_PROP_DB_INIT_BASE_DIR)) {
			System.setProperty(KEY_SYS_PROP_DB_INIT_BASE_DIR, DEFAULT_DB_INIT_BASE_DIR); // defaults to "src/web"
		}

		// Set the nfo file location to the temp directory.
		// Otherwise it will try to write to the src/web/WEB-INF directory every time.
		System.setProperty(Constants.KEY_SYS_PROP_NFO_FILE, System.getProperty("java.io.tmpdir") + File.separator
				+ Constants.LIC_NFO_NAME);
	}

	private void dcsMigrateEachBranch(Long branchId) 
	{
		if(branchId == null)
			return;
		
		Branch currentBranch = Branch.findById(branchId);
		if (currentBranch == null)
			return;
		
		LogUtil.getLog(InitializeDatabase.class).info("Starting initializing DCS node for domain: " + currentBranch.getName());
		
		String currentSchema = null;
		List<Branch> children = currentBranch.getAllChildren();
		if(!currentBranch.isMaster() && branchIds.contains(currentBranch.getId())){
			Node existingDefault = currentBranch.getDefaultNode();
			String existingDefaultSchemaName = existingDefault.getSchemaName();
			currentSchema = currentBranch.getDcsSchemaName();
			Node dcsNode = Node.findBySchema(currentSchema);
			int nodeVersion = Node.getSchemaDBVersion(currentSchema);
			try {
				if (nodeVersion < 0 || dcsNode.getStatus() == Node.NODE_STATUS_NOT_INITIALIZED) {
					if (nodeVersion == -2) {
						try {
							LogUtil.getLog(InitializeDatabase.class).info("Request create DB schema = " + currentSchema);
							HibernateUtil.getManager().callCreateUser(currentSchema, MessagepointMultiTenantConnectionProvider.getPodMasterPassword());
							HibernateUtil.getManager().callFixUserSchemaPermissions(currentSchema);
						} catch(Exception e) {
							LogUtil.getLog(InitializeDatabase.class).info("There was an error: " + e);
						}
					}
				
					LogUtil.getLog(InitializeDatabase.class).info("Initializing DB schema " + currentSchema);
					InitializeSchema nodeTask = new InitializeSchema();
					nodeTask.setDbTask("rebuild");
					nodeTask.setDbFlavor("postgres");
					nodeTask.setWebInfDir(new File(ApplicationUtil.getRootPath() + "/WEB-INF"));
					nodeTask.setActivateNode(dcsNode.getId());
					nodeTask.setTenantIdentifier(currentSchema);

					SessionHolder mainSessionHolder = null;
					try {
						mainSessionHolder = HibernateUtil.getManager().openTemporarySession(currentSchema);
						nodeTask.performMainProcessing();
					} finally {
						HibernateUtil.getManager().restoreSession(mainSessionHolder);
					}
				}
				
				int nodeStatus = Node.NODE_STATUS_ONLINE;
				int branchStatus = Branch.DCS_STATUS_ONLINE;
				
				if (existingDefault.isAccessible())
				{
					LogUtil.getLog(InitializeDatabase.class).info("Copy data from DB schema " + existingDefaultSchemaName + " to " + currentBranch.getDcsSchemaName());
					
					if (!copyLocalDataFromExistingDefaultNode(currentBranch.getDcsSchemaName(), existingDefaultSchemaName))
					{
						nodeStatus = Node.NODE_STATUS_NOT_INITIALIZED;
						branchStatus = Branch.DCS_STATUS_MIGRATION_ERROR;
					}
				}

				SessionHolder mainSessionHolder = null;
				try {
					mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);

					// Set status of the node and branch

					Node node = HibernateUtil.getManager().getObject(Node.class, dcsNode.getId());

					node.setEnabled(nodeStatus == Node.NODE_STATUS_ONLINE);
					node.setStatus(nodeStatus);
					node.save();

					Branch branch = HibernateUtil.getManager().getObject(Branch.class, currentBranch.getId());

					branch.setEnabled(branchStatus == Branch.DCS_STATUS_ONLINE);
					branch.setStatus(branchStatus);
					branch.save();

					HibernateUtil.getManager().getSession().flush();
				} finally {
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}
			} catch( Exception ex ) {
				LogUtil.getLog(InitializeDatabase.class).error("Caught exception: ", ex);
			}
		}
		for(Branch child:children){
			dcsMigrateEachBranch(child.getId());
		}
	}
	
	
	private void updateBranchNodesIdpIdentifiers(Map<String, String> changeToGUIDsMap, Long branchId) 
	{
		if(branchId == null)
			return;
		
		Branch currentBranch = Branch.findById(branchId);
		if (currentBranch == null)
			return;

		SessionHolder sessionHolder = null;
		try {
			sessionHolder = HibernateUtil.getManager().openTemporarySession(null);

			List<Branch> children = currentBranch.getAllChildren();
			List<String> sqls = new ArrayList<>();
			String updateExternalUserIdentifier = "";
			for (String key : changeToGUIDsMap.keySet()) {
				updateExternalUserIdentifier = "UPDATE users set idp_identifier = '" + changeToGUIDsMap.get(key) + "' where idp_type IN (0, 1, 2, 9) and idp_identifier = '" + key + "'";
				sqls.add(updateExternalUserIdentifier);
			}

			List<Node> allBranchNodes = currentBranch.getAllNodes(true); // with DCS

			for (Node branchNode : allBranchNodes) {
				if (branchNode.getSchemaName() == null || branchNode.getSchemaName().isEmpty() || !branchNode.isAccessible())
					continue;

				SessionHolder newSessionHolder = null;
				try {
					newSessionHolder = HibernateUtil.getManager().openTemporarySession(branchNode.getSchemaName());
					for (String sql : sqls) {
						LogUtil.getLog(InitializeDatabase.class).info("Modify schema " + branchNode.getSchemaName() + " sql=" + sql);
						NativeQuery identifierUpdateQuery = HibernateUtil.getManager().getSession().createNativeQuery(sql);
						identifierUpdateQuery.executeUpdate();
					}

					HibernateUtil.getManager().getSession().flush();
				} finally {
					HibernateUtil.getManager().restoreSession(newSessionHolder);
				}
			}

			for (Branch child : children) {
				updateBranchNodesIdpIdentifiers(changeToGUIDsMap, child.getId());
			}

			HibernateUtil.getManager().getSession().flush();
		} finally {
			HibernateUtil.getManager().restoreSession(sessionHolder);
		}
	}
		
	@SuppressWarnings("unchecked")
	private static boolean copyLocalDataFromExistingDefaultNode(String toSchemaName, String existingDefaultSchemaName) 
	{
		boolean result = true;
		boolean isPostgres = MessagepointMultiTenantConnectionProvider.isPostgres();
		
		HibernateObjectManager hom = HibernateUtil.getManager();
		
		String[] tableNames = {"users", "password_history", "password_recovery", "role", "role_permission", "user_permission", "user_role", "application_locale", 
				"audit_event", "permission", "security_settings", "permission_category", "category_group", "notification_settings",
				"background_themes", "branch", "node", "domain", "domain_pod", "licence", "licence_application", "licence_audit",
				"licence_audit_action", "licence_change_reasons", "licence_customer", "licence_customer_keypair", "licence_history",
				"licence_history_reason", "licence_module", "licence_representative", "licence_resource", "licence_server", "licence_server_ip_address",
				"mp_licence", "pod", "redirection_info", "system_property"}; 
		String getTableDataQuery = "";
		String tableInsertQuery = "";
		for (String tableName:tableNames) {
			SessionHolder mainSessionHolder = null;
			String[] paramNames;
			String[] types;
			String[] nullables;
			Object[] defaultValues;
			String queryString = "";
			String queryParamString = "";
			List<Object> tableRows;
			try {
				mainSessionHolder = hom.openTemporarySession(existingDefaultSchemaName);

				String tableQuery;
				if (isPostgres) {
					tableQuery = "select COLUMN_NAME, DATA_TYPE, IS_NULLABLE as nullable, column_default as DATA_DEFAULT from information_schema.columns where table_schema = :schema_name and table_name = :table_name";
				} else {
					tableQuery = "select COLUMN_NAME, DATA_TYPE, NULLABLE, DATA_DEFAULT from ALL_TAB_COLUMNS where owner = :schema_name and TABLE_NAME = :table_name";
				}

				NativeQuery tableQuery_ = hom.getSession().createNativeQuery(tableQuery);
				if (isPostgres) {
					tableQuery_.setParameter("schema_name", existingDefaultSchemaName.toLowerCase());
					tableQuery_.setParameter("table_name", tableName.toLowerCase());
				} else {
					tableQuery_.setParameter("schema_name", existingDefaultSchemaName.toUpperCase());
					tableQuery_.setParameter("table_name", tableName.toUpperCase());
				}

				tableQuery_.executeUpdate();
				List<Object> objects = tableQuery_.list();

				int length = objects.size();
				paramNames = new String[length];
				types = new String[length];
				nullables = new String[length];
				defaultValues = new Object[length];

				//int userIdIndex = 0;
				for (int i = 0; i < length; i++) {
					Object[] rsCommentsItems = (Object[]) objects.get(i);
					paramNames[i] = rsCommentsItems[0].toString().toLowerCase();
//				if(tableName.equals("users") && paramNames[i].equals("id")){
//					userIdIndex = i;
//				}
					types[i] = rsCommentsItems[1].toString().toLowerCase();
					nullables[i] = rsCommentsItems[2].toString();
					if (rsCommentsItems[3] == null)
						defaultValues[i] = null;
					else
						defaultValues[i] = rsCommentsItems[3];
					//LogUtil.getLog(InitializeDatabase.class).info(rsCommentsItems[0] + "//" + rsCommentsItems[1] + "//" + rsCommentsItems[2] + "//" + rsCommentsItems[3]);
				}

				for (int i = 0; i < paramNames.length; i++) {
					if (!queryString.isEmpty())
						queryString = queryString.concat(", ");
					queryString = queryString.concat(paramNames[i]);

					if (!queryParamString.isEmpty())
						queryParamString = queryParamString.concat(", ");
					queryParamString = queryParamString.concat(":" + paramNames[i]);
				}
				getTableDataQuery = "SELECT " + queryString + " FROM " + tableName;

				NativeQuery sqlCommentsQuery_ = HibernateUtil.getManager().getSession().createNativeQuery(getTableDataQuery);
				sqlCommentsQuery_.executeUpdate();
				tableRows = sqlCommentsQuery_.list();
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}
		    
			if (!tableRows.isEmpty()) {
				SessionHolder newSessionHolder = null;
				try {
					newSessionHolder = HibernateUtil.getManager().openTemporarySession(toSchemaName);
					// Delete existing records
					String sqlDeleteCommentsQueryString = "DELETE from " + tableName;
					NativeQuery sqlDeleteCommentsQuery = HibernateUtil.getManager().getSession().createNativeQuery(sqlDeleteCommentsQueryString);
					sqlDeleteCommentsQuery.executeUpdate();

					tableInsertQuery = "INSERT INTO " + toSchemaName + "." + tableName + " (" + queryString + ") VALUES (" + queryParamString + ")";

					for (Object commentRs : tableRows) {
						Object[] rsCommentsItems = (Object[]) commentRs;
						NativeQuery sqlInsertQuery = HibernateUtil.getManager().getSession().createNativeQuery(tableInsertQuery);
						boolean needToUpdateCategoryId = false;
						boolean needToUpdateUpdatedId = false;
						boolean needToUpdateCreatedId = false;
						for (int i = 0; i < paramNames.length; i++) {

							if (types[i].equalsIgnoreCase("DATE")) {
								sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? (Date) rsCommentsItems[i] : null));
							} else if (types[i].equalsIgnoreCase("TIMESTAMP(6)")) {
								if (rsCommentsItems[i] != null && (rsCommentsItems[i] != null && !rsCommentsItems[i].equals("null") && !rsCommentsItems[i].toString().trim().isEmpty())) {

									sqlInsertQuery.setParameter(paramNames[i], (Date) rsCommentsItems[i]);
								} else {
									sqlInsertQuery.setParameter(paramNames[i], null);
								}
							} else if (types[i].equalsIgnoreCase(isPostgres ? "NUMERIC" : "NUMBER")) {
//    	      					LogUtil.getLog(InitializeDatabase.class).info("number value:" + rsCommentsItems[i] + " " + paramNames[i] + " is this null:" + (rsCommentsItems[i] == null? "yes": "no"));
//          						String list = "count: ";
//          						for(int t=0; t < rsCommentsItems.length; t++){
//          							list = list.concat(", " + (rsCommentsItems[t]));
//          						}
//          						LogUtil.getLog(InitializeDatabase.class).info("table: " + tableName + ", data: " + list.toString());
								if (((tableName.equals("node") || tableName.equals("branch") || tableName.equals("audit_event") || tableName.equals("permission_category")) && paramNames[i].contains("_id"))
										|| paramNames[i].equalsIgnoreCase("category_id")
										|| paramNames[i].equalsIgnoreCase("updated_by_id")
										|| paramNames[i].equalsIgnoreCase("created_by_id")) {
									if (paramNames[i].equalsIgnoreCase("category_id")) {
										needToUpdateCategoryId = true;
									}
									if (paramNames[i].equalsIgnoreCase("updated_by_id")) {
										needToUpdateUpdatedId = true;
									}
									if (paramNames[i].equalsIgnoreCase("created_by_id")) {
										needToUpdateCreatedId = true;
									}
									sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i] : (defaultValues[i] == null ? 0 : defaultValues[i])));
								} else if (tableName.equals("users") && (paramNames[i].equalsIgnoreCase("app_locale_id") || paramNames[i].equalsIgnoreCase("deactiv_reason_id"))) {
									int value = 0;
									if (paramNames[i].equalsIgnoreCase("app_locale_id"))
										value = 1;
									sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i] : (defaultValues[i] == null ? value : defaultValues[i])));
								} else {
									sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i] : (defaultValues[i] == null ? null : defaultValues[i])));
								}
							} else if (types[i].equalsIgnoreCase(isPostgres ? "VARCHAR" : "VARCHAR2")) {
								if (tableName.equals("system_property") && paramNames[i].equalsIgnoreCase("prop_value")) {
									// replace existing schema name to new schema name in prop_value
									sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i].toString().replace(existingDefaultSchemaName.toLowerCase(), toSchemaName.toLowerCase()) : (defaultValues[i] == null ? null : defaultValues[i].toString())));
								} else {
									sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i].toString() : (defaultValues[i] == null ? null : defaultValues[i].toString())));
								}
							} else {
								sqlInsertQuery.setParameter(paramNames[i], (rsCommentsItems[i] != null ? rsCommentsItems[i].toString() : (defaultValues[i] == null ? null : defaultValues[i])));
							}
						}

						try {
							sqlInsertQuery.executeUpdate();
						} catch (Exception ex) {
							LogUtil.getLog(InitializeDatabase.class).error("Caught exception: ", ex);
							LogUtil.getLog(InitializeDatabase.class).error(sqlInsertQuery);

							for (int i = 0; i < paramNames.length; i++) {
								LogUtil.getLog(InitializeDatabase.class).error("parameter = " + paramNames[i] + " type = " + types[i] + " value = " + rsCommentsItems[i]);
							}

							result = false;
						}

						if (tableName.equalsIgnoreCase("node") || tableName.equalsIgnoreCase("branch")) {
							NativeQuery sqlupdateQuery = HibernateUtil.getManager().getSession().createNativeQuery("Update " + tableName + " SET parent_id=null where parent_id=0");
							sqlupdateQuery.executeUpdate();
						}
						if (tableName.equalsIgnoreCase("permission_category")) {
							NativeQuery sqlupdateQuery = HibernateUtil.getManager().getSession().createNativeQuery("Update " + tableName + " SET category_group_id=null where category_group_id=0");
							sqlupdateQuery.executeUpdate();
						}
						if (needToUpdateCategoryId) {
							NativeQuery sqlupdateQuery = HibernateUtil.getManager().getSession().createNativeQuery("Update " + tableName + " SET category_id=null where category_id=0");
							sqlupdateQuery.executeUpdate();
						}
						if (needToUpdateUpdatedId) {
							NativeQuery sqlupdateQuery = HibernateUtil.getManager().getSession().createNativeQuery("Update " + tableName + " SET updated_by_id=null where updated_by_id=0");
							sqlupdateQuery.executeUpdate();
						}
						if (needToUpdateCreatedId) {
							NativeQuery sqlupdateQuery = HibernateUtil.getManager().getSession().createNativeQuery("Update " + tableName + " SET created_by_id=null where created_by_id=0");
							sqlupdateQuery.executeUpdate();
						}
					}
				} finally {
					HibernateUtil.getManager().restoreSession(newSessionHolder);
				}
          	}
		}

		SessionHolder newSessionHolder = null;
		try {
			newSessionHolder = HibernateUtil.getManager().openTemporarySession(toSchemaName);

			tableInsertQuery = "INSERT INTO " + toSchemaName + ".SYSTEM_PROPERTY (ID, PROP_KEY, PROP_VALUE, PROP_DESC, READONLY) VALUES (890, 'dcs.migration.from', '" + existingDefaultSchemaName + "', 'DCS created by migration', 1)";

			NativeQuery sqlInsertQuery = HibernateUtil.getManager().getSession().createNativeQuery(tableInsertQuery);
			sqlInsertQuery.executeUpdate();

			String query;

			if (isPostgres) {
				query = "SELECT last_value as LAST_NUMBER FROM pg_catalog.pg_sequences WHERE schemaname='" + existingDefaultSchemaName.toLowerCase() + "' AND sequencename = 'hibernate_sequence'";
			} else {
				query = "SELECT LAST_NUMBER FROM ALL_SEQUENCES WHERE sequence_owner='" + existingDefaultSchemaName.toUpperCase() + "' AND sequence_name = 'HIBERNATE_SEQUENCE'";
			}
			NativeQuery sqlQuery = hom.getSession().createNativeQuery(query);
			sqlQuery.executeUpdate();

			long last_number_of_default_sequencer = 0;
			List<Object> values = sqlQuery.list();
			if (values != null && !values.isEmpty())
				last_number_of_default_sequencer = ((Number) values.get(0)).longValue();

			if (isPostgres) {
				query = "SELECT last_value as LAST_NUMBER FROM pg_catalog.pg_sequences WHERE schemaname='" + toSchemaName.toLowerCase() + "' AND sequencename = 'hibernate_sequence'";
			} else {
				query = "SELECT LAST_NUMBER FROM ALL_SEQUENCES WHERE sequence_owner='" + toSchemaName.toUpperCase() + "' AND sequence_name = 'HIBERNATE_SEQUENCE'";
			}
			sqlQuery = hom.getSession().createNativeQuery(query);
			sqlQuery.executeUpdate();

			long last_number_of_dcs_sequencer = 0;
			values = sqlQuery.list();
			if (values != null && !values.isEmpty())
				last_number_of_dcs_sequencer = ((Number) values.get(0)).longValue();

			if ((last_number_of_default_sequencer - last_number_of_dcs_sequencer + 7) > 0) {
				query = "ALTER SEQUENCE HIBERNATE_SEQUENCE INCREMENT BY " + (last_number_of_default_sequencer - last_number_of_dcs_sequencer + 7);
				LogUtil.getLog(InitializeDatabase.class).info(query);
				sqlQuery = hom.getSession().createNativeQuery(query);
				sqlQuery.executeUpdate();

				if (isPostgres) {
					sqlQuery = hom.getSession().createNativeQuery("SELECT nextval('hibernate_sequence')");
				} else {
					sqlQuery = hom.getSession().createNativeQuery("SELECT HIBERNATE_SEQUENCE.NEXTVAL FROM DUAL");
				}
				sqlQuery.executeUpdate();

				sqlQuery = hom.getSession().createNativeQuery("ALTER SEQUENCE HIBERNATE_SEQUENCE INCREMENT BY 1");
				sqlQuery.executeUpdate();
			}

			sqlQuery = hom.getSession().createNativeQuery("UPDATE USERS SET IDP_TYPE = 0, IDP_IDENTIFIER = NULL, IDP_USER_GUID = NULL, IDP_SUBJECT = NULL WHERE IDP_TYPE IN (0,1)");
			sqlQuery.executeUpdate();

			sqlQuery = hom.getSession().createNativeQuery("UPDATE USERS SET IDP_TYPE = 2, IDP_SUBJECT = NULL WHERE IDP_TYPE = 9 OR IDP_TYPE = 2");
			sqlQuery.executeUpdate();

			sqlQuery = hom.getSession().createNativeQuery("UPDATE USERS SET IDP_TYPE = 3 WHERE IDP_TYPE = 4");
			sqlQuery.executeUpdate();

			sqlQuery = hom.getSession().createNativeQuery("UPDATE USERS SET WORKGROUP_ID = 1 WHERE ID < 900 OR ID > 999");
			sqlQuery.executeUpdate();

			sqlQuery = hom.getSession().createNativeQuery("UPDATE USERS SET ISENABLED = 0 WHERE ID < 900 OR ID > 999 AND ID != 1");
			sqlQuery.executeUpdate();

			// Removed all external users

			sqlQuery = hom.getSession().createNativeQuery("SELECT ID FROM USERS WHERE IDP_TYPE = 2 OR IDP_TYPE = 9");
			sqlQuery.executeUpdate();

			values = sqlQuery.list();
			if (values != null && !values.isEmpty()) {
				for (Object value : values) {
					long userId = ((BigInteger) value).longValue();
					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM PASSWORD_HISTORY WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM PASSWORD_RECOVERY WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM APPROVAL_USER WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM AUDIT_EVENT_DOC WHERE AUDIT_EVENT_ID IN (SELECT ID FROM AUDIT_EVENT WHERE OBJECT_ID = " + userId + " OR USER_ID = " + userId + ")");
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM AUDIT_EVENT WHERE OBJECT_ID = " + userId + " OR USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM AUDIT_REPORT WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM USER_ROLE WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM USER_PERMISSION WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM TP_SEL_VISIBLE_USER WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM RATIONALIZER_APP_VISIBLE_USER WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM FOLDERS WHERE OWNER_ID = " + userId);
					sqlQuery.executeUpdate();

					//sqlQuery = hom.getSession().createNativeQuery("DELETE FROM SYSTEM_TASK_USER WHERE USER_ID = " + userId);
					//sqlQuery.executeUpdate();

					//sqlQuery = hom.getSession().createNativeQuery("DELETE FROM HISTORY_TASK_USER WHERE USER_ID = " + userId);
					//sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM TASK_USER WHERE USER_ID = " + userId);
					sqlQuery.executeUpdate();

					sqlQuery = hom.getSession().createNativeQuery("DELETE FROM USERS WHERE ID = " + userId);
					sqlQuery.executeUpdate();
				}
			}
		} finally {
			HibernateUtil.getManager().restoreSession(newSessionHolder);
		}

    	return result;
	}

}
