package com.prinova.messagepoint.initialization;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.manager.HibernateObjectManager;
import com.prinova.messagepoint.model.util.SyncTouchpointUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.admin.CreateOrUpdateNodesService;
import com.prinova.messagepoint.util.*;
import org.apache.commons.logging.Log;
import org.springframework.orm.hibernate5.SessionHolder;

import java.io.File;
import java.util.Date;

public class CopySchema extends MessagePointRunnable 
{
	private static final Log log = LogUtil.getLog(CopySchema.class);
	
	private final Node fromNode;
	private final String fromSchemaName;
	private final Node toNode;
    private String dbflavor = "postgres";
    private File webInfDir;
    private boolean createSyncHistoryOnly = false;

	public CopySchema( Node fromNode, String fromSchemaName, Node toNode, boolean createSyncHistoryOnly )
	{
		this.fromNode = fromNode;
		this.fromSchemaName = fromSchemaName;
		this.toNode = toNode;
        this.createSyncHistoryOnly = createSyncHistoryOnly;

        setDbFlavor("postgres");
        setWebInfDir(new File(ApplicationUtil.getRootPath() + "/WEB-INF"));
	}

    public String getDbFlavor()
    {
        return dbflavor;
    }
    public void setDbFlavor(String flavor)
    {
        dbflavor = flavor;
    }

    public File getWebInfDir()
    {
        return webInfDir;
    }
    public void setWebInfDir(File file)
    {
        webInfDir = file;
    }

	@Override
	public void performMainProcessing()
	{
		try
		{
            if(createSyncHistoryOnly) {
                createSyncHistory();
                // bring node back online
                changeNodeState(toNode, CreateOrUpdateNodesService.ACTION_DEACTIVATE);
                return;
            }

            // invoke "copy schema" stored procedure
            copySchema();

            // create sync history
            createSyncHistory();

            // copy fileroot of FromNode to ToNode
            copyFileroot();

			String schemaName = toNode.getSchemaName();
			String fromPath = fromNode.getFileroot().getAbsolutePath();
			String toPath = toNode.getFileroot().getAbsolutePath();

			log.info("Request migrate DB for Node ID = " + toNode.getId() + " schema name = " + schemaName);
			log.info("  From path: " + fromPath);
			log.info("  To path: " + toPath);

			MigrateSchema task = new MigrateSchema();

			task.setFromPath(fromPath);
			task.setToPath(toPath);
			task.setMigrateNodeId(toNode.getId());
			task.setDcsNodeGuid(toNode.getBranch().getDcsNode().getGuid());

			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
				task.performMainProcessing();
			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

			// bring node back online
			changeNodeState(toNode, CreateOrUpdateNodesService.ACTION_ACTIVATE);
		}
		catch( Exception ex )
		{
			Node.changeStatus(toNode.getId(), Node.NODE_STATUS_INITIALIZATION_ERROR);
			Node.activateNode(toNode.getId(), false);
			log.error("Caught exception: ", ex);
		}
	}

	protected boolean changeNodeState( Node node, int action )
	{
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateNodesService.SERVICE_NAME, CreateOrUpdateNodesService.class);
		ServiceExecutionContext context = CreateOrUpdateNodesService.createContext(node, 0, action, node.getSchemaName());

		service.execute(context);

		if ( context.getResponse().hasMessages() ) 
		{
			context.getResponse().logMessages(LogUtil.getLog(this.getClass()));
		}
		return context.getResponse().isSuccessful();
	}

	protected void copyFileroot() throws Exception
	{
		Branch b = Node.getCurrentBranch();

		log.info("Branch: " + b.getName() + ".  Copying fileroot from: " + fromNode.getName() + " to: " + toNode.getName());
		File toFileroot = toNode.getFileroot();
		File fromFileroot = fromNode.getFileroot();

		log.info("  From fileroot: " + fromFileroot);
		log.info("  To fileroot: " + toFileroot);

		FileUtil.deleteDir(toFileroot);
		FileUtil.copyDir(fromFileroot, toFileroot);
	}

	protected void copySchema() throws Exception {
        Branch fromBranch = fromNode.getBranch();
        Branch toBranch = toNode.getBranch();

        log.info("Copying schema from: " + fromBranch.getName() + ":" + fromNode.getName() + " to: " + toBranch.getName() + ":" + toNode.getName());
        String toSchemaName = toNode.getSchemaName();

        HibernateObjectManager hom = HibernateUtil.getManager();

        SyncTouchpointUtil.makeSureHashCalculated(fromSchemaName);

        log.info("Cloning schema from: " + fromSchemaName + " to: " + toSchemaName);
        hom.callCloneSchema(fromSchemaName, toSchemaName, false);

        log.info("Configuring target instance: " + toNode.getName());
		hom.callConfigureUser(toSchemaName, fromNode.getSchemaFilerootRelativePath(true),
				toNode.getSchemaFilerootRelativePath(true));
		hom.callConfigureUser(toSchemaName, fromNode.getSchemaFilerootRelativePath(false),
				toNode.getSchemaFilerootRelativePath(false));

        log.info("Reseting domain users in target instance: " + toNode.getName());
        hom.callResetDomainUsers(toSchemaName, toBranch.getDcsNode().getGuid());
    }

    protected void createSyncHistory() throws Exception {
        Branch fromBranch = fromNode.getBranch();
        Branch toBranch = toNode.getBranch();
        String toSchemaName = toNode.getSchemaName();

        log.info("Create sync history in target instance: " + toNode.getName());
        String scriptPath = webInfDir.getAbsolutePath() + "/initialization/database/" + dbflavor + "/CreateSyncHistoryV5.sql";
        Date syncTime = DateUtil.now();
//        hom.callCreateSyncHistory(scriptPath, fromSchemaName, toSchemaName, syncTime);
        SyncTouchpointUtil.createSyncHistoryOnSchemaCopy(fromSchemaName, toSchemaName, syncTime);
        log.info("Create sync history in target instance: " + toNode.getName() + " finished.");
	}
}