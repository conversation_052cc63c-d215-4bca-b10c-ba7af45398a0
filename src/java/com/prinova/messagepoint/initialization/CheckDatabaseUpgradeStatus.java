package com.prinova.messagepoint.initialization;

import com.prinova.migrate.Migrate;
import com.prinova.migrate.Options;

import java.util.HashMap;

public class CheckDatabaseUpgradeStatus {

    public static void main(String[] args) throws Exception {

        Options options = new Options(args);
        Migrate migrate = Migrate.createInstance(options);

        HashMap<String, String> staticDataVersions = migrate.getDataMigrationInfoStatic();

        if (StaticTableUtils.isStaticTableRebuildRequired(staticDataVersions)) {
            System.out.println("isStaticTableRebuildRequired=true");
            System.exit(1);
        }

        if (!migrate.areAllStepsApplied()) {
            System.out.println("Not all migration steps applied. Migration is required.");
            System.exit(1);
        }

        if (!migrate.areAllReportStepsApplied()) {
            System.out.println("Not all migration report steps applied. Migration is required.");
            System.exit(1);
        }

        if (!migrate.isAppVersionSame()) {
            System.out.println("!migrate.isAppVersionSame()");
            System.exit(1);
        }

        System.exit(0);
    }
}
