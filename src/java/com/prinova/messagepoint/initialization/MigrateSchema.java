package com.prinova.messagepoint.initialization;

import java.math.BigInteger;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.SystemPropertyKeys;
import com.prinova.messagepoint.model.SystemPropertyManager;
import com.prinova.messagepoint.util.HibernateUtil;

public class MigrateSchema extends MessagePointRunnable {
	
	private static final Log log = LogUtil.getLog(MigrateSchema.class);
	
	private String dbtask = "migrate";
	private String fromPath = null;
	private String toPath = null;
	private String dcsNodeGuid = "";
	private long migrateNodeId = 0;
	private boolean isPodMaster = false;
	private boolean enableNodeAfterMigration = true;

	public void performMainProcessing() {
		
		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		Session session = HibernateUtil.getManager().getSession();

		isPodMaster = (currentSchema == null);

		try {
			// Set following parameters in case it's ANT process
			String messagepointFileroot = System.getProperty("messagepoint.fileroot");
			if (messagepointFileroot == null || messagepointFileroot.trim().isEmpty())
				messagepointFileroot = "/messagepoint/mp/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode() + "/fileroot";

			String messagepointQEFileroot = System.getProperty("messagepointQE.fileroot");
			if (messagepointQEFileroot == null || messagepointQEFileroot.trim().isEmpty())
				messagepointQEFileroot = "/messagepoint/declient/" + MessagepointMultiTenantConnectionProvider.getPodMasterCode();

			String compositionEnginesFileroot = System.getProperty("compositionEngines.fileroot");
			if (compositionEnginesFileroot == null || compositionEnginesFileroot.trim().isEmpty())
				compositionEnginesFileroot = "/messagepoint/compEngine";

			String buildXml = System.getProperty("umh.build.xml");
			if (buildXml == null || buildXml.trim().isEmpty())
				buildXml = "/messagepointUMH/build.xml";

			String antHome = System.getProperty("ant.home");
			if (antHome == null || antHome.trim().isEmpty())
				antHome = "/ant";

			if (currentSchema == null)
			{
				if (migrateNodeId > 0)
				{
					currentSchema = MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName();
				}
				else
				{
					currentSchema = (System.getProperty("db.messagepoint.user") == null ? "default" : System.getProperty("db.messagepoint.user") );
				}
			}
			
			// Global folders
			
			String axis2repository = messagepointFileroot + "/repository";
			String outgoingJobs = messagepointFileroot + "/outgoing/";
			//String metadataReports = messagepointFileroot + "/metadataReports/";
			// Web folders
			
			String webRoot = "/dynamiccontent/application/" + currentSchema.toLowerCase() + "/";
			String emailRoot = "/dynamiccontent/email/" + currentSchema.toLowerCase() + "/";
			
			// Email settings
			
			String emailServer = "";
			String emailAccount = "";
			String emailPassword = "";
			String emailPort = "-1";
			String emailSecurity = "0";
			
			log.info("Starting task migrate the schema: " + currentSchema + " isPodMaster = " + isPodMaster + " migrateNodeId = " + migrateNodeId);
			
			if (migrateNodeId > 0) {
				SessionHolder mainSessionHolder = null;
				try {
					mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
					Node.changeStatus(migrateNodeId, Node.NODE_STATUS_IN_MIGRATING_PROCESS);
					Node.activateNode(migrateNodeId, false);

					// This is initialization from running UMH UI
					// Determine following parameters from pod master node
					//
					if (!isPodMaster) {
						axis2repository = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_Axis2RepositoryDir);
						log.info("Pod Master repository path: " + axis2repository);
						if (axis2repository != null)
							messagepointFileroot = axis2repository.replace("/repository", "");
					}

					if (fromPath == null)
						fromPath = messagepointFileroot + "/" + currentSchema.toLowerCase();

					if (toPath == null)
						toPath = messagepointFileroot + "/" + currentSchema.toLowerCase();

					if (!isPodMaster || fromPath.equalsIgnoreCase(toPath)) {
						messagepointQEFileroot = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_MessagepointQEFileroot);
						log.info("Pod Master DE path: " + messagepointQEFileroot);

						compositionEnginesFileroot = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.KEY_CompositionEnginesFileroot);

						buildXml = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ExactTargetSettings.KEY_ExactTargetBuildXMLPath);
						antHome = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.ExactTargetSettings.KEY_ExactTargetAntPath);

						outgoingJobs = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Folder.Job.KEY_OutgoingDir);
					}

					String temp;

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerAddress);
					if (temp != null)
						emailServer = temp;

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerUserId);
					if (temp != null)
						emailAccount = temp;

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerUserPassword);
					if (temp != null)
						emailPassword = temp;

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpServerPort);
					if (temp != null)
						emailPort = temp;

					temp = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Email.KEY_SmtpConnectionSecurity);
					if (temp != null)
						emailSecurity = temp;
				} finally {
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}
			}
			
			if (fromPath == null)
				fromPath = messagepointFileroot + "/" + currentSchema.toLowerCase();
			
			if (toPath == null)
				toPath = messagepointFileroot + "/" + currentSchema.toLowerCase();
			
			log.info("From path: " + fromPath);
			log.info("To path:   " + toPath);

			String[] updateQueries = {
					"UPDATE gmc_configuration SET applied_de_version = null",
					"UPDATE dialogue_configuration SET applied_de_version = null",
					"UPDATE system_property SET prop_value = null WHERE prop_key = 'de.versions'",
					"UPDATE system_property SET prop_value = REPLACE(prop_value,?,?)",
					"UPDATE audit_report SET report_path = REPLACE(report_path,?,?)",
					"UPDATE content SET image_location = REPLACE(image_location,?,?)",
					"UPDATE hist_content SET image_location = REPLACE(image_location,?,?)",
					"UPDATE dialogue_configuration SET pub_file = REPLACE(pub_file,?,?)",
					"UPDATE document_preview SET output_path = REPLACE(output_path,?,?)",
					"UPDATE document_section SET image_location = REPLACE(image_location,?,?)",
					"UPDATE proof SET output_path = REPLACE(output_path,?,?)",
					"UPDATE tenant_theme_info SET logo_location = REPLACE(logo_location,?,?)",
					"UPDATE tenant_theme_info SET provider_logo_location = REPLACE(provider_logo_location,?,?)",
					"UPDATE test_scenario SET output_path = REPLACE(output_path,?,?)",
					"UPDATE deserver SET post_process_script = REPLACE(post_process_script,?,?)",
					"UPDATE condition_subelement SET data_file_path = REPLACE(data_file_path,?,?)",
					"UPDATE condition_sub_attrib SET attribute_value = REPLACE(attribute_value,?,?)",
					"UPDATE status_polling_background_task SET output_path = REPLACE(output_path,?,?)"
			};

			NativeQuery sqlQuery;
			for (String query : updateQueries)
			{
				sqlQuery = session.createNativeQuery(query);
				int paramsCount = StringUtils.countMatches(query, "?");
				if (paramsCount == 2)
				{
					sqlQuery.setParameter(1, fromPath);
					sqlQuery.setParameter(2, toPath);
				}
				sqlQuery.executeUpdate();					
			}
			
			String query = null;
			
			// Set the global folder

			query = "UPDATE system_property SET prop_value = '" + axis2repository + "' where prop_key = 'services.axis2.repository.dir'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			query = "UPDATE system_property SET prop_value = '" + outgoingJobs + "' where prop_key = 'job.folder.outgoing'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			query = "UPDATE system_property SET prop_value = '" + messagepointQEFileroot + "' where prop_key = 'messagepoint.qe.fileroot'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			query = "UPDATE system_property SET prop_value = '" + compositionEnginesFileroot + "' where prop_key = 'composition.engines.fileroot'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			// Set Web folders

			query = "UPDATE system_property SET prop_value = '" + webRoot + "' where prop_key = 'message.folder.application'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			query = "UPDATE system_property SET prop_value = '" + emailRoot + "' where prop_key = 'email.webroot.dir'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			// Exact Target folders
			
			query = "UPDATE system_property SET prop_value = '" + buildXml + "' where prop_key = 'exact.target.build.xml.path'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			query = "UPDATE system_property SET prop_value = '" + antHome + "' where prop_key = 'exact.target.ant.path'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();
			
			// Email settings
			
			query = "UPDATE system_property SET prop_value = '" + emailServer + "' where prop_key = 'email.server.outgoing'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			query = "UPDATE system_property SET prop_value = '" + emailAccount + "' where prop_key = 'email.server.outgoing.user'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			query = "UPDATE system_property SET prop_value = '" + emailPassword + "' where prop_key = 'email.server.outgoing.password'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			query = "UPDATE system_property SET prop_value = '" + emailPort + "' where prop_key = 'email.server.outgoing.port'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			query = "UPDATE system_property SET prop_value = '" + emailSecurity + "' where prop_key = 'email.server.connection.security'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			query = "UPDATE system_property SET prop_value = 'true' where prop_key = 'global.sendemail.booleanflag'";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.executeUpdate();

			/* fogbus#18040
			If any system propery has default path, @@MESSAGEPOINT_FILEROOT@@/@@MESSAGEPOINT_SCHEMA@@ this needs to be replaced too.
			 */
			query = "UPDATE system_property SET prop_value = REPLACE(prop_value,?,?)";
			sqlQuery = session.createNativeQuery(query);
			sqlQuery.setParameter(1, SystemPropertyKeys.DefaultValues.VALUE_MessagepointSchemaFileroot);
			sqlQuery.setParameter(2, toPath);
			sqlQuery.executeUpdate();

			// Reset domain users
			//
			HibernateUtil.getManager().callResetDomainUsers(currentSchema, dcsNodeGuid);
			
			if (migrateNodeId > 0)
			{
				long migrateBranchId = 0;
				Node migrateNode = Node.findById(migrateNodeId);
				if (migrateNode != null)
					migrateBranchId = migrateNode.getBranch().getId();
				
				// Reset user default instances if there are not part of the domain
				sqlQuery = session.createNativeQuery("SELECT DEFAULT_NODE_ID FROM USERS WHERE DEFAULT_NODE_ID > 0");
				//sqlQuery.executeUpdate();

				@SuppressWarnings("unchecked")
				List<Object> values = sqlQuery.list();
		        if (values != null && !values.isEmpty())
		        {
		        	for (Object value : values)
		        	{
		        		boolean reset_default_node = true;
		        		long default_node_id = ((BigInteger)value).longValue();
		        		Node default_node = Node.findById(default_node_id);
	        			if (default_node != null && default_node.getBranch().getId() == migrateBranchId)
	        				reset_default_node = false;
		        		
		        		if(reset_default_node)
		        		{
		    				sqlQuery = session.createNativeQuery("UPDATE USERS SET DEFAULT_NODE_ID = 0 WHERE DEFAULT_NODE_ID = " + default_node_id);
		    				sqlQuery.executeUpdate();
		        		}
		        	}
		        }

				if (enableNodeAfterMigration) {
					// Enable the node after initialization
					Node.activateNode(migrateNodeId, true);
					if (isPodMaster)
						Node.changeStatus(migrateNodeId, Node.NODE_STATUS_ONLINE);
					else
						Node.changeStatus(migrateNodeId, Node.NODE_STATUS_OFFLINE);
				}
			}
			
			log.info("Ending task migrate the schema: " + currentSchema);
			
		} catch(Exception e){
			if (migrateNodeId > 0)
				Node.changeStatus(migrateNodeId, Node.NODE_STATUS_MIGRATION_ERROR);
			try {
				log.error("Exception occur in Migration: ", e);
				if(session!=null)
				{	Transaction tx = session.getTransaction();
					if (tx != null && tx.isActive())
					{
						tx.rollback();
					}
				}
			} catch (Exception ex) {
				log.error("Exception occur when trying to rollback the transaction ,  "+ ex.getMessage());
			}
		}

		if (isPodMaster && session != null)
			session.flush();

		SystemPropertyManager.getInstance().clearPropertyCache(null);
		SystemPropertyManager.getInstance().clearPropertyCache(currentSchema);

	}
	
	public long getMigrateNodeId() {
		return migrateNodeId;
	}

	public void setMigrateNodeId(long migrateNodeId) {
		this.migrateNodeId = migrateNodeId;
	}

	public String getDbTask(){
		return dbtask;
	}
	public void setDbTask(String task){
		dbtask = task;
	}
   
	public String getFromPath(){
		return fromPath;
	}
	public void setFromPath(String value){
		fromPath = value;
	}
   
	public String getToPath(){
		return toPath;
	}
	public void setToPath(String value){
		toPath = value;
	}
   
	public String getDcsNodeGuid(){
		return dcsNodeGuid;
	}
	public void setDcsNodeGuid(String value){
		dcsNodeGuid = value;
	}

	public boolean getIsPodMaster(){
		return isPodMaster;
	}
	public void setIsPodMaster(boolean value){
		isPodMaster = value;
	}

	public boolean getEnableNodeAfterMigration(){
		return enableNodeAfterMigration;
	}
	public void setEnableNodeAfterMigration(boolean value){
		enableNodeAfterMigration = value;
	}
}
