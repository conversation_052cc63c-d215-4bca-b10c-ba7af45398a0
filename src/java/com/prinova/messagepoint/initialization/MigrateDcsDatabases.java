package com.prinova.messagepoint.initialization;

import java.io.File;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate5.SessionHolder;

import com.prinova.messagepoint.MessagePointRunnable;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.util.HibernateUtil;

public class MigrateDcsDatabases extends MessagePointRunnable {
	
	private static final Log log = LogUtil.getLog(MigrateDcsDatabases.class);
	
	private String dbtask = "dcsmigrate";
	private String dbflavor = "postgres";
	private File webInfDir;
	private long activateNodeId = 0;
	private long activateBranchId = 0;
	private List<Long> branchIDs = new ArrayList<>();
	

	public void performMainProcessing() {
		
		log.info("MigrateDcsDatabases Starting task the dbtask: " + dbtask);
		
		Session session = HibernateUtil.getManager().getSession();
		Branch branch = Branch.findById(activateBranchId);
		dcsMigrateBranch(branch, session);
		//session.flush();
	}
	
	private void dcsMigrateBranch(Branch branch, Session session) {
		log.info("Starting dcsMigrateBranch " + branch.getName());
		SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
		if(!branch.isMaster())
		{
			// Condition: Branch dcsSchemaName should be null and node with given schemaName shouldn't exist in Node table
			Branch currentBranch = Branch.findById(branch.getId());
			String currentDcsSchema = currentBranch.getDcsSchemaName();
			if (currentDcsSchema == null)
				currentDcsSchema = generateDscSchemaNamebyParentBranch(currentBranch);
			if(Node.findBySchema(currentDcsSchema) == null)
			{
				log.info("Current Schema " + currentDcsSchema);
				
				Node dcsNode = new Node();
				dcsNode.setBranch(currentBranch);
				
				dcsNode.setIsDefaultNode(false);
				dcsNode.setNodeCountNumber(1);
				
				dcsNode.setSchemaName(currentDcsSchema);
				dcsNode.setName(Node.BRANCH_DCS_NODE_NAME);
				dcsNode.setFriendlyName("DCS of " + currentBranch.getName());
				dcsNode.setNodeType(Node.NODE_TYPE_DCS);
				dcsNode.setStatus(Node.NODE_STATUS_NOT_INITIALIZED);
				dcsNode.setEnabled(false);
				
				if(currentBranch.getParentBranch() != null){
					Node parentDcsNode = currentBranch.getParentBranch().getDcsNode();
					if(parentDcsNode != null){
						dcsNode.setParentNode(parentDcsNode);
					}
				}else{
					log.info("parentBranch is null");
				}
				dcsNode.save();
				
				currentBranch.setDcsSchemaName(currentDcsSchema);
				currentBranch.setStatus(Branch.DCS_STATUS_IN_MIGRATING_PROCESS);
				currentBranch.setEnabled(false);
				currentBranch.save();
				
				// need to set all other nodes' parent to DCS
				for(Node child: currentBranch.getAllNodes(false)) // without DCS
				{
					child.setParentNode(Node.findById(dcsNode.getId()));
					child.save();
				}
				
				this.setActivateNode(dcsNode.getId());
				branchIDs.add(currentBranch.getId());
			}
			else 
			{
				currentBranch.setDcsSchemaName(currentDcsSchema);
				
				if(currentBranch.getStatus() == Branch.DCS_STATUS_IN_MIGRATING_PROCESS || currentBranch.getStatus() == Branch.DCS_STATUS_MIGRATION_ERROR || Node.getSchemaDBVersion(currentDcsSchema) < 0)
				{
					// For the case branch has dcsSchemaName but the schema doesn't exist or it's not initialized
					if(!branchIDs.contains(currentBranch.getId()))
						branchIDs.add(currentBranch.getId());
				
					currentBranch.setStatus(Branch.DCS_STATUS_IN_MIGRATING_PROCESS);
					currentBranch.setEnabled(false);
				}
				
				currentBranch.save();
			}
			
		}
		else
		{
			// This is POD MASTER 
			if (branch.getStatus() != Branch.DCS_STATUS_ONLINE && branch.getStatus() != Branch.DCS_STATUS_OFFLINE)
			{
				Node podNode = Node.findById(branch.getDefaultNode().getId());
				podNode.setNodeType(Node.NODE_TYPE_DCS);
				podNode.setName(Node.BRANCH_POD_MASTER_DCS_NODE_NAME);
				podNode.save();
				
				// Remove user account duplicates from POD MASTER
				//
				SessionHolder localSessionHolder = HibernateUtil.getManager().openTemporarySession(null);
				
				NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("SELECT a.ID FROM USERS a LEFT JOIN (SELECT MIN(ID) ID, USER_ID, IDP_TYPE, IDP_USER_GUID, IDP_IDENTIFIER FROM USERS GROUP BY USER_ID, IDP_TYPE, IDP_USER_GUID, IDP_IDENTIFIER ) b ON a.ID = b.ID AND a.USER_ID = b.USER_ID AND a.IDP_TYPE = b.IDP_TYPE WHERE b.ID IS NULL");
				sqlQuery.executeUpdate();
				
				@SuppressWarnings("unchecked")
				List<Object> values = sqlQuery.list();
		        if (values != null && !values.isEmpty())
		        {
		        	for (Object value : values)
		        	{
			        	long userId = ((BigInteger) value).longValue();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM PASSWORD_HISTORY WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM PASSWORD_RECOVERY WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM APPROVAL_USER WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM AUDIT_EVENT_DOC WHERE AUDIT_EVENT_ID IN (SELECT ID FROM AUDIT_EVENT WHERE OBJECT_ID = " + userId + " OR USER_ID = " + userId + ")");
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM AUDIT_EVENT WHERE OBJECT_ID = " + userId + " OR USER_ID = " + userId);
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM AUDIT_REPORT WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM USER_ROLE WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();
			        	
			        	sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM USER_PERMISSION WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();
			        	
			        	sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM TP_SEL_VISIBLE_USER WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();
						
						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM RATIONALIZER_APP_VISIBLE_USER WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();
			        	
			        	sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM FOLDERS WHERE OWNER_ID = " + userId);
						sqlQuery.executeUpdate();

						//sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM SYSTEM_TASK_USER WHERE USER_ID = " + userId);
						//sqlQuery.executeUpdate();

						//sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM HISTORY_TASK_USER WHERE USER_ID = " + userId);
						//sqlQuery.executeUpdate();

						sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM TASK_USER WHERE USER_ID = " + userId);
						sqlQuery.executeUpdate();

			        	sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("DELETE FROM USERS WHERE ID = " + userId);
						sqlQuery.executeUpdate();
		        	}
		        }
				
				sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("UPDATE USERS SET IDP_TYPE = 3, IDP_SUBJECT = NULL WHERE IDP_TYPE = 4 OR IDP_TYPE = 3");
				sqlQuery.executeUpdate();
		        
				sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("UPDATE USERS SET IDP_TYPE = 2, IDP_SUBJECT = NULL WHERE IDP_TYPE = 9 OR IDP_TYPE = 2");
				sqlQuery.executeUpdate();
		        
				HibernateUtil.getManager().restoreSession(localSessionHolder);
				
				branch.setStatus(Branch.DCS_STATUS_ONLINE);
				branch.setEnabled(true);
				branch.save();
			}
		}
		
		session.flush();
		
		for(Branch child:branch.getAllChildren()){
			log.info("Starting loop " + child.getName());
			child.getId();
			dcsMigrateBranch(child, session);
		}
		log.info("End of dcsMigrateBranch " + branch.getName());
		HibernateUtil.getManager().restoreSession(mainSessionHolder);
	}
	
	private String generateDscSchemaNamebyParentBranch(Branch branch){
		String schemaName = "";
		// get POD MASTER CODE
		schemaName = schemaName.concat(MessagepointMultiTenantConnectionProvider.getPodMasterCode()).concat("_");
		List<Branch> ancesters = branch.getInvertedAncesters();
		for(Branch ancestorBranch : ancesters){
			if(!ancestorBranch.isMaster()){
				Branch ancestor = Branch.findById(ancestorBranch.getId());
				String branchCode = ancestor.getBranchCode().toUpperCase();
				schemaName = schemaName.concat(branchCode.substring(0, branchCode.length() >= 4 ? 4 : branchCode.length())).concat("_");
			}
		}
		if(!branch.isMaster())
		{
			String branchCode = branch.getBranchCode().toUpperCase();
			schemaName = schemaName.concat(branchCode.substring(0, branchCode.length() >= 4 ? 4 : branchCode.length())).concat("_");
		}
		return schemaName.concat("DCS");
	}
	
	public String getDbTask()
	{
		return dbtask;
	}
	public void setDbTask(String task)
	{
		dbtask = task;
	}
	
	public String getDbFlavor()
	{
		return dbflavor;
	}
	public void setDbFlavor(String flavor)
	{
		dbflavor = flavor;
	}
   
	public File getWebInfDir()
	{
		return webInfDir;
	}
	public void setWebInfDir(File file)
	{
		webInfDir = file;
	}
   
	public long getActivateNode()
	{
		return activateNodeId;
	}
	public void setActivateNode(long value)
	{
		activateNodeId = value;
	}
   
	public long getActivateBranch()
	{
		return activateBranchId;
	}
	public void setActivateBranch(long value)
	{
		activateBranchId = value;
	}

	public List<Long> getBranchIDs() {
		return branchIDs;
	}

	public void setBranchIDs(List<Long> branchIDs) {
		this.branchIDs = branchIDs;
	}
}
