package com.prinova.messagepoint.initialization;

import java.util.HashSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/// Class for representing the options that can be given to this program
public class Options
{
   private static final String DBTASK = "-dbtask";
   private static final String DBFLAVOR = "-dbflavor";
   private static final String DBSCHEMA = "-dbschema";
   private static final String HELP = "-help";
   private static final String FILEROOT = "-fileroot";
   
   private String dbtask = "update";
   private String dbflavor = "postgres";
   private String dbschema = null;
   private String fileroot = null;
   
   private Map<String, String> knownArgs = new TreeMap<>();
   private Set<String> unknown = new HashSet<>();

   public Options( String[] args )
   {
      addOption( DBTASK, "=<string>.  rebuild or update task");
      addOption( DBFLAVOR, "=<string>.  sqlserver, oracle12c, postgres flavor");
      addOption( DBSCHEMA, "=<string>.  do task for all or specific schema");
      addOption( FILEROOT, "=<dir>.  Location of fileroot");
      addOption( HELP, "show this");

      Map<String, String> argMap = createMap( args );
      makeUnknown(argMap);

      if ( !unknown.isEmpty() )
         printUnknown();

      if ( argMap.containsKey(HELP) )
      {
         printHelp();
         System.exit(0);
      }
      
      if ( argMap.containsKey(DBTASK) )
    	  dbtask = argMap.get(DBTASK);
      if ( argMap.containsKey(DBFLAVOR) )
    	  dbflavor = argMap.get(DBFLAVOR);
      if ( argMap.containsKey(DBSCHEMA) )
    	  dbschema = argMap.get(DBSCHEMA);
      if ( argMap.containsKey(FILEROOT) )
    	  fileroot = argMap.get(FILEROOT);
   }

   public String getDbTask()
   {
      return dbtask;
   }
   public String getDbFlavor()
   {
      return dbflavor;
   }
   public String getDbSchema()
   {
      return dbschema;
   }
   public String getFileroot()
   {
	   return fileroot;
   }
   
   private void makeUnknown( Map<String, String> args )
   {
      Set<String> keys = args.keySet();
      for( String key : keys )
      {
         if ( !knownArgs.containsKey(key) )
         {
            unknown.add(key);
         }
      }
   }

   private void addOption( String arg, String description )
   {
      knownArgs.put(arg, description);
   }

   private void printHelp()
   {
      System.out.println("Valid command line options");
      for( String key : knownArgs.keySet() )
      {
         String desc = knownArgs.get(key);
         System.out.println("  " + key + ":  " + desc);
      }
   }

   private void printUnknown()
   {
      for( String key : unknown )
      {
         System.out.println("Unknown command line option " + key);
      }
   }

   private static Map<String, String> createMap( String[] args )
   {
      Map<String, String> config = new HashMap<>();
      for( String s : args )
      {
         int pos = s.indexOf('=');
         if ( pos == -1 )
         {
            config.put(s, "");
         }
         else
         {
            String key = s.substring(0,pos);
            String value = s.substring(pos+1);
            config.put(key, value);
         }
      }

      return config;
   }
}
