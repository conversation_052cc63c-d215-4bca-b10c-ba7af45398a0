package com.prinova.messagepoint.initialization;

import org.hibernate.EmptyInterceptor;

public class InitializationHibernateInterceptor extends EmptyInterceptor {
	private static final long serialVersionUID = 2056144587535020870L;
	private InitializeClassTableNames initializeClassTableNames;

	public InitializeClassTableNames getInitializeClassTableNames() {
		return this.initializeClassTableNames;
	}
	public void setInitializeClassTableNames(InitializeClassTableNames initializeClassTableNames) {
		this.initializeClassTableNames = initializeClassTableNames;
	}

	/* (non-Javadoc)
	 * @see org.hibernate.EmptyInterceptor#onPrepareStatement(java.lang.String)
	 */
	@Override
	public String onPrepareStatement(String sql) {
		if ( (this.initializeClassTableNames != null) &&
			 (this.initializeClassTableNames.getHibernateDialect() != null) &&
			 (this.initializeClassTableNames.getHibernateDialect().toLowerCase().contains(("sqlserver"))) ) {
			if (sql.startsWith("insert into ")) {
				// Extract the table name from the sql command
				String tableName = sql.substring(12).split(" ")[0];
				if ( (tableName != null) &&
					 (!tableName.isEmpty()) &&
					 (this.initializeClassTableNames.getTableNames().contains(tableName)) ) {
					// Alter the INSERT statement to set the IDENTITY_INSERT property ON & OFF before and after the INSERT statement
					sql = "SET IDENTITY_INSERT "+tableName+" ON; " + sql + "; SET IDENTITY_INSERT "+tableName+" OFF;";
				}
			}
		}
		return super.onPrepareStatement(sql);
	}
}
