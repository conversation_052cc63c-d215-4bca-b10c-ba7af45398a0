package com.prinova.messagepoint.initialization;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.*;
import com.prinova.messagepoint.model.admin.deserver.DEServerCommunicationType;
import com.prinova.messagepoint.model.admin.proxylogin.PodStatus;
import com.prinova.messagepoint.model.admin.proxylogin.PodType;
import com.prinova.messagepoint.model.dialogue.DialogueDataMapping;
import com.prinova.messagepoint.model.filters.DashboardFilter;
import com.prinova.messagepoint.model.navigation.*;
import com.prinova.messagepoint.model.security.*;
import com.prinova.messagepoint.model.tenant.Tenant;
import com.prinova.messagepoint.model.tenant.TenantThemeInfo;
import com.prinova.messagepoint.model.version.VersionStatus;
import com.prinova.messagepoint.model.workflow.*;
import com.prinova.messagepoint.reports.model.JobMetadataDetailPart;
import com.prinova.messagepoint.theme.ThemeManager;
import com.prinova.messagepoint.util.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.hibernate.query.NativeQuery;
import org.hibernate.Session;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.*;

public class InitializeMetadataParser extends MessagePointXmlParser {
    User primaryUser = null;
    String messagepointFileroot = "/messagepoint";
    String messagepointJobFileroot = "/repository/jobs";
    String messagepointJobWorkingFileroot = "/localFiles/jobs";
    String messagepointOutputFileroot = "/repository/outputs";
    String messagepointQEFileroot = "/messagepoint/DE";
    String compositionEnginesFileroot = "/messagepoint/compEngine";
    String buildXml = "/messagepointUMH/build.xml";
    String antHome = "/ant";

    long currentMaxTreeId = Integer.MAX_VALUE >> 2;
    boolean rebuild;
    Session session;
    String schema;
    private static final Log log = LogUtil.getLog(InitializeMetadataParser.class);

    public InitializeMetadataParser(boolean val, Session session, String schemaName, String messagepointFileroot,
                                    String messagepointJobFileroot, String messagepointJobWorkingFileroot,
                                    String messagepointOutputFileroot, String messagepointQEFileroot,
                                    String compositionEnginesFileroot, String buildXml, String antHome) {
        rebuild = val;
        this.session = session;

        if (schemaName == null || schemaName.isEmpty())
            this.schema = "default";
        else
            this.schema = schemaName.toLowerCase();

        if (messagepointFileroot != null)
            this.messagepointFileroot = messagepointFileroot;

        if (messagepointJobFileroot != null)
            this.messagepointJobFileroot = messagepointJobFileroot;

        if (messagepointJobWorkingFileroot != null)
            this.messagepointJobWorkingFileroot = messagepointJobWorkingFileroot;

        if (messagepointOutputFileroot != null)
            this.messagepointOutputFileroot = messagepointOutputFileroot;

        if (messagepointQEFileroot != null)
            this.messagepointQEFileroot = messagepointQEFileroot;

        if (compositionEnginesFileroot != null)
            this.compositionEnginesFileroot = compositionEnginesFileroot;

        if (buildXml != null)
            this.buildXml = buildXml;

        if (antHome != null)
            this.antHome = antHome;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = false)
    public synchronized void parseAllMetadata(User primaryUser, File webInfDir) throws Exception {

        log.info("Entering parseAllMetadata(primaryUser='"
                + ((primaryUser == null) ? "--null--" : primaryUser.getEmail()) + "')");
        if (primaryUser != null) {
            this.primaryUser = primaryUser;
        }

        TreeMap<Long, Permission> permissions = new TreeMap<>();

        File securityMetadata = new File(webInfDir, "initialization/securitymetadata.xml");
        File generalMetadata = new File(webInfDir, "initialization/generalmetadata.xml");
        File navworkflowMetadata = new File(webInfDir, "initialization/navworkflowmetadata.xml");
        File touchpointMetadata = new File(webInfDir, "initialization/touchpointmetadata.xml");

        log.info("Start parseSecurityMetadata...");
        permissions = parseSecurityMetadata(securityMetadata, primaryUser);
        updateDatabaseFileVersion(securityMetadata);

        log.info("Start parseGeneralMetadata...");
        parseGeneralMetadata(generalMetadata);
        updateDatabaseFileVersion(generalMetadata);

        log.info("Start parseNavWorkflowMetadata...");
        parseNavWorkflowMetadata(navworkflowMetadata, permissions);
        updateDatabaseFileVersion(navworkflowMetadata);

        log.info("Start parseTouchpointMetadata...");
        parseTouchpointMetadata(touchpointMetadata);
        updateDatabaseFileVersion(touchpointMetadata);

        log.info("Exiting parseAllMetadata(primaryUser='"
                + ((primaryUser == null) ? "--null--" : primaryUser.getEmail()) + "')...");
    }

    private void saveObject(UpdatableMessagePointModel object, boolean isNew) {
        if (this.primaryUser != null) {
            object.setUpdatedBy(primaryUser.getId());
        } else {
            log.warn("null primaryUser object passed into saveObject()!");
        }

        HibernateUtil.getManager().saveObjectWithPropagationRequired(object, isNew);
    }

    public TreeMap<Long, Permission> parseSecurityMetadata(File securityMetadataFile, User primaryUser) {
        if (securityMetadataFile == null) {
            throw new IllegalArgumentException("The securityMetadataFile parameter cannot be null!");
        }
        log.info("Inside InitializeMetadataParser.parseSecurityMetadata(securityMetadataFile='"
                        + securityMetadataFile.getAbsolutePath() + "')...");

        this.parseDocument(securityMetadataFile);

        // Ensure that the document member variable is not null
        if (this.document == null) {
            throw new IllegalArgumentException("this.document cannot be null!");
        }

        TreeMap<Long, Permission> permissions = null;

        // Save TreeMaps of all the parsed Objects to be used in subsequent
        // parsing steps
        permissions = parsePermissions();
        HashMap<Long, PermissionCategory> categories = parsePermissionCategories(permissions);
        parsePermissionCategoryGroups(categories);

        this.session.flush();

        for (Long permissionsKey : permissions.keySet()) {
            saveObject(permissions.get(permissionsKey), true);
        }

        this.session.flush();

        int count = 0;
        if (!rebuild) {
            NativeQuery sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("SELECT COUNT(*) FROM ROLE_PERMISSION");
            List retList = sqlQuery.list();

            if (retList != null && !retList.isEmpty()) {
                Object retObj = retList.get(0);
                if (Number.class.isInstance(retObj))
                    count = ((Number) retObj).intValue();
            }

            sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("select p.id from permission p left join tenant_permission tp on tp.permission_id = p.id where tp.permission_id is null and CAST(p.type AS integer) != 9 and p.id not in (130, 131)");
            retList = sqlQuery.list();

            if (retList != null && !retList.isEmpty()) {
                for (Object value : retList) {
                    long permissionId = ((BigInteger) value).longValue();
                    sqlQuery = HibernateUtil.getManager().getSession().createNativeQuery("INSERT INTO TENANT_PERMISSION (tenant_id, permission_id) VALUES (1, " + permissionId + ")");
                    sqlQuery.executeUpdate();
                }
            }
        }

        if (rebuild || count < 295) {
            TreeMap<Long, User> users = parseUsers();
            // Add the primaryUser to the Users cache
            if (users == null) {
                users = new TreeMap<>();
            }
            if (primaryUser != null) {
                users.put(primaryUser.getId(), primaryUser);
            }
            parseRoles(users, permissions);
        }

        this.session.flush();

        log.info("Exiting InitializeMetadataParser.parseSecurityMetadata(File securityMetadataFile='"
                        + securityMetadataFile.getAbsolutePath() + "')...");
        return permissions;
    }

    private HashMap<Long, CategoryGroup> parsePermissionCategoryGroups(HashMap<Long, PermissionCategory> permissions) {
        boolean isNew = true;
        HashMap<Long, CategoryGroup> permissionCategoryHashMap = new HashMap<>();
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "CategoryGroups");
        if (nodeList != null) {

            for (Node groupNode : getNode(nodeList, "CategoryGroups")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "CategoryGroup")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {

                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            CategoryGroup categoryGroup = new CategoryGroup();
                            categoryGroup.setId(id);
                            categoryGroup.setName(getChildNodeValue(itemNode, "Name"));
                            categoryGroup.setDescription(getChildNodeValue(itemNode, "Description"));
                            categoryGroup.setPriority(Integer.valueOf(getChildNodeValue(itemNode, "Priority")).intValue());
                            saveObject(categoryGroup, true);
                            for (Node permissionCategory : getNode(itemNode.getChildNodes(), "PermissionCategories")) {
                                for (Node supportedComparisonTypeNode : getNode(permissionCategory.getChildNodes(),
                                        "PermissionCategory")) {
                                    String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {

                                            PermissionCategory referencedPermissionCategory = permissions.get(refId);
                                            if (referencedPermissionCategory != null) {
                                                referencedPermissionCategory.setCategoryGroup(categoryGroup);
                                                saveObject(referencedPermissionCategory, isNew);
                                                categoryGroup.addPermissionCateogory(referencedPermissionCategory);
                                            } else {
                                                log.error("CategoryGroup with id='"
                                                                + id
                                                                + "' has a reference to a PermissionCategory object with refid='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                            // Now, save the object to the database
                            saveObject(categoryGroup, false);
                            permissionCategoryHashMap.put(categoryGroup.getId(), categoryGroup);
                        }
                    }
                }
            }
        }

        return permissionCategoryHashMap;
    }

    public synchronized void parseGeneralMetadata(File generalMetadataFile) throws Exception {
        if (generalMetadataFile == null) {
            throw new IllegalArgumentException("The generalMetadataFile parameter cannot be null!");
        }
        log.info("Inside InitializeMetadataParser.parseGeneralMetadata(File generalMetadataFile='"
                        + generalMetadataFile.getAbsolutePath() + "')...");

        this.parseDocument(generalMetadataFile);

        // Ensure that the document member variable is not null
        if (this.document == null) {
            throw new IllegalArgumentException("this.document cannot be null!");
        }

        // Start with the basic items
        parseAppVersion();
        parseSystemThemes();
        parseBackgroundThemes();

        if (rebuild)
            parseSystemProperties();

        parseItemTypes();
        parseSystemStates();
        parseTranslationProviders();

        Map<Long, ContentType> contentMap = parseContentTypes();
        parseSubContentTypes(contentMap);
        parseDailyFrequencyTypes();

        this.session.flush();

        parseFrequencyTypes();
        parseDeliveryEventTypes();
        parseEventTypes();
        parseSourceTypes();
        parseLayoutTypes();
        parsePodTypes();
        parsePodStatuses();
        parseDEServerCommunicationTypes();

        this.session.flush();

        parseEncodingTypes();
        parseUserDeactivateReasonTypes();
        parseDataRecordLevels();
        parseAggregationOperators();
        parseConditionOperators();

        this.session.flush();

        parseModels(generalMetadataFile.getParentFile().getParentFile());

        this.session.flush();

        parseMessagepointLocales();

        this.session.flush();

        parseApplicationLocales();

        this.session.flush();

        // Save a TreeMap of the parsed DataComparisonTypes to be used in
        // subsequent parsing steps
        TreeMap<Long, DataComparison> dataComparisonTypes = parseDataComparisonTypes();
        // Save a TreeMap of the parsed DataComparisonTypes to be used in
        // subsequent parsing steps
        TreeMap<Long, DataSubtype> dataSubtypes = parseDataSubtypes(dataComparisonTypes);

        parseDataTypes(dataSubtypes);
        parseDateDataValueTypes();

        parseConditionTypes();

        this.session.flush();

        parseRecordTypes();
        parseReportJobMetadataParts();

        log.info("Exiting InitializeMetadataParser.parseGeneralMetadata(File generalMetadataFile='"
                        + generalMetadataFile.getAbsolutePath() + "')...");
    }

    public void parseNavWorkflowMetadata(File navWorkflowMetadataFile, TreeMap<Long, Permission> permissions) throws Exception {
        if (navWorkflowMetadataFile == null) {
            throw new IllegalArgumentException("The navWorkflowMetadataFile parameter cannot be null!");
        }
        log.info("Inside InitializeMetadataParser.parseNavWorkflowMetadata(File navWorkflowMetadataFile='"
                        + navWorkflowMetadataFile.getAbsolutePath() + "')...");

        this.parseDocument(navWorkflowMetadataFile);

        // Ensure that the document member variable is not null
        if (this.document == null) {
            throw new IllegalArgumentException("this.document cannot be null!");
        }

        if ((permissions == null) || (permissions.isEmpty())) {
            permissions = retrieveAllPermissions();
        }

        this.session.flush();

        TreeMap<Long, Tree> trees = parseNavigationTrees();

        this.session.flush();

        TreeMap<Long, Long> dropDownMenuTabRelationships = new TreeMap<>();
        TreeMap<Long, DropDownMenu> dropDownMenus = parseNavigationDropDownMenus(dropDownMenuTabRelationships, permissions);

        this.session.flush();

        TreeMap<Long, DropDownMenuItem> dropDownMenuItems = parseNavigationDropDownMenuItems(trees,
                dropDownMenus,
                permissions);

        this.session.flush();

        TreeMap<Long, NavigationTab> navigationTabs = parseNavigationTabs(dropDownMenuItems, permissions);

        this.session.flush();

        fixDropDownMenuTabRelationships(dropDownMenus, navigationTabs, dropDownMenuTabRelationships);

        this.session.flush();

        TreeMap<Long, Workflow> workflows = parseWorkflows();

        this.session.flush();

        TreeMap<Long, WorkflowState> workflowStates = parseWorkflowStates();

        this.session.flush();

        // Create some reference TreeMaps so we can go back after parsing the
        // WorkflowPosition objects and fix any references to Previous and Next
        // objects
        TreeMap<Long, Long> workflowPositionPreviousReferences = new TreeMap<>();
        TreeMap<Long, Long> workflowPositionNextReferences = new TreeMap<>();
        TreeMap<Long, WorkflowPosition> workflowPositions = null;
        workflowPositions = parseWorkflowPositions(workflows,
                workflowStates,
                workflowPositionPreviousReferences,
                workflowPositionNextReferences,
                permissions);

        this.session.flush();

        fixWorkflowPositionReferences(workflowPositions, workflowPositionPreviousReferences, "Previous");
        fixWorkflowPositionReferences(workflowPositions, workflowPositionNextReferences, "Next");

        this.session.flush();

        parseWorkflowTabs(workflowPositions, permissions);

        this.session.flush();

        parseWorkflowProperties(workflowPositions);

        this.session.flush();

        log.info("Exiting InitializeMetadataParser.parseNavWorkflowMetadata(File navWorkflowMetadataFile='"
                        + navWorkflowMetadataFile.getAbsolutePath() + "')...");

        // Create the shared content workflow and corresponding instance
        if (ConfigurableWorkflow.getSharedContentWorkflow() == null) {
            ConfigurableWorkflow scWorkflow = new ConfigurableWorkflow();
            scWorkflow.setModelType(ConfigurableWorkflow.SHARED_CONTENT_WORKFLOW);
            scWorkflow.setDocument(null);
            saveObject(scWorkflow, true);

            ConfigurableWorkflowInstance scWorkflowInst = new ConfigurableWorkflowInstance();
            scWorkflowInst.setConfigurableWorkflow(scWorkflow);
            scWorkflowInst.setStatus(VersionStatus.findById(VersionStatus.VERSION_PRODUCTION));
            scWorkflowInst.setOwnerId(null);
            saveObject(scWorkflowInst, true);
        }

        // Create the embedded content workflow and corresponding instance
        if (ConfigurableWorkflow.getGlobalSmartTextWorkflow() == null) {
            ConfigurableWorkflow ecWorkflow = new ConfigurableWorkflow();
            ecWorkflow.setModelType(ConfigurableWorkflow.GLOBAL_SMART_TEXT_WORKFLOW);
            ecWorkflow.setDocument(null);
            saveObject(ecWorkflow, true);

            ConfigurableWorkflowInstance ecWorkflowInst = new ConfigurableWorkflowInstance();
            ecWorkflowInst.setConfigurableWorkflow(ecWorkflow);
            ecWorkflowInst.setStatus(VersionStatus.findById(VersionStatus.VERSION_PRODUCTION));
            ecWorkflowInst.setOwnerId(null);
            saveObject(ecWorkflowInst, true);
        }

        // Create the content library workflow and corresponding instance
        if (ConfigurableWorkflow.getGlobalImageWorkflow() == null) {
            ConfigurableWorkflow clWorkflow = new ConfigurableWorkflow();
            clWorkflow.setModelType(ConfigurableWorkflow.GLOBAL_IMAGE_WORKFLOW);
            clWorkflow.setDocument(null);
            saveObject(clWorkflow, true);

            ConfigurableWorkflowInstance clWorkflowInst = new ConfigurableWorkflowInstance();
            clWorkflowInst.setConfigurableWorkflow(clWorkflow);
            clWorkflowInst.setStatus(VersionStatus.findById(VersionStatus.VERSION_PRODUCTION));
            clWorkflowInst.setOwnerId(null);
            saveObject(clWorkflowInst, true);
        }

        // Create the content library workflow and corresponding instance
        if (ConfigurableWorkflow.getLookupTableWorkflow() == null) {
            ConfigurableWorkflow ltWorkflow = new ConfigurableWorkflow();
            ltWorkflow.setModelType(ConfigurableWorkflow.LOOKUP_TABLE_WORKFLOW);
            ltWorkflow.setDocument(null);
            saveObject(ltWorkflow, true);

            ConfigurableWorkflowInstance ltWorkflowInst = new ConfigurableWorkflowInstance();
            ltWorkflowInst.setConfigurableWorkflow(ltWorkflow);
            ltWorkflowInst.setStatus(VersionStatus.findById(VersionStatus.VERSION_PRODUCTION));
            ltWorkflowInst.setOwnerId(null);
            saveObject(ltWorkflowInst, true);
        }
    }

    public void parseTouchpointMetadata(File touchpointMetadataFile) {
        if (touchpointMetadataFile == null) {
            throw new IllegalArgumentException("The touchpointMetadataFile parameter cannot be null!");
        }
        log.info("Inside InitializeMetadataParser.parseTouchpointMetadata(File touchpointMetadataFile='"
                        + touchpointMetadataFile.getAbsolutePath() + "')...");

        this.parseDocument(touchpointMetadataFile);

        // Ensure that the document member variable is not null
        if (this.document == null) {
            throw new IllegalArgumentException("this.document cannot be null!");
        }

        parseDialogueDataMappings();
        // Save a TreeMap of the parsed DataComparisonTypes to be used in
        // subsequent parsing steps

        this.session.flush();

        log.info("Exiting InitializeMetadataParser.parseTouchpointMetadata(File touchpointMetadataFile='"
                        + touchpointMetadataFile.getAbsolutePath() + "')...");
    }

    private TreeMap<Long, User> parseUsers() {
        TreeMap<Long, User> users = new TreeMap<>();
        boolean isNew = false;
        NodeList nodeList = document.getElementsByTagName("Users");
        for (Node groupNode : getNode(nodeList, "Users")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "User")) {
                String idString = getAttribute(itemNode, "id");
                if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                    Long id = Long.parseLong(idString);
                    if ((id != null) && (id > 0)) {
                        User user = HibernateUtil.getManager().getObject(User.class, id);
                        if (user == null)
                        {
                            user = new User();
                            isNew = true;
                        }
                        if (getChildNodeValue(itemNode, "workgroupId") != null) {
                            user.setWorkgroupId(Long.parseLong(getChildNodeValue(itemNode, "workgroupId")));
                        } else {
                            user.setWorkgroupId(1);
                        }

                        user.setId(id);
                        user.setFirstName(getChildNodeValue(itemNode, "FirstName"));
                        user.setLastName(getChildNodeValue(itemNode, "LastName"));
                        user.setEmail(getChildNodeValue(itemNode, "Email"));
                        user.setUsername(getChildNodeValue(itemNode, "Username"));
                        user.setEmailVerified(true);
                        user.createSalt();
                        if (getChildNodeValue(itemNode, "MasterAdminStatus") != null)
                            user.setMasterAdminStatus(Integer.valueOf(getChildNodeValue(itemNode, "MasterAdminStatus")));
                        else
                            user.setMasterAdminStatus(0);
                        String password = UserUtil.hashAndSaltPassword(getChildNodeValue(itemNode, "Password"), user);
                        user.setPassword(password);
                        user.setPasswordLastUpdated(new Date(System.currentTimeMillis()));
                        user.setAccountActive(true);
                        if (getChildNodeValue(itemNode, "defaultTabId") != null) {
                            user.setDefaultTabId(Integer.parseInt(getChildNodeValue(itemNode, "defaultTabId")));
                        }
                        user.setApplicationLocaleId(1L);

                        // Save the object to the database
                        saveObject(user, isNew);

                        if (isNew) {
                            // Add to Password History
                            PasswordHistory passwordHistory = new PasswordHistory();
                            passwordHistory.setId(id);
                            passwordHistory.setUserId(user.getId());
                            passwordHistory.setPassword(password);
                            passwordHistory.setCreated(new Date(System.currentTimeMillis()));

                            // Save the object to the database
                            saveObject(passwordHistory, isNew);
                        }

                        // Save a copy of the object in a TreeMap so it can be
                        // used in subsequent XML references
                        users.put(id, user);
                    }
                }
            }
        }
        return users;
    }

    private TreeMap<Long, Permission> parsePermissions() {
        TreeMap<Long, Permission> permissions = new TreeMap<>();
        TreeMap<Long, KeyValuePair> descriptionKeyValuePairs = parseKeyValuePairsById(document,
                "Permissions",
                "Permission",
                "Name",
                "Description");
        TreeMap<Long, KeyValuePair> typeKeyValuePairs = parseKeyValuePairsById(document,
                "Permissions",
                "Permission",
                "Name",
                "Type");
        Set<Long> keys = descriptionKeyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair descriptionKeyValuePair = descriptionKeyValuePairs.get(key);
            KeyValuePair typeKeyValuePair = typeKeyValuePairs.get(key);
            Permission permission = new Permission();
            permission.setId(key);
            permission.setName(descriptionKeyValuePair.getKey());
            permission.setDescription(descriptionKeyValuePair.getValue());
            permission.setType(typeKeyValuePair.getValue());
            permissions.put(permission.getId(), permission);
        }
        return permissions;
    }

    private HashMap<Long, PermissionCategory> parsePermissionCategories(TreeMap<Long, Permission> permissions) {
        boolean isNew = true;
        HashMap<Long, PermissionCategory> permissionCategoryHashMap = new HashMap<>();
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "PermissionCategories");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "PermissionCategories")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "PermissionCategory")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            PermissionCategory permissionCategory = new PermissionCategory();
                            permissionCategory.setId(id);
                            permissionCategory.setName(getChildNodeValue(itemNode, "Name"));
                            permissionCategory.setDescription(getChildNodeValue(itemNode, "Description"));
                            permissionCategory.setPriority(Integer.valueOf(getChildNodeValue(itemNode, "Priority")).intValue());
                            for (Node supportedComparisonTypes : getNode(itemNode.getChildNodes(), "Permissions")) {
                                for (Node supportedComparisonTypeNode : getNode(supportedComparisonTypes.getChildNodes(),
                                        "Permission")) {
                                    String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                referencedPermission.setCategory(permissionCategory);
                                            } else {
                                                log.error("permission Category with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refid='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                            // Now, save the object to the database
                            saveObject(permissionCategory, isNew);
                            permissionCategoryHashMap.put(permissionCategory.getId(), permissionCategory);
                        }
                    }
                }
            }
        }

        return permissionCategoryHashMap;
    }

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void parseRoles(TreeMap<Long, User> users, TreeMap<Long, Permission> permissions) {
        boolean isNew = false;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "Roles");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "Roles")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "Role")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            Role role = HibernateUtil.getManager().getObject(Role.class, id);
                            if (role == null) {
                                role = new Role();
                                role.setId(id);
                                isNew = true;
                            }
                            role.setName(getChildNodeValue(itemNode, "Name"));
                            role.setDescription(getChildNodeValue(itemNode, "Description"));
                            role.setVisibility(getChildNodeValue(itemNode, "Visibility"));

                            boolean hiddenFlag = false;
                            String hiddenFlagString = getChildNodeValue(itemNode, "HiddenFlag");
                            if ((hiddenFlagString != null) && (hiddenFlagString.equalsIgnoreCase("TRUE"))) {
                                hiddenFlag = true;
                            }
                            role.setHiddenFlag(hiddenFlag);

                            boolean active = false;
                            String activeString = getChildNodeValue(itemNode, "Active");
                            if ((activeString != null) && (activeString.equalsIgnoreCase("TRUE"))) {
                                active = true;
                            }
                            role.setActive(active);
                            for (Node supportedComparisonTypes : getNode(itemNode.getChildNodes(), "Users")) {
                                for (Node supportedComparisonTypeNode : getNode(supportedComparisonTypes.getChildNodes(),
                                        "User")) {
                                    String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            User referencedUser = users.get(refId);
                                            if (referencedUser != null) {
                                                if (role.getUsers() == null) {
                                                    role.setUsers(new HashSet<>());
                                                }
                                                if (referencedUser.getRoles() == null) {
                                                    referencedUser.setRoles(new HashSet<>());
                                                }
                                                role.getUsers().add(referencedUser);
                                                referencedUser.getRoles().add(role);
                                            } else {
                                                log.error("Role with id='"
                                                                + id
                                                                + "' has a reference to a User object with refid='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                            for (Node supportedComparisonTypes : getNode(itemNode.getChildNodes(), "Permissions")) {
                                for (Node supportedComparisonTypeNode : getNode(supportedComparisonTypes.getChildNodes(),
                                        "Permission")) {
                                    String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                if (role.getPermissions() == null) {
                                                    role.setPermissions(new HashSet<>());
                                                }
                                                role.getPermissions().add(referencedPermission);
                                            } else {
                                                log.error("Role with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refid='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                            // Now, save the object to the database
                            saveObject(role, isNew);
                        }
                    }
                }
            }
        }
    }

    private void parseModels(File webInfDir) throws Exception {
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "Model");
        // set of classes that have been identified as having no initial items in the DB.
        Set<String> isRefreshable = new HashSet<>();

        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "Model")) {
                String className = getAttribute(groupNode, "class");
                Class<?> clazz = Class.forName(className);

                // Parse and process Dashboard filters - e.g. wordCount filter
                if(DashboardFilter.class.isAssignableFrom(clazz)) {
                    HibernateUtil.getManager().getSession().beginTransaction();
                    parseDashboardFilter(groupNode, clazz);
                    HibernateUtil.getManager().getSession().getTransaction().commit();
                }

                if (!IdentifiableMessagePointModel.class.isAssignableFrom(clazz))
                    continue;

                // Only parse this model (and save) if the table is empty.
                // This assumes that the some previous step will have deleted all entries
                // for tables that need to be refreshed.
                boolean refreshItem = isRefreshable.contains(className);
                if (!refreshItem && HibernateUtil.getManager().getObjects(clazz).isEmpty()) {
                    isRefreshable.add(className);
                    refreshItem = true;
                }

                if (!refreshItem)
                    continue;

                Object model = clazz.getConstructor(new Class[]{}).newInstance(new Object[]{});

                for (Node propertyNode : getNode(groupNode.getChildNodes(), "Property")) {
                    String propertyName = getAttribute(propertyNode, "name");
                    String type = getAttribute(propertyNode, "type");
                    Class<?> argClazz = ReflectionUtils.isPrimitive(type) ? ReflectionUtils.getPrimitiveClass(type)
                            : Class.forName(type);

                    String textValue = processValue(propertyNode.getTextContent().trim());
                    if (textValue != null && !textValue.isEmpty()) {
                        Object convertedValue = ReflectionUtils.parseActualObject(type, textValue);
                        ReflectionUtils.setField(model, propertyName, argClazz, convertedValue);
                    }

                    /**
                     * Hack; hql to populate the data from the table that is also being populated in the same
                     * transaction (not yet committed), so a commit to the existing transaction has to be
                     * performed before the hql query and update.
                     */
                    for (Node queryNode : getNode(propertyNode.getChildNodes(), "Query")) {

                        String hqlQuery = getAttribute(queryNode, "hql");
                        boolean firstOnly = getAttribute(queryNode, "first").equals("true");

                        this.session.flush();

                        List<?> values = HibernateUtil.getManager().getObjectsAdvanced(hqlQuery);
                        Object convertedValue;
                        if (firstOnly) {
                            Object value = values.iterator().next();
                            convertedValue = ReflectionUtils.parseActualObject(type, value);
                        } else {
                            convertedValue = ReflectionUtils.parseActualObject(type, values);
                        }
                        ReflectionUtils.setField(model, propertyName, argClazz, convertedValue);
                    }

                }

                // HACK - define a more generic way of doing this.
                if (Tenant.class.getName().equals(className)) {

                    TenantThemeInfo themeInfo = TenantThemeInfo.createDefault();
                    Tenant t = (Tenant) model;
                    TenantThemeInfo.associate(t, themeInfo);
                    themeInfo.setOwner(t);
                    t.setThemeInfo(themeInfo);
                    HibernateUtil.getManager().saveObject(themeInfo);
                    HibernateUtil.getManager().saveObject(t);

                    try {
                        File logoFile = new File(webInfDir.getParentFile(), "_ux/img/brands/messagepoint-logo-dark-theme.svg");
                        String location = ThemeManager.addCorporateLogo(themeInfo,
                                logoFile.getName(),
                                FileUtils.readFileToByteArray(logoFile));
                        themeInfo.setLogoLocation(location);
                        HibernateUtil.getManager().saveObject(themeInfo);
                    } catch (IOException ex) {
                        log.error("ThemeManager " + ex);
                    }
                }
                else {
                    HibernateUtil.getManager().saveObject((IdentifiableMessagePointModel) model);
                }

            }
        }
    }

    private void parseDashboardFilter(Node groupNode, Class<?> clazz) throws Exception {
        Object model = clazz.getConstructor(new Class[]{}).newInstance(new Object[]{});
        List<MessagepointCriterion> critList = new ArrayList<>();
        Object newFilterValue = null;
        String newFilterType = "";
        for (Node propertyNode : getNode(groupNode.getChildNodes(), "Property")) {
            String propertyName = getAttribute(propertyNode, "name");
            String type = getAttribute(propertyNode, "type");
            String textValue = processValue(propertyNode.getTextContent().trim());

            Class<?> argClazz = ReflectionUtils.isPrimitive(type) ? ReflectionUtils.getPrimitiveClass(type)
                    : Class.forName(type);

            if("value".equalsIgnoreCase(propertyName)) {
                newFilterValue = ReflectionUtils.parseActualObject(type, textValue);
                newFilterType = type;
            }

            if(!StringUtil.isEmptyOrNull(textValue) && !"value".equalsIgnoreCase(propertyName)) {
                Object convertedValue = ReflectionUtils.parseActualObject(type, textValue);
                ReflectionUtils.setField(model, propertyName, argClazz, convertedValue);
                critList.add(MessagepointRestrictions.eq(propertyName, convertedValue));
            }
        }

        DashboardFilter existingFilter = (DashboardFilter) HibernateUtil.getManager().getObjectUnique(clazz, critList);
        if(Objects.isNull(existingFilter)) {
            if(!StringUtil.isEmptyOrNull(newFilterType) && !Objects.isNull(newFilterValue)) {
                Class<?> argClazz = ReflectionUtils.isPrimitive(newFilterType) ? ReflectionUtils.getPrimitiveClass(newFilterType)
                        : Class.forName(newFilterType);
                ReflectionUtils.setField(model, "value", argClazz, newFilterValue);
                HibernateUtil.getManager().saveObject(model, true);
            }
        }
    }

    private void parseItemTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "ItemTypes", "ItemType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            ItemType itemType = new ItemType();
            itemType.setId(key.longValue());
            itemType.setName(name);
            itemType.setDescription("");
            saveObject(itemType, isNew);
        }
    }

    private void parseSystemStates(){
        NodeList nodeList=MessagePointXmlParser.getNodeListByName(document,"SystemStates");
        if (nodeList != null) {
            for (Node groupNode:getNode(nodeList,"SystemStates")) {
                for (Node itemNode:getNode(groupNode.getChildNodes(),"SystemState")) {
                    String idString = getAttribute(itemNode,"id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            SystemState systemState = new SystemState();
                            systemState.setId(id.intValue());
                            systemState.setName(getChildNodeValue(itemNode,"Name"));
                            systemState.setStringCode(getChildNodeValue(itemNode,"StringCode"));
                            String bitFlagStr = getChildNodeValue(itemNode,"BitFlag");
                            if (bitFlagStr != null && !bitFlagStr.isEmpty()) {
                                systemState.setBitFlag(Long.parseLong(bitFlagStr));
                            }
                            saveObject(systemState,true);
                        }
                    }
                }
            }
        }
    }

    private Map<Long, ContentType> parseContentTypes() {
        boolean isNew = true;
        Map<Long, ContentType> retMap = new HashMap<>();
        TreeMap<Integer, String> names = parseSubItemText(document, "ContentTypes", "ContentType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            ContentType contentType = new ContentType();
            contentType.setId(key.longValue());
            contentType.setName(name);
            contentType.setDescription("");
            saveObject(contentType, isNew);
            retMap.put(Long.valueOf(contentType.getId()), contentType);
        }
        return retMap;
    }

    private void parseSubContentTypes(Map<Long, ContentType> contentMap) {
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "SubContentTypes");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "SubContentTypes")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "SubContentType")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {

                            SubContentType subType = new SubContentType();
                            subType.setId(id.intValue());
                            subType.setName(getChildNodeValue(itemNode, "Name"));
                            subType.setDescription(getChildNodeValue(itemNode, "Description"));
                            for (Node contentTypeNode : getNode(itemNode.getChildNodes(), "ContentType")) {
                                String refIdString = getAttribute(contentTypeNode, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        ContentType parentType = contentMap.get(refId);
                                        if (parentType != null) {
                                            subType.setParentContentType(parentType);
                                        } else {
                                            log.error("SubContentType with id='"
                                                            + id
                                                            + "' has a reference to a ContentType object with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            HibernateUtil.getManager().saveObjectWithPropagationRequired(subType, true);
                        }
                    }

                }//end of for
            }//end fo for
        }

    }

    private void parseUserDeactivateReasonTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "UserDeactivateReasonTypes", "DeactivateReasonType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            UserDeactivateReasonType reasonType = new UserDeactivateReasonType();
            reasonType.setId(key.longValue());
            reasonType.setName(name);
            saveObject(reasonType, isNew);
        }
    }

    private void parseDailyFrequencyTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "DailyFrequencyTypes", "DailyFrequencyType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            DailyFrequencyType dailyFrequencyType = new DailyFrequencyType();
            dailyFrequencyType.setId(key.intValue());
            dailyFrequencyType.setName(name);
            dailyFrequencyType.setDescription("");
            saveObject(dailyFrequencyType, isNew);
        }
    }

    private void parseFrequencyTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "FrequencyTypes", "FrequencyType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            FrequencyType frequencyType = new FrequencyType();
            frequencyType.setId(key.intValue());
            frequencyType.setName(name);
            frequencyType.setDescription("");
            saveObject(frequencyType, isNew);
        }
    }

    private void parseDeliveryEventTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "DeliveryEventTypes", "DeliveryEventType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            DeliveryEventType deliveryEventType = new DeliveryEventType();
            deliveryEventType.setId(key.intValue());
            deliveryEventType.setName(name);
            deliveryEventType.setDescription("");
            saveObject(deliveryEventType, isNew);
        }
    }

    private void parseEventTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "EventTypes", "EventType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            EventType eventType = new EventType();
            eventType.setId(key.longValue());
            eventType.setName(name);
            eventType.setDescription("");
            saveObject(eventType, isNew);
        }
    }

    private void parseSourceTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "SourceTypes", "SourceType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            SourceType sourceType = new SourceType();
            sourceType.setId(key);
            sourceType.setName(name);
            sourceType.setDescription("");
            saveObject(sourceType, isNew);
        }
    }

    private void parseTranslationProviders() {
        NodeList nodeList=MessagePointXmlParser.getNodeListByName(document,"TranslationProviders");
        if (nodeList != null) {
            for (Node groupNode:getNode(nodeList,"TranslationProviders")) {
                for (Node itemNode:getNode(groupNode.getChildNodes(),"TranslationProvider")) {
                    String idString = getAttribute(itemNode,"id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        long id = Long.parseLong(idString);
                        if (id > 0) {
                            TranslationProvider translationProvider = new TranslationProvider();
                            translationProvider.setId(id);
                            translationProvider.setName(getChildNodeValue(itemNode,"Name"));
                            translationProvider.setCode(getChildNodeValue(itemNode,"Code"));
                            translationProvider.setNameCode(getChildNodeValue(itemNode,"NameCode"));
                            saveObject(translationProvider,true);
                        }
                    }
                }
            }
        }
    }

    private void parsePodTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "PodTypes", "PodType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            PodType podType = new PodType();
            podType.setId(key);
            podType.setName(name);
            podType.setDescription("");
            saveObject(podType, isNew);
        }
    }

    private void parsePodStatuses() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "PodStatuses", "PodStatus");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            PodStatus podStatus = new PodStatus();
            podStatus.setId(key);
            podStatus.setName(name);
            podStatus.setDescription("");
            saveObject(podStatus, isNew);
        }
    }

    private void parseDEServerCommunicationTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "DEServerCommunicationTypes", "CommunicationType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            DEServerCommunicationType type = new DEServerCommunicationType();
            type.setId(key);
            type.setName(name);
            type.setDescription("");
            saveObject(type, isNew);
        }
    }

    private void parseLayoutTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "LayoutTypes", "LayoutType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            LayoutType layoutType = new LayoutType();
            layoutType.setId(key);
            layoutType.setName(name);
            layoutType.setDescription("");
            saveObject(layoutType, isNew);
        }
    }

    private void parseEncodingTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "EncodingTypes", "EncodingType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            EncodingType encodingType = new EncodingType();
            encodingType.setId(key);
            encodingType.setName(name);
            encodingType.setDescription("");
            saveObject(encodingType, isNew);
        }
    }

    private void parseDataRecordLevels() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "DataRecordLevels", "DataRecordLevel");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            DataRecordLevel dataRecordLevel = new DataRecordLevel();
            dataRecordLevel.setId(key);
            dataRecordLevel.setName(name);
            String localeName = ApplicationUtil.getMessage(name);
            String value = localeName.trim().substring(localeName.trim().indexOf(" ")).trim();
            dataRecordLevel.setValue(Integer.parseInt(value));
            saveObject(dataRecordLevel, isNew);
        }
    }

    private void parseAggregationOperators() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "AggregationOperators", "AggregationOperator");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String value = names.get(key);
            AggregationOperator aggregationOperator = new AggregationOperator();
            aggregationOperator.setId(key);
            aggregationOperator.setOperator(value);
            saveObject(aggregationOperator, isNew);
        }
    }

    private void parseConditionOperators() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "ConditionOperators", "ConditionOperator");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String value = names.get(key);
            ConditionOperator conditionOperator = new ConditionOperator();
            conditionOperator.setId(key);
            conditionOperator.setOperator(value);
            saveObject(conditionOperator, isNew);
        }
    }

    private TreeMap<Long, DataComparison> parseDataComparisonTypes() {
        TreeMap<Long, DataComparison> dataComparisonTypesById = new TreeMap<>();
        boolean isNew = true;

        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "DataComparisonTypes");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "DataComparisonTypes")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "DataComparisonType")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Integer id = Integer.parseInt(idString);
                        if ((id != null) && (id > 0)) {

                            DataComparison dataComparison = new DataComparison();
                            dataComparison.setId(id);

                            dataComparison.setName(getChildNodeValue(itemNode, "Name"));

                            dataComparison.setDescription("");
                            dataComparison.setScheme(getChildNodeValue(itemNode, "Scheme"));

                            saveObject(dataComparison, isNew);
                            dataComparisonTypesById.put(dataComparison.getId(), dataComparison);
                        }
                    }
                }
            }
        }
        return dataComparisonTypesById;
    }

    private void parseDateDataValueTypes() {
        boolean isNew = true;
        TreeMap<Integer, String> names = parseSubItemText(document, "DateDataValueTypes", "DateDataValueType");
        Set<Integer> keys = names.keySet();
        for (Integer key : keys) {
            String name = names.get(key);
            DateDataValue dateDataValue = new DateDataValue();
            dateDataValue.setId(key.longValue());
            dateDataValue.setName(name);
            dateDataValue.setDescription("");
            saveObject(dateDataValue, isNew);
        }
    }

    private void parseConditionTypes() {
        boolean isNew = true;
        NodeList nodeList = document.getElementsByTagName("ConditionTypes");
        for (Node groupNode : getNode(nodeList, "ConditionTypes")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "ConditionType")) {
                String idString = getAttribute(itemNode, "id");
                int id = Integer.parseInt(idString);
                if (id > 0) {
                    ConditionType conditionType = new ConditionType();
                    conditionType.setId(id);
                    conditionType.setName(getChildNodeValue(itemNode, "Name"));
                    conditionType.setParameterized(Boolean.valueOf(getChildNodeValue(itemNode, "Name")).booleanValue());
                    conditionType.setDescription("");
                    saveObject(conditionType, isNew);
                }
            }
        }
    }

    private void parseRecordTypes() {
        boolean isNew = true;
        NodeList nodeList = document.getElementsByTagName("RecordTypes");
        for (Node groupNode : getNode(nodeList, "RecordTypes")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "RecordType")) {
                String idString = getAttribute(itemNode, "id");
                int id = Integer.parseInt(idString);
                if (id > 0) {
                    RecordType recordType = new RecordType();
                    recordType.setId(id);
                    recordType.setName(getChildNodeValue(itemNode, "Name"));
                    recordType.setStringId(getAttribute(itemNode, "stringid"));
                    saveObject(recordType, isNew);
                }
            }
        }
    }

    private void parseReportJobMetadataParts() {
        boolean isNew = true;
        NodeList nodeList = document.getElementsByTagName("JobMetadataDetailParts");
        for (Node groupNode : getNode(nodeList, "JobMetadataDetailParts")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "JobMetadataDetailPart")) {
                String idString = getAttribute(itemNode, "id");
                String seqString = getAttribute(itemNode, "sequence");
                if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                    Long id = Long.parseLong(idString);
                    int sequence = Integer.parseInt(seqString);
                    if ((id != null) && (id > 0)) {
                        JobMetadataDetailPart jobMetadataDetailPart = new JobMetadataDetailPart();
                        jobMetadataDetailPart.setId(id);
                        jobMetadataDetailPart.setSequence(sequence);
                        jobMetadataDetailPart.setName(getChildNodeValue(itemNode, "Name"));
                        // Save the object to the database
                        saveObject(jobMetadataDetailPart, isNew);
                    }
                }
            }
        }
    }

    private void parseAppVersion() {
        boolean isNew = true;
        TreeMap<Long, KeyValuePair> keyValuePairs = parseKeyValuePairsById(document,
                "AppVersions",
                "AppVersion",
                "Key",
                "Value");

        Properties prop = new Properties();
        try {
            InputStream stream = InitializeMetadataParser.class.getClassLoader().getResourceAsStream("appbuild.properties");
            prop.load(stream);
        } catch (Exception ex) {
            log.error("Error:", ex);
        }



        Set<Long> keys = keyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair keyValuePair = keyValuePairs.get(key);
            AppVersion appVersion = new AppVersion();
            appVersion.setId(key);
            appVersion.setKey(keyValuePair.getKey());
            appVersion.setValue(prop.getProperty(keyValuePair.getValue()));
            if (primaryUser != null)
                appVersion.setCreatedBy(primaryUser.getId());
            saveObject(appVersion, isNew);
        }
    }

    private void parseSystemThemes() {
        boolean isNew = true;
        TreeMap<Long, KeyValuePair> keyValuePairs = parseKeyValuePairsById(document,
                "SystemThemes",
                "SystemTheme",
                "Name",
                "Folder");
        Set<Long> keys = keyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair keyValuePair = keyValuePairs.get(key);
            SystemTheme systemTheme = new SystemTheme();
            systemTheme.setId(key);
            systemTheme.setName(keyValuePair.getKey());
            systemTheme.setFolder(keyValuePair.getValue());
            saveObject(systemTheme, isNew);
        }
    }

    private void parseBackgroundThemes() {
        boolean isNew = true;
        TreeMap<Long, KeyValuePair> keyValuePairs = parseKeyValuePairsById(document,
                "BackgroundThemes",
                "BackgroundTheme",
                "Name",
                "Filename");
        Set<Long> keys = keyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair keyValuePair = keyValuePairs.get(key);
            BackgroundTheme bkgrndTheme = new BackgroundTheme();
            bkgrndTheme.setId(key);
            bkgrndTheme.setName(keyValuePair.getKey());
            bkgrndTheme.setFilename(keyValuePair.getValue());
            saveObject(bkgrndTheme, isNew);
        }
    }

    private void parseSystemProperties() {
        boolean isNew = true;
        boolean isReadonly = false;
        NodeList nodeList = document.getElementsByTagName("SystemProperties");
        for (Node groupNode : getNode(nodeList, "SystemProperties")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "SystemProperty")) {
                String idString = getAttribute(itemNode, "id");
                String readonlyString = getAttribute(itemNode, "readonly");

                if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                    Long id = Long.parseLong(idString);
                    if ((id != null) && (id > 0)) {
                        if (readonlyString != null) {
                            isReadonly = Boolean.parseBoolean(readonlyString);
                        }
                        SystemProperty systemProperty = new SystemProperty();
                        systemProperty.setId(id);
                        systemProperty.setKey(getChildNodeValue(itemNode, "Key"));
                        String tmpValue = getChildNodeValue(itemNode, "Value");
                        systemProperty.setValue((tmpValue == null) ? "" : processValue(tmpValue));
                        systemProperty.setDescription("");
                        systemProperty.setReadonly(isReadonly);

                        // Save the object to the database
                        saveObject(systemProperty, isNew);
                    }
                }
            }
        }
    }

    private void parseMessagepointLocales() {
        NodeList nodeList = document.getElementsByTagName("MessagepointLocales");
        for (Node groupNode : getNode(nodeList, "MessagepointLocales")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "MessagepointLocale")) {
                String idString = getAttribute(itemNode, "id");
                if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                    long id = Long.parseLong(idString);
                    boolean favourite = Boolean.valueOf(getChildNodeValue(itemNode, "Favourite"));
                    boolean defaultLocale = Boolean.valueOf(getChildNodeValue(itemNode, "DefaultLocale"));
                    boolean langFavourite = Boolean.valueOf(getChildNodeValue(itemNode, "LanguageFavourite"));
                    String name = getChildNodeValue(itemNode, "Name");
                    String displayCode = getChildNodeValue(itemNode, "DisplayCode");
                    String code = getChildNodeValue(itemNode, "Code");
                    String langName = getChildNodeValue(itemNode, "LanguageName");
                    String langDisplayCode = getChildNodeValue(itemNode, "LanguageDisplayCode");
                    String langCode = getChildNodeValue(itemNode, "LanguageCode");
                    long langId = Long.valueOf(getChildNodeValue(itemNode, "LanguageId"));
                    long langGroup = Long.valueOf(getChildNodeValue(itemNode, "LanguageGroup"));

                    MessagepointLocale mpLocale = MessagepointLocale.findById(id);
                    if (mpLocale == null) {
                        mpLocale = new MessagepointLocale();
                        mpLocale.setId(id);
                        mpLocale.setFavourite(favourite);
                        mpLocale.setDefaultLocale(defaultLocale);
                        mpLocale.setLanguageFavourite(langFavourite);
                        mpLocale.setName(name);
                        mpLocale.setDisplayCode(displayCode);
                        mpLocale.setCode(code);
                        mpLocale.setLanguageName(langName);
                        mpLocale.setLanguageDisplayCode(langDisplayCode);
                        mpLocale.setLanguageCode(langCode);
                        mpLocale.setLanguageId(langId);
                        mpLocale.setLanguageGroup(langGroup);
                    }
                    else {
                        if (rebuild) {
                            // Modified these parameters only if rebuild of schema is requested
                            mpLocale.setFavourite(favourite);
                            mpLocale.setDefaultLocale(defaultLocale);
                            mpLocale.setLanguageFavourite(langFavourite);
                        }

                        mpLocale.setName(name);
                        mpLocale.setDisplayCode(displayCode);
                        mpLocale.setCode(code);
                        mpLocale.setLanguageName(langName);
                        mpLocale.setLanguageDisplayCode(langDisplayCode);
                        mpLocale.setLanguageCode(langCode);
                        mpLocale.setLanguageId(langId);
                        mpLocale.setLanguageGroup(langGroup);
                    }

                    mpLocale.save();
                }
            }
        }
    }

    private void parseApplicationLocales() {
        com.prinova.messagepoint.model.Node currentNode = com.prinova.messagepoint.model.Node.getCurrentNode();

        if (currentNode == null) {
            currentNode = com.prinova.messagepoint.model.Node.findBySchema(MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName());
            if (currentNode == null) {
                currentNode = com.prinova.messagepoint.model.Node.findBySchema(null);
            }
        }

        Branch currentBranch = currentNode.getBranch();
        boolean isPodMasterNode = MessagepointCurrentTenantIdentifierResolver.isPodMasterNode();
        com.prinova.messagepoint.model.Node parentNode = com.prinova.messagepoint.model.Node.getParentNode(currentNode.getId());
        NodeList nodeList = document.getElementsByTagName("ApplicationLocales");
        for (Node groupNode : getNode(nodeList, "ApplicationLocales")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "ApplicationLocale")) {
                String langCode = getChildNodeValue(itemNode, "Code");
                MessagepointLocale mpLocale = MessagepointLocale.getLanguageLocaleByLocaleCode(langCode);
                if (mpLocale != null) {
                    ApplicationLocale appLocale = ApplicationLocale.findByMpLocaleId(mpLocale.getId());
                    boolean enable = Boolean.valueOf(getChildNodeValue(itemNode, "Enable"));
                    boolean accessible = Boolean.valueOf(getChildNodeValue(itemNode, "Accessible"));
                    if (appLocale == null) {
                        appLocale = new ApplicationLocale();
                        appLocale.setMessagepointLocale(mpLocale);

                        if (isPodMasterNode) {    // If it is pod master, apply the metadata settings directly
                            appLocale.setEnable(enable);
                            appLocale.setAccessible(accessible);
                        } else {    // Otherwise, get the settings from parent node
                            SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(parentNode.getSchemaName());
                            ApplicationLocale parentAppLocale = ApplicationLocale.findByMpLocaleId(mpLocale.getId());
                            boolean parentEnable = parentAppLocale.getEnable();
                            boolean parentAccessible = parentAppLocale.getAccessible();
                            HibernateUtil.getManager().restoreSession(mainSessionHolder);
                            appLocale.setEnable(parentEnable);
                            appLocale.setAccessible(parentAccessible);
                        }

                        // Save the object to the database
                        saveObject(appLocale, true);
                    } else {
                        if (appLocale.getEnable()) {    // db entry is enable
                            if (!enable) {    // Disable all
                                appLocale.setEnable(enable);
                                appLocale.setAccessible(accessible);
                                saveObject(appLocale, false);
                            }
                        } else {    // db entry is disable
                            if (enable) {
                                if (isPodMasterNode) {
                                    // Cascade changes to children
                                    List<com.prinova.messagepoint.model.Node> allChildren = currentBranch.getAllAccessibleChildrenNodes(true); // with DCS
                                    long branchId = currentBranch.getId();
                                    for (com.prinova.messagepoint.model.Node node : allChildren) {
                                        SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(node.getSchemaName());
                                        ApplicationLocale.applyApplicationLocaleChanges(mpLocale.getId(), true, false, accessible, node.getBranch().getId() == branchId);
                                        HibernateUtil.getManager().restoreSession(mainSessionHolder);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Update the messagepoint and messagepointQE file root if one is specified via system property.
     */
    private String processValue(String value) {
        if (value == null) {
            return null;
        }
        // only replace if system property is specified.
        if (messagepointFileroot != null) {
            String token = "@@MESSAGEPOINT_FILEROOT@@";
            if (value.contains(token)) {
                value = value.replace(token, messagepointFileroot);
            }
        }

        if (messagepointJobFileroot != null) {
            String token = "@@MESSAGEPOINTJOB_FILEROOT@@";
            if (value.contains(token)) {
                value = value.replace(token, messagepointJobFileroot);
            }
        }

        if (messagepointJobWorkingFileroot != null) {
            String token = "@@MESSAGEPOINTJOB_WORKING_FILEROOT@@";
            if (value.contains(token)) {
                value = value.replace(token, messagepointJobWorkingFileroot);
            }
        }

        if (messagepointOutputFileroot != null) {
            String token = "@@MESSAGEPOINT_OUTPUT_FILEROOT@@";
            if (value.contains(token)) {
                value = value.replace(token, messagepointOutputFileroot);
            }
        }

        if (messagepointFileroot != null) {
            String token = "@@MESSAGEPOINT_SCHEMA@@";
            if (value.contains(token)) {
                return value.replace(token, schema);
            }
        }
        if (messagepointQEFileroot != null) {
            String token = "@@MESSAGEPOINTQE_FILEROOT@@";
            if (value.contains(token)) {
                return value.replace(token, messagepointQEFileroot);
            }
        }
        if (compositionEnginesFileroot != null) {
            String token = "@@COMPOSITIONENGINES_FILEROOT@@";
            if (value.contains(token)) {
                return value.replace(token, compositionEnginesFileroot);
            }
        }
        if (buildXml != null) {
            String token = "@@UMH_BUILD_XML@@";
            if (value.contains(token))
                return PathUtil.normalize(value.replace(token, buildXml), false);
        }
        if (antHome != null) {
            String token = "@@ANT_HOME@@";
            if (value.contains(token))
                return PathUtil.normalize(value.replace(token, antHome), false);
        }
        return value;
    }

    private TreeMap<Long, DataSubtype> parseDataSubtypes(TreeMap<Long, DataComparison> dataComparisonTypes) {
        boolean isNew = true;
        TreeMap<Long, DataSubtype> dataSubtypes = new TreeMap<>();
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "DataSubtypes");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "DataSubtypes")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "DataSubtype")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            boolean selectable = false;
                            String selectableString = getChildNodeValue(itemNode, "Selectable");
                            if ((selectableString != null) && (selectableString.equalsIgnoreCase("TRUE"))) {
                                selectable = true;
                            }
                            DataSubtype dataSubtype = new DataSubtype();
                            dataSubtype.setId(id.intValue());
                            dataSubtype.setName(getChildNodeValue(itemNode, "Name"));
                            dataSubtype.setSelectable(selectable);
                            for (Node supportedComparisonTypes : getNode(itemNode.getChildNodes(),
                                    "DataComparisonTypes")) {
                                for (Node supportedComparisonTypeNode : getNode(supportedComparisonTypes.getChildNodes(),
                                        "DataComparisonType")) {
                                    String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            DataComparison dataComparison = dataComparisonTypes.get(refId);
                                            if (dataComparison != null) {
                                                if (dataSubtype.getDataComparisons() == null) {
                                                    dataSubtype.setDataComparisons(new HashSet<>());
                                                }
                                                dataSubtype.getDataComparisons().add(dataComparison);
                                            } else {
                                                log.error("DataSubtype with id='"
                                                                + id
                                                                + "' has a reference to a DataComparison with refid='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }
                                }
                            }
                            // Now, save the DataSubtype object to the database
                            saveObject(dataSubtype, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            dataSubtypes.put(id, dataSubtype);
                        }
                    }
                }
            }
        }
        return dataSubtypes;
    }

    private void parseDataTypes(TreeMap<Long, DataSubtype> dataSubtypes) {
        boolean isNew = true;
        NodeList nodeList = document.getElementsByTagName("DataTypes");
        for (Node groupNode : getNode(nodeList, "DataTypes")) {
            for (Node itemNode : getNode(groupNode.getChildNodes(), "DataType")) {
                String idString = getAttribute(itemNode, "id");
                if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                    Long id = Long.parseLong(idString);
                    if ((id != null) && (id > 0)) {
                        boolean selectable = false;
                        String selectableString = getChildNodeValue(itemNode, "Selectable");
                        if ((selectableString != null) && (selectableString.equalsIgnoreCase("TRUE"))) {
                            selectable = true;
                        }
                        DataType dataType = new DataType();
                        dataType.setId(id.intValue());
                        dataType.setName(getChildNodeValue(itemNode, "Name"));
                        dataType.setSelectable(selectable);
                        for (Node supportedComparisonTypes : getNode(itemNode.getChildNodes(), "DataSubtypes")) {
                            for (Node supportedComparisonTypeNode : getNode(supportedComparisonTypes.getChildNodes(),
                                    "DataSubtype")) {
                                String refIdString = getAttribute(supportedComparisonTypeNode, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        DataSubtype dataSubtype = dataSubtypes.get(refId);
                                        if (dataSubtype != null) {
                                            if (dataType.getDataSubtypes() == null) {
                                                dataType.setDataSubtypes(new HashSet<>());
                                            }
                                            dataType.getDataSubtypes().add(dataSubtype);
                                        } else {
                                            log.error("DataType with id='"
                                                            + id
                                                            + "' has a reference to a DataSubtype with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                        }
                        // Now, save the DataType object to the database
                        saveObject(dataType, isNew);
                    }
                }
            }
        }
    }

    private List<Tree> parseTree(Node itemNode, Tree parent) {
        ArrayList<Tree> trees = new ArrayList<>();

        String idString = getAttribute(itemNode, "id");
        long id = 0;
        if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
            id = Long.parseLong(idString);
        } else {
            id = currentMaxTreeId + 1;
        }

        if (Math.max(currentMaxTreeId, id) == id)
            currentMaxTreeId = id;

        Tree newTreeObject = new Tree();
        newTreeObject.setId(id);
        newTreeObject.setQuery(getChildNodeValue(itemNode, "Query"));
        newTreeObject.setParameter(getChildNodeValue(itemNode, "Parameter"));
        newTreeObject.setUrl(getChildNodeValue(itemNode, "Url"));
        String itemValue = getChildNodeValue(itemNode, "Type");
        newTreeObject.setType((Integer) ReflectionUtils.getPublicStaticField(itemValue));
        newTreeObject.setIcon(getChildNodeValue(itemNode, "Icon"));
        newTreeObject.setNodes(new ArrayList<>());

        for (Node referencedNodes : getNode(itemNode.getChildNodes(), "Nodes")) {
            for (Node navigationTree : getNode(referencedNodes.getChildNodes(), "NavigationTree")) {
                List<Tree> childTrees = parseTree(navigationTree, newTreeObject);
                newTreeObject.getNodes().addAll(childTrees);
            }
        }
        trees.add(newTreeObject);
        return trees;
    }

    private TreeMap<Long, Tree> parseNavigationTrees() {
        TreeMap<Long, Tree> trees = new TreeMap<>();
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "NavigationTrees");
        int count = 0;
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "NavigationTrees")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "NavigationTree")) {
                    List<Tree> treeArray = parseTree(itemNode, null);

                    // Save a copy of the object in a TreeMap so it can be used
                    // in subsequent XML references
                    for (Tree savedTree : treeArray) {
                        saveObject(savedTree, true);
                        trees.put(savedTree.getId(), savedTree);
                        count++;
                    }
                    if (count >= 50) {
                        this.session.flush();
                        count = 0;
                    }
                }
            }
        }
        return trees;
    }

    private TreeMap<Long, DropDownMenu> parseNavigationDropDownMenus(TreeMap<Long, Long> dropDownMenuTabRelationships,
                                                                     TreeMap<Long, Permission> permissions) {
        TreeMap<Long, DropDownMenu> dropDownMenus = new TreeMap<>();
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "NavigationDropDownMenus");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "NavigationDropDownMenus")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "NavigationDropDownMenu")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            DropDownMenu dropDownMenu = new DropDownMenu();
                            dropDownMenu.setId(id);
                            dropDownMenu.setName(getChildNodeValue(itemNode, "Name"));
                            dropDownMenu.setOrdering(Integer.parseInt(getChildNodeValue(itemNode, "Order")));
                            dropDownMenu.setRequires(getChildNodeValue(itemNode, "Requires"));
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "NavigationTab")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        dropDownMenuTabRelationships.put(id, refId);
                                    }
                                }
                            }
                            // Now, save the object to the database
                            saveObject(dropDownMenu, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            dropDownMenus.put(id, dropDownMenu);
                        }
                    }
                }
            }
        }
        return dropDownMenus;
    }

    private TreeMap<Long, DropDownMenuItem> parseNavigationDropDownMenuItems(TreeMap<Long, Tree> trees,
                                                                             TreeMap<Long, DropDownMenu> dropDownMenus, TreeMap<Long, Permission> permissions) {
        TreeMap<Long, DropDownMenuItem> dropDownMenuItems = new TreeMap<>();
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "NavigationDropDownMenuItems");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "NavigationDropDownMenuItems")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "NavigationDropDownMenuItem")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            DropDownMenuItem dropDownMenuItem = new DropDownMenuItem();
                            dropDownMenuItem.setId(id);
                            dropDownMenuItem.setName(getChildNodeValue(itemNode, "Name"));
                            dropDownMenuItem.setOrdering(Integer.parseInt(getChildNodeValue(itemNode, "Order")));
                            dropDownMenuItem.setIcon(getChildNodeValue(itemNode, "Icon"));
                            dropDownMenuItem.setRequires(getChildNodeValue(itemNode, "Requires"));
                            String type = getChildNodeValue(itemNode, "Type");
                            if (type == null) {
                                type = DropDownMenuItem.TYPE_ORDINARY_CODE;
                            }
                            dropDownMenuItem.setType(type);
                            dropDownMenuItem.setUrl(getChildNodeValue(itemNode, "Url"));
                            dropDownMenuItem.setAuthorizationType(getChildNodeValue(itemNode, "AuthorizationType"));
                            // <NavigationTree refid="1" />
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "NavigationTree")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        Tree referencedTree = trees.get(refId);
                                        if (referencedTree != null) {
                                            dropDownMenuItem.setTree(referencedTree);
                                        } else {
                                            log.error("DropDownMenuItem object with id='"
                                                            + id
                                                            + "' has a reference to a NavigationTree object with refId='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            // <NavigationDropDownMenu refid="1" />
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "NavigationDropDownMenu")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        DropDownMenu referencedDropDownMenu = dropDownMenus.get(refId);
                                        if (referencedDropDownMenu != null) {
                                            dropDownMenuItem.setMenu(referencedDropDownMenu);
                                            referencedDropDownMenu.getItems().add(dropDownMenuItem);
                                        } else {
                                            log.error("DropDownMenuItem object with id='"
                                                            + id
                                                            + "' has a reference to a DropDownMenu object with refId='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            dropDownMenuItem.setAuthorizationType(getChildNodeValue(itemNode, "AuthorizationType"));
                            // <Permissions>
                            // <Permission refid="1" />
                            // <Permission refid="2" />
                            // </Permissions>
                            for (Node permissionsNode : getNode(itemNode.getChildNodes(), "Permissions")) {
                                Set<Permission> referencedPermissions = new HashSet<>();
                                for (Node permissionNode : getNode(permissionsNode.getChildNodes(), "Permission")) {
                                    String refIdString = getAttribute(permissionNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                referencedPermissions.add(referencedPermission);
                                            } else {
                                                log.error("DropDownMenuItem object with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refId='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }

                                }
                                dropDownMenuItem.setPermissions(referencedPermissions);
                            }

                            // Now, save the object to the database
                            saveObject(dropDownMenuItem, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            dropDownMenuItems.put(id, dropDownMenuItem);
                        }
                    }
                }
            }
        }
        return dropDownMenuItems;
    }

    private TreeMap<Long, NavigationTab> parseNavigationTabs(TreeMap<Long, DropDownMenuItem> dropDownMenuItems,
                                                             TreeMap<Long, Permission> permissions) {
        TreeMap<Long, NavigationTab> navigationTabs = new TreeMap<>();
        boolean isNew = true;
        // <NavigationTab id="1">
        // <Order>1</Order>
        // <Name>Tasks</Name>
        // <StringId>page.label.tasks</StringId>
        // <PermissionId>2</PermissionId>
        // <Url>tasks/index.jsp</Url>
        // <DefaultMenuItem refid="200" />
        // </NavigationTab>
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "NavigationTabs");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "NavigationTabs")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "NavigationTab")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            NavigationTab navigationTab = new NavigationTab();
                            navigationTab.setId(id);
                            navigationTab.setName(getChildNodeValue(itemNode, "Name"));
                            navigationTab.setOrdering(Integer.parseInt(getChildNodeValue(itemNode, "Order")));
                            navigationTab.setStringId(getChildNodeValue(itemNode, "StringId"));
                            navigationTab.setUrl(getChildNodeValue(itemNode, "Url"));
                            navigationTab.setAuthorizationType(getChildNodeValue(itemNode, "AuthorizationType"));
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "DefaultMenuItem")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        DropDownMenuItem referencedDropDownMenuItem = dropDownMenuItems.get(refId);
                                        if (referencedDropDownMenuItem != null) {
                                            navigationTab.setDefaultItem(referencedDropDownMenuItem);
                                        } else {
                                            log.error("NavigationTab object with id='"
                                                            + id
                                                            + "' has a reference to a DropDownMenuItem object with refId='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "DefaultMenuItemMap")) {
                                for (Node defaultItemMapNode : getNode(referencedNodes.getChildNodes(), "Item")) {
                                    String type = getAttribute(defaultItemMapNode, "type");
                                    if ((type != null) && (!"".equalsIgnoreCase(type))) {
                                        long refId = Long.valueOf(defaultItemMapNode.getTextContent()).longValue();
                                        DropDownMenuItem referencedDropDownMenuItem = dropDownMenuItems.get(refId);
                                        if (referencedDropDownMenuItem != null) {
                                            if (navigationTab.getDefaultItemMap() == null) {
                                                navigationTab.setDefaultItemMap(new TreeMap<>());
                                            }
                                            navigationTab.getDefaultItemMap().put(type, referencedDropDownMenuItem);
                                        } else {
                                            log.error("NavigationTab object with id='"
                                                            + id
                                                            + "' has a reference to a DropDownMenuItem object with refId='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }

                            for (Node permissionsNode : getNode(itemNode.getChildNodes(), "Permissions")) {
                                Set<Permission> referencedPermissions = new HashSet<>();
                                for (Node permissionNode : getNode(permissionsNode.getChildNodes(), "Permission")) {
                                    String refIdString = getAttribute(permissionNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                referencedPermissions.add(referencedPermission);
                                            } else {
                                                log.error("NavigationTab object with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refId='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }

                                }
                                navigationTab.setPermissions(referencedPermissions);
                            }

                            // Now, save the object to the database
                            saveObject(navigationTab, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            navigationTabs.put(id, navigationTab);
                        }
                    }
                }
            }
        }
        return navigationTabs;
    }

    private TreeMap<Long, Workflow> parseWorkflows() {
        TreeMap<Long, Workflow> workflows = new TreeMap<>();
        boolean isNew = true;
        TreeMap<Long, KeyValuePair> keyValuePairs = parseKeyValuePairsById(document,
                "Workflows",
                "Workflow",
                "Name",
                "ClassName");
        Set<Long> keys = keyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair keyValuePair = keyValuePairs.get(key);
            Workflow workflow = new Workflow();
            workflow.setId(key);
            workflow.setName(keyValuePair.getKey());
            workflow.setClassName(keyValuePair.getValue());
            saveObject(workflow, isNew);
            workflows.put(workflow.getId(), workflow);
        }
        return workflows;
    }

    private TreeMap<Long, WorkflowState> parseWorkflowStates() {
        TreeMap<Long, WorkflowState> workflowStates = new TreeMap<>();
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "WorkflowStates");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "WorkflowStates")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "WorkflowState")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            WorkflowState workflowState = new WorkflowState();
                            workflowState.setId(id);
                            workflowState.setName(getChildNodeValue(itemNode, "Name"));
                            workflowState.setStringId(getChildNodeValue(itemNode, "StringId"));
                            // Now, save the object to the database
                            saveObject(workflowState, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            workflowStates.put(id, workflowState);
                        }
                    }
                }
            }
        }
        return workflowStates;
    }

    /*
	 * <WorkflowPosition id="1"> <Workflow refid="1" /> <WorkflowState refid="1" /> <Required>TRUE</Required>
	 * <RequiresApproval>true</RequiresApproval> <Enabled>TRUE</Enabled> <Next refid="2" /> <Name>Overview</Name>
	 * <Permission refid="1" /> <AssignedUserId refid="1" /> </WorkflowPosition>
	 */
    private TreeMap<Long, WorkflowPosition> parseWorkflowPositions(TreeMap<Long, Workflow> workflows,
                                                                   TreeMap<Long, WorkflowState> workflowStates, TreeMap<Long, Long> workflowPositionPreviousReferences,
                                                                   TreeMap<Long, Long> workflowPositionNextReferences, TreeMap<Long, Permission> permissions) {
        TreeMap<Long, WorkflowPosition> workflowPositions = new TreeMap<>();
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "WorkflowPositions");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "WorkflowPositions")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "WorkflowPosition")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            boolean required = false;
                            String requiredString = getChildNodeValue(itemNode, "Required");
                            if ((requiredString != null) && (requiredString.equalsIgnoreCase("TRUE"))) {
                                required = true;
                            }
                            boolean enabled = false;
                            String enabledString = getChildNodeValue(itemNode, "Enabled");
                            if ((enabledString != null) && (enabledString.equalsIgnoreCase("TRUE"))) {
                                enabled = true;
                            }
                            boolean requiresApproval = false;
                            String rquiresApprovalString = getChildNodeValue(itemNode, "RequiresApproval");
                            if ((rquiresApprovalString != null) && (rquiresApprovalString.equalsIgnoreCase("TRUE"))) {
                                requiresApproval = true;
                            }
                            WorkflowPosition workflowPosition = null;
                            try {
                                workflowPosition = HibernateUtil.getManager().getObject(WorkflowPosition.class, id);
                            } catch (Exception e) {
                            }
                            if (workflowPosition == null) {
                                workflowPosition = new WorkflowPosition();
                                workflowPosition.setId(id);
                                workflowPosition.setName(getChildNodeValue(itemNode, "Name"));
                                workflowPosition.setRequired(required);
                                workflowPosition.setEnabled(enabled);
                                workflowPosition.setRequiresApproval(requiresApproval);
                            }
                            // Resolve the Workflow object reference
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "Workflow")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        Workflow referencedWorkflow = workflows.get(refId);
                                        if (referencedWorkflow != null) {
                                            workflowPosition.setWorkflow(referencedWorkflow);
                                        } else {
                                            log.error("WorkflowPosition object with id='"
                                                            + id
                                                            + "' has a reference to a Workflow object with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            // Resolve the WorkflowState object reference
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "WorkflowState")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowState referencedWorkflowState = workflowStates.get(refId);
                                        if (referencedWorkflowState != null) {
                                            workflowPosition.setWorkflowState(referencedWorkflowState);
                                        } else {
                                            log.error("WorkflowPosition object with id='"
                                                            + id
                                                            + "' has a reference to a WorkflowState object with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            // Try and resolve the Previous and Next object
                            // references
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "Previous")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowPosition referencedWorkflowPosition = workflowPositions.get(refId);
                                        if (referencedWorkflowPosition != null) {
                                            workflowPosition.setPrevious(referencedWorkflowPosition);
                                        } else {
                                            // Note this relationship so it can
                                            // be resolved later on
                                            workflowPositionPreviousReferences.put(id, refId);
                                        }
                                    }
                                }
                            }
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "Next")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowPosition referencedWorkflowPosition = workflowPositions.get(refId);
                                        if (referencedWorkflowPosition != null) {
                                            workflowPosition.setNext(referencedWorkflowPosition);
                                        } else {
                                            // Note this relationship so it can
                                            // be resolved later on
                                            workflowPositionNextReferences.put(id, refId);
                                        }
                                    }
                                }
                            }
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "AssignedUserId")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        if (this.primaryUser == null) {
                                            log.warn("(this.primaryUser == null)! Cannot associate WorkflowPosition object's reference to an AssignedUserId='"
                                                            + refId
                                                            + "'! Skipping this reference and continuing processing...");
                                        } else if (refId == this.primaryUser.getId()) {
                                            workflowPosition.setAssignedUser(this.primaryUser);
                                        } else {
                                            log.warn("(refId == this.primaryUser.getId())! Cannot associate WorkflowPosition object's reference to an AssignedUserId='"
                                                            + refId
                                                            + "'! Skipping this reference and continuing processing...");
                                        }
                                    }
                                }
                            }

                            workflowPosition.setAuthorizationType(getChildNodeValue(itemNode, "AuthorizationType"));
                            // <Permissions>
                            // <Permission refid="1" />
                            // <Permission refid="2" />
                            // </Permissions>
                            for (Node permissionsNode : getNode(itemNode.getChildNodes(), "Permissions")) {
                                Set<Permission> referencedPermissions = new HashSet<>();
                                for (Node permissionNode : getNode(permissionsNode.getChildNodes(), "Permission")) {
                                    String refIdString = getAttribute(permissionNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                referencedPermissions.add(referencedPermission);
                                            } else {
                                                log.error("WorkflowPosition object with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refId='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }

                                }
                                workflowPosition.setPermissions(referencedPermissions);
                            }

                            // Now, save the object to the database
                            saveObject(workflowPosition, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            workflowPositions.put(id, workflowPosition);
                        }
                    }
                }
            }
        }
        return workflowPositions;
    }

    /*
	 * <WorkflowProperties> <WorkflowProperty id="1"> <WorkflowPosition refid="10" /> <Required>TRUE</Required>
	 * <Name>name</Name> </WorkflowProperty> </WorkflowProperties>
	 */
    private TreeMap<Long, WorkflowPosition> parseWorkflowProperties(TreeMap<Long, WorkflowPosition> workflowPositions) {
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "WorkflowProperties");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "WorkflowProperties")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "WorkflowProperty")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            boolean required = false;
                            String requiredString = getChildNodeValue(itemNode, "Required");
                            if ((requiredString != null) && (requiredString.equalsIgnoreCase("TRUE"))) {
                                required = true;
                            }
                            WorkflowProperty workflowProperty = new WorkflowProperty();
                            workflowProperty.setId(id);
                            workflowProperty.setName(getChildNodeValue(itemNode, "Name"));
                            workflowProperty.setRequired(required);
                            // Resolve the Workflow object reference
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "WorkflowPosition")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowPosition referencedWorkflowPosition = workflowPositions.get(refId);
                                        if (referencedWorkflowPosition != null) {
                                            workflowProperty.setWorkflowPosition(referencedWorkflowPosition);
                                        } else {
                                            log.error("WorkflowPosition object with id='"
                                                            + id
                                                            + "' has a reference to a Workflow object with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            if (workflowProperty.getWorkflowPosition() == null)
                                throw new RuntimeException("WorkflowPosition is null for WorkflowProperty");
                            // Now, save the object to the database
                            saveObject(workflowProperty, isNew);
                        }
                    }
                }
            }
        }
        return workflowPositions;
    }

    private void fixWorkflowPositionReferences(TreeMap<Long, WorkflowPosition> workflowPositions,
                                               TreeMap<Long, Long> workflowPositionReferences, String referenceType) {
        boolean isNew = false;
        Set<Long> keys = workflowPositionReferences.keySet();
        for (Long workflowPositionId : keys) {
            WorkflowPosition workflowPosition = workflowPositions.get(workflowPositionId);
            if (workflowPosition != null) {
                Long referencedWorkflowPositionId = workflowPositionReferences.get(workflowPositionId);
                WorkflowPosition referencedWorkflowPosition = workflowPositions.get(referencedWorkflowPositionId);
                if (referencedWorkflowPosition != null) {
                    if ((referenceType != null) && ("Next".equalsIgnoreCase(referenceType))) {
                        workflowPosition.setNext(referencedWorkflowPosition);
                    } else if ((referenceType != null) && ("Previous".equalsIgnoreCase(referenceType))) {
                        workflowPosition.setPrevious(referencedWorkflowPosition);
                    }
                    saveObject(workflowPosition, isNew);
                } else {
                    log.error("referencedWorkflowPosition object with id="
                                    + referencedWorkflowPositionId
                                    + " and type='"
                                    + referenceType
                                    + "' (referenced by WorkflowPosition.id="
                                    + workflowPositionId
                                    + ") could not be found! Cannot establish references for this object! Skipping this task and continuing processing...");
                }
            } else {
                log.error("WorkflowPosition object with id="
                                + workflowPositionId
                                + " and type='"
                                + referenceType
                                + "' could not be found! Cannot establish references for this object! Skipping this task and continuing processing...");
            }
        }
    }

    /*
	 * <WorkflowTab id="16"> <WorkflowPosition refid="18" /> <Name>Message Testing</Name>
	 * <StringId>page.label.approvals</StringId> <Permission refid="6" /> <EditUrl>message/message_edit_approvals.form</EditUrl>
	 * <ViewUrl>message/message_view_approvals.jsp</ViewUrl> <RequiredId refid="13" /> </WorkflowTab>
	 */
    private TreeMap<Long, WorkflowTab> parseWorkflowTabs(TreeMap<Long, WorkflowPosition> workflowPositions,
                                                         TreeMap<Long, Permission> permissions) {
        TreeMap<Long, WorkflowTab> workflowTabs = new TreeMap<>();
        boolean isNew = true;
        NodeList nodeList = MessagePointXmlParser.getNodeListByName(document, "WorkflowTabs");
        if (nodeList != null) {
            for (Node groupNode : getNode(nodeList, "WorkflowTabs")) {
                for (Node itemNode : getNode(groupNode.getChildNodes(), "WorkflowTab")) {
                    String idString = getAttribute(itemNode, "id");
                    if ((idString != null) && (!"".equalsIgnoreCase(idString))) {
                        Long id = Long.parseLong(idString);
                        if ((id != null) && (id > 0)) {
                            WorkflowTab workflowTab = new WorkflowTab();
                            workflowTab.setId(id);
                            workflowTab.setName(getChildNodeValue(itemNode, "Name"));
                            workflowTab.setStringId(getChildNodeValue(itemNode, "StringId"));
                            workflowTab.setEditUrl(getChildNodeValue(itemNode, "EditUrl"));
                            workflowTab.setViewUrl(getChildNodeValue(itemNode, "ViewUrl"));
                            workflowTab.setListUrl(getChildNodeValue(itemNode, "ListUrl"));
                            workflowTab.setParameter(getChildNodeValue(itemNode, "Parameter"));
                            workflowTab.setVisibilityToggleAttr(getChildNodeValue(itemNode, "VisibilityToggleAttr"));
                            // Resolve the WorkflowPosition object reference
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "WorkflowPosition")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowPosition referencedWorkflowPosition = workflowPositions.get(refId);
                                        if (referencedWorkflowPosition != null) {
                                            workflowTab.setWorkflowPosition(referencedWorkflowPosition);
                                        } else {
                                            log.error("WorkflowTab object with id='"
                                                            + id
                                                            + "' has a reference to a WorkflowPosition object with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            // Resolve the Permission object reference
                            workflowTab.setAuthorizationType(getChildNodeValue(itemNode, "AuthorizationType"));
                            // <Permissions>
                            // <Permission refid="1" />
                            // <Permission refid="2" />
                            // </Permissions>
                            for (Node permissionsNode : getNode(itemNode.getChildNodes(), "Permissions")) {
                                Set<Permission> referencedPermissions = new HashSet<>();
                                for (Node permissionNode : getNode(permissionsNode.getChildNodes(), "Permission")) {
                                    String refIdString = getAttribute(permissionNode, "refid");
                                    if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                        Long refId = Long.parseLong(refIdString);
                                        if ((refId != null) && (refId > 0)) {
                                            Permission referencedPermission = permissions.get(refId);
                                            if (referencedPermission != null) {
                                                referencedPermissions.add(referencedPermission);
                                            } else {
                                                log.error("WorkflowTab object with id='"
                                                                + id
                                                                + "' has a reference to a Permission object with refId='"
                                                                + refId
                                                                + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                            }
                                        }
                                    }

                                }
                                workflowTab.setPermissions(referencedPermissions);
                            }
                            // Try and resolve the RequiredId object references
                            for (Node referencedNodes : getNode(itemNode.getChildNodes(), "RequiredId")) {
                                String refIdString = getAttribute(referencedNodes, "refid");
                                if ((refIdString != null) && (!"".equalsIgnoreCase(refIdString))) {
                                    Long refId = Long.parseLong(refIdString);
                                    if ((refId != null) && (refId > 0)) {
                                        WorkflowTab referencedWorkflowTab = workflowTabs.get(refId);
                                        if (referencedWorkflowTab != null) {
                                            workflowTab.setRequired(referencedWorkflowTab);
                                        } else {
                                            log.error("WorkflowTab object with id='"
                                                            + id
                                                            + "' has a reference to another WorkflowTab object [by <RequiredId />] with refid='"
                                                            + refId
                                                            + "' which does not exist! Could not establish relationship! Skipping this reference and continuing...");
                                        }
                                    }
                                }
                            }
                            // Now, save the object to the database
                            saveObject(workflowTab, isNew);
                            // Save a copy of the object in a TreeMap so it can
                            // be used in subsequent XML references
                            workflowTabs.put(id, workflowTab);
                        }
                    }
                }
            }
        }
        return workflowTabs;
    }

    private void fixDropDownMenuTabRelationships(TreeMap<Long, DropDownMenu> dropDownMenus,
                                                 TreeMap<Long, NavigationTab> navigationTabs, TreeMap<Long, Long> dropDownMenuTabRelationships) {
        boolean isNew = false;
        Set<Long> keys = dropDownMenuTabRelationships.keySet();
        for (Long key : keys) {
            Long value = dropDownMenuTabRelationships.get(key);
            DropDownMenu dropDownMenu = dropDownMenus.get(key);
            NavigationTab navigationTab = navigationTabs.get(value);
            if ((dropDownMenu != null) && (navigationTab != null)) {
                dropDownMenu.setTab(navigationTab);
                navigationTab.getMenus().add(dropDownMenu);
                saveObject(dropDownMenu, isNew);
            } else {
                log.error("DropDownMenuId="
                                + key
                                + ", NavigationTab="
                                + value
                                + ", one of which returns a null object! Cannot establish this relationship! Continuing processing...");
            }
        }
    }

    private void parseDialogueDataMappings() {
        boolean isNew = true;
        TreeMap<Long, KeyValuePair> keyValuePairs = parseKeyValuePairsById(document,
                "DialogueDataMappings",
                "DialogueDataMapping",
                "DialogueDataType",
                "DataType");
        Set<Long> keys = keyValuePairs.keySet();
        for (Long key : keys) {
            KeyValuePair keyValuePair = keyValuePairs.get(key);
            DialogueDataMapping dialogueDataMapping = new DialogueDataMapping();
            dialogueDataMapping.setId(key);
            dialogueDataMapping.setDialogueDataType(keyValuePair.getKey());
            dialogueDataMapping.setDataTypeId(Integer.parseInt(keyValuePair.getValue()));
            saveObject(dialogueDataMapping, isNew);
        }
    }

    private TreeMap<Long, Permission> retrieveAllPermissions() {
        TreeMap<Long, Permission> permissions = new TreeMap<>();
        List<Permission> permissionList = Permission.findAll();
        for (Permission permission : permissionList) {
            permissions.put(permission.getId(), permission);
        }
        return permissions;
    }

    public static boolean isQuartzTableRebuildRequired(File webInfDir) {
        List<File> files = new ArrayList<>();
        files.add(new File(webInfDir, "messagepoint-schedule.xml"));

        for (File file : files) {
            if (InitializeSchema.isSourceFileOutOfSync(file)) {
                return true;
            }
        }

        return false;
    }



    public static void updateDatabaseFileVersion(File file) {

        try {
            if (file != null) {
                Session session = HibernateUtil.getManager().getSession();
                NativeQuery delete = session.createNativeQuery("DELETE from data_migration_info_static WHERE filename = :filename");
                NativeQuery insert = session.createNativeQuery("INSERT into data_migration_info_static (filename, version) VALUES (:filename, :version)");

                delete.setParameter("filename", file.getName());
                insert.setParameter("filename", file.getName());
                insert.setParameter("version", StaticTableUtils.getFileMD5Checksum(file));

                delete.executeUpdate();
                insert.executeUpdate();
            }
        } catch (Exception ex) {
            log.error("Error updating static table version: " + ex.getMessage());
        }

    }

}
