package com.prinova.messagepoint.initialization;

import java.util.LinkedList;

import org.apache.commons.logging.Log;
import org.springframework.core.io.Resource;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;

import com.prinova.messagepoint.util.LogUtil;

public class InitializationLocalSessionFactoryBean extends LocalSessionFactoryBean {

	private static final Log log = LogUtil.getLog(InitializationLocalSessionFactoryBean.class);
	
    @Override
	public void setMappingLocations(Resource[] mappingLocations) {
		LinkedList<Resource> initializationMappingLocation = new LinkedList<>();
		LinkedList<String> usedMappingFiles = new LinkedList<>();
		
		log.info("setMappingLocations...");
		for (int i = 0 ; i < mappingLocations.length; i++)
		{
			// log.debug(": " + mappingLocations[i].getFilename());				
			if (!usedMappingFiles.contains(mappingLocations[i].getFilename()))
			{
				initializationMappingLocation.add(mappingLocations[i]);
				usedMappingFiles.add(mappingLocations[i].getFilename());
			}
			else
			{
				log.info("removing duplicate: " + mappingLocations[i].getFilename());				
			}
		}
		
		Resource[] ml = new Resource[0];
		ml = initializationMappingLocation.toArray(ml);
		
		super.setMappingLocations(ml);
	}
    
}
