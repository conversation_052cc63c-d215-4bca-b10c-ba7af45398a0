package com.prinova.messagepoint;

import ai.mpr.marcie.content.rationalizer.ApplicationResponse;
import ai.mpr.marcie.content.rationalizer.ElasticsearchService;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.prinova.licence.LicenceError;
import com.prinova.licence.LicenceVerifier;
import com.prinova.licence.common.Constants;
import com.prinova.messagepoint.connected.data.json.content.LocalImagesDiskHandler;
import com.prinova.messagepoint.controller.NodeHealthCheckController;
import com.prinova.messagepoint.controller.WebAssetPackageController;
import com.prinova.messagepoint.controller.communication.connected.ConnectedMigrateCommunicationJsonBackgroundTask;
import com.prinova.messagepoint.controller.communication.connected.ConnectedMigrateReferenceDataBaseToJson;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.*;
import com.prinova.messagepoint.model.admin.DataElementVariable;
import com.prinova.messagepoint.model.admin.DataSource;
import com.prinova.messagepoint.model.content.Content;
import com.prinova.messagepoint.model.content.ContentObject;
import com.prinova.messagepoint.model.content.ContentObjectData;
import com.prinova.messagepoint.model.rationalizer.RationalizerApplication;
import com.prinova.messagepoint.model.rationalizer.SearchResultIds;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.model.util.ContentObjectContentUtil;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.ServiceResponse;
import com.prinova.messagepoint.platform.services.admin.MigrateLocalUsersAndDisableUserMenuItemsService;
import com.prinova.messagepoint.platform.services.admin.UpdateDataExpirationScheduleService;
import com.prinova.messagepoint.platform.services.admin.UpdateFilerootManagementService;
import com.prinova.messagepoint.platform.services.aws.util.ContentReindexMetadataProviderImpl;
import com.prinova.messagepoint.platform.services.backgroundtask.*;
import com.prinova.messagepoint.platform.services.bundledelivery.MigrateBundleDeliveryPostProcessScriptsService;
import com.prinova.messagepoint.platform.services.content.CreateOrUpdateListStyleService;
import com.prinova.messagepoint.platform.services.content.UpdateSystemVariablesService;
import com.prinova.messagepoint.platform.services.dataadmin.CheckAndDisableMenuItemsService;
import com.prinova.messagepoint.platform.services.dataadmin.CleanStalledJobsService;
import com.prinova.messagepoint.platform.services.dataadmin.MigrateDataResourceRemoteFilePathService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetingSearchStringService;
import com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetingTPAssignmentService;
import com.prinova.messagepoint.platform.services.dictionary.MigrateDictionariesService;
import com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.MessagepointElasticSearchHandler;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchFactory;
import com.prinova.messagepoint.platform.services.elasticsearch.RationalizerElasticSearchHandler;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService;
import com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerContentService;
import com.prinova.messagepoint.platform.services.rationalizer.MigrateRationalizerPostProcessScriptsService;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerMigrateMetadataToJsonFormatBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerUpdateStyleFontSizeBackgroundTask;
import com.prinova.messagepoint.platform.services.rationalizer.RationalizerUtilsService;
import com.prinova.messagepoint.platform.services.tag.CreateOrUpdateTagService;
import com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService;
import com.prinova.messagepoint.platform.services.testing.UpdateTestSuiteService;
import com.prinova.messagepoint.platform.services.testing.UpdateTestingScenarioService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentService;
import com.prinova.messagepoint.platform.services.tpadmin.UpdateTemplateModifiersService;
import com.prinova.messagepoint.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Session;
import com.prinova.messagepoint.query.criterion.MessagepointCriterion;
import com.prinova.messagepoint.query.criterion.MessagepointRestrictions;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.mock.web.MockServletContext;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.web.context.ServletContextAware;

import javax.servlet.ServletContext;
import java.io.File;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.getValueForJsonPath;
import static ai.mpr.marcie.content.rationalizer.misc.ApplicationUtils.readJsonElement;
import static ai.mpr.marcie.reindex.common.elasticsearch.ElasticsearchUtil.buildElasticsearchScallingService;
import static com.prinova.messagepoint.controller.MessagepointStartFilter.generateCSRFToken;
import static com.prinova.messagepoint.model.SystemPropertyManager.initializeMarcieBootSystemProperties;
import static com.prinova.messagepoint.model.rationalizer.MessagepointRedisKeys.deleteMessagepointRedisCacheKeysOnMessagepointStartup;
import static com.prinova.messagepoint.model.rationalizer.RationalizerApplicationRedisKeys.deleteRationalizerRedisCacheKeysOnMessagepointStartup;
import static com.prinova.messagepoint.platform.services.elasticsearch.ElasticsearchContentUtils.extractElasticAliasIdFromElasticIndexName;

public class MessagePointStartUp implements ServletContextAware, InitializingBean, Serializable{


	private static final long serialVersionUID = -8903269346379452854L;

	public static final String KEY_BUILD_PROPERTIES = "com.prinova.messagepoint.buildInfo";
	public static final String KEY_DEVTOOLS_PROPERTIES = "com.prinova.messagepoint.devTools";
	private static final String NODE_IDENTIFIER = generateCSRFToken();

	private static ServletContext servletContext;
	private String defaultLicenceFile;
	private String licenceFile;

	/**
	 * This property should be randomised with each version.
	 */
	@SuppressWarnings("unused")
	private static final String int_build_version = "Messagepoint_3.0.000_-8903269312379452854";

	public void setServletContext(ServletContext servletContext) {
		MessagePointStartUp.servletContext = servletContext;
	}

	public static void setMockServletContext(MockServletContext servletContext) {
		MessagePointStartUp.servletContext = servletContext;
	}

	public String getLicenceFile() {
		return licenceFile;
	}

	public void setLicenceFile(String licenceFile) {
		this.licenceFile = licenceFile;
	}

	public static ServletContext getServletContext()
	{
		return servletContext;
	}

	public void afterPropertiesSet() throws Exception {
		try {
			initializeMessagePoint();
		} finally {
			NodeHealthCheckController.setStartupComplete(true);
		}
	}

	public void initializeMessagePoint(){
        LogUtil.getLog(MessagePointStartUp.class).info("initializeMessagePoint NODE_IDENTIFIER; " + NODE_IDENTIFIER );

		List<Node> allNodes = new ArrayList<>();

		// Only verify and initilize the licence if it is the top master schema
		String currentSchema = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
		if(currentSchema == null){
			verifyLicence();
			processLicenceToken();
		}

		processBuildInfo();

		initClientLanguageFiles();

		if ( HibernateUtil.getManager() != null)
		{
			LogUtil.getLog(MessagePointStartUp.class).info("enableTenantFilters: " + HibernateUtil.getManager().isEnableFilter());

			allNodes.addAll(Node.getAllOnlineAndOfflineNodes());

            checkRecalculateHashFlagInPodMaster();

            // run following start up processes for all nodes
			//
			for (Node node : allNodes)
			{
				startupSchema(node);
			}
			LogUtil.getLog(MessagePointStartUp.class).info("STARTUP MIGRATION API TIMINGS:");
			GlobalTimingAggregator.displayTimings();
			GlobalTimingAggregator.resetTimings();

			migrateConnectedReferencedataSourceToJson(allNodes);

			// Update user licence and guid
			//
			SessionHolder mainSessionHolder = null;
			try {
				mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);

				SystemProperty systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_UserLicenceTypeAndGuidsUpdated);
				if (systemProperty == null || systemProperty.getValue() == null) {
					// Update user licence type and GUID
					//
					for (Branch branch : BranchUtil.getAllBranches()) {
						if (branch.isAccessible()) {
							LogUtil.getLog(MessagePointStartUp.class).info("Updating User Licence Type And GUIDs for domain: " + branch.getName());

							SessionHolder branchSessionHolder = null;
							try {
								branchSessionHolder = HibernateUtil.getManager().openTemporarySession(branch.getDcsSchemaName());

								// Find all local DCS users (IDP_TYPE_MP_DCS_USER or (IDP_TYPE_SSO_DCS_USER and IdPSubject == null)
								//
								List<User> dcsLocalUsers = User.findAllLocalDcsUsers(false);
								dcsLocalUsers.addAll(User.findAllLocalSSODcsUsers());

								for (User dcsUser : dcsLocalUsers) {
									int userLicenceType = dcsUser.getLicenceTypeByRoleAndAccountEnabledStatus();

									for (Node node : allNodes) {
										User nodeUser = dcsUser.getNodeUser(node);
										if (nodeUser != null) {
											SessionHolder localSessionHolder = null;
											try {
												localSessionHolder = HibernateUtil.getManager().openTemporarySession(nodeUser.getSchemaOfThisUser());

												if (userLicenceType != User.LICENSED_TYPE_REGULAR_ACCESS && userLicenceType < nodeUser.getLicenceTypeByRoleAndAccountEnabledStatus())
													userLicenceType = nodeUser.getLicenceTypeByRoleAndAccountEnabledStatus();

												nodeUser.setGuid(dcsUser.getGuid());
												nodeUser.setLicensedType(User.LICENSED_TYPE_NONE);
												nodeUser.save();

												HibernateUtil.getManager().getSession().flush();
											} finally {
												HibernateUtil.getManager().restoreSession(localSessionHolder);
											}
										}
									}

									dcsUser.setLicensedType(userLicenceType);
									dcsUser.save();
								}

								HibernateUtil.getManager().getSession().flush();
							} finally {
								HibernateUtil.getManager().restoreSession(branchSessionHolder);
							}
						}
					}

					if (systemProperty == null)
						systemProperty = new SystemProperty(SystemPropertyKeys.Data.KEY_UserLicenceTypeAndGuidsUpdated, DateUtil.formatDateForXMLOutput(DateUtil.now()), "Date and time of update");
					else
						systemProperty.setValue(DateUtil.formatDateForXMLOutput(DateUtil.now()));

					systemProperty.save();
				}

			} finally {
				HibernateUtil.getManager().restoreSession(mainSessionHolder);
			}

			migrateRationalizerMetadataToJsonMetadataFormat(allNodes);
			//CI_253 revert/suppress style font size update for now
			//updateRationalizerStyleFontSize(allNodes);
			migrateCommunicationJsonToBackend(allNodes);
		}

		WebAssetPackageController.init();
	}

    private static void checkRecalculateHashFlagInPodMaster() {
        boolean hashAlgorithmChangedMaster = false;

        SessionHolder mainSessionHolder = HibernateUtil.getManager().openTemporarySession(null);

        try {
            SystemProperty systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged);

            if (systemProperty != null) {
                if (systemProperty.getValue() != null && systemProperty.getValue().equalsIgnoreCase("true")) {
                    hashAlgorithmChangedMaster = true;
                }
                systemProperty.setValue("false");
                systemProperty.save();
                HibernateUtil.getManager().getSession().flush();
            }
        } finally {
            HibernateUtil.getManager().restoreSession(mainSessionHolder);
        }

        if(hashAlgorithmChangedMaster) {
            List<Node> allNodes = Node.getAllOnlineAndOfflineNodes();

            for(Node node : allNodes) {
                if(node.isDcsNode()) continue;

                String nodeSchema = node.getSchemaName();

                SessionHolder nodeSessionHolder = HibernateUtil.getManager().openTemporarySession(nodeSchema);
                try {
                    SystemProperty nodeSystemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged);
                    if(nodeSystemProperty == null) {
                        nodeSystemProperty = new SystemProperty();
                        nodeSystemProperty.setKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged);
                    }
                    nodeSystemProperty.setValue("true");
                    nodeSystemProperty.save();
                    HibernateUtil.getManager().getSession().flush();
                } finally {
                    HibernateUtil.getManager().restoreSession(nodeSessionHolder);
                }
            }
        }
    }

	public static void startupSchema(Node node)
	{
		if (node == null)
			return;

		String schema = node.getSchemaName();
		String schemaForDisplay = schema;
		if (schemaForDisplay == null)
			schemaForDisplay = "POD MASTER (" + MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName() + ")";
		LogUtil.getLog(MessagePointStartUp.class).info("Starting start-up processes for DB schema: " + schemaForDisplay + ". This may take a while...");

		SessionHolder mainSessionHolder = null;
		try {
			mainSessionHolder = HibernateUtil.getManager().openTemporarySession(schema);

			long startTime, endTime; // For timing

			if (schema == null) {
				enablePodMasterNode();
			}

			startTime = System.currentTimeMillis();
			processCleanJobs();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("processCleanJobs", endTime - startTime);

			startTime = System.currentTimeMillis();
			processCheckAndDisableMenuItems();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("processCheckAndDisableMenuItems", endTime - startTime);

			startTime = System.currentTimeMillis();
			initSystemVariables();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initSystemVariables", endTime - startTime);

			if (schema == null) {
				// Set system props only for POD_MASTER
				startTime = System.currentTimeMillis();
				initializeMarcieBootSystemProperties();
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("initializeMarcieBootSystemProperties", endTime - startTime);
			}

			startTime = System.currentTimeMillis();
			initHTMLRationalizerContent();            // 18.1
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initHTMLRationalizerContent", endTime - startTime);

			startTime = System.currentTimeMillis();
			migrateDictionaries();        // 18.2
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("migrateDictionaries", endTime - startTime);

			startTime = System.currentTimeMillis();
			migrateBundleDeliveryPostProcessScripts();    // 18.2
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("migrateBundleDeliveryPostProcessScripts", endTime - startTime);

			startTime = System.currentTimeMillis();
			migrateRationalizerPostProcessScripts(); // 18.2
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("migrateRationalizerPostProcessScripts", endTime - startTime);

			if (schema != null) {
				startTime = System.currentTimeMillis();
				normalizeMarcieStats();            // 20.1
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("normalizeMarcieStats", endTime - startTime);

				startTime = System.currentTimeMillis();
				deleteRationalizerRedisCacheKeysOnMessagepointStartup(); // 20.2
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("deleteRationalizerRedisCacheKeysOnMessagepointStartup", endTime - startTime);

				startTime = System.currentTimeMillis();
				deleteMessagepointRedisCacheKeysOnMessagepointStartup(); // 21.1
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("deleteMessagepointRedisCacheKeysOnMessagepointStartup", endTime - startTime);

				startTime = System.currentTimeMillis();
				normalizeRationalizerSharedContentsOnMessagePointStartup(schema); // 21.1
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("normalizeRationalizerSharedContentsOnMessagePointStartup", endTime - startTime);

				startTime = System.currentTimeMillis();
				deleteSearchResultIdsOnMessagepointStartup(); // 21.1
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("deleteSearchResultIdsOnMessagepointStartup", endTime - startTime);

				startTime = System.currentTimeMillis();
				// This can be removed in the next major release.
				LocalImagesDiskHandler.deleteLocalImagesDirectory(); // 23.1
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("deleteLocalImagesDirectory", endTime - startTime);

				startTime = System.currentTimeMillis();
				fixContentContentTargetingHash(node);
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("fixContentContentTargetingHash", endTime - startTime);

				startTime = System.currentTimeMillis();
				recalculateHashIfAlgorithmChanged(node);
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("recalculateHashIfAlgorithmChanged", endTime - startTime);

				startTime = System.currentTimeMillis();
				boolean updateStyleCache = true;
				String startupMetadataStr = SystemPropertyManager.getInstance().getSystemProperty(SystemPropertyKeys.Data.KEY_StartupMigrationMetadataJSON);
				JSONObject startupMetadata;
				if (startupMetadataStr == null || startupMetadataStr.isEmpty()) {
					startupMetadata = new JSONObject();
				} else {
					startupMetadata = new JSONObject(startupMetadataStr);
				}
				if (startupMetadata.has("startup_style_cache_refresh")) {
					updateStyleCache = false;
				}
				startupMetadata.put("startup_style_cache_refresh", getBuildRevision());
				SystemPropertyManager.getInstance().updateProperty(SystemPropertyKeys.Data.KEY_StartupMigrationMetadataJSON, startupMetadata.toString());

				if (updateStyleCache) {
					refreshCachedStyleData();
				}
				endTime = System.currentTimeMillis();
				GlobalTimingAggregator.addTiming("refreshCachedStyleData", endTime - startTime);
			}

			// 20.2: Migrate java resolved value to DB value to enhance Test list performance
			startTime = System.currentTimeMillis();
			initTestScenarioLastRunDate(); // (Can remove when all pods are on 20.2+)
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initTestScenarioLastRunDate", endTime - startTime);

			startTime = System.currentTimeMillis();
			initTestSuiteLastRunDate(); // Can remove after all pods are on 24.2 +
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initTestSuiteLastRunDate", endTime - startTime);

			if (schema != null && node.getId() > 0) {
				Node.postInitNode(node.getId());
			}

			startTime = System.currentTimeMillis();
			migrateLocalUsersAndDisableUserMenuItems();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("migrateLocalUsersAndDisableUserMenuItems", endTime - startTime);

			startTime = System.currentTimeMillis();
			initHealthCheckContentReferencesRepairTasks();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initHealthCheckContentReferencesRepairTasks", endTime - startTime);

			startTime = System.currentTimeMillis();
			initHealthcheckMessageInvalidOwningVariantRepairTasks();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initHealthcheckMessageInvalidOwningVariantRepairTasks", endTime - startTime);

			startTime = System.currentTimeMillis();
			initHealthcheckMissingTargetRuleValueTasks();
			endTime = System.currentTimeMillis();
			GlobalTimingAggregator.addTiming("initHealthcheckMissingTargetRuleValueTasks", endTime - startTime);

            startTime = System.currentTimeMillis();
            initHealthcheckReferencedContentObjectsRepairTask();
            endTime = System.currentTimeMillis();
            GlobalTimingAggregator.addTiming("initHealthcheckReferencedContentObjectsRepairTask", endTime - startTime);
        } finally {
			HibernateUtil.getManager().restoreSession(mainSessionHolder);
		}

		MessagepointMultiTenantConnectionProvider.logCurrentStaticStatus(schema);

		LogUtil.getLog(MessagePointStartUp.class).info("Finished start-up processes for DB schema: " + schemaForDisplay);
		Long count = RedisUtil.getRedisContext().increment(node.getSchemaName() != null ? node.getSchemaName() : MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName());
		LogUtil.getLog(MessagePointStartUp.class).info((node.getSchemaName() != null ? node.getSchemaName() : MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName()) + ":+:" + count);
	}

	public static void shutdownSchema(Node node) {

		if (node == null) {
			return;
		}

		String schemaName = node.getSchemaName() == null ? MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName() : node.getSchemaName();

		if (schemaName != null) {
			schemaName = MessagepointMultiTenantConnectionProvider.isPostgres() ? schemaName.toLowerCase() : schemaName;

			Long count = RedisUtil.getRedisContext().decrement(schemaName);
			LogUtil.getLog(MessagePointStartUp.class).info( MessageFormat.format("shutdownSchema: {0}, currentCount: {1}", schemaName, count));
		}

	}

	public static void normalizeRationalizerSharedContentsOnMessagePointStartup(String schemaName) {
		try {
			LogUtil.getLog(MessagePointStartUp.class).info("Normalizing the RationalizerSharedContents for " + schemaName + " schema.");

			RationalizerUtilsService rationalizerUtilsService = (RationalizerUtilsService) ApplicationUtil.getBean("rationalizerUtilsService");
			ServiceResponse serviceResponse = rationalizerUtilsService.normalizeRationalizerSharedContents();

			if (!serviceResponse.isSuccessful()) {
				LogUtil.getLog(MessagePointStartUp.class).error("Error when trying to normalize the RationalizerSharedContents for " + schemaName + " schema.");
				if (serviceResponse.hasMessages()) {
					serviceResponse.logMessages(LogUtil.getLog(MessagePointStartUp.class));
				}
			}

		} catch (Throwable th) {
			LogUtil.getLog(MessagePointStartUp.class).error("Error when trying to normalize the RationalizerSharedContents for " + schemaName + " schema.", th);
		}
	}

	public static void deleteSearchResultIdsOnMessagepointStartup() {
		try {
			LogUtil.getLog(MessagePointStartUp.class).info("Deleting SearchResultIds.");
			SearchResultIds.clearSearchResultIdsForAllUsers();

		} catch (Throwable th) {
			LogUtil.getLog(MessagePointStartUp.class).error("SearchResultIds were not deleted successfully.", th);
		}
	}

	private static void initClientLanguageFiles() {
		ApplicationLanguageUtils.generateApplicationClientLanguageFiles();
	}

	private static void initTargetingSearchString() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTargetingSearchStringService.SERVICE_NAME, UpdateTargetingSearchStringService.class);
		ServiceExecutionContext context = UpdateTargetingSearchStringService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Building targeting search string.");
			service.execute(context);
		}
	}

	private static void initTagCloud() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTagCloudService.SERVICE_NAME, UpdateTagCloudService.class);
		ServiceExecutionContext context = UpdateTagCloudService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Building tag cloud.");
			service.execute(context);
		}
	}

	private static void initTargetingTPAssignment(){
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTargetingTPAssignmentService.SERVICE_NAME, UpdateTargetingTPAssignmentService.class);
		ServiceExecutionContext context = UpdateTargetingTPAssignmentService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Building targeting touchpoint assignment.");
			service.execute(context);
		}
	}

	private static void refreshCachedStyleData() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateListStyleService.SERVICE_NAME, CreateOrUpdateListStyleService.class);
		ServiceExecutionContext context = CreateOrUpdateListStyleService.createContextForStyleDataUpdate();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Updating content styling cache data.");
			service.execute(context);
		}
	}

	private static void initTempateModifier() {

		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTemplateModifiersService.SERVICE_NAME, UpdateTemplateModifiersService.class);
		ServiceExecutionContext context = UpdateTemplateModifiersService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Building template modifiers.");
			service.execute(context);
		}
	}

	private static void initTouchpointTargeting() {

		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDocumentService.SERVICE_NAME, UpdateDocumentService.class);
		ServiceExecutionContext context = UpdateDocumentService.createContextForInitializeTargeting();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing touchpoint targeting.");
			service.execute(context);
		}
	}

	private static void migrateDataResourceRemoteFilePath(){
		Service service = MessagepointServiceFactory.getInstance().lookupService(MigrateDataResourceRemoteFilePathService.SERVICE_NAME, MigrateDataResourceRemoteFilePathService.class);
		ServiceExecutionContext context = MigrateDataResourceRemoteFilePathService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Migrating data resource remote file path.");
			service.execute(context);
		}
	}

	private static void initSystemVariables() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateSystemVariablesService.SERVICE_NAME, UpdateSystemVariablesService.class);
		ServiceExecutionContext context = UpdateSystemVariablesService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing system variables.");
			service.execute(context);
		}
	}

	private static void initTestScenarioLastRunDate() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTestingScenarioService.SERVICE_NAME, UpdateTestingScenarioService.class);
		ServiceExecutionContext context = UpdateTestingScenarioService.createContextForInitLastRunDate();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing test last run date.");
			service.execute(context);
		}
	}

	private static void initTestSuiteLastRunDate() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateTestSuiteService.SERVICE_NAME, UpdateTestSuiteService.class);
		ServiceExecutionContext context = UpdateTestSuiteService.createContextForInitLastRunDate();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing test suite last run date.");
			service.execute(context);
		}
	}

	private static void initRationalizerContentOrder() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerApplicationService.SERVICE_NAME, CreateOrUpdateRationalizerApplicationService.class);
		ServiceExecutionContext context = CreateOrUpdateRationalizerApplicationService.createContextForInitDocumentContentOrder();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing rationalizer content order.");
			service.execute(context);
		}
	}


	private static void initPrintStreamTags() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateTagService.SERVICE_NAME, CreateOrUpdateTagService.class);
		ServiceExecutionContext context = CreateOrUpdateTagService.createContextForStartupMigration();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing print stream tag migration (document to set and content to complexvalue).");
			service.execute(context);
		}
	}

	private static void migrateRationalizerMetadataToJsonMetadataFormat(List<Node> allNodes) {
		RationalizerMigrateMetadataToJsonFormatBackgroundTask migrateMetadataBackgroundTask = new RationalizerMigrateMetadataToJsonFormatBackgroundTask(allNodes);
		MessagePointRunnableUtil.startThread(migrateMetadataBackgroundTask, Thread.MAX_PRIORITY);
	}

	private static void migrateCommunicationJsonToBackend(List<Node> allNodes){
		ConnectedMigrateCommunicationJsonBackgroundTask connectedMigrateCommunicationJsonBackgroundTask = new ConnectedMigrateCommunicationJsonBackgroundTask(allNodes);
		MessagePointRunnableUtil.startThread(connectedMigrateCommunicationJsonBackgroundTask, Thread.MAX_PRIORITY);
	}

	private static void migrateConnectedReferencedataSourceToJson(List<Node> allNodes){
		ConnectedMigrateReferenceDataBaseToJson connectedMigrateReferenceDataBaseToJsonBackgroundTask = new ConnectedMigrateReferenceDataBaseToJson(allNodes);
		connectedMigrateReferenceDataBaseToJsonBackgroundTask.performMainProcessing();
	}

	private static void updateRationalizerStyleFontSize(List<Node> allNodes) {
		RationalizerUpdateStyleFontSizeBackgroundTask updateRationalizerStyleFontSizeBackgroundTask = new RationalizerUpdateStyleFontSizeBackgroundTask(allNodes);
		MessagePointRunnableUtil.startThread(updateRationalizerStyleFontSizeBackgroundTask, Thread.MAX_PRIORITY);
	}

	private static void initHTMLRationalizerContent() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(CreateOrUpdateRationalizerContentService.SERVICE_NAME, CreateOrUpdateRationalizerContentService.class);
		ServiceExecutionContext context = CreateOrUpdateRationalizerContentService.createContextForStartupMigration();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing rationalizer HTML content migration.");
			service.execute(context);
		}
	}

	private static void normalizeMarcieStats() {
		try {
			LogUtil.getLog(MessagePointStartUp.class).info("Normalizing Marcie Stats for Rationalizer and Messagepoint applications.");

			final String awsElasticUri = new ContentReindexMetadataProviderImpl().getAwsElasticUri();
			ElasticsearchService service = buildElasticsearchScallingService(
					awsElasticUri,
					null
			);

			final ApplicationResponse applicationResponse = service.applicationIndexNames("");
			if (applicationResponse.failure()) {
				LogUtil.getLog(MessagePointStartUp.class).warn("Marcie Stats were not normalized because ElasticSearch service is not available." +
						" awsElasticUri = " + awsElasticUri);

				return;
			}

			final JsonElement indexNamesJsonElement = readJsonElement(applicationResponse.getResponseBody());
			if (JsonNull.INSTANCE == indexNamesJsonElement || !indexNamesJsonElement.isJsonArray()) {
				LogUtil.getLog(MessagePointStartUp.class).error("Empty list of ElasticSearch indices returned." +
						" Stop to normalize Marcie Stats because there is no work to be done.");

				return;
			}

			LinkedHashSet<String> elasticUppercaseAliases = StreamSupport.stream(indexNamesJsonElement.getAsJsonArray().spliterator(), false)
					.map(jsonElement -> getValueForJsonPath(jsonElement, "index", "/", String.class))
					.filter(value -> StringUtils.isNotEmpty(value))
					.map(value -> extractElasticAliasIdFromElasticIndexName(value.toUpperCase()))
					.collect(Collectors.toCollection(LinkedHashSet::new));
			if (CollectionUtils.isEmpty(elasticUppercaseAliases)) {
				LogUtil.getLog(MessagePointStartUp.class).error("Empty list of ElasticSearch indices returned." +
						" Stop to normalize Marcie Stats because there is no work to be done.");

				return;
			}

			List<RationalizerApplication> rationalizerApplications = RationalizerApplication.findAll();
			rationalizerApplications.forEach(rationalizerApplication -> {
				try {
					final String crtUppercaseElasticAppId = rationalizerApplication.buildElasticApplicationId().toUpperCase();
					if (!elasticUppercaseAliases.contains(crtUppercaseElasticAppId)) {
						return;
					}

					RationalizerElasticSearchFactory rationalizerElasticSearchFactory = (RationalizerElasticSearchFactory) ApplicationUtil.getBean("rationalizerElasticSearchFactory");
					RationalizerElasticSearchHandler rationalizerElasticSearchHandler = rationalizerElasticSearchFactory.buildRationalizerElasticSearchHandler(rationalizerApplication);

					rationalizerElasticSearchHandler.normalizeMarcieStatsOnMessagepointStartUp();
					LogUtil.getLog(MessagePointStartUp.class).info(MessageFormat.format(
							"[rationalizerApplicationId={0}]" +
									"[rationalizerElasticAppId={1}]" +
									" Successfully normalized Marcie Stats for Rationalizer application.",
							Long.toString(rationalizerApplication.getId()),
							crtUppercaseElasticAppId)
					);
				} catch (Throwable th) {
					LogUtil.getLog(MessagePointStartUp.class).error("Failed to normalize Marcie Stats for rationalizer application with id="
							+ rationalizerApplication.getId(), th);
				}
			});

			MessagepointElasticSearchFactory messagepointElasticSearchFactory = (MessagepointElasticSearchFactory) ApplicationUtil.getBean("messagepointElasticSearchFactory");
			String messagepointAppGuid = null;
			try {
				messagepointAppGuid = ElasticsearchContentUtils.buildElasticApplicationIdForMessagepoint();

				if (elasticUppercaseAliases.contains(messagepointAppGuid)) {
					MessagepointElasticSearchHandler messagepointElasticSearchHandler = messagepointElasticSearchFactory.buildMessagepointElasticSearchHandler(messagepointAppGuid);

					messagepointElasticSearchHandler.normalizeMarcieStatsOnMessagepointStartUp();
					LogUtil.getLog(MessagePointStartUp.class).info(MessageFormat.format(
							"[messagepointNodeGuid={0}]" +
									"[messagepointElasticAppId={1}]" +
									" Successfully normalized Marcie Stats for Messagepoint application.",
							Node.getCurrentNode().getGuid(),
							messagepointAppGuid)
					);
				}
			} catch (Throwable th) {
				LogUtil.getLog(MessagePointStartUp.class).warn("Failed to normalize Marcie Stats for Messagepoint application with guid=" + messagepointAppGuid);
			}
		} catch (Throwable th) {
			LogUtil.getLog(MessagePointStartUp.class).warn("Marcie Stats were not normalized.");
		}
	}

	private static void enablePodMasterNode(){
		List<MessagepointCriterion> critList = new ArrayList<>();
		critList.add(MessagepointRestrictions.isNull("schemaName"));
		Node podMasterNode = HibernateUtil.getManager().getObjectUnique(Node.class, critList);
		if(podMasterNode != null){
			podMasterNode.setStatus(Node.NODE_STATUS_ONLINE);
			podMasterNode.save();
		}

		// Enable the Pod master level statistic report generating
		if (SystemProperty.findByKey("statistic.report.pod.level.generating") == null ) {
			SystemProperty sp1 = new SystemProperty("statistic.report.pod.level.generating", "true");
			sp1.save();
		}

		// Start the job purging schedulers
		Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateFilerootManagementService.SERVICE_NAME, UpdateFilerootManagementService.class);
		ServiceExecutionContext context = UpdateFilerootManagementService.createContext();
		if ( service != null && context != null ) {
			LogUtil.getLog(MessagePointStartUp.class).info("Initializing job purging schedulers.");
			service.execute(context);
		}

		scheduleDataExpirationJobs();
	}

	private static void scheduleDataExpirationJobs(){
		// Schedule Sync Data Expiration job
		DataExpirationSchedule schedule = DataExpirationSchedule.findById(DataExpirationSchedule.SYNC_SUPPORT_DATA_PURGE_ID);
		if(schedule != null){
			Service service = MessagepointServiceFactory.getInstance().lookupService(UpdateDataExpirationScheduleService.SERVICE_NAME, UpdateDataExpirationScheduleService.class);
			ServiceExecutionContext context = UpdateDataExpirationScheduleService.createContext(schedule);
			if ( service != null && context != null ) {
				LogUtil.getLog(MessagePointStartUp.class).info("Initializing data expiry schedule.");
				service.execute(context);
			}
		}
	}

	private static void migrateDictionaries(){
		Service service = MessagepointServiceFactory.getInstance().lookupService(MigrateDictionariesService.SERVICE_NAME, MigrateDictionariesService.class);
		ServiceExecutionContext context = MigrateDictionariesService.createContext();
		if ( service != null && context != null ){
			LogUtil.getLog(MessagePointStartUp.class).info("Migrating dictionaries.");
			service.execute(context);
		}
	}

	private static void migrateBundleDeliveryPostProcessScripts() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(MigrateBundleDeliveryPostProcessScriptsService.SERVICE_NAME, MigrateBundleDeliveryPostProcessScriptsService.class);
		ServiceExecutionContext context = MigrateBundleDeliveryPostProcessScriptsService.createContext();

		if (service != null && context != null) {
			LogUtil.getLog(MessagePointStartUp.class).info("Migrating bundle delivery post process scripts.");
			service.execute(context);
		}
	}

	private static void migrateRationalizerPostProcessScripts() {
		Service service = MessagepointServiceFactory.getInstance().lookupService(MigrateRationalizerPostProcessScriptsService.SERVICE_NAME, MigrateRationalizerPostProcessScriptsService.class);
		ServiceExecutionContext context = MigrateRationalizerPostProcessScriptsService.createContext();

		if (service != null && context != null) {
			LogUtil.getLog(MessagePointStartUp.class).info("Migrating rationalizer post process scripts.");
			service.execute(context);
		}
	}

	private static void processCleanJobs()
	{
		Service service = MessagepointServiceFactory.getInstance().lookupService(CleanStalledJobsService.SERVICE_NAME, CleanStalledJobsService.class);
		ServiceExecutionContext context = CleanStalledJobsService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Cleaning stalled jobs.");
			service.execute(context);
		}
	}

	private static void processCheckAndDisableMenuItems()
	{
		Service service = MessagepointServiceFactory.getInstance().lookupService(CheckAndDisableMenuItemsService.SERVICE_NAME, CheckAndDisableMenuItemsService.class);
		ServiceExecutionContext context = CheckAndDisableMenuItemsService.createContext();
		if ( service != null && context != null )
		{
			LogUtil.getLog(MessagePointStartUp.class).info("Checking constants and shared content menu items.");
			service.execute(context);
		}
	}

	private static void migrateLocalUsersAndDisableUserMenuItems()
	{
		try {
			Service service = MessagepointServiceFactory.getInstance().lookupService(MigrateLocalUsersAndDisableUserMenuItemsService.SERVICE_NAME, MigrateLocalUsersAndDisableUserMenuItemsService.class);
			ServiceExecutionContext context = MigrateLocalUsersAndDisableUserMenuItemsService.createContext();
			if (service != null && context != null) {
				LogUtil.getLog(MessagePointStartUp.class).info("Migrating local users from not default nodes.");
				service.execute(context);
			}
		} catch (Throwable th) {
			LogUtil.getLog(MessagePointStartUp.class).error("Error while calling " + MigrateLocalUsersAndDisableUserMenuItemsService.class.getSimpleName() + " service.", th);
		}
	}

    private static void initHealthCheckContentReferencesRepairTasks() {
        HealthcheckContentReferencesRepairTask.clearOnStartup();
    }

    private static void initHealthcheckMessageInvalidOwningVariantRepairTasks() {
        HealthcheckMessageInvalidOwningVariantRepairTask.clearOnStartup();
    }

    private static void initHealthcheckMissingTargetRuleValueTasks() {
        HealthcheckMissingTargetRuleValueTask.clearOnStartup();
    }

    private static void initHealthcheckReferencedContentObjectsRepairTask() {
        HealthcheckReferencedContentObjectsRepairTask.clearOnStartup();
    }

    static class StartupThreadIsRunning {
        boolean value = false;
    };

    private static StartupThreadIsRunning startupThreadIsRunning = new StartupThreadIsRunning();

    private static final String startupNode = "isStartupNode";
    private static final String startupThreadsToExecuteKey = "startupThreadWorksToExecute";
    private static final String startThreadsToExecuteSeparator = ".";
    private static final String startupThreadFixContentContentTargetingHash = "fixContentContentTargetingHash";
    private static final String startupThreadRecalculateHashIfAlgorithmChanged = "recalculateHashIfAlgorithmChanged";

    private static void fixContentContentTargetingHash(Node node) {
        RedisUtil.getRedisContext().putNewValue(startupNode, NODE_IDENTIFIER);
        RedisUtil.getRedisContext().expire(startupNode, 3600);

		String startupNodeRedisValue = RedisUtil.getRedisContext().getValue(startupNode, String.class);
        if (startupNodeRedisValue != null && startupNodeRedisValue.equals(NODE_IDENTIFIER)) {
            RedisUtil.getRedisContext().lpush(startupThreadsToExecuteKey, node.getId() + startThreadsToExecuteSeparator + startupThreadFixContentContentTargetingHash);
        }

        startStartupThreads();
    }

    private static void doFixContentContentTargetingHash(Node node) {
        LogUtil.getLog(MessagePointStartUp.class).info("fixContentContentTargetingHash started on node " + node.getName());
         try {
            boolean isDcs = node.getNodeType() == Node.NODE_TYPE_DCS;

            String schemaName = node.getSchemaName();

            SessionHolder sessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);

            Session session = HibernateUtil.getManager().getSession();

            SystemProperty systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_FixContentContentTargetingHash);

            try {
                boolean needFixContentHash = false;
                if (systemProperty != null && systemProperty.getValue() != null && systemProperty.getValue().equalsIgnoreCase("true")) {
                    systemProperty.setValue("false");
                    systemProperty.save();
                    needFixContentHash = true;
                }

                if (!needFixContentHash) return;
                if (isDcs) return;

                Map<String, Set<Long>> hashToIdMap = new HashMap<>();
                for (ContentTargeting contentTargeting : ContentTargeting.findAll()) {
                    String hash = contentTargeting.calculateSha256Hash();
                    Long id = contentTargeting.getId();
                    Set<Long> ids = hashToIdMap.get(hash);
                    if(ids == null) {
                        ids = new HashSet<>();
                        hashToIdMap.put(hash, ids);
                    }
                    ids.add(id);
                }

                Set<Long> usedContentTargetingId = new HashSet<>();

                {
                    Query query = session.createQuery(
                        "FROM Content AS c " +
                            "WHERE " +
                            "c.encodedContent IS NOT NULL " +
                            "AND (c.encodedContent LIKE '% content_targeting_id=\"%' OR c.encodedContent LIKE '% selected_targeting_id=\"%') "
                    );

                    List<Content> contents = query.list();

                    for(Content c : contents) {
                        String textContent = c.getEncodedContent();
                        org.jsoup.nodes.Document document = Jsoup.parse(textContent);
                        Elements tags = document.select("[content_targeting_id],[selected_targeting_id]");
                        for (Element currentElement : tags) {
                            String contentTargetingId = currentElement.attr("content_targeting_id");
                            if(currentElement.hasAttr("selected_targeting_id")) {
                                contentTargetingId = currentElement.attr("selected_targeting_id");
                            }
                            Long id = Long.valueOf(contentTargetingId);
                            usedContentTargetingId.add(id);
                        }
                    }
                }

                {
                    Query query = session.createQuery(
                        "FROM Content AS c " +
                            "WHERE " +
                            "c.encodedContent IS NOT NULL " +
                            "AND c.encodedContent LIKE '% contenttargetinghash=\"%' "
                    );

                    List<Content> contents = query.list();
                    long count = contents.size();
                    long handled = 0;
                    long start = System.currentTimeMillis();
                    for(Content content : contents) {
                        String textContent = content.getEncodedContent();
                        if (textContent != null) {
                            if (textContent.contains("contenttargetinghash")) {
                                boolean hasFixes = false;
                                org.jsoup.nodes.Document document = Jsoup.parse(textContent);
                                Elements tags = document.select("[contenttargetinghash]");
                                for (Element currentElement : tags) {
                                    String contentTargetingHash = currentElement.attr("contenttargetinghash");
                                    Set<Long> ids = hashToIdMap.get(contentTargetingHash);
                                    if(ids != null && !ids.isEmpty()) {
                                        Long contentTargetingId = null;
                                        for (Long id : ids) {
                                            if (! usedContentTargetingId.contains(id )) {
                                                contentTargetingId = id;
                                                usedContentTargetingId.add(id);
                                                break;
                                            }
                                        }
                                        if(contentTargetingId == null) {
                                            Long sourceId = ids.stream().findAny().orElse(null);
                                            ContentTargeting sourceContentTargeting = ContentTargeting.findById(sourceId);
                                            ContentTargeting clonedContentTargeting = (ContentTargeting) sourceContentTargeting.clone();
                                            clonedContentTargeting.save();
                                            contentTargetingId = clonedContentTargeting.getId();
                                        }
                                        if(contentTargetingId != null) {
                                            currentElement.attr("content_targeting_id", "" + contentTargetingId);
                                            currentElement.removeAttr("contenttargetinghash");
                                            hasFixes = true;
                                        }
                                    }
                                }

                                if (hasFixes) {
                                    content.setEncodedContent(ContentObjectContentUtil.formatBody(document));
                                    content.save();

                                    RedisUtil.getRedisContext().expire(startupNode, 3600);
                                }
                            }
                        }
                        handled ++;
                        long current = System.currentTimeMillis();
                        if(current - start > 1000 * 60) {
                            start = current;
                            LogUtil.getLog(MessagePointStartUp.class).info("fixContentContentTargetingHash on schema " + schemaName + " handled " + handled + " of " + count);
                            RedisUtil.getRedisContext().expire(startupNode, 3600);
                        }
                    }
                }

            } catch (Exception ex) {
                systemProperty.setValue("true");
                systemProperty.save();
            } finally {
                session.flush();

                HibernateUtil.getManager().restoreSession(sessionHolder);
            }
        } finally {
             LogUtil.getLog(MessagePointStartUp.class).info("fixContentContentTargetingHash finished on node " + node.getName());
         }
    }

    private static void recalculateHashIfAlgorithmChanged(Node node) {
        RedisUtil.getRedisContext().putNewValue(startupNode, NODE_IDENTIFIER);
        RedisUtil.getRedisContext().expire(startupNode, 3600);

		String startupNodeRedisValue = RedisUtil.getRedisContext().getValue(startupNode, String.class);
		if (startupNodeRedisValue != null && startupNodeRedisValue.equals(NODE_IDENTIFIER)) {
            RedisUtil.getRedisContext().lpush(startupThreadsToExecuteKey, node.getId() + startThreadsToExecuteSeparator + startupThreadRecalculateHashIfAlgorithmChanged);
        }

        startStartupThreads();
    }

    private static void doRecalculateHashIfAlgorithmChanged(Node node)
    {
        if(node.getNodeType() == Node.NODE_TYPE_DCS) return;
        String schemaName = node.getSchemaName();

        LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged start recalculation on schema " + schemaName);

        SessionHolder sessionHolder = HibernateUtil.getManager().openTemporarySession(schemaName);
        try {
            SystemProperty systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged);

            boolean needRecalculateHashOnAllObjects = false;
            boolean needRecalculateHashOnSomeObjects = false;
            long count = 0;

            if (systemProperty != null && systemProperty.getValue() != null && systemProperty.getValue().equalsIgnoreCase("true")) {
                needRecalculateHashOnAllObjects = true;
            } else {
                systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChangedOnObjects);
                if (systemProperty != null && systemProperty.getValue() != null && systemProperty.getValue().equalsIgnoreCase("true")) {
                    needRecalculateHashOnSomeObjects = true;
                }

                try {
                    NativeQuery query = HibernateUtil.getManager().getSession().createNativeQuery(
                        "SELECT count(distinct id) " +
                            "FROM hash_algorithm_change_objects"
                    );
                    Number result = (Number) query.getSingleResult();
                    count = result.longValue();
                } catch (Exception e) {
                    count = 0;
                }

                if(count > 0) {
                    needRecalculateHashOnSomeObjects = true;
                }
            }

            User userAdmin = User.findById(1);
            if (needRecalculateHashOnAllObjects) {
                RecalculateHashBackgroundTask recalculateHashBackgroundTask = new RecalculateHashBackgroundTask(
                    RecalculateHashBackgroundTask.OBJECT_ALL,
                    -1L,
                    -1L,
                    node.getId(),
                    true,
                    userAdmin
                );

                MessagePointRunnableUtil.startThreadAndWait(recalculateHashBackgroundTask);

                systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChanged);
                if (systemProperty != null) {
                    systemProperty.setValue("false");
                    systemProperty.save();
                }

                systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChangedOnObjects);
                if (systemProperty != null) {
                    systemProperty.setValue("false");
                    systemProperty.save();
                }
                HibernateUtil.getManager().getSession().createNativeQuery(
                    "DELETE " +
                        "FROM hash_algorithm_change_objects"
                ).executeUpdate();

                HibernateUtil.getManager().getSession().flush();
            }
            if (needRecalculateHashOnSomeObjects) {
                boolean hasFailedObject = false;

                List<Object> result;
                {
                    NativeQuery query = HibernateUtil.getManager().getSession().createNativeQuery(
                        "SELECT distinct id, guid, class_name " +
                            "FROM hash_algorithm_change_objects"
                    );
                    result = query.list();
                }

                long handled = 0;
                long start = System.currentTimeMillis();
                for (Object object : result) {
                    try {
                        if (object instanceof Object[]) {
                            Object[] objectRow = (Object[]) object;
                            Long id = objectRow[0] == null ? null : Long.valueOf(objectRow[0].toString());
                            String guid = objectRow[1] == null ? null : (String) objectRow[1];
                            String className = objectRow[2] == null ? null : (String) objectRow[2];
                            try {
                                if (className.equals("content")) {
                                    Content c = Content.findById(id);
                                    if (c != null)
                                        c.makeHash(true);
                                } else if (className.equals("complex_value")) {
                                    ComplexValue cv = ComplexValue.findById(id);
                                    if (cv != null)
                                        cv.makeHash(true);
                                } else if (className.equals("content_object")) {
                                    ContentObject contentObject = ContentObject.findById(id);
                                    if (contentObject != null && ! contentObject.isRemoved())
                                        contentObject.makeHash(true);
                                } else if (className.equals("content_object_data")) {
                                    ContentObjectData contentObjectData = ContentObjectData.findByGuid(guid);
                                    if (contentObjectData != null) {
                                        ContentObject contentObject = contentObjectData.getContentObject();
                                        if (contentObject != null && ! contentObject.isRemoved())
                                            contentObject.makeHash(true);
                                    }
                                } else if (className.equals("data_element_variable")) {
                                    DataElementVariable dev = DataElementVariable.findById(id);
                                    if (dev != null)
                                        dev.makeHash(true);
                                } else if (className.equals("touchpoint_selection")) {
                                    TouchpointSelection ts = TouchpointSelection.findById(id);
                                    if (ts != null && ! ts.isDeleted()) {
                                        ts.makeHash(true);
                                    }
                                } else if (className.equals("data_source")) {
                                    DataSource ds = DataSource.findById(id);
                                    if (ds != null) {
                                        ds.makeHash(true);
                                    }
                                }

                                HibernateUtil.getManager().getSession().createNativeQuery(
                                    "DELETE " +
                                        "FROM hash_algorithm_change_objects " +
                                        "WHERE id = " + id
                                ).executeUpdate();
                            } catch (IllegalStateException e) {
                                e.printStackTrace();
                                LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged in schema " + schemaName + " failed on object " + className + "(" + id + ")");
                                throw e;
                            } catch (MessagepointException me) {
                                Exception e = me;
                                while(e.getCause() != null && e.getCause() instanceof MessagepointException) {
                                    e = (MessagepointException) e.getCause();
                                }
                                if (e instanceof IllegalStateException) {
                                    e.printStackTrace();
                                    LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged in schema " + schemaName + " failed on object " + className + "(" + id + ")");
                                    throw e;
                                } else {
                                    LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged in schema " + schemaName + " failed on object " + className + "(" + id + ")");
                                }
                            } catch (Exception e) {
//                                            e.printStackTrace();
                                LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged in schema " + schemaName + " failed on object " + className + "(" + id + ")");
                            }
                        }
                    } catch (IllegalStateException e) {
                        throw e;
                    } catch (MessagepointException me) {
                        Exception e = me;
                        while(e.getCause() != null && e.getCause() instanceof MessagepointException) {
                            e = (MessagepointException) e.getCause();
                        }
                        if (e instanceof IllegalStateException) {
                            throw e;
                        } else {
                            hasFailedObject = true;
                            e.printStackTrace();
                        }
                    } catch (Exception e) {
                        hasFailedObject = true;
                        e.printStackTrace();
                    }
                    handled = handled + 1;
                    long current = System.currentTimeMillis();
                    if(current - start > 1000 * 60) {
                        start = current;
                        LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged on schema " + schemaName + " handled " + handled + " of " + count);
                        RedisUtil.getRedisContext().expire(startupNode, 3600);
                    }
                }

                if(! hasFailedObject) {
                    systemProperty = SystemProperty.findByKey(SystemPropertyKeys.Data.KEY_HashAlgorithmChangedOnObjects);
                    if (systemProperty != null) {
                        systemProperty.setValue("false");
                        systemProperty.save();
                    }
                }

                HibernateUtil.getManager().getSession().flush();
            }
            LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged finished recalculation on schema " + schemaName);
        } catch (IllegalStateException | MessagepointException ise) {
            throw ise;
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.getLog(MessagePointStartUp.class).info("recalculateHashIfAlgorithmChanged failed recalculation on schema " + schemaName);
        } finally {
            HibernateUtil.getManager().restoreSession(sessionHolder);
        }
    }

    private static void startStartupThreads() {
        MessagePointRunnableUtil.startThread(new MessagePointRunnable() {
            @Override
            public void performMainProcessing() throws Exception {
                boolean alreadyStarted = false;
                synchronized(startupThreadIsRunning) {
                    if(startupThreadIsRunning.value) {
                        alreadyStarted = true;
                    }
                    startupThreadIsRunning.value = true;
                    if(alreadyStarted) return;
                }

                try {
                    while(true) {
                        List<Object> works = RedisUtil.getRedisContext().rpop(startupThreadsToExecuteKey);
                        if(works == null || works.isEmpty()) break;
                        for (Object wo : works) {
                            if (wo instanceof String) {
                                String work = (String) wo;
                                String[] components = work.split("[" + startThreadsToExecuteSeparator + "]");
                                long nodeId = Long.parseLong(components[0]);
                                String action = components[1];
                                try {
                                    Node node = Node.findById(nodeId);
                                    if (node == null) continue;
                                    if (action.equalsIgnoreCase(startupThreadFixContentContentTargetingHash)) {
                                        doFixContentContentTargetingHash(node);
                                    } else if (action.equalsIgnoreCase(startupThreadRecalculateHashIfAlgorithmChanged)) {
                                        doRecalculateHashIfAlgorithmChanged(node);
                                    }
                                } catch (Exception e) {
                                    RedisUtil.getRedisContext().lpush(startupThreadsToExecuteKey, nodeId + startThreadsToExecuteSeparator + action);
                                }
                            }
                        }
                    }
                } finally {
                    synchronized(startupThreadIsRunning) {
                        startupThreadIsRunning.value = false;
                    }
                }

				String redisContext = RedisUtil.getRedisContext().getValue(startupNode, String.class);
                if(redisContext != null && redisContext.equals(NODE_IDENTIFIER)) {
                    RedisUtil.getRedisContext().delete(startupNode);
                }
            }
        }, Thread.MAX_PRIORITY);
    }

    private void verifyLicence(){

		String licenceFile = getMessagepointLicenceFilePath();
		File dir = new File(licenceFile);

		if (dir != null && dir.getParentFile() != null)
		{
			LogUtil.getLog(MessagePointStartUp.class).info("licenceFile is "+ licenceFile);
			System.setProperty(Constants.KEY_SYS_PROP_LICENCE_FILE, licenceFile);
			System.setProperty(Constants.KEY_SYS_PROP_NFO_FILE, dir.getParentFile().getAbsolutePath() + File.separator + Constants.LIC_NFO_NAME);
		}

		final byte[] bytes = {
				99,111,109,46,
				112,114,105,110,111,118,97,46,
				108,105,99,101,110,99,101,46,99,111,109,109,111,110,46,
				76,105,99,101,110,99,101,86,101,114,105,102,105,101,114};

		Object instance;
		try {
			Class<?> verifierClass = Class.forName(new String(bytes));
			instance = verifierClass.newInstance();

			((LicenceVerifier)instance).verify();

		} catch (LicenceError | Exception licenceError) {
			LogUtil.getLog(MessagePointStartUp.class).warn("Licence Could not be verified.", licenceError);
		}

	}

	private void processLicenceToken(){
		MessagepointLicenceManager.getInstance().initLicenceProperties();
	}

	private String getMessagepointLicenceFilePath(){

		// First check to see if a license file has been configured via a runtime property
		String licenseConfigPath = PropertyUtils.getRuntimeProperty("prinova.license.file.name");

		if (licenseConfigPath != null) {
			return licenseConfigPath;
		}

		// Next check to see if a license file exists at ~/Work/licence/licence.xml (legacy)
		File devLicenceFile = Paths.get(System.getProperty("user.home"), "Work", "licence", "licence.xml").toFile();

		// Next load the default license path conigured via licenceConfig.properties (defaults to /Work/licence/licence.xml) (legacy)
		if (devLicenceFile.exists() && devLicenceFile.canRead()) {
			return devLicenceFile.getAbsolutePath();
		}


		if(licenceFile!=null && !licenceFile.trim().isEmpty()){
			return licenceFile;
		}else
			return getRootPath() + defaultLicenceFile;
	}

	public String getDefaultLicenceFile() {
		return defaultLicenceFile;
	}

	public void setDefaultLicenceFile(String defaultLicenceFile) {
		this.defaultLicenceFile = defaultLicenceFile;
	}


	private String getRootPath(){
		String parentPath = servletContext.getRealPath("/");

		if(!parentPath.endsWith(File.separator)){
			parentPath  = parentPath + File.separator;
		}

		return parentPath;
	}

	private void processBuildInfo(){
		try {
			Properties devProperties = new Properties();
			InputStream input = getClass().getResourceAsStream("/devtools.properties");
			devProperties.load( input );

			servletContext.setAttribute(KEY_DEVTOOLS_PROPERTIES, devProperties);

			Properties buildProperties = new Properties();
			InputStream in = getClass().getResourceAsStream("/appbuild.properties");
			buildProperties.load( in );

			servletContext.setAttribute(KEY_BUILD_PROPERTIES, buildProperties);

			setupBuildDate(buildProperties);
			setupMajorBuildRevision(buildProperties);
			setupBuildRevision(buildProperties);

		} catch (Exception e) {
			LogUtil.getLog(MessagePointStartUp.class).error("Error: ", e);
		}

	}

	public static String getDevToolsPropertyValue(String devToolsPropertyKey)
	{
		Properties devProps = (Properties)servletContext.getAttribute(KEY_DEVTOOLS_PROPERTIES);

		String value =  (devProps == null)?null :devProps.getProperty(devToolsPropertyKey);
		if (value == null)
		{
			return "";

		}
		else
		{
			return value;
		}
	}

	public static boolean getDevToolsPropertyBooleanValue(String devToolsPropertyKey)
	{
		if (servletContext == null) {
			return false;
		}

		Properties devProps = (Properties)servletContext.getAttribute(KEY_DEVTOOLS_PROPERTIES);

		String value =  (devProps == null)?null :devProps.getProperty(devToolsPropertyKey);
		if (value == null)
		{
			return false;
		}
		else if(value.equalsIgnoreCase("true"))
		{
			return true;

		}
		else
		{
			return false;
		}
	}

	private static String buildDate = "";

	private static void setupBuildDate(Properties buildProps) {
		if (buildProps == null) {
			throw new RuntimeException("No build infomration is included.");
		};

		String date =  (buildProps == null) ? null : buildProps.getProperty("app.build.date") ;
		if (date == null) {
			buildDate = "";
		} else if ("@DATE@".equals(date)) {
			buildDate = "Development";
		} else {
			buildDate = date;
		}
	}

	public static String getBuildDate() {
		return buildDate;
	}

	private static String majorBuildRevision = "xx.xx";

	private static void setupMajorBuildRevision(Properties buildProps) {
		if (buildProps == null) {
			throw new RuntimeException("No build infomration is included.");
		};

		String major   = buildProps.getProperty("app.version.major");
		String minor   = buildProps.getProperty("app.version.minor");

		StringBuilder sb = new StringBuilder();
		sb.append((StringUtils.isBlank(major))?"xx": major).append('.');
		sb.append((StringUtils.isBlank(minor))?"xx": minor);

		majorBuildRevision = sb.toString();
	}

	public static String getMajorBuildRevision(){
		return majorBuildRevision;
	}

	private static String buildRevision = "xx.xx.xxx";

	private static void setupBuildRevision(Properties buildProps) {
		if (buildProps == null) {
			throw new RuntimeException("No build infomration is included.");
		};

		String major   = buildProps.getProperty("app.version.major");
		String minor   = buildProps.getProperty("app.version.minor");
		String version = buildProps.getProperty("app.version.revision");

		StringBuilder sb = new StringBuilder();
		sb.append((StringUtils.isBlank(major))?"xx": major).append('.');
		sb.append((StringUtils.isBlank(minor))?"xx": minor).append('.');
		sb.append((StringUtils.isBlank(version))?"xxx": version);

		buildRevision = sb.toString();
	}

	public static String getBuildRevision(){
		return buildRevision;
	}
}
