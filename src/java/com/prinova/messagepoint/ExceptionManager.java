package com.prinova.messagepoint;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import com.prinova.messagepoint.exceptionhandler.LogExceptionHandler;
import com.prinova.messagepoint.exceptionhandler.RollbackHibernateExceptionHandler;
import com.prinova.messagepoint.exceptionhandler.SpringMvcExceptionHandler;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;

/**
 * This is a global exception handler.  All exception handling 
 * should be referred to {@link #handle(Throwable, Object...)} method.
 * 
 * This is also a spring {@link HandlerExceptionResolver}.  Spring will 
 * invoke this global exception handler (if defined in the bean factory).
 *
 */
public class ExceptionManager implements HandlerExceptionResolver {

	/**
	 * My global static self.
	 */
	private static final ExceptionManager self = new ExceptionManager();
	
	/**
	 * Registered handlers.
	 */
	private static Set<ExceptionHandler> handlers = initHandlers();	
	
	public static ExceptionManager instance(){
		return self;
	}
	
	private ExceptionManager() { /* static class */ }
	
	/**
	 * Registers a new {@link ExceptionHandler}
	 *
     */
	public void register(ExceptionHandler handler){
		if(handler != null){
			handlers.add(handler);	
		}
	}
	
	/**
	 */
	public int getPriority() { return -1; } 
	
	
	public Object handle(Throwable t, Object...parameters) {
		return handle(ExceptionEvent.createDefault(t, parameters));
	}

	public Object handle(ExceptionEvent event) {
		
		if(event == null){ return false; }
		
		boolean processed = false;
		
		HashMap<ExceptionHandler, Object> returnValues = new HashMap<>();
		Throwable processingException = null;
		
		try {
			List<ExceptionHandler> handlers = getRegisteredHandlers(event);
			
			Object returnValue = null;
			for (ExceptionHandler exceptionHandler : handlers) {
				returnValue = exceptionHandler.handle(event);
				returnValues.put(exceptionHandler, returnValue);
			}
			
			processed = true;
			
		} catch (Throwable e) {
			processed = false;
			processingException = e;
			
		}finally{
			//Should never block an error
			if(Error.class.isInstance(processingException)){
				throw (Error)processingException;
			}
			
			//OK we are dealing with an Exception/RuntimeException
			if(event.isThrowException()){
				Throwable t = (processed)? event.getException(): processingException;
				ExceptionHandlingResult result = ExceptionHandlingResult.createNew(event, returnValues, processed);
				String message = "Exception for an instance with schema name: " + MessagepointCurrentTenantIdentifierResolver.getTenantIdentifierWithPodMasterTranslation();
				throw new MessagepointException(message, t, event, result); 
			}
		}
		
		return returnValues;
	}
	
	@SuppressWarnings("unchecked")
	public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
		
		Throwable originalException = ex;
		ExceptionEvent event = null;
		
		
		if(MessagepointException.class.isInstance(ex)){
			MessagepointException messagepointException = (MessagepointException) ex;
			originalException = messagepointException.getCause();
			event = messagepointException.getEvent();
			
			if(messagepointException.isProcessed()){
				//The exception is already handled.  Just show the view.
				return SpringMvcExceptionHandler.getGlobalErroView( originalException );
			}
		}
		
		
		//OK We are here because the exception is not a messagepoint exception or has not been processed.
		if(event == null){
			event = ExceptionEvent.createDefault(originalException, request, response);	
		}
		event.setThrowException(false);
		
		Map<ExceptionHandler, Object> returnValues = (HashMap<ExceptionHandler, Object>) handle( event );

		for (Object object : returnValues.values()) {
			if(ModelAndView.class.isInstance(object)){
				return (ModelAndView)object;
			}
		}
		
		return null;
	}
	
	public List<ExceptionHandler> getRegisteredHandlers(ExceptionEvent event){
		ArrayList<ExceptionHandler> list = new ArrayList<>();
		
		boolean overrideHandlers = (event != null) && event.isOverrideHandlers();
		
		if(!overrideHandlers){
			for (ExceptionHandler exceptionHandler : handlers) {
				if(!list.contains(exceptionHandler) && exceptionHandler.canHandle(event)){
					list.add(exceptionHandler);	
				}
			}
		}

		if (event != null) {
			for (ExceptionHandler exceptionHandler : event.getHandlers()) {
				if (!list.contains(exceptionHandler) && exceptionHandler.canHandle(event)) {
					list.add(exceptionHandler);
				}
			}
		}
		
		//order them according to their priority
		list.sort(new PriorityComparator());
		
		
		return list;
	}
	
	
	private static Set<ExceptionHandler> initHandlers(){
		
		ExceptionHandler[] handlers = {
				new RollbackHibernateExceptionHandler(), 
				new LogExceptionHandler(),
				new SpringMvcExceptionHandler()
		};

        return new HashSet<>(Arrays.asList(handlers));
	}
	
	private static class PriorityComparator implements Comparator<ExceptionHandler>{
		public int compare(ExceptionHandler o1, ExceptionHandler o2) {
			return Integer.compare(o1.getPriority(), o2.getPriority());
		}
	}
	
	
}
