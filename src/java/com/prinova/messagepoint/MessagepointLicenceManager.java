package com.prinova.messagepoint;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.prinova.licence.common.Constants;
import com.prinova.licence.common.XmlUtils;
import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider;
import com.prinova.messagepoint.model.Branch;
import com.prinova.messagepoint.model.MessagepointLicence;
import com.prinova.messagepoint.model.admin.ApplicationLocale;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.platform.services.MessagepointServiceFactory;
import com.prinova.messagepoint.platform.services.Service;
import com.prinova.messagepoint.platform.services.ServiceExecutionContext;
import com.prinova.messagepoint.platform.services.licence.AddOrUpdateMessagepointLicencesService;
import com.prinova.messagepoint.util.BranchUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;
import org.springframework.orm.hibernate5.SessionHolder;

import javax.servlet.ServletContext;
import java.io.FileInputStream;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class MessagepointLicenceManager {

	private static final Log log = LogUtil.getLog(MessagepointLicenceManager.class);

	private static final Map<String, MessagepointLicenceManager> INSTANCES_MAP = Collections.synchronizedMap(new LinkedHashMap<>());

	private final String schemaName;
	private final MessagepointLicencesCacheHandler messagepointLicencesCacheHandler;

	private MessagepointLicenceManager(String schemaName) {
		this.schemaName = schemaName;
		this.messagepointLicencesCacheHandler = new MessagepointLicencesCacheHandler(schemaName);
	}

	public static MessagepointLicenceManager getInstance() {
		String schemaName = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();

		return getInstance(schemaName);
	}

	public static MessagepointLicenceManager getInstance(String schemaName) {
		final String schemaNameUpperCase = schemaName == null ? "" : schemaName.toUpperCase();

		synchronized (INSTANCES_MAP) {
			MessagepointLicenceManager messagepointLicenceManager = INSTANCES_MAP.get(schemaNameUpperCase);
			if (messagepointLicenceManager == null) {
				messagepointLicenceManager = new MessagepointLicenceManager(schemaNameUpperCase);
				INSTANCES_MAP.put(schemaNameUpperCase, messagepointLicenceManager);
			}

			return messagepointLicenceManager;
		}
	}

	public static void clearMessagepointLicencesCache(String targetSchemaName) {
		INSTANCES_MAP.remove(targetSchemaName.toUpperCase());
	}

	public MessagepointLicence getMessagepointLicence(String key) {
		Map<String, MessagepointLicence> messagepointLicencesMap = messagepointLicencesCacheHandler.getMessagepointLicencesMap();

		MessagepointLicence messagepointLicence = messagepointLicencesMap.get(key);
		return messagepointLicence;
	}

	public List<MessagepointLicence> getMessagepointLicences(List<String> keys) {
		final Map<String, MessagepointLicence> messagepointLicencesMap = messagepointLicencesCacheHandler.getMessagepointLicencesMap();

		List<MessagepointLicence> result = keys.stream()
				.map(key -> messagepointLicencesMap.get(key))
				.filter(messagepointLicence -> messagepointLicence != null)
				.collect(Collectors.toCollection(LinkedList::new));

		return result;
	}

	public List<MessagepointLicence> getMessagepointLicencesForPredicate(Predicate<MessagepointLicence> predicate) {
		final Map<String, MessagepointLicence> messagepointLicencesMap = messagepointLicencesCacheHandler.getMessagepointLicencesMap();

		List<MessagepointLicence> result = messagepointLicencesMap.values().stream()
				.filter(messagepointLicence -> messagepointLicence != null)
				.filter(messagepointLicence -> predicate.test(messagepointLicence))
				.collect(Collectors.toCollection(LinkedList::new));

		return result;
	}

	public MessagepointLicence findByName(String messagepointLicenceName) {
		final List<MessagepointLicence> messagepointLicencesForPredicate = getMessagepointLicencesForPredicate(messagepointLicence -> StringUtils.equals(messagepointLicence.getName(), messagepointLicenceName));
		if (CollectionUtils.isEmpty(messagepointLicencesForPredicate)) {
			return null;
		}

		return messagepointLicencesForPredicate.get(0);
	}

	public List<MessagepointLicence> getAllMessagepointLicences() {
		final Map<String, MessagepointLicence> messagepointLicencesMap = messagepointLicencesCacheHandler.getMessagepointLicencesMap();

		List<MessagepointLicence> result = messagepointLicencesMap.values().stream()
				.filter(messagepointLicence -> messagepointLicence != null)
				.collect(Collectors.toCollection(LinkedList::new));

		return result;
	}

	private int getMessagepointLicenceAccessibleNumberOrDefault(String key, Integer defaultValue) {
		final MessagepointLicence messagepointLicence = getMessagepointLicence(key);
		if (messagepointLicence != null) {
			return messagepointLicence.getAccessible();
		}

		return defaultValue;
	}

	private boolean getMessagepointLicenceFeatureAccessibleBooleanOrDefault(String key, Boolean defaultValue) {
		final MessagepointLicence messagepointLicence = getMessagepointLicence(key);
		if (messagepointLicence != null) {
			return messagepointLicence.isFeatureAccessible();
		}

		return defaultValue;
	}

	public int getNumberOfFullUsers() {
		return getMessagepointLicenceAccessibleNumberOrDefault(MessagepointLicence.LIC_KEY_NUMBEROFFULLUSERS, 0);
	}

	public int getNumberOfRestrictedUsers() {
		return getMessagepointLicenceAccessibleNumberOrDefault(MessagepointLicence.LIC_KEY_NUMBEROFRESTRICTEDUSERS, 0);
	}

	public int getNumberOfConnectedUsers() {
		return getMessagepointLicenceAccessibleNumberOrDefault(MessagepointLicence.LIC_KEY_NUMBEROFCONNECTEDUSERS, 0);
	}

	public int getNumberOfAllowedTenants() {
		return getMessagepointLicenceAccessibleNumberOrDefault(MessagepointLicence.LIC_KEY_NUMBEROFTENANTS, 0);
	}

	public int getNumberOfAllowedSandboxes() {
		return getMessagepointLicenceAccessibleNumberOrDefault(MessagepointLicence.LIC_KEY_NUMBEROFSANDBOXES, 0);
	}

	public boolean isMultiTenant() {
		return !getAllMessagepointLicences().isEmpty();
	}

	public boolean isSimulationFlag() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SIMULATION, false);
	}

	public boolean isLicencedForInsertManagement() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_INSERTMANAGEMENT, false);
	}

	public boolean isLicencedForGMCConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_GMCCONNECTOR, false);
	}

	public boolean isLicencedForDialogueConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_DIALOGUECONNECTOR, false);
	}

	public boolean isLicencedForDialogueDXF() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_DIALOGUEDXF, false);
	}

	public boolean isLicensedForSefasConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SEFASCONNECTOR, false);
	}

	public boolean isLicensedForSefasHCSConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SEFASHCSCONNECTOR, false);
	}

	public boolean isLicencedForEMessagingEmailConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_EMESSAGINGEMAILCONNECTOR, false);
	}

	public boolean isLicencedForEMessagingSMSConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_EMESSAGINGSMSCONNECTOR, false);
	}

	public boolean isLicencedForSendmailConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SENDMAILCONNECTOR, false);
	}

	public boolean isLicencedForExactTargetConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_EXACTTARGETCONNECTOR, false);
	}

	public boolean isLicencedForClickatellConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_CLICKATELLCONNECTOR, false);
	}

	public boolean isLicencedForFTPWebConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_FTPWEBCONNECTOR, false);
	}

	public boolean isLicencedForNativePrintConnector() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_NATIVEPRINTCONNECTOR, false);
	}

	public boolean isLicencedForMessagepointInteractive() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_MESSAGEPOINTINTERACTIVE, false);
	}

	public boolean isLicencedForVariantManagement() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_VARIANTMANAGEMENT, false);
	}

	public boolean isLicencedForDataAnonymizer() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_DATAANONYMIZER, false);
	}

	public boolean isLicencedForECatalog() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_ECATALOG, false);
	}

	public boolean isLicencedForLookupTable() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_LOOKUPTABLE, false);
	}

	public boolean isLicencedForTransactionAPI() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_TRANSACTIONAPI, false);
	}

    public boolean isLicencedForSyncUpdateFromParent() { return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_PARENT, false); }

    public boolean isLicencedForSyncUpdateFromChild() { return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_CHILD, false); }

	public boolean isLicencedForAIRewrites() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_AIREWRITES, false);
	}

	public boolean isLicencedForAITranslate() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_AITRANSLATE, false);
	}

	public boolean isLicencedForExchange() {
		return MessagePointStartUp.getDevToolsPropertyBooleanValue("enable.touchpoint.exchange");
	}

	public boolean isLicensedForMessagepointExchange() {
		return MessagePointStartUp.getDevToolsPropertyBooleanValue("enable.messagepoint.exchange");
	}

	public boolean isLicencedForMarcieAssist() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_MARCIE_ASSIST, false);
	}

	/**
	 * Returns always true as the previous implementation.
	 */
	public boolean isLicencedForSFMCJBConnector() {
// TODO: fix licensing once external license is created
//		return licencedForSFMCJBConnector;
		return true;
	}

//	public boolean isLicencedForSFMCJBConnector() {
//		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_SFMCJBCONNECTOR, false);
//	}

	public MessagepointLicenceToken getOtherProperties() {
		return (MessagepointLicenceToken) MessagePointStartUp.getServletContext().getAttribute(Constants.KEY_SYS_PROP_LICENCE_FILE);
	}

	public final String getId() {
		return this.getOtherProperties().getId();
	}

	public final String getVersion() {
		return this.getOtherProperties().getVersion();
	}

	public final String getCustomerName(){
		return this.getOtherProperties().getCustomerName();
	}

	public final String getApplicationName(){
		return this.getOtherProperties().getApplicationName();
	}

	public final String getEnvironmentType(){
		return this.getOtherProperties().getEnvironmentType();
	}

	public final String getServerName(){
		return this.getOtherProperties().getServerName();
	}

	public final String[] getServerIps(){
		return this.getOtherProperties().getServerIps();
	}

	public final Date getLicenceCreationDate() {
		return this.getOtherProperties().getLicenceCreationDate();
	}

	public final Date getLicenceStartDate() {
		return this.getOtherProperties().getLicenceStartDate();
	}

	public final Date getLicenceExpiryDate() {
		return this.getOtherProperties().getLicenceExpiryDate();
	}

	public boolean isLicencedForPINC() {
		return getMessagepointLicenceFeatureAccessibleBooleanOrDefault(MessagepointLicence.LIC_KEY_PINC, false);
	}

	public String getBuildDate() {
		return MessagePointStartUp.getBuildDate();
	}

	public String getBuildRevision(){
		return MessagePointStartUp.getBuildRevision();
	}

	public void reloadLicences() {
		messagepointLicencesCacheHandler.clearMessagepointLicencesCache();
	}

	public boolean isEmailChannelsAuthorized() {
		return this.isLicencedForEMessagingEmailConnector() || this.isLicencedForExactTargetConnector() || this.isLicencedForSendmailConnector() || this.isLicencedForSFMCJBConnector();
	}

	public boolean isPrintChannelsAuthorized() {
		return this.isLicencedForDialogueConnector() || this.isLicencedForGMCConnector() || this.isLicencedForNativePrintConnector() ||  this.isLicensedForSefasConnector() ||  this.isLicensedForSefasHCSConnector();
	}

	public boolean isSmsChannelsAuthorized() {
		return this.isLicencedForEMessagingSMSConnector() || this.isLicencedForClickatellConnector();
	}

	public boolean isWebChannelsAuthorized() {
		return this.isLicencedForFTPWebConnector();
	}

	public boolean isLicencedForLocale(long messageLocaleId){
		ApplicationLocale appLocale = ApplicationLocale.findByMpLocaleId(messageLocaleId);
		if (appLocale != null) {
			return appLocale.getAccessible();
		} else {
			return false;
		}
	}

	public static boolean canActivateNewWorkflowUser(Branch branch){
		if (branch == null) {
			return false;
		}

		return (BranchUtil.getNumberOfCurrentUsedLicences(branch, User.LICENSED_TYPE_WORKFLOW_ACCESS, 0) < MessagepointLicenceManager.getInstance(branch.getDcsSchemaName()).getNumberOfRestrictedUsers());
	}

	public static boolean canActivateNewConnectedUser(Branch branch){
		if (branch == null) {
			return false;
		}

		return (BranchUtil.getNumberOfCurrentUsedLicences(branch, User.LICENSED_TYPE_CONNECTED_ACCESS, 0) < MessagepointLicenceManager.getInstance(branch.getDcsSchemaName()).getNumberOfConnectedUsers());
	}

	public static boolean canActivateNewRegularUser(Branch branch){
		if (branch == null) {
			return false;
		}

		return (BranchUtil.getNumberOfCurrentUsedLicences(branch, User.LICENSED_TYPE_REGULAR_ACCESS, 0) < MessagepointLicenceManager.getInstance(branch.getDcsSchemaName()).getNumberOfFullUsers());
	}

	public static boolean canAddDomain(){
		return canAddProductionInstance(com.prinova.messagepoint.model.Node.getCurrentBranch());
	}

	public static boolean canAddProductionInstance(Branch branch){
		// check total # of licensed production instance from MessagepointLicenceManager based on branch
		// return true if existing is less than the total #, else false
		return (com.prinova.messagepoint.model.Node.findAllByNodeTypeAndBranch(
				branch, com.prinova.messagepoint.model.Node.NODE_TYPE_PRODUCTION).size()
				< MessagepointLicenceManager.getInstance(branch.getDcsSchemaName()).getNumberOfAllowedTenants());
	}

	public static boolean canAddSandboxInstance(Branch branch){
		// check total # of licensed sandbox instance from MessagepointLicenceManager based on branch
		// return true if existing is less than the total #, else false
		return (com.prinova.messagepoint.model.Node.findAllByNodeTypeAndBranch(
				branch, com.prinova.messagepoint.model.Node.NODE_TYPE_SANDBOX).size()
				< MessagepointLicenceManager.getInstance(branch.getDcsSchemaName()).getNumberOfAllowedSandboxes());
	}

	public static boolean canAddExchangeInstance(Branch branch){
		return (com.prinova.messagepoint.model.Node.findAllByNodeTypeAndBranch(
				branch, com.prinova.messagepoint.model.Node.NODE_TYPE_EXCHANGE).size()
				< 100);
	}

	public static void removeInstance(String schemaName) {
		INSTANCES_MAP.remove(schemaName == null ? "" : schemaName.toUpperCase());
	}


	/**
	 * This function is invoked at startup.
	 */
	public void initLicenceProperties() {
		try {
			FileInputStream in = new FileInputStream( System.getProperty(Constants.KEY_SYS_PROP_LICENCE_FILE) );
			Document d;
			d = XmlUtils.readDocument(in);
			in.close();

			Map<String, MessagepointLicence> licencesMap = new HashMap<>();
			licencesMap.put(MessagepointLicence.LIC_KEY_NUMBEROFRESTRICTEDUSERS, createLicenceEntry(d, MessagepointLicence.LIC_KEY_NUMBEROFRESTRICTEDUSERS, Constants.XP_NumberOfRestrictedUsers, false));
			// TODO: replace key to Connected
			licencesMap.put(MessagepointLicence.LIC_KEY_NUMBEROFCONNECTEDUSERS, createLicenceEntry(d, MessagepointLicence.LIC_KEY_NUMBEROFCONNECTEDUSERS, Constants.XP_NumberOfRestrictedUsers, false));
			licencesMap.put(MessagepointLicence.LIC_KEY_NUMBEROFFULLUSERS, createLicenceEntry(d, MessagepointLicence.LIC_KEY_NUMBEROFFULLUSERS, Constants.XP_NumberOfFullUsers, false));
			// Modules information
			for(String moduleKey : MessagepointLicence.MODULES_KEYS){
				MessagepointLicence licence = createLicenceEntry(d, moduleKey, moduleKey, true);
				// Hard coded for "E-Catalog" and "Lookup Table" features
				if (moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_ECATALOG)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_LOOKUPTABLE)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_PINC)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_AIREWRITES)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_AITRANSLATE)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SEFASCONNECTOR)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SEFASHCSCONNECTOR)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_PARENT)
                        || moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_CHILD)
						|| moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_MARCIE_ASSIST)
				){
					licence.setValue("enable");
				}

				if (moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_PARENT)) {
					licence.setAccessible(0);
				}

                if (moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_SYNC_UPDATE_FROM_CHILD)) {
                    licence.setAccessible(0);
                }

                licencesMap.put(moduleKey, licence);
				if (moduleKey.equalsIgnoreCase(MessagepointLicence.LIC_KEY_NUMBEROFTENANTS)) {
					licencesMap.put(MessagepointLicence.LIC_KEY_NUMBEROFSANDBOXES, createLicenceEntry(d, MessagepointLicence.LIC_KEY_NUMBEROFSANDBOXES, MessagepointLicence.LIC_KEY_NUMBEROFTENANTS, true));
				}
			}

			Service service = MessagepointServiceFactory.getInstance().lookupService(AddOrUpdateMessagepointLicencesService.SERVICE_NAME, AddOrUpdateMessagepointLicencesService.class);
			ServiceExecutionContext context = AddOrUpdateMessagepointLicencesService.createContextForMasterNodeStartupInit(-1, licencesMap);
			if ( service != null && context != null ){
				LogUtil.getLog(this.getClass()).info("About to initialize licences.");
				service.execute(context);
				LogUtil.getLog(this.getClass()).info("Finished initializing licences.");
			}

			// Save other properties to the servlet context
			ServletContext servletContext = MessagePointStartUp.getServletContext();
			servletContext.setAttribute(Constants.KEY_SYS_PROP_LICENCE_FILE, new MessagepointLicenceToken());
		} catch (Exception e) {
			log.error("Error: ", e);
		}
	}

	private MessagepointLicence createLicenceEntry(Document d, String dbKey, String xmlKey, boolean fromModule){
		MessagepointLicence lic = new MessagepointLicence();
		lic.setKey(dbKey);
		if (fromModule) {
			lic.setValue(getModuleLicenceValue(d, xmlKey));
		} else {
			lic.setValue(getStringValue(d, xmlKey));
		}

		return lic;
	}

	private final String getStringValue(Document d, String xpath){
		Node n = d.selectSingleNode(xpath);
		return (n == null) ? null: n.getText();
	}

	private String getModuleLicenceValue (Document d, String moduleKey) {
		String query = "//modules//*//property[@key='"+moduleKey+"']";
		Element propertyNode = (Element)d.selectSingleNode(query);

		if (propertyNode != null) {
			String value = propertyNode.attributeValue("value");
			return value;
		}

		return null;
	}

	private static class MessagepointLicencesCacheHandler {

		private final Object schemaLock = new Object();

		private final LoadingCache<String, Map<String, MessagepointLicence>> messagepointLicencesCache = CacheBuilder.newBuilder()
				.expireAfterWrite(1, TimeUnit.MINUTES)
				.build(
                        new CacheLoader<>() {
                            @Override
                            public Map<String, MessagepointLicence> load(String key) {
                                // LoadingCache does not support null values.

                                log.debug("Cleared MP_LICENSE values cache for " + (StringUtils.isEmpty(key) ? MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName() : key) + " dbschema.");
                                return new LinkedHashMap<>();
                            }
                        }
				);

		private final String schemaName;

		private MessagepointLicencesCacheHandler(String schemaName) {
			this.schemaName = schemaName;
		}

		protected void clearMessagepointLicencesCache() {
			messagepointLicencesCache.invalidateAll();
		}

		private String computeLicensesCacheKey() {
			return schemaName == null ? "" : schemaName.toUpperCase();
		}

		protected Map<String, MessagepointLicence> getMessagepointLicencesMap() {
			synchronized (getSchemaLock()) {
				try {
					Map<String, MessagepointLicence> messagepointLicencesMap = messagepointLicencesCache.get(computeLicensesCacheKey());
					if (MapUtils.isNotEmpty(messagepointLicencesMap)) {
						return messagepointLicencesMap;
					}

					log.debug("Reload values from MP_LICENSE table for dbschema: " +
									(StringUtils.isEmpty(schemaName) ? MessagepointMultiTenantConnectionProvider.getPodMasterSchemaName() : schemaName)
							);
					List<MessagepointLicence> list = readMessagepointLicences();
					list = (list == null) ? Lists.newLinkedList() : list;

					messagepointLicencesMap = list.stream()
							.collect(Collectors.toMap(
									MessagepointLicence::getKey,
									Function.identity(),
									(oldValue, newValue) -> oldValue,
									LinkedHashMap::new)
							);

					if (MapUtils.isNotEmpty(messagepointLicencesMap)) {
						// We assume that the system has at least one System MessagepointLicence set.
						// So we store in cache only a non-empty map of System Properties.

						messagepointLicencesCache.put(computeLicensesCacheKey(), messagepointLicencesMap);
					}

					// We return a non-null map in all cases in order to prevent NullPointerException errors for the caller.
					return messagepointLicencesMap;
				} catch (ExecutionException e) {
					log.error(e.getMessage(), e);
				}

				return Maps.newLinkedHashMap();
			}
		}

		protected void putMessagepointLicences(Map<String, MessagepointLicence> messagepointLicencesMap) {
			messagepointLicencesCache.put(computeLicensesCacheKey(), messagepointLicencesMap);
		}

		private List<MessagepointLicence> readMessagepointLicences() {
			final String tmpCurrentSchemaName = MessagepointCurrentTenantIdentifierResolver.getTenantIdentifier();
			List<MessagepointLicence> result = null;

			if (StringUtils.equalsIgnoreCase(tmpCurrentSchemaName, schemaName)) {

				try {
					result = HibernateUtil.getManager().getObjects(MessagepointLicence.class);
				} catch (Exception e) {
					log.error("readMessagepointLicences (tmpCurrentSchemaName = " + tmpCurrentSchemaName + ", schemaName = " + schemaName + "): " + e.getMessage());
				}

			} else {

				String tmpTenantIdentifier = StringUtils.isEmpty(schemaName) ? null : schemaName;

				SessionHolder mainSessionHolder = null;
				try {
					mainSessionHolder = HibernateUtil.getManager().openTemporarySession(tmpTenantIdentifier);

					try {
						result = HibernateUtil.getManager().getObjects(MessagepointLicence.class);
					} catch (Exception e) {
						log.error("readMessagepointLicences (tmpCurrentSchemaName = " + tmpCurrentSchemaName + ", schemaName = " + schemaName + "): " + e.getMessage());
					}
				} finally {
					HibernateUtil.getManager().restoreSession(mainSessionHolder);
				}
			}

			return result;
		}

		private Object getSchemaLock() {
			return schemaLock;
		}
	}
}
