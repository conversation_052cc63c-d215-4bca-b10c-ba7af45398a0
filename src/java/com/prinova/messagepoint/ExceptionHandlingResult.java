package com.prinova.messagepoint;

import java.util.HashMap;
import java.util.Map;


public class ExceptionHandlingResult {

	private ExceptionEvent event;
	private java.util.Map<ExceptionHandler, Object> returnValues = new HashMap<>();
	private boolean processed = false;
	
	
	ExceptionHandlingResult() { }

	public ExceptionEvent getEvent() {
		return event;
	}

	public void setEvent(ExceptionEvent event) {
		this.event = event;
	}

	public java.util.Map<ExceptionHandler, Object> getReturnValues() {
		return returnValues;
	}

	public void setReturnValues(java.util.Map<ExceptionHandler, Object> returnValues) {
		this.returnValues = returnValues;
	}

	public boolean isProcessed() {
		return processed;
	}

	public void setProcessed(boolean processed) {
		this.processed = processed;
	}
	
	public static ExceptionHandlingResult createNew(ExceptionEvent event, Map<ExceptionHandler, Object> returnValues, boolean processed){
		ExceptionHandlingResult result = new ExceptionHandlingResult();
		
		result.setEvent(event);
		result.setProcessed(processed);
		
		if(returnValues != null){
			result.getReturnValues().putAll(returnValues);	
		}
		
		return result;
	}

}
