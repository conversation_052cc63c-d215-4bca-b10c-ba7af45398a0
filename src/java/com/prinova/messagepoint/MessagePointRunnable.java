package com.prinova.messagepoint;

import com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver;
import com.prinova.messagepoint.model.Node;
import com.prinova.messagepoint.model.security.User;
import com.prinova.messagepoint.util.ApplicationUtil;
import com.prinova.messagepoint.util.HibernateUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate5.SessionFactoryUtils;
import org.springframework.orm.hibernate5.SessionHolder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.List;

/**
 * This abstract class binds the thread's current session to the transaction manager to ensure the OpenSessionInView
 * pattern in the thread (ie. single session)
 * 
 * Subclass should implement all business logic in the method performMainProcessing().
 * 
 */
public abstract class MessagePointRunnable implements MessagePointRunnableInterface {

	private static final Log log = LogUtil.getLog(MessagePointRunnable.class);

	private String localSchemaName = "all";
	private User localUser = null;
	private Thread owningThread; 
	private Boolean finished = false;
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, readOnly = false)
	public abstract void performMainProcessing() throws Exception;

	public void setTenantIdentifier(String schemaName) {
		localSchemaName = schemaName;
	}

	public String getTenantIdentifier() {
		return localSchemaName;
	}

	public void setLocalUser(User user) {
		localUser = user;
	}
	
	public Thread getOwningThread()
	{
		return owningThread;
	}
	
	@Override
	public void run() 
	{
        ApplicationUtil.setDebugModeInMessagePointRunnable();

		try
		{
			owningThread = Thread.currentThread();
			
			if (localUser != null) {
				MessagepointCurrentTenantIdentifierResolver.setUser(localUser);
			}
			List<String> allSchemas = new ArrayList<>();

			if (localSchemaName != null)
			{
				if (localSchemaName.equals("all") || localSchemaName.equalsIgnoreCase("allOnline"))
				{
					allSchemas.addAll(Node.getAllOnlineSchemas());
				}
				else 
				{
					if (localSchemaName.equalsIgnoreCase("allOffline"))
					{
						allSchemas.addAll(Node.getAllOfflineSchemas());
					}
					else
					{
						if (localSchemaName.equalsIgnoreCase("allEnabled"))
						{
							allSchemas.addAll(Node.getAllEnabledSchemas());
						}
						else
						{
							allSchemas.add(localSchemaName);				
						}
					}
				}
			}
			else
			{
				allSchemas.add(localSchemaName);							
			}

			if (log.isDebugEnabled())
			{
				log.debug("MessagePointRunnable " + this);
			}

			for (String schema : allSchemas)
			{
				SessionFactory sessionFactory = HibernateUtil.getManager().getSessionFactory();
				Session session = null;
				boolean wait = false;

				try 
				{
					if (TransactionSynchronizationManager.hasResource(sessionFactory)) 
					{
						// We cannot bind a new session. We have to wait.
						//
						if (log.isDebugEnabled())
						{
							log.debug("MessagePointRunnable for schema = " + schema + " has to wait.");
						}
						wait = true;
					} 
					else 
					{
						// Process a background task for the schema
						//
						MessagepointCurrentTenantIdentifierResolver.setTenantIdentifier(schema);
						session = HibernateUtil.getManager().openSessionInView();
						TransactionSynchronizationManager.bindResource(sessionFactory, new SessionHolder(session));

						if (log.isDebugEnabled())
						{
							log.debug("MessagePointRunnable for schema = " + schema + " is processing.");
						}
						performMainProcessing();
					}
				}
				catch (RuntimeException ex)
				{
					if (session != null)
						log.error("MessagePointRunnable RuntimeException caught using session/transaction for session " + session.hashCode() +  " and DB schema = " + schema, ex);
					else
						log.error("MessagePointRunnable RuntimeException caught using session/transaction for session NULL and DB schema = " + schema, ex);
				}
				finally
				{
					if (!wait && TransactionSynchronizationManager.hasResource(sessionFactory)) 
					{
						// unbind and close the session
						SessionHolder sessionHolder = (SessionHolder) TransactionSynchronizationManager.unbindResource(sessionFactory);
						SessionFactoryUtils.closeSession(sessionHolder.getSession());

						if (log.isDebugEnabled())
						{
							log.debug("MessagePointRunnable for schema = " + schema + " finished processing.");
						}
					}

				}
			}
		}
		catch( Throwable t )
		{
			log.error("Exception caught in MessagePointRunnable " + this.getClass().getSimpleName(), t);
		}
        finally {
            ApplicationUtil.clearDebugModeInMessagePointRunnable();
        }

			finished = true;
	}

	public boolean isFinished() {
		return finished;
	}
}
