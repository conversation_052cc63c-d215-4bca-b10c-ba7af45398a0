defaultRegex=^[A-Za-z0-9\\s_\\-,'`.%]*+$
rd_url=^[A-Za-z\\s0-9\\p{L}\\p{Sc}\\'_\\-\\@\\\#\\$\\*\\.\\+\\\=\\?\\\:\\;\\[\\]\\%\\~\!\\&/\\(\\)\\s]*+$
activate=^[A-Za-z]*+$
action=^[A-Za-z\\s0-9_\\-]*+$
addNewRule=^[0-9]*+$
addSampleCheckFirst=^[A-Za-z]*+$
audience=^[A-Za-z\\s0-9\\p{L}\\p{Sc}\\'_\\-\\@\\\#\\$\\*\\.\\+\\\=\\?\\\:\\;\\[\\]\\%\\~\!\\&/\\(\\)\\s]*+$
email=^[A-Za-z0-9\\s_\\-\\'\\*\!\\.$\\+\=\\?,\:;@\#\\\\/\\[\\]\\^]*+
all=^[A-Za-z]*+$
appid=^[0-9\\-]*+$
appId=^[0-9\\-]*+$
cacheStamp=^[0-9\\-]*+$
cancelView=^[A-Za-z0-9_\\-\\.]*+$
class=^[A-Za-z0-9\\.]*+$
compareVariables=^[A-Za-z0-9\\,\\-]*+$
constId=^[0-9\\-]*+$
content=.*
contentId=^[0-9\\-]*+$
customerid=^[A-Za-z\\s0-9\\-<>()%\:\\._]*+$
dataFileId=^[0-9\\-]*+$
datagroupid=^[0-9\\-]*+$
dataImportId=^[0-9\\-]*+$
datasourceid=^[0-9\\-]*+$
dataKey=^[A-Za-z\\s0-9\\-\:\\._\!]*+$
dataValue=.*
date=^[A-Za-z0-9\\s_\\-\:]*+$
debug_data=^[A-Za-z0-9\\:\\s_\\-\\.\\'\\,\\[\\]]*+$
deid=^[0-9\\-]*+$
delivered=^[A-Za-z]*+$
dim=^[0-9\\.\:]*+$
dontOverrideObjects=^[0-9\\,]*+$
drid=^[0-9\\-]*+$
xdtid=^[0-9\\-]*+$
jddid=^[0-9\\-]*+$
dpeid=^[0-9\\-]*+$
dsid=^[0-9\\-]*+$
edit=^[0-9\\-]*+$
editDocument=^[0-9\\-]*+$
editFont=^[0-9\\-]*+$
editLanguage=^[0-9\\-]*+$
editStyle=^[0-9\\-]*+$
elementid=^[0-9]*+$
errorcode=^[A-Za-z0-9\\s_\\-\\.\\'\\,\\[\\]]*+$
file=.*
folder=.*
folderid=^[0-9\\-]*+$
fullValue=^[A-Za-z\\s0-9\\p{L}\\p{Sc}\\'_\\-\\@\\\#\\$\\*\\.\\+\\\=\\?\\\:\\;\\[\\]\\%\\~\!]*+$
groupid=^[0-9]*+$
helpid=^[A-Za-z0-9\\s_\\-']*+$
id=^[0-9\\-]*+$
image=^[A-Za-z0-9\\s_\\-\:./\\\\]*+$
insertSchedIds=^[0-9\\,]*+$
itemid=^[0-9\\-]*+$
jobid=^[0-9\\-]*+$
jsessionid=^[A-Za-z0-9\\s_\\.\\-']*+$
languageForSyncIds=^[0-9\\,]*+$
languagesForSync=^[0-9\\,]*+$
languagesSelected=^[0-9\\,\\-]*+$
langCode=^[A-Za-z]*+$
left=^[A-Za-z0-9_\\-\\.]*+$
localeSettingsId=^[0-9\\-]*+$
localeId=^[0-9\\-]*+$
maxpages=^[0-9]*+$
message=^[A-Za-z0-9\\s_\\-\\.\\'\\,\\[\\]]*+$
contentObjectId=^[A-Za-z0-9_\\,\\-]*+$
dataType=^[A-Za-z0-9\\-_]*+
messageId=^[0-9\\-]*+$
msgid=^[0-9\\-]*+$
msgkey=^[A-Za-z0-9.]*+$
name=^[A-Za-z0-9\\p{L}\\p{Sc}\\s_\\-'\:]*+$
nameSearchStr=^[A-Za-z0-9\\p{L}s_\\\\\\-'\\s]*+
nameStr=^[A-Za-z0-9\\p{L}s_\\\\\\-'\\s]*+
new=^[A-Za-z]*+$
newPartAttr=^[A-Za-z0-9_\\-\\.\\\:\\;]*+$
newZoneAttr=^[A-Za-z0-9_\\-\\.\\\:\\;]*+$
nodeId=^[0-9\\-]*+$
numberOfTenants=^[0-9\\-]*+$
objectId=^[A-Za-z0-9_\\,\\-]*+$
output=^[A-Za-z0-9\\s_\\-']*+$
page=^[0-9]*+$
pageMode=^[0-9\\-]*+$
pagesize=^[0-9]*+$
parameter=^[A-Za-z0-9.]*+$
paramgrpid=^[0-9\\-]*+$
paramInstId=^[0-9\\-]*+$
paramid=^[0-9\\-]*+$
qualified=^[A-Za-z]*+$
query=((.|\n)*)
recip_id=.*
refData=.*
removedParts=^[A-Za-z0-9_\\-\\.\\\:\\;]*+$
returnPage=^[A-Za-z0-9\\s_%&\=\\?\\-\\\:\\./\\\\]*+$
return_url=^[A-Za-z\\s0-9\\p{L}\\p{Sc}\\'_\\-\\@\\\#\\$\\*\\.\\+\\\=\\?\\\:\\;\\[\\]\\%\\~\!\\&/\\(\\)\\s]*+$
role=^[0-9\\-]*+$
scenarioid=^[0-9\\-]*+$
sColumns=.*
searchName=^[A-Za-z0-9\\p{L}\\p{Sc}\\s_\\-]*+$
selectedIds=^[A-Za-z0-9_\\,\\-]*+$
sSearch=.*
sSearch_0=.*
sSearch_1=.*
sSearch_2=.*
sSearch_3=.*
sSearch_4=.*
sSearch_5=.*
sSearch_6=.*
sSearch_7=.*
sSearch_8=.*
sSearch_9=.*
sSearch_10=.*
submittype=^[A-Za-z0-9\\s_\\-']*+$
successParameter=^[A-Za-z]*+$
testid=^[0-9\\-]*+$
touchpointid=^[0-9\\-]*+$
touchpointSelectionId=^[0-9\\,\\-]*+$
tpid=^[0-9\\-]*+$
type=^[A-Za-z0-9_\\-]*+$
url=^[A-Za-z\\s0-9\\p{L}\\p{Sc}\\'_\\-\\@\\\#\\$\\*\\.\\+\\\=\\?\\\:\\;\\[\\]\\%\\~\!\\&/\\(\\)\\s]*+$
user=^[0-9]*+$
userid=^[0-9\\-]*+$
v=^[A-Za-z0-9.]*+$
values_a=((.|\n)*)
values_b=((.|\n)*)
variableId=^[0-9]*+$
viewid=^[0-9\\-]*+$
windowType=^[A-Za-z0-9\\s_\\-]*+$
workflowid=^[0-9\\-]*+$
wgid=^[0-9\\-]*+$
zoneid=^[0-9\\-]*+$
zoneId=^[0-9\\,\\-]*+$
idp=*
errormsgkey=^[A-Za-z0-9.]*+$
parentid=^[0-9\\-]*+$
auditingDateFrom=^[A-Za-z0-9\\s_\\,']*+$
auditingDateTo=^[A-Za-z0-9\\s_\\,']*+$
dateSelect=^[A-Za-z0-9\\s_\\,']*+$
targetPage=^[A-Za-z_]+\\.form$
resource=(.|\\s)*\\S(.|\\s)*
sortDirection=\\b(asc)\\b|\\b(desc)\\b
sortKey=[a-zA-Z0-9]*
statusFilterId=^[0-9\\-]*+$
redirect_uri=.*
client_id=.*
openid_auth=(.|\\s)*\\S(.|\\s)*
state=.*