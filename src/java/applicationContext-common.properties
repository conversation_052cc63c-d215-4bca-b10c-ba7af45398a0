db.messagepoint.hibernate.dialect=com.prinova.messagepoint.util.MessagepointPostgresDialect
db.messagepoint.driver=org.postgresql.Driver
db.messagepoint.url=****************************************************
db.messagepoint.user=master_pod_master
db.messagepoint.password=prinova
db.messagepoint.xa.datasource=org.postgresql.xa.PGXADataSource
db.messagepoint.datasourcename=java:/messagepoint_TX_DS
session.messagepoint.redis.namespace=trunk
session.messagepoint.redis.host=127.0.0.1
session.messagepoint.redis.port=6379
session.messagepoint.redis.serverless=false
# Serverless Redis specific configuration (in milliseconds)
session.messagepoint.redis.serverless.meta_command_timeout=1000
session.messagepoint.redis.serverless.default_command_timeout=250
# must be LOWER than default_command_timeout
session.messagepoint.redis.serverless.connect_timeout=100
