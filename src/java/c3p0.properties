#
# Descriptions and default values can be found here: http://www.mchange.com/projects/c3p0/index.html#configuration_properties
#

# This file is for advanced c3p0 configuration, beyond what is already included inside the Hibernate config file.
# However since we are using multi tenancy, we need to specify all these parameters here
#

# <prop key="hibernate.c3p0.acquire_increment">2</prop> <!-- How many connections to acquire when allocating more for the pool -->
c3p0.acquireIncrement=2

# <prop key="hibernate.c3po.idle_test_period">200</prop> <!-- If this is a number greater than 0, c3p0 will test all idle, pooled but unchecked-out connections, every this number of seconds. -->
c3p0.idleConnectionTestPeriod=200

# <prop key="hibernate.c3p0.timeout">300</prop> <!-- The seconds a Connection can remain pooled but unused before being discarded. Zero means idle connections never expire. -->
c3p0.maxIdleTime=300

# <prop key="hibernate.c3po.max_size">50</prop> <!-- The maximum number of connections to keep in the pool. -->
c3p0.maxPoolSize=2500

# <prop key="hibernate.c3po.min_size">10</prop> <!-- The minimum number of connections to keep in the pool. -->
c3p0.minPoolSize=2

c3p0.initialPoolSize=2

# <prop key="hibernate.c3po.max_statements">0</prop> <!-- The size of c3p0's PreparedStatement cache. Zero means statement caching is turned off. -->
# http://www.mchange.com/projects/c3p0/#configuring_statement_pooling
c3p0.maxStatements=0

c3p0.statementCacheNumDeferredCloseThreads=1

# The query to run when testing whether a connection is still valid
# c3p0.preferredTestQuery=SELECT 1

# This will allow the pool to shrink when things are not very busy
c3p0.maxIdleTimeExcessConnections=100

# Test each connection for validness when it is returned to the pool
c3p0.testConnectionOnCheckin=false

# This is a very expensive operation when set to true!
# <prop key="hibernate.c3po.validate">false</prop> <!-- VERY expensive operation, so leave this set to false! -->
c3p0.testConnectionOnCheckout=false

# http://www.mchange.com/projects/c3p0/#numHelperThreads
c3p0.numHelperThreads=10
