//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON><PERSON> decompiler)
//

package org.springframework.web.servlet.mvc;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.PropertyEditorRegistrar;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingErrorProcessor;
import org.springframework.validation.MessageCodesResolver;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.support.WebBindingInitializer;
import org.springframework.web.context.request.ServletWebRequest;

/** @deprecated */
@Deprecated
public abstract class BaseCommandController extends AbstractController {
    public static final String DEFAULT_COMMAND_NAME = "command";
    private String commandName = "command";
    private Class commandClass;
    private Validator[] validators;
    private boolean validateOnBinding = true;
    private MessageCodesResolver messageCodesResolver;
    private BindingErrorProcessor bindingErrorProcessor;
    private PropertyEditorRegistrar[] propertyEditorRegistrars;
    private WebBindingInitializer webBindingInitializer;

    public BaseCommandController() {
    }

    public final void setCommandName(String commandName) {
        this.commandName = commandName;
    }

    public final String getCommandName() {
        return this.commandName;
    }

    public final void setCommandClass(Class commandClass) {
        this.commandClass = commandClass;
    }

    public final Class getCommandClass() {
        return this.commandClass;
    }

    public final void setValidator(Validator validator) {
        this.validators = new Validator[]{validator};
    }

    public final Validator getValidator() {
        return this.validators != null && this.validators.length > 0 ? this.validators[0] : null;
    }

    public final void setValidators(Validator[] validators) {
        this.validators = validators;
    }

    public final Validator[] getValidators() {
        return this.validators;
    }

    public final void setValidateOnBinding(boolean validateOnBinding) {
        this.validateOnBinding = validateOnBinding;
    }

    public final boolean isValidateOnBinding() {
        return this.validateOnBinding;
    }

    public final void setMessageCodesResolver(MessageCodesResolver messageCodesResolver) {
        this.messageCodesResolver = messageCodesResolver;
    }

    public final MessageCodesResolver getMessageCodesResolver() {
        return this.messageCodesResolver;
    }

    public final void setBindingErrorProcessor(BindingErrorProcessor bindingErrorProcessor) {
        this.bindingErrorProcessor = bindingErrorProcessor;
    }

    public final BindingErrorProcessor getBindingErrorProcessor() {
        return this.bindingErrorProcessor;
    }

    public final void setPropertyEditorRegistrar(PropertyEditorRegistrar propertyEditorRegistrar) {
        this.propertyEditorRegistrars = new PropertyEditorRegistrar[]{propertyEditorRegistrar};
    }

    public final void setPropertyEditorRegistrars(PropertyEditorRegistrar[] propertyEditorRegistrars) {
        this.propertyEditorRegistrars = propertyEditorRegistrars;
    }

    public final PropertyEditorRegistrar[] getPropertyEditorRegistrars() {
        return this.propertyEditorRegistrars;
    }

    public final void setWebBindingInitializer(WebBindingInitializer webBindingInitializer) {
        this.webBindingInitializer = webBindingInitializer;
    }

    public final WebBindingInitializer getWebBindingInitializer() {
        return this.webBindingInitializer;
    }

    protected void initApplicationContext() {
        if (this.validators != null) {
            for(int i = 0; i < this.validators.length; ++i) {
                if (this.commandClass != null && !this.validators[i].supports(this.commandClass)) {
                    throw new IllegalArgumentException("Validator [" + this.validators[i] + "] does not support command class [" + this.commandClass.getName() + "]");
                }
            }
        }

    }

    protected Object getCommand(HttpServletRequest request) throws Exception {
        return this.createCommand();
    }

    protected final Object createCommand() throws Exception {
        if (this.commandClass == null) {
            throw new IllegalStateException("Cannot create command without commandClass being set - either set commandClass or (in a form controller) override formBackingObject");
        } else {
            if (this.logger.isDebugEnabled()) {
                this.logger.debug("Creating new command of class [" + this.commandClass.getName() + "]");
            }

            return BeanUtils.instantiateClass(this.commandClass);
        }
    }

    protected final boolean checkCommand(Object command) {
        return this.commandClass == null || this.commandClass.isInstance(command);
    }

    protected final ServletRequestDataBinder bindAndValidate(HttpServletRequest request, Object command) throws Exception {
        ServletRequestDataBinder binder = this.createBinder(request, command);
        BindException errors = new BindException(binder.getBindingResult());
        if (!this.suppressBinding(request)) {
            binder.bind(request);
            this.onBind(request, command, errors);
            if (this.validators != null && this.isValidateOnBinding() && !this.suppressValidation(request, command, errors)) {
                for(int i = 0; i < this.validators.length; ++i) {
                    ValidationUtils.invokeValidator(this.validators[i], command, errors);
                }
            }

            this.onBindAndValidate(request, command, errors);
        }

        return binder;
    }

    protected boolean suppressBinding(HttpServletRequest request) {
        return false;
    }

    protected ServletRequestDataBinder createBinder(HttpServletRequest request, Object command) throws Exception {
        ServletRequestDataBinder binder = new ServletRequestDataBinder(command, this.getCommandName());
        this.prepareBinder(binder);
        this.initBinder(request, binder);
        return binder;
    }

    protected final void prepareBinder(ServletRequestDataBinder binder) {
        if (this.useDirectFieldAccess()) {
            binder.initDirectFieldAccess();
        }

        if (this.messageCodesResolver != null) {
            binder.setMessageCodesResolver(this.messageCodesResolver);
        }

        if (this.bindingErrorProcessor != null) {
            binder.setBindingErrorProcessor(this.bindingErrorProcessor);
        }

        if (this.propertyEditorRegistrars != null) {
            for(int i = 0; i < this.propertyEditorRegistrars.length; ++i) {
                this.propertyEditorRegistrars[i].registerCustomEditors(binder);
            }
        }

    }

    protected boolean useDirectFieldAccess() {
        return false;
    }

    protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) throws Exception {
        if (this.webBindingInitializer != null) {
            this.webBindingInitializer.initBinder(binder, new ServletWebRequest(request));
        }

    }

    protected void onBind(HttpServletRequest request, Object command, BindException errors) throws Exception {
        this.onBind(request, command);
    }

    protected void onBind(HttpServletRequest request, Object command) throws Exception {
    }

    protected boolean suppressValidation(HttpServletRequest request, Object command, BindException errors) {
        return this.suppressValidation(request, command);
    }

    protected boolean suppressValidation(HttpServletRequest request, Object command) {
        return this.suppressValidation(request);
    }

    /** @deprecated */
    @Deprecated
    protected boolean suppressValidation(HttpServletRequest request) {
        return false;
    }

    protected void onBindAndValidate(HttpServletRequest request, Object command, BindException errors) throws Exception {
    }
}
