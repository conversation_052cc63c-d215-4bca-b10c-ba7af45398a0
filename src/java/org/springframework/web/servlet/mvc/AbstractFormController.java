//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package org.springframework.web.servlet.mvc;

import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.HttpSessionRequiredException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;

/** @deprecated */
@Deprecated
public abstract class AbstractFormController extends BaseCommandController {
    private boolean bindOnNewForm = false;
    private boolean sessionForm = false;

    public AbstractFormController() {
        this.setCacheSeconds(0);
    }

    public final void setBindOnNewForm(boolean bindOnNewForm) {
        this.bindOnNewForm = bindOnNewForm;
    }

    public final boolean isBindOnNewForm() {
        return this.bindOnNewForm;
    }

    public final void setSessionForm(boolean sessionForm) {
        this.sessionForm = sessionForm;
    }

    public final boolean isSessionForm() {
        return this.sessionForm;
    }

    protected ModelAndView handleRequestInternal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (this.isFormSubmission(request)) {
            try {
                Object command = this.getCommand(request);
                ServletRequestDataBinder binder = this.bindAndValidate(request, command);
                BindException errors = new BindException(binder.getBindingResult());
                return this.processFormSubmission(request, response, command, errors);
            } catch (HttpSessionRequiredException var6) {
                if (this.logger.isDebugEnabled()) {
                    this.logger.debug("Invalid submit detected: " + var6.getMessage());
                }

                return this.handleInvalidSubmit(request, response);
            }
        } else {
            return this.showNewForm(request, response);
        }
    }

    protected boolean isFormSubmission(HttpServletRequest request) {
        return "POST".equals(request.getMethod());
    }

    protected String getFormSessionAttributeName(HttpServletRequest request) {
        return this.getFormSessionAttributeName();
    }

    protected String getFormSessionAttributeName() {
        return this.getClass().getName() + ".FORM." + this.getCommandName();
    }

    protected final ModelAndView showNewForm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        this.logger.debug("Displaying new form");
        return this.showForm(request, response, this.getErrorsForNewForm(request));
    }

    protected final BindException getErrorsForNewForm(HttpServletRequest request) throws Exception {
        Object command = this.formBackingObject(request);
        if (command == null) {
            throw new ServletException("Form object returned by formBackingObject() must not be null");
        } else if (!this.checkCommand(command)) {
            throw new ServletException("Form object returned by formBackingObject() must match commandClass");
        } else {
            ServletRequestDataBinder binder = this.createBinder(request, command);
            BindException errors = new BindException(binder.getBindingResult());
            if (this.isBindOnNewForm()) {
                this.logger.debug("Binding to new form");
                binder.bind(request);
                this.onBindOnNewForm(request, command, errors);
            }

            return errors;
        }
    }

    protected void onBindOnNewForm(HttpServletRequest request, Object command, BindException errors) throws Exception {
        this.onBindOnNewForm(request, command);
    }

    protected void onBindOnNewForm(HttpServletRequest request, Object command) throws Exception {
    }

    protected final Object getCommand(HttpServletRequest request) throws Exception {
        if (!this.isSessionForm()) {
            return this.formBackingObject(request);
        } else {
            HttpSession session = request.getSession(false);
            if (session == null) {
                throw new HttpSessionRequiredException("Must have session when trying to bind (in session-form mode)");
            } else {
                String formAttrName = this.getFormSessionAttributeName(request);
                Object sessionFormObject = session.getAttribute(formAttrName);
                if (sessionFormObject == null) {
                    throw new HttpSessionRequiredException("Form object not found in session (in session-form mode)");
                } else {
                    if (this.logger.isDebugEnabled()) {
                        this.logger.debug("Removing form session attribute [" + formAttrName + "]");
                    }

                    session.removeAttribute(formAttrName);
                    return this.currentFormObject(request, sessionFormObject);
                }
            }
        }
    }

    protected Object formBackingObject(HttpServletRequest request) throws Exception {
        return this.createCommand();
    }

    protected Object currentFormObject(HttpServletRequest request, Object sessionFormObject) throws Exception {
        return sessionFormObject;
    }

    protected abstract ModelAndView showForm(HttpServletRequest var1, HttpServletResponse var2, BindException var3) throws Exception;

    protected final ModelAndView showForm(HttpServletRequest request, BindException errors, String viewName) throws Exception {
        return this.showForm(request, errors, viewName, (Map)null);
    }

    protected final ModelAndView showForm(HttpServletRequest request, BindException errors, String viewName, Map controlModel) throws Exception {
        if (this.isSessionForm()) {
            String formAttrName = this.getFormSessionAttributeName(request);
            if (this.logger.isDebugEnabled()) {
                this.logger.debug("Setting form session attribute [" + formAttrName + "] to: " + errors.getTarget());
            }

            request.getSession().setAttribute(formAttrName, errors.getTarget());
        }

        Map model = errors.getModel();
        Map referenceData = this.referenceData(request, errors.getTarget(), errors);
        if (referenceData != null) {
            model.putAll(referenceData);
        }

        if (controlModel != null) {
            model.putAll(controlModel);
        }

        return new ModelAndView(viewName, model);
    }

    protected Map referenceData(HttpServletRequest request, Object command, Errors errors) throws Exception {
        return null;
    }

    protected abstract ModelAndView processFormSubmission(HttpServletRequest var1, HttpServletResponse var2, Object var3, BindException var4) throws Exception;

    protected ModelAndView handleInvalidSubmit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Object command = this.formBackingObject(request);
        ServletRequestDataBinder binder = this.bindAndValidate(request, command);
        BindException errors = new BindException(binder.getBindingResult());
        return this.processFormSubmission(request, response, command, errors);
    }
}
