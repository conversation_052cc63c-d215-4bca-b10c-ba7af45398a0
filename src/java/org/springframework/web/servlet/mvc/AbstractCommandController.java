//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package org.springframework.web.servlet.mvc;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.BindException;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.servlet.ModelAndView;

/** @deprecated */
@Deprecated
public abstract class AbstractCommandController extends BaseCommandController {
    public AbstractCommandController() {
    }

    public AbstractCommandController(Class commandClass) {
        this.setCommandClass(commandClass);
    }

    public AbstractCommandController(Class commandClass, String commandName) {
        this.setCommandClass(commandClass);
        this.setCommandName(commandName);
    }

    protected ModelAndView handleRequestInternal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Object command = this.getCommand(request);
        ServletRequestDataBinder binder = this.bindAndValidate(request, command);
        BindException errors = new BindException(binder.getBindingResult());
        return this.handle(request, response, command, errors);
    }

    protected abstract ModelAndView handle(HttpServletRequest var1, HttpServletResponse var2, Object var3, BindException var4) throws Exception;
}
