package tools.prinova.messagepoint.application;

import java.awt.BorderLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.io.File;
import java.util.Iterator;
import java.util.LinkedList;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFileChooser;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JPopupMenu;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.filechooser.FileFilter;

import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

//User Interface for MessageReplace to help replace hard code strings with variables
public class MessageReplaceUI {

	private static final Log log = LogUtil.getLog(MessageReplaceUI.class);

	private int width = 1024, height = 768; 
	
	private int startOfSelection, endOfSelection;

	//text for labels and other widgets
	private String title = "Properties Replacement Application";
	private String ignoreBtnLabel = "Ignore";
	private String replace1BtnLabel = "Replace With Key";
	private String replace2BtnLabel = "Replace With Messagepoint Formatting";
	private String replace3BtnLabel = "Replace With Spring Formatting";
	private String customReplaceLabel = "Custom Replace";
//	private String keyLabelText = "Key: ";
//	private String messageLabelText = "Message: ";
	private String customPrefixLabelText = "Prefix: ";
	private String customSuffixLabelText = "Suffix: ";
	private String equalsSign = " = ";
	private String duplicateMessageAlertText = "Some keys already exist for this message: ";
	private String 	duplicateKeyString = "That key is already in use!",
					blankKeyString = "Please enter a key!",
					blank = "",
					invalidKeyText = "Key entered is Invalid!";
	
	//boolean for tracking the state of the current value for a key. true = key is already used, false = key is new and unique
	private Boolean keyDuplicate = true;
	
	//the actual frame we will put components on
	public JFrame window = new JFrame(title);
	
	//scrollable frame for displaying code
	private JTextArea codeView = new JTextArea();
	private JScrollPane scrollingCodeView = new JScrollPane(codeView);
	private int tabSize = 3;
	
	//ui buttons for different replacing options
	private JButton ignoreBtn = new JButton(ignoreBtnLabel);
	private JButton replace1Btn = new JButton(replace1BtnLabel);
	private JButton replace2Btn = new JButton(replace2BtnLabel);
	private JButton replace3Btn = new JButton(replace3BtnLabel);
	private JButton replaceCustomBtn = new JButton(customReplaceLabel);
	
	private JComboBox duplicateMessageCB = new JComboBox();
	private LinkedList<String> duplicates = new LinkedList<>();
	
	//text fields and labels pertaining to them
	private JTextField message = new JTextField();
	private JTextField key = new JTextField();
//	private JLabel keyLabel = new JLabel (keyLabelText);
//	private JLabel messageLabel = new JLabel( messageLabelText);
	private JLabel keyInfoLabel = new JLabel(blank);
	private JLabel fileName = new JLabel(blank);
	private JLabel duplicateMessageAlert = new JLabel(blank);
	
	//menubar for changing which property file we add to
	private JMenuBar jmb = new JMenuBar();;
	private JMenu fileMenu = new JMenu("File");
	private JMenuItem changeProperty = new JMenuItem("Change Property File");
	private JMenuItem exit = new JMenuItem("Exit");
	
	
	//contructor, adds all the declared widgets to the frame via BorderLayout and several panels
	MessageReplaceUI() {
		window.getContentPane().setLayout(new BorderLayout());
		window.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
		
		fileMenu.add(changeProperty);
		fileMenu.add(exit);
		jmb.add(fileMenu);
		window.setJMenuBar(jmb);
		
		//buttons to do the different kinds of replacements
		JPanel buttonPane = new JPanel();
		buttonPane.add(replace1Btn);
		buttonPane.add(replace2Btn);
		buttonPane.add(replace3Btn);
		buttonPane.add(replaceCustomBtn);
		buttonPane.add(ignoreBtn);
		
		addListeners();
		
		//text fields for the key and message of property
		JPanel textFieldPane = new JPanel();
		textFieldPane.add(key);
		textFieldPane.add(new JLabel(equalsSign));
		textFieldPane.add(message);
		
		int textFieldWidth = 30;
		key.setColumns(textFieldWidth);
		message.setColumns(textFieldWidth);
		
		JPanel keyInfoLabelPane = new JPanel();
		keyInfoLabelPane.add(keyInfoLabel);
		keyInfoLabelPane.add(duplicateMessageAlert);
		keyInfoLabelPane.add(duplicateMessageCB);
		duplicateMessageCB.setVisible(false);
		
		//combine buttons and text fields
		JPanel fieldPane = new JPanel(new BorderLayout());
		fieldPane.add(buttonPane, BorderLayout.SOUTH);
		fieldPane.add(keyInfoLabelPane, BorderLayout.CENTER);
		fieldPane.add(textFieldPane, BorderLayout.NORTH);
		
		
		//setup the code view pane
		codeView.setEditable(false);
		codeView.setTabSize(tabSize);
		//codeView.setPreferredSize(new Dimension(600, 300));
		
		//pane between buttons and text fields
		
		//finally add all the panes and widgets to the frame
		window.add(scrollingCodeView, BorderLayout.CENTER);
		window.add(fieldPane, BorderLayout.SOUTH);
		window.add(fileName,BorderLayout.NORTH);
		window.setResizable(false);
		
		//show the actual window
		window.pack();
		window.setSize(width, height);
		window.setVisible(true);
		
		changeButtons();
		
		//force the user to choose a file which will be the file we add property records to
		int returnVal;
		do {
			returnVal = choosePropertyFile();
		} while(returnVal!=JFileChooser.APPROVE_OPTION);
	}
	
	//custom file filter which accepts only .properties files, these are the only files we should be choosing to add to
	public static class PropertyFileFilter extends FileFilter {

		String description = "Property Files";
		
		public boolean accept(File f) {
			if (f.isDirectory()) {
				return true;
			}
			
			//gets the extension of the file, substring of the last occurence of a period and onwards
			int index = f.getPath().lastIndexOf("."); 
			String ext = null;
			if (index > 0 && index < f.getPath().length()-1){
				ext = f.getPath().substring(index+1).toLowerCase();
			}
			
			if (ext!= null){
				if (ext.equals("properties")){
					return true;
				}
			}
			
			return false;
		}
		
		//required method
		public String getDescription(){
			return description;
		}
		
	}
	
	//function that handles creating and showing the file tree using a JFileChooser for selection of a property file
	private int choosePropertyFile(){
		JFileChooser chooser;
		try {
			chooser = new JFileChooser(System.getProperty("user.dir")+"\\src\\web\\WEB-INF\\i18n");
		} catch (Exception e) {
			log.error("Error: ", e);
			chooser = new JFileChooser(System.getProperty("user.dir"));
		}
		
		PropertyFileFilter filter = new PropertyFileFilter();
		chooser.setFileFilter(filter);
		
		//might need a filter for .properties files only?
		int returnVal = chooser.showOpenDialog(window);
		
		if (returnVal == JFileChooser.APPROVE_OPTION){
			I18NHardcodingSearchTool.setPropertyFile(chooser.getSelectedFile());
		}
		
		return returnVal;
	}
	
	//add action listeners for the buttons that fire when they are pressed
	//also adds change listeners which fire everytime something new is done to the text fields
	private void addListeners(){
		
		//textarea listener pop up menu on right click, cheap way to get highlighting back
		codeView.addMouseListener(new MouseListener() {

			public void mouseClicked(MouseEvent arg0) {
				// TODO Auto-generated method stub
				int button = arg0.getButton();
				if (button == MouseEvent.BUTTON3){
					JPopupMenu popUp = new JPopupMenu("Show Replace Text");
					JMenuItem jmi = new JMenuItem("Show Replace Text");
					jmi.addActionListener(new ActionListener() {

						public void actionPerformed(ActionEvent arg0) {
							// TODO Auto-generated method stub
							showHighlight();
						}
						
					});
					popUp.add(jmi);
					
					popUp.show(codeView, arg0.getX(), arg0.getY());
				}
			}

			public void mouseEntered(MouseEvent arg0) {
				// TODO Auto-generated method stub
				
			}

			public void mouseExited(MouseEvent arg0) {
				// TODO Auto-generated method stub
				
			}

			public void mousePressed(MouseEvent arg0) {
				// TODO Auto-generated method stub
				
			}

			public void mouseReleased(MouseEvent arg0) {
				// TODO Auto-generated method stub
				
			}
			
		});
		
		//listeners for the file menu items
		exit.addActionListener(new ActionListener() {

			public void actionPerformed(ActionEvent arg0) {
				window.dispose();
			}
			
		});
		
		changeProperty.addActionListener(new ActionListener() {

			public void actionPerformed(ActionEvent e) {
				choosePropertyFile();
			}
			
		});
		
		//action listeners on the buttons, for making replacements
		ignoreBtn.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e){
				I18NHardcodingSearchTool.replaceType(1, null, null);
			}
		});
		replace1Btn.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e){
				I18NHardcodingSearchTool.replaceType(2, null, null);
			}
		});
		replace2Btn.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e){
				I18NHardcodingSearchTool.replaceType(3, null, null);
			}
		});
		replace3Btn.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e){
				I18NHardcodingSearchTool.replaceType(4, null, null);
			}
		});
		
		//custom prefix and suffix, makes a dialog box to prompt for user input
		replaceCustomBtn.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				JPanel customInputPanel = new JPanel(new BorderLayout());
				JPanel customInputPanelTop = new JPanel();
				JPanel customInputPanelBot = new JPanel();
				JTextField customPrefix = new JTextField();
				customPrefix.setColumns(20);
				JTextField customSuffix = new JTextField();
				customSuffix.setColumns(20);
				
				JLabel customPrefixLabel = new JLabel(customPrefixLabelText);
				JLabel customSuffixLabel = new JLabel(customSuffixLabelText);
				
				
				customInputPanelTop.add(customPrefixLabel);
				customInputPanelTop.add(customPrefix);
				customInputPanelBot.add(customSuffixLabel);
				customInputPanelBot.add(customSuffix);
				
				customInputPanel.add(customInputPanelTop, BorderLayout.NORTH);
				customInputPanel.add(customInputPanelBot, BorderLayout.SOUTH);
				
				
				int returnval = JOptionPane.showConfirmDialog(window, customInputPanel, "Enter Custom Formatting", JOptionPane.OK_CANCEL_OPTION);
				if (returnval == JOptionPane.OK_OPTION){					
					I18NHardcodingSearchTool.replaceType(5, customPrefix.getText().trim(), customSuffix.getText().trim());	
				}
				
			}
		});
		
		//change listeners for key and message textfields
		key.getDocument().addDocumentListener(new DocumentListener() {
			
			public void removeUpdate(DocumentEvent e) {
				checkKeys(e);
			}
			
			public void insertUpdate(DocumentEvent e) {
				checkKeys(e);
			}
			
			public void changedUpdate(DocumentEvent e) {
				checkKeys(e);
				
			}
			private void checkKeys(DocumentEvent e) {
				//key.setText(key.getText().trim());
				int b = I18NHardcodingSearchTool.checkKeys(key.getText().trim());
				if (b == 1) {
					String keyText = key.getText().trim();
					if (keyText.isEmpty()) {
						keyInfoLabel.setText(blankKeyString);
						
					//special case where we are using a key from the CB of ones already used for the current message
					} else if (duplicates.contains(keyText)) {
						keyInfoLabel.setText(blank);
						keyDuplicate = false;
						changeButtons();
						return;
						
					} else {
						keyInfoLabel.setText(duplicateKeyString);
					}
					keyDuplicate = true;
					
				} else if (b == 2) {
					keyInfoLabel.setText(invalidKeyText);
					keyDuplicate = true;
				} else {

					keyInfoLabel.setText(blank);
					keyDuplicate = false;
				}
				changeButtons();
			}
		});
		
		message.getDocument().addDocumentListener(new DocumentListener() {
			
			public void removeUpdate(DocumentEvent e) {
				checkMessages();
			}
			
			public void insertUpdate(DocumentEvent e) {
				checkMessages();
			}
			
			public void changedUpdate(DocumentEvent e) {
				checkMessages();
				
			}
			private void checkMessages() {
				//LinkedList<String> duplicates = new LinkedList<String>();
				Boolean b = I18NHardcodingSearchTool.checkMessages(message.getText().trim(), duplicates );
				duplicateMessageCB.removeAllItems();
				duplicateMessageCB.setVisible(false);
				duplicateMessageAlert.setText(blank);
				if (b) {
					//duplicateMessageDialog();
					//see notes, not really sure what to do here.
					Iterator<String> itr = duplicates.iterator();
					while(itr.hasNext()){
						duplicateMessageAlert.setText(duplicateMessageAlertText);
						duplicateMessageCB.addItem(itr.next());
						duplicateMessageCB.setVisible(true);
					}
				}
			}
		});
		
		duplicateMessageCB.addActionListener(new ActionListener() {

			public void actionPerformed(ActionEvent e) {
				key.setText((String)duplicateMessageCB.getSelectedItem());
			}
			
		});
		
	}
	
	//disable or enable buttons depending on the key we are trying to add's validity
	private void changeButtons() {
		if (keyDuplicate) {	//the key we're trying to use is already in prop files
			//ignoreBtn.setEnabled(false);
			replace1Btn.setEnabled(false);
			replace2Btn.setEnabled(false);
			replace3Btn.setEnabled(false);
			replaceCustomBtn.setEnabled(false);
		} else {
			ignoreBtn.setEnabled(true);
			replace1Btn.setEnabled(true);
			replace2Btn.setEnabled(true);
			replace3Btn.setEnabled(true);
			replaceCustomBtn.setEnabled(true);
		}
	}
	
	private void showHighlight(){
		codeView.setSelectionStart(startOfSelection-1);
		codeView.setSelectionEnd(endOfSelection+1);
		codeView.grabFocus();
	}
	
	//changes what is shown the code view, gather message from this
	public void setCodeView(String file, String fName, int startSelection, int endSelection) {
			startOfSelection = startSelection;
			endOfSelection = endSelection;
			codeView.setText(file);
			codeView.setSelectionStart(startSelection);
			codeView.setSelectionEnd(endSelection);
			message.setText(codeView.getSelectedText());
			//include " in the selection

			showHighlight();
			
			key.setText("");
			fileName.setText(fName);
			
	}
	
	//simple getter methods for property information (accessed by MessageReplace methods)
	public String getKey(){
		return key.getText();
	}
	
	public String getMessage(){
		return message.getText();
	}
	
	public String getProperty(){
		return (key.getText().trim() + "=" + message.getText().trim());
	}
	
}
