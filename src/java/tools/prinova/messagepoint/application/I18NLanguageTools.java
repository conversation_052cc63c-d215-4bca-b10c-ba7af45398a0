package tools.prinova.messagepoint.application;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.regex.Pattern;

import com.prinova.messagepoint.util.ApplicationLanguageUtils;
import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;

//methods and a simple main to facilitate the alphabetization of properties files 
//specifically messages.properties
//strips all comments and white space, leaves any commented props at top of file
//main take slist of properties to be sorted, or defaults to the 2 basic files(if run from messagepoint proj)
public class I18NLanguageTools {

	private static final Log log = LogUtil.getLog(I18NLanguageTools.class);

    static String noArgsMsg 					= "Error! No arguments given. Please specify file path.";
	static String propertyPattern 				= ".+=.*";
	static String baseFilePath 					= System.getProperty("user.dir") + File.separator + "src"; 	//"C:\\dev-tools\\workspace\\messagepoint\\src\\";
	static String encoding						= ApplicationLanguageUtils.FILE_OF_MESSAGES_ENCODING;
	static String[] appLanguages 				= {"en","fr","es","de","pt","zh","ja"};
	
	public static Pattern AZPattern = Pattern.compile("[A-Z]");
	public static Pattern azPattern = Pattern.compile("[a-z]");
	
	//easy toggle for putting changes into a different file or overwriting, true = overwrite original
	static Boolean writeChanges 				= true;	
	
	private static List<String> masterKeyList	= new ArrayList<>();
	
	//expects one line property string, returns the key substring (LHS of equals)
	public static String keyOf(String property) {
		property = property.trim();
		int endIndex = property.indexOf('=');
		String toReturn = property.substring(0, endIndex).trim();
		return toReturn;
	}
	
	//expects one line property string, returns the message substring (RHS of equals)
	public static String messageOf(String property) {
		property = property.trim();
		int beginIndex = property.indexOf('=');
		String toReturn = property.substring(beginIndex+1).trim();
		return toReturn;
	}
	
	//compares (trimmed) keys of 2 properties, true if same
	private static Boolean compareKeys(String property1, String property2) {
		String 	key1 = keyOf(property1),
				key2 = keyOf(property2);
		return key1.equals(key2);
	}
	
	//compares (trimmed) messages of two passed in properties, true if same
	private static Boolean compareMessages(String property1, String property2) {
		String 	message1 = messageOf(property1),
				message2 = messageOf(property2);
		return message1.equals(message2);
	}
	
	private static String concatKeyValue( String[] valueArray) {
		if ( valueArray.length < 2)
			return "";
		
		// Discard first array index value (represents key)
		StringBuilder concatString = new StringBuilder();
		for (int i=1; i < valueArray.length; i++) {
			concatString.append(valueArray[i].trim());
			if ( i < valueArray.length -1 )
				concatString.append("=");
		}
			
		return concatString.toString();
	}
	
	//take a file, alphabetize the properties and list the duplicate keys and messages
	public static int alphabetize(String file){
		try{
			
			File f = new File(file);

			File modifiedFile 	= new File(file+"-modified");
			File sortedFile 	= new File(file+"-sorted");
			File duplicatesFile = new File(file+"-duplicates");
		
			List<String> lineList 				= FileUtil.readLines(f, encoding);
			List<String> translatedKeyValues 	= new ArrayList<>();
			List<String> emptyKeyValues 		= new ArrayList<>();
			List<String> currentKeyList 		= new ArrayList<>();

			for (String currentKeyValue: lineList) {
				String[] currentKeyValuePair = currentKeyValue.trim().split("=");
				if ( currentKeyValuePair.length > 1  ) {
					currentKeyList.add(currentKeyValuePair[0].trim());
					if ( currentKeyValuePair[1] == null || currentKeyValuePair[1].trim().isEmpty() )
						emptyKeyValues.add(currentKeyValue);
					else
						translatedKeyValues.add(currentKeyValue);
				}
			}

			for (String currentMasterKey: masterKeyList) {
				if ( !currentKeyList.contains(currentMasterKey) )
					emptyKeyValues.add(currentMasterKey + "=");
			}
			Collections.sort(translatedKeyValues);
			Collections.sort(emptyKeyValues);

			emptyKeyValues.addAll(translatedKeyValues);
			lineList = emptyKeyValues;
			
			//only write this debug output if toggled
			if (!writeChanges) {
				FileUtil.writeLines(sortedFile, encoding, lineList);
			}
			
			//Compare for duplicates
			//lists to store the duplicate instances
			List<String> duplicateKeys = new LinkedList<>();
			duplicateKeys.add("List of Duplicate Keys: ");
			List<String> duplicateMessages = new LinkedList<>();
			duplicateMessages.add("\nList of Duplicate Messages: ");
			List<String> removedStrings = new LinkedList<>();
			removedStrings.add("\nList of removed records: ");
			
			//iterate through all the records
			ListIterator<String> itr = lineList.listIterator();
			String current = itr.next(), next;
			int countRemoves = 0;
			
			List<String> cleanList = new ArrayList<>();
			
			//iterate across all records
			while(itr.hasNext()){
				itr.set(current.trim());	//remove any trailing white space from lines
				next = itr.next();
				//has to match the pattern for a property
				if (current.matches(propertyPattern)) {
					
					Boolean keyDuplicate = compareKeys(current, next);
					Boolean messageDuplicate = compareMessages(current, next);
					
					if (keyDuplicate && messageDuplicate){	
						
						// DUPLICATE KEY AND MESSAGE: REMOVE
						System.err.println("DUPLICATE: " + current);
						// MARK AS REMOVED
						if (!current.trim().isEmpty())
							removedStrings.add(current);
						countRemoves += 1;
					
					//list full property if we have a duplicate
					} else if (keyDuplicate) {
						// MARK AS DUPLICATE KEY
						if ( !duplicateKeys.contains(current) )
							duplicateKeys.add(current);
						duplicateKeys.add(next);		//(keyOf(next));
						// ADD KEY/VALUE
						cleanList.add(current);
					} else if (messageDuplicate) {
						// MARK AS DUPLICATE MESSAGE
						if ( !duplicateMessages.contains(current) )
							duplicateMessages.add(current);
						duplicateMessages.add(next);	//(messageOf(next));
						// ADD KEY/VALUE
						cleanList.add(current);
					} else {
						// ADD KEY/VALUE
						cleanList.add(current);
					}

				//doesn't match pattern, remove (probably a comment)
				} else {
					
					// MARK AS REMOVED: only track non-whitespace
					if (!current.trim().isEmpty())
						removedStrings.add(current);
					countRemoves += 1;

				}
				// SET NEXT KEY/VALUE
				current = next;
			}//iterating
			// LAST KEY/VALUE
			cleanList.add(current);
			
			//add totals for the output file
			duplicateKeys.add("Number of duplicate keys: " + Integer.toString(duplicateKeys.size() - 1));
			duplicateMessages.add("Number of duplicate messages: " + Integer.toString(duplicateMessages.size() - 1));
			
			//append all the duplicate messages for writing
			duplicateKeys.addAll(duplicateMessages);
			duplicateKeys.addAll(removedStrings);
			duplicateKeys.add("Total lines removed (including whitespace lines): " + Integer.toString(countRemoves));
			
			//write the changes and duplicates
			if (writeChanges) {
				FileUtil.writeLines(f, encoding, cleanList);
			} else {
				FileUtil.writeLines(modifiedFile, encoding, cleanList);
			}
			FileUtil.writeLines(duplicatesFile, encoding, duplicateKeys);
			
		} catch (Exception e) {
			log.error("Error: ", e);
		}
		return 0;//success
	}

	//simple script to take all the property files from the command line args or the default 2 files
	//all files are expected to be property files and are alphabetized by key
	public static void main(String[] args) {
		if (args.length > 0){
			int[] errors = new int[args.length];
			for (int i = 0; i < args.length; i++) {
				errors[i] = alphabetize(args[i]);
			}
			for (int i =0; i <errors.length; i++) {
				if (errors[i] == 1) {
					System.err.println("ERROR - Could not find file: " + args[i]);
				}
			}
		} else {
			
			try{
				
				File masterLanguageFile = new File(baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages.properties");
				if ( !masterLanguageFile.exists() ) {
					System.err.println("ERROR - Master language file not found: " + baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages.properties");
					return;
				}
				
				List<String> keyValueList = FileUtil.readLines(masterLanguageFile, ApplicationLanguageUtils.FILE_OF_MESSAGES_ENCODING);
				List<String> xKeyValueList = new ArrayList<>();
				List<String> enPrimeKeyValueList = new ArrayList<>();
				for (String currentKeyValue : keyValueList) {
					String[] currentKeyValuePair = currentKeyValue.trim().split("=");
					if ( currentKeyValuePair.length > 1 && !currentKeyValuePair[0].isEmpty()) {
						masterKeyList.add( currentKeyValuePair[0].trim() );
						String trimmedCurrentKeyValuePair = concatKeyValue(currentKeyValuePair).trim();
						String anonCurrentKeyValuePair = AZPattern.matcher(trimmedCurrentKeyValuePair).replaceAll("X");
						anonCurrentKeyValuePair = azPattern.matcher(anonCurrentKeyValuePair).replaceAll("x");
						if ( currentKeyValuePair[0].indexOf("client_messages.calendar") == -1 ) {
							xKeyValueList.add( currentKeyValuePair[0].trim() + "=" + anonCurrentKeyValuePair );
							enPrimeKeyValueList.add( currentKeyValuePair[0].trim() + "=" + "x" + concatKeyValue(currentKeyValuePair) + "x" );
						} else {
							xKeyValueList.add( currentKeyValuePair[0].trim() + "=" + trimmedCurrentKeyValuePair );
							enPrimeKeyValueList.add( currentKeyValuePair[0].trim() + "=" + concatKeyValue(currentKeyValuePair) );
						}
					}
				}
				
				Collections.sort(xKeyValueList);
				Collections.sort(enPrimeKeyValueList);
				
				// GENERATE TEST LANGUAGE FILE: Language values are x'd out
				FileUtil.writeLines(new File(baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages_xx.properties"), encoding, xKeyValueList);
				// GENERATE TEST LANGUAGE FILE: English values are encapsulated with 'x' markers
				FileUtil.writeLines(new File(baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages_zz.properties"), encoding, enPrimeKeyValueList);
				
			} catch (Exception e) {
				log.error("Error: ", e);
			}
		
			for (String currentLangCode: appLanguages) {

				String messagePropertiesFileName = "messages.properties";
				if ( !currentLangCode.toLowerCase().equals("en") )
					messagePropertiesFileName = "messages_" + currentLangCode.toLowerCase() + ".properties";
			
				int error = alphabetize(baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + messagePropertiesFileName);
				if ( error > 0 )
					System.err.println("ERROR - Could not find file: " + baseFilePath + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + messagePropertiesFileName);
			
			}

		}
		
		System.out.println("Application Language Utils:  Processing complete");
	}

}
