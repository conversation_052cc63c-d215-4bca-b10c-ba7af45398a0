package tools.prinova.messagepoint.application;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;
import org.apache.commons.logging.Log;


//utility to check properties files against the code base
//will need files paths to properties to be check and top level code directory, can flag with -remove to auto remove keys
//leaves an output file in the top level code directory that was specified
//command line vars for main: [prop file#1] ... [prop file#n] [code dir] [-remove]
//alternately no file paths given will default to messages with entire project as code dir
public class I18NUnreferencedKeysTool {

	private static final Log log = LogUtil.getLog(I18NUnreferencedKeysTool.class);

	private List<String> keyList = null;
	public static String propertyPattern = "[^#].*=.*";
	private String[] files;
	private File codeDirectory;
	private String encoding = null;
	private String[] extensions = {"java", "jsp", "xml", ".properties", "html", "js"}; 
	private static String removeFlagString = "-remove";
	private static String baseFilePath = System.getProperty("user.dir") + "\\src";	//"C:\\dev-tools\\workspace\\messagepoint\\src\\";
	private static String[] defaultProperties = {baseFilePath + "\\web\\WEB-INF\\i18n\\messages.properties"};
	private static String defaultCodeDir = baseFilePath;
	private static String seperator = "-------------------------------------------";
	
	I18NUnreferencedKeysTool(String[] propertyFiles, File codeDir){
		files = propertyFiles;
		if (codeDir.isDirectory()) {
			codeDirectory = codeDir;
		} else {
			codeDirectory = codeDir.getParentFile();
		}
		indexKeys();
	}

	//finds all uncommented keys form property files and adds them to keyList
	//this will allow us to check against the keyList later to see what keys are used
	public void indexKeys(){
		keyList = new LinkedList<>();
		List<String> fileList = null;
		
		try{
			for (int i = 0; i < files.length; i++) {
				File f = new File(files[i]);
				
				if (f.exists()){ 	
					//init keyList if not already, otherwise add elements to it
					if (fileList == null){
						fileList = FileUtil.readLines(f, encoding);
					} else {
						fileList.addAll(FileUtil.readLines(f, encoding));
					}
				} else {
					System.err.println("ERROR - Failed to find file: " + f);
				}
			}
			
			Iterator<String> itr = fileList.iterator();
			while(itr.hasNext()){
				String property = itr.next();
				if (property.matches(propertyPattern)){
					keyList.add(I18NLanguageTools.keyOf(property));
				} else {
					System.out.println("Rejected: \'" + property+ "\'");
				}
			}
		} catch( Exception e ){
			log.error("Error: ", e);
		}
		
	}
	
	//meat and potatoes, accepts a file and then checks all keys in the list against it
	private void keyCheckFile(File f) {
		if(!f.isFile()){
			return;
		}
		//check to make sure we dont read property files that we are checking
		for (int i =0; i < files.length; i++){
			if (files[i].equals(f.toString())){
				return;
			}
		}
				
		String fileAsString = null;
		try {
			fileAsString = FileUtils.readFileToString(f, encoding);
			
		} catch (Exception e) {
			log.error("Error: ", e);
		}
		
		Iterator<String> keyItr = keyList.iterator();
		while (keyItr.hasNext()){
			String key = keyItr.next();
			if (fileAsString.indexOf(key) > -1 ){
				keyItr.remove();
			}
			
		}
	}
	
	//accepts a directory, iterates over all files in it and sub dirs calling keyCheckFile for each
	private void keyCheckHelper(File directory){

		Collection<File> allFiles = FileUtils.listFiles(directory, extensions, true);
		Iterator<File> fileItr = allFiles.iterator();
		while (fileItr.hasNext()){
			File f = fileItr.next();
			
			keyCheckFile(f);
		}
	}
	
	//should run through the code base and check the keyList against message references, reporting unused messages
	public int keyCheck(Boolean removeUnused){
		if (keyList == null){
			return 1; //error cannot perform check since we have no keys to check for
		}
		
		File[] firstLevel = codeDirectory.listFiles();
		List<File> arrayAsList = Arrays.asList(firstLevel);
		ArrayList<File> firstLevelList = new ArrayList<>(arrayAsList);
		File webDir = new File(codeDirectory, "web");
		if(firstLevelList.contains(webDir)  && webDir.exists()){
			
			//we want to check web dir first then java dir
			int index = firstLevelList.indexOf(webDir);
			firstLevelList.remove(index);
			keyCheckHelper(webDir);
			Iterator<File> fItr = firstLevelList.iterator();
			
			//iterate through all other dirs and files (besides web)
			while(fItr.hasNext()){
				File f = fItr.next();
				if (f.isDirectory()) {
					keyCheckHelper(f);
				} else if (f.isFile()){
					//if its a file we need to apply the extension filter still
					if (FileUtils.listFiles(f.getParentFile(), extensions, false).contains(f)) {
						keyCheckFile(f);						
					}
				}
			}
			
		} else {	//codedir doesnt contain web dir
			//uhh we may or may not want this to happen... just go in whatever order is default
			System.err.println("No web folder found! Doing a unordered run on all files.");
			keyCheckHelper(codeDirectory);
		}
		
	
		removeUnusedKeys(removeUnused);
		
		return 0;
	}
	
	//remove all the keys that are unused from their respective files (if boolean flag is true)
	//also generates a report on all the unused keys as it goes
	private void removeUnusedKeys(Boolean removeKeys) {
		if (keyList == null){		//keyList.isEmpty() ||
			return;
		}
		
		File report = new File(baseFilePath + "\\web\\WEB-INF\\i18n\\", "unreferenced_keys.txt");
		//collection to hold all the report output, will be written all at once at end
		List<String> output = new LinkedList<>();
		output.add("LIST OF UNUSED KEYS: ");
		
		//loop through all the property files specified in constructor
		for (int i = 0; i < files.length; i++){
			output.add(seperator);
			output.add("FILE: \"" + files[i] + "\"");
			output.add(seperator);
			File f = new File(files[i]);
			List<String> fileList = null;
			
			//read in all file lines
			try{
				fileList = FileUtil.readLines(f, encoding);
			} catch (Exception e){
				log.error("Error: ", e);
				return;
			}
			
			//iterate through the file and remove all the lines that aren't used
			Iterator<String> itr = fileList.iterator();
			while(itr.hasNext()){
				String property = itr.next();
				if (property.matches(propertyPattern) && keyList.contains(I18NLanguageTools.keyOf(property))){
					output.add(property);
					if (removeKeys){
						itr.remove();
					}
				}
			}
			
			//rewrite the file after all changes, print report as well
			try{
				if (removeKeys) {
					FileUtil.writeLines(f, encoding, fileList);
				}
			} catch (Exception e) {
				log.error("Error: ", e);
			}
		}
		output.add("\n Total Unused Keys: " + Integer.toString(keyList.size()));
		try{
		    FileUtil.writeLines(report, encoding, output);
		} catch (Exception e) {
			log.error("Error: ", e);
		}

		System.out.println("Unreferenced Keys Check: Execution complete");

	}
	
	//expects atleast 2 arguments, first n-1 arguments are properties files
	//last arg is the directory to search for code to compare against
	public static void main(String[] args) {
		boolean removeFlag = false;
		String[] propertiesFiles = defaultProperties;
		File codeDirectory = new File(defaultCodeDir);	//default values for property files and code dir

		System.out.println(System.getProperty("user.dir"));
		//baseFilePath = System.getProperty("user.dir") + "\\src";
		
		
		List<String> argAsList = Arrays.asList(args);
		LinkedList<String> argList = new LinkedList<>(argAsList);
		
		if (argList.contains(removeFlagString)){
			removeFlag = true;
			argList.remove(argList.indexOf(removeFlagString));
		}
		
		//if they specified property file(s) and a coding dir then pick those out of the args
		if (argList.size() > 1) {
			String[] newArgs = new String[argList.size()];
			argList.toArray(newArgs);
			
			//seperate the property file args from the code dir arg
			propertiesFiles = new String[newArgs.length-1];
			System.arraycopy(newArgs, 0, propertiesFiles, 0, newArgs.length-1);
			codeDirectory = new File(newArgs[newArgs.length-1]);
			
			//didnt specify enough parameters
		} else if (!argList.isEmpty()){
			System.err.println("ERROR - Invalid arguments.");
			return;
		}
		
		//make utility object, perform actual check with it
		I18NUnreferencedKeysTool pc = new I18NUnreferencedKeysTool(propertiesFiles, codeDirectory);		

		int error = pc.keyCheck(removeFlag);				
		if (error > 0){
			System.err.println("ERROR - keyCheck failed.");
		}
		
	}

}