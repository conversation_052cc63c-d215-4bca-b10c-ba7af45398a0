package tools.prinova.messagepoint.application;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.*;

import org.json.JSONException;
import org.json.JSONObject;
import com.google.gson.JsonObject;

public class I18NTranslationTools {

    private static final String BASE_FILE_PATH = System.getProperty("user.dir") + File.separator + "src";
    private static final String ENGLISH_FILE_PATH = BASE_FILE_PATH + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages.properties";
    private static final String API_KEY = "b7523605-e3647ba0-6f7f57c6-18d009c3b70";
    private static final int CONCURRENCY_TRANSLATION_LEVEL = 100;
    private static final int CONCURRENCY_ACCURACY_LEVEL = 10;

    private static final String TRANSLATION_MODEL = "gpt4-o";

    public static void main(String[] args) throws Exception {
        Map<String, String> masterMap = loadProperties(ENGLISH_FILE_PATH);

        int goodTranslations = 0;
        int badTranslations = 0;
        int totalTranslations = 0;
        int totalAccuracies = 0;

        // Languages you want to process
        String[] langCodes = {"fr"};

        ExecutorService executorTranslation = Executors.newFixedThreadPool(CONCURRENCY_TRANSLATION_LEVEL);
        ExecutorService accuracyExecutor = Executors.newFixedThreadPool(CONCURRENCY_ACCURACY_LEVEL);

        for (String langCode : langCodes) {
            String langFilePath = BASE_FILE_PATH + File.separator + "web" + File.separator + "WEB-INF" + File.separator + "i18n" + File.separator + "messages_" + langCode + ".properties";
            Map<String, String> langMap = loadProperties(langFilePath);

            List<Future<TranslationResult>> futures = new ArrayList<>();

            for (String key : langMap.keySet()) {
                if (langMap.get(key).isEmpty() && masterMap.containsKey(key)) {
                    final String keyFinal = key;
                    futures.add(accuracyExecutor.submit(() -> {
                        try {
                            String translation = translateText(masterMap.get(keyFinal), langCode);
                            return new TranslationResult(keyFinal, translation, null);
                        } catch(Exception e) {
                            return new TranslationResult(keyFinal, null, e);
                        }
                    }));
                }
            }

            List<Future<AccuracyResult>> accuracyFutures = new ArrayList<>();

            for (Future<TranslationResult> future : futures) {
                try {
                    TranslationResult result = future.get();
                    if (result.getException() == null) {
                        // Split the result to get the translated text and the original text
                        String[] parts = result.getTranslation().split("\\|\\|");
                        String translatedText = parts[0];
                        String originalText = parts[1];

                        final String keyFinal = result.getKey();
                        final String translatedTextFinal = translatedText;
                        final String originalTextFinal = originalText;

                        totalTranslations++;

                        if (totalTranslations % 100 == 0)
                            System.out.println("Total translations processed: " + totalTranslations);

                        // Submit accuracy check tasks to the executor
                        accuracyFutures.add(accuracyExecutor.submit(() -> {
                            boolean isAccurate = isTranslationAccurate(originalTextFinal, translatedTextFinal);
                            return new AccuracyResult(keyFinal, isAccurate, translatedTextFinal, originalTextFinal);
                        }));
                    }
                } catch (InterruptedException | ExecutionException e) {
                    System.out.println("Error processing a translation result: " + e.getMessage());
                }
            }

            // Process the results of accuracy checks
            for (Future<AccuracyResult> future : accuracyFutures) {
                try {
                    AccuracyResult result = future.get();
                    if (result.isAccurate()) {
                        langMap.put(result.getKey(), result.getTranslatedText());
                        goodTranslations++;
                    } else {
                        langMap.put(result.getKey(), "");
                        System.out.println("Bad translation: " + result.getKey() + ":" + result.getOriginalText() + "=" + result.getTranslatedText());
                        badTranslations++;
                    }

                    totalAccuracies++;
                    if (totalAccuracies % 100 == 0)
                        System.out.println("Total accuracies processed: " + totalAccuracies);

                } catch (InterruptedException | ExecutionException e) {
                    System.out.println("Error processing an accuracy check result:" + e.getMessage());
                }
            }

            saveProperties(langMap, langFilePath);
            saveValidationFile(langMap, masterMap, langFilePath);

            // Output the totals
            System.out.println("Good translations: " + goodTranslations);
            System.out.println("Bad translations: " + badTranslations);

        }

        executorTranslation.shutdown();
        accuracyExecutor.shutdown();
    }

    private static Map<String, String> loadProperties(String path) throws IOException {
        Properties props = new Properties();
        try (InputStream input = new FileInputStream(path)) {
            props.load(new InputStreamReader(input, "UTF-8"));
        }
        return new HashMap<>(props.stringPropertyNames().stream().collect(HashMap::new, (m, k) -> m.put(k, props.getProperty(k)), Map::putAll));
    }

    private static void saveProperties(Map<String, String> langMap, String originalPath) throws IOException {
        String translatedPath = originalPath.replace(".properties", ".properties_translated");
        List<String> sortedKeys = new ArrayList<>(langMap.keySet());
        Collections.sort(sortedKeys);

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(translatedPath), "UTF-8"))) {
            for (String key : sortedKeys) {
                String value = langMap.get(key).replace("\"", "\\\"");
                // Escape \n and \t characters
                if ( value.contains("\n") || value.contains("\t") )
                    value = value.replaceAll("\n", "\\\\n").replaceAll("\t", "\\\\t");
                writer.write(key + "=" + value + "\n");
            }
        }
    }

    private static void saveValidationFile(Map<String, String> langMap, Map<String, String> masterMap, String originalPath) throws IOException {
        String validationFilePath = originalPath.replace(".properties", ".properties_validation.csv");

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(validationFilePath), "UTF-8"))) {
            writer.write("Key|Translated Text|Original English Text\n");  // Header with | delimiter

            List<String> sortedKeys = new ArrayList<>(langMap.keySet());
            Collections.sort(sortedKeys);

            for (String key : sortedKeys) {
                writer.write(String.format("%s|%s|%s\n", key, langMap.get(key), masterMap.get(key)));
            }
        }
    }

    private static String translateText(String text, String targetLanguage) throws IOException {
        JsonObject jsonPayload = new JsonObject();
        jsonPayload.addProperty("text", text);
        jsonPayload.addProperty("target", targetLanguage);
        jsonPayload.addProperty("engine","genai");
        JsonObject ctxgenai = new JsonObject();
        ctxgenai.addProperty("model","gpt4");
        jsonPayload.add("ctxgenai", ctxgenai);
        String payload = jsonPayload.toString();

        String host = "https://w1waoh1clk.execute-api.us-east-1.amazonaws.com";
        String basePath = "semantex-qa";
        String pathname = "/" + basePath + "/text/translate";

        URL url = new URL(host + pathname);
        HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setRequestProperty("Content-Type", "application/json");
        httpURLConnection.setRequestProperty("x-api-key", API_KEY);
        httpURLConnection.setDoOutput(true);

        try (OutputStream os = httpURLConnection.getOutputStream()) {
            byte[] input = payload.getBytes("UTF-8");
            os.write(input, 0, input.length);
        }

        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(httpURLConnection.getInputStream(), "UTF-8"))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            JSONObject jsonResponse = new JSONObject(response.toString());

            if (jsonResponse.getJSONObject("status").getBoolean("success")) {
                // Extract the translated text
                String translatedText = jsonResponse.getJSONObject("result").getString("translation");
                return translatedText + "||" + text; // Return both translated and original text
            } else {
                String errorMessage = jsonResponse.getJSONObject("error").getString("message");
                throw new RuntimeException(errorMessage);
            }
        }
    }

    private static boolean isTranslationAccurate(String originalText, String translatedText) throws IOException {
        String host = "https://w1waoh1clk.execute-api.us-east-1.amazonaws.com";
        String basePath = "semantex-qa";
        String pathname = "/" + basePath + "/text/translate/accuracy";

        URL url = new URL(host + pathname);
        HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setRequestProperty("Content-Type", "application/json");
        httpURLConnection.setRequestProperty("x-api-key", API_KEY);
        httpURLConnection.setDoOutput(true);

        JsonObject jsonPayload = new JsonObject();
        jsonPayload.addProperty("text1", originalText);
        jsonPayload.addProperty("text2", translatedText);
        jsonPayload.addProperty("model",TRANSLATION_MODEL);
        String payload = jsonPayload.toString();

        try (OutputStream os = httpURLConnection.getOutputStream()) {
            byte[] input = payload.getBytes("UTF-8");
            os.write(input, 0, input.length);
        }

        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(httpURLConnection.getInputStream(), "UTF-8"))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        JSONObject jsonResponse = new JSONObject(response.toString());

        // Check if the translation is accurate
        try {
            return jsonResponse.getJSONObject("result").getString("label").equals("t");
        } catch (JSONException e) {
            System.out.println("JSONException encountered: " + e.getMessage());
            System.out.println("Response: " + response);
            return false; // Handle the case where the 'result' field is not found
        }
    }

    private static class TranslationResult {
        private final String key;
        private final String translation;
        private final Exception exception;

        public TranslationResult(String key, String translation, Exception exception) {
            this.key = key;
            this.translation = translation;
            this.exception = exception;
        }

        public String getKey() {
            return key;
        }

        public String getTranslation() {
            return translation;
        }

        public Exception getException() {
            return exception;
        }
    }

    private static class AccuracyResult {
        private final String key;
        private final boolean isAccurate;
        private final String translatedText;
        private final String originalText;

        public AccuracyResult(String key, boolean isAccurate, String translatedText, String originalText) {
            this.key = key;
            this.isAccurate = isAccurate;
            this.translatedText = translatedText;
            this.originalText = originalText;
        }

        public String getKey() {
            return key;
        }

        public boolean isAccurate() {
            return isAccurate;
        }

        public String getTranslatedText() {
            return translatedText;
        }

        public String getOriginalText() {
            return originalText;
        }
    }
}