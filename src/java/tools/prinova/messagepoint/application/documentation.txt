Documentation of Part C Phase 1 of language retrofit

Message Replace UI Usage:

Don't run MessageReplace.java, only run MessageReplace.java as a regular application.

This is meant to run from within the messagepoint project!
Command line arguments are only allowed to be .property files. Adding files to the cmd line args will automatically index them (instead of manually one by one)
5 options for each replace candidate: ignore, straight key replace, messagepoint formatted replace - ${msgpt:getMessage('KEY')}, spring formatted replace - <fmtSpring:message code="KEY"/>, and custom replace

Limitations:
- regex could miss some punctuation that is used mid string (trying to prevent from parsing labels ie page.label.blah)
- highlighting of the code we are replacing is not very persistent, could try to upgrade to a JEditPane or something to change font of selected code snippet
- Obviously doesn't work for anything other than jsp files
- only checks inside tags for label=, itemLabel=, title= but can easily be changed if needed
- can only add properties to one file, this is determined by user at runtime and can be changed in the menu.



AlphabetizeUtil.java Usage:

Run as an application. Can be run from command line.
By default it will run on messages.properties.
Arguments can be supplied to the command line to run on different target files instead of the defaults.
Only command line arguments should be property files that you want to alphabetize.


PropertiesCleanup.java

utility to check properties files against the code base
Will default to running on messages.properties if no command line args are given
will need files paths to properties to be check and top level code directory, can flag with -remove to auto remove keys
leaves an output file in the top level code directory that was specified
command line vars for main: [prop file#1] ... [prop file#n] [code dir] [-remove]
alternately no file paths given will default to messages and with entire project as code dir

Common Samples


${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}
${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}