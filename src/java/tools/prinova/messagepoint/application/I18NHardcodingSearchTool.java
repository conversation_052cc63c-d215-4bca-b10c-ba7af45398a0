package tools.prinova.messagepoint.application;

import java.io.File;
import java.io.FileWriter;
import java.util.Collection;
import java.util.Collections;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JOptionPane;

import org.apache.commons.io.FileUtils;

import org.apache.commons.logging.Log;
import tools.prinova.messagepoint.application.MessageReplaceUI.PropertyFileFilter;

import com.prinova.messagepoint.util.FileUtil;
import com.prinova.messagepoint.util.LogUtil;

//this is Part C of Phase 1 of fixing languages in messagepoint
//this tool is meant to search the code (specifically jsp files) and find any hard coded strings
//it will prompt the user to enter a key which will be placed in a property file with the value of the hard coded string
//uses MessageReplaceUI for its interface
//parameters passed in should be property files which are automatically indexed, still will be prompted to choose the file to add new properties to
public class I18NHardcodingSearchTool {

	private static final Log log = LogUtil.getLog(I18NHardcodingSearchTool.class);

	static String encoding = null;
	
	//parsing variables for pattern matching
	//String parsePattern = "<[^%]+\\s.*label=\"([\\w]*)\"";		//looks for label= tags
	//String parsePatternLabel = "<[^$\\s]+\\slabel=\"([\\w]+[[\\s]*[\\w]*]*)\"";
	String myWord = "[\\w|():,\\-/\']";
	String tags = "(?:label|title|listHeader)";
	String plainString = "(" + myWord + "+[[\\s][" + myWord + "+]]*)";
	//String parsePatternLabel = "<[^\\s]+[^\n]*\\s" + tags + "=\"(" + myWord + "+[[\\s]*" + myWord +"*]*)\"";
	String parsePatternLabel = "<[^\\s]+[^\n]*\\s"+ tags +"=[\"|\'](" + myWord + "+[[\\s]*" + myWord +"*]*)[\"|\']";
	String parsePatternContent = ">\\s*(" + myWord + "+[[\\s][" + myWord + "+]]*\\b[\\p{Punct}]?)\\s*<";
	//String parsePattern[] = {parsePatternLabel, parsePatternContent, ">\\s*(\\w+)\\s*"
	int parsePatternLabelGroup = 1; 
	
	//these keep track of the position of the most recently found replace spot
	//also an offset for changes made to current file since parsing is asynchronous
	private static int startReplace = 0 , endReplace = 0, replaceOffset = 0;
	
	//user interface class
	static MessageReplaceUI window;
	
	Hashtable properties = new Hashtable();
	
	//different prefixes and suffixes for each type of replacement
	private static String	messagepointReplacePrefix = "${msgpt:getMessage('", 
							messagepointReplaceSuffix = "')}",
							springReplacePrefix = "<fmtSpring:message code=\"",
							springReplaceSuffix = "\"/>";
	
	//code file and property file variables for file names and actual file values
	private static String		codeFileAsString;
	
	private static File			codeFile, propertyFile;
	private static List<File>	propertyFiles;
	
	private static LinkedList<ReplaceSpot> replaceQueue = new LinkedList<>();
	
	//lists of all the keys that are already in the system
	private static Vector<String>	keys = new Vector<>(),
								messages = new Vector<>();
	
	//constructor, indexes all the initial files we want to use and instantiates ui
	//expects an array of property files which it will index keys and messages from
	I18NHardcodingSearchTool(String[] fileNames) {
		propertyFiles = new LinkedList<>();
		for (int i = 0; i < fileNames.length; i++){
			File f = new File(fileNames[i]);
			PropertyFileFilter filter = new PropertyFileFilter();
			if (filter.accept(f) && !f.isDirectory()){
				propertyFiles.add(f);
				indexProperties(f, keys, messages);
			}
			

		}
		
		Thread fpThread = new Thread(new FileParser(new File(System.getProperty("user.dir")), replaceQueue));
		fpThread.setPriority(Thread.MIN_PRIORITY);
		fpThread.setName("parser");
		fpThread.setDaemon(true);
		fpThread.start();
		window = new MessageReplaceUI();
		
		nextReplace();
	}
	
	//takes the next replace spot out of the queue and loads it then updates ui
	private static void nextReplace() {
		
		//no items left means we're all done, dispose of everything
		if(replaceQueue.isEmpty()){
			JOptionPane.showMessageDialog(window.window, "Search Complete");
			window.window.dispose();
		}
		ReplaceSpot rs = replaceQueue.removeFirst();	//pop off queue
		
		File tempCodeFile = rs.getFile();
		if (!tempCodeFile.equals(codeFile)){
			replaceOffset = 0;
		}
		codeFile = tempCodeFile;
		
		//use an offset to keep the indexes in the correct spot in case something has been replaced already
		startReplace = rs.getStartIndex() + replaceOffset;
		endReplace = rs.getEndIndex() + replaceOffset;

		
		codeFileAsString = "ERROR";
		try {
			codeFileAsString = FileUtils.readFileToString(codeFile, encoding);
		} catch (Exception e) {
			log.error("Error: ", e);
		}
		
		//update ui
		window.setCodeView(codeFileAsString, codeFile.getPath(), startReplace, endReplace);
	}
	
	//adds all keys and messages found in the file to their respective lists
	private static int indexProperties(File file, List<String> keyList, List<String> messageList){
		List<String> fileList = null;
		/*	keyList.clear();
		 *	messageList.clear();
		 */
		
		try{				
			//get the listing of properties from the file
			if (file.exists()){ 	
				fileList = FileUtil.readLines(file, encoding);
			} else {
				System.err.println("ERROR - Failed to find file: " + file);
				return 1;
			}
		
			
			//iterate over all the properties extracting keys
			Iterator<String> itr = fileList.iterator();
			while(itr.hasNext()){
				String property = itr.next();
				//property has to match the pattern, and have a message that is not blank
				if (property.matches(I18NUnreferencedKeysTool.propertyPattern) && (!I18NLanguageTools.messageOf(property).trim().isEmpty())){
					keyList.add(I18NLanguageTools.keyOf(property));
					messageList.add(I18NLanguageTools.messageOf(property));
				} else {
					System.out.println("Rejected: \'" + property+ "\'");
				}
			}
		} catch( Exception e ){
			log.error("Error: ", e);
			return 1;
		}
		
		return 0;
	}
	
	//method for UI to call which is used to replace the current instance of hard code
	//type determines what kind of operation we are doing (see switch statement)
	public static void replaceType(int type, String prefix, String suffix){
		if (startReplace == 0 && endReplace == 0){
			return;
		}
		String replaceKey = window.getKey();
		String replaceMessage = window.getMessage();
		String replaceProperty = window.getProperty();
		String replaceText = null;
		
		switch (type){
			//1 = ignore
			case 1:	replaceText = null;
					break;
			//2 = simple key replacement
			case 2:	replaceText = replaceKey;
					break;
			//3 = replace with messagepoint formatting
			case 3: replaceText = messagepointReplacePrefix + replaceKey + messagepointReplaceSuffix;
					break;
			//4 = replace with spring formatting
			case 4: replaceText = springReplacePrefix + replaceKey + springReplaceSuffix;
					break;
			//5 - custom replace with custom prefix and suffix
			case 5: replaceText = prefix + replaceKey + suffix;
					break;
		}
		
		//FileUtils.readFileToString(codeFile, codeFileAsString);
		//assume that codeFileAsString already has the file loaded into it
		if (replaceText != null){
			//replaces the text with the key chosen by user
			codeFileAsString = codeFileAsString.substring(0, startReplace) + replaceText + codeFileAsString.substring(endReplace);
			try {
				FileUtils.writeStringToFile(codeFile, codeFileAsString, encoding);
			} catch (Exception e) {
				log.error("Error: ", e);
			}
			
			//add the new property to the designated file (only if we dont already have it indexed)
			if (!keys.contains(replaceKey)){
				addProperty(replaceProperty);
				keys.add(replaceKey);
				messages.add(replaceMessage);
			}
			
			replaceOffset += (replaceText.length() - (endReplace - startReplace));
		}
		
		//reset the replace substring
		startReplace = 0;
		endReplace = 0;
		
		//should continue to next entry we are checking...
		nextReplace();
		
	}
	
	//checks to see if a key is already in our list of keys
	public static int checkKeys(String key) {
		if (key.isEmpty()){
			return 1; // return that this key is already used so we cannot use blank key
		} else if (key.matches(".*\\s.*")) {
			return 2;	// cannot contain spaces...
		}
		if (keys.contains(key)){
			return 1;	//already used
		} else {
			return 0;	// key is available
		}
	}
	
	//checks to see if a message is already in one of our property files 
	public static Boolean checkMessages(String message, List<String> duplicates){
		/*Iterator<String> itr = messages.iterator();
		while(itr.hasNext())
		*/
		
		int index = messages.indexOf(message);
		if (index == -1){
			duplicates.clear();
		} else {
			while (index != -1){
				duplicates.add(keys.get(index));
				index = messages.indexOf(message,index+1);
			}
		}
		
		
		return messages.contains(message);
	}
	
	//set the file which we will add new properties to!
	//will also index keys and messages IF this file is not already contained in the list
	public static void setPropertyFile(File f) {
		propertyFile = f;
		if (!propertyFiles.contains(f)){
			indexProperties(f, keys, messages);
			propertyFiles.add(f);
		}
	}
	
	//adds a property to the registered file.
	private static void addProperty(String property){
		try{
			FileWriter fw = new FileWriter(propertyFile,true);
			fw.write(property+"\n");
			fw.flush();
			fw.close();
		} catch(Exception e) {
			log.error("Error: ", e);
		}
	}
	
	//data structure that keeps track of a replace in a specific file with indexes
	private static class ReplaceSpot implements Comparable<Object>{
		private int startIndex;
		private int endIndex;
		private File file;
		
		ReplaceSpot(File f, int start, int end){
			setIndices(start, end);
			setFile(f);
		}
		
		public void setIndices(int start, int end) {
			startIndex = start;
			endIndex = end;
		}
		
		public void setFile(File f){
			file = f;
		}
		
		public File getFile(){
			return file;
		}
		
		public int getStartIndex(){
			return startIndex;
		}
		
		public int getEndIndex() {
			return endIndex;
		}

		public int compareTo(Object rs) {
			return (this.startIndex - ((ReplaceSpot)rs).startIndex);
		}
	}
	
	//Separate thread for parsing jsp files and indexing possible replaces
	private class FileParser implements Runnable {
		
		private String[] extensions = {"jsp"};
		Collection<File> files;
		List<ReplaceSpot> replaceQueue = new LinkedList<>();
		List<ReplaceSpot> finalQueue;

		FileParser(File topLevel, List<ReplaceSpot> queue ){	//need to give it some sort of queue for putting replaces in...
			files = FileUtils.listFiles(topLevel, extensions, true);
			finalQueue = queue;
		}
		
		public void run() {
			Iterator<File> itr = files.iterator();
			
			//go through each file
			while(itr.hasNext()){
				File file = itr.next();
				String fileString = null;
				try{
					fileString = FileUtils.readFileToString(file, encoding);
				} catch (Exception e){
					log.error("Error: ", e);
				}
				
				//first pattern for tag embedded stuff like label=
				Pattern p = Pattern.compile(parsePatternLabel);
				Matcher m = p.matcher(fileString);
				
				while (m.find()){
					int startIndex = m.start(parsePatternLabelGroup);
					int endIndex = m.end(parsePatternLabelGroup);
					
					replaceQueue.add(new ReplaceSpot(file, startIndex, endIndex));
					System.out.println("M1: \"" + m.group(1) + "\"");
				}
				
				
				//second pattern for actual content areas
				Pattern p2 = Pattern.compile(parsePatternContent);
				Matcher m2 = p2.matcher(fileString);
				
				while(m2.find()){
					int startIndex = m2.start(1);
					int endIndex = m2.end(1);
					System.out.println("M2: \"" + m2.group(1) + "\"");
					replaceQueue.add(new ReplaceSpot(file, startIndex, endIndex));
				}

				//sort the queue so that we dont jump up in a file just down, important for how offset works
				Collections.sort(replaceQueue);
				finalQueue.addAll(replaceQueue);	//add them to the global queue
				replaceQueue.clear();				//flush local queue
				
			}

			System.out.println("DONE PARSING!");
		}
		
	}
	
	//main for actually kicking off the script
	@SuppressWarnings("unused")
	public static void main(String[] args) {

		I18NHardcodingSearchTool mr = new I18NHardcodingSearchTool(args);

	}

}
